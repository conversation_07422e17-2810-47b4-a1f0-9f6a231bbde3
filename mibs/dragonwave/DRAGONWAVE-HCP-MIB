
-- File Name : DRAGONWAVE-HCP-MIB.mib
-- Version   : 1.3.10
-- Date      : December, 2015
-- Author    : DragonWave Inc.

DRAGONWAVE-HCP-MIB DEFINITIONS ::= BEGIN

IMPORTS
	MODULE-IDENTITY, NOTIFICATION-TYPE, OB<PERSON>ECT-<PERSON><PERSON><PERSON>, 
	<PERSON>32, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Integer32
		FROM SNMPv2-SMI
	RowStatus, DateAndTime, DisplayString
		FROM SNMPv2-TC
	ifIndex
		FROM RFC1213-MIB
	horizon
		FROM HORIZON-MIB
	horizonEquipmentOutTrapsCounter
		FROM HORIZON-EQUIPMENT-LOG-MIB
	In<PERSON>refi<PERSON>ength, In<PERSON><PERSON><PERSON><PERSON>, InetAddressType
		FROM INET-ADDRESS-MIB;

hzCpModIdentity MODULE-IDENTITY
	LAST-UPDATED "201403281023Z"
	ORGANIZATION 
		"Dragonwave Inc."
	CONTACT-INFO 
		"http://www.dragonwaveinc.com
		600-411 Legget Drive
		Ottawa, Ontario
		Canada, K2K 3C9
		
		Tel : ************
		Fax: ************
		Support: ************"
	DESCRIPTION 
		"This specifies the contact information and the revision tracking information for the MIB module version."

	REVISION "201301171109Z"
	DESCRIPTION 
		"The MIB module for Dragonwave HCP product."

	REVISION "201403281028Z"
	DESCRIPTION 
		"Added PM Rspi Threshold table."

	REVISION "201411241712Z"
	DESCRIPTION 
		"Added support for IPv6 auto discovery. Added PM Adv Threshold table."

	REVISION "201512101712Z"
	DESCRIPTION 
		"Added a new hzCpConfigChanged trap."

::= { horizonCompactPlus 1000 }


-- Important!! For HP OpenView, the following OID definition for ISO must be commented out,
-- otherwise it won't compile. HP openView has already defined ISO.
--      iso   OBJECT IDENTIFIER  ::=  { 1 }

horizonCompactPlus   OBJECT IDENTIFIER ::= { horizon 5 }

-- 
-- Node definitions
-- 

hzCpSystem           OBJECT IDENTIFIER ::= { horizonCompactPlus 1 }

-- -------------
--   hzCpSystem
-- -------------

hzCpSysGeneral       OBJECT IDENTIFIER ::= { hzCpSystem 1 }
hzCpSysSpeed         OBJECT IDENTIFIER ::= { hzCpSystem 2 }
hzCpSysUpgradeSpeed  OBJECT IDENTIFIER ::= { hzCpSysSpeed 3 }
hzCpSysDowngradeSpeed  OBJECT IDENTIFIER ::= { hzCpSysSpeed 4 }
hzCpInventory        OBJECT IDENTIFIER ::= { hzCpSystem 3 }

-- ----------------------------
--   hzCpInventory
-- ----------------------------  

hzCpHwInventory      OBJECT IDENTIFIER ::= { hzCpInventory 1 }
hzCpSwInventory      OBJECT IDENTIFIER ::= { hzCpInventory 2 }
hzCpPeerSysInfo      OBJECT IDENTIFIER ::= { hzCpSystem 4 }
hzCpSysRedundancy    OBJECT IDENTIFIER ::= { hzCpSystem 5 }
hzCpRedundancyLinkSwitchParameters  OBJECT IDENTIFIER ::= { hzCpSysRedundancy 6 }
hzCpRedundancyLinkMonitorParameters  OBJECT IDENTIFIER ::= { hzCpSysRedundancy 7 }
hzCpPartnerSysInfo   OBJECT IDENTIFIER ::= { hzCpSysRedundancy 8 }
hzCpPeerPartnerSysInfo  OBJECT IDENTIFIER ::= { hzCpSysRedundancy 9 }
hzCpPartnerCommunication  OBJECT IDENTIFIER ::= { hzCpSysRedundancy 10 }
hzCpRedundancyStatus  OBJECT IDENTIFIER ::= { hzCpSysRedundancy 11 }
hzCpSysDiagnostics   OBJECT IDENTIFIER ::= { hzCpSystem 6 }

-- -------------
--   hzCpSysDiagnostics
-- -------------

hzCpLoopback         OBJECT IDENTIFIER ::= { hzCpSysDiagnostics 1 }
hzCpHitlessAamDiagnostics  OBJECT IDENTIFIER ::= { hzCpSysDiagnostics 2 }
hzCpSysLicensedFeatureGroups  OBJECT IDENTIFIER ::= { hzCpSystem 7 }
hzCpPeerAuthentication  OBJECT IDENTIFIER ::= { hzCpSystem 8 }
hzCpCalendar         OBJECT IDENTIFIER ::= { hzCpSystem 9 }
hzCpSntp             OBJECT IDENTIFIER ::= { hzCpSystem 10 }
hzCpSyncE            OBJECT IDENTIFIER ::= { hzCpSystem 11 }
hzCpBac              OBJECT IDENTIFIER ::= { hzCpSystem 12 }
hzCpEcfmVsm          OBJECT IDENTIFIER ::= { hzCpSystem 13 }
hzCpBandwidthVsm     OBJECT IDENTIFIER ::= { hzCpEcfmVsm 1 }
hzCpPM               OBJECT IDENTIFIER ::= { hzCpSystem 14 }
hzCpManagement       OBJECT IDENTIFIER ::= { horizonCompactPlus 2 }
hzCpNetworkManagementInterface  OBJECT IDENTIFIER ::= { hzCpManagement 4 }
hzCpNetMgmtInterfaceVlan  OBJECT IDENTIFIER ::= { hzCpNetworkManagementInterface 1 }
hzCpNetMgmtIpv4      OBJECT IDENTIFIER ::= { hzCpNetworkManagementInterface 2 }

-- Reserved

hzCpNetMgmttIpv6     OBJECT IDENTIFIER ::= { hzCpNetworkManagementInterface 3 }
hzCpSnmp             OBJECT IDENTIFIER ::= { hzCpManagement 5 }
hzCpTrapConfig       OBJECT IDENTIFIER ::= { hzCpManagement 6 }

-- ----------------------------
-- TRAP ENABLE 
-- ----------------------------

hzCpTrapEnable       OBJECT IDENTIFIER ::= { hzCpTrapConfig 3 }
hzCpRadius           OBJECT IDENTIFIER ::= { hzCpManagement 7 }
hzCpManagementSessions  OBJECT IDENTIFIER ::= { hzCpManagement 8 }
hzCpHttp             OBJECT IDENTIFIER ::= { hzCpManagement 9 }
hzCpHttpSecure       OBJECT IDENTIFIER ::= { hzCpHttp 2 }
hzCpNetworkInterface  OBJECT IDENTIFIER ::= { horizonCompactPlus 3 }
hzCpEnetPort         OBJECT IDENTIFIER ::= { hzCpNetworkInterface 1 }
hzCpEnetAggregatedStats  OBJECT IDENTIFIER ::= { hzCpEnetPort 4 }
hzCpEnetAggregatedLcStats  OBJECT IDENTIFIER ::= { hzCpEnetPort 6 }
hzCpWirelessInterface  OBJECT IDENTIFIER ::= { horizonCompactPlus 4 }

-- ----------------------------
--   hzCpWirelessInterface
-- ----------------------------      

hzCpWirelessInterfaceNames  OBJECT IDENTIFIER ::= { hzCpWirelessInterface 1 }
hzCpWirelessInterfaceModems  OBJECT IDENTIFIER ::= { hzCpWirelessInterface 2 }
hzCpWirelessAggregateStats  OBJECT IDENTIFIER ::= { hzCpWirelessInterfaceModems 3 }
hzCpWirelessAggregateLcStats  OBJECT IDENTIFIER ::= { hzCpWirelessInterfaceModems 5 }

-- hzCpWirelessInterfaceIF  OBJECT IDENTIFIER ::= { hzCpWirelessInterface 3 }

hzCpWirelessInterfaceRadios  OBJECT IDENTIFIER ::= { hzCpWirelessInterface 4 }
hzCpWirelessInterfaceRadioFrequencies  OBJECT IDENTIFIER ::= { hzCpWirelessInterface 5 }
hzCpWirelessInterfaceRadio1Frequencies  OBJECT IDENTIFIER ::= { hzCpWirelessInterfaceRadioFrequencies 1 }

-- --------------------------------------
-- hzCpWirelessInterfaceRadioFrequencies
-- --------------------------------------

hzCpRadio1ProgrammedFrequency  OBJECT IDENTIFIER ::= { hzCpWirelessInterfaceRadio1Frequencies 4 }

-- hzCpWirelessInterfaceRadio2Frequencies  OBJECT IDENTIFIER ::= { hzCpWirelessInterfaceRadioFrequencies 2 }
-- hzCpRadio2ProgrammedFrequency  OBJECT IDENTIFIER ::= { hzCpWirelessInterfaceRadio2Frequencies 5 }

hzCpWirelessInterfaceAntenna  OBJECT IDENTIFIER ::= { hzCpWirelessInterface 6 }
hzCpAlarms           OBJECT IDENTIFIER ::= { horizonCompactPlus 5 }
hzCpThresholdAlarmConfig  OBJECT IDENTIFIER ::= { hzCpAlarms 1 }

-- 
-- hzCpThresholdAlarmConfig
-- 

hzCpAggregatedThresholdAlarm  OBJECT IDENTIFIER ::= { hzCpThresholdAlarmConfig 1 }
hzCpQBasedThresholdAlarm  OBJECT IDENTIFIER ::= { hzCpThresholdAlarmConfig 2 }
hzCpQos              OBJECT IDENTIFIER ::= { horizonCompactPlus 6 }
hzCpRapidLinkShutdown  OBJECT IDENTIFIER ::= { horizonCompactPlus 7 }
hzCpLogs             OBJECT IDENTIFIER ::= { horizonCompactPlus 8 }
hzCpSysLog           OBJECT IDENTIFIER ::= { hzCpLogs 4 }
hzCpAtpc             OBJECT IDENTIFIER ::= { horizonCompactPlus 9 }
hzCpHitlessAam       OBJECT IDENTIFIER ::= { horizonCompactPlus 10 }
hzCpSnmpNotifications  OBJECT IDENTIFIER ::= { horizonCompactPlus 11 }

-- ----------------------------
--   hzCpGeneral
-- ----------------------------   

hzCpResetSystem  OBJECT-TYPE
	SYNTAX     INTEGER {
		reset (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Writing '1' to this object resets the system."
	::= { hzCpSysGeneral 1 }

hzCpSaveMIB  OBJECT-TYPE
	SYNTAX     INTEGER {
		save (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Writing '1' or 'save' to this object save all MIB values."
	::= { hzCpSysGeneral 2 }

hzCpOperStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		up (1),
		down (2),
		testing (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current operational state of the system. 
		Testing indicates that no operational packets can be passed."
	::= { hzCpSysGeneral 3 }

-- ----------------------------
--   hzCpSysSpeed
-- ----------------------------   

hzCpSystemCurrentSpeed  OBJECT-TYPE
	SYNTAX     Integer32 (0..1000)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Changes the working speed of the system."
	::= { hzCpSysSpeed 1 }

hzCpSystemLicensedSpeed  OBJECT-TYPE
	SYNTAX     Integer32 (0..1000)
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the maximum speed that this system can operate at. Due to backwards compatibility issue, 0 value should be treated as 0 Mbps."
	::= { hzCpSysSpeed 2 }

-- ----------------------------
--   hzCpSysUpgradeSpeed
-- ----------------------------   

hzCpLicensedSpeedUpgradeSpeedAndKey  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Enter your key here along with the maximum speed for that key to upgrade the system speed.
		 
		Format: [speed] [key]
		Example: 800 abc123"
	::= { hzCpSysUpgradeSpeed 1 }

hzCpLicensedSpeedCount  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This object indicates the number of times the licensed speed has been changed successfully. It is used when generating new license keys for your system."
	::= { hzCpSysUpgradeSpeed 2 }

-- ----------------------------
--   hzQtmSysDowngradeSpeed
-- ----------------------------   

hzCpLicensedSpeedDowngradeSpeed  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Reduce your licensed speed by entering a speed to this object and take note of the generated key in hzCpLicensedSpeedConfirmationKey.
		 
		Format: [speed]
		Example: 200"
	::= { hzCpSysDowngradeSpeed 1 }

hzCpLicensedSpeedCountUsedForKey  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This object indicates the number of times the licensed speed has been changed successfully. It is used when generating new license keys for your system."
	::= { hzCpSysDowngradeSpeed 2 }

hzCpLicensedSpeedConfirmationKey  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This read-only object will show a 'key' after successfully downgrading the license speed."
	::= { hzCpSysDowngradeSpeed 3 }

hzCpSysDowngradeSpeedDecrement  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This object indicates the difference for the dropped speed."
	::= { hzCpSysDowngradeSpeed 4 }

-- ----------------------------
--   hzCpHwInventory
-- ---------------------------- 

hzCpFrequencyFilePartNumber  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing Part Number of the Frequency File"
	::= { hzCpHwInventory 1 }

hzCpUnitSerialNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing Serial Number of the Unit."
	::= { hzCpHwInventory 2 }

hzCpUnitAssemblyNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing the Assembly Number of the Unit."
	::= { hzCpHwInventory 3 }

hzCpModemSerialNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing Serial no of the Modem."
	::= { hzCpHwInventory 4 }

hzCpModemAssemblyNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing assembly no of the Modem."
	::= { hzCpHwInventory 5 }

hzCpPsuSerialNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing Serial no of the PSU."
	::= { hzCpHwInventory 6 }

hzCpPsuAssemblyNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing assembly no of the PSU."
	::= { hzCpHwInventory 7 }

hzCpRfSerialNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing Serial no of the RF."
	::= { hzCpHwInventory 8 }

hzCpRfAssemblyNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing assembly no of the RF."
	::= { hzCpHwInventory 9 }

hzCpDiplexerSerialNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing Serial no of the Diplexer."
	::= { hzCpHwInventory 10 }

hzCpDiplexerAssemblyNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing assembly no of the Diplexer."
	::= { hzCpHwInventory 11 }

hzCpSystemCleiNo  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing system CLEI code."
	::= { hzCpHwInventory 12 }

-- ----------------------------
--   hzCpSwInventory
-- ----------------------------

hzCpSwInventoryTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSwInventoryEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table contains the software inventory."
	::= { hzCpSwInventory 1 }

hzCpSwInventoryEntry  OBJECT-TYPE
	SYNTAX 	HzCpSwInventoryEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Software inventory entry."
	INDEX { hzCpSwInvBank }
	::= { hzCpSwInventoryTable 1 }

HzCpSwInventoryEntry ::= SEQUENCE {
	hzCpSwInvBank
		INTEGER,
	hzCpSwInvStatus
		INTEGER,
	hzCpSwInvOmniRelease
		DisplayString,
	hzCpSwInvFrequencyFileVersion
		DisplayString,
	hzCpSwInvMibVersion
		DisplayString,
	hzCpSwInvBootloaderVersion
		DisplayString
}

hzCpSwInvBank  OBJECT-TYPE
	SYNTAX     INTEGER {
		bank-A (1),
		bank-B (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Software bank in the flash. "
	::= { hzCpSwInventoryEntry 1 }

hzCpSwInvStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		active (1),
		standby (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Software bank status. "
	::= { hzCpSwInventoryEntry 2 }

hzCpSwInvOmniRelease  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The version number of the Omni."
	::= { hzCpSwInventoryEntry 3 }

hzCpSwInvFrequencyFileVersion  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The version number of the frequency file."
	::= { hzCpSwInventoryEntry 4 }

hzCpSwInvMibVersion  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The version number of the system MIB."
	::= { hzCpSwInventoryEntry 5 }

hzCpSwInvBootloaderVersion  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The version number of the bootloader."
	::= { hzCpSwInventoryEntry 6 }

-- ----------------------
--  hzCpPeerSysInfo
-- ---------------------

hzCpPeerMacAddress  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The MAC Address of the peer network interface card."
	::= { hzCpPeerSysInfo 1 }

hzCpPeerIpAddress  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IP Address of the peer network interface card."
	::= { hzCpPeerSysInfo 2 }

hzCpPeerSubnetMask  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The subnet mask for the peer network interface card."
	::= { hzCpPeerSysInfo 3 }

hzCpPeerDefaultGateway  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The default gateway address of the peer node."
	::= { hzCpPeerSysInfo 4 }

hzCpPeerIpv6Prefix  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The peer IPv6 prefix."
	::= { hzCpPeerSysInfo 5 }

hzCpPeerIpv6Domain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The peer IPv6 domain."
	::= { hzCpPeerSysInfo 6 }

hzCpPeerIpv6Address  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The peer IPv6 address."
	::= { hzCpPeerSysInfo 7 }

hzCpPeerIpv6GatewayDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The peer IPv6 gateway domain."
	::= { hzCpPeerSysInfo 8 }

hzCpPeerIpv6GatewayAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The peer IPv6 gateway address."
	::= { hzCpPeerSysInfo 9 }

-- ----------------------------
--   hzCpSysRedundancy
-- ----------------------------

hzCpRedundancyMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		primary-hsb (2),
		secondary-hsb (3),
		primary-x2 (4),
		secondary-x2 (5)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"allows the user to set system redundancy mode:
		         [ off | primary-hsb | secondary-hsb | primary-x2 | secondary-x2 ]"
	::= { hzCpSysRedundancy 1 }

hzCpRedundancySwitchMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		auto (1),
		force-active (2),
		force-standby (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Allows the user to override the active link. User can force the link to stay on either force-active, force-standby, auto or manual."
	::= { hzCpSysRedundancy 2 }

hzCpRedundancyStandbyEnetState  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2),
		pulse (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Allows the user to set standby payload port state."
	::= { hzCpSysRedundancy 3 }

hzCpRedundancyStateSwitch  OBJECT-TYPE
	SYNTAX     INTEGER {
		on (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Allows the user to switch redundancy state."
	::= { hzCpSysRedundancy 4 }

hzCpRedundancyMgmtSource  OBJECT-TYPE
	SYNTAX     INTEGER {
		local-node (1),
		active-node (2),
		standby-node (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"When set to local-node, each node is managed locally through specified management interface. When set to standby-node, the management traffic for the partner is processed through the standby node interface."
	::= { hzCpSysRedundancy 5 }

hzCpRedundancyPrimaryTimeInActiveState  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Time, in miliseconds, given to the primary unit to establish a link once it becomes active."
	::= { hzCpRedundancyLinkSwitchParameters 1 }

hzCpRedundancyPrimarySwitchErrorThreshold  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The percent of allowed bad blocks, on the primary unit, needed to establish a link."
	::= { hzCpRedundancyLinkSwitchParameters 2 }

hzCpRedundancySecondaryTimeInActiveState  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Time, in miliseconds, given to the secondary unit to establish a link once it becomes active."
	::= { hzCpRedundancyLinkSwitchParameters 3 }

hzCpRedundancySecondarySwitchErrorThreshold  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The percent of allowed bad blocks, on the secondary unit, needed to establish a link."
	::= { hzCpRedundancyLinkSwitchParameters 4 }

hzCpRedundancyFaultPeriod  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The period of time, in milliseconds, for which the Fault Threshold is applied."
	::= { hzCpRedundancyLinkMonitorParameters 1 }

hzCpRedundancyFaultThreshold  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The maximum percentage of the bad blocks allowed in specified Fault Sample Period to maintain link when in active state."
	::= { hzCpRedundancyLinkMonitorParameters 2 }

hzCpPartnerMacAddress  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The MAC Address of the partner node."
	::= { hzCpPartnerSysInfo 1 }

hzCpPartnerIpAddress  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IP Address of the partner node."
	::= { hzCpPartnerSysInfo 2 }

hzCpPartnerSubnetMask  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The subnet mask for the partner node."
	::= { hzCpPartnerSysInfo 3 }

hzCpPartnerDefaultGateway  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The default gateway address of the partner node."
	::= { hzCpPartnerSysInfo 4 }

hzCpPartnerIpv6Prefix  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 prefix of the partner node."
	::= { hzCpPartnerSysInfo 5 }

hzCpPartnerIpv6Domain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 domain of the partner node."
	::= { hzCpPartnerSysInfo 6 }

hzCpPartnerIpv6Address  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 address of the partner node."
	::= { hzCpPartnerSysInfo 7 }

hzCpPartnerIpv6GatewayDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 gateway domain of the partner node."
	::= { hzCpPartnerSysInfo 8 }

hzCpPartnerIpv6GatewayAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 gateway address of the partner node."
	::= { hzCpPartnerSysInfo 9 }

hzCpPeerPartnerMacAddress  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The MAC Address of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 1 }

hzCpPeerPartnerIpAddress  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IP Address of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 2 }

hzCpPeerPartnerSubnetMask  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The subnet mask for the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 3 }

hzCpPeerPartnerDefaultGateway  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The default gateway address of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 4 }

hzCpPeerPartnerIpv6Prefix  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 prefix of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 5 }

hzCpPeerPartnerIpv6Domain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 domain of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 6 }

hzCpPeerPartnerIpv6Address  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 address of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 7 }

hzCpPeerPartnerIpv6GatewayDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 gateway domain of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 8 }

hzCpPeerPartnerIpv6GatewayAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The IPv6 gateway address of the partner's peer node."
	::= { hzCpPeerPartnerSysInfo 9 }

hzCpPartnerAutoDiscovery  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Disables or enables the partner's auto discovery."
	::= { hzCpPartnerCommunication 1 }

hzCpPartnerMacAddressOverride  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The MAC Address for partner to be used when the auto discovery is disabled."
	::= { hzCpPartnerCommunication 2 }

hzCpPartnerVlanTagging  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Disables or enables the VLAN tagging for partner's communication. VLAN tagging is the practice of inserting a VLAN Id into a packet header in order to identify which VLAN the packets belongs to."
	::= { hzCpPartnerCommunication 3 }

hzCpPartnerVlanTagId  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates the VLAN Tag Id (1...4095) for partner's communication."
	::= { hzCpPartnerCommunication 4 }

hzCpPartnerVlanTagPriority  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The priority for VLAN tagging (0...7) for partner's communication."
	::= { hzCpPartnerCommunication 5 }

hzCpRedundancyUserTrafficStatus  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the redundancy user traffic status."
	::= { hzCpRedundancyStatus 1 }

hzCpRedundancyWirelessLinkStatus  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the redundancy wireless link status."
	::= { hzCpRedundancyStatus 2 }

hzCpRedundancyEnetCrossLinkStatus  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the redundancy ethernet cross link status. Primary node is active and processing user traffic at one end of the link, and secondary node is active at the other end of the link resulting in end to end cross connection to user network equipment."
	::= { hzCpRedundancyStatus 3 }

hzCpRedundancyPortGroupStatus  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the redundancy port group status."
	::= { hzCpRedundancyStatus 4 }

-- ----------------------------
-- hzCpLoopback
-- ----------------------------

hzCpLoopbackType  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		radioNonNetwork (2),
		radioNetwork (3),
		networkNearEnd (4),
		networkFarEnd (5)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"What the user sets is cached until the loopback is started. The get returns the running status of the active loopback type. "
	::= { hzCpLoopback 1 }

hzCpLoopbackTimeout  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The loopback timeout period."
	::= { hzCpLoopback 2 }

hzCpLoopbackNetworkMac  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The MAC address used for network loopback."
	::= { hzCpLoopback 3 }

hzCpLoopbackNetworkQueue  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The user queue used for far-end network loopback."
	::= { hzCpLoopback 4 }

hzCpLoopbackNetworkPort  OBJECT-TYPE
	SYNTAX     INTEGER {
		enet-port-1 (1),
		enet-port-2 (2),
		enet-port-3 (3),
		enet-port-4 (4)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The user enet port used for network loopback."
	::= { hzCpLoopback 5 }

hzCpLoopbackStart  OBJECT-TYPE
	SYNTAX     INTEGER {
		start (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Start the loopback by writing '1' to this object."
	::= { hzCpLoopback 6 }

hzCpLoopbackStop  OBJECT-TYPE
	SYNTAX     INTEGER {
		stop (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Stop the loopback by writing '1' to this object."
	::= { hzCpLoopback 7 }

-- ----------------------------
--   hzCpHitlessAamDiagnostics
-- ----------------------------   

hzCpHitlessAamModemTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpHitlessAamModemEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Wireless Interface Modems"
	::= { hzCpHitlessAamDiagnostics 1 }

hzCpHitlessAamModemEntry  OBJECT-TYPE
	SYNTAX 	HzCpHitlessAamModemEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A Wireless Interface Modem"
	INDEX { hzCpHitlessAamModemIndex }
	::= { hzCpHitlessAamModemTable 1 }

HzCpHitlessAamModemEntry ::= SEQUENCE {
	hzCpHitlessAamModemIndex
		INTEGER,
	hzCpHitlessAamModemDiagnose
		INTEGER,
	hzCpHitlessAamModemDiagnosticResult
		DisplayString
}

hzCpHitlessAamModemIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		modem-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpHitlessAamModemEntry 1 }

hzCpHitlessAamModemDiagnose  OBJECT-TYPE
	SYNTAX     INTEGER {
		up (1),
		down (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This service effecting object accepts either 'up' or 'down' to diagnose whether the modulation speed can be increased or decreased respectively for modem. Poll the hzCpHitlessAamModemDiagnosticResult object to determine the results."
	::= { hzCpHitlessAamModemEntry 2 }

hzCpHitlessAamModemDiagnosticResult  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The result from Hitless AAM Diagnostic is read from this object."
	::= { hzCpHitlessAamModemEntry 3 }

hzCpCodeCheckCount  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A counter representing the number of queue-freezup error correction that has encountered in the system."
	::= { hzCpSysDiagnostics 3 }

-- ----------------------------
--   hzCpSysLicensedFeatureGroups
-- ----------------------------

hzCpSysUpgradeFeatureGroupsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSysUpgradeFeatureGroupsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of system upgrade feature groups."
	::= { hzCpSysLicensedFeatureGroups 1 }

hzCpSysUpgradeFeatureGroupsEntry  OBJECT-TYPE
	SYNTAX 	HzCpSysUpgradeFeatureGroupsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A system upgrade feature group entry containing all licensed feature groups."
	INDEX { hzCpUpgradeLicensedFeatureIndex }
	::= { hzCpSysUpgradeFeatureGroupsTable 1 }

HzCpSysUpgradeFeatureGroupsEntry ::= SEQUENCE {
	hzCpUpgradeLicensedFeatureIndex
		INTEGER,
	hzCpUpgradeLicensedFeatureKey
		DisplayString,
	hzCpUpgradeLicensedFeatureCount
		Integer32
}

hzCpUpgradeLicensedFeatureIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		rls (1),
		eoamEcfmLldp (2),
		hitlessAam (3),
		bac (4),
		encryption (5)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each licensed group. "
	::= { hzCpSysUpgradeFeatureGroupsEntry 1 }

hzCpUpgradeLicensedFeatureKey  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The key required for upgrading a feature."
	::= { hzCpSysUpgradeFeatureGroupsEntry 2 }

hzCpUpgradeLicensedFeatureCount  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This object indicates the number of times the licensed feature has been changed successfully."
	::= { hzCpSysUpgradeFeatureGroupsEntry 3 }

hzCpSysDowngradeFeatureGroupsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSysDowngradeFeatureGroupsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of system downgrade system groups."
	::= { hzCpSysLicensedFeatureGroups 2 }

hzCpSysDowngradeFeatureGroupsEntry  OBJECT-TYPE
	SYNTAX 	HzCpSysDowngradeFeatureGroupsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A system downgrade feature group entry containing all licensed feature groups."
	INDEX { hzCpDowngradeLicensedFeatureIndex }
	::= { hzCpSysDowngradeFeatureGroupsTable 1 }

HzCpSysDowngradeFeatureGroupsEntry ::= SEQUENCE {
	hzCpDowngradeLicensedFeatureIndex
		INTEGER,
	hzCpDowngradeLicensedFeature
		INTEGER,
	hzCpDowngradeLicensedFeatureCount
		Integer32,
	hzCpDowngradeLicensedFeatureKey
		DisplayString
}

hzCpDowngradeLicensedFeatureIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		rls (1),
		eoamEcfmLldp (2),
		hitlessAam (3),
		bac (4),
		encryption (5)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each licensed group. "
	::= { hzCpSysDowngradeFeatureGroupsEntry 1 }

hzCpDowngradeLicensedFeature  OBJECT-TYPE
	SYNTAX     INTEGER {
		downgrade (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Downgrading a licensed feature."
	::= { hzCpSysDowngradeFeatureGroupsEntry 2 }

hzCpDowngradeLicensedFeatureCount  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This object indicates the number of times the licensed feature has been downgraded."
	::= { hzCpSysDowngradeFeatureGroupsEntry 3 }

hzCpDowngradeLicensedFeatureKey  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The key generated after downgrading a licensed feature."
	::= { hzCpSysDowngradeFeatureGroupsEntry 4 }

hzCpSysLicensedFeatureGroupsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSysLicensedFeatureGroupsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of licensed feature groups."
	::= { hzCpSysLicensedFeatureGroups 3 }

hzCpSysLicensedFeatureGroupsEntry  OBJECT-TYPE
	SYNTAX 	HzCpSysLicensedFeatureGroupsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A system feature group entry containing all licensed feature groups."
	INDEX { hzCpSysLicensedFeatureGroupIndex }
	::= { hzCpSysLicensedFeatureGroupsTable 1 }

HzCpSysLicensedFeatureGroupsEntry ::= SEQUENCE {
	hzCpSysLicensedFeatureGroupIndex
		INTEGER,
	hzCpSysLicensedFeatureGroup
		DisplayString,
	hzCpSysLicensedFeatureGroupStatus
		INTEGER
}

hzCpSysLicensedFeatureGroupIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		rls (1),
		eoamEcfmLldp (2),
		hitlessAam (3),
		bac (4),
		encryption (5)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each licensed group. "
	::= { hzCpSysLicensedFeatureGroupsEntry 1 }

hzCpSysLicensedFeatureGroup  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The name of the licensed feature group."
	::= { hzCpSysLicensedFeatureGroupsEntry 2 }

hzCpSysLicensedFeatureGroupStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		unlicensed (1),
		licensed (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Specifies whether the feature group is unlicensed or licensed."
	::= { hzCpSysLicensedFeatureGroupsEntry 3 }

-- - ----------------------------
--   hzCpAuthentication
-- ----------------------------   

hzCpPeerAuthenticationUniqueKey  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..34))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The serial number of the peer node that this node will communicate with. 
		 This is only used when authentication mode is set to unique."
	::= { hzCpPeerAuthentication 1 }

hzCpPeerAuthenticationMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		none (1),
		unique (2),
		group (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This sets the authentication mode for the system. If configured for unique 
		     authentication, a node only exchanges user traffic with one other 
		     node. The serial number is used in this case.
		     
		     If configured for group authentication, a node exchanges user 
		     traffic with another node of the same group and uses the 
		     authentication keys.
		     "
	DEFVAL  { none }
	::= { hzCpPeerAuthentication 2 }

hzCpPeerAuthenticationFailureAction  OBJECT-TYPE
	SYNTAX     INTEGER {
		blockTraffic (1),
		passTraffic (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This sets the authentication failure action for the system. 
		
		 Block traffic will block all user traffic including remote 
		 management access.  Pass traffic will allow all user traffic
		 to be sent and recieved
		"
	DEFVAL  { blockTraffic }
	::= { hzCpPeerAuthentication 3 }

hzCpPeerAuthenticationStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		notAuthenticated (1),
		authenticated (2),
		explicitAuthenticationFailure (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Specifies the current authentication status of the system.
		
		 Authenticated means authentication is on and the system
		 has been authenticated, notAuthenticated means authentication
		 is off or the system has not communicated yet with the other node,
		 explicit authentication failure means authentication is on and 
		 authentication has failed"
	::= { hzCpPeerAuthentication 4 }

hzCpPeerAuthenticationGroupKey  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..34))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The serial number of the group that this node will communicate within. 
		This is only used when authentication mode is set to group."
	::= { hzCpPeerAuthentication 5 }

-- 
-- hzCpCalendar
-- 

hzCpDate  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"A string in the format as follows:
		                                                XX/YY/ZZ 
		                                                Where XX = day of month (range 01 to  31) 
		                                                YY = month of year(range 01 to 12) 
		                                                ZZ = year (range 01 - 99)"
	::= { hzCpCalendar 1 }

hzCpTime  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"A string in the format as follows:
		                                                 aa:bb:cc.ddd
		                                                 Where aa = hour of day ( range 00 to 23 ) 
		                                                       bb = minute of hour ( range 00 to 59 ) 
		                                                       cc = second of minute( range 00 to 59 ) 
		                                                       ddd = thousandths of second (range 000 to 999)"
	::= { hzCpCalendar 2 }

hzCpDateAndTimeForLastTimeChange  OBJECT-TYPE
	SYNTAX     DateAndTime
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Date and Time for the last System time adjustment"
	::= { hzCpCalendar 3 }

hzCpLastTimeChange  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Last System time adjustment in hundredths of seconds"
	::= { hzCpCalendar 4 }

hzCpCumulativeTimeChange  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Cumulative System time adjustmen in hundredths of seconds"
	::= { hzCpCalendar 5 }

-- 
-- hzCpSntp
-- 

hzCpSntpEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether SNTP feature is enabled."
	DEFVAL  { enabled }
	::= { hzCpSntp 1 }

hzCpSntpOffset  OBJECT-TYPE
	SYNTAX     Integer32 (-140..140)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"System time offset from GMT in tenths of hours. For example, an offset of 10 indicates 10 tenths, or 1 hour.  An offset of 5 indicates half an hour."
	DEFVAL  { -40 }
	::= { hzCpSntp 2 }

hzCpSntpServerDepTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSntpServerDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Table containing SNTP server entries"
	::= { hzCpSntp 3 }

hzCpSntpServerDepEntry  OBJECT-TYPE
	SYNTAX 	HzCpSntpServerDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Contains the SNTP server information"
	INDEX { hzCpSntpServerIpAddressDep }
	::= { hzCpSntpServerDepTable 1 }

HzCpSntpServerDepEntry ::= SEQUENCE {
	hzCpSntpServerIndexDep
		Integer32,
	hzCpSntpServerIpAddressDep
		IpAddress,
	hzCpSntpServerStatusDep
		INTEGER,
	hzCpSntpServerStratumDep
		Integer32
}

hzCpSntpServerIndexDep  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"A unique value for each server."
	::= { hzCpSntpServerDepEntry 1 }

hzCpSntpServerIpAddressDep  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The IP address of the SNTP server. This object is deprecated by hzCpSntpServerAddress.
		The value is 0.0.0.0 if not available or not applicable."
	::= { hzCpSntpServerDepEntry 2 }

hzCpSntpServerStatusDep  OBJECT-TYPE
	SYNTAX     INTEGER {
		good (1),
		failed (2)
	}
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The status of the SNTP server."
	::= { hzCpSntpServerDepEntry 3 }

hzCpSntpServerStratumDep  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"Specifies the stratum of a server, 0 for failed server"
	::= { hzCpSntpServerDepEntry 4 }

hzCpSntpServerTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSntpServerEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table containing SNTP server entries"
	::= { hzCpSntp 4 }

hzCpSntpServerEntry  OBJECT-TYPE
	SYNTAX 	HzCpSntpServerEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains the SNTP server information"
	INDEX { hzCpSntpServerIndex }
	::= { hzCpSntpServerTable 1 }

HzCpSntpServerEntry ::= SEQUENCE {
	hzCpSntpServerIndex
		Integer32,
	hzCpSntpServerDomain
		InetAddressType,
	hzCpSntpServerAddress
		InetAddress,
	hzCpSntpServerStatus
		INTEGER,
	hzCpSntpServerStratum
		Integer32
}

hzCpSntpServerIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each server."
	::= { hzCpSntpServerEntry 1 }

hzCpSntpServerDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates SNTP server inet address type. Valid types are ipv4 or ipv6. "
	::= { hzCpSntpServerEntry 2 }

hzCpSntpServerAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates SNTP server inet address octet string. "
	::= { hzCpSntpServerEntry 3 }

hzCpSntpServerStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		good (1),
		failed (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The status of the SNTP server."
	::= { hzCpSntpServerEntry 4 }

hzCpSntpServerStratum  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Specifies the stratum of a server, 0 for failed server"
	::= { hzCpSntpServerEntry 5 }

hzCpSntpRestoreDefault  OBJECT-TYPE
	SYNTAX     INTEGER {
		restore (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Restores SNTP servers to default Addresses."
	::= { hzCpSntp 5 }

hzCpSyncEState  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		manual (2),
		auto (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"In manual mode, syncE source will use primary clock source and when the primary source has loss of lock, the syncE goes into holdover. The primary source will be used again after it comes back. 
		 In auto mode, if the current source has trouble, it will try to recover from the other source."
	::= { hzCpSyncE 1 }

hzCpSyncEPrimarySource  OBJECT-TYPE
	SYNTAX     INTEGER {
		p1 (1),
		p2 (2),
		p3 (3),
		p4 (4),
		wp1 (5),
		free-run (6)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Selects which port to be used as syncE master clock source."
	::= { hzCpSyncE 2 }

hzCpSyncESecondarySource  OBJECT-TYPE
	SYNTAX     INTEGER {
		p1 (1),
		p2 (2),
		p3 (3),
		p4 (4),
		wp1 (5),
		free-run (6)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Selects which port to be used as syncE secondary when the master syncE is not available."
	::= { hzCpSyncE 3 }

hzCpSyncEMemberPorts  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Enter a string indicating all SyncE member ports.
		Example: p1 p4 
		Port options: p1 p2 p3 p4 wp1"
	::= { hzCpSyncE 4 }

hzCpSyncEForcedHoldover  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This forces SyncE into holdover mode. After the configurable timeout value, the forced holdover will be over. The forced timeout can be 0 to 300sec, 0 means infinite and default is 30sec.
		An example of the format of the string is as follows: 'on 20' or 'off'.  The second parameter when holdover is the time in sec. The single quote marks i.e. ' ' are not used in the command."
	::= { hzCpSyncE 5 }

hzCpSyncERevertive  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"In revertive mode, if the current source is secondary and the primary is Ok for configured amount of time, the primary will be used. The default revertive time is 10sec.
		An example of the format of the string is as follows: 'on 20' or 'off'.  The second parameter when revertive is turned on is the time in sec. The single quote marks i.e. ' ' are not used in the command."
	::= { hzCpSyncE 6 }

hzCpSyncEClockSource  OBJECT-TYPE
	SYNTAX     INTEGER {
		p1 (1),
		p2 (2),
		p3 (3),
		p4 (4),
		wp1 (5),
		free-run (6)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Retrieves Synce clock source."
	::= { hzCpSyncE 7 }

hzCpSyncEAcquisitionStatus  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Retrieves Synce acquisition status."
	::= { hzCpSyncE 8 }

hzCpSyncEWanderFilter  OBJECT-TYPE
	SYNTAX     INTEGER {
		option1 (1),
		option2 (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"ITU G.8262 specifies different wander filter requirements for networks based on the 2.048 kbit/s hiearchy , option1, vs those based on the 1.544kbit/s hiearchy, option2.  Option 2 specifies a low pass filter bandwidth of 0.1Hz, while option1 specifies a badwidth between 1 and10 Hz.  "
	::= { hzCpSyncE 9 }

hzCpBacRecordAvgPeriod  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Configures the average period used for computing the running average BAC gain and uncompressed ratio. Default average period is set to 10 seconds. "
	::= { hzCpBac 1 }

hzCpBacTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpBacEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table containing BAC entries"
	::= { hzCpBac 2 }

hzCpBacEntry  OBJECT-TYPE
	SYNTAX 	HzCpBacEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains the BAC information for all queues"
	INDEX { hzCpBacQIndex }
	::= { hzCpBacTable 1 }

HzCpBacEntry ::= SEQUENCE {
	hzCpBacQIndex
		INTEGER,
	hzCpBacQEnable
		INTEGER,
	hzCpBacQBlockSize
		Integer32,
	hzCpBacRecordLogging
		INTEGER,
	hzCpBacUncompressedRatio
		DisplayString,
	hzCpBacGain
		DisplayString
}

hzCpBacQIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each of the 8 Queues."
	::= { hzCpBacEntry 1 }

hzCpBacQEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Configure queue compression state to disable/enable."
	::= { hzCpBacEntry 2 }

hzCpBacQBlockSize  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Configure queue compression block size (256...8000). Block size has to be in increments of 64 bytes. Otherwise it will be rounded off to nearest 64 bytes.
		Valid block sizes are <256 512 1024 4032 8000>
		"
	::= { hzCpBacEntry 3 }

hzCpBacRecordLogging  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		" Enables/disables logging of BAC related statistics like Maximum BAC gain and Maximum uncompressed ratio 
		 on a  per queue basis to syslog and flashlog."
	::= { hzCpBacEntry 4 }

hzCpBacUncompressedRatio  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Running average Uncompressed ratio is calculated by collecting n samples of bytes uncompressed and bytes in data over specified average period in percentage."
	::= { hzCpBacEntry 5 }

hzCpBacGain  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The Running Average BAC gain is the sum of all bytes getting into the compressor divided by the sum of all bytes compressed and bytes uncompressed calculated over an average period."
	::= { hzCpBacEntry 6 }

hzCpBwVsmVendorOui  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"As string representing the vendor OUI code. 
		Example: 00-00-0C for Cisco"
	::= { hzCpBandwidthVsm 1 }

hzCpBwVsmMdLevel  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The Maintenanc level of Bandwidth VSM. The valid range is between 0-7. By default, the BW-VSM is associated with Maintenance Level 0."
	::= { hzCpBandwidthVsm 2 }

hzCpBwVsmWaitTime  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The wait time before sending vendor specific message."
	::= { hzCpBandwidthVsm 3 }

hzCpBwVsmPeriod  OBJECT-TYPE
	SYNTAX     INTEGER {
		one-sec (1),
		ten-sec (2),
		one-min (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This indicates the VSM transmission period. The default value is 10 seconds."
	::= { hzCpBandwidthVsm 4 }

hzCpBwVsmVlanTag  OBJECT-TYPE
	SYNTAX     INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Bandwidth VSM can be configured to be transmitted with a configurable valid VLAN tag. By default, the BW-VSM will be sent untagged."
	::= { hzCpBandwidthVsm 5 }

hzCpBwVsmVlanId  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This indicates the VSM VLAN Id."
	::= { hzCpBandwidthVsm 6 }

hzCpBwVsmVlanPriority  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This indicates the VSM priority. The valid range is between 1-7 and the default priority is 7."
	::= { hzCpBandwidthVsm 7 }

hzCpBwVsmPortList  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The vendor specific message will only be sent out through the specified ports in the list.
		Port options: a comma-separted list of ports p1 to p4
		Example: p1,p2"
	::= { hzCpBandwidthVsm 8 }

hzCpBwVsmState  OBJECT-TYPE
	SYNTAX     INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This enables or disables vendor specific messaging."
	::= { hzCpBandwidthVsm 9 }

hzCpPmRspiThresholdTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpPmRspiThresholdEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Performance Monitoring table for Radio Synchronous Physical Interface thresholds."
	::= { hzCpPM 1 }

hzCpPmRspiThresholdEntry  OBJECT-TYPE
	SYNTAX 	HzCpPmRspiThresholdEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains information for Radio Synchronous Physical Interface thresholds."
	INDEX { hzCpPmRspiThrIndex }
	::= { hzCpPmRspiThresholdTable 1 }

HzCpPmRspiThresholdEntry ::= SEQUENCE {
	hzCpPmRspiThrIndex
		Integer32,
	hzCpPmRLT1
		Integer32,
	hzCpPmRLT2
		Integer32,
	hzCpPmRLT3
		Integer32,
	hzCpPmRLT4
		Integer32,
	hzCpPmTLT1
		Integer32,
	hzCpPmTLT2
		Integer32,
	hzCpPmRspiThrRowStatus
		RowStatus
}

hzCpPmRspiThrIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Index to PM RSPI Threshold table."
	::= { hzCpPmRspiThresholdEntry 1 }

hzCpPmRLT1  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Receive Level Threshold 1, represents the number of seconds below the first threshold."
	::= { hzCpPmRspiThresholdEntry 2 }

hzCpPmRLT2  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Receive Level Threshold 2, represents the number of seconds below the second threshold."
	::= { hzCpPmRspiThresholdEntry 3 }

hzCpPmRLT3  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Receive Level Threshold 3, represents the number of seconds below the third threshold."
	::= { hzCpPmRspiThresholdEntry 4 }

hzCpPmRLT4  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Receive Level Threshold 4, represents the number of seconds below the fourth threshold."
	::= { hzCpPmRspiThresholdEntry 5 }

hzCpPmTLT1  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Transmit Level Threshold 1, represents the number of seconds below the first threshold."
	::= { hzCpPmRspiThresholdEntry 6 }

hzCpPmTLT2  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Transmit Level Threshold 2, represents the number of seconds below the second threshold."
	::= { hzCpPmRspiThresholdEntry 7 }

hzCpPmRspiThrRowStatus  OBJECT-TYPE
	SYNTAX     RowStatus
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This object is used for applying all RSPI thresholds simultaneously."
	::= { hzCpPmRspiThresholdEntry 8 }

hzCpPmAdvThresholdTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpPmAdvThresholdEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Performance Monitoring table for Capacity thresholds."
	::= { hzCpPM 2 }

hzCpPmAdvThresholdEntry  OBJECT-TYPE
	SYNTAX 	HzCpPmAdvThresholdEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains information for Capacity thresholds."
	INDEX { hzCpPmAdvThrIndex }
	::= { hzCpPmAdvThresholdTable 1 }

HzCpPmAdvThresholdEntry ::= SEQUENCE {
	hzCpPmAdvThrIndex
		Integer32,
	hzCpPmAdvTxHitsT1
		Integer32,
	hzCpPmAdvRxHitsT1
		Integer32,
	hzCpPmAdvRowStatus
		RowStatus
}

hzCpPmAdvThrIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Index to PM Capacity Threshold table."
	::= { hzCpPmAdvThresholdEntry 1 }

hzCpPmAdvTxHitsT1  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Transmit Hits Threshold 1."
	::= { hzCpPmAdvThresholdEntry 2 }

hzCpPmAdvRxHitsT1  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Receive Hits Threshold 1."
	::= { hzCpPmAdvThresholdEntry 3 }

hzCpPmAdvRowStatus  OBJECT-TYPE
	SYNTAX     RowStatus
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This object is used for applying all Capacity thresholds simultaneously."
	::= { hzCpPmAdvThresholdEntry 4 }

-- 
-- NetworkManagement 
--   

hzCpMacAddress  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The MAC Address of the system."
	::= { hzCpManagement 1 }

hzCpTelnetAccessMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether Telnet access is allowed."
	DEFVAL  { enabled }
	::= { hzCpManagement 2 }

hzCpSshAccessMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether ssh access is allowed."
	DEFVAL  { enabled }
	::= { hzCpManagement 3 }

hzCpNetMgmtPortList  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Describes the network management interface connection options. Enter a string indicating the management port.
		
		Example: p3
		Port options: p1 or p2 or p3 or p4 or dp1 or dp2 or dp3 or dp4
		
		Note: Need to set hzCpNetMgmtAppyToSystem if you want the changes to take effect in the system."
	::= { hzCpNetMgmtInterfaceVlan 1 }

hzCpNetMgmtVlanTagId  OBJECT-TYPE
	SYNTAX     Integer32 (1..4095)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates the VLAN Tag Id (1...4095).
		
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmtInterfaceVlan 2 }

hzCpNetMgmtVlanTagPriority  OBJECT-TYPE
	SYNTAX     Integer32 (0..7)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The priority for VLAN tagging (0...7)
		
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmtInterfaceVlan 3 }

hzCpNetMgmtVlanTagging  OBJECT-TYPE
	SYNTAX     INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Disables or enables VLAN tagging. VLAN tagging is the practice of inserting a VLAN Id into a packet header in order to identify which VLAN the packets belongs to.
		
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmtInterfaceVlan 4 }

hzCpNetMgmtDscpPriority  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The Differentiated Serices Code Point (DSCP) priority to classify and prioritize types of traffic (0...63)
		
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmtInterfaceVlan 5 }

hzCpIpAddress  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The IP Address of the system.
		
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmtIpv4 1 }

hzCpSubnetMask  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The subnet mask for the system.
		
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmtIpv4 2 }

hzCpDefaultGateway  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The default gateway for the system.
		
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmtIpv4 3 }

hzCpIpv6Domain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates ipv6 inet address type. "
	::= { hzCpNetMgmttIpv6 1 }

hzCpIpv6Address  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates ipv6 inet address octet string. 
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmttIpv6 2 }

hzCpIpv6Prefix  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates ipv6 prefix. 
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmttIpv6 3 }

hzCpIpv6GatewayDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates ipv6 gateway inet address type. "
	::= { hzCpNetMgmttIpv6 4 }

hzCpIpv6GatewayAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates ipv6 gateway inet address octet string. 
		Note: Changing this option doesn't get applied to the system unless hzCpNetMgmtAppyToSystem is set."
	::= { hzCpNetMgmttIpv6 5 }

hzCpIpv6LinkLocalDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates ipv6 link local inet address type. "
	::= { hzCpNetMgmttIpv6 6 }

hzCpIpv6LinkLocalAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates ipv6 link local inet address octet string. "
	::= { hzCpNetMgmttIpv6 7 }

hzCpNetMgmtApplyToSystem  OBJECT-TYPE
	SYNTAX     INTEGER {
		apply (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"If system allows, setting this oid applies all network management interface changes to the system and a system reset can be avoided."
	::= { hzCpNetworkManagementInterface 4 }

-- 
-- SNMP MANAGERS
-- 

hzCpSnmpUserAccess  OBJECT-TYPE
	SYNTAX     INTEGER {
		explicitManagers (1),
		any (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the user access to the system. If access is set to 'explicitManagers' then only managers with ip addresses set in snmpManagersTable will be able to gain SNMP access to the system.  If access is set to 'any' then any manager with any-community-name will be able to gain SNMP access to the system"
	::= { hzCpSnmp 1 }

hzCpSnmpManagerAnyCommunityName  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The community string name used by the 'any' manager."
	::= { hzCpSnmp 2 }

hzCpSnmpSetRequests  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates whether SNMP SET requests are allowed."
	::= { hzCpSnmp 3 }

hzCpSnmpManagersDepTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSnmpManagersDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Table containing snmp manager entries"
	::= { hzCpSnmp 4 }

hzCpSnmpManagersDepEntry  OBJECT-TYPE
	SYNTAX 	HzCpSnmpManagersDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Contains the snmp manager information"
	INDEX { hzCpSnmpManagersIndexDep }
	::= { hzCpSnmpManagersDepTable 1 }

HzCpSnmpManagersDepEntry ::= SEQUENCE {
	hzCpSnmpManagersIndexDep
		Integer32,
	hzCpSnmpManagerIpAddressDep
		IpAddress,
	hzCpSnmpManagerCommunityNameDep
		DisplayString,
	hzCpSnmpManagerActivatedDep
		INTEGER
}

hzCpSnmpManagersIndexDep  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"A unique value for each manager."
	::= { hzCpSnmpManagersDepEntry 1 }

hzCpSnmpManagerIpAddressDep  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"This object is deprecated by hzCpSnmpManagerAddress. The value is 0.0.0.0 if not available or not applicable."
	::= { hzCpSnmpManagersDepEntry 2 }

hzCpSnmpManagerCommunityNameDep  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The community string name used by the manager."
	::= { hzCpSnmpManagersDepEntry 3 }

hzCpSnmpManagerActivatedDep  OBJECT-TYPE
	SYNTAX     INTEGER {
		notActive (1),
		active (2)
	}
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"Specifies whether a specific manager is allowed to access the system. This object is deprecated by hzCpSnmpManagersActivated. The value is 0.0.0.0 if not available or not applicable."
	::= { hzCpSnmpManagersDepEntry 4 }

hzCpSnmpV3ManagersTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSnmpV3ManagersEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table containing snmp V3 manager entries"
	::= { hzCpSnmp 5 }

hzCpSnmpV3ManagersEntry  OBJECT-TYPE
	SYNTAX 	HzCpSnmpV3ManagersEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains the snmp v3 manager information"
	INDEX { hzCpSnmpV3ManagersIndex }
	::= { hzCpSnmpV3ManagersTable 1 }

HzCpSnmpV3ManagersEntry ::= SEQUENCE {
	hzCpSnmpV3ManagersIndex
		Integer32,
	hzCpSnmpV3ManagerUserName
		DisplayString,
	hzCpSnmpV3ManagerAuthProtocol
		INTEGER,
	hzCpSnmpV3ManagerAuthPassword
		DisplayString,
	hzCpSnmpV3ManagerPrivProtocol
		INTEGER,
	hzCpSnmpV3ManagerPrivPassword
		DisplayString,
	hzCpSnmpV3ManagerActivated
		INTEGER
}

hzCpSnmpV3ManagersIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each v3 manager."
	::= { hzCpSnmpV3ManagersEntry 1 }

hzCpSnmpV3ManagerUserName  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The user name of the v3 Manager."
	::= { hzCpSnmpV3ManagersEntry 2 }

hzCpSnmpV3ManagerAuthProtocol  OBJECT-TYPE
	SYNTAX     INTEGER {
		noAuth (1),
		md5 (2),
		sha (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The authentication alogorithm used by the v3 manager."
	::= { hzCpSnmpV3ManagersEntry 3 }

hzCpSnmpV3ManagerAuthPassword  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The authentication password of the v3 Manager."
	::= { hzCpSnmpV3ManagersEntry 4 }

hzCpSnmpV3ManagerPrivProtocol  OBJECT-TYPE
	SYNTAX     INTEGER {
		noPriv (1),
		des (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The privacy encryption alogorithm used by the v3 manager."
	::= { hzCpSnmpV3ManagersEntry 5 }

hzCpSnmpV3ManagerPrivPassword  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The privacy password of the v3 Manager."
	::= { hzCpSnmpV3ManagersEntry 6 }

hzCpSnmpV3ManagerActivated  OBJECT-TYPE
	SYNTAX     INTEGER {
		notActive (1),
		active (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether a specific v3 manager is allowed to access the system"
	::= { hzCpSnmpV3ManagersEntry 7 }

hzCpSnmpAccessMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		v1 (2),
		v2c (3),
		v3 (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the running SNMP version on the system."
	::= { hzCpSnmp 6 }

hzCpSnmpManagersTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSnmpManagersEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table containing snmp manager entries"
	::= { hzCpSnmp 7 }

hzCpSnmpManagersEntry  OBJECT-TYPE
	SYNTAX 	HzCpSnmpManagersEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains the snmp manager information"
	INDEX { hzCpSnmpManagersIndex }
	::= { hzCpSnmpManagersTable 1 }

HzCpSnmpManagersEntry ::= SEQUENCE {
	hzCpSnmpManagersIndex
		Integer32,
	hzCpSnmpManagerDomain
		InetAddressType,
	hzCpSnmpManagerAddress
		InetAddress,
	hzCpSnmpManagerCommunityName
		DisplayString,
	hzCpSnmpManagersPrefixLength
		InetAddressPrefixLength,
	hzCpSnmpManagerActivated
		INTEGER
}

hzCpSnmpManagersIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each manager."
	::= { hzCpSnmpManagersEntry 1 }

hzCpSnmpManagerDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates snmp manager inet address type. Valid types are ipv4 or ipv6. "
	::= { hzCpSnmpManagersEntry 2 }

hzCpSnmpManagerAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates snmp manager inet address octet string. "
	::= { hzCpSnmpManagersEntry 3 }

hzCpSnmpManagerCommunityName  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The community string name used by the manager."
	::= { hzCpSnmpManagersEntry 4 }

hzCpSnmpManagersPrefixLength  OBJECT-TYPE
	SYNTAX     InetAddressPrefixLength
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The Ip Address prefix length. The valid range for IPV4 Address is 1-32 and for IPV6 is 1-128."
	::= { hzCpSnmpManagersEntry 5 }

hzCpSnmpManagerActivated  OBJECT-TYPE
	SYNTAX     INTEGER {
		notActive (1),
		active (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether a specific manager is allowed to access the system. This object deprecates hzCpSnmpManagerActivatedDep."
	::= { hzCpSnmpManagersEntry 32 }

-- 
-- TRAP INFORMATION HOSTS, ENABLE/DISABLE
--	

hzCpSnmpTrapHostDepTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSnmpTrapHostDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Table containing SNMP trap host entries."
	::= { hzCpTrapConfig 1 }

hzCpSnmpTrapHostDepEntry  OBJECT-TYPE
	SYNTAX 	HzCpSnmpTrapHostDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"The snmp trap host entry containing all the trap host information"
	INDEX { hzCpSnmpTrapHostIndexDep }
	::= { hzCpSnmpTrapHostDepTable 1 }

HzCpSnmpTrapHostDepEntry ::= SEQUENCE {
	hzCpSnmpTrapHostIndexDep
		Integer32,
	hzCpSnmpTrapHostIpAddressDep
		IpAddress,
	hzCpSnmpTrapHostCommunityNameDep
		DisplayString,
	hzCpSnmpTrapHostActivatedDep
		INTEGER
}

hzCpSnmpTrapHostIndexDep  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"A unique value for each trap host. "
	::= { hzCpSnmpTrapHostDepEntry 1 }

hzCpSnmpTrapHostIpAddressDep  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"This object is deprecated by hzCpSnmpV3TrapHostAddress. The value is 0.0.0.0 if not available or not applicable."
	::= { hzCpSnmpTrapHostDepEntry 2 }

hzCpSnmpTrapHostCommunityNameDep  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The community string name used in Traps."
	::= { hzCpSnmpTrapHostDepEntry 3 }

hzCpSnmpTrapHostActivatedDep  OBJECT-TYPE
	SYNTAX     INTEGER {
		notActive (1),
		active (2)
	}
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"This object is deprecated by hzCpSnmpTrapHostActivated. The value is 0.0.0.0 if not available or not applicable."
	::= { hzCpSnmpTrapHostDepEntry 4 }

-- 
-- SNMP V3 TRAP HOST TABLE
-- 

hzCpSnmpV3TrapHostsDepTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSnmpV3TrapHostsDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Table containing snmp V3 trap host entries"
	::= { hzCpTrapConfig 2 }

hzCpSnmpV3TrapHostsDepEntry  OBJECT-TYPE
	SYNTAX 	HzCpSnmpV3TrapHostsDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Contains the snmp v3 trap host information"
	INDEX { hzCpSnmpV3TrapHostsIndexDep }
	::= { hzCpSnmpV3TrapHostsDepTable 1 }

HzCpSnmpV3TrapHostsDepEntry ::= SEQUENCE {
	hzCpSnmpV3TrapHostsIndexDep
		Integer32,
	hzCpSnmpV3TrapHostIpAddressDep
		IpAddress,
	hzCpSnmpV3TrapHostUserNameDep
		DisplayString,
	hzCpSnmpV3TrapHostAuthProtocolDep
		INTEGER,
	hzCpSnmpV3TrapHostAuthPasswordDep
		DisplayString,
	hzCpSnmpV3TrapHostPrivProtocolDep
		INTEGER,
	hzCpSnmpV3TrapHostPrivPasswordDep
		DisplayString,
	hzCpSnmpV3TrapHostActivatedDep
		INTEGER
}

hzCpSnmpV3TrapHostsIndexDep  OBJECT-TYPE
	SYNTAX     Integer32 (0..4)
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"A unique value for each v3 trap host."
	::= { hzCpSnmpV3TrapHostsDepEntry 1 }

hzCpSnmpV3TrapHostIpAddressDep  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"This object is deprecated by hzCpSnmpV3TrapHostAddress. The value is 0.0.0.0 if not available or not applicable."
	::= { hzCpSnmpV3TrapHostsDepEntry 2 }

hzCpSnmpV3TrapHostUserNameDep  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The user name of the v3 trap host."
	::= { hzCpSnmpV3TrapHostsDepEntry 3 }

hzCpSnmpV3TrapHostAuthProtocolDep  OBJECT-TYPE
	SYNTAX     INTEGER {
		noAuth (1),
		md5 (2),
		sha (3)
	}
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The authentication alogorithm used by the v3 trap host."
	::= { hzCpSnmpV3TrapHostsDepEntry 4 }

hzCpSnmpV3TrapHostAuthPasswordDep  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The authentication password of the v3 trap host."
	::= { hzCpSnmpV3TrapHostsDepEntry 5 }

hzCpSnmpV3TrapHostPrivProtocolDep  OBJECT-TYPE
	SYNTAX     INTEGER {
		noPriv (1),
		des (2)
	}
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The privacy encryption alogorithm used by the v3 trap host."
	::= { hzCpSnmpV3TrapHostsDepEntry 6 }

hzCpSnmpV3TrapHostPrivPasswordDep  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The privacy password of the v3 trap host."
	::= { hzCpSnmpV3TrapHostsDepEntry 7 }

hzCpSnmpV3TrapHostActivatedDep  OBJECT-TYPE
	SYNTAX     INTEGER {
		notActive (1),
		active (2)
	}
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"Specifies whether a specific v3 trap host is allowed to access the system"
	::= { hzCpSnmpV3TrapHostsDepEntry 8 }

hzCpColdStartTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 1 }

hzCpLinkDownTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 2 }

hzCpPeerAuthenticationFailureTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 3 }

hzCpHitlessAamConfigMismatchTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 4 }

hzCpHitlessAamModulationLoweredTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 5 }

hzCpHitlessAamModulationChangedEventTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	::= { hzCpTrapEnable 6 }

hzCpAtpcConfigMismatchTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 7 }

hzCpAtpcAutoDisabledTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 8 }

hzCpNoSntpServersReachableTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 9 }

hzCpFrequencyFileInvalidTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 10 }

hzCpAggregateDroppedFramesThresholdTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 11 }

hzCpQueueDroppedFramesThresholdTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 12 }

hzCpBwUtilizationThresholdTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 13 }

hzCpQueueDepthThresholdTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 14 }

hzCpRlsConfigMismatchTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 15 }

hzCpRlsShutdownActivatedTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 16 }

hzCpRlsQueueBasedShutdownActivatedTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 17 }

hzCpModemRxLossOfSignalLockTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 18 }

hzCpModemSnrBelowThresholdTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 19 }

hzCpModemEqualizerStressThresholdTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 20 }

hzCpRslBelowThresholdTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 21 }

hzCpRadioSynthLostLockTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 22 }

hzCpRadioCalTableNotAvailableTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 23 }

hzCpRadioDrainCurrentOutOfLimitTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 24 }

hzCpRadioPowerAmplifierTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 25 }

hzCpTemperatureOutOfLimitTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 26 }

hzCpRedundancyConfigMismatchTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 27 }

hzCpRedundancyActiveOnSecondaryTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 28 }

hzCpRedundancyOperatingInForcedSwitchTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 29 }

hzCpRedundancyEnetCrossLinkTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 30 }

hzCpRedundancyActiveUsingPartnerWLinkTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 31 }

hzCpRedundancyStandbyWLinkInUseTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 32 }

hzCpRedundancyStandbyOnPrimaryTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 33 }

hzCpX2DeliveringHalfCapacityTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 34 }

hzCpBncCableSignalNotDetectedTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 35 }

hzCpEthernetSpeedReducedTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 36 }

hzCpSynceLostLockTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	::= { hzCpTrapEnable 37 }

hzCpSynceSecondarySourceInUseTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	::= { hzCpTrapEnable 38 }

hzCpUserSessionTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not. "
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 39 }

hzCpInvalidSystemConfigTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 40 }

hzCpMibChangeNotSavedTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 41 }

hzCpTransmitterLossOfSyncTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 42 }

hzCpRadioLinearityCalErrorTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	::= { hzCpTrapEnable 43 }

hzCpSynceConfigMismatchTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 44 }

hzCpCryptoPowerUpTestsFailedTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { enabled }
	::= { hzCpTrapEnable 45 }

hzCpCryptoConfigMismatchTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	DEFVAL  { disabled }
	::= { hzCpTrapEnable 46 }

hzCpSystemTimeChangeTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not"
	::= { hzCpTrapEnable 47 }

hzCpCodeCheckTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not."
	::= { hzCpTrapEnable 48 }

hzCpConfigChangedTrap  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether this trap is to be sent or not."
	::= { hzCpTrapEnable 49 }

hzCpSnmpTrapHostTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSnmpTrapHostEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table containing SNMP trap host entries."
	::= { hzCpTrapConfig 4 }

hzCpSnmpTrapHostEntry  OBJECT-TYPE
	SYNTAX 	HzCpSnmpTrapHostEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"The snmp trap host entry containing all the trap host information"
	INDEX { hzCpSnmpTrapHostIndex }
	::= { hzCpSnmpTrapHostTable 1 }

HzCpSnmpTrapHostEntry ::= SEQUENCE {
	hzCpSnmpTrapHostIndex
		Integer32,
	hzCpSnmpTrapHostDomain
		InetAddressType,
	hzCpSnmpTrapHostAddress
		InetAddress,
	hzCpSnmpTrapHostCommunityName
		DisplayString,
	hzCpSnmpTrapHostActivated
		INTEGER
}

hzCpSnmpTrapHostIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each trap host. "
	::= { hzCpSnmpTrapHostEntry 1 }

hzCpSnmpTrapHostDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates snmp trap host inet address type. Valid types are ipv4 or ipv6. "
	::= { hzCpSnmpTrapHostEntry 2 }

hzCpSnmpTrapHostAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates snmp trap host inet address octet string. This object deprecates hzCpSnmpTrapHostIpAddressDep."
	::= { hzCpSnmpTrapHostEntry 3 }

hzCpSnmpTrapHostCommunityName  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The community string name used in Traps."
	::= { hzCpSnmpTrapHostEntry 4 }

hzCpSnmpTrapHostActivated  OBJECT-TYPE
	SYNTAX     INTEGER {
		notActive (1),
		active (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether traps are to be sent to this specific host or not. This object deprecates hzCpSnmpTrapHostActivatedDep."
	::= { hzCpSnmpTrapHostEntry 32 }

-- 
-- SNMP V3 TRAP HOST TABLE
-- 

hzCpSnmpV3TrapHostsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSnmpV3TrapHostsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table containing snmp V3 trap host entries"
	::= { hzCpTrapConfig 5 }

hzCpSnmpV3TrapHostsEntry  OBJECT-TYPE
	SYNTAX 	HzCpSnmpV3TrapHostsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains the snmp v3 trap host information"
	INDEX { hzCpSnmpV3TrapHostsIndex }
	::= { hzCpSnmpV3TrapHostsTable 1 }

HzCpSnmpV3TrapHostsEntry ::= SEQUENCE {
	hzCpSnmpV3TrapHostsIndex
		Integer32,
	hzCpSnmpV3TrapHostDomain
		InetAddressType,
	hzCpSnmpV3TrapHostAddress
		InetAddress,
	hzCpSnmpV3TrapHostUserName
		DisplayString,
	hzCpSnmpV3TrapHostAuthProtocol
		INTEGER,
	hzCpSnmpV3TrapHostAuthPassword
		DisplayString,
	hzCpSnmpV3TrapHostPrivProtocol
		INTEGER,
	hzCpSnmpV3TrapHostPrivPassword
		DisplayString,
	hzCpSnmpV3TrapHostActivated
		INTEGER
}

hzCpSnmpV3TrapHostsIndex  OBJECT-TYPE
	SYNTAX     Integer32 (0..4)
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each v3 trap host."
	::= { hzCpSnmpV3TrapHostsEntry 1 }

hzCpSnmpV3TrapHostDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates v3 trap host inet address type. Valid types are ipv4 or ipv6. "
	::= { hzCpSnmpV3TrapHostsEntry 2 }

hzCpSnmpV3TrapHostAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates snmp v3 trap host inet address octet string. This object deprecates hzCpSnmpV3TrapHostIpAddressDep."
	::= { hzCpSnmpV3TrapHostsEntry 3 }

hzCpSnmpV3TrapHostUserName  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The user name of the v3 trap host."
	::= { hzCpSnmpV3TrapHostsEntry 4 }

hzCpSnmpV3TrapHostAuthProtocol  OBJECT-TYPE
	SYNTAX     INTEGER {
		noAuth (1),
		md5 (2),
		sha (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The authentication alogorithm used by the v3 trap host."
	::= { hzCpSnmpV3TrapHostsEntry 5 }

hzCpSnmpV3TrapHostAuthPassword  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The authentication password of the v3 trap host."
	::= { hzCpSnmpV3TrapHostsEntry 6 }

hzCpSnmpV3TrapHostPrivProtocol  OBJECT-TYPE
	SYNTAX     INTEGER {
		noPriv (1),
		des (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The privacy encryption alogorithm used by the v3 trap host."
	::= { hzCpSnmpV3TrapHostsEntry 7 }

hzCpSnmpV3TrapHostPrivPassword  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The privacy password of the v3 trap host."
	::= { hzCpSnmpV3TrapHostsEntry 8 }

hzCpSnmpV3TrapHostActivated  OBJECT-TYPE
	SYNTAX     INTEGER {
		notActive (1),
		active (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies whether v3 traps are to be sent to this specific host or not. This object deprecates hzCpSnmpV3TrapHostActivatedDep."
	::= { hzCpSnmpV3TrapHostsEntry 32 }

-- 
-- hzCpRadius
-- 

hzCpRadiusSuperUserAuthentication  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"If strict mode is off, the super user can login when radius is turned off or unreachable."
	DEFVAL  { off }
	::= { hzCpRadius 1 }

hzCpRadiusServerTimeOut  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		" Timeout period for authentication requests"
	::= { hzCpRadius 2 }

hzCpRadiusServerDeadTime  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		" Time to declare an unresponsive server 'dead'"
	::= { hzCpRadius 3 }

hzCpRadiusServerReTransmit  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Maximum number of retransmits to an unresponsive server"
	::= { hzCpRadius 4 }

hzCpRadiusServerDepTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRadiusServerDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Table containing radius server entries"
	::= { hzCpRadius 5 }

hzCpRadiusServerDepEntry  OBJECT-TYPE
	SYNTAX 	HzCpRadiusServerDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"Contains the SNTP server information"
	INDEX { hzCpRadiusServerIndexDep }
	::= { hzCpRadiusServerDepTable 1 }

HzCpRadiusServerDepEntry ::= SEQUENCE {
	hzCpRadiusServerIndexDep
		Integer32,
	hzCpRadiusCfgdHostIpAddressDep
		IpAddress,
	hzCpRadiusCfgdHostKeyDep
		DisplayString
}

hzCpRadiusServerIndexDep  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"A unique value for each radius server."
	::= { hzCpRadiusServerDepEntry 1 }

hzCpRadiusCfgdHostIpAddressDep  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The IP address of the configured radius server. This object is deprecated by hzCpRadiusCfgdHostAddress. The value is 0.0.0.0 if not available or not applicable.
		"
	::= { hzCpRadiusServerDepEntry 2 }

hzCpRadiusCfgdHostKeyDep  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"The key of the configured radius server."
	::= { hzCpRadiusServerDepEntry 3 }

hzCpRadiusServerTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRadiusServerEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Table containing radius server entries"
	::= { hzCpRadius 6 }

hzCpRadiusServerEntry  OBJECT-TYPE
	SYNTAX 	HzCpRadiusServerEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Contains the SNTP server information"
	INDEX { hzCpRadiusServerIndex }
	::= { hzCpRadiusServerTable 1 }

HzCpRadiusServerEntry ::= SEQUENCE {
	hzCpRadiusServerIndex
		Integer32,
	hzCpRadiusCfgdHostDomain
		InetAddressType,
	hzCpRadiusCfgdHostAddress
		InetAddress,
	hzCpRadiusCfgdHostKey
		DisplayString
}

hzCpRadiusServerIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each radius server."
	::= { hzCpRadiusServerEntry 1 }

hzCpRadiusCfgdHostDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates configured radius radius inet address type. Valid types are ipv4 or ipv6. "
	::= { hzCpRadiusServerEntry 2 }

hzCpRadiusCfgdHostAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates configured radius server inet address octet string. This object deprecates hzCpRadiusCfgdHostIpAddressDep."
	::= { hzCpRadiusServerEntry 3 }

hzCpRadiusCfgdHostKey  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The key of the configured radius server."
	::= { hzCpRadiusServerEntry 4 }

-- ------------------------------
-- hzCpManagementSessions       
-- ------------------------------

hzCpUserSessionUserTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpUserSessionUserEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of user sessions"
	::= { hzCpManagementSessions 1 }

hzCpUserSessionUserEntry  OBJECT-TYPE
	SYNTAX 	HzCpUserSessionUserEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"User session entry"
	INDEX { hzCpUserSessionUserIndex }
	::= { hzCpUserSessionUserTable 1 }

HzCpUserSessionUserEntry ::= SEQUENCE {
	hzCpUserSessionUserIndex
		INTEGER,
	hzCpUserSessionUserName
		DisplayString,
	hzCpUserSessionUserConnectionType
		DisplayString,
	hzCpUserSessionUserState
		INTEGER
}

hzCpUserSessionUserIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		user1 (1),
		user2 (2),
		user3 (3),
		user4 (4),
		user5 (5),
		user6 (6),
		user7 (7),
		user8 (8),
		user9 (9),
		user10 (10),
		user11 (11),
		user12 (12),
		user13 (13),
		user14 (14),
		user15 (15),
		user16 (16),
		user17 (17),
		user18 (18),
		user19 (19),
		user20 (20)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each user."
	::= { hzCpUserSessionUserEntry 1 }

hzCpUserSessionUserName  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The username of a management session using Telnet or HTTP. The session state variable must be checked to determine if the management session is currently in progress."
	::= { hzCpUserSessionUserEntry 2 }

hzCpUserSessionUserConnectionType  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Either telnet, ssh, http or https. Ethernet connection may be through any physical port(s) dedicated to management of the equipment."
	::= { hzCpUserSessionUserEntry 3 }

hzCpUserSessionUserState  OBJECT-TYPE
	SYNTAX     INTEGER {
		informationNotAvailable (1),
		sessionTerminated (2),
		sessionInProgress (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The state of the session. The session is inProgress if the user is currently logged into the system. The session is terminated if the user has logged out of the system."
	::= { hzCpUserSessionUserEntry 4 }

-- 
-- HTTPS group
-- 

hzCpHttpEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether HTTP is enabled."
	DEFVAL  { disabled }
	::= { hzCpHttp 1 }

hzCpHttpSecureCertificateStatus  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..100))
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Returns the HTTPS certificate status"
	::= { hzCpHttpSecure 1 }

hzCpHttpSecureAccessForAdminUsers  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"the HTTPS access requirement for Admin user group."
	::= { hzCpHttpSecure 2 }

hzCpHttpSecureAccessForNocUsers  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"the HTTPS access requirement for Noc user group."
	::= { hzCpHttpSecure 3 }

hzCpHttpSecureAccessForSuperUsers  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"the HTTPS access requirement for Super user group."
	::= { hzCpHttpSecure 4 }

hzCpEnetPortConfigTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpEnetPortConfigEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table for enet port configuration"
	::= { hzCpEnetPort 1 }

hzCpEnetPortConfigEntry  OBJECT-TYPE
	SYNTAX 	HzCpEnetPortConfigEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry for enet port configuration"
	INDEX { hzCpEnetPortIndex }
	::= { hzCpEnetPortConfigTable 1 }

HzCpEnetPortConfigEntry ::= SEQUENCE {
	hzCpEnetPortIndex
		INTEGER,
	hzCpEnetPortName
		DisplayString,
	hzCpEnetPortAutoNegotiation
		INTEGER,
	hzCpEnetPortSpeed
		INTEGER,
	hzCpEnetPortDuplex
		INTEGER,
	hzCpEnetPortMedia
		INTEGER,
	hzCpEnetPortAdminState
		INTEGER,
	hzCpEnetPortPauseFrame
		INTEGER,
	hzCpEnetPortMaxFrameSize
		Integer32,
	hzCpEnetPortOpticalTransceiverState
		INTEGER,
	hzCpEnetPortState
		INTEGER,
	hzCpEnetPayloadState
		INTEGER
}

hzCpEnetPortIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		enet-port-1 (1),
		enet-port-2 (2),
		enet-port-3 (3),
		enet-port-4 (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each enet port."
	::= { hzCpEnetPortConfigEntry 1 }

-- 
-- PORT CONFIG
--   

hzCpEnetPortName  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Describes the port interface name"
	::= { hzCpEnetPortConfigEntry 2 }

hzCpEnetPortAutoNegotiation  OBJECT-TYPE
	SYNTAX     INTEGER {
		on (1),
		off (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Turns auto-negotiation on or off for Ethernet Port. Turning off results in the interface defaulting to 100BaseT, Full Duplex. "
	::= { hzCpEnetPortConfigEntry 3 }

hzCpEnetPortSpeed  OBJECT-TYPE
	SYNTAX     INTEGER {
		x10M (1),
		x100M (2),
		x1000M (3),
		auto (4)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Configure the port interface speed ."
	::= { hzCpEnetPortConfigEntry 4 }

hzCpEnetPortDuplex  OBJECT-TYPE
	SYNTAX     INTEGER {
		full (1),
		half (2),
		auto (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Configure the port interface duplex."
	::= { hzCpEnetPortConfigEntry 5 }

hzCpEnetPortMedia  OBJECT-TYPE
	SYNTAX     INTEGER {
		copper (1),
		fiber (2),
		auto (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Configure the port interface media type."
	::= { hzCpEnetPortConfigEntry 6 }

hzCpEnetPortAdminState  OBJECT-TYPE
	SYNTAX     INTEGER {
		on (1),
		off (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Enable or disable the admin status of the port."
	::= { hzCpEnetPortConfigEntry 7 }

hzCpEnetPortPauseFrame  OBJECT-TYPE
	SYNTAX     INTEGER {
		on (1),
		off (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"When PAUSE is enabled, port pause frames can be generated by the system and the pause frames will flow toward the link partner on the network. 
		When PAUSE is not enabled, no pause frames will be generated by the system. "
	DEFVAL  { off }
	::= { hzCpEnetPortConfigEntry 8 }

hzCpEnetPortMaxFrameSize  OBJECT-TYPE
	SYNTAX     Integer32 (1600..9600)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"For port the default maximum frame size is 1600 bytes. The settable 
		                                         maximum frame size must be a multiple of 128. If the chosen
		                                         maximum frame size is not a multiple of 128, its closest 
		                                         number which is a multiple of 128 is actually used. The 
		                                         settable lowest maximum frame size is 1664, and the settable
		                                         highest maximum frame size is 9600. If the chosen maximum 
		                                         frame size is out of this range, the default maximum frame 
		                                         size of 1600 is used. "
	DEFVAL  { 1600 }
	::= { hzCpEnetPortConfigEntry 9 }

hzCpEnetPortOpticalTransceiverState  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"When it is on, optical transmitter for port1 is enabled. Otherwise
		         optical transmitter is disabled. This option is valid for port1 only if media type is configured as fiber."
	DEFVAL  { off }
	::= { hzCpEnetPortConfigEntry 10 }

hzCpEnetPortState  OBJECT-TYPE
	SYNTAX     INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Enables or disables enet-port3 or enet-port4."
	::= { hzCpEnetPortConfigEntry 11 }

hzCpEnetPayloadState  OBJECT-TYPE
	SYNTAX     INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Enables or disables payload on a specific port. If payload state is disabled for that port, it's considered as purely management."
	::= { hzCpEnetPortConfigEntry 12 }

hzCpEnetPortStatusTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpEnetPortStatusEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table for enet port status"
	::= { hzCpEnetPort 2 }

hzCpEnetPortStatusEntry  OBJECT-TYPE
	SYNTAX 	HzCpEnetPortStatusEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry for enet port status"
	INDEX { hzCpEnetPortStatusIndex }
	::= { hzCpEnetPortStatusTable 1 }

HzCpEnetPortStatusEntry ::= SEQUENCE {
	hzCpEnetPortStatusIndex
		INTEGER,
	hzCpEnetPortLinkStatus
		INTEGER,
	hzCpEnetPortAutoNegotiationStatus
		INTEGER,
	hzCpEnetPortSpeedStatus
		INTEGER,
	hzCpEnetPortDuplexStatus
		INTEGER,
	hzCpEnetPortMediaStatus
		INTEGER
}

hzCpEnetPortStatusIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		enet-port-1 (1),
		enet-port-2 (2),
		enet-port-3 (3),
		enet-port-4 (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each enet port."
	::= { hzCpEnetPortStatusEntry 1 }

-- 
-- PORT STATUS
--   

hzCpEnetPortLinkStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		down (1),
		up (2),
		invalid (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the port link status."
	::= { hzCpEnetPortStatusEntry 2 }

hzCpEnetPortAutoNegotiationStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		on (1),
		off (2),
		invalid (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the port AutoNegotiation status."
	::= { hzCpEnetPortStatusEntry 3 }

hzCpEnetPortSpeedStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		x10M (1),
		x100M (2),
		x1000M (3),
		auto (4),
		invalid (5)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the port interface speed status."
	::= { hzCpEnetPortStatusEntry 4 }

hzCpEnetPortDuplexStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		full (1),
		half (2),
		auto (3),
		invalid (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the port interface duplex status."
	::= { hzCpEnetPortStatusEntry 5 }

hzCpEnetPortMediaStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		copper (1),
		fiber (2),
		auto (3),
		invalid (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the port interface media status."
	::= { hzCpEnetPortStatusEntry 6 }

hzCpEnetPortStatsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpEnetPortStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table for enet port stats"
	::= { hzCpEnetPort 3 }

hzCpEnetPortStatsEntry  OBJECT-TYPE
	SYNTAX 	HzCpEnetPortStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry for enet port stats"
	INDEX { hzCpEnetPortStatsIndex }
	::= { hzCpEnetPortStatsTable 1 }

HzCpEnetPortStatsEntry ::= SEQUENCE {
	hzCpEnetPortStatsIndex
		INTEGER,
	hzCpEnetPortTxFrames
		Counter64,
	hzCpEnetPortTxBytes
		Counter64,
	hzCpEnetPortRxFramesOk
		Counter64,
	hzCpEnetPortRxBytesOk
		Counter64,
	hzCpEnetPortRxFramesLengthErrors
		Counter64,
	hzCpEnetPortRxFramesCrcErrors
		Counter64,
	hzCpEnetPortRxDiscarded
		Counter64
}

hzCpEnetPortStatsIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		enet-port-1 (1),
		enet-port-2 (2),
		enet-port-3 (3),
		enet-port-4 (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each enet port."
	::= { hzCpEnetPortStatsEntry 1 }

hzCpEnetPortTxFrames  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The total number of packets that higher-level
		  protocols requested be transmitted to a
		  subnetwork-unicast address, including those that
		  were discarded or not sent."
	::= { hzCpEnetPortStatsEntry 2 }

hzCpEnetPortTxBytes  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The total number of packets that higher-level
		  protocols requested be transmitted to a non-
		  unicast (i.e., a subnetwork-broadcast or
		  subnetwork-multicast) address, including those
		  that were discarded or not sent."
	::= { hzCpEnetPortStatsEntry 3 }

hzCpEnetPortRxFramesOk  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The total number of bytes received on the interface, including framing characters."
	::= { hzCpEnetPortStatsEntry 4 }

hzCpEnetPortRxBytesOk  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The number of inbound packets which were chosen
		  to be discarded even though no errors had been
		  detected to prevent their being deliverable to a
		  higher-layer protocol.  One possible reason for
		  discarding such a packet could be to free up buffer space."
	::= { hzCpEnetPortStatsEntry 5 }

hzCpEnetPortRxFramesLengthErrors  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The number of inbound packets that contained
		  errors preventing them from being deliverable to a
		  higher-layer protocol."
	::= { hzCpEnetPortStatsEntry 6 }

hzCpEnetPortRxFramesCrcErrors  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The number of packets received via the interface
		  which were discarded because of rx frames CRC errors."
	::= { hzCpEnetPortStatsEntry 7 }

hzCpEnetPortRxDiscarded  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The number of inbound packets which were chosen
		  to be discarded even though no errors had been
		  detected to prevent their being deliverable to a
		  higher-layer protocol.  One possible reason for
		  discarding such a packet could be to free up buffer space."
	::= { hzCpEnetPortStatsEntry 8 }

hzCpEnetAggTxFrames  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of aggregated port egress frames."
	::= { hzCpEnetAggregatedStats 1 }

hzCpEnetAggTxBytes  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of aggregated port egress bytes."
	::= { hzCpEnetAggregatedStats 2 }

hzCpEnetAggRxFramesOK  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of aggregated port ingress frames."
	::= { hzCpEnetAggregatedStats 3 }

hzCpEnetAggRxBytesOK  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of aggregated port ingress bytes."
	::= { hzCpEnetAggregatedStats 4 }

hzCpEnetAggRxFramesLengthError  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of aggregated frames received that were discarded due to length error."
	::= { hzCpEnetAggregatedStats 5 }

hzCpEnetAggPortRxFramesCrcError  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of aggregated frames received that were discarded due to CRC error. "
	::= { hzCpEnetAggregatedStats 6 }

hzCpEnetAggPortRxFramesDrops  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of aggregated frames received that were discarded due to an error. "
	::= { hzCpEnetAggregatedStats 7 }

hzCpEnetAggBWUtilization  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The carried load over link capacity for port."
	::= { hzCpEnetAggregatedStats 8 }

hzCpEnetAggIngressDataRate  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The data rate coming into the ethernet for port.
		 The data rate you see is multiplied by 100. e.g. A display
		 of 1530 is actually 15.30 Mpbs"
	::= { hzCpEnetAggregatedStats 9 }

hzCpEnetAggEgressDataRate  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The data rate going out of the ethernet for port.
		 The data rate you see is multiplied by 100. e.g. A display
		 of 1530 is actually 15.30 Mpbs."
	::= { hzCpEnetAggregatedStats 10 }

hzCpEnetAggFramesInQueueTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpEnetAggFramesInQueueEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of enet aggregate frames."
	::= { hzCpEnetAggregatedStats 11 }

hzCpEnetAggFramesInQueueEntry  OBJECT-TYPE
	SYNTAX 	HzCpEnetAggFramesInQueueEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of enet aggregate frames."
	INDEX { hzCpEnetAggFramesInQueueIndex }
	::= { hzCpEnetAggFramesInQueueTable 1 }

HzCpEnetAggFramesInQueueEntry ::= SEQUENCE {
	hzCpEnetAggFramesInQueueIndex
		INTEGER,
	hzCpEnetAggFramesInQueue
		Counter64,
	hzCpEnetAggFramesInQueueDiscarded
		Counter64
}

hzCpEnetAggFramesInQueueIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each of the 8 queues."
	::= { hzCpEnetAggFramesInQueueEntry 1 }

hzCpEnetAggFramesInQueue  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of frames in queues."
	::= { hzCpEnetAggFramesInQueueEntry 2 }

hzCpEnetAggFramesInQueueDiscarded  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of frames received in queues that  were discarded due to an error. 
		 One possible reason is lack of buffer space."
	::= { hzCpEnetAggFramesInQueueEntry 3 }

hzCpEnetPortLcStatsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpEnetPortLcStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table for enet port stats"
	::= { hzCpEnetPort 5 }

hzCpEnetPortLcStatsEntry  OBJECT-TYPE
	SYNTAX 	HzCpEnetPortLcStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry for enet port stats"
	INDEX { hzCpEnetPortLcStatsIndex }
	::= { hzCpEnetPortLcStatsTable 1 }

HzCpEnetPortLcStatsEntry ::= SEQUENCE {
	hzCpEnetPortLcStatsIndex
		INTEGER,
	hzCpEnetPortLcTxFrames
		Counter32,
	hzCpEnetPortLcTxBytes
		Counter32,
	hzCpEnetPortLcRxFramesOk
		Counter32,
	hzCpEnetPortLcRxBytesOk
		Counter32,
	hzCpEnetPortLcRxFramesLengthErrors
		Counter32,
	hzCpEnetPortLcRxFramesCrcErrors
		Counter32,
	hzCpEnetPortLcRxDiscarded
		Counter32
}

hzCpEnetPortLcStatsIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		enet-port-1 (1),
		enet-port-2 (2),
		enet-port-3 (3),
		enet-port-4 (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each enet port."
	::= { hzCpEnetPortLcStatsEntry 1 }

hzCpEnetPortLcTxFrames  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing total number of packets that higher-level
		  protocols requested be transmitted to a
		  subnetwork-unicast address, including those that
		  were discarded or not sent."
	::= { hzCpEnetPortLcStatsEntry 2 }

hzCpEnetPortLcTxBytes  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing total number of packets that higher-level
		  protocols requested be transmitted to a non-
		  unicast (i.e., a subnetwork-broadcast or
		  subnetwork-multicast) address, including those
		  that were discarded or not sent."
	::= { hzCpEnetPortLcStatsEntry 3 }

hzCpEnetPortLcRxFramesOk  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing total number of bytes received on the interface, including framing characters."
	::= { hzCpEnetPortLcStatsEntry 4 }

hzCpEnetPortLcRxBytesOk  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the number of inbound packets which were chosen
		  to be discarded even though no errors had been
		  detected to prevent their being deliverable to a
		  higher-layer protocol.  One possible reason for
		  discarding such a packet could be to free up buffer space."
	::= { hzCpEnetPortLcStatsEntry 5 }

hzCpEnetPortLcRxFramesLengthErrors  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the number of inbound packets that contained
		  errors preventing them from being deliverable to a
		  higher-layer protocol."
	::= { hzCpEnetPortLcStatsEntry 6 }

hzCpEnetPortLcRxFramesCrcErrors  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the number of packets received via the interface
		  which were discarded because of rx frames CRC errors."
	::= { hzCpEnetPortLcStatsEntry 7 }

hzCpEnetPortLcRxDiscarded  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the number of inbound packets which were chosen
		  to be discarded even though no errors had been
		  detected to prevent their being deliverable to a
		  higher-layer protocol.  One possible reason for
		  discarding such a packet could be to free up buffer space."
	::= { hzCpEnetPortLcStatsEntry 8 }

hzCpEnetAggLcTxFrames  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of aggregated port egress frames."
	::= { hzCpEnetAggregatedLcStats 1 }

hzCpEnetAggLcTxBytes  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of aggregated port egress bytes."
	::= { hzCpEnetAggregatedLcStats 2 }

hzCpEnetAggLcRxFramesOK  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of aggregated port ingress frames."
	::= { hzCpEnetAggregatedLcStats 3 }

hzCpEnetAggLcRxBytesOK  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of aggregated port ingress bytes."
	::= { hzCpEnetAggregatedLcStats 4 }

hzCpEnetAggLcRxFramesLengthError  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of aggregated frames received that were discarded due to length error."
	::= { hzCpEnetAggregatedLcStats 5 }

hzCpEnetAggLcRxFramesCrcError  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of aggregated frames received that were discarded due to CRC error."
	::= { hzCpEnetAggregatedLcStats 6 }

hzCpEnetAggLcRxFramesDrops  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of aggregated frames received that were discarded due to an error."
	::= { hzCpEnetAggregatedLcStats 7 }

hzCpEnetAggLcBWUtilization  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the carried load over link capacity for port."
	::= { hzCpEnetAggregatedLcStats 8 }

hzCpEnetAggLcIngressDataRate  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the data rate coming into the ethernet for port.
		 The data rate you see is multiplied by 100. e.g. A display
		 of 1530 is actually 15.30 Mpbs"
	::= { hzCpEnetAggregatedLcStats 9 }

hzCpEnetAggLcEgressDataRate  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the data rate going out of the ethernet for port.
		 The data rate you see is multiplied by 100. e.g. A display
		 of 1530 is actually 15.30 Mpbs."
	::= { hzCpEnetAggregatedLcStats 10 }

hzCpEnetAggLcFramesInQueueTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpEnetAggLcFramesInQueueEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of enet aggregate frames."
	::= { hzCpEnetAggregatedLcStats 11 }

hzCpEnetAggLcFramesInQueueEntry  OBJECT-TYPE
	SYNTAX 	HzCpEnetAggLcFramesInQueueEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of enet aggregate frames."
	INDEX { hzCpEnetAggLcFramesInQueueIndex }
	::= { hzCpEnetAggLcFramesInQueueTable 1 }

HzCpEnetAggLcFramesInQueueEntry ::= SEQUENCE {
	hzCpEnetAggLcFramesInQueueIndex
		INTEGER,
	hzCpEnetAggLcFramesInQueue
		Counter32,
	hzCpEnetAggLcFramesInQueueDiscarded
		Counter32
}

hzCpEnetAggLcFramesInQueueIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each of the 8 queues."
	::= { hzCpEnetAggLcFramesInQueueEntry 1 }

hzCpEnetAggLcFramesInQueue  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of frames in queues."
	::= { hzCpEnetAggLcFramesInQueueEntry 2 }

hzCpEnetAggLcFramesInQueueDiscarded  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of frames received in queues that  were discarded due to an error. 
		 One possible reason is lack of buffer space."
	::= { hzCpEnetAggLcFramesInQueueEntry 3 }

hzCpEnetPortVlanTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpEnetPortVlanEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of enet port vlan setting."
	::= { hzCpEnetPort 7 }

hzCpEnetPortVlanEntry  OBJECT-TYPE
	SYNTAX 	HzCpEnetPortVlanEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of enet port vlan setting."
	INDEX { hzCpEnetPortVlanIndex }
	::= { hzCpEnetPortVlanTable 1 }

HzCpEnetPortVlanEntry ::= SEQUENCE {
	hzCpEnetPortVlanIndex
		INTEGER,
	hzCpEnetPortDefaultVlanId
		Integer32,
	hzCpEnetPortDefaultVlanPriority
		Integer32
}

hzCpEnetPortVlanIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		enet-port-1 (1),
		enet-port-2 (2),
		enet-port-3 (3),
		enet-port-4 (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique name identifying each enet port."
	::= { hzCpEnetPortVlanEntry 1 }

hzCpEnetPortDefaultVlanId  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates default VLAN Tag Id (1...4095) per port."
	::= { hzCpEnetPortVlanEntry 2 }

hzCpEnetPortDefaultVlanPriority  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicated default VLAN tagging priority (0...7) per port."
	::= { hzCpEnetPortVlanEntry 3 }

-- -----------------------------
--   hzCpWirelessInterfaceNames
-- ----------------------------- 

hzCpWirelessInterfaceNameTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpWirelessInterfaceNameEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Wireless Interface Modems"
	::= { hzCpWirelessInterfaceNames 1 }

hzCpWirelessInterfaceNameEntry  OBJECT-TYPE
	SYNTAX 	HzCpWirelessInterfaceNameEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of Wireless Interface Modem"
	INDEX { hzCpWirelessInterfaceNameIndex }
	::= { hzCpWirelessInterfaceNameTable 1 }

HzCpWirelessInterfaceNameEntry ::= SEQUENCE {
	hzCpWirelessInterfaceNameIndex
		INTEGER,
	hzCpWirelessInterfaceName
		DisplayString
}

hzCpWirelessInterfaceNameIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1),
		wireless-port-2 (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each Wireless Interface."
	::= { hzCpWirelessInterfaceNameEntry 1 }

hzCpWirelessInterfaceName  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Describes the functionality of the Wireless Interface."
	::= { hzCpWirelessInterfaceNameEntry 2 }

-- ----------------
-- hzCpModemTable
-- ----------------

hzCpModemTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpModemEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Wireless Interface Modems"
	::= { hzCpWirelessInterfaceModems 1 }

hzCpModemEntry  OBJECT-TYPE
	SYNTAX 	HzCpModemEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of Wireless Interface Modem"
	INDEX { hzCpModemIndex }
	::= { hzCpModemTable 1 }

HzCpModemEntry ::= SEQUENCE {
	hzCpModemIndex
		INTEGER,
	hzCpModemOperStatus
		INTEGER,
	hzCpModemChannelizedRSL
		Integer32,
	hzCpModemChannelizedRSLUnsignedInt
		Integer32,
	hzCpModemModulationType
		INTEGER,
	hzCpModemRxSpeed
		Integer32,
	hzCpModemTxSpeed
		Integer32,
	hzCpModemSNR
		Integer32,
	hzCpModemEbToNoiseRatio
		Integer32,
	hzCpModemEqualizerStress
		Integer32
}

hzCpModemIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpModemEntry 1 }

hzCpModemOperStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		up (1),
		down (2),
		testing (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current operational state of the interface. Testing indicates that no operational packets can be passed."
	::= { hzCpModemEntry 2 }

hzCpModemChannelizedRSL  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"An estimate of the modem's channelized RSL. Divide the value by 10 to get the actual RSL value. Once this number is divided by 10 the units are dBm.  For example -352 is actually -35.2dBm"
	::= { hzCpModemEntry 3 }

hzCpModemChannelizedRSLUnsignedInt  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The absolute value of the estimate of the modem's channelized RSL. Divide the value by 10 to get the actual RSL value. Once this number is divided by 10 the units are dBm.  For example -352 is actually 35.2dBm"
	::= { hzCpModemEntry 4 }

hzCpModemModulationType  OBJECT-TYPE
	SYNTAX     INTEGER {
		qpsk (1),
		qam16 (2),
		qam32 (3),
		qam64 (4),
		qam128 (5),
		qam256 (6),
		qam512 (7),
		qam1024 (8),
		qam2048 (9)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The modulation type of the modem, distinguished according 
		 to the physical/link protocol."
	::= { hzCpModemEntry 5 }

hzCpModemRxSpeed  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"An estimate of the modem's current Rx bandwidth in bits per 
		 second. Divide the value by 10000 to get the actual data rate in Mbps"
	::= { hzCpModemEntry 6 }

hzCpModemTxSpeed  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"An estimate of the modem's current Tx bandwidth in bits per 
		 second. Divide the value by 10000 to get the actual data rate in Mbps"
	::= { hzCpModemEntry 7 }

hzCpModemSNR  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"SNR value in dB. Divide the value by 10 to get the actual SNR."
	::= { hzCpModemEntry 8 }

hzCpModemEbToNoiseRatio  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The ratio of the modem Estimated Energy per information bit to Noise power spectral density.
		                                        Divide the value by 10 to get the actual EbToNoiseRatio"
	::= { hzCpModemEntry 9 }

hzCpModemEqualizerStress  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the average magnitude of all the equalizer taps to provide a measure of how hard the equalizer is working."
	::= { hzCpModemEntry 10 }

-- ----------------------------
-- hzCpWirelessInterfaceModems
-- ----------------------------

hzCpWirelessPortStatsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpWirelessPortStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Wireless Interface Modem Statistics"
	::= { hzCpWirelessInterfaceModems 2 }

hzCpWirelessPortStatsEntry  OBJECT-TYPE
	SYNTAX 	HzCpWirelessPortStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Statistics for a Wireless Interface Modem"
	INDEX { hzCpWirelessPortStatsIndex }
	::= { hzCpWirelessPortStatsTable 1 }

HzCpWirelessPortStatsEntry ::= SEQUENCE {
	hzCpWirelessPortStatsIndex
		INTEGER,
	hzCpWirelessPortTxBlocks
		Counter64,
	hzCpWirelessPortRxBlocksOKs
		Counter64,
	hzCpWirelessPortRxBlocksErrors
		Counter64
}

hzCpWirelessPortStatsIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1),
		wireless-port-2 (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. Wireless-port-1 contains the local aggregate wireless statistics and wireless-port-2 contains the partner aggregate wireless statistics only in redundancy configuration mode."
	::= { hzCpWirelessPortStatsEntry 1 }

hzCpWirelessPortTxBlocks  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of Modem blocks transmitted."
	::= { hzCpWirelessPortStatsEntry 2 }

hzCpWirelessPortRxBlocksOKs  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of Modem blocks received."
	::= { hzCpWirelessPortStatsEntry 3 }

hzCpWirelessPortRxBlocksErrors  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of Modem blocks received in error."
	::= { hzCpWirelessPortStatsEntry 4 }

hzCpWirelessAggTxFrames  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of Ethernet Combiner frames sent."
	::= { hzCpWirelessAggregateStats 1 }

hzCpWirelessAggRxFramesOK  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of good Ethernet Combiner frames received."
	::= { hzCpWirelessAggregateStats 2 }

hzCpWirelessAggRxFramesErrors  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of Ethernet Combiner frames received in error."
	::= { hzCpWirelessAggregateStats 3 }

hzCpWirelessAggRxFramesDiscards  OBJECT-TYPE
	SYNTAX     Counter64
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current count of Ethernet Combiner frames in the queue that were discarded."
	::= { hzCpWirelessAggregateStats 4 }

hzCpWirelessPortLcStatsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpWirelessPortLcStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Wireless Interface Modem Statistics"
	::= { hzCpWirelessInterfaceModems 4 }

hzCpWirelessPortLcStatsEntry  OBJECT-TYPE
	SYNTAX 	HzCpWirelessPortLcStatsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Statistics for a Wireless Interface Modem"
	INDEX { hzCpWirelessPortLcStatsIndex }
	::= { hzCpWirelessPortLcStatsTable 1 }

HzCpWirelessPortLcStatsEntry ::= SEQUENCE {
	hzCpWirelessPortLcStatsIndex
		INTEGER,
	hzCpWirelessPortLcTxBlocks
		Counter32,
	hzCpWirelessPortLcRxBlocksOKs
		Counter32,
	hzCpWirelessPortLcRxBlocksErrors
		Counter32
}

hzCpWirelessPortLcStatsIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1),
		wireless-port-2 (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. Wireless-port-1 contains the local aggregate wireless statistics and wireless-port-2 contains the partner aggregate wireless statistics only in redundancy configuration mode."
	::= { hzCpWirelessPortLcStatsEntry 1 }

hzCpWirelessPortLcTxBlocks  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of Modem blocks transmitted."
	::= { hzCpWirelessPortLcStatsEntry 2 }

hzCpWirelessPortLcRxBlocksOKs  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of Modem blocks received."
	::= { hzCpWirelessPortLcStatsEntry 3 }

hzCpWirelessPortLcRxBlocksErrors  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of Modem blocks received in error."
	::= { hzCpWirelessPortLcStatsEntry 4 }

hzCpWirelessAggLcTxFrames  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of Ethernet Combiner frames sent."
	::= { hzCpWirelessAggregateLcStats 1 }

hzCpWirelessAggLcRxFramesOK  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of good Ethernet Combiner frames received."
	::= { hzCpWirelessAggregateLcStats 2 }

hzCpWirelessAggLcRxFramesErrors  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of Ethernet Combiner frames received in error."
	::= { hzCpWirelessAggregateLcStats 3 }

hzCpWirelessAggLcRxFramesDiscards  OBJECT-TYPE
	SYNTAX     Counter32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A low capacity 32-bit counter representing the current count of Ethernet Combiner frames in the queue that were discarded."
	::= { hzCpWirelessAggregateLcStats 4 }

-- 
-- RADIO INFORMATION
-- 

hzCpRadioTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRadioEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Wireless Interface Radios"
	::= { hzCpWirelessInterfaceRadios 1 }

hzCpRadioEntry  OBJECT-TYPE
	SYNTAX 	HzCpRadioEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of Wireless Interface Radio"
	INDEX { hzCpRadioIndex }
	::= { hzCpRadioTable 1 }

HzCpRadioEntry ::= SEQUENCE {
	hzCpRadioIndex
		INTEGER,
	hzCpRadioDescription
		DisplayString,
	hzCpRadioOperStatus
		INTEGER,
	hzCpRadioTxGain
		Integer32,
	hzCpRadioRxGain
		Integer32,
	hzCpRadioReset
		Integer32,
	hzCpRadioTransmitPowerdBm
		Integer32,
	hzCpRadioPowerOption
		INTEGER,
	hzCpRadioTxState
		INTEGER,
	hzCpRadioActualTxState
		INTEGER,
	hzCpRadioTemperature
		Integer32,
	hzCpRadioMaxTransmitPowerdBm
		Integer32,
	hzCpRadioActualTransmitPowerdBm
		Integer32
}

hzCpRadioIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpRadioEntry 1 }

hzCpRadioDescription  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A textual string containing information about the radio. Includes the 
		manufacturer, product name, software version,  serial number, and hardware 
		version of the radio."
	::= { hzCpRadioEntry 2 }

hzCpRadioOperStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		up (1),
		down (2),
		testing (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The current operational state of the interface. Testing 
		 indicates that no operational packets can be passed. Testing
		 also indicates that a firmware upgrade may be in progress"
	::= { hzCpRadioEntry 3 }

hzCpRadioTxGain  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The gain of the radio in the transmit chain. Indicates the gain in dB."
	::= { hzCpRadioEntry 4 }

hzCpRadioRxGain  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The gain of the radio in the receive chain. Indicates the gain in dB."
	::= { hzCpRadioEntry 5 }

hzCpRadioReset  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Writing 1 to this object causes the radio to be reset. All other values 
		are not recognized. This variable  always reads back as 0. radioOperStatus 
		should be polled  by the user after this object is written to, to verify  
		that the radio card has been reset."
	::= { hzCpRadioEntry 6 }

hzCpRadioTransmitPowerdBm  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This defines the programmed transmit level of the radio.
		The power you see is divided by 10. e.g. A display of 133 is actually 13.3 dBm.
		Notes: 
		- If HAAM is enabled, transmit power cannot be changed.
		- Programmed and Actual transmit power may differ - see hzCpRadioActualTransmitPower."
	DEFVAL  { 0 }
	::= { hzCpRadioEntry 7 }

hzCpRadioPowerOption  OBJECT-TYPE
	SYNTAX     INTEGER {
		normal (1),
		highPower (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This indicates the power option of the radio: normal or high power."
	::= { hzCpRadioEntry 8 }

hzCpRadioTxState  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This indicates the power option of the radio: normal or high power."
	::= { hzCpRadioEntry 9 }

hzCpRadioActualTxState  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This defines the actual transmit state of the radio."
	::= { hzCpRadioEntry 10 }

hzCpRadioTemperature  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The Radio temperature in degree Celsius. The actual temperature is determined by dividing the number by 10. e.g. 202 is actually 20.2 degrees Celsius."
	::= { hzCpRadioEntry 11 }

hzCpRadioMaxTransmitPowerdBm  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The maximum Transmit Power allowed."
	::= { hzCpRadioEntry 12 }

hzCpRadioActualTransmitPowerdBm  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This defines the actual transmit level of the radio.
		The power you see is divided by 10. e.g. A display of 133 is actually 13.3 dBm.
		This value will be undefined: 
		- If the radio is operational and muted.
		- If the radio is not operational.
		- If the radio does not have transmit calibration tables programmed into its EEPROM, this transmit power level cannot be used as it is not possible to accurately calculate the actual transmit level. In this case this object will return -99."
	::= { hzCpRadioEntry 13 }

hzCpRadio1FreqGroupSelected  OBJECT-TYPE
	SYNTAX     INTEGER {
		txLow (1),
		txHigh (2),
		none (3)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The frequency group selected for Radio 1.
		
		The frequency subbands are divided into frequency groups txHigh and txLow.  A system must have one node configured to txLow and the other node configure to txHigh"
	::= { hzCpWirelessInterfaceRadio1Frequencies 1 }

hzCpRadio1BandTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRadio1BandEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of radio bands for Radio 1. Frequency bands are divided into sub bands.
		 
		i.e. FCC 23 Ghz Band is comprised of fcc23a, fcc23b, fcc23c and 
		fcc23d which make up the entire FCC23 band"
	::= { hzCpWirelessInterfaceRadio1Frequencies 2 }

hzCpRadio1BandEntry  OBJECT-TYPE
	SYNTAX 	HzCpRadio1BandEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A radio band entry containing all the radio band values"
	INDEX { hzCpRadio1BandIndex }
	::= { hzCpRadio1BandTable 1 }

HzCpRadio1BandEntry ::= SEQUENCE {
	hzCpRadio1BandIndex
		Integer32,
	hzCpRadio1BandId
		Integer32,
	hzCpRadio1BandName
		DisplayString,
	hzCpRadio1BandProgrammed
		INTEGER
}

hzCpRadio1BandIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each radio band. "
	::= { hzCpRadio1BandEntry 1 }

hzCpRadio1BandId  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"radio band ID.  
		Changing this option might require a system reset unless the user has permission to set hzCpApplyFrequencyChangesToSystem after the changes."
	::= { hzCpRadio1BandEntry 2 }

hzCpRadio1BandName  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The radio band name."
	::= { hzCpRadio1BandEntry 3 }

hzCpRadio1BandProgrammed  OBJECT-TYPE
	SYNTAX     INTEGER {
		active (1),
		notActive (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the operating radio band for the modem.
		
		***Only 1 index may be selected in a 1 modem system."
	DEFVAL  { notActive }
	::= { hzCpRadio1BandEntry 4 }

hzCpRadio1FreqTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRadio1FreqEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of txHigh frequency settings for Radio 1"
	::= { hzCpWirelessInterfaceRadio1Frequencies 3 }

hzCpRadio1FreqEntry  OBJECT-TYPE
	SYNTAX 	HzCpRadio1FreqEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A TX-High or TX-Low Frequency entry containing all the frequency values"
	INDEX { hzCpRadio1FreqIndex }
	::= { hzCpRadio1FreqTable 1 }

HzCpRadio1FreqEntry ::= SEQUENCE {
	hzCpRadio1FreqIndex
		Integer32,
	hzCpRadio1FreqChannelIndex
		DisplayString,
	hzCpRadio1FreqTransmitRfFrequency
		Integer32,
	hzCpRadio1FreqReceiveRfFrequency
		Integer32,
	hzCpRadio1FreqProgrammed
		INTEGER
}

hzCpRadio1FreqIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each frequency channel. "
	::= { hzCpRadio1FreqEntry 1 }

hzCpRadio1FreqChannelIndex  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The channel index for the frequency."
	::= { hzCpRadio1FreqEntry 2 }

hzCpRadio1FreqTransmitRfFrequency  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The RF TX frequency in KHz."
	::= { hzCpRadio1FreqEntry 3 }

hzCpRadio1FreqReceiveRfFrequency  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The RF RX frequency in KHz."
	::= { hzCpRadio1FreqEntry 4 }

hzCpRadio1FreqProgrammed  OBJECT-TYPE
	SYNTAX     INTEGER {
		active (1),
		notActive (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the operating frequency channel for the modem.
		
		***Only 1 index may be selected in a 1 modem system."
	DEFVAL  { notActive }
	::= { hzCpRadio1FreqEntry 5 }

hzCpRadio1ProgrammedFrequencyChannel  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The channel that has been programmed for Radio 1."
	::= { hzCpRadio1ProgrammedFrequency 1 }

hzCpRadio1ProgrammedFrequencyTxRf  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The programmed RF TX frequency in KHz."
	::= { hzCpRadio1ProgrammedFrequency 2 }

hzCpRadio1ProgrammedFrequencyRxRf  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The RF RX frequency in KHz."
	::= { hzCpRadio1ProgrammedFrequency 3 }

hzCpSystemModeTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpSystemModeEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of system mode settings. Radio Band needs to be configured before the system mode."
	::= { hzCpWirelessInterfaceRadioFrequencies 2 }

hzCpSystemModeEntry  OBJECT-TYPE
	SYNTAX 	HzCpSystemModeEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A system mode entry containing all the system mode values"
	INDEX { hzCpSystemModeIndex }
	::= { hzCpSystemModeTable 1 }

HzCpSystemModeEntry ::= SEQUENCE {
	hzCpSystemModeIndex
		Integer32,
	hzCpSystemModeId
		Integer32,
	hzCpSystemModeName
		DisplayString,
	hzCpSystemModeProgrammed
		INTEGER
}

hzCpSystemModeIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each system mode. "
	::= { hzCpSystemModeEntry 1 }

hzCpSystemModeId  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"System mode Id. "
	::= { hzCpSystemModeEntry 2 }

hzCpSystemModeName  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The system mode name."
	::= { hzCpSystemModeEntry 3 }

hzCpSystemModeProgrammed  OBJECT-TYPE
	SYNTAX     INTEGER {
		active (1),
		notActive (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the operating system mode for the modem.
		
		Notes: 
		Set Radio Band before System Mode
		Only 1 index may be selected.
		Changing this option might require a system reset unless the user has permission to set hzCpApplyFrequencyChangesToSystem after the changes."
	DEFVAL  { notActive }
	::= { hzCpSystemModeEntry 4 }

hzCpApplyFrequencyChangesToSystem  OBJECT-TYPE
	SYNTAX     INTEGER {
		apply (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"If system allows, setting this oid applies the radio band, system mode and frequency changes dynamilcally and a system reset can be avoided. "
	::= { hzCpWirelessInterfaceRadioFrequencies 3 }

-- ----------------------------
-- hzCpWirelessInterfaceAntenna
-- ----------------------------

hzCpAntennaDiameter  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This defines the diameter of the antenna represented in inches."
	::= { hzCpWirelessInterfaceAntenna 1 }

hzCpAntennaTilt  OBJECT-TYPE
	SYNTAX     INTEGER {
		unknown (1),
		vertical (2),
		horizontal (3),
		flat (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This defines the position of the antenna for radio 1."
	::= { hzCpWirelessInterfaceAntenna 2 }

hzCpDroppedFramesThresholdParams  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The threshold, in percent, and the number of seconds that it must exceed this threshold are programmed by the user. An example of the format of the string is as follows: '70 10'. The first parameter is the percentage of frames that are dropped, the second is the time in seconds that the threshold must be exceeded. The single quote marks i.e. ' ' are not used in the command."
	::= { hzCpAggregatedThresholdAlarm 1 }

hzCpBwUtilizationThresholdParams  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The percentage of available bandwidth threshold and the number of seconds that it must exceed this threshold are programmed by the user. An example of the format of the string is as follows: '70 10'. The first parameter is the threshold point in percent, the number of seconds that the threshold must be exceeded. The single quote marks i.e. ' ' are not used in the command."
	::= { hzCpAggregatedThresholdAlarm 2 }

hzCpQBasedThresholdAlarmTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpQBasedThresholdAlarmEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Q based Threshold Alarm settings"
	::= { hzCpQBasedThresholdAlarm 1 }

hzCpQBasedThresholdAlarmEntry  OBJECT-TYPE
	SYNTAX 	HzCpQBasedThresholdAlarmEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry based queues."
	INDEX { hzCpQBasedThresholdIndex }
	::= { hzCpQBasedThresholdAlarmTable 1 }

HzCpQBasedThresholdAlarmEntry ::= SEQUENCE {
	hzCpQBasedThresholdIndex
		INTEGER,
	hzCpQBasedDepthThresholdParams
		DisplayString,
	hzCpQBasedDroppedFramesThresholdParams
		DisplayString
}

hzCpQBasedThresholdIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique identifier for one of the 8 queues"
	::= { hzCpQBasedThresholdAlarmEntry 1 }

hzCpQBasedDepthThresholdParams  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The threshold, in percent, and the number of seconds that it must exceed this threshold are programmed by the user. An example of the format of the string is as follows: '70 10'. The first parameter is the percentage of queue depth, the second is the time in seconds that the threshold must be exceeded."
	::= { hzCpQBasedThresholdAlarmEntry 2 }

hzCpQBasedDroppedFramesThresholdParams  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The threshold, in percent, and the number of seconds that it must exceed this threshold are programmed by the user. An example of the format of the string is as follows: '70 10'. The first parameter is the percentage of frames that are dropped, the second is the time in seconds that the threshold must be exceeded."
	::= { hzCpQBasedThresholdAlarmEntry 3 }

hzCpWirelessInterfaceThresholdAlarmTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpWirelessInterfaceThresholdAlarmEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of wireless threshold crossing alarms"
	::= { hzCpThresholdAlarmConfig 3 }

hzCpWirelessInterfaceThresholdAlarmEntry  OBJECT-TYPE
	SYNTAX 	HzCpWirelessInterfaceThresholdAlarmEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of wireless threshold crossing alarm"
	INDEX { hzCpWirelessInterfaceThresholdAlarmIndex }
	::= { hzCpWirelessInterfaceThresholdAlarmTable 1 }

HzCpWirelessInterfaceThresholdAlarmEntry ::= SEQUENCE {
	hzCpWirelessInterfaceThresholdAlarmIndex
		INTEGER,
	hzCpWirelessInterfaceRslThresholdParams
		DisplayString,
	hzCpWirelessInterfaceSnrThreshold
		DisplayString
}

hzCpWirelessInterfaceThresholdAlarmIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique identifier for each wireless port"
	::= { hzCpWirelessInterfaceThresholdAlarmEntry 1 }

hzCpWirelessInterfaceRslThresholdParams  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"A read-write string: The threshold in dBm  and the number of seconds threshold are programmed by the user. An example of the format of the string is as follows: '-75 10'. The first parameter power level in dBm, the second is the time in seconds that the threshold must be exceeded. In this example the threshold -75 dBm and the time is set to 10 seconds. The single quote marks i.e. ' ' are not used in the command."
	::= { hzCpWirelessInterfaceThresholdAlarmEntry 2 }

hzCpWirelessInterfaceSnrThreshold  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"A read-write string specifing the modem SNR threshold."
	::= { hzCpWirelessInterfaceThresholdAlarmEntry 3 }

hzCpAlarmConfigTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpAlarmConfigEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of alarm configuration"
	::= { hzCpAlarms 2 }

hzCpAlarmConfigEntry  OBJECT-TYPE
	SYNTAX 	HzCpAlarmConfigEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of alarm configuration"
	INDEX { hzCpAlarmConfigIndex, 
		hzCpAlarmInstanceIndex }
	::= { hzCpAlarmConfigTable 1 }

HzCpAlarmConfigEntry ::= SEQUENCE {
	hzCpAlarmConfigIndex
		INTEGER,
	hzCpAlarmInstanceIndex
		Unsigned32,
	hzCpAlarmConfigSeverity
		INTEGER,
	hzCpAlarmConfigState
		INTEGER
}

hzCpAlarmConfigIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		linkDown (1),
		peerAuthenticationFailure (2),
		haamConfigMismatch (3),
		haamOnLowModulation (4),
		atpcConfigMismatch (5),
		atpcAutoDisabled (6),
		sntpUnreachable (7),
		freqFileInvalid (8),
		aggregateDroppedFramesThreshold (9),
		queueDroppedFramesThreshold (10),
		bwUtilizationThreshold (11),
		queueDepthThreshold (12),
		rlsConfigMismatch (13),
		rlsShutdownActivated (14),
		rlsQBasedShutdown (15),
		modemRxLossOfSignal (16),
		modemSnrBelowThreshold (17),
		modemEqStressAboveThreshold (18),
		rslBelowThershold (19),
		radioSynthLostLock (20),
		radioCalTableUnavailable (21),
		radioCurrentOutOfLimits (22),
		radioPowerAmp (23),
		tempOutOfLimits (24),
		redundancyConfigMismatch (25),
		redundancyActiveOnSecondary (26),
		redundancyOperatingInForcedSwitch (27),
		redundancyEnetCrossLinkActive (28),
		redundancyActiveUsingPartnerWLink (29),
		redundancyStandbyWLinkInUse (30),
		redundancyStandbyOnPrimary (31),
		x2DeliveringHalfCapacity (32),
		bncSignalNotDetected (33),
		ethernetSpeedReduced (34),
		synceLostLock (35),
		synceSecondarySourceInUse (36),
		invalidSysConfig (37),
		mibChangeNotSaved (38),
		transmitterLossOfSync (39),
		radioLinearityCalError (40),
		synceConfigMismatch (41),
		cryptoPowerUpTestsFailed (42),
		cyptoConfigMismatch (43)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each alarm."
	::= { hzCpAlarmConfigEntry 1 }

hzCpAlarmInstanceIndex  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each instance of an alarm."
	::= { hzCpAlarmConfigEntry 2 }

hzCpAlarmConfigSeverity  OBJECT-TYPE
	SYNTAX     INTEGER {
		minor (1),
		major (2),
		critical (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The severity of the alarm as minor, major or critical."
	::= { hzCpAlarmConfigEntry 3 }

hzCpAlarmConfigState  OBJECT-TYPE
	SYNTAX     INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The state of the alarm which can be either enabled or disabled."
	::= { hzCpAlarmConfigEntry 4 }

hzCpAlarmStatusTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpAlarmStatusEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of alarm status"
	::= { hzCpAlarms 3 }

hzCpAlarmStatusEntry  OBJECT-TYPE
	SYNTAX 	HzCpAlarmStatusEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of alarm status"
	INDEX { hzCpAlarmStatusIndex, 
		hzCpAlarmStatusInstanceIndex }
	::= { hzCpAlarmStatusTable 1 }

HzCpAlarmStatusEntry ::= SEQUENCE {
	hzCpAlarmStatusIndex
		INTEGER,
	hzCpAlarmStatusInstanceIndex
		Unsigned32,
	hzCpAlarmStatus
		INTEGER,
	hzCpAlarmRaisedDateAndTime
		DateAndTime,
	hzCpAlarmClearedDateAndTime
		DateAndTime
}

hzCpAlarmStatusIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		linkDown (1),
		peerAuthenticationFailure (2),
		haamConfigMismatch (3),
		haamOnLowModulation (4),
		atpcConfigMismatch (5),
		atpcAutoDisabled (6),
		sntpUnreachable (7),
		freqFileInvalid (8),
		aggregateDroppedFramesThreshold (9),
		queueDroppedFramesThreshold (10),
		bwUtilizationThreshold (11),
		queueDepthThreshold (12),
		rlsConfigMismatch (13),
		rlsShutdownActivated (14),
		rlsQBasedShutdown (15),
		modemRxLossOfSignal (16),
		modemSnrBelowThreshold (17),
		modemEqStressAboveThreshold (18),
		rslBelowThershold (19),
		radioSynthLostLock (20),
		radioCalTableUnavailable (21),
		radioCurrentOutOfLimits (22),
		radioPowerAmp (23),
		tempOutOfLimits (24),
		redundancyConfigMismatch (25),
		redundancyActiveOnSecondary (26),
		redundancyOperatingInForcedSwitch (27),
		redundancyEnetCrossLinkActive (28),
		redundancyActiveUsingPartnerWLink (29),
		redundancyStandbyWLinkInUse (30),
		redundancyStandbyOnPrimary (31),
		x2DeliveringHalfCapacity (32),
		bncSignalNotDetected (33),
		ethernetSpeedReduced (34),
		synceLostLock (35),
		synceSecondarySourceInUse (36),
		invalidSysConfig (37),
		mibChangeNotSaved (38),
		transmitterLossOfSync (39),
		radioLinearityCalError (40),
		synceConfigMismatch (41),
		cryptoPowerUpTestsFailed (42),
		cryptoConfigMismatch (43)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each alarm."
	::= { hzCpAlarmStatusEntry 1 }

hzCpAlarmStatusInstanceIndex  OBJECT-TYPE
	SYNTAX     Unsigned32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each instance of an alarm."
	::= { hzCpAlarmStatusEntry 2 }

hzCpAlarmStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates whether the alarm exists or not."
	::= { hzCpAlarmStatusEntry 3 }

hzCpAlarmRaisedDateAndTime  OBJECT-TYPE
	SYNTAX     DateAndTime
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Specifies the date and time when the alarm was raised. If the alarm never occured, the value shows as zero."
	::= { hzCpAlarmStatusEntry 4 }

hzCpAlarmClearedDateAndTime  OBJECT-TYPE
	SYNTAX     DateAndTime
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Specifies the date and time when the alarm was cleared. If the alarm never occured, the value shows as zero."
	::= { hzCpAlarmStatusEntry 5 }

-- 
-- Giga Ethernet Qos
-- 

hzCpQosEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether QOS is enabled."
	DEFVAL  { disabled }
	::= { hzCpQos 1 }

hzCpCosType  OBJECT-TYPE
	SYNTAX     INTEGER {
		cosVlan (1),
		cosQinQiTag (2),
		cosQinQoTag (3),
		cosDscp (4),
		cosMplsExp (5)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates the class of service tag type.  Based on the setting Horizon will use the CoS value in the VLAN tag, or Q-in-Q inner VLAN tag, or Q-in-Q outer VLAN tag."
	DEFVAL  { cosVlan }
	::= { hzCpQos 2 }

-- 
-- 802.1p priorities assignment.
-- 

hzCpCosQinQiTag  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Sets the value of the Q-in-Q inner tag. This tag is also used to classify the non-Q-in-Q VLAN. Default value is 0x8100."
	::= { hzCpQos 3 }

hzCpCosQinQoTag  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Sets the value of the Q-in-Q outer VLAN tag.  Default value is 0x8100."
	::= { hzCpQos 4 }

-- 
-- Queue operation mode
-- 

hzCpCosExpediteQueue  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This sets the user queue operation mode. (a) In expedite mode,
		                                        any Queue can be assigned 100% of CIR. Queue 4 is serviced first
		                                        and queue 1 is servcied last.(b) In non expedite mode, the sum of all CIRs 
		                                        must be equal to 100%. In this mode higher priority queues
		                                        will be serviced first until that queue is emptied or
		                                        the defined CIR is reached."
	DEFVAL  { disabled }
	::= { hzCpQos 5 }

-- 
-- user queues configuration.
-- 

hzCpCosQueueCIR  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Committed Information Rate (CIR) assignments for CoS queues
		                                         1 to 4. CIR is assigned as the percentage of system current speed.
		                                         When expedite queue is enabled any queue can be assigned 100% bandwidth.
		                                         When expedite queue is disabled the sum of CIR of all queues should be 100."
	::= { hzCpQos 6 }

hzCpCosQueueCBS  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Comitted Burst Size (CBS) for CoS queues 1 to 4. CBS is the percentage 
		                                         of total packet buffer size. The sum of CBS's of all 4
		                                         queues must be equal to 100. Minimum CBS assignable to any queue
		                                         is 1."
	::= { hzCpQos 7 }

-- 
-- hzCpCosWfq
-- 

hzCpQosPolicy  OBJECT-TYPE
	SYNTAX     INTEGER {
		strict-priority (1),
		wfq (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Sets the Qos policy."
	::= { hzCpQos 8 }

hzCpCosWfqWeight  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..32))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The weight assigned to each of the 4 cos queue.  The wireless bandwidth is distributed amongst the 
		queues proportional to their weight.
		                                        The weights have values from 0-15, where 0 is the lowest weight and
		                                        15 is the highest weight. 
		                                        Setting the Cos weight example: 1 2 3 4."
	::= { hzCpQos 9 }

hzCpCosNumPriorityQueues  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"When expedite queue is enabled, user can set the number of priority queues. The default value for number of priority queues are 2, maximum is 4 and minimum is 1. "
	::= { hzCpQos 10 }

hzCpCosCutThroughQueue  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		q1 (2),
		q2 (3),
		q3 (4),
		q4 (5),
		q5 (6),
		q6 (7),
		q7 (8),
		q8 (9)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Cut through queue can be enabled or disabled. Any one of the 8 queues can be selected as cut through queue."
	::= { hzCpQos 11 }

hzCpQosPortConfigTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpQosPortConfigEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Qos Settings per port"
	::= { hzCpQos 12 }

hzCpQosPortConfigEntry  OBJECT-TYPE
	SYNTAX 	HzCpQosPortConfigEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of Qos setting per port"
	INDEX { hzCpCosPortIndex }
	::= { hzCpQosPortConfigTable 1 }

HzCpQosPortConfigEntry ::= SEQUENCE {
	hzCpCosPortIndex
		INTEGER,
	hzCpCosQueueMapping
		DisplayString,
	hzCpCosDefaultValue
		Integer32
}

hzCpCosPortIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		enet-port-1 (1),
		enet-port-2 (2),
		enet-port-3 (3),
		enet-port-4 (4)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique name identifying each enet port"
	::= { hzCpQosPortConfigEntry 1 }

hzCpCosQueueMapping  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"User queue assignments for CoS values 0 to 7.
		There are 8 CoS queues. Their corresponding queue numbers are 1 to 8 where 1 is the lowest priority queue and 8 is the highest priority queue. 
		CoS to queue mapping example: 1 7 2 8 3 6 1 4."
	::= { hzCpQosPortConfigEntry 2 }

hzCpCosDefaultValue  OBJECT-TYPE
	SYNTAX     Integer32 (0..7)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Default CoS value assigned to the ethernet 
		                                         frames that don't have VLAN or Q-in-Q tag."
	DEFVAL  { 0 }
	::= { hzCpQosPortConfigEntry 3 }

hzCpCosUserFlowDepTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpCosUserFlowDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"A table of Cos user flow setting"
	::= { hzCpQos 13 }

hzCpCosUserFlowDepEntry  OBJECT-TYPE
	SYNTAX 	HzCpCosUserFlowDepEntry
	MAX-ACCESS not-accessible
	STATUS     deprecated
	DESCRIPTION 
		"An entry of Cos user flow setting"
	INDEX { hzCpCosUserFlowIndexDep }
	::= { hzCpCosUserFlowDepTable 1 }

HzCpCosUserFlowDepEntry ::= SEQUENCE {
	hzCpCosUserFlowIndexDep
		INTEGER,
	hzCpCosUserFlowStateDep
		DisplayString
}

hzCpCosUserFlowIndexDep  OBJECT-TYPE
	SYNTAX     INTEGER {
		flow1 (1),
		flow2 (2),
		flow3 (3)
	}
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"This object is deprecated by hzCpCosUserFlowMappingIndex."
	::= { hzCpCosUserFlowDepEntry 1 }

hzCpCosUserFlowStateDep  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"This object is deprecated by entries in hzCpCosUserFlowMappingTable."
	::= { hzCpCosUserFlowDepEntry 2 }

hzCpCosUserClassTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpCosUserClassEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Cos user class setting."
	::= { hzCpQos 14 }

hzCpCosUserClassEntry  OBJECT-TYPE
	SYNTAX 	HzCpCosUserClassEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of Cos user class setting."
	INDEX { hzCpCosUserClassIndex }
	::= { hzCpCosUserClassTable 1 }

HzCpCosUserClassEntry ::= SEQUENCE {
	hzCpCosUserClassIndex
		INTEGER,
	hzCpCosUserClassOffset
		Integer32,
	hzCpCosUserClassValue
		DisplayString,
	hzCpCosUserClassMask
		DisplayString
}

hzCpCosUserClassIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		c1 (1),
		c2 (2),
		c3 (3),
		c4 (4),
		c5 (5),
		c6 (6),
		c7 (7),
		c8 (8),
		c9 (9),
		c10 (10),
		c11 (11),
		c12 (12),
		c13 (13)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique name specifying user class group."
	::= { hzCpCosUserClassEntry 1 }

hzCpCosUserClassOffset  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the user class offset. The valid range is 0-31."
	::= { hzCpCosUserClassEntry 2 }

hzCpCosUserClassValue  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"A string specifying the user class value in hex format. The string must hold even number of characters."
	::= { hzCpCosUserClassEntry 3 }

hzCpCosUserClassMask  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"A string specifying the user class mask in hex format. The string must hold even number of characters and have the same length as the user class value."
	::= { hzCpCosUserClassEntry 4 }

hzCpCosUserFlowFilterTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpCosUserFlowFilterEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Cos user flow filter setting."
	::= { hzCpQos 15 }

hzCpCosUserFlowFilterEntry  OBJECT-TYPE
	SYNTAX 	HzCpCosUserFlowFilterEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of Cos user flow filter setting."
	INDEX { hzCpCosUserFlowFilterIndex }
	::= { hzCpCosUserFlowFilterTable 1 }

HzCpCosUserFlowFilterEntry ::= SEQUENCE {
	hzCpCosUserFlowFilterIndex
		INTEGER,
	hzCpCosUserFlowFilter
		DisplayString
}

hzCpCosUserFlowFilterIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		flow1 (1),
		flow2 (2),
		flow3 (3),
		flow4 (4),
		flow5 (5),
		flow6 (6),
		flow7 (7),
		flow8 (8),
		flow9 (9),
		flow10 (10),
		flow11 (11),
		flow12 (12),
		flow13 (13)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique name specifying user flow group."
	::= { hzCpCosUserFlowFilterEntry 1 }

hzCpCosUserFlowFilter  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"A string specifying the user class combination. For example a user flow can be configured to include c1 c5. If a user class hasn't been assigned a value and mask, the class can't be part of the user flow filter. "
	::= { hzCpCosUserFlowFilterEntry 2 }

hzCpCosUserFlowMappingTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpCosUserFlowMappingEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Cos user flow mapping setting."
	::= { hzCpQos 16 }

hzCpCosUserFlowMappingEntry  OBJECT-TYPE
	SYNTAX 	HzCpCosUserFlowMappingEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"An entry of Cos user flow mapping setting."
	INDEX { hzCpCosUserFlowMappingIndex }
	::= { hzCpCosUserFlowMappingTable 1 }

HzCpCosUserFlowMappingEntry ::= SEQUENCE {
	hzCpCosUserFlowMappingIndex
		INTEGER,
	hzCpCosUserFlowEnable
		INTEGER,
	hzCpCosUserFlowP1Queue
		INTEGER,
	hzCpCosUserFlowP1TargetPort
		INTEGER,
	hzCpCosUserFlowP2Queue
		INTEGER,
	hzCpCosUserFlowP2TargetPort
		INTEGER,
	hzCpCosUserFlowP3Queue
		INTEGER,
	hzCpCosUserFlowP3TargetPort
		INTEGER,
	hzCpCosUserFlowP4Queue
		INTEGER,
	hzCpCosUserFlowP4TargetPort
		INTEGER
}

hzCpCosUserFlowMappingIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		flow1 (1),
		flow2 (2),
		flow3 (3),
		flow4 (4),
		flow5 (5),
		flow6 (6),
		flow7 (7),
		flow8 (8),
		flow9 (9),
		flow10 (10),
		flow11 (11),
		flow12 (12),
		flow13 (13)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique name specifying user flow group."
	::= { hzCpCosUserFlowMappingEntry 1 }

hzCpCosUserFlowEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Enables or disables a CoS user flow."
	::= { hzCpCosUserFlowMappingEntry 2 }

hzCpCosUserFlowP1Queue  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the selected queue for P1."
	::= { hzCpCosUserFlowMappingEntry 3 }

hzCpCosUserFlowP1TargetPort  OBJECT-TYPE
	SYNTAX     INTEGER {
		p1 (1),
		p2 (2),
		p3 (3),
		p4 (4)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the target egress port for P1."
	::= { hzCpCosUserFlowMappingEntry 4 }

hzCpCosUserFlowP2Queue  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the selected queue for P2."
	::= { hzCpCosUserFlowMappingEntry 5 }

hzCpCosUserFlowP2TargetPort  OBJECT-TYPE
	SYNTAX     INTEGER {
		p1 (1),
		p2 (2),
		p3 (3),
		p4 (4)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the target egress port for P2."
	::= { hzCpCosUserFlowMappingEntry 6 }

hzCpCosUserFlowP3Queue  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the selected queue for P3."
	::= { hzCpCosUserFlowMappingEntry 7 }

hzCpCosUserFlowP3TargetPort  OBJECT-TYPE
	SYNTAX     INTEGER {
		p1 (1),
		p2 (2),
		p3 (3),
		p4 (4)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the target egress port for P3."
	::= { hzCpCosUserFlowMappingEntry 8 }

hzCpCosUserFlowP4Queue  OBJECT-TYPE
	SYNTAX     INTEGER {
		q1 (1),
		q2 (2),
		q3 (3),
		q4 (4),
		q5 (5),
		q6 (6),
		q7 (7),
		q8 (8)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the selected queue for P4."
	::= { hzCpCosUserFlowMappingEntry 9 }

hzCpCosUserFlowP4TargetPort  OBJECT-TYPE
	SYNTAX     INTEGER {
		p1 (1),
		p2 (2),
		p3 (3),
		p4 (4)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the target egress port for P4."
	::= { hzCpCosUserFlowMappingEntry 10 }

hzCpRlsMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		basic (2),
		advanced (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Off, when the RLS feature is disabled. Basic when the feature is enabled but hard fault monitor is disabled. Advanced when the feature is enabled and hard fault monitor is enabled. Changing this option requires a system reset."
	::= { hzCpRapidLinkShutdown 1 }

hzCpRlsLinkControl  OBJECT-TYPE
	SYNTAX     INTEGER {
		auto (1),
		manual (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Returns RLS user control for re-enabling failed link. If manual, then user has to explicitly enable the link.
		"
	DEFVAL  { auto }
	::= { hzCpRapidLinkShutdown 2 }

hzCpApplyRlsMonitorParametersToSystem  OBJECT-TYPE
	SYNTAX     INTEGER {
		apply (1)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This writes RLS soft and hard fault monitor parameters to RAM Reading this object returns '1'."
	::= { hzCpRapidLinkShutdown 3 }

hzCpRlsLinkEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Returns RLS global link enable status as on or off. If off, the link will be shutdown."
	::= { hzCpRapidLinkShutdown 4 }

hzCpRlsPortGroup  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the RLS port group configuration setting. Options are the port list and any/all. 
		For example: 
		p1,p3 any - RLS will trigger if either p1 or p3 is down.
		or
		p1,p2,p3 all - RLS will trigger only if all p1,p2 and p3 are down.
		"
	::= { hzCpRapidLinkShutdown 5 }

hzCpRlsShutdownPolicy  OBJECT-TYPE
	SYNTAX     INTEGER {
		port-down (1),
		eoam-msg (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicated whether to shutdown the port or send an EOAM message when RLS is triggered."
	::= { hzCpRapidLinkShutdown 6 }

hzCpRlsSoftFaultMonitorTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRlsSoftFaultMonitorEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of RLS Soft Fault Monitor Parameters"
	::= { hzCpRapidLinkShutdown 7 }

hzCpRlsSoftFaultMonitorEntry  OBJECT-TYPE
	SYNTAX 	HzCpRlsSoftFaultMonitorEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"RLS Parameters for Soft Fault Monitoring"
	INDEX { hzCpRlsSoftFaultMonitorIndex }
	::= { hzCpRlsSoftFaultMonitorTable 1 }

HzCpRlsSoftFaultMonitorEntry ::= SEQUENCE {
	hzCpRlsSoftFaultMonitorIndex
		INTEGER,
	hzCpRlsEstablishErredFrameThreshold
		Integer32,
	hzCpRlsShutdownErredFrameThreshold
		Integer32,
	hzCpRlsEstablishNumberOfSamples
		Integer32,
	hzCpRlsShutdownNumberOfSamples
		Integer32,
	hzCpRlsEstablishSamplePeriod
		Integer32,
	hzCpRlsShutdownSamplePeriod
		Integer32,
	hzCpRlsQuickShutdownSamplePeriod
		Integer32
}

hzCpRlsSoftFaultMonitorIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpRlsSoftFaultMonitorEntry 1 }

hzCpRlsEstablishErredFrameThreshold  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Maximum erred blocks, per sample, allowed for link establishment."
	::= { hzCpRlsSoftFaultMonitorEntry 2 }

hzCpRlsShutdownErredFrameThreshold  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Minimum erred blocks, per sample, allowed before link shutdown."
	::= { hzCpRlsSoftFaultMonitorEntry 3 }

hzCpRlsEstablishNumberOfSamples  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Minimum number of consecutive good samples required before link is established."
	::= { hzCpRlsSoftFaultMonitorEntry 4 }

hzCpRlsShutdownNumberOfSamples  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Minimum number of consecutive erred samples before link is shutdown."
	::= { hzCpRlsSoftFaultMonitorEntry 5 }

hzCpRlsEstablishSamplePeriod  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Period of Time, in milliseconds, for monitoring Establish Erred Frame Threshold."
	::= { hzCpRlsSoftFaultMonitorEntry 6 }

hzCpRlsShutdownSamplePeriod  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Period of Time, in milliseconds, for monitoring Shutdown Erred Frame Threshold."
	::= { hzCpRlsSoftFaultMonitorEntry 7 }

hzCpRlsQuickShutdownSamplePeriod  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Minimum number of milliseconds required to classify samples as erred."
	::= { hzCpRlsSoftFaultMonitorEntry 8 }

hzCpRlsHardFaultMonitorTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRlsHardFaultMonitorEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of RLS Hard Fault Monitor Parameters"
	::= { hzCpRapidLinkShutdown 8 }

hzCpRlsHardFaultMonitorEntry  OBJECT-TYPE
	SYNTAX 	HzCpRlsHardFaultMonitorEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"RLS Parameters for Hard Fault Monitoring"
	INDEX { hzCpRlsHardFaultMonitorIndex }
	::= { hzCpRlsHardFaultMonitorTable 1 }

HzCpRlsHardFaultMonitorEntry ::= SEQUENCE {
	hzCpRlsHardFaultMonitorIndex
		INTEGER,
	hzCpRlsHardFaultSamplePeriod
		Integer32,
	hzCpRlsHardFaultThreshold
		Integer32
}

hzCpRlsHardFaultMonitorIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpRlsHardFaultMonitorEntry 1 }

hzCpRlsHardFaultSamplePeriod  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The period of time, in milliseconds, for which the rlsFaultThreshold is applied."
	::= { hzCpRlsHardFaultMonitorEntry 2 }

hzCpRlsHardFaultThreshold  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The percentage of the rlsFaultSamplePeriodMilliSeconds period which must be degraded before the link is set to the 'impaired' state."
	::= { hzCpRlsHardFaultMonitorEntry 3 }

hzCpRlsReceiveSignalLevelMonitorTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRlsReceiveSignalLevelMonitorEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of RLS RSL Monitor Parameters"
	::= { hzCpRapidLinkShutdown 9 }

hzCpRlsReceiveSignalLevelMonitorEntry  OBJECT-TYPE
	SYNTAX 	HzCpRlsReceiveSignalLevelMonitorEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"RLS RSL Monitor Parameters"
	INDEX { hzCpRlsReceiveSignalLevelMonitorIndex }
	::= { hzCpRlsReceiveSignalLevelMonitorTable 1 }

HzCpRlsReceiveSignalLevelMonitorEntry ::= SEQUENCE {
	hzCpRlsReceiveSignalLevelMonitorIndex
		INTEGER,
	hzCpRlsMakeRslMonitorRslValue
		DisplayString,
	hzCpRlsMakeRslMonitorPeriod
		Integer32
}

hzCpRlsReceiveSignalLevelMonitorIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpRlsReceiveSignalLevelMonitorEntry 1 }

hzCpRlsMakeRslMonitorRslValue  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The RSL threshold, in dBm. When Link is inactive and if RSL
		is higher than this threshold for a desired
		period, the link will become active."
	::= { hzCpRlsReceiveSignalLevelMonitorEntry 2 }

hzCpRlsMakeRslMonitorPeriod  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"The desired period, in seconds. When Link is inactive and if RSL
		is higher than the desiredthreshold for this 
		period, the link will become active."
	::= { hzCpRlsReceiveSignalLevelMonitorEntry 3 }

hzCpRlsStatusTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpRlsStatusEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of RLS Parameters and states"
	::= { hzCpRapidLinkShutdown 10 }

hzCpRlsStatusEntry  OBJECT-TYPE
	SYNTAX 	HzCpRlsStatusEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"RLS Parameters and states"
	INDEX { hzCpRlsStatusIndex }
	::= { hzCpRlsStatusTable 1 }

HzCpRlsStatusEntry ::= SEQUENCE {
	hzCpRlsStatusIndex
		INTEGER,
	hzCpRlsOption
		DisplayString,
	hzCpRlsState
		DisplayString,
	hzCpRlsMismatchState
		DisplayString,
	hzCpRlsDegradeMonitorState
		DisplayString,
	hzCpRlsHardFaultMonitorState
		DisplayString,
	hzCpRlsMakeRslThresholdState
		DisplayString,
	hzCpRlsPeerRslState
		DisplayString,
	hzCpRlsRadioInterfaceState
		DisplayString,
	hzCpRlsNetworkInterfaceState
		DisplayString,
	hzCpRlsUserConfiguredEstablishFer
		DisplayString,
	hzCpRlsMinimumAchievableEstablishFer
		DisplayString,
	hzCpRlsUserConfiguredShutdownFer
		DisplayString,
	hzCpRlsMinimumAchievableShutdownFer
		DisplayString,
	hzCpRlsUserConfiguredEstablishMonitorTime
		Integer32,
	hzCpRlsActualEstablishMonitorTime
		Integer32,
	hzCpRlsUserConfiguredShutdownMonitorTime
		Integer32,
	hzCpRlsActualShutdownMonitorTime
		Integer32
}

hzCpRlsStatusIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpRlsStatusEntry 1 }

hzCpRlsOption  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"RLS Option. On Basic: RLS enabled with base degrade monitoring;
		 On Advanced: RLS enabled with hard fault monitoring;
		 Off: Entire RLS feature disabled."
	::= { hzCpRlsStatusEntry 2 }

hzCpRlsState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Rapid Link Shutdown State. If the state is activated, that means 
		link is down; If the state is inactivated, that means that
		link is up."
	::= { hzCpRlsStatusEntry 3 }

hzCpRlsMismatchState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"RLS Mismatch State. If the state is activated, that means RLS 
		configuration is mismatched with peer. If the state is OK,
		that means RLS configuration is matched with peer."
	::= { hzCpRlsStatusEntry 4 }

hzCpRlsDegradeMonitorState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Degrade Monitor State. If the state is activated, that means
		the RLS Degrade Monitor is asserting the link down. If the
		state is OK, that means the RLS Degrade Monitor is asserting
		the link up."
	::= { hzCpRlsStatusEntry 5 }

hzCpRlsHardFaultMonitorState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Hard Fault Monitor State. If the state is activated, that means
		the Hard Fault Monitor is asserting the link down. If the state
		is OK, that means the RLS Hard Fault Monitor is asserting the
		link up."
	::= { hzCpRlsStatusEntry 6 }

hzCpRlsMakeRslThresholdState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Make RSL Threshold State. If the state is not met, that means the 
		RLS signal level is below the threshold value. If the state is OK,
		that means the RLS signal level is above the threshold value."
	::= { hzCpRlsStatusEntry 7 }

hzCpRlsPeerRslState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Peer RSL State. If the state is activated, that means the peer has
		activated RLS. If the state is OK, that means the Peer hasn't
		activated RLS."
	::= { hzCpRlsStatusEntry 8 }

hzCpRlsRadioInterfaceState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Radio Interface State. If the state is down, that means The radio 
		interface is not operational(muted,disconnected, not configured).
		If the state is up, that means the radio interface is operational."
	::= { hzCpRlsStatusEntry 9 }

hzCpRlsNetworkInterfaceState  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Network Interface State. If the state is down, that means the network 
		interface (Ethernet/PHY) is not operational(disconnected, not 
		configured). If the state is up, that means the network interface
		(Ethernet/PHY) is operational."
	::= { hzCpRlsStatusEntry 10 }

hzCpRlsUserConfiguredEstablishFer  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"User Configured Establish Frame Error Ratio required for the 
		link to be restored to service, set by the user. An example
		value being '1.0E-7'."
	::= { hzCpRlsStatusEntry 11 }

hzCpRlsMinimumAchievableEstablishFer  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Minimum achievable Frame Error Ratio required for the link to be 
		restored to service. An example value being '1.0E-7'."
	::= { hzCpRlsStatusEntry 12 }

hzCpRlsUserConfiguredShutdownFer  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"User Configured Shutdown Frame Error Ratio required for the 
		link to remain in-service, set by the user. An example value
		being '1.0E-7'."
	::= { hzCpRlsStatusEntry 13 }

hzCpRlsMinimumAchievableShutdownFer  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Minimum achievable Frame Error Ratio required for the link to 
		remain in-service. An example value being '1.0E-7'."
	::= { hzCpRlsStatusEntry 14 }

hzCpRlsUserConfiguredEstablishMonitorTime  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"User Configured Establish Monitor Time, in milliseconds, for 
		which the 'User Configured Establish FER' must be achieved
		to bring link into service, set by user"
	::= { hzCpRlsStatusEntry 15 }

hzCpRlsActualEstablishMonitorTime  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Actual Establish Monitor Time, in milliseconds, for which the 
		'User Configured Establish FER'must be achieved to bring 
		link into service, it's a multiple of the establish sample 
		period required to observe the FER"
	::= { hzCpRlsStatusEntry 16 }

hzCpRlsUserConfiguredShutdownMonitorTime  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"User Configured Shutdown Monitor Time, in milliseconds, for which
		the 'User Configured Shutdown FER' must be met for the link to
		remain in-service, set by user."
	::= { hzCpRlsStatusEntry 17 }

hzCpRlsActualShutdownMonitorTime  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Actual Shutdown Monitor Time, in milliseconds, for which the 'User
		Configured Shutdown FER' must be met for the link to remain 
		in-service, it's a multiple of the shutdown sample period required
		to  observe the FER"
	::= { hzCpRlsStatusEntry 18 }

-- 
-- hzCpLogs
-- 

hzCpEventLogEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether event log is enabled."
	DEFVAL  { enabled }
	::= { hzCpLogs 1 }

hzCpPerfmLogEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether performance log is enabled."
	DEFVAL  { enabled }
	::= { hzCpLogs 2 }

hzCpPerfmLogInterval  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..10))
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Performance statistics are logged periodically by this interval. The interval can be up to 24:00:00 (24 hours) and as short as 00:00:1 (1 seconds)."
	::= { hzCpLogs 3 }

hzCpSysLogEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether sys log is enabled."
	::= { hzCpSysLog 1 }

hzCpSysLogIpAddressDep  OBJECT-TYPE
	SYNTAX     IpAddress
	MAX-ACCESS read-only
	STATUS     deprecated
	DESCRIPTION 
		"Indicates the IP address of sys log. This object is deprecated by hzCpSysLogHostAddress. The value is 0.0.0.0 if not available or not applicable."
	::= { hzCpSysLog 2 }

hzCpSysLogHostDomain  OBJECT-TYPE
	SYNTAX     InetAddressType
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates sys log host inet address type. Valid types are ipv4 or ipv6. "
	::= { hzCpSysLog 3 }

hzCpSysLogHostAddress  OBJECT-TYPE
	SYNTAX     InetAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates sys log host inet address octet string. This object deprecates hzCpSysLogIpAddressDep."
	::= { hzCpSysLog 4 }

-- ----------------------------
--   hzCpAtpc
-- ----------------------------  		    

hzCpAtpcEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether the Adaptive Transmit Power Control (ATPC) feature is enabled."
	DEFVAL  { off }
	::= { hzCpAtpc 1 }

hzCpAtpcStatus  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		disabledAuto (2),
		running (3),
		runningToggling (4),
		suspended (5),
		suspendedLostComm (6),
		suspendedRadioDown (7),
		suspendedRadioMuted (8),
		suspendedRadioLoopback (9)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Indicates the actual condition of ATPC:
		disabled               Configuration is set to off.
		disabledAuto           Feature was automatically disabled and is transmitting at coordinated power.
		running                Feature is running normally.
		runningToggling        Feature is toggling between maximum and minimum power to recover from a loss of mod sync.
		suspended              Feature was suspended.
		suspendedLostComm      Feature is suspended because communication is lost with peer ATPC.
		suspendedRadioDown     Feature is suspended because the radio is down.
		suspendedRadioMuted    Feature is suspended because the radio is muted.
		suspendedRadioLoopback Feature is suspended because the radio is in loopback mode."
	::= { hzCpAtpc 2 }

hzCpAtpcCoordinatedPowerDbm  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This value is the coordinated power offset from the configured transmit power. 
		         The value is multiplied by 10. e.g. A display
		                 of 133 is actually 13.3 dBm."
	DEFVAL  { 0 }
	::= { hzCpAtpc 3 }

hzCpAtpcCoordinatedPwrEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		disabled (1),
		enabled (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Indicates whether Adaptive Transmit Power Control (ATPC) coordinated power option is enabled."
	DEFVAL  { disabled }
	::= { hzCpAtpc 4 }

hzCpAtpcMinTxPower  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Sets the minimum ATPC transmit power in dBm x 10. Valid range -990..100."
	::= { hzCpAtpc 5 }

hzCpAtpcMaxTxPower  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Sets the maximum ATPC transmit power in dBm x 10. When ATPC is enabled, transmit power range will be between ATPC min. and ATPC max. power."
	::= { hzCpAtpc 6 }

-- ----------------------------
--   hzCpHitlessAam
-- ----------------------------   

hzCpHitlessAamEnable  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Read or set the Hitless Automatic Adaptive Modulation (HAAM) as 'on' or 'off'."
	DEFVAL  { off }
	::= { hzCpHitlessAam 1 }

hzCpHitlessAamManualMode  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Read or set the Hitless Automatic Adaptive Modulation (HAAM) Manual Mode as 'on' or 'off'."
	::= { hzCpHitlessAam 2 }

hzCpHitlessAamWaitToRestore  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Read or set the Hitless Automatic Adaptive Modulation (HAAM) wait to restore duration time in seconds."
	::= { hzCpHitlessAam 3 }

hzCpHitlessAamOrgSpecEoamMsg  OBJECT-TYPE
	SYNTAX     INTEGER {
		off (1),
		on (2)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Turns on or off the Hitless Automatic Adaptive Modulation (HAAM) Organizational Specific EOAM message to peer."
	::= { hzCpHitlessAam 4 }

-- ----------------
-- hzCpHitlessAamTable
-- ----------------

hzCpHitlessAamTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpHitlessAamEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of Hitless Aam."
	::= { hzCpHitlessAam 5 }

hzCpHitlessAamEntry  OBJECT-TYPE
	SYNTAX 	HzCpHitlessAamEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"Hitless Aam Status and Configurations."
	INDEX { hzCpWirelessPortIndex }
	::= { hzCpHitlessAamTable 1 }

HzCpHitlessAamEntry ::= SEQUENCE {
	hzCpWirelessPortIndex
		INTEGER,
	hzCpHitlessAamCurrentMode
		DisplayString
}

hzCpWirelessPortIndex  OBJECT-TYPE
	SYNTAX     INTEGER {
		wireless-port-1 (1)
	}
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each interface. "
	::= { hzCpHitlessAamEntry 1 }

hzCpHitlessAamCurrentMode  OBJECT-TYPE
	SYNTAX     DisplayString (SIZE(0..24))
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A read-only string specifing the currently running Hitless AAM Mode."
	::= { hzCpHitlessAamEntry 2 }

hzCpHitlessAamModeTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF HzCpHitlessAamModeEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A table of HitlessAam available modes."
	::= { hzCpHitlessAam 6 }

hzCpHitlessAamModeEntry  OBJECT-TYPE
	SYNTAX 	HzCpHitlessAamModeEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		"A HAAM mode entry containing all the HAAM system mode values"
	INDEX { hzCpHitlessAamModeIndex }
	::= { hzCpHitlessAamModeTable 1 }

HzCpHitlessAamModeEntry ::= SEQUENCE {
	hzCpHitlessAamModeIndex
		Integer32,
	hzCpHitlessAammModeName
		DisplayString,
	hzCpHitlessAamModeRange
		INTEGER
}

hzCpHitlessAamModeIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"A unique value for each Hitless Aam mode. "
	::= { hzCpHitlessAamModeEntry 1 }

hzCpHitlessAammModeName  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"The Hitless Aam mode name."
	::= { hzCpHitlessAamModeEntry 2 }

hzCpHitlessAamModeRange  OBJECT-TYPE
	SYNTAX     INTEGER {
		notAllowed (1),
		allowed (2),
		max (3)
	}
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Specifies the Hitless Aam available and max system mode. Only max option is allowed to be set in this release."
	DEFVAL  { notAllowed }
	::= { hzCpHitlessAamModeEntry 3 }

-- NOTIFICATION_TYPE
-- for generic traps real trap value of linkDown is 2.

hzCpLinkDown  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		ifIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"A linkDown trap signifies that the sending protocol entity recognizes a failure in one of the communication links represented in the agent's configuration."
	::= { hzCpSnmpNotifications 1 }

-- for generic traps real trap value of linkup is 3.

hzCpLinkUp  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		ifIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"A linkUp trap signifies that the sending protocol entity recognizes that one of the communication links represented in the agent's configuration has come up."
	::= { hzCpSnmpNotifications 2 }

hzCpPeerAuthenticationFailure  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Authentication of the peer horizon node has failed. The severity is critical, the probable cause is an incorrect authentication configuration on horizon faulty 
		 cable between the modem and radio, and recommended course of action is to check both ends of the link."
	::= { hzCpSnmpNotifications 3 }

hzCpPeerAuthenticationFailureCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The condition has cleared. The peer node is now authenticated."
	::= { hzCpSnmpNotifications 4 }

hzCpHitlessAamConfigMisMatch  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Hitless Automatic Adaptive modulation configuration mismatch"
	::= { hzCpSnmpNotifications 5 }

hzCpHitlessAamConfigMisMatchCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Hitless Automatic Adaptive modulation configuration mismatch cleared"
	::= { hzCpSnmpNotifications 6 }

hzCpHitlessAamModulationLowered  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Hitless Automatic Adaptive modulation on and system running on lowest modulation"
	::= { hzCpSnmpNotifications 7 }

hzCpHitlessAamModulationLoweredCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Hitless Automatic Adaptive modulation on  system running on higher modulation"
	::= { hzCpSnmpNotifications 8 }

hzCpHitlessAamModulationChangedEvent  NOTIFICATION-TYPE
	OBJECTS { hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Hitless Automatic Adaptive has changed modulation."
	::= { hzCpSnmpNotifications 9 }

hzCpAtpcConfigMismatch  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"ATPC configuration mismatch in the Horizon Compact Plus systems. ATPC is enabled at one system, but disabled at the other system."
	::= { hzCpSnmpNotifications 10 }

hzCpAtpcConfigMismatchCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"ATPC configuration mismatch in the Horizon systems is cleared. Both systems enabled ATPC."
	::= { hzCpSnmpNotifications 11 }

hzCpAtpcAutoDisabled  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"ATPC auto-disabled (transmitting at coordinated power)."
	::= { hzCpSnmpNotifications 12 }

hzCpAtpcAutoDisabledCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"ATPC has been enabled."
	::= { hzCpSnmpNotifications 13 }

hzCpNoSntpServersReachable  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Can't reach any of the SNTP servers"
	::= { hzCpSnmpNotifications 14 }

hzCpNoSntpServersReachableCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"At least one of SNTP servers is reachable"
	::= { hzCpSnmpNotifications 15 }

hzCpFrequencyFileInvalid  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"This trap is generated when the executing frequency file is invalid."
	::= { hzCpSnmpNotifications 16 }

hzCpAggregateDroppedFramesThreshold  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The ethernet dropped frames threshold has been detected above its threshold value."
	::= { hzCpSnmpNotifications 17 }

hzCpAggregateDroppedFramesThresholdCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The condition has cleared. The ethernet dropped frames threshold is now below its threshold value."
	::= { hzCpSnmpNotifications 18 }

hzCpQueueDroppedFramesThreshold  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpQBasedThresholdIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The ethernet queue based dropped frames threshold has been detected above its threshold value."
	::= { hzCpSnmpNotifications 19 }

hzCpQueueDroppedFramesThresholdCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpQBasedThresholdIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The condition has cleared. The ethernet queue based dropped frames threshold is now below its threshold value."
	::= { hzCpSnmpNotifications 20 }

hzCpBwUtilizationThreshold  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The ethernet bandwidth threshold has been detected above its threshold value."
	::= { hzCpSnmpNotifications 21 }

hzCpBwUtilizationThresholdCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The condition has cleared. The ethernet bandwidth threshold is now below its threshold value."
	::= { hzCpSnmpNotifications 22 }

hzCpQueueDepthThreshold  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpQBasedThresholdIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The ethernet queue depth threshold has been detected above its threshold value."
	::= { hzCpSnmpNotifications 23 }

hzCpQueueDepthThresholdCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpQBasedThresholdIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The condition has cleared. The ethernet queue depth threshold is now below its threshold value."
	::= { hzCpSnmpNotifications 24 }

hzCpRlsConfigMismatch  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"RLS Configuration mismatch"
	::= { hzCpSnmpNotifications 25 }

hzCpRlsConfigMismatchCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The condition has cleared. "
	::= { hzCpSnmpNotifications 26 }

hzCpRlsShutdownActivated  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"RLS shutdown activated"
	::= { hzCpSnmpNotifications 27 }

hzCpRlsShutdownActivatedCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"RLS shutdown activated condition has cleared."
	::= { hzCpSnmpNotifications 28 }

hzCpRlsQueueBasedShutdownActivated  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"RLS Queue-Based shutdown is activated"
	::= { hzCpSnmpNotifications 29 }

hzCpRlsQueueBasedShutdownActivatedCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"RLS Queue-Based shutdown is not activated"
	::= { hzCpSnmpNotifications 30 }

hzCpModemRxLossOfSignalLock  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Loss of signal lock from the demodulator. The severity is critical, the probable cause is lost synchronization with the far end, 
		 and recommended course of action is to check the operational state of both ends of the link."
	::= { hzCpSnmpNotifications 31 }

hzCpModemRxLossOfSignalLockCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Loss of signal lock from demodulator condition has cleared."
	::= { hzCpSnmpNotifications 32 }

hzCpModemSnrBelowThreshold  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The demodulator SNR performance worse than programmed levels. The severity is major, the probable cause 
		       is physical interference in the air link path or misalignment of the radios or severe weather conditions, 
		       and recommended course of action is to check the line of site between the horizon nodes."
	::= { hzCpSnmpNotifications 33 }

hzCpModemSnrBelowThresholdCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"SNR performance back to normal."
	::= { hzCpSnmpNotifications 34 }

hzCpModemEqualizerStressThreshold  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Equalizer Stress measured within the demodulator exceeds the threshold value."
	::= { hzCpSnmpNotifications 35 }

hzCpModemEqualizerStressThresholdCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Equalizer Stress measured within the demodulator is within the threshold value."
	::= { hzCpSnmpNotifications 36 }

hzCpRslBelowThreshold  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The RSL has been detected below its minimum threshold."
	::= { hzCpSnmpNotifications 37 }

hzCpRslBelowThresholdCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpModemIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The condition has cleared. RSL is now above its minimum threshold."
	::= { hzCpSnmpNotifications 38 }

hzCpRadioSynthLostLock  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The PLDRO/LO has lost lock on the radio. The severity is critical, the probable cause a faulty radio unit within AirPair, 
		 and recommended course of action is to replace the radio unit"
	::= { hzCpSnmpNotifications 39 }

hzCpRadioSynthLostLockCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"The radio PLDRO/LO lost lock condition has cleared."
	::= { hzCpSnmpNotifications 40 }

hzCpRadioCalTableNotAvailable  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Temp comp table not available."
	::= { hzCpSnmpNotifications 41 }

hzCpRadioCalTableNotAvailableCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Cal table is now available."
	::= { hzCpSnmpNotifications 42 }

hzCpRadioDrainCurrentOutOfLimit  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio current out of limits."
	::= { hzCpSnmpNotifications 43 }

hzCpRadioDrainCurrentOutOfLimitCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio current out of limits cleared."
	::= { hzCpSnmpNotifications 44 }

hzCpRadioPowerAmplifier  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio power amplifier"
	::= { hzCpSnmpNotifications 45 }

hzCpRadioPowerAmplifierCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpRadioIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio power amplifier cleared"
	::= { hzCpSnmpNotifications 46 }

hzCpTemperatureOutOfLimit  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio temperature out of limits."
	::= { hzCpSnmpNotifications 47 }

hzCpTemperatureOutOfLimitCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio temperature out of limits cleared."
	::= { hzCpSnmpNotifications 48 }

hzCpRedundancyConfigMismatch  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpAlarmInstanceIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Redundancy configuration mismatch either with partner or peer. Alarm instance 2 indicates partner and 3 indicates peer."
	::= { hzCpSnmpNotifications 49 }

hzCpRedundancyConfigMismatchCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpAlarmInstanceIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Redundancy configuration mismatch is cleared. Alarm instance 2 indicates partner and 3 indicates peer."
	::= { hzCpSnmpNotifications 50 }

hzCpRedundancyActiveOnSecondary  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Secondary unit is processing the user traffic."
	::= { hzCpSnmpNotifications 51 }

hzCpRedundancyActiveOnSecondaryCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Secondary unit processing the user traffic condition is cleared."
	::= { hzCpSnmpNotifications 52 }

hzCpRedundancyOperatingInForcedSwitch  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Redundancy operating in either forced-active or forced-standby switch mode."
	::= { hzCpSnmpNotifications 53 }

hzCpRedundancyOperatingInForcedSwitchCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Redundancy operating in either forced-active or forced-standby switch mode condition is cleared."
	::= { hzCpSnmpNotifications 54 }

hzCpRedundancyEnetCrossLink  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Redundnacy cross link."
	::= { hzCpSnmpNotifications 55 }

hzCpRedundancyEnetCrossLinkCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Redundancy cross link is cleared."
	::= { hzCpSnmpNotifications 56 }

hzCpRedundancyActiveUsingPartnerWLink  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Node is active but using its standby partner's radio to transport the data to the other end f the link. This alarm is raised on the active node."
	::= { hzCpSnmpNotifications 57 }

hzCpRedundancyActiveUsingPartnerWLinkCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Active node using partner wireless link condition is cleared."
	::= { hzCpSnmpNotifications 58 }

hzCpRedundancyStandbyWLinkInUse  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"This alarm is raised on the standby node (primary or secondary) when it's radio is used by the partner node for transporting the data to the other end of the link."
	::= { hzCpSnmpNotifications 59 }

hzCpRedundancyStandbyWLinkInUseCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Standby node wirless link is not used by the partner any more."
	::= { hzCpSnmpNotifications 60 }

hzCpRedundancyStandbyOnPrimary  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Primary node is in standby mode."
	::= { hzCpSnmpNotifications 61 }

hzCpRedundancyStandbyOnPrimaryCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Primary node in standby condition is cleared."
	::= { hzCpSnmpNotifications 62 }

hzCpX2DeliveringHalfCapacity  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"X2 operation is using single wireless link. System throughput is reduced to half."
	::= { hzCpSnmpNotifications 63 }

hzCpX2DeliveringHalfCapacityCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"X2 operation is back to two wireless links."
	::= { hzCpSnmpNotifications 64 }

hzCpBncCableSignalNotDetected  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"BNC signal is not detected. The cable may be disconnected."
	::= { hzCpSnmpNotifications 65 }

hzCpBncCableSignalNotDetectedCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"BNC signal error condition is cleared."
	::= { hzCpSnmpNotifications 66 }

hzCpEthernetSpeedReduced  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		ifIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Ethernet Speed negotiated below the Max Speed."
	::= { hzCpSnmpNotifications 67 }

hzCpEthernetSpeedReducedCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		ifIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Ethernet speed is set back to Max Speed."
	::= { hzCpSnmpNotifications 68 }

hzCpSynceLostLock  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Synce clock PLL lost lock"
	::= { hzCpSnmpNotifications 69 }

hzCpSynceLostLockCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Synce clock PLL restored from lost of lock"
	::= { hzCpSnmpNotifications 70 }

hzCpSynceSecondarySourceInUse  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Synce clock selected secondary source"
	::= { hzCpSnmpNotifications 71 }

hzCpSynceSecondarySourceInUseCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Synce clock selected primary source"
	::= { hzCpSnmpNotifications 72 }

hzCpUserSession  NOTIFICATION-TYPE
	OBJECTS { hzCpUserSessionUserName, 
		hzCpUserSessionUserConnectionType, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"A person has logged in to an ascii management port (telnet, local management port, or HTTP). The username is included in the Trap."
	::= { hzCpSnmpNotifications 73 }

hzCpUserSessionCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpUserSessionUserName, 
		hzCpUserSessionUserConnectionType, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"A person has logged out of an ascii management port (telnet, local management port, or HTTP). The username is included in the Trap."
	::= { hzCpSnmpNotifications 74 }

hzCpInvalidSystemConfig  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Overall system Configuration is not valid."
	::= { hzCpSnmpNotifications 75 }

hzCpInvalidSystemConfigCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"System configuration is valid."
	::= { hzCpSnmpNotifications 76 }

hzCpMibChangeNotSaved  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Mib has been changed but not saved."
	::= { hzCpSnmpNotifications 77 }

hzCpMibChangeNotSavedCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Mib has been saved."
	::= { hzCpSnmpNotifications 78 }

hzCpTransmitterLossOfSync  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"System has detected an internal fault."
	::= { hzCpSnmpNotifications 79 }

hzCpTransmitterLossOfSyncCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Internal fault has cleared."
	::= { hzCpSnmpNotifications 80 }

hzCpRadioLinearityCalError  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio linearity calibration error."
	::= { hzCpSnmpNotifications 81 }

hzCpRadioLinearityCalErrorCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Radio linearity calibration error is cleared."
	::= { hzCpSnmpNotifications 82 }

hzCpSynceConfigMismatch  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpAlarmInstanceIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Synce configuration mismatch with peer."
	::= { hzCpSnmpNotifications 83 }

hzCpSynceConfigMismatchCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpAlarmInstanceIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Synce configuration mismatch is cleared."
	::= { hzCpSnmpNotifications 84 }

hzCpCryptoPowerUpTestsFailed  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Crypto power up tests failed."
	::= { hzCpSnmpNotifications 85 }

hzCpCryptoConfigMismatch  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpAlarmInstanceIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Crypto configuration mismatch either with partner or peer. Alarm instance 2 indicates partner and 3 indicates peer."
	::= { hzCpSnmpNotifications 86 }

hzCpCryptoConfigMismatchCleared  NOTIFICATION-TYPE
	OBJECTS { hzCpAlarmConfigSeverity, 
		hzCpAlarmInstanceIndex, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"Crypto configuration mismatch is cleared. Alarm instance 2 indicates partner and 3 indicates peer."
	::= { hzCpSnmpNotifications 87 }

hzCpSystemTimeChange  NOTIFICATION-TYPE
	OBJECTS { hzCpDateAndTimeForLastTimeChange, 
		hzCpLastTimeChange, 
		horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"System Date Time changed"
	::= { hzCpSnmpNotifications 88 }

hzCpCodeCheck  NOTIFICATION-TYPE
	OBJECTS { horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		"A code check correction event has occured in the system."
	::= { hzCpSnmpNotifications 89 }

hzCpConfigChanged  NOTIFICATION-TYPE
	OBJECTS { horizonEquipmentOutTrapsCounter }
	STATUS     current
	DESCRIPTION 
		""
	::= { hzCpSnmpNotifications 90 }
END


