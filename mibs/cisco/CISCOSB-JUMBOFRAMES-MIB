CISCOSB-JUMBOFRAMES-MIB DEFINITIONS ::= BEGIN

-- Title:                CISCOSB ROS
--                       Private Jumbo Frames MIB
-- Version:              7.46
-- Date:                 15-Jan-2007

IMPORTS
    switch001                                    FROM CISCOSB-MIB
    OBJECT-TYPE, MODULE-IDENTITY                 FROM SNMPv2-SMI;

rlJumboFrames MODULE-IDENTITY
                LAST-UPDATED "200701020000Z"
                ORGANIZATION "Cisco Systems, Inc."

                CONTACT-INFO
                "Postal: 170 West Tasman Drive
                San Jose , CA 95134-1706
                USA

                
                Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

                DESCRIPTION
                      "This private MIB module defines Jumbo Frames private MIBs."
                REVISION "200701020000Z"
                DESCRIPTION
                      "Initial revision."
        ::= { switch001 91 }

rlJumboFramesCurrentStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Show the current Jumbo Frames status"
    ::= { rlJumboFrames 1 }

rlJumboFramesStatusAfterReset OBJECT-TYPE
    SYNTAX  INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "Set the Jumbo Frames status after reset"
    ::= { rlJumboFrames 2 }

END
