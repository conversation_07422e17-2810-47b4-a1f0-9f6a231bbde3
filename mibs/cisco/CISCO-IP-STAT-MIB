-- *****************************************************************
-- Cisco IP Statistics MIB file.
--
-- August 1997, Shu Tai
--
-- Copyright (c) 1997-1999 by Cisco Systems, Inc.
-- All rights reserved.
--
-- *****************************************************************
-- $Endlog$
--

CISCO-IP-STAT-MIB DEFINITIONS ::= BEGIN

IMPORTS
	MODULE-IDENTITY,
	OBJECT-TYPE,
	Integer32,
	<PERSON>auge32,
	Counter32,
	Counter64
		FROM SNMPv2-SMI
	MODULE-COMPLIANCE,
	OBJECT-GROUP
		FROM SNMPv2-CONF
	MacAddress,
	TEXTUAL-CONVENTION
		FROM SNMPv2-TC
	ifIndex
		FROM IF-MIB
	ciscoMgmt
		FROM CISCO-SMI;

ciscoIpStatMIB MODULE-IDENTITY
	LAST-UPDATED	"200112202300Z"
	ORGANIZATION	"Cisco Systems, Inc."
	CONTACT-INFO
		"	Cisco Systems
			Customer Service

		Postal:	170 W. Tasman Drive
			San Jose, CA  95134-1706
			USA

		   Tel: ****** 553-NETS

		E-mail: <EMAIL>"

	DESCRIPTION
		" Cisco IP Statistics MIB - Overview

    This MIB incorporates objects to provide support for the Cisco IP
    statistics as implemented in command interfaces

    ip accounting [ mac-address {input | ouput } |
    		   precedence { input | ouput } ]
    
    The above CLI provides additional IP statistics categorized by
    precedence of IP packets, as well as the MAC address associated
    with IP packets.  This is a supplement to those defined in
    OLD-CISCO-IP-MIB.my.
    "

	REVISION	"200112202300Z"
	DESCRIPTION
		"Added the following table and objects for 64 bit
		 packet/byte counters:
		 cipPrecedenceXTable ->
					cipPrecedenceHCSwitchedPkts,
					cipPrecedenceHCSwitchedBytes
		 cipMacXTable ->
				 cipMacHCSwitchedPkts,
				 cipMacHCSwitchedBytes "
	REVISION	"9707180000Z"
	DESCRIPTION
		"Initial version of this MIB module."
	::= { ciscoMgmt 84 }

ciscoIpStatMIBObjects OBJECT IDENTIFIER ::= { ciscoIpStatMIB 1 }

-- Subgroups:
--
-- IP MAC Statistic Table
-- IP Precedence Statistic Table

-- Textual Conventions

PacketSource ::= TEXTUAL-CONVENTION
	STATUS	current
	DESCRIPTION
		"The source of the packet the statistics is for.

		input	statistics of the input packets.
		output	statistics of the output packets.
		"
	SYNTAX  INTEGER { input(1), output(2) }

cipPrecedence	OBJECT IDENTIFIER ::= { ciscoIpStatMIBObjects 1 }
cipMacIf	OBJECT IDENTIFIER ::= { ciscoIpStatMIBObjects 2 }

cipPrecedenceTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF CipPrecedenceEntry
	MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "A table of entries sorted by the precedence of IP packets.
	The table is created and deleted via ip accounting
	command line interface."

        ::= { cipPrecedence 1 }

cipPrecedenceEntry OBJECT-TYPE
        SYNTAX CipPrecedenceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
		"An entry in the cipPrecedenceTable is created
		for each IP precedence value. There are 8 precedences
		total."
 
        INDEX {
		ifIndex,
		cipPrecedenceDirection,
		cipPrecedenceIpPrecedence
	}
        ::= { cipPrecedenceTable 1 }

CipPrecedenceEntry ::=
	SEQUENCE {
		cipPrecedenceDirection
               		PacketSource,
		cipPrecedenceIpPrecedence
                        Integer32,
		cipPrecedenceSwitchedPkts
                        Counter32,
		cipPrecedenceSwitchedBytes
                        Counter32
	}

cipPrecedenceDirection OBJECT-TYPE
	SYNTAX  PacketSource
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
       		"The data source for the object."
        ::= { cipPrecedenceEntry 1 }

cipPrecedenceIpPrecedence OBJECT-TYPE
	SYNTAX  Integer32 (0..7)
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                "The ip precedence value this object is collected upon."

        ::= { cipPrecedenceEntry 2 }

cipPrecedenceSwitchedPkts OBJECT-TYPE
	SYNTAX  Counter32
	UNITS	"packets"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"Traffic, in packets, at the cipPrecedenceIpPrecedence
		precedence."

	::= { cipPrecedenceEntry 3 }

cipPrecedenceSwitchedBytes OBJECT-TYPE
	SYNTAX  Counter32
	UNITS	"bytes"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"Traffic, in bytes, at the cipPrecedenceIpPrecedence
		precedence."

	::= { cipPrecedenceEntry 4 }

-- end of cipPrecedenceTable

cipMacTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF CipMacEntry
	MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
		"A table is created and deleted via ip accounting
		command line interface."
 
        ::= { cipMacIf 1 }

cipMacEntry OBJECT-TYPE
        SYNTAX CipMacEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
		"An entry in the cipMacTable is created
		for each unique MAC address which receives/sends
		IP packets.  Entries are deleted via the command
		line interface."
 
        INDEX { ifIndex, cipMacDirection, cipMacAddress }
        ::= { cipMacTable 1 }

CipMacEntry ::=
	SEQUENCE {
		cipMacDirection
               		PacketSource,
		cipMacAddress
                        MacAddress,
		cipMacSwitchedPkts
                        Counter32,
		cipMacSwitchedBytes
                        Counter32
	}

cipMacDirection OBJECT-TYPE
	SYNTAX  PacketSource
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
       		"The data source for the object."
        ::= { cipMacEntry 1 }

cipMacAddress OBJECT-TYPE
	SYNTAX  MacAddress
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                "The MAC address."

        ::= { cipMacEntry 2 }

cipMacSwitchedPkts OBJECT-TYPE
	SYNTAX  Counter32
	UNITS	"packets"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"The counter in packets with respect to cipMacAddress."

	::= { cipMacEntry 3 }

cipMacSwitchedBytes OBJECT-TYPE
	SYNTAX  Counter32
	UNITS	"bytes"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"The counter in bytes with respect to cipMacAddress."

	::= { cipMacEntry 4 }

-- end of cipMacTable

cipMacFreeTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF CipMacFreeEntry
	MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        	"A table of free space available to store new MAC address
		information."

        ::= { cipMacIf 2 }

cipMacFreeEntry OBJECT-TYPE
        SYNTAX CipMacFreeEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
		"The MAC free space table entry.  Entry is created when
		Entries are created and deleted via ip accounting
		command line interface."
 
        INDEX { ifIndex, cipMacFreeDirection }
        ::= { cipMacFreeTable 1 }

CipMacFreeEntry ::=
	SEQUENCE {
		cipMacFreeDirection
               		PacketSource,
		cipMacFreeCount
                        Gauge32
	}

cipMacFreeDirection OBJECT-TYPE
	SYNTAX  PacketSource
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
       		"The data source for the object."
        ::= { cipMacFreeEntry 1 }

cipMacFreeCount OBJECT-TYPE
	SYNTAX  Gauge32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Number of items in the MAC free space."

        ::= { cipMacFreeEntry 2 }

-- end of cipMacFreeTable

cipPrecedenceXTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF CipPrecedenceXEntry
	MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "This table contains additional objects for the
	cipPrecedenceTable."

        ::= { cipPrecedence 2 }

cipPrecedenceXEntry OBJECT-TYPE
        SYNTAX CipPrecedenceXEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
		"An entry containing additional management information
		applicable to a particular precedence value."
 
        AUGMENTS { cipPrecedenceEntry }
        ::= { cipPrecedenceXTable 1 }

CipPrecedenceXEntry ::=
	SEQUENCE {
		cipPrecedenceHCSwitchedPkts
                        Counter64,
		cipPrecedenceHCSwitchedBytes
                        Counter64
	}

cipPrecedenceHCSwitchedPkts OBJECT-TYPE
	SYNTAX  Counter64
	UNITS	"packets"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"Traffic, in packets, at the cipPrecedenceIpPrecedence
		precedence.  This object is the 64-bit version of
		cipPrecedenceSwitchedPkts."

	::= { cipPrecedenceXEntry 1 }

cipPrecedenceHCSwitchedBytes OBJECT-TYPE
	SYNTAX  Counter64
	UNITS	"bytes"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"Traffic, in bytes, at the cipPrecedenceIpPrecedence
		precedence.  This object is the 64-bit version of
		cipPrecedenceSwitchedBytes."

	::= { cipPrecedenceXEntry 2 }

-- end of cipPrecedenceXTable

cipMacXTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF CipMacXEntry
	MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "This table contains additional objects for the	cipMacTable."
 
        ::= { cipMacIf 3 }

cipMacXEntry OBJECT-TYPE
        SYNTAX CipMacXEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
		"An entry containing additional management information
		applicable to a particular MAC address."
 
        AUGMENTS { cipMacEntry }
        ::= { cipMacXTable 1 }

CipMacXEntry ::=
	SEQUENCE {
		cipMacHCSwitchedPkts
                        Counter64,
		cipMacHCSwitchedBytes
                        Counter64
	}

cipMacHCSwitchedPkts OBJECT-TYPE
	SYNTAX  Counter64
	UNITS	"packets"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"The high capacity counter in packets with respect to
		cipMacAddress.  This object is the 64-bit version of
		cipMacSwitchedPkts."

	::= { cipMacXEntry 1 }

cipMacHCSwitchedBytes OBJECT-TYPE
	SYNTAX  Counter64
	UNITS	"bytes"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
		"The counter in bytes with respect to cipMacAddress.
		This object is the 64-bit version of
		cipMacSwitchedBytes."

	::= { cipMacXEntry 2 }

-- end of cipMacXTable

-- conformance information

ciscoIpStatMIBConformance OBJECT IDENTIFIER ::= { ciscoIpStatMIB 3 }
ciscoIpStatMIBCompliances OBJECT IDENTIFIER ::=
  { ciscoIpStatMIBConformance 1 }
ciscoIpStatMIBGroups      OBJECT IDENTIFIER ::=
  { ciscoIpStatMIBConformance 2 }


-- compliance statement

ciscoIpStatMIBCompliance MODULE-COMPLIANCE
	STATUS	deprecated -- deprecated by ciscoIpStatMIBComplianceRev2
	DESCRIPTION
		"The compliance statement for entities which implement
		the IP Statistics on a Cisco router."
	MODULE	-- this module
		MANDATORY-GROUPS { ciscoIpStatMIBGroup }
	::= { ciscoIpStatMIBCompliances 1 }

ciscoIpStatMIBComplianceRev2 MODULE-COMPLIANCE
	STATUS	current
	DESCRIPTION
		"The compliance statement for entities which implement
		the IP Statistics on a Cisco router.
		This deprecates ciscoIpStatMIBCompliance to provide
		64-bit versions of packet and byte counter objects."
	MODULE	-- this module
		MANDATORY-GROUPS { ciscoIpStatMIBGroup }

		GROUP	   ciscoIpStatHCMIBGroup
		DESCRIPTION
			"This group is recommended for high speed
			(high speed interfaces transmit and receive at
			speeds over 20,000,000 bits/second) network
			interfaces."

	::= { ciscoIpStatMIBCompliances 2 }

-- units of conformance

ciscoIpStatMIBGroup OBJECT-GROUP
	OBJECTS {
		cipPrecedenceSwitchedPkts,
		cipPrecedenceSwitchedBytes,
		cipMacSwitchedPkts,
		cipMacSwitchedBytes,
		cipMacFreeCount
	}
	STATUS	current
	DESCRIPTION
		"A collection of objects providing IP Statistics
		monitoring.
		For interfaces that operate at speeds greater than 
		20,000,000 bits/second, it is recommended that the HC
		counters in the optional compliance section be supported
		and used."
	::= { ciscoIpStatMIBGroups 1 }

ciscoIpStatHCMIBGroup OBJECT-GROUP
	OBJECTS {
		cipPrecedenceHCSwitchedPkts,
		cipPrecedenceHCSwitchedBytes,
		cipMacHCSwitchedPkts,
		cipMacHCSwitchedBytes
	}
	STATUS	current
	DESCRIPTION
		"A collection of objects providing IP Statistics
		monitoring. This is recommended for high speed (high
		speed interfaces transmit and receive at speeds over
		20,000,000 bits/second) network interfaces."
	::= { ciscoIpStatMIBGroups 2 }

END
