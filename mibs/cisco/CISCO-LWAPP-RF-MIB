-- *******************************************************************
-- CISCO-LWAPP-RF-MIB.my
-- This MIB helps to manage the RF parameters on the controller
-- June 2011, <PERSON><PERSON>
--   
-- Copyright (c) 2011, 2016-2018 by Cisco Systems, Inc.
-- All rights reserved.
-- *******************************************************************

CISCO-LWAPP-RF-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    Unsigned32,
    Integer32
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    OBJECT-GROUP
        FROM SNMPv2-CONF
    TEXTUAL-CONVENTION,
    RowStatus,
    TruthValue,
    StorageType
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    CLApIfType
        FROM CISCO-LWAPP-TC-MIB
    cLAPGroupName
        FROM CISCO-LWAPP-WLAN-MIB
    ciscoMgmt
        FROM CISCO-SMI;


ciscoLwappRFMIB MODULE-IDENTITY
    LAST-UPDATED    "201811150000Z"
    ORGANIZATION    "Cisco Systems Inc."
    CONTACT-INFO
            "Cisco Systems,
            Customer Service
            Postal: 170 West Tasman Drive
            San Jose, CA  95134
            USA
            Tel: ****** 553-NETS

            Email: <EMAIL>"
    DESCRIPTION
        "This MIB is intended to be implemented on all those
        devices operating as Central Controllers (CC)  that
        terminate the Light Weight Access Point Protocol
        tunnel from Cisco Light-weight LWAPP Access Points.

        This MIB helps to manage the Radio Frequency (RF) 
        parameters on the controller.

        The relationship between CC and the LWAPP APs
        can be depicted as follows:

        +......+     +......+     +......+           +......+
        +      +     +      +     +      +           +      +
        +  CC  +     +  CC  +     +  CC  +           +  CC  +
        +      +     +      +     +      +           +      +
        +......+     +......+     +......+           +......+
        ..            .             .                 .
        ..            .             .                 .
        .  .            .             .                 .
        .    .            .             .                 .
        .      .            .             .                 .
        .        .            .             .                 .
        +......+ +......+     +......+      +......+          +......+
        +      + +      +     +      +      +      +          +      +
        +  AP  + +  AP  +     +  AP  +      +  AP  +          +  AP  +
        +      + +      +     +      +      +      +          +      +
        +......+ +......+     +......+      +......+          +......+
        .              .             .                 .
        .  .              .             .                 .
        .    .              .             .                 .
        .      .              .             .                 .
        .        .              .             .                 .
        +......+ +......+     +......+      +......+          +......+
        +      + +      +     +      +      +      +          +      +
        +  MN  + +  MN  +     +  MN  +      +  MN  +          +  MN  +
        +      + +      +     +      +      +      +          +      +
        +......+ +......+     +......+      +......+          +......+

        The LWAPP tunnel exists between the controller and
        the APs.  The MNs communicate with the APs through
        the protocol defined by the 802.11 standard.

        LWAPP APs, upon bootup, discover and join one of the
        controllers and the controller pushes the configuration,
        that includes the WLAN parameters, to the LWAPP APs.
        The APs then encapsulate all the 802.11 frames from
        wireless clients inside LWAPP frames and forward
        the LWAPP frames to the controller.

                           GLOSSARY

        Access Point ( AP )

        An entity that contains an 802.11 medium access
        control ( MAC ) and physical layer ( PHY ) interface
        and provides access to the distribution services via
        the wireless medium for associated clients.  

        LWAPP APs encapsulate all the 802.11 frames in
        LWAPP frames and sends it to the controller to which
        it is logically connected to.

        Central Controller ( CC )

        The central entity that terminates the LWAPP protocol
        tunnel from the LWAPP APs.  Throughout this MIB,
        this entity also referred to as 'controller'.

        Light Weight Access Point Protocol ( LWAPP ) 

        This is a generic protocol that defines the
        communication between the Access Points and the
        controllers.

        Mobile Node ( MN )

        A roaming 802.11 wireless device in a wireless
        network associated with an access point. 

        802.1x

        The IEEE ratified standard for enforcing port based
        access control.  This was originally intended for
        use on wired LANs and later extended for use in
        802.11 WLAN environments.  This defines an
        architecture with three main parts - a supplicant
        (Ex. an 802.11 wireless client), an authenticator
        (the AP) and an authentication server(a Radius
        server).  The authenticator passes messages back
        and forth between the supplicant and the
        authentication server to enable the supplicant
        get authenticated to the network.

        Radio Frequency ( RF )

        Radio frequency (RF) is a rate of oscillation in the 
        range of about 3 kHz to 300 GHz, which corresponds to 
        the frequency of radio waves, and the 
        alternating currents which carry radio signals.

        Received Signal Strength Indicator ( RSSI )

        A measure of the strength of the signal as
        observed by the entity that received it,
        expressed in 'dbm'.

        Coverage Hole Detection ( CHD )

        If clients on an Access Point are detected at low 
        RSSI levels, it is considered a coverage hole  
        by the Access Points. This indicates the existence 
        of an area where clients are continually getting poor 
        signal coverage, without having a viable location to 
        roam to.


        REFERENCE

        [1] Wireless LAN Medium Access Control ( MAC ) and
        Physical Layer ( PHY ) Specifications.

        [2] Draft-obara-capwap-lwapp-00.txt, IETF Light 
        Weight Access Point Protocol 

        [3] IEEE 802.11 - The original 1 Mbit/s and 2 Mbit/s, 
        2.4 GHz RF and IR standard."
    REVISION        "201811150000Z"
    DESCRIPTION
        "Added below object to the cLRFProfileTable
         - cLRFProfileAirTimeAllocation
         - cLRFProfileAirTimeFairnessMode
         - cLRFProfileAirTimeFairnessOptimization
         - cLRFProfileUnusedChannelList
         - cLRFProfileShutdown         
         - cLRFProfileBridgeClientAccess
         - cLRFProfileRxSopThresholdCustom
         Modified below object of cLRFProfileTable
         - cLRFProfileRxSopThreshold
         Added new table cLRFProfileRemoveChannelTable
         and cLRFProfileAddChannelTable"
    REVISION        "201707070000Z"
    DESCRIPTION
        "Added new table cLRFProfileChannelListTable."
    REVISION        "201204270000Z"
    DESCRIPTION
        "Add 11n MCS rates support in profile,
        cLRFProfileMcsDataRateTable is added for
        this rate setting."
    REVISION        "201201270000Z"
    DESCRIPTION
        "Below new objects have been added to the cLRFProfileTable
        cLRFProfileHighDensityMaxRadioClients
        cLRFProfileBandSelectProbeResponse
        cLRFProfileBandSelectCycleCount
        cLRFProfileBandSelectCycleThreshold
        cLRFProfileBandSelectExpireSuppression
        cLRFProfileBandSelectExpireDualBand
        cLRFProfileBandSelectClientRSSI
        cLRFProfileLoadBalancingWindowSize
        cLRFProfileCHDDataRSSIThreshold
        cLRFProfileCHDVoiceRSSIThreshold
        cLRFProfileCHDClientExceptionLevel
        cLRFProfileCHDCoverageExceptionLevel
        cLRFProfileMulticastDataRate
        cLRFProfileBandSelectClientMidRSSI
        cLRFProfileClientNetworkPreference

        One new scalar object has been added
        cLRFProfileOutOfBoxAPConfig
        cLRFProfileOutOfBoxAPPersistenceConfig"
    REVISION        "201111010000Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 778 }


ciscoLwappRFMIBNotifs  OBJECT IDENTIFIER
    ::= { ciscoLwappRFMIB 0 }

ciscoLwappRFMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoLwappRFMIB 1 }

ciscoLwappRFMIBConform  OBJECT IDENTIFIER
    ::= { ciscoLwappRFMIB 2 }

ciscoLwappRFConfig  OBJECT IDENTIFIER
    ::= { ciscoLwappRFMIBObjects 1 }

ciscoLwappRFGlobalObjects  OBJECT IDENTIFIER
    ::= { ciscoLwappRFMIBObjects 2 }


CiscoLwappRFApDataRates ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "This field indicates the data rates supported by an AP
        'disabled'       
             The rate is not supported by the AP
        'supported'      
            The rate is supported by the AP
        'mandatoryRate' 
            The rate is required by the AP
        'notApplicable' 
            The rate is notApplicable."
    SYNTAX          INTEGER  {
                        disabled(0),
                        supported(1),
                        mandatoryRate(2),
                        notApplicable(3)
                    }
-- ********************************************************************
-- *       AP Groups RF Profile
-- ********************************************************************

cLAPGroupsRFProfileTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CLAPGroupsRFProfileEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table lists the mapping between an RF profile
        and an AP group."
    ::= { ciscoLwappRFConfig 1 }

cLAPGroupsRFProfileEntry OBJECT-TYPE
    SYNTAX          CLAPGroupsRFProfileEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing the configuration attributes
        that affect the operation of the APs within
        a group.

        Entries can be added/deleted by explicit management 
        action from NMS/EMS through the 'bsnAPGroupsVlanRowStatus' 
        object in bsnAPGroupsVlanTable as defined by the 
        AIRESPACE-WIRELESS-MIB."
    INDEX           { cLAPGroupName } 
    ::= { cLAPGroupsRFProfileTable 1 }

CLAPGroupsRFProfileEntry ::= SEQUENCE {
        cLAPGroups802dot11bgRFProfileName SnmpAdminString,
        cLAPGroups802dot11aRFProfileName  SnmpAdminString
}

cLAPGroups802dot11bgRFProfileName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the RF profile name assigned to this
        site on the 802.11bg radio. This profile being assigned should 
        exist in the 'cLRFProfileTable'. To disassociate a profile with 
        this site a string of zero length should be set." 
    ::= { cLAPGroupsRFProfileEntry 1 }

cLAPGroups802dot11aRFProfileName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the RF profile name assigned to this
        site on the 802.11a radio. This profile being assigned should 
        exist in the 'cLRFProfileTable'. To disassociate a profile with 
        this site a string of zero length should be set." 
    ::= { cLAPGroupsRFProfileEntry 2 }
 

-- ********************************************************************
-- *       RF Profile Table
-- ********************************************************************

cLRFProfileTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CLRFProfileEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table lists the configuration for each
        RF profile."
    ::= { ciscoLwappRFConfig 2 }

cLRFProfileEntry OBJECT-TYPE
    SYNTAX          CLRFProfileEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing the configuration attributes
        that affect the operation of  802.11 RF domain.

        Entries can be added/deleted by explicit management 
        action from NMS/EMS or through user console."
    INDEX           { cLRFProfileName } 
    ::= { cLRFProfileTable 1 }

CLRFProfileEntry ::= SEQUENCE {
        cLRFProfileName                        SnmpAdminString,
        cLRFProfileDescr                       SnmpAdminString,
        cLRFProfileTransmitPowerMin            Integer32,
        cLRFProfileTransmitPowerMax            Integer32,
        cLRFProfileTransmitPowerThresholdV1    Integer32,
        cLRFProfileTransmitPowerThresholdV2    Integer32,
        cLRFProfileDataRate1Mbps               CiscoLwappRFApDataRates,
        cLRFProfileDataRate2Mbps               CiscoLwappRFApDataRates,
        cLRFProfileDataRate5AndHalfMbps        CiscoLwappRFApDataRates,
        cLRFProfileDataRate11Mbps              CiscoLwappRFApDataRates,
        cLRFProfileDataRate6Mbps               CiscoLwappRFApDataRates,
        cLRFProfileDataRate9Mbps               CiscoLwappRFApDataRates,
        cLRFProfileDataRate12Mbps              CiscoLwappRFApDataRates,
        cLRFProfileDataRate18Mbps              CiscoLwappRFApDataRates,
        cLRFProfileDataRate24Mbps              CiscoLwappRFApDataRates,
        cLRFProfileDataRate36Mbps              CiscoLwappRFApDataRates,
        cLRFProfileDataRate48Mbps              CiscoLwappRFApDataRates,
        cLRFProfileDataRate54Mbps              CiscoLwappRFApDataRates,
        cLRFProfileRadioType                   CLApIfType,
        cLRFProfileStorageType                 StorageType,
        cLRFProfileRowStatus                   RowStatus,
        cLRFProfileHighDensityMaxRadioClients  Unsigned32,
        cLRFProfileBandSelectProbeResponse     TruthValue,
        cLRFProfileBandSelectCycleCount        Unsigned32,
        cLRFProfileBandSelectCycleThreshold    Unsigned32,
        cLRFProfileBandSelectExpireSuppression Unsigned32,
        cLRFProfileBandSelectExpireDualBand    Unsigned32,
        cLRFProfileBandSelectClientRSSI        Integer32,
        cLRFProfileLoadBalancingWindowSize     Unsigned32,
        cLRFProfileLoadBalancingMaxDenialCount Unsigned32,
        cLRFProfileCHDDataRSSIThreshold        Integer32,
        cLRFProfileCHDVoiceRSSIThreshold       Integer32,
        cLRFProfileCHDClientExceptionLevel     Unsigned32,
        cLRFProfileCHDCoverageExceptionLevel   Unsigned32,
        cLRFProfileMulticastDataRate           Unsigned32,
        cLRFProfile11nOnly                     TruthValue,
        cLRFProfileHDClientTrapThreshold       Unsigned32,
        cLRFProfileInterferenceThreshold       Unsigned32,
        cLRFProfileNoiseThreshold              Integer32,
        cLRFProfileUtilizationThreshold        Unsigned32,
        cLRFProfileDCAForeignContribution      TruthValue,
        cLRFProfileDCAChannelWidth             INTEGER,
        cLRFProfileDCAChannelList              SnmpAdminString,
        cLRFProfileRxSopThreshold              INTEGER,
        cLRFProfileHSRMode                     TruthValue,
        cLRFProfileHSRNeighborTimeoutFactor    Unsigned32,
        cLRFProfileBandSelectClientMidRSSI     Integer32,
        cLRFProfileClientNetworkPreference     INTEGER,
        cLRFProfileUnusedChannelList           SnmpAdminString,
        cLRFProfileShutdown                    TruthValue,
        cLRFProfileAirTimeFairnessMode         INTEGER,
        cLRFProfileAirTimeFairnessOptimization INTEGER,
        cLRFProfileBridgeClientAccess          TruthValue,
        cLRFProfileAirTimeAllocation           Unsigned32,
        cLRFProfileRxSopThresholdCustom        Integer32
}

cLRFProfileName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (1..64))
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object uniquely identifies a RF Profile." 
    ::= { cLRFProfileEntry 1 }

cLRFProfileDescr OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies a human-readable description of the
        profile." 
    ::= { cLRFProfileEntry 2 }

cLRFProfileTransmitPowerMin OBJECT-TYPE
    SYNTAX          Integer32 (-10..30)
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the lower bound of transmit
        power value supported by an AP."
    DEFVAL          { -10 } 
    ::= { cLRFProfileEntry 3 }

cLRFProfileTransmitPowerMax OBJECT-TYPE
    SYNTAX          Integer32 (-10..30)
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the uppoer bound of transmit
        power value supported by an AP."
    DEFVAL          { 30 } 
    ::= { cLRFProfileEntry 4 }

cLRFProfileTransmitPowerThresholdV1 OBJECT-TYPE
    SYNTAX          Integer32 (-80..-50)
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the transmit power control
        version 1 threshold for the radio resource management 
        algorithm."
    DEFVAL          { -70 } 
    ::= { cLRFProfileEntry 5 }

cLRFProfileTransmitPowerThresholdV2 OBJECT-TYPE
    SYNTAX          Integer32 (-80..-50)
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the transmit power control
        version 2 threshold for the radio resource management 
        algorithm."
    DEFVAL          { -67 } 
    ::= { cLRFProfileEntry 6 }

cLRFProfileDataRate1Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { mandatoryRate } 
    ::= { cLRFProfileEntry 7 }

cLRFProfileDataRate2Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { mandatoryRate } 
    ::= { cLRFProfileEntry 8 }

cLRFProfileDataRate5AndHalfMbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { mandatoryRate } 
    ::= { cLRFProfileEntry 9 }

cLRFProfileDataRate11Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { mandatoryRate } 
    ::= { cLRFProfileEntry 10 }

cLRFProfileDataRate6Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 11 }

cLRFProfileDataRate9Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 12 }

cLRFProfileDataRate12Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 13 }

cLRFProfileDataRate18Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 14 }

cLRFProfileDataRate24Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 15 }

cLRFProfileDataRate36Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 16 }

cLRFProfileDataRate48Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 17 }

cLRFProfileDataRate54Mbps OBJECT-TYPE
    SYNTAX          CiscoLwappRFApDataRates
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration for this
        data rate."
    DEFVAL          { supported } 
    ::= { cLRFProfileEntry 18 }

cLRFProfileRadioType OBJECT-TYPE
    SYNTAX          CLApIfType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to configure the radio
        type for this profile." 
    ::= { cLRFProfileEntry 19 }

cLRFProfileStorageType OBJECT-TYPE
    SYNTAX          StorageType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the storage type for this
        conceptual row."
    DEFVAL          { nonVolatile } 
    ::= { cLRFProfileEntry 20 }

cLRFProfileRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This is the status column for this row and used
        to create and delete specific instances of rows 
        in this table." 
    ::= { cLRFProfileEntry 21 }

cLRFProfileHighDensityMaxRadioClients OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the maximum number
        of clients per AP radio in high density environment."
    DEFVAL          { 200 } 
    ::= { cLRFProfileEntry 22 }

cLRFProfileBandSelectProbeResponse OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the AP's probe response
        with clients to verify whether client can associate
        on both 2.4 GHz and 5Ghz spectrum.
        When set to true, AP suppresses probe response 
        to new clients for all SSIDs that are not being 
        Band Select disabled."
    DEFVAL          { false } 
    ::= { cLRFProfileEntry 23 }

cLRFProfileBandSelectCycleCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies number of times a client probe (2.4 G-Hz)
        is suppressed before the AP starts responding to the probes."
    DEFVAL          { 2 } 
    ::= { cLRFProfileEntry 24 }

cLRFProfileBandSelectCycleThreshold OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies time threshold for a new scanning
        mobile station period. Mobile station cycle counter will 
        increment only if mobile station scans same channel any 
        time after threshold is passed."
    DEFVAL          { 200 } 
    ::= { cLRFProfileEntry 25 }

cLRFProfileBandSelectExpireSuppression OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the duration at which
        an AP waits to conduct another probe suppression."
    DEFVAL          { 20 } 
    ::= { cLRFProfileEntry 26 }

cLRFProfileBandSelectExpireDualBand OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the timeout duration until
        which an AP should not respond to 2.4ghz probe requests 
        from a dual-band client. Once this band select duration 
        expires the client will be marked as a non-dual band client 
        and AP will start responding to the 2.4 Ghz probes."
    DEFVAL          { 60 } 
    ::= { cLRFProfileEntry 27 }

cLRFProfileBandSelectClientRSSI OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies minimum mobile station RSSI threshold.
        Mobile stations having RSSI below this value will not be
        recorded in the suppressed table.
        This configuration filters out far away mobile stations with
        weaker signal strength. This will limit the number of
        mobile stations on the table to a reasonable amount."
    DEFVAL          { -80 } 
    ::= { cLRFProfileEntry 28 }

cLRFProfileLoadBalancingWindowSize OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the minimum number of clients
        to be associated on an AP in order to trigger load balancing."
    DEFVAL          { 5 } 
    ::= { cLRFProfileEntry 29 }

cLRFProfileLoadBalancingMaxDenialCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies maximum number of association
        rejections sent by AP to a mobile station for a given sequence
        of association.

        When a mobile station tries to associate on wireless network, it
        sends association request to AP. If AP is overloaded and
        load balancing is enable on controller, AP will send a
        denial to association request. If there are no other AP
        in the vicinity of mobile station, it will try to associate same
        AP again. So to restrict the number of denial sent from
        AP, cldLoadBalancingMaxDenialCount is defined. After maximum
        denial count is reached mobile station will be able to
        associate."
    DEFVAL          { 3 } 
    ::= { cLRFProfileEntry 30 }

cLRFProfileCHDDataRSSIThreshold OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the RSSI value for non-voice clients
        i.e., clients sending only data traffic
        to trigger coverage event."
    DEFVAL          { -80 } 
    ::= { cLRFProfileEntry 31 }

cLRFProfileCHDVoiceRSSIThreshold OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the RSSI value for voice
        clients to trigger coverage event."
    DEFVAL          { -80 } 
    ::= { cLRFProfileEntry 32 }

cLRFProfileCHDClientExceptionLevel OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the minimum
        number of clients required to trigger 
        coverage hole."
    DEFVAL          { 3 } 
    ::= { cLRFProfileEntry 33 }

cLRFProfileCHDCoverageExceptionLevel OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "%"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the minimum
        percentage of clients in the coverage hole region
        of the AP, required to trigger a 
        coverage hole exception"
    DEFVAL          { 25 } 
    ::= { cLRFProfileEntry 34 }

cLRFProfileMulticastDataRate OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "Mbps"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the minimum
        data rate at which multicast clients
        can associate to AP.
        A value 0 indicates that AP will automatically 
        adjust data rates."
    DEFVAL          { 0 } 
    ::= { cLRFProfileEntry 35 }

cLRFProfile11nOnly OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies if 11n-client-only mode is
        enabled.
        A value of 'true' indicates 11n-client-only mode
        enabled.
        A value of 'false' indicates 11n-client-only mode
        disabled."
    DEFVAL          { false } 
    ::= { cLRFProfileEntry 36 }

cLRFProfileHDClientTrapThreshold OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the threshold number
        of clients per AP radio to trigger a trap.
        The trap ciscoLwappApClientThresholdNotify
        will be triggered once the count of clients
        on the AP radio reaches this limit. A value
        of zero indicates that the trap is disabled." 
    ::= { cLRFProfileEntry 37 }

cLRFProfileInterferenceThreshold OBJECT-TYPE
    SYNTAX          Unsigned32 (0..100)
    UNITS           "percent"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies interference threshold in %
        traps are generated when interference exceeds this value ." 
    ::= { cLRFProfileEntry 38 }

cLRFProfileNoiseThreshold OBJECT-TYPE
    SYNTAX          Integer32 (-127..0)
    UNITS           "dBm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the threshold number
        of noise threshold between -127 and 0 dBm." 
    ::= { cLRFProfileEntry 39 }

cLRFProfileUtilizationThreshold OBJECT-TYPE
    SYNTAX          Unsigned32 (0..100)
    UNITS           "percent"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the threshold of bandwidth (%)
        being utilized by an access-point. 
        Traps are sent when this value is exceeded" 
    ::= { cLRFProfileEntry 40 }

cLRFProfileDCAForeignContribution OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies whether foreign interference
        is taken into account for the DCA metrics.
        A value of 'true' indicates that foreign interference is 
        is considered when DCA metrics is derived. A value of 'false' 
        indicates foreign interference is not taken into account 
        when deriving the DCA metrics." 
    ::= { cLRFProfileEntry 41 }

cLRFProfileDCAChannelWidth OBJECT-TYPE
    SYNTAX          INTEGER  {
                        twenty(1),
                        forty(2),
                        eighty(3),
                        onesixty(4),
                        best(5)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies how the system performs DCA
        channel width selection for the RFProfile
             twenty     - 20MHz channel width supported by
                          this radio.
             forty     - 40MHz channel width supported by this radio.
             eighty     - 80MHz channel width supported by this radio.
             onesixty    - 160MHz channel width supported by this radio.
             best   - Dynamic channel width(160MHz, 80MHz, 40MHz or 20MHz) supported by this radio." 
    ::= { cLRFProfileEntry 42 }

cLRFProfileDCAChannelList OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (1..500))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the 802.11 channels available to the
        RF Profile. A comma separated list of integers." 
    ::= { cLRFProfileEntry 43 }

cLRFProfileRxSopThreshold OBJECT-TYPE
    SYNTAX          INTEGER  {
                        auto(0),
                        low(1),
                        medium(2),
                        high(3),
                        custom(4)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the receiver start of packet threshold
        for the rf profile.
           auto - Reverts radio receiver SOP to auto.
           high - Sets radio receiver SOP to high.
           low  - Sets radio receiver SOP to low.
           medium - Sets radio receiver SOP to medium.
           custom - Sets radio receiver SOP to Custom value."
    DEFVAL          { auto } 
    ::= { cLRFProfileEntry 44 }

cLRFProfileHSRMode OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies whether HSR mode
        for RF profile is enabled or not.
           A value of 'true' indicates that HSR is enabled. 
           A value of 'false' indicates that HSR is disabled." 
    ::= { cLRFProfileEntry 45 }

cLRFProfileHSRNeighborTimeoutFactor OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the timeout interval
        of neighbors that will be configured for RF profile.
        Neighbor timeout factor will be five times the neighbor interval" 
    ::= { cLRFProfileEntry 46 }

cLRFProfileBandSelectClientMidRSSI OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dbm"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies medium mobile station RSSI threshold.
        Mobile station between this value and cldBandSelectClientRssi
        will be recorded in suppressed table but will not be denied.
        The mobile station having RSSI stronger than this value will be recorded
        and denied."
    DEFVAL          { -80 } 
    ::= { cLRFProfileEntry 47 }

cLRFProfileClientNetworkPreference OBJECT-TYPE
    SYNTAX          INTEGER  {
                        default(0),
                        connectivity(1),
                        throughput(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the client network preference.
        default(0)       - RRM Algorithm has no preference on the clients.
        connectivity(1)  - RRM Algorithm has a preference on connectiviry of the clients.
        throughput(2)    - RRM Algorithm has a preference on high thoughput of the clients."
    DEFVAL          { default } 
    ::= { cLRFProfileEntry 48 }

cLRFProfileUnusedChannelList OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (1..500))
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This object specifies the 802.11 channels present in ths un-used
        channel list of RF Profile. A comma separated list of integers."
   ::= {cLRFProfileEntry 49 }

cLRFProfileShutdown OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies if rf profile is 
         disabled."
    DEFVAL          {true}
    ::= { cLRFProfileEntry 50 }

cLRFProfileAirTimeFairnessMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        disable(1),
                        ssid(2),
                        monitor(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the mode of fairness for ATF
         supported APs, joined to the controller.
         disable(1) - This indicates that fairness mode is 
                      disabled. 
         SSID Mode(2) - This indicates that fairness mode is SSID 
                        mode.
         Monitor Mode(3) - This indicates that fairness mode is 
                           monitor mode."
    DEFVAL          { 1 } 
    ::= { cLRFProfileEntry 51 }

cLRFProfileAirTimeFairnessOptimization OBJECT-TYPE
    SYNTAX          INTEGER  {
                        disable(1),
                        enable(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the optimization for ATF 
         supported APs, joined to the controller to be
         configured."
    DEFVAL          { 1 } 
    ::= { cLRFProfileEntry 52 }

cLRFProfileBridgeClientAccess OBJECT-TYPE
    SYNTAX      TruthValue  
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "This object specifies whether airtime
     allocation is enabled for the client access node."
    DEFVAL          { false } 
    ::= {cLRFProfileEntry 53 }

cLRFProfileAirTimeAllocation OBJECT-TYPE
    SYNTAX      Unsigned32(5..90)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "This object specifies the percentage for airtime
     allocation for the client access node."
    DEFVAL          { 5 } 
    ::= {cLRFProfileEntry 54 }

cLRFProfileRxSopThresholdCustom OBJECT-TYPE
    SYNTAX      Integer32(-85..-60)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "This object specifies the percentage for the receiver 
     start of packet custom threshold for the client access node."
    DEFVAL          { -85 } 
    ::= {cLRFProfileEntry 55 }

-- *******************************************************************
-- *     Out of Box AP config
-- *******************************************************************

cLRFProfileOutOfBoxAPConfig OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the out of box AP config feature.
        Enabling this feature will allow any AP that does not have 
        group name configured upon join to controller then
        it will be considered as out of box AP and it will be 
        moved to a special AP Group. Disabling this feature
        will only stop further subscription of new AP's to
        Out of Box AP group. A value of 'true' indicates that
        out of box AP feature is enabled and APs will be moved
        to newly created AP group out-of-box and shut of their
        radios. A value of 'false' indicates that out of box AP
        feature is disabled." 
    ::= { ciscoLwappRFGlobalObjects 1 }

cLRFProfileOutOfBoxAPPersistenceConfig OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the
        out of box AP group to be persistent or not.
        A value of 'true' indicates out-of-box persistence need to be
        enabled.
        A value of 'false' indicates out-of-box persistence need to be
        disabled." 
    ::= { ciscoLwappRFGlobalObjects 2 }

cLRFProfileMcsDataRateTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CLRFProfileMcsDataRateEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object specifies the 11n MCS rates supported by
        the RF profile, indexed by the MCS rate, ranging from 1
        to 32, corresponding to rate MCS-0, MCS-1, ... MCS-31."
    ::= { ciscoLwappRFConfig 3 }

cLRFProfileMcsDataRateEntry OBJECT-TYPE
    SYNTAX          CLRFProfileMcsDataRateEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing MCS date rate information applicable
        to a particular profile. Entry is added to the table when 
        the data rate is set to 'supported' or 'mandatory'."
    INDEX           {
                        cLRFProfileMcsName,
                        cLRFProfileMcsRate
                    } 
    ::= { cLRFProfileMcsDataRateTable 1 }

CLRFProfileMcsDataRateEntry ::= SEQUENCE {
        cLRFProfileMcsName        SnmpAdminString,
        cLRFProfileMcsRate        Unsigned32,
        cLRFProfileMcsRateSupport TruthValue
}

cLRFProfileMcsName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (1..64))
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object uniquely identifies a RF Profile." 
    ::= { cLRFProfileMcsDataRateEntry 1 }

cLRFProfileMcsRate OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object uniquely identifies the MCS data rate
        for a particular profile." 
    ::= { cLRFProfileMcsDataRateEntry 2 }

cLRFProfileMcsRateSupport OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable or disable the data
        rate. 
        A value of 'true' indicates MCS support is enabled. 
        A value of 'false' indicates MCS support is disabled."
    DEFVAL          { true } 
    ::= { cLRFProfileMcsDataRateEntry 3 }
 


cLRFProfileChannelListTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CLRFProfileChannelListEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object specifies the list of channels that is available for DCA
        of a RF profile."
    ::= { ciscoLwappRFConfig 4 }

cLRFProfileChannelListEntry OBJECT-TYPE
    SYNTAX          CLRFProfileChannelListEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing Channel List information applicable
        to a particular profile."
    INDEX           {
                        cLRFProfileName,
                        cLRFProfileChanNumber
                    } 
    ::= { cLRFProfileChannelListTable 1 }

CLRFProfileChannelListEntry ::= SEQUENCE {
        cLRFProfileChanNumber    Unsigned32,
        cLRFProfileChanAddRemove INTEGER,
        cLRFProfileChanRowStatus RowStatus
}

cLRFProfileChanNumber OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object uniquely identifies the channel
        for a particular profile." 
    ::= { cLRFProfileChannelListEntry 1 }

cLRFProfileChanAddRemove OBJECT-TYPE
    SYNTAX          INTEGER  {
                        add(1),
                        remove(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies a channel is to be added
        or removed from DCA list of the RFProfile.
        add - Add the channel to global DCA channel list.
        remove - Removed the channel from global DCA channel list." 
    ::= { cLRFProfileChannelListEntry 2 }

cLRFProfileChanRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This is the status column for this row and used
        to create and delete specific instances of rows 
        in this table." 
    ::= { cLRFProfileChannelListEntry 3 }
 
cLRFProfileRemoveChannelTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CLRFProfileRemoveChannelEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the removed channel information
        from default DCA channel list for an AP's 802.11
        interface using the RF profile. 
        Based on the country that an AP operates in, it 
        gets assigned to a regulatory domain. For a given
        regulatory domain a fixed number of channels are
        assigned automatically to an AP for a given interface.
        We can remove a channel from this default list and
        those channels will be put in this table.
        This table represents the removed DCA channels information.

        By default, there will not be any entries in this table.
        An entry can be created or deleted from this table
        using cLRFProfileRemoveChannelRowStatus."
    ::= { ciscoLwappRFConfig 5 }

cLRFProfileRemoveChannelEntry OBJECT-TYPE
    SYNTAX          CLRFProfileRemoveChannelEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry in this table provides the
        removed channel information from the 
        default DCA channel list for this RF profile"
    INDEX           {
                        cLRFProfileRemoveChannelName,
                        cLRFProfileRemoveChannelNum
                    }
    ::= { cLRFProfileRemoveChannelTable 1 }

CLRFProfileRemoveChannelEntry ::= SEQUENCE {
        cLRFProfileRemoveChannelName      SnmpAdminString,
        cLRFProfileRemoveChannelNum       Unsigned32,
        cLRFProfileRemovedChannelDcaState TruthValue,
        cLRFProfileRemoveChannelRowStatus RowStatus
}

cLRFProfileRemoveChannelName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (1..64))
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
                    "This object uniquely identifies a RF Profile."
    ::= { cLRFProfileRemoveChannelEntry 1 }

cLRFProfileRemoveChannelNum OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The channel nubmer for this entry. This value
        represents the unique channel number removed from
        default DCA channel list for this RF profile."
    ::= { cLRFProfileRemoveChannelEntry 2 }

cLRFProfileRemovedChannelDcaState OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies whether this channel is part
        of the dynamic channel allocation (DCA) Remove List. 
        A value of 'false' indicates that the DCA for this 
        channel is disabled."
    DEFVAL          { false }
    ::= { cLRFProfileRemoveChannelEntry 3 }

cLRFProfileRemoveChannelRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row.
        All writable objects in this row may be modified 
        at any time."
    ::= { cLRFProfileRemoveChannelEntry 4 }

cLRFProfileAddChannelTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CLRFProfileAddChannelEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the added channel information
        to the default DCA channel list in this RF profile. 
        Based on the country that an AP operates in, it 
        gets assigned to a regulatory domain. For a given
        regulatory domain a fixed number of channels are
        assigned automatically to an AP for a given interface.
        We can add a channel to this default list and those
        channels will be put in this table.
        This table represents the added channels information
        to the default DCA channel list.

        By default, there will not be any entries in this table.
        An entry can be created or deleted from this table
        using cLRFProfileAddChannelRowStatus."
    ::= { ciscoLwappRFConfig 6 }

cLRFProfileAddChannelEntry OBJECT-TYPE
    SYNTAX          CLRFProfileAddChannelEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry in this table provides the
        added channel information to the default
        DCA channel list in this RF profile."
    INDEX           {
                        cLRFProfileAddChannelName,
                        cLRFProfileAddChannelNum
                    }
    ::= { cLRFProfileAddChannelTable 1 }

CLRFProfileAddChannelEntry ::= SEQUENCE {
        cLRFProfileAddChannelName       SnmpAdminString,
        cLRFProfileAddChannelNum        Unsigned32,
        cLRFProfileAddedChannelDcaState TruthValue,
        cLRFProfileAddChannelRowStatus  RowStatus
}

cLRFProfileAddChannelName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (1..64))
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
                    "This object uniquely identifies a RF Profile."
    ::= { cLRFProfileAddChannelEntry 1 }

cLRFProfileAddChannelNum OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The channel nubmer for this entry.  This value represents
        the unique channel number added to the default DCA list in
        this RF profile."
    ::= { cLRFProfileAddChannelEntry 2 }

cLRFProfileAddedChannelDcaState OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies whether this channel is part
        of the dynamic channel allocation (DCA) Add List. 
        A value of 'true' indicates that the DCA for this 
        channel is enabled."
    DEFVAL          { true }
    ::= { cLRFProfileAddChannelEntry 3 }

cLRFProfileAddChannelRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row.
        All writable objects in this row may be modified 
        at any time."
    ::= { cLRFProfileAddChannelEntry 4 }

-- ********************************************************************
-- *    Compliance statements
-- ********************************************************************

ciscoLwappRFMIBCompliances  OBJECT IDENTIFIER
    ::= { ciscoLwappRFMIBConform 1 }

ciscoLwappRFMIBGroups  OBJECT IDENTIFIER
    ::= { ciscoLwappRFMIBConform 2 }


ciscoLwappRFMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the SNMP entities that
        implement the ciscoLwappRFMIB module.

        This compliance is deprecated and replaced by
        ciscoLwappRFMIBComplianceVer1 ."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoLwappRFConfigGroup }

    OBJECT          cLRFProfileRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    DESCRIPTION
        "An implementation is only required to support
        three of the six enumerated values of the
        RowStatus textual convention, specifically,
        'active', 'createAndGo' and 'destroy'.
        This compliance is deprecated and replaced 
        by ciscoLwappRFMIBComplianceVer1."
    ::= { ciscoLwappRFMIBCompliances 1 }

ciscoLwappRFMIBComplianceVer1 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the SNMP entities that
        implement the ciscoLwappRFMIB module."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoLwappRFConfigGroup1,
                        ciscoLwappRFGlobalConfigGroup
                    }

    OBJECT          cLRFProfileRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    DESCRIPTION
        "An implementation is only required to support
        three of the six enumerated values of the
        RowStatus textual convention, specifically,
        'active', 'createAndGo' and 'destroy'."
    ::= { ciscoLwappRFMIBCompliances 2 }

ciscoLwappRFMIBComplianceVer2 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the SNMP entities that
        implement the ciscoLwappRFMIB module.
        Added ciscoLwappRFConfigGroup2 to add object to raise
         trap when client count exceeds threshold and 
         ciscoLwappRFConfigGroup3 to address DCA settings"
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoLwappRFConfigGroup1,
                        ciscoLwappRFGlobalConfigGroup, 
                        ciscoLwappRFConfigGroup3
                    }
    ::= { ciscoLwappRFMIBCompliances 3 }

ciscoLwappRFMIBComplianceRev3 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the SNMP entities that
        implement the ciscoLwappRFMIB module."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoLwappRFConfigGroupVer1,
                        ciscoLwappRFConfigGroup1,
                        ciscoLwappRFConfigGroup3,
                        ciscoLwappRFConfigGroup4,
                        ciscoLwappRFGroupTrapThresholdConfig,
                        ciscoLwappRFGroupChannelConfig,
                        ciscoLwappRFGlobalConfigGroup,
                        ciscoLwappRFConfigGroupExtension1
                    }

    OBJECT          cLRFProfileRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    DESCRIPTION
        "An implementation is only required to support
        three of the six enumerated values of the
        RowStatus textual convention, specifically,
        'active', 'createAndGo' and 'destroy'."
    ::= { ciscoLwappRFMIBCompliances 4 }
    
ciscoLwappRFMIBComplianceRev4 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the SNMP entities that
        implement the ciscoLwappRFMIB module."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoLwappRFConfigGroupVer1,
                        ciscoLwappRFConfigGroup1,
                        ciscoLwappRFConfigGroup3,
                        ciscoLwappRFConfigGroup4,
                        ciscoLwappRFGroupTrapThresholdConfig,
                        ciscoLwappRFGroupChannelConfig,
                        ciscoLwappRFGlobalConfigGroup,
                        ciscoLwappRFConfigGroupExtension1,
                        ciscoLwappRFConfigGroup5,
                        ciscoLwappRFConfigGroup6,
                        ciscoLwappRFConfigGroup7
                    }
    OBJECT          cLRFProfileRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    DESCRIPTION
        "An implementation is only required to support
        three of the six enumerated values of the
        RowStatus textual convention, specifically,
        'active', 'createAndGo' and 'destroy'."
    ::= { ciscoLwappRFMIBCompliances 5 }

-- ********************************************************************
-- *    Units of conformance
-- ********************************************************************

ciscoLwappRFConfigGroup OBJECT-GROUP
    OBJECTS         {
                        cLAPGroups802dot11bgRFProfileName,
                        cLAPGroups802dot11aRFProfileName,
                        cLRFProfileDescr,
                        cLRFProfileTransmitPowerMin,
                        cLRFProfileTransmitPowerMax,
                        cLRFProfileTransmitPowerThresholdV1,
                        cLRFProfileTransmitPowerThresholdV2,
                        cLRFProfileDataRate1Mbps,
                        cLRFProfileDataRate2Mbps,
                        cLRFProfileDataRate5AndHalfMbps,
                        cLRFProfileDataRate11Mbps,
                        cLRFProfileDataRate6Mbps,
                        cLRFProfileDataRate9Mbps,
                        cLRFProfileDataRate12Mbps,
                        cLRFProfileDataRate18Mbps,
                        cLRFProfileDataRate24Mbps,
                        cLRFProfileDataRate36Mbps,
                        cLRFProfileDataRate48Mbps,
                        cLRFProfileDataRate54Mbps,
                        cLRFProfileRadioType,
                        cLRFProfileStorageType,
                        cLRFProfileRowStatus,
                        cLRFProfile11nOnly
                    }
    STATUS          deprecated
    DESCRIPTION
        "This collection of objects specifies the
        configuration of RF parameters on the controller 
        to be passed to an LWAPP AP.This config group 
        ciscoLwappRFConfigGroup is deprecated and replaced 
        by ciscoLwappRFConfigGroupVer1"
    ::= { ciscoLwappRFMIBGroups 1 }

-- ********************************************************************
-- *    Units of conformance
-- ********************************************************************

ciscoLwappRFConfigGroupVer1 OBJECT-GROUP
    OBJECTS         {
                        cLAPGroups802dot11bgRFProfileName,
                        cLAPGroups802dot11aRFProfileName,
                        cLRFProfileDescr,
                        cLRFProfileTransmitPowerMin,
                        cLRFProfileTransmitPowerMax,
                        cLRFProfileTransmitPowerThresholdV1,
                        cLRFProfileTransmitPowerThresholdV2,
                        cLRFProfileDataRate1Mbps,
                        cLRFProfileDataRate2Mbps,
                        cLRFProfileDataRate5AndHalfMbps,
                        cLRFProfileDataRate11Mbps,
                        cLRFProfileDataRate6Mbps,
                        cLRFProfileDataRate9Mbps,
                        cLRFProfileDataRate12Mbps,
                        cLRFProfileDataRate18Mbps,
                        cLRFProfileDataRate24Mbps,
                        cLRFProfileDataRate36Mbps,
                        cLRFProfileDataRate48Mbps,
                        cLRFProfileDataRate54Mbps,
                        cLRFProfileRadioType,
                        cLRFProfileStorageType,
                        cLRFProfileRowStatus,
                        cLRFProfile11nOnly,
                        cLRFProfileHSRMode,
                        cLRFProfileHSRNeighborTimeoutFactor,
                        cLRFProfileOutOfBoxAPPersistenceConfig,
                        cLRFProfileMcsRateSupport
                    }
    STATUS          current
    DESCRIPTION
        "This collection of objects specifies the
        configuration of RF parameters on the controller 
        to be passed to an LWAPP AP."
    ::= { ciscoLwappRFMIBGroups 2 }

ciscoLwappRFConfigGroup1 OBJECT-GROUP
    OBJECTS         {
                        cLRFProfileHighDensityMaxRadioClients,
                        cLRFProfileBandSelectProbeResponse,
                        cLRFProfileBandSelectCycleCount,
                        cLRFProfileBandSelectCycleThreshold,
                        cLRFProfileBandSelectExpireSuppression,
                        cLRFProfileBandSelectExpireDualBand,
                        cLRFProfileBandSelectClientRSSI,
                        cLRFProfileLoadBalancingWindowSize,
                        cLRFProfileLoadBalancingMaxDenialCount,
                        cLRFProfileCHDDataRSSIThreshold,
                        cLRFProfileCHDVoiceRSSIThreshold,
                        cLRFProfileCHDClientExceptionLevel,
                        cLRFProfileCHDCoverageExceptionLevel,
                        cLRFProfileMulticastDataRate,
                        cLRFProfileBandSelectClientMidRSSI,
                        cLRFProfileClientNetworkPreference
                    }
    STATUS          current
    DESCRIPTION
        "This collection of objects specifies the
        configuration of RF parameters on the controller 
        to be passed to an LWAPP AP."
    ::= { ciscoLwappRFMIBGroups 3 }

ciscoLwappRFGlobalConfigGroup OBJECT-GROUP
    OBJECTS         { cLRFProfileOutOfBoxAPConfig }
    STATUS          current
    DESCRIPTION
        "This is the RF global config parameter."
    ::= { ciscoLwappRFMIBGroups 4 }

ciscoLwappRFGroupTrapThresholdConfig OBJECT-GROUP
    OBJECTS         {
                        cLRFProfileHDClientTrapThreshold,
                        cLRFProfileInterferenceThreshold,
                        cLRFProfileNoiseThreshold,
                        cLRFProfileUtilizationThreshold
                    }
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration of Trap
        threshold to be configured on the interface of an LWAPP AP."
    ::= { ciscoLwappRFMIBGroups 5 }

ciscoLwappRFConfigGroup3 OBJECT-GROUP
    OBJECTS         {
                        cLRFProfileDCAForeignContribution,
                        cLRFProfileDCAChannelWidth,
                        cLRFProfileDCAChannelList
                    }
    STATUS          current
    DESCRIPTION
        "This object specifies the configuration DCA for
        RF Profiles."
    ::= { ciscoLwappRFMIBGroups 6 }

ciscoLwappRFConfigGroup4 OBJECT-GROUP
    OBJECTS         { 
                      cLRFProfileRxSopThreshold,
                      cLRFProfileRxSopThresholdCustom
                     }
    STATUS          current
    DESCRIPTION
        "This object specifies the receiver start of packet
        threshold for RF Profiles."
    ::= { ciscoLwappRFMIBGroups 7 }

ciscoLwappRFGroupDCAChannelConfig OBJECT-GROUP
    OBJECTS         { cLRFProfileDCAChannelList }
    STATUS          deprecated
    DESCRIPTION
        "This object specifies the add and remove channels for RF Profiles."
    ::= { ciscoLwappRFMIBGroups 8 }

ciscoLwappRFGroupChannelConfig OBJECT-GROUP
    OBJECTS         {
                        cLRFProfileChanRowStatus,
                        cLRFProfileChanAddRemove
                    }
    STATUS          current
    DESCRIPTION
        "This object specifies the add and remove channels for RF Profiles."
    ::= { ciscoLwappRFMIBGroups 9 }

ciscoLwappRFConfigGroupExtension1 OBJECT-GROUP
    OBJECTS         { cLRFProfileClientNetworkPreference }
    STATUS          current
    DESCRIPTION
        "This object specifies the
        configuration of RF parameters on the controller 
        to be passed to an LWAPP AP."
    ::= { ciscoLwappRFMIBGroups 10 }
    
ciscoLwappRFConfigGroup5 OBJECT-GROUP
    OBJECTS         {
                       cLRFProfileUnusedChannelList,
                       cLRFProfileShutdown
                    }
    STATUS          current
    DESCRIPTION
        "This object specifies the receiver start of packet
        threshold for RF Profiles."
    ::= { ciscoLwappRFMIBGroups 11 }

ciscoLwappRFConfigGroup6 OBJECT-GROUP
    OBJECTS         {
                       cLRFProfileAirTimeFairnessMode,
                       cLRFProfileAirTimeFairnessOptimization,
                       cLRFProfileBridgeClientAccess,
                       cLRFProfileAirTimeAllocation
                    }
    STATUS          current
    DESCRIPTION
        "This collection of object specifies the configuration
        of AirTime Fairness parameters."
    ::= { ciscoLwappRFMIBGroups 12 }

ciscoLwappRFConfigGroup7 OBJECT-GROUP
    OBJECTS         {
                       cLRFProfileRemovedChannelDcaState,
                       cLRFProfileRemoveChannelRowStatus,
                       cLRFProfileAddedChannelDcaState,
                       cLRFProfileAddChannelRowStatus
                    }
    STATUS          current
    DESCRIPTION
        "This collection of object specifies the removed channel
        and add channel list information from default DCA channel
        list for a RF profile"
    ::= { ciscoLwappRFMIBGroups 13 }
END

