-- *****************************************************************
-- CISCO-VPDN-MGMT-MIB.my:  VPDN Management MIB
--
-- July 1997, <PERSON> Chan
--
-- The following line marks this file as a single-source file which
-- will be kept in-sync among all single-source branches:
--
-- EDGE_SERVICES_SINGLESOURCE_FILE
--
-- Copyright (c) 1997-2006, 2009 by cisco Systems, Inc.
-- All rights reserved.
--
-- *****************************************************************

CISCO-VPDN-MGMT-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY,
        OBJECT-TYPE,
        NOTIFICATION-TYPE,
        TimeTicks,
        <PERSON><PERSON><PERSON>32,
        <PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        Unsigned32
                FROM SNMPv2-SM<PERSON>
        MODULE-COMPLIANCE,
        OBJECT-GROUP,
        NOTIFICATION-GROUP
                FROM SNMPv2-CONF
        TEXTUAL-CONVENTION,
        TimeStamp,
        DisplayString,
        TruthValue
                FROM SNMPv2-TC
        SnmpAdminString
                FROM SNMP-FRAMEWORK-MIB
        InterfaceIndexOrZero
                FROM IF-MIB
        InetAddressType,
        InetAddress
                FROM INET-ADDRESS-MIB
        ciscoExperiment
                FROM CISCO-SMI;

ciscoVpdnMgmtMIB MODULE-IDENTITY
        LAST-UPDATED    "200906160000Z"
        ORGANIZATION    "Cisco Systems, Inc."
        CONTACT-INFO
                "       Cisco Systems
                        Customer Service

                Postal: 170 W Tasman Drive
                        San Jose, CA  95134
                        USA

                   Tel: ****** 553-NETS

                E-mail: <EMAIL>"
        DESCRIPTION
                "The MIB module for VPDN.

        Overview of VPDN MIB

        MIB description

        This MIB is to support the Virtual Private Dialup Network (VPDN)
        feature of Cisco IOS.  VPDN handles the forwarding of PPP links
        from an Internet Provider (ISP) to a Home Gateway.

        The VPDN MIB provides the operational information on Cisco's
        VPDN tunnelling implementation.  The following entities are
        managed:
        1) Global VPDN information
        2) VPDN tunnel information
        3) VPDN tunnel's user information
        4) Failure history per user

        The following example configuration shows how the VPDN MIB
        returns VPDN information, from either CISCO A - Network Access
        Server (NAS) or CISCO B - Tunnel Server (TS).  The User call is
        projected by either domain name or Dialed Number Identification
        Service (DNIS).

        The terms NAS and TS are generic terms refering to the VPDN
        systems.

        The following table shows the corresponding technology-specific
        terms.

              Network Access Server            Tunnel Server
              ------------------------------   -------------------------
        L2F   Network Access Server    (NAS)   Home Gateway (HGW)
        L2TP  L2TP Access Concentrator (LAC)   L2TP Network Server (LNS)
        PPTP  PPTP Access Concentrator (PAC)   PPTP Network Server (PNS)

                   (NAS)                          (TS)
        User ===== Cisco A ===== IP Network ===== Cisco B ===== Server
                        |                          |
                        +========== VPDN ==========+

        1) The VPDN global entry identifies the system wide VPDN
           information.
        2) The VPDN tunnel table identifies the active VPDN tunnels on
           Cisco A and Cisco B.  The table contains an entry for each
           active tunnel on the system.
        3) The VPDN tunnel user table identifies the active users for
           each active tunnel on each system and provides relevant
           information for each user.
        4) The VPDN failure history table identifies the last failure
           recorded per user and provides relevant information.
     "
        REVISION        "200601200000Z"
        DESCRIPTION
            "Obsoleted the following deprecated L2F specific objects.
             These set of objects have been replaced by the 
             corresponding multi-protocol objects since 1999-03-24.

             Obsoleted Objects       Existing replacements
             =================       =====================
             cvpdnTunnelTotal        cvpdnSystemTable
             cvpdnSessionTotal       cvpdnSystemTable
             cvpdnDeniedUsersTotal   cvpdnSystemTable
             cvpdnTunnelTable        cvpdnTunnelAttrTable
             cvpdnTunnelSessionTable cvpdnSessionAttrTable

             Deprecated the following objects and added corresponding
             InetAddressType/InetAddress compliant counterparts:

             Deprecated Objects   Added Replacements
             ==================   ==================
             cvpdnTunnelAttrLocalIpAddress  
                                  cvpdnTunnelAttrLocalInetAddressType
                                  cvpdnTunnelAttrLocalInetAddress
             cvpdnTunnelAttrSourceIpAddress 
                                  cvpdnTunnelAttrSourceInetAddressType
                                  cvpdnTunnelAttrSourceInetAddress
             cvpdnTunnelAttrRemoteIpAddress 
                                  cvpdnTunnelAttrRemoteInetAddressType
                                  cvpdnTunnelAttrRemoteInetAddress
             cvpdnUnameToFailHistSourceIp   
                                  cvpdnUnameToFailHistSourceInetType
                                  cvpdnUnameToFailHistSourceInetAddr
             cvpdnUnameToFailHistDestIp     
                                  cvpdnUnameToFailHistDestInetType
                                  cvpdnUnameToFailHistDestInetAddr

             Added two new values, 'pwUp' and 'pwDown', for the
             existing object cvpdnNotifSessionEvent to support
             pseudowire status change event reporting.
            "

        REVISION        "200406080000Z"
        DESCRIPTION
            "Deprecated the cvpdnBundleEndpointType object since it's
             values did not align with the PPP protocol. This object was
             replaced by cvpdnBundleEndpointClass.
            "
        REVISION        "200404020000Z"
        DESCRIPTION
            "Added support for Multilink PPP VPDN information.  Modified
             the cvpdnSessionAttrTable to add objects that specify the
             multilink PPP bundle to which a session belongs.  Added
             scalar objects to hold the total number of multilink PPP
             bundles comprised of one, two, and more than two links.
             Added the cvpdnBundleTable that describes the PPP multilink
             bundle.  Added the cvpdnChildBundleTable to expose the
             containment relationship between the multilink PPP bundle
             and the VPDN tunnel.
            "
        REVISION        "200207080000Z"
        DESCRIPTION
            "Added support for VPDN Template information.  The template
             table reports the number of active sessions for each
             template.  This number is the sum of active sessions for
             all VPDN groups associated with each template.
            "
        REVISION        "200205170000Z"
        DESCRIPTION
            "Changed cvpdnSessionAttrVirtualCircuitID to an Unsigned32
             object.  Also, moved Unsigned32 import to SNMPv2-SMI.
            "
        REVISION        "200204020000Z"
        DESCRIPTION
            "Added virtual circuit ID, packets dropped, and notification
             objects for WAN/IP support.
            "
        REVISION        "200001120000Z"
        DESCRIPTION
            "Added support for Point-to-Point Tunneling Protocol (PPTP).
             Changed object descriptions to use generic terms.
            "
        REVISION        "9903240000Z"
        DESCRIPTION
            "Added support for multiple tunnel protocols with these
             tables
              1) cvpdnSystemTable
              2) cvpdnTunnelAttrTable
              3) cvpdnTunnelSessionAttrTable

             Deprecated objects and tables duplicated by the three new
             tables
              1) cvpdnTunnelTotal, cvpdnSessionTotal,
                 cvpdnDeniedUsersTotal
              2) cvpdnTunnelTable
              3) cvpdnTunnelSessionTable
            "
        REVISION        "9707150000Z"
        DESCRIPTION
            "Initial version of this MIB module."
        ::= { ciscoExperiment 24 }

TunnelType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The tunnel type.  This is the tunnel protocol of a VPDN
         tunnel."
    SYNTAX      INTEGER {
                    l2f(1),
                    l2tp(2),
                    pptp(3)
                }

EndpointClass ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The endpoint discriminator class supplied by the remote peer in
         a PPP Multilink bundle.

         RFC 1990 defines the following classes:

         none:        Class 0 - Null Class.  No endpoint discriminator
                      is being used.  The endpoint discriminator should
                      contain a SnmpAdminString (SIZE (0)) value.

         local:       Class 1 - Locally Assigned Address.  This class is
                      defined to permit a local assignment in the case
                      where use of one of the globally unique classes is
                      not possible.  The endpoint discriminator should
                      contain a SnmpAdminString (SIZE(1..20)) value.

         ipV4Address: Class 2 - Internet Protocol (IP) Address.  An
                      address in this class contains an IP host address.
                      The endpoint discriminator should contain a
                      InetAddressIPv4 value.

         macAddress:  Class 3 - IEEE 802.1 Globally Assigned MAC
                      Address.  An address in this class contains an
                      IEEE 802.1 MAC address in canonical (802.3)
                      format.  The endpoint discriminator should contain
                      a MacAddress value.

         magicNumber: Class 4 - PPP Magic-Number Block.  This is not an
                      address but a block of 1 to 5 concatenated 32 bit
                      PPP Magic-Numbers.  The endpoint discriminator
                      should contain an OCTET STRING (SIZE (4|8|12|16|
                      20)) value.

         phone:       Class 5 - Public Switched Network Directory 
                      Number.  An address in this class contains an
                      octet sequence as defined by I.331 (E.164)
                      representing an international telephone directory
                      number suitable for use to access the endpoint via
                      the public switched telephone network.  The
                      endpoint discriminator should contain a
                      SnmpAdminString (SIZE(1..15)) value."
    REFERENCE
        "The PPP Multilink Protocol (MP), RFC 1990, Section 5.1.3."
    SYNTAX      INTEGER {
                    none(1),
                    local(2),
                    ipV4Address(3),
                    macAddress(4),
                    magicNumber(5),
                    phone(6)
                }

ciscoVpdnMgmtMIBObjects    OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIB 1 }

cvpdnSystemInfo            OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBObjects 1 }

cvpdnTunnelInfo            OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBObjects 2 }

cvpdnTunnelSessionInfo     OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBObjects 3 }

cvpdnUserToFailHistInfo    OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBObjects 4 }

cvpdnTemplateInfo          OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBObjects 5 }

cvpdnMultilinkInfo         OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBObjects 6 }

-- ******************************************************************
-- * System Wide VPDN Information
-- ******************************************************************

cvpdnTunnelTotal OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "tunnels"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of VPDN tunnels that are currently
                 active within this system."
        ::= { cvpdnSystemInfo 1 }

cvpdnSessionTotal OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "users"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of active users in all the active VPDN
                 tunnels within this system."
        ::= { cvpdnSystemInfo 2 }

cvpdnDeniedUsersTotal OBJECT-TYPE
        SYNTAX      Counter32
        UNITS      "attempts"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of denied user attempts to all the
                 active VPDN tunnels within this system."
        ::= { cvpdnSystemInfo 3 }

-- VPDN System Table provides aggregated tunnel information for each
-- tunnel protocol.
--   The contents of this table supercedes the three objects
--   cvpdnTunnelTotal, cvpdnSessionTotal, and cvpdnDeniedUsersTotal

cvpdnSystemTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnSystemEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Table of information about the VPDN system for all tunnel
             types."
        ::= { cvpdnSystemInfo 4 }

cvpdnSystemEntry OBJECT-TYPE
        SYNTAX      CvpdnSystemEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry in the table, containing information about a
             single type of VPDN tunnel."
        INDEX { cvpdnSystemTunnelType }
        ::= { cvpdnSystemTable 1 }

CvpdnSystemEntry ::=
        SEQUENCE {
            cvpdnSystemTunnelType        TunnelType,
            cvpdnSystemTunnelTotal       Gauge32,
            cvpdnSystemSessionTotal      Gauge32,
            cvpdnSystemDeniedUsersTotal  Counter32,
            cvpdnSystemInitialConnReq    Counter32,
            cvpdnSystemSuccessConnReq    Counter32,
            cvpdnSystemFailedConnReq     Counter32
        }

cvpdnSystemTunnelType OBJECT-TYPE
        SYNTAX      TunnelType
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The tunnel type.  This is the tunnel protocol."
        ::= { cvpdnSystemEntry 1 }

cvpdnSystemTunnelTotal OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "tunnels"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of VPDN tunnels that are currently active
             of this tunnel type."
        ::= { cvpdnSystemEntry 2 }

cvpdnSystemSessionTotal OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "sessions"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of active sessions in all the active VPDN
             tunnels of this tunnel type."
        ::= { cvpdnSystemEntry 3 }

cvpdnSystemDeniedUsersTotal OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "attempts"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of denied user attempts to all the VPDN
             tunnels of this tunnel type since last system
             re-initialization."
        ::= { cvpdnSystemEntry 4 }

cvpdnSystemInitialConnReq OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "attempts"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number tunnel connection attempts on all the VPDN
             tunnels of this tunnel type since last system
             re-initialization."
        ::= { cvpdnSystemEntry 5 }

cvpdnSystemSuccessConnReq OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "attempts"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number tunnel Successful connection attempts in VPDN
             tunnels of this tunnel type since last system
             re-initialization."
        ::= { cvpdnSystemEntry 6 }

cvpdnSystemFailedConnReq OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "attempts"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number tunnel Failed connection attempts in VPDN
             tunnels of this tunnel type since last system
             re-initialization."
        ::= { cvpdnSystemEntry 7 }

-- Objects indicating whether the specified notifications are enabled
-- or not.

cvpdnSystemNotifSessionEnabled OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Indicates whether Layer 2 VPN session notifications are
             enabled."
        DEFVAL      { false }
        ::= { cvpdnSystemInfo 5 }

cvpdnSystemClearSessions OBJECT-TYPE
        SYNTAX      INTEGER {
                        none(1),
                        all(2),
                        l2f(3),
                        l2tp(4),
                        pptp(5)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Clears all the sessions in a given tunnel type.  When
             reading this object, the value of 'none' will always be
             returned.

             When setting these values, the following operations will be
             performed:

                 none: no operation.

                 all:  clears all the sessions in all the tunnels.

                 l2f:  clears all the L2F sessions.

                 l2tp: clears all the L2TP sessions.

                 pptp: clears all the PPTP sessions."
        ::= { cvpdnSystemInfo 6 }

-- ******************************************************************
-- * VPDN Tunnel General Information Table
-- ******************************************************************

cvpdnTunnelTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnTunnelEntry
        MAX-ACCESS  not-accessible
        STATUS      obsolete
        DESCRIPTION
                "Table of information about the active VPDN tunnels."
        ::= { cvpdnTunnelInfo 1 }

cvpdnTunnelEntry OBJECT-TYPE
        SYNTAX      CvpdnTunnelEntry
        MAX-ACCESS  not-accessible
        STATUS      obsolete
        DESCRIPTION
                "An entry in the table, containing information about a
                 single active VPDN tunnel."
        INDEX { cvpdnTunnelTunnelId }
        ::= { cvpdnTunnelTable 1 }

CvpdnTunnelEntry ::=
        SEQUENCE {
            cvpdnTunnelTunnelId                 Unsigned32,
            cvpdnTunnelRemoteTunnelId           Unsigned32,
            cvpdnTunnelLocalName                DisplayString,
            cvpdnTunnelRemoteName               DisplayString,
            cvpdnTunnelRemoteEndpointName       DisplayString,
            cvpdnTunnelLocalInitConnection      TruthValue,
            cvpdnTunnelOrigCause                INTEGER,
            cvpdnTunnelState                    INTEGER,
            cvpdnTunnelActiveSessions           Gauge32,
            cvpdnTunnelDeniedUsers              Counter32,
            cvpdnTunnelSoftshut                 TruthValue,
            cvpdnTunnelNetworkServiceType       INTEGER,
            cvpdnTunnelLocalIpAddress           IpAddress,
            cvpdnTunnelSourceIpAddress          IpAddress,
            cvpdnTunnelRemoteIpAddress          IpAddress
        }

cvpdnTunnelTunnelId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      obsolete
        DESCRIPTION
                "The Tunnel ID of an active VPDN tunnel.  If it is the
                 instigator of the tunnel, the ID is the HGW/LNS tunnel
                 ID, otherwise it is the NAS/LAC tunnel ID."
        ::= { cvpdnTunnelEntry 1 }

cvpdnTunnelRemoteTunnelId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The remote Tunnel ID of an active VPDN tunnel.  If it
                 is the instigator of the tunnel, the ID is the NAS/LAC
                 tunnel ID, otherwise it is the HGW/LNS tunnel ID."
        ::= { cvpdnTunnelEntry 2 }

cvpdnTunnelLocalName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The local name of an active VPDN tunnel.  It will be
                 the NAS/LAC name of the tunnel if the router serves as
                 the NAS/LAC, or the HGW/LNS name of the tunnel if the
                 system serves as the home gateway.  Typically, the
                 local name is the configured host name of the router."
        ::= { cvpdnTunnelEntry 3 }

cvpdnTunnelRemoteName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The remote name of an active VPDN tunnel.  It will be
                 the home gateway name of the tunnel if the system is a
                 NAS/LAC, or the NAS/LAC name of the tunnel if the
                 system serves as the home gateway."
        ::= { cvpdnTunnelEntry 4 }

cvpdnTunnelRemoteEndpointName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The remote end point name of an active VPDN tunnel.
                 This name is either the domain name or the DNIS that
                 this tunnel is projected with."
        ::= { cvpdnTunnelEntry 5 }

cvpdnTunnelLocalInitConnection OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "This object indicates whether the tunnel was generated
                 locally or not."
        ::= { cvpdnTunnelEntry 6 }

cvpdnTunnelOrigCause OBJECT-TYPE
        SYNTAX      INTEGER {
                        domain(1),
                        dnis(2),
                        stack(3)
                    }
        MAX-ACCESS read-only
        STATUS     obsolete
        DESCRIPTION
                "The cause which originated an active VPDN tunnel.  The
                 tunnel can be projected via domain name, DNIS or a
                 stack group (SGBP)."
        ::= { cvpdnTunnelEntry 7 }

cvpdnTunnelState OBJECT-TYPE
        SYNTAX      INTEGER {
                        unknown(1),
                        opening(2),
                        open(3),
                        closing(4)
                    }
        MAX-ACCESS read-only
        STATUS     obsolete
        DESCRIPTION
                "The current state of an active VPDN tunnel.  Each state
                 code is explained below:

                        unknown: The current state of the tunnel is
                                 unknown.

                        opening: The tunnel has just been instigated and
                                 is pending for a remote end reply to
                                 complete the process.

                        open:    The tunnel is active.

                        closing: The tunnel has just been shut down and
                                 is pending for the remote end to reply
                                 to complete the process."
        ::= { cvpdnTunnelEntry 8 }

cvpdnTunnelActiveSessions OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "sessions"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of active session currently in the
                 tunnel."
        ::= { cvpdnTunnelEntry 9 }

cvpdnTunnelDeniedUsers OBJECT-TYPE
        SYNTAX      Counter32
        UNITS      "calls"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "A count of the accumulated total of denied users for
                 the tunnel."
        ::= { cvpdnTunnelEntry 10 }

cvpdnTunnelSoftshut OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "A VPDN tunnel can be put into a soft shut state to
                 prevent any new user session to be added.  This object
                 specifies whether this tunnel has been soft shut."
        ::= { cvpdnTunnelEntry 12 }

cvpdnTunnelNetworkServiceType OBJECT-TYPE
        SYNTAX      INTEGER {
                        ip(1)
                    }
        MAX-ACCESS read-only
        STATUS     obsolete
        DESCRIPTION
                "The type of network service used in the active tunnel.
                 For now it is IP only."
        ::= { cvpdnTunnelEntry 13 }

cvpdnTunnelLocalIpAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The local IP address of the tunnel.  This IP address is
                 that of the interface at the local end of the tunnel."
        ::= { cvpdnTunnelEntry 14 }

cvpdnTunnelSourceIpAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The source IP address of the tunnel.  This IP address
                 is the user configurable IP address for Stack Group
                 Biding Protocol (SGBP) via the CLI command:
                 vpdn source-ip"
        REFERENCE
            "The Stack Group Biding Protocol (SGBP), United States 
             Patent 6073176"
        ::= { cvpdnTunnelEntry 15 }

cvpdnTunnelRemoteIpAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The remote IP address of the tunnel.  This IP address
                 is that of the interface at the remote end of the
                 tunnel."
        ::= { cvpdnTunnelEntry 16 }

--
-- VPDN Tunnel Attribute Table provides tunnel level information
--  This table supercedes the VPDN Tunnel Table, cvpdnTunnelTable

cvpdnTunnelAttrTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnTunnelAttrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Table of information about the active VPDN tunnels.  An
             entry is added to the table when a new tunnel is initiated
             and removed from the table when the tunnel is terminated."
        ::= { cvpdnTunnelInfo 2 }

cvpdnTunnelAttrEntry OBJECT-TYPE
        SYNTAX      CvpdnTunnelAttrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry in the table, containing information about a
             single active VPDN tunnel."
        INDEX { cvpdnSystemTunnelType,
                cvpdnTunnelAttrTunnelId }
        ::= { cvpdnTunnelAttrTable 1 }

CvpdnTunnelAttrEntry ::=
        SEQUENCE {
            cvpdnTunnelAttrTunnelId                Integer32,
            cvpdnTunnelAttrRemoteTunnelId          Integer32,
            cvpdnTunnelAttrLocalName               DisplayString,
            cvpdnTunnelAttrRemoteName              DisplayString,
            cvpdnTunnelAttrRemoteEndpointName      DisplayString,
            cvpdnTunnelAttrLocalInitConnection     TruthValue,
            cvpdnTunnelAttrOrigCause               INTEGER,
            cvpdnTunnelAttrState                   INTEGER,
            cvpdnTunnelAttrActiveSessions          Gauge32,
            cvpdnTunnelAttrDeniedUsers             Counter32,
            cvpdnTunnelAttrSoftshut                TruthValue,
            cvpdnTunnelAttrNetworkServiceType      INTEGER,
            cvpdnTunnelAttrLocalIpAddress          IpAddress,
            cvpdnTunnelAttrSourceIpAddress         IpAddress,
            cvpdnTunnelAttrRemoteIpAddress         IpAddress,
            cvpdnTunnelAttrLocalInetAddressType    InetAddressType,
            cvpdnTunnelAttrLocalInetAddress        InetAddress,
            cvpdnTunnelAttrSourceInetAddressType   InetAddressType,
            cvpdnTunnelAttrSourceInetAddress       InetAddress,
            cvpdnTunnelAttrRemoteInetAddressType   InetAddressType,
            cvpdnTunnelAttrRemoteInetAddress       InetAddress
        }

cvpdnTunnelAttrTunnelId OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The Tunnel ID of an active VPDN tunnel.  If this end is the
             instigator of the tunnel, the ID is the TS tunnel ID,
             otherwise it is the NAS tunnel ID.

             Two distinct tunnels with the same tunnel ID may exist, but
             with different tunnel types.
            "
        ::= { cvpdnTunnelAttrEntry 1 }

cvpdnTunnelAttrRemoteTunnelId OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The remote Tunnel ID of an active VPDN tunnel.  If this end
             is the instigator of the tunnel, the ID is the NAS tunnel
             ID, otherwise it is the TS tunnel ID."
        ::= { cvpdnTunnelAttrEntry 2 }

cvpdnTunnelAttrLocalName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The local name of an active VPDN tunnel.  It will be the
             NAS name of the tunnel if the system serves as the NAS, or
             the TS name of the tunnel if the system serves as the
             tunnel server.  Typically, the local name is the configured
             host name of the system."
        ::= { cvpdnTunnelAttrEntry 3 }

cvpdnTunnelAttrRemoteName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The remote name of an active VPDN tunnel.  It will be the
             tunnel server name of the tunnel if the system is a NAS,
             or the NAS name of the tunnel if the system serves as the
             tunnel server."
        ::= { cvpdnTunnelAttrEntry 4 }

cvpdnTunnelAttrRemoteEndpointName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The remote end point name of an active VPDN tunnel.  This
             name is either the domain name or the DNIS that this tunnel
             is projected with."
        ::= { cvpdnTunnelAttrEntry 5 }

cvpdnTunnelAttrLocalInitConnection OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object indicates whether the tunnel was originated
             locally or not.  If it's true, the tunnel was originated
             locally."
        ::= { cvpdnTunnelAttrEntry 6 }

cvpdnTunnelAttrOrigCause OBJECT-TYPE
        SYNTAX      INTEGER {
                        domain(1),
                        dnis(2),
                        stack(3),
                        xconnect(4)
                    }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The cause which originated an active VPDN tunnel.  The
             tunnel can be projected via domain name, DNIS, stack group,
             or L2 Xconnect."
        ::= { cvpdnTunnelAttrEntry 7 }

cvpdnTunnelAttrState OBJECT-TYPE
        SYNTAX      INTEGER {
                        unknown(1),
                        l2fOpening(2),
                        l2fOpenWait(3),
                        l2fOpen(4),
                        l2fClosing(5),
                        l2fCloseWait(6),
                        l2tpIdle(7),
                        l2tpWaitCtlReply(8),
                        l2tpEstablished(9),
                        l2tpShuttingDown(10),
                        l2tpNoSessionLeft(11),
                        pptpIdle(12),
                        pptpWaitConnect(13),
                        pptpWaitCtlRequest(14),
                        pptpWaitCtlReply(15),
                        pptpEstablished(16),
                        pptpWaitStopReply(17),
                        pptpTerminal(18)
                    }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The current state of an active VPDN tunnel.
             Tunnels of type l2f will have states with the 'l2f' prefix.
             Tunnels of type l2tp will have states with the 'l2tp'
             prefix.
             Tunnels of type pptp will have states with the 'pptp'
             prefix.

             Each state code is explained below:

                 unknown:            The current state of the tunnel is
                                     unknown.

                 l2fOpening:         The tunnel has just been initiated
                                     and is pending for a remote end
                                     reply to complete the process.

                 l2fOpenWait:        This end received a tunnel open
                                     request from the remote end and is
                                     waiting for the tunnel to be
                                     established.

                 l2fOpen:            The tunnel is active.

                 l2fClosing:         This end received a tunnel close
                                     request.

                 l2fCloseWait:       The tunnel has just been shut down
                                     and is pending for the remote end
                                     to reply to complete the process.

                 l2tpIdle:           No tunnel is initiated yet.

                 l2tpWaitCtlReply:   The tunnel has been initiated and
                                     is pending for a remote end reply
                                     to complete the process.

                 l2tpEstablished:    The tunnel is active.

                 l2tpShuttingDown:   The tunnel is in progress of
                                     shutting down.

                 l2tpNoSessionLeft:  There is no session left in the
                                     tunnel.

                 pptpIdle:           No tunnel is initiated yet.

                 pptpWaitConnect:    The tunnel is waiting for a TCP
                                     connection.

                 pptpWaitCtlRequest: The tunnel has been initiated and
                                     is pending for a remote end
                                     request.

                 pptpWaitCtlReply:   The tunnel has been initiated and
                                     is pending for a remote end reply.

                 pptpEstablished:    The tunnel is active.

                 pptpWaitStopReply:  The tunnel is being shut down and
                                     is pending for a remote end reply.

                 pptpTerminal:       The tunnel has been shut down."
        ::= { cvpdnTunnelAttrEntry 8 }

cvpdnTunnelAttrActiveSessions OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "sessions"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of active session currently in the
             tunnel."
        ::= { cvpdnTunnelAttrEntry 9 }

cvpdnTunnelAttrDeniedUsers OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "calls"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "A count of the accumulated total of denied users for the
             tunnel."
        ::= { cvpdnTunnelAttrEntry 10 }

cvpdnTunnelAttrSoftshut OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "A VPDN tunnel can be put into a soft shut state to prevent
             any new session to be added.  This object specifies whether
             this tunnel has been soft shut.  If its true, it has been
             soft shut."
        ::= { cvpdnTunnelAttrEntry 11 }

cvpdnTunnelAttrNetworkServiceType OBJECT-TYPE
        SYNTAX      INTEGER {
                        ip(1)
                    }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The type of network service used in the active tunnel."
        ::= { cvpdnTunnelAttrEntry 12 }

cvpdnTunnelAttrLocalIpAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      deprecated
        DESCRIPTION
            "The local IP address of the tunnel.  This IP address is
             that of the interface at the local end of the tunnel."
        ::= { cvpdnTunnelAttrEntry 13 }

cvpdnTunnelAttrSourceIpAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      deprecated
        DESCRIPTION
            "The source IP address of the tunnel.  This IP address is
             the user configurable IP address for Stack Group Biding
             Protocol."
        REFERENCE
            "The Stack Group Biding Protocol (SGBP), United States 
             Patent 6073176"
        ::= { cvpdnTunnelAttrEntry 14 }

cvpdnTunnelAttrRemoteIpAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      deprecated
        DESCRIPTION
            "The remote IP address of the tunnel.  This IP address is
             that of the interface at the remote end of the tunnel."
        ::= { cvpdnTunnelAttrEntry 15 }

cvpdnTunnelAttrLocalInetAddressType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Indicates the type of address contained in
             cvpdnTunnelAttrLocalInetAddress"
        ::= { cvpdnTunnelAttrEntry 16 }

cvpdnTunnelAttrLocalInetAddress OBJECT-TYPE
        SYNTAX      InetAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The local IP address of the tunnel.  This IP address is
             that of the interface at the local end of the tunnel.
             The type of this address is determined by the value of 
             cvpdnTunnelAttrLocalInetAddressType."
        ::= { cvpdnTunnelAttrEntry 17 }

cvpdnTunnelAttrSourceInetAddressType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Indicates the type of address contained in
             cvpdnTunnelAttrSourceInetAddress"
        ::= { cvpdnTunnelAttrEntry 18 }

cvpdnTunnelAttrSourceInetAddress OBJECT-TYPE
        SYNTAX      InetAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The source IP address of the tunnel.  This IP address is
             the user configurable IP address for Stack Group Biding
             Protocol.  The type of this address is determined by the 
             value of cvpdnTunnelAttrSourceInetAddressType."
        REFERENCE
            "The Stack Group Biding Protocol (SGBP), United States 
             Patent 6073176"
        ::= { cvpdnTunnelAttrEntry 19 }

cvpdnTunnelAttrRemoteInetAddressType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Indicates the type of address contained in
             cvpdnTunnelAttrRemoteInetAddress"
        ::= { cvpdnTunnelAttrEntry 20 }

cvpdnTunnelAttrRemoteInetAddress OBJECT-TYPE
        SYNTAX      InetAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The remote IP address of the tunnel.  This IP address is
             that of the interface at the remote end of the tunnel.
             The type of this address is determined by the value of 
             cvpdnTunnelAttrRemoteInetAddressType."
        ::= { cvpdnTunnelAttrEntry 21 }

-- ******************************************************************
-- * VPDN Tunnel Session Information
-- ******************************************************************

cvpdnTunnelSessionTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnTunnelSessionEntry
        MAX-ACCESS  not-accessible
        STATUS      obsolete
        DESCRIPTION
                "Table of information about individual user sessions
                 within the active tunnels.  Entry is added to the table
                 when new user session is initiated and be removed from
                 the table when the user session is terminated."
        ::= { cvpdnTunnelSessionInfo 1 }

cvpdnTunnelSessionEntry OBJECT-TYPE
        SYNTAX      CvpdnTunnelSessionEntry
        MAX-ACCESS  not-accessible
        STATUS      obsolete
        DESCRIPTION
                "An entry in the table, containing information about a
                 single user session within the tunnel."
        INDEX { cvpdnTunnelTunnelId,
                cvpdnTunnelSessionId }
        ::= { cvpdnTunnelSessionTable 1 }

CvpdnTunnelSessionEntry ::=
        SEQUENCE {
            cvpdnTunnelSessionId                   Unsigned32,
            cvpdnTunnelSessionUserName             DisplayString,
            cvpdnTunnelSessionState                INTEGER,
            cvpdnTunnelSessionCallDuration         TimeTicks,
            cvpdnTunnelSessionPacketsOut           Counter32,
            cvpdnTunnelSessionBytesOut             Counter32,
            cvpdnTunnelSessionPacketsIn            Counter32,
            cvpdnTunnelSessionBytesIn              Counter32,
            cvpdnTunnelSessionDeviceType           INTEGER,
            cvpdnTunnelSessionDeviceCallerId       DisplayString,
            cvpdnTunnelSessionDevicePhyId          InterfaceIndexOrZero,
            cvpdnTunnelSessionMultilink            TruthValue,
            cvpdnTunnelSessionModemSlotIndex       Unsigned32,
            cvpdnTunnelSessionModemPortIndex       Unsigned32,
            cvpdnTunnelSessionDS1SlotIndex         Unsigned32,
            cvpdnTunnelSessionDS1PortIndex         Unsigned32,
            cvpdnTunnelSessionDS1ChannelIndex      Unsigned32,
            cvpdnTunnelSessionModemCallStartTime   TimeStamp,
            cvpdnTunnelSessionModemCallStartIndex  Unsigned32
        }

cvpdnTunnelSessionId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      obsolete
        DESCRIPTION
                "The ID of an active VPDN tunnel user session."
        ::= { cvpdnTunnelSessionEntry 1 }

cvpdnTunnelSessionUserName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The name of the user of the user session."
        ::= { cvpdnTunnelSessionEntry 2 }

cvpdnTunnelSessionState OBJECT-TYPE
       SYNTAX      INTEGER {
                        unknown(1),
                        opening(2),
                        open(3),
                        closing(4),
                        waitingForTunnel(5)
                   }
       MAX-ACCESS read-only
       STATUS     obsolete
       DESCRIPTION
               "The current state of an active user session.  Each state
                code is explained below:

                    unknown:          The current state of the tunnel's
                                      session is unknown.

                    opening:          The user session has just been
                                      initiated through a tunnel and is
                                      pending for the remote end reply
                                      to complete the process.

                    open:             The user session is active.

                    closing:          The user session has just been
                                      closed and is pending for the
                                      remote end reply to complete the
                                      process.

                    waitingForTunnel: The user session is in this state
                                      when the tunnel which this session
                                      is going through is still in
                                      CLOSED state.  It waits for the
                                      tunnel to become OPEN before the
                                      session is allow to be fully
                                      established."
       ::= { cvpdnTunnelSessionEntry 3 }

cvpdnTunnelSessionCallDuration OBJECT-TYPE
       SYNTAX     TimeTicks
       MAX-ACCESS read-only
       STATUS     obsolete
       DESCRIPTION
               "This object specifies the call duration of the current
                active user session in value of system uptime."
       ::= { cvpdnTunnelSessionEntry 4 }

cvpdnTunnelSessionPacketsOut OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "packets"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of output packets through this active
                 user session."
        ::= { cvpdnTunnelSessionEntry 5 }

cvpdnTunnelSessionBytesOut OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of output bytes through this active
                 user session."
        ::= { cvpdnTunnelSessionEntry 6 }

cvpdnTunnelSessionPacketsIn OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "packets"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of input packets through this active
                 user session."
        ::= { cvpdnTunnelSessionEntry 7 }

cvpdnTunnelSessionBytesIn OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The total number of input bytes through this active
                 user session."
        ::= { cvpdnTunnelSessionEntry 8 }

cvpdnTunnelSessionDeviceType OBJECT-TYPE
        SYNTAX      INTEGER {
                        other(1),
                        asyncInternalModem(2),
                        async(3),
                        bchan(4),
                        sync(5),
                        virtualAccess(6),
                        xdsl(7),
                        cable(8)
                    }
       MAX-ACCESS read-only
       STATUS     obsolete
       DESCRIPTION
                "The type of physical devices that this user session
                 is attached to for the local end of the tunnel.  The
                 meaning of each device type is explained below:

                     other:              Any device that has not been
                                         defined.

                     asyncInternalModem: Modem Pool device of an access
                                         server.

                     async:              A regular asynchronous serial
                                         interface.

                     sync:               A regular synchronous serial
                                         interface.

                     bchan:              An ISDN call.

                     xdsl:               Future application with xDSL
                                         devices.

                     cable:              Future application with Cable
                                         modem devices."
       ::= { cvpdnTunnelSessionEntry 9 }

cvpdnTunnelSessionDeviceCallerId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The incoming caller identification of the user.  It is
                 the originating number that called into the device that
                 initiates the user session.  This object can be empty
                 since not all dial device can provide caller ID
                 information."
        ::= { cvpdnTunnelSessionEntry 10 }

cvpdnTunnelSessionDevicePhyId OBJECT-TYPE
        SYNTAX      InterfaceIndexOrZero
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The device ID of the physical interface for the user
                 session.  The object is the the interface index which
                 points to the ifTable.  For virtual interface that is
                 not in the ifTable, it will have zero value."
        ::= { cvpdnTunnelSessionEntry 11 }

cvpdnTunnelSessionMultilink OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "This object indicates whether the session is part of a
                 multilink or not."
        ::= { cvpdnTunnelSessionEntry 12 }

cvpdnTunnelSessionModemSlotIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The Modem Pool database slot index if it is associated
                 with this user session.  Only a session with device of
                 type asyncInternalModem will have a valid value for
                 this object."
        ::= { cvpdnTunnelSessionEntry 13 }

cvpdnTunnelSessionModemPortIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The Modem Pool database port index if it is associated
                 with this user session.  Only a session with a device
                 of type asyncInternalModem will have a valid value for
                 this object."
        ::= { cvpdnTunnelSessionEntry 14 }

cvpdnTunnelSessionDS1SlotIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The DS1 database slot index if it is associated with
                 this user session.  Only a session with a device of
                 type asyncInternalModem will have a valid value for
                 this object."
        ::= { cvpdnTunnelSessionEntry 15 }

cvpdnTunnelSessionDS1PortIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The DS1 database port index if it is associated with
                 this user session.  Only a session with a device of
                 type asyncInternalModem will have a a valid value for
                 this object."
        ::= { cvpdnTunnelSessionEntry 16 }

cvpdnTunnelSessionDS1ChannelIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The DS1 database channel index if it is associated with
                 this user session.  Only a session over a device of
                 type asyncInternalModem will have a valid value for
                 this object."
        ::= { cvpdnTunnelSessionEntry 17 }

cvpdnTunnelSessionModemCallStartTime OBJECT-TYPE
        SYNTAX      TimeStamp
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "The start time of the current modem call.  Only a
                 session with a  device of type asyncInternalModem will
                 have a valid value for this object."
        ::= { cvpdnTunnelSessionEntry 18 }

cvpdnTunnelSessionModemCallStartIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      obsolete
        DESCRIPTION
                "Arbitrary small integer to distinguish modem calls that
                 occurred at the same time tick.  Only session over
                 device asyncInternalModem will have a valid value for
                 this object."
        ::= { cvpdnTunnelSessionEntry 19 }

--
-- VPDN Session Attribute Table provides session level information
--    This table supercedes the VPDN Tunnel Session Table,
--    cvpdnTunnelSessionTable

cvpdnSessionAttrTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnSessionAttrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Table of information about individual sessions within the
             active tunnels.  An entry is added to the table when a new
             session is initiated and removed from the table when the
             session is terminated."
        ::= { cvpdnTunnelSessionInfo 2 }

cvpdnSessionAttrEntry OBJECT-TYPE
        SYNTAX      CvpdnSessionAttrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry in the table, containing information about a
             single session within the tunnel."
        INDEX { cvpdnSystemTunnelType,
                cvpdnTunnelAttrTunnelId,
                cvpdnSessionAttrSessionId }
        ::= { cvpdnSessionAttrTable 1 }

CvpdnSessionAttrEntry ::=
        SEQUENCE {
            cvpdnSessionAttrSessionId           Integer32,
            cvpdnSessionAttrUserName            DisplayString,
            cvpdnSessionAttrState               INTEGER,
            cvpdnSessionAttrCallDuration        TimeTicks,
            cvpdnSessionAttrPacketsOut          Counter32,
            cvpdnSessionAttrBytesOut            Counter32,
            cvpdnSessionAttrPacketsIn           Counter32,
            cvpdnSessionAttrBytesIn             Counter32,
            cvpdnSessionAttrDeviceType          INTEGER,
            cvpdnSessionAttrDeviceCallerId      DisplayString,
            cvpdnSessionAttrDevicePhyId         InterfaceIndexOrZero,
            cvpdnSessionAttrMultilink           TruthValue,
            cvpdnSessionAttrModemSlotIndex      Unsigned32,
            cvpdnSessionAttrModemPortIndex      Unsigned32,
            cvpdnSessionAttrDS1SlotIndex        Unsigned32,
            cvpdnSessionAttrDS1PortIndex        Unsigned32,
            cvpdnSessionAttrDS1ChannelIndex     Unsigned32,
            cvpdnSessionAttrModemCallStartTime  TimeStamp,
            cvpdnSessionAttrModemCallStartIndex Unsigned32,
            cvpdnSessionAttrVirtualCircuitID    Unsigned32,
            cvpdnSessionAttrSentPktsDropped     Counter32,
            cvpdnSessionAttrRecvPktsDropped     Counter32,
            cvpdnSessionAttrMultilinkBundle     SnmpAdminString,
            cvpdnSessionAttrMultilinkIfIndex    InterfaceIndexOrZero
        }

cvpdnSessionAttrSessionId OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The ID of an active VPDN session."
        ::= { cvpdnSessionAttrEntry 1 }

cvpdnSessionAttrUserName OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The name of the user of the session."
        ::= { cvpdnSessionAttrEntry 2 }

cvpdnSessionAttrState OBJECT-TYPE
        SYNTAX      INTEGER {
                        unknown(1),
                        l2fOpening(2),
                        l2fOpen(3),
                        l2fCloseWait(4),
                        l2fWaitingForTunnel(5),
                        l2tpIdle(6),
                        l2tpWaitingTunnel(7),
                        l2tpWaitReply(8),
                        l2tpWaitConnect(9),
                        l2tpEstablished(10),
                        l2tpShuttingDown(11),
                        pptpWaitVAccess(12),
                        pptpPacEstablished(13),
                        pptpWaitTunnel(14),
                        pptpWaitOCRP(15),
                        pptpPnsEstablished(16),
                        pptpWaitCallDisc(17),
                        pptpTerminal(18)
                    }
       MAX-ACCESS read-only
       STATUS     current
       DESCRIPTION
            "The current state of a tunnel session.
             L2F tunnel sessions will have states with the 'l2f' prefix.
             L2TP tunnel sessions will have states with the 'l2tp'
             prefix.

             Each state code is explained below:

                 unknown:             The current state of the tunnel's
                                      session is unknown.

                 l2fOpening:          The session has just been
                                      initiated through a tunnel and is
                                      pending for the remote end reply
                                      to complete the process.

                 l2fOpen:             The session is active.

                 l2fCloseWait:        The session has just been closed
                                      and is pending for the remote end
                                      reply to complete the process.

                 l2fWaitingForTunnel: The session is in this state when
                                      the tunnel which this session is
                                      going through is still in CLOSED
                                      state.  It waits for the tunnel to
                                      become OPEN before the session is
                                      allowed to be fully established.

                 l2tpIdle:            No session is initiated yet.

                 l2tpWaitingTunnel:   The session is waiting for the
                                      tunnel to be established.

                 l2tpWaitReply:       The session has been initiated and
                                      is pending for the remote end
                                      reply to complete the process.

                 l2tpWaitConnect:     This end has acknowledged a
                                      connection request and is waiting
                                      for the remote end to connect.

                 l2tpEstablished:     The session is active.

                 l2tpShuttingDown:    The session is in progress of
                                      shutting down.

                 pptpWaitVAccess:     The session is waiting for the
                                      creation of a virtual access
                                      interface.

                 pptpPacEstablished:  The session is active.

                 pptpWaitTunnel:      The session is waiting for the
                                      tunnel to be established.

                 pptpWaitOCRP:        The session has been initiated and
                                      is pending for the remote end
                                      reply to complete the process.

                 pptpPnsEstablished:  The session is active.

                 pptpWaitCallDisc:    Session shutdown is in progress."
       ::= { cvpdnSessionAttrEntry 3 }

cvpdnSessionAttrCallDuration OBJECT-TYPE
       SYNTAX     TimeTicks
       MAX-ACCESS read-only
       STATUS     current
       DESCRIPTION
           "This object specifies the call duration of the current
            active session."
       ::= { cvpdnSessionAttrEntry 4 }

cvpdnSessionAttrPacketsOut OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "packets"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of output packets through this active
             session."
        ::= { cvpdnSessionAttrEntry 5 }

cvpdnSessionAttrBytesOut OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of output bytes through this active
             session."
        ::= { cvpdnSessionAttrEntry 6 }

cvpdnSessionAttrPacketsIn OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "packets"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of input packets through this active
             session."
        ::= { cvpdnSessionAttrEntry 7 }

cvpdnSessionAttrBytesIn OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of input bytes through this active
             session."
        ::= { cvpdnSessionAttrEntry 8 }

cvpdnSessionAttrDeviceType OBJECT-TYPE
        SYNTAX      INTEGER {
                        other(1),
                        asyncInternalModem(2),
                        async(3),
                        bchan(4),
                        sync(5),
                        virtualAccess(6),
                        xdsl(7),
                        cable(8)
                    }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The type of physical devices that this session is attached
             to for the local end of the tunnel.  The meaning of each
             device type is explained below:

                 other:              Any device that has not been
                                     defined.

                 asyncInternalModem: Modem Pool device of an access
                                     server.

                 async:              A regular asynchronous serial
                                     interface.

                 sync:               A regular synchronous serial
                                     interface.

                 bchan:              An ISDN call.

                 xdsl:               xDSL interface.

                 cable:              cable modem interface."
       ::= { cvpdnSessionAttrEntry 9 }

cvpdnSessionAttrDeviceCallerId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The incoming caller identification of the user.  It is the
             originating number that called into the device that
             initiates the session.  This object can be empty since not
             all dial devices can provide caller ID information."
        ::= { cvpdnSessionAttrEntry 10 }

cvpdnSessionAttrDevicePhyId OBJECT-TYPE
        SYNTAX      InterfaceIndexOrZero
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The device ID of the physical interface for the session.
             The object is the the interface index which points to the
             ifTable.  For virtual interfaces that are not in the
             ifTable, the value will be zero."
        ::= { cvpdnSessionAttrEntry 11 }

cvpdnSessionAttrMultilink OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object indicates whether the session is part of a
             multilink PPP bundle, even if there is only one link or
             session in the bundle.  If it is multilink PPP, the value
             is true."
        ::= { cvpdnSessionAttrEntry 12 }

cvpdnSessionAttrModemSlotIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The Modem Pool database slot index if it is associated with
             this session.  Only a session with device of type
             'asyncInternalModem' will have a valid value for this
             object; otherwise, it is not instantiated."
        ::= { cvpdnSessionAttrEntry 13 }

cvpdnSessionAttrModemPortIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The Modem Pool database port index if it is associated with
             this session.  Only a session with a device of type
             'asyncInternalModem' will have a valid value for this
             object; otherwise, it is not instantiated."
        ::= { cvpdnSessionAttrEntry 14 }

cvpdnSessionAttrDS1SlotIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The DS1 database slot index if it is associated with this
             session.  Only a session with a device of type
             'asyncInternalModem' will have a valid value for this
             object; otherwise it is not instantiated."
        ::= { cvpdnSessionAttrEntry 15 }

cvpdnSessionAttrDS1PortIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The DS1 database port index if it is associated with this
             session.  Only a session with a device of type
             'asyncInternalModem' will have a valid value for this
             object; otherwise it is not instantiated."
        ::= { cvpdnSessionAttrEntry 16 }

cvpdnSessionAttrDS1ChannelIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The DS1 database channel index if it is associated with
             this session.  Only a session over a device of type
             'asyncInternalModem' will have a valid value for this
             object; otherwise it is not instantiated."
        ::= { cvpdnSessionAttrEntry 17 }

cvpdnSessionAttrModemCallStartTime OBJECT-TYPE
        SYNTAX      TimeStamp
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The start time of the current modem call.  Only a session
             with a device of type 'asyncInternalModem' will have a
             valid value for this object; otherwise, it is not
             instantiated."
        ::= { cvpdnSessionAttrEntry 18 }

cvpdnSessionAttrModemCallStartIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Arbitrary small integer to distinguish modem calls that
             occurred at the same time tick.  Only session over device
             'asyncInternalModem' will have a valid value for this
             object; otherwise, it is not instantiated."
        ::= { cvpdnSessionAttrEntry 19 }

cvpdnSessionAttrVirtualCircuitID OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The virtual circuit ID of an active Layer 2 VPN session."
        ::= { cvpdnSessionAttrEntry 20 }

cvpdnSessionAttrSentPktsDropped OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "packets"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of dropped packets that could not be sent
             into this active session."
        ::= { cvpdnSessionAttrEntry 21 }

cvpdnSessionAttrRecvPktsDropped OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "packets"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of dropped packets that were received from
             this active session."
        ::= { cvpdnSessionAttrEntry 22 }

cvpdnSessionAttrMultilinkBundle OBJECT-TYPE
        SYNTAX      SnmpAdminString     (SIZE(0..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object specifies the name of the multilink bundle to
             which this session belongs.  The value of this object is
             only valid when the value of cvpdnSessionAttrMultilink is
             'true'.  If the value of cvpdnSessionAttrMultilink is
             'false', then the value of this object will be the empty
             string."
        ::= { cvpdnSessionAttrEntry 23 }

cvpdnSessionAttrMultilinkIfIndex OBJECT-TYPE
        SYNTAX      InterfaceIndexOrZero
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object specifies the ifIndex of the multilink bundle
             to which this session belongs.  The value of this object is
             only valid when the value of cvpdnSessionAttrMultilink is
             'true'.  If the value of cvpdnSessionAttrMultilink is
             'false', then the value of this object will be zero."
        ::= { cvpdnSessionAttrEntry 24 }

-- ******************************************************************
-- * VPDN User Name to user failure Information
-- ******************************************************************

--
-- The cvpdnUserToFailHistInfoTable is only populated for L2F tunnels
--
cvpdnUserToFailHistInfoTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnUserToFailHistInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "Table of the record of failure objects which can be
                 referenced by an user name.  Only a name that has a
                 valid item in the Cisco IOS VPDN failure history table
                 will yield a valid entry in this table.  The table has
                 a maximum size of 50 entries.  Only the newest 50
                 entries will be kept in the table."
        ::= { cvpdnUserToFailHistInfo 1 }

cvpdnUserToFailHistInfoEntry OBJECT-TYPE
        SYNTAX      CvpdnUserToFailHistInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "An entry in the table, containing failure history
                 relevant to an user name."
        INDEX { cvpdnUnameToFailHistUname,
                cvpdnUnameToFailHistTunnelId }
        ::= { cvpdnUserToFailHistInfoTable 1 }

CvpdnUserToFailHistInfoEntry ::=
        SEQUENCE {
            cvpdnUnameToFailHistUname           DisplayString,
            cvpdnUnameToFailHistTunnelId        Unsigned32,
            cvpdnUnameToFailHistUserId          Unsigned32,
            cvpdnUnameToFailHistLocalInitConn   TruthValue,
            cvpdnUnameToFailHistLocalName       DisplayString,
            cvpdnUnameToFailHistRemoteName      DisplayString,
            cvpdnUnameToFailHistSourceIp        IpAddress,
            cvpdnUnameToFailHistDestIp          IpAddress,
            cvpdnUnameToFailHistCount           Counter32,
            cvpdnUnameToFailHistFailTime        TimeStamp,
            cvpdnUnameToFailHistFailType        DisplayString,
            cvpdnUnameToFailHistFailReason      DisplayString,
            cvpdnUnameToFailHistSourceInetType  InetAddressType,
            cvpdnUnameToFailHistSourceInetAddr  InetAddress,
            cvpdnUnameToFailHistDestInetType    InetAddressType,
            cvpdnUnameToFailHistDestInetAddr    InetAddress
        }

cvpdnUnameToFailHistUname OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The user name for this failure entry."
        ::= { cvpdnUserToFailHistInfoEntry 1 }

cvpdnUnameToFailHistTunnelId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The Tunnel ID for this failure entry.  If it is the
                 instigator of the tunnel, the ID is the TS tunnel ID,
                 otherwise it is the NAS tunnel ID."
        ::= { cvpdnUserToFailHistInfoEntry 2 }

cvpdnUnameToFailHistUserId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The user ID of this failure entry."
        ::= { cvpdnUserToFailHistInfoEntry 3 }

cvpdnUnameToFailHistLocalInitConn OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object indicates whether the tunnel in which the
                 failure occurred was generated locally or not."
        ::= { cvpdnUserToFailHistInfoEntry 4 }

cvpdnUnameToFailHistLocalName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The local name of the VPDN tunnel in which the failure
                 occurred.  It will be the NAS name of the tunnel if the
                 system serves as the NAS, or the TS name of the tunnel
                 if the system serves as the tunnel server.  The local
                 name is the configured host name of the system.  This
                 object can be empty if the failure occurred prior to
                 successful tunnel projection, thus no source name will
                 be available."
        ::= { cvpdnUserToFailHistInfoEntry 5 }

cvpdnUnameToFailHistRemoteName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The remote name of the VPDN tunnel in which the failure
                 occurred.  It will be the tunnel server name of the
                 tunnel if the system is a NAS, or the NAS name of the
                 tunnel if the system serves as the tunnel server.  This
                 object can be empty if the failure occurred prior to
                 successful tunnel projection, thus no source name will
                 be available."
        ::= { cvpdnUserToFailHistInfoEntry 6 }

cvpdnUnameToFailHistSourceIp OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      deprecated
        DESCRIPTION
                "The source IP address of the tunnel in which the
                 failure occurred.  This IP address is that of the
                 interface at the instigator end of the tunnel."
        ::= { cvpdnUserToFailHistInfoEntry 7 }

cvpdnUnameToFailHistDestIp OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      deprecated
        DESCRIPTION
                "The destination IP address of the tunnel in which the
                 failure occurred.  This IP address is that of the
                 interface at the receiver end of the tunnel."
        ::= { cvpdnUserToFailHistInfoEntry 8 }

cvpdnUnameToFailHistCount OBJECT-TYPE
        SYNTAX      Counter32
        UNITS      "failures"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object is incremented when multiple failures has
                 been experienced by this user on this tunnel.  Seeing a
                 delta of >1 is an indication that the current failure
                 record is the latest of a series of failures that has
                 been recorded."
        ::= { cvpdnUserToFailHistInfoEntry 9 }

cvpdnUnameToFailHistFailTime OBJECT-TYPE
       SYNTAX     TimeStamp
       MAX-ACCESS read-only
       STATUS     current
       DESCRIPTION
               "This object specifies the time when the failure is
                occurred."
       ::= { cvpdnUserToFailHistInfoEntry 10 }

cvpdnUnameToFailHistFailType OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The type of failure for the current failure record.  It
                 comes in a form of a an ASCII string which describes
                 the type of failure."
        ::= { cvpdnUserToFailHistInfoEntry 11 }

cvpdnUnameToFailHistFailReason OBJECT-TYPE
        SYNTAX      DisplayString       (SIZE(1..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The reason of failure for the current failure record."
        ::= { cvpdnUserToFailHistInfoEntry 12 }

cvpdnUnameToFailHistSourceInetType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Indicates the type of address contained in
                 cvpdnUnameToFailHistSourceInetAddr."
        ::= { cvpdnUserToFailHistInfoEntry 13 }

cvpdnUnameToFailHistSourceInetAddr OBJECT-TYPE
        SYNTAX      InetAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The source IP address of the tunnel in which the
                 failure occurred.  This IP address is that of the
                 interface at the instigator end of the tunnel.  The
                 instigator end is the peer which initiates the tunnel
                 estalishment.  The type of this address is determined
                 by the value of cvpdnUnameToFailHistSourceInetType."
        ::= { cvpdnUserToFailHistInfoEntry 14 }

cvpdnUnameToFailHistDestInetType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Indicates the type of address contained in
                 cvpdnUnameToFailHistDestInetAddr."
        ::= { cvpdnUserToFailHistInfoEntry 15 }

cvpdnUnameToFailHistDestInetAddr OBJECT-TYPE
        SYNTAX      InetAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The destination IP address of the tunnel in which the
                 failure occurred.  This IP address is that of the
                 interface at the receiver end of the tunnel.  The type 
                 of this address is determined by the value of 
                 cvpdnUnameToFailHistDestInetType."
        ::= { cvpdnUserToFailHistInfoEntry 16 }

-- *********************************************************************
-- * Notifications
-- *********************************************************************

ciscoVpdnMgmtMIBNotifs OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIB 0 }

cvpdnNotifSessionID OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        --MAX-ACCESS  accessible-for-notify
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object contains the local session ID of the L2X
                 session for which this notification has been
                 generated."
        ::= { ciscoVpdnMgmtMIBNotifs 1 }

cvpdnNotifSessionEvent OBJECT-TYPE
        SYNTAX      INTEGER {
                        up(1),
                        down(2),
                        pwUp(3),
                        pwDown(4)
                    }
        --MAX-ACCESS  accessible-for-notify
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Indicates the event that generated the L2X session
                 notification.

                 The events are represented as follows:

                 up:     Session has come up.

                 down:   Session has gone down.

                 pwUp:   Pseudowire associated with this 
                         session has come up.

                 pwDown: Pseudowire associated with this 
                         session has gone down.
                "
        ::= { ciscoVpdnMgmtMIBNotifs 2 }

cvpdnNotifSession NOTIFICATION-TYPE
       OBJECTS {
           cvpdnNotifSessionID,
           cvpdnNotifSessionEvent,
           cvpdnSessionAttrDevicePhyId,
           cvpdnSessionAttrVirtualCircuitID
       }
       STATUS       current
       DESCRIPTION
                "Conveys an event regarding the L2X session with the
                 indicated session ID and Xconnect VCID."
       ::= { ciscoVpdnMgmtMIBNotifs 3 }

-- ******************************************************************
-- * VPDN Template Information Table
-- ******************************************************************

cvpdnTemplateTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnTemplateEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "Table of information about the VPDN templates.  The
                 VPDN template is a grouping mechanism that allows
                 configuration settings to be shared among multiple VPDN
                 groups.  One such setting is a limit on the number of
                 active sessions across all VPDN groups associated with
                 the template.  The template table allows customers to
                 monitor template-wide information such as tracking the
                 allocation of sessions across templates."
        ::= { cvpdnTemplateInfo 1 }

cvpdnTemplateEntry OBJECT-TYPE
        SYNTAX      CvpdnTemplateEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "An entry in the table, containing information about a
                 single VPDN template."
        INDEX { IMPLIED cvpdnTemplateName }
        ::= { cvpdnTemplateTable 1 }

CvpdnTemplateEntry ::=
        SEQUENCE {
            cvpdnTemplateName                   SnmpAdminString,
            cvpdnTemplateActiveSessions         Gauge32
        }

cvpdnTemplateName OBJECT-TYPE
        SYNTAX      SnmpAdminString     (SIZE(1..255))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The name of the VPDN template."
        ::= { cvpdnTemplateEntry 1 }

cvpdnTemplateActiveSessions OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "sessions"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The total number of active session in all groups
                 associated with the template."
        ::= { cvpdnTemplateEntry 2 }

-- ******************************************************************
-- * VPDN Multilink Information Objects
-- ******************************************************************

cvpdnBundlesWithOneLink OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of bundles comprised of a single link."
        ::= { cvpdnMultilinkInfo 1 }

cvpdnBundlesWithTwoLinks OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of bundles comprised of two links."
        ::= { cvpdnMultilinkInfo 2 }

cvpdnBundlesWithMoreThanTwoLinks OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of bundles comprised of more than two
            links."
        ::= { cvpdnMultilinkInfo 3 }

cvpdnBundleTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnBundleEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Table that describes the multilink PPP attributes of the
            active VPDN sessions."
        ::= { cvpdnMultilinkInfo 4 }

cvpdnBundleEntry OBJECT-TYPE
        SYNTAX      CvpdnBundleEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry in this table represents an active multilink PPP
            bundle that belongs to a VPDN tunnel."
        INDEX { cvpdnBundleName }
        ::= { cvpdnBundleTable 1 }

CvpdnBundleEntry ::=
        SEQUENCE {
            cvpdnBundleName           SnmpAdminString,
            cvpdnBundleLinkCount      Gauge32,
            cvpdnBundleEndpointType   INTEGER,
            cvpdnBundleEndpoint       OCTET STRING,
            cvpdnBundlePeerIpAddrType InetAddressType,
            cvpdnBundlePeerIpAddr     InetAddress,
            cvpdnBundleEndpointClass  EndpointClass
        }

cvpdnBundleName OBJECT-TYPE
        SYNTAX      SnmpAdminString     (SIZE(1..64))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The name of the multilink PPP bundle associated with a VPDN
            tunnel."
        ::= { cvpdnBundleEntry 1 }

cvpdnBundleLinkCount OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "links"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of active links in a multilink PPP bundle
            associated with a VPDN tunnel."
        ::= { cvpdnBundleEntry 2 }

cvpdnBundleEndpointType OBJECT-TYPE
        SYNTAX      INTEGER {
                        none(1),
                        hostname(2),
                        string(3),
                        macAddress(4),
                        ipV4Address(5),
                        ipV6Address(6),
                        phone(7),
                        magicNumber(8)
                    }
        MAX-ACCESS  read-only
        STATUS      deprecated
        DESCRIPTION
            "The multilink PPP bundle discriminator type associated with
            a VPDN tunnel.  The value of this object represents the type
            of discriminator used in cvpdnBundleEndpoint.

                none:        No endpoint discriminator was supplied, the
                             default value is being used.

                hostname:    The router's hostname is being used as
                             discriminator.

                string:      User specified string is being used as
                             discriminator.

                macAddress:  A MAC address as defined by the MacAddress
                             textual convention is being used as
                             discriminator.

                ipV4Address: An IP address as defined by the
                             InetAddressIPv4 textual convention is being
                             used as discriminator.

                ipV6Address: An IP address as defined by the
                             InetAddressIPv6 textual convention is being
                             used as discriminator.

                phone:       The PSTN phone number is being used as
                             discriminator.

                magicNumber: A magic number is being used as
                             discriminator."
        ::= { cvpdnBundleEntry 3 }

cvpdnBundleEndpoint OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (0..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Indicates the discriminator used in this bundle that is
             associated with a VPDN tunnel."
        ::= { cvpdnBundleEntry 4 }

cvpdnBundlePeerIpAddrType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Indicates the type of address contained in
             cvpdnBundlePeerIpAddr."
        ::= { cvpdnBundleEntry 5 }

cvpdnBundlePeerIpAddr OBJECT-TYPE
        SYNTAX      InetAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The IP address of the multilink PPP peer in a VPDN tunnel."
        ::= { cvpdnBundleEntry 6 }

cvpdnBundleEndpointClass OBJECT-TYPE
        SYNTAX      EndpointClass
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The multilink PPP bundle discriminator class associated
            with a VPDN tunnel.  The value of this object represents the
            type of discriminator used in cvpdnBundleEndpoint."
        ::= { cvpdnBundleEntry 7 }

cvpdnBundleLastChanged OBJECT-TYPE
        SYNTAX      TimeStamp
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The value of the sysUpTime object when the contents of
            cvpdnBundleTable last changed."
        ::= { cvpdnMultilinkInfo 5 }

cvpdnBundleChildTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvpdnBundleChildEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table that exposes the containment relationship between a
            multilink PPP bundle and a VPDN tunnel."
        ::= { cvpdnMultilinkInfo 6 }

cvpdnBundleChildEntry OBJECT-TYPE
        SYNTAX      CvpdnBundleChildEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry in this table represents a session that belongs to
            a VPDN tunnel and to a multilink PPP bundle."
        INDEX { cvpdnBundleName,
                cvpdnBundleChildTunnelType,
                cvpdnBundleChildTunnelId,
                cvpdnBundleChildSessionId }
        ::= { cvpdnBundleChildTable 1 }

CvpdnBundleChildEntry ::=
        SEQUENCE {
            cvpdnBundleChildTunnelType      TunnelType,
            cvpdnBundleChildTunnelId        Unsigned32,
            cvpdnBundleChildSessionId       Unsigned32
        }

cvpdnBundleChildTunnelType OBJECT-TYPE
        SYNTAX      TunnelType
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The tunnel type.  This is the tunnel protocol of an active
            VPDN session that is associated with a multilink PPP
            bundle."
        ::= { cvpdnBundleChildEntry 1 }

cvpdnBundleChildTunnelId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The Tunnel ID of an active VPDN session that is associated
            with a multilink PPP bundle."
        ::= { cvpdnBundleChildEntry 2 }

cvpdnBundleChildSessionId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The ID of an active VPDN session that is associated with a
            multilink PPP bundle."
        ::= { cvpdnBundleChildEntry 3 }

-- *********************************************************************
-- * Conformance
-- *********************************************************************

ciscoVpdnMgmtMIBConformance OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIB 3 }

ciscoVpdnMgmtMIBCompliances OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBConformance 1 }

ciscoVpdnMgmtMIBGroups      OBJECT IDENTIFIER
        ::= { ciscoVpdnMgmtMIBConformance 2 }

-- *********************************************************************
-- * Compliance
-- *********************************************************************

ciscoVpdnMgmtMIBCompliance MODULE-COMPLIANCE
        STATUS obsolete -- superceded by ciscoVpdnMgmtMIBComplianceRev1
        DESCRIPTION
                "The compliance statement for entities which implement
                 the Cisco VPDN Management MIB"
        MODULE  -- this module
                MANDATORY-GROUPS {
                        cvpdnSystemInfoGroup,
                        cvpdnTunnelInfoGroup,
                        cvpdnTunnelSessionInfoGroup,
                        cvpdnUserToFailHistInfoGroup
                }
        ::= { ciscoVpdnMgmtMIBCompliances 1 }

ciscoVpdnMgmtMIBComplianceRev1 MODULE-COMPLIANCE
        STATUS obsolete -- superceded by
                        -- ciscoVpdnMgmtMIBComplianceRev2
        DESCRIPTION
                "The compliance statement for entities which implement
                the Cisco VPDN Management MIB"
        MODULE  -- this module
                MANDATORY-GROUPS {
                        cvpdnSystemInfoGroup,
                        cvpdnTunnelInfoGroup,
                        cvpdnTunnelSessionInfoGroup,
                        cvpdnUserToFailHistInfoGroup,
                        cvpdnSystemGroup,
                        cvpdnTunnelAttrGroup,
                        cvpdnSessionAttrGroup
                }
        ::= { ciscoVpdnMgmtMIBCompliances 2 }

ciscoVpdnMgmtMIBComplianceRev2 MODULE-COMPLIANCE
        STATUS deprecated -- superceded by
                          -- ciscoVpdnMgmtMIBComplianceRev3
        DESCRIPTION
                "The compliance statement for entities which implement
                the Cisco VPDN Management MIB"
        MODULE  -- this module
                MANDATORY-GROUPS {
                        cvpdnUserToFailHistInfoGroup,
                        cvpdnSystemGroup,
                        cvpdnTunnelAttrGroup,
                        cvpdnSessionAttrGroupRev1
                }
        ::= { ciscoVpdnMgmtMIBCompliances 3 }

ciscoVpdnMgmtMIBComplianceRev3 MODULE-COMPLIANCE
        STATUS deprecated -- superceded by
                          -- ciscoVpdnMgmtMIBComplianceRev4
        DESCRIPTION
                "The compliance statement for entities which implement
                the Cisco VPDN Management MIB"
        MODULE  -- this module
                MANDATORY-GROUPS {
                        cvpdnUserToFailHistInfoGroup,
                        cvpdnSystemGroup,
                        cvpdnTunnelAttrGroup,
                        cvpdnSessionAttrGroupRev1,
                        cvpdnTemplateGroup,
                        cvpdnNotifEnabledGroup,
                        cvpdnSessionNotifGroup,
                        cvpdnConfigGroup,
                        cvpdnMultilinkGroup
                }
        ::= { ciscoVpdnMgmtMIBCompliances 4 }

ciscoVpdnMgmtMIBComplianceRev4 MODULE-COMPLIANCE
        STATUS deprecated -- superceded by
                          -- ciscoVpdnMgmtMIBComplianceRev5
        DESCRIPTION
                "The compliance statement for entities which implement
                the Cisco VPDN Management MIB"
        MODULE  -- this module
                MANDATORY-GROUPS {
                        cvpdnUserToFailHistInfoGroup,
                        cvpdnSystemGroup,
                        cvpdnTunnelAttrGroup,
                        cvpdnSessionAttrGroupRev1,
                        cvpdnTemplateGroup,
                        cvpdnNotifEnabledGroup,
                        cvpdnSessionNotifGroup,
                        cvpdnConfigGroup,
                        cvpdnMultilinkGroupRev1
                }
        ::= { ciscoVpdnMgmtMIBCompliances 5 }

ciscoVpdnMgmtMIBComplianceRev5 MODULE-COMPLIANCE
        STATUS current
        DESCRIPTION
                "The compliance statement for entities which implement
                 the Cisco VPDN Management MIB"
        MODULE  -- this module
                MANDATORY-GROUPS {
                        cvpdnUserToFailHistInfoGroupRev1,
                        cvpdnSystemGroup,
                        cvpdnTunnelAttrGroupRev1,
                        cvpdnSessionAttrGroupRev1,
                        cvpdnTemplateGroup,
                        cvpdnNotifEnabledGroup,
                        cvpdnSessionNotifGroup,
                        cvpdnConfigGroup,
                        cvpdnMultilinkGroupRev1
                }
        ::= { ciscoVpdnMgmtMIBCompliances 6 }

-- *********************************************************************
-- * Units of Conformance
-- *********************************************************************

cvpdnSystemInfoGroup OBJECT-GROUP
        OBJECTS {
            cvpdnTunnelTotal,
            cvpdnSessionTotal,
            cvpdnDeniedUsersTotal
        }
        STATUS obsolete
        DESCRIPTION
                "A collection of objects providing VPDN system status
                 information."
        ::= { ciscoVpdnMgmtMIBGroups 1 }

cvpdnTunnelInfoGroup OBJECT-GROUP
        OBJECTS {
            cvpdnTunnelRemoteTunnelId,
            cvpdnTunnelLocalName,
            cvpdnTunnelRemoteName,
            cvpdnTunnelRemoteEndpointName,
            cvpdnTunnelLocalInitConnection,
            cvpdnTunnelOrigCause,
            cvpdnTunnelState,
            cvpdnTunnelActiveSessions,
            cvpdnTunnelDeniedUsers,
            cvpdnTunnelSoftshut,
            cvpdnTunnelNetworkServiceType,
            cvpdnTunnelLocalIpAddress,
            cvpdnTunnelSourceIpAddress,
            cvpdnTunnelRemoteIpAddress
        }
        STATUS obsolete
        DESCRIPTION
                "A collection of objects providing VPDN tunnel status
                 information."
        ::= { ciscoVpdnMgmtMIBGroups 2 }

cvpdnTunnelSessionInfoGroup OBJECT-GROUP
        OBJECTS {
            cvpdnTunnelSessionUserName,
            cvpdnTunnelSessionState,
            cvpdnTunnelSessionCallDuration,
            cvpdnTunnelSessionPacketsOut,
            cvpdnTunnelSessionBytesOut,
            cvpdnTunnelSessionPacketsIn,
            cvpdnTunnelSessionBytesIn,
            cvpdnTunnelSessionDeviceType,
            cvpdnTunnelSessionDeviceCallerId,
            cvpdnTunnelSessionDevicePhyId,
            cvpdnTunnelSessionMultilink,
            cvpdnTunnelSessionModemSlotIndex,
            cvpdnTunnelSessionModemPortIndex,
            cvpdnTunnelSessionDS1SlotIndex,
            cvpdnTunnelSessionDS1PortIndex,
            cvpdnTunnelSessionDS1ChannelIndex,
            cvpdnTunnelSessionModemCallStartTime,
            cvpdnTunnelSessionModemCallStartIndex
        }
        STATUS obsolete
        DESCRIPTION
                "A collection of objects providing session information
                 of VPDN tunnel."
        ::= { ciscoVpdnMgmtMIBGroups 3 }

cvpdnUserToFailHistInfoGroup  OBJECT-GROUP
        OBJECTS {
            cvpdnUnameToFailHistUserId,
            cvpdnUnameToFailHistLocalInitConn,
            cvpdnUnameToFailHistLocalName,
            cvpdnUnameToFailHistRemoteName,
            cvpdnUnameToFailHistSourceIp,
            cvpdnUnameToFailHistDestIp,
            cvpdnUnameToFailHistCount,
            cvpdnUnameToFailHistFailTime,
            cvpdnUnameToFailHistFailType,
            cvpdnUnameToFailHistFailReason
        }
        STATUS deprecated -- superceded by cvpdnUserToFailHistInfoGroupRev1
        DESCRIPTION
                "A collection of objects providing user failure
                 information of VPDN system."
        ::= { ciscoVpdnMgmtMIBGroups 4 }

cvpdnSystemGroup OBJECT-GROUP
        OBJECTS {
            cvpdnSystemTunnelTotal,
            cvpdnSystemSessionTotal,
            cvpdnSystemDeniedUsersTotal,
            cvpdnSystemInitialConnReq,
            cvpdnSystemSuccessConnReq,
            cvpdnSystemFailedConnReq
        }
        STATUS current
        DESCRIPTION
            "A collection of objects providing VPDN system status
             information for multiple tunnel types."
        ::= { ciscoVpdnMgmtMIBGroups 5 }

cvpdnTunnelAttrGroup OBJECT-GROUP
        OBJECTS {
            cvpdnTunnelAttrRemoteTunnelId,
            cvpdnTunnelAttrLocalName,
            cvpdnTunnelAttrRemoteName,
            cvpdnTunnelAttrRemoteEndpointName,
            cvpdnTunnelAttrLocalInitConnection,
            cvpdnTunnelAttrOrigCause,
            cvpdnTunnelAttrState,
            cvpdnTunnelAttrActiveSessions,
            cvpdnTunnelAttrDeniedUsers,
            cvpdnTunnelAttrSoftshut,
            cvpdnTunnelAttrNetworkServiceType,
            cvpdnTunnelAttrLocalIpAddress,
            cvpdnTunnelAttrSourceIpAddress,
            cvpdnTunnelAttrRemoteIpAddress
        }
        STATUS deprecated -- superceded by cvpdnTunnelAttrGroupRev1
        DESCRIPTION
            "A collection of objects providing VPDN tunnel attribute
             information for multiple tunnel types."
        ::= { ciscoVpdnMgmtMIBGroups 6 }

cvpdnSessionAttrGroup OBJECT-GROUP
        OBJECTS {
            cvpdnSessionAttrUserName,
            cvpdnSessionAttrState,
            cvpdnSessionAttrCallDuration,
            cvpdnSessionAttrPacketsOut,
            cvpdnSessionAttrBytesOut,
            cvpdnSessionAttrPacketsIn,
            cvpdnSessionAttrBytesIn,
            cvpdnSessionAttrDeviceType,
            cvpdnSessionAttrDeviceCallerId,
            cvpdnSessionAttrDevicePhyId,
            cvpdnSessionAttrMultilink,
            cvpdnSessionAttrModemSlotIndex,
            cvpdnSessionAttrModemPortIndex,
            cvpdnSessionAttrDS1SlotIndex,
            cvpdnSessionAttrDS1PortIndex,
            cvpdnSessionAttrDS1ChannelIndex,
            cvpdnSessionAttrModemCallStartTime,
            cvpdnSessionAttrModemCallStartIndex
        }
        STATUS deprecated -- superceded by cvpdnSessionAttrGroupRev1
        DESCRIPTION
            "A collection of objects providing session attributed
             information for multiple tunnel types."
        ::= { ciscoVpdnMgmtMIBGroups 7 }

cvpdnSessionAttrGroupRev1 OBJECT-GROUP
        OBJECTS {
            cvpdnSessionAttrUserName,
            cvpdnSessionAttrState,
            cvpdnSessionAttrCallDuration,
            cvpdnSessionAttrPacketsOut,
            cvpdnSessionAttrBytesOut,
            cvpdnSessionAttrPacketsIn,
            cvpdnSessionAttrBytesIn,
            cvpdnSessionAttrDeviceType,
            cvpdnSessionAttrDeviceCallerId,
            cvpdnSessionAttrDevicePhyId,
            cvpdnSessionAttrMultilink,
            cvpdnSessionAttrModemSlotIndex,
            cvpdnSessionAttrModemPortIndex,
            cvpdnSessionAttrDS1SlotIndex,
            cvpdnSessionAttrDS1PortIndex,
            cvpdnSessionAttrDS1ChannelIndex,
            cvpdnSessionAttrModemCallStartTime,
            cvpdnSessionAttrModemCallStartIndex,
            cvpdnSessionAttrVirtualCircuitID,
            cvpdnSessionAttrSentPktsDropped,
            cvpdnSessionAttrRecvPktsDropped
        }
        STATUS current
        DESCRIPTION
            "A collection of objects providing session attributed
             information for multiple tunnel types."
        ::= { ciscoVpdnMgmtMIBGroups 8 }

cvpdnNotifEnabledGroup OBJECT-GROUP
        OBJECTS {
            cvpdnSystemNotifSessionEnabled,
            cvpdnNotifSessionID,
            cvpdnNotifSessionEvent
        }
        STATUS current
        DESCRIPTION
            "A collection of objects indicating whether Layer 2 VPN
             notifications are enabled."
        ::= { ciscoVpdnMgmtMIBGroups 9 }

cvpdnSessionNotifGroup NOTIFICATION-GROUP
        NOTIFICATIONS {
            cvpdnNotifSession
        }
        STATUS current
        DESCRIPTION
            "A collection of objects providing basic Layer 2 VPN session
             notifications."
        ::= { ciscoVpdnMgmtMIBGroups 10 }

cvpdnTemplateGroup OBJECT-GROUP
        OBJECTS {
            cvpdnTemplateActiveSessions
        }
        STATUS current
        DESCRIPTION
            "A collection of objects providing VPDN template
             information."
        ::= { ciscoVpdnMgmtMIBGroups 11 }

cvpdnConfigGroup OBJECT-GROUP
        OBJECTS {
            cvpdnSystemClearSessions
        }
        STATUS current
        DESCRIPTION
            "A collection of objects providing VPDN system
             configuration."
        ::= { ciscoVpdnMgmtMIBGroups 12 }

cvpdnMultilinkGroup OBJECT-GROUP
        OBJECTS {
            cvpdnSessionAttrMultilinkBundle,
            cvpdnSessionAttrMultilinkIfIndex,
            cvpdnBundlesWithOneLink,
            cvpdnBundlesWithTwoLinks,
            cvpdnBundlesWithMoreThanTwoLinks,
            cvpdnBundleLinkCount,
            cvpdnBundleEndpointType,
            cvpdnBundleEndpoint,
            cvpdnBundlePeerIpAddrType,
            cvpdnBundlePeerIpAddr,
            cvpdnBundleLastChanged,
            cvpdnBundleChildSessionId
        }
        STATUS deprecated -- superceded by cvpdnMultilinkGroupRev1
        DESCRIPTION
            "A collection of objects providing information about PPP
             multilink bundles associates with a VPDN tunnel."
        ::= { ciscoVpdnMgmtMIBGroups 13 }

cvpdnMultilinkGroupRev1 OBJECT-GROUP
        OBJECTS {
            cvpdnSessionAttrMultilinkBundle,
            cvpdnSessionAttrMultilinkIfIndex,
            cvpdnBundlesWithOneLink,
            cvpdnBundlesWithTwoLinks,
            cvpdnBundlesWithMoreThanTwoLinks,
            cvpdnBundleLinkCount,
            cvpdnBundleEndpoint,
            cvpdnBundlePeerIpAddrType,
            cvpdnBundlePeerIpAddr,
            cvpdnBundleLastChanged,
            cvpdnBundleChildSessionId,
            cvpdnBundleEndpointClass
        }
        STATUS current
        DESCRIPTION
            "A collection of objects providing information about PPP
             multilink bundles associates with a VPDN tunnel."
        ::= { ciscoVpdnMgmtMIBGroups 14 }

cvpdnUserToFailHistInfoGroupRev1  OBJECT-GROUP
        OBJECTS {
            cvpdnUnameToFailHistUserId,
            cvpdnUnameToFailHistLocalInitConn,
            cvpdnUnameToFailHistLocalName,
            cvpdnUnameToFailHistRemoteName,
            cvpdnUnameToFailHistCount,
            cvpdnUnameToFailHistFailTime,
            cvpdnUnameToFailHistFailType,
            cvpdnUnameToFailHistFailReason,
            cvpdnUnameToFailHistSourceInetType,
            cvpdnUnameToFailHistSourceInetAddr,
            cvpdnUnameToFailHistDestInetType,
            cvpdnUnameToFailHistDestInetAddr
        }
        STATUS current
        DESCRIPTION
                "A collection of objects providing user failure
                 information of VPDN system."
        ::= { ciscoVpdnMgmtMIBGroups 15 }

cvpdnTunnelAttrGroupRev1 OBJECT-GROUP
        OBJECTS {
            cvpdnTunnelAttrRemoteTunnelId,
            cvpdnTunnelAttrLocalName,
            cvpdnTunnelAttrRemoteName,
            cvpdnTunnelAttrRemoteEndpointName,
            cvpdnTunnelAttrLocalInitConnection,
            cvpdnTunnelAttrOrigCause,
            cvpdnTunnelAttrState,
            cvpdnTunnelAttrActiveSessions,
            cvpdnTunnelAttrDeniedUsers,
            cvpdnTunnelAttrSoftshut,
            cvpdnTunnelAttrNetworkServiceType,
            cvpdnTunnelAttrLocalInetAddressType,
            cvpdnTunnelAttrLocalInetAddress,
            cvpdnTunnelAttrSourceInetAddressType,
            cvpdnTunnelAttrSourceInetAddress,
            cvpdnTunnelAttrRemoteInetAddressType,
            cvpdnTunnelAttrRemoteInetAddress
        }
        STATUS current
        DESCRIPTION
            "A collection of objects providing VPDN tunnel attribute
             information for multiple tunnel types."
        ::= { ciscoVpdnMgmtMIBGroups 16 }

END
