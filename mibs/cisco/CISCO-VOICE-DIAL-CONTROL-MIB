-- *****************************************************************
-- CISCO-VOICE-DIAL-CONTROL-MIB.my: Voice Dial Control MIB file
--   
-- September 1996  <PERSON><PERSON> Shih
--   
-- Copyright (c) 1996-1999, 2000-2003, 2004-2006, 2009-2012 by cisco Systems Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-VOICE-DIAL-CONTROL-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    NOTIFICATION-TYPE,
    Ip<PERSON>ddress,
    Gauge32,
    Integer32,
    Unsigned32,
    Counter32
        FROM SNMPv2-SMI
    MODULE-COMPL<PERSON>NCE,
    OBJECT-GROUP,
    NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    TEXTUAL-CONVENTION,
    DisplayString,
    RowStatus,
    TruthValue,
    TimeStamp,
    PhysAddress,
    DateAndTime
        FROM SNMPv2-TC
    callActiveSetupTime,
    callActiveIndex,
    AbsoluteCounter32,
    callActivePeerAddress,
    callActivePeerId
        FROM DIAL-CONTROL-MIB
    QosService
        FROM INT-SERV-MIB
    ifIndex,
    InterfaceIndexOrZero
        FROM IF-MIB
    InetAddressType,
    InetAddress
        FROM INET-ADDRESS-MIB
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    cCallHistoryIndex,
    cCallHistoryCallOrigin
        FROM CISCO-DIAL-CONTROL-MIB
    CvcSpeechCoderRate,
    CvcFaxTransmitRate,
    CvcCoderTypeRate,
    CvcGUid,
    CvcInBandSignaling,
    cvCommonDcCallHistoryCoderTypeRate,
    CvcCallReferenceIdOrZero
        FROM CISCO-VOICE-COMMON-DIAL-CONTROL-MIB
    DnisMapname
        FROM CISCO-VOICE-DNIS-MIB
    ciscoMgmt
        FROM CISCO-SMI;


ciscoVoiceDialControlMIB MODULE-IDENTITY
    LAST-UPDATED    "201205150000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
            "Cisco Systems
            Customer Service

            Postal: 170 W. Tasman Drive
            San Jose, CA  95134
            USA

            Tel: ****** 553-NETS

            E-mail: <EMAIL>"
    DESCRIPTION
        "This MIB module enhances the IETF Dial Control MIB
        (RFC2128) by providing management of voice telephony
        peers on both a circuit-switched telephony network,
        and an IP data network.

        *** ABBREVIATIONS, ACRONYMS AND SYMBOLS ***

        GSM    - Global System for Mobile Communication

        AMR-NB - Adaptive Multi Rate - Narrow Band 

        iLBC   - internet Low Bitrate Codec 

        KPML   - Key Press Markup Language

        MGCP   - Media Gateway Control Protocol.

        SIP    - Session Initiation Protocol.

        H323   - One of the voip signalling protocol.

        SCCP   - Skinny Client Control Protocol.

        dialpeer - This represents a configured interface that 
                   carries the dial map.  A dialpeer maps the 
                   called and calling numbers with the port or 
                   IP interface to be used.

        License call capacity - This represents the licensed 
                                maximum call capacity of 
                                the gateway.

        iSAC    -  Internet Speech Audio Codec

        RPH    - Resource Priority Header

        DSCP   - Diffserv Code Points"
    REVISION        "201205150000Z"
    DESCRIPTION
        "[1] Added New OBJECT-GROUP cvCallVolumeStatsHistory
        in cvGatewayCallHistory.
        [2] Added following tables
         cvCallRateStatsTable,
         cvCallLegRateStatsTable,
         cvActiveCallStatsTable,
         cvCallDurationStatsTable,
         cvSipMsgRateStatsTable under cvCallVolumeStatsHistory
        [3] Added following tables
         cvCallRateWMTable,
         cvCallLegRateWMTable,
         cvActiveCallWMTable,
         cvSipMsgRateWMTable under cvCallVolumeStatsHistory
        [4] Added following textual conversation 
         CvCallVolumeStatsIntvlType &
         CvCallVolumeWMIntvlType         
        [5] Added new cvdcCallVolumeStatsHistory conformance group 
         in cvdcMIBGroups. 
        [6] Added cvdcMIBComplianceRev22 depricating cvdcMIBComplianceRev21"
    REVISION        "201107110000Z"
    DESCRIPTION
        "[1] Added cvVoIPCallActiveSessionId in cvVoIPCallActiveEntry and
        cvVoIPCallHistorySessionId in cvVoIPCallHistoryEntry
        [2] Added cvVoIPCallActiveSessionId and
        cvVoIPCallHistorySessionId to cvVoIPCallGroupSup3
        [3] Deprecated cvVoIPCallGroupSup2 and superceded it with
        cvVoIPCallGroupSup3
        [4] Deprecated cvdcMIBComplianceRev20 and superceded it with
        with cvdcMIBComplianceRev21"
    REVISION        "201105310000Z"
    DESCRIPTION
        "The following changes is done for RPH to DSCP mapping and Media
        policy feature.
        1) Added two new objects in the cvVoIPCallActiveEntry 
           ccVoIPCallActivePolicyName
           cvVoIPCallActiveReversedDirectionPeerAddress
        2) Added one new notification object
           cvdcPolicyViolationNotification
        3) Imported two objects from DIAL-CONTROL-MIB
           callActivePeerId
           callActivePeerAddress
        4) Added four objects as part of new Notification
        cvdcPolicyViolationNotification
           ccVoIPCallActiveViolationReason
           cvVoIPCallActiveCalledNumber
           callActivePeerId
           callActivePeerAddress
        5) Added new notification group cvdcNotificationGroupRev4 and
        changed the status of cvdcNotificationGroupRev3 to depricated.
        6) Added new conformance group cvVoIPCallGroupSup2 and changed
        the status of cvVoIPCallGroupSup1 to deprecated.
        7) Modified cvdcMIBComplianceRev20 to add new groups and remove
        the deprecated one
        8) Added the following objects in CvVoIPPeerCfgEntry            
           cvVoIPPeerCfgDSCPPolicyNotificationEnable 
           cvVoIPPeerCfgMediaPolicyNotificationEnable
        9) Added the following objects in cvdcGeneralCfgGroupRev6 
           cvGeneralDSCPPolicyNotificationEnable
           cvGeneralMediaPolicyNotificationEnable"
    REVISION        "201007260000Z"
    DESCRIPTION
        "[1] Modified MAX-ACCESS for cvVoIPPeerCfgFrameSize from read-only to read-write.
        [2] Added a call reference ID to associate the video call entry and
        voice call entry of the same endpoint."
    REVISION        "200905070000Z"
    DESCRIPTION
        "[1] Added Abbreviation for iSAC
        [2] Added the following objects to cvVoIPPeerCfgTable for iSAC
        codec support.
           - cvVoIPPeerCfgCodingMode
           - cvVoIPPeerCfgBitRate
           - cvVoIPPeerCfgFramesize
        [3] Added a new OBJECT-GROUP cvdcVoIPCfgISACGroup.
        [4] Added a new MODULE-COMPLIANCE cvdcMIBComplianceRev19 which
        deprecates cvdcMIBComplianceRev18."
    REVISION        "200904200000Z"
    DESCRIPTION
        "The following changes have been made.

        [1] Added new CvCallConnectionType TEXTUAL-CONVENTION.
        [2] Added the following new OBJECT IDENTIFIERs: 
            cvCallVolume, 
            cvCallRateMonitor
        [3] Added the following objects to cvCallVolume: 
            cvCallVolConnTable,
            cvCallVolConnTotalActiveConnections,
            cvCallVolConnMaxCallConnectionLicenese,
            cvCallVolPeerTable,
            cvCallVolIfTable.
        [4] Added the following objects to new cvCallVolConnTable:
            cvCallVolConnIndex,
            cvCallVolConnActiveConnection.
        [5] Added the following objects to new cvCallVolPeerTable:
            cvCallVolPeerIncomingCalls,
            cvCallVolPeerOutgoingCalls.
        [6] Added the following objects to new cvCallVolIfTable:
            cvCallVolMediaIncomingCalls,
            cvCallVolMediaOutgoingCalls.
        [7] Added the following objects to cvCallRateMonitor: 
            cvCallRateMonitorEnable,
            cvCallRateMonitorTime,
            cvCallRate,
            cvCallRateHiWaterMark.
        [8] Add the following OBJECT-GROUPs:
            cvCallVolumeGroup,
            cvCallRateMonitorGroup.
        [9] Deprecated MODULE-COMPLIANCE cvdcMIBComplianceRev17.
        [10] Added MODULE-COMPLIANCE cvdcMIBComplianceRev18."
    REVISION        "200901120000Z"
    DESCRIPTION
        "The following changes have been made.

        [1] The descriptions of the following objects have   
            been modified to replace the term 'call identifier'
            with 'connection identifier'.  
            cvCallActiveConnectionId  
            cvVoIPCallActiveConnectionId  
            cvCallHistoryConnectionId  
            cvVoIPCallHistoryConnectionId.  
        [2] The object cvCallActiveCallId has been added  
            to cvCallActiveTable.  
        [3] The object cvVoIPCallActiveCallId has been added  
            to cvVoIPCallActiveTable.  
        [4] The object cvCallHistoryCallId has been added  
            to cvCallHistoryTable.  
        [5] The object cvVoIPCallHistoryCallId has been added  
            to cvVoIPCallHistoryTable.
        [6] 'sipKpml' has been added to the object
            cvVoIPPeerCfgDigitRelay.
        [7] Added following OBJECT-GROUPs
            cvCallGroupSup1,
            cvVoIPCallGroupSup1
        [8] Deprecated MODULE-COMPLIANCE
            cvdcMIBComplianceRev16 and is superceded by
            cvdcMIBComplianceRev17."
    REVISION        "200603120000Z"
    DESCRIPTION
        "[1] Added Abbreviations for GSM, AMR-NB and iLBC
        [2] Added cvVoIPPeerCfgCoderMode object to 
            cvVoIPPeerCfgTable for iLBC codec support.
        [3] Added cvVoIPCallActiveCoderMode object to 
            cvVoIPCallActiveTable for iLBC codec support.
        [4] Added cvVoIPCallHistoryCoderMode object to 
            cvVoIPCallHistoryTable for iLBC codec support.
        [5] Created CvIlbcFrameMode textual convention.
        [6] Added REFERENCE clause to all new MIB objects and 
            textual conventions added.
        [7] Created cvVoIPCallIlbcGroup and cvdcVoIPCfgIlbcGroup 
            groups for iLBC specific objects.
        [8] Created module-compliance cvdcMIBComplianceRev16 and
            made cvVoIPCallIlbcGroup and cvdcVoIPCfgIlbcGroup
            as non mandatory.
        [9] Changed the default value for cvVoIPPeerCfgBitRates to
            all 8 bitrates as all 8 bitrates are supported."
    REVISION        "200509290000Z"
    DESCRIPTION
        "Modified DESCRIPTION of cvPeerCfgPeerType object."
    REVISION        "200507250000Z"
    DESCRIPTION
        "[1] Add new objects to cvVoIPCallActiveTable and
        cvVoIPCallHistoryTable for GSM AMR-NB codec support:
        Frame type, Bit rates, Mode change period,
        Mode change neighbor, Max payload time, CRC,
        Robust sorting, Encapsulation, Payload time
        channels, for call active and history table.
        [2] Add new objects to cvVoIPPeerCfgTable for GSM AMR-NB
        codec support:
        Frame type, Bit rates and CRC.
        [3] Created groups cvVoIPCallAmrNbGroup and
        cvdcVoIPCfgAmrNbGroup, for GSM AMR-NB
        specific objects.
        [4] Created a module-compliance cvdcMIBComplianceRev13
        and made cvVoIPCallAmrNbGroup and cvdcVoIPCfgAmrNbGroup
        as non mandatory.
        [5] Created following textual conventions:
        CvAmrNbBitRateMode and CvAmrNbRtpEncap."
    REVISION        "200503020000Z"
    DESCRIPTION
        "The following values were added to the
        cvVoicePeerCfgEchoCancellerTest object:
        echoCancellerG168Test5,
        echoCancellerG168Test7"
    REVISION        "200503010000Z"
    DESCRIPTION
        "Modified DESCRIPTION of cvVoIPCallHistoryFallbackLoss
        object."
    REVISION        "200404300000Z"
    DESCRIPTION
        "Added object
        Added cvVoIPCallHistoryRemSigIPAddrT and
        cvVoIPCallHistoryRemSigIPAddr to the 
        cvdcFallbackNotification ."
    REVISION        "200404160000Z"
    DESCRIPTION
        "Added object cvVoIPCallHistoryFallbackIcpif,
        cvVoIPCallHistoryFallbackLoss and 
        cvVoIPCallHistoryFallbackDelay
        to the cvVoIPCallHistoryTable.
        Added cvdcFallbackNotification for the 
        fallback notification.
        Added cvGeneralFallbackNotificationEnable to
        enable/disable cvdcFallbackNotification."
    REVISION        "200403090000Z"
    DESCRIPTION
        "Added new objects for handling the following features:
        [1] The number of DS0s with voice calls up.
            cvCallActiveDS0s
        [2] The threshold number of DS0s above which a
            Notification will be triggered.
            cvCallActiveDS0sHighThreshold
        [3] The threshold number of DS0s below which a
            Notification will be triggered.
            cvCallActiveDS0sLowThreshold
        [4] Objects to enable the above two notifications.
            cvCallActiveDS0sHighNotifyEnable, 
            cvCallActiveDS0sLowNotifyEnable.
        [5] Notifications for the above two thresholds.
            cvdcActiveDS0sHighNotification, 
            cvdcActiveDS0sLowNotification."
    REVISION        "200401200000Z"
    DESCRIPTION
        "Added object cvVoIPCallActiveSRTPEnable to
        cvVoIPCallActiveTable and cvVoIPCallHistorySRTPEnable 
        to cvVoIPCallHistoryTable to indicate whether SRTP is 
        enabled on a call."
    REVISION        "200306260000Z"
    DESCRIPTION
        "Modified the DEVAL definition of
        cvVoIPPeerCfgDigitRelay object based on 
        no bit field was enabled when the object
        was created."
    REVISION        "200304140000Z"
    DESCRIPTION
        "The DESCRIPTION of the following objects was
        modified to specify the allowable characters
        cvPeerCommonCfgSourceTrunkGrpLabel
        cvPeerCommonCfgTargetTrunkGrpLabel
        cvPeerCommonCfgSourceCarrierId
        cvPeerCommonCfgTargetCarrierId."
    REVISION        "200212310000Z"
    DESCRIPTION
        "The following values were added to the
        cvVoicePeerCfgEchoCancellerTest object:
        echoCancellerG168Test4,
        echoCancellerG168Test6,
        echoCancellerG168Test9"
    REVISION        "200212020000Z"
    DESCRIPTION
        "The range of the following objects was changed:
        cvCallActiveNoiseLevel
        cvCallActiveACOMLevel
        cvCallActiveOutSignalLevel
        cvCallActiveInSignalLevel
        cvVoIPCallHistoryIcpif"
    REVISION        "200210310000Z"
    DESCRIPTION
        "Add object cvVoIPPeerCfgMediaSetting to the table.
        This object defines whether IP-IP media streams will
        flow-through or flow-around the Gateway.
        Add two new objects cvVoIPPeerCfgDesiredQoSVideo and
        cvVoIPPeerCfgMinAcceptableQoSVideo to the table
        cvVoIPPeerCfgTable. Also added objects
        cvCallActiveAccountCode and cvCallHistoryAccountCode.
        Depreciating element cvCallActiveERLLevel 
        Adding New element cvCallActiveERLLevelRev1
        Adding New type sipNotify in cvVoIPPeerCfgDigitRelay
        Adding New element cvVoIPPeerCfgRedirectip2ip  in
        CvVoIPPeerCfgEntry"
    REVISION        "200207120000Z"
    DESCRIPTION
        "* Add a new cvPeerCfgPeerType object to define the
        peer type
        * Added rtpNte, a new value for the
        cvVoIPPeerCfgDigitRelay object."
    REVISION        "200108200000Z"
    DESCRIPTION
        "Addition of the following objects:
        cvVoIPCallActiveUsername
        cvVoIPCallActiveProtocolCallId
        cvVoIPCallActiveRemSigIPAddrT
        cvVoIPCallActiveRemSigIPAddr
        cvVoIPCallActiveRemSigPort
        cvVoIPCallActiveRemMediaIPAddrT
        cvVoIPCallActiveRemMediaIPAddr
        cvVoIPCallActiveRemMediaPort
        cvVoIPCallHistoryUsername
        cvVoIPCallHistoryProtocolCallId
        cvVoIPCallHistoryRemSigIPAddrT
        cvVoIPCallHistoryRemSigIPAddr
        cvVoIPCallHistoryRemSigPort
        cvVoIPCallHistoryRemMediaIPAddrT
        cvVoIPCallHistoryRemMediaIPAddr
        cvVoIPCallHistoryRemMediaPort
        cvdcPoorQoVNotificationRev1
        Deprecation of the following objects:
        cvVoIPCallActiveRemoteIPAddress
        cvVoIPCallActiveRemoteUDPPort
        cvVoIPCallHistoryRemoteIPAddress
        cvVoIPCallHistoryRemoteUDPPort
        cvdcPoorQoVNotification"
    REVISION        "200107020000Z"
    DESCRIPTION
        "Add two new objects cvPeerCommonCfgSourceTrunkGrpLabel and
        cvPeerCommonCfgTargetTrunkGrpLabel."
    REVISION        "200104100000Z"
    DESCRIPTION
        "[1] Add a new object for handling the following feature:
        Location in milliseconds of largest amplitude reflector
        detected by echo canceller in call active table.
        Add new object for specifying an echo canceller test to
        to be run when a call using a dial peer so configured is
        connected.
        [2] Change QosService import from INT-SERV-MIB to
        INTEGRATED-SERVICES-MIB, since INTEGRATED-SERVICES-MIB
        (RFC2213) is where QosService is defined."
    REVISION        "200103250000Z"
    DESCRIPTION
        "Add two new objects cvPeerCommonCfgSourceCarrierId and
        cvPeerCommonCfgTargetCarrierId. Add a enum definition to 
        cvVoIPPeerCfgSessionTarget object."
    REVISION        "200005040000Z"
    DESCRIPTION
        "Add new objects for handling the following features:
        [1] Calling Name display for call active and history table.
        [2] Caller ID Block, which indicates whether the Caller ID
            feature is in using,  for call active and history table."
    REVISION        "200004190000Z"
    DESCRIPTION
        "Changed VoIP dialpeer default values for
        Icpif to 20
        ExpectFactor to 0"
    REVISION        "200003130000Z"
    DESCRIPTION
        "Adding a new object cvPeerCommonCfgDnisMappingName."
    REVISION        "9906280000Z"
    DESCRIPTION
        "Moved common Textual Conventions and the following items in
        the VoIP Call Active and Call History tables to the
        CISCO-VOICE-COMMON-DIAL-CONTROL-MIB, as these items apply to
        multiple network encapsulations.

            CoderTypeRate
            VADEnable

        Added new objects for handling the following features:
        [1] Gatekeeper registration configuration for voice peer
            destination pattern.
        [2] Dialpeer huntstop configuration.
        [3] Voip configuration support for Coder Bytes, Fax Bytes,
            settlement server and inband signaling.

        Added the following objects to the cvdcPoorQoVNotification.
            cvVoIPCallHistoryRemoteIPAddress,
            cCallHistoryCallOrigin,
            cvCommonDcCallHistoryCoderTypeRate"
    REVISION        "9901290000Z"
    DESCRIPTION
        "Add new enums in cvVoIPPeerCfgDigitRelay object for
        handling the transmission of dtmf digits across an
        H.245 channel via the signal and alphanumeric fields."
    REVISION        "9809110000Z"
    DESCRIPTION
        "Add new objects for handling the following features:
        [1] FAX page count for call active and history table.
        [2] Peer Preference for peer configuration.
        [3] Add FAX rate 12000 bps, G723.1 Annex A codec support.
        [4] Technology prefix for VoIP encapsulated peer
            configuration.
        [5] Modify cvCallActiveACOMLeve, cvCallActiveERLLevel and
            cvCallHistoryACOMLevel to support undeterminable state.
        [6] Add digit relay configuration feature."
    REVISION        "9802220000Z"
    DESCRIPTION
        "Add new objects for handling the following:
        [1] DNIS number matching.
        [2] Maximum connection to/from peer.
        [3] DID call treatment enable/disable.
        [4] Dial peer association to lower layer interfaces with
            CAS group.
        [5] Lost/Ealy/Late voice packets object in both call active
            and call history tables.
        [6] Update compliance statements."
    ::= { ciscoMgmt 63 }



CvCallVolumeWMIntvlType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Represents the Id of the watermark table.
        Here is what different values represent

        1 : Seconds Table: Here the entries are among last 60 second
        2 : Minutes Table: Here the entries are among last 60 minutes
        3 : Hours Table: Here the entries are among last 72 Hours
        4 : Uptime Table: Here the entries are from last reload/reboot"
    SYNTAX          INTEGER  {
                        secondStats(1),
                        minuteStats(2),
                        hourStats(3),
                        fromReloadStats(4)
                    }

CvCallVolumeStatsIntvlType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Represents the ids of the stats vlolume table
        Here is what each entry corresponds.
        1 : Seconds Table: Here each entry corresponds to a second
        2 : Minutes Table: Here each entry corresponds to a minute
        3 : Hours Table: Here each entry corresponds to an hour"
    SYNTAX          INTEGER  {
                        secondStats(1),
                        minuteStats(2),
                        hourStats(3)
                    }

-- Textual Conventions
--   

--   
-- Session Protocol textual convention

CvSessionProtocol ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Represents a Session Protocol used by Voice calls between a
        local and remote router via the IP backbone.

        other - none of the following.
        cisco - cisco proprietary H.323 Lite session protocol.
        sdp   - Session Description Protocol.
        sip   - Session Initiation Protocol.
        sccp  - Skinny Call Control Protocol."
    SYNTAX          INTEGER  {
                        other(1),
                        cisco(2),
                        sdp(3),
                        sip(4),
                        multicast(5),
                        sccp(6)
                    }

-- Channel Associated Signalling Group (CAS) group definition

CvCasGroup ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS          current
    DESCRIPTION
        "The Channel Associated Signalling Group of a
        T1/E1. The valid CAS group range is from 0 to 23 for T1
        and from 0 to 30 for E1. The value -1 indicates CAS group is
        disabled."
    SYNTAX          Integer32 (-1..30)

-- GSM AMR-NB Bit rate modes

CvAmrNbBitRateMode ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Represents GSM AMR-NB bit rate modes.

        CodecMode             Bit-rate (kbps)
        0                     4.75
        1                     5.15
        2                     5.90
        3                     6.70
        4                     7.40
        5                     7.95
        6                     10.2
        7                     12.2"
    SYNTAX          BITS {
                        amrBitRateMode0(0),
                        amrBitRateMode1(1),
                        amrBitRateMode2(2),
                        amrBitRateMode3(3),
                        amrBitRateMode4(4),
                        amrBitRateMode5(5),
                        amrBitRateMode6(6),
                        amrBitRateMode7(7)
                    }

-- GSM AMR-NB Encapsulation types

CvAmrNbRtpEncap ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Represents GSM AMR-NB codec RTP encapsulation type."
    SYNTAX          INTEGER  {
                        rfc3267(1)
                    }

-- iLBC codec modes

CvIlbcFrameMode ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "This Texatual Convention represents the iLBC codec
        frame modes.  
        The possible values are :
        frameMode20: This mode operates at 15.2 kbps and each 
                     frame is packetized in 38 bytes. 
        frameMode30: This mode operates at 13.33 kbps and each 
                     frame is packetized in 50 bytes."

    REFERENCE       "RFC 3952: Section 4.2 for details about iLBC mode"
    SYNTAX          INTEGER  {
                        frameMode20(20),
                        frameMode30(30)
                    }

-- call connection textual convention

CvCallConnectionType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Call connection represents the connection/association/session
        between two media call end points. Following are the different
        connection types.

        h323       - h323 protocol.
        sip        - Session Initiation Protocol.
        mgcp       - Media Gateway Control Protocol.
        sccp       - Cisco proprietary Skinny Call Control Protocol.
        multicast  - multicast call connections.
        cacontrol  - Call Agent Control.
        telephony  - telephony signal call connections."
    SYNTAX          INTEGER  {
                        h323(1),
                        sip(2),
                        mgcp(3),
                        sccp(4),
                        multicast(5),
                        cacontrol(6),
                        telephony(7)
                    }
-- Overview of Voice Gateway Call between Data and Telephony network
--   
-- |   Network    |  Voice  |   Voice    |   Telephony  |
-- <========+== Protocol ==+=Gateway=+=== Peer ===+= Interfaces =+==========>
-- Data    | :  I | U | R |         |            | DS0    T1/E1 | Telephony
-- Network |    P | D | T | voice   | Voice      | ISDN B       | Network
-- |      | P | P | session |encapsulation        FXO   |
-- |      |   |   | protocol|            |        E&M   |
-- |      |   |   |         |            |        FXS   |
-- |Voice      P|
-- <==========================+Packet<=*=>C+================>
-- Voice packet                |  M    PCM data
-- CODEC
--   
-- CODEC: Coder and Decoder.
-- PCM  : Pulse Code Modulation.
--   

-- Voice Gateway call leg connections are as follows:
--   
-- [1] Call establishment using a local telephony interface
-- Call establishment using a local telephony interface involves
-- the Voice Session Control module, the Voice/FAX Protocol module,
-- and the Telephony interface itself. Voice Session Control is
-- responsible for controlling the local telephony interfaces and
-- assigning a Voice/FAX encapsulation module, with its associated
-- CODEC (most CODECs execute in DSP code) to the call. The CODEC is
-- responsible for encode/decode, as well has the generation and
-- detection of signaling information, such as call progress tones,
-- DTMF or pulse dialing signals, etc. During an active call, the
-- selected CODEC is responsible for collecting the voice and fax
-- statistics for the call.
--   
-- [2] Call establishment using the session protocol over IP backbone
-- Call establishment using the session protocol over IP backbone
-- involves the Voice Session Control module, VoIP encapsulation
-- module, and RTP protocol module. Voice Session Control is
-- responsible for controlling the selected session protocol and
-- assigning a VoIP encapsulation module with its associated RTP
-- (real-time transport protocol) connection to a VoIP call. For an
-- active VoIP call, the RTP protocol is responsible for collecting
-- the voice packet statistics for the call.
--   
-- This MIB uses IETF Dial Control MIB (RFC2128) as a base and adds
-- features to support voice calls via a Voice-over-IP gateway. The
-- supports connection establishment of both telephony circuit-switched
-- call and the Voice-over-IP session.
-- The following architectural diagram illustrates this:
--   
-- +================================================================+
-- |                          Voice Gateway                         |
-- +===+ +=========+ +=======+ +=======+ +==========+ +=============+
-- | |         | |       | |       | |          | |
-- | |         | |       | |       | |          | |
-- | |         | |       | |       | |          | |
-- | |         | |       | |       | |          | |
-- | |         | |       | |       | |          | |
-- | |         | |       | |       | |          | |
-- | |         | |       | |       | |          | |<== appears
-- | |         | |       | |       | |          | |    active
-- +=+ +=+     +=+ +=+   +=+ +=+   +=+ +=+      +=+ +=+
-- |VOICE|     |VOICE|   |VOICE|   |VOICE|      |VoIP | VOICE
-- | for |     | for |   | for |   | for |      | for | ifEntry with
-- |Peer1|     |Peer2|   |Peer3|   |Peer4|      |Peer5| shadow
-- |     |     |     |   |     |   |     |      |     | Voice PeerEntry
-- +=====+     +=+ +=+   +=+ +=+   +=+ +=+      +=====+
-- | |       | |       | |             <== appears
-- +=======+   +==+ +==+ +==+ +==+    | |                 active when
-- |   B   |   |   B   | |  DS0  |    | |                 the VOICE
-- |channel|   |channel| |channel|    | |                 connects to
-- +==+ +==+   +==+ +==+ +==+ +==+    | |                 telephony
-- | |         | |       | |       | |                 interface
-- +===+ +=========+ +==+ +==+ +==+ +==+ +======+
-- |  ISDN BRI/PRI      | | T1/E1 | |E&M/FXO/FXS|
-- +====================+ +=======+ +===========+
--   

-- In this model, each voice peer must have an associated VOICE
-- encapsulation interface ifEntry. Grouping of peer entries is
-- supported through the dialCtlPeerCfgId of the IETF Dial Control MIB,
-- allowing the particular type of telephony interfaces for a call to
-- be selected at call establishment time.
--   
-- The VOICE encapsulation interface is normally used to convert
-- between PCM (pulse code modulation) voice produced and consumed by
-- the lower layer telephony interfaces and compressed packetized voice
-- exchanged between the CODEC and the voice session control module.
--   
-- Each VOICE encapsulation ifEntry contains the ifType 'voiceEncap'.
-- The VOICE interface for each voice peer correlates to lower-level
-- telephony interface via ifStackTable.
--   
-- Each VoIP encapsulation ifEntry contains the ifType 'voiceOverIp'
-- For each VoIP encapsulation associated peer, the
-- dialCtlPeerCfgIfType object of IETF Dial Control MIB must set to
-- 'other' and dialCtlPeerCfgLowerIf must set to '0'.
--   

--   
-- Support for generic peers
--   
-- Generic Voice related Peers can be supported by permitting the left
-- justified substring of a phone number in
-- dialCtlPeerCfgOriginateAddress and dialCtlPeerCfgAnswerAddress of
-- IETF Dial control MIB for specifying a group of voice encapsulation
-- and voice over IP peers.
-- The leading '+' in these two objects indicates the E.164 phone
-- number (e.g. +1408526, +1408, +1, +) substring.
--   
-- The dialCtlPeerCfgOriginateAddress of the peer contains a wild-card
-- number for the outgoing call. For example, the '+1408526' in
-- dialCtlPeerCfgOriginateAddress means that the peer is used for the
-- outgoing call to the phones with a number of the form 1-408-526-xxxx.
--   
-- The dialCtlPeerCfgAnswerAddress of the voice encapsulation peer
-- contains the same type of regular expression as the one that is
-- specified in dialCtlPeerCfgOriginateAddress for filtering incoming
-- call from telephony interface.
--   
-- The dialCtlPeerCfgAnswerAddress of the voice over IP peer contains
-- an IP address wild-card (with regular expression rule) for filtering
-- the incoming call from IP backbone.
--   
-- ifEntry for the 'voiceEncap' Encapsulation
--   
-- The ifEntry for the 'voiceEncap' encapsulation supports the
-- ifGeneralInformationGroup of Cisco IF-MIB.
--   
-- ifTable           Comments
-- ==============    ===========================================
-- ifIndex           Each voice encapsulation is represented by an
-- ifEntry.
-- ifDescr           Textual description
-- "Voice Encapsulation: ".
-- ifType            The IANA value of voiceEncap(103).
-- ifSpeed           contains a zero value.
-- ifPhysAddress     contains an octet string of zero length.
--   
-- ifAdminStatus     The desired administrative status of the
-- voice encapsulation.
-- ifOperStatus      The current operational status of the voice
-- encapsulation.
-- ifLastChange      Refer to RFC 1573.
-- ifLinkUpDownTrapEnable
-- Only support the read-only ifLinkUpDownTrapEnable.
-- It contains a 'disabled(2)' value.
-- ifConnectorPresent
-- Refer to RFC 1573.
-- ifHighSpeed       contains a zero value.
-- ifName            Refer to RFC 1573.
-- ifAlias           Refer to RFC 1573.
--   

--   
-- ifEntry for the 'voiceOverIp' encapsulation
--   
-- The ifEntry for the 'voiceOverIp' encapsulation supports the
-- ifGeneralInformationGroup of Cisco IF-MIB
--   
-- ifTable           Comments
-- ==============    ===========================================
-- ifIndex           Each voiceOverIp encapsulation is represented by
-- an ifEntry.
-- ifDescr           Textual description
-- "Voice Over IP Encapsulation: ".
-- ifType            The IANA value of voiceOverIp(104).
-- ifSpeed           contains a zero value.
-- ifPhysAddress     contains an octet string of zero length.
--   
-- ifAdminStatus     The desired administrative status of the
-- VoIP encapsulation.
-- ifOperStatus      The current operational status of the VoIP
-- encapsulation.
-- ifLastChange      Refer to RFC 1573.
-- ifLinkUpDownTrapEnable
-- Only support the read-only ifLinkUpDownTrapEnable.
-- It contains a 'disabled(2)' value.
-- ifConnectorPresent
-- Refer to RFC 1573.
-- ifHighSpeed       contains a zero value.
-- ifName            Refer to RFC 1573.
-- ifAlias           Refer to RFC 1573.
--   

--   
-- Voice Dial Control MIB objects definitions

cvdcMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoVoiceDialControlMIB 1 }

-- The Voice Dial Control MIB consists of the following group
-- [1] Voice Dial Control General Configuration group
-- (cvGeneralConfiguration)
-- [2] Voice Dial Control Peer Group (cvPeer)
-- [3] Voice Dial Control Gateway Call Active Group
-- (cvGatewayCallActive)
-- [4] Voice Dial Control Gateway Call History Group
-- (cvGatewayCallHistory)

cvGeneralConfiguration  OBJECT IDENTIFIER
    ::= { cvdcMIBObjects 1 }

cvPeer  OBJECT IDENTIFIER
    ::= { cvdcMIBObjects 2 }

cvGatewayCallActive  OBJECT IDENTIFIER
    ::= { cvdcMIBObjects 3 }

cvGatewayCallHistory  OBJECT IDENTIFIER
    ::= { cvdcMIBObjects 4 }


-- ********************************************************************
-- Voice Dial Control General Configuration Group
-- ********************************************************************
--   
-- Voice Dial Control general configuration data/parameters

cvGeneralPoorQoVNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates whether cvdcPoorQoVNotification (or
        the newer cvdcPoorQoVNotificationRev1) traps should be
        generated for a poor quality of voice calls.

        If the value of this object is 'true',
        cvdcPoorQoVNotification (or the newer
        cvdcPoorQoVNotificationRev1) traps will be generated
        for all voice over IP peers when a poor quality of voice
        call condition is detected after the voice gateway call
        disconnection.  If the value of this object is 'false',
        cvdcPoorQoVNotification (or the newer
        cvdcPoorQoVNotificationRev1) traps will be generated only
        for calls for which the
        cvVoIPPeerCfgPoorQoVNotificationEnable object of voice
        over IP peers having set to 'true'."
    DEFVAL          { false } 
    ::= { cvGeneralConfiguration 1 }

cvGeneralFallbackNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates whether cvdcFallbackNotification
        traps should be generated for fallback.
        If the value of this object is 'true',
        cvdcFallbackNotification traps will be generated
        for all voice over IP peers."
    DEFVAL          { false } 
    ::= { cvGeneralConfiguration 2 }

cvGeneralDSCPPolicyNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates whether cvdcPolicyViolationNotification
        traps should be generated for a RPH to DSCP mapping violation
        for SIP voice calls.

        If the value of this object is 'true',
        cvdcPolicyViolationNotification traps will be generated for SIP
        voice over IP peers when a RPH to DSCP violation condition is
        detected .  If the value of this object is 'false',
        cvdcPolicyViolationNotification traps will be generated only
        for calls for which the 
        cvVoIPPeerCfgDSCPPolicyNotificationEnable object of voice
        over IP peers having set to 'true'."
    DEFVAL          { false } 
    ::= { cvGeneralConfiguration 3 }

cvGeneralMediaPolicyNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates whether cvdcPolicyViolationNotification
        traps should be generated for Media violation
        for SIP voice calls.

        If the value of this object is 'true',
        cvdcPolicyViolationNotification traps will be generated for SIP
        voice over IP peers when media violation condition is
        detected .  If the value of this object is 'false',
        cvdcPolicyViolationNotification traps will be generated only
        for calls for which the 
        cvVoIPPeerCfgMediaPolicyNotificationEnable object of voice
        over IP peers having set to 'true'."
    DEFVAL          { false } 
    ::= { cvGeneralConfiguration 4 }
-- *********************************************************************
-- Voice Dial Control Peer Group
-- *********************************************************************
-- The objects in this group are responsible for the voice related peer
-- configuration which controls the dial string and the interface or
-- session target for the call establishment to a peer on the telephony
-- network or on the IP backbone.
--   

-- Voice Generic Peer Configuration Table

cvPeerCfgTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvPeerCfgEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The table contains the Voice Generic Peer information that
        is used to create an ifIndexed row with an appropriate
        ifType that is associated with the cvPeerCfgType and
        cvPeerCfgPeerType objects."
    ::= { cvPeer 1 }

cvPeerCfgEntry OBJECT-TYPE
    SYNTAX          CvPeerCfgEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A single voice generic Peer. The creation of this
        entry will create an associated ifEntry with an ifType
        that is associated with cvPeerCfgType, i.e., for
        'voiceEncap' encapsulation, an ifEntry will contain an
        ifType voiceEncap(103); for 'voiceOverIp' encapsulation,
        an ifEntry will contain an ifType voiceOverIp(104). The
        ifAdminStatus of the newly created ifEntry is set to 'up'
        and ifOperStatus is set to 'down'. In addition, an
        associated voiceEncap/voiceOverIp Peer configuration
        entry is created after the successful ifEntry creation.
        Then ifIndex of the newly created ifEntry must be used by
        the network manager to create a peer configuration entry
        of IETF Dial Control MIB (Refer to RFC 2128 section
        ******* and the description of dialCtlPeerCfgEntry for the
        detailed information).
        In summary, the voice dial peer creation steps are as
        follows:
        [1] create this entry (voice/data generic peer entry).
        [2] read the cvPeerCfgIfIndex of this entry for the
            ifIndex of newly created voice/data generic peer.
        [3] create the dialCtlPeerCfgEntry of RFC 2128 with the
            indices of dialCtlPeerCfgId and the ifIndex of newly
            created voice generic peer.

        For each VoIP peer, it uses IP address and UDP port with
        RTP protocol to transfer voice packet. Therefore, it does
        not have its lower layer interface. The
        dialCtlPeerCfgIfType object of IETF Dial Control MIB must
        set to 'other' and the dialCtlPeerCfgLowerIf must set to
        '0'.

        After the successful creation of peer configuration entry
        of IETF Dial Control MIB, the dial plan software in
        managed device will set the ifOperStatus of the newly
        created voiceEncap/voiceOverIp ifEntry to 'up' for
        enabling the peer function if the peer configuration is
        completed.
        When this entry is deleted, its associated ifEntry,
        voiceEncap/voiceOverIp specific peer entry and the peer
        entry of IETF Dial Control MIB are deleted."
    INDEX           { cvPeerCfgIndex } 
    ::= { cvPeerCfgTable 1 }

CvPeerCfgEntry ::= SEQUENCE {
        cvPeerCfgIndex     Integer32,
        cvPeerCfgIfIndex   InterfaceIndexOrZero,
        cvPeerCfgType      INTEGER,
        cvPeerCfgRowStatus RowStatus,
        cvPeerCfgPeerType  INTEGER
}

cvPeerCfgIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An arbitrary index that uniquely identifies a generic
        voice peer." 
    ::= { cvPeerCfgEntry 1 }

cvPeerCfgIfIndex OBJECT-TYPE
    SYNTAX          InterfaceIndexOrZero
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The ifIndex of the peer associated ifEntry. The ifIndex
        appears after the associated ifEntry is created
        successfully.
        This ifIndex will be used to access the objects in the
        Voice over Telephony or Voice over IP peer specific table.
        In addition, the ifIndex is also used to access the
        associated peer configuration entry of the IETF Dial
        Control MIB. If the peer associated ifEntry had not been
        created, then this object has a value of zero." 
    ::= { cvPeerCfgEntry 2 }

cvPeerCfgType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        voice(1),
                        voip(2),
                        mmail(3),
                        voatm(4),
                        vofr(5)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the type of voice related encapsulation.
        voice - voice encapsulation (voiceEncap ifType) on the
                telephony network.
        voip  - VoIP encapsulation (voiceOverIp ifType) on the IP
                network.
        mmail - Media Mail over IP encapsulation (mediaMailOverIp
                ifType) on the IP network.
        voatm - VoATM encapsulation (voiceOverATM ifType) on the
                ATM network.
        vofr  - VoFR encapsulation (voiceOverFR ifType) on the
                Frame Relay network."
    DEFVAL          { voice } 
    ::= { cvPeerCfgEntry 3 }

cvPeerCfgRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to create a new row or modify or
        delete an existing row in this table." 
    ::= { cvPeerCfgEntry 4 }

cvPeerCfgPeerType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        voice(1),
                        data(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the type of a peer.
        voice - peer in voice type to be defined in a voice
                gateway for voice calls. 
        data  - peer in data type to be defined in gateway
                that supports universal ports for modem/data
                calls and integrated ports for data calls."
    DEFVAL          { voice } 
    ::= { cvPeerCfgEntry 5 }
 

-- Voice over Telephony specific Peer Configuration Table

cvVoicePeerCfgTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvVoicePeerCfgEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The table contains the Voice over Telephony peer specific
        information that is required to accept voice calls or to
        which it will place them or perform various loopback tests
        via interface."
    ::= { cvPeer 2 }

cvVoicePeerCfgEntry OBJECT-TYPE
    SYNTAX          CvVoicePeerCfgEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A single Voice specific Peer. One entry per voice
        encapsulation.
        The entry is created when its associated 'voiceEncap(103)'
        encapsulation ifEntry is created.
        This entry is deleted when its associated ifEntry is
        deleted."
    INDEX           { ifIndex } 
    ::= { cvVoicePeerCfgTable 1 }

CvVoicePeerCfgEntry ::= SEQUENCE {
        cvVoicePeerCfgSessionTarget     DisplayString,
        cvVoicePeerCfgDialDigitsPrefix  DisplayString,
        cvVoicePeerCfgDIDCallEnable     TruthValue,
        cvVoicePeerCfgCasGroup          CvCasGroup,
        cvVoicePeerCfgRegisterE164      TruthValue,
        cvVoicePeerCfgForwardDigits     Integer32,
        cvVoicePeerCfgEchoCancellerTest INTEGER
}

cvVoicePeerCfgSessionTarget OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the session target of the peer.
        Session Targets definitions:
        The session target has the syntax used by the IETF service
        location protocol. The syntax is as follows:

        mapping-type:type-specific-syntax

        the mapping-type specifies a scheme for mapping the
        matching dial string to a session target.

        The valid Mapping type definitions for the peer are as
        follows:
        loopback - Syntax: loopback:where
           'where' string is defined as follows:
           compressed - loopback is performed on compressed voice
                        as close to the CODEC which processes the
                        data as possible.
           uncompressed - loopback is performed on the PCM or
                        analog voice as close to the telephony
                        endpoint as possible.

        Local loopback case:
        uncompressed - the PCM voice coming into the DSP is simply
            turned around and sent back out, allowing testing of
            the transmit--> receive paths in the telephony
            endpoint.
        compressed - the compressed voice coming out of the CODEC is
            turned around on the DSP module and fed back into the
            decompressor through the jitter buffer. In addition to
            the telephony endpoint, this tests both the encode and
            decode paths without involving data paths or packet
            handling on the host router.

        Remote loopback case:
        compressed - RTP packets received from the network are
            decapsulated and passed to the DSP board. Instead of
            feeding these into the CODEC for decompression, they
            are immediately sent back to the session application
            as if they had originated locally and been compressed.
        uncompressed - In addition to the above, the voice samples
            are sent all the way through the CODEC and then turned
            around instead of being sent to the telephony
            endpoint"
    DEFVAL          { "" } 
    ::= { cvVoicePeerCfgEntry 1 }

cvVoicePeerCfgDialDigitsPrefix OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..32))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the prefix of the dial digits for the
        peer. The dial digits prefix is sent to telephony
        interface before the real phone number when the system
        places the outgoing call to the voice encapsulation peer
        over telephony interface."
    DEFVAL          { "" } 
    ::= { cvVoicePeerCfgEntry 2 }

cvVoicePeerCfgDIDCallEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object enables/disables the DID call treatment for
        incoming DNIS digits.
        true  - treat the incoming DNIS digits as if the digits
                are received from DID trunk.
        false - Disable DID call treatment for incoming DNIS
                digits."
    DEFVAL          { false } 
    ::= { cvVoicePeerCfgEntry 3 }

cvVoicePeerCfgCasGroup OBJECT-TYPE
    SYNTAX          CvCasGroup
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the CAS group number of a CAS
        capable T1/E1  that is in the dialCtlPeerCfgLowerIf object
        of RFC2128.
        This object can be set to a valid CAS group number only if
        the dialCtlPeerCfgLowerIf contains a valid ifIndex for a
        CAS capable T1/E1. The object must set to -1 before the
        value of the  dialCtlPeerCfgLowerIf object of RFC2128 can
        be changed."
    DEFVAL          { -1 } 
    ::= { cvVoicePeerCfgEntry 4 }

cvVoicePeerCfgRegisterE164 OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies that the E.164 number specified in
        the dialCtlPeerCfgOriginateAddress field of the associated
        dialCtlPeerCfgTable entry be registered as an extension 
        phone number of this gateway for H323 gatekeeper and/or 
        SIP registrar."
    DEFVAL          { false } 
    ::= { cvVoicePeerCfgEntry 5 }

cvVoicePeerCfgForwardDigits OBJECT-TYPE
    SYNTAX          Integer32 (-3..32)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the number of dialed digits to
        forward to the remote destination in the setup message.
        The object can take the value:
            0-32 number of right justified digits to forward
            -1 default
            -2 forward extra digits i.e those over and above
               those needed to match to the destination pattern
            -3 forward all digits"
    DEFVAL          { 0 } 
    ::= { cvVoicePeerCfgEntry 6 }

cvVoicePeerCfgEchoCancellerTest OBJECT-TYPE
    SYNTAX          INTEGER  {
                        echoCancellerTestNone(1),
                        echoCancellerG168Test2A(2),
                        echoCancellerG168Test2B(3),
                        echoCancellerG168Test2Ca(4),
                        echoCancellerG168Test2Cb(5),
                        echoCancellerG168Test3A(6),
                        echoCancellerG168Test3B(7),
                        echoCancellerG168Test3C(8),
                        echoCancellerG168Test4(9),
                        echoCancellerG168Test6(10),
                        echoCancellerG168Test9(11),
                        echoCancellerG168Test5(12),
                        echoCancellerG168Test7(13)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies which, if any, test to run in the
        echo canceller when a call from the network is connected.
        echoCancellerTestNone    - do not run a test.
        echoCancellerG168Test2A  - run ITU-T G.168 Test 2A.
        echoCancellerG168Test2B  - run ITU-T G.168 Test 2B.
        echoCancellerG168Test2Ca - run ITU-T G.168 Test 2C(a).
        echoCancellerG168Test2Cb - run ITU-T G.168 Test 2C(b).
        echoCancellerG168Test3A  - run ITU-T G.168 Test 3A.
        echoCancellerG168Test3B  - run ITU-T G.168 Test 3B.
        echoCancellerG168Test3C  - run ITU-T G.168 Test 3C.
        echoCancellerG168Test4   - run ITU-T G.168 Test 4.
        echoCancellerG168Test5   - run ITU-T G.168 Test 5.
        echoCancellerG168Test6   - run ITU-T G.168 Test 6.
        echoCancellerG168Test7   - run ITU-T G.168 Test 7.
        echoCancellerG168Test9   - run ITU-T G.168 Test 9."
    REFERENCE
        "ITU-T G.168(04/2000): Section 6.4 Tests and requirements
             for performance with input signals applied to the send and
             receive paths."
    DEFVAL          { echoCancellerTestNone } 
    ::= { cvVoicePeerCfgEntry 7 }
 

-- Voice over IP specific Peer Configuration Table

cvVoIPPeerCfgTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvVoIPPeerCfgEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The table contains the Voice over IP (VoIP) peer specific
        information that is required to accept voice calls or to
        which it will place them via IP backbone with the
        specified session protocol in cvVoIPPeerCfgSessionProtocol."
    ::= { cvPeer 3 }

cvVoIPPeerCfgEntry OBJECT-TYPE
    SYNTAX          CvVoIPPeerCfgEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A single VoIP specific Peer. One entry per VoIP
        encapsulation.
        The entry is created when its associated 'voiceOverIp(104)'
        encapsulation ifEntry is created.
        This entry is deleted when its associated ifEntry is
        deleted."
    INDEX           { ifIndex } 
    ::= { cvVoIPPeerCfgTable 1 }

CvVoIPPeerCfgEntry ::= SEQUENCE {
        cvVoIPPeerCfgSessionProtocol               CvSessionProtocol,
        cvVoIPPeerCfgDesiredQoS                    QosService,
        cvVoIPPeerCfgMinAcceptableQoS              QosService,
        cvVoIPPeerCfgSessionTarget                 DisplayString,
        cvVoIPPeerCfgCoderRate                     CvcSpeechCoderRate,
        cvVoIPPeerCfgFaxRate                       CvcFaxTransmitRate,
        cvVoIPPeerCfgVADEnable                     TruthValue,
        cvVoIPPeerCfgExpectFactor                  Integer32,
        cvVoIPPeerCfgIcpif                         Integer32,
        cvVoIPPeerCfgPoorQoVNotificationEnable     TruthValue,
        cvVoIPPeerCfgUDPChecksumEnable             TruthValue,
        cvVoIPPeerCfgIPPrecedence                  Integer32,
        cvVoIPPeerCfgTechPrefix                    DisplayString,
        cvVoIPPeerCfgDigitRelay                    BITS,
        cvVoIPPeerCfgCoderBytes                    Integer32,
        cvVoIPPeerCfgFaxBytes                      Integer32,
        cvVoIPPeerCfgInBandSignaling               CvcInBandSignaling,
        cvVoIPPeerCfgMediaSetting                  INTEGER,
        cvVoIPPeerCfgDesiredQoSVideo               QosService,
        cvVoIPPeerCfgMinAcceptableQoSVideo         QosService,
        cvVoIPPeerCfgRedirectip2ip                 TruthValue,
        cvVoIPPeerCfgOctetAligned                  TruthValue,
        cvVoIPPeerCfgBitRates                      CvAmrNbBitRateMode,
        cvVoIPPeerCfgCRC                           TruthValue,
        cvVoIPPeerCfgCoderMode                     CvIlbcFrameMode,
        cvVoIPPeerCfgCodingMode                    INTEGER,
        cvVoIPPeerCfgBitRate                       Unsigned32,
        cvVoIPPeerCfgFrameSize                     INTEGER,
        cvVoIPPeerCfgDSCPPolicyNotificationEnable  TruthValue,
        cvVoIPPeerCfgMediaPolicyNotificationEnable TruthValue
}

cvVoIPPeerCfgSessionProtocol OBJECT-TYPE
    SYNTAX          CvSessionProtocol
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the session protocol to be used
        for Internet call between local and remote router via
        IP backbone."
    DEFVAL          { cisco } 
    ::= { cvVoIPPeerCfgEntry 1 }

cvVoIPPeerCfgDesiredQoS OBJECT-TYPE
    SYNTAX          QosService
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the user requested Quality of Service
        for the call."
    DEFVAL          { bestEffort } 
    ::= { cvVoIPPeerCfgEntry 2 }

cvVoIPPeerCfgMinAcceptableQoS OBJECT-TYPE
    SYNTAX          QosService
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the minimally acceptable Quality of
        Service for the call."
    DEFVAL          { bestEffort } 
    ::= { cvVoIPPeerCfgEntry 3 }

cvVoIPPeerCfgSessionTarget OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the session target of the peer.
        Session Targets definitions:
        The session target has the syntax used by the IETF service
        location protocol. The syntax is as follows:

        mapping-type:type-specific-syntax

        the mapping-type specifies a scheme for mapping the
        matching dial string to a session target. The
        type-specific-syntax is exactly that, something that the
        particular mapping scheme can understand.
        For example,
        Session target           Meaning
        ipv4:************:1006   The session target is the IP
                                 version 4 address of ************
                                 and port 1006.
        dns:pots.cisco.com:1661  The session target is the IP host
                                 with dns name pots.cisco.com, and
                                 port 1661.
        ras                      The session target is the
                                 gatekeeper with RAS (Registration
                                 , Admission,  Status protocol).
        settlement               The session target is the
                                 settlement server.
        enum:1                   The session target is the enum 
                                 profile match table 1.

        The valid Mapping type definitions for the peer are as
        follows:
        ipv4       - Syntax: ipv4:w.x.y.z:port or  ipv4:w.x.y.z
        dns        - Syntax: dns:host.domain:port or
                             dns:host.domain
        ras        - Syntax: ras
        settlement - Syntax: settlement
        enum       - Syntax: enum:

        loopback - Syntax: loopback:where
           'where' string is defined as follows:
           rtp - loopback is performed at the transport protocol
                 level.

        Local loopback case:
        rtp - the session application sets up an RTP stream to
            itself (i.e. actually allocates a port pair and opens
            the appropriate UDP sockets). It then does the full
            RTP encapsulation, sends the packets to the loopback
            IP address, receives the RTP packets, and hands the
            compressed voice back to the CODEC. This tests the
            entire local processing path, both transmit and
            receive, in the router, as well as all of the above
            paths.

        Remote loopback case:
        rtp: RTP packets received from the network are decapsulated and
             immediately re-encapsulated in the outbound RTP
             stream, using the same media clock (i.e. timestamp)
             as the received packet. They are then sent back to
             the remote source router as if the voice had
             originated on a telephony port on the local router."
    DEFVAL          { "" } 
    ::= { cvVoIPPeerCfgEntry 4 }

cvVoIPPeerCfgCoderRate OBJECT-TYPE
    SYNTAX          CvcSpeechCoderRate
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the most desirable codec of speech
        for the VoIP peer."
    DEFVAL          { g729IETFr8000 } 
    ::= { cvVoIPPeerCfgEntry 5 }

cvVoIPPeerCfgFaxRate OBJECT-TYPE
    SYNTAX          CvcFaxTransmitRate
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the default transmit rate of FAX
        the VoIP peer. If the value of this object is 'none',
        then the Fax relay feature is disabled; otherwise the Fax
        relay feature is enabled."
    DEFVAL          { voiceRate } 
    ::= { cvVoIPPeerCfgEntry 6 }

cvVoIPPeerCfgVADEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether or not the VAD (Voice
        Activity Detection) voice data is continuously transmitted
        to IP backbone."
    DEFVAL          { true } 
    ::= { cvVoIPPeerCfgEntry 7 }

cvVoIPPeerCfgExpectFactor OBJECT-TYPE
    SYNTAX          Integer32 (0..20)
    UNITS           "equipment impairment factor (eif)"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the user requested Expectation
        Factor of voice quality for the call via this peer."
    REFERENCE
        "ITU G.113: Sections 8 Considerations Concerning the
             Expectation Factor A."
    DEFVAL          { 0 } 
    ::= { cvVoIPPeerCfgEntry 8 }

cvVoIPPeerCfgIcpif OBJECT-TYPE
    SYNTAX          Integer32 (0..55)
    UNITS           "equipment impairment factor (eif)"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the user requested Calculated
        Planning Impairment Factor (Icpif) for the call via this
        peer."
    REFERENCE
        "ITU G.113: Sections 9 Considerations associated with the
             Calculated Planning Impairment Factor (Icpif)"
    DEFVAL          { 20 } 
    ::= { cvVoIPPeerCfgEntry 9 }

cvVoIPPeerCfgPoorQoVNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether cvdcPoorQoVNotification (or
        the newer cvdcPoorQoVNotificationRev1) traps should be
        generated for the call that is associated with this
        peer."
    DEFVAL          { false } 
    ::= { cvVoIPPeerCfgEntry 10 }

cvVoIPPeerCfgUDPChecksumEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether the outgoing voice related
        UDP packet contains a valid checksum value.
        true  - enable the checksum of outgoing voice UDP packets
        false - disable the checksum of outgoing voice UDP packets"
    DEFVAL          { false } 
    ::= { cvVoIPPeerCfgEntry 11 }

cvVoIPPeerCfgIPPrecedence OBJECT-TYPE
    SYNTAX          Integer32 (0..7)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the value to be stored in the IP
        Precedence field of voice packets, with values ranging
        from 0 (normal precedence) through 7 (network control),
        allowing the managed system to set the importance of each
        voice packet for delivering them to the destination peer."
    REFERENCE
        "RFC 791: Section 3.1.  Internet Header Format; Type of
             Service."
    DEFVAL          { 0 } 
    ::= { cvVoIPPeerCfgEntry 12 }

cvVoIPPeerCfgTechPrefix OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..128))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the technology prefix of the peer,
        The technology prefix and the called party address
        are passed in Admission Request (ARQ) to gatekeeper
        for the called party address resolution during call setup."
    REFERENCE
        "ITU-T H.225.0: Section 7.6 Common Message Elements."
    DEFVAL          { "" } 
    ::= { cvVoIPPeerCfgEntry 13 }

cvVoIPPeerCfgDigitRelay OBJECT-TYPE
    SYNTAX          BITS {
                        rtpCisco(0),
                        h245Signal(1),
                        h245Alphanumeric(2),
                        rtpNte(3),
                        sipNotify(4),
                        sipKpml(5)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the methods to transmit dial digits
        (DTMF or MF digits) via IP network.
        rtpCisco       - Enable capability to transmit dial digits
                         with Cisco proprietary RTP payload type.
        h245Signal     - Enable capability to transmit dtmf digits
                         across the H.245 channel, via the signal
                         field of the UserInputIndication message
        h245Alphanumeric - Enable capability to transmit dtmf
                         digit across the H.245 channel, via the
                         string or alphanumeric fields of the
                         UserInputIndication message
        rtpNte         - Enable capability to transmit dial digits
                         using Named Telephony Event per RFC 2833
                         section 3.
        sipNotify      - Enable capability to transmit dtmf
                         digits using unsolicited SIP NOTIFY
                         messages. This mechanism is only available
                         for SIP dialpeers.
        sipKpml        - Enable capability to transmit dtmf
                         digits using KPML over SIP SUBSCRIBE
                         and NOTIFY messages. This mechanism is
                         only available for SIP dialpeers.


        Modifying the value of cvVoIPPeerCfgSessionProtocol
        can reset the digit-relay method associated bits value in
        this object if the modified session protocol does not
        support  these digit-relay methods."
    REFERENCE
        "[1] ITU H.245: Section 6.
             [2] RFC 2833 : Section 3."
    DEFVAL          { {  } } 
    ::= { cvVoIPPeerCfgEntry 14 }

cvVoIPPeerCfgCoderBytes OBJECT-TYPE
    SYNTAX          Integer32 (0 | 10..240)
    UNITS           "bytes"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the size of the voice payload sample
        to be produced by the coder specified in
        cvVoIPPeerCfgCoderRate.
        Each coder sample produces 10 bytes of voice payload. The
        specified value will be rounded down to the nearest valid
        size.

        A value of 0, specifies that the coder defined by
        cvVoIPPeerCfgCoderRate should produce its default payload
        size."
    DEFVAL          { 0 } 
    ::= { cvVoIPPeerCfgEntry 15 }

cvVoIPPeerCfgFaxBytes OBJECT-TYPE
    SYNTAX          Integer32 (0 | 10..255)
    UNITS           "bytes"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the payload size to be produced by
        the coder when it is generating fax data. A value of 0,
        specifies that the coder specified in
        cvVoIPCfgPeerCoderRate should produce its default fax
        payload size."
    DEFVAL          { 0 } 
    ::= { cvVoIPPeerCfgEntry 16 }

cvVoIPPeerCfgInBandSignaling OBJECT-TYPE
    SYNTAX          CvcInBandSignaling
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the type of in-band signaling that
        will be used between the end points of the call. It is
        used by the router to determine how to interpret ABCD
        signaling bits sent as part of voice payload data."
    DEFVAL          { cas } 
    ::= { cvVoIPPeerCfgEntry 17 }


cvVoIPPeerCfgMediaSetting OBJECT-TYPE
    SYNTAX          INTEGER  {
                        flowThrough(1),
                        flowAround(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies how the media is to be setup on
        an IP-IP Gateway. Two choices are valid: flow-through
        and flow-around. When in flow-through mode, which is the
        default setting, the IP-IP Gateway will terminate and 
        then re-originate the media stream. When flow-around
        is configured the Gateway will not be involved with the
        media, since it will flow-around the Gateway and will
        be established directly between the endpoints."
    DEFVAL          { flowThrough } 
    ::= { cvVoIPPeerCfgEntry 23 }

cvVoIPPeerCfgDesiredQoSVideo OBJECT-TYPE
    SYNTAX          QosService
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the user requested Quality of Service
        for the video portion of the call."
    DEFVAL          { bestEffort } 
    ::= { cvVoIPPeerCfgEntry 24 }

cvVoIPPeerCfgMinAcceptableQoSVideo OBJECT-TYPE
    SYNTAX          QosService
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the minimally acceptable Quality of
        Service for the video portion of the call."
    DEFVAL          { bestEffort } 
    ::= { cvVoIPPeerCfgEntry 25 }

cvVoIPPeerCfgRedirectip2ip OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the Inbound VoIP calls that match
        an outbound VoIP dialpeer will be responded with a SIP 
        redirect(for inbound SIP) or H.450.3 call-forward(for 
        inbound H.323)."
    DEFVAL          { false } 
    ::= { cvVoIPPeerCfgEntry 26 }

cvVoIPPeerCfgOctetAligned OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "If the object has a value true(1) octet align operation
        is used, and if the value is false(2), bandwidth efficient
        operation is used. This object is not instantiated when
        the object cvVoIPPeerCfgCoderRate is not equal to
        gsmAmrNb enum."
    DEFVAL          { true } 
    ::= { cvVoIPPeerCfgEntry 27 }

cvVoIPPeerCfgBitRates OBJECT-TYPE
    SYNTAX          CvAmrNbBitRateMode
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates modes of Bit rates. One or more
        upto four modes can be configured at the same time as
        bit rates can be changed dynamically for AMR-NB codec.
        This object is not instantiated when the object
        cvVoIPPeerCfgCoderRate is not equal to gsmAmrNb enum."
    DEFVAL          { {  } } 
    ::= { cvVoIPPeerCfgEntry 28 }

cvVoIPPeerCfgCRC OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "If the object has a value of true(1), frame CRC will be
        included in the payload and if the value is false(2),
        frame CRC will not be included in the payload.
        This object is applicable only when RTP frame type
        is octet aligned. This object is not instantiated when
        the object cvVoIPPeerCfgCoderRate is not equal to
        gsmAmrNb enum."
    DEFVAL          { false } 
    ::= { cvVoIPPeerCfgEntry 29 }

cvVoIPPeerCfgCoderMode OBJECT-TYPE
    SYNTAX          CvIlbcFrameMode
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates the iLBC codec mode to be used.
        The value of this object is valid only if 
        cvVoIPPeerCfgCoderRate is equal to 'iLBC'."
    REFERENCE
        "RFC 3952: Section 4.2 -  MIME Registration of iLBC"
    DEFVAL          { frameMode20 } 
    ::= { cvVoIPPeerCfgEntry 30 }

cvVoIPPeerCfgCodingMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        adaptive(1),
                        independent(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the coding mode to be used. The object is
        instantiated only if cvVoIPPeerCfgCoderRate is 'iSAC'. Following
        coding modes are supported:
        adaptive    (1) - adaptive mode where iSAC performs bandwidth  
                          estimation and adapts to the available channel

                          bandwidth.
        independent (2) - independent mode in which no bandwidth
        estimation 
                          is performed."
    DEFVAL          { independent } 
    ::= { cvVoIPPeerCfgEntry 31 }

cvVoIPPeerCfgBitRate OBJECT-TYPE
    SYNTAX          Unsigned32 (10000..32000)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the target bit rate. The object is
        instantiated only if cvVoIPPeerCfgCoderRate is 'iSAC'."
    DEFVAL          { 32000 } 
    ::= { cvVoIPPeerCfgEntry 32 }

cvVoIPPeerCfgFrameSize OBJECT-TYPE
    SYNTAX          INTEGER  {
                        frameSize30(1),
                        frameSize60(2),
                        frameSize30fixed(3),
                        frameSize60fixed(4)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the frame size used. The object is
        instantiated only if cvVoIPPeerCfgCoderRate is 'iSAC'.
        The frame size can be 30 ms or 60 ms, and it can be fixed for
        all packets or vary depending on the configuration and bandwidth
        estimation. Thus it can have the following values:
        frameSize30      - initial frame size of 30 ms
        frameSize60      - initial frame size of 60 ms
        frameSize30fixed - fixed frame size 30 ms
        frameSize60fixed - fixed frame size 60 ms"
    DEFVAL          { frameSize30 } 
    ::= { cvVoIPPeerCfgEntry 33 }

cvVoIPPeerCfgDSCPPolicyNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether cvdcPolicyViolationNotification
        traps should be generated for the call that is associated with
        this peer for RPH to DSCP mapping and policing feature."
    DEFVAL          { false } 
    ::= { cvVoIPPeerCfgEntry 34 }

cvVoIPPeerCfgMediaPolicyNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether cvdcPolicyViolationNotification
        traps should be generated for the call that is associated with
        this peer for Media policing feature.."
    DEFVAL          { false } 
    ::= { cvVoIPPeerCfgEntry 35 }
 

-- Voice specific common Peer Configuration Table

cvPeerCommonCfgTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvPeerCommonCfgEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The table contains the Voice specific peer common
        configuration information that is required to accept voice
        calls or to which it will place them or process the
        incoming calls."
    ::= { cvPeer 4 }

cvPeerCommonCfgEntry OBJECT-TYPE
    SYNTAX          CvPeerCommonCfgEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A single Voice specific Peer. One entry per voice related
        encapsulation.
        The entry is created when a voice related encapsulation
        ifEntry is created.
        This entry is deleted when its associated ifEntry is
        deleted."
    INDEX           { ifIndex } 
    ::= { cvPeerCommonCfgTable 1 }

CvPeerCommonCfgEntry ::= SEQUENCE {
        cvPeerCommonCfgIncomingDnisDigits  DisplayString,
        cvPeerCommonCfgMaxConnections      Integer32,
        cvPeerCommonCfgApplicationName     DisplayString,
        cvPeerCommonCfgPreference          Integer32,
        cvPeerCommonCfgHuntStop            TruthValue,
        cvPeerCommonCfgDnisMappingName     DnisMapname,
        cvPeerCommonCfgSourceCarrierId     SnmpAdminString,
        cvPeerCommonCfgTargetCarrierId     SnmpAdminString,
        cvPeerCommonCfgSourceTrunkGrpLabel SnmpAdminString,
        cvPeerCommonCfgTargetTrunkGrpLabel SnmpAdminString
}

cvPeerCommonCfgIncomingDnisDigits OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..32))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the prefix of the incoming Dialed
        Number Identification Service (DNIS) digits for the peer.
        The DNIS digits prefix is used to match with the incoming
        DNIS number for incoming call discrimination. If the
        digits in this object are matched with incoming DNIS
        number, the  associated dialCtlPeerCfgInfoType in RFC 2128
        will be used as a call discriminator for differentiating
        speech, data, fax, video or modem calls."
    DEFVAL          { "" } 
    ::= { cvPeerCommonCfgEntry 1 }

cvPeerCommonCfgMaxConnections OBJECT-TYPE
    SYNTAX          Integer32 (-1 | 1..2147483647)
    UNITS           "connections"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the maximum allowed connection
        to/from the peer. A value of -1 disables the limit of
        maximum connections."
    DEFVAL          { -1 } 
    ::= { cvPeerCommonCfgEntry 2 }

cvPeerCommonCfgApplicationName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the application to handle the incoming
        call after the peer is selected.
        If no application name is specified, then the default
        session application will take care of the incoming call."
    DEFVAL          { "" } 
    ::= { cvPeerCommonCfgEntry 3 }

cvPeerCommonCfgPreference OBJECT-TYPE
    SYNTAX          Integer32 (0..10)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the selection preference of a peer
        when multiple peers are matched to the selection criteria.
        The value of 0 has the lowest preference for peer
        selection."
    DEFVAL          { 0 } 
    ::= { cvPeerCommonCfgEntry 4 }

cvPeerCommonCfgHuntStop OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether dialpeer hunting should stop
        when this peer is reached."
    DEFVAL          { false } 
    ::= { cvPeerCommonCfgEntry 5 }

cvPeerCommonCfgDnisMappingName OBJECT-TYPE
    SYNTAX          DnisMapname
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies a Dialer Number Identification
        Service (DNIS) map name for the Voice specific peer
        entry specified in this row. A DNIS is a called party
        number and they can be grouped and identified by DNIS
        map."
    DEFVAL          { "" } 
    ::= { cvPeerCommonCfgEntry 6 }

cvPeerCommonCfgSourceCarrierId OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the Source Carrier Id for the peer.
        The Source Carrier Id is used to match with the Source
        Carrier Id of a call. If the Source Carrier Id in this
        object is matched with the Source Carrier Id of a call,
        then the associated peer will be used to handle the call. 
        Only alphanumeric characters, '-' and '_' are allowed in
        the string."
    DEFVAL          { "" } 
    ::= { cvPeerCommonCfgEntry 7 }

cvPeerCommonCfgTargetCarrierId OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the Target Carrier Id for the peer.
        The Target Carrier Id is used to match with the Target
        Carrier Id of a call. If the Target Carrier Id in this
        object is matched with the Target Carrier Id of a call,
        then the associated peer will be used to handle the call.
        Only alphanumeric characters, '-' and '_' are allowed in
        the string."
    DEFVAL          { "" } 
    ::= { cvPeerCommonCfgEntry 8 }

cvPeerCommonCfgSourceTrunkGrpLabel OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the Source Trunk Group Label for the
        peer. The Source Trunk Group Label is used to match with
        the Source Trunk Group Label of a call. If the Source
        Trunk Group Label in this object is matched with the
        Source Trunk Group Label of a call, then the associated
        peer will be used to handle the call. 
        Only alphanumeric characters, '-' and '_' are allowed in
        the string."
    DEFVAL          { "" } 
    ::= { cvPeerCommonCfgEntry 9 }

cvPeerCommonCfgTargetTrunkGrpLabel OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The object specifies the Target Trunk Group Label for the
        peer. The Target Trunk Group Label is used to match with
        the Target Trunk Group Label of a call. If the Target
        Trunk Group Label in this object is matched with the
        Target Trunk Group Label of a call, then the associated
        peer will be used to handle the call.
        Only alphanumeric characters, '-' and '_' are allowed in
        the string."
    DEFVAL          { "" } 
    ::= { cvPeerCommonCfgEntry 10 }
 

--   
-- *********************************************************************
-- Voice Dial Control Gateway Call Active Group
-- *********************************************************************
--   
-- For an end-to-end Gateway call, it normally has two or more call leg
-- entries appear in the call active table after a successful end-to-end
-- Gateway call establishment. The unique global call identifier in
-- each call leg is used to identify a specific end-to-end call.
-- If the call leg entries in call active table contain the same
-- global call identifier, they belong to the same gateway call.
--   

--   
-- Voice over Telephony Call Active Table

cvCallActiveTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallActiveEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table is the voice extension to the call active table
        of IETF Dial Control MIB. It contains voice encapsulation
        call leg information that is derived from the statistics
        of lower layer telephony interface."
    ::= { cvGatewayCallActive 1 }

cvCallActiveEntry OBJECT-TYPE
    SYNTAX          CvCallActiveEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The information regarding a single voice encapsulation
        call leg.
        The call leg entry is identified by using the same index
        objects that are used by Call Active table of IETF Dial
        Control MIB to identify the call.
        An entry of this table is created when its associated call
        active entry in the IETF Dial Control MIB is created and
        call active entry contains the call establishment to a
        voice over telephony network peer.
        The entry is deleted when its associated call active entry
        in the IETF Dial Control MIB is deleted."
    INDEX           {
                        callActiveSetupTime,
                        callActiveIndex
                    } 
    ::= { cvCallActiveTable 1 }

CvCallActiveEntry ::= SEQUENCE {
        cvCallActiveConnectionId          CvcGUid,
        cvCallActiveTxDuration            AbsoluteCounter32,
        cvCallActiveVoiceTxDuration       AbsoluteCounter32,
        cvCallActiveFaxTxDuration         AbsoluteCounter32,
        cvCallActiveCoderTypeRate         CvcCoderTypeRate,
        cvCallActiveNoiseLevel            Integer32,
        cvCallActiveACOMLevel             Integer32,
        cvCallActiveOutSignalLevel        Integer32,
        cvCallActiveInSignalLevel         Integer32,
        cvCallActiveERLLevel              Integer32,
        cvCallActiveSessionTarget         DisplayString,
        cvCallActiveImgPageCount          AbsoluteCounter32,
        cvCallActiveCallingName           SnmpAdminString,
        cvCallActiveCallerIDBlock         TruthValue,
        cvCallActiveEcanReflectorLocation Integer32,
        cvCallActiveAccountCode           SnmpAdminString,
        cvCallActiveERLLevelRev1          Integer32,
        cvCallActiveCallId                Unsigned32
}

cvCallActiveConnectionId OBJECT-TYPE
    SYNTAX          CvcGUid
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The global connection identifier for the
        active telephony leg of the call." 
    ::= { cvCallActiveEntry 1 }

cvCallActiveTxDuration OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of Transmit path open from this peer to the
        voice gateway for the call leg. This counter object will
        lock at the maximum value which is approximately two
        days." 
    ::= { cvCallActiveEntry 2 }

cvCallActiveVoiceTxDuration OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice transmitted from this peer to voice
        gateway for this call leg. The Voice Utilization Rate can
        be obtained by dividing this by cvCallActiveTXDuration
        object. This counter object will lock at the maximum
        value which is approximately two days." 
    ::= { cvCallActiveEntry 3 }

cvCallActiveFaxTxDuration OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of fax transmitted from this peer to voice gateway
        for this call leg. The FAX Utilization Rate can be
        obtained by dividing this by cvCallActiveTXDuration
        object. This counter object will lock at the maximum
        value which is approximately two days." 
    ::= { cvCallActiveEntry 4 }

cvCallActiveCoderTypeRate OBJECT-TYPE
    SYNTAX          CvcCoderTypeRate
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated coder rate. It specifies the transmit rate of
        voice/fax compression to its associated call leg for the
        call." 
    ::= { cvCallActiveEntry 5 }

cvCallActiveNoiseLevel OBJECT-TYPE
    SYNTAX          Integer32 (-128..8)
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object contains the active noise level for the call
        leg." 
    ::= { cvCallActiveEntry 6 }

cvCallActiveACOMLevel OBJECT-TYPE
    SYNTAX          Integer32 (-1..127)
    UNITS           "dB"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object contains the sum of Echo Return Loss (ERL),
        cancellation loss (Echo Return Loss Enhancement) and
        nonlinear processing loss for the call leg.
        The value -1 indicates the level can not be determined or
        level detection is disabled." 
    ::= { cvCallActiveEntry 7 }

cvCallActiveOutSignalLevel OBJECT-TYPE
    SYNTAX          Integer32 (-128..8)
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object contains the active output signal level to
        telephony interface that is used by the call leg." 
    ::= { cvCallActiveEntry 8 }

cvCallActiveInSignalLevel OBJECT-TYPE
    SYNTAX          Integer32 (-128..8)
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object contains the active input signal level from
        telephony interface that is used by the call leg." 
    ::= { cvCallActiveEntry 9 }

cvCallActiveERLLevel OBJECT-TYPE
    SYNTAX          Integer32 (-1..45)
    UNITS           "dB"
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The object contains the current Echo Return Loss (ERL)
        level for the call leg.
        The value -1 indicates the level can not be determined or
        level detection is disabled." 
    ::= { cvCallActiveEntry 10 }

cvCallActiveSessionTarget OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object specifies the session target of the peer that
        is used for the call leg. This object is set with the
        information in the call associated
        cvVoicePeerCfgSessionTarget object when the call is
        connected." 
    ::= { cvCallActiveEntry 11 }

cvCallActiveImgPageCount OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "pages"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of FAX related image pages are received or
        transmitted via the peer for the call leg." 
    ::= { cvCallActiveEntry 12 }

cvCallActiveCallingName OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The calling party name of the call. If the name is
        not available, then it will have a length of zero." 
    ::= { cvCallActiveEntry 13 }

cvCallActiveCallerIDBlock OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates whether or not the caller ID feature
        is blocked for this call." 
    ::= { cvCallActiveEntry 14 }

cvCallActiveEcanReflectorLocation OBJECT-TYPE
    SYNTAX          Integer32 (0..128)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The location in milliseconds of the largest amplitude
        reflector detected by the echo canceller for this call. 
        The value 0 indicates there is no reflector or the 
        information is not available." 
    ::= { cvCallActiveEntry 15 }

cvCallActiveAccountCode OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..50))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the account code input to the call.
        It could be used for call screen or by down stream server
        for billing purpose.
        The value of empty string indicates no account code input."
    DEFVAL          { "" } 
    ::= { cvCallActiveEntry 16 }

cvCallActiveERLLevelRev1 OBJECT-TYPE
    SYNTAX          Integer32 (-1..200)
    UNITS           "dB"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object contains the current Echo Return Loss (ERL)
        level for the call leg.
        The value -1 indicates the level can not be determined or
        level detection is disabled." 
    ::= { cvCallActiveEntry 17 }

cvCallActiveCallId OBJECT-TYPE
    SYNTAX          Unsigned32 (1..**********)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the call identifier
        for the active telephony leg of the call." 
    ::= { cvCallActiveEntry 18 }
 

-- Voice over IP Call Active Table

cvVoIPCallActiveTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvVoIPCallActiveEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table is the VoIP extension to the call active table of
        IETF Dial Control MIB. It contains VoIP call leg
        information about specific VoIP call destination and the
        selected QoS for the call leg."
    ::= { cvGatewayCallActive 2 }

cvVoIPCallActiveEntry OBJECT-TYPE
    SYNTAX          CvVoIPCallActiveEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The information regarding a single VoIP call leg.
        The call leg entry is identified by using the same index
        objects that are used by Call Active table of IETF Dial
        Control MIB to identify the call.
        An entry of this table is created when its associated call
        active entry in the IETF Dial Control MIB is created and
        the call active entry contains information for the call
        establishment to the peer on the IP backbone via a voice
        over  IP peer.
        The entry is deleted when its associated call active entry
        in the IETF Dial Control MIB is deleted."
    INDEX           {
                        callActiveSetupTime,
                        callActiveIndex
                    } 
    ::= { cvVoIPCallActiveTable 1 }

CvVoIPCallActiveEntry ::= SEQUENCE {
        cvVoIPCallActiveConnectionId                 CvcGUid,
        cvVoIPCallActiveRemoteIPAddress              IpAddress,
        cvVoIPCallActiveRemoteUDPPort                Integer32,
        cvVoIPCallActiveRoundTripDelay               Gauge32,
        cvVoIPCallActiveSelectedQoS                  QosService,
        cvVoIPCallActiveSessionProtocol              CvSessionProtocol,
        cvVoIPCallActiveSessionTarget                DisplayString,
        cvVoIPCallActiveOnTimeRvPlayout              AbsoluteCounter32,
        cvVoIPCallActiveGapFillWithSilence           AbsoluteCounter32,
        cvVoIPCallActiveGapFillWithPrediction        AbsoluteCounter32,
        cvVoIPCallActiveGapFillWithInterpolation     AbsoluteCounter32,
        cvVoIPCallActiveGapFillWithRedundancy        AbsoluteCounter32,
        cvVoIPCallActiveHiWaterPlayoutDelay          AbsoluteCounter32,
        cvVoIPCallActiveLoWaterPlayoutDelay          Gauge32,
        cvVoIPCallActiveReceiveDelay                 Gauge32,
        cvVoIPCallActiveVADEnable                    TruthValue,
        cvVoIPCallActiveCoderTypeRate                CvcCoderTypeRate,
        cvVoIPCallActiveLostPackets                  AbsoluteCounter32,
        cvVoIPCallActiveEarlyPackets                 AbsoluteCounter32,
        cvVoIPCallActiveLatePackets                  AbsoluteCounter32,
        cvVoIPCallActiveUsername                     SnmpAdminString,
        cvVoIPCallActiveProtocolCallId               OCTET STRING,
        cvVoIPCallActiveRemSigIPAddrT                InetAddressType,
        cvVoIPCallActiveRemSigIPAddr                 InetAddress,
        cvVoIPCallActiveRemSigPort                   Integer32,
        cvVoIPCallActiveRemMediaIPAddrT              InetAddressType,
        cvVoIPCallActiveRemMediaIPAddr               InetAddress,
        cvVoIPCallActiveRemMediaPort                 Integer32,
        cvVoIPCallActiveSRTPEnable                   TruthValue,
        cvVoIPCallActiveOctetAligned                 TruthValue,
        cvVoIPCallActiveBitRates                     CvAmrNbBitRateMode,
        cvVoIPCallActiveModeChgPeriod                Integer32,
        cvVoIPCallActiveModeChgNeighbor              TruthValue,
        cvVoIPCallActiveMaxPtime                     Integer32,
        cvVoIPCallActiveCRC                          TruthValue,
        cvVoIPCallActiveRobustSorting                TruthValue,
        cvVoIPCallActiveEncap                        CvAmrNbRtpEncap,
        cvVoIPCallActiveInterleaving                 Integer32,
        cvVoIPCallActivePtime                        Integer32,
        cvVoIPCallActiveChannels                     Integer32,
        cvVoIPCallActiveCoderMode                    CvIlbcFrameMode,
        cvVoIPCallActiveCallId                       Unsigned32,
        cvVoIPCallActiveCallReferenceId              CvcCallReferenceIdOrZero,
        ccVoIPCallActivePolicyName                   DisplayString,
        cvVoIPCallActiveReversedDirectionPeerAddress DisplayString,
        cvVoIPCallActiveSessionId                    Unsigned32
}

cvVoIPCallActiveConnectionId OBJECT-TYPE
    SYNTAX          CvcGUid
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The global connection identifier for
        the active VoIP leg of the call." 
    ::= { cvVoIPCallActiveEntry 1 }

cvVoIPCallActiveRemoteIPAddress OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "Remote system IP address for the VoIP call." 
    ::= { cvVoIPCallActiveEntry 2 }

cvVoIPCallActiveRemoteUDPPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "Remote system UDP listener port to which to transmit voice
        packets." 
    ::= { cvVoIPCallActiveEntry 3 }

cvVoIPCallActiveRoundTripDelay OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The voice packet round trip delay between local and
        the remote system on the IP backbone during the call." 
    ::= { cvVoIPCallActiveEntry 4 }

cvVoIPCallActiveSelectedQoS OBJECT-TYPE
    SYNTAX          QosService
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The selected RSVP QoS for the voice call." 
    ::= { cvVoIPCallActiveEntry 5 }

cvVoIPCallActiveSessionProtocol OBJECT-TYPE
    SYNTAX          CvSessionProtocol
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object specifies the session protocol to be used
        for Internet call between local and remote router via
        IP backbone." 
    ::= { cvVoIPCallActiveEntry 6 }

cvVoIPCallActiveSessionTarget OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object specifies the session target of the peer that
        is used for the call. This object is set with the
        information in the call associated
        cvVoIPPeerCfgSessionTarget object when the voice over IP
        call is connected." 
    ::= { cvVoIPCallActiveEntry 7 }

cvVoIPCallActiveOnTimeRvPlayout OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice playout from data received on time for
        this call. This plus the durations for the GapFills in the
        following entries gives the Total Voice Playout Duration
        for Active Voice.
        This does not include duration for which no data was sent by the
        Transmit end as voice signal, e.g., silence suppression
        and fax signal. The On Time Playout Rate can be computed
        by dividing this entry by the Total Voice Playout Duration.
        This counter object will lock at the maximum value which
        is approximately two days." 
    ::= { cvVoIPCallActiveEntry 8 }

cvVoIPCallActiveGapFillWithSilence OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal replaced with signal played out
        during silence due to voice data not received on time
        (or lost) from voice gateway this call. This counter
        object will lock at the maximum value which is
        approximately two days." 
    ::= { cvVoIPCallActiveEntry 9 }

cvVoIPCallActiveGapFillWithPrediction OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal played out with signal synthesized
        from parameters or samples of data preceding in time due
        to voice data not received on time (or lost) from voice
        gateway for this call. An example of such playout is
        frame-erasure or frame-concealment strategies in G.729 and
        G.723.1 compression algorithms. This counter object will
        lock at the maximum value which is approximately two days." 
    ::= { cvVoIPCallActiveEntry 10 }

cvVoIPCallActiveGapFillWithInterpolation OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal played out with signal synthesized
        from parameters or samples of data preceding and following
        in time due to voice data not received on time (or lost)
        from voice gateway for this call. This counter object
        will lock at the maximum value which is approximately two
        days." 
    ::= { cvVoIPCallActiveEntry 11 }

cvVoIPCallActiveGapFillWithRedundancy OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal played out with signal synthesized
        from redundancy parameters available due to voice data not
        received on time (or lost) from voice gateway for this call.
        This counter object will lock at the maximum value which
        is approximately two days." 
    ::= { cvVoIPCallActiveEntry 12 }

cvVoIPCallActiveHiWaterPlayoutDelay OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The high water mark Voice Playout FIFO Delay during
        the voice call. This counter object will lock at the
        maximum value which is approximately two days." 
    ::= { cvVoIPCallActiveEntry 13 }

cvVoIPCallActiveLoWaterPlayoutDelay OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The low water mark Voice Playout FIFO Delay during
        the voice call." 
    ::= { cvVoIPCallActiveEntry 14 }

cvVoIPCallActiveReceiveDelay OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average Playout FIFO Delay plus the decoder delay
        during the voice call." 
    ::= { cvVoIPCallActiveEntry 15 }

cvVoIPCallActiveVADEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The object indicates whether or not the VAD (Voice Activity
        Detection) was enabled for the voice call." 
    ::= { cvVoIPCallActiveEntry 16 }

cvVoIPCallActiveCoderTypeRate OBJECT-TYPE
    SYNTAX          CvcCoderTypeRate
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The negotiated coder rate. It specifies the transmit rate of
        voice/fax compression to its associated call leg for the
        call. This rate is different from the configuration rate
        because of rate negotiation during the call." 
    ::= { cvVoIPCallActiveEntry 17 }

cvVoIPCallActiveLostPackets OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of lost voice packets during the call." 
    ::= { cvVoIPCallActiveEntry 18 }

cvVoIPCallActiveEarlyPackets OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of received voice packets that
        arrived too early to store in jitter buffer
        during the call." 
    ::= { cvVoIPCallActiveEntry 19 }

cvVoIPCallActiveLatePackets OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of received voice packets that
        arrived too late to playout with CODEC (Coder/Decoder)
        during the call." 
    ::= { cvVoIPCallActiveEntry 20 }

cvVoIPCallActiveUsername OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..16))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The textual identifier of the calling party (user) of the
        call. If the username is not available, then the value of
        this object will have a length of zero." 
    ::= { cvVoIPCallActiveEntry 21 }

cvVoIPCallActiveProtocolCallId OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..255))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The protocol-specific call identifier for the VoIP call." 
    ::= { cvVoIPCallActiveEntry 22 }

cvVoIPCallActiveRemSigIPAddrT OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object specifies the type of address contained in
        the associated instance of cvVoIPCallActiveRemSigIPAddr." 
    ::= { cvVoIPCallActiveEntry 23 }

cvVoIPCallActiveRemSigIPAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote signalling IP address for the VoIP call." 
    ::= { cvVoIPCallActiveEntry 24 }

cvVoIPCallActiveRemSigPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote signalling listener port to which to transmit
        voice packets." 
    ::= { cvVoIPCallActiveEntry 25 }

cvVoIPCallActiveRemMediaIPAddrT OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object specifies the type of address contained in
        the associated instance of
        cvVoIPCallActiveRemMediaIPAddr." 
    ::= { cvVoIPCallActiveEntry 26 }

cvVoIPCallActiveRemMediaIPAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote media end point IP address for the VoIP call." 
    ::= { cvVoIPCallActiveEntry 27 }

cvVoIPCallActiveRemMediaPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote media end point listener port to which to transmit
        voice packets." 
    ::= { cvVoIPCallActiveEntry 28 }

cvVoIPCallActiveSRTPEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates whether or not the SRTP (Secured RTP)
        was enabled for the voice call." 
    ::= { cvVoIPCallActiveEntry 29 }

cvVoIPCallActiveOctetAligned OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value true(1) octet align operation
        is used, and if the value is false(2), bandwidth efficient
        operation is used. This object is not instantiated when
        the object cvVoIPCallActiveCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 30 }

cvVoIPCallActiveBitRates OBJECT-TYPE
    SYNTAX          CvAmrNbBitRateMode
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates modes of Bit rates.
        This object is not instantiated when the object
        cvVoIPCallActiveCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 31 }

cvVoIPCallActiveModeChgPeriod OBJECT-TYPE
    SYNTAX          Integer32 (1..100)
    UNITS           "frame-blocks"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the interval (N frame-blocks) at which
        codec mode changes are allowed. This object is not
        instantiated when the object cvVoIPCallActiveCoderTypeRate
        is not equal to gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 32 }

cvVoIPCallActiveModeChgNeighbor OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value of true(1), mode changes will be
        made to only neighboring modes set to
        cvVoIPCallActiveBitRates object. If the value is false(2),
        mode changes will be allowed to any modes set to
        cvVoIPCallActiveBitRates object. This object is not
        instantiated when the object cvVoIPCallActiveCoderTypeRate
        is not equal to gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 33 }

cvVoIPCallActiveMaxPtime OBJECT-TYPE
    SYNTAX          Integer32 (20..100)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the maximum amount of media that
        can be encapsulated in a payload. Supported value is
        20 msec. This object is not instantiated when the
        object cvVoIPCallActiveCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 34 }

cvVoIPCallActiveCRC OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value of true(1), frame CRC will be
        included in the payload and if the value is false(2),
        frame CRC will not be included in the payload.
        This object is applicable only when RTP frame type
        is octet aligned. This object is not instantiated when
        the object cvVoIPCallActiveCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 35 }

cvVoIPCallActiveRobustSorting OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value of true(1), payload employs
        robust sorting and if the value is false(2), payload
        does not employ robust sorting. This object is applicable
        only when RTP frame type is octet aligned. This object
        is not instantiated when the object
        cvVoIPCallActiveCoderTypeRate is not equal to gsmAmrNb
        enum." 
    ::= { cvVoIPCallActiveEntry 36 }

cvVoIPCallActiveEncap OBJECT-TYPE
    SYNTAX          CvAmrNbRtpEncap
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the RTP encapsulation type.
        Supported RTP encapsulation type is RFC3267.
        This object is not instantiated when the object
        cvVoIPCallActiveCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 37 }

cvVoIPCallActiveInterleaving OBJECT-TYPE
    SYNTAX          Integer32 (1..50)
    UNITS           "frame-blocks"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the maximum number of frame-blocks
        allowed in an interleaving group. It indicates that
        frame-block level interleaving will be used for that
        session. If this object is not set, interleaving
        is not used. This object is applicable only when
        RTP frame type is octet aligned. This object is not
        instantiated when the object cvVoIPCallActiveCoderTypeRate
        is not equal to gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 38 }

cvVoIPCallActivePtime OBJECT-TYPE
    SYNTAX          Integer32 (20..100)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the length of the time in milliseconds
        represented by the media of the packet. Supported value is
        20 milliseconds. This object is not instantiated when the
        object cvVoIPCallActiveCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 39 }

cvVoIPCallActiveChannels OBJECT-TYPE
    SYNTAX          Integer32 (1..6)
    UNITS           "channels"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the number of audio channels.
        Supported value is 1. This object is not instantiated
        when the object cvVoIPCallActiveCoderTypeRate is not equal
        to gsmAmrNb enum." 
    ::= { cvVoIPCallActiveEntry 40 }

cvVoIPCallActiveCoderMode OBJECT-TYPE
    SYNTAX          CvIlbcFrameMode
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the iLBC codec mode.
        The value of this object is valid only if 
        cvVoIPCallActiveCoderTypeRate is equal to 
        'iLBC'."
    REFERENCE
        "RFC 3952: Section 5, for codec negotiation for iLBC mode." 
    ::= { cvVoIPCallActiveEntry 41 }

cvVoIPCallActiveCallId OBJECT-TYPE
    SYNTAX          Unsigned32 (1..**********)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the call identifier
        for the active VoIP leg of the call." 
    ::= { cvVoIPCallActiveEntry 42 }

cvVoIPCallActiveCallReferenceId OBJECT-TYPE
    SYNTAX          CvcCallReferenceIdOrZero
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The call reference ID associates the video call entry and voice
        call entry of the same endpoint.  An audio-only call may or may
        not have a valid call reference ID (i.e. value greater than
        zero), but in both cases, there will not be a video call entry
        associated with it.  

        For a video call, the video-specific information  is stored in a
        call entry in cVideoSessionActive of CISCO-VIDEO-SESSION-MIB, in
        which the call reference ID is also identified." 
    ::= { cvVoIPCallActiveEntry 43 }

ccVoIPCallActivePolicyName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object holds the policy name. It could be media
        policy, DSCP policy etc." 
    ::= { cvVoIPCallActiveEntry 44 }

cvVoIPCallActiveReversedDirectionPeerAddress OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object store the reversed direction peer address  If the
        address is not available, then it will have a length of zero.

        If the call is ingress then it contains called number and if the
        call is egress then it contains calling number." 
    ::= { cvVoIPCallActiveEntry 45 }

cvVoIPCallActiveSessionId OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the active session ID assigned by the
        call manager to identify call legs that belong to the same call
        session." 
    ::= { cvVoIPCallActiveEntry 46 }
 


cvCallActiveDS0s OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "interfaces"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The current number of DS0 interfaces used for the
        active calls." 
    ::= { cvGatewayCallActive 3 }

cvCallActiveDS0sHighThreshold OBJECT-TYPE
    SYNTAX          Unsigned32 (0..100)
    UNITS           "percent"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "A high threshold used to determine when to generate the
        cvdcActiveDS0sHighNotification. This object 
        represents the percentage of active DS0s in total number 
        of DS0s."
    DEFVAL          { 100 } 
    ::= { cvGatewayCallActive 4 }

cvCallActiveDS0sLowThreshold OBJECT-TYPE
    SYNTAX          Unsigned32 (0..100)
    UNITS           "percent"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "A low threshold used to determine when to generate the
        cvdcActiveDS0sLowNotification notification. This object 
        represents the percentage of active DS0s in total number 
        of DS0s."
    DEFVAL          { 0 } 
    ::= { cvGatewayCallActive 5 }

cvCallActiveDS0sHighNotifyEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether or not cvdcActiveDS0sHighNotification
        should be generated.

        'true' : Indicates that the cvdcActiveDS0sHighNotification
                 generation is enabled.

        'false': Indicates that cvdcActiveDS0sHighNotification
                 generation is disabled."
    DEFVAL          { false } 
    ::= { cvGatewayCallActive 6 }

cvCallActiveDS0sLowNotifyEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether or not cvdcActiveDS0sLowNotification
        should be generated.

        'true' : Indicates that the cvdcActiveDS0sLowNotification
                 generation is enabled.

        'false': Indicates that cvdcActiveDS0sLowNotification
                 generation is disabled."
    DEFVAL          { false } 
    ::= { cvGatewayCallActive 7 }
-- Call Volume

cvCallVolume  OBJECT IDENTIFIER
    ::= { cvGatewayCallActive 8 }


cvCallVolConnTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallVolConnEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the number of active
        call connections for each call connection type
        in the voice gateway."
    ::= { cvCallVolume 1 }

cvCallVolConnEntry OBJECT-TYPE
    SYNTAX          CvCallVolConnEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in the cvCallVolConnTable indicates
        number of active calls for a call connection type
        in the voice gateway."
    INDEX           { cvCallVolConnIndex } 
    ::= { cvCallVolConnTable 1 }

CvCallVolConnEntry ::= SEQUENCE {
        cvCallVolConnIndex            CvCallConnectionType,
        cvCallVolConnActiveConnection Gauge32
}

cvCallVolConnIndex OBJECT-TYPE
    SYNTAX          CvCallConnectionType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object represents index to the
        cvCallVolConnTable." 
    ::= { cvCallVolConnEntry 1 }

cvCallVolConnActiveConnection OBJECT-TYPE
    SYNTAX          Gauge32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of
        active calls for a connection type 
        in the voice gateway." 
    ::= { cvCallVolConnEntry 2 }
 


cvCallVolConnTotalActiveConnections OBJECT-TYPE
    SYNTAX          Gauge32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of
        active call legs in the voice gateway." 
    ::= { cvCallVolume 2 }

cvCallVolConnMaxCallConnectionLicenese OBJECT-TYPE
    SYNTAX          Unsigned32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the licensed call capacity
        for a voice gateway.  If the value is 0, no 
        licensing is done and the gateway can be 
        accomodate as many calls depending on its capability." 
    ::= { cvCallVolume 3 }
-- Calls per Dial peer

cvCallVolPeerTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallVolPeerEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the information about
        the usage of dialpeers configured in a 
        voice gateway."
    ::= { cvCallVolume 4 }

cvCallVolPeerEntry OBJECT-TYPE
    SYNTAX          CvCallVolPeerEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry represents a row in cvCallVolPeerTable
        and corresponds to the information about a 
        dialpeer configured on the voice gateway."
    AUGMENTS           { cvPeerCfgEntry  } 
    ::= { cvCallVolPeerTable 1 }

CvCallVolPeerEntry ::= SEQUENCE {
        cvCallVolPeerIncomingCalls Gauge32,
        cvCallVolPeerOutgoingCalls Gauge32
}

cvCallVolPeerIncomingCalls OBJECT-TYPE
    SYNTAX          Gauge32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of
        active calls that has selected the dialpeer
        as an incoming dialpeer." 
    ::= { cvCallVolPeerEntry 1 }

cvCallVolPeerOutgoingCalls OBJECT-TYPE
    SYNTAX          Gauge32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of
        active calls that has selected the dialpeer
        as an outgoing dialpeer." 
    ::= { cvCallVolPeerEntry 2 }
 

-- Calls per IP Interface

cvCallVolIfTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallVolIfEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the information about
        the usage of an IP interface in a voice
        gateway for voice media calls. This table
        has a sparse-dependent relationship with  
        ifTable. There exists an entry in this table, 
        for each of the  entries in ifTable where ifType 
        is one of 'ethernetCsmacd' and 'softwareLoopback'."
    ::= { cvCallVolume 5 }

cvCallVolIfEntry OBJECT-TYPE
    SYNTAX          CvCallVolIfEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry represents a row in cvCallVolIfTable
        and corresponds to the information about an IP 
        interface in the voice gateway."
    INDEX           { ifIndex } 
    ::= { cvCallVolIfTable 1 }

CvCallVolIfEntry ::= SEQUENCE {
        cvCallVolMediaIncomingCalls Gauge32,
        cvCallVolMediaOutgoingCalls Gauge32
}

cvCallVolMediaIncomingCalls OBJECT-TYPE
    SYNTAX          Gauge32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of
        inbound active media calls through this IP 
        interface." 
    ::= { cvCallVolIfEntry 1 }

cvCallVolMediaOutgoingCalls OBJECT-TYPE
    SYNTAX          Gauge32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of
        outbound active media calls through the IP 
        interface." 
    ::= { cvCallVolIfEntry 2 }
 

-- call-rate.

cvCallRateMonitor  OBJECT IDENTIFIER
    ::= { cvGatewayCallActive 11 }


cvCallRateMonitorEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object represents the state of call-monitoring.
        A value of 'true' indicates that call-monitoring 
        is enabled.  A value of 'false' indicates that 
        call-monitoring is disabled."
    DEFVAL          { true } 
    ::= { cvCallRateMonitor 1 }

cvCallRateMonitorTime OBJECT-TYPE
    SYNTAX          Unsigned32 (1..12)
    UNITS           "five seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object represents the interval for
        which the gateway monitors the call-rate."
    DEFVAL          { 1 } 
    ::= { cvCallRateMonitor 2 }

cvCallRate OBJECT-TYPE
    SYNTAX          Gauge32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of
        calls handled by the gateway during the 
        monitored time." 
    ::= { cvCallRateMonitor 3 }

cvCallRateHiWaterMark OBJECT-TYPE
    SYNTAX          Unsigned32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the high water mark
        for the number of calls handled by the 
        gateway in an unit interval of 
        cvCallRateMonitorTime, from the time 
        the call-monitoring is enabled." 
    ::= { cvCallRateMonitor 4 }
-- *********************************************************************
-- Voice Dial Control Gateway Call History Group
-- *********************************************************************
--   

-- Voice over Telephony Call History Table

cvCallHistoryTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallHistoryEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table is the voice extension to the call history table
        of IETF Dial Control MIB. It contains voice encapsulation
        call leg information such as voice packet statistics,
        coder usage and end to end bandwidth of the call leg."
    ::= { cvGatewayCallHistory 1 }

cvCallHistoryEntry OBJECT-TYPE
    SYNTAX          CvCallHistoryEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The information regarding a single voice encapsulation
        call leg.
        The call leg entry is identified by using the same index
        objects that are used by Call Active table of IETF Dial
        Control MIB to identify the call.
        An entry of this table is created when its associated call
        history entry in the IETF Dial Control MIB is created and
        the call history entry contains the call establishment to
        a voice encapsulation peer.
        The entry is deleted when its associated call active entry
        in the IETF Dial Control MIB is deleted."
    INDEX           { cCallHistoryIndex } 
    ::= { cvCallHistoryTable 1 }

CvCallHistoryEntry ::= SEQUENCE {
        cvCallHistoryConnectionId    CvcGUid,
        cvCallHistoryTxDuration      AbsoluteCounter32,
        cvCallHistoryVoiceTxDuration AbsoluteCounter32,
        cvCallHistoryFaxTxDuration   AbsoluteCounter32,
        cvCallHistoryCoderTypeRate   CvcCoderTypeRate,
        cvCallHistoryNoiseLevel      Integer32,
        cvCallHistoryACOMLevel       Integer32,
        cvCallHistorySessionTarget   DisplayString,
        cvCallHistoryImgPageCount    AbsoluteCounter32,
        cvCallHistoryCallingName     SnmpAdminString,
        cvCallHistoryCallerIDBlock   TruthValue,
        cvCallHistoryAccountCode     SnmpAdminString,
        cvCallHistoryCallId          Unsigned32
}

cvCallHistoryConnectionId OBJECT-TYPE
    SYNTAX          CvcGUid
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The global connection identifier for the
        telephony leg, which was assigned to the call." 
    ::= { cvCallHistoryEntry 1 }

cvCallHistoryTxDuration OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of Transmit path open from this peer to the
        voice gateway for the call leg. This counter object will
        lock at the maximum value which is approximately two
        days." 
    ::= { cvCallHistoryEntry 2 }

cvCallHistoryVoiceTxDuration OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration for this call leg. The Voice Utilization Rate
        can be obtained by dividing this by
        cvCallHistoryTXDuration object. This counter object will
        lock at the maximum value which is approximately two
        days." 
    ::= { cvCallHistoryEntry 3 }

cvCallHistoryFaxTxDuration OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of fax transmitted from this peer to voice
        gateway for this call leg. The FAX Utilization Rate can be
        obtained by dividing this by cvCallHistoryTXDuration
        object. This counter object will lock at the maximum
        value which is approximately two days." 
    ::= { cvCallHistoryEntry 4 }

cvCallHistoryCoderTypeRate OBJECT-TYPE
    SYNTAX          CvcCoderTypeRate
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated coder rate. It specifies the transmit rate
        of voice/fax compression to its associated call leg for
        the call." 
    ::= { cvCallHistoryEntry 5 }

cvCallHistoryNoiseLevel OBJECT-TYPE
    SYNTAX          Integer32 (-128..8)
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object contains the average noise level for the call
        leg." 
    ::= { cvCallHistoryEntry 6 }

cvCallHistoryACOMLevel OBJECT-TYPE
    SYNTAX          Integer32 (-1..127)
    UNITS           "dB"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object contains the average ACOM level for the call
        leg. The value -1 indicates the level can not be
        determined or level detection is disabled." 
    ::= { cvCallHistoryEntry 7 }

cvCallHistorySessionTarget OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object specifies the session target of the peer that
        is used for the call leg via telephony interface." 
    ::= { cvCallHistoryEntry 8 }

cvCallHistoryImgPageCount OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "pages"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of FAX related image pages are received or
        transmitted via the peer for the call leg." 
    ::= { cvCallHistoryEntry 9 }

cvCallHistoryCallingName OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The calling party name of the call. If the name is
        not available, then it will have a length of zero." 
    ::= { cvCallHistoryEntry 10 }

cvCallHistoryCallerIDBlock OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates whether or not the caller ID feature
        is blocked for this call." 
    ::= { cvCallHistoryEntry 11 }

cvCallHistoryAccountCode OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..50))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the account code input to the call.
        It could be used by down stream billing server.
        The value of empty string indicates no account code input."
    DEFVAL          { "" } 
    ::= { cvCallHistoryEntry 12 }

cvCallHistoryCallId OBJECT-TYPE
    SYNTAX          Unsigned32 (1..**********)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the call identifier for the
        telephony leg, which was assigned to the call." 
    ::= { cvCallHistoryEntry 13 }
 

-- Voice over IP Call History Table

cvVoIPCallHistoryTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvVoIPCallHistoryEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table is the VoIP extension to the call history table
        of IETF Dial Control MIB. It contains VoIP call leg
        information about specific VoIP call destination and the
        selected QoS for the call leg."
    ::= { cvGatewayCallHistory 2 }

cvVoIPCallHistoryEntry OBJECT-TYPE
    SYNTAX          CvVoIPCallHistoryEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The information regarding a single VoIP call leg.
        The call leg entry is identified by using the same index
        objects that are used by Call Active table of IETF Dial
        Control MIB to identify the call.
        An entry of this table is created when its associated call
        history entry in the IETF Dial Control MIB is created and
        the call history entry contains information for the call
        establishment to the peer on the IP backbone via a voice
        over IP peer.
        The entry is deleted when its associated call history
        entry in the IETF Dial Control MIB is deleted."
    INDEX           { cCallHistoryIndex } 
    ::= { cvVoIPCallHistoryTable 1 }

CvVoIPCallHistoryEntry ::= SEQUENCE {
        cvVoIPCallHistoryConnectionId             CvcGUid,
        cvVoIPCallHistoryRemoteIPAddress          IpAddress,
        cvVoIPCallHistoryRemoteUDPPort            Integer32,
        cvVoIPCallHistoryRoundTripDelay           Gauge32,
        cvVoIPCallHistorySelectedQoS              QosService,
        cvVoIPCallHistorySessionProtocol          CvSessionProtocol,
        cvVoIPCallHistorySessionTarget            DisplayString,
        cvVoIPCallHistoryOnTimeRvPlayout          AbsoluteCounter32,
        cvVoIPCallHistoryGapFillWithSilence       AbsoluteCounter32,
        cvVoIPCallHistoryGapFillWithPrediction    AbsoluteCounter32,
        cvVoIPCallHistoryGapFillWithInterpolation AbsoluteCounter32,
        cvVoIPCallHistoryGapFillWithRedundancy    AbsoluteCounter32,
        cvVoIPCallHistoryHiWaterPlayoutDelay      AbsoluteCounter32,
        cvVoIPCallHistoryLoWaterPlayoutDelay      Gauge32,
        cvVoIPCallHistoryReceiveDelay             Gauge32,
        cvVoIPCallHistoryVADEnable                TruthValue,
        cvVoIPCallHistoryCoderTypeRate            CvcCoderTypeRate,
        cvVoIPCallHistoryIcpif                    Integer32,
        cvVoIPCallHistoryLostPackets              AbsoluteCounter32,
        cvVoIPCallHistoryEarlyPackets             AbsoluteCounter32,
        cvVoIPCallHistoryLatePackets              AbsoluteCounter32,
        cvVoIPCallHistoryUsername                 SnmpAdminString,
        cvVoIPCallHistoryProtocolCallId           OCTET STRING,
        cvVoIPCallHistoryRemSigIPAddrT            InetAddressType,
        cvVoIPCallHistoryRemSigIPAddr             InetAddress,
        cvVoIPCallHistoryRemSigPort               Integer32,
        cvVoIPCallHistoryRemMediaIPAddrT          InetAddressType,
        cvVoIPCallHistoryRemMediaIPAddr           InetAddress,
        cvVoIPCallHistoryRemMediaPort             Integer32,
        cvVoIPCallHistorySRTPEnable               TruthValue,
        cvVoIPCallHistoryFallbackIcpif            Integer32,
        cvVoIPCallHistoryFallbackLoss             AbsoluteCounter32,
        cvVoIPCallHistoryFallbackDelay            Gauge32,
        cvVoIPCallHistoryOctetAligned             TruthValue,
        cvVoIPCallHistoryBitRates                 CvAmrNbBitRateMode,
        cvVoIPCallHistoryModeChgPeriod            Integer32,
        cvVoIPCallHistoryModeChgNeighbor          TruthValue,
        cvVoIPCallHistoryMaxPtime                 Integer32,
        cvVoIPCallHistoryCRC                      TruthValue,
        cvVoIPCallHistoryRobustSorting            TruthValue,
        cvVoIPCallHistoryEncap                    CvAmrNbRtpEncap,
        cvVoIPCallHistoryInterleaving             Integer32,
        cvVoIPCallHistoryPtime                    Integer32,
        cvVoIPCallHistoryChannels                 Integer32,
        cvVoIPCallHistoryCoderMode                CvIlbcFrameMode,
        cvVoIPCallHistoryCallId                   Unsigned32,
        cvVoIPCallHistoryCallReferenceId          CvcCallReferenceIdOrZero,
        cvVoIPCallHistorySessionId                Unsigned32
}

cvVoIPCallHistoryConnectionId OBJECT-TYPE
    SYNTAX          CvcGUid
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The global connection identifier for the
        VoIP leg, which was assigned to the call." 
    ::= { cvVoIPCallHistoryEntry 1 }

cvVoIPCallHistoryRemoteIPAddress OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "Remote system IP address for the call." 
    ::= { cvVoIPCallHistoryEntry 2 }

cvVoIPCallHistoryRemoteUDPPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "Remote system UDP listener port to which to transmit voice
        packets for the call." 
    ::= { cvVoIPCallHistoryEntry 3 }

cvVoIPCallHistoryRoundTripDelay OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The voice packet round trip delay between local and
        the remote system on the IP backbone during the call." 
    ::= { cvVoIPCallHistoryEntry 4 }

cvVoIPCallHistorySelectedQoS OBJECT-TYPE
    SYNTAX          QosService
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The selected RSVP QoS for the call." 
    ::= { cvVoIPCallHistoryEntry 5 }

cvVoIPCallHistorySessionProtocol OBJECT-TYPE
    SYNTAX          CvSessionProtocol
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object specifies the session protocol to be used
        for Internet call between local and remote router via
        IP backbone." 
    ::= { cvVoIPCallHistoryEntry 6 }

cvVoIPCallHistorySessionTarget OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object specifies the session target of the peer that
        is used for the Voice over IP call." 
    ::= { cvVoIPCallHistoryEntry 7 }

cvVoIPCallHistoryOnTimeRvPlayout OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice playout from data received on time for
        this call. This plus the durations for the GapFills in the
        following entries gives the Total Voice Playout Duration
        for Active Voice.
        This does not include duration for which no data was sent by the
        Transmit end as voice signal, e.g., silence suppression
        and fax signal. The On Time Playout Rate can be computed
        by dividing this entry by the Total Voice Playout Duration.
        This counter object will lock at the maximum value which
        is approximately two days." 
    ::= { cvVoIPCallHistoryEntry 8 }

cvVoIPCallHistoryGapFillWithSilence OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal replaced with signal played out
        during silence due to voice data not received on time
        (or lost) from voice gateway this call. This counter
        object will lock at the maximum value which is
        approximately two days." 
    ::= { cvVoIPCallHistoryEntry 9 }

cvVoIPCallHistoryGapFillWithPrediction OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal played out with signal synthesized
        from parameters or samples of data preceding in time due to
        voice data not received on time (or lost) from voice gateway
        for this call. An example of such playout is frame-erasure
        or  frame-concealment strategies in G.729 and G.723.1
        compression algorithms. This counter object will lock at
        the maximum value which is approximately two days." 
    ::= { cvVoIPCallHistoryEntry 10 }

cvVoIPCallHistoryGapFillWithInterpolation OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal played out with signal synthesized
        from parameters or samples of data preceding and following
        in time due to voice data not received on time (or lost)
        from voice gateway for this call. This counter object
        will lock at the maximum value which is approximately two
        days." 
    ::= { cvVoIPCallHistoryEntry 11 }

cvVoIPCallHistoryGapFillWithRedundancy OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Duration of voice signal played out with signal synthesized
        from redundancy parameters available due to voice data not
        received on time (or lost) from voice gateway for this call.
        This counter object will lock at the maximum value which
        is approximately two days." 
    ::= { cvVoIPCallHistoryEntry 12 }

cvVoIPCallHistoryHiWaterPlayoutDelay OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The high water mark Voice Playout FIFO Delay during
        the voice call. This counter object will lock at the
        maximum value which is approximately two days." 
    ::= { cvVoIPCallHistoryEntry 13 }

cvVoIPCallHistoryLoWaterPlayoutDelay OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The low water mark Voice Playout FIFO Delay during
        the voice call." 
    ::= { cvVoIPCallHistoryEntry 14 }

cvVoIPCallHistoryReceiveDelay OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average Playout FIFO Delay plus the decoder delay
        during the voice call." 
    ::= { cvVoIPCallHistoryEntry 15 }

cvVoIPCallHistoryVADEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The object indicates whether or not the VAD (Voice Activity
        Detection) was enabled for the voice call." 
    ::= { cvVoIPCallHistoryEntry 16 }

cvVoIPCallHistoryCoderTypeRate OBJECT-TYPE
    SYNTAX          CvcCoderTypeRate
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The negotiated coder rate. It specifies the transmit rate of
        voice/fax compression to its associated call leg for the
        call. This rate is different from the configuration rate
        because of rate negotiation during the call." 
    ::= { cvVoIPCallHistoryEntry 17 }

cvVoIPCallHistoryIcpif OBJECT-TYPE
    SYNTAX          Integer32 (-1..55)
    UNITS           "equipment impairment factor (eif)"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Calculated Planning Impairment Factor (Icpif) of the
        call  that is associated to this call leg.
        The value in this object is computed by the following
        equation.
        Icpif of the call =
        Itotal (total impairment value) of the call - A
        (Expectation Factor) in the cvVoIPPeerCfgExpectFactor of
        the call leg associated peer.
        A value of -1 implies that Icpif was not calculated and is
        meaningless for this call."
    REFERENCE
        "[1] ITU G.113: Section 7.2 Impairment factors and the total
         impairment value.
             [2] ITU G.113: Sections 9 Considerations associated with
         the Calculated Planning Impairment Factor (Icpif)." 
    ::= { cvVoIPCallHistoryEntry 18 }

cvVoIPCallHistoryLostPackets OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of lost voice packets during the call." 
    ::= { cvVoIPCallHistoryEntry 19 }

cvVoIPCallHistoryEarlyPackets OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of received voice packets that are
        arrived too early to store in jitter buffer
        during the call." 
    ::= { cvVoIPCallHistoryEntry 20 }

cvVoIPCallHistoryLatePackets OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    UNITS           "packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of received voice packets that are
        arrived too late to playout with CODEC (Coder/Decoder)
        during the call." 
    ::= { cvVoIPCallHistoryEntry 21 }

cvVoIPCallHistoryUsername OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..16))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The textual identifier of the calling party (user) of the
        call. If the username is not available, then the value of
        this object will have a length of zero." 
    ::= { cvVoIPCallHistoryEntry 22 }

cvVoIPCallHistoryProtocolCallId OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..255))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The protocol-specific call identifier for the VoIP call." 
    ::= { cvVoIPCallHistoryEntry 23 }

cvVoIPCallHistoryRemSigIPAddrT OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object specifies the type of address contained in
        the associated instance of cvVoIPCallHistoryRemSigIPAddr." 
    ::= { cvVoIPCallHistoryEntry 24 }

cvVoIPCallHistoryRemSigIPAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote signalling IP address for the VoIP call." 
    ::= { cvVoIPCallHistoryEntry 25 }

cvVoIPCallHistoryRemSigPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote signalling listener port to which to transmit
        voice packets." 
    ::= { cvVoIPCallHistoryEntry 26 }

cvVoIPCallHistoryRemMediaIPAddrT OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object specifies the type of address contained in
        the associated instance of
        cvVoIPCallHistoryRemMediaIPAddr." 
    ::= { cvVoIPCallHistoryEntry 27 }

cvVoIPCallHistoryRemMediaIPAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote media end point IP address for the VoIP call." 
    ::= { cvVoIPCallHistoryEntry 28 }

cvVoIPCallHistoryRemMediaPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Remote media end point listener port to which to transmit
        voice packets." 
    ::= { cvVoIPCallHistoryEntry 29 }

cvVoIPCallHistorySRTPEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates whether or not the SRTP (Secured RTP)
        was enabled for the voice call." 
    ::= { cvVoIPCallHistoryEntry 30 }

cvVoIPCallHistoryFallbackIcpif OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Calculated Planning Impairment Factor (Icpif) of the
        call  that is associated to this call leg.
        The value in this object is computed by the following
        equation.
        Icpif of the fallback probe =
        Itotal (total impairment value)  - configured fallback
        (Expectation Factor).
        A value of 0 implies that Icpif was not calculated and is
        meaningless for this attempt." 
    ::= { cvVoIPCallHistoryEntry 31 }

cvVoIPCallHistoryFallbackLoss OBJECT-TYPE
    SYNTAX          AbsoluteCounter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "FallbackLoss is the percentage of loss packets based on
        the total packets sent." 
    ::= { cvVoIPCallHistoryEntry 32 }

cvVoIPCallHistoryFallbackDelay OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The FallbackDelay is calculated as follows -
        Take the sum of the round trips for all the probes, 
        divide by the number of probes, 
        and divide by two to get the one-way delay.  
        Then add in jitter_in or jiter_out,
        which ever is higher." 
    ::= { cvVoIPCallHistoryEntry 33 }

cvVoIPCallHistoryOctetAligned OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value true(1) octet align operation
        is used, and if the value is false(2), bandwidth efficient
        operation is used. This object is not instantiated when
        the object cvVoIPCallHistoryCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 34 }

cvVoIPCallHistoryBitRates OBJECT-TYPE
    SYNTAX          CvAmrNbBitRateMode
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates modes of Bit rates.
        This object is not instantiated when the object
        cvVoIPCallHistoryCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 35 }

cvVoIPCallHistoryModeChgPeriod OBJECT-TYPE
    SYNTAX          Integer32 (1..100)
    UNITS           "frame-blocks"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the interval (N frame-blocks) at which
        codec mode changes are allowed. This object is not
        instantiated when the object cvVoIPCallHistoryCoderTypeRate
        is not equal to gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 36 }

cvVoIPCallHistoryModeChgNeighbor OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value of true(1), mode changes will be
        made to only neighboring modes set to
        cvVoIPCallHistoryBitRates object. If the value is false(2),
        mode changes will be allowed to any modes set to
        cvVoIPCallHistoryBitRates object. This object is not
        instantiated when the object cvVoIPCallHistoryCoderTypeRate
        is not equal to gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 37 }

cvVoIPCallHistoryMaxPtime OBJECT-TYPE
    SYNTAX          Integer32 (20..100)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the maximum amount of media that
        can be encapsulated in a payload. Supported value is
        20 msec. This object is not instantiated when the
        object cvVoIPCallHistoryCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 38 }

cvVoIPCallHistoryCRC OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value of true(1), frame CRC will be
        included in the payload and if the value is false(2),
        frame CRC will not be included in the payload.
        This object is applicable only when RTP frame type
        is octet aligned. This object is not instantiated when
        the object cvVoIPCallHistoryCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 39 }

cvVoIPCallHistoryRobustSorting OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the object has a value of true(1), payload employs
        robust sorting and if the value is false(2), payload
        does not employ robust sorting. This object is applicable
        only when RTP frame type is octet aligned. This object
        is not instantiated when the object
        cvVoIPCallHistoryCoderTypeRate is not equal to gsmAmrNb
        enum." 
    ::= { cvVoIPCallHistoryEntry 40 }

cvVoIPCallHistoryEncap OBJECT-TYPE
    SYNTAX          CvAmrNbRtpEncap
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the RTP encapsulation type.
        Supported RTP encapsulation type is RFC3267.
        This object is not instantiated when the object
        cvVoIPCallHistoryCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 41 }

cvVoIPCallHistoryInterleaving OBJECT-TYPE
    SYNTAX          Integer32 (1..50)
    UNITS           "frame-blocks"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the maximum number of frame-blocks
        allowed in an interleaving group. It indicates that
        frame-block level interleaving will be used for that
        session. If this object is not set, interleaving
        is not used. This object is applicable only when
        RTP frame type is octet aligned. This object is not
        instantiated when the object cvVoIPCallHistoryCoderTypeRate
        is not equal to gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 42 }

cvVoIPCallHistoryPtime OBJECT-TYPE
    SYNTAX          Integer32 (20..100)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the length of the time in milliseconds
        represented by the media of the packet. Supported value is
        20 milliseconds. This object is not instantiated when the
        object cvVoIPCallHistoryCoderTypeRate is not equal to
        gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 43 }

cvVoIPCallHistoryChannels OBJECT-TYPE
    SYNTAX          Integer32 (1..6)
    UNITS           "channels"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the number of audio channels.
        Supported value is 1. This object is not instantiated
        when the object cvVoIPCallHistoryCoderTypeRate is not equal
        to gsmAmrNb enum." 
    ::= { cvVoIPCallHistoryEntry 44 }

cvVoIPCallHistoryCoderMode OBJECT-TYPE
    SYNTAX          CvIlbcFrameMode
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object indicates the iLBC mode.
        The value of this object is valid only if 
        cvVoIPCallHistoryCoderTypeRate is equal to 
        'iLBC'."
    REFERENCE
        "RFC 3952: Section 5, for codec negotiation for iLBC mode." 
    ::= { cvVoIPCallHistoryEntry 45 }

cvVoIPCallHistoryCallId OBJECT-TYPE
    SYNTAX          Unsigned32 (1..**********)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the call identifier for the
        VoIP leg, which was assigned to the call." 
    ::= { cvVoIPCallHistoryEntry 46 }

cvVoIPCallHistoryCallReferenceId OBJECT-TYPE
    SYNTAX          CvcCallReferenceIdOrZero
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The call reference ID associates the video call entry and voice
        call entry of the same endpoint.  An audio-only call may or may
        not have a valid call reference ID (i.e. value greater than
        zero), but in both cases, there will not be a video call entry
        associated with it. 

        For a video call, the video-specific information  is stored in a
        call entry in cVideoSessionActive of CISCO-VIDEO-SESSION-MIB, in
        which the call reference ID is also identified." 
    ::= { cvVoIPCallHistoryEntry 47 }

cvVoIPCallHistorySessionId OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the session ID assigned by the call
        manager to identify call legs that belong to the same call
        session.  This session ID (history) represents a completed call
        session, whereas the active session ID
        (cvVoIPCallActiveSessionId) represents an ongoing session." 
    ::= { cvVoIPCallHistoryEntry 48 }
 

cvCallVolumeStatsHistory  OBJECT IDENTIFIER
    ::= { cvGatewayCallHistory 3 }


cvCallRateStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallRateStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents voice call rate measurement in various
        interval lengths defined by the 
        CvCallVolumeStatsIntvlType object.

        Each interval may contain one or more entries to allow for
        detailed measurement to be collected."
    ::= { cvCallVolumeStatsHistory 1 }

cvCallRateStatsEntry OBJECT-TYPE
    SYNTAX          CvCallRateStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvCallRateStatsTable
        This entry is created at the system initialization and is
        updated at every epoch based on CvCallVolumeStatsIntvlType"
    INDEX           {
                        cvCallRateStatsIntvlDurUnits,
                        cvCallRateStatsIntvlDur
                    } 
    ::= { cvCallRateStatsTable 1 }

CvCallRateStatsEntry ::= SEQUENCE {
        cvCallRateStatsIntvlDurUnits CvCallVolumeStatsIntvlType,
        cvCallRateStatsIntvlDur      Unsigned32,
        cvCallRateStatsMaxVal        Unsigned32,
        cvCallRateStatsAvgVal        Unsigned32
}

cvCallRateStatsIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeStatsIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in Call Rate Table to select one among three
        interval-tables.

        The different types in this table are represented by 
        CvCallVolumeStatsIntvlType" 
    ::= { cvCallRateStatsEntry 1 }

cvCallRateStatsIntvlDur OBJECT-TYPE
    SYNTAX          Unsigned32 (1..72)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to the different past periods
        in given in interval of call rate table.
        This range is 1-60 for Seconds and Minutes table 
        wherein 1-72 for hours table." 
    ::= { cvCallRateStatsEntry 2 }

cvCallRateStatsMaxVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls-per-second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the maximum calls per second
        that occured for the given period for the given interval." 
    ::= { cvCallRateStatsEntry 3 }

cvCallRateStatsAvgVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls-per-second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the average calls per second
        that occured for the given period for the given interval." 
    ::= { cvCallRateStatsEntry 4 }
 


cvCallLegRateStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallLegRateStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cvCallLegRateStatsTable table represents voice call leg rate
        measurement in various interval lengths defined by 
        the CvCallVolumeStatsIntvlType object.
        Each interval may contain one or more entries to allow for
        detailed measurement to be collected."
    ::= { cvCallVolumeStatsHistory 2 }

cvCallLegRateStatsEntry OBJECT-TYPE
    SYNTAX          CvCallLegRateStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvCallLegRateStatsTable
        This entry is created at the system initialization and is
        updated at every epoch based on CvCallVolumeStatsIntvlType"
    INDEX           {
                        cvCallLegRateStatsIntvlDurUnits,
                        cvCallLegRateStatsIntvlDur
                    } 
    ::= { cvCallLegRateStatsTable 1 }

CvCallLegRateStatsEntry ::= SEQUENCE {
        cvCallLegRateStatsIntvlDurUnits CvCallVolumeStatsIntvlType,
        cvCallLegRateStatsIntvlDur      Unsigned32,
        cvCallLegRateStatsMaxVal        Unsigned32,
        cvCallLegRateStatsAvgVal        Unsigned32
}

cvCallLegRateStatsIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeStatsIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in Call Leg Rate Table to select one among
        three
        interval-tables.

        The different types in this table are represented by 
        CvCallVolumeStatsIntvlType" 
    ::= { cvCallLegRateStatsEntry 1 }

cvCallLegRateStatsIntvlDur OBJECT-TYPE
    SYNTAX          Unsigned32 (1..72)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to the different past periods
        in given in interval of call rate table.
        This range is 1-60 for Seconds and Minutes table 
        wherein 1-72 for hours table." 
    ::= { cvCallLegRateStatsEntry 2 }

cvCallLegRateStatsMaxVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "call-legs per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the maximum call-legs per second
        that occured for the given period for the given interval." 
    ::= { cvCallLegRateStatsEntry 3 }

cvCallLegRateStatsAvgVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "call-legs per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the average call-legs per second
        that occured for the given period for the given interval." 
    ::= { cvCallLegRateStatsEntry 4 }
 


cvActiveCallStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvActiveCallStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the active voice calls in various
        interval lengths defined by the 
        CvCallVolumeStatsIntvlType object.

        Each interval may contain one or more entries to allow for
        detailed measurement to be collected."
    ::= { cvCallVolumeStatsHistory 3 }

cvActiveCallStatsEntry OBJECT-TYPE
    SYNTAX          CvActiveCallStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvActiveCallStatsTable
        This entry is created at the system initialization and is
        updated at every epoch based on CvCallVolumeStatsIntvlType"
    INDEX           {
                        cvActiveCallStatsIntvlDurUnits,
                        cvActiveCallStatsIntvlDur
                    } 
    ::= { cvActiveCallStatsTable 1 }

CvActiveCallStatsEntry ::= SEQUENCE {
        cvActiveCallStatsIntvlDurUnits CvCallVolumeStatsIntvlType,
        cvActiveCallStatsIntvlDur      Unsigned32,
        cvActiveCallStatsMaxVal        Unsigned32,
        cvActiveCallStatsAvgVal        Unsigned32
}

cvActiveCallStatsIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeStatsIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in Active Call Rate Table (con-current calls
        table) to select one among three interval-tables.

        The different types in this table are represented by 
        CvCallVolumeStatsIntvlType" 
    ::= { cvActiveCallStatsEntry 1 }

cvActiveCallStatsIntvlDur OBJECT-TYPE
    SYNTAX          Unsigned32 (1..72)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to the different past periods
        in given in interval of active call table.
        This range is 1-60 for Seconds and Minutes table 
        wherein 1-72 for hours table." 
    ::= { cvActiveCallStatsEntry 2 }

cvActiveCallStatsMaxVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the maximum number of active call
        that occured for the given period for the given interval." 
    ::= { cvActiveCallStatsEntry 3 }

cvActiveCallStatsAvgVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the average number of active calls
        that occured for the given period for the given interval." 
    ::= { cvActiveCallStatsEntry 4 }
 


cvCallDurationStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallDurationStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the number of calls below a specific
        duration in various interval length defined by 
        the CvCallVolumeStatsIntvlType object.  

        The specific duration is configurable value of 
         cvCallDurationStatsThreshold object.

        Each interval may contain one or more entries to allow for 
        detailed measurement to be collected."
    ::= { cvCallVolumeStatsHistory 4 }

cvCallDurationStatsEntry OBJECT-TYPE
    SYNTAX          CvCallDurationStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvCallDurationStatsTable
        This entry is created at the system initialization and is
        updated at every epoch based on CvCallVolumeStatsIntvlType"
    INDEX           {
                        cvCallDurationStatsIntvlDurUnits,
                        cvCallDurationStatsIntvlDur
                    } 
    ::= { cvCallDurationStatsTable 1 }

CvCallDurationStatsEntry ::= SEQUENCE {
        cvCallDurationStatsIntvlDurUnits CvCallVolumeStatsIntvlType,
        cvCallDurationStatsIntvlDur      Unsigned32,
        cvCallDurationStatsMaxVal        Unsigned32,
        cvCallDurationStatsAvgVal        Unsigned32
}

cvCallDurationStatsIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeStatsIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in Call Duration Table to select one among
        three interval-tables.

        The different types in this table are represented by 
        CvCallVolumeStatsIntvlType" 
    ::= { cvCallDurationStatsEntry 1 }

cvCallDurationStatsIntvlDur OBJECT-TYPE
    SYNTAX          Unsigned32 (1..72)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to the different past periods
        in given in interval of call Duration table.
        This range is 1-60 for Seconds and Minutes table 
        wherein 1-72 for hours table." 
    ::= { cvCallDurationStatsEntry 2 }

cvCallDurationStatsMaxVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the maximum number of calls having a
        duration which is below the threshold for the given interval." 
    ::= { cvCallDurationStatsEntry 3 }

cvCallDurationStatsAvgVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the average number of calls having a
        duration which is below the threshold for the given interval." 
    ::= { cvCallDurationStatsEntry 4 }
 


cvSipMsgRateStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvSipMsgRateStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents the SIP message rate measurement in
        various interval length defined by the 
        CvCallVolumeStatsIntvlType object.

        Each interval may contain one or more entries to allow for
        detailed measurement to be collected"
    ::= { cvCallVolumeStatsHistory 5 }

cvSipMsgRateStatsEntry OBJECT-TYPE
    SYNTAX          CvSipMsgRateStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvSipMsgRateStatsTable
        This entry is created at the system initialization and is
        updated at every epoch based on CvCallVolumeStatsIntvlType"
    INDEX           {
                        cvSipMsgRateStatsIntvlDurUnits,
                        cvSipMsgRateStatsIntvlDur
                    } 
    ::= { cvSipMsgRateStatsTable 1 }

CvSipMsgRateStatsEntry ::= SEQUENCE {
        cvSipMsgRateStatsIntvlDurUnits CvCallVolumeStatsIntvlType,
        cvSipMsgRateStatsIntvlDur      Unsigned32,
        cvSipMsgRateStatsMaxVal        Unsigned32,
        cvSipMsgRateStatsAvgVal        Unsigned32
}

cvSipMsgRateStatsIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeStatsIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in SIP Message Rate Table to select one
        among three interval-tables.

        The different types in this table are represented by 
        CvCallVolumeStatsIntvlType" 
    ::= { cvSipMsgRateStatsEntry 1 }

cvSipMsgRateStatsIntvlDur OBJECT-TYPE
    SYNTAX          Unsigned32 (1..72)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to the different past
        periods in given in interval of SIP message rate table.
        This range is 1-60 for Seconds and Minutes table 
        wherein 1-72 for hours table." 
    ::= { cvSipMsgRateStatsEntry 2 }

cvSipMsgRateStatsMaxVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "SIP messages per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the maximum SIP messages  per second that
        is received for the given interval." 
    ::= { cvSipMsgRateStatsEntry 3 }

cvSipMsgRateStatsAvgVal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "SIP messages per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the average SIP messages per second that
        is received for the given interval." 
    ::= { cvSipMsgRateStatsEntry 4 }
 


cvCallRateWMTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallRateWMEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents high watermarks achieved
        by call rate in various interval length defined 
        by CvCallVolumeWMIntvlType. 

        Each interval may contain one or more entries to allow for 
        detailed measurement to be collected"
    ::= { cvCallVolumeStatsHistory 6 }

cvCallRateWMEntry OBJECT-TYPE
    SYNTAX          CvCallRateWMEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvCallRateWMTable
        This entry is created at the system initialization and is
        updated whenever 
        a) This entry is obsolete OR
        b) A new/higher entry is available.
        These entries are reinitialised/added/deleted  if
        cvCallVolumeWMTableSize is changed"
    INDEX           {
                        cvCallRateWMIntvlDurUnits,
                        cvCallRateWMIndex
                    } 
    ::= { cvCallRateWMTable 1 }

CvCallRateWMEntry ::= SEQUENCE {
        cvCallRateWMIntvlDurUnits CvCallVolumeWMIntvlType,
        cvCallRateWMIndex         Unsigned32,
        cvCallRateWMValue         Unsigned32,
        cvCallRateWMts            DateAndTime
}

cvCallRateWMIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeWMIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in call rate Water mark Table to select one
        among four interval-tables.

        The different types in this table are represented by 
        CvCallVolumeWMIntvlType" 
    ::= { cvCallRateWMEntry 1 }

cvCallRateWMIndex OBJECT-TYPE
    SYNTAX          Unsigned32 (1..10)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to different peaks in
        past period in call rate watermark table.

        The number of watermarks entries stored for each table are 
        based on cvCallVolumeWMTableSize" 
    ::= { cvCallRateWMEntry 2 }

cvCallRateWMValue OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates high watermark value achieved by the
        calls per second for the given interval" 
    ::= { cvCallRateWMEntry 3 }

cvCallRateWMts OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates date and Time when the high watermark
        is achieved for calls per second for the given interval" 
    ::= { cvCallRateWMEntry 4 }
 


cvCallLegRateWMTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvCallLegRateWMEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cvCallLegRateWMTable table represents high watermarks achieved
        by call-leg rate in various interval length defined 
        by CvCallVolumeWMIntvlType. 

        Each interval may contain one or more entries to allow for 
        detailed measurement to be collected"
    ::= { cvCallVolumeStatsHistory 7 }

cvCallLegRateWMEntry OBJECT-TYPE
    SYNTAX          CvCallLegRateWMEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvCallLegRateWMTable
        This entry is created at the system initialization and is
        updated whenever 
        a) This entry is obsolete OR
        b) A new/higher entry is available.
        These entries are reinitialised/added/deleted  if
        cvCallVolumeWMTableSize is changed"
    INDEX           {
                        cvCallLegRateWMIntvlDurUnits,
                        cvCallLegRateWMIndex
                    } 
    ::= { cvCallLegRateWMTable 1 }

CvCallLegRateWMEntry ::= SEQUENCE {
        cvCallLegRateWMIntvlDurUnits CvCallVolumeWMIntvlType,
        cvCallLegRateWMIndex         Unsigned32,
        cvCallLegRateWMValue         Unsigned32,
        cvCallLegRateWMts            DateAndTime
}

cvCallLegRateWMIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeWMIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in call leg rate Water mark Table to select
        one
        among four interval-tables.

        The different types in this table are represented by 
        CvCallVolumeWMIntvlType" 
    ::= { cvCallLegRateWMEntry 1 }

cvCallLegRateWMIndex OBJECT-TYPE
    SYNTAX          Unsigned32 (1..10)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to different peaks in
        past period in call leg rate watermark table.

        The number of watermarks entries stored for each table are 
        based on cvCallVolumeWMTableSize" 
    ::= { cvCallLegRateWMEntry 2 }

cvCallLegRateWMValue OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "call legs per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates high watermark value achieved by the
        call legs per second for the given interval" 
    ::= { cvCallLegRateWMEntry 3 }

cvCallLegRateWMts OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates date and time when the high watermark
        is achieved for call-legs per second for the given interval" 
    ::= { cvCallLegRateWMEntry 4 }
 


cvActiveCallWMTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvActiveCallWMEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents high watermarks achieved
        by active calls in various interval length defined 
        by CvCallVolumeWMIntvlType. 

        Each interval may contain one or more entries to allow 
        for detailed measurement to be collected."
    ::= { cvCallVolumeStatsHistory 8 }

cvActiveCallWMEntry OBJECT-TYPE
    SYNTAX          CvActiveCallWMEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvActiveCallWMTable
        This entry is created at the system initialization and is
        updated whenever 
        a) This entry is obsolete OR
        b) A new/higher entry is available.
        These entries are reinitialised/added/deleted  if
        cvCallVolumeWMTableSize is changed"
    INDEX           {
                        cvActiveCallWMIntvlDurUnits,
                        cvActiveCallWMIndex
                    } 
    ::= { cvActiveCallWMTable 1 }

CvActiveCallWMEntry ::= SEQUENCE {
        cvActiveCallWMIntvlDurUnits CvCallVolumeWMIntvlType,
        cvActiveCallWMIndex         Unsigned32,
        cvActiveCallWMValue         Unsigned32,
        cvActiveCallWMts            DateAndTime
}

cvActiveCallWMIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeWMIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in active call Water mark Table to select
        one among four interval-tables.

        The different types in this table are represented by 
        CvCallVolumeWMIntvlType" 
    ::= { cvActiveCallWMEntry 1 }

cvActiveCallWMIndex OBJECT-TYPE
    SYNTAX          Unsigned32 (1..10)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to different peaks in
        past period in acive call watermark table.

        The number of watermarks entries stored for each table are 
        based on cvCallVolumeWMTableSize" 
    ::= { cvActiveCallWMEntry 2 }

cvActiveCallWMValue OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "calls"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates high watermark value achieved by the
        active calls for the given interval" 
    ::= { cvActiveCallWMEntry 3 }

cvActiveCallWMts OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates date and time when the high watermark
        is achieved for active calls for the given interval" 
    ::= { cvActiveCallWMEntry 4 }
 


cvSipMsgRateWMTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CvSipMsgRateWMEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table represents of high watermarks achieved
        by SIP message rate in various interval length defined 
        by CvCallVolumeWMIntvlType. 

        Each interval may contain one or more entries to allow for
        detailed measurement to be collected"
    ::= { cvCallVolumeStatsHistory 9 }

cvSipMsgRateWMEntry OBJECT-TYPE
    SYNTAX          CvSipMsgRateWMEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is a conceptual-row in cvSipMsgRateWMTable.
        This entry is created at the system initialization and is
        updated whenever 
        a) This entry is obsolete OR
        b) A new/higher entry is available.
        These entries are reinitialised/added/deleted if
        cvCallVolumeWMTableSize is changed"
    INDEX           {
                        cvSipMsgRateWMIntvlDurUnits,
                        cvSipMsgRateWMIndex
                    } 
    ::= { cvSipMsgRateWMTable 1 }

CvSipMsgRateWMEntry ::= SEQUENCE {
        cvSipMsgRateWMIntvlDurUnits CvCallVolumeWMIntvlType,
        cvSipMsgRateWMIndex         Unsigned32,
        cvSipMsgRateWMValue         Unsigned32,
        cvSipMsgRateWMts            DateAndTime
}

cvSipMsgRateWMIntvlDurUnits OBJECT-TYPE
    SYNTAX          CvCallVolumeWMIntvlType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Object indexes in SIP Message rate Water mark Table to
        select one among four interval-tables.

        The different types in this table are represented by 
        CvCallVolumeWMIntvlType" 
    ::= { cvSipMsgRateWMEntry 1 }

cvSipMsgRateWMIndex OBJECT-TYPE
    SYNTAX          Unsigned32 (1..10)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is an index that references to different peaks in
        past period in sip message rate watermark table.

        The number of watermarks entries stored for each table are 
        based on cvCallVolumeWMTableSize" 
    ::= { cvSipMsgRateWMEntry 2 }

cvSipMsgRateWMValue OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "SIP messages per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates high watermark value achieved by the
        SIP messages per second for the given interval" 
    ::= { cvSipMsgRateWMEntry 3 }

cvSipMsgRateWMts OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates date and time when the high watermark
        is achieved for SIP messages per second for the given interval" 
    ::= { cvSipMsgRateWMEntry 4 }
 


cvCallDurationStatsThreshold OBJECT-TYPE
    SYNTAX          Unsigned32 (1..3600)
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This Object specifies the thresold duration in seconds.
        cvCallDurationStatsTable will monitor all the calls below this 
        threshold.

        Decresing the value of the threshold will reset this table."
    DEFVAL          { 5 } 
    ::= { cvCallVolumeStatsHistory 10 }

cvCallVolumeWMTableSize OBJECT-TYPE
    SYNTAX          Unsigned32 (3..10)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This Object specifies the number of entries the watermark table
        will maintain.

        This value will decide the number of elements in
        cvCallRateWMTable,
        cvCallLegRateWMTable, cvActiveCallWMTable and
        cvSipMsgRateWMTable."
    DEFVAL          { 5 } 
    ::= { cvCallVolumeStatsHistory 11 }
-- *********************************************************************
-- Notifications
-- *********************************************************************

cvdcMIBNotificationPrefix  OBJECT IDENTIFIER
    ::= { ciscoVoiceDialControlMIB 2 }

cvdcMIBNotifications  OBJECT IDENTIFIER
    ::= { cvdcMIBNotificationPrefix 0 }


cvdcPoorQoVNotification NOTIFICATION-TYPE
    OBJECTS         {
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryIcpif,
                        cvVoIPCallHistoryRemoteIPAddress,
                        cCallHistoryCallOrigin,
                        cvCommonDcCallHistoryCoderTypeRate
                    }
    STATUS          deprecated
    DESCRIPTION
        "Poor quality of voice notification. A
        cvdcPoorQoVNotification is sent at the call disconnection
        time if the value of cvVoIPCallHistoryIcpif exceeds the
        value of cvVoIPPeerCfgIcpif in the call associated peer."
   ::= { cvdcMIBNotifications 1 }

cvdcPoorQoVNotificationRev1 NOTIFICATION-TYPE
    OBJECTS         {
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryIcpif,
                        cvVoIPCallHistoryRemMediaIPAddrT,
                        cvVoIPCallHistoryRemMediaIPAddr,
                        cCallHistoryCallOrigin,
                        cvCommonDcCallHistoryCoderTypeRate
                    }
    STATUS          current
    DESCRIPTION
        "Poor quality of voice notification. A
        cvdcPoorQoVNotificationRev1 is sent at the call
        disconnection time if the value of cvVoIPCallHistoryIcpif
        exceeds the value of cvVoIPPeerCfgIcpif in the call
        associated peer."
   ::= { cvdcMIBNotifications 2 }

cvdcActiveDS0sHighNotification NOTIFICATION-TYPE
    OBJECTS         {
                        cvCallActiveDS0s,
                        cvCallActiveDS0sHighThreshold
                    }
    STATUS          current
    DESCRIPTION
        "The cvdcActiveDS0sHighNotification is sent when the
        percentage of active DS0s exceeds 
        cvCallActiveDS0sHighThreshold value."
   ::= { cvdcMIBNotifications 3 }

cvdcActiveDS0sLowNotification NOTIFICATION-TYPE
    OBJECTS         {
                        cvCallActiveDS0s,
                        cvCallActiveDS0sLowThreshold
                    }
    STATUS          current
    DESCRIPTION
        "The cvdcActiveDS0sLowNotification is sent when the
        percentage of active DS0s is less than 
        cvCallActiveDS0sLowThreshold value."
   ::= { cvdcMIBNotifications 4 }

cvdcFallbackNotification NOTIFICATION-TYPE
    OBJECTS         {
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryFallbackIcpif,
                        cvVoIPCallHistoryFallbackLoss,
                        cvVoIPCallHistoryFallbackDelay,
                        cvVoIPCallHistoryRemSigIPAddrT,
                        cvVoIPCallHistoryRemSigIPAddr,
                        cvVoIPCallHistoryRemMediaIPAddrT,
                        cvVoIPCallHistoryRemMediaIPAddr,
                        cCallHistoryCallOrigin,
                        cvCommonDcCallHistoryCoderTypeRate
                    }
    STATUS          current
    DESCRIPTION
        "This cvdcFallbackNotificaion is sent out,
        when the destination
            IP address of call fallback probe fails. This probe
            generated for beginning of each call, 
            with exception of existing cached entry."
   ::= { cvdcMIBNotifications 5 }

cvdcPolicyViolationNotification NOTIFICATION-TYPE
    OBJECTS         {
                        ccVoIPCallActivePolicyName,
                        cvVoIPCallActiveReversedDirectionPeerAddress,
                        callActivePeerId,
                        callActivePeerAddress
                    }
    STATUS          current
    DESCRIPTION
        "Policy violation voice notification. A
        cvdcPolicyViolationNotification is sent when the call is active
        and has policy violation. It can be media policy violation or
        DSCP policy violation."
   ::= { cvdcMIBNotifications 6 }
cvdcMIBConformance  OBJECT IDENTIFIER
    ::= { ciscoVoiceDialControlMIB 3 }

cvdcMIBCompliances  OBJECT IDENTIFIER
    ::= { cvdcMIBConformance 1 }

cvdcMIBGroups  OBJECT IDENTIFIER
    ::= { cvdcMIBConformance 2 }


-- compliance statements

cvdcMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroup }

    GROUP           cvdcVoiceCfgGroup
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroup
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroup
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroup
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 1 }

cvdcMIBComplianceRev1 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev1 }

    GROUP           cvdcVoiceCfgGroupRev1
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroup
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroup
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev1
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 2 }

cvdcMIBComplianceRev2 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev2 }

    GROUP           cvdcVoiceCfgGroupRev1
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev1
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev1
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev1
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 3 }

cvdcMIBComplianceRev3 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev3 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev1
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev3
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 4 }

cvdcMIBComplianceRev4 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev4 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev1
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev3
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 5 }

cvdcMIBComplianceRev5 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev3 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev2
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev3
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 6 }

cvdcMIBComplianceRev6 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev3 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev2
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev4
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 7 }

cvdcMIBComplianceRev7 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev4 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev2
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev4
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 8 }

cvdcMIBComplianceRev8 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev5 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev3
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev2
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev4
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 9 }

cvdcMIBComplianceRev9 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev5 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev3
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev4
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 10 }

cvdcMIBComplianceRev10 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev5 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev4
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev4
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 11 }

cvdcMIBComplianceRev11 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev5 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev4
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 12 }

cvdcMIBComplianceRev12 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev5 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 13 }

cvdcMIBComplianceRev13 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."
    ::= { cvdcMIBCompliances 14 }

cvdcMIBComplianceRev14 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcNotificationGroupRev3
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."
    ::= { cvdcMIBCompliances 15 }

cvdcMIBComplianceRev15 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcNotificationGroupRev3
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."
    ::= { cvdcMIBCompliances 16 }

cvdcMIBComplianceRev16 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for entities which
        implement the CISCO VOICE DIAL CONTROL MIB"
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcNotificationGroupRev3
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."

    GROUP           cvdcVoIPCfgIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec configuration."

    GROUP           cvVoIPCallIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec."
    ::= { cvdcMIBCompliances 17 }

cvdcMIBComplianceRev17 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the entities
        that implement CISCO-VOICE-DIAL-CONTROL-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcNotificationGroupRev3
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."

    GROUP           cvdcVoIPCfgIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec configuration."

    GROUP           cvVoIPCallIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec."

    GROUP           cvCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track voice-related active
        calls and call-history via GSTN."

    GROUP           cvVoIPCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track VoIP-related active
        calls and call-history via IP network."
    ::= { cvdcMIBCompliances 18 }

cvdcMIBComplianceRev18 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the entities
        that implement CISCO-VOICE-DIAL-CONTROL-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcNotificationGroupRev3
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."

    GROUP           cvdcVoIPCfgIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec configuration."

    GROUP           cvVoIPCallIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec."

    GROUP           cvCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track voice-related active
        calls and call-history via GSTN."

    GROUP           cvVoIPCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track VoIP-related active
        calls and call-history via IP network."

    GROUP           cvCallVolumeGroup
    DESCRIPTION
        "This group is mandatory to track per-protocol active
        calls."

    GROUP           cvCallRateMonitorGroup
    DESCRIPTION
        "This group is mandatory to track call-rate."
    ::= { cvdcMIBCompliances 19 }

cvdcMIBComplianceRev19 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the entities
        that implement CISCO-VOICE-DIAL-CONTROL-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcNotificationGroupRev3
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."

    GROUP           cvdcVoIPCfgIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec configuration."

    GROUP           cvVoIPCallIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec."

    GROUP           cvCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track voice-related active
        calls and call-history via GSTN."

    GROUP           cvVoIPCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track VoIP-related active
        calls and call-history via IP network."

    GROUP           cvCallVolumeGroup
    DESCRIPTION
        "This group is mandatory to track per-protocol active
        calls."

    GROUP           cvCallRateMonitorGroup
    DESCRIPTION
        "This group is mandatory to track call-rate."

    GROUP           cvdcVoIPCfgISACGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which support
        iSAC codec configuration."
    ::= { cvdcMIBCompliances 20 }

cvdcMIBComplianceRev20 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the entities
        that implement CISCO-VOICE-DIAL-CONTROL-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."

    GROUP           cvdcVoIPCfgIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec configuration."

    GROUP           cvVoIPCallIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec."

    GROUP           cvCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track voice-related active
        calls and call-history via GSTN."

    GROUP           cvCallVolumeGroup
    DESCRIPTION
        "This group is mandatory to track per-protocol active
        calls."

    GROUP           cvCallRateMonitorGroup
    DESCRIPTION
        "This group is mandatory to track call-rate."

    GROUP           cvdcVoIPCfgISACGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which support
        iSAC codec configuration."

    GROUP           cvVoIPCallGroupSup2
    DESCRIPTION
        "This group supplements cvVoIPCallGroup to
        provide the call identifier and policy for the VoIP
        leg of the gateway call"

    GROUP           cvdcNotificationGroupRev4
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."
    ::= { cvdcMIBCompliances 21 }

cvdcMIBComplianceRev21 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the entities
        that implement CISCO-VOICE-DIAL-CONTROL-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."

    GROUP           cvdcVoIPCfgIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec configuration."

    GROUP           cvVoIPCallIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec."

    GROUP           cvCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track voice-related active
        calls and call-history via GSTN."

    GROUP           cvCallVolumeGroup
    DESCRIPTION
        "This group is mandatory to track per-protocol active
        calls."

    GROUP           cvCallRateMonitorGroup
    DESCRIPTION
        "This group is mandatory to track call-rate."

    GROUP           cvdcVoIPCfgISACGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which support
        iSAC codec configuration."

    GROUP           cvVoIPCallGroupSup3
    DESCRIPTION
        "This group is mandatory for all VoIP calls.  It supplements
        cvVoIPCallGroup to provide the call identifier and policy for the VoIP
        leg of the gateway call."

    GROUP           cvdcNotificationGroupRev4
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."
    ::= { cvdcMIBCompliances 22 }

cvdcMIBComplianceRev22 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the entities
        that implement CISCO-VOICE-DIAL-CONTROL-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cvdcGeneralCfgGroupRev6 }

    GROUP           cvdcVoiceCfgGroupRev2
    DESCRIPTION
        "This group is mandatory for voice dial control
        configuration which is related to GSTN(General Switched
        Telephony Network).
        GSTN encompasses analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."

    GROUP           cvdcVoIPCfgGroupRev4
    DESCRIPTION
        "This group is mandatory for VoIP dial control configuration
        which is related to transmit voice packet via IP network."

    GROUP           cvCallGroupRev5
    DESCRIPTION
        "This group is mandatory for all voice related active call
        and call history via GSTN."

    GROUP           cvVoIPCallGroupRev6
    DESCRIPTION
        "This group is mandatory for all VoIP related active call
        and call history via IP network."

    GROUP           cvdcVoIPCfgAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec configuration."

    GROUP           cvVoIPCallAmrNbGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support GSM AMR-NB codec."

    GROUP           cvdcVoIPCfgIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec configuration."

    GROUP           cvVoIPCallIlbcGroup
    DESCRIPTION
        "This group is mandatory only for those platforms
        which support iLBC codec."

    GROUP           cvCallGroupSup1
    DESCRIPTION
        "This group is mandatory to track voice-related active
        calls and call-history via GSTN."

    GROUP           cvCallVolumeGroup
    DESCRIPTION
        "This group is mandatory to track per-protocol active
        calls."

    GROUP           cvCallRateMonitorGroup
    DESCRIPTION
        "This group is mandatory to track call-rate."

    GROUP           cvdcVoIPCfgISACGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which support
        iSAC codec configuration."

    GROUP           cvVoIPCallGroupSup3
    DESCRIPTION
        "This group is mandatory for all VoIP calls.  It supplements
        cvVoIPCallGroup to provide the call identifier and policy for the VoIP
        leg of the gateway call."

    GROUP           cvdcNotificationGroupRev4
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."

    GROUP           cvdcCallVolumeStatsHistory
    DESCRIPTION
        "This group is mandatory to track voice related call volume
        stats history"
    ::= { cvdcMIBCompliances 23 }

-- units of conformance

cvdcGeneralCfgGroup OBJECT-GROUP
    OBJECTS         {
                        cvGeneralPoorQoVNotificationEnable,
                        cvPeerCfgIfIndex,
                        cvPeerCfgType,
                        cvPeerCfgRowStatus
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the general
        Voice Gateway Call Dial Control configuration capability.
        cvdcGeneralCfgGroup group is superseded by cvdcGeneralCfgGroupRev1."
    ::= { cvdcMIBGroups 1 }

cvdcVoiceCfgGroup OBJECT-GROUP
    OBJECTS         {
                        cvVoicePeerCfgSessionTarget,
                        cvVoicePeerCfgDialDigitsPrefix
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure.
        cvdcVoiceCfgGroup group is superseded by cvdcVoiceCfgGroupRev1."
    ::= { cvdcMIBGroups 2 }

cvdcVoIPCfgGroup OBJECT-GROUP
    OBJECTS         {
                        cvVoIPPeerCfgSessionProtocol,
                        cvVoIPPeerCfgSessionTarget,
                        cvVoIPPeerCfgCoderRate,
                        cvVoIPPeerCfgFaxRate,
                        cvVoIPPeerCfgDesiredQoS,
                        cvVoIPPeerCfgMinAcceptableQoS,
                        cvVoIPPeerCfgVADEnable,
                        cvVoIPPeerCfgExpectFactor,
                        cvVoIPPeerCfgIcpif,
                        cvVoIPPeerCfgPoorQoVNotificationEnable,
                        cvVoIPPeerCfgUDPChecksumEnable,
                        cvVoIPPeerCfgIPPrecedence
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Dial Control
        configuration capability.
        cvdcVoIPCfgGroup group is superseded by cvdcVoIPCfgGroupRev1."
    ::= { cvdcMIBGroups 3 }

cvCallGroup OBJECT-GROUP
    OBJECTS         {
                        cvCallActiveConnectionId,
                        cvCallActiveTxDuration,
                        cvCallActiveVoiceTxDuration,
                        cvCallActiveFaxTxDuration,
                        cvCallActiveCoderTypeRate,
                        cvCallActiveNoiseLevel,
                        cvCallActiveACOMLevel,
                        cvCallActiveOutSignalLevel,
                        cvCallActiveInSignalLevel,
                        cvCallActiveERLLevel,
                        cvCallActiveSessionTarget,
                        cvCallHistoryConnectionId,
                        cvCallHistoryTxDuration,
                        cvCallHistoryVoiceTxDuration,
                        cvCallHistoryFaxTxDuration,
                        cvCallHistoryCoderTypeRate,
                        cvCallHistoryNoiseLevel,
                        cvCallHistoryACOMLevel,
                        cvCallHistorySessionTarget
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Call capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure.
        cvCallGroup group is superseded by cvCallGroupRev1."
    ::= { cvdcMIBGroups 4 }

cvVoIPCallGroup OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveConnectionId,
                        cvVoIPCallActiveRemoteIPAddress,
                        cvVoIPCallActiveRemoteUDPPort,
                        cvVoIPCallActiveRoundTripDelay,
                        cvVoIPCallActiveSelectedQoS,
                        cvVoIPCallActiveSessionProtocol,
                        cvVoIPCallActiveSessionTarget,
                        cvVoIPCallActiveOnTimeRvPlayout,
                        cvVoIPCallActiveGapFillWithSilence,
                        cvVoIPCallActiveGapFillWithPrediction,
                        cvVoIPCallActiveGapFillWithInterpolation,
                        cvVoIPCallActiveGapFillWithRedundancy,
                        cvVoIPCallActiveHiWaterPlayoutDelay,
                        cvVoIPCallActiveLoWaterPlayoutDelay,
                        cvVoIPCallActiveReceiveDelay,
                        cvVoIPCallActiveVADEnable,
                        cvVoIPCallActiveCoderTypeRate,
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryRemoteIPAddress,
                        cvVoIPCallHistoryRemoteUDPPort,
                        cvVoIPCallHistoryRoundTripDelay,
                        cvVoIPCallHistorySelectedQoS,
                        cvVoIPCallHistorySessionProtocol,
                        cvVoIPCallHistorySessionTarget,
                        cvVoIPCallHistoryOnTimeRvPlayout,
                        cvVoIPCallHistoryGapFillWithSilence,
                        cvVoIPCallHistoryGapFillWithPrediction,
                        cvVoIPCallHistoryGapFillWithInterpolation,
                        cvVoIPCallHistoryGapFillWithRedundancy,
                        cvVoIPCallHistoryHiWaterPlayoutDelay,
                        cvVoIPCallHistoryLoWaterPlayoutDelay,
                        cvVoIPCallHistoryReceiveDelay,
                        cvVoIPCallHistoryVADEnable,
                        cvVoIPCallHistoryCoderTypeRate,
                        cvVoIPCallHistoryIcpif
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Call
        capability.
        cvVoIPCallGroup group is superseded by cvVoIPCallGroupRev1."
    ::= { cvdcMIBGroups 5 }

cvdcGeneralCfgGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        cvGeneralPoorQoVNotificationEnable,
                        cvPeerCfgIfIndex,
                        cvPeerCfgType,
                        cvPeerCfgRowStatus,
                        cvPeerCommonCfgIncomingDnisDigits,
                        cvPeerCommonCfgMaxConnections,
                        cvPeerCommonCfgApplicationName
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the general
        Voice Gateway Call Dial Control configuration capability.
        cvdcGeneralCfgGroupRev1 group is superseded by cvdcGeneralCfgGroupRev2."
    ::= { cvdcMIBGroups 6 }

cvdcVoiceCfgGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        cvVoicePeerCfgSessionTarget,
                        cvVoicePeerCfgDialDigitsPrefix,
                        cvVoicePeerCfgDIDCallEnable,
                        cvVoicePeerCfgCasGroup
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure.
        cvdcVoiceCfgGroupRev1 group is superseded by cvdcVoiceCfgGroupRev2."
    ::= { cvdcMIBGroups 7 }

cvVoIPCallGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveConnectionId,
                        cvVoIPCallActiveRemoteIPAddress,
                        cvVoIPCallActiveRemoteUDPPort,
                        cvVoIPCallActiveRoundTripDelay,
                        cvVoIPCallActiveSelectedQoS,
                        cvVoIPCallActiveSessionProtocol,
                        cvVoIPCallActiveSessionTarget,
                        cvVoIPCallActiveOnTimeRvPlayout,
                        cvVoIPCallActiveGapFillWithSilence,
                        cvVoIPCallActiveGapFillWithPrediction,
                        cvVoIPCallActiveGapFillWithInterpolation,
                        cvVoIPCallActiveGapFillWithRedundancy,
                        cvVoIPCallActiveHiWaterPlayoutDelay,
                        cvVoIPCallActiveLoWaterPlayoutDelay,
                        cvVoIPCallActiveReceiveDelay,
                        cvVoIPCallActiveVADEnable,
                        cvVoIPCallActiveCoderTypeRate,
                        cvVoIPCallActiveLostPackets,
                        cvVoIPCallActiveLatePackets,
                        cvVoIPCallActiveEarlyPackets,
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryRemoteIPAddress,
                        cvVoIPCallHistoryRemoteUDPPort,
                        cvVoIPCallHistoryRoundTripDelay,
                        cvVoIPCallHistorySelectedQoS,
                        cvVoIPCallHistorySessionProtocol,
                        cvVoIPCallHistorySessionTarget,
                        cvVoIPCallHistoryOnTimeRvPlayout,
                        cvVoIPCallHistoryGapFillWithSilence,
                        cvVoIPCallHistoryGapFillWithPrediction,
                        cvVoIPCallHistoryGapFillWithInterpolation,
                        cvVoIPCallHistoryGapFillWithRedundancy,
                        cvVoIPCallHistoryHiWaterPlayoutDelay,
                        cvVoIPCallHistoryLoWaterPlayoutDelay,
                        cvVoIPCallHistoryReceiveDelay,
                        cvVoIPCallHistoryVADEnable,
                        cvVoIPCallHistoryCoderTypeRate,
                        cvVoIPCallHistoryIcpif,
                        cvVoIPCallHistoryLostPackets,
                        cvVoIPCallHistoryLatePackets,
                        cvVoIPCallHistoryEarlyPackets
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Call
        capability.
        cvVoIPCallGroupRev1 group is superseded by cvVoIPCallGroupRev2."
    ::= { cvdcMIBGroups 8 }

cvCallGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        cvCallActiveConnectionId,
                        cvCallActiveTxDuration,
                        cvCallActiveVoiceTxDuration,
                        cvCallActiveFaxTxDuration,
                        cvCallActiveCoderTypeRate,
                        cvCallActiveNoiseLevel,
                        cvCallActiveACOMLevel,
                        cvCallActiveOutSignalLevel,
                        cvCallActiveInSignalLevel,
                        cvCallActiveERLLevel,
                        cvCallActiveSessionTarget,
                        cvCallActiveImgPageCount,
                        cvCallHistoryConnectionId,
                        cvCallHistoryTxDuration,
                        cvCallHistoryVoiceTxDuration,
                        cvCallHistoryFaxTxDuration,
                        cvCallHistoryCoderTypeRate,
                        cvCallHistoryNoiseLevel,
                        cvCallHistoryACOMLevel,
                        cvCallHistorySessionTarget,
                        cvCallHistoryImgPageCount
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure.
        cvCallGroupRev1 group is superseded by cvCallGroupRev2."
    ::= { cvdcMIBGroups 9 }

cvdcGeneralCfgGroupRev2 OBJECT-GROUP
    OBJECTS         {
                        cvGeneralPoorQoVNotificationEnable,
                        cvPeerCfgIfIndex,
                        cvPeerCfgType,
                        cvPeerCfgRowStatus,
                        cvPeerCommonCfgIncomingDnisDigits,
                        cvPeerCommonCfgMaxConnections,
                        cvPeerCommonCfgApplicationName,
                        cvPeerCommonCfgPreference
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the general
        Voice Gateway Call Dial Control configuration capability.
        cvdcGeneralCfgGroupRev2 group is superseded by cvdcGeneralCfgGroupRev3."
    ::= { cvdcMIBGroups 10 }

cvdcVoIPCfgGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPPeerCfgSessionProtocol,
                        cvVoIPPeerCfgSessionTarget,
                        cvVoIPPeerCfgCoderRate,
                        cvVoIPPeerCfgFaxRate,
                        cvVoIPPeerCfgDesiredQoS,
                        cvVoIPPeerCfgMinAcceptableQoS,
                        cvVoIPPeerCfgVADEnable,
                        cvVoIPPeerCfgExpectFactor,
                        cvVoIPPeerCfgIcpif,
                        cvVoIPPeerCfgPoorQoVNotificationEnable,
                        cvVoIPPeerCfgUDPChecksumEnable,
                        cvVoIPPeerCfgIPPrecedence,
                        cvVoIPPeerCfgTechPrefix,
                        cvVoIPPeerCfgDigitRelay
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Dial Control
        configuration capability.
        cvdcVoIPCfgGroupRev1 group is superseded by cvdcVoIPCfgGroupRev2."
    ::= { cvdcMIBGroups 11 }

cvdcGeneralCfgGroupRev3 OBJECT-GROUP
    OBJECTS         {
                        cvGeneralPoorQoVNotificationEnable,
                        cvPeerCfgIfIndex,
                        cvPeerCfgType,
                        cvPeerCfgRowStatus,
                        cvPeerCommonCfgIncomingDnisDigits,
                        cvPeerCommonCfgMaxConnections,
                        cvPeerCommonCfgApplicationName,
                        cvPeerCommonCfgPreference,
                        cvPeerCommonCfgHuntStop
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the general
        Voice Gateway Call Dial Control configuration capability.
        cvdcGeneralCfgGroupRev3 group is superseded by cvdcGeneralCfgGroupRev4."
    ::= { cvdcMIBGroups 12 }

cvdcVoiceCfgGroupRev2 OBJECT-GROUP
    OBJECTS         {
                        cvVoicePeerCfgSessionTarget,
                        cvVoicePeerCfgDialDigitsPrefix,
                        cvVoicePeerCfgDIDCallEnable,
                        cvVoicePeerCfgCasGroup,
                        cvVoicePeerCfgRegisterE164,
                        cvVoicePeerCfgForwardDigits,
                        cvVoicePeerCfgEchoCancellerTest
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."
    ::= { cvdcMIBGroups 13 }

cvdcVoIPCfgGroupRev2 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPPeerCfgSessionProtocol,
                        cvVoIPPeerCfgSessionTarget,
                        cvVoIPPeerCfgCoderRate,
                        cvVoIPPeerCfgFaxRate,
                        cvVoIPPeerCfgDesiredQoS,
                        cvVoIPPeerCfgMinAcceptableQoS,
                        cvVoIPPeerCfgVADEnable,
                        cvVoIPPeerCfgExpectFactor,
                        cvVoIPPeerCfgIcpif,
                        cvVoIPPeerCfgPoorQoVNotificationEnable,
                        cvVoIPPeerCfgUDPChecksumEnable,
                        cvVoIPPeerCfgIPPrecedence,
                        cvVoIPPeerCfgTechPrefix,
                        cvVoIPPeerCfgDigitRelay,
                        cvVoIPPeerCfgCoderBytes,
                        cvVoIPPeerCfgFaxBytes,
                        cvVoIPPeerCfgInBandSignaling
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Dial Control
        configuration capability.
        cvdcVoIPCfgGroupRev2 group is superseded by cvdcVoIPCfgGroupRev3."
    ::= { cvdcMIBGroups 14 }

cvVoIPCallGroupRev3 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveConnectionId,
                        cvVoIPCallActiveRemoteIPAddress,
                        cvVoIPCallActiveRemoteUDPPort,
                        cvVoIPCallActiveRoundTripDelay,
                        cvVoIPCallActiveSelectedQoS,
                        cvVoIPCallActiveSessionProtocol,
                        cvVoIPCallActiveSessionTarget,
                        cvVoIPCallActiveOnTimeRvPlayout,
                        cvVoIPCallActiveGapFillWithSilence,
                        cvVoIPCallActiveGapFillWithPrediction,
                        cvVoIPCallActiveGapFillWithInterpolation,
                        cvVoIPCallActiveGapFillWithRedundancy,
                        cvVoIPCallActiveHiWaterPlayoutDelay,
                        cvVoIPCallActiveLoWaterPlayoutDelay,
                        cvVoIPCallActiveReceiveDelay,
                        cvVoIPCallActiveLostPackets,
                        cvVoIPCallActiveLatePackets,
                        cvVoIPCallActiveEarlyPackets,
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryRemoteIPAddress,
                        cvVoIPCallHistoryRemoteUDPPort,
                        cvVoIPCallHistoryRoundTripDelay,
                        cvVoIPCallHistorySelectedQoS,
                        cvVoIPCallHistorySessionProtocol,
                        cvVoIPCallHistorySessionTarget,
                        cvVoIPCallHistoryOnTimeRvPlayout,
                        cvVoIPCallHistoryGapFillWithSilence,
                        cvVoIPCallHistoryGapFillWithPrediction,
                        cvVoIPCallHistoryGapFillWithInterpolation,
                        cvVoIPCallHistoryGapFillWithRedundancy,
                        cvVoIPCallHistoryHiWaterPlayoutDelay,
                        cvVoIPCallHistoryLoWaterPlayoutDelay,
                        cvVoIPCallHistoryReceiveDelay,
                        cvVoIPCallHistoryIcpif,
                        cvVoIPCallHistoryLostPackets,
                        cvVoIPCallHistoryLatePackets,
                        cvVoIPCallHistoryEarlyPackets
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Call
        capability.
        cvVoIPCallGroupRev3 group is superseded by cvVoIPCallGroupRev4."
    ::= { cvdcMIBGroups 15 }

cvdcNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS    { cvdcPoorQoVNotification }
    STATUS          deprecated
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB
        cvdcNotificationGroup group is superseded by cvdcNotificationGroupRev1."
    ::= { cvdcMIBGroups 16 }

cvCallGroupRev2 OBJECT-GROUP
    OBJECTS         {
                        cvCallActiveConnectionId,
                        cvCallActiveTxDuration,
                        cvCallActiveVoiceTxDuration,
                        cvCallActiveFaxTxDuration,
                        cvCallActiveCoderTypeRate,
                        cvCallActiveNoiseLevel,
                        cvCallActiveACOMLevel,
                        cvCallActiveOutSignalLevel,
                        cvCallActiveInSignalLevel,
                        cvCallActiveERLLevel,
                        cvCallActiveSessionTarget,
                        cvCallActiveImgPageCount,
                        cvCallActiveCallingName,
                        cvCallActiveCallerIDBlock,
                        cvCallActiveEcanReflectorLocation,
                        cvCallHistoryConnectionId,
                        cvCallHistoryTxDuration,
                        cvCallHistoryVoiceTxDuration,
                        cvCallHistoryFaxTxDuration,
                        cvCallHistoryCoderTypeRate,
                        cvCallHistoryNoiseLevel,
                        cvCallHistoryACOMLevel,
                        cvCallHistorySessionTarget,
                        cvCallHistoryImgPageCount,
                        cvCallHistoryCallingName,
                        cvCallHistoryCallerIDBlock
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure.
        cvCallGroupRev2 group is superseded by cvCallGroupRev3."
    ::= { cvdcMIBGroups 17 }

cvdcGeneralCfgGroupRev4 OBJECT-GROUP
    OBJECTS         {
                        cvGeneralPoorQoVNotificationEnable,
                        cvPeerCfgIfIndex,
                        cvPeerCfgType,
                        cvPeerCfgRowStatus,
                        cvPeerCommonCfgIncomingDnisDigits,
                        cvPeerCommonCfgMaxConnections,
                        cvPeerCommonCfgApplicationName,
                        cvPeerCommonCfgPreference,
                        cvPeerCommonCfgHuntStop,
                        cvPeerCommonCfgDnisMappingName,
                        cvPeerCommonCfgSourceCarrierId,
                        cvPeerCommonCfgTargetCarrierId,
                        cvPeerCommonCfgSourceTrunkGrpLabel,
                        cvPeerCommonCfgTargetTrunkGrpLabel
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the general
        Voice Gateway Call Dial Control configuration
        capability.
        cvdcGeneralCfgGroupRev4 group is superseded by cvdcGeneralCfgGroupRev5."
    ::= { cvdcMIBGroups 18 }

cvVoIPCallGroupRev4 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveConnectionId,
                        cvVoIPCallActiveRoundTripDelay,
                        cvVoIPCallActiveSelectedQoS,
                        cvVoIPCallActiveSessionProtocol,
                        cvVoIPCallActiveSessionTarget,
                        cvVoIPCallActiveOnTimeRvPlayout,
                        cvVoIPCallActiveGapFillWithSilence,
                        cvVoIPCallActiveGapFillWithPrediction,
                        cvVoIPCallActiveGapFillWithInterpolation,
                        cvVoIPCallActiveGapFillWithRedundancy,
                        cvVoIPCallActiveHiWaterPlayoutDelay,
                        cvVoIPCallActiveLoWaterPlayoutDelay,
                        cvVoIPCallActiveReceiveDelay,
                        cvVoIPCallActiveLostPackets,
                        cvVoIPCallActiveLatePackets,
                        cvVoIPCallActiveEarlyPackets,
                        cvVoIPCallActiveUsername,
                        cvVoIPCallActiveProtocolCallId,
                        cvVoIPCallActiveRemSigIPAddrT,
                        cvVoIPCallActiveRemSigIPAddr,
                        cvVoIPCallActiveRemSigPort,
                        cvVoIPCallActiveRemMediaIPAddrT,
                        cvVoIPCallActiveRemMediaIPAddr,
                        cvVoIPCallActiveRemMediaPort,
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryRoundTripDelay,
                        cvVoIPCallHistorySelectedQoS,
                        cvVoIPCallHistorySessionProtocol,
                        cvVoIPCallHistorySessionTarget,
                        cvVoIPCallHistoryOnTimeRvPlayout,
                        cvVoIPCallHistoryGapFillWithSilence,
                        cvVoIPCallHistoryGapFillWithPrediction,
                        cvVoIPCallHistoryGapFillWithInterpolation,
                        cvVoIPCallHistoryGapFillWithRedundancy,
                        cvVoIPCallHistoryHiWaterPlayoutDelay,
                        cvVoIPCallHistoryLoWaterPlayoutDelay,
                        cvVoIPCallHistoryReceiveDelay,
                        cvVoIPCallHistoryIcpif,
                        cvVoIPCallHistoryLostPackets,
                        cvVoIPCallHistoryLatePackets,
                        cvVoIPCallHistoryEarlyPackets,
                        cvVoIPCallHistoryUsername,
                        cvVoIPCallHistoryProtocolCallId,
                        cvVoIPCallHistoryRemSigIPAddrT,
                        cvVoIPCallHistoryRemSigIPAddr,
                        cvVoIPCallHistoryRemSigPort,
                        cvVoIPCallHistoryRemMediaIPAddrT,
                        cvVoIPCallHistoryRemMediaIPAddr,
                        cvVoIPCallHistoryRemMediaPort
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Call
        capability.
        cvVoIPCallGroupRev4 group is superseded by cvVoIPCallGroupRev5."
    ::= { cvdcMIBGroups 19 }

cvdcNotificationGroupRev1 NOTIFICATION-GROUP
   NOTIFICATIONS    { cvdcPoorQoVNotificationRev1 }
    STATUS          deprecated
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB
        cvdcNotificationGroupRev1 group is superseded by cvdcNotificationGroupRev2."
    ::= { cvdcMIBGroups 20 }

cvdcVoIPCfgGroupRev3 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPPeerCfgSessionProtocol,
                        cvVoIPPeerCfgSessionTarget,
                        cvVoIPPeerCfgCoderRate,
                        cvVoIPPeerCfgFaxRate,
                        cvVoIPPeerCfgDesiredQoS,
                        cvVoIPPeerCfgMinAcceptableQoS,
                        cvVoIPPeerCfgVADEnable,
                        cvVoIPPeerCfgExpectFactor,
                        cvVoIPPeerCfgIcpif,
                        cvVoIPPeerCfgPoorQoVNotificationEnable,
                        cvVoIPPeerCfgUDPChecksumEnable,
                        cvVoIPPeerCfgIPPrecedence,
                        cvVoIPPeerCfgTechPrefix,
                        cvVoIPPeerCfgDigitRelay,
                        cvVoIPPeerCfgCoderBytes,
                        cvVoIPPeerCfgFaxBytes,
                        cvVoIPPeerCfgInBandSignaling
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Dial Control
        configuration capability.
        cvdcVoIPCfgGroupRev3 group is superseded by cvdcVoIPCfgGroupRev4."
    ::= { cvdcMIBGroups 21 }

cvdcGeneralCfgGroupRev5 OBJECT-GROUP
    OBJECTS         {
                        cvGeneralPoorQoVNotificationEnable,
                        cvPeerCfgIfIndex,
                        cvPeerCfgType,
                        cvPeerCfgRowStatus,
                        cvPeerCfgPeerType,
                        cvPeerCommonCfgIncomingDnisDigits,
                        cvPeerCommonCfgMaxConnections,
                        cvPeerCommonCfgApplicationName,
                        cvPeerCommonCfgPreference,
                        cvPeerCommonCfgHuntStop,
                        cvPeerCommonCfgDnisMappingName,
                        cvPeerCommonCfgSourceCarrierId,
                        cvPeerCommonCfgTargetCarrierId,
                        cvPeerCommonCfgSourceTrunkGrpLabel,
                        cvPeerCommonCfgTargetTrunkGrpLabel
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the general
        Voice Gateway Call Dial Control configuration
        capability.
        cvdcGeneralCfgGroupRev5 group is superseded by cvdcGeneralCfgGroupRev6."
    ::= { cvdcMIBGroups 22 }

cvdcVoIPCfgGroupRev4 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPPeerCfgSessionProtocol,
                        cvVoIPPeerCfgSessionTarget,
                        cvVoIPPeerCfgCoderRate,
                        cvVoIPPeerCfgFaxRate,
                        cvVoIPPeerCfgDesiredQoS,
                        cvVoIPPeerCfgMinAcceptableQoS,
                        cvVoIPPeerCfgVADEnable,
                        cvVoIPPeerCfgExpectFactor,
                        cvVoIPPeerCfgIcpif,
                        cvVoIPPeerCfgPoorQoVNotificationEnable,
                        cvVoIPPeerCfgUDPChecksumEnable,
                        cvVoIPPeerCfgIPPrecedence,
                        cvVoIPPeerCfgTechPrefix,
                        cvVoIPPeerCfgDigitRelay,
                        cvVoIPPeerCfgCoderBytes,
                        cvVoIPPeerCfgFaxBytes,
                        cvVoIPPeerCfgInBandSignaling,
                        cvVoIPPeerCfgMediaSetting,
                        cvVoIPPeerCfgDesiredQoSVideo,
                        cvVoIPPeerCfgMinAcceptableQoSVideo,
                        cvVoIPPeerCfgRedirectip2ip,
                        cvVoIPPeerCfgDSCPPolicyNotificationEnable,
                        cvVoIPPeerCfgMediaPolicyNotificationEnable
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the VoIP Dial Control
        configuration capability."
    ::= { cvdcMIBGroups 23 }

cvCallGroupRev3 OBJECT-GROUP
    OBJECTS         {
                        cvCallActiveConnectionId,
                        cvCallActiveTxDuration,
                        cvCallActiveVoiceTxDuration,
                        cvCallActiveFaxTxDuration,
                        cvCallActiveCoderTypeRate,
                        cvCallActiveNoiseLevel,
                        cvCallActiveACOMLevel,
                        cvCallActiveOutSignalLevel,
                        cvCallActiveInSignalLevel,
                        cvCallActiveERLLevel,
                        cvCallActiveSessionTarget,
                        cvCallActiveImgPageCount,
                        cvCallActiveCallingName,
                        cvCallActiveCallerIDBlock,
                        cvCallActiveEcanReflectorLocation,
                        cvCallActiveAccountCode,
                        cvCallHistoryConnectionId,
                        cvCallHistoryTxDuration,
                        cvCallHistoryVoiceTxDuration,
                        cvCallHistoryFaxTxDuration,
                        cvCallHistoryCoderTypeRate,
                        cvCallHistoryNoiseLevel,
                        cvCallHistoryACOMLevel,
                        cvCallHistorySessionTarget,
                        cvCallHistoryImgPageCount,
                        cvCallHistoryCallingName,
                        cvCallHistoryCallerIDBlock,
                        cvCallHistoryAccountCode,
                        cvCallActiveERLLevelRev1
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure.
        cvCallGroupRev3 group is superseded by cvCallGroupRev4."
    ::= { cvdcMIBGroups 24 }

cvCallGroupRev4 OBJECT-GROUP
    OBJECTS         {
                        cvCallActiveConnectionId,
                        cvCallActiveTxDuration,
                        cvCallActiveVoiceTxDuration,
                        cvCallActiveFaxTxDuration,
                        cvCallActiveCoderTypeRate,
                        cvCallActiveNoiseLevel,
                        cvCallActiveACOMLevel,
                        cvCallActiveOutSignalLevel,
                        cvCallActiveInSignalLevel,
                        cvCallActiveSessionTarget,
                        cvCallActiveImgPageCount,
                        cvCallActiveCallingName,
                        cvCallActiveCallerIDBlock,
                        cvCallActiveEcanReflectorLocation,
                        cvCallActiveAccountCode,
                        cvCallHistoryConnectionId,
                        cvCallHistoryTxDuration,
                        cvCallHistoryVoiceTxDuration,
                        cvCallHistoryFaxTxDuration,
                        cvCallHistoryCoderTypeRate,
                        cvCallHistoryNoiseLevel,
                        cvCallHistoryACOMLevel,
                        cvCallHistorySessionTarget,
                        cvCallHistoryImgPageCount,
                        cvCallHistoryCallingName,
                        cvCallHistoryCallerIDBlock,
                        cvCallHistoryAccountCode,
                        cvCallActiveERLLevelRev1
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure.
        cvCallGroupRev4 group is superseded by cvCallGroupRev5."
    ::= { cvdcMIBGroups 25 }

cvVoIPCallGroupRev5 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveConnectionId,
                        cvVoIPCallActiveRoundTripDelay,
                        cvVoIPCallActiveSelectedQoS,
                        cvVoIPCallActiveSessionProtocol,
                        cvVoIPCallActiveSessionTarget,
                        cvVoIPCallActiveOnTimeRvPlayout,
                        cvVoIPCallActiveGapFillWithSilence,
                        cvVoIPCallActiveGapFillWithPrediction,
                        cvVoIPCallActiveGapFillWithInterpolation,
                        cvVoIPCallActiveGapFillWithRedundancy,
                        cvVoIPCallActiveHiWaterPlayoutDelay,
                        cvVoIPCallActiveLoWaterPlayoutDelay,
                        cvVoIPCallActiveReceiveDelay,
                        cvVoIPCallActiveLostPackets,
                        cvVoIPCallActiveLatePackets,
                        cvVoIPCallActiveEarlyPackets,
                        cvVoIPCallActiveUsername,
                        cvVoIPCallActiveProtocolCallId,
                        cvVoIPCallActiveRemSigIPAddrT,
                        cvVoIPCallActiveRemSigIPAddr,
                        cvVoIPCallActiveRemSigPort,
                        cvVoIPCallActiveRemMediaIPAddrT,
                        cvVoIPCallActiveRemMediaIPAddr,
                        cvVoIPCallActiveRemMediaPort,
                        cvVoIPCallActiveSRTPEnable,
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryRoundTripDelay,
                        cvVoIPCallHistorySelectedQoS,
                        cvVoIPCallHistorySessionProtocol,
                        cvVoIPCallHistorySessionTarget,
                        cvVoIPCallHistoryOnTimeRvPlayout,
                        cvVoIPCallHistoryGapFillWithSilence,
                        cvVoIPCallHistoryGapFillWithPrediction,
                        cvVoIPCallHistoryGapFillWithInterpolation,
                        cvVoIPCallHistoryGapFillWithRedundancy,
                        cvVoIPCallHistoryHiWaterPlayoutDelay,
                        cvVoIPCallHistoryLoWaterPlayoutDelay,
                        cvVoIPCallHistoryReceiveDelay,
                        cvVoIPCallHistoryIcpif,
                        cvVoIPCallHistoryLostPackets,
                        cvVoIPCallHistoryLatePackets,
                        cvVoIPCallHistoryEarlyPackets,
                        cvVoIPCallHistoryUsername,
                        cvVoIPCallHistoryProtocolCallId,
                        cvVoIPCallHistoryRemSigIPAddrT,
                        cvVoIPCallHistoryRemSigIPAddr,
                        cvVoIPCallHistoryRemSigPort,
                        cvVoIPCallHistoryRemMediaIPAddrT,
                        cvVoIPCallHistoryRemMediaIPAddr,
                        cvVoIPCallHistoryRemMediaPort,
                        cvVoIPCallHistorySRTPEnable
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the VoIP Call
        capability.
        cvVoIPCallGroupRev5 group is superseded by cvVoIPCallGroupRev6."
    ::= { cvdcMIBGroups 26 }

cvCallGroupRev5 OBJECT-GROUP
    OBJECTS         {
                        cvCallActiveDS0s,
                        cvCallActiveDS0sHighThreshold,
                        cvCallActiveDS0sLowThreshold,
                        cvCallActiveDS0sHighNotifyEnable,
                        cvCallActiveDS0sLowNotifyEnable,
                        cvCallActiveConnectionId,
                        cvCallActiveTxDuration,
                        cvCallActiveVoiceTxDuration,
                        cvCallActiveFaxTxDuration,
                        cvCallActiveCoderTypeRate,
                        cvCallActiveNoiseLevel,
                        cvCallActiveACOMLevel,
                        cvCallActiveOutSignalLevel,
                        cvCallActiveInSignalLevel,
                        cvCallActiveSessionTarget,
                        cvCallActiveImgPageCount,
                        cvCallActiveCallingName,
                        cvCallActiveCallerIDBlock,
                        cvCallActiveEcanReflectorLocation,
                        cvCallActiveAccountCode,
                        cvCallHistoryConnectionId,
                        cvCallHistoryTxDuration,
                        cvCallHistoryVoiceTxDuration,
                        cvCallHistoryFaxTxDuration,
                        cvCallHistoryCoderTypeRate,
                        cvCallHistoryNoiseLevel,
                        cvCallHistoryACOMLevel,
                        cvCallHistorySessionTarget,
                        cvCallHistoryImgPageCount,
                        cvCallHistoryCallingName,
                        cvCallHistoryCallerIDBlock,
                        cvCallHistoryAccountCode,
                        cvCallActiveERLLevelRev1
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the GSTN (General
        Switched Telephony Network Voice Dial Control configuration
        capability.
        GSTN (General Switched Telephony Network), which is
        supposed to encompass analog, ISDN, Cellular - essentially
        anything circuit switched over the TDM infrastructure."
    ::= { cvdcMIBGroups 27 }

cvdcNotificationGroupRev2 NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        cvdcPoorQoVNotificationRev1,
                        cvdcActiveDS0sHighNotification,
                        cvdcActiveDS0sLowNotification
                    }
    STATUS          deprecated
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB
        cvdcNotificationGroupRev2 group is superseded by cvdcNotificationGroupRev3."
    ::= { cvdcMIBGroups 28 }

cvVoIPCallGroupRev6 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveConnectionId,
                        cvVoIPCallActiveRoundTripDelay,
                        cvVoIPCallActiveSelectedQoS,
                        cvVoIPCallActiveSessionProtocol,
                        cvVoIPCallActiveSessionTarget,
                        cvVoIPCallActiveOnTimeRvPlayout,
                        cvVoIPCallActiveGapFillWithSilence,
                        cvVoIPCallActiveGapFillWithPrediction,
                        cvVoIPCallActiveGapFillWithInterpolation,
                        cvVoIPCallActiveGapFillWithRedundancy,
                        cvVoIPCallActiveHiWaterPlayoutDelay,
                        cvVoIPCallActiveLoWaterPlayoutDelay,
                        cvVoIPCallActiveReceiveDelay,
                        cvVoIPCallActiveLostPackets,
                        cvVoIPCallActiveLatePackets,
                        cvVoIPCallActiveEarlyPackets,
                        cvVoIPCallActiveUsername,
                        cvVoIPCallActiveProtocolCallId,
                        cvVoIPCallActiveRemSigIPAddrT,
                        cvVoIPCallActiveRemSigIPAddr,
                        cvVoIPCallActiveRemSigPort,
                        cvVoIPCallActiveRemMediaIPAddrT,
                        cvVoIPCallActiveRemMediaIPAddr,
                        cvVoIPCallActiveRemMediaPort,
                        cvVoIPCallActiveSRTPEnable,
                        cvVoIPCallHistoryConnectionId,
                        cvVoIPCallHistoryRoundTripDelay,
                        cvVoIPCallHistorySelectedQoS,
                        cvVoIPCallHistorySessionProtocol,
                        cvVoIPCallHistorySessionTarget,
                        cvVoIPCallHistoryOnTimeRvPlayout,
                        cvVoIPCallHistoryGapFillWithSilence,
                        cvVoIPCallHistoryGapFillWithPrediction,
                        cvVoIPCallHistoryGapFillWithInterpolation,
                        cvVoIPCallHistoryGapFillWithRedundancy,
                        cvVoIPCallHistoryHiWaterPlayoutDelay,
                        cvVoIPCallHistoryLoWaterPlayoutDelay,
                        cvVoIPCallHistoryReceiveDelay,
                        cvVoIPCallHistoryIcpif,
                        cvVoIPCallHistoryLostPackets,
                        cvVoIPCallHistoryLatePackets,
                        cvVoIPCallHistoryEarlyPackets,
                        cvVoIPCallHistoryUsername,
                        cvVoIPCallHistoryProtocolCallId,
                        cvVoIPCallHistoryRemSigIPAddrT,
                        cvVoIPCallHistoryRemSigIPAddr,
                        cvVoIPCallHistoryRemSigPort,
                        cvVoIPCallHistoryRemMediaIPAddrT,
                        cvVoIPCallHistoryRemMediaIPAddr,
                        cvVoIPCallHistoryRemMediaPort,
                        cvVoIPCallHistorySRTPEnable,
                        cvVoIPCallHistoryFallbackIcpif,
                        cvVoIPCallHistoryFallbackLoss,
                        cvVoIPCallHistoryFallbackDelay
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the VoIP Call
        capability."
    ::= { cvdcMIBGroups 29 }

cvdcGeneralCfgGroupRev6 OBJECT-GROUP
    OBJECTS         {
                        cvGeneralPoorQoVNotificationEnable,
                        cvGeneralFallbackNotificationEnable,
                        cvPeerCfgIfIndex,
                        cvPeerCfgType,
                        cvPeerCfgRowStatus,
                        cvPeerCfgPeerType,
                        cvPeerCommonCfgIncomingDnisDigits,
                        cvPeerCommonCfgMaxConnections,
                        cvPeerCommonCfgApplicationName,
                        cvPeerCommonCfgPreference,
                        cvPeerCommonCfgHuntStop,
                        cvPeerCommonCfgDnisMappingName,
                        cvPeerCommonCfgSourceCarrierId,
                        cvPeerCommonCfgTargetCarrierId,
                        cvPeerCommonCfgSourceTrunkGrpLabel,
                        cvPeerCommonCfgTargetTrunkGrpLabel,
                        cvGeneralDSCPPolicyNotificationEnable,
                        cvGeneralMediaPolicyNotificationEnable
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the general
        Voice Gateway Call Dial Control configuration
        capability."
    ::= { cvdcMIBGroups 30 }

cvdcNotificationGroupRev3 NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        cvdcPoorQoVNotificationRev1,
                        cvdcActiveDS0sHighNotification,
                        cvdcActiveDS0sLowNotification,
                        cvdcFallbackNotification
                    }
    STATUS          deprecated
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB.

        This group  cvdcNotificationGroupRev3 is deprecated and replaced
        by cvdcNotificationGroupRev4.
        cvdcNotificationGroupRev3 group is superseded by cvdcNotificationGroupRev4."
    ::= { cvdcMIBGroups 31 }

cvdcVoIPCfgAmrNbGroup OBJECT-GROUP
    OBJECTS         {
                        cvVoIPPeerCfgOctetAligned,
                        cvVoIPPeerCfgBitRates,
                        cvVoIPPeerCfgCRC
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the VoIP Dial Control
        configuration capability for GSM AMR-NB codec."
    ::= { cvdcMIBGroups 32 }

cvVoIPCallAmrNbGroup OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveOctetAligned,
                        cvVoIPCallActiveBitRates,
                        cvVoIPCallActiveModeChgPeriod,
                        cvVoIPCallActiveModeChgNeighbor,
                        cvVoIPCallActiveMaxPtime,
                        cvVoIPCallActiveCRC,
                        cvVoIPCallActiveRobustSorting,
                        cvVoIPCallActiveEncap,
                        cvVoIPCallActiveInterleaving,
                        cvVoIPCallActivePtime,
                        cvVoIPCallActiveChannels,
                        cvVoIPCallHistoryOctetAligned,
                        cvVoIPCallHistoryBitRates,
                        cvVoIPCallHistoryModeChgPeriod,
                        cvVoIPCallHistoryModeChgNeighbor,
                        cvVoIPCallHistoryMaxPtime,
                        cvVoIPCallHistoryCRC,
                        cvVoIPCallHistoryRobustSorting,
                        cvVoIPCallHistoryEncap,
                        cvVoIPCallHistoryInterleaving,
                        cvVoIPCallHistoryPtime,
                        cvVoIPCallHistoryChannels
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing GSM AMR-NB
        codec related information."
    ::= { cvdcMIBGroups 33 }

cvdcVoIPCfgIlbcGroup OBJECT-GROUP
    OBJECTS         { cvVoIPPeerCfgCoderMode }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the VoIP Dial Control
        configuration capability for iLBC codec."
    ::= { cvdcMIBGroups 34 }

cvVoIPCallIlbcGroup OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveCoderMode,
                        cvVoIPCallHistoryCoderMode
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing iLBC
        codec related information."
    ::= { cvdcMIBGroups 35 }

cvCallGroupSup1 OBJECT-GROUP
    OBJECTS         {
                        cvCallActiveCallId,
                        cvCallHistoryCallId
                    }
    STATUS          current
    DESCRIPTION
        "This group supplements cvCallGroup to provide
        the call identifier for the telephony leg of
        the gateway call."
    ::= { cvdcMIBGroups 36 }

cvVoIPCallGroupSup1 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveCallId,
                        cvVoIPCallHistoryCallId,
                        cvVoIPCallActiveCallReferenceId,
                        cvVoIPCallHistoryCallReferenceId
                    }
    STATUS          deprecated
    DESCRIPTION
        "This group supplements cvVoIPCallGroup to
        provide the call identifier for the VoIP
        leg of the gateway call.

        The group cvVoIPCallGroupSup1 is deprecated and replaced by
        cvVoIPCallGroupSup2."
    ::= { cvdcMIBGroups 37 }

cvCallVolumeGroup OBJECT-GROUP
    OBJECTS         {
                        cvCallVolConnActiveConnection,
                        cvCallVolConnTotalActiveConnections,
                        cvCallVolConnMaxCallConnectionLicenese,
                        cvCallVolPeerIncomingCalls,
                        cvCallVolPeerOutgoingCalls,
                        cvCallVolMediaIncomingCalls,
                        cvCallVolMediaOutgoingCalls
                    }
    STATUS          current
    DESCRIPTION
        "This group provides the information about the call-volume
        in the voice gateway."
    ::= { cvdcMIBGroups 38 }

cvCallRateMonitorGroup OBJECT-GROUP
    OBJECTS         {
                        cvCallRateMonitorEnable,
                        cvCallRateMonitorTime,
                        cvCallRate,
                        cvCallRateHiWaterMark
                    }
    STATUS          current
    DESCRIPTION
        "This group represents the objects used to
        monitor the call-rate."
    ::= { cvdcMIBGroups 39 }

cvdcVoIPCfgISACGroup OBJECT-GROUP
    OBJECTS         {
                        cvVoIPPeerCfgCodingMode,
                        cvVoIPPeerCfgBitRate,
                        cvVoIPPeerCfgFrameSize
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing iSAC codec related
        information."
    ::= { cvdcMIBGroups 40 }

cvdcNotificationGroupRev4 NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        cvdcPoorQoVNotificationRev1,
                        cvdcActiveDS0sHighNotification,
                        cvdcActiveDS0sLowNotification,
                        cvdcFallbackNotification,
                        cvdcPolicyViolationNotification
                    }
    STATUS          current
    DESCRIPTION
        "The notifications for the CISCO-VOICE-DIAL-CONTROL-MIB."
    ::= { cvdcMIBGroups 41 }

cvVoIPCallGroupSup2 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveReversedDirectionPeerAddress,
                        ccVoIPCallActivePolicyName,
                        cvVoIPCallActiveCallId,
                        cvVoIPCallActiveCallReferenceId,
                        cvVoIPCallHistoryCallId,
                        cvVoIPCallHistoryCallReferenceId
                    }
    STATUS          deprecated
    DESCRIPTION
        "This group supplements cvVoIPCallGroup to provide the call
        identifier and policy for the VoIP leg of the gateway call.
        cvVoIPCallGroupSup2 group is superseded by cvVoIPCallGroupSup3."
    ::= { cvdcMIBGroups 42 }

cvVoIPCallGroupSup3 OBJECT-GROUP
    OBJECTS         {
                        cvVoIPCallActiveReversedDirectionPeerAddress,
                        ccVoIPCallActivePolicyName,
                        cvVoIPCallActiveCallId,
                        cvVoIPCallActiveCallReferenceId,
                        cvVoIPCallActiveSessionId,
                        cvVoIPCallHistoryCallId,
                        cvVoIPCallHistoryCallReferenceId,
                        cvVoIPCallHistorySessionId
                    }
    STATUS          current
    DESCRIPTION
        "This group supplements cvVoIPCallGroup to identify the VoIP leg
        of the gateway call."
    ::= { cvdcMIBGroups 43 }

cvdcCallVolumeStatsHistory OBJECT-GROUP
    OBJECTS         {
                        cvCallRateStatsMaxVal,
                        cvCallRateStatsAvgVal,
                        cvCallLegRateStatsMaxVal,
                        cvCallLegRateStatsAvgVal,
                        cvActiveCallStatsMaxVal,
                        cvActiveCallStatsAvgVal,
                        cvCallDurationStatsMaxVal,
                        cvCallDurationStatsAvgVal,
                        cvSipMsgRateStatsMaxVal,
                        cvSipMsgRateStatsAvgVal,
                        cvCallRateWMValue,
                        cvCallRateWMts,
                        cvActiveCallWMValue,
                        cvActiveCallWMts,
                        cvSipMsgRateWMValue,
                        cvSipMsgRateWMts,
                        cvCallDurationStatsThreshold,
                        cvCallVolumeWMTableSize,
                        cvCallLegRateWMValue,
                        cvCallLegRateWMts
                    }
    STATUS          current
    DESCRIPTION
        "This group provides the information about the Call Volume Stats
        History information in the voice gateway."
    ::= { cvdcMIBGroups 44 }

END















































































































