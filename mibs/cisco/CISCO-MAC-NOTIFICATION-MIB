-- *****************************************************************
-- CISCO-MAC-NOTIFICATION-MIB.my:  Cisco MAC Notification MIB 
--
-- October 2001, <PERSON>
-- February 2003, <PERSON>
--
-- Copyright (c) 2001, 2003, 2006 by cisco Systems, Inc.
-- All rights reserved.
--
-- *****************************************************************

CISCO-MAC-NOTIFICATION-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, Counter32, NOTIFICATION-TYPE,
        Unsigned32, Integer32
                FROM SNMPv2-SMI
        MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
                FROM SNMPv2-CONF
        TruthValue, <PERSON><PERSON>tam<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>EXTUAL-CONVENTION
                FROM SNMPv2-TC
        ifIndex
                FROM IF-MIB
        VlanIndex
                FROM CISCO-VTP-MIB
        entPhysicalIndex
                FROM ENTITY-MIB
        Percent
                FROM CISCO-QOS-PIB-MIB
        ciscoMgmt
                FROM CISCO-SMI;


ciscoMacNotificationMIB MODULE-IDENTITY
    LAST-UPDATED    "200706110000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
        "       Cisco Systems
                Customer Service

                Postal: 170 W Tasman Drive
                        San Jose, CA  95134
                        USA

                Tel: ****** 553-NETS

                E-mail: <EMAIL>"
    DESCRIPTION
        "This MIB module is for configuration of the MAC notification
        feature. MAC notification is a mechanism to inform monitoring
        devices when there are MAC addresses learnt or removed from
        the forwarding database of the monitored devices." 

    REVISION   "200706110000Z"
    DESCRIPTION
        "Fixed typo and  made changes to the description of 
         cmnMACMoveObjects, cmnMACThresholdNotifEnabled and 
         cmnMacThresholdExceedNotif."

    REVISION   "200303210000Z"
    DESCRIPTION
        "Added cmnMACMoveObjects, cmnMACThresholdObjects." 

    REVISION   "200110220000Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 215 }

-- Cisco MAC Notification MIB object definitions

ciscoMacNotificationMIBObjects OBJECT IDENTIFIER
    ::= { ciscoMacNotificationMIB 1 }

-- Cisco MAC Notification MIB consists of the following groups
-- [1] Cisco Mac Notification Global Group (cmnGlobalObjects).
-- [2] Cisco Mac Notification Interface Group (cmnInterfaceObjects).
-- [3] Cisco Mac Notification Move Group (cmnMACMoveObjects).
-- [4] Cisco Mac Notification Threshold Group (cmnMACThresholdObjects).
cmnGlobalObjects     OBJECT IDENTIFIER
    ::= { ciscoMacNotificationMIBObjects 1 }
cmnInterfaceObjects     OBJECT IDENTIFIER
    ::= { ciscoMacNotificationMIBObjects 2 }
cmnMACMoveObjects     OBJECT IDENTIFIER
    ::= { ciscoMacNotificationMIBObjects 3 }
cmnMACThresholdObjects     OBJECT IDENTIFIER
    ::= { ciscoMacNotificationMIBObjects 4 }


--**********************************************************************
-- Cisco Mac Notification Global Group
--**********************************************************************

cmnGlobalFeatureEnabled  OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Indicates whether the MAC notification feature is currently 
        running in the device.

        Setting this object to false(2) disables the MAC notification
        feature globally thus disabling the feature at each interface.

        Setting this object to true(1) will start the MAC notification
        feature running in the device. If the feature is already
        running, setting to true(1) has no effect. Once the MAC
        notification is enabled, whether the feature is running at each
        interface is controlled by the cmnIfConfigTable."   
    ::= { cmnGlobalObjects 1 }

cmnNotificationInterval  OBJECT-TYPE
    SYNTAX        Unsigned32 (0..4294967295)
    UNITS         "seconds"
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the maximum interval of time between
        cmnMacChangedNotifications being generated by the device.
        If the value of cmnNotificationsEnabled is true(1), the
        device will send out the generated cmnMacChangedNotifications
        and archive the MAC change notification events in the
        cmnHistoryTable. If the value of cmnNotificationEnabled is
        false(2), the device will not send out the generated
        cmnMacChangedNotifications but it will archive these events
        in the cmnHistoryTable.   
        
        If the value of this object is equal to 0, the device will 
        generate cmnMacChangedNotifications and archive the MAC 
        change notification events in the cmnHistoryTable as soon as
        there is MAC address learnt or removed by the device.

        If the value of this object is greater than 0, the device will
        wait for a period of time equal to the value of this object
        before generate the cmnMacChangedNotifications and archive
        the MAC change notification events in the cmnHistoryTable."
    ::= { cmnGlobalObjects 2 }

cmnMacAddressesLearnt OBJECT-TYPE
    SYNTAX        Counter32 
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Indicates the number of MAC addresses learnt by the
        device." 
    ::= { cmnGlobalObjects 3 }

cmnMacAddressesRemoved OBJECT-TYPE
    SYNTAX        Counter32 
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Indicates the number of MAC addresses removed from the
        forwarding database." 
    ::= { cmnGlobalObjects 4 }

cmnNotificationsEnabled OBJECT-TYPE
     SYNTAX          TruthValue
     MAX-ACCESS      read-write
     STATUS          current
     DESCRIPTION
         "Indicates whether cmnMacChangedNotification notifications
          will or will not be sent when there are MAC addresses
          learnt or removed from the device's forwarding database. 
          Disabling notifications does not prevent the MAC address
          info from being added to the cmnHistoryTable."
     DEFVAL { false }
     ::= { cmnGlobalObjects 5 }

cmnNotificationsSent OBJECT-TYPE
    SYNTAX         Counter32 
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "Indicates the number of cmnMacChangedNotifications sent out
        by the device."
    ::= { cmnGlobalObjects 6 }

cmnHistTableMaxLength OBJECT-TYPE
    SYNTAX          Unsigned32 (0..500)
    UNITS           "entries"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The upper limit on the number of entries that the
        cmnHistoryTable may contain.  A value of 0 will
        prevent any history from being retained. When this
        table is full, the oldest entry will be deleted and
        a new one will be created."
    DEFVAL  { 1 }
    ::= { cmnGlobalObjects 7 }

cmnHistoryTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF CmnHistoryEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "This table will archive the MAC change notification events 
        generated by this device. The MAC change notification
        events are archived here even if cmnMacChangesNotifications 
        are not actually sent."
    ::= { cmnGlobalObjects 8 }

cmnHistoryEntry OBJECT-TYPE
    SYNTAX     CmnHistoryEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "A MAC change notification message that was previously
        generated by this device.  Each entry is indexed by a message
        index."
    INDEX   { cmnHistIndex }
    ::= { cmnHistoryTable 1 }

CmnHistoryEntry ::=
    SEQUENCE {
        cmnHistIndex
                Unsigned32,
        cmnHistMacChangedMsg
                OCTET STRING,
        cmnHistTimestamp
                TimeStamp
    }

cmnHistIndex OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An index that uniquely identifies a MAC change notification
        event previously generated by the device. This index starts at
        1 and increases by one when a MAC change notification is 
        generated.  When it reaches the maximum value, the agent wraps
        the value back to 1."
    ::= { cmnHistoryEntry 1 }

cmnHistMacChangedMsg OBJECT-TYPE
    SYNTAX        OCTET STRING (SIZE(1..254))
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This object contains the information of a MAC change
        notification event. It consists of several tuples packed
        together in the format of '<tuple1><tuple2>...'.
        
        Each tuple consist of 11 octets in the format of
        '<operation><VLAN><MAC><dot1dBasePort>' where 

        <operation> is of size 1 octet and supports the following values
          0 - End of MIB object.
          1 - MAC learnt.
          2 - MAC removed.

        <VLAN> is VLAN number of the VLAN which the MAC address is
        belonged to and has size of 2 octet.

        <MAC> is the Layer2 Mac Address and has size of 6 octets.

        <dot1dBasePort> is the value of dot1dBasePort for the
        interface from which the MAC address is learnt and has size
        of 2 octets."
    ::= { cmnHistoryEntry 2 }

cmnHistTimestamp  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of sysUpTime when the cmnMacChangedNotification
        containing the information denoted by the cmnHistMacChangedMsg
        object in this entry was generated."
    ::= { cmnHistoryEntry 3 }

--*********************************************************************
-- Cisco Mac Notification Interface Group
--*********************************************************************

--
-- cmnIfConfigTable
--

cmnIfConfigTable  OBJECT-TYPE
    SYNTAX        SEQUENCE OF CmnIfConfigEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table enables or disables the generation of notification
        at each interface when MAC address is learnt or removed." 
    ::= { cmnInterfaceObjects 1 }

cmnIfConfigEntry  OBJECT-TYPE
    SYNTAX        CmnIfConfigEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "Each entry contains the configuration for enabling the
        MAC notification at each interface that supports this
        feature." 
    INDEX { ifIndex }
    ::= { cmnIfConfigTable 1 }

CmnIfConfigEntry  ::= SEQUENCE {
    cmnMacAddrLearntEnable    TruthValue, 
    cmnMacAddrRemovedEnable   TruthValue 
}

cmnMacAddrLearntEnable OBJECT-TYPE
    SYNTAX        TruthValue 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Indicates whether this interface is enabled to send 
        cmnMacChangedNotification when it learns a new MAC address. This
        variable has no effect when the value of cmnGlobalFeatureEnabled
        object is false(2).

        Setting this object to true(1) enables the sending of
        cmnMacChangedNotification when this interface learns a 
        new MAC address.

        Setting this object to false(2) disables the sending
        of cmnMacChangedNotification when this interface learns
        a new MAC address."      
    DEFVAL { false }
    ::= { cmnIfConfigEntry 1 }

cmnMacAddrRemovedEnable OBJECT-TYPE
    SYNTAX        TruthValue 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Indicates whether this interface is enabled to send 
        cmnMacChangedNotification when a MAC address which it learnt
        previously is removed from the forwarding table. This variable
        has no effect when the value of cmnGlobalFeatureEnabled object
        is false(2). 

        Setting this object to true(1) enables the sending of
        cmnMacChangedNotification when a MAC address which this
        interface learnt previously is removed from the forwarding
        table.

        Setting this object to false(2) disables the sending of
        cmnMacChangedNotification when a MAC address which this
        interface learnt previously is removed from the forwarding
        table."
    DEFVAL { false }
    ::= { cmnIfConfigEntry 2 }

--*********************************************************************
-- Cisco Mac Notification Move Group
--*********************************************************************

cmnMACMoveFeatureEnabled  OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Specifies whether the MAC Move notification feature is 
         currently running in the device.

         Setting this object to false(2) disables the MAC Move 
         notification feature globally.

         Setting this object to true(1) will start the MAC Move 
         notification feature running in the device."  
    ::= { cmnMACMoveObjects 1 }

cmnMACMoveNotificationsEnabled OBJECT-TYPE
     SYNTAX          TruthValue
     MAX-ACCESS      read-write
     STATUS          current
     DESCRIPTION
         "Specifies whether cmnMacMoveNotification notifications
          will or will not be sent when a MAC move is detected by
          the MAC move notification feature.

          Setting this object to false(2) will not send the
          cmnMacMoveNotification notifications.
   
          Setting this object to true(1) will send the 
          cmnMacMoveNotification notifications."
    ::= { cmnMACMoveObjects 2 }

cmnMACMoveAddress OBJECT-TYPE
     SYNTAX          MacAddress
     MAX-ACCESS      read-only
     STATUS          current
     DESCRIPTION
         "Indicates the MAC address that is moved between
          cmnMACMoveFromPortId and cmnMACMoveToPortId on 
          cmnMACMoveVlanNumber.  This object is instantiated only 
          when cmnMACMoveFeatureEnabled value is set to true(1) and
          a MAC move is detected by the MAC move notification feature."
    ::= { cmnMACMoveObjects 3 }

cmnMACMoveVlanNumber OBJECT-TYPE
     SYNTAX          VlanIndex
     MAX-ACCESS      read-only
     STATUS          current
     DESCRIPTION
         "Indicates the VLAN on which the cmnMACMoveAddress is
          moved from cmnMACMoveFromPortId to cmnMACMoveToPortId. 
          This object is instantiated only when
          cmnMACMoveFeatureEnabled value is set to true(1) and a
          MAC move is detected by the MAC move notification feature."
    ::= { cmnMACMoveObjects 4 }

cmnMACMoveFromPortId OBJECT-TYPE
     SYNTAX          Integer32 (1..65535) 
     MAX-ACCESS      read-only
     STATUS          current
     DESCRIPTION
         "The value of dot1dBasePort for the bridge port from which 
          the cmnMACMoveAddress is moved to cmnMACMoveToPortId on 
          cmnMACMoveVlanNumber. This object is instantiated only when
          cmnMACMoveFeatureEnabled value is set to true(1) and a 
          MAC move is detected by the MAC move notification feature."
     REFERENCE
               "dot1dBasePort is defined in RFC1493."
    ::= { cmnMACMoveObjects 5 }

cmnMACMoveToPortId OBJECT-TYPE
     SYNTAX          Integer32 (1..65535)
     MAX-ACCESS      read-only
     STATUS          current
     DESCRIPTION
         "The value of dot1dBasePort for the bridge port to which 
          the cmnMACMoveAddress is moved from cmnMACMoveFromPortId 
          on cmnMACMoveVlanNumber.  This object is instantiated only 
          when cmnMACMoveFeatureEnabled value is set to true(1) and  
          a MAC move is detected by the MAC move notification feature."
     REFERENCE
               "dot1dBasePort is defined in RFC1493."
    ::= { cmnMACMoveObjects 6 }

cmnMACMoveTime OBJECT-TYPE
     SYNTAX          TimeStamp
     MAX-ACCESS      read-only
     STATUS          current
     DESCRIPTION
         "The value of sysUpTime when a cmnMACMoveAddress is moved 
          between cmnMACMoveFromPortId and cmnMACMACMoveToPortId. 
          This object is instantiated only when 
          cmnMACMoveFeatureEnabled value is set to true(1) and  
          a MAC move is detected by the MAC move notification feature."
    ::= { cmnMACMoveObjects 7 }

--*********************************************************************
-- Cisco Mac Notification Threshold Group
--*********************************************************************

cmnMACThresholdFeatureEnabled  OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Specifies whether the MAC Threshold notification feature is 
         currently running in the device.

         Setting this object to false(2) disables the MAC Threshold 
         notification feature globally.
 
         Setting this object to true(1) will start the MAC Threshold 
         notification feature running in the device." 
    ::= { cmnMACThresholdObjects 1 }

cmnMACThresholdLimit  OBJECT-TYPE
    SYNTAX        Percent
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "Indicate the threshold limit of the forwarding table 
          utilization."
    ::= { cmnMACThresholdObjects 2 }

cmnMACThresholdInterval  OBJECT-TYPE
    SYNTAX        Unsigned32
    UNITS         "seconds"
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Interval at which forwarding table utilization is compared 
         against cmnMACThresholdLimit." 
    ::= { cmnMACThresholdObjects 3 }

cmnMACThresholdNotifEnabled OBJECT-TYPE
     SYNTAX          TruthValue
     MAX-ACCESS      read-write
     STATUS          current
     DESCRIPTION
         "Specifies whether cmnMacThresholdExceedNotif 
          notifications will or will not be sent when the forwarding 
          table utilization exceeds or equals to cmnMACThresholdLimit 
          value. cmnMacThresholdExceedNotif notification is not sent 
          when cmnMACThresholdLimit is set to zero."
    ::= { cmnMACThresholdObjects 4 }

cmnUtilizationTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF CmnUtilizationEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "cmnUtilizationTable  specifies the forwarding table 
         utilization information. This table is instantiated only 
         when cmnMACThresholdFeatureEnabled value is set to true(1).
         Entries in this table are updated at the end of every
         cmnMACThresholdInterval." 
          
    ::= { cmnMACThresholdObjects 5 }

cmnUtilizationEntry OBJECT-TYPE
    SYNTAX     CmnUtilizationEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "A conceptual row containing forwarding table utilization 
         maintained by switching engine (identified by 
         entPhysicalIndex). Each switching engine managed by this 
         MIB module can have at least one entry in this table."
    INDEX   { entPhysicalIndex }
    ::= { cmnUtilizationTable 1 }

CmnUtilizationEntry ::=
    SEQUENCE {
        cmnUtilizationEntries
                Unsigned32,
        cmnUtilizationUtilization
                Percent,
        cmnUtilizationTimeStamp
                TimeStamp
    }

cmnUtilizationEntries OBJECT-TYPE
    SYNTAX          Unsigned32 
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of entries present in the forwarding 
         table for the given entPhysicalIndex calculated at the 
         end of cmnMACThresholdInterval."
    ::= { cmnUtilizationEntry 1 }

cmnUtilizationUtilization OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the utilization of the forwarding table for the given
         entPhysicalIndex calculated at the end of 
         cmnMACThresholdInterval."
    ::= { cmnUtilizationEntry 2 }

cmnUtilizationTimeStamp OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the sysUptime at which the cmnUtilizationUtilization 
         is updated."
    ::= { cmnUtilizationEntry 3 }

--
--
-- Notification 
--

cmnMIBNotificationPrefix OBJECT IDENTIFIER
    ::= { ciscoMacNotificationMIB 2 }

cmnMIBNotifications
    OBJECT IDENTIFIER ::= { cmnMIBNotificationPrefix 0 }

cmnMacChangedNotification NOTIFICATION-TYPE
    OBJECTS { cmnHistMacChangedMsg, cmnHistTimestamp } 
    STATUS  current
    DESCRIPTION
        "This notification is generated when there is enough MAC
        address information to fully occupy a maximum size SNMP trap
        message. This notification is also generated when there
        is at least one MAC address changed or removed and the amount
        of time elapsed from the previous notification is greater
        than the maximum wait time denoted by 
        cmnNotificationInterval object. 

        If there are more MAC addresses information than can fit into
        one cmmHistTrapContent object, then multiple notifications
        will be generated." 
    ::= { cmnMIBNotifications 1 } 

cmnMacMoveNotification NOTIFICATION-TYPE
    OBJECTS { 
              cmnMACMoveAddress, 
              cmnMACMoveVlanNumber, 
              cmnMACMoveFromPortId, 
              cmnMACMoveToPortId,
              cmnMACMoveTime
            }
    STATUS  current
    DESCRIPTION
        "cmnMacMoveNotification is generated when a MAC address is 
         moved between two interfaces."
    ::= { cmnMIBNotifications 2 } 

cmnMacThresholdExceedNotif  NOTIFICATION-TYPE
    OBJECTS  {
              cmnUtilizationUtilization, 
              cmnMACThresholdLimit,
              cmnUtilizationTimeStamp 
             }
    STATUS  current
    DESCRIPTION
         "cmnMacThresholdExceedNotif is sent when 
          cmnUtilizationUtilization exceeds or equals to the 
          cmnMACThresholdLimit for a given entPhysicalIndex.
          cmnMacThresholdExceedNotif is not sent when 
          cmnMACThresholdLimit is set to zero"
    ::= { cmnMIBNotifications 3 } 

--
-- Conformance
--

cmnMIBConformance OBJECT IDENTIFIER ::= { ciscoMacNotificationMIB 3 }

cmnMIBCompliances OBJECT IDENTIFIER
    ::= { cmnMIBConformance 1 }

cmnMIBGroups      OBJECT IDENTIFIER
    ::= { cmnMIBConformance 2 }


-- Compliance

cmnMIBCompliance MODULE-COMPLIANCE
     STATUS deprecated
     DESCRIPTION
         "The compliance statement for the CISCO-MAC-NOTIFICATION-MIB."
     MODULE
     MANDATORY-GROUPS { cmnGlobalGroup,
                        cmnInterfaceGroup,
                        cmnNotificationGroup }
     ::= { cmnMIBCompliances 1 }

cmnMIBComplianceVer1 MODULE-COMPLIANCE
     STATUS current
     DESCRIPTION
         "The compliance statement for the CISCO-MAC-NOTIFICATION-MIB."
     MODULE
     MANDATORY-GROUPS { cmnGlobalGroup,
                        cmnInterfaceGroup,
                        cmnNotificationGroup 
                      }

     GROUP        cmnMACMoveGroup
     DESCRIPTION  "This group is mandatory if the managed system
                   supports MAC Move notification feature."

     GROUP        cmnMACThresholdGroup
     DESCRIPTION  "This group is mandatory if the managed system
                   supports MAC Threshold notification feature."

     GROUP        cmnMACMoveNotifGroup
     DESCRIPTION  "This group is mandatory if the managed system
                   supports Notifications for MAC Move notification 
                   feature."

     GROUP        cmnMACThresholdNotifGroup
     DESCRIPTION  "This group is mandatory if the managed system
                   supports Notifications for MAC Threshold notification
                   feature."

     ::= { cmnMIBCompliances 2 }

--
-- Units of Conformance
--
-- Units of Conformance
--
cmnGlobalGroup OBJECT-GROUP
    OBJECTS {
      cmnGlobalFeatureEnabled,
      cmnNotificationInterval,
      cmnMacAddressesLearnt,
      cmnMacAddressesRemoved,
      cmnNotificationsEnabled,
      cmnHistTableMaxLength,
      cmnHistMacChangedMsg,
      cmnHistTimestamp,
      cmnNotificationsSent
    }
    STATUS current
    DESCRIPTION
        "A collection of objects providing the global configuration
        and information for MAC notification." 
    ::= { cmnMIBGroups 1 }

cmnInterfaceGroup OBJECT-GROUP
    OBJECTS {
      cmnMacAddrLearntEnable,
      cmnMacAddrRemovedEnable
    }
    STATUS current
    DESCRIPTION
        "A collection of objects providing the configuration information
        for MAC notification at each interface." 
    ::= { cmnMIBGroups 2 }

cmnNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS { cmnMacChangedNotification }
    STATUS current
    DESCRIPTION
        "The notification generated by the CISCO-MAC-NOTIFICATION-MIB."
    ::= { cmnMIBGroups 3 } 

cmnMACMoveGroup OBJECT-GROUP
    OBJECTS{
        cmnMACMoveFeatureEnabled,
        cmnMACMoveNotificationsEnabled,
        cmnMACMoveAddress,
        cmnMACMoveVlanNumber,
        cmnMACMoveFromPortId,
        cmnMACMoveToPortId,
        cmnMACMoveTime
    }
    STATUS current
    DESCRIPTION
        "A collection of objects providing the global configuration
        and information for MAC Move notification feature." 
    ::= { cmnMIBGroups 5 }

cmnMACThresholdGroup OBJECT-GROUP
    OBJECTS{
        cmnMACThresholdFeatureEnabled,
        cmnMACThresholdLimit,
        cmnMACThresholdInterval,
        cmnMACThresholdNotifEnabled,
        cmnUtilizationEntries,
        cmnUtilizationUtilization,
        cmnUtilizationTimeStamp
    }
    STATUS current
    DESCRIPTION
        "A collection of objects providing the global configuration
        and information for MAC Threshold notification feature." 
    ::= { cmnMIBGroups 6 }


cmnMACMoveNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS{
        cmnMacMoveNotification
    }
    STATUS current
    DESCRIPTION
        "A collection of objects providing the notification
        information for MAC Move notification feature."
    ::= { cmnMIBGroups 7 }

cmnMACThresholdNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS{
        cmnMacThresholdExceedNotif
    }
    STATUS current
    DESCRIPTION
        "A collection of objects providing the notification
        information for MAC Threshold notification feature."
    ::= { cmnMIBGroups 8 }
END
