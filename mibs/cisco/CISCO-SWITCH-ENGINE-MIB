-- *****************************************************************
-- <PERSON><PERSON> Switch Engine MIB
--   
-- February 2000, <PERSON>
-- July 2000, <PERSON>
-- February 2002, <PERSON>
-- February 2003, <PERSON>
-- May 2003, <PERSON><PERSON>
-- August 2003, <PERSON>
-- %DNP% March 2005, <PERSON><PERSON>
--   
-- Copyright (c) 2000-2020 by cisco Systems Inc.
-- by cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-SWITCH-ENGINE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    G<PERSON><PERSON>32,
    <PERSON>32,
    <PERSON>64,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>te<PERSON><PERSON>,
    Unsigned32
        FROM SNMPv2-SMI
    MODULE-COMP<PERSON><PERSON><PERSON><PERSON>,
    OB<PERSON>ECT-GROUP
        FROM SNMPv2-CO<PERSON>
    ifIndex,
    <PERSON>er<PERSON><PERSON>,
    InterfaceIndexOrZero
        FROM IF-MIB
    InetAddressType,
    InetAddress
        FROM INET-ADDRESS-MIB
    entPhysicalIndex
        FROM ENTITY-MIB
    TEXTUAL-CONVENTION,
    DisplayString,
    RowStatus,
    TimeInterval,
    MacAddress,
    TruthValue,
    TimeStamp
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    MplsVpnId
        FROM MPLS-VPN-MIB
    CiscoNetworkProtocol,
    CiscoPort
        FROM CISCO-TC
    VlanIndex
        FROM CISCO-VTP-MIB
    ciscoMgmt
        FROM CISCO-SMI;


ciscoSwitchEngineMIB MODULE-IDENTITY
    LAST-UPDATED    "202005260000Z"
    ORGANIZATION    "Cisco Systems Inc."
    CONTACT-INFO
            "Cisco Systems
            Customer Service

            Postal: 170 W Tasman Drive
            San Jose, CA 95134
            USA

            Tel: ****** 553-NETS

            E-mail: <EMAIL>"
    DESCRIPTION
        "This MIB module defines management objects for Cisco Layer 2/3
        switches. These devices may either have a single (central) switching
        engine entity or may consist of multiple (distributed) switching
        engine entities which are inter-connected via a common 'switching
        fabric'. In the central switching engine model, all the physical
        ports in the system are handled by the only switching engine in the
        system.  In the distributed switching model, each switching engine
        will handle a set of 'local' physical ports and when necessary,
        packets are also switched between switching engines over the
        switching fabric.

        Cisco L2/L3 switching devices use regular routers to assist them
        in learning packet 'flows' by observing how a router routes a
        candidate flow. A flow is some combination of source network address,
        destination network address and the transport port numbers, as
        applicable.  Once a flow is established (learned), all traffic
        belonging to that flow will be switched at Layer 3 by the switch
        engine, effectively bypassing the router, until the flow has been
        'aged' out. Most Cisco L2/L3 switching devices employ built-in
        (internal) router module(s) for integrating Layer 3 switching with
        Layer 2 forwarding. However, they can also learn 'flows' through
        other physically-separate (external) Cisco routers that are
        connected to the switch-engine through the network."
    REVISION        "202005260000Z"
    DESCRIPTION
        "Add enumerated value 304 - 308 to cseTcamResourceType."    
    REVISION        "202003060000Z"
    DESCRIPTION
        "Add enumerated value 282 - 303 to cseTcamResourceType."    
    REVISION        "201907110000Z"
    DESCRIPTION
        "Add enumerated value 232 - 281 to cseTcamResourceType."
    REVISION        "201806200000Z"
    DESCRIPTION
        "Add enumerated value 229 - 231 to cseTcamResourceType."
    REVISION        "201712070000Z"
    DESCRIPTION
        "Add enumerated value 86 - 228 to cseTcamResourceType."
    REVISION        "201302130000Z"
    DESCRIPTION
        "Add enumerated value 37 - 85 to cseTcamResourceType."
    REVISION        "201203120000Z"
    DESCRIPTION
        "Add cseStatisticsFlowGroup1."
    REVISION        "201012170000Z"
    DESCRIPTION
        "Add the following new enumerations to cseTcamResourceType:
        dgtSgtRegion(31), anyAnyRegion(32), tcamALabel(33),
        tcamBLabel(34), destInfoIn(35) and destInfoOut(36)."
    REVISION        "200811110000Z"
    DESCRIPTION
        "Add new enumerations to cseTcamResourceType."
    REVISION        "200801290000Z"
    DESCRIPTION
        "Add cseL3SwitchedPktsPerSecGroup.

        Add cseCacheStatisticsGroup.

        Add new enumerations to cseTcamResourceType.

        Add new enumerations to cseFlowIPFlowMask object."
    REVISION        "200509160000Z"
    DESCRIPTION
        "Add cseFlowMcastMgmtGroup2.

        Deprecate the objects:
        cseFlowMcastQuerySrc,
        cseFlowMcastQueryGrp,
        cseFlowMcastResultGrp,
        cseFlowMcastResultSrc.

        Add new enumerations to cseFlowMcastQueryMask."
    REVISION        "200504120000Z"
    DESCRIPTION
        "Add new enumerations to cseFlowIPFlowMask object.
        Add ingressInterfaceMapping and egressInterfaceMapping
        enumerations to cseTcamResourceType object."
    REVISION        "200411150000Z"
    DESCRIPTION
        "Add cseMetUsageGroup."
    REVISION        "200406090000Z"
    DESCRIPTION
        "Add the following Groups:
        cseNetflowASInfoExportGroup
        cseNetflowPerVlanIfGroup"
    REVISION        "200311070000Z"
    DESCRIPTION
        "Add cseErrorStatsLCTable."
    REVISION        "200308200000Z"
    DESCRIPTION
        "Add the following tables to support forwarding information base:
        cseCefFibTable,
        cseCefAdjacencyTable.

        Add the following table to support TCAM (Ternary Content
        Addressable Memory) resource usage:
        cseTcamUsageTable.

        Add default value for the following objects:
        cseFlowQuerySource,
        cseFlowQuerySourceMask,
        cseFlowQueryDestination,
        cseFlowQueryDestinationMask,
        cseFlowQueryOwner."
    REVISION        "200306100000Z"
    DESCRIPTION
        "Deprecated the objects:
        cseNetflowLSExportHost,
        cseNetflowLSExportTransportNumber"
    REVISION        "200305060000Z"
    DESCRIPTION
        "Added the object cseFlowQuerySkipNFlows"
    REVISION        "200302210000Z"
    DESCRIPTION
        "Added the following objects and table:
        cseFlowLongAgingTime, 
        cseNetFlowIfIndexEnable,
        cseFlowStatsTable,
        cseFlowExcludeTable.
        Modified the description of the following objects:
        cseL2IpPkts,
        cseL2IpxPkts,
        cseL2AssignedProtoPkts,
        cseL2OtherProtoPkts,
        cseL2HCIpPkts,
        cseL2HCIpxPkts,
        cseL2HCAssignedProtoPkts,
        cseL2HCOtherProtoPkts."
    REVISION        "200208050000Z"
    DESCRIPTION
        "Added the following objects: cseFlowIPFlowMask,
        cseFlowIPXFlowMask, cseProtocolFilterEnable."
    REVISION        "200202070000Z"
    DESCRIPTION
        "Added the objects in cseBridgedFlowStatsCtrlTable and
        cseErrorStatsTable.

        Added the following objects:
        cseL3VlanInUnicastPkts
        cseL3VlanInUnicastOctets
        cseL3VlanOutUnicastPkts
        cseL3VlanOutUnicastOctets."
    REVISION        "200110260000Z"
    DESCRIPTION
        "Added the object cseFlowQueryTotalFlows"
    REVISION        "200109130000Z"
    DESCRIPTION
        "Added the follwowing objects
        o cseNetflowLSFilterSupport
        o cseNetflowLSFilterTable.
        Also created the new groups 
        o cseNDEMandatoryGroup
        o cseNDESingleFilterGroup
        o cseNDEMultipleFiltersGroup"
    REVISION        "200105160000Z"
    DESCRIPTION
        "Added 4k Vlan support"
    REVISION        "200103090000Z"
    DESCRIPTION
        "Update the range of cseFlowEstablishedAgingTime,
        cseFlowIPXEstablishedAgingTime, cseFlowOperEstablishedAgingTime,
        cseFlowOperIPXAgingTime.
        Replace cseFlowQueryResult with cseFlowQueryResultingRows."
    REVISION        "200006230000Z"
    DESCRIPTION
        "Added the following objects:
        o cseFlowOperEstablishedAgingTime.
        o cseFlowOperFastAgingTime.
        o cseFlowOperFastAgePktThreshold.
        o cseFlowOperIPXAgingTime."
    REVISION        "200001311130Z"
    DESCRIPTION
        "Added  one High Capacity L2 Statistics table, an extension
        of cseL2StatsTable and new objects in NetflowLS group for
        new netflow export features. Also added a new enum type of 
        cseRouterFlowMask to support L3 multicast."
    REVISION        "9912091130Z"
    DESCRIPTION
        "Added MIB objects to manage Switch Engine (SE) portion of
        Multicast MLS control protocol."
    REVISION        "9806241130Z"
    DESCRIPTION
        "Added 2 groups, for the purging (clearing) of layer 3 unicast and
        multicast flow entries stored in the cache. Also added new objects
        for layer 3 flow statistics."
    REVISION        "9805281130Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 97 }


-- Overview of MIB Objects:
--   
-- Defines 9 groups of objects.
--   
-- 1. cseL2Objects: Contains mainly Layer 2 statistics maintained by the
-- switching engine hardware.
--   
-- 2. cseFlow: This group has all the important objects used in the
-- management of switching engine hardware. It contains :
-- - Scalars for configuring aging times that determine how long
-- certain learned flows, are used for L3 switching its traffic.
-- - A table of all routers whose "flows" are learned by the
-- switching
-- engine.
-- - A table for adding external routers to the router table and
-- enabling
-- the switching engine to learn of all the flows through those
-- routers.
-- - A table listing the MAC address and VLAN number used for each
-- router.
-- - A query/result table pair for monitoring the switching
-- performance
-- of the switching engine(s).
-- - A control table for enabling/disabling the flow switching
-- feature
-- per protocol type (ip, ipx).
--   
-- 3. cseNetflowLS: A group of objects used to manage
-- the Netflow LAN Switching data export feature.
--   
-- 4. cseL3Objects: Contains
-- - L3 statistics maintained by the switching engine hardware.
-- - L3 packet/octets statistics (in/out) maintained per vlan.
--   
-- 5. cseProtocolFilter: Contains
-- - Table for configuring protocol filters per (non-trunking) port.
--   
-- 6. cseUcastCache: Contains
-- - a MIB table to perform actions of purging IP/IPX
-- flows in switch engine cache pools.
--   
-- 7. cseMcastCache:
-- - a MIB table to perform actions of purging IP
-- multicast flows in switch engine caches.
--   
-- 8. cseCef: CEF (Cisco Express Forwarding) group. It contains:
-- - tables provide information on IP forwarding database
-- and statistics.
--   
-- 9. cseTcamUsage: Contains
-- - table provides resource usage information on TCAM (Ternary
-- Content Addressable Memory) in the device.
--   
-- 10. cseMet: MET group. It contains:
-- - table provides resource usage information on MET (Multicast
-- Expansion Table) in the device.

cseMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoSwitchEngineMIB 1 }

-- object groups

cseL2Objects  OBJECT IDENTIFIER
    ::= { cseMIBObjects 1 }

cseFlow  OBJECT IDENTIFIER
    ::= { cseMIBObjects 2 }

cseNetflowLS  OBJECT IDENTIFIER
    ::= { cseMIBObjects 3 }

cseL3Objects  OBJECT IDENTIFIER
    ::= { cseMIBObjects 4 }

cseProtocolFilter  OBJECT IDENTIFIER
    ::= { cseMIBObjects 5 }

cseUcastCache  OBJECT IDENTIFIER
    ::= { cseMIBObjects 6 }

cseMcastCache  OBJECT IDENTIFIER
    ::= { cseMIBObjects 7 }

cseCef  OBJECT IDENTIFIER
    ::= { cseMIBObjects 8 }

cseTcamUsage  OBJECT IDENTIFIER
    ::= { cseMIBObjects 9 }

cseMet  OBJECT IDENTIFIER
    ::= { cseMIBObjects 10 }


CiscoGauge64 ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "This TC describes an object with a nonnegative integer value
        that may increase or decrease, with a maximum value of 2^64-1."
    SYNTAX          Counter64

ControlStatus ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "This TC describes the current status of a controlled object
        value."
    SYNTAX          INTEGER  {
                        enabled(1),
                        disabled(2)
                    }

McastGroupIp ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1d.1d.1d.1d"
    STATUS          current
    DESCRIPTION
        "This TC specifies an multicast group IP address,
        a class D IP address with the first byte in the range of
        224 to 239."
    SYNTAX          IpAddress

FlowAddressComponent ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1x:"
    STATUS          current
    DESCRIPTION
        "Represents a network layer address.  The length and format of
        the address is protocol dependent as follows:
        ip              6 octets
                        first 4 octets are the IP address in network
                        order
                        last 2 bytes is the transport's port number.
        ipx             10 octets
                        first 4 octets are the net number
                        last 6 octets are the host number"
    SYNTAX          OCTET STRING (SIZE (6..6 | 10..10))
-- Layer 2 statistics per switching engine

cseL2StatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseL2StatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing switching engine's L2 statistics counters."
    ::= { cseL2Objects 1 }

cseL2StatsEntry OBJECT-TYPE
    SYNTAX          CseL2StatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row containing L2 statistics maintained by a
        switching engine (identified by entPhysicalIndex). 
        Each switching engine managed by this MIB module has an
        entry in this table."
    INDEX           { entPhysicalIndex } 
    ::= { cseL2StatsTable 1 }

CseL2StatsEntry ::= SEQUENCE {
        cseL2ForwardedLocalPkts   Counter32,
        cseL2ForwardedLocalOctets Counter64,
        cseL2ForwardedTotalPkts   Counter32,
        cseL2NewAddressLearns     Counter32,
        cseL2AddrLearnFailures    Counter32,
        cseL2DstAddrLookupMisses  Counter32,
        cseL2IpPkts               Counter32,
        cseL2IpxPkts              Counter32,
        cseL2AssignedProtoPkts    Counter32,
        cseL2OtherProtoPkts       Counter32
}

cseL2ForwardedLocalPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets received from ports local to this switching
        engine and forwarded at layer 2." 
    ::= { cseL2StatsEntry 1 }

cseL2ForwardedLocalOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of octets in the packets received from ports local to this
        switching engine and forwarded at layer 2." 
    ::= { cseL2StatsEntry 2 }

cseL2ForwardedTotalPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of packets received from all sources (local and over
        the fabric) and forwarded at layer 2 by this switching engine." 
    ::= { cseL2StatsEntry 3 }

cseL2NewAddressLearns OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of new MAC addresses learned by the switching engine." 
    ::= { cseL2StatsEntry 4 }

cseL2AddrLearnFailures OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of MAC addresses failed to be learned because the L2
        forwarding address table was full. If the value keeps increasing,
        the network topology should be reconfigured." 
    ::= { cseL2StatsEntry 5 }

cseL2DstAddrLookupMisses OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of misses during destination MAC address table lookups.
        A few misses happen normally.  Large numbers of misses occur as
        a result of cseL2AddrLearnFailures." 
    ::= { cseL2StatsEntry 6 }

cseL2IpPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to the IP family received by this
        switching engine from all sources.  This value includes L3 
        switched packets." 
    ::= { cseL2StatsEntry 7 }

cseL2IpxPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to the IPX family received by this
        switching engine from all sources.  This value includes L3 
        switched packets." 
    ::= { cseL2StatsEntry 8 }

cseL2AssignedProtoPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to an assigned group of network
        protocols (typically AppleTalk, DecNet and Vines) received 
        by this switching engine from all sources. 
        This value includes L3 switched packets." 
    ::= { cseL2StatsEntry 9 }

cseL2OtherProtoPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to all other protocol families,
        received by this switching engine from all sources. 
        This value includes L3 switched packets." 
    ::= { cseL2StatsEntry 10 }
 

-- High Capacity extensions for cseL2StatsTable.

cseL2StatsHCTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseL2StatsHCEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Contains the High Capacity L2 Statistics extensions  to the
        cseL2StatsTable."
    ::= { cseL2Objects 2 }

cseL2StatsHCEntry OBJECT-TYPE
    SYNTAX          CseL2StatsHCEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Contains the High Capacity L2 Statistics extensions to
        cseL2StatsEntry. These objects will be created by the agent
        for all cseL2StatsEntries it deems appropriate."
    INDEX           { entPhysicalIndex } 
    ::= { cseL2StatsHCTable 1 }

CseL2StatsHCEntry ::= SEQUENCE {
        cseL2HCOverflowForwardedLocalPkts Counter32,
        cseL2HCForwardedLocalPkts         Counter64,
        cseL2HCOverflowForwardedTotalPkts Counter32,
        cseL2HCForwardedTotalPkts         Counter64,
        cseL2HCOverflowIpPkts             Counter32,
        cseL2HCIpPkts                     Counter64,
        cseL2HCOverflowIpxPkts            Counter32,
        cseL2HCIpxPkts                    Counter64,
        cseL2HCOverflowAssignedProtoPkts  Counter32,
        cseL2HCAssignedProtoPkts          Counter64,
        cseL2HCOverflowOtherProtoPkts     Counter32,
        cseL2HCOtherProtoPkts             Counter64
}

cseL2HCOverflowForwardedLocalPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the associated cseL2ForwardedLocalPkts
        counter has overflowed." 
    ::= { cseL2StatsHCEntry 1 }

cseL2HCForwardedLocalPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets received from ports local to this switching
        engine and forwarded at layer 2." 
    ::= { cseL2StatsHCEntry 2 }

cseL2HCOverflowForwardedTotalPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the associated cseL2ForwardeTotalPkts counter
        has overflowed." 
    ::= { cseL2StatsHCEntry 3 }

cseL2HCForwardedTotalPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of packets received from all sources
        (local and over the fabric) and forwarded at layer 2
        by this switching engine." 
    ::= { cseL2StatsHCEntry 4 }

cseL2HCOverflowIpPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the associated cseL2IpPkts counter
        has overflowed." 
    ::= { cseL2StatsHCEntry 5 }

cseL2HCIpPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to the IP family received by this
        switching engine from all sources.  This value includes 
        L3 switched packets." 
    ::= { cseL2StatsHCEntry 6 }

cseL2HCOverflowIpxPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the associated cseL2IpxPkts counter
        has overflowed." 
    ::= { cseL2StatsHCEntry 7 }

cseL2HCIpxPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to the IPX family received by this
        switching engine from all sources.  
        This value includes L3 switched packets." 
    ::= { cseL2StatsHCEntry 8 }

cseL2HCOverflowAssignedProtoPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the associated cseL2HCAssignedProtoPkts
        counter has overflowed." 
    ::= { cseL2StatsHCEntry 9 }

cseL2HCAssignedProtoPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to an assigned group of network
        protocols (typically AppleTalk, DecNet and Vines) received 
        by this switching engine from all sources.
        This value includes L3 switched packets." 
    ::= { cseL2StatsHCEntry 10 }

cseL2HCOverflowOtherProtoPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the associated cseL2HCOtherProtoPkts
        counter has overflowed." 
    ::= { cseL2StatsHCEntry 11 }

cseL2HCOtherProtoPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets belonging to all other protocol families,
        received by this switching engine from all sources.  This value 
        includes L3 switched packets." 
    ::= { cseL2StatsHCEntry 12 }
 


-- Flow group of objects
--   
-- Global aging times for the flows learned

cseFlowEstablishedAgingTime OBJECT-TYPE
    SYNTAX          Integer32 (1..65535)
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The adminstrative aging time for IP established flows.
        The default value for this object is implementation specific.
        If the cseFlowEstablishedAgingTime is not configured to the
        appropriate value, it will be adjusted to the closest value.
        The corresponding operational object, taken effect on the
        a device, is cseFlowOperIPEstablishedAgingTime." 
    ::= { cseFlow 1 }

cseFlowFastAgingTime OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The administrative fast aging time for the established flow
        entries, that have less number of packets than the value set in 
        the cseFlowFastAgePktThreshold, switched within this time. 
        Setting to value of 0 turns off fast aging.

        The default value for this object is implementation specific.
        If the cseFlowFastAgingTime is not configured to the
        appropriate value, it will be adjusted to the closest value.
        The corresponding operational object, taken effect on the
        device, is cseFlowOperFastAgingTime." 
    ::= { cseFlow 2 }

cseFlowFastAgePktThreshold OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "packets"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The administrative packet threshold setting for the
        cseFlowFastAgingTime. The default for Fast Aging Packet
        Threshold is 0, i.e. no packets switched within the time
        set in cseFlowFastAgingTime, after an L3 flow was established.
        If the cseFlowFastAgingTime is not configured to the
        appropriate value, it will be adjusted to the closest value.
        The corresponding operational object, taken effect on the
        device,  is cseFlowOperFastAgePktThreshold." 
    ::= { cseFlow 3 }
-- Router Table

cseRouterTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseRouterEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing information about all routers that are
        discovered by the switch, including internal and external
        routers."
    ::= { cseFlow 4 }

cseRouterEntry OBJECT-TYPE
    SYNTAX          CseRouterEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row in the cseRouterTable containing information
        about a router. A row appears either directly through dynamic 
        learning or indirectly through management configuration 
        (via SNMP,by creating an entry in the
        cseStaticExtRouterTable or via CLI)."
    INDEX           { cseRouterIndex } 
    ::= { cseRouterTable 1 }

CseRouterEntry ::= SEQUENCE {
        cseRouterIndex       IpAddress,
        cseRouterFlowMask    INTEGER,
        cseRouterName        DisplayString,
        cseRouterStatic      TruthValue,
        cseRouterIpxFlowMask INTEGER
}

cseRouterIndex OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The router's IP address which is used to uniquely identify
        it for L3 flows." 
    ::= { cseRouterEntry 1 }

cseRouterFlowMask OBJECT-TYPE
    SYNTAX          INTEGER  {
                        dstOnly(1),
                        srcDst(2),
                        fullFlow(3),
                        notApplicable(4),
                        srcDstVlan(5)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP type of mask configured for the router represented by
        this row.  Each flow known to the switching engine has a mask
        which is applied to all packets in order to compare them to
        that flow.  Each hardware-learned flow has the mask configured
        for the router which logically forwards that flow." 
    ::= { cseRouterEntry 2 }

cseRouterName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "DNS name (if available) of the router." 
    ::= { cseRouterEntry 3 }

cseRouterStatic OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the value of the object is true, this router was
        configured via SNMP or CLI. Otherwise, this router was
        learned automatically." 
    ::= { cseRouterEntry 4 }

cseRouterIpxFlowMask OBJECT-TYPE
    SYNTAX          INTEGER  {
                        dstOnly(1),
                        srcDst(2),
                        fullFlow(3),
                        notApplicable(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IPX type of mask configured for the router represented by
        this row.  Each flow known to the switching engine has a mask
        which is applied to all packets in order to compare them to
        that flow.  Each hardware-learned flow has the mask configured
        for the router which logically forwards that flow." 
    ::= { cseRouterEntry 5 }
 

-- Table of external routers that are enabled.

cseStaticExtRouterTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseStaticExtRouterEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of external routers which are enabled for Layer 3 IP
        switching by the switching engine. This table may contain 
        routers that have not yet been discovered by the device."
    ::= { cseFlow 5 }

cseStaticExtRouterEntry OBJECT-TYPE
    SYNTAX          CseStaticExtRouterEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row in the cseStaticExtRouterTable for
        enabling an external router to be installed in the switch's 
        router table. The entry is created and deleted by using
        cseStaticRouterStatus."
    INDEX           { cseRouterIndex } 
    ::= { cseStaticExtRouterTable 1 }

CseStaticExtRouterEntry ::= SEQUENCE {
        cseStaticRouterName   DisplayString,
        cseStaticRouterOwner  OwnerString,
        cseStaticRouterStatus RowStatus,
        cseStaticRouterType   BITS
}

cseStaticRouterName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "DNS name (if available) of the external router." 
    ::= { cseStaticExtRouterEntry 1 }

cseStaticRouterOwner OBJECT-TYPE
    SYNTAX          OwnerString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "String indicating the owner who created the static entry." 
    ::= { cseStaticExtRouterEntry 2 }

cseStaticRouterStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Used to manage creation and deletion of rows in this table.
        Once a row becomes active, values within that row cannot be
        modified except by deleting and creating the row." 
    ::= { cseStaticExtRouterEntry 3 }

-- per Router, per VLAN MAC address table for the flows

cseStaticRouterType OBJECT-TYPE
    SYNTAX          BITS {
                        unicast(0),
                        multicast(1)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Indicates if the router is included for unicast switching,
        or multicast switching, or both."
    DEFVAL          { { unicast } } 
    ::= { cseStaticExtRouterEntry 4 }
 


cseRouterVlanTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseRouterVlanEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The table listing the MAC address used by routers on
        particular VLANs."
    ::= { cseFlow 6 }

cseRouterVlanEntry OBJECT-TYPE
    SYNTAX          CseRouterVlanEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of the cseRouterVlanTable.
        An entry exists for each known VLAN of each known router."
    INDEX           {
                        cseRouterIndex,
                        cseRouterMac,
                        cseRouterVlan
                    } 
    ::= { cseRouterVlanTable 1 }

CseRouterVlanEntry ::= SEQUENCE {
        cseRouterMac      MacAddress,
        cseRouterVlan     VlanIndex,
        cseRouterProtocol BITS
}

cseRouterMac OBJECT-TYPE
    SYNTAX          MacAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Mac address used by the router for this VLAN number." 
    ::= { cseRouterVlanEntry 1 }

cseRouterVlan OBJECT-TYPE
    SYNTAX          VlanIndex
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Vlan number associated with the router's MAC address." 
    ::= { cseRouterVlanEntry 2 }

cseRouterProtocol OBJECT-TYPE
    SYNTAX          BITS {
                        ip(0),
                        ipx(1)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates which protocols are routed by this router on this
        VLAN using this Mac address." 
    ::= { cseRouterVlanEntry 3 }
 


-- Unicast Flow Query table

cseFlowMaxQueries OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Maximum number of query entries allowed to be outstanding at
        any time, in the cseFlowQueryTable. The typical value for 
        this object is 5." 
    ::= { cseFlow 7 }

cseFlowQueryTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseFlowQueryEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A control table used to query the switching engine by
        specifying retrieval criteria for L3 flows.
        The resulting data for each instance of a query in this
        table is returned in the cseFlowDataTable. 
        The maximum number of entries (rows) in this table
        cannot exceed the value returned by cseFlowMaxQueries."
    ::= { cseFlow 8 }

cseFlowQueryEntry OBJECT-TYPE
    SYNTAX          CseFlowQueryEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of the cesFlowQueryTable used to setup
        retrieval criteria to search for L3 flows on a particular 
        switching engine entity identified by entPhysicalIndex.
        The actual search is started by setting the value of 
        cseFlowQueryStatus to 'active'. Once a row becomes active,
        values within the row cannot be modified, except by
        deleting and re-creating the row."
    INDEX           {
                        entPhysicalIndex,
                        cseFlowQueryIndex
                    } 
    ::= { cseFlowQueryTable 1 }

CseFlowQueryEntry ::= SEQUENCE {
        cseFlowQueryIndex             Unsigned32,
        cseFlowQueryMask              INTEGER,
        cseFlowQueryTransport         BITS,
        cseFlowQuerySource            FlowAddressComponent,
        cseFlowQuerySourceMask        FlowAddressComponent,
        cseFlowQueryDestination       FlowAddressComponent,
        cseFlowQueryDestinationMask   FlowAddressComponent,
        cseFlowQueryRouterIndex       IpAddress,
        cseFlowQueryOwner             OwnerString,
        cseFlowQueryResultingRows     Integer32,
        cseFlowQueryResultTotalPkts   CiscoGauge64,
        cseFlowQueryResultTotalOctets CiscoGauge64,
        cseFlowQueryResultAvgDuration TimeInterval,
        cseFlowQueryResultAvgIdle     TimeInterval,
        cseFlowQueryStatus            RowStatus,
        cseFlowQueryCreateTime        TimeStamp,
        cseFlowQueryTotalFlows        Unsigned32,
        cseFlowQuerySkipNFlows        Integer32
}

cseFlowQueryIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An arbitrary integer which uniquely identifies the control
        query among all those specified for the switching engine
        indicated by entPhysicalIndex." 
    ::= { cseFlowQueryEntry 1 }

cseFlowQueryMask OBJECT-TYPE
    SYNTAX          INTEGER  {
                        dstOnly(1),
                        srcOrDst(2),
                        srcAndDst(3),
                        fullFlow(4),
                        ipxDstOnly(5),
                        ipxSrcAndDst(6),
                        any(7)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Setting each value causes the appropriate action:

        'dstOnly' - causes the creation of rows in the
        cseFlowDataTable corresponding to the current L3 flow 
        information for the absolute destination IP address 
        set in the cseFlowQueryDestination object in this table. 
        If cseFlowQueryDestinationMask is also specified at the 
        same time, it will be applied to the address part of
        cseFlowQueryDestination.

        'srcOrDst' -  causes the creation of rows in the
        cseFlowDataTable corresponding to the current L3 flow 
        information for EITHER of the absolute IP addresses set in 
        the cseFlowQueryDestination or cseFlowQuerySource objects. 
        If either of cseFlowQueryDestinationMask and
        cseFlowQuerySourceMask
        objects are also specified at the same time, 
        they will be applied to the respective address parts 
        of cseFlowQueryDestination and cseFlowQuerySource
        objects. This option is typically used to 
        setup queries for flows on traffic in either directions.

        'srcAndDst' -  causes the creation of rows in the
        cseFlowDataTable corresponding to the current L3 flow 
        information for BOTH the absolute IP addresses set in 
        the cseFlowQueryDestination and cseFlowQuerySource objects. 
        If either of cseFlowQueryDestinationMask and
        cseFlowQuerySourceMask objects are also specified
        at the same time, they will be applied to the 
        respective address parts of cseFlowQueryDestination and 
        cseFlowQuerySource objects. This option is typically used to 
        setup queries for flows on traffic in one direction only.


        'fullFlow' -  causes the creation of row(s) in the
        cseFlowDataTable exactly corresponding to the current
        L3 flow information for the complete IP flow (including the 
        transport port numbers) set in the cseFlowQueryDestination and 
        cseFlowQuerySource objects. If either of
        cseFlowQueryDestinationMask and cseFlowQuerySourceMask
        objects are also specified at the same
        time, they will be applied to the respective address parts of 
        cseFlowQueryDestination and cseFlowQuerySource objects. 
        This option is typically used to setup queries for flows
        on traffic for specific (TCP/UDP) port numbers
        corresponding to standard protocols such as FTP, 
        WWW, TELNET, etc.

        'ipxDstOnly' - causes the creation of rows in the
        cseFlowDataTable corresponding to the current L3 flow
        information for the absolute destination IPX address
        set in the cseFlowQueryDestination object in this table.
        If cseFlowQueryDestinationMask is also specified at 
        the same time, it will be applied to the address part
        of cseFlowQueryDestination.

        'ipxSrcAndDst' - causes the creation of rows in the
        cseFlowDataTable corresponding to the current L3 flow
        information for BOTH the absolute IPX addresses set in
        the cseFlowQueryDestination and cseFlowQuerySource objects.
        If either of cseFlowQueryDestinationMask and
        cseFlowQuerySourceMask objects are also specified at the 
        same time, they will be applied to the respective address
        parts of cseFlowQueryDestination and
        cseFlowQuerySource objects. 

        'any' - returns all rows corresponding to all established 
        flow entries in the cseFlowDataTable.

        Note: 
        1. The type FlowAddressComponent used for objects 
           cseFlowQuerySource and cseFlowQueryDestination, has the
           network address part and also the transport port 
           number part, if applicable.
        2. The value of this object cannot be modified when the 
           corresponding instance of cseFlowQueryStatus is 'active'."
    DEFVAL          { any } 
    ::= { cseFlowQueryEntry 2 }

cseFlowQueryTransport OBJECT-TYPE
    SYNTAX          BITS {
                        udp(0),
                        tcp(1)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The IP transport protocol type specified for this query. Ignored
        for IPX flow queries. The value of this object cannot be 
        modified when the corresponding instance of cseFlowQueryStatus 
        is 'active'."
    DEFVAL          { { udp , tcp } } 
    ::= { cseFlowQueryEntry 3 }

cseFlowQuerySource OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The source network address and port (if applicable). The
        value of this object cannot be modified when the 
        corresponding instance of 
        cseFlowQueryStatus is 'active'."
    DEFVAL          { '000000000000'H } 
    ::= { cseFlowQueryEntry 4 }

cseFlowQuerySourceMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The source address mask to be applied to the corresponding
        instance of cseFlowQuerySource. The value of this object
        cannot be modified  when the corresponding instance of 
        cseFlowQueryStatus is 'active'."
    DEFVAL          { 'FFFFFFFFFFFF'H } 
    ::= { cseFlowQueryEntry 5 }

cseFlowQueryDestination OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The destination network address and port (if applicable).
        The value of this object cannot be modified when the
        corresponding instance of cseFlowQueryStatus is 'active'."
    DEFVAL          { '000000000000'H } 
    ::= { cseFlowQueryEntry 6 }

cseFlowQueryDestinationMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The destination address mask to be applied to the corresponding
        instance of cseFlowQueryDestination. The value of this object 
        cannot be modified when the corresponding instance of 
        cseFlowQueryStatus is 'active'."
    DEFVAL          { 'FFFFFFFFFFFF'H } 
    ::= { cseFlowQueryEntry 7 }

cseFlowQueryRouterIndex OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Index of the router for which the flows are available.
        An 'all-zero' IP address indicates that the query is for 
        any router. The value of this object cannot be modified
        when the corresponding instance of cseFlowQueryStatus
        is 'active'." 
    ::= { cseFlowQueryEntry 8 }

cseFlowQueryOwner OBJECT-TYPE
    SYNTAX          OwnerString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The manager entity that configured this entry and is therefore
        using the resources assigned to it."
    DEFVAL          { "" } 
    ::= { cseFlowQueryEntry 9 }

cseFlowQueryResultingRows OBJECT-TYPE
    SYNTAX          Integer32 (-1..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The result status of the query. Possible values are:

        -1            - Either the query has not been initiated or 
                        the agent is busy processing this query instance.
                        Time to completion of the query processing
                        depends on the complexity of the query and
                        the number of matches that satisfy this query.

        0..2147483647 - The search has ended and this is the number of 
                        rows in the cseFlowDataTable, resulting 
                        from this query." 
    ::= { cseFlowQueryEntry 10 }

cseFlowQueryResultTotalPkts OBJECT-TYPE
    SYNTAX          CiscoGauge64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The aggregate number of total packets switched by the system on
        all the flows matching this query. This is a snapshot value and 
        is valid only when the corresponding instance of
        cseFlowQueryResultingRows is greater than  or equal to 0." 
    ::= { cseFlowQueryEntry 11 }

cseFlowQueryResultTotalOctets OBJECT-TYPE
    SYNTAX          CiscoGauge64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The aggregate number of total octets switched by the system on
        all the flows matching this query. This is a snapshot value 
        and is valid only when the corresponding instance of
        cseFlowQueryResultingRows is greater than or equal to 0." 
    ::= { cseFlowQueryEntry 12 }

cseFlowQueryResultAvgDuration OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average duration of the flows matching this query. This is
        a snapshot value and is valid only when the corresponding 
        instance of cseFlowQueryResultingRows is greater
        than or equal to 0." 
    ::= { cseFlowQueryEntry 13 }

cseFlowQueryResultAvgIdle OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The elapsed time since the flows were last used, averaged over
        all flows matching this query. This is a snapshot value 
        and is valid only when the corresponding instance of
        cseFlowQueryResultingRows is greater than or equal to 0." 
    ::= { cseFlowQueryEntry 14 }

cseFlowQueryStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status object used to manage rows in this table.
        When set to active(1), the query is initiated. 
        Once initiated, the value may 
        not be modified until the value of cseFlowQueryResultingRows is
        greater than or equal to 0. However, this object can be set to
        active(1) only after all the appropriate objects for this query
        as defined by the value set in the cseFlowQueryMask object,
        have also been set.
        Once a row becomes active, values within the row cannot
        be modified, except by deleting and re-creating it." 
    ::= { cseFlowQueryEntry 15 }

cseFlowQueryCreateTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Time when this query was created." 
    ::= { cseFlowQueryEntry 16 }

cseFlowQueryTotalFlows OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of L3 flows matching the query criterion." 
    ::= { cseFlowQueryEntry 17 }

cseFlowQuerySkipNFlows OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The number of searched flows to be skipped before storing
        any L3 flows in cseFlowDataTable.

        This object can be used along with cseFloQueryTotalFlows
        object to skip previously found flows by setting the variable
        equal to the number of the associated rows in
        cseFlowDataTable, and only query the remaining flows
        in the table.

        Note that due to the dynamical nature of the L3 flows, the
        queried flows may be missed or repeated by setting this object.

        The value of this object cannot be modified
        when the corresponding instance of cseFlowQueryStatus
        is 'active'."
    DEFVAL          { 0 } 
    ::= { cseFlowQueryEntry 18 }
 

-- The Unicast Flow query results data table

cseFlowDataTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseFlowDataEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing L3 flow information corresponding to all
        the completed queries setup in the cseFlowQueryTable, that were 
        initiated on the switch engine(s)."
    ::= { cseFlow 9 }

cseFlowDataEntry OBJECT-TYPE
    SYNTAX          CseFlowDataEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of the cseFlowDataTable used to return
        information about one of the L3 flows which matched the 
        search criteria set by the cseFlowQueryMask object in the 
        corresponding instance of the cseFlowQueryTable."
    INDEX           {
                        entPhysicalIndex,
                        cseFlowQueryIndex,
                        cseFlowDataIndex
                    } 
    ::= { cseFlowDataTable 1 }

CseFlowDataEntry ::= SEQUENCE {
        cseFlowDataIndex       Unsigned32,
        cseFlowDataSrcMac      MacAddress,
        cseFlowDataDstMac      MacAddress,
        cseFlowDataStaticFlow  TruthValue,
        cseFlowDataEncapType   INTEGER,
        cseFlowDataSource      FlowAddressComponent,
        cseFlowDataDestination FlowAddressComponent,
        cseFlowDataDestVlan    VlanIndex,
        cseFlowDataIpQOS       Integer32,
        cseFlowDataIpQOSPolicy Integer32,
        cseFlowDataWhenCreated TimeStamp,
        cseFlowDataLastUsed    TimeStamp,
        cseFlowDataPkts        Gauge32,
        cseFlowDataOctets      CiscoGauge64
}

cseFlowDataIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A number to uniquely identify a result entry that matches a
        particular query for a specific switching engine." 
    ::= { cseFlowDataEntry 1 }

cseFlowDataSrcMac OBJECT-TYPE
    SYNTAX          MacAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Source Mac Address of the router's outgoing interface." 
    ::= { cseFlowDataEntry 2 }

cseFlowDataDstMac OBJECT-TYPE
    SYNTAX          MacAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Destination Mac Address used to forward the packets in
        this flow." 
    ::= { cseFlowDataEntry 3 }

cseFlowDataStaticFlow OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates whether this flow was software-installed." 
    ::= { cseFlowDataEntry 4 }

cseFlowDataEncapType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        ipArpa(1),
                        ipxEthernet(2),
                        ipx802raw(3),
                        ipx802sap(4),
                        ipx802snap(5),
                        other(6)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Protocol encapsulation type used to forward packets in this flow
        to their destination." 
    ::= { cseFlowDataEntry 5 }

cseFlowDataSource OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The source network address and the port (if appropriate) of this
        flow." 
    ::= { cseFlowDataEntry 6 }

cseFlowDataDestination OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The destination network address and port (if appropriate) of this
        flow." 
    ::= { cseFlowDataEntry 7 }

cseFlowDataDestVlan OBJECT-TYPE
    SYNTAX          VlanIndex
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The vlan number on which packets belonging to this flow are
        forwarded." 
    ::= { cseFlowDataEntry 8 }

cseFlowDataIpQOS OBJECT-TYPE
    SYNTAX          Integer32 (0..7)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Level of Quality of service for this IP flow.
        If it is not an IP flow, this object will not be instantiated." 
    ::= { cseFlowDataEntry 9 }

cseFlowDataIpQOSPolicy OBJECT-TYPE
    SYNTAX          Integer32 (0..7)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Level of the Quality of service policy for this IP flow.
        If it is not an IP flow, this object will not be instantiated." 
    ::= { cseFlowDataEntry 10 }

cseFlowDataWhenCreated OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Time when this flow was created in the switching engine." 
    ::= { cseFlowDataEntry 11 }

cseFlowDataLastUsed OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Time since this flow was last used to forward a packet by the
        switching engine." 
    ::= { cseFlowDataEntry 12 }

cseFlowDataPkts OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A snapshot value of the number of packets forwarded on this flow
        at the time of corresponding query." 
    ::= { cseFlowDataEntry 13 }

cseFlowDataOctets OBJECT-TYPE
    SYNTAX          CiscoGauge64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A snapshot value of octets forwarded on this flow at the time of
        corresponding query." 
    ::= { cseFlowDataEntry 14 }
 


cseFlowSwitchControlTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseFlowSwitchControlEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table used to control the L3 flow switching operation, per
        protocol type."
    ::= { cseFlow 10 }

cseFlowSwitchControlEntry OBJECT-TYPE
    SYNTAX          CseFlowSwitchControlEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row for the configuration of Flow switching
        feature for an L3 protocol type."
    INDEX           { cseFlowSwitchProtocol } 
    ::= { cseFlowSwitchControlTable 1 }

CseFlowSwitchControlEntry ::= SEQUENCE {
        cseFlowSwitchProtocol CiscoNetworkProtocol,
        cseFlowSwitchStatus   ControlStatus
}

cseFlowSwitchProtocol OBJECT-TYPE
    SYNTAX          CiscoNetworkProtocol
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Protocol type for which this row instance. Only ip(1) and ipx(14)
        values are currently supported." 
    ::= { cseFlowSwitchControlEntry 1 }

cseFlowSwitchStatus OBJECT-TYPE
    SYNTAX          ControlStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The current status of the global flow switching capability for
        the specified L3 protocol type." 
    ::= { cseFlowSwitchControlEntry 2 }
 


-- Multicast Flow Query table

cseFlowMcastMaxQueries OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Maximum number of query entries allowed to be outstanding
        at any time, in the cseFlowMcastQueryTable.  The typical value
        for this object is 5." 
    ::= { cseFlow 11 }

cseFlowMcastQueryTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseFlowMcastQueryEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A control table used to query the switching engine by
        specifying retrieval criteria for IP multicast L3 flows.
        Each row instance in the table represents a query with
        its parameters.  The resulting data for each instance of
        a query in this table is returned in the 
        cseFlowMcastResultTable.
        The maximum number of entries (rows) in this table cannot
        exceed the value of cseFlowMcastMaxQueries object.

        Unlike unicast switched layer 3 flows, an IP multicast
        switched flow is created and installed by software, and
        is uniquely identified by flow's source IP address, and
        multicast group IP address.  It is stored with input Vlan
        ID in the cache entry, so that the packets in the flow
        will not be replicated and forwarded to the receivers on the
        same (input) Vlan.
        Another difference is that all IP multicast hardware
        switched flows belonging to the same (source, group) are
        stored only on one switch engine on a Cisco L3 switch with
        distributed switch engines, whereas unicast flows identified
        by certain criterion may resident on multiple switch engines 
        in the system."
    ::= { cseFlow 12 }

cseFlowMcastQueryEntry OBJECT-TYPE
    SYNTAX          CseFlowMcastQueryEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of the cesMcastFlowQueryTable used to
        setup retrieval criteria to search for IP multicast L3
        flows on all switching engine entities in the device.
        The actual search is started by setting the value of
        cseFlowMcastQueryStatus to 'active'.  Once a row becomes
        active, values within the row cannot be modified, without
        setting the associated RowStatus object to 'notInService'
        first, or deleting and re-creating the row."
    INDEX           { cseFlowMcastQueryIndex } 
    ::= { cseFlowMcastQueryTable 1 }

CseFlowMcastQueryEntry ::= SEQUENCE {
        cseFlowMcastQueryIndex      Unsigned32,
        cseFlowMcastQueryMask       BITS,
        cseFlowMcastQuerySrc        IpAddress,
        cseFlowMcastQueryGrp        McastGroupIp,
        cseFlowMcastQuerySrcVlan    VlanIndex,
        cseFlowMcastQueryRtrIndex   IpAddress,
        cseFlowMcastQuerySkipNFlows Integer32,
        cseFlowMcastQueryOwner      OwnerString,
        cseFlowMcastQueryTotalFlows Integer32,
        cseFlowMcastQueryRows       Integer32,
        cseFlowMcastQueryStatus     RowStatus,
        cseFlowMcastQueryCreateTime TimeStamp,
        cseFlowMcastQueryMvrf       MplsVpnId,
        cseFlowMcastQueryAddrType   InetAddressType,
        cseFlowMcastQuerySource     InetAddress,
        cseFlowMcastQueryGroup      InetAddress
}

cseFlowMcastQueryIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An arbitrary integer in the range of 1 to
        cseFlowMcastMaxQueries to identify this control query." 
    ::= { cseFlowMcastQueryEntry 1 }

cseFlowMcastQueryMask OBJECT-TYPE
    SYNTAX          BITS {
                        source(0),
                        group(1),
                        vlan(2),
                        router(3),
                        mvrf(4),
                        sourceip(5),
                        groupip(6)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to set up the query criterion for
        the multicast flows of interest.  If any one of the
        defined BITs is set, then the value of the corresponding
        object in the same row instance will be used for the search.

        Specifically, if the 'source(0)' BIT is set, then the 
        cseFlowMcastQuerySrc object will be included in the
        search criterion.

        If the group(1) BIT is set, then the 
        cseFlowMcastQueryGrp object will be included in the
        search criterion.

        If the vlan(2) BIT is set, then the 
        cseFlowMcastQuerySrcVlan object will be included in the 
        search criterion.

        If the router(3) BIT is set, then the
        cseFlowMcastQueryRtrIndex object will be included in the
        search criterion.

        If the mvrf(4) BIT is set, then the
        cseFlowMcastQueryMvrf object will be included in the 
        search criterion; 

        If the sourceip(5) BIT is set, then the
        cseFlowMcastQueryAddrType and cseFlowMcastQuerySource
        objects will be included in the search criterion.

        If the groupip(6) BIT is set, then the
        cseFlowMcastQueryAddrType and cseFlowMcastQueryGroup
        objects will be included in the search criterion.

        If the source(0) or group(1) BIT is set, then the
        sourceip(5) or groupip(6) cannot be set, and vice-versa.

        If any of the BITs in this variable is cleared, the 
        corresponding parameter object in the same row is 
        treated as a wildcard.  When the row is instantiated,
        the BITs in the variable will be cleared, and none of
        query parameter objects in this row will be instantiated.
        This will be considered as a wildcard search for flows
        on the default Multicast Virtual Private Network (MVPN)
        routing/forwarding (MVRF) instance. 
        i.e. it will return all rows corresponding 
        to all established multicast flow entries in the default
        MVRF, in cseFlowMcastResultTable. The address type of this
        wildcard search will be specified be cseFlowMcastQueryAddrType.
        It is SNMP managers' responsibility to set certain
        bits on in this object instance, if necessary,
        and the corresponding flow parameter variables to the 
        appropriate values in order to setup the desired
        query criteria.
        The value of this object can not be altered when the 
        corresponding instance of cseFlowMcastQueryStatus is 'active'."
    DEFVAL          { {  } } 
    ::= { cseFlowMcastQueryEntry 2 }

cseFlowMcastQuerySrc OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "The source address of the IP multicast layer 3 flows.
        This object should be instantiated and assigned a
        proper IP address whenever the 'source' bit of
        cseFlowMcastQueryMask object in the same row is on.
        If the 'source' bit is set, and an appropriate IP address
        is assigned to this object, then only flows with the
        specified source address will be containing in the 
        result table.
        If the 'source' bit in the associated cseFlowMcastQueryMask
        is cleared, this object is ignored during the query, and all
        flows will be considered regardless of their source IP address.

        This object is deprecated and replaced by
        cseFlowMcastQueryAddrType and cseFlowMcastQuerySource." 
    ::= { cseFlowMcastQueryEntry 3 }

cseFlowMcastQueryGrp OBJECT-TYPE
    SYNTAX          McastGroupIp
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "The IP multicast group address of the queried flows.
        This object should be instantiated and set whenever
        the 'group' bit of the associated cseFlowMcastQueryMask object
        is on.
        If the 'group' bit is set, and a multicast group address is 
        assigned to the object, only flows with the specified group
        address will be contained in the result table.
        If the 'group' bit in the associated cseFlowMcastQueryMask
        is cleared, this object is ignored during the query, and all
        flows will be considered regardless of their group address.

        This object is deprecated and replaced by 
        cseFlowMcastQueryAddrType and cseFlowMcastQueryGroup." 
    ::= { cseFlowMcastQueryEntry 4 }

cseFlowMcastQuerySrcVlan OBJECT-TYPE
    SYNTAX          VlanIndex
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The source Vlan ID of the IP multicast layer 3 flows.
        This object should be instantiated and set whenever
        the 'vlan' bit of the associated cseFlowMcastQueryMask object
        is on.
        If the 'vlan' bit is set, and a Vlan ID is assigned to this
        object, only flows belonging to that vlan will be contained
        in the result table.
        If the 'vlan' bit in the associated cseFlowMcastQueryMask object
        is cleared, this object is ignored during the query, and all
        flows will be considered regardless of their vlan IDs." 
    ::= { cseFlowMcastQueryEntry 5 }

cseFlowMcastQueryRtrIndex OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Index of the router for which the multicast flows are
        available, that is the flows would be replicated
        and routed by the specified
        router, should the flows did not get switched.
        This object should be instantiated and set whenever the 'router'
        bit of the asociated cseFlowMcastQueryMask object is on.
        If the 'router' bit is set, and a router's IP address 
        is assigned to this object, then only flows associated with
        that router will be contained in the result table.
        If the 'router' bit in the cseFlowMcastQueryMask object
        is cleared, this object is ignored during the query, and all
        flows will be considered regardless of the routers 
        being switched." 
    ::= { cseFlowMcastQueryEntry 6 }

cseFlowMcastQuerySkipNFlows OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The number of searched flows to be skipped before storing
        any multicast flows in cseFlowMcastResultTable.
        This object can be used along with cseFlowMcastQueryTotalFlows
        object to skip previously found flows by setting the variable
        equal to the number of the associated rows in 
        cseFlowMcastResultTable, and only query the remaining flows
        in the table.
        Note that due to the dynamical nature of the L3 flows, the
        queried flows may be missed or repeated by setting this object."
    DEFVAL          { 0 } 
    ::= { cseFlowMcastQueryEntry 7 }

cseFlowMcastQueryOwner OBJECT-TYPE
    SYNTAX          OwnerString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The manager entity that configured this entry and is therefore
        using the resources assigned to it.  It is used to model an
        administratively assigned name of the owner of a resource.
        It is recommended that this object have one or more the following
        information: IP address, management station name, network
        manager's name, location, or phone number." 
    ::= { cseFlowMcastQueryEntry 8 }

cseFlowMcastQueryTotalFlows OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of flows matching the query criterion." 
    ::= { cseFlowMcastQueryEntry 9 }

cseFlowMcastQueryRows OBJECT-TYPE
    SYNTAX          Integer32 (-1..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicating the status of the query by following values:
        -1 - Either the query has not been started or the
             agent is still processing this query instance.
             It is the default value when the row is instantiated.

        0..2147483647 - The search has ended and this is the
                        number of rows in the cseFlowMcastResultTable,
                        resulting from this query." 
    ::= { cseFlowMcastQueryEntry 10 }

cseFlowMcastQueryStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status object used to manage rows in this table.
        When set to 'active', the query of flows is initiated.
        This object can be set to active only after all the
        appropriate objects for this query as defined by the
        bits in the cseFlowMcastQueryMask object, have also been
        instantiated.  The completion of the query is indicated
        by the value of cseFlowMcastQueryRows as soon as it
        becomes greater than or equal to 0.
        Once a row becomes active, values within the row cannot be
        modified without setting it to 'notInService' first, or just
        deleting and re-creating it.
        To abort a lengthy on-going query, setting this object to
        'notInService', or 'destroy' will terminate a search
        if one is in progress, and cause the associated rows in
        cseFlowMcastResultTable, if any, to be deleted." 
    ::= { cseFlowMcastQueryEntry 11 }

cseFlowMcastQueryCreateTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Time when this query was last set to active." 
    ::= { cseFlowMcastQueryEntry 12 }

cseFlowMcastQueryMvrf OBJECT-TYPE
    SYNTAX          MplsVpnId
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The human-readable name of the Multicast
        Virtual Private Network (MVPN) routing/forwarding
        instance (MVRF). When the 'mvrf' bit of
        cseFlowMcastQueryMask object in the same row is on,
        an appropriate value should be specified and only flows
        with the specified MVRF name will be contained in the
        result table. If the 'mvrf' bit in the associated
        cseFlowMcastQueryMask is cleared, this object is ignored
        during the query, and all the flows corresponding to the
        default MVRF will be considered." 
    ::= { cseFlowMcastQueryEntry 13 }

cseFlowMcastQueryAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The Internet address type for this multicast search
        query."
    DEFVAL          { ipv4 } 
    ::= { cseFlowMcastQueryEntry 14 }

cseFlowMcastQuerySource OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The source Internet address of the IP multicast layer 3
        flows. When the 'sourceip' bit of cseFlowMcastQueryMask
        cseFlowMcastQueryMask object in the same row is on,
        an appropriate value should be specified and only flows
        with the specified source address will be contained in the
        result table. If the 'sourceip' bit in the associated
        cseFlowMcastQueryMask is cleared, this object is ignored
        during the query, and all flows will be considered regardless
        of their source address.

        The type of this address is determined by the value of the 
        cseFlowMcastQueryAddrType object.

        The default value of this object is all zeros."
    DEFVAL          { '00000000'H } 
    ::= { cseFlowMcastQueryEntry 15 }

cseFlowMcastQueryGroup OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The multicast group Internet address of the queried flows.
        When the 'mvrf' bit of cseFlowMcastQueryMask object 
        in the same row is on, an appropriate value should be 
        specified and only flows with the specified group address
        will be contained in the result table. If the 'groupip' bit 
        in the associated cseFlowMcastQueryMask is cleared, this 
        object is ignored during the query, and all flows will
        be considered regardless of their group address.

        The type of this address is determined by the value of the
        cseFlowMcastQueryAddrType object.

        The default value of this object is all zeros."
    DEFVAL          { '00000000'H } 
    ::= { cseFlowMcastQueryEntry 16 }
 

-- The multicast flow query result data table

cseFlowMcastResultTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseFlowMcastResultEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing current IP multicast flow information
        corresponding to all the completed queries set up in
        the cseFlowMcastQueryTable, that were initiated on the switch
        engine(s).  The query result will not become available until
        the current search completes."
    ::= { cseFlow 13 }

cseFlowMcastResultEntry OBJECT-TYPE
    SYNTAX          CseFlowMcastResultEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of cseFlowMcastResultTable, containing
        information about an IP multicast layer 3 flow that matchs
        the search criteria set in the corresponding row of
        cseFlowMcastQueryTable.  This row instance is indexed by
        the query index (cseFlowMcastQueryIndex), the switch engine
        entity (entPhysicalIndex), and data entry index
        (cseFlowMcastResultIndex).  The value of entPhysicalIndex
        object is assigned by Entity-MIB, and uniquely identifies
        a switching engine on which the IP multicast flow is stored."
    INDEX           {
                        cseFlowMcastQueryIndex,
                        entPhysicalIndex,
                        cseFlowMcastResultIndex
                    } 
    ::= { cseFlowMcastResultTable 1 }

CseFlowMcastResultEntry ::= SEQUENCE {
        cseFlowMcastResultIndex      Integer32,
        cseFlowMcastResultGrp        McastGroupIp,
        cseFlowMcastResultSrc        IpAddress,
        cseFlowMcastResultSrcVlan    VlanIndex,
        cseFlowMcastResultRtrIp      IpAddress,
        cseFlowMcastResultRtrMac     MacAddress,
        cseFlowMcastResultCreatedTS  TimeStamp,
        cseFlowMcastResultLastUsedTS TimeStamp,
        cseFlowMcastResultPkts       Counter64,
        cseFlowMcastResultOctets     Counter64,
        cseFlowMcastResultDstVlans   OCTET STRING,
        cseFlowMcastResultDstVlans2k OCTET STRING,
        cseFlowMcastResultDstVlans3k OCTET STRING,
        cseFlowMcastResultDstVlans4k OCTET STRING,
        cseFlowMcastResultMvrf       MplsVpnId,
        cseFlowMcastResultAddrType   InetAddressType,
        cseFlowMcastResultGroup      InetAddress,
        cseFlowMcastResultSource     InetAddress,
        cseFlowMcastResultFlowType   INTEGER,
        cseFlowMcastResultHFlag1k2k  OCTET STRING,
        cseFlowMcastResultHFlag3k4k  OCTET STRING
}

cseFlowMcastResultIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A positive integer which uniquely identify a result entry
        on a specific switching engine matching a particular query." 
    ::= { cseFlowMcastResultEntry 1 }

cseFlowMcastResultGrp OBJECT-TYPE
    SYNTAX          McastGroupIp
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The multicast group IP address of the multicast layer 3 flow.

        This object is deprecated and replaced by 
        cseFlowMcastResultAddrType and cseFlowMcastResultGroup." 
    ::= { cseFlowMcastResultEntry 2 }

cseFlowMcastResultSrc OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The source address of the multicast layer 3 flow.

        This object is deprecated and replaced by 
        cseFlowMcastResultAddrType and cseFlowMcastResultSource." 
    ::= { cseFlowMcastResultEntry 3 }

cseFlowMcastResultSrcVlan OBJECT-TYPE
    SYNTAX          VlanIndex
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The source Vlan ID of the IP multicast layer 3 flow." 
    ::= { cseFlowMcastResultEntry 4 }

cseFlowMcastResultRtrIp OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The interface IP address of the router this multicast flow
        is switching for.  Since IP multicast flows can only be
        established for a router's trunk ports, it is the primary
        IP address of the router's trunk link that connects to the
        switch." 
    ::= { cseFlowMcastResultEntry 5 }

cseFlowMcastResultRtrMac OBJECT-TYPE
    SYNTAX          MacAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The default MAC address of the router the multicast flow is
        switching for.  Different multicast flows switching different
        ports of the same router will have the identical value of this
        object." 
    ::= { cseFlowMcastResultEntry 6 }

cseFlowMcastResultCreatedTS OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Time when the IP multicast flow was created." 
    ::= { cseFlowMcastResultEntry 7 }

cseFlowMcastResultLastUsedTS OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Time when this IP multicast flow was last used." 
    ::= { cseFlowMcastResultEntry 8 }

cseFlowMcastResultPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of multicast traffic packets forwarded
        for this flow (replicated packets are not counted)." 
    ::= { cseFlowMcastResultEntry 9 }

cseFlowMcastResultOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of multicast traffic octets forwarded
        for this flow (replicated packets are not counted)." 
    ::= { cseFlowMcastResultEntry 10 }

cseFlowMcastResultDstVlans OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..128))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string of octets containing one bit per VLAN.
        Each octet within the value of this object specifies a
        set of eight VLANs, e.g. the first octet corresponding to
        VLANs with VlanIndex values of 0 through 7, the second
        octet to VLANs 8 through 15, etc.  Within each octet,
        the most significant bit represents the lowest numbered
        VLAN, and the least significant bit represents the highest
        numbered VLAN, thus each vlan is represented by a single bit
        within the octet.  The bits in this object will be set
        to '1' if the corresponding Vlans are in the out-going
        interface (vlan) list of the IP multicast flow described
        by this row instance." 
    ::= { cseFlowMcastResultEntry 11 }

cseFlowMcastResultDstVlans2k OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..128))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string of octets containing one bit per VLAN,
        with VlanIndex values of 1024 through 2047,with
        each octet within the value of this object specifies a
        set of eight VLANs, e.g. the first octet corresponding to
        VLANs with VlanIndex values of 1024 through 1031, the second
        octet to VLANs 1032 through 1039 etc.  Within each octet,
        the most significant bit represents the lowest numbered
        VLAN, and the least significant bit represents the highest
        numbered VLAN, thus each vlan is represented by a single bit
        within the octet.  The bits in this object will be set
        to '1' if the corresponding Vlans are in the out-going    
        interface (vlan) list of the IP multicast flow described    
        by this row instance." 
    ::= { cseFlowMcastResultEntry 12 }

cseFlowMcastResultDstVlans3k OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..128))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string of octets containing one bit per VLAN.
        With VlanIndex values of 2048 through 3071 with
        each octet within the value of this object specifies a
        set of eight VLANs, e.g. the first octet corresponding to
        VLANs with VlanIndex values of 2048 through 2055, the second
        octet to VLANs 2056 through 2063 etc.  Within each octet,
        the most significant bit represents the lowest numbered
        VLAN, and the least significant bit represents the highest
        numbered VLAN, thus each vlan is represented by a single bit
        within the octet.  The bits in this object will be set   
        to '1' if the corresponding Vlans are in the out-going 
        interface (vlan) list of the IP multicast flow described  
        by this row instance." 
    ::= { cseFlowMcastResultEntry 13 }

cseFlowMcastResultDstVlans4k OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..128))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string of octets containing one bit per VLAN.
        With VlanIndex values of 3072 through 4095, with
        each octet within the value of this object specifies a
        set of eight VLANs, e.g. the first octet corresponding to
        VLANs with VlanIndex values of 3072 through 3079 the second
        octet to VLANs 3080 through 3087 etc.  Within each octet,
        the most significant bit represents the lowest numbered
        VLAN, and the least significant bit represents the highest
        numbered VLAN, thus each vlan is represented by a single bit
        within the octet.  The bits in this object will be set   
        to '1' if the corresponding Vlans are in the out-going 
        interface (vlan) list of the IP multicast flow described  
        by this row instance." 
    ::= { cseFlowMcastResultEntry 14 }

cseFlowMcastResultMvrf OBJECT-TYPE
    SYNTAX          MplsVpnId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The MVRF to which this flow belongs to." 
    ::= { cseFlowMcastResultEntry 15 }

cseFlowMcastResultAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Internet address type of cseFlowMcastResultGroup
        and cseFlowMcastResultSource." 
    ::= { cseFlowMcastResultEntry 16 }

cseFlowMcastResultGroup OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The multicast group IP address of the multicast layer
        3 flow." 
    ::= { cseFlowMcastResultEntry 17 }

cseFlowMcastResultSource OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The source address of the multicast layer 3 flow." 
    ::= { cseFlowMcastResultEntry 18 }

cseFlowMcastResultFlowType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        other(1),
                        rpfMfd(2),
                        partialSC(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the type of multicast layer 3 flow.

        other       - Multicast flow type is none of the followoing. 

        rpfMfd      - This flow is a RPF MFD flow.

        partial     - This flow is a partial shortcut flow." 
    ::= { cseFlowMcastResultEntry 19 }

cseFlowMcastResultHFlag1k2k OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..256))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string of octets containing one bit per out-going
        interface (VLAN) with VlanIndex values of 0 through 2047.
        Each octet within the value of this object specifies
        a set of eight VLANs, e.g. the first octet
        corresponding to VLANs with VlanIndex values of 
        0 through 7, the second octet to VLANs 8 through 15, 
        etc. Within each octet, the most significant bit 
        represents the lowest numbered VLAN, and the least
        significant bit represents the highest numbered VLAN,
        thus each vlan is represented by a single bit within the
        octet.

        The bits in this object will be set to '1' if the 
        multicast layer 3 flow described by this row instance
        is hardware switched on the corresponding VLAN.
        If the length of this string is less than 256 octets,
        any 'missing' octets are assumed to contain the value 
        of zero." 
    ::= { cseFlowMcastResultEntry 20 }

cseFlowMcastResultHFlag3k4k OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..256))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string of octets containing one bit per out-going
        interface (VLAN) with VlanIndex values of 2048 through
        4095. Each octet within the value of this object 
        specifies a set of eight VLANs, e.g. the first octet 
        corresponding to VLANs with VlanIndex values of 2048
        through 2055, the second octet to VLANs 2056 through
        2063 etc. Within each octet, the most significant
        bit represents the lowest numbered VLAN, and the least
        significant bit represents the highest numbered VLAN,
        thus each vlan is represented by a single bit within 
        the octet.

        The bits in this object will be set to '1' if the 
        multicast layer 3 flow described by this row instance
        is hardware switched on the corresponding VLAN.
        If the length of this string is less than 256 octets,
        any 'missing' octets are assumed to contain the value
        of zero." 
    ::= { cseFlowMcastResultEntry 21 }
 


-- Multicast MLS-SE global configuration

cseFlowMcastSwitchStatus OBJECT-TYPE
    SYNTAX          ControlStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The current status of the global IP multicast flow switching
        capability.  When enabled, the switch engine will be able to
        install multicast flow entries in its L3 forwarding table,
        and perform hardware assisted switching for the flows." 
    ::= { cseFlow 14 }

-- Now that our hardware can L3 switch IPX traffic

cseFlowIPXEstablishedAgingTime OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The administrative aging time for established IPX flows. The
        default value for this object is implementation specific.
        The corresponding operational object is
        cseFlowOperIPXAgingTime." 
    ::= { cseFlow 15 }

cseStaticIpxExtRouterTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseStaticIpxExtRouterEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of external routers which are enabled for
        Layer 3 IPX switching by the switching engine.
        This table may contain routers
        that have not yet been discovered by the device."
    ::= { cseFlow 16 }

cseStaticIpxExtRouterEntry OBJECT-TYPE
    SYNTAX          CseStaticIpxExtRouterEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row in the cseStaticIpxExtRouterTable for
        enabling an external router to be installed in the
        switch's router table. The entry is created and deleted
        by using cseStaticIpxRouterStatus."
    INDEX           { cseRouterIndex } 
    ::= { cseStaticIpxExtRouterTable 1 }

CseStaticIpxExtRouterEntry ::= SEQUENCE {
        cseStaticIpxRouterName   DisplayString,
        cseStaticIpxRouterOwner  OwnerString,
        cseStaticIpxRouterStatus RowStatus
}

cseStaticIpxRouterName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "DNS name (if available) of the external router." 
    ::= { cseStaticIpxExtRouterEntry 1 }

cseStaticIpxRouterOwner OBJECT-TYPE
    SYNTAX          OwnerString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "String indicating the owner who created the static entry." 
    ::= { cseStaticIpxExtRouterEntry 2 }

cseStaticIpxRouterStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Used to manage creation and deletion of rows in this table.
        Once a row becomes active, values within that row cannot be
        modified except by deleting and creating the row." 
    ::= { cseStaticIpxExtRouterEntry 3 }
 


cseFlowOperEstablishedAgingTime OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The operational aging time for IP established flows." 
    ::= { cseFlow 17 }

cseFlowOperFastAgingTime OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The operational fast aging time for the established flow
        entries, that have less number of packets than the value set 
        in the cseFlowOperFastAgePktThreshold,switched within this
        time." 
    ::= { cseFlow 18 }

cseFlowOperFastAgePktThreshold OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The operational packet threshold for the
        cseFlowOperFastAgingTime." 
    ::= { cseFlow 19 }

cseFlowOperIPXAgingTime OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The operational aging time for established IPX flows." 
    ::= { cseFlow 20 }
-- The bridged flow statistics control table

cseBridgedFlowStatsCtrlTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseBridgedFlowStatsCtrlEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table controls the reporting of intra-vlan statistics
        for bridged flow per vlan. When a vlan is created in
        a device supporting this table, a corresponding entry
        of this table will be added."
    ::= { cseFlow 21 }

cseBridgedFlowStatsCtrlEntry OBJECT-TYPE
    SYNTAX          CseBridgedFlowStatsCtrlEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance contains the configuration to enable
        or disable the reporting of intra-vlan statistics for
        bridged flow per vlan."
    INDEX           { cseBridgedFlowVlan } 
    ::= { cseBridgedFlowStatsCtrlTable 1 }

CseBridgedFlowStatsCtrlEntry ::= SEQUENCE {
        cseBridgedFlowVlan            VlanIndex,
        cseFlowBridgedFlowStatsEnable TruthValue
}

cseBridgedFlowVlan OBJECT-TYPE
    SYNTAX          VlanIndex
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the Vlan number on which the reporting of
        intra-vlan bridged flow statistics is configured." 
    ::= { cseBridgedFlowStatsCtrlEntry 1 }

cseFlowBridgedFlowStatsEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether intra-vlan bridged flow statistics is
        enabled. If this object is set to 'true', intra-vlan
        bridged flow statistics is reported in cseFlowDataTable
        when a corresponding query is set up in cseFlowQueryTable.
        If this object is set to 'false', intra-vlan bridged flow
        statistics is not reported. The default is false."
    DEFVAL          { false } 
    ::= { cseBridgedFlowStatsCtrlEntry 2 }
 


cseFlowIPFlowMask OBJECT-TYPE
    SYNTAX          INTEGER  {
                        dstOnly(1),
                        srcDst(2),
                        fullFlow(3),
                        srcOnly(4),
                        intDstSrc(5),
                        intFull(6),
                        null(7),
                        intDstOnly(8),
                        intSrcOnly(9)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates the flow mask for IP flows.

        If dstOnly(1) is used, it enables flows based on Layer 3
        destination addresses only.

        If srcDst(2) is used, it enables flows based on both Layer 3
        source and destination addresses only.

        If fullFlow(3) is used, it enables flows based on Layer 4 port 
        numbers in addition to source and destination addresses.

        If srcOnly(4) is used, it enables flows based on Layer 3
        source addresses only.

        If intDstSrc(5) is used, it enables flows based on source
        interface in addition to source and destination addresses. 

        If intFull(6) is used, it enables flows based on source 
        interface in addition to Layer 4 port numbers, source and 
        destination addresses.

        If null(7) is used, no flow will be enabled.

        If intDstOnly(8) is used, it enables flows based on source
        interface in addition to the destination addresses.

        If intSrcOnly(9) is used, it enables flows based on source
        interface in addition to the source addresses." 
    ::= { cseFlow 22 }

cseFlowIPXFlowMask OBJECT-TYPE
    SYNTAX          INTEGER  {
                        dstOnly(1),
                        srcDst(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the flow mask for IPX flows.

        If dstOnly(1) is used, it enables flows based on Layer 3
        destination addresses only.

        If srcDst(2) is used, it enables flows based on both Layer 3
        source and destination addresses only." 
    ::= { cseFlow 23 }

cseFlowLongAgingTime OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The administrative long aging time for the established
        flow entries. Setting to value of 0 turns off long aging." 
    ::= { cseFlow 24 }
-- The protocol exclude table

cseFlowExcludeTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseFlowExcludeEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table controls the flow creation based on protocol
        and port number. If a packet matches the protocol and
        port number specified in this table entries, a flow
        entry will not be established."
    ::= { cseFlow 25 }

cseFlowExcludeEntry OBJECT-TYPE
    SYNTAX          CseFlowExcludeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance contains the configuration to enable or
        disable the establishment of flow entry for matching
        traffic."
    INDEX           { cseFlowExcludePort } 
    ::= { cseFlowExcludeTable 1 }

CseFlowExcludeEntry ::= SEQUENCE {
        cseFlowExcludePort     CiscoPort,
        cseFlowExcludeProtocol INTEGER,
        cseFlowExcludeStatus   RowStatus
}

cseFlowExcludePort OBJECT-TYPE
    SYNTAX          CiscoPort
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the TCP or UDP port number that matching
        traffic will be excluded from flow establishment.
        The value of 0 is not allowed." 
    ::= { cseFlowExcludeEntry 1 }

cseFlowExcludeProtocol OBJECT-TYPE
    SYNTAX          INTEGER  {
                        udp(1),
                        tcp(2),
                        both(3)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Indicates the protocol that matching traffic will be
        excluded from flow establishment." 
    ::= { cseFlowExcludeEntry 2 }

cseFlowExcludeStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row. New rows are created
        using 'createAndGo' and deleted using 'destroy'.

        Once 'active' this object may be set to only 'destroy'.
        cseFlowExcludeProtocol may be modified at any time (even
        while the row is active)." 
    ::= { cseFlowExcludeEntry 3 }
 

-- The flow statistics table

cseFlowStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseFlowStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing flow statistics information on each switching
        engine."
    ::= { cseFlow 26 }

cseFlowStatsEntry OBJECT-TYPE
    SYNTAX          CseFlowStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of cseFlowStatsTable, containing flow
        statistics maintained by a switching engine entity
        (identified by entPhysicalIndex). Each switching engine
        managed by this MIB module has an entry in this table."
    INDEX           { entPhysicalIndex } 
    ::= { cseFlowStatsTable 1 }

CseFlowStatsEntry ::= SEQUENCE {
        cseFlowTotalFlows     Gauge32,
        cseFlowTotalIpv4Flows Gauge32
}

cseFlowTotalFlows OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of flow entries installed in
        this switching engine." 
    ::= { cseFlowStatsEntry 1 }

cseFlowTotalIpv4Flows OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of IPv4 flow entries in
        this switching engine." 
    ::= { cseFlowStatsEntry 2 }
 


-- Optional NetFlow Lan Switching group

cseNetflowLSExportStatus OBJECT-TYPE
    SYNTAX          ControlStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Status of the Netflow LAN Switching data export feature." 
    ::= { cseNetflowLS 1 }

cseNetflowLSExportHost OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-write
    STATUS          deprecated
    DESCRIPTION
        "Network(IP) address in dotted decimal format or the DNS hostname
        of the host to which Netflow LAN switching statistics are 
        exported.

        This object is deprecated and replaced by cndeCollectorAddress
        in CISCO-NDE-MIB." 
    ::= { cseNetflowLS 2 }

cseNetflowLSExportTransportNumber OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-write
    STATUS          deprecated
    DESCRIPTION
        "The transport(UDP) port number to be used for the Netflow LAN
        switching statistics being exported.

        This object is deprecated and replaced by cndeCollectorPort
        in CISCO-NDE-MIB." 
    ::= { cseNetflowLS 3 }

cseNetflowLSExportDataSource OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The source network address used as a filter for selecting
        the flows to which the netflow LAN switching data export 
        feature is applied." 
    ::= { cseNetflowLS 4 }

cseNetflowLSExportDataSourceMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The mask to be applied to the corresponding instance of
        cseNetflowExportDataSource." 
    ::= { cseNetflowLS 5 }

cseNetflowLSExportDataDest OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The destination network address used as a filter for
        selecting the flows to which the netflow LAN switching
        data export feature is applied." 
    ::= { cseNetflowLS 6 }

cseNetflowLSExportDataDestMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The mask to be applied to its corresponding instance
        of cseNetflowExportDataDest." 
    ::= { cseNetflowLS 7 }

cseNetflowLSExportDataProtocol OBJECT-TYPE
    SYNTAX          Integer32 (0..255)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The protocol used as a filter for selecting the
        flows to which the netflow LAN switching data export feature
        is applied."
    REFERENCE       "The protocol value is defined in the RFC 1700." 
    ::= { cseNetflowLS 8 }

cseNetflowLSExportDataFilterSelection OBJECT-TYPE
    SYNTAX          INTEGER  {
                        included(1),
                        excluded(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The filter set can be chosen either included(1) or
        excluded(2) mutually exclusive.  If this object is set to
              included(1) -   exports the flows that match
                              cseNetflowLSExportDataSource, 
                              cseNetflowLSExportDataSourceMask, 
                              cseNetflowLSExportDataDest,
                              cseNetflowLSExportDataDestMask and 
                              cseNetflowLSExportDataProtocol.

              excluded(2) -   exports the flows that don't match
                              cseNetflowLSExportDataSource, 
                              cseNetflowLSExportDataSourceMask, 
                              cseNetflowLSExportDataDest,
                              cseNetflowLSExportDataDestMask and 
                              cseNetflowLSExportDataProtocol." 
    ::= { cseNetflowLS 9 }

cseNetflowLSExportNDEVersionNumber OBJECT-TYPE
    SYNTAX          Integer32 (1..65535)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The netflow data export version number which is
        supported by the device.
        The typical value of this object can be 1, 7 or 8." 
    ::= { cseNetflowLS 10 }

cseNetflowLSFilterSupport OBJECT-TYPE
    SYNTAX          INTEGER  {
                        single(1),
                        multiple(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates whether this device supports single filter or
        multiple filters.

        single   - use objects in cseNDESingleFilterGroupRev1 to
                   configure NDE filtering paramaters.

        multiple - use objects in cseNDEMultipleFiltersGroup to
                   configure NDE filtering paramaters." 
    ::= { cseNetflowLS 11 }

cseNetflowLSFilterTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseNetflowLSFilterEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A Table containing Netflow Data Export filtering
        configuration."
    ::= { cseNetflowLS 12 }

cseNetflowLSFilterEntry OBJECT-TYPE
    SYNTAX          CseNetflowLSFilterEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptutal row in the cseNetflowLSFilterTable,
        representing a NDE filter configuration."
    INDEX           { cseNetflowLSFilterIndex } 
    ::= { cseNetflowLSFilterTable 1 }

CseNetflowLSFilterEntry ::= SEQUENCE {
        cseNetflowLSFilterIndex          Unsigned32,
        cseNetflowLSFilterDataSource     FlowAddressComponent,
        cseNetflowLSFilterDataSourceMask FlowAddressComponent,
        cseNetflowLSFilterDataDest       FlowAddressComponent,
        cseNetflowLSFilterDataDestMask   FlowAddressComponent,
        cseNetflowLSFilterDataProtocol   Integer32,
        cseNetflowLSFilterSelection      INTEGER,
        cseNetflowLSFilterStatus         RowStatus
}

cseNetflowLSFilterIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An arbitrary integer which uniquely identifies the filter" 
    ::= { cseNetflowLSFilterEntry 1 }

cseNetflowLSFilterDataSource OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The source network address used as a filter for selecting
        the flows to which the netflow LAN switching data export
        feature is applied. If cseNetflowLSNDEFilterDataSource 
        contains all zeros, then the 
        cseNetflowLSNDEFilterDataSource object will not be 
        included in the filtering criterion."
    DEFVAL          { '000000000000'H } 
    ::= { cseNetflowLSFilterEntry 2 }

cseNetflowLSFilterDataSourceMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The mask to be applied to the corresponding instance of
        cseNetflowExportDataSource.
        If cseNetflowLSFilterDataSourceMask contains all zeros, 
        then the cseNetflowLSFilterDataSourceMask object will
        not be included in the filtering criterion."
    DEFVAL          { '000000000000'H } 
    ::= { cseNetflowLSFilterEntry 3 }

cseNetflowLSFilterDataDest OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The destination network address used as a filter for
        selecting the flows to which the netflow LAN switching data
        export feature is applied.
        If cseNetflowLSFilterDataDest contains all zeros, then the
        cseNetflowLSFilterDataDest object will not be included in
        the filtering criterion."
    DEFVAL          { '000000000000'H } 
    ::= { cseNetflowLSFilterEntry 4 }

cseNetflowLSFilterDataDestMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The mask to be applied to its corresponding instance
        of cseNetflowExportDataDest.
        If cseNetflowLSFilterDataDestMask contains all zeros, 
        then the cseNetflowLSFilterDataDestMask object will not be
        included in the filtering criterion."
    DEFVAL          { '000000000000'H } 
    ::= { cseNetflowLSFilterEntry 5 }

cseNetflowLSFilterDataProtocol OBJECT-TYPE
    SYNTAX          Integer32 (0..255)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The protocol used as a filter for selecting the
        flows to which the netflow LAN switching data export
        feature is applied.
        The default value is set to 0, to specify that no value
        has been set.
        If cseNetflowLSFilterDataProtocol is set to 0, then the
        cseNetflowLSFilterDataProtocol object will not be included in
        the filtering criterion."
    DEFVAL          { 0 } 
    ::= { cseNetflowLSFilterEntry 6 }

cseNetflowLSFilterSelection OBJECT-TYPE
    SYNTAX          INTEGER  {
                        included(1),
                        excluded(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The filter set can be chosen either included(1) or
        excluded(2).
        If this object is set to
        included(1) -   exports the flows that match
                        cseNetflowLSFilterDataSource,
                        cseNetflowLSFilterDataSourceMask,
                        cseNetflowLSFilterDataDest,
                        cseNetflowLSFilterDataDestMask and 
                        cseNetflowLSFilterDataProtocol.

        excluded(2) -   exports the flows that don't match
                        cseNetflowLSFilterDataSource,
                        cseNetflowLSFilterDataSourceMask,
                        cseNetflowLSFilterDataDest,
                        cseNetflowLSFilterDataDestMask and 
                        cseNetflowLSFilterDataProtocol." 
    ::= { cseNetflowLSFilterEntry 7 }

cseNetflowLSFilterStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status object used to manage the rows in this table.
        Once a row becomes active, values within that row cannot be
        modified except by deleting and creating the row." 
    ::= { cseNetflowLSFilterEntry 8 }
 


cseNetFlowIfIndexEnable OBJECT-TYPE
    SYNTAX          BITS {
                        destIfIndex(0),
                        srcIfIndex(1)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether ifIndex reporting in NDE (Netflow
        Data Export) is enabled.

        if bit destIfIndex(0) is on, destination ifIndex reporting
        in NDE is enabled.

        if bit srcIfIndex(1) is on, source ifIndex reporting in NDE
        is enabled." 
    ::= { cseNetflowLS 13 }

cseNetflowASInfoExportCtrl OBJECT-TYPE
    SYNTAX          INTEGER  {
                        disable(1),
                        originate(2),
                        peer(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether export of Autonomous System(AS) number
        information, in the NDE records, is enabled.

        disable   - Disables the export of AS number information.

        originate - Enables the export of origination AS numbers of
                    source and destination IP addresses.

        peer      - Enables the export of peer AS numbers of
                    source and destination IP addresses." 
    ::= { cseNetflowLS 14 }

cseNetflowPerVlanIfGlobalEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether 'creation of Netflow entries per
        VLAN interface' feature is enabled at the device level. 

        If this object is set to 'false',
        netflow entries will be created for all VLANs.

        If this object is set to 'true', creation of netflow   
        entries can be controlled by cseNetflowPerVlanIfCtrlTable." 
    ::= { cseNetflowLS 15 }

cseNetflowPerVlanIfCtrlTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseNetflowPerVlanIfCtrlEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table to control netflow entry creation for each VLAN.
        When a VLAN is created, a corresponding entry is added 
        to this table."
    ::= { cseNetflowLS 16 }

cseNetflowPerVlanIfCtrlEntry OBJECT-TYPE
    SYNTAX          CseNetflowPerVlanIfCtrlEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing the configuration to enable or
        disable netflow entry creation for each VLAN."
    INDEX           { cseNetflowPerVlanIfCtrlVlan } 
    ::= { cseNetflowPerVlanIfCtrlTable 1 }

CseNetflowPerVlanIfCtrlEntry ::= SEQUENCE {
        cseNetflowPerVlanIfCtrlVlan VlanIndex,
        cseNetflowPerVlanIfEnable   TruthValue
}

cseNetflowPerVlanIfCtrlVlan OBJECT-TYPE
    SYNTAX          VlanIndex
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the VLAN number on which creation of netflow
        entries is configured." 
    ::= { cseNetflowPerVlanIfCtrlEntry 1 }

cseNetflowPerVlanIfEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether creation of netflow entries is enabled
        on this VLAN.

        If this object is set to 'true', the system will create 
        netflow entries for this VLAN.

        If this object is set to 'false', the system will not create
        any netflow entries for this VLAN.

        When the value of cseNetflowPerVlanIfGlobalEnable is 'false',
        this object will not take effect." 
    ::= { cseNetflowPerVlanIfCtrlEntry 2 }
 

-- L3 switching statistics

cseL3StatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseL3StatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing L3 statistics information on each switching
        engine."
    ::= { cseL3Objects 1 }

cseL3StatsEntry OBJECT-TYPE
    SYNTAX          CseL3StatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of cseL3StatsTable, containing L3 statistics
        maintained by a switching engine entity (identified by
        entPhysicalIndex).  Each switching engine managed by this
        MIB module has an entry in this table."
    INDEX           { entPhysicalIndex } 
    ::= { cseL3StatsTable 1 }

CseL3StatsEntry ::= SEQUENCE {
        cseL3SwitchedTotalPkts   Counter32,
        cseL3SwitchedTotalOctets Counter64,
        cseL3CandidateFlowHits   Counter32,
        cseL3EstablishedFlowHits Counter32,
        cseL3ActiveFlows         Gauge32,
        cseL3FlowLearnFailures   Counter32,
        cseL3IntFlowInvalids     Counter32,
        cseL3ExtFlowInvalids     Counter32,
        cseL3SwitchedPktsPerSec  Counter32
}

cseL3SwitchedTotalPkts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of packets switched at Layer 3 by this switching
        engine." 
    ::= { cseL3StatsEntry 1 }

cseL3SwitchedTotalOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of octets in the total packets switched at Layer 3 by this
        switching engine." 
    ::= { cseL3StatsEntry 2 }

cseL3CandidateFlowHits OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of L3 Cache hits for the candidate flow entries in this
        switching engine." 
    ::= { cseL3StatsEntry 3 }

cseL3EstablishedFlowHits OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of L3 Cache hits for established flow entries in this
        switching engine." 
    ::= { cseL3StatsEntry 4 }

cseL3ActiveFlows OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of active flows in the Layer 3 flow table of this switching
        engine." 
    ::= { cseL3StatsEntry 5 }

cseL3FlowLearnFailures OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of flows that failed to be learned because the Layer 3
        flow table in this switching engine was full." 
    ::= { cseL3StatsEntry 6 }

cseL3IntFlowInvalids OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of flow invalidation events received by this switching
        engine from the internal router(s)." 
    ::= { cseL3StatsEntry 7 }

cseL3ExtFlowInvalids OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of flow invalidation events received by this switching
        engine from external routers." 
    ::= { cseL3StatsEntry 8 }

cseL3SwitchedPktsPerSec OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets switched per second at Layer 3 by this
        switching engine." 
    ::= { cseL3StatsEntry 9 }
 

-- Per-VLAN L3 statistics

cseL3VlanStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseL3VlanStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing per-VLAN, Layer 3 statistics information per
        switching engine."
    ::= { cseL3Objects 2 }

cseL3VlanStatsEntry OBJECT-TYPE
    SYNTAX          CseL3VlanStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row of cseL3VlanStatsTable, containing per-VLAN
        Layer 3 statistics maintained by a switching engine (identified
        by entPhysicalIndex).  An entry exists for each known VLAN for
        each switching engine."
    INDEX           {
                        entPhysicalIndex,
                        cseL3VlanIndex
                    } 
    ::= { cseL3VlanStatsTable 1 }

CseL3VlanStatsEntry ::= SEQUENCE {
        cseL3VlanIndex            VlanIndex,
        cseL3VlanInPkts           Counter64,
        cseL3VlanInOctets         Counter64,
        cseL3VlanOutPkts          Counter64,
        cseL3VlanOutOctets        Counter64,
        cseL3VlanInUnicastPkts    Counter64,
        cseL3VlanInUnicastOctets  Counter64,
        cseL3VlanOutUnicastPkts   Counter64,
        cseL3VlanOutUnicastOctets Counter64
}

cseL3VlanIndex OBJECT-TYPE
    SYNTAX          VlanIndex
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Vlan number for which the statistics are maintained by this
        entry." 
    ::= { cseL3VlanStatsEntry 1 }

cseL3VlanInPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets Layer 3 forwarded from this Vlan to some
        other VLAN by this switching engine." 
    ::= { cseL3VlanStatsEntry 2 }

cseL3VlanInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of octets in packets Layer-3 forwarded from this Vlan
        to some other VLAN by this switching engine." 
    ::= { cseL3VlanStatsEntry 3 }

cseL3VlanOutPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets Layer-3 forwarded to this Vlan by this
        switching engine." 
    ::= { cseL3VlanStatsEntry 4 }

cseL3VlanOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of octets in packets Layer-3 forwarded to this Vlan
        by this switching engine." 
    ::= { cseL3VlanStatsEntry 5 }

cseL3VlanInUnicastPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of unicast packets Layer 3 forwarded from this Vlan
        to some other VLAN by this switching engine." 
    ::= { cseL3VlanStatsEntry 6 }

cseL3VlanInUnicastOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of octets in unicast packets Layer-3 forwarded from
        this Vlan to some other VLAN by this switching engine." 
    ::= { cseL3VlanStatsEntry 7 }

cseL3VlanOutUnicastPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of unicast packets Layer 3 forwarded to this
        Vlan by this switching engine." 
    ::= { cseL3VlanStatsEntry 8 }

cseL3VlanOutUnicastOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of octets in unicast packets Layer-3 forwarded
        to this Vlan by this switching engine." 
    ::= { cseL3VlanStatsEntry 9 }
 

-- Switch Engine based layer 3 flow statistics; it is protocol
-- independent, i.e. IP and IPX statistics are not separated
-- This group is an augmentation of cseL3StatsTable

cseStatsFlowTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseStatsFlowEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of flow statistics per switch engine that is
        not covered in cseL3StatsTable."
    ::= { cseL3Objects 3 }

cseStatsFlowEntry OBJECT-TYPE
    SYNTAX          CseStatsFlowEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row about slot based L3 flow statistics."
    AUGMENTS           { cseL3StatsEntry  } 
    ::= { cseStatsFlowTable 1 }

CseStatsFlowEntry ::= SEQUENCE {
        cseStatsFlowAged       Counter32,
        cseStatsFlowPurged     Counter32,
        cseStatsFlowParityFail Counter32
}

cseStatsFlowAged OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of layer 3 flows aged out by hardware.  Management
        applications can control flow aging by setting the value of
        cseFlowEstablishedAgingTime object." 
    ::= { cseStatsFlowEntry 1 }

cseStatsFlowPurged OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of layer 3 flows purged by software; it may happen
        because a router invalidates certain flows, or a router for which
        flows are being switched has been excluded from cseRouterTable, or
        access-list has changed, or certain features have been enabled
        that would purge certain flows (TCP interception, Web cache are
        examples of such features)." 
    ::= { cseStatsFlowEntry 2 }

cseStatsFlowParityFail OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of memory parity errors on accessing flows in
        the cache pools.  It may be due to the internal RAM reading
        error, not necessarily the corrupted flow data." 
    ::= { cseStatsFlowEntry 3 }
 

-- Utilization level of flow cache pool per Switch Engine
-- Flows are combined for IP and IPX

cseCacheUtilTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseCacheUtilEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of utilization levels in percentage of cache
        capacity per switch engine.  Each row instance is the
        current flow utilization information in the cache pool
        of the corresponding switching engine."
    ::= { cseL3Objects 4 }

cseCacheUtilEntry OBJECT-TYPE
    SYNTAX          CseCacheUtilEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance represents layer 3 flow utilization of
        a particular cache pool on a switching engine."
    INDEX           { entPhysicalIndex } 
    ::= { cseCacheUtilTable 1 }

CseCacheUtilEntry ::= SEQUENCE {
        cseCacheUtilization    Gauge32,
        cseCacheEntriesCreated Unsigned32,
        cseCacheEntriesFailed  Unsigned32
}

cseCacheUtilization OBJECT-TYPE
    SYNTAX          Gauge32 (0..100)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Flow utilization level in percentage in this switching engine.
        It includes the flow entries for both unicast and multicast.
        The lighter the utilization level, the less risk of dropping
        flows, i.e. the higher success-rate of flow insertion." 
    ::= { cseCacheUtilEntry 1 }

cseCacheEntriesCreated OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the total number of flow entries
        successfully created in this switching engine." 
    ::= { cseCacheUtilEntry 2 }

cseCacheEntriesFailed OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of flow entries
        which were failed to be created in this switching engine." 
    ::= { cseCacheUtilEntry 3 }
 

-- L3 error counters table.

cseErrorStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseErrorStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of IP and IPX error counters per switch engine."
    ::= { cseL3Objects 5 }

cseErrorStatsEntry OBJECT-TYPE
    SYNTAX          CseErrorStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance represents layer-3 IP and IPX error counters
        on a switching engine."
    INDEX           { entPhysicalIndex } 
    ::= { cseErrorStatsTable 1 }

CseErrorStatsEntry ::= SEQUENCE {
        cseIpPlenErrors      Counter64,
        cseIpTooShortErrors  Counter64,
        cseIpCheckSumErrors  Counter64,
        cseIpxPlenErrors     Counter64,
        cseIpxTooShortErrors Counter64
}

cseIpPlenErrors OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IP length against physical length
        errors." 
    ::= { cseErrorStatsEntry 1 }

cseIpTooShortErrors OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IP length too short errors." 
    ::= { cseErrorStatsEntry 2 }

cseIpCheckSumErrors OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IP checksum errors." 
    ::= { cseErrorStatsEntry 3 }

cseIpxPlenErrors OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IPX length against physical length
        errors." 
    ::= { cseErrorStatsEntry 4 }

cseIpxTooShortErrors OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IPX length too short errors." 
    ::= { cseErrorStatsEntry 5 }
 

-- L3 error counters table.

cseErrorStatsLCTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseErrorStatsLCEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of IP and IPX error counters per switch engine."
    ::= { cseL3Objects 6 }

cseErrorStatsLCEntry OBJECT-TYPE
    SYNTAX          CseErrorStatsLCEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance represents layer-3 IP and IPX error counters
        on a switching engine."
    INDEX           { entPhysicalIndex } 
    ::= { cseErrorStatsLCTable 1 }

CseErrorStatsLCEntry ::= SEQUENCE {
        cseLCIpPlenErrors      Counter32,
        cseLCIpTooShortErrors  Counter32,
        cseLCIpCheckSumErrors  Counter32,
        cseLCIpxPlenErrors     Counter32,
        cseLCIpxTooShortErrors Counter32
}

cseLCIpPlenErrors OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IP length against physical length
        errors." 
    ::= { cseErrorStatsLCEntry 1 }

cseLCIpTooShortErrors OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IP length too short errors." 
    ::= { cseErrorStatsLCEntry 2 }

cseLCIpCheckSumErrors OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IP checksum errors." 
    ::= { cseErrorStatsLCEntry 3 }

cseLCIpxPlenErrors OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IPX length against physical length
        errors." 
    ::= { cseErrorStatsLCEntry 4 }

cseLCIpxTooShortErrors OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of IPX length too short errors." 
    ::= { cseErrorStatsLCEntry 5 }
 


-- Packets Switched Per Second

cseL3SwitchedAggrPktsPerSec OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of packets switched per second at Layer 3 by the
        entire system." 
    ::= { cseL3Objects 7 }

-- Protocol Filter capability

cseProtocolFilterEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates if protocol filtering is enabled in the device." 
    ::= { cseProtocolFilter 1 }

cseProtocolFilterPortTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseProtocolFilterPortEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing the protocol filtering configuration and status
        information on ports."
    ::= { cseProtocolFilter 2 }

cseProtocolFilterPortEntry OBJECT-TYPE
    SYNTAX          CseProtocolFilterPortEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row in the cseProtocolFilterPortTable,
        representing a Protocol filter configuration
        status information for a particular
        port (identified by ifIndex) and protocol type."
    INDEX           {
                        ifIndex,
                        cseProtocolFilterPortProtocol
                    } 
    ::= { cseProtocolFilterPortTable 1 }

CseProtocolFilterPortEntry ::= SEQUENCE {
        cseProtocolFilterPortProtocol    INTEGER,
        cseProtocolFilterPortAdminStatus INTEGER,
        cseProtocolFilterPortOperStatus  INTEGER
}

cseProtocolFilterPortProtocol OBJECT-TYPE
    SYNTAX          INTEGER  {
                        ip(1),
                        ipx(2),
                        grpProtocols(3) -- Appletalk/Decnet/Vines                        
                    }
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The protocol (group) to filter, used here as the secondary
        index." 
    ::= { cseProtocolFilterPortEntry 1 }

cseProtocolFilterPortAdminStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        on(1),
                        off(2),
                        auto(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "An indication of the administrative status of the protocol
        filtering on this interface.

        - on(1) indicates that the interface will send and receive the 
          traffic for protocol specified in 
          cseProtocolFilterPortProtocol.
        - off(2) indicates that the interface will not receive 
          traffic for this protocol, or if this feature is not 
          supported.
        - auto(3) indicates that the corresponding 
          cseProtocolFilterPortOperStatus will transit to 'on' after 
          receiving one packet of this protocol type." 
    ::= { cseProtocolFilterPortEntry 2 }

cseProtocolFilterPortOperStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        on(1),
                        off(2),
                        notSupported(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An indication of the operational status of filtering for
        this protocol on this interface.
        - on(1) indicates that the interface will send and receive the 
          protocol traffic.
        - off(2) indicates that the interface will drop all traffic 
          belonging to this protocol.
        - notSupported(3) indicates the hardware does not support 
          protocol filtering." 
    ::= { cseProtocolFilterPortEntry 3 }
 

-- This MIB group/table is designed for control of purging flow caches
-- The caches are distributed in the switching engines across the system

cseUcastCacheTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseUcastCacheEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A control table used to purge certain types of IP/IPX layer 3
        unicast flows stored in the cache pool."
    ::= { cseUcastCache 1 }

cseUcastCacheEntry OBJECT-TYPE
    SYNTAX          CseUcastCacheEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row in the cseUcastCacheTable, used to set up
        flow clearing criteria.  The actual purging is started by setting
        the value of cseUcastCacheStatus to 'active'.  Once a row becomes
        active, values within the row cannot be modified, except by
        setting it to 'notInService' first or deleting and re-creating
        the row."
    INDEX           { cseUcastCacheIndex } 
    ::= { cseUcastCacheTable 1 }

CseUcastCacheEntry ::= SEQUENCE {
        cseUcastCacheIndex     Unsigned32,
        cseUcastCacheFlowType  INTEGER,
        cseUcastCacheTransport INTEGER,
        cseUcastCacheDest      FlowAddressComponent,
        cseUcastCacheDestMask  FlowAddressComponent,
        cseUcastCacheSource    FlowAddressComponent,
        cseUcastCacheSrcMask   FlowAddressComponent,
        cseUcastCacheRtrIp     IpAddress,
        cseUcastCacheOwner     OwnerString,
        cseUcastCacheResult    INTEGER,
        cseUcastCacheStatus    RowStatus
}

cseUcastCacheIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An arbitrary integer which uniquely identifies the flow
        purge contained in this row instance." 
    ::= { cseUcastCacheEntry 1 }

cseUcastCacheFlowType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        any(1),
                        dstOnly(2),
                        srcOrDst(3),
                        srcAndDst(4),
                        fullFlow(5),
                        ipxDstOnly(6),
                        ipxSrcAndDst(7)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Setting each value causes the appropriate action:

        'dstOnly' - causes purge of flows in the cache whose absolute
        destination IP addresses match the address part of the value set
        in the cseUcastCacheDest object.  If cseUcastCacheDestMask is also
        specified at the same time, it will be applied to the address part
        of cseUcastCacheDest.

        'srcOrDst' - causes purge of flows in the cache whose either
        absolute source or destination IP addresses match the address
        parts of the values set in cseUcastCacheSource or 
        cseUcastCacheDest.
        If cseUcastCacheDestMask/cseUcastCacheSrcMask also specified,
        they will be applied to the address parts of cseUcastCacheSource/
        cseUcastCacheDest appropriately.

        'srcAndDst' - causes purge of flows in the cache whose both
        absolute source and destination IP addresses match the address
        parts of values set in cseUcastCacheSource and cseUcastCacheDest
        objects.  If cseUcastCacheSrcMask and cseUcastCacheDestMask also
        specified, they will be applied to the address parts of
        cseUcastCacheSource and cseUcastCacheDest.

        'fullFlow' - causes purge of IP flows whose IP addresses and
        transport port numbers match the values set in cseUcastCacheDest
        and cseUcastCacheSource objects.
        If either cseUcastCacheDestMask or cseUcastCacheSrcMask objects
        have valid values, they will be applied to the respective address
        parts of cseUcastCacheDest and cseUcastCacheSource objects.
        This option is typically used to purge flows relevant to specific
        applications such as FTP, WWW, TELNET, etc.

        'ipxDstOnly' - causes purge of IPX flows in the cache whose
        absolute destination IPX address match the address part of
        the value set in cseUcastCacheDest object.
        if cseUcastCacheDestMask holds valid value at the same time,
        it will be applied to the address part of cseUcastCacheDest.

        'ipxSrcAndDst' - causes purge of IPX flows in the cache whose
        both absolute source and destination IPX addresses match the
        address parts of the values set in cseUcastCacheSource and
        cseUcastCacheDest objects.
        If either of cseUcastCacheSrcMask or cseUcastCacheDestMask
        have valid values at the same time, they will be applied to
        the respective address parts of cseUcastCacheSource and
        cseUcastCacheDest objects.

        'any' - causes purge of all established flows currently in
        the cache.  The values of cseUcastCacheDest, cseUcastCacheSource,
        cseUcastCacheDestMask, cseUcastCacheSrcMask, and
        cseUcastCacheTransport should be ignored in this case.

        Note:
          1. When the row instance is initialized, the value of this 
             object instance will be set to 'any'.
          2. The rest flow parameter variables will not be instantiated
             until they get set by management applications based on
             the value of cseUcastCacheFlowType object."
    DEFVAL          { any } 
    ::= { cseUcastCacheEntry 2 }

cseUcastCacheTransport OBJECT-TYPE
    SYNTAX          INTEGER  {
                        udp(1),
                        tcp(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The IP transport protocol type (if applicable) of the specified
        switched flows to be purged; it will be instantiated if and only
        if it gets set by the management applications and the value of
        cseUcastCacheFlowMask is equal to 'fullFlow'.
        Its value can not be modified when the corresponding instance
        of cseUcastCacheStatus is 'active'." 
    ::= { cseUcastCacheEntry 3 }

cseUcastCacheDest OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Destination network address and port number (if applicable).
        The port field is ignored for IPX flows and IP flows if the
        value of cseUcastCacheFlowMask is not equal to 'fullFlow'.
        Its value can not be modified when the corresponding instance
        of cseUcastCacheStatus is 'active'." 
    ::= { cseUcastCacheEntry 4 }

cseUcastCacheDestMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "If instantiated, specified and applicable, the destination
        address mask will be applied to the value of
        cseUcastCacheDest in the same row instance.
        Its value can not be modified when the corresponding instance
        of cseUcastCacheStatus is 'active'." 
    ::= { cseUcastCacheEntry 5 }

cseUcastCacheSource OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Source network address and port number (if applicable).
        The port field is ignored for IPX flows and IP flows if the
        value of cseUcastCacheFlowMask is not equal to 'fullFlow'.
        Its value can not be modified when the corresponding instance
        of cseUcastCacheStatus is 'active'." 
    ::= { cseUcastCacheEntry 6 }

cseUcastCacheSrcMask OBJECT-TYPE
    SYNTAX          FlowAddressComponent
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "If instantiated, specified and applicable, the source address
        mask will be applied to the  value of cseUcastCacheSource
        in the same row instance.
        Its value can not be modified when the corresponding instance
        of cseUcastCacheStatus is 'active'." 
    ::= { cseUcastCacheEntry 7 }

cseUcastCacheRtrIp OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "IP address of the router (internal or external) for which the
        flows are being switched, and need to be purged.  An 'all-zero'
        value is a wildcard IP address for any router.
        Its value can not be modified when the corresponding instance
        of cseUcastCacheStatus is 'active'." 
    ::= { cseUcastCacheEntry 8 }

cseUcastCacheOwner OBJECT-TYPE
    SYNTAX          OwnerString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The manager entity that configured this entry and is therefore
        using the resources assigned to it." 
    ::= { cseUcastCacheEntry 9 }

cseUcastCacheResult OBJECT-TYPE
    SYNTAX          INTEGER  {
                        purging(1),
                        notPurging(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "state of the flow purging operation.

        'purging' - purging operation is proceeding

        'notPurging' - the purging operation completed, or not 
         started yet."
    DEFVAL          { notPurging } 
    ::= { cseUcastCacheEntry 11 }

cseUcastCacheStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status object used to manage rows in this table.
        When set to active(1), the flow purge is initiated, and
        the value of cseUcastCacheResult object becomes 'purging'.
        However, this object can be set to active(1) only after
        all the appropriate objects for this query as defined 
        by the value set in the cseUcastCacheFlowType object,
        have also been set.  Upon the completion of flow purge,
        the value of cseUcastCacheResult object changes to
        'notPurging'.
        Once a row becomes active, values within the row cannot
        be modified, except by setting it to 'notInService' first,
        or deleting and re-creating it." 
    ::= { cseUcastCacheEntry 10 }
 

-- This MIB group/table is designed for purging IP multicast
-- flows.  For a multicast switch, a row instance can be used to clear
-- specified multicast L3 flows from its cache pools

cseMcastCacheTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseMcastCacheEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A control table used to purge specified IP multicast flows
        from the switch engine."
    ::= { cseMcastCache 1 }

cseMcastCacheEntry OBJECT-TYPE
    SYNTAX          CseMcastCacheEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A conceptual row in the cseMcastCacheTable, used to set up
        flow clearing criteria.  The actual purging is started by setting
        the value of cseMcastCacheStatus to 'active'.  Once a row becomes
        active, values within the row cannot be modified, except by
        setting it to 'notInService' first, or deleting and re-creating
        the row."
    INDEX           { cseMcastCacheIndex } 
    ::= { cseMcastCacheTable 1 }

CseMcastCacheEntry ::= SEQUENCE {
        cseMcastCacheIndex    Unsigned32,
        cseMcastCacheFlowType INTEGER,
        cseMcastCacheGrp      McastGroupIp,
        cseMcastCacheSrc      IpAddress,
        cseMcastCacheRtrIp    IpAddress,
        cseMcastCacheOwner    OwnerString,
        cseMcastCacheResult   INTEGER,
        cseMcastCacheStatus   RowStatus
}

cseMcastCacheIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An arbitrary integer which uniquely identifies the flow
        purge contained in the current row instance." 
    ::= { cseMcastCacheEntry 1 }

cseMcastCacheFlowType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        any(1),
                        group(2),
                        grpAndSrc(3)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Setting each value causes the appropriate action:

        'any' - causes purge of all established IP multicast
        layer 3 flows in the cache.  The value of cseMcastCacheGrp,
        and cseMcastCacheSrc will be ignored in this case.

        'group' - causes purge of flows whose multicast
        group IP address match the values of cseMcastCacheGrp.

        'grpAndSrc' - causes purge of multicast flows whose both
        group IP address and source Ip address match the
        values of cseMcastCacheGrp and cseMcastCacheSrc.

        Note:
          1. The instance of this object is initialized to
             'any' when the corresponding row instance is
             being instantiated.
          2. Flow parameter variables, cseMcastCacheGrp, 
             cseMcastCacheSrc, cseMcastCacheRtrIp will not
             be instantiated until they get set by management
             applications (in such cases, cseMcastCacheFlowType
             object should be set to a value other than 'any')."
    DEFVAL          { any } 
    ::= { cseMcastCacheEntry 2 }

cseMcastCacheGrp OBJECT-TYPE
    SYNTAX          McastGroupIp
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifying multicast group IP address of the flows to be
        cleared.  Its value can not be modified when the corresponding
        instance of cseMcastCacheStatus is 'active'." 
    ::= { cseMcastCacheEntry 3 }

cseMcastCacheSrc OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The source address of the IP multicast flows to be purged.
        Its value can not be modified when the corresponding instance
        of cseMcastCacheStatus is 'active'." 
    ::= { cseMcastCacheEntry 4 }

cseMcastCacheRtrIp OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The IP address of the router whose flows are currently
        being switched, and will be purged.  An 'all-zero' value is
        a wildcard IP address for any router.
        Its value can not be modified when the corresponding instance
        of cseMcastCacheStatus is 'active'." 
    ::= { cseMcastCacheEntry 5 }

cseMcastCacheOwner OBJECT-TYPE
    SYNTAX          OwnerString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The manager entity that configured this entry and is therefore
        using the resources assigned to it." 
    ::= { cseMcastCacheEntry 6 }

cseMcastCacheResult OBJECT-TYPE
    SYNTAX          INTEGER  {
                        purging(1),
                        notPurging(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "state of the flow purging operation.

        'purging' - purging operation is proceeding

        'notPurging' - the purging operation completed, or not 
         started yet."
    DEFVAL          { notPurging } 
    ::= { cseMcastCacheEntry 7 }

cseMcastCacheStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status object used to manage rows in this table.
        When set to active(1), the flow purge is initiated, and
        the value of cseMcastCacheResult object becomes 'purging'.
        However, this object can be set to active(1) only after
        all the appropriate objects for this query as defined 
        by the value set in the cseMcastCacheFlowType object,
        have also been set.  Upon the completion of flow purge,
        the value of cseMcastCacheResult object changes to
        'notPurging'.
        Once a row becomes active, values within the row cannot
        be modified, except by setting it to 'notInService' first,
        or deleting and re-creating it." 
    ::= { cseMcastCacheEntry 8 }
 

-- cseCef Group
--   

--   
-- The cseCefFibTable

cseCefFibTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseCefFibEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains the information stored in the device's
        forwarding information base (FIB). 

        FIB is a forwarding scheme that utilizes matching pattern
        to provide optimized lookup for efficient packet forwarding.
        It contains a forwarding table which consist of matching
        criteria for incoming traffic as well as information to
        forward traffic that matched defined criteria."
    ::= { cseCef 1 }

cseCefFibEntry OBJECT-TYPE
    SYNTAX          CseCefFibEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the IP address type, the final destination
        IP address, the final destination IP address mask as well as
        the FIB entry type."
    INDEX           { cseCefFibIndex } 
    ::= { cseCefFibTable 1 }

CseCefFibEntry ::= SEQUENCE {
        cseCefFibIndex      Unsigned32,
        cseCefFibAddrType   InetAddressType,
        cseCefFibDestIp     InetAddress,
        cseCefFibDestIpMask InetAddress,
        cseCefFibType       INTEGER
}

cseCefFibIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The index of this table entry." 
    ::= { cseCefFibEntry 1 }

cseCefFibAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of IP address denoted in cseCefFibDestIp and
        cseCefFibDestIpMask object." 
    ::= { cseCefFibEntry 2 }

cseCefFibDestIp OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The destination IP address specified in IP packet header." 
    ::= { cseCefFibEntry 3 }

cseCefFibDestIpMask OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The specified destination IP address mask." 
    ::= { cseCefFibEntry 4 }

cseCefFibType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        other(1),
                        resolved(2),
                        bridge(3),
                        drop(4),
                        connected(5),
                        receive(6),
                        wildcard(7),
                        tunnel(8),
                        default(9)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the FIB entry type.

        other(1) indicates that this FIB entry type is none
        of the following.

        resolved(2) indicates that IP traffic matched the
        destination prefix of this entry is associated with a
        valid next-hop address. 

        bridge(3) indicates that IP traffic matched the destination
        prefix of this entry will be forwarded using Layer 2
        look up result. 

        drop(4) indicates that IP traffic matched the destination
        prefix of this entry will be dropped.

        connected(5) indicates that IP traffic matched the destination
        prefix of this entry is associated with a connected network. 

        receive(6) indicates that IP traffic matched the destination
        prefix of this entry will be sent to a router interface.

        wildcard(7) indicates this FIB entry will match all traffic. 

        tunnel(8) indicates this FIB entry applied to tunneling
        traffic. 

        default(9) indicates that IP traffic matched the destination
        prefix of this entry will be forwarded using a default route." 
    ::= { cseCefFibEntry 5 }
 

-- The cseCefAdjacencyTable

cseCefAdjacencyTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseCefAdjacencyEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains information stored in the device's
        adjacency table. Entry in this table is linked to entry
        of cseCefFibTable by its cseCefFibIndex object."
    ::= { cseCef 3 }

cseCefAdjacencyEntry OBJECT-TYPE
    SYNTAX          CseCefAdjacencyEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains next hop IP address, next-hop Ethernet
        address, adjacency type, and number of bytes and packets
        transmitted to each adjacency entry. Next hop encapsulation
        type and MTU value are also available if supported by the
        device."
    INDEX           {
                        cseCefFibIndex,
                        cseCefAdjacencyIndex
                    } 
    ::= { cseCefAdjacencyTable 1 }

CseCefAdjacencyEntry ::= SEQUENCE {
        cseCefAdjacencyIndex          Unsigned32,
        cseCefAdjacencyAddrType       InetAddressType,
        cseCefAdjacencyNextHopIp      InetAddress,
        cseCefAdjacencyNextHopMac     MacAddress,
        cseCefAdjacencyNextHopIfIndex InterfaceIndexOrZero,
        cseCefAdjacencyType           INTEGER,
        cseCefAdjacencyPkts           Counter64,
        cseCefAdjacencyOctets         Counter64,
        cseCefAdjacencyEncap          INTEGER,
        cseCefAdjacencyMTU            Unsigned32
}

cseCefAdjacencyIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The adjacency index of this table entry." 
    ::= { cseCefAdjacencyEntry 1 }

cseCefAdjacencyAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of IP address denoted in cseCefAdjacencyNextHopIp
        object." 
    ::= { cseCefAdjacencyEntry 2 }

cseCefAdjacencyNextHopIp OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The next hop IP address." 
    ::= { cseCefAdjacencyEntry 3 }

cseCefAdjacencyNextHopMac OBJECT-TYPE
    SYNTAX          MacAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The next hop Ethernet address." 
    ::= { cseCefAdjacencyEntry 4 }

cseCefAdjacencyNextHopIfIndex OBJECT-TYPE
    SYNTAX          InterfaceIndexOrZero
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the next hop interface ifIndex." 
    ::= { cseCefAdjacencyEntry 5 }

cseCefAdjacencyType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        other(1),
                        punt(2),
                        glean(3),
                        drop(4),
                        null(5),
                        noRewrite(6),
                        forceDrop(7),
                        connect(8),
                        unresolved(9),
                        loopback(10),
                        tunnel(11)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates this adjacency entry type.

        other(1) indicates the adjacency entry type is none of
        the following.

        punt(2) indicates entry that sends traffic to the router.

        glean(3) indicates entry that needs to be gleaned for incoming
        traffic.

        drop(4) indicates entry that drops packets.

        null(5) indicates entry that drops packets destined 
        for the Null0 interface.

        noRewrite(6) indicates entry that sends traffic to the router
        when rewrite information is incomplete. 

        forceDrop(7) indicates entry that drop packets due to ARP
        throttling. 

        connect(8) indicates entry that contains complete rewrite
        information.

        unresolved(9) indicates entry that next hop traffic is 
        unresolved. 

        loopback(10) indicates entry that drops packets destined 
        for loopback interface.

        tunnel(11) indicates entry that next hop traffic is 
        through a tunnel." 
    ::= { cseCefAdjacencyEntry 6 }

cseCefAdjacencyPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of Layer 3 packets transmitted to
        this adjacency entry." 
    ::= { cseCefAdjacencyEntry 7 }

cseCefAdjacencyOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of Layer 3 octets transmitted to
        this adjacency entry." 
    ::= { cseCefAdjacencyEntry 8 }

cseCefAdjacencyEncap OBJECT-TYPE
    SYNTAX          INTEGER  {
                        arpa(1),
                        raw(2),
                        sap(3),
                        snap(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the next hop destination encapsulation type.

        arpa(1) indicates that next hop destination used ARPA
        encapsulation type to forward packets.

        raw(2) indicates that next hop destination used RAW
        encapsulation type to forward packets.

        sap(3) indicates that next hop destination used SAP 
        encapsulation type to forward packets.

        snap(4) indicates that next hop destination used SNAP
        encapsulation type to forward packets." 
    ::= { cseCefAdjacencyEntry 9 }

cseCefAdjacencyMTU OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the next hop destination MTU value." 
    ::= { cseCefAdjacencyEntry 10 }
 

-- cseTcamUsage group
--   

--   
-- The cseTcamUsageTable

cseTcamUsageTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseTcamUsageEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains the resource usage of TCAM (Ternary
        Content Addressable Memory) in the device.  Not all the
        resource types denoted by cseTcamResourceType object
        are supported. If that is the case, the corresponding row
        for that type will not be instantiated in this table."
    ::= { cseTcamUsage 1 }

cseTcamUsageEntry OBJECT-TYPE
    SYNTAX          CseTcamUsageEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains a short description of the resource type,
        the total amount of TCAM allocated for that type as well
        as the amount of allocated resource has been used up."
    INDEX           {
                        entPhysicalIndex,
                        cseTcamResourceType
                    } 
    ::= { cseTcamUsageTable 1 }

CseTcamUsageEntry ::= SEQUENCE {
        cseTcamResourceType  INTEGER,
        cseTcamResourceDescr SnmpAdminString,
        cseTcamResourceUsed  Unsigned32,
        cseTcamResourceTotal Unsigned32
}

cseTcamResourceType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        aclStorageMask(1),
                        aclStorageValue(2),
                        aclDynamicStorageMask(3),
                        aclDynamicStorageValue(4),
                        qosStorageMask(5),
                        qosStorageValue(6),
                        qosDynamicStorageMask(7),
                        qosDynamicStorageValue(8),
                        l4PortOperator(9),
                        interfaceMapping(10),
                        ingressInterfaceMapping(11),
                        egressInterfaceMapping(12),
                        louSource(13),
                        louDestination(14),
                        andOr(15),
                        orAnd(16),
                        aclAdjacency(17),
                        aclHighStorageMask(18),
                        aclHighStorageValue(19),
                        aclLowStorageMask(20),
                        aclLowStorageValue(21),
                        qosHighStorageMask(22),
                        qosHighStorageValue(23),
                        qosLowStorageMask(24),
                        qosLowStorageValue(25),
                        sgacl(26),
                        accounting(27),
                        ipv6Ext(28),
                        ethertype(29),
                        destInfo(30),
                        dgtSgtRegion(31),
                        anyAnyRegion(32),
                        tcamALabel(33),
                        tcamBLabel(34),
                        destInfoIn(35),
                        destInfoOut(36),
                        tcam0Bank0(37),
                        tcam0Bank1(38),
                        tcam1Bank0(39),
                        tcam1Bank1(40),
                        tcam0Aggregate(41),
                        tcam1Aggregate(42),
                        bank0Aggregate(43),
                        bank1Aggregate(44),
                        lou(45),
                        bothLouOperands(46),
                        singleLouOperands(47),
                        louL4SourcePort(48),
                        louL4DstPort(49),
                        louL3PacketLength(50),
                        louIpTos(51),
                        louIpDscp(52),
                        louIpPrecedence(53),
                        louIpTtl(54),
                        tcpFlags(55),
                        l4DynamicProtocolCam(56),
                        macEtypeOrProtoCam(57),
                        nonL4OpLabelsTcam0(58),
                        nonL4OpLabelsTcam1(59),
                        l4OpLabelTcam0(60),
                        l4OpLabelTcam1(61),
                        ingressDestInfoTable(62),
                        egressDestInfoTable(63),
                        ingressTcam(64),
                        ingressIpv6Tcam(65),
                        ingressLou(66),
                        ingressBothLouOperands(67),
                        ingressSingleLouOperands(68),
                        ingressLouL4SourcePort(69),
                        ingressLouL4DstPort(70),
                        ingressLouL3PacketLength(71),
                        ingressLouL3Ttl(72),
                        ingressLouL2Ttl(73),
                        ingressTcpFlags(74),
                        egressTcam(75),
                        egressIpv6Tcam(76),
                        egressLou(77),
                        egressBothLouOperands(78),
                        egressSingleLouOperands(79),
                        egressLouL4SourcePort(80),
                        egressLouL4DstPort(81),
                        egressLouL3PacketLength(82),
                        egressLouL3Ttl(83),
                        egressLouL2Ttl(84),
                        egressTcpFlags(85),
                        l4OpLabelTcam2(86),
                        l4OpLabelTcam3(87),
                        l4OpLabelTcam4(88),
                        l4OpLabelTcam5(89),
                        l4OpLabelTcam6(90),
                        l4OpLabelTcam7(91),
                        l4OpLabelTcam8(92),
                        l4OpLabelTcam9(93),
                        l4OpLabelTcam10(94),
                        l4OpLabelTcam11(95),
                        l4OpLabelTcam12(96),
                        l4OpLabelTcam13(97),
                        l4OpLabelTcam14(98),
                        l4OpLabelTcam15(99),
                        l4OpLabelTcam16(100),
                        l4OpLabelTcam17(101),
                        l4OpLabelTcam18(102),
                        l4OpLabelTcam19(103),
                        ingressPacl(104),
                        ingressVacl(105),
                        ingressRacl(106),
                        ingressRbacl(107),
                        ingressNbm(108),
                        ingressL2Qos(109),
                        ingressL3VlanQos(110),
                        ingressSup(111),
                        ingressL2SpanAcl(112),
                        ingressL3VlanSpanAcl(113),
                        ingressFstat(114),
                        ingressLatency(115),
                        span(116),
                        nat(117),
                        egressVacl(118),
                        egressRacl(119),
                        egressRbacl(120),
                        egressSup(121),
                        egressL2Qos(122),
                        egressL3VlanQos(123),
                        netflowAnalyticsFilterTcam(124),
                        ingressNetflowL3(125),
                        ingressNetflowL2(126),
                        featureVxLanOam(127),
                        featureBfd(128),
                        featureDhcpSnoop(129),
                        ingressRedirect(130),
                        featureDhcpV6Relay(131),
                        featureArpSnoop(132),
                        featureDhcpSnoopFhs(133),
                        featureDhcpVaclFhs(134),
                        featureDhcpSisf(135),
                        egressSystem(136),
                        rplusEgressSystem(137),
                        supSystem(138),
                        rplusSupSystem(139),
                        fmSupSystem(140),
                        supCopp(141),
                        rplusSupCopp(142),
                        supCoppReasonCode(143),
                        ingressIpv4Pacl(144),
                        ingressIpv6Pacl(145),
                        ingressMacPacl(146),
                        ingressFexIpv4Pacl(147),
                        ingressFexIpv6Pacl(148),
                        ingressFexMacPacl(149),
                        ingressIpv4PortQos(150),
                        ingressIpv4PortQosLite(151),
                        ingressIpv6PortQos(152),
                        ingressMacPortQos(153),
                        ingressIpv4FexPortQos(154),
                        ingressIpv4FexPortQosLite(155),
                        ingressIpv6FexPortQos(156),
                        ingressMacFexPortQos(157),
                        ingressIpv4Vacl(158),
                        ingressIpv6Vacl(159),
                        ingressMacVacl(160),
                        ingressIpv4VlanQos(161),
                        ingressIpv4VlanQosLite(162),
                        ingressIpv6VlanQos(163),
                        ingressMacVlanQos(164),
                        ingressIpv4Racl(165),
                        ingressIpv6Racl(166),
                        ingressIpv4L3Qos(167),
                        ingressIPv4L3QosLite(168),
                        ingressIPv6L3Qos(169),
                        ingressMacL3Qos(170),
                        ingressFlowCounters(171),
                        ingressSviCounters(172),
                        egressIpv4Vacl(173),
                        egressIpv6Vacl(174),
                        egressMacVacl(175),
                        egressIpv4Qos(176),
                        egressIpv4QosLite(177),
                        egressIpv6Qos(178),
                        egressMacQos(179),
                        rplusIngressEgressIpv4Qos(180),
                        redirect(181),
                        rplusIngressEgressIpv4QosLite(182),
                        rplusIngressEgressIpv6Qos(183),
                        rplusIngressEgressMacQos(184),
                        egressIpv4Racl(185),
                        egressIpv6Racl(186),
                        egressFlowCounters(187),
                        ingressNsIpv4PortQos(188),
                        ingressNsIpv6PortQos(189),
                        ingressNsMacPortQos(190),
                        ingressNsIpv4VlanQos(191),
                        ingressNsIpv6VlanQos(192),
                        ingressNsMacVlanQos(193),
                        ingressNsIpv4L3Qos(194),
                        ingressNsIpv6L3Qos(195),
                        vpcConvergence(196),
                        ipsgSmacIpBindingTable(197),
                        openflowAcl(198),
                        openflowIpv6Acl(199),
                        ingressEtherAcl(200),
                        mplsFeature(201),
                        ingressIpv4Qos(202),
                        ingressIpv6Qos(203),
                        ipv6Sup(204),
                        ingressIpv4Pbr(205),
                        ingressIpv6Pbr(206),
                        ingressIpv4PaclDoubleWide(207),
                        arpAcl(208),
                        sflowNorthstarAcl(209),
                        mcastBidir(210),
                        redirectTunnel(211),
                        ingressFcoeCounters(212),
                        egressFcoeCounters(213),
                        spanSflowCombined(214),
                        mcastPerformance(215),
                        fhs(216),
                        openflowLiteAcl(217),
                        ipv6DestCompression(218),
                        ingressIpv4RaclLite(219),
                        ipv6SrcCompression(220),
                        ipv4RaclSpanUdf(221),
                        ingressIpv4PortQosIntraTcamLite(222),
                        ingressIpv4L3QosIntraTcamLite(223),
                        ingressIpv4VlanQosIntraTcamLite(224),
                        ipv4PaclSpanUdf(225),
                        coppSystem(226),
                        ingressIpv4PaclLite(227),
                        ingressIpv4VaclLite(228),
                        vxLanXConnect(229),
                        dot1X(230),
                        dot1XMultiAuth(231),
                        ingressPaclAll(232),
                        ingressRaclAll(233),
                        ingressVaclAll(234),
                        ingressMacPqos(235),
                        ingressIpv4Pqos(236),
                        ingressIpv6Pqos(237),
                        ingressPqos(238),
                        ingressMacVqos(239),
                        ingressIpv4Vqos(240),
                        ingressIpv6Vqos(241),
                        ingressVqosAll(242),
                        ingressIpv4L3qos(243),
                        ingressIpv6L3qos(244),
                        ingressL3qosAll(245),
                        ingressCopp(246),
                        ingressMacSpan(247),
                        ingressIpv4Span(248),
                        ingressSpan(249),
                        ingressSpanAll(250),
                        egressRaclAll(251),
                        egressVaclAll(252),
                        egressMacPortQos(253),
                        egressIpv4PortQos(254),
                        egressIv6PortQos(255),
                        egressPortQos(256),
                        egressMacVlanQos(257),
                        egressIpv4VlanQos(258),
                        egressIpv6VlanQos(259),
                        egressVlanQos(260),
                        egressIpv4L3Qos(261),
                        egressIpv6L3Qos(262),
                        egressL3QosAll(263),
                        egressMacSpan(264),
                        egressIpv4Span(265),
                        egressIpv6Span(266),
                        egressSpanAll(267),
                        dhcp(268),
                        labelLblA(269),
                        labelLblB(270),
                        labelLblD(271),
                        labelLblE(272),
                        labelLblF(273),
                        labelLblG(274),
                        labelLblH(275),
                        labelLblI(276),
                        labelLblK(277),
                        ingressSupAll(278),
                        egressSupAll(279),
                        ingressVlanSpan(280),
                        ingressNetflow(281),
                        ingressCntAcl(282),
                        egressCntAcl(283),
                        ingressHwTelemetry(284),
                        labelLblAv1(285),
                        labelLblBv1(286),
                        labelLblCv1(287),
                        labelLblDv1(288),
                        labelLblEv1(289),
                        labelLblFv1(290),
                        labelLblGv1(291),
                        labelLblHv1(292),
                        labelLblIv1(293),
                        labelLblJv1(294),
                        labelLblKv1(295),
                        labelLblLv1(296),
                        labelLblMv1(297),
                        labelLblNv1(298),
                        labelLblOv1(299),
                        labelLblPv1(300),
                        labelLblQv1(301),
                        labelLblRv1(302),
                        ingressNetflowAnalytics(303),
                        ingressNatOutside(304),
                        ingressNatInside(305),
                        ingressL2L3QosAll(306),
                        natRewriteTable(307),
                        tcpAwareNat(308)
                    }
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The resource type which take up TCAM space.

        aclStorageMask(1) indicates that TCAM space is allocated
        to store ACL masks.

        aclStorageValue(2) indicates that TCAM space is allocated
        to store ACL value.

        aclDynamicStorageMask(3) indicates that TCAM space is
        allocated to dynamically store ACL masks.

        aclDynamicStorageValue(4) indicates that TCAM space is
        allocated to dynamically store ACL values.

        qosStorageMask(5) indicates that TCAM space is allocated
        to store QoS masks.

        qosStorageValue(6) indicates that TCAM space is allocated
        to store QoS value.

        qosDynamicStorageMask(7) indicates that TCAM space is
        allocated to dynamically store QoS masks.

        qosDynamicStorageValue(8) indicates that TCAM space is
        allocated to dynamically store QoS values.

        l4PortOperator(9) indicates that TCAM space is allocated
        for layer 4 port operators purpose.

        interfaceMapping(10) indicates that TCAM space is allocated
        for interface mapping purpose.

        ingressInterfaceMapping(11) indicates that TCAM space is
        allocated for ingress interface mapping purpose.

        egressInterfaceMapping(12) indicates that TCAM space is
        allocated for egress interface mapping purpose.

        louSource(13) indicates that TCAM space is allocated
        as source LOUs (Logical Operation Unit).

        louDestination(14) indicates that TCAM space is allocated
        as destination LOUs.

        andOr(15) indicates that TCAM space is allocated for 
        ANDOR purpose.

        orAnd(16) indicates that TCAM space is allocated for
        ORAND purpose.

        aclAdjacency(17) indicates that TCAM space is allocated
        for ACL adjacency purpose.

        aclHighStorageMask(18) indicates that high bank TCAM 
        space is allocated to store ACL masks.

        aclHighStorageValue(19) indicates that high bank TCAM
        space is allocated to store ACL value.

        aclLowStorageMask(20) indicates that low bank TCAM space
        is allocated to store ACL masks.

        aclLowStorageValue(21) indicates that low bank TCAM space
        is allocated to store ACL values.

        qosHighStorageMask(22) indicates that high bank TCAM space
        is allocated to store QoS masks.

        qosHighStorageValue(23) indicates that high bank TCAM space
        is allocated to store QoS value.

        qosLowStorageMask(24) indicates that low bank TCAM space is
        allocated to store QoS masks.

        qosLowStorageValue(25) indicates that low bank TCAM space is
        allocated to store QoS values.

        sgacl(26) indicates that TCAM space is allocated for SGACL
        (Security Group Access Control List) purpose.

        accounting(27) indicates that TCAM space is allocated
        for accounting purpose such as AS (Autonomous System) 
        based accounting, classification based accounting. 

        ipv6Ext(28) indicates that TCAM space is allocated for
        IPv6 Extended Header lookup purpose.

        ethertype(29) indicates that TCAM space is allocated for
        layer2 ethertype lookup purpose.

        destInfo(30) indicates that TCAM space is allocated for
        destination information lookup purpose.

        dgtSgtRegion(31) indicates that TCAM space is allocated for
        specific SGT (Secutiry Group Tag), DGT (Destination Group Tag)
        pairs.

        anyAnyRegion(32) indicates that TCAM space is allocated for
        SGT (Secutiry Group Tag), DGT (Destination Group Tag)  pairs 
        with one or both of them as ANY.

        tcamALabel(33) indicates that TCAM space is allocated for
        labels used by TCAM A entries.

        tcamBLabel(34) indicates that TCAM space is allocated for
        labels used by TCAM B entries.

        destInfoIn(35) indicates that TCAM space is allocated for
        destination information table for IFE (Ingress Forwarding Engine)  
        ACL redirects.

        destInfoOut(36) indicates that TCAM space is allocated for
        destination information table for OFE (Output/Egress Forwarding 
        Engine) ACL redirects.

        tcam0Bank0(37) indicates that TCAM space is allocated for
        TCAM 0 Bank 0.

        tcam0Bank1(38) indicates that TCAM space is allocated for
        TCAM 0 Bank 1.

        tcam1Bank0(39) indicates that TCAM space is allocated for
        TCAM 1 Bank 0.

        tcam1Bank1(40) indicates that TCAM space is allocated for
        TCAM 1 Bank 1.

        tcam0Aggregate(41) indicates that TCAM space is allocated for
        the aggregate of Bank 0 and Bank 1 on TCAM 0.

        tcam1Aggregate(42) indicates that TCAM space is allocated for
        the aggregate of Bank 0 and Bank 1 on TCAM 1.

        bank0Aggregate(43) indicates that TCAM space is allocated for
        the aggregate of TCAM 0 and TCAM 1 for Bank 0.

        bank1Aggregate(44) indicates that TCAM space is allocated for
        the aggregate of TCAM 0 and TCAM 1 for Bank 1.

        lou(45) indicates that TCAM space is allocated for
        LOUs (Logical Operation Unit). 

        bothLouOperands(46) indicates that TCAM space is allocated for
        LOUs with both operands.

        singleLouOperands(47) indicates that TCAM space is allocated for
        LOUs with single operands.

        louL4SourcePort(48) indicates that TCAM space is allocated for
        LOUs with L4 source port in comparison.

        louL4DstPort(49) indicates that TCAM space is allocated for
        LOUs with L4 destination port in comparison.

        louL3PacketLength(50) indicates that TCAM space is allocated for
        LOUs with L3 Length in comparison.

        louIpTos(51) indicates that TCAM space is allocated for
        LOUs with IP ToS (Type of Service) in comparison.

        louIpDscp(52) indicates that TCAM space is allocated for
        LOUs with IP DSCP (Differentiated Services Code Point)
        in comparison.

        louIpPrecedence(53) indicates that TCAM space is allocated for
        LOUs with IP Precedence in comparison.

        louIpTtl(54) indicates that TCAM space is allocated for
        LOUs with IP TTL in comparison.

        tcpFlags(55) indicates that TCAM space is allocated for
        TCP Flags.

        l4DynamicProtocolCam(56) indicates that TCAM space is allocated for
        L4 Dynamic Protocol CAM.

        macEtypeOrProtoCam(57) indicates that TCAM space is allocated for
        MAC Etype or Protocol CAM.

        nonL4OpLabelsTcam0(58) indicates that TCAM space is allocated for
        labels without using any L4 operator resources like LOUs or TCP Flags 
        for TCAM 0.

        nonL4OpLabelsTcam1(59) indicates that TCAM space is allocated for
        labels without using any L4 operator resources like LOUs or TCP Flags
        for TCAM 1.

        l4OpLabelTcam0(60) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 0.

        l4OpLabelTcam1(61) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 1.

        ingressDestInfoTable(62) indicates that TCAM space is allocated for
        Ingress Destination Info Table.

        egressDestInfoTable(63) indicates that TCAM space is allocated for
        Egress Destination Info Table.

        ingressTcam(64) indicates that ingress TCAM resource utilization.

        ingressIpv6Tcam(65) indicates that ingress TCAM space is allocated
        for IPv6 compression.

        ingressLou(66) indicates that ingress TCAM space is allocated for
        LOUs (Logical Operation Unit).

        ingressBothLouOperands(67) indicates that ingress TCAM space is
        allocated for LOUs with both operands.

        ingressSingleLouOperands(68) indicates that ingress TCAM space is
        allocated for LOUs with single operands.

        ingressLouL4SourcePort(69) indicates that ingress TCAM space is
        allocated for LOUs with L4 source port in comparison.

        ingressLouL4DstPort(70) indicates that ingress TCAM space is
        allocated for LOUs with L4 destination port in comparison.

        ingressLouL3PacketLength(71) indicates that ingress TCAM space is
        allocated for LOUs with L3 Length in comparison.

        ingressLouL3Ttl(72) indicates that ingress TCAM space is allocated
        for LOUs with L3 TTL in comparison.

        ingressLouL2Ttl(73) indicates that ingress TCAM space is allocated
        for LOUs with L2 TTL in comparison. 

        ingressTcpFlags(74) indicates that ingress TCAM space is allocated
        for TCP Flags.

        egressTcam(75) indicates that egress TCAM resource utilization.

        egressIpv6Tcam(76) indicates that egress TCAM space is allocated
        for IPv6 compression.

        egressLou(77)indicates that egress TCAM space is allocated for
        LOUs (Logical Operation Unit).

        egressBothLouOperands(78) indicates that egress TCAM space is
        allocated for LOUs with both operands.

        egressSingleLouOperands(79) indicates that egress TCAM space is
        allocated for LOUs with single operands.

        egressLouL4SourcePort(80) indicates that egress TCAM space is
        allocated for LOUs with L4 source port in comparison.

        egressLouL4DstPort(81) indicates that egress TCAM space is
        allocated for LOUs with L4 destination port in comparison.

        egressLouL3PacketLength(82) indicates that egress TCAM space is
        allocated for LOUs with L3 Length in comparison.

        egressLouL3Ttl(83) indicates that egress TCAM space is allocated
        for the LOUs with L3 TTL in comparison.

        egressLouL2Ttl(84) indicates that egress TCAM space is allocated
        for LOUs with L2 TTL in comparison.

        egressTcpFlags(85) indicates that egress TCAM space is allocated
        for TCP Flags.

        l4OpLabelTcam2(86) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 2.

        l4OpLabelTcam3(87) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 3.

        l4OpLabelTcam4(88) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 4.

        l4OpLabelTcam5(89) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 5.

        l4OpLabelTcam6(90) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 6.

        l4OpLabelTcam7(91) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 7.

        l4OpLabelTcam8(92) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 8.

        l4OpLabelTcam9(93) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 9.

        l4OpLabelTcam10(94) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 10.

        l4OpLabelTcam11(95) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 11.

        l4OpLabelTcam12(96) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 12.

        l4OpLabelTcam13(97) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 13.

        l4OpLabelTcam14(98) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 14.

        l4OpLabelTcam15(99) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 15.

        l4OpLabelTcam16(100) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 16.

        l4OpLabelTcam17(101) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 17.

        l4OpLabelTcam18(102) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 18.

        l4OpLabelTcam19(103) indicates that TCAM space is allocated for
        labels using any L4 operator resources like LOUs or TCP Flags
        for TCAM 19.

        ingressPacl(104) indicates that ingress TCAM space is allocated
        for PACL.

        ingressVacl(105) indicates that ingress TCAM space is allocated
        for VACL.

        ingressRacl(106) indicates that ingress TCAM space is allocated
        for RACL.

        ingressRbacl(107) indicates that ingress TCAM space is allocated
        for RBACL.

        ingressNbm(108) indicates that ingress TCAM space is allocated
        for NBM.

        ingressL2Qos(109) indicates that ingress TCAM space is allocated
        for L2 QoS.

        ingressL3VlanQos(110) indicates that ingress TCAM space is allocated
        for L3/VLAN QOS.

        ingressSup(111) indicates that ingress TCAM space is allocated for
        SUP.

        ingressL2SpanAcl(112) indicates that ingress TCAM space is allocated
        for L2 SPAN ACL.

        ingressL3VlanSpanAcl(113) indicates that ingress TCAM space is
        allocated for L3/VLAN SPAN ACL.

        ingressFstat(114) indicates that ingress TCAM space is allocated
        for FSTAT.

        ingressLatency(115) indicates that ingress TCAM space is allocated
        for LATENCY.

        span(116) indicates that TCAM space is allocated for SPAN.

        nat(117) indicates that TCAM space is allocated for NAT.

        egressVacl(118) indicates that egress TCAM space is allocated
        for VACL. 

        egressRacl(119) indicates that egress TCAM space is allocated
        for RACL.

        egressRbacl(120) indicates that egress TCAM space is allocated
        for RBACL.

        egressSup(121) indicates that egress TCAM space is allocated for
        SUP.

        egressL2Qos(122) indicates that egress TCAM space is allocated
        for L2 QoS.

        egressL3VlanQos(123) indicates that egress TCAM space is allocated
        for L3/VLAN QoS.

        netflowAnalyticsFilterTcam(124) indicates that TCAM space is
        allocated for Netflow/Analytics Filter.

        ingressNetflowL3(125) indicates that ingress TCAM space is allocated
        for L3 Netflow.

        ingressNetflowL2(126) indicates that ingress TCAM space is allocated
        for L2 Netflow.

        featureVxLanOam(127) indicates that TCAM space is allocated for
        Feature VxLAN OAM.

        featureBfd(128) indicates that TCAM space is allocated for Feature
        BFD.

        featureDhcpSnoop(129) indicates that TCAM space is allocated for
        Feature DHCP SNOOP.

        ingressRedirect(130) indicates that TCAM space is allocated for
        ingress REDIRECT.

        featureDhcpV6Relay(131) indicates that TCAM space is allocated for
        Feature DHCPv6 RELAY.

        featureArpSnoop(132) indicates that TCAM space is allocated for
        Feature ARP SNOOP.

        featureDhcpSnoopFhs(133) indicates that TCAM space is allocated for
        Feature DHCP SNOOP FHS.

        featureDhcpVaclFhs(134) indicates that TCAM space is allocated for
        Feature DHCP VACL FHS.

        featureDhcpSisf(135) indicates that TCAM space is allocated for
        Feature DHCP SISF.

        egressSystem(136) indicates that egress TCAM space is allocated for
        System.

        rplusEgressSystem(137) indicates that TCAM space is allocated for
        RPLUS Egress System.

        supSystem(138) indicates that TCAM space is allocated for SUP System.

        rplusSupSystem(139) indicates that TCAM space is allocated for RPLUS
        SUP System.

        fmSupSystem(140) indicates that TCAM space is allocated for FM SUP
        System.

        supCopp(141) indicates that TCAM space is allocated for SUP COPP.

        rplusSupCopp(142) indicates that TCAM space is allocated for RPLUS
        SUP COPP.

        supCoppReasonCode(143) indicates that TCAM space is allocated for
        SUP COPP Reason Code.

        ingressIpv4Pacl(144) indicates that ingress TCAM space is allocated
        for IPv4 PACL.

        ingressIpv6Pacl(145) indicates that ingress TCAM space is allocated
        for IPv6 PACL.

        ingressMacPacl(146) indicates that ingress TCAM space is allocated
        for MAC PACL.

        ingressFexIpv4Pacl(147) indicates that ingress TCAM space is
        allocated for Fex IPv4 PACL.

        ingressFexIpv6Pacl(148) indicates that ingress TCAM space is
        allocated for Ingress Fex IPv6 PACL.

        ingressFexMacPacl(149) indicates that ingress TCAM space is
        allocated for Ingress Fex MAC PACL.

        ingressIpv4PortQos(150) indicates that ingress TCAM space is
        allocated for IPv4 Port QoS.

        ingressIpv4PortQosLite(151) indicates that ingress TCAM space is
        allocated for IPv4 Port QoS (Lite).

        ingressIpv6PortQos(152) indicates that ingress TCAM space is
        allocated for IPv6 Port QoS.

        ingressMacPortQos(153) indicates that ingress TCAM space is
        allocated for MAC Port QoS.

        ingressIpv4FexPortQos(154) indicates that ingress TCAM space is
        allocated for IPv4 FEX Port QoS.

        ingressIpv4FexPortQosLite(155) indicates that ingress TCAM space
        is allocated for IPv4 FEX Port QoS (Lite).

        ingressIpv6FexPortQos(156) indicates that ingress TCAM space is
        allocated for IPv6 FEX Port QoS.

        ingressMacFexPortQos(157) indicates that ingress TCAM space is
        allocated for MAC FEX Port QoS.

        ingressIpv4Vacl(158) indicates that ingress TCAM space is allocated
        for IPv4 VACL.

        ingressIpv6Vacl(159) indicates that ingress TCAM space is allocated
        for IPv6 VACL.

        ingressMacVacl(160) indicates that ingress TCAM space is allocated
        for MAC VACL.

        ingressIpv4VlanQos(161) indicates that ingress TCAM space is
        allocated for IPv4 VLAN QoS.

        ingressIpv4VlanQosLite(162) indicates that ingress TCAM space is
        allocated for IPv4 VLAN QoS (Lite).

        ingressIpv6VlanQos(163) indicates that ingress TCAM space is
        allocated for IPv6 VLAN QoS.

        ingressMacVlanQos(164) indicates that ingress TCAM space is allocated
        for MAC VLAN QoS.

        ingressIpv4Racl(165) indicates that ingress TCAM space is allocated
        for IPv4 RACL.

        ingressIpv6Racl(166) indicates that ingress TCAM space is allocated
        for IPv6 RACL.

        ingressIpv4L3Qos(167) indicates that ingress TCAM space is allocated
        for IPv4 L3 QoS.

        ingressIPv4L3QosLite(168) indicates that ingress TCAM space is
        allocated for IPv4 L3 QoS (Lite).

        ingressIPv6L3Qos(169) indicates that ingress TCAM space is allocated
        for IPv6 L3 QoS.

        ingressMacL3Qos(170) indicates that ingress TCAM space is allocated
        for MAC L3 QoS.

        ingressFlowCounters(171) indicates that TCAM space is allocated for
        ingress Flow Counters.

        ingressSviCounters(172) indicates that TCAM space is allocated for
        Ingress SVI Counters.

        egressIpv4Vacl(173) indicates that egress TCAM space is allocated
        for IPv4 VACL.

        egressIpv6Vacl(174) indicates that egress TCAM space is allocated
        for IPv6 VACL.

        egressMacVacl(175) indicates that egress TCAM space is allocated
        for MAC VACL.

        egressIpv4Qos(176) indicates that egress TCAM space is allocated
        for IPv4 QoS.

        egressIpv4QosLite(177) indicates that egress TCAM space is allocated
        for IPv4 QoS Lite.

        egressIpv6Qos(178) indicates that egress TCAM space is allocated for
        IPv6 QoS.

        egressMacQos(179) indicates that egress TCAM space is allocated for
        MAC QoS.

        rplusIngressEgressIpv4Qos(180) indicates that TCAM space is allocated
        for RPLUS Ingress/Egress IPv4 QoS.

        redirect(181) indicates that TCAM space is allocated for Redirect.

        rplusIngressEgressIpv4QosLite(182) indicates that TCAM space is
        allocated for RPLUS Ingress/Egress IPv4 QoS Lite.

        rplusIngressEgressIpv6Qos(183) indicates that TCAM space is allocated
        for Ingress/Egress IPv6 QoS.

        rplusIngressEgressMacQos(184) indicates that TCAM space is allocated
        for Ingress/Egress MAC QoS.

        egressIpv4Racl(185) indicates that egress TCAM space is allocated for
        IPv4 RACL.

        egressIpv6Racl(186) indicates that egress TCAM space is allocated for
        IPv6 RACL.

        egressFlowCounters(187) indicates that TCAM space is allocated for
        Egress Flow Counters.

        ingressNsIpv4PortQos(188) indicates that ingress TCAM space is
        allocated for NS IPv4 Port QoS.

        ingressNsIpv6PortQos(189) indicates that ingress TCAM space is
        allocated for NS IPv6 Port QoS.

        ingressNsMTCAM_MIB.myacPortQos(190) indicates that ingress TCAM space is
        allocated for NS MAC Port QoS.

        ingressNsIpv4VlanQos(191) indicates that ingress TCAM space is
        allocated for NS IPv4 VLAN QoS.

        ingressNsIpv6VlanQos(192) indicates that ingress TCAM space is
        allocated for NS IPv6 VLAN QoS.

        ingressNsMacVlanQos(193) indicates that ingress TCAM space is
        allocated for NS MAC VLAN QoS.

        ingressNsIpv4L3Qos(194) indicates that ingress TCAM space is
        allocated for NS IPv4 L3 QoS.

        ingressNsIpv6L3Qos(195) indicates that ingress TCAM space is
        allocated for NS IPv6 L3 QoS.

        vpcConvergence(196) indicates that TCAM space is allocated for
        VPC Convergence.

        ipsgSmacIpBindingTable(197) indicates that TCAM space is
        allocated for IPSG SMAC-IP binding table.

        openflowAcl(198) indicates that TCAM space is allocated for
        OPENFLOW ACL.

        openflowIpv6Acl(199) indicates that TCAM space is allocated
        for OPENFLOW IPV6 ACL.

        ingressEtherAcl(200) indicates that ingress TCAM space is
        allocated for Ether ACL.

        mplsFeature(201) indicates that TCAM space is allocated for
        MPLS feature.

        ingressIpv4Qos(202) indicates that ingress TCAM space is
        allocated for IPv4 QoS.

        ingressIpv6Qos(203) indicates that ingress TCAM space is
        allocated for IPv6 QoS.

        ipv6Sup(204) indicates that TCAM space is allocated for
        IPV6 SUP.

        ingressIpv4Pbr(205) indicates that ingress TCAM space is
        allocated for IPv4 PBR.

        ingressIpv6Pbr(206) indicates that ingress TCAM space is
        allocated for IPv6 PBR.

        ingressIpv4PaclDoubleWide(207) indicates that ingress TCAM
        space is allocated for IPv4 PACL DoubleWide.

        arpAcl(208) indicates that TCAM space is allocated for ARP ACL.

        sflowNorthstarAcl(209) indicates that TCAM space is allocated for
        sFlow Northstar ACL.

        mcastBidir(210) indicates that TCAM space is allocated for mcast
        bidir.

        redirectTunnel(211) indicates that TCAM space is allocated for
        Redirect TUNNEL.

        ingressFcoeCounters(212) indicates that TCAM space is allocated
        for Ingress FCoE Counters.

        egressFcoeCounters(213) indicates that egress TCAM space is
        allocated for Egress FCoE Counters.

        spanSflowCombined(214) indicates that TCAM space is allocated for
        SPAN SFLOW combined.

        mcastPerformance(215) indicates that TCAM space is allocated for
        mcast performance.

        fhs(216) indicates that TCAM space is allocated for FHS.

        openflowLiteAcl(217) indicates that TCAM space is allocated for
        OPENFLOW LITE ACL.

        ipv6DestCompression(218) indicates that TCAM space is allocated
        for IPv6 Dest Compression.

        ingressIpv4RaclLite(219) indicates that TCAM space is allocated
        for Ingress IPv4 RACL Lite.

        ipv6SrcCompression(220) indicates that TCAM space is allocated for
        IPv6 Src Compression.

        ipv4RaclSpanUdf(221) indicates that TCAM space is allocated for
        IPV4 RACL SPAN UDF.

        ingressIpv4PortQosIntraTcamLite(222) indicates that TCAM space is
        allocated for Ingress IPv4 Port QoS (Intra-TCAM Lite).

        ingressIpv4L3QosIntraTcamLite(223) indicates that TCAM space is
        allocated for Ingress IPv4 L3 QoS (Intra-TCAM Lite).

        ingressIpv4VlanQosIntraTcamLite(224) indicates that TCAM space is
        allocated for Ingress IPv4 VLAN QoS (Intra-TCAM Lite).

        ipv4PaclSpanUdf(225) indicates that TCAM space is allocated for IPV4
        PACL SPAN UDF.

        coppSystem(226) indicates that TCAM space is allocated for COPP
        SYSTEM.

        ingressIpv4PaclLite(227) indicates that TCAM space is allocated for
        ingress IPv4 PACL lite.

        ingressIpv4VaclLite(228) indicates that TCAM space is allocated for
        ingress IPv4 VACL lite.

        vxLanXConnect(229) indicates that TCAM space is allocated for VxLAN XConnect.

        dot1X(230) indicates that TCAM space is allocated for DOT1X.

        dot1XMultiAuth(231) indicates that TCAM space is allocated for DOT1X Multi
        Auth.
        
        ingressPaclAll(232) indicates that TCAM space is allocated for
        Ingress PACL ALL.

        ingressRaclAll(233) indicates that TCAM space is allocated for
        Ingress RACL ALL.

        ingressVaclAll(234) indicates that TCAM space is allocated for
        Ingress VACL ALL.

        ingressMacPqos(235) indicates that TCAM space is allocated for
        Ingress MAC PQOS.

        ingressIpv4Pqos(236) indicates that TCAM space is allocated for
        Ingress IPV4 PQOS.

        ingressIpv6Pqos(237) indicates that TCAM space is allocated for
        Ingress IPV6 PQOS.

        ingressPqos(238) indicates that TCAM space is allocated for Ingress PQOS.
        
        ingressMacVqos(239) indicates that TCAM space is allocated for
        Ingress MAC VQOS.

        ingressIpv4Vqos(240) indicates that TCAM space is allocated for
        Ingress IPV4 VQOS.

        ingressIpv6Vqos(241) indicates that TCAM space is allocated for
        Ingress IPV6 VQOS.

        ingressVqosAll(242) indicates that TCAM space is allocated for
        Ingress VQOS ALL.

        ingressIpv4L3qos(243) indicates that TCAM space is allocated for
        Ingress IPV4 L3QOS.

        ingressIpv6L3qos(244) indicates that TCAM space is allocated for
        Ingress IPV6 L3QOS.

        ingressL3qosAll(245) indicates that TCAM space is allocated for
        Ingress L3QOS ALL.

        ingressCopp(246) indicates that TCAM space is allocated for Ingress COPP.
        
        ingressMacSpan(247) indicates that TCAM space is allocated for
        Ingress MAC SPAN.

        ingressIpv4Span(248) indicates that TCAM space is allocated for
        Ingress IPV4 SPAN.

        ingressSpan(249) indicates that TCAM space is allocated for Ingress SPAN.
        
        ingressSpanAll(250) indicates that TCAM space is allocated for
        Ingress SPAN ALL.

        egressRaclAll(251) indicates that TCAM space is allocated for
        Egress RACL ALL.

        egressVaclAll(252) indicates that TCAM space is allocated for
        Egress VACL ALL.

        egressMacPortQos(253) indicates that TCAM space is allocated for
        Egress MAC Port QOS.

        egressIpv4PortQos(254) indicates that TCAM space is allocated for
        Egress IPV4 Port QOS.

        egressIv6PortQos(255) indicates that TCAM space is allocated for
        Egress IPv6 Port QOS.

        egressPortQos(256) indicates that TCAM space is allocated for
        Egress Port QOS.

        egressMacVlanQos(257) indicates that TCAM space is allocated for
        Egress MAC VLAN QOS.

        egressIpv4VlanQos(258) indicates that TCAM space is allocated for
        Egress IPV4 VLAN QOS.

        egressIpv6VlanQos(259) indicates that TCAM space is allocated for
        Egress IPV6 VLAN QOS.

        egressVlanQos(260) indicates that TCAM space is allocated for
        Egress VLAN QOS.

        egressIpv4L3Qos(261) indicates that TCAM space is allocated for
        Egress IPV4 L3 QOS.

        egressIpv6L3Qos(262) indicates that TCAM space is allocated for
        Egress IPV6 L3 QOS.

        egressL3QosAll(263) indicates that TCAM space is allocated for
        Egress L3 QOS ALL.

        egressMacSpan(264) indicates that TCAM space is allocated for
        Egress MAC SPAN.

        egressIpv4Span(265) indicates that TCAM space is allocated for
        Egress IPV4 SPAN.

        egressIpv6Span(266) indicates that TCAM space is allocated for
        Egress IPV6 SPAN.

        egressSpanAll(267) indicates that TCAM space is allocated for
        Egress SPAN ALL.

        dhcp(268) indicates that TCAM space is allocated for DHCP.
        
        labelLblA(269) indicates that TCAM space is allocated for
        Label LBL A, Ingress-Physical interface-PACL,PQOS.

        labelLblB(270) indicates that TCAM space is allocated for
        Label LBL B, Ingress-Logical interface-PACL,PQOS.

        labelLblD(271) indicates that TCAM space is allocated for
        Label LBL D, Ingress-Physical interface-RACL,L3QOS.

        labelLblE(272) indicates that TCAM space is allocated for
        Label LBL E, Ingress-Logical interface-RACL,L3QOS.

        labelLblF(273) indicates that TCAM space is allocated for
        Label LBL F, Egress-Physical interface-RACL,L3QOS.

        labelLblG(274) indicates that TCAM space is allocated for
        Label LBL G, Egress-Logical interface-RACL,L3QOS.

        labelLblH(275) indicates that TCAM space is allocated for
        Label LBL H, Ingress-VLAN-VACL,VQOS.

        labelLblI(276) indicates that TCAM space is allocated for
        Label LBL I, Egress-VLAN-VACL,VQOS.

        labelLblK(277) indicates that TCAM space is allocated for
        Label LBL K, Ingress-All interface-DHCP.

        ingressSupAll(278) indicates that TCAM space is allocated for
        Ingress SUP ALL.

        egressSupAll(279) indicates that TCAM space is allocated for
        Egress SUP ALL.

        ingressVlanSpan(280) indicates that TCAM space is allocated for
        Ingress Vlan SPAN.

        ingressNetflow(281) indicates that TCAM space is allocated for
        Ingress Netflow.
        
        ingressCntAcl(282) indicates that TCAM space is allocated for
        Ingress CNTACL.

        egressCntAcl(283) indicates that TCAM space is allocated for
        Egress CNTACL.

        ingressHwTelemetry(284) indicates that TCAM space is allocated for
        Ingress HW-TELEMETRY.

        labelLblAv1(285) indicates that TCAM space is allocated for Label LBL A.

        labelLblBv1(286) indicates that TCAM space is allocated for Label LBL B.

        labelLblCv1(287) indicates that TCAM space is allocated for Label LBL C.

        labelLblDv1(288) indicates that TCAM space is allocated for Label LBL D.

        labelLblEv1(289) indicates that TCAM space is allocated for Label LBL E.

        labelLblFv1(290) indicates that TCAM space is allocated for Label LBL F.

        labelLblGv1(291) indicates that TCAM space is allocated for Label LBL G.

        labelLblHv1(292) indicates that TCAM space is allocated for Label LBL H.

        labelLblIv1(293) indicates that TCAM space is allocated for Label LBL I.

        labelLblJv1(294) indicates that TCAM space is allocated for Label LBL J.

        labelLblKv1(295) indicates that TCAM space is allocated for Label LBL K.

        labelLblLv1(296) indicates that TCAM space is allocated for Label LBL L.

        labelLblMv1(297) indicates that TCAM space is allocated for Label LBL M.

        labelLblNv1(298) indicates that TCAM space is allocated for Label LBL N.

        labelLblOv1(299) indicates that TCAM space is allocated for Label LBL O.

        labelLblPv1(300) indicates that TCAM space is allocated for Label LBL P.

        labelLblQv1(301) indicates that TCAM space is allocated for Label LBL Q.

        labelLblRv1(302) indicates that TCAM space is allocated for Label LBL R.

        ingressNetflowAnalytics(303) indicates that TCAM space is allocated for
        Ingress Netflow/Analytics.

        ingressNatOutside(304) indicates that TCAM space is allocated for Ingress
        NAT OUTSIDE.

        ingressNatInside(305) indicates that TCAM space is allocated for Ingress
        NAT INSIDE.

        ingressL2L3QosAll(306) indicates that TCAM space is allocated for
        Ingress L2 L3 QOS ALL.

        natRewriteTable(307) indicates that TCAM space is allocated for NAT
        Rewrite Table.

        tcpAwareNat(308) indicates that TCAM space is allocated for TCP
        Aware NAT."
        
    ::= { cseTcamUsageEntry 1 }

cseTcamResourceDescr OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The short description of the resource type." 
    ::= { cseTcamUsageEntry 2 }

cseTcamResourceUsed OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The amount of TCAM resource has been used up for this resource
        type." 
    ::= { cseTcamUsageEntry 3 }

cseTcamResourceTotal OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total amount of TCAM resource is allocated for this
        resource type." 
    ::= { cseTcamUsageEntry 4 }
 

-- cseMet group
--   

--   
-- The cseMetUsageTable

cseMetUsageTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CseMetUsageEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains the resource usage of MET (Multicast
        Expansion Table) in the device."
    ::= { cseMet 1 }

cseMetUsageEntry OBJECT-TYPE
    SYNTAX          CseMetUsageEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the total number of entries in each MET,
        the number of free entries in unallocated as well as
        allocated space of the MET. Each row represents MET data 
        maintained by each module (identified by its entPhysicalIndex)
        which is capable of this feature."
    INDEX           {
                        entPhysicalIndex,
                        cseMetIndex
                    } 
    ::= { cseMetUsageTable 1 }

CseMetUsageEntry ::= SEQUENCE {
        cseMetIndex                     Unsigned32,
        cseMetTotalEntries              Unsigned32,
        cseMetUnallocatedSpcFreeEntries Unsigned32,
        cseMetAllocatedSpcFreeEntries   Unsigned32
}

cseMetIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A value uniquely identifies a MET in a module." 
    ::= { cseMetUsageEntry 1 }

cseMetTotalEntries OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of entries in this MET." 
    ::= { cseMetUsageEntry 2 }

cseMetUnallocatedSpcFreeEntries OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of free entries reside in unallocated
        space of this MET." 
    ::= { cseMetUsageEntry 3 }

cseMetAllocatedSpcFreeEntries OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of free entries reside in allocated
        space of this MET." 
    ::= { cseMetUsageEntry 4 }
 

-- Notifications

cseMIBNotifications  OBJECT IDENTIFIER
    ::= { ciscoSwitchEngineMIB 2 }

-- no notifications defined
--   
-- Conformance

cseMIBConformance  OBJECT IDENTIFIER
    ::= { ciscoSwitchEngineMIB 3 }

cseMIBCompliances  OBJECT IDENTIFIER
    ::= { cseMIBConformance 1 }

cseMIBGroups  OBJECT IDENTIFIER
    ::= { cseMIBConformance 2 }


-- compliance statements

cseMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH_ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseMcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP multicast flow cache purging in the system."
    ::= { cseMIBCompliances 1 }

cseMIBCompliance2 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH-ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseMcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP multicast flow cache purging in the system."

    GROUP           cseFlowMgmtOperStatusGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support operating status on aging time for flows used
        in L3 switching."
    ::= { cseMIBCompliances 2 }

cseMIBCompliance3 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH-ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseFlowMgmtOperStatusGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support operating status on aging time for flows used
        in L3 switching."

    GROUP           cse4kVlanGroup
    DESCRIPTION
        "This group must be implemented by the devices which
        support the range of VlanIndex between 1024 and 4095"
    ::= { cseMIBCompliances 3 }

cseMIBCompliance4 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH-ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseMcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP multicast flow cache purging in the system."

    GROUP           cseFlowMgmtOperStatusGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support operating status on aging time for flows used
        in L3 switching."

    GROUP           cse4kVlanGroup
    DESCRIPTION
        "This group must be implemented by the devices which
        support the range of VlanIndex between 1024 and 4095"

    GROUP           cseNDEMandatoryGroup
    DESCRIPTION
        "This group is mandatory for those switched which
        support Netflow Data Export"

    GROUP           cseNDESingleFilterGroup
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is single."

    GROUP           cseNDEMultipleFiltersGroup
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is multiple."

    GROUP           cseProtocolFilterGroup
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseStatisticsGroup2
    DESCRIPTION
        "Implementation of this group is optional."

    OBJECT          cseNetflowLSFilterSelection
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter."

    OBJECT          cseNetflowLSFilterStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter"
    ::= { cseMIBCompliances 4 }

cseMIBCompliance5 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH-ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroupRev1
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseMcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP multicast flow cache purging in the system."

    GROUP           cseFlowMgmtOperStatusGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support operating status on aging time for flows used
        in L3 switching."

    GROUP           cse4kVlanGroup
    DESCRIPTION
        "This group must be implemented by the devices which
        support the range of VlanIndex between 1024 and 4095"

    GROUP           cseNDEMandatoryGroup
    DESCRIPTION
        "This group is mandatory for those switched which
        support Netflow Data Export"

    GROUP           cseNDESingleFilterGroup
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is single."

    GROUP           cseNDEMultipleFiltersGroup
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is multiple."

    GROUP           cseProtocolFilterGroup
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseStatisticsGroup2
    DESCRIPTION
        "Implementation of this group is optional."

    OBJECT          cseNetflowLSFilterSelection
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter."

    OBJECT          cseNetflowLSFilterStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter"
    ::= { cseMIBCompliances 5 }

cseMIBCompliance6 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH-ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroupRev1
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseMcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP multicast flow cache purging in the system."

    GROUP           cseFlowMgmtOperStatusGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support operating status on aging time for flows used
        in L3 switching."

    GROUP           cse4kVlanGroup
    DESCRIPTION
        "This group must be implemented by the devices which
        support the range of VlanIndex between 1024 and 4095"

    GROUP           cseNDEMandatoryGroup
    DESCRIPTION
        "This group is mandatory for those switched which
        support Netflow Data Export"

    GROUP           cseNDESingleFilterGroupRev1
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is single."

    GROUP           cseNDEMultipleFiltersGroup
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is multiple."

    GROUP           cseProtocolFilterGroup
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseStatisticsGroup2
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseFlowMgmtExtGroup2
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    OBJECT          cseNetflowLSFilterSelection
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter."

    OBJECT          cseNetflowLSFilterStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter"
    ::= { cseMIBCompliances 6 }

cseMIBCompliance7 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH-ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroupRev1
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup1
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseMcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP multicast flow cache purging in the system."

    GROUP           cseFlowMgmtOperStatusGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support operating status on aging time for flows used
        in L3 switching."

    GROUP           cse4kVlanGroup
    DESCRIPTION
        "This group must be implemented by the devices which
        support the range of VlanIndex between 1024 and 4095"

    GROUP           cseNDEMandatoryGroup
    DESCRIPTION
        "This group is mandatory for those switched which
        support Netflow Data Export"

    GROUP           cseNDESingleFilterGroupRev1
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is single."

    GROUP           cseNDEMultipleFiltersGroup
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is multiple."

    GROUP           cseProtocolFilterGroup
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseStatisticsGroup2
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseFlowMgmtExtGroup2
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastRtrMgmtGroup
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseFlowMcastMgmtGroup2
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    OBJECT          cseNetflowLSFilterSelection
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter."

    OBJECT          cseNetflowLSFilterStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter"
    ::= { cseMIBCompliances 7 }

cseMIBCompliance8 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for
        the CISCO-SWITCH-ENGINE-MIB MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { cseStatisticsGroup }

    GROUP           cseRouterGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseVlanStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        can provide per-vlan statistics."

    GROUP           cseFlowMgmtGroupRev1
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastMgmtGroup1
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseUcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP/IPX unicast flow cache purging in the system."

    GROUP           cseMcastCachePurgeGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        supports IP multicast flow cache purging in the system."

    GROUP           cseFlowMgmtOperStatusGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support operating status on aging time for flows used
        in L3 switching."

    GROUP           cse4kVlanGroup
    DESCRIPTION
        "This group must be implemented by the devices which
        support the range of VlanIndex between 1024 and 4095"

    GROUP           cseNDEMandatoryGroup
    DESCRIPTION
        "This group is mandatory for those switched which
        support Netflow Data Export"

    GROUP           cseNDESingleFilterGroupRev1
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is single."

    GROUP           cseNDEMultipleFiltersGroup
    DESCRIPTION
        "This group is mandatory in agents for which the value of
        cseNetflowLSFilterSupport is multiple."

    GROUP           cseProtocolFilterGroup
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseStatisticsGroup2
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseFlowMgmtExtGroup2
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 switching in the system."

    GROUP           cseFlowMcastRtrMgmtGroup
    DESCRIPTION
        "Implementation of this group is optional."

    GROUP           cseFlowMcastMgmtGroup2
    DESCRIPTION
        "This group is mandatory only for those switches which
        support layer 3 IP multicast switching in the system."

    GROUP           cseCacheStatisticsGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support switch engine statistics on flow cache entries
        in the system."

    GROUP           cseL3SwitchedPktsPerSecGroup
    DESCRIPTION
        "This group is mandatory only for those switches which
        support switch engine statistics on total number of 
        packets switched per second in the system."

    GROUP           cseStatisticsFlowGroup1
    DESCRIPTION
        "This group is mandatory only for those switches which
        support switch engine statistics on total number of 
        Ipv4 flow entries."

    OBJECT          cseNetflowLSFilterSelection
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter."

    OBJECT          cseNetflowLSFilterStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required, for systems which
        support just one include and one exclude filter"
    ::= { cseMIBCompliances 8 }

-- units of conformance

cseStatisticsGroup OBJECT-GROUP
    OBJECTS         {
                        cseL2ForwardedLocalPkts,
                        cseL2ForwardedLocalOctets,
                        cseL2ForwardedTotalPkts,
                        cseL2NewAddressLearns,
                        cseL2AddrLearnFailures,
                        cseL2DstAddrLookupMisses,
                        cseL3SwitchedTotalPkts,
                        cseL3SwitchedTotalOctets,
                        cseL3CandidateFlowHits,
                        cseL3EstablishedFlowHits,
                        cseL3ActiveFlows,
                        cseL3FlowLearnFailures,
                        cseL3IntFlowInvalids,
                        cseL3ExtFlowInvalids,
                        cseL2HCOverflowForwardedLocalPkts,
                        cseL2HCForwardedLocalPkts,
                        cseL2HCOverflowForwardedTotalPkts,
                        cseL2HCForwardedTotalPkts,
                        cseL2HCOverflowIpPkts,
                        cseL2HCIpPkts,
                        cseL2HCOverflowIpxPkts,
                        cseL2HCIpxPkts,
                        cseL2HCOverflowAssignedProtoPkts,
                        cseL2HCAssignedProtoPkts,
                        cseL2HCOverflowOtherProtoPkts,
                        cseL2HCOtherProtoPkts
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing switch engine statistics."
    ::= { cseMIBGroups 1 }

cseStatisticsGroup2 OBJECT-GROUP
    OBJECTS         {
                        cseStatsFlowAged,
                        cseStatsFlowPurged,
                        cseStatsFlowParityFail,
                        cseCacheUtilization
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing switch engine statistics
        on aged/purged flows, and the cache utilizations."
    ::= { cseMIBGroups 2 }

cseVlanStatisticsGroup OBJECT-GROUP
    OBJECTS         {
                        cseL3VlanInPkts,
                        cseL3VlanInOctets,
                        cseL3VlanOutPkts,
                        cseL3VlanOutOctets
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing per-vlan switch engine
        statistics, if supported."
    ::= { cseMIBGroups 3 }

cseRouterGroup OBJECT-GROUP
    OBJECTS         {
                        cseRouterFlowMask,
                        cseRouterName,
                        cseRouterStatic,
                        cseStaticRouterOwner,
                        cseStaticRouterName,
                        cseStaticRouterType,
                        cseStaticRouterStatus,
                        cseRouterIpxFlowMask,
                        cseStaticIpxRouterOwner,
                        cseStaticIpxRouterName,
                        cseStaticIpxRouterStatus,
                        cseRouterMac,
                        cseRouterProtocol
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information on routers which
        are used to support layer 3 switching in the system."
    ::= { cseMIBGroups 4 }

cseFlowMgmtGroup OBJECT-GROUP
    OBJECTS         {
                        cseFlowEstablishedAgingTime,
                        cseFlowFastAgingTime,
                        cseFlowFastAgePktThreshold,
                        cseFlowIPXEstablishedAgingTime,
                        cseFlowMaxQueries,
                        cseFlowQueryMask,
                        cseFlowQueryTransport,
                        cseFlowQuerySource,
                        cseFlowQuerySourceMask,
                        cseFlowQueryDestination,
                        cseFlowQueryDestinationMask,
                        cseFlowQueryRouterIndex,
                        cseFlowQueryOwner,
                        cseFlowQueryResultingRows,
                        cseFlowQueryResultTotalPkts,
                        cseFlowQueryResultTotalOctets,
                        cseFlowQueryResultAvgDuration,
                        cseFlowQueryResultAvgIdle,
                        cseFlowQueryStatus,
                        cseFlowQueryCreateTime,
                        cseFlowDataSrcMac,
                        cseFlowDataDstMac,
                        cseFlowDataEncapType,
                        cseFlowDataSource,
                        cseFlowDataStaticFlow,
                        cseFlowDataDestination,
                        cseFlowDataDestVlan,
                        cseFlowDataIpQOS,
                        cseFlowDataIpQOSPolicy,
                        cseFlowDataWhenCreated,
                        cseFlowDataLastUsed,
                        cseFlowDataPkts,
                        cseFlowDataOctets,
                        cseFlowSwitchStatus
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing information for determining the
        L3 flow switching performance in the switching engine. There may
        be some platform specific limitations when performing a SET 
        on some of these objects.

        The following are valid for Catalyst 5000 platforms:

        - cseFlowEstablishedAgingTime has a default value of 256.

        - cseFlowFastAgePktThreshold can only be set to 1, 3, 7, 15, 31 
        or 63 packets. If the packet threshold is not configured to one 
        of these values, it will be adjusted to the closest value.

        - cseFlowFastAgingTime can be set to only values that are 
        multiples  of 8 in the range (0..128).
        If it is set to a value that is not 
        multiple of 8, then the closest value that is a multiple of 8
        will take effect. The default value for fast aging time is 32
        seconds. (i.e. less than cseFlowFastAgePktThreshold number of
        packets were switched within 32 seconds after the an L3
        flow entry was established).

        - cseFlowIPXEstablishedAgingTime has a default value of 256.

        cseFlowMgmtGroup object is superseded by cseFlowMgmtGroupRev1."
    ::= { cseMIBGroups 5 }

cseNetflowLSGroup OBJECT-GROUP
    OBJECTS         {
                        cseNetflowLSExportHost,
                        cseNetflowLSExportTransportNumber,
                        cseNetflowLSExportStatus,
                        cseNetflowLSExportDataSource,
                        cseNetflowLSExportDataSourceMask,
                        cseNetflowLSExportDataDest,
                        cseNetflowLSExportDataDestMask,
                        cseNetflowLSExportDataProtocol,
                        cseNetflowLSExportDataFilterSelection,
                        cseNetflowLSExportNDEVersionNumber
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing information on the Netflow LAN
        switching Data Export feature, if supported.

        cseNetflowLSGroup object is superseded by cseNDESingleFilterGroupRev1."
    ::= { cseMIBGroups 6 }

cseProtocolFilterGroup OBJECT-GROUP
    OBJECTS         {
                        cseProtocolFilterPortAdminStatus,
                        cseProtocolFilterPortOperStatus,
                        cseL2IpPkts,
                        cseL2IpxPkts,
                        cseL2AssignedProtoPkts,
                        cseL2OtherProtoPkts
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information on the Protocol
        filter feature, if supported."
    ::= { cseMIBGroups 7 }

cseFlowMcastMgmtGroup OBJECT-GROUP
    OBJECTS         {
                        cseFlowMcastMaxQueries,
                        cseFlowMcastQueryMask,
                        cseFlowMcastQuerySrc,
                        cseFlowMcastQueryGrp,
                        cseFlowMcastQuerySrcVlan,
                        cseFlowMcastQueryRtrIndex,
                        cseFlowMcastQuerySkipNFlows,
                        cseFlowMcastQueryOwner,
                        cseFlowMcastQueryTotalFlows,
                        cseFlowMcastQueryRows,
                        cseFlowMcastQueryStatus,
                        cseFlowMcastQueryCreateTime,
                        cseFlowMcastResultSrc,
                        cseFlowMcastResultGrp,
                        cseFlowMcastResultSrcVlan,
                        cseFlowMcastResultRtrIp,
                        cseFlowMcastResultRtrMac,
                        cseFlowMcastResultCreatedTS,
                        cseFlowMcastResultLastUsedTS,
                        cseFlowMcastResultPkts,
                        cseFlowMcastResultOctets,
                        cseFlowMcastResultDstVlans,
                        cseFlowMcastSwitchStatus
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects for querying IP multicast flows
        stored in hardware switching cache.

        cseFlowMcastMgmtGroup object is superseded by cseFlowMcastMgmtGroup1."
    ::= { cseMIBGroups 8 }

cseUcastCachePurgeGroup OBJECT-GROUP
    OBJECTS         {
                        cseUcastCacheFlowType,
                        cseUcastCacheTransport,
                        cseUcastCacheDest,
                        cseUcastCacheDestMask,
                        cseUcastCacheSource,
                        cseUcastCacheSrcMask,
                        cseUcastCacheRtrIp,
                        cseUcastCacheOwner,
                        cseUcastCacheStatus,
                        cseUcastCacheResult
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing IP/IPX unicast flow cache
        purging function."
    ::= { cseMIBGroups 9 }

cseMcastCachePurgeGroup OBJECT-GROUP
    OBJECTS         {
                        cseMcastCacheFlowType,
                        cseMcastCacheGrp,
                        cseMcastCacheSrc,
                        cseMcastCacheRtrIp,
                        cseMcastCacheOwner,
                        cseMcastCacheStatus,
                        cseMcastCacheResult
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing IP multicast flow cache purge
        function."
    ::= { cseMIBGroups 10 }

cseFlowMgmtOperStatusGroup OBJECT-GROUP
    OBJECTS         {
                        cseFlowOperEstablishedAgingTime,
                        cseFlowOperFastAgingTime,
                        cseFlowOperFastAgePktThreshold,
                        cseFlowOperIPXAgingTime
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing operating status information
        on aging time for flows used in L3 switching."
    ::= { cseMIBGroups 11 }

cse4kVlanGroup OBJECT-GROUP
    OBJECTS         {
                        cseFlowMcastResultDstVlans2k,
                        cseFlowMcastResultDstVlans3k,
                        cseFlowMcastResultDstVlans4k
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information
        for VLANS with VlanIndex from 1024 to 4095."
    ::= { cseMIBGroups 12 }

cseNDEMandatoryGroup OBJECT-GROUP
    OBJECTS         {
                        cseNetflowLSFilterSupport,
                        cseNetflowLSExportStatus,
                        cseNetflowLSExportNDEVersionNumber
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information on the type
        of filter support, status and the version of NDE used."
    ::= { cseMIBGroups 13 }

cseNDESingleFilterGroup OBJECT-GROUP
    OBJECTS         {
                        cseNetflowLSExportHost,
                        cseNetflowLSExportTransportNumber,
                        cseNetflowLSExportDataSource,
                        cseNetflowLSExportDataSourceMask,
                        cseNetflowLSExportDataDest,
                        cseNetflowLSExportDataDestMask,
                        cseNetflowLSExportDataProtocol,
                        cseNetflowLSExportDataFilterSelection
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing information on the Netflow LAN
        switching Data Export feature, with a single host and a
        single filter support.

        cseNDESingleFilterGroup object is superseded by cseNDESingleFilterGroupRev1."
    ::= { cseMIBGroups 14 }

cseNDEMultipleFiltersGroup OBJECT-GROUP
    OBJECTS         {
                        cseNetflowLSFilterDataSource,
                        cseNetflowLSFilterDataSourceMask,
                        cseNetflowLSFilterDataDest,
                        cseNetflowLSFilterDataDestMask,
                        cseNetflowLSFilterDataProtocol,
                        cseNetflowLSFilterSelection,
                        cseNetflowLSFilterStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information on the Netflow LAN
        switching Data Export feature, with multiple filter support."
    ::= { cseMIBGroups 15 }

cseFlowMgmtGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        cseFlowEstablishedAgingTime,
                        cseFlowFastAgingTime,
                        cseFlowFastAgePktThreshold,
                        cseFlowIPXEstablishedAgingTime,
                        cseFlowMaxQueries,
                        cseFlowQueryMask,
                        cseFlowQueryTransport,
                        cseFlowQuerySource,
                        cseFlowQuerySourceMask,
                        cseFlowQueryDestination,
                        cseFlowQueryDestinationMask,
                        cseFlowQueryRouterIndex,
                        cseFlowQueryOwner,
                        cseFlowQueryResultingRows,
                        cseFlowQueryResultTotalPkts,
                        cseFlowQueryResultTotalOctets,
                        cseFlowQueryResultAvgDuration,
                        cseFlowQueryResultAvgIdle,
                        cseFlowQueryStatus,
                        cseFlowQueryCreateTime,
                        cseFlowQueryTotalFlows,
                        cseFlowDataSrcMac,
                        cseFlowDataDstMac,
                        cseFlowDataEncapType,
                        cseFlowDataSource,
                        cseFlowDataStaticFlow,
                        cseFlowDataDestination,
                        cseFlowDataDestVlan,
                        cseFlowDataIpQOS,
                        cseFlowDataIpQOSPolicy,
                        cseFlowDataWhenCreated,
                        cseFlowDataLastUsed,
                        cseFlowDataPkts,
                        cseFlowDataOctets,
                        cseFlowSwitchStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information for determining the
        L3 flow switching performance in the switching engine. There may
        be some platform specific limitations when performing a SET 
        on some of these objects.

        The following are valid for Catalyst 5000 platforms:

        - cseFlowEstablishedAgingTime has a default value of 256.

        - cseFlowFastAgePktThreshold can only be set to 1, 3, 7, 15, 31 
        or 63 packets. If the packet threshold is not configured to one 
        of these values, it will be adjusted to the closest value.

        - cseFlowFastAgingTime can be set to only values that are 
        multiples  of 8 in the range (0..128).
        If it is set to a value that is not 
        multiple of 8, then the closest value that is a multiple of 8
        will take effect. The default value for fast aging time is 32
        seconds. (i.e. less than cseFlowFastAgePktThreshold number of
        packets were switched within 32 seconds after the an L3
        flow entry was established).

        - cseFlowIPXEstablishedAgingTime has a default value of 256."
    ::= { cseMIBGroups 16 }

cseL3ErrorsGroup OBJECT-GROUP
    OBJECTS         {
                        cseIpPlenErrors,
                        cseIpTooShortErrors,
                        cseIpCheckSumErrors,
                        cseIpxPlenErrors,
                        cseIpxTooShortErrors
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the IP and IPX error
        statistics."
    ::= { cseMIBGroups 17 }

cseBridgedFlowGroup OBJECT-GROUP
    OBJECTS         { cseFlowBridgedFlowStatsEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects control the reporting of intra-vlan
        bridged flow statistics."
    ::= { cseMIBGroups 18 }

cseVlanStatisticsExtGroup OBJECT-GROUP
    OBJECTS         {
                        cseL3VlanInUnicastPkts,
                        cseL3VlanInUnicastOctets,
                        cseL3VlanOutUnicastPkts,
                        cseL3VlanOutUnicastOctets
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing additional per-vlan switch
        engine statistics, if supported."
    ::= { cseMIBGroups 19 }

cseProtocolFilterExtGroup OBJECT-GROUP
    OBJECTS         { cseProtocolFilterEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing additional information on the
        Protocol filter feature, if supported."
    ::= { cseMIBGroups 20 }

cseFlowMgmtExtGroup OBJECT-GROUP
    OBJECTS         {
                        cseFlowIPFlowMask,
                        cseFlowIPXFlowMask
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing additional information on the
        L3 flow switching in the switching engine."
    ::= { cseMIBGroups 21 }

cseFlowMgmtExtGroup1 OBJECT-GROUP
    OBJECTS         {
                        cseFlowLongAgingTime,
                        cseFlowExcludeProtocol,
                        cseFlowExcludeStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing additional information on the
        L3 flow switching in the switching engine."
    ::= { cseMIBGroups 22 }

cseNDEReportGroup OBJECT-GROUP
    OBJECTS         { cseNetFlowIfIndexEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        on NDE ifIndex report feature."
    ::= { cseMIBGroups 23 }

cseStatisticsFlowGroup OBJECT-GROUP
    OBJECTS         { cseFlowTotalFlows }
    STATUS          current
    DESCRIPTION
        "A collection of object providing switch engine statistics
        on total number of installed flows."
    ::= { cseMIBGroups 24 }

cseFlowMgmtExtGroup2 OBJECT-GROUP
    OBJECTS         { cseFlowQuerySkipNFlows }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing additional information on the
        L3 flow switching in the switching engine."
    ::= { cseMIBGroups 25 }

cseNDESingleFilterGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        cseNetflowLSExportDataSource,
                        cseNetflowLSExportDataSourceMask,
                        cseNetflowLSExportDataDest,
                        cseNetflowLSExportDataDestMask,
                        cseNetflowLSExportDataProtocol,
                        cseNetflowLSExportDataFilterSelection
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information on the Netflow LAN
        switching Data Export feature, with a single filter support."
    ::= { cseMIBGroups 26 }

cseCefFibAdjacencyGroup OBJECT-GROUP
    OBJECTS         {
                        cseCefFibAddrType,
                        cseCefFibDestIp,
                        cseCefFibDestIpMask,
                        cseCefFibType,
                        cseCefAdjacencyAddrType,
                        cseCefAdjacencyNextHopIp,
                        cseCefAdjacencyNextHopMac,
                        cseCefAdjacencyNextHopIfIndex,
                        cseCefAdjacencyType,
                        cseCefAdjacencyPkts,
                        cseCefAdjacencyOctets
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing FIB and adjacency
        information available in the device."
    ::= { cseMIBGroups 27 }

cseCefAdjacencyEncapGroup OBJECT-GROUP
    OBJECTS         { cseCefAdjacencyEncap }
    STATUS          current
    DESCRIPTION
        "A collection of object providing adjacency next hop
        encapsulation information available in the device."
    ::= { cseMIBGroups 28 }

cseCefAdjacencyMTUGroup OBJECT-GROUP
    OBJECTS         { cseCefAdjacencyMTU }
    STATUS          current
    DESCRIPTION
        "A collection of object providing adjacency next hop
        MTU information available in the device."
    ::= { cseMIBGroups 29 }

cseTcamUsageGroup OBJECT-GROUP
    OBJECTS         {
                        cseTcamResourceDescr,
                        cseTcamResourceUsed,
                        cseTcamResourceTotal
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the resource usage
        information on TCAM available in the device."
    ::= { cseMIBGroups 30 }

cseL3ErrorsLCGroup OBJECT-GROUP
    OBJECTS         {
                        cseLCIpPlenErrors,
                        cseLCIpTooShortErrors,
                        cseLCIpCheckSumErrors,
                        cseLCIpxPlenErrors,
                        cseLCIpxTooShortErrors
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the IP and IPX error
        statistics."
    ::= { cseMIBGroups 31 }

cseNetflowASInfoExportGroup OBJECT-GROUP
    OBJECTS         { cseNetflowASInfoExportCtrl }
    STATUS          current
    DESCRIPTION
        "A collection of object providing AS number information
        export control."
    ::= { cseMIBGroups 32 }

cseNetflowPerVlanIfGroup OBJECT-GROUP
    OBJECTS         {
                        cseNetflowPerVlanIfGlobalEnable,
                        cseNetflowPerVlanIfEnable
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing control of netflow entry
        creation per vlan."
    ::= { cseMIBGroups 33 }

cseMetUsageGroup OBJECT-GROUP
    OBJECTS         {
                        cseMetTotalEntries,
                        cseMetUnallocatedSpcFreeEntries,
                        cseMetAllocatedSpcFreeEntries
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing MET utilization
        information."
    ::= { cseMIBGroups 34 }

cseFlowMcastMgmtGroup1 OBJECT-GROUP
    OBJECTS         {
                        cseFlowMcastMaxQueries,
                        cseFlowMcastQueryMask,
                        cseFlowMcastQuerySrcVlan,
                        cseFlowMcastQuerySkipNFlows,
                        cseFlowMcastQueryOwner,
                        cseFlowMcastQueryTotalFlows,
                        cseFlowMcastQueryRows,
                        cseFlowMcastQueryStatus,
                        cseFlowMcastQueryCreateTime,
                        cseFlowMcastResultSrcVlan,
                        cseFlowMcastResultCreatedTS,
                        cseFlowMcastResultLastUsedTS,
                        cseFlowMcastResultPkts,
                        cseFlowMcastResultOctets,
                        cseFlowMcastResultDstVlans,
                        cseFlowMcastSwitchStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for querying IP multicast flows
        stored in hardware switching cache."
    ::= { cseMIBGroups 35 }

cseFlowMcastRtrMgmtGroup OBJECT-GROUP
    OBJECTS         {
                        cseFlowMcastQueryRtrIndex,
                        cseFlowMcastResultRtrIp,
                        cseFlowMcastResultRtrMac
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for specifying the router based
        information while IP multicast flows stored in the hardware 
        switching cache are queried."
    ::= { cseMIBGroups 36 }

cseFlowMcastMgmtGroup2 OBJECT-GROUP
    OBJECTS         {
                        cseFlowMcastQueryMvrf,
                        cseFlowMcastQueryAddrType,
                        cseFlowMcastQuerySource,
                        cseFlowMcastQueryGroup,
                        cseFlowMcastResultMvrf,
                        cseFlowMcastResultAddrType,
                        cseFlowMcastResultGroup,
                        cseFlowMcastResultSource,
                        cseFlowMcastResultFlowType,
                        cseFlowMcastResultHFlag1k2k,
                        cseFlowMcastResultHFlag3k4k
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for enhanced querying of
        IP multicast flows stored in hardware switching cache."
    ::= { cseMIBGroups 37 }

cseCacheStatisticsGroup OBJECT-GROUP
    OBJECTS         {
                        cseCacheEntriesCreated,
                        cseCacheEntriesFailed
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing switch engine statistics
        on the flow cache entries."
    ::= { cseMIBGroups 38 }

cseL3SwitchedPktsPerSecGroup OBJECT-GROUP
    OBJECTS         {
                        cseL3SwitchedPktsPerSec,
                        cseL3SwitchedAggrPktsPerSec
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing switch engine
        statistics on total number of packets switched per
        second."
    ::= { cseMIBGroups 39 }

cseStatisticsFlowGroup1 OBJECT-GROUP
    OBJECTS         { cseFlowTotalIpv4Flows }
    STATUS          current
    DESCRIPTION
        "A collection of object providing switch engine statistics
        on total number of Ipv4 flow entries."
    ::= { cseMIBGroups 40 }

END


