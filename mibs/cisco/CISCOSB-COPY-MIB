CISCOSB-COPY-MIB DEFINITIONS ::= BEGIN

IMPORTS
    switch001, rndNotifications             FROM CISCOSB-MIB
    rndErrorDesc , rndErrorSeverity         FROM CISCOSB-DEVICEPARAMS-MIB
    OBJECT-TYP<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,<PERSON><PERSON>, <PERSON>T<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>-IDENTI<PERSON>, NOTIFICATION-TYPE      FROM SNMPv2-SMI
    DisplayString, TEXTUAL-CONVENTION,
    TruthValue, RowStatus                   FROM SNMPv2-TC
    InetAddressType,InetAddress             FROM INET-ADDRESS-MIB
    RlSecSdAccessType                       FROM CISCOSB-SECSD-MIB; -- RFC2851

 rlCopy   MODULE-IDENTITY
          LAST-UPDATED "202105170000Z"
          ORGANIZATION "Cisco Systems, Inc."

          CONTACT-INFO
          "Postal: 170 West Tasman Drive
          San Jose , CA 95134-1706
          USA

          
          Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

          DESCRIPTION
                  "Added field rlCopyInetSpecificCopyInfo"
          REVISION "201007250000Z"
          DESCRIPTION
                  "Added USB file type."
          REVISION "201005110000Z"
          DESCRIPTION
                  "Added objects rlCopyInetDestinationUnitList,rlCopyHistoryInetDestinationUnitList,rlCopyInetUnitStatusList,rlCopyHistoryInetUnitStatusList."
          REVISION "201002170000Z"
          DESCRIPTION
                  "Added mirror-config and MIB rlCopyMirrorTimeout."
          REVISION "200908100000Z"
          DESCRIPTION
                  "The private MIB module definition for file copy in CISCOSB devices."
          REVISION "200602020000Z"
          DESCRIPTION
                  "Added objects rlCopyMessagesTable and rlCopyMessagesTableRemoveEntries."
          REVISION "200309220000Z"
          DESCRIPTION
                  "Initial version of this MIB."
          ::= { switch001 87 }

RlCopyApplicationType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "Specifies management application"
    SYNTAX  INTEGER {
        mcli(1),
        cli(2),
        ewb(3),
        nms(4),
        initerm(5),
        serial(6)
}

RlCopyLocationType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "Specifies file location"
    SYNTAX  INTEGER {
        local(1),
        anotherUnit(2),
        tftp(3),
        xmodem(4),
        scp(5),
        serial(6),
        http(7),
        https(8),
        http-xml(9),
        https-xml(10)

}

RlCopyFileType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The File type"
    SYNTAX  INTEGER {
        other(1),
        runningConfig(2),
        startupConfig(3),
        backupConfig(4),
        runningMibConfig(5),
        startupMibConfig(6),
        backupMibConfig(7),
        image(8),
        boot(9),
        null(10),
        logging(11),
        mirrorConfig(12),
        usb(13),        
        findit-tech-support(14),
        language(15)        
}
RlCopySecSdAccessType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "Sec sd access type"
    SYNTAX  INTEGER {
        exclude(1),
        include-encrypted(2),
        include-decrypted(3),
        default(4)

}

rlCopyMibVersion OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the Copy support version. The current version is 1."
    ::= {rlCopy 1 }

rlCopyTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlCopyEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " The (conceptual) table listing only one entry at a time
          with parameters needed for configuration
          of the file copy action."
    ::= {rlCopy 2 }

rlCopyEntry  OBJECT-TYPE
    SYNTAX RlCopyEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry (conceptual row) in the CopyTable."
    INDEX {rlCopyIndex }
    ::= {rlCopyTable  1 }

RlCopyEntry ::= SEQUENCE {
    rlCopyIndex                         INTEGER,
    rlCopyApplicationId                 RlCopyApplicationType,
    rlCopySourceLocation                RlCopyLocationType,
    rlCopySourceIpAddress               IpAddress,
    rlCopySourceUnitNumber              INTEGER,
    rlCopySourceFileName                DisplayString,
    rlCopySourceFileType                RlCopyFileType,
    rlCopyDestinationLocation           RlCopyLocationType,
    rlCopyDestinationIpAddress          IpAddress,
    rlCopyDestinationUnitNumber         INTEGER,
    rlCopyDestinationFileName           DisplayString,
    rlCopyDestinationFileType           RlCopyFileType,
    rlCopyUpTime                        TimeTicks,
    rlCopyOperationState                INTEGER,
    rlCopyBytesTransferred              INTEGER,
    rlCopyInBackground                  INTEGER,
    rlCopyRowStatus                     RowStatus,
    rlCopyHistoryIndex                  INTEGER
}

rlCopyIndex OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The row index in the table."
    ::=  { rlCopyEntry 1 }

rlCopyApplicationId OBJECT-TYPE
    SYNTAX RlCopyApplicationType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The applicatione activated this comand"
    ::=  { rlCopyEntry 2}

rlCopySourceLocation OBJECT-TYPE
    SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Source File Location"
    ::=  { rlCopyEntry 3 }

rlCopySourceIpAddress OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Ip address of the source remote host"
    ::=  { rlCopyEntry  4 }

rlCopySourceUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the source unit"
    ::=  { rlCopyEntry  5 }

rlCopySourceFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the source file."
    ::=  { rlCopyEntry 6 }

rlCopySourceFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the source file."
    ::=  { rlCopyEntry 7 }

rlCopyDestinationLocation OBJECT-TYPE
   SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Destination File Location"
    ::=  { rlCopyEntry 8 }

rlCopyDestinationIpAddress OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Ip address of the destination remote host"
    ::=  { rlCopyEntry  9 }

rlCopyDestinationUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the destination unit,
         value 257 means all units"
    ::=  { rlCopyEntry  10 }

rlCopyDestinationFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the destination file."
    ::=  { rlCopyEntry 11 }

rlCopyDestinationFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the destination file."
    ::=  { rlCopyEntry 12 }

rlCopyUpTime OBJECT-TYPE
    SYNTAX TimeTicks
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The time elapsed since this entry was created."
    ::= { rlCopyEntry 13 }

rlCopyOperationState OBJECT-TYPE
    SYNTAX INTEGER {
        uploadInProgress(1),
        downloadInProgress(2),
        copyFailed(3),
        copyTimedout(4),
        copyFinished(5)
    }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The state of the copy operation."
    ::=  { rlCopyEntry 14 }

rlCopyBytesTransferred OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of bytes that were transferred by the copy operation."
    ::=  { rlCopyEntry 15 }

rlCopyInBackground OBJECT-TYPE
    SYNTAX INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled the copy operation is done in the background."
    DEFVAL { disable }
    ::=  { rlCopyEntry 16 }

rlCopyRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "It is used to insert or delete an entry"
    ::= { rlCopyEntry 17 }

-- Robi 9/5/2003
rlCopyHistoryIndex OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "The index of the entry corresponding to this one in the
         rlCopyHistoryTable (equals rlCopyHistoryHistoryIndex).
         A value of 0 indicates that no history entry should be kept
         for this copy operation."
    DEFVAL { 0 }
    ::= { rlCopyEntry 18 }


rlCopyFreeHistoryIndex OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Gives the next free index in the rlCopyHistoryTable (i.e., the next
        available value for rlCopyHistoryHistoryIndex)"
    ::= { rlCopy 3 }


rlCopyHistoryTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlCopyHistoryEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " The (conceptual) table listing only one entry at a time
          with parameters needed for configuration
          of the file copy action."
    ::= {rlCopy 4 }

rlCopyHistoryEntry  OBJECT-TYPE
    SYNTAX RlCopyHistoryEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry (conceptual row) in the rlCopyHistoryTable."
    INDEX {rlCopyHistoryHistoryIndex }
    ::= {rlCopyHistoryTable  1 }

RlCopyHistoryEntry ::= SEQUENCE {
    rlCopyHistoryHistoryIndex                  INTEGER,
    rlCopyHistoryApplicationId                 RlCopyApplicationType,
    rlCopyHistorySourceLocation                RlCopyLocationType,
    rlCopyHistorySourceIpAddress               IpAddress,
    rlCopyHistorySourceUnitNumber              INTEGER,
    rlCopyHistorySourceFileName                DisplayString,
    rlCopyHistorySourceFileType                RlCopyFileType,
    rlCopyHistoryDestinationLocation           RlCopyLocationType,
    rlCopyHistoryDestinationIpAddress          IpAddress,
    rlCopyHistoryDestinationUnitNumber         INTEGER,
    rlCopyHistoryDestinationFileName           DisplayString,
    rlCopyHistoryDestinationFileType           RlCopyFileType,
    rlCopyHistoryUpTime                        TimeTicks,
    rlCopyHistoryOperationState                INTEGER,
    rlCopyHistoryBytesTransferred              INTEGER,
    rlCopyHistoryInBackground                  INTEGER,
    rlCopyHistoryRowStatus                     RowStatus,
    rlCopyHistoryErrorMessage                  DisplayString
}

rlCopyHistoryHistoryIndex OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The row index in the table."
    ::=  { rlCopyHistoryEntry 1 }

rlCopyHistoryApplicationId OBJECT-TYPE
    SYNTAX RlCopyApplicationType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The application that activated this comand"
    ::=  { rlCopyHistoryEntry 2}

rlCopyHistorySourceLocation OBJECT-TYPE
    SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Source File Location"
    ::=  { rlCopyHistoryEntry 3 }

rlCopyHistorySourceIpAddress OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IP address of the source remote host"
    ::=  { rlCopyHistoryEntry  4 }

rlCopyHistorySourceUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the source unit"
    ::=  { rlCopyHistoryEntry  5 }

rlCopyHistorySourceFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the source file."
    ::=  { rlCopyHistoryEntry 6 }

rlCopyHistorySourceFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the source file."
    ::=  { rlCopyHistoryEntry 7 }

rlCopyHistoryDestinationLocation OBJECT-TYPE
   SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Destination File Location"
    ::=  { rlCopyHistoryEntry 8 }

rlCopyHistoryDestinationIpAddress OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IP address of the destination remote host"
    ::=  { rlCopyHistoryEntry  9 }

rlCopyHistoryDestinationUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the destination unit"
    ::=  { rlCopyHistoryEntry  10 }

rlCopyHistoryDestinationFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the destination file."
    ::=  { rlCopyHistoryEntry 11 }

rlCopyHistoryDestinationFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the destination file."
    ::=  { rlCopyHistoryEntry 12 }

rlCopyHistoryUpTime OBJECT-TYPE
    SYNTAX TimeTicks
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The time elapsed since this entry was created."
    ::= { rlCopyHistoryEntry 13 }

rlCopyHistoryOperationState OBJECT-TYPE
    SYNTAX INTEGER {
        uploadInProgress(1),
        downloadInProgress(2),
        copyFailed(3),
        copyTimedout(4),
        copyFinished(5)
    }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The state of the copy operation."
    ::=  { rlCopyHistoryEntry 14 }

rlCopyHistoryBytesTransferred OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of bytes that were transferred by the copy operation."
    ::=  { rlCopyHistoryEntry 15 }

rlCopyHistoryInBackground OBJECT-TYPE
    SYNTAX INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled the copy operation is done in the background."
    DEFVAL { disable }
    ::=  { rlCopyHistoryEntry 16 }

rlCopyHistoryRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "It is used delete an entry. No other operations are permitted since
         the history table is populated and updated through the copy table only."
    ::= { rlCopyHistoryEntry 17 }

rlCopyHistoryErrorMessage OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Error message as a result of failed copy action."
    ::=  { rlCopyHistoryEntry 18 }


---         File Copy auditing control (SysLog)

rlCopyAuditingEnable  OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Controls whether SysLog messages
         should be issued on file copy events"
    DEFVAL { true }
    ::= { rlCopy 5 }

rlCopyMessagesTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlCopyMessagesEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " The table listing the messages issued during a COPY operation."
    ::= {rlCopy 6 }

rlCopyMessagesEntry  OBJECT-TYPE
    SYNTAX RlCopyMessagesEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry (conceptual row) in the rlCopyMessagesTable."
    INDEX {rlCopyMessagesCopyIndex, rlCopyMessagesMessageIndex }
    ::= {rlCopyMessagesTable  1 }

RlCopyMessagesEntry ::= SEQUENCE {
    rlCopyMessagesCopyIndex      INTEGER,
    rlCopyMessagesMessageIndex   INTEGER,
    rlCopyMessagesMessageText    DisplayString,
    rlCopyMessagesStatus         RowStatus
}

rlCopyMessagesCopyIndex OBJECT-TYPE
    SYNTAX INTEGER(1..2147483647)
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The index of the copy operation whose messages are displayed."
    ::=  { rlCopyMessagesEntry 1 }

rlCopyMessagesMessageIndex OBJECT-TYPE
    SYNTAX INTEGER(1..2147483647)
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The index of the displayed message."
    ::=  { rlCopyMessagesEntry 2 }

rlCopyMessagesMessageText OBJECT-TYPE
    SYNTAX DisplayString(SIZE(0..80))
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The text of the message occurred in the copy operation."
    ::=  { rlCopyMessagesEntry 3 }

rlCopyMessagesStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Only destroy is supported."
    ::=  { rlCopyMessagesEntry 4 }


rlCopyMessagesTableRemoveEntries  OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Setting this object with value n will remove all entries associated
         with copy operation represented by the value n from the rlCopyMessagesTable,
         i.e. all the entries with rlCopyMessagesCopyIndex = n."
    ::= { rlCopy 7 }

--
-- Copy Notifications
--

rlCopyFinished NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that the device has finished
         a copy operation successfully."
    ::= { rndNotifications 180 }

rlCopyFailed NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "Informational trap indicating that the copy operation has failed."
    ::= { rndNotifications 181 }

rlCopySWFinished NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "A trap indicating that the device has finished a copy operation of
         image successfully."
    ::= { rndNotifications 211 }

rlCopySWToUnits NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity, rlCopyUnitsList }
    STATUS  current
    DESCRIPTION
        "A trap indicating to controller unit that remote units have finished
         a copy operation of image successfully."
    ::= { rndNotifications 212 }

rlCopyMirrorFileIllegal NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "A trap indicating that the mirror configuration file is illegal
         due to failure of prevoius copy operation/s to mirror-config."
    ::= { rndNotifications 220 }

-- AlexeyK 30/11/06 IPv6 support

rlCopyInetTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlCopyInetEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " The (conceptual) table listing only one entry at a time
          with parameters needed for configuration
          of the file copy action."
    ::= {rlCopy 8 }

rlCopyInetEntry  OBJECT-TYPE
    SYNTAX RlCopyInetEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry (conceptual row) in the CopyTable."
    INDEX {rlCopyInetIndex }
    ::= {rlCopyInetTable  1 }

RlCopyInetEntry ::= SEQUENCE {
    rlCopyInetIndex                         INTEGER,
    rlCopyInetApplicationId                 RlCopyApplicationType,
    rlCopyInetSourceLocation                RlCopyLocationType,
    rlCopyInetSourceIpAddressType           InetAddressType,
    rlCopyInetSourceIpAddress               InetAddress,
    rlCopyInetSourceUnitNumber              INTEGER,
    rlCopyInetSourceFileName                DisplayString,
    rlCopyInetSourceFileType                RlCopyFileType,
    rlCopyInetDestinationLocation           RlCopyLocationType,
    rlCopyInetDestinationIpAddressType      InetAddressType,
    rlCopyInetDestinationIpAddress          InetAddress,
    rlCopyInetDestinationUnitNumber         INTEGER,
    rlCopyInetDestinationFileName           DisplayString,
    rlCopyInetDestinationFileType           RlCopyFileType,
    rlCopyInetUpTime                        TimeTicks,
    rlCopyInetOperationState                INTEGER,
    rlCopyInetBytesTransferred              INTEGER,
    rlCopyInetInBackground                  INTEGER,
    rlCopyInetRowStatus                     RowStatus,
    rlCopyInetHistoryIndex                  INTEGER,
    rlCopyInetDestinationUnitList           INTEGER,
    rlCopyInetUnitStatusList                INTEGER,
    rlCopyInetSpecificCopyInfo              DisplayString
}

rlCopyInetIndex OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The row index in the table."
    ::=  { rlCopyInetEntry 1 }

rlCopyInetApplicationId OBJECT-TYPE
    SYNTAX RlCopyApplicationType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The applicatione activated this comand"
    ::=  { rlCopyInetEntry 2}

rlCopyInetSourceLocation OBJECT-TYPE
    SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Source File Location"
    ::=  { rlCopyInetEntry 3 }

rlCopyInetSourceIpAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address type of the source remote host"
    ::=  { rlCopyInetEntry  4 }

rlCopyInetSourceIpAddress OBJECT-TYPE
    SYNTAX InetAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address of the source remote host"
    ::=  { rlCopyInetEntry  5 }


rlCopyInetSourceUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the source unit"
    ::=  { rlCopyInetEntry  6 }

rlCopyInetSourceFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the source file."
    ::=  { rlCopyInetEntry 7 }

rlCopyInetSourceFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the source file."
    ::=  { rlCopyInetEntry 8 }

rlCopyInetDestinationLocation OBJECT-TYPE
   SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Destination File Location"
    ::=  { rlCopyInetEntry 9 }

rlCopyInetDestinationIpAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address type of the destination remote host"
    ::=  { rlCopyInetEntry  10 }

rlCopyInetDestinationIpAddress OBJECT-TYPE
    SYNTAX InetAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address of the destination remote host"
    ::=  { rlCopyInetEntry  11 }


rlCopyInetDestinationUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the destination unit,
         value 257 means all units"
    ::=  { rlCopyInetEntry  12 }

rlCopyInetDestinationFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the destination file."
    ::=  { rlCopyInetEntry 13 }

rlCopyInetDestinationFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the destination file."
    ::=  { rlCopyInetEntry 14 }

rlCopyInetUpTime OBJECT-TYPE
    SYNTAX TimeTicks
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The time elapsed since this entry was created."
    ::= { rlCopyInetEntry 15 }

rlCopyInetOperationState OBJECT-TYPE
    SYNTAX INTEGER {
        uploadInProgress(1),
        downloadInProgress(2),
        copyFailed(3),
        copyTimedout(4),
        copyFinished(5)
    }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The state of the copy operation."
    ::=  { rlCopyInetEntry 16 }

rlCopyInetBytesTransferred OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of bytes that were transferred by the copy operation."
    ::=  { rlCopyInetEntry 17 }

rlCopyInetInBackground OBJECT-TYPE
    SYNTAX INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled the copy operation is done in the background."
    DEFVAL { disable }
    ::=  { rlCopyInetEntry 18 }

rlCopyInetRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "It is used to insert or delete an entry"
    ::= { rlCopyInetEntry 19 }

rlCopyInetHistoryIndex OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "The index of the entry corresponding to this one in the
         rlCopyHistoryTable (equals rlCopyHistoryHistoryIndex).
         A value of 0 indicates that no history entry should be kept
         for this copy operation."
    DEFVAL { 0 }
    ::= { rlCopyInetEntry 20 }

-- Liran 17-Feb-2010
rlCopyInetDestinationUnitList OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "A bitmap of destination units for copy operation."
    DEFVAL { 0 }
    ::= { rlCopyInetEntry 21 }

rlCopyInetUnitStatusList OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "A bitmap of copy operation status for each unit."
    ::= { rlCopyInetEntry 22 }

rlCopyInetSpecificCopyInfo OBJECT-TYPE
    SYNTAX  DisplayString
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "Copy application specific information. For example: for SCP copy this
         field can be used to pass username and password information."
     DEFVAL { "" }
    ::= { rlCopyInetEntry 23 }


-- AlexeyK 30/11/06 IPv6

rlCopyHistoryInetTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlCopyHistoryInetEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " The (conceptual) table listing only one entry at a time
          with parameters needed for configuration
          of the file copy action."
    ::= {rlCopy 9 }

rlCopyHistoryInetEntry  OBJECT-TYPE
    SYNTAX RlCopyHistoryInetEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry (conceptual row) in the rlCopyHistoryTable."
    INDEX {rlCopyHistoryInetHistoryIndex }
    ::= {rlCopyHistoryInetTable  1 }

RlCopyHistoryInetEntry ::= SEQUENCE {
    rlCopyHistoryInetHistoryIndex                  INTEGER,
    rlCopyHistoryInetApplicationId                 RlCopyApplicationType,
    rlCopyHistoryInetSourceLocation                RlCopyLocationType,
    rlCopyHistoryInetSourceIpAddressType           InetAddressType,
    rlCopyHistoryInetSourceIpAddress               InetAddress,
    rlCopyHistoryInetSourceUnitNumber              INTEGER,
    rlCopyHistoryInetSourceFileName                DisplayString,
    rlCopyHistoryInetSourceFileType                RlCopyFileType,
    rlCopyHistoryInetDestinationLocation           RlCopyLocationType,
    rlCopyHistoryInetDestinationIpAddressType      InetAddressType,
    rlCopyHistoryInetDestinationIpAddress          InetAddress,
    rlCopyHistoryInetDestinationUnitNumber         INTEGER,
    rlCopyHistoryInetDestinationFileName           DisplayString,
    rlCopyHistoryInetDestinationFileType           RlCopyFileType,
    rlCopyHistoryInetUpTime                        TimeTicks,
    rlCopyHistoryInetOperationState                INTEGER,
    rlCopyHistoryInetBytesTransferred              INTEGER,
    rlCopyHistoryInetInBackground                  INTEGER,
    rlCopyHistoryInetRowStatus                     RowStatus,
    rlCopyHistoryInetErrorMessage                  DisplayString,
    rlCopyHistoryInetDestinationUnitList           INTEGER,
    rlCopyHistoryInetUnitStatusList                INTEGER,
    rlCopyHistoryInetTotalFileSize		   INTEGER
}

rlCopyHistoryInetHistoryIndex OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The row index in the table."
    ::=  { rlCopyHistoryInetEntry 1 }

rlCopyHistoryInetApplicationId OBJECT-TYPE
    SYNTAX RlCopyApplicationType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The application that activated this comand"
    ::=  { rlCopyHistoryInetEntry 2}

rlCopyHistoryInetSourceLocation OBJECT-TYPE
    SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Source File Location"
    ::=  { rlCopyHistoryInetEntry 3 }

rlCopyHistoryInetSourceIpAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address type of the source remote host"
    ::=  { rlCopyHistoryInetEntry  4 }

rlCopyHistoryInetSourceIpAddress OBJECT-TYPE
    SYNTAX InetAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address of the source remote host"
    ::=  { rlCopyHistoryInetEntry  5 }


rlCopyHistoryInetSourceUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the source unit"
    ::=  { rlCopyHistoryInetEntry  6 }

rlCopyHistoryInetSourceFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the source file."
    ::=  { rlCopyHistoryInetEntry 7 }

rlCopyHistoryInetSourceFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the source file."
    ::=  { rlCopyHistoryInetEntry 8 }

rlCopyHistoryInetDestinationLocation OBJECT-TYPE
   SYNTAX RlCopyLocationType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Destination File Location"
    ::=  { rlCopyHistoryInetEntry 9 }

rlCopyHistoryInetDestinationIpAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address type of the destination remote host"
    ::=  { rlCopyHistoryInetEntry  10 }

rlCopyHistoryInetDestinationIpAddress OBJECT-TYPE
    SYNTAX InetAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address of the destination remote host"
    ::=  { rlCopyHistoryInetEntry  11 }


rlCopyHistoryInetDestinationUnitNumber OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit number of the destination unit"
    ::=  { rlCopyHistoryInetEntry  12 }

rlCopyHistoryInetDestinationFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the destination file."
    ::=  { rlCopyHistoryInetEntry 13 }

rlCopyHistoryInetDestinationFileType OBJECT-TYPE
    SYNTAX RlCopyFileType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The type of the destination file."
    ::=  { rlCopyHistoryInetEntry 14 }

rlCopyHistoryInetUpTime OBJECT-TYPE
    SYNTAX TimeTicks
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The time elapsed since this entry was created."
    ::= { rlCopyHistoryInetEntry 15 }

rlCopyHistoryInetOperationState OBJECT-TYPE
    SYNTAX INTEGER {
        uploadInProgress(1),
        downloadInProgress(2),
        copyFailed(3),
        copyTimedout(4),
        copyFinished(5)
    }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The state of the copy operation."
    ::=  { rlCopyHistoryInetEntry 16 }

rlCopyHistoryInetBytesTransferred OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of bytes that were transferred by the copy operation."
    ::=  { rlCopyHistoryInetEntry 17 }

rlCopyHistoryInetInBackground OBJECT-TYPE
    SYNTAX INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled the copy operation is done in the background."
    DEFVAL { disable }
    ::=  { rlCopyHistoryInetEntry 18 }

rlCopyHistoryInetRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "It is used delete an entry. No other operations are permitted since
         the history table is populated and updated through the copy table only."
    ::= { rlCopyHistoryInetEntry 19 }

rlCopyHistoryInetErrorMessage OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Error message as a result of failed copy action."
    ::=  { rlCopyHistoryInetEntry 20 }

-- Liran 17-Feb-2010
rlCopyHistoryInetDestinationUnitList OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "A bitmap of destination units for copy operation."
    DEFVAL { 0 }
    ::= { rlCopyHistoryInetEntry 21 }

rlCopyHistoryInetUnitStatusList OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "A bitmap of copy operation status for each unit."
    ::= { rlCopyHistoryInetEntry 22 }

rlCopyHistoryInetTotalFileSize  OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total length of image/static file"
    ::= {  rlCopyHistoryInetEntry 23 }

rlCopyUnitsList OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
        "Potential list of units used by trap rlCopySWToUnits"
    ::= {rlCopy 10}

rlCopyMirrorTimeout  OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        " This MIB should be used in order to change the time-interval of automatic copy of
          startup-config to mirror-config.
          The MIB is for debugging purposes only, and given value should be the
          number of minutes for time-interval of this copy."
    ::= { rlCopy 11 }

rlCopyOptionsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlCopyOptionsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The table includes more field options for the copy operation."
    ::= {rlCopy 12 }

rlCopyOptionsEntry OBJECT-TYPE
    SYNTAX RlCopyOptionsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry (conceptual row) in the rlCopyOptionsTable."
    INDEX {rlCopyOptionsIndex }
    ::= { rlCopyOptionsTable  1 }

RlCopyOptionsEntry ::= SEQUENCE {
    rlCopyOptionsIndex               		Unsigned32,
    rlCopyOptionsRequestedSsdAccess  		RlCopySecSdAccessType,
    rlCopyOptionsCheckFilePermission 		TruthValue,
    rlCopyOptionsCheckSystemReservedStorage 	TruthValue
}

rlCopyOptionsIndex OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "The index should be equal to the value of rlCopyInetIndex of the relevant rlCopyInetEntry."
    ::= { rlCopyOptionsEntry 1 }

rlCopyOptionsRequestedSsdAccess  OBJECT-TYPE
    SYNTAX  RlCopySecSdAccessType
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "The requested format for sensitive data in the copy. Relevant only for configuration file upload"
    DEFVAL { default }
    ::= { rlCopyOptionsEntry 2 }

rlCopyOptionsCheckFilePermission  OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "Controls whether to check file permissions during copy."
    DEFVAL { true }
    ::= { rlCopyOptionsEntry 3 }
	
rlCopyOptionsCheckSystemReservedStorage  OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "Controls whether to check system reserved storage during copy."
    DEFVAL { true }
    ::= { rlCopyOptionsEntry 4 }
	
rlCopyMirror OBJECT IDENTIFIER ::= { rlCopy 13 }

rlCopyMirrorEnable  OBJECT-TYPE
    SYNTAX INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        " This MIB should be used in order to enable/disable automatic copy of
          startup-config to mirror-config."
    ::= { rlCopyMirror 1 }

        ---         File Copy downgrade status

rlCopyStaticDowngradeStatus  OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Controls whether static downgrade in progress "
    DEFVAL { false }
    ::= { rlCopy 14 }


	


END


