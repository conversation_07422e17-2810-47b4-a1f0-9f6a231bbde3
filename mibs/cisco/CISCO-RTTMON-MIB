-- $Id: CISCO-RTTMON-MIB.my,v ******* 1996/09/04 17:34:50 lmetzger Exp $
-- $Source: /release/112/cvs/Xsys/MIBS/CISCO-RTTMON-MIB.my,v $
-- *****************************************************************
-- Response Time Monitor Mib.
--
-- March 1996, <PERSON>
--
-- Copyright (c) 1995-2012, 2014-2016 by Cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************
-- $Log: CISCO-RTTMON-MIB.my,v $
-- %DNP% Revision *******  1996/09/04  17:34:50  lmetzger
-- %DNP% CSCdi68018:  Second Update to Description in RTTMON mib
-- %DNP% Branch: California_branch
-- %DNP%
-- %DNP% Revision *******  1996/08/16  18:38:07  lmetzger
-- %DNP% CSCdi66311:  Update Descriptions in RTTMON mib
-- %DNP% Branch: California_branch
-- %DNP%
-- %DNP% Revision *******  1996/06/11  19:38:37  snyder
-- %DNP% CSCdi60118:  MIB doc spellink errors
-- %DNP% Branch: California_branch
-- %DNP%
-- %DNP% Revision *******  1996/05/17  10:38:45  ppearce
-- %DNP% Merge IbuMod into Calif
-- %DNP%
-- %DNP% Revision *******  1996/03/22  19:10:10  lmetzger
-- %DNP% Initial Version of Response Time Reporter
-- %DNP%
-- %DNP% Revision 3.1  1996/03/20  01:05:49  lmetzger
-- %DNP% Placeholder for IbuMod_Calif_branch
-- %DNP%
-- *****************************************************************
-- $Endlog$
--

-- %DNP%  FYI:  Lines containing a comment starting with the "Do Not
-- %DNP%  Publish" prefix "%DNP%" (such as these) are automagicially
-- %DNP%  stripped from MIBS prior to publishing on ftp and cio, and
-- %DNP%  should be used for comments intended for cisco engineering
-- %DNP%  eyes only, or for new product identifiers that are not yet
-- %DNP%  announce

CISCO-RTTMON-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    NOTIFICATION-TYPE,
    Integer32,
    Unsigned32,
    Counter32,
    Gauge32,
    TimeTicks
        FROM SNMPv2-SMI
    NOTIFICATION-GROUP,
    MODULE-COMPLIANCE,
    OBJECT-GROUP
        FROM SNMPv2-CONF
    StorageType,
    DisplayString,
    TruthValue,
    RowStatus,
    TimeInterval,
    TimeStamp,
    MacAddress
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    InetAddress,
    InetAddressType
        FROM INET-ADDRESS-MIB
    VlanId
        FROM Q-BRIDGE-MIB
    Dscp
        FROM DIFFSERV-DSCP-TC
    InterfaceIndexOrZero
        FROM IF-MIB
    QosLayer2Cos
        FROM CISCO-QOS-PIB-MIB
    CfmMepid
        FROM CISCO-ETHER-CFM-MIB
    RttReset,
    RttMonOperation,
    RttResponseSense,
    RttMonRttType,
    RttMplsVpnMonRttType,
    RttMplsVpnMonLpdFailureSense,
    RttMplsVpnMonLpdGrpStatus,
    RttMonProtocol,
    RttMonCodecType,
    RttMonLSPPingReplyMode,
    RttMonTargetAddress,
    RttMonReactVar,
    RttMonScheduleStartType
        FROM CISCO-RTTMON-TC-MIB
    ciscoMgmt
        FROM CISCO-SMI;


ciscoRttMonMIB MODULE-IDENTITY
    LAST-UPDATED    "201604140000Z"
    ORGANIZATION    "Cisco IOS"
    CONTACT-INFO
            "Cisco Systems, Inc.
            Customer Service

            Postal: 170 W Tasman Drive
            San Jose, CA 95134

            Tel: ****** 553 NETS

            <EMAIL>"
    DESCRIPTION
        "This module defines a MIB for Round Trip Time
        (RTT) monitoring of a list of targets, using a
        variety of protocols.

        The table structure overview is a follows (t:
         indicates a table, at:  indicates an augmented
         table, and it:  indicates table with the same
         indices/control as parent table):

        RTTMON MIB
        |--- Application Group
        |    |--- Application Identity
        |    |--- Application Capabilities
        |    |--- Application Reset
        |    |t-- Supported RTT Types
        |         |--- Truth Value
        |    |t-- Supported Protocols
        |         |--- Truth Value
        |    |t-- Application Preconfigured
        |         |--- Script Names
        |         |--- File Paths
        |    |--- Responder control
        |    |t-- Control Protocol Authentication
        |
        |--- Overall Control Group
        |    |t-- Master Definitions Table
        |    |    |--- Global Configuration Definitions
        |    |         |--- Config for a single RTT Life
        |    |    |it- Echo Specific Configuration
        |    |    |it- Echo Path Hop Address Configuration
        |    |    |it- File I/O Specific Configuration
        |    |    |it- Script Specific Configuration
        |    |    |at- Schedule Configuration
        |    |    |at- Reaction Specific Config
        |    |    |at- Statistics Capture Configuration
        |    |    |at- History Collection Configuration
        |    |    |at- Monitoring Operational State
        |    |    |at- Last RTT operation
        |    |
        |    |t-- Reaction Trigger Table
        |         |at- Reaction Trigger Operational State
        |
        |--- Statistics Collection Group
        |    |t-- Statistics Capture Table
        |         |--- Captured Statistics
        |              |--- Path Information
        |              |--- Distribution Capture
        |              |--- Mean and Deviation Capture
        |         |it- Statistics Collection Table
        |    |it- Statistics Totals Table
        |    |t-- HTTP Stats Table
        |    |t-- Jitter Stats Table
        |
        |--- History Collection Group
        |    |t-- History Collection Table
        |         |-- Path Information
        |         |-- Completion Information per operation
        |
        |--- Latest Operation Group
        |    |t-- Latest HTTP Oper Table
        |    |t-- Latest Jitter Oper Table

        DEFINITIONS:
          conceptual RTT control row -
                  This is a row in the 'Overall Control
                  Group'.  This row is indexed via the
                  rttMonCtrlAdminIndex object.  This row
                  is spread across multiple real tables
                  in the 'Overall Control Group'.
          probe -
                  This is the entity that executes via a
                  conceptual RTT control row and populates
                  a conceptual statistics row and a
                  conceptual history row.
          Rtt operation -
                  This is a single operation performed by
                  a probe.  This operation can be a single
                  Rtt attempt/completion or a group of Rtt
                  attempts/completions that produce one
                  operation table entry.

        ARR Protocol Definition:

        The format of the RTT Asymmetric Request/Responses
         (ARR) protocol is as follows:

          The ARR Header (total of 12 octets):

          4 octet -> eyecatcher: 'WxYz'
          1 octet -> version   : 0x01 - protocol version
          1 octet -> command   : 0x01 - logoff request
                                 0x02 - echo request
                                 0x03 - echo response
                                 0x04 - software version request
                                 0x05 - software version response
          2 octet -> sequence number (Network Byte Order)
          4 octet -> response data size (Network Byte Order)

          The ARR Data:

          n octets -> request/response data
                                : 'AB..ZAB..ZAB..'

          For software version request/response the
           protocol version octet will contain the version
           number of the responder.  Thus the sequence
           number, etc will not be included.

          For snaLU0EchoAppl and snaLU2EchoAppl all character
           fields will be in EBCDIC.

          The response data should be appended to the
           origin request data.  This allows data
           verification to check the data that flows in
           both directions.  If the response data size is
           smaller than the request data size the original
           request data will be truncated.

          An example would be:
            Request:        /       Response:
            'WxYz'          /       'WxYz'
            0x01            /       0x01
            0x02            /       0x03
            0x0001          /       0x0001
            0x00000008      /       0x00000008
            'ABCDEF'        /       'ABCDEFGH'

          NOTE: We requested 8 bytes in the response and
                the response had 8 bytes.  The size of the
                request data has no correlation to the
                size of the response data.

        NOTE:  For native RTT request/response (i.e.
               ipIcmpecho) operations both the 'Header'
               and 'Data' will be included.  Only the
               'sequence number' in the Header will be
               valid.

        NOTE:  For non-connection oriented protocol the
               initial RTT request/response operation will
               be preceded with an RTT request/response
               operation to the target address to force
               path exploration and to prove
               connectivity.  The History collection table
               will contain these responses, but the
               Statistics capture table will omit them to
               prevent skewed results."
    REVISION        "201604140000Z"
    DESCRIPTION
        "Refined rttMonCtrlAdminFrequency range in the new compliance
        statement from Integer32 (0..604800) to (1..604800). This
        change was advertised in revision 200405180000Z but was not
        implemented.
        Refined range for the following port objects to exclude value 0
            rttMonEchoAdminTargetPort,
            rttMonEchoAdminSourcePort,
            rttMonEchoAdminEmulateSourcePort,
            rttMonEchoAdminEmulateTargetPort
        Added following objects to support random start-time feature
        for scheduling group and sla probes.
            rttMonScheduleAdminStartType,
            rttMonScheduleAdminStartDelay,
            rttMonGrpScheduleAdminStartType,
            rttMonGrpScheduleAdminStartDelay
        Added following objects to report over threshold statistics
        for jitter probes
            rttMonLatestJitterOperNumOverThresh,
            rttMonJitterStatsNumOverThresh
        Deprecated rttMonCtrlAdminTag and replaced with
        rttMonCtrlAdminLongTag
        Added new compliance
            ciscoRttMonMibComplianceRev23."
    REVISION        "201404100000Z"
    DESCRIPTION
        "Added following objects for Fabric Path Echo
        probe
            rttMonEchoAdminTargetSwitchId,
            rttMonEchoAdminProfileId,
            rttMonEchoAdminOutputInterface
        Added new compliance
            ciscoRttMonMibComplianceRev22."
    REVISION        "201208160000Z"
    DESCRIPTION
        "Added following objects to support Y1731 Synthetic Loss
        Measurement
            rttMonEchoAdminEnableBurst,
            rttMonEchoAdminAggBurstCycles,
            rttMonEchoAdminLossRatioNumFrames,
            rttMonEchoAdminAvailNumFrames
        Added following object to support improving accuracy for jitter
        probes measurement.
            rttMonEchoAdminTstampOptimization
        Deprecated rttMonScheduleAdminConceptRowAgeout and replace
        with  rttMonScheduleAdminConceptRowAgeoutV2.
        Deprecated rttMonControlEnableErrors, rttMonStatsRetrieveErrors
        and replace with rttMonStatsCollectCtrlEnErrors,
        rttMonStatsCollectRetrieveErrors.
        Modified the descriptions for
            rttMonLatestJitterOperMOS
            rttMonLatestJitterOperICPIF,
            rttMonJitterStatsMinOfMOS
            rttMonJitterStatsMinOfICPIF,
            rttMonJitterStatsMaxOfMOS
            rttMonJitterStatsMaxOfICPIF
        Added new compliance
            ciscoRttMonMibComplianceRev21."
    REVISION        "201109150000Z"
    DESCRIPTION
        "Added following objects to rttMonEchoAdmin to support
        Y1731 extensions.
        - rttMonEchoAdminTargetMacAddress,
        - rttMonEchoAdminSourceMacAddress and
        - rttMonEchoAdminSourceMPID"
    REVISION        "201102210000Z"
    DESCRIPTION
        "Added four new objects rttMonEchoAdminEmulateSourceAddress,
        rttMonEchoAdminEmulateSourcePort,
        rttMonEchoAdminEmulateTargetAddress,
        rttMonEchoAdminEmulateTargetPort to RttMonEchoAdminEntry."
    REVISION        "201010180000Z"
    DESCRIPTION
        "Added three new objects rttMonEchoAdminDscp,
        rttMonEchoAdminReserveDsp, rttMonEchoAdminInputInterface"
    REVISION        "201006040000Z"
    DESCRIPTION
        "Changes added for video operation:
        -Added new MIB object rttMonEchoAdminVideoTrafficProfile
        -Changed the maximum range of rttMonEchoAdminCallDuration to 600
        -Changed the minimum range of rttMonEchoAdminCallDuration to 1
        from 10"
    REVISION        "200904070000Z"
    DESCRIPTION
        "- Added rttMonEchoAdminTargetMEPPort object in
        rttMonEchoAdminEntry."
    REVISION        "200803240000Z"
    DESCRIPTION
        "- Added nine new objects rttMonLatestJitterOperRTTSumHigh,
        rttMonLatestJitterOperRTTSum2High,
        rttMonLatestJitterOperOWSumSDHigh,
        rttMonLatestJitterOperOWSum2SDHigh,
        rttMonLatestJitterOperOWSumDSHigh,
        rttMonLatestJitterOperOWSum2DSHigh,
        rttMonJitterStatsRTTSumHigh, rttMonJitterStatsOWSumSDHigh,
        rttMonJitterStatsOWSumDSHigh.
        - Modified the unit and the description of
        rttMonLatestRttOperCompletionTime,
        rttMonJitterStatsRTTSum, rttMonJitterStatsOWSumSD,
        rttMonJitterStatsOWSumDS, rttMonLatestJitterOperRTTSum,
        rttMonLatestJitterOperRTTSum2, rttMonLatestJitterOperOWSumSD,
        rttMonLatestJitterOperOWSumDS, rttMonLatestJitterOperOWSum2SD,
        rttMonLatestJitterOperOWSum2DS."
    REVISION        "200801060000Z"
    DESCRIPTION
        "Added a new object rttMonEchoAdminTargetEVC."
    REVISION        "200612080000Z"
    DESCRIPTION
        "Added a new object rttMonCtrlAdminGroupName to support the
        auto measure project. Group Name will be shown for auto
        generated operations."
    REVISION        "200606080000Z"
    DESCRIPTION
        "Added a new rttMonRttType named as lspPingPseudowire.
        Added an object rttMonEchoAdminLSPVccvID to support
        lspPingPseudowire.
        Change the default value of rttMplsVpnMonTypeLpdScanPeriod
        from 1 to 240."
    REVISION        "200603020000Z"
    DESCRIPTION
        "Added two new rttMonRttType's ethernetPing and ethernetJitter.
        Added four objects rttMonEchoAdminTargetMPID,
        rttMonEchoAdminTargetVLAN, rttMonEchoAdminTargetDomainName
        and rttMonEchoAdminEthernetCOS in rttMonEchoAdminEntry to
        support ethernetPing and ethernetJitter.
        Change the default value of rttMplsVpnMonTypeLpdScanPeriod
        from 0 to 1.
        Modified the range of rttMonApplProbeCapacity and
        rttMonApplNumCtrlAdminEntry to 1 .. 2147483647.
        Modified the range of rttMonLatestJitterOperMOS,
        rttMonJitterStatsMinOfMOS and rttMonJitterStatsMaxOfMOS to
        (0|100 .. 500).
        Modified the SYNTAX of rttMonCtrlAdminOwner to OCTET STRING
        because OwnerString is deprecated."
    REVISION        "200508110000Z"
    DESCRIPTION
        "- TEXTUAL Conventions previously defined in the MIB are defined
        in CISCO-RTTMON-TC-MIB."
    REVISION        "200504210000Z"
    DESCRIPTION
        "- Added new objects given in ciscoCtrlGroupRev4 to
        rttMonGrpScheduleAdminTable.
        - Changed description of object rttMonHTTPStatsRTTMax."
    REVISION        "200501040000Z"
    DESCRIPTION
        "Added two new rttMonRttType's rtp and lspGroup. Added a new
        object rttMonApplLpdGrpStatsReset to reset the LPD Group Stats.
        Added rttMonLpdGrpStatsTable for supporting LSP Path Discovery.
        Added two new notifications rttMonLpdDiscoveryNotification
        and rttMonLpdGrpStatusNotification.
        Added and modified descriptions of some objects in
        rttMplsVpnMonCtrlTable, rttMplsVpnMonTypeTable and
        rttMplsVpnMonReactTable for LSP Path Discovery.
        Added 6 options in the rttMonReactVar.
        Added rttMonEchoAdminSourceVoicePort and
        rttMonEchoAdminCallDuration in rttMonEchoAdminTable."
    REVISION        "200408260000Z"
    DESCRIPTION
        "Added a table rttMonReactTable, which defines the
        the reaction configurations for the probes.
        Deprecated the old reaction table rttMonReactAdminTable.
        This is replaced by the new table (rttMonReactTable).
        Depreacted the notification types
          rttMonConnectionChangeNotification
          rttMonTimeoutNotification
          rttMonThresholdNotification
          rttMonVerifyErrorNotification
        Added new notification type rttMonNotification.
        Added two objects, rttMonGrpScheduleAdminFreqMax
        and rttMonGrpScheduleAdminFreqMin to table
        rttMonGrpScheduleAdminTable."
    REVISION        "200405180000Z"
    DESCRIPTION
        "- Add the following fields for VoIP GK registration delay:
        RttMonEchoAdminEntry:
         rttMonEchoAdminGKRegistration
        - Add the following fields for VoIP Post dial delay:
        RttMonOperation:
         voipDTAlertRinging(6),
         voipDTConnectOK(7)
        RttMonRttType:
         voip(13)
        RttMonProtocol:
         voipAppl(31)
        RttMonEchoAdminEntry:
         rttMonEchoAdminCalledNumber
         rttMonEchoAdminDetectPoint
        - Add HTTP code 301, 302 as non-error scenario.
        - Modify description for rttMonEchoAdminNameServer:
         it is applicable for DNS and HTTP probe.
        - Modify rttMonCtrlAdminFrequency range
         from Integer32 (0..604800) to (1..604800)
        - Added following new objects for jitter probe precision
        and other improvements:
        rttMonEchoAdminPrecision, rttMonEchoAdminProbePakPriority,
        rttMonJitterStatsIAJOut, rttMonJitterStatsIAJIn,
        rttMonJitterStatsAvgJitter, rttMonJitterStatsAvgJitterSD,
        rttMonJitterStatsAvgJitterDS, rttMonJitterStatsUnSyncRTs,
        rttMonLatestJitterOperIAJIn, rttMonLatestJitterOperAvgJitter,
        rttMonLatestJitterOperAvgSDJ, rttMonLatestJitterOperAvgDSJ,
        rttMonLatestJitterOperOWAvgSD, rttMonLatestJitterOperOWAvgDS,
        rttMonLatestJitterOperIAJOut, rttMonLatestJitterOperNTPState,
        rttMonEchoAdminOWNTPSyncTolAbs,
        rttMonEchoAdminOWNTPSyncTolPct,
        rttMonEchoAdminOWNTPSyncTolType,
        rttMonLatestJitterOperUNSyncRTs"
    REVISION        "200401200000Z"
    DESCRIPTION
        "Created new tables for Auto SAA L3 MPLS VPN.
        rttMplsVpnMonCtrlTable
        rttMplsVpnMonTypeTable
        rttMplsVpnMonScheduleTable
        rttMplsVpnMonReactTable.
        Modified MIB for creation of echo and pathecho operations based
        on MPLS LSP Ping."
    REVISION        "200308110000Z"
    DESCRIPTION
        "Added 1 object rttMonScheduleAdminRttRecurring to the
        rttMonScheduleAdminTable. Added a new table
        rttMonGrpScheduleAdminTable for group scheduling. This table
        contains the following objects
        rttMonGrpScheduleAdminIndex
        rttMonGrpScheduleAdminProbes
        rttMonGrpScheduleAdminPeriod
        rttMonGrpScheduleAdminFrequency
        rttMonGrpScheduleAdminLife
        rttMonGrpScheduleAdminAgeout
        rttMonGrpScheduleAdminStatus.
        Modified the default value of rttMonReactTriggerAdminStatus
        from createAndGo to no default value. Corrected the Revision
        clause specified for the existing and all the earlier
        submissions."
    REVISION        "200305210000Z"
    DESCRIPTION
        "Range for rttMonLatestJitterOperMOS, rttMonJitterStatsMinOfMOS
        and rttMonJitterStatsMaxOfMOS to be changed to 100..500
        instead of 1..5. Modifying the range of rttMonApplProbeCapacity
        and rttMonApplNumCtrlAdminEntry to 1..10000.Added value other(0)
        for RttResponseSense and changed the range of objects
        rttMonLatestRttOperApplSpecificSense and
        rttMonHistoryCollectionApplSpecificSense to 0..2147483647.
        Added range for rttMonApplAuthIndex."
    REVISION        "200304150000Z"
    DESCRIPTION
        "Removed default values from
        rttMonEchoAdminCodecInterval
        rttMonEchoAdminCodecPayload
        rttMonEchoAdminCodecNumPackets.
        Corrected some typos in the earliar revision."
    REVISION        "200303120000Z"
    DESCRIPTION
        "Added 5 objects in the rttMonEchoAdminTable to support
        codec configuration.
        Added few objects in rttMonLatestJitterOperTable and
        rttMonJitterStatsTable for ICPIF (Calculated Planning
        Impairment Factor) and MOS (Mean Opinion Score) scores."
    REVISION        "200011030000Z"
    DESCRIPTION
        "deprecated 4 objects in the rttMonJitterStatsTable
        and added the same objects with a SYNTAX of Gauge32.
        Also added the capability to specify a VrfName."
    REVISION        "9906150000Z"
    DESCRIPTION
        "created rttMonAuthTable."
    ::= { ciscoMgmt 42 }


-- Round Trip Time (RTT) Monitor MIB Objects

ciscoRttMonObjects  OBJECT IDENTIFIER
    ::= { ciscoRttMonMIB 1 }

-- Round Trip Time Monitoring Object Groups

rttMonAppl  OBJECT IDENTIFIER
    ::= { ciscoRttMonObjects 1 }

rttMonCtrl  OBJECT IDENTIFIER
    ::= { ciscoRttMonObjects 2 }

rttMonStats  OBJECT IDENTIFIER
    ::= { ciscoRttMonObjects 3 }

rttMonHistory  OBJECT IDENTIFIER
    ::= { ciscoRttMonObjects 4 }

rttMonLatestOper  OBJECT IDENTIFIER
    ::= { ciscoRttMonObjects 5 }


-- GLOBAL RTT MONITORING VARIABLES

rttMonApplVersion OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Round Trip Time monitoring application version
        string.

        The format will be:

        'Version.Release.Patch-Level: Textual-Description'

        For example:  '1.0.0: Initial RTT Application'"
    ::= { rttMonAppl 1 }

rttMonApplMaxPacketDataSize OBJECT-TYPE
    SYNTAX          Integer32 (0..16384)
    UNITS           "octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum size of the data portion an echo
        packet supported by this RTT application.  This is
        the maximum value that can be specified by
        (rttMonEchoAdminPktDataRequestSize + ARR Header)
        or
        (rttMonEchoAdminPktDataResponseSize + ARR Header)
        in the rttMonCtrlAdminTable.

        This object is undefined for conceptual RTT
        control rows when the RttMonRttType object is set
        to 'fileIO' or 'script'."
    ::= { rttMonAppl 2 }

rttMonApplTimeOfLastSet OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The last time at which a set operation occurred
        on any of the objects in this MIB.  The managing
        application can inspect this value in order to
        determine whether changes have been made without
        retrieving the entire Administration portion of
        this MIB.

        This object applies to all settable objects in this
        MIB, including the 'Reset' objects that could clear
        saved history/statistics."
    ::= { rttMonAppl 3 }

rttMonApplNumCtrlAdminEntry OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object defines the maximum number of entries
        that can be added to the rttMonCtrlAdminTable. It
        is calculated at the system init time. The value
        is impacted when rttMonApplFreeMemLowWaterMark is changed."
    ::= { rttMonAppl 4 }

rttMonApplReset OBJECT-TYPE
    SYNTAX          RttReset
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "When set to 'reset' the entire RTT application
        goes through a reset sequence, making a best
        effort to revert to its startup condition.  Any
        and all rows in the Overall Control Group will be
        immediately deleted, together with any associated
        rows in the Statistics Collection Group, and
        History Collection Group.  All open connections
        will also be closed.  Finally the
        rttMonApplPreConfigedTable will reset (see
        rttMonApplPreConfigedReset)."
    ::= { rttMonAppl 5 }

rttMonApplPreConfigedReset OBJECT-TYPE
    SYNTAX          RttReset
    MAX-ACCESS      read-write
    STATUS          obsolete
    DESCRIPTION
        "When set to 'reset' the RTT application will
        reset the Application Preconfigured MIB section.

        This will force the RTT application to delete all
        entries in the rttMonApplPreConfigedTable and then
        to repopulate the table with the current configuration.

        This provides a mechanism to load and unload user
        scripts and file paths."
    ::= { rttMonAppl 6 }
-- Supported RTT Types.

rttMonApplSupportedRttTypesTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonApplSupportedRttTypesEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of which contains the supported Rtt
        Monitor Types.

        See the RttMonRttType textual convention for
        the definition of each type."
    ::= { rttMonAppl 7 }

rttMonApplSupportedRttTypesEntry OBJECT-TYPE
    SYNTAX          RttMonApplSupportedRttTypesEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list that presents the valid Rtt Monitor
        Types."
    INDEX           { rttMonApplSupportedRttTypes }
    ::= { rttMonApplSupportedRttTypesTable 1 }

RttMonApplSupportedRttTypesEntry ::= SEQUENCE {
        rttMonApplSupportedRttTypes      RttMonRttType,
        rttMonApplSupportedRttTypesValid TruthValue
}

rttMonApplSupportedRttTypes OBJECT-TYPE
    SYNTAX          RttMonRttType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indexes the supported
        'RttMonRttType' types."
    ::= { rttMonApplSupportedRttTypesEntry 1 }

rttMonApplSupportedRttTypesValid OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object defines the supported
        'RttMonRttType' types."
    ::= { rttMonApplSupportedRttTypesEntry 2 }


-- Supported Protocols.

rttMonApplSupportedProtocolsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonApplSupportedProtocolsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of which contains the supported Rtt
        Monitor Protocols.

        See the RttMonProtocol textual convention
        for the definition of each protocol."
    ::= { rttMonAppl 8 }

rttMonApplSupportedProtocolsEntry OBJECT-TYPE
    SYNTAX          RttMonApplSupportedProtocolsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list that presents the valid Rtt Monitor
        Protocols."
    INDEX           { rttMonApplSupportedProtocols }
    ::= { rttMonApplSupportedProtocolsTable 1 }

RttMonApplSupportedProtocolsEntry ::= SEQUENCE {
        rttMonApplSupportedProtocols      RttMonProtocol,
        rttMonApplSupportedProtocolsValid TruthValue
}

rttMonApplSupportedProtocols OBJECT-TYPE
    SYNTAX          RttMonProtocol
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indexes the supported
        'RttMonProtocol' protocols."
    ::= { rttMonApplSupportedProtocolsEntry 1 }

rttMonApplSupportedProtocolsValid OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object defines the supported
        'RttMonProtocol' protocols."
    ::= { rttMonApplSupportedProtocolsEntry 2 }


-- Preconfigured Script Names and File IO targets.

rttMonApplPreConfigedTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonApplPreConfigedEntry
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "A table of which contains the previously
        configured Script Names and File IO targets.

        These Script Names and File IO targets are installed
        via a different mechanism than this application, and
        are specific to each platform."
    ::= { rttMonAppl 9 }

rttMonApplPreConfigedEntry OBJECT-TYPE
    SYNTAX          RttMonApplPreConfigedEntry
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "A list of objects that describe the previously
        configured Script Names and File IO targets."
    INDEX           {
                        rttMonApplPreConfigedType,
                        rttMonApplPreConfigedName
                    }
    ::= { rttMonApplPreConfigedTable 1 }

RttMonApplPreConfigedEntry ::= SEQUENCE {
        rttMonApplPreConfigedType  INTEGER,
        rttMonApplPreConfigedName  DisplayString,
        rttMonApplPreConfigedValid TruthValue
}

rttMonApplPreConfigedType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        filePath(1),
                        scriptName(2)
                    }
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "This is the type of value being stored in the
        rttMonApplPreConfigedName object."
    ::= { rttMonApplPreConfigedEntry 2 }

rttMonApplPreConfigedName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "This is either one of the following depending on the
        value of the rttMonApplPreConfigedType object:

         - The file path to a server.  One of these file paths
           must be used when defining an entry in the
           rttMonFileIOAdminTable table with 'fileIO' as the
           value of the rttMonCtrlAdminRttType object.

         - The script name to be used when generating RTT
           operations.  One of these script names must be used
           when defining an entry in the rttMonScriptAdminTable
           table with 'script' as the value of the
           rttMonCtrlAdminRttType object.

        NOTE:  For script names, command line parameters
               can follow these names in the
               rttMonScriptAdminTable table."
    ::= { rttMonApplPreConfigedEntry 3 }

rttMonApplPreConfigedValid OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          obsolete
    DESCRIPTION
        "When this row exists, this value will be 'true'.
        This object exists only to create a valid row in this
        table."
    ::= { rttMonApplPreConfigedEntry 4 }



rttMonApplProbeCapacity OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object defines the number of new probes that can be
        configured on a router. The number depends on the value
        of rttMonApplFreeMemLowWaterMark, free bytes
        available on the router and the system configured
        rttMonCtrlAdminEntry number.
        Equation:
        rttMonApplProbeCapacity =
        MIN(((Free_Bytes_on_the_Router - rttMonApplFreeMemLowWaterMark)/
        Memory_required_by_each_probe),
        rttMonApplNumCtrlAdminEntry -
        Num_of_Probes_already_configured))"
    ::= { rttMonAppl 10 }

rttMonApplFreeMemLowWaterMark OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the amount of free memory a router must
        have in order to configure RTR. If RTR found out that the
        memory is falling below this mark, it will not allow new
        probes to be configured.

        This value should not be set higher (or very close to) than
        the free bytes available on the router."
    ::= { rttMonAppl 11 }

rttMonApplLatestSetError OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An error description for the last error message caused
        by set.

        Currently, it includes set error caused due to setting
        rttMonApplFreeMemLowWaterMark greater than the available
        free memory on the router or not enough memory left to
        create new probes."
    ::= { rttMonAppl 12 }

rttMonApplResponder OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Enable or disable RTR responder on the router."
    ::= { rttMonAppl 13 }
-- MD5 Authentication for RTR Control Protocol

rttMonApplAuthTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonApplAuthEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table which contains the definitions for key-strings
        that will be used in authenticating RTR Control Protocol."
    ::= { rttMonAppl 14 }

rttMonApplAuthEntry OBJECT-TYPE
    SYNTAX          RttMonApplAuthEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list that presents the valid parameters for Authenticating
        RTR Control Protocol."
    INDEX           { rttMonApplAuthIndex }
    ::= { rttMonApplAuthTable 1 }

RttMonApplAuthEntry ::= SEQUENCE {
        rttMonApplAuthIndex      Integer32,
        rttMonApplAuthKeyChain   DisplayString,
        rttMonApplAuthKeyString1 DisplayString,
        rttMonApplAuthKeyString2 DisplayString,
        rttMonApplAuthKeyString3 DisplayString,
        rttMonApplAuthKeyString4 DisplayString,
        rttMonApplAuthKeyString5 DisplayString,
        rttMonApplAuthStatus     RowStatus
}

rttMonApplAuthIndex OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Uniquely identifies a row in the rttMonApplAuthTable.
        This is a pseudo-random number selected by the management
        station when creating a row via the rttMonApplAuthStatus
        object. If the pseudo-random number is already in use, an
        'inconsistentValue' is returned. Currently, only one row
        can be created."
    ::= { rttMonApplAuthEntry 1 }

rttMonApplAuthKeyChain OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (1..48))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents the key-chain name. If multiple
        key-strings are specified, then the authenticator will
        alternate between the specified strings."
    ::= { rttMonApplAuthEntry 2 }

rttMonApplAuthKeyString1 OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (1..48))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents a key-string name whose id is 1."
    ::= { rttMonApplAuthEntry 3 }

rttMonApplAuthKeyString2 OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (1..48))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents a key-string name whose id is 2."
    ::= { rttMonApplAuthEntry 4 }

rttMonApplAuthKeyString3 OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (1..48))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents a key-string name whose id is 3."
    ::= { rttMonApplAuthEntry 5 }

rttMonApplAuthKeyString4 OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (1..48))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents a key-string name whose id is 4."
    ::= { rttMonApplAuthEntry 6 }

rttMonApplAuthKeyString5 OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (1..48))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents a key-string name whose id is 5."
    ::= { rttMonApplAuthEntry 7 }

rttMonApplAuthStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of the Authentication row."
    ::= { rttMonApplAuthEntry 8 }



rttMonApplLpdGrpStatsReset OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to reset certain objects within the
        rttMonLpdGrpStatsTable.  When the object is set to value of
        an active LPD Group identifier the associated objects will be
        reset. The reset objects will be set to a value as specified
        in the object's description.

        The following objects will not be reset.
        - rttMonLpdGrpStatsTargetPE
        - rttMonLpdGrpStatsGroupProbeIndex
        - rttMonLpdGrpStatsGroupIndex
        - rttMonLpdGrpStatsStartTimeIndex."
    ::= { rttMonAppl 15 }
-- RTT Configuration Definitions

rttMonCtrlAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonCtrlAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring definitions.

        The RTT administration control is in multiple tables.
        This first table, is used to create a conceptual RTT
        control row.  The following tables contain objects which
        configure scheduling, information gathering, and
        notification/trigger generation.  All of these tables
        will create the same conceptual RTT control row as this
        table using this tables' index as their own index.

        This table is limited in size by the agent
        implementation.  The object rttMonApplNumCtrlAdminEntry
        will reflect this tables maximum number of entries."
    ::= { rttMonCtrl 1 }

rttMonCtrlAdminEntry OBJECT-TYPE
    SYNTAX          RttMonCtrlAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A base list of objects that define a conceptual RTT
        control row."
    INDEX           { rttMonCtrlAdminIndex }
    ::= { rttMonCtrlAdminTable 1 }

RttMonCtrlAdminEntry ::= SEQUENCE {
        rttMonCtrlAdminIndex      Integer32,
        rttMonCtrlAdminOwner      OCTET STRING,
        rttMonCtrlAdminTag        DisplayString,
        rttMonCtrlAdminRttType    RttMonRttType,
        rttMonCtrlAdminThreshold  Integer32,
        rttMonCtrlAdminFrequency  Integer32,
        rttMonCtrlAdminTimeout    Integer32,
        rttMonCtrlAdminVerifyData TruthValue,
        rttMonCtrlAdminStatus     RowStatus,
        rttMonCtrlAdminNvgen      TruthValue,
        rttMonCtrlAdminGroupName  SnmpAdminString,
        rttMonCtrlAdminLongTag    SnmpAdminString
}

rttMonCtrlAdminIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Uniquely identifies a row in the rttMonCtrlAdminTable.
        This is a pseudo-random number, selected by the management
        station or auto-generated based on  operation started by the
        management station,when creating a row via
         the rttMonCtrlAdminStatus object.  If the pseudo-random
         number is already in use an 'inconsistentValue' return code
         will be returned when
        set operation is attempted."
    ::= { rttMonCtrlAdminEntry 1 }

rttMonCtrlAdminOwner OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..255))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Identifies the entity that created this table row."
    DEFVAL          { "" }
    ::= { rttMonCtrlAdminEntry 2 }

rttMonCtrlAdminTag OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..16))
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "A string which is used by a managing application to
        identify the RTT target. This string is inserted into trap
        notifications, but has no other significance to the
        agent.

        rttMonCtrlAdminTag object is superseded by
        rttMonCtrlAdminLongTag."
    DEFVAL          { "" }
    ::= { rttMonCtrlAdminEntry 3 }

rttMonCtrlAdminRttType OBJECT-TYPE
    SYNTAX          RttMonRttType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The type of RTT operation to be performed.  This value
        must be set in the same PDU or before setting any type
        specific configuration.

        Note: The RTT operation 'lspGroup' cannot be created via this
        control row. It will be created automatically by Auto SAA L3
        MPLS VPN when rttMplsVpnMonCtrlLpd is 'true'."
    DEFVAL          { echo }
    ::= { rttMonCtrlAdminEntry 4 }

rttMonCtrlAdminThreshold OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object defines an administrative threshold limit.
        If the RTT operation time exceeds this limit and if the
        conditions specified in rttMonReactAdminThresholdType or
        rttMonHistoryAdminFilter are satisfied, a
        threshold is generated."
    DEFVAL          { 5000 }
    ::= { rttMonCtrlAdminEntry 5 }

rttMonCtrlAdminFrequency OBJECT-TYPE
    SYNTAX          Integer32 (0..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the duration between initiating each RTT
        operation.

        This object cannot be set to a value which would be a
        shorter duration than rttMonCtrlAdminTimeout.

        When the RttMonRttType specifies an operation that is
        synchronous in nature, it may happen that the next RTT
        operation is blocked by a RTT operation which has not
        yet completed.  In this case, the value of a counter
        (rttMonStatsCollectBusies) in rttMonStatsCaptureTable is
        incremented in lieu of initiating a RTT operation, and
        the next attempt will occur at the next
        rttMonCtrlAdminFrequency expiration.

        NOTE:  When the rttMonCtrlAdminRttType object is defined
               to be 'pathEcho', setting this value to a small
               value for your network size may cause an operation
               attempt (or multiple attempts) to be started
               before the previous operation has finished.  In
               this situation the rttMonStatsCollectBusies object
               will be incremented in lieu of initiating a new
               RTT operation, and the next attempt will occur at
               the next rttMonCtrlAdminFrequency expiration.

        When the rttMonCtrlAdminRttType object is defined
        to be 'pathEcho', the suggested value for this object
        is greater than rttMonCtrlAdminTimeout times the
        maximum number of expected hops to the target.

        NOTE:  When the rttMonCtrlAdminRttType object is defined
               to be 'dhcp', the minimum allowed value for this
               object is 10 seconds.  This restriction is due to
               protocol limitations described in RFC 2131."
    DEFVAL          { 60 }
    ::= { rttMonCtrlAdminEntry 6 }

rttMonCtrlAdminTimeout OBJECT-TYPE
    SYNTAX          Integer32 (0..*********)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the duration to wait for a RTT operation
        completion.  The value of this object cannot be set to
        a value which would specify a duration exceeding
        rttMonCtrlAdminFrequency.

        For connection oriented protocols, this may cause the
        connection to be closed by the probe.  Once closed, it
        will be assumed that the connection reestablishment
        will be performed.  To prevent unwanted closure of
        connections, be sure to set this value to a realistic
        connection timeout."
    DEFVAL          { 5000 }
    ::= { rttMonCtrlAdminEntry 7 }

rttMonCtrlAdminVerifyData OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When set to true, the resulting data in each RTT
        operation is compared with the expected data.  This
        includes checking header information (if possible) and
        exact packet size.  Any mismatch will be recorded in the
        rttMonStatsCollectVerifyErrors object.

        Some RttMonRttTypes may not support this option.  When
        a type does not support this option, the agent will
        transition this object to false.  It is the management
        applications responsibility to check for this
        transition."
    DEFVAL          { false }
    ::= { rttMonCtrlAdminEntry 8 }

rttMonCtrlAdminStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of the conceptual RTT control row.

        In order for this object to become active, the following
        row objects must be defined:
         - rttMonCtrlAdminRttType
        Additionally:
         - for echo, pathEcho based on 'ipIcmpEcho' and dlsw probes
            rttMonEchoAdminProtocol and
            rttMonEchoAdminTargetAddress;
         - for echo, pathEcho based on 'mplsLspPingAppl'
            rttMonEchoAdminProtocol, rttMonEchoAdminTargetAddress
            and rttMonEchoAdminLSPFECType
         - for udpEcho, tcpConnect and jitter probes
            rttMonEchoAdminTargetAddress and
            rttMonEchoAdminTargetPort
         - for http and ftp probe
            rttMonEchoAdminURL
         - for dns probe
            rttMonEchoAdminTargetAddressString
            rttMonEchoAdminNameServer
         - dhcp probe doesn't require any additional objects

        All other objects can assume default values. The
        conceptual Rtt control row will be placed into a
        'pending' state (via the rttMonCtrlOperState object)
        if rttMonScheduleAdminRttStartTime is not specified.

        Most conceptual Rtt control row objects cannot be
        modified once this conceptual Rtt control row has been
        created.  The objects that can change are the following:

         - Objects in the rttMonReactAdminTable can be modified
           as needed without setting this object to
           'notInService'.
         - Objects in the rttMonScheduleAdminTable can be
           modified only when this object has the value of
           'notInService'.
         - The rttMonCtrlOperState can be modified to control
           the state of the probe.

        Once this object is in 'active' status, it cannot be
        set to 'notInService' while the rttMonCtrlOperState
        is in 'active' state.  Thus the rttMonCtrlOperState
        object must be transitioned first.

        This object can be set to 'destroy' from any value
        at any time."
    ::= { rttMonCtrlAdminEntry 9 }

rttMonCtrlAdminNvgen OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When set to true, this entry will be shown in
        'show running' command and can be saved into
        Non-volatile memory."
    DEFVAL          { false }
    ::= { rttMonCtrlAdminEntry 10 }

rttMonCtrlAdminGroupName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the operation is created through auto measure group
        creation, then this string will specify the group name
        to which this operation is associated."
    DEFVAL          { "" }
    ::= { rttMonCtrlAdminEntry 11 }

rttMonCtrlAdminLongTag OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..128))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object provides additional information about the Probe
        and is inserted into trap notifications, but has no other
        significance to the agent.

        Setting this object will overwrite any description already set
        by rttMonCtrlAdminTag and vice-versa."
    DEFVAL          { "" }
    ::= { rttMonCtrlAdminEntry 12 }


-- Echo Administration Table

rttMonEchoAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonEchoAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table that contains Round Trip Time (RTT) specific
        definitions.

        This table is controlled via the
        rttMonCtrlAdminTable.  Entries in this table are
        created via the rttMonCtrlAdminStatus object."
    ::= { rttMonCtrl 2 }

rttMonEchoAdminEntry OBJECT-TYPE
    SYNTAX          RttMonEchoAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define specific configuration for
        RttMonRttType conceptual Rtt control rows."
    INDEX           { rttMonCtrlAdminIndex }
    ::= { rttMonEchoAdminTable 1 }

RttMonEchoAdminEntry ::= SEQUENCE {
        rttMonEchoAdminProtocol             RttMonProtocol,
        rttMonEchoAdminTargetAddress        RttMonTargetAddress,
        rttMonEchoAdminPktDataRequestSize   Integer32,
        rttMonEchoAdminPktDataResponseSize  Integer32,
        rttMonEchoAdminTargetPort           Integer32,
        rttMonEchoAdminSourceAddress        RttMonTargetAddress,
        rttMonEchoAdminSourcePort           Integer32,
        rttMonEchoAdminControlEnable        TruthValue,
        rttMonEchoAdminTOS                  Integer32,
        rttMonEchoAdminLSREnable            TruthValue,
        rttMonEchoAdminTargetAddressString  DisplayString,
        rttMonEchoAdminNameServer           RttMonTargetAddress,
        rttMonEchoAdminOperation            RttMonOperation,
        rttMonEchoAdminHTTPVersion          DisplayString,
        rttMonEchoAdminURL                  DisplayString,
        rttMonEchoAdminCache                TruthValue,
        rttMonEchoAdminInterval             Integer32,
        rttMonEchoAdminNumPackets           Integer32,
        rttMonEchoAdminProxy                DisplayString,
        rttMonEchoAdminString1              DisplayString,
        rttMonEchoAdminString2              DisplayString,
        rttMonEchoAdminString3              DisplayString,
        rttMonEchoAdminString4              DisplayString,
        rttMonEchoAdminString5              DisplayString,
        rttMonEchoAdminMode                 RttMonOperation,
        rttMonEchoAdminVrfName              OCTET STRING,
        rttMonEchoAdminCodecType            RttMonCodecType,
        rttMonEchoAdminCodecInterval        Integer32,
        rttMonEchoAdminCodecPayload         Integer32,
        rttMonEchoAdminCodecNumPackets      Integer32,
        rttMonEchoAdminICPIFAdvFactor       Integer32,
        rttMonEchoAdminLSPFECType           INTEGER,
        rttMonEchoAdminLSPSelector          RttMonTargetAddress,
        rttMonEchoAdminLSPReplyMode         RttMonLSPPingReplyMode,
        rttMonEchoAdminLSPTTL               Integer32,
        rttMonEchoAdminLSPExp               Integer32,
        rttMonEchoAdminPrecision            INTEGER,
        rttMonEchoAdminProbePakPriority     INTEGER,
        rttMonEchoAdminOWNTPSyncTolAbs      Integer32,
        rttMonEchoAdminOWNTPSyncTolPct      Integer32,
        rttMonEchoAdminOWNTPSyncTolType     INTEGER,
        rttMonEchoAdminCalledNumber         SnmpAdminString,
        rttMonEchoAdminDetectPoint          RttMonOperation,
        rttMonEchoAdminGKRegistration       TruthValue,
        rttMonEchoAdminSourceVoicePort      DisplayString,
        rttMonEchoAdminCallDuration         Integer32,
        rttMonEchoAdminLSPReplyDscp         Integer32,
        rttMonEchoAdminLSPNullShim          TruthValue,
        rttMonEchoAdminTargetMPID           CfmMepid,
        rttMonEchoAdminTargetDomainName     SnmpAdminString,
        rttMonEchoAdminTargetVLAN           VlanId,
        rttMonEchoAdminEthernetCOS          QosLayer2Cos,
        rttMonEchoAdminLSPVccvID            Integer32,
        rttMonEchoAdminTargetEVC            SnmpAdminString,
        rttMonEchoAdminTargetMEPPort        TruthValue,
        rttMonEchoAdminVideoTrafficProfile  SnmpAdminString,
        rttMonEchoAdminDscp                 Dscp,
        rttMonEchoAdminReserveDsp           INTEGER,
        rttMonEchoAdminInputInterface       InterfaceIndexOrZero,
        rttMonEchoAdminEmulateSourceAddress RttMonTargetAddress,
        rttMonEchoAdminEmulateSourcePort    Integer32,
        rttMonEchoAdminEmulateTargetAddress RttMonTargetAddress,
        rttMonEchoAdminEmulateTargetPort    Integer32,
        rttMonEchoAdminTargetMacAddress     MacAddress,
        rttMonEchoAdminSourceMacAddress     MacAddress,
        rttMonEchoAdminSourceMPID           CfmMepid,
        rttMonEchoAdminEndPointListName     SnmpAdminString,
        rttMonEchoAdminSSM                  TruthValue,
        rttMonEchoAdminControlRetry         Unsigned32,
        rttMonEchoAdminControlTimeout       Unsigned32,
        rttMonEchoAdminIgmpTreeInit         Unsigned32,
        rttMonEchoAdminEnableBurst          TruthValue,
        rttMonEchoAdminAggBurstCycles       Integer32,
        rttMonEchoAdminLossRatioNumFrames   Integer32,
        rttMonEchoAdminAvailNumFrames       Integer32,
        rttMonEchoAdminTstampOptimization   TruthValue,
        rttMonEchoAdminTargetSwitchId       Unsigned32,
        rttMonEchoAdminProfileId            Unsigned32,
        rttMonEchoAdminOutputInterface      InterfaceIndexOrZero
}

rttMonEchoAdminProtocol OBJECT-TYPE
    SYNTAX          RttMonProtocol
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the protocol to be used to perform the RTT
        operation. The following list defines what protocol
        should be used for each probe type:

        echo, pathEcho   - ipIcmpEcho / mplsLspPingAppl
        udpEcho          - ipUdpEchoAppl
        tcpConnect       - ipTcpConn
        http             - httpAppl
        jitter           - jitterAppl
        dlsw             - dlswAppl
        dhcp             - dhcpAppl
        ftp              - ftpAppl
        mplsLspPing      - mplsLspPingAppl
        voip             - voipAppl
        video            - videoAppl
        fabricPathEcho   - fabricPathEchoAppl

        When this protocol does not support the type, a 'badValue'
        error will be returned."
    DEFVAL          { notApplicable }
    ::= { rttMonEchoAdminEntry 1 }

rttMonEchoAdminTargetAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the address of the target."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 2 }

rttMonEchoAdminPktDataRequestSize OBJECT-TYPE
    SYNTAX          Integer32 (0..16384)
    UNITS           "octets"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the number of octets to be
        placed into the ARR Data portion of the request
        message, when using SNA protocols.

        For non-ARR protocols' RTT request/responses,
        this value represents the native payload size.

        REMEMBER:  The ARR Header overhead is not included
                   in this value.

        For echo probes the total packet size = (IP header(20) +
        ICMP header(8) + 8 (internal timestamps) + request size).

        For echo and pathEcho default request size is 28.
        For udp probe, default request size is 16 and for jitter
        probe it is 32. For dlsw probes default request size is 0.

        The minimum request size for echo and pathEcho is 28 bytes,
        for udp it is 4 and for jitter it is 16.
        For udp and jitter probes the maximum request size is 1500.

        For ethernetPing the default request size is 66.
        For ethernetJitter the default request size is 51."
    DEFVAL          { 1 }
    ::= { rttMonEchoAdminEntry 3 }

rttMonEchoAdminPktDataResponseSize OBJECT-TYPE
    SYNTAX          Integer32 (0..16384)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the number of octets to be
        placed into the ARR Data portion of the response message.
        This value is passed to the RTT Echo Server via a
        field in the ARR Header.

        For non-ARR RTT request/response (i.e. ipIcmpecho)
        this value will be set by the agent to match the
        size of rttMonEchoAdminPktDataRequestSize, when
        native payloads are supported.

        REMEMBER:  The ARR Header overhead is not included
                   in this value.

        This object is only supported by SNA protocols."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 4 }

rttMonEchoAdminTargetPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65536)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the target's port number. This
        object is applicable to udpEcho, tcpConnect and jitter probes."
    DEFVAL          { 1 }
    ::= { rttMonEchoAdminEntry 5 }

rttMonEchoAdminSourceAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the IP address of the source.
        This object is applicable to all probes except dns, dlsw
        and sna."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 6 }

rttMonEchoAdminSourcePort OBJECT-TYPE
    SYNTAX          Integer32 (0..65536)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the source's port number. If this
        object is not specified, the application will get a
        port allocated by the system. This object is applicable
        to all probes except dns, dlsw and sna."
    DEFVAL          { 1 }
    ::= { rttMonEchoAdminEntry 7 }

rttMonEchoAdminControlEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "If this object is enabled, then the RTR application
        will send control messages to a responder, residing on the
        target router to respond to the data request packets being
        sent by the source router. This object is not applicable to
        echo, pathEcho, dns and http probes."
    DEFVAL          { true }
    ::= { rttMonEchoAdminEntry 8 }

rttMonEchoAdminTOS OBJECT-TYPE
    SYNTAX          Integer32 (0..255)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the type of service octet in an
        IP header. This object is not applicable to dhcp, dns,
        ethernetPing and ethernetJitter."
    REFERENCE
        "Refer to the following documents for TOS definition.
               RFC791/1349  for IPv4, IPv6,
               draft-ietf-diffserv-header-02.txt"
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 9 }

rttMonEchoAdminLSREnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If this object is enabled then it means that the application
        calculates response time for a specific path, defined in
        rttMonEchoPathAdminEntry. This object is applicable to echo
        probe only."
    DEFVAL          { false }
    ::= { rttMonEchoAdminEntry 10 }

rttMonEchoAdminTargetAddressString OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the address of the target. This string
        can be in IP address format or a hostname. This object
        is applicable to dns probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 11 }

rttMonEchoAdminNameServer OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the ip address of the name-server.
        This object is applicable to dns probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 12 }

rttMonEchoAdminOperation OBJECT-TYPE
    SYNTAX          RttMonOperation
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A code that represents the specific type of RTT operation.
        This object is applicable to http and ftp probe only."
    ::= { rttMonEchoAdminEntry 13 }

rttMonEchoAdminHTTPVersion OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (3..10))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the version number of the HTTP
        Server.  The syntax for the version string is
        <major number>.<minor number> An example would be 1.0,
        1.1 etc.,.  This object is applicable to http probe only."
    DEFVAL          { "1.0" }
    ::= { rttMonEchoAdminEntry 14 }

rttMonEchoAdminURL OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents the URL to which a HTTP probe should
        communicate with. This object is applicable to http probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 15 }

rttMonEchoAdminCache OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "If this object is false then it means that HTTP request should
        not download cached pages. This means that the request should
        be forwarded to the origin server. This object is applicable
        to http probe only."
    DEFVAL          { true }
    ::= { rttMonEchoAdminEntry 16 }

rttMonEchoAdminInterval OBJECT-TYPE
    SYNTAX          Integer32 (0..60000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This value represents the inter-packet delay between packets
        and is in milliseconds. This value is currently used for
        Jitter probe. This object is applicable to jitter probe only."
    DEFVAL          { 20 }
    ::= { rttMonEchoAdminEntry 17 }

rttMonEchoAdminNumPackets OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This value represents the number of packets that need to be
        transmitted. This value is currently used for Jitter probe.
        This object is applicable to jitter probe only."
    DEFVAL          { 10 }
    ::= { rttMonEchoAdminEntry 18 }

rttMonEchoAdminProxy OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This string represents the proxy server information.
        This object is applicable to http probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 19 }

rttMonEchoAdminString1 OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This string stores the content of HTTP raw request.
        If the request cannot fit into String1 then it should
        be split and put in Strings 1 through 5.

        This string stores the content of the DHCP raw option
        data.  The raw DHCP option data must be in HEX.
        If an odd number of characters are specified, a 0
        will be appended to the end of the string.  Only
        DHCP option 82 (decimal) is allowed.
        Here is an example of a valid string:
        5208010610005A6F1234
        Only rttMonEchoAdminString1 is used for dhcp, Strings
        1 through 5 are not used.

        This object is applicable to http and dhcp probe
        types only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 20 }

rttMonEchoAdminString2 OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This string stores the content of HTTP raw request.
        rttMonEchoAdminString1-5 are concatenated to
        form the HTTP raw request used in the RTT operation.
        This object is applicable to http probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 21 }

rttMonEchoAdminString3 OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This string stores the content of HTTP raw request.
        rttMonEchoAdminString1-5 are concatenated to
        form the HTTP raw request used in the RTT operation.
        This object is applicable to http probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 22 }

rttMonEchoAdminString4 OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This string stores the content of HTTP raw request.
        rttMonEchoAdminString1-5 are concatenated to
        form the HTTP raw request used in the RTT operation.
        This object is applicable to http probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 23 }

rttMonEchoAdminString5 OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This string stores the content of HTTP raw request.
        rttMonEchoAdminString1-5 are concatenated to
        form the HTTP raw request used in the RTT operation.
        This object is applicable to http probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 24 }

rttMonEchoAdminMode OBJECT-TYPE
    SYNTAX          RttMonOperation
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A code that represents the specific type of RTT operation.
        This object is applicable to ftp probe only."
    DEFVAL          { ftpPassive }
    ::= { rttMonEchoAdminEntry 25 }

rttMonEchoAdminVrfName OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..32))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This field is used to specify the VPN name in
        which the RTT operation will be used. For regular RTT
        operation this field should not be configured. The agent
        will use this field to identify the VPN routing Table for
        this operation."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 26 }

rttMonEchoAdminCodecType OBJECT-TYPE
    SYNTAX          RttMonCodecType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the codec type to be used with jitter probe. This is
        applicable only for the jitter probe.

        If codec-type is configured the following parameters cannot be
        configured.
        rttMonEchoAdminPktDataRequestSize
        rttMonEchoAdminInterval
        rttMonEchoAdminNumPackets"
    ::= { rttMonEchoAdminEntry 27 }

rttMonEchoAdminCodecInterval OBJECT-TYPE
    SYNTAX          Integer32 (0..60000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This field represents the inter-packet delay between
        packets and is in milliseconds. This object is applicable
        only to jitter probe which uses codec type."
    ::= { rttMonEchoAdminEntry 28 }

rttMonEchoAdminCodecPayload OBJECT-TYPE
    SYNTAX          Integer32 (0..16384)
    UNITS           "octets"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the number of octets that needs to be
        placed into the Data portion of the message. This value is
        used only for jitter probe which uses codec type."
    ::= { rttMonEchoAdminEntry 29 }

rttMonEchoAdminCodecNumPackets OBJECT-TYPE
    SYNTAX          Integer32 (0..60000)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This value represents the number of packets that need to be
        transmitted. This value is used only for jitter probe which
        uses codec type."
    ::= { rttMonEchoAdminEntry 30 }

rttMonEchoAdminICPIFAdvFactor OBJECT-TYPE
    SYNTAX          Integer32 (0..20)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The advantage factor is dependant on the type of access and
        how the service is to be used.
        Conventional Wire-line     0
        Mobility within Building    5
        Mobility within geographic area  10
        Access to hard-to-reach location   20

        This will be used while calculating the ICPIF values
        This valid only for Jitter while calculating the ICPIF value"
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 31 }

rttMonEchoAdminLSPFECType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        ldpIpv4Prefix(1)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The type of the target FEC for the RTT 'echo' and 'pathEcho'
        operations based on 'mplsLspPingAppl' RttMonProtocol.

        ldpIpv4Prefix   - LDP IPv4 prefix."
    ::= { rttMonEchoAdminEntry 32 }

rttMonEchoAdminLSPSelector OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies a valid 127/8 address. This address
        is of the form 127.x.y.z.
        This address is not used to route the MPLS echo packet to the
        destination but is used for load balancing in cases where the
        IP payload's destination address is used for load balancing."
    DEFVAL          { "7F 00 00 01" }
    ::= { rttMonEchoAdminEntry 33 }

rttMonEchoAdminLSPReplyMode OBJECT-TYPE
    SYNTAX          RttMonLSPPingReplyMode
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the reply mode for the LSP Echo
        requests."
    DEFVAL          { replyIpv4Udp }
    ::= { rttMonEchoAdminEntry 34 }

rttMonEchoAdminLSPTTL OBJECT-TYPE
    SYNTAX          Integer32 (0..255)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the TTL setting for MPLS echo request
        packets. For ping operation this represents the TTL value to
        be set in the echo request packet. For trace operation it
        represent the maximum ttl value that can be set in the echo
        request packets starting with TTL=1.

        For 'echo' based on mplsLspPingAppl the default TTL will be
        set to 255, and for 'pathEcho' based on mplsLspPingAppl the
        default will be set to 30.

        Note: This object cannot be set to the value of 0. The
        default value of 0 signifies the default TTL values to be
        used for 'echo' and 'pathEcho' based on 'mplsLspPingAppl'."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 35 }

rttMonEchoAdminLSPExp OBJECT-TYPE
    SYNTAX          Integer32 (0..7)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the EXP value that needs to be
        put as precedence bit in the MPLS echo request IP header."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 36 }

rttMonEchoAdminPrecision OBJECT-TYPE
    SYNTAX          INTEGER  {
                        milliseconds(1),
                        microseconds(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the accuracy of statistics that
        needs to be calculated
        milliseconds - The accuracy of stats will be of milliseconds
        microseconds - The accuracy of stats will be in microseconds.
        This value can be set only for jitter operation"
    DEFVAL          { milliseconds }
    ::= { rttMonEchoAdminEntry 37 }

rttMonEchoAdminProbePakPriority OBJECT-TYPE
    SYNTAX          INTEGER  {
                        normal(1),
                        high(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the priority that will be assigned
        to probe packet.  This value can be set only for jitter
        operation"
    DEFVAL          { normal }
    ::= { rttMonEchoAdminEntry 38 }

rttMonEchoAdminOWNTPSyncTolAbs OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "microseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the total clock synchronization error
        on source and responder that is considered acceptable for
        oneway measurement when NTP is used as clock synchronization
        mechanism.  The total clock synchronization error is sum of
        NTP offsets on source and responder. The value specified is
        microseconds. This value can be set only for jitter operation
        with precision of microsecond."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 39 }

rttMonEchoAdminOWNTPSyncTolPct OBJECT-TYPE
    SYNTAX          Integer32 (0..100)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the total clock synchronization error
        on source and responder that is considered acceptable for
        oneway measurement when NTP is used as clock synchronization
        mechanism.  The total clock synchronization error is sum of
        NTP offsets on source and responder. The value is expressed
        as the percentage of actual oneway latency that is measured.
        This value can be set only for jitter operation with precision
        of microsecond."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 40 }

rttMonEchoAdminOWNTPSyncTolType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        percent(1),
                        absolute(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies whether the value in specified for oneway
        NTP sync tolerance is absolute value or percent value"
    DEFVAL          { percent }
    ::= { rttMonEchoAdminEntry 41 }

rttMonEchoAdminCalledNumber OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..24))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This string stores the called number of post dial delay.
        This object is applicable to voip post dial delay probe only.
        The number will be like the one actualy the user could dial.
        It has the number required by the local country dial plan, plus
        E.164 number. The maximum length is 24 digits. Only digit (0-9)
        is allowed."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 42 }

rttMonEchoAdminDetectPoint OBJECT-TYPE
    SYNTAX          RttMonOperation
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A code that represents the detect point of post dial delay.
        This object is applicable to SAA post dial delay probe only."
    DEFVAL          { voipDTAlertRinging }
    ::= { rttMonEchoAdminEntry 43 }

rttMonEchoAdminGKRegistration OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A boolean that represents VoIP GK registration delay.
        This object is applicable to SAA GK registration delay
        probe only."
    DEFVAL          { false }
    ::= { rttMonEchoAdminEntry 44 }

rttMonEchoAdminSourceVoicePort OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the voice-port on the source gateway.
        This object is applicable to RTP probe only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 45 }

rttMonEchoAdminCallDuration OBJECT-TYPE
    SYNTAX          Integer32 (1..600)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Duration of RTP/Video Probe session.
        This object is applicable to RTP and Video probe."
    DEFVAL          { 60 }
    ::= { rttMonEchoAdminEntry 46 }

rttMonEchoAdminLSPReplyDscp OBJECT-TYPE
    SYNTAX          Integer32 (0..63 | 255)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the DSCP value to be set in the IP header
        of the LSP echo reply packet.
        The value of this object will be in range of DiffServ codepoint
        values between 0 to 63.

        Note: This object cannot be set to value of 255. This default
        value specifies that DSCP is not set for this row."
    DEFVAL          { 255 }
    ::= { rttMonEchoAdminEntry 47 }

rttMonEchoAdminLSPNullShim OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies if the explicit-null label is to be added
        to LSP echo requests which are sent while performing RTT
        operation."
    DEFVAL          { false }
    ::= { rttMonEchoAdminEntry 48 }

rttMonEchoAdminTargetMPID OBJECT-TYPE
    SYNTAX          CfmMepid
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the destination maintenance point ID.
        It is only applicable to ethernetPing and ethernetJitter
        operation. It will be set to 0 for other types of
        operations."
    ::= { rttMonEchoAdminEntry 49 }

rttMonEchoAdminTargetDomainName OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the name of the domain in which the
        destination maintenance point lies. It is only applicable to
        ethernetPing and ethernetJitter operation."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 50 }

rttMonEchoAdminTargetVLAN OBJECT-TYPE
    SYNTAX          VlanId
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the ID of the VLAN in which the
        destination maintenance point lies. It is only applicable to
        ethernetPing and ethernetJitter operation.
        It will be set to 0 for other types of operations."
    ::= { rttMonEchoAdminEntry 51 }

rttMonEchoAdminEthernetCOS OBJECT-TYPE
    SYNTAX          QosLayer2Cos (0..7)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the class of service in an Ethernet
        packet header. It is only applicable to ethernetPing and
        ethernetJitter operation."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 52 }

rttMonEchoAdminLSPVccvID OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies MPLS LSP pseudowire VCCV ID
        values between 1 to 2147483647.

        Note: This object cannot be set to value of 0. This default
        value specifies that VCCV is not set for this row."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 53 }

rttMonEchoAdminTargetEVC OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..100))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the Ethernet Virtual Connection in
        which the destination maintenance point lies. It is only
        applicable to ethernetPing and ethernetJitter operation.
        It will be set to NULL for other types of operations."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 54 }

rttMonEchoAdminTargetMEPPort OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies that Port Level CFM testing towards an
        Outward/Down MEP will be used. It is only applicable to
        ethernetPing and ethernetJitter operation.
        It will be set to NULL for other types of operations."
    DEFVAL          { false }
    ::= { rttMonEchoAdminEntry 55 }

rttMonEchoAdminVideoTrafficProfile OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..255))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which represents the profile name to which a video
        probe should use. This object is applicable to video probe
        only."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 56 }

rttMonEchoAdminDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the Differentiated Service Code Point
        (DSCP) QoS marking in the generated synthetic packets.

        Value - DiffServ Class
            0 - BE (default)
           10 - AF11
           12 - AF12
           14 - AF13
           18 - AF21
           20 - AF22
           22 - AF23
           26 - AF31
           28 - AF32
           30 - AF33
           34 - AF41
           36 - AF42
           38 - AF43
            8 - CS1
           16 - CS2
           24 - CS3
           32 - CS4
           40 - CS5
           48 - CS6
           56 - CS7
           46 - EF"
    REFERENCE       "RFC 2474, RFC 2780"
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 57 }

rttMonEchoAdminReserveDsp OBJECT-TYPE
    SYNTAX          INTEGER  {
                        be(1), -- best effort
                        gs(2), -- guaranteed service
                        na(3) -- not applicable
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the video traffic generation source.

        be : best effort using DSP but without reservation
        gs : guaranteed service using DSP with reservation
        na : not applicable for not using DSP"
    DEFVAL          { na }
    ::= { rttMonEchoAdminEntry 58 }

rttMonEchoAdminInputInterface OBJECT-TYPE
    SYNTAX          InterfaceIndexOrZero
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the network input interface on the
        sender router where the synthetic packets are received from the
        emulated endpoint source. This is used for path congruence with
        correct feature processing at the sender router.

        The user can get the InterfaceIndex number from ifIndex object
        by looking up in ifTable. In fact, it should be useful to first
        get the entry by the augmented table ifXTable which has ifName
        object which matches the interface name used on the router or
        switch equipment console."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 59 }

rttMonEchoAdminEmulateSourceAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the IP address of the emulated source
        from which the synthetic packets would be generated. If this
        object is not specified, the emulated source IP address will by
        default be the same as rttMonEchoAdminSourceAddress. This object
        is applicable to video probes."
    ::= { rttMonEchoAdminEntry 60 }

rttMonEchoAdminEmulateSourcePort OBJECT-TYPE
    SYNTAX          Integer32 (0..65536)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the port number of the emulated source
        from which the synthetic packets would be generated. If this
        object is not specified, the emulated source port number will by
        default be the same as rttMonEchoAdminSourcePort. This object is
        applicable to video probes."
    ::= { rttMonEchoAdminEntry 61 }

rttMonEchoAdminEmulateTargetAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the IP address of the emulated target by
        which the synthetic packets would be received. If this object is
        not specified, the emulated target IP address will by default be
        the same as rttMonEchoAdminTargetAddress. This object is
        applicable to video probes."
    ::= { rttMonEchoAdminEntry 62 }

rttMonEchoAdminEmulateTargetPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65536)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the port number of the emulated target
        by which the synthetic packets would be received. If this object
        is not specified, the emulated target port number will by
        default be the same as rttMonEchoAdminTargetPort. This object is
        applicable to video probes."
    ::= { rttMonEchoAdminEntry 63 }

rttMonEchoAdminTargetMacAddress OBJECT-TYPE
    SYNTAX          MacAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the MAC address of the target device.
        This object is only applicable for Y.1731 operations.
        rttMonEchoAdminTargetMacAddress and rttMonEchoAdminTargetMPID
        may not be used in conjunction."
    ::= { rttMonEchoAdminEntry 64 }

rttMonEchoAdminSourceMacAddress OBJECT-TYPE
    SYNTAX          MacAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the MAC address of the source device.
        This object is only applicable for Y.1731 operations.
        rttMonEchoAdminSourceMacAddress and rttMonEchoAdminSourceMPID
        may not be used in conjunction."
    ::= { rttMonEchoAdminEntry 65 }

rttMonEchoAdminSourceMPID OBJECT-TYPE
    SYNTAX          CfmMepid
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the source maintenance point ID.  It is
        only applicable to Y.1731 operation.  It will be set to zero for
        other types of opearations.  rttMonEchoAdminSourceMPID and
        rttMonEchoAdminSourceMacAddress may not be used in
        conjunction."
    ::= { rttMonEchoAdminEntry 66 }

rttMonEchoAdminEndPointListName OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (1..64))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the name of endpoint list which a probe
        uses to generate operations."
    DEFVAL          { "" }
    ::= { rttMonEchoAdminEntry 67 }

rttMonEchoAdminSSM OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies if Source Specific Multicast is to be
        added. This object is applicable to multicast probe only."
    DEFVAL          { false }
    ::= { rttMonEchoAdminEntry 68 }

rttMonEchoAdminControlRetry OBJECT-TYPE
    SYNTAX          Unsigned32 (1..5)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the maximum number of retries for control
        message."
    DEFVAL          { 3 }
    ::= { rttMonEchoAdminEntry 69 }

rttMonEchoAdminControlTimeout OBJECT-TYPE
    SYNTAX          Unsigned32 (1..10000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the wait duration before control message
        timeout."
    DEFVAL          { 5000 }
    ::= { rttMonEchoAdminEntry 70 }

rttMonEchoAdminIgmpTreeInit OBJECT-TYPE
    SYNTAX          Unsigned32 (0..10)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies number of packets to be sent for
        multicast tree setup. This object is applicable to multicast
        probe only."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 71 }

rttMonEchoAdminEnableBurst OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates that packets will be sent in burst."
    DEFVAL          { false }
    ::= { rttMonEchoAdminEntry 72 }

rttMonEchoAdminAggBurstCycles OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of burst cycles to be sent
        during the aggregate interval. This value is currently used
        for Y1731 SLM(Synthetic Loss Measurment) probe.
        This object is applicable to Y1731 SLM probe only."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 73 }

rttMonEchoAdminLossRatioNumFrames OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of frames over which to
        calculate the frame loss ratio. This object is applicable
        to Y1731 SLM probe only."
    DEFVAL          { 10 }
    ::= { rttMonEchoAdminEntry 74 }

rttMonEchoAdminAvailNumFrames OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of frames over which to
        calculate the availability. This object is applicable to
        Y1731 SLM probe only."
    DEFVAL          { 10 }
    ::= { rttMonEchoAdminEntry 75 }

rttMonEchoAdminTstampOptimization OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies whether timestamp optimization is
        enabled.

        When the value is 'true' then timestamp optimization is
        enabled.  The probe will utilize lower layer (Hardware/Packet
        Processor) timestamping values to improve accuracy of
        statistics.

        This value can be set only for udp jitter operation with
        precision of microsecond."
    DEFVAL          { false }
    ::= { rttMonEchoAdminEntry 76 }

rttMonEchoAdminTargetSwitchId OBJECT-TYPE
    SYNTAX          Unsigned32 (0 | 1..65535)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the destination Switch Id that
        needed to send one TRILL/FB OAM Loopback request. This
        object is applicable to Fabric Path Echo probe only.
        Value 0 means not applicable."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 77 }

rttMonEchoAdminProfileId OBJECT-TYPE
    SYNTAX          Unsigned32 (0 | 1..1023)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the destination Profile Id that
        needed to send one TRILL/FB OAM Loopback request. This
        object is applicable to Fabric Path Echo probe only.
        Value 0 means not applicable."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 78 }

rttMonEchoAdminOutputInterface OBJECT-TYPE
    SYNTAX          InterfaceIndexOrZero
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the network interface on the
        sender router where the packets are transmitted from the
        emulated endpoint source.

        The user can get the InterfaceIndex number from ifIndex object
        by looking up in ifTable. In fact, it should be useful to first
        get the entry by the augmented table ifXTable which has ifName
        object which matches the interface name used on the router or
        switch equipment console."
    DEFVAL          { 0 }
    ::= { rttMonEchoAdminEntry 79 }


-- FileIO Administration TABLE

rttMonFileIOAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonFileIOAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring 'fileIO'
        specific definitions.

        When the RttMonRttType is not 'fileIO' this table is
        not valid.

        This table is controlled via the
        rttMonCtrlAdminTable.  Entries in this table are
        created via the rttMonCtrlAdminStatus object."
    ::= { rttMonCtrl 3 }

rttMonFileIOAdminEntry OBJECT-TYPE
    SYNTAX          RttMonFileIOAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "A list of objects that define specific configuration for
        'fileIO' RttMonRttType conceptual Rtt control rows."
    INDEX           { rttMonCtrlAdminIndex }
    ::= { rttMonFileIOAdminTable 1 }

RttMonFileIOAdminEntry ::= SEQUENCE {
        rttMonFileIOAdminFilePath DisplayString,
        rttMonFileIOAdminSize     INTEGER,
        rttMonFileIOAdminAction   INTEGER
}

rttMonFileIOAdminFilePath OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          obsolete
    DESCRIPTION
        "The fully qualified file path that will be the target
        of the RTT operation.

        This value must match one of the rttMonApplPreConfigedName
        entries."
    DEFVAL          { "" }
    ::= { rttMonFileIOAdminEntry 1 }

rttMonFileIOAdminSize OBJECT-TYPE
    SYNTAX          INTEGER  {
                        n256(1),
                        n1k(2),
                        n64k(3),
                        n128k(4),
                        n256k(5)
                    }
    UNITS           "bytes"
    MAX-ACCESS      read-create
    STATUS          obsolete
    DESCRIPTION
        "The size of the file to write/read from the File
        Server."
    DEFVAL          { n256 }
    ::= { rttMonFileIOAdminEntry 2 }

rttMonFileIOAdminAction OBJECT-TYPE
    SYNTAX          INTEGER  {
                        write(1),
                        read(2),
                        writeRead(3)
                    }
    MAX-ACCESS      read-create
    STATUS          obsolete
    DESCRIPTION
        "The File I/O action to be performed."
    DEFVAL          { read }
    ::= { rttMonFileIOAdminEntry 3 }


-- Script Administration Table

rttMonScriptAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonScriptAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring 'script'
        specific definitions.

        When the RttMonRttType is not 'script' this table is
        not valid.

        This table is controlled via the
        rttMonCtrlAdminTable.  Entries in this table are
        created via the rttMonCtrlAdminStatus object."
    ::= { rttMonCtrl 4 }

rttMonScriptAdminEntry OBJECT-TYPE
    SYNTAX          RttMonScriptAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          obsolete
    DESCRIPTION
        "A list of objects that define specific configuration for
        'script' RttMonRttType conceptual Rtt control rows."
    INDEX           { rttMonCtrlAdminIndex }
    ::= { rttMonScriptAdminTable 1 }

RttMonScriptAdminEntry ::= SEQUENCE {
        rttMonScriptAdminName          DisplayString,
        rttMonScriptAdminCmdLineParams DisplayString
}

rttMonScriptAdminName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          obsolete
    DESCRIPTION
        "This will be the Name of the Script that will be used to
        generate RTT operations.

        This object must match one of the
        rttMonApplPreConfigedName entries."
    DEFVAL          { "" }
    ::= { rttMonScriptAdminEntry 1 }

rttMonScriptAdminCmdLineParams OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-create
    STATUS          obsolete
    DESCRIPTION
        "This will be the actual command line parameters
        passed to the rttMonScriptAdminName when being
        executed."
    DEFVAL          { "" }
    ::= { rttMonScriptAdminEntry 2 }


-- Schedule Administration Table

rttMonScheduleAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonScheduleAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring scheduling
        specific definitions.

        This table is controlled via the
        rttMonCtrlAdminTable.  Entries in this table are
        created via the rttMonCtrlAdminStatus object."
    ::= { rttMonCtrl 5 }

rttMonScheduleAdminEntry OBJECT-TYPE
    SYNTAX          RttMonScheduleAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define specific configuration for
        the scheduling of RTT operations."
    AUGMENTS           { rttMonCtrlAdminEntry  }
    ::= { rttMonScheduleAdminTable 1 }

RttMonScheduleAdminEntry ::= SEQUENCE {
        rttMonScheduleAdminRttLife            Integer32,
        rttMonScheduleAdminRttStartTime       TimeTicks,
        rttMonScheduleAdminConceptRowAgeout   Integer32,
        rttMonScheduleAdminRttRecurring       TruthValue,
        rttMonScheduleAdminConceptRowAgeoutV2 Integer32,
        rttMonScheduleAdminStartType          RttMonScheduleStartType,
        rttMonScheduleAdminStartDelay         Integer32
}

rttMonScheduleAdminRttLife OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object value will be placed into the
        rttMonCtrlOperRttLife object when the rttMonCtrlOperState
        object transitions to 'active' or 'pending'.

        The value 2147483647 has a special meaning.  When
        this object is set to 2147483647, the
        rttMonCtrlOperRttLife object will not decrement.
        And thus the life time will never end."
    DEFVAL          { 3600 }
    ::= { rttMonScheduleAdminEntry 1 }

rttMonScheduleAdminRttStartTime OBJECT-TYPE
    SYNTAX          TimeTicks
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This is the time when this conceptional row will
        activate.

        This is the value of MIB-II's sysUpTime in the future.
        When sysUpTime equals this value this object will
        cause the activation of a conceptual Rtt row.

        When an agent has the capability to determine date and
        time, the agent should store this object as DateAndTime.
        This allows the agent to completely reset (restart) and
        still be able to start conceptual Rtt rows at the
        intended time.  If the agent cannot keep date and time
        and the agent resets, all entries should take on one of
        the special value defined below.

        The first special value allows this conceptual Rtt
        control row to immediately transition the
        rttMonCtrlOperState object into 'active' state when the
        rttMonCtrlAdminStatus  object transitions to active.
        This special value is defined to be a value of this
        object that, when initially set, is 1.

        The second special value allows this conceptual Rtt
        control row to immediately transition the
        rttMonCtrlOperState object into 'pending' state when
        the rttMonCtrlAdminStatus object transitions to active.
        Also, when the rttMonCtrlOperRttLife counts down to zero
        (and not when set to zero), this special value causes
        this conceptual Rtt control row to  retransition the
        rttMonCtrlOperState object into 'pending' state.  This
        special value is defined to be a value of this object
        that, when initially set, is smaller than the current
        sysUpTime. (With the exception of one, as defined in
        the previous paragraph)

        If rttMonScheduleAdminStartType is specified with this
        object then rttMonScheduleAdminRttStartTime
        is ignored and probe start time is configured based on
        rttMonScheduleAdminStartType."
    DEFVAL          { 0 }
    ::= { rttMonScheduleAdminEntry 2 }

rttMonScheduleAdminConceptRowAgeout OBJECT-TYPE
    SYNTAX          Integer32 (0..2073600)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "The amount of time this conceptual Rtt control row will
        exist when not in an 'active' rttMonCtrlOperState.

        When this conceptual Rtt control row enters an 'active'
        state, this timer will be reset and suspended.  When
        this conceptual RTT control row enters a state other
        than 'active', the timer will be restarted.

        NOTE:  When a conceptual Rtt control row ages out, the
               agent needs to remove the associated entries in
               the rttMonReactTriggerAdminTable and
               rttMonReactTriggerOperTable.

        When this value is set to zero, this entry will
        never be aged out.
        rttMonScheduleAdminConceptRowAgeout object is superseded by
        rttMonScheduleAdminConceptRowAgeoutV2."
    DEFVAL          { 3600 }
    ::= { rttMonScheduleAdminEntry 3 }

rttMonScheduleAdminRttRecurring OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When set to true, this entry will be scheduled to
        run automatically for the specified duration equal
        to the life configured, at the same time daily.

        This value cannot be set to true
        (a) if rttMonScheduleAdminRttLife object has value greater or
           equal to 86400 seconds.
        (b) if sum of values of rttMonScheduleAdminRttLife and
           rttMonScheduleAdminConceptRowAgeout is less or equal to
           86400 seconds."
    DEFVAL          { false }
    ::= { rttMonScheduleAdminEntry 4 }

rttMonScheduleAdminConceptRowAgeoutV2 OBJECT-TYPE
    SYNTAX          Integer32 (0..2073600)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The amount of time this conceptual Rtt control row will
        exist when not in an 'active' rttMonCtrlOperState.

        When this conceptual Rtt control row enters an 'active'
        state, this timer will be reset and suspended.  When
        this conceptual RTT control row enters a state other
        than 'active', the timer will be restarted.

        NOTE:  It is the same as rttMonScheduleAdminConceptRowAgeout
               except DEFVAL is 0 to be consistent with CLI ageout
               default.

        When this value is set to zero, this entry will
        never be aged out."
    DEFVAL          { 0 }
    ::= { rttMonScheduleAdminEntry 5 }

rttMonScheduleAdminStartType OBJECT-TYPE
    SYNTAX          RttMonScheduleStartType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the start-time option to be used for
        scheduling the start time of RTT operation.
        The following list defines the start-time options
        that can be configured for the probe:

        pending(1)      pending state.
        now(2)          schedule to immediately start the
                        probe.
        random(3)       schedule start time of probe at random
                        time over a range.
        after(4)        schedule start time of probe after a
                        certain amount of time from now.
        specific(5)     schedule start time of probe at a given
                        specific time interval"
    DEFVAL          { pending }
    ::= { rttMonScheduleAdminEntry 6 }

rttMonScheduleAdminStartDelay OBJECT-TYPE
    SYNTAX          Integer32 (500..10000)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the start time delay range in
        milliseconds.
        The value of this object is only effective with
        rttMonScheduleAdminStartType value.

        For rttMonScheduleAdminStartType with random option,
        a random time is generated with in provided start delay
        range and added to current time to generate the random
        start time of the probe."
    DEFVAL          { 500 }
    ::= { rttMonScheduleAdminEntry 7 }


-- Reaction Administration Table

rttMonReactAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonReactAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          deprecated
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring Notification
        and Trigger definitions.

        All Notification/Reactions are applied to all RTT
        End-to-End operations.  Thus, they do not apply to hops
        along a path to the target, when RttMonRttType is
        'pathEcho'.

        The format and content of SNA NMVT's are not defined
        within this module.

        It can be noted, however, that there are Alert NMVT's,
        and traps which are sent when an abnormal
        condition occurs, i.e. when one of
        rttMonCtrlOperConnectionLostOccurred,
        rttMonCtrlOperTimeoutOccurred or
        rttMonCtrlOperOverThresholdOccurred are changed to true,
        and Resolution NMVT's, and Resolution traps which are
        sent when that condition clears, i.e. when one of
        rttMonCtrlOperConnectionLostOccurred,
        rttMonCtrlOperTimeoutOccurred or
        rttMonCtrlOperOverThresholdOccurred is changed back to
        false.

        When rttMonReactAdminActionType is set to one of the
        following:
          -  triggerOnly
          -  trapAndTrigger
          -  nmvtAndTrigger
          -  trapNmvtAndTrigger
        The corresponding rows in the
        rttMonReactTriggerAdminTable defined via the
        rttMonCtrlAdminIndex will become active.

        This table augments the rttMonCtrlAdminTable.
        rttMonReactAdminTable object is superseded by rttMonReactTable."
    ::= { rttMonCtrl 6 }

rttMonReactAdminEntry OBJECT-TYPE
    SYNTAX          RttMonReactAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          deprecated
    DESCRIPTION
        "A list of objects that define RTT reaction operations.
        rttMonReactAdminEntry object is superseded by rttMonReactEntry."
    AUGMENTS           { rttMonCtrlAdminEntry  }
    ::= { rttMonReactAdminTable 1 }

RttMonReactAdminEntry ::= SEQUENCE {
        rttMonReactAdminConnectionEnable  TruthValue,
        rttMonReactAdminTimeoutEnable     TruthValue,
        rttMonReactAdminThresholdType     INTEGER,
        rttMonReactAdminThresholdFalling  Integer32,
        rttMonReactAdminThresholdCount    Integer32,
        rttMonReactAdminThresholdCount2   Integer32,
        rttMonReactAdminActionType        INTEGER,
        rttMonReactAdminVerifyErrorEnable TruthValue
}

rttMonReactAdminConnectionEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "If true, a reaction is generated when a RTT
        operation to a rttMonEchoAdminTargetAddress
        (echo type) causes
        rttMonCtrlOperConnectionLostOccurred to change its
        value.  Thus connections to intermediate hops will
        not cause this value to change.
        rttMonReactAdminConnectionEnable object is superseded by
        rttMonReactVar."
    DEFVAL          { false }
    ::= { rttMonReactAdminEntry 1 }

rttMonReactAdminTimeoutEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "If true, a reaction is generated when a RTT
        operation causes rttMonCtrlOperTimeoutOccurred
        to change its value.

        When the RttMonRttType is 'pathEcho' timeouts to
        intermediate hops will not cause
        rttMonCtrlOperTimeoutOccurred to change its value.
        rttMonReactAdminTimeoutEnable object is superseded by
        rttMonReactVar."
    DEFVAL          { false }
    ::= { rttMonReactAdminEntry 2 }

rttMonReactAdminThresholdType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        never(1),
                        immediate(2),
                        consecutive(3),
                        xOfy(4),
                        average(5)
                    }
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "This object specifies the conditions under which
        rttMonCtrlOperOverThresholdOccurred is changed:

        NOTE:  When the RttMonRttType is 'pathEcho' this
               objects' value and all associated
               object values are only valid when RTT
               'echo' operations are to the
               rttMonEchoAdminTargetAddress object address.  Thus
               'pathEcho' operations to intermediate
               hops will not cause this object to change.

        never       - rttMonCtrlOperOverThresholdOccurred is
                       never set
        immediate   - rttMonCtrlOperOverThresholdOccurred is set
                       to true when an operation completion time
                       exceeds rttMonCtrlAdminThreshold;
                       conversely
                       rttMonCtrlOperOverThresholdOccurred is set
                       to false when an operation completion time
                       falls below
                       rttMonReactAdminThresholdFalling
        consecutive - rttMonCtrlOperOverThresholdOccurred is set
                       to true when an operation completion time
                       exceeds rttMonCtrlAdminThreshold on
                       rttMonReactAdminThresholdCount consecutive
                       RTT operations; conversely,
                       rttMonCtrlOperOverThresholdOccurred is set
                       to false when an operation completion time
                       falls under the
                       rttMonReactAdminThresholdFalling
                       for the same number of consecutive
                       operations
        xOfy        - rttMonCtrlOperOverThresholdOccurred is set
                       to true when x (as specified by
                       rttMonReactAdminThresholdCount) out of the
                       last y (as specified by
                       rttMonReactAdminThresholdCount2)
                       operation completion time exceeds
                       rttMonCtrlAdminThreshold;
                       conversely, it is set to false when x,
                       out of the last y operation completion
                       time fall below
                       rttMonReactAdminThresholdFalling
                       NOTE: When x > y, the probe will never
                             generate a reaction.
        average     - rttMonCtrlOperOverThresholdOccurred is set
                       to true when the running average of the
                       previous rttMonReactAdminThresholdCount
                       operation completion times exceed
                       rttMonCtrlAdminThreshold; conversely, it
                       is set to false when the running average
                       falls below the
                       rttMonReactAdminThresholdFalling

        If this value is changed by a management station,
        rttMonCtrlOperOverThresholdOccurred is set to false, but
        no reaction is generated if the prior value of
        rttMonCtrlOperOverThresholdOccurred was true.
        rttMonReactAdminThresholdType object is superseded by
        rttMonReactThresholdType."
    DEFVAL          { never }
    ::= { rttMonReactAdminEntry 3 }

rttMonReactAdminThresholdFalling OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "This object defines a threshold limit. If the RTT
        operation time falls below this limit and if the conditions
        specified in rttMonReactAdminThresholdType are satisfied, an
        threshold is generated.
        rttMonReactAdminThresholdFalling object is superseded by
        rttMonReactThresholdFalling."
    DEFVAL          { 3000 }
    ::= { rttMonReactAdminEntry 4 }

rttMonReactAdminThresholdCount OBJECT-TYPE
    SYNTAX          Integer32 (1..16)
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "This object defines the 'x' value of the xOfy condition
        specified in rttMonReactAdminThresholdType.
        rttMonReactAdminThresholdCount object is superseded by
        rttMonReactThresholdCountX."
    DEFVAL          { 5 }
    ::= { rttMonReactAdminEntry 5 }

rttMonReactAdminThresholdCount2 OBJECT-TYPE
    SYNTAX          Integer32 (1..16)
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "This object defines the 'y' value of the xOfy condition
        specified in rttMonReactAdminThresholdType.
        rttMonReactAdminThresholdCount2 object is superseded by
        rttMonReactThresholdCountyY."
    DEFVAL          { 5 }
    ::= { rttMonReactAdminEntry 6 }

rttMonReactAdminActionType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        trapOnly(2),
                        nmvtOnly(3),
                        triggerOnly(4),
                        trapAndNmvt(5),
                        trapAndTrigger(6),
                        nmvtAndTrigger(7),
                        trapNmvtAndTrigger(8)
                    }
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "Specifies what type(s), if any, of reaction(s) to
        generate if an operation violates one of the watched
        conditions:

        none               - no reaction is generated
        trapOnly           - a trap is generated
        nmvtOnly           - an SNA NMVT is generated
        triggerOnly        - all trigger actions defined for this
                              entry are initiated
        trapAndNmvt        - both a trap and an SNA NMVT are
                              generated
        trapAndTrigger     - both a trap and all trigger actions
                              are initiated
        nmvtAndTrigger     - both a NMVT and all trigger actions
                              are initiated
        trapNmvtAndTrigger - a NMVT, trap, and all trigger actions
                              are initiated

        A trigger action is defined via the
        rttMonReactTriggerAdminTable.
        rttMonReactAdminActionType object is superseded by
        rttMonReactActionType."
    DEFVAL          { none }
    ::= { rttMonReactAdminEntry 7 }

rttMonReactAdminVerifyErrorEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          deprecated
    DESCRIPTION
        "If true, a reaction is generated when a RTT
        operation causes rttMonCtrlOperVerifyErrorOccurred
        to change its value.
        rttMonReactAdminVerifyErrorEnable object is superseded by
        rttMonReactVar."
    DEFVAL          { false }
    ::= { rttMonReactAdminEntry 8 }


-- Statistics Administration Table

rttMonStatisticsAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonStatisticsAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring statistics
        definitions.

        The definitions in this table control what and how many
        entries will be placed into the rttMonStatsCaptureTable.

        The statistics capture table is a rollover table.  When
        the rttMonStatisticsAdminNumHourGroups index value
        exceeds its value defined in this table, the oldest
        corresponding group will be deleted and will be replaced
        with the new group.  All other indices will only fill to
        there maximum size.

        NOTE:  The maximum size of this table is defined to be
               the product of the rttMonCtrlAdminIndex times
               rttMonStatisticsAdminNumHourGroups times
               rttMonStatisticsAdminNumPaths times
               rttMonStatisticsAdminNumHops times
               rttMonStatisticsAdminNumDistBuckets.

        NOTE WELL:  Each of the 'Num' objects values in this
                    have a special behavior.  When one of the
                    objects is set to a value larger than the
                    Rtt application can support the set will
                    succeed, but the resultant value will
                    be set to the applications maximum value.
                    The setting management station must reread
                    this object to verify the actual value.

        This table augments the rttMonCtrlAdminTable."
    ::= { rttMonCtrl 7 }

rttMonStatisticsAdminEntry OBJECT-TYPE
    SYNTAX          RttMonStatisticsAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define RTT statistics
        capture operations."
    AUGMENTS           { rttMonCtrlAdminEntry  }
    ::= { rttMonStatisticsAdminTable 1 }

RttMonStatisticsAdminEntry ::= SEQUENCE {
        rttMonStatisticsAdminNumHourGroups  Integer32,
        rttMonStatisticsAdminNumPaths       Integer32,
        rttMonStatisticsAdminNumHops        Integer32,
        rttMonStatisticsAdminNumDistBuckets Integer32,
        rttMonStatisticsAdminDistInterval   Integer32
}

rttMonStatisticsAdminNumHourGroups OBJECT-TYPE
    SYNTAX          Integer32 (0..25)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The maximum number of groups of paths to record.
        Specifically this is the number of hourly groups
        to keep before rolling over.

        The value of one is not advisable because the
        group will close and immediately be deleted before
        the network management station will have the
        opportunity to retrieve the statistics.

        The value used in the rttMonStatsCaptureTable to
        uniquely identify this group is the
        rttMonStatsCaptureStartTimeIndex.

        HTTP and Jitter probes store only two hours of data.

        When this object is set to the value of zero all
        rttMonStatsCaptureTable data capturing will be shut off."
    DEFVAL          { 2 }
    ::= { rttMonStatisticsAdminEntry 1 }

rttMonStatisticsAdminNumPaths OBJECT-TYPE
    SYNTAX          Integer32 (1..128)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When RttMonRttType is 'pathEcho' this is the maximum
        number of statistics paths to record per hourly group.
        This value directly represents the path to a target.
        For all other RttMonRttTypes this value will be
        forced to one by the agent.

        NOTE: For 'pathEcho' a source to target path will be
              created to to hold all errors that occur when a
              specific path or connection has not be found/setup.
              Thus, it is advised to set this value greater
              than one.

        Since this index does not rollover, only the first
        rttMonStatisticsAdminNumPaths will be kept."
    DEFVAL          { 5 }
    ::= { rttMonStatisticsAdminEntry 2 }

rttMonStatisticsAdminNumHops OBJECT-TYPE
    SYNTAX          Integer32 (1..30)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When RttMonRttType is 'pathEcho' this is the maximum
        number of statistics hops to record per path group.
        This value directly represents the number of hops along
        a path to a target, thus we can only support 30 hops.
        For all other RttMonRttTypes this value will be
        forced to one by the agent.

        Since this index does not rollover, only the first
        rttMonStatisticsAdminNumHops will be kept. This object
        is applicable to pathEcho probes only."
    DEFVAL          { 16 }
    ::= { rttMonStatisticsAdminEntry 3 }

rttMonStatisticsAdminNumDistBuckets OBJECT-TYPE
    SYNTAX          Integer32 (1..20)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The maximum number of statistical distribution
        Buckets to accumulate.

        Since this index does not rollover, only the first
        rttMonStatisticsAdminNumDistBuckets will be kept.

        The last rttMonStatisticsAdminNumDistBucket will
        contain all entries from its distribution interval
        start point to infinity. This object is not applicable
        to http and jitter probes."
    DEFVAL          { 1 }
    ::= { rttMonStatisticsAdminEntry 4 }

rttMonStatisticsAdminDistInterval OBJECT-TYPE
    SYNTAX          Integer32 (1..100)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The statistical distribution buckets interval.

        Distribution Bucket Example:

        rttMonStatisticsAdminNumDistBuckets = 5 buckets
        rttMonStatisticsAdminDistInterval = 10 milliseconds

        | Bucket 1 | Bucket 2 | Bucket 3 | Bucket 4 | Bucket 5  |
        |  0-9 ms  | 10-19 ms | 20-29 ms | 30-39 ms | 40-Inf ms |

        Odd Example:

        rttMonStatisticsAdminNumDistBuckets = 1 buckets
        rttMonStatisticsAdminDistInterval = 10 milliseconds

        | Bucket 1  |
        |  0-Inf ms |

        Thus, this odd example shows that the value of
        rttMonStatisticsAdminDistInterval does not apply when
        rttMonStatisticsAdminNumDistBuckets is one.
        This object is not applicable to http and jitter probes."
    DEFVAL          { 20 }
    ::= { rttMonStatisticsAdminEntry 5 }


-- History Administration Table

rttMonHistoryAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonHistoryAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring history
        definitions.

        The definitions in this table control what and how many
        entries will be placed into the
        rttMonHistoryCollectionTable.

        The history collection table is a rollover table.  When
        the rttMonHistoryAdminNumLives index value exceeds its
        value defined in this table, the oldest corresponding
        'lives' group will be deleted and will be replaced with
        the new 'lives' group.  All other indices will only fill
        to their maximum size.

        NOTE:  The maximum size of this table is defined to be
               the product of the rttMonCtrlAdminIndex times
               rttMonHistoryAdminNumLives times
               rttMonHistoryAdminNumBuckets times
               rttMonHistoryAdminNumSamples.

        NOTE WELL:  Each of the 'Num' objects values in this
                    have a special behavior.  When one of the
                    objects is set to a value larger than the
                    Rtt application can support the set will
                    succeed, but the resultant value will
                    be set to the applications maximum value.
                    The setting management station must reread
                    this object to verify the actual value.

        NOTE: this table is not applicable to http and jitter
              probes"
    ::= { rttMonCtrl 8 }

rttMonHistoryAdminEntry OBJECT-TYPE
    SYNTAX          RttMonHistoryAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define RTT history collection
        operations."
    AUGMENTS           { rttMonCtrlAdminEntry  }
    ::= { rttMonHistoryAdminTable 1 }

RttMonHistoryAdminEntry ::= SEQUENCE {
        rttMonHistoryAdminNumLives   Integer32,
        rttMonHistoryAdminNumBuckets Integer32,
        rttMonHistoryAdminNumSamples Integer32,
        rttMonHistoryAdminFilter     INTEGER
}

rttMonHistoryAdminNumLives OBJECT-TYPE
    SYNTAX          Integer32 (0..2)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The maximum number of history lives to record.  A life
        is defined by the countdown (or transition) to zero
        by the rttMonCtrlOperRttLife object.  A new life is
        created when the same conceptual RTT control row is
        restarted via the transition of the
        rttMonCtrlOperRttLife object and its subsequent
        countdown.

        The value of zero will shut off all
        rttMonHistoryAdminTable data collection."
    DEFVAL          { 0 }
    ::= { rttMonHistoryAdminEntry 1 }

rttMonHistoryAdminNumBuckets OBJECT-TYPE
    SYNTAX          Integer32 (1..60)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The maximum number of history buckets to record.  When
        the RttMonRttType is 'pathEcho'  this value directly
        represents a path to a target.  For all other
        RttMonRttTypes this value should be set to the number
        of operations to keep per lifetime.

        After rttMonHistoryAdminNumBuckets are filled, the
        and the oldest entries are deleted and the most recent
        rttMonHistoryAdminNumBuckets buckets are retained."
    DEFVAL          { 15 }
    ::= { rttMonHistoryAdminEntry 2 }

rttMonHistoryAdminNumSamples OBJECT-TYPE
    SYNTAX          Integer32 (1..30)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The maximum number of history samples to record per
        bucket.  When the RttMonRttType is 'pathEcho' this
        value directly represents the number of hops along a
        path to a target, thus we can only support 30 hops.
        For all other RttMonRttTypes this value will be
        forced to one by the agent."
    DEFVAL          { 16 }
    ::= { rttMonHistoryAdminEntry 3 }

rttMonHistoryAdminFilter OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        all(2),
                        overThreshold(3),
                        failures(4)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Defines a filter for adding RTT results to the history
        buffer:

        none          - no history is recorded
        all           - the results of all completion times
                         and failed completions are recorded
        overThreshold - the results of completion times
                         over rttMonCtrlAdminThreshold are
                         recorded.
        failures      - the results of failed operations (only)
                         are recorded."
    DEFVAL          { none }
    ::= { rttMonHistoryAdminEntry 4 }


-- Overall Operational values

rttMonCtrlOperTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonCtrlOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of which contains the Operational values for the
        probe, and the conceptual RTT control row.

        This table augments the rttMonCtrlAdminTable."
    ::= { rttMonCtrl 9 }

rttMonCtrlOperEntry OBJECT-TYPE
    SYNTAX          RttMonCtrlOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that describe the current state
        of probe, and the conceptual RTT control row."
    AUGMENTS           { rttMonCtrlAdminEntry  }
    ::= { rttMonCtrlOperTable 1 }

RttMonCtrlOperEntry ::= SEQUENCE {
        rttMonCtrlOperModificationTime       TimeStamp,
        rttMonCtrlOperDiagText               DisplayString,
        rttMonCtrlOperResetTime              TimeStamp,
        rttMonCtrlOperOctetsInUse            Gauge32,
        rttMonCtrlOperConnectionLostOccurred TruthValue,
        rttMonCtrlOperTimeoutOccurred        TruthValue,
        rttMonCtrlOperOverThresholdOccurred  TruthValue,
        rttMonCtrlOperNumRtts                Integer32,
        rttMonCtrlOperRttLife                Integer32,
        rttMonCtrlOperState                  INTEGER,
        rttMonCtrlOperVerifyErrorOccurred    TruthValue
}

rttMonCtrlOperModificationTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is updated whenever an object in
        the conceptual RTT control row is changed or
        updated."
    ::= { rttMonCtrlOperEntry 1 }

rttMonCtrlOperDiagText OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..51))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string which can be used as an aid in tracing problems.
        The content of this field will depend on the type of
        target (rttMonEchoAdminProtocol).

        When rttMonEchoAdminProtocol is one of snaLU0EchoAppl, or
        snaLU2EchoAppl this object contains the name of the
        Logical Unit (LU) being used for this RTT session (from
        the HOST's point of view), once the session has been
        established; this can then be used to correlate this
        name to the connection information stored in the
        Mainframe Host.

        When rttMonEchoAdminProtocol is snaLU62EchoAppl, this
        object contains the Logical Unit (LU) name being used for
        this RTT session, once the session has been established.
        This name can be used by the management application to
        correlate this objects value to the connection
        information stored at this SNMP Agent via the APPC or
        APPN mib.

        When rttMonEchoAdminProtocol is not one of the
        previously mentioned values, this value will be null.

        It is primarily intended that this object contains
        information which has significance to a human operator."
    DEFVAL          { "" }
    ::= { rttMonCtrlOperEntry 2 }

rttMonCtrlOperResetTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is set when the rttMonCtrlOperState is set
        to reset."
    ::= { rttMonCtrlOperEntry 3 }

rttMonCtrlOperOctetsInUse OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is the number of octets currently in use
        by this composite conceptual RTT row.  A composite
        conceptual row include the control, statistics, and
        history conceptual rows combined.  (All octets that
        are addressed via the rttMonCtrlAdminIndex in this
        mib.)"
    ::= { rttMonCtrlOperEntry 4 }

rttMonCtrlOperConnectionLostOccurred OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object will only change its value when the
        RttMonRttType is 'echo' or 'pathEcho'.

        This object is set to true when the RTT connection fails
        to be established or is lost, and set to false when a
        connection is reestablished.  When the RttMonRttType
        is 'pathEcho', connection loss applies only to the
        rttMonEchoAdminTargetAddress and not to intermediate
        hops to the Target.

        When this value changes and
        rttMonReactAdminConnectionEnable is true, a reaction
        will occur.

        If a trap is sent it is a
        rttMonConnectionChangeNotification.

        When this value changes and any one of the rttMonReactTable row
        has rttMonReactVar object value as 'connectionLoss(8)',
        a reaction may occur.

        If a trap is sent it is rttMonNotification with rttMonReactVar
        value of 'connectionLoss'."
    DEFVAL          { false }
    ::= { rttMonCtrlOperEntry 5 }

rttMonCtrlOperTimeoutOccurred OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object will change its value for all
        RttMonRttTypes.

        This object is set to true when an operation times out,
        and set to false when an operation completes under
        rttMonCtrlAdminTimeout.  When this value changes, a
        reaction may occur, as defined by
        rttMonReactAdminTimeoutEnable.

        When the RttMonRttType is 'pathEcho', this timeout
        applies only to the rttMonEchoAdminTargetAddress and
        not to intermediate hops to the Target.

        If a trap is sent it is a rttMonTimeoutNotification.

        When this value changes and any one of the rttMonReactTable
        row has rttMonReactVar object value as 'timeout(7)', a reaction
        may occur.

        If a trap is sent it is rttMonNotification with rttMonReactVar
        value of 'timeout'."
    DEFVAL          { false }
    ::= { rttMonCtrlOperEntry 6 }

rttMonCtrlOperOverThresholdOccurred OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object will change its value for all
        RttMonRttTypes.

        This object is changed by operation completion times over
        threshold, as defined by rttMonReactAdminThresholdType.
        When this value changes, a reaction may occur, as defined
        by rttMonReactAdminThresholdType.

        If a trap is sent it is a rttMonThresholdNotification.

        This object is set to true if the operation completion time
        exceeds the rttMonCtrlAdminThreshold and set to false when an
        operation completes under rttMonCtrlAdminThreshold. When this
        value changes, a reaction may occur, as defined by
        rttMonReactThresholdType.

        If a trap is sent it is rttMonNotification with rttMonReactVar
        value of 'rtt'."
    DEFVAL          { false }
    ::= { rttMonCtrlOperEntry 7 }

rttMonCtrlOperNumRtts OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This is the total number of probe operations that have
        been attempted.

        This value is incremented for each start of an RTT
        operation.  Thus when rttMonCtrlAdminRttType is set to
        'pathEcho' this value will be incremented by one and
        not for very every hop along the path.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object.

        This value is not effected by the rollover of a statistics
        hourly group."
    ::= { rttMonCtrlOperEntry 8 }

rttMonCtrlOperRttLife OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is decremented every second, until it
        reaches zero.  When the value of this object is zero
        RTT operations for this row are suspended.  This
        object will either reach zero by a countdown or
        it will transition to zero via setting the
        rttMonCtrlOperState.

        When this object reaches zero the agent needs to
        transition the rttMonCtrlOperState to 'inactive'.

        REMEMBER:  The value 2147483647 has a special
                   meaning.  When this object has the
                   value 2147483647, this object will
                   not decrement.  And thus the life
                   time will never.

        When the rttMonCtrlOperState object is 'active' and
        the rttMonReactTriggerOperState object transitions to
        'active' this object will not be updated with the
        current value of rttMonCrtlAdminRttLife object."
    ::= { rttMonCtrlOperEntry 9 }

rttMonCtrlOperState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        reset(1),
                        orderlyStop(2),
                        immediateStop(3),
                        pending(4),
                        inactive(5),
                        active(6),
                        restart(7)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The RttMonOperStatus object is used to
        manage the 'state' of the probe that is implementing
        conceptual RTT control row.

        This status object has six defined values:

        reset(1)          - reset this entry, transition
                            to 'pending'
        orderlyStop(2)    - shutdown this entry at the end
                             of the next RTT operation attempt,
                             transition to 'inactive'
        immediateStop(3)  - shutdown this entry immediately
                             (if possible), transition to
                             'inactive'
        pending(4)        - this value is not settable and
                             this conceptual RTT control row is
                             waiting for further control either
                             via the rttMonScheduleAdminTable
                             or the rttMonReactAdminTable/
                             rttMonReactTriggerAdminTable;
                             This object can transition to this
                             value via two mechanisms, first by
                             reseting this object, and second
                             by creating a conceptual Rtt control
                             row with the
                             rttMonScheduleAdminRttStartTime
                             object with the its special value
        inactive(5)       - this value is not settable and
                             this conceptual RTT control row is
                             waiting for further control via
                             the rttMonScheduleAdminTable;
                             This object can transition to this
                             value via two mechanisms, first by
                             setting this object to 'orderlyStop'
                             or 'immediateStop', second by
                             the rttMonCtrlOperRttLife object
                             reaching zero
        active(6)         - this value is not settable and
                             this conceptual RTT control row is
                             currently active
        restart(7)        - this value is only settable when the
                             state is active. It clears the data
                             of this entry and remain on active state.

        The probes action when this object is set to 'reset':
          -  all rows in rttMonStatsCaptureTable that relate to
              this conceptual RTT control row are destroyed and
              the indices are set to 1
          -  if rttMonStatisticsAdminNumHourGroups is not zero, a
              single new rttMonStatsCaptureTable row is created
          -  all rows in rttMonHistoryCaptureTable that relate
              to this RTT definition are destroyed and the indices
              are set to 1
          -  implied history used for timeout or threshold
              notification (see rttMonReactAdminThresholdType or
              rttMonReactThresholdType)
              is purged
          -  rttMonCtrlOperRttLife is set to
              rttMonScheduleAdminRttLife
          -  rttMonCtrlOperNumRtts is set to zero
          -  rttMonCtrlOperTimeoutOccurred,
              rttMonCtrlOperOverThresholdOccurred, and
              rttMonCtrlOperConnectionLostOccurred are set to
              false; if this causes a change in the value of
              either of these objects, resolution notifications
              will not occur
          -  the next RTT operation is controlled by the objects
              in the rttMonScheduleAdminTable or the
              rttMonReactAdminTable/rttMonReactTriggerAdminTable
          -  if the rttMonReactTriggerOperState is 'active', it
              will transition to 'pending'
          -  all rttMonReactTriggerAdminEntries pointing to
              this conceptual entry with their
              rttMonReactTriggerOperState object 'active',
              will transition their OperState to 'pending'
          -  all open connections must be maintained

        This can be used to synchronize various RTT
        definitions, so that the RTT requests occur
        simultaneously, or as simultaneously as possible.

        The probes action when this object transitions to
          'inactive' (via setting this object to 'orderlyStop'
          or 'immediateStop' or by rttMonCtrlOperRttLife
          reaching zero):
          -  all statistics and history collection information
              table entries will be closed and kept
          -  implied history used for timeout or threshold
              notification (see rttMonReactAdminThresholdType or
              rttMonReactThresholdType)
              is purged
          -  rttMonCtrlOperTimeoutOccurred,
              rttMonCtrlOperOverThresholdOccurred, and
              rttMonCtrlOperConnectionLostOccurred are set to
              false; if this causes a change in the value of
              either of these objects, resolution notifications
              will not occur.
          -  the next RTT request is controlled by the objects
              in the rttMonScheduleAdminTable
          -  if the rttMonReactTriggerOperState is 'active', it
              will transition to 'pending' (this denotes that
              the Trigger will be ready the next time this
              object goes active)
          -  all rttMonReactTriggerAdminEntries pointing to
              this conceptual entry with their
              rttMonReactTriggerOperState object 'active',
              will transition their OperState to 'pending'
          -  all open connections are to be closed and cleanup.

                     rttMonCtrlOperState
                            STATE
                  +-------------------------------------------+
                  |      A       |       B      |      C      |
        ACTION       |  'pending'   |  'inactive'  |   'active'  |
        +----------------+--------------+--------------+-------------+
        | OperState set  |    noError   |inconsistent- |   noError   |
        |  to 'reset'    |              | Value        |             |
        |                |    -> A      |              |   -> A      |
        +----------------+--------------+--------------+-------------+
        | OperState set  |    noError   |    noError   |   noError   |
        |to 'orderlyStop'|    -> B      |    -> B      |   -> B      |
        |     or to      |              |              |             |
        |'immediateStop' |              |              |             |
        +----------------+--------------+--------------+-------------+
        |  Event causes  |    -> C      |    -> B      |   -> C      |
        | Trigger State  |              |              |   see (3)   |
        | to transition  |              |              |             |
        | to 'active'    |              |              |             |
        +----------------+--------------+--------------+-------------+
        | AdminStatus    |    -> C      |    -> C      |   see (1)   |
        | transitions to |              |              |             |
        | 'active' &     |              |              |             |
        | RttStartTime is|              |              |             |
        | special value  |              |              |             |
        | of one.        |              |              |             |
        +----------------+--------------+--------------+-------------+
        | AdminStatus    |    -> A      |    -> A      |   see (1)   |
        | transitions to |              |              |             |
        | 'active' &     |              |              |             |
        | RttStartTime is|              |              |             |
        | special value  |              |              |             |
        | of less than   |              |              |             |
        | current time,  |              |              |             |
        | excluding one. |              |              |             |
        +----------------+--------------+--------------+-------------+
        | AdminStatus    |    -> A      |    -> B      |   see (2)   |
        | transitions to |              |              |             |
        | 'notInService' |              |              |             |
        +----------------+--------------+--------------+-------------+
        | AdminStatus    |    -> B      |    -> B      |   -> B      |
        | transitions to |              |              |             |
        | 'delete'       |              |              |             |
        +----------------+--------------+--------------+-------------+
        | AdminStatus is |    -> C      |    -> C      |   -> C      |
        | 'active' & the |              |              |   see (3)   |
        | RttStartTime   |              |              |             |
        | arrives        |              |              |             |
        +----------------+--------------+--------------+-------------+
        |   RowAgeout    |    -> B      |    -> B      |   -> B      |
        |    expires     |              |              |             |
        +----------------+--------------+--------------+-------------+
        |  OperRttLife   |    N/A       |    N/A       |   -> B      |
        | counts down to |              |              |             |
        | zero           |              |              |             |
        +----------------+--------------+--------------+-------------+

        (1) - rttMonCtrlOperState must have transitioned to 'inactive'
        or 'pending' before the rttMonCtrlAdminStatus can
        transition to 'active'.  See (2).
        (2) - rttMonCtrlAdminStatus cannot transition to 'notInService'
        unless rttMonCtrlOperState has been previously forced
        to 'inactive' or 'pending'.
        (3) - when this happens the rttMonCtrlOperRttLife will not
        be updated with the rttMonCtrlAdminRttLife.

        NOTE:  In order for all objects in a PDU to be set
               at the same time, this object can not be
               part of a multi-bound PDU."
    ::= { rttMonCtrlOperEntry 10 }

rttMonCtrlOperVerifyErrorOccurred OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is true if rttMonCtrlAdminVerifyData is
        set to true and data corruption occurs."
    DEFVAL          { false }
    ::= { rttMonCtrlOperEntry 11 }


-- Latest RTT operation values

rttMonLatestRttOperTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonLatestRttOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of which contains the status of latest RTT
        operation.

        When the RttMonRttType is 'pathEcho', operations
        performed to the hops along the path will be recorded
        in this table.

        This table augments the RTT definition table,
        rttMonCtrlAdminTable."
    ::= { rttMonCtrl 10 }

rttMonLatestRttOperEntry OBJECT-TYPE
    SYNTAX          RttMonLatestRttOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that record the latest RTT operation."
    AUGMENTS           { rttMonCtrlAdminEntry  }
    ::= { rttMonLatestRttOperTable 1 }

RttMonLatestRttOperEntry ::= SEQUENCE {
        rttMonLatestRttOperCompletionTime    Gauge32,
        rttMonLatestRttOperSense             RttResponseSense,
        rttMonLatestRttOperApplSpecificSense Integer32,
        rttMonLatestRttOperSenseDescription  DisplayString,
        rttMonLatestRttOperTime              TimeStamp,
        rttMonLatestRttOperAddress           RttMonTargetAddress
}

rttMonLatestRttOperCompletionTime OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds/microseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The completion time of the latest RTT operation
        successfully completed.

        The unit of this object will be microsecond when
        rttMonCtrlAdminRttType is set to 'jitter' and
        rttMonEchoAdminPrecision is set to 'microsecond'.
        Otherwise, the unit of this object will be millisecond."
    ::= { rttMonLatestRttOperEntry 1 }

rttMonLatestRttOperSense OBJECT-TYPE
    SYNTAX          RttResponseSense
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A sense code for the completion status of the latest
        RTT operation."
    ::= { rttMonLatestRttOperEntry 2 }

rttMonLatestRttOperApplSpecificSense OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An application specific sense code for the
        completion status of the latest RTT operation.  This
        object will only be valid when the
        rttMonLatestRttOperSense object is set to
        'applicationSpecific'.  Otherwise, this object's
        value is not valid."
    ::= { rttMonLatestRttOperEntry 3 }

rttMonLatestRttOperSenseDescription OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A sense description for the completion status of
        the latest RTT operation when the
        rttMonLatestRttOperSense object is set to
        'applicationSpecific'."
    ::= { rttMonLatestRttOperEntry 4 }

rttMonLatestRttOperTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the agent system time at the time of the
        latest RTT operation."
    ::= { rttMonLatestRttOperEntry 5 }

rttMonLatestRttOperAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'echo', 'pathEcho', 'udpEcho',
        'tcpConnect', 'dns' and 'dlsw' this is a string which specifies
        the address of the target for this RTT operation.  When the
        RttMonRttType is not one of these types this object will
        be null.

        This address will be the address of the hop along the
        path to the rttMonEchoAdminTargetAddress address,
        including rttMonEchoAdminTargetAddress address, or just
        the rttMonEchoAdminTargetAddress address, when the
        path information is not collected.  This behavior is
        defined by the rttMonCtrlAdminRttType object.

        The interpretation of this string depends on the type
        of RTT operation selected, as specified by the
        rttMonEchoAdminProtocol object.

        See rttMonEchoAdminTargetAddress for a complete
        description."
    ::= { rttMonLatestRttOperEntry 6 }


-- LatestHTTPOper Table

rttMonLatestHTTPOperTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonLatestHTTPOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table which contains the status of latest HTTP RTT
        operation."
    ::= { rttMonLatestOper 1 }

rttMonLatestHTTPOperEntry OBJECT-TYPE
    SYNTAX          RttMonLatestHTTPOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that record the latest HTTP RTT
        operation. This entry is created automatically after the
        rttMonCtrlAdminEntry is created. Also the entry is
        automatically deleted when rttMonCtrlAdminEntry is deleted."
    INDEX           { rttMonCtrlAdminIndex }
    ::= { rttMonLatestHTTPOperTable 1 }

RttMonLatestHTTPOperEntry ::= SEQUENCE {
        rttMonLatestHTTPOperRTT               Gauge32,
        rttMonLatestHTTPOperDNSRTT            Gauge32,
        rttMonLatestHTTPOperTCPConnectRTT     Gauge32,
        rttMonLatestHTTPOperTransactionRTT    Gauge32,
        rttMonLatestHTTPOperMessageBodyOctets Gauge32,
        rttMonLatestHTTPOperSense             RttResponseSense,
        rttMonLatestHTTPErrorSenseDescription DisplayString
}

rttMonLatestHTTPOperRTT OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Round Trip Time taken to perform HTTP operation. This value
        is the sum of DNSRTT, TCPConnectRTT and TransactionRTT."
    ::= { rttMonLatestHTTPOperEntry 1 }

rttMonLatestHTTPOperDNSRTT OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Round Trip Time taken to perform DNS query within the
        HTTP operation. If an IP Address is specified in the URL,
        then DNSRTT is 0."
    ::= { rttMonLatestHTTPOperEntry 2 }

rttMonLatestHTTPOperTCPConnectRTT OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Round Trip Time taken to connect to the HTTP server."
    ::= { rttMonLatestHTTPOperEntry 3 }

rttMonLatestHTTPOperTransactionRTT OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Round Trip Time taken to download the object specified by
        the URL."
    ::= { rttMonLatestHTTPOperEntry 4 }

rttMonLatestHTTPOperMessageBodyOctets OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The size of the message body received as a response to
        the HTTP request."
    ::= { rttMonLatestHTTPOperEntry 5 }

rttMonLatestHTTPOperSense OBJECT-TYPE
    SYNTAX          RttResponseSense
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An application specific sense code for the completion status
        of the latest RTT operation."
    ::= { rttMonLatestHTTPOperEntry 6 }

rttMonLatestHTTPErrorSenseDescription OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An sense description for the completion status
        of the latest RTT operation."
    ::= { rttMonLatestHTTPOperEntry 7 }


-- LatestJitterOper Table

rttMonLatestJitterOperTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonLatestJitterOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table which contains the status of latest Jitter
        operation."
    ::= { rttMonLatestOper 2 }

rttMonLatestJitterOperEntry OBJECT-TYPE
    SYNTAX          RttMonLatestJitterOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that record the latest Jitter
        operation."
    INDEX           { rttMonCtrlAdminIndex }
    ::= { rttMonLatestJitterOperTable 1 }

RttMonLatestJitterOperEntry ::= SEQUENCE {
        rttMonLatestJitterOperNumOfRTT            Gauge32,
        rttMonLatestJitterOperRTTSum              Gauge32,
        rttMonLatestJitterOperRTTSum2             Gauge32,
        rttMonLatestJitterOperRTTMin              Gauge32,
        rttMonLatestJitterOperRTTMax              Gauge32,
        rttMonLatestJitterOperMinOfPositivesSD    Gauge32,
        rttMonLatestJitterOperMaxOfPositivesSD    Gauge32,
        rttMonLatestJitterOperNumOfPositivesSD    Gauge32,
        rttMonLatestJitterOperSumOfPositivesSD    Gauge32,
        rttMonLatestJitterOperSum2PositivesSD     Gauge32,
        rttMonLatestJitterOperMinOfNegativesSD    Gauge32,
        rttMonLatestJitterOperMaxOfNegativesSD    Gauge32,
        rttMonLatestJitterOperNumOfNegativesSD    Gauge32,
        rttMonLatestJitterOperSumOfNegativesSD    Gauge32,
        rttMonLatestJitterOperSum2NegativesSD     Gauge32,
        rttMonLatestJitterOperMinOfPositivesDS    Gauge32,
        rttMonLatestJitterOperMaxOfPositivesDS    Gauge32,
        rttMonLatestJitterOperNumOfPositivesDS    Gauge32,
        rttMonLatestJitterOperSumOfPositivesDS    Gauge32,
        rttMonLatestJitterOperSum2PositivesDS     Gauge32,
        rttMonLatestJitterOperMinOfNegativesDS    Gauge32,
        rttMonLatestJitterOperMaxOfNegativesDS    Gauge32,
        rttMonLatestJitterOperNumOfNegativesDS    Gauge32,
        rttMonLatestJitterOperSumOfNegativesDS    Gauge32,
        rttMonLatestJitterOperSum2NegativesDS     Gauge32,
        rttMonLatestJitterOperPacketLossSD        Gauge32,
        rttMonLatestJitterOperPacketLossDS        Gauge32,
        rttMonLatestJitterOperPacketOutOfSequence Gauge32,
        rttMonLatestJitterOperPacketMIA           Gauge32,
        rttMonLatestJitterOperPacketLateArrival   Gauge32,
        rttMonLatestJitterOperSense               RttResponseSense,
        rttMonLatestJitterErrorSenseDescription   DisplayString,
        rttMonLatestJitterOperOWSumSD             Gauge32,
        rttMonLatestJitterOperOWSum2SD            Gauge32,
        rttMonLatestJitterOperOWMinSD             Gauge32,
        rttMonLatestJitterOperOWMaxSD             Gauge32,
        rttMonLatestJitterOperOWSumDS             Gauge32,
        rttMonLatestJitterOperOWSum2DS            Gauge32,
        rttMonLatestJitterOperOWMinDS             Gauge32,
        rttMonLatestJitterOperOWMaxDS             Gauge32,
        rttMonLatestJitterOperNumOfOW             Gauge32,
        rttMonLatestJitterOperMOS                 Gauge32,
        rttMonLatestJitterOperICPIF               Gauge32,
        rttMonLatestJitterOperIAJOut              Gauge32,
        rttMonLatestJitterOperIAJIn               Gauge32,
        rttMonLatestJitterOperAvgJitter           Gauge32,
        rttMonLatestJitterOperAvgSDJ              Gauge32,
        rttMonLatestJitterOperAvgDSJ              Gauge32,
        rttMonLatestJitterOperOWAvgSD             Gauge32,
        rttMonLatestJitterOperOWAvgDS             Gauge32,
        rttMonLatestJitterOperNTPState            INTEGER,
        rttMonLatestJitterOperUnSyncRTs           Counter32,
        rttMonLatestJitterOperRTTSumHigh          Gauge32,
        rttMonLatestJitterOperRTTSum2High         Gauge32,
        rttMonLatestJitterOperOWSumSDHigh         Gauge32,
        rttMonLatestJitterOperOWSum2SDHigh        Gauge32,
        rttMonLatestJitterOperOWSumDSHigh         Gauge32,
        rttMonLatestJitterOperOWSum2DSHigh        Gauge32,
        rttMonLatestJitterOperNumOverThresh       Gauge32
}

rttMonLatestJitterOperNumOfRTT OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT's that were successfully measured."
    ::= { rttMonLatestJitterOperEntry 1 }

rttMonLatestJitterOperRTTSum OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of Jitter RTT's that are successfully measured (low
        order 32 bits). The high order 32 bits are stored in
        rttMonLatestJitterOperRTTSumHigh."
    ::= { rttMonLatestJitterOperEntry 2 }

rttMonLatestJitterOperRTTSum2 OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's that are successfully measured (low
        order 32 bits). The high order 32 bits are stored in
        rttMonLatestJitterOperRTTSum2High."
    ::= { rttMonLatestJitterOperEntry 3 }

rttMonLatestJitterOperRTTMin OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of RTT's that were successfully measured."
    ::= { rttMonLatestJitterOperEntry 4 }

rttMonLatestJitterOperRTTMax OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of RTT's that were successfully measured."
    ::= { rttMonLatestJitterOperEntry 5 }

rttMonLatestJitterOperMinOfPositivesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all positive jitter values from packets sent
        from source to destination."
    ::= { rttMonLatestJitterOperEntry 6 }

rttMonLatestJitterOperMaxOfPositivesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all positive jitter values from packets sent
        from source to destination."
    ::= { rttMonLatestJitterOperEntry 7 }

rttMonLatestJitterOperNumOfPositivesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of all positive jitter values from packets
        sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 8 }

rttMonLatestJitterOperSumOfPositivesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's of all positive jitter values from packets
        sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 9 }

rttMonLatestJitterOperSum2PositivesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of square of RTT's of all positive jitter values from
        packets sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 10 }

rttMonLatestJitterOperMinOfNegativesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of absolute values of all negative jitter values
        from packets sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 11 }

rttMonLatestJitterOperMaxOfNegativesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of absolute values of all negative jitter values
        from packets sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 12 }

rttMonLatestJitterOperNumOfNegativesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of number of all negative jitter values from packets
        sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 13 }

rttMonLatestJitterOperSumOfNegativesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of all negative jitter values from packets
        sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 14 }

rttMonLatestJitterOperSum2NegativesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of square of RTT's of all negative jitter values from
        packets sent from source to destination."
    ::= { rttMonLatestJitterOperEntry 15 }

rttMonLatestJitterOperMinOfPositivesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all positive jitter values from packets sent
        from destination to source."
    ::= { rttMonLatestJitterOperEntry 16 }

rttMonLatestJitterOperMaxOfPositivesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all positive jitter values from packets sent
        from destination to source."
    ::= { rttMonLatestJitterOperEntry 17 }

rttMonLatestJitterOperNumOfPositivesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of number of all positive jitter values from packets
        sent from destination to source."
    ::= { rttMonLatestJitterOperEntry 18 }

rttMonLatestJitterOperSumOfPositivesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's of all positive jitter values from packets
        sent from destination to source."
    ::= { rttMonLatestJitterOperEntry 19 }

rttMonLatestJitterOperSum2PositivesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's of all positive jitter values from
        packets sent from destination to source."
    ::= { rttMonLatestJitterOperEntry 20 }

rttMonLatestJitterOperMinOfNegativesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all negative jitter values from packets sent
        from destination to source."
    ::= { rttMonLatestJitterOperEntry 21 }

rttMonLatestJitterOperMaxOfNegativesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all negative jitter values from packets sent
        from destination to source."
    ::= { rttMonLatestJitterOperEntry 22 }

rttMonLatestJitterOperNumOfNegativesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of number of all negative jitter values from packets
        sent from destination to source."
    ::= { rttMonLatestJitterOperEntry 23 }

rttMonLatestJitterOperSumOfNegativesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's of all negative jitter values from packets
        sent from destination to source."
    ::= { rttMonLatestJitterOperEntry 24 }

rttMonLatestJitterOperSum2NegativesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's of all negative jitter values from
        packets sent from destination to source."
    ::= { rttMonLatestJitterOperEntry 25 }

rttMonLatestJitterOperPacketLossSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets lost when sent from source to
        destination."
    ::= { rttMonLatestJitterOperEntry 26 }

rttMonLatestJitterOperPacketLossDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets lost when sent from destination to
        source."
    ::= { rttMonLatestJitterOperEntry 27 }

rttMonLatestJitterOperPacketOutOfSequence OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets arrived out of sequence."
    ::= { rttMonLatestJitterOperEntry 28 }

rttMonLatestJitterOperPacketMIA OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets that are lost for which we cannot
        determine the direction."
    ::= { rttMonLatestJitterOperEntry 29 }

rttMonLatestJitterOperPacketLateArrival OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets that arrived after the timeout."
    ::= { rttMonLatestJitterOperEntry 30 }

rttMonLatestJitterOperSense OBJECT-TYPE
    SYNTAX          RttResponseSense
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An application specific sense code for the completion status
        of the latest Jitter RTT operation."
    ::= { rttMonLatestJitterOperEntry 31 }

rttMonLatestJitterErrorSenseDescription OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An sense description for the completion status
        of the latest Jitter RTT operation."
    ::= { rttMonLatestJitterOperEntry 32 }

rttMonLatestJitterOperOWSumSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way latency from source to destination (low
        order 32 bits). The high order 32 bits are stored in
        rttMonLatestJitterOperOWSumSDHigh."
    ::= { rttMonLatestJitterOperEntry 33 }

rttMonLatestJitterOperOWSum2SD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way latency from source to
        destination (low order 32 bits). The high order 32 bits are
        stored in rttMonLatestJitterOperOWSum2SDHigh."
    ::= { rttMonLatestJitterOperEntry 34 }

rttMonLatestJitterOperOWMinSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all one way latency from source to
        destination."
    ::= { rttMonLatestJitterOperEntry 35 }

rttMonLatestJitterOperOWMaxSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all one way latency from source to
        destination."
    ::= { rttMonLatestJitterOperEntry 36 }

rttMonLatestJitterOperOWSumDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way latency from destination to source (low
        order 32 bits). The high order 32 bits are stored in
        rttMonLatestJitterOperOWSumDSHigh."
    ::= { rttMonLatestJitterOperEntry 37 }

rttMonLatestJitterOperOWSum2DS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way latency from destination to
        source (low order 32 bits). The high order 32 bits are stored in
        rttMonLatestJitterOperOWSum2DSHigh."
    ::= { rttMonLatestJitterOperEntry 38 }

rttMonLatestJitterOperOWMinDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all one way latency from destination to
        source."
    ::= { rttMonLatestJitterOperEntry 39 }

rttMonLatestJitterOperOWMaxDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all one way latency from destination to
        source."
    ::= { rttMonLatestJitterOperEntry 40 }

rttMonLatestJitterOperNumOfOW OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of successful one way latency measurements."
    ::= { rttMonLatestJitterOperEntry 41 }

rttMonLatestJitterOperMOS OBJECT-TYPE
    SYNTAX          Gauge32 (0 | 100..500)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The MOS value for the latest jitter operation in hundreds.
        This value will be 0 if
         - rttMonEchoAdminCodecType of the operation is notApplicable
         - the operation is not started
         - the operation is started but failed
        This value will be 1 for packet loss of 10% or more."
    ::= { rttMonLatestJitterOperEntry 42 }

rttMonLatestJitterOperICPIF OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Represents ICPIF value for the latest jitter operation.

        This value will be 93 for packet loss of 10% or more."
    ::= { rttMonLatestJitterOperEntry 43 }

rttMonLatestJitterOperIAJOut OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Interarrival Jitter (RC1889) at responder."
    REFERENCE
        "Refer to the following documents for the definition: RFC 1889"
    ::= { rttMonLatestJitterOperEntry 44 }

rttMonLatestJitterOperIAJIn OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Interarrival Jitter (RFC1889) at source."
    REFERENCE
        "Refer to the following documents for the definition: RFC 1889"
    ::= { rttMonLatestJitterOperEntry 45 }

rttMonLatestJitterOperAvgJitter OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average of positive and negative jitter
        values in SD and DS direction for latest operation."
    ::= { rttMonLatestJitterOperEntry 46 }

rttMonLatestJitterOperAvgSDJ OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average of positive and negative jitter values
        from source to destination for latest operation."
    ::= { rttMonLatestJitterOperEntry 47 }

rttMonLatestJitterOperAvgDSJ OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average of positive and negative jitter values
        from destination to source for latest operation."
    ::= { rttMonLatestJitterOperEntry 48 }

rttMonLatestJitterOperOWAvgSD OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average latency value from source to destination."
    ::= { rttMonLatestJitterOperEntry 49 }

rttMonLatestJitterOperOWAvgDS OBJECT-TYPE
    SYNTAX          Gauge32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average latency value from destination to source."
    ::= { rttMonLatestJitterOperEntry 50 }

rttMonLatestJitterOperNTPState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        sync(1),
                        outOfSync(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A value of sync(1) means sender and responder was in sync
        with NTP. The NTP sync means the total of NTP offset
        on sender and responder is within configured tolerance level."
    ::= { rttMonLatestJitterOperEntry 51 }

rttMonLatestJitterOperUnSyncRTs OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT operations that have completed with
        sender and responder out of sync with NTP. The NTP sync means
        the total of NTP offset on sender and responder is within
        configured tolerance level."
    ::= { rttMonLatestJitterOperEntry 52 }

rttMonLatestJitterOperRTTSumHigh OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of Jitter RTT's that are successfully measured.
        (high order 32 bits). The low order 32 bits are stored
        in rttMonLatestJitterOperRTTSum."
    ::= { rttMonLatestJitterOperEntry 53 }

rttMonLatestJitterOperRTTSum2High OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's that are successfully measured
        (high order 32 bits). The low order 32 bits are stored in
        rttMonLatestJitterOperRTTSum2."
    ::= { rttMonLatestJitterOperEntry 54 }

rttMonLatestJitterOperOWSumSDHigh OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way latency from source to destination
        (high order 32 bits). The low order 32 bits are stored in
        rttMonLatestJitterOperOWSumSD."
    ::= { rttMonLatestJitterOperEntry 55 }

rttMonLatestJitterOperOWSum2SDHigh OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way latency from source to
        destination (high order 32 bits). The low order 32 bits
        are stored in rttMonLatestJitterOperOWSum2SD."
    ::= { rttMonLatestJitterOperEntry 56 }

rttMonLatestJitterOperOWSumDSHigh OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way latency from destination to source
        (high order 32 bits). The low order 32 bits are stored
        in rttMonLatestJitterOperOWSumDS."
    ::= { rttMonLatestJitterOperEntry 57 }

rttMonLatestJitterOperOWSum2DSHigh OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way latency from destination to
        source (high order 32 bits). The low order 32 bits are
        stored in rttMonLatestJitterOperOWSum2DS."
    ::= { rttMonLatestJitterOperEntry 58 }

rttMonLatestJitterOperNumOverThresh OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of RTTs that were over
        the threshold value."
    ::= { rttMonLatestJitterOperEntry 59 }


-- Reaction Trigger Administration Table

rttMonReactTriggerAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonReactTriggerAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of which contains the list of conceptual RTT
        control rows that will start to collect data when a
        reaction condition is violated and when
        rttMonReactAdminActionType is set to one of the
        following:
          -  triggerOnly
          -  trapAndTrigger
          -  nmvtAndTrigger
          -  trapNmvtAndTrigger
        or when a reaction condition is violated and when any of the
        row in rttMonReactTable has rttMonReactActionType as one of
        the following:
          - triggerOnly
          - trapAndTrigger

        The goal of this table is to define one or more
        additional conceptual RTT control rows that will become
        active and start to collect additional history and
        statistics (depending on the rows configuration values),
        when a problem has been detected.

        If the conceptual RTT control row is undefined, and a
        trigger occurs, no action will take place.

        If the conceptual RTT control row is scheduled to start
        at a later time, triggering that row will have no effect.

        If the conceptual RTT control row is currently active,
        triggering that row will have no effect on that row, but
        the rttMonReactTriggerOperState object will transition to
        'active'.

        An entry in this table can only be triggered when
        it is not currently in a triggered state.  The
        object rttMonReactTriggerOperState will
        reflect the state of each entry in this table."
    ::= { rttMonCtrl 11 }

rttMonReactTriggerAdminEntry OBJECT-TYPE
    SYNTAX          RttMonReactTriggerAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that will be triggered when
        a reaction condition is violated."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonReactTriggerAdminRttMonCtrlAdminIndex
                    }
    ::= { rttMonReactTriggerAdminTable 1 }

RttMonReactTriggerAdminEntry ::= SEQUENCE {
        rttMonReactTriggerAdminRttMonCtrlAdminIndex Integer32,
        rttMonReactTriggerAdminStatus               RowStatus
}

rttMonReactTriggerAdminRttMonCtrlAdminIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object points to a single conceptual Rtt control
        row.  If this row does not exist and this value is
        triggered no action will result.

        The conceptual Rtt control row will be triggered for the
        rttMonCtrlOperRttLife length.  If this conceptual Rtt
        control row is already active, rttMonCtrlOperRttLife will
        not be updated, and its life will continue as previously
        defined."
    ::= { rttMonReactTriggerAdminEntry 1 }

rttMonReactTriggerAdminStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to create Trigger entries."
    ::= { rttMonReactTriggerAdminEntry 2 }


-- Reaction Violation Trigger Operational State Table

rttMonReactTriggerOperTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonReactTriggerOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of which contains the operational state
        of each entry in the
        rttMonReactTriggerAdminTable.

        This table augments the RTT trigger
        definition table, rttMonReactTriggerAdminTable."
    ::= { rttMonCtrl 12 }

rttMonReactTriggerOperEntry OBJECT-TYPE
    SYNTAX          RttMonReactTriggerOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of state objects for the
        rttMonReactTriggerAdminTable."
    AUGMENTS           { rttMonReactTriggerAdminEntry  }
    ::= { rttMonReactTriggerOperTable 1 }

RttMonReactTriggerOperEntry ::= SEQUENCE {
        rttMonReactTriggerOperState INTEGER
}

rttMonReactTriggerOperState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        active(1),
                        pending(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object takes on the value active
        when its associated entry in the
        rttMonReactTriggerAdminTable has been
        triggered.

        When the associated entry in the
        rttMonReactTriggerAdminTable is not under
        a trigger state, this object will be
        pending.

        When this object is in the active state
        this entry can not be retriggered."
    ::= { rttMonReactTriggerOperEntry 1 }


-- EchoPath Admin Table

rttMonEchoPathAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonEchoPathAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table to store the hop addresses in a Loose Source Routing
        path. Response times are computed along the specified path
        using ping.

        This maximum table size is limited by the size of the
        maximum number of hop addresses that can fit in an IP header,
        which is 8. The object rttMonEchoPathAdminEntry will reflect
        this tables maximum number of entries.

        This table is coupled with rttMonCtrlAdminStatus."
    ::= { rttMonCtrl 13 }

rttMonEchoPathAdminEntry OBJECT-TYPE
    SYNTAX          RttMonEchoPathAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define intermediate hop's IP Address.

        This entry can be added only if the rttMonCtrlAdminRttType is
        'echo'. The entry gets deleted when the corresponding RTR entry,
        which has an index of rttMonCtrlAdminIndex, is deleted."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonEchoPathAdminHopIndex
                    }
    ::= { rttMonEchoPathAdminTable 1 }

RttMonEchoPathAdminEntry ::= SEQUENCE {
        rttMonEchoPathAdminHopIndex   Integer32,
        rttMonEchoPathAdminHopAddress RttMonTargetAddress
}

rttMonEchoPathAdminHopIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..8)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Uniquely identifies a row in the rttMonEchoPathAdminTable.
        This number represents the hop address number in a specific
        ping path. All the indicies should start from 1 and must be
        contiguous ie., entries should be
        (say rttMonCtrlAdminIndex = 1)
        1.1, 1.2, 1.3, they cannot be 1.1, 1.2, 1.4"
    ::= { rttMonEchoPathAdminEntry 1 }

rttMonEchoPathAdminHopAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the address of an intermediate hop's
        IP Address for a RTT 'echo' operation"
    ::= { rttMonEchoPathAdminEntry 2 }



rttMonGrpScheduleAdminTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonGrpScheduleAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of Round Trip Time (RTT) monitoring group scheduling
        specific definitions.
        This table is used to create a conceptual group scheduling
        control row. The entries in this control row contain objects
        used to define group schedule configuration parameters.

        The objects of this table will be used to schedule a group of
        probes identified by the conceptual rows of the
        rttMonCtrlAdminTable."
    ::= { rttMonCtrl 14 }

rttMonGrpScheduleAdminEntry OBJECT-TYPE
    SYNTAX          RttMonGrpScheduleAdminEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define a conceptual group scheduling
        control row."
    INDEX           { rttMonGrpScheduleAdminIndex }
    ::= { rttMonGrpScheduleAdminTable 1 }

RttMonGrpScheduleAdminEntry ::= SEQUENCE {
        rttMonGrpScheduleAdminIndex      Integer32,
        rttMonGrpScheduleAdminProbes     DisplayString,
        rttMonGrpScheduleAdminPeriod     Integer32,
        rttMonGrpScheduleAdminFrequency  Integer32,
        rttMonGrpScheduleAdminLife       Integer32,
        rttMonGrpScheduleAdminAgeout     Integer32,
        rttMonGrpScheduleAdminStatus     RowStatus,
        rttMonGrpScheduleAdminFreqMax    Integer32,
        rttMonGrpScheduleAdminFreqMin    Integer32,
        rttMonGrpScheduleAdminStartTime  Integer32,
        rttMonGrpScheduleAdminAdd        TruthValue,
        rttMonGrpScheduleAdminDelete     TruthValue,
        rttMonGrpScheduleAdminReset      TruthValue,
        rttMonGrpScheduleAdminStartType  RttMonScheduleStartType,
        rttMonGrpScheduleAdminStartDelay Integer32
}

rttMonGrpScheduleAdminIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Uniquely identifies a row in the
        rttMonGrpScheduleAdminTable.

        This is a pseudo-random number selected by the management
        station when creating a row via the
        rttMonGrpScheduleAdminStatus object. If the pseudo-random
        number is already in use an 'inconsistentValue' return code
        will be returned when set operation is attempted."
    ::= { rttMonGrpScheduleAdminEntry 1 }

rttMonGrpScheduleAdminProbes OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..200))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which holds the different probes which are to be
        group scheduled. The probes can be specified in the following
        forms.
        (a) Individual ID's with comma separated as 23,45,34.
        (b) Range form including hyphens with multiple ranges being
            separated by a comma as 1-10,12-34.
        (c) Mix of the above two forms as 1,2,4-10,12,15,19-25.

        Any whitespace in the string is considered an error. Duplicates
        and overlapping ranges as an example 1,2,3,2-10 are considered
        fine. For a single range like 1-20 the upper value (in this
        example 20) must be greater than lower value (1), otherwise it's
        treated as an error. The agent will not normalize the list e.g.,
        it will not change 1,2,1-10 or even 1,2,3,4,5,6.. to 1-10."
    DEFVAL          { "" }
    ::= { rttMonGrpScheduleAdminEntry 2 }

rttMonGrpScheduleAdminPeriod OBJECT-TYPE
    SYNTAX          Integer32 (0..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the time duration over which all the probes have to
        be scheduled."
    DEFVAL          { 0 }
    ::= { rttMonGrpScheduleAdminEntry 3 }

rttMonGrpScheduleAdminFrequency OBJECT-TYPE
    SYNTAX          Integer32 (0..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the duration between initiating each RTT
        operation for all the probes specified in the group.

        The value of this object is only effective when both
        rttMonGrpScheduleAdminFreqMax and rttMonGrpScheduleAdminFreqMin
        have zero values."
    DEFVAL          { 0 }
    ::= { rttMonGrpScheduleAdminEntry 4 }

rttMonGrpScheduleAdminLife OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the life of all the probes included in
        the object rttMonGrpScheduleAdminProbes, that are getting group
        scheduled. This value will be placed into
        rttMonScheduleAdminRttLife object for each of the probes listed
        in rttMonGrpScheduleAdminProbes when this conceptual control
        row becomes 'active'.

        The value 2147483647 has a special meaning. When this object is
        set to 2147483647, the rttMonCtrlOperRttLife object for all the
        probes listed in rttMonGrpScheduleAdminProbes,  will not
        decrement. And thus the life time of the probes will never end."
    DEFVAL          { 3600 }
    ::= { rttMonGrpScheduleAdminEntry 5 }

rttMonGrpScheduleAdminAgeout OBJECT-TYPE
    SYNTAX          Integer32 (0..2073600)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the ageout value of all the probes
        included in the object rttMonGrpScheduleAdminProbes, that are
        getting group scheduled. This value will be placed into
        rttMonScheduleAdminConceptRowAgeout object for each of the
        probes listed in rttMonGrpScheduleAdminProbes when this
        conceptual control row becomes 'active'.

        When this value is set to zero, the probes listed in
        rttMonGrpScheduleAdminProbes, will never ageout."
    DEFVAL          { 3600 }
    ::= { rttMonGrpScheduleAdminEntry 6 }

rttMonGrpScheduleAdminStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of the conceptual RTT group schedule control row.

        In order for this object to become active, the following
        row objects must be defined:
         - rttMonGrpScheduleAdminProbes
         - rttMonGrpScheduleAdminPeriod
        All other objects can assume default values.

        The conceptual RTT group schedule control row objects cannot be
        modified once this conceptual RTT group schedule control row
        has been created.
        Once this object is in 'active' status, it cannot be set to
        'notInService'.
        When this object moves to 'active' state it will schedule the
        probes of the rttMonCtrlAdminTable which had been created using
        'createAndWait'.

        This object can be set to 'destroy' from any value at any time.
        When this object is set to 'destroy' it will stop all the probes
        of the rttMonCtrlAdminTable, which had been group scheduled
        by it earlier, before destroying the RTT group schedule
        control row."
    ::= { rttMonGrpScheduleAdminEntry 7 }

rttMonGrpScheduleAdminFreqMax OBJECT-TYPE
    SYNTAX          Integer32 (0..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the max duration between initiating each RTT
        operation for all the probes specified in the group.

        If this is 0 and rttMonGrpScheduleAdminFreqMin is also 0
        then rttMonGrpScheduleAdminFrequency becomes the fixed
        frequency."
    DEFVAL          { 0 }
    ::= { rttMonGrpScheduleAdminEntry 8 }

rttMonGrpScheduleAdminFreqMin OBJECT-TYPE
    SYNTAX          Integer32 (0..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the min duration between initiating each RTT
        operation for all the probes specified in the group.

        The value of this object cannot be greater than the value of
        rttMonGrpScheduleAdminFreqMax.

        If this is 0 and rttMonGrpScheduleAdminFreqMax is 0 then
        rttMonGrpScheduleAdminFrequency becomes the fixed frequency."
    DEFVAL          { 0 }
    ::= { rttMonGrpScheduleAdminEntry 9 }

rttMonGrpScheduleAdminStartTime OBJECT-TYPE
    SYNTAX          Integer32 (0..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This is the time in seconds after which the member probes of
        this group specified in rttMonGrpScheduleAdminProbes will
        transition to active state

        If rttMonScheduleAdminStartType is specified with this
        object then rttMonScheduleAdminRttStartTime
        is ignored and group start time is configured based on
        rttMonScheduleAdminStartType."
    DEFVAL          { 0 }
    ::= { rttMonGrpScheduleAdminEntry 10 }

rttMonGrpScheduleAdminAdd OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Addition of members to an existing group will be allowed
        if this object is set to TRUE (1). The members, IDs of
        which are mentioned in rttMonGrpScheduleAdminProbes object
        are added to the existing group"
    ::= { rttMonGrpScheduleAdminEntry 11 }

rttMonGrpScheduleAdminDelete OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Removal of members from an existing group will be allowed
        if this object is set to TRUE (1). The members, IDs of
        which are mentioned in rttMonGrpScheduleAdminProbes object
        are removed from the existing group"
    ::= { rttMonGrpScheduleAdminEntry 12 }

rttMonGrpScheduleAdminReset OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When this is set to true then all members
        of this group will be stopped and rescheduled using the
        previously set values of this group."
    ::= { rttMonGrpScheduleAdminEntry 13 }

rttMonGrpScheduleAdminStartType OBJECT-TYPE
    SYNTAX          RttMonScheduleStartType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the start-time option to be used for
        scheduling the start time of the group with
        probes included in the object rttMonGrpScheduleAdminProbes.

        The following list defines the start-time options
        that can be configured for the group schedule:

        pending(1)      pending state.
        now(2)          schedule to immediately start the
                        group with defined probes.
        random(3)       schedule start time of group at random
                        time over a range.
        after(4)        schedule start time of group after a
                        certain amount of time from now.
        specific(5)     schedule start time of group at a given
                        specific time interval."
    DEFVAL          { pending }
    ::= { rttMonGrpScheduleAdminEntry 14 }

rttMonGrpScheduleAdminStartDelay OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the start time delay range in
        milliseconds.
        The value of this object is only effective with
        rttMonScheduleAdminStartType value.

        For rttMonGrpScheduleAdminStartType with random option,
        a random time is generated with in provided start delay
        range and added to current time to generate the random
        start time of the group with defined probes."
    DEFVAL          { 0 }
    ::= { rttMonGrpScheduleAdminEntry 15 }


-- Auto SAA L3 MPLS VPN Configuration Definitions

rttMplsVpnMonCtrlTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMplsVpnMonCtrlEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of Auto SAA L3 MPLS VPN definitions.

        The Auto SAA L3 MPLS VPN administration control is in multiple
        tables.

        This first table, is used to create a conceptual Auto SAA L3
        MPLS VPN control row.  The following tables contain objects
        which used in type specific configurations, scheduling and
        reaction configurations. All of these tables will create the
        same conceptual control row as this table using this table's
        index as their own index.

        In order to a row in this table to become active the following
        objects must be defined.
          rttMplsVpnMonCtrlRttType,
          rttMplsVpnMonCtrlVrfName and
          rttMplsVpnMonSchedulePeriod."
    ::= { rttMonCtrl 15 }

rttMplsVpnMonCtrlEntry OBJECT-TYPE
    SYNTAX          RttMplsVpnMonCtrlEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A base list of objects that define a conceptual Auto SAA L3
        MPLS VPN control row."
    INDEX           { rttMplsVpnMonCtrlIndex }
    ::= { rttMplsVpnMonCtrlTable 1 }

RttMplsVpnMonCtrlEntry ::= SEQUENCE {
        rttMplsVpnMonCtrlIndex         Integer32,
        rttMplsVpnMonCtrlRttType       RttMplsVpnMonRttType,
        rttMplsVpnMonCtrlVrfName       OCTET STRING,
        rttMplsVpnMonCtrlTag           DisplayString,
        rttMplsVpnMonCtrlThreshold     Integer32,
        rttMplsVpnMonCtrlTimeout       Integer32,
        rttMplsVpnMonCtrlScanInterval  Integer32,
        rttMplsVpnMonCtrlDelScanFactor Integer32,
        rttMplsVpnMonCtrlEXP           Integer32,
        rttMplsVpnMonCtrlRequestSize   Integer32,
        rttMplsVpnMonCtrlVerifyData    TruthValue,
        rttMplsVpnMonCtrlStorageType   StorageType,
        rttMplsVpnMonCtrlProbeList     DisplayString,
        rttMplsVpnMonCtrlStatus        RowStatus,
        rttMplsVpnMonCtrlLpd           TruthValue,
        rttMplsVpnMonCtrlLpdGrpList    DisplayString,
        rttMplsVpnMonCtrlLpdCompTime   Integer32
}

rttMplsVpnMonCtrlIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Uniquely identifies a row in the rttMplsVpnMonCtrlTable.

        This is a pseudo-random number selected by the management
        station when creating a row via the
        rttMplsVpnMonCtrlStatus object.

        If the pseudo-random number is already in use an
        'inconsistentValue' return code will be returned when set
        operation is attempted."
    ::= { rttMplsVpnMonCtrlEntry 1 }

rttMplsVpnMonCtrlRttType OBJECT-TYPE
    SYNTAX          RttMplsVpnMonRttType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The type of RTT operation to be performed for Auto SAA L3
        MPLS VPN.

        This value must be set in the same PDU of
        rttMplsVpnMonCtrlStatus.

        This value must be set before setting
        any other parameter configuration of an Auto SAA L3 MPLS VPN."
    ::= { rttMplsVpnMonCtrlEntry 2 }

rttMplsVpnMonCtrlVrfName OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (0..32))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This field is used to specify the VPN name for which
        the Auto SAA L3 MPLS VPN RTT operation will be used.

        This value must be set in the same PDU of
        rttMplsVpnMonCtrlStatus.

        The Auto SAA L3 MPLS VPN will find the PEs participating in
        this VPN and configure RTT operation corresponding to value
        specified in rttMplsVpnMonCtrlRttType.

        If the VPN corresponds to the value configured for this object
        doesn't exist 'inconsistentValue' error will be returned.

        The value 'saa-vrf-all' has a special meaning. When this
        object is set to 'saa-vrf-all', all the VPNs in the PE will be
        discovered and Auto SAA L3 MPLS VPN will configure RTT
        operations corresponding to all these PEs with the value
        specified in rttMplsVpnMonCtrlRttType as type for those
        operations.

        So, the user should avoid using this string for a particular
        VPN name when using this feature in order to avoid ambiguity."
    ::= { rttMplsVpnMonCtrlEntry 3 }

rttMplsVpnMonCtrlTag OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..255))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which is used by a managing application to
        identify the RTT target.

        This string will be configured as rttMonCtrlAdminTag for all
        the operations configured by this Auto SAA L3 MPLS VPN.

        The usage of this value in Auto SAA L3 MPLS VPN is same as
        rttMonCtrlAdminTag in RTT operation."
    DEFVAL          { "" }
    ::= { rttMplsVpnMonCtrlEntry 4 }

rttMplsVpnMonCtrlThreshold OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object defines an administrative threshold limit.

        This value will be configured as rttMonCtrlAdminThreshold for
        all the operations that will be configured by the current
        Auto SAA L3 MPLS VPN.

        The usage of this value in Auto SAA L3 MPLS VPN is same as
        rttMonCtrlAdminThreshold."
    DEFVAL          { 5000 }
    ::= { rttMplsVpnMonCtrlEntry 5 }

rttMplsVpnMonCtrlTimeout OBJECT-TYPE
    SYNTAX          Integer32 (0..*********)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the duration to wait for a RTT operation configured
        automatically by the Auto SAA L3 MPLS VPN to complete.

        The value of this object cannot be set to a value which would
        specify a duration exceeding rttMplsVpnMonScheduleFrequency.

        The usage of this value in Auto SAA L3 MPLS VPN is similar to
        rttMonCtrlAdminTimeout."
    DEFVAL          { 5000 }
    ::= { rttMplsVpnMonCtrlEntry 6 }

rttMplsVpnMonCtrlScanInterval OBJECT-TYPE
    SYNTAX          Integer32 (1..70560)
    UNITS           "minutes"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the frequency at which the automatic PE addition
        should take place if there is any for an Auto SAA L3 MPLS VPN.

        New RTT operations corresponding to the new PEs discovered will
        be created and scheduled.

        The default value for this object is 4 hours. The maximum value
        supported is 49 days."
    DEFVAL          { 240 }
    ::= { rttMplsVpnMonCtrlEntry 7 }

rttMplsVpnMonCtrlDelScanFactor OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the frequency at which the automatic PE deletion
        should take place.

        This object specifies the number of times of
        rttMonMplslmCtrlScanInterval (rttMplsVpnMonCtrlDelScanFactor *
        rttMplsVpnMonCtrlScanInterval) to wait before removing the PEs.
        This object doesn't directly specify the explicit value to
        wait before removing the PEs that were down.

        If this object set 0 the entries will never removed."
    DEFVAL          { 1 }
    ::= { rttMplsVpnMonCtrlEntry 8 }

rttMplsVpnMonCtrlEXP OBJECT-TYPE
    SYNTAX          Integer32 (0..7)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the EXP value that needs to be
        put as precedence bit of an IP header."
    DEFVAL          { 0 }
    ::= { rttMplsVpnMonCtrlEntry 9 }

rttMplsVpnMonCtrlRequestSize OBJECT-TYPE
    SYNTAX          Integer32 (0..16384)
    UNITS           "octets"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the native payload size that needs to
        be put on the packet.

        This value will be configured as
        rttMonEchoAdminPktDataRequestSize for all the RTT operations
        configured by the current Auto SAA L3 MPLS VPN.

        The minimum request size for jitter probe is 16. The maximum
        for jitter probe is 1500. The default request size is 32 for
        jitter probe.

        For echo and pathEcho default request size is 28.
        The minimum request size for echo and pathEcho is 28 bytes."
    DEFVAL          { 1 }
    ::= { rttMplsVpnMonCtrlEntry 10 }

rttMplsVpnMonCtrlVerifyData OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When set to true, the resulting data in each RTT operation
        created by the current Auto SAA L3 MPLS VPN is compared with
        the expected data. This includes checking header information
        (if possible) and exact packet size.  Any mismatch will be
        recorded in the rttMonStatsCollectVerifyErrors object of each
        RTT operation created by the current Auto SAA L3 MPLS VPN."
    DEFVAL          { false }
    ::= { rttMplsVpnMonCtrlEntry 11 }

rttMplsVpnMonCtrlStorageType OBJECT-TYPE
    SYNTAX          StorageType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The storage type of this conceptual row. When set to
        'nonVolatile', this entry will be shown in 'show running'
        command and can be saved into Non-volatile memory.

        By Default the entry will not be saved into Non-volatile
        memory.

        This object can be set to either 'volatile' or 'nonVolatile'.
        Other values are not applicable for this conceptual row and
        are not supported."
    DEFVAL          { volatile }
    ::= { rttMplsVpnMonCtrlEntry 12 }

rttMplsVpnMonCtrlProbeList OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object holds the list of probes ID's that are created by
        the Auto SAA L3 MPLS VPN.

        The probes will be specified in the following form.
        (a) Individual ID's with comma separated as 1,5,3.
        (b) Range form including hyphens with multiple ranges being
            separated by comma as 1-10,12-34.
        (c) Mix of the above two forms as 1,2,4-10,12,15,19-25."
    ::= { rttMplsVpnMonCtrlEntry 13 }

rttMplsVpnMonCtrlStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of the conceptual Auto SAA L3 MPLS VPN control row.

        In order for this object to become active
        rttMplsVpnMonCtrlRttType,  rttMplsVpnMonCtrlVrfName and
        rttMplsVpnMonSchedulePeriod objects must be defined.
        All other objects can assume default values.

        If the object is set to 'createAndGo' rttMplsVpnMonCtrlRttType,
        rttMplsVpnMonCtrlVrfName and rttMplsVpnMonSchedulePeriod needs
        to be set along with rttMplsVpnMonCtrlStatus.

        If the object is set to 'createAndWait' rttMplsVpnMonCtrlRttType
        and rttMplsVpnMonCtrlVrfName needs to be set along with
        rttMplsVpnMonCtrlStatus. rttMplsVpnMonSchedulePeriod needs to be
        specified before setting rttMplsVpnMonCtrlStatus to 'active'.

        The following objects cannot be modified after creating the
        Auto SAA L3 MPLS VPN conceptual row.

         - rttMplsVpnMonCtrlRttType
         - rttMplsVpnMonCtrlVrfName

        The following objects can be modified even after creating the
        Auto SAA L3 MPLS VPN conceptual row by setting this object to
        'notInService'

         - All other writable objects in rttMplsVpnMonCtrlTable except
           rttMplsVpnMonCtrlRttType and rttMplsVpnMonCtrlVrfName.
         - Objects in the rttMplsVpnMonTypeTable.
         - Objects in the rttMplsVpnMonScheduleTable.

        The following objects can be modified as needed without setting
        this object to 'notInService' even after creating the
        Auto SAA L3 MPLS VPN conceptual row.

         - Objects in rttMplsVpnMonReactTable.

        This object can be set to 'destroy' from any value
        at any time. When this object is set to 'destroy' it will stop
        and destroy all the probes created by this Auto SAA L3 MPLS VPN
        before destroying Auto SAA L3 MPLS VPN control row."
    ::= { rttMplsVpnMonCtrlEntry 14 }

rttMplsVpnMonCtrlLpd OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "When set to true, this implies that LPD (LSP Path Discovery)
        is enabled for this row.

        The Auto SAA L3 MPLS VPN will find all the paths to each of the
        PE's and configure RTT operation with rttMonCtrlAdminRttType
        value as 'lspGroup'. The 'lspGroup' probe will walk through
        the list of set of information that uniquely identifies a path
        and send the LSP echo requests across them. All these LSP echo
        requests sent for 1st path, 2nd path etc. can be thought of as
        'single probes' sent as a part of 'lspGroup'. These single
        probes will of type 'rttMplsVpnMonCtrlRttType'.

        'lspGroup' probe is a superset of individual probes that will
        test multiple paths. For example Suppose there are 10 paths to
        the target. One 'lspGroup' probe will be created which will
        store all the information related to uniquely identify the 10
        paths. When the 'lspGroup' probe will run it will sweep through
        the set of information for 1st path, 2nd path, 3rd path and so
        on till it has tested all the paths."
    DEFVAL          { false }
    ::= { rttMplsVpnMonCtrlEntry 15 }

rttMplsVpnMonCtrlLpdGrpList OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object holds the list of LPD Group IDs that are created
        for this Auto SAA L3 MPLS VPN row.

        This object will be applicable only when LSP Path Discovery is
        enabled for this row.

        The LPD Groups will be specified in the following form.
        (a) Individual ID's with comma separated as 1,5,3.
        (b) Range form including hyphens with multiple ranges being
            separated by comma as 1-10,12-34.
        (c) Mix of the above two forms as 1,2,4-10,12,15,19-25."
    ::= { rttMplsVpnMonCtrlEntry 16 }

rttMplsVpnMonCtrlLpdCompTime OBJECT-TYPE
    SYNTAX          Integer32 (1..65535)
    UNITS           "minutes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The completion time of the LSP Path Discovery for the entire
        set of PEs which are discovered for this Auto SAA.

        This object will be applicable only when LSP Path Discovery is
        enabled for this row."
    ::= { rttMplsVpnMonCtrlEntry 17 }


-- Auto SAA L3 MPLS VPN Type Specific Configuration

rttMplsVpnMonTypeTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMplsVpnMonTypeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table that contains Auto SAA L3 MPLS VPN configured RTT
        operation specific definitions.

        This table is controlled via the rttMplsVpnMonCtrlTable.
        Entries in this table are created via the
        rttMplsVpnMonCtrlStatus object."
    ::= { rttMonCtrl 16 }

rttMplsVpnMonTypeEntry OBJECT-TYPE
    SYNTAX          RttMplsVpnMonTypeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define RTT operation specific
        configuration for an Auto SAA L3 MPLS VPN."
    AUGMENTS           { rttMplsVpnMonCtrlEntry  }
    ::= { rttMplsVpnMonTypeTable 1 }

RttMplsVpnMonTypeEntry ::= SEQUENCE {
        rttMplsVpnMonTypeInterval        Integer32,
        rttMplsVpnMonTypeNumPackets      Integer32,
        rttMplsVpnMonTypeDestPort        Integer32,
        rttMplsVpnMonTypeSecFreqType     INTEGER,
        rttMplsVpnMonTypeSecFreqValue    Integer32,
        rttMplsVpnMonTypeLspSelector     OCTET STRING,
        rttMplsVpnMonTypeLSPReplyMode    RttMonLSPPingReplyMode,
        rttMplsVpnMonTypeLSPTTL          Integer32,
        rttMplsVpnMonTypeLSPReplyDscp    Integer32,
        rttMplsVpnMonTypeLpdMaxSessions  Integer32,
        rttMplsVpnMonTypeLpdSessTimeout  Integer32,
        rttMplsVpnMonTypeLpdEchoTimeout  Integer32,
        rttMplsVpnMonTypeLpdEchoInterval Integer32,
        rttMplsVpnMonTypeLpdEchoNullShim TruthValue,
        rttMplsVpnMonTypeLpdScanPeriod   Integer32,
        rttMplsVpnMonTypeLpdStatHours    Integer32
}

rttMplsVpnMonTypeInterval OBJECT-TYPE
    SYNTAX          Integer32 (1..60000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This value represents the inter-packet delay between packets
        and is in milliseconds. This value is currently used for
        Jitter probe. This object is applicable to jitter probe only.

        The usage of this value in RTT operation is same as
        rttMonEchoAdminInterval."
    DEFVAL          { 20 }
    ::= { rttMplsVpnMonTypeEntry 1 }

rttMplsVpnMonTypeNumPackets OBJECT-TYPE
    SYNTAX          Integer32 (1..60000)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This value represents the number of packets that need to be
        transmitted. This value is currently used for Jitter probe.
        This object is applicable to jitter probe only.

        The usage of this value in RTT operation is same as
        rttMonEchoAdminNumPackets."
    DEFVAL          { 10 }
    ::= { rttMplsVpnMonTypeEntry 2 }

rttMplsVpnMonTypeDestPort OBJECT-TYPE
    SYNTAX          Integer32 (1..65536)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the target's port number to which the
        packets need to be sent.

        This value will be configured as target port for all the
        operations that is going to be configured

        The usage of this value is same as rttMonEchoAdminTargetPort
        in RTT operation. This object is applicable to jitter type.

        If this object is not being set random port will be used as
        destination port."
    ::= { rttMplsVpnMonTypeEntry 3 }

rttMplsVpnMonTypeSecFreqType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        timeout(2),
                        connectionLoss(3),
                        both(4)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the reaction type for which the
        rttMplsVpnMonTypeSecFreqValue should be applied.

        The Value 'timeout' will cause secondary frequency to be set
        for frequency on timeout condition.

        The Value 'connectionLoss' will cause secondary frequency to
        be set for frequency on connectionloss condition.

        The Value 'both' will cause secondary frequency to be set for
        frequency on either of timeout/connectionloss condition.

        Notifications must be configured on corresponding reaction type
        in order to rttMplsVpnMonTypeSecFreqValue get effect.

        When LSP Path Discovery is enabled for this row the following
        rttMplsVpnMonReactLpdNotifyType notifications must be
        configured in order to rttMplsVpnMonTypeSecFreqValue get effect.
          - 'lpdGroupStatus' or 'lpdAll'.

        Since the Frequency of the operation changes the stats will be
        collected in new bucket.

        If any of the reaction type (timeout/connectionLoss) occurred
        for an operation configured by this Auto SAA L3 MPLS VPN and
        the following conditions are satisfied, the frequency of the
        operation will be changed to rttMplsVpnMonTypeSecFreqValue.

          1) rttMplsVpnMonTypeSecFreqType is set for a reaction type
          (timeout/connectionLoss).
          2) A notification is configured for the same reaction type
          (timeout/connectionLoss).

        When LSP Path Discovery is enabled for this row, if any of the
        reaction type (timeout/connectionLoss) occurred for 'single
        probes' configured by this Auto SAA L3 MPLS VPN and the
        following conditions are satisfied, the secondary frequency
        rttMplsVpnMonTypeSecFreqValue will be applied to the
        'lspGroup' probe.

          1) rttMplsVpnMonTypeSecFreqType is set for a reaction type
          (timeout/connectionLoss/both).
          2) rttMplsVpnMonReactLpdNotifyType object must be set to
          value of 'lpdGroupStatus' or 'lpdAll'.

        The frequency of the individual operations will be restored to
        original frequency once the trap is sent."
    DEFVAL          { none }
    ::= { rttMplsVpnMonTypeEntry 4 }

rttMplsVpnMonTypeSecFreqValue OBJECT-TYPE
    SYNTAX          Integer32 (1..604800)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the value that needs to be applied to
        secondary frequency of individual RTT operations configured by
        Auto SAA L3 MPLS VPN.

        Setting rttMplsVpnMonTypeSecFreqValue without setting
        rttMplsVpnMonTypeSecFreqType will not have any effect."
    DEFVAL          { 60 }
    ::= { rttMplsVpnMonTypeEntry 5 }

rttMplsVpnMonTypeLspSelector OBJECT-TYPE
    SYNTAX          OCTET STRING
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "A string which specifies the address of the local host
        (127.X.X.X).

        This object will be used as lsp-selector in MPLS RTT
        operations configured by the Auto SAA L3 MPLS VPN.

        When LSP Path Discovery is enabled for the row, this object will
        be used to indicate the base LSP selector value to be used in
        the LSP Path Discovery.

        This value of this object is significant in MPLS load
        balancing scenario. This value will be used as one of the
        parameter in that load balancing."
    DEFVAL          { "7F 00 00 01" }
    ::= { rttMplsVpnMonTypeEntry 6 }

rttMplsVpnMonTypeLSPReplyMode OBJECT-TYPE
    SYNTAX          RttMonLSPPingReplyMode
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the reply mode for the LSP Echo
        requests originated by the operations configured by the
        Auto SAA L3 MPLS VPN.

        This object is currently used by echo and pathEcho."
    DEFVAL          { replyIpv4Udp }
    ::= { rttMplsVpnMonTypeEntry 7 }

rttMplsVpnMonTypeLSPTTL OBJECT-TYPE
    SYNTAX          Integer32 (0..255)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the TTL setting for MPLS echo request
        packets originated by the operations configured by the
        Auto SAA L3 MPLS VPN.

        This object is currently used by echo and pathEcho.

        For 'echo' the default TTL will be set to 255.
        For 'pathEcho' the default will be set to 30.

        Note: This object cannot be set to the value of 0. The
        default value of 0 signifies the default TTL values will be
        used for 'echo' and 'pathEcho'."
    DEFVAL          { 0 }
    ::= { rttMplsVpnMonTypeEntry 8 }

rttMplsVpnMonTypeLSPReplyDscp OBJECT-TYPE
    SYNTAX          Integer32 (0..63 | 255)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the DSCP value to be set in the IP header
        of the LSP echo reply packet.
        The value of this object will be in range of DiffServ codepoint
        values between 0 to 63.

        Note: This object cannot be set to value of 255. This default
        value specifies that DSCP is not set for this row."
    DEFVAL          { 255 }
    ::= { rttMplsVpnMonTypeEntry 9 }

rttMplsVpnMonTypeLpdMaxSessions OBJECT-TYPE
    SYNTAX          Integer32 (1..15)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object represents the number of concurrent path discovery
        requests that will be active at one time per MPLS VPN control
        row. This object is meant for reducing the time for discovery
        of all the paths to the target in a large customer network.
        However its value should be chosen such that it does not cause
        any performance impact.

        Note: If the customer network has low end routers in the Core
        it is recommended to keep this value low."
    DEFVAL          { 1 }
    ::= { rttMplsVpnMonTypeEntry 10 }

rttMplsVpnMonTypeLpdSessTimeout OBJECT-TYPE
    SYNTAX          Integer32 (1..900)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the maximum allowed duration of a
        particular tree trace request.

        If no response is received in configured time the request will
        be considered a failure."
    DEFVAL          { 120 }
    ::= { rttMplsVpnMonTypeEntry 11 }

rttMplsVpnMonTypeLpdEchoTimeout OBJECT-TYPE
    SYNTAX          Integer32 (0..*********)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the timeout value for the LSP echo
        requests which are sent while performing the LSP Path
        Discovery."
    DEFVAL          { 5000 }
    ::= { rttMplsVpnMonTypeEntry 12 }

rttMplsVpnMonTypeLpdEchoInterval OBJECT-TYPE
    SYNTAX          Integer32 (0..3600000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the send interval between LSP echo
        requests which are sent while performing the LSP Path
        Discovery."
    DEFVAL          { 0 }
    ::= { rttMplsVpnMonTypeEntry 13 }

rttMplsVpnMonTypeLpdEchoNullShim OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies if the explicit-null label is added to
        LSP echo requests which are sent while performing the LSP Path
        Discovery.

        If set to TRUE all the probes configured as part of this control
        row will send the LSP echo requests with the explicit-null
        label added."
    DEFVAL          { false }
    ::= { rttMplsVpnMonTypeEntry 14 }

rttMplsVpnMonTypeLpdScanPeriod OBJECT-TYPE
    SYNTAX          Integer32 (0..7200)
    UNITS           "minutes"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the scan time for the completion of LSP
        Path Discovery for all the PEs discovered for this control row.
        If the scan period is exceeded on completion of the LSP Path
        Discovery for all the PEs, the next discovery will start
        immediately else it will wait till expiry of scan period.

        For example: If the value is set to 30 minutes then on start of
        the LSP Path Discovery a timestamp will be taken say T1. At the
        end of the tree trace discovery one more timestamp will be taken
        again say T2. If (T2-T1) is greater than 30, the next discovery
        will start immediately else next discovery  will wait for
        [30 - (T2-T1)].

        Note: If the object is set to a special value of '0', it will
        force immediate start of the next discovery on all neighbours
        without any delay."
    DEFVAL          { 240 }
    ::= { rttMplsVpnMonTypeEntry 15 }

rttMplsVpnMonTypeLpdStatHours OBJECT-TYPE
    SYNTAX          Integer32 (0..2)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The maximum number of hours of data to be kept per LPD
        group. The LPD group statistics will be kept in an hourly
        bucket. At the maximum there can be two buckets.
        The value of 'one' is not advisable because the group will close
        and immediately be deleted before the network management station
        will have the opportunity to retrieve the statistics.

        The value used in the rttMplsVpnLpdGroupStatsTable to
        uniquely identify this group is the
        rttMonStatsCaptureStartTimeIndex.

        Note: When this object is set to the value of '0' all
        rttMplsVpnLpdGroupStatsTable data capturing will be shut off."
    DEFVAL          { 2 }
    ::= { rttMplsVpnMonTypeEntry 16 }


-- Auto SAA L3 MPLS VPN Schedule Table

rttMplsVpnMonScheduleTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMplsVpnMonScheduleEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table of Auto SAA L3 MPLS VPN monitoring scheduling
        specific definitions.

        This table is controlled via the rttMplsVpnMonCtrlTable.
        Entries in this table are created via the
        rttMplsVpnMonCtrlStatus object."
    ::= { rttMonCtrl 17 }

rttMplsVpnMonScheduleEntry OBJECT-TYPE
    SYNTAX          RttMplsVpnMonScheduleEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define specific configuration for
        the scheduling of RTT operations."
    AUGMENTS           { rttMplsVpnMonCtrlEntry  }
    ::= { rttMplsVpnMonScheduleTable 1 }

RttMplsVpnMonScheduleEntry ::= SEQUENCE {
        rttMplsVpnMonScheduleRttStartTime TimeTicks,
        rttMplsVpnMonSchedulePeriod       Integer32,
        rttMplsVpnMonScheduleFrequency    Integer32
}

rttMplsVpnMonScheduleRttStartTime OBJECT-TYPE
    SYNTAX          TimeTicks
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This is the time when this conceptual row will
        activate. rttMplsVpnMonSchedulePeriod object must be specified
        before setting this object.

        This is the value of MIB-II's sysUpTime in the future.
        When sysUpTime equals this value this object will
        cause the activation of a conceptual Auto SAA L3 MPLS VPN row.

        When an agent has the capability to determine date and
        time, the agent should store this object as DateAndTime.
        This allows the agent to be able to activate conceptual
        Auto SAA L3 MPLS VPN row at the intended time.

        If this object has value as 1, this means start the operation
        now itself. Value of 0 puts the operation in pending state."
    DEFVAL          { 0 }
    ::= { rttMplsVpnMonScheduleEntry 1 }

rttMplsVpnMonSchedulePeriod OBJECT-TYPE
    SYNTAX          Integer32 (1..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the time duration over which all the probes created
        by the current Auto SAA L3 MPLS VPN have to be scheduled.

        This object must be set first before setting
        rttMplsVpnMonScheduleRttStartTime."
    ::= { rttMplsVpnMonScheduleEntry 2 }

rttMplsVpnMonScheduleFrequency OBJECT-TYPE
    SYNTAX          Integer32 (1..604800)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies the duration between initiating each RTT
        operation configured by the Auto SAA L3 MPLS VPN.

        This object cannot be set to a value which would be a
        shorter duration than rttMplsVpnMonCtrlTimeout.

        The usage of this value in RTT operation is same as
        rttMonCtrlAdminFrequency."
    DEFVAL          { 60 }
    ::= { rttMplsVpnMonScheduleEntry 3 }


-- Auto SAA L3 MPLS VPN Reaction Table

rttMplsVpnMonReactTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMplsVpnMonReactEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A Table of Auto SAA L3 MPLS VPN Notification definitions.

        This table augments the rttMplsVpnMonCtrlTable."
    ::= { rttMonCtrl 18 }

rttMplsVpnMonReactEntry OBJECT-TYPE
    SYNTAX          RttMplsVpnMonReactEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects that define Auto SAA L3 MPLS VPN reaction
        configuration."
    AUGMENTS           { rttMplsVpnMonCtrlEntry  }
    ::= { rttMplsVpnMonReactTable 1 }

RttMplsVpnMonReactEntry ::= SEQUENCE {
        rttMplsVpnMonReactConnectionEnable TruthValue,
        rttMplsVpnMonReactTimeoutEnable    TruthValue,
        rttMplsVpnMonReactThresholdType    INTEGER,
        rttMplsVpnMonReactThresholdCount   Integer32,
        rttMplsVpnMonReactActionType       INTEGER,
        rttMplsVpnMonReactLpdNotifyType    INTEGER,
        rttMplsVpnMonReactLpdRetryCount    Integer32
}

rttMplsVpnMonReactConnectionEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The value set for this will be applied as
        rttMonReactAdminConnectionEnable for individual probes created
        by the Auto SAA L3 MPLS VPN.

        When this object is set to true, rttMonReactVar for individual
        probes created by the Auto SAA L3 MPLS VPN will be set to
        'connectionLoss(8)'."
    DEFVAL          { false }
    ::= { rttMplsVpnMonReactEntry 1 }

rttMplsVpnMonReactTimeoutEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The value set for this will be applied as
        rttMonReactAdminTimeoutEnable for individual probes created
        by the Auto SAA L3 MPLS VPN.

        When this object is set to true, rttMonReactVar for individual
        probes created by the Auto SAA L3 MPLS VPN will be set to
        'timeout(7)'."
    DEFVAL          { false }
    ::= { rttMplsVpnMonReactEntry 2 }

rttMplsVpnMonReactThresholdType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        never(1),
                        immediate(2),
                        consecutive(3)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The value corresponding to this object will be applied as
        rttMonReactAdminThresholdType for individual probes created by
        the Auto SAA L3 MPLS VPN.

        The value corresponding to this object will be applied as
        rttMonReactThresholdType for individual probes created by
        the Auto SAA L3 MPLS VPN."
    DEFVAL          { never }
    ::= { rttMplsVpnMonReactEntry 3 }

rttMplsVpnMonReactThresholdCount OBJECT-TYPE
    SYNTAX          Integer32 (1..16)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object value will be applied as
        rttMonReactAdminThresholdCount for individual probes created by
        the Auto SAA L3 MPLS VPN.

        This object value will be applied as rttMonReactThresholdCountX
        for individual probes created by the Auto SAA L3 MPLS VPN."
    DEFVAL          { 5 }
    ::= { rttMplsVpnMonReactEntry 4 }

rttMplsVpnMonReactActionType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        trapOnly(2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The value corresponding to this object will be applied as
        rttMonReactAdminActionType of individual probes created by
        this Auto SAA L3 MPLS VPN.

        The value corresponding to this object will be applied as
        rttMonReactActionType of individual probes created by
        this Auto SAA L3 MPLS VPN."
    DEFVAL          { none }
    ::= { rttMplsVpnMonReactEntry 5 }

rttMplsVpnMonReactLpdNotifyType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        lpdPathDiscovery(2),
                        lpdGroupStatus(3),
                        lpdAll(4)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the type of LPD notifications to be
        generated for the current Auto SAA L3 MPLS VPN control row.

        This object will be applicable only when LSP Path Discovery is
        enabled for this row.

        There are two types of notifications supported for the LPD -
        (a) rttMonLpdDiscoveryNotification - This notification will
            be sent on the failure of LSP Path Discovery to the
            particular PE. Reversal of the failure will also result in
            sending the notification.
        (b) rttMonLpdGrpStatusNotification - Individual probes in an LPD
            group will not generate notifications independently but will
            be generating dependent on the state of the group. Any
            individual probe can initiate the generation of a
            notification, dependent on the state of the group.
            Notifications are only generated if the failure/restoration
            of an individual probe causes the state of the group to
            change.

        The Value 'none' will not cause any notifications to be sent.

        The Value 'lpdPathDiscovery' will cause (a) to be sent.

        The Value 'lpdGroupStatus' will cause (b) to be sent.

        The Value 'lpdAll' will cause both (a) and (b) to sent
        depending on the failure conditions."
    DEFVAL          { none }
    ::= { rttMplsVpnMonReactEntry 6 }

rttMplsVpnMonReactLpdRetryCount OBJECT-TYPE
    SYNTAX          Integer32 (1..16)
    UNITS           "attempts"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object value specifies the number of attempts to be
        performed before declaring the path as 'down'. Each 'single
        probe' which is part of 'lspGroup' probe will be retried these
        many times before marking it as 'down'.

        This object will be applicable only when LSP Path Discovery is
        enabled for this row.

          - When rttMplsVpnMonTypeSecFreqType is not configured, the
            failure count will be incremented at the next cycle of
            'lspGroup' probe at interval's of
            rttMplsVpnMonScheduleFrequency value.

            For example: Assume there are 10 paths discovered and on
            the first run of the 'lspGroup' probe first two paths failed
            and rest passed. On the second run all the probes will be
            run again. The probes 1 and 2 will be retried till the
            rttMplsVpnMonReactLpdRetryCount value, and
            then marked as 'down' and rttMonLpdGrpStatusNotification
            will be sent if configured.

          - When rttMplsVpnMonTypeSecFreqType value is anything other
            than 'none', the retry will happen for the failed probes at
            the rttMplsVpnMonTypeSecFreqValue and only the failed
            probes will be retried.

            For example: Assume there are 10 paths discovered and on the
            first run of the 'lspGroup' probe first two paths failed and
            rest passed. The secondary frequency will be applied to the
            failed probes. At secondary frequency interval the first two
            probes will be run again. The probes 1 and 2 will be retried
            till the rttMplsVpnMonReactLpdRetryCount value, and
            then marked as 'down' and rttMonLpdGrpStatusNotification
            will be sent if configured."
    DEFVAL          { 1 }
    ::= { rttMplsVpnMonReactEntry 7 }


-- A new Reaction Table for the probes.

rttMonReactTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonReactEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table that contains the reaction configurations. Each
        conceptual row in rttMonReactTable corresponds to a reaction
        configured for the probe defined in rttMonCtrlAdminTable.

        For each reaction configured for a probe there is an entry in
        the table.

        Each Probe can have multiple reactions and hence there can be
        multiple rows for a particular probe.

        This table is coupled with rttMonCtrlAdminTable."
    ::= { rttMonCtrl 19 }

rttMonReactEntry OBJECT-TYPE
    SYNTAX          RttMonReactEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A base list of objects that define a conceptual reaction
        configuration control row."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonReactConfigIndex
                    }
    ::= { rttMonReactTable 1 }

RttMonReactEntry ::= SEQUENCE {
        rttMonReactConfigIndex      Integer32,
        rttMonReactVar              RttMonReactVar,
        rttMonReactThresholdType    INTEGER,
        rttMonReactActionType       INTEGER,
        rttMonReactThresholdRising  Integer32,
        rttMonReactThresholdFalling Integer32,
        rttMonReactThresholdCountX  Integer32,
        rttMonReactThresholdCountY  Integer32,
        rttMonReactValue            Integer32,
        rttMonReactOccurred         TruthValue,
        rttMonReactStatus           RowStatus
}

rttMonReactConfigIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object along with rttMonCtrlAdminIndex identifies
        a particular reaction-configuration for a particular probe.
        This is a pseudo-random number selected by the management
        station when creating a row via the rttMonReactStatus.
        If the pseudo-random number is already in use an
        'inconsistentValue' return code will be returned when
        set operation is attempted."
    ::= { rttMonReactEntry 1 }

rttMonReactVar OBJECT-TYPE
    SYNTAX          RttMonReactVar
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the type of reaction configured for a
        probe.

        The reaction types 'rtt', 'timeout', and 'connectionLoss'
        can be configured for all probe types.

        The reaction type 'verifyError' can be configured for all
        probe types except RTP probe type.

        The reaction types 'jitterSDAvg', 'jitterDSAvg', 'jitterAvg',
        'packetLateArrival', 'packetOutOfSequence',
        'maxOfPositiveSD', 'maxOfNegativeSD', 'maxOfPositiveDS'
        and 'maxOfNegativeDS' can be configured for UDP jitter
        and ICMP jitter probe types only.

        The reaction types 'mos' and 'icpif' can be configured
        for UDP jitter and ICMP jitter probe types only.

        The reaction types 'packetLossDS', 'packetLossSD' and
        'packetMIA' can be configured for UDP jitter, and
        RTP probe types only.

        The reaction types 'iaJitterDS', 'frameLossDS', 'mosLQDS',
        'mosCQDS', 'rFactorDS', 'iaJitterSD', 'rFactorSD', 'mosCQSD'
        can be configured for RTP probe type only.

        The reaction types 'successivePacketLoss', 'maxOfLatencyDS',
        'maxOfLatencySD', 'latencyDSAvg', 'latencySDAvg' and
        'packetLoss' can be configured for ICMP jitter probe
        type only."
    ::= { rttMonReactEntry 2 }

rttMonReactThresholdType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        never(1),
                        immediate(2),
                        consecutive(3),
                        xOfy(4),
                        average(5)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the conditions under which
        the notification ( trap ) is sent.

        never       - rttMonReactOccurred is never set

        immediate   - rttMonReactOccurred is set to 'true' when the
                      value of parameter for which reaction is
                      configured ( e.g rtt, jitterAvg, packetLossSD,
                      mos etc ) violates the threshold.
                      Conversely, rttMonReactOccurred is set to 'false'
                      when the parameter ( e.g rtt, jitterAvg,
                      packetLossSD, mos etc ) is below the threshold
                      limits.

        consecutive - rttMonReactOccurred is set to true when the value
                      of parameter for which reaction is configured
                      ( e.g rtt, jitterAvg, packetLossSD, mos etc )
                      violates the threshold for configured consecutive
                      times.
                      Conversely, rttMonReactOccurred is set to false
                      when the value of parameter ( e.g rtt, jitterAvg
                      packetLossSD, mos etc ) is below the threshold
                      limits for the same number of consecutive
                      operations.

        xOfy        - rttMonReactOccurred is set to true when x
                      ( as specified by rttMonReactThresholdCountX )
                      out of the last y ( as specified by
                      rttMonReacthresholdCountY ) times the value of
                      parameter for which the reaction is configured
                      ( e.g rtt, jitterAvg, packetLossSD, mos etc )
                      violates the threshold.
                      Conversely, it is set to false when x, out of the
                      last y times the value of parameter
                      ( e.g rtt, jitterAvg, packetLossSD, mos ) is
                      below the threshold limits.
                      NOTE: When x > y, the probe will never
                            generate a reaction.

        average    - rttMonReactOccurred is set to true when the
                     average ( rttMonReactThresholdCountX times )
                     value of parameter for which reaction is
                     configured ( e.g rtt, jitterAvg, packetLossSD,
                     mos etc ) violates the threshold condition.
                     Conversely, it is set to false when the
                     average value of parameter ( e.g rtt, jitterAvg,
                     packetLossSD, mos etc ) is below the threshold
                     limits.

        If this value is changed by a management station,
        rttMonReactOccurred is set to false, but
        no reaction is generated if the prior value of
        rttMonReactOccurred was true."
    DEFVAL          { never }
    ::= { rttMonReactEntry 3 }

rttMonReactActionType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        trapOnly(2),
                        triggerOnly(3),
                        trapAndTrigger(4)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Specifies what type(s), if any, of reaction(s) to
        generate if an operation violates one of the watched
        ( reaction-configuration ) conditions:

        none               - no reaction is generated
        trapOnly           - a trap is generated
        triggerOnly        - all trigger actions defined for this
                             entry are initiated
        trapAndTrigger     - both a trap and all trigger actions
                             are initiated
        A trigger action is defined via the
        rttMonReactTriggerAdminTable."
    DEFVAL          { none }
    ::= { rttMonReactEntry 4 }

rttMonReactThresholdRising OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object defines the higher threshold limit.
        If the value ( e.g rtt, jitterAvg, packetLossSD etc ) rises
        above this limit and if the condition specified in
        rttMonReactThresholdType are satisfied, a trap is generated.

        Default value of rttMonReactThresholdRising for
           'rtt' is 5000
           'jitterAvg' is 100.
           'jitterSDAvg' is 100.
           'jitterDSAvg' 100.
           'packetLossSD' is 10000.
           'packetLossDS' is 10000.
           'mos' is 500.
           'icpif' is 93.
           'packetMIA' is 10000.
           'packetLateArrival' is 10000.
           'packetOutOfSequence' is 10000.
           'maxOfPositiveSD' is 10000.
           'maxOfNegativeSD' is 10000.
           'maxOfPositiveDS' is 10000.
           'maxOfNegativeDS' is 10000.
           'iaJitterDS' is 20.
           'frameLossDS' is 10000.
           'mosLQDS' is 400.
           'mosCQDS' is 400.
           'rFactorDS' is 80.
           'successivePacketLoss' is 1000.
           'maxOfLatencyDS' is 5000.
           'maxOfLatencySD' is 5000.
           'latencyDSAvg' is 5000.
           'latencySDAvg' is 5000.
           'packetLoss' is 10000.

        This object is not applicable if the rttMonReactVar is
        'timeout', 'connectionLoss' or 'verifyError'. For 'timeout',
        'connectionLoss' and 'verifyError' default value of
        rttMonReactThresholdRising will be 0."
    ::= { rttMonReactEntry 5 }

rttMonReactThresholdFalling OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object defines a lower threshold limit. If the
        value ( e.g rtt, jitterAvg, packetLossSD etc ) falls
        below this limit and if the conditions specified in
        rttMonReactThresholdType are satisfied, a trap is generated.

        Default value of rttMonReactThresholdFalling
           'rtt' is 3000
           'jitterAvg' is 100.
           'jitterSDAvg' is 100.
           'jitterDSAvg' 100.
           'packetLossSD' is 10000.
           'packetLossDS' is 10000.
           'mos' is 500.
           'icpif' is 93.
           'packetMIA' is 10000.
           'packetLateArrival' is 10000.
           'packetOutOfSequence' is 10000.
           'maxOfPositiveSD' is 10000.
           'maxOfNegativeSD' is 10000.
           'maxOfPositiveDS' is 10000.
           'maxOfNegativeDS' is 10000.
           'iaJitterDS' is 20.
           'frameLossDS' is 10000.
           'mosLQDS' is 310.
           'mosCQDS' is 310.
           'rFactorDS' is 60.
           'successivePacketLoss' is 1000.
           'maxOfLatencyDS' is 3000.
           'maxOfLatencySD' is 3000.
           'latencyDSAvg' is 3000.
           'latencySDAvg' is 3000.
           'packetLoss' is 10000.
           'iaJitterSD' is 20.
           'mosCQSD' is 310.
           'rFactorSD' is 60.

        This object is not applicable if the rttMonReactVar is
        'timeout', 'connectionLoss' or 'verifyError'. For 'timeout',
        'connectionLoss' and 'verifyError' default value of
        rttMonReactThresholdFalling will be 0."
    ::= { rttMonReactEntry 6 }

rttMonReactThresholdCountX OBJECT-TYPE
    SYNTAX          Integer32 (1..16)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "If rttMonReactThresholdType value is 'xOfy', this object
        defines the 'x' value.

        If rttMonReactThresholdType value is 'consecutive'
        this object defines the number of consecutive occurrences
        that needs threshold violation before setting
        rttMonReactOccurred as true.

        If rttMonReactThresholdType value is 'average' this object
        defines the number of samples that needs be considered for
        calculating average.

        This object has no meaning if rttMonReactThresholdType has
        value of 'never' and 'immediate'."
    DEFVAL          { 5 }
    ::= { rttMonReactEntry 7 }

rttMonReactThresholdCountY OBJECT-TYPE
    SYNTAX          Integer32 (1..16)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object defines the 'y' value of the xOfy condition
        if rttMonReactThresholdType is 'xOfy'.

        For other values of rttMonReactThresholdType, this object
        is not applicable."
    DEFVAL          { 5 }
    ::= { rttMonReactEntry 8 }

rttMonReactValue OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object will be set when the configured threshold condition
        is violated as defined by rttMonReactThresholdType and holds the
        actual value that violated the configured threshold values.

        This object is not valid for the following values of
        rttMonReactVar and It will be always 0:
          - timeout
          - connectionLoss
          - verifyError."
    DEFVAL          { 0 }
    ::= { rttMonReactEntry 9 }

rttMonReactOccurred OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is set to true when the configured threshold
        condition is violated as defined by rttMonReactThresholdType.
        It will be again set to 'false' if the condition reverses.

        This object is set to true in the following conditions:
         - rttMonReactVar is set to timeout and
           rttMonCtrlOperTimeoutOccurred set to true.
         - rttMonReactVar is set to connectionLoss and
           rttMonCtrlOperConnectionLostOccurred set to true.
         - rttMonReactVar is set to verifyError and
           rttMonCtrlOperVerifyErrorOccurred is set to true.
         - For all other values of rttMonReactVar, if the
           corresponding value exceeds the configured
           rttMonReactThresholdRising.

         This object is set to false in the following conditions:
         - rttMonReactVar is set to timeout and
           rttMonCtrlOperTimeoutOccurred set to false.
         - rttMonReactVar is set to connectionLoss and
           rttMonCtrlOperConnectionLostOccurred set to false.
         - rttMonReactVar is set to verifyError and
           rttMonCtrlOperVerifyErrorOccurred is set to false.
         - For all other values of rttMonReactVar, if the
           corresponding value fall below the configured
           rttMonReactThresholdFalling.

        When the RttMonRttType is 'pathEcho' or 'pathJitter',
        this object is applied only to the
        rttMonEchoAdminTargetAddress and not to intermediate
        hops to the Target."
    DEFVAL          { false }
    ::= { rttMonReactEntry 10 }

rttMonReactStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This objects indicates the status of the conceptual RTT
        Reaction Control Row.Only CreateAndGo and destroy
        operations are permitted on the row.

        When this object moves to active state, the conceptual row
        having the Reaction configuration for the probe is monitored
        and the notifications are generated when the threshold violation
        takes place.

        In order for this object to become active rttMonReactVar must
        be defined. All other objects assume the default value.

        This object can be set to 'destroy' from any value at any time.
        When this object is set to 'destroy' no reaction configuration
        for the probes would exist. The reaction configuration for the
        probe is removed."
    ::= { rttMonReactEntry 11 }


-- Generated Oper Table

rttMonGeneratedOperTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonGeneratedOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains information about the generated
        operation id as part of a parent IP SLA operation. The parent
        operation id is pseudo-random number, selected by the management
        station based on an operation started by the management
        station,when creating a row via the rttMonCtrlAdminStatus
        object in the rttMonCtrlAdminTable table."
    ::= { rttMonCtrl 20 }

rttMonGeneratedOperEntry OBJECT-TYPE
    SYNTAX          RttMonGeneratedOperEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in the Generated Oper table corresponding to
        a child or generated operation as part of a parent
        IP SLA operation."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonGeneratedOperRespIpAddrType,
                        rttMonGeneratedOperRespIpAddr
                    }
    ::= { rttMonGeneratedOperTable 1 }

RttMonGeneratedOperEntry ::= SEQUENCE {
        rttMonGeneratedOperRespIpAddrType InetAddressType,
        rttMonGeneratedOperRespIpAddr     InetAddress,
        rttMonGeneratedOperCtrlAdminIndex Unsigned32
}

rttMonGeneratedOperRespIpAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The type of Internet address, IPv4 or IPv6, of a responder
        for an IP SLA operation."
    ::= { rttMonGeneratedOperEntry 1 }

rttMonGeneratedOperRespIpAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The internet address of a responder for IP SLA
        operation. The type of this address is determined
        by the value of rttMonGeneratedOperRespIpAddrType."
    ::= { rttMonGeneratedOperEntry 2 }

rttMonGeneratedOperCtrlAdminIndex OBJECT-TYPE
    SYNTAX          Unsigned32 (1..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This is a pseudo-random number, auto-generated based
        to identify a child operation based on a parent
        operation started by the management station,when
        creating a row via the rttMonCtrlAdminStatus
        object."
    ::= { rttMonGeneratedOperEntry 3 }


-- Statistics Capture Table

rttMonStatsCaptureTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonStatsCaptureEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The statistics capture database.

        The statistics capture table contains summarized
        information of the results for a conceptual RTT control
        row.  A rolling accumulated history of this information
        is maintained in a series of hourly 'group(s)'.  Each
        'group' contains a series of 'path(s)', each 'path'
        contains a series of 'hop(s)', each 'hop' contains a
        series of 'statistics distribution bucket(s)'.

        Each conceptual statistics row has a current hourly
        group, into which RTT results are accumulated.  At the
        end of each hour a new hourly group is created which
        then becomes current.  The counters and accumulators in
        the new group are initialized to zero.  The previous
        group(s) is kept in the table until the table contains
        rttMonStatisticsAdminNumHourGroups groups for the
        conceptual statistics row;  at this point, the oldest
        group is discarded and is replaced by the newly created
        one.  The hourly group is uniquely identified by the
        rttMonStatsCaptureStartTimeIndex object.

        If the activity for a conceptual RTT control row ceases
        because the rttMonCtrlOperState object transitions to
        'inactive', the corresponding current hourly group in
        this table is 'frozen', and a new hourly group is
        created when activity is resumed.

        If the activity for a conceptual RTT control row ceases
        because the rttMonCtrlOperState object transitions to
        'pending' this whole table will be cleared and reset to
        its initial state.

        When the RttMonRttType is 'pathEcho', the path
        exploration RTT requests' statistics will not be
        accumulated in this table.

        NOTE: When the RttMonRttType is 'pathEcho', a source to
              target rttMonStatsCapturePathIndex path will be
              created for each rttMonStatsCaptureStartTimeIndex
              to hold all errors that occur when a specific path
              had not been found or connection has not be setup.

        Using this rttMonStatsCaptureTable, a managing
        application can retrieve summarized data from accurately
        measured periods, which is synchronized across multiple
        conceptual RTT control rows.  With the new hourly group
        creation being performed on a 60 minute period, the
        managing station has plenty of time to collect the data,
        and need not be concerned with the vagaries of network
        delays and lost PDU's when trying to get matching data.
        Also, the managing station can spread the data gathering
        over a longer period, which removes the need for a flood
        of get requests in a short period which otherwise would
        occur."
    ::= { rttMonStats 1 }

rttMonStatsCaptureEntry OBJECT-TYPE
    SYNTAX          RttMonStatsCaptureEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects which accumulate the results of a
        series of RTT operations over a 60 minute time period.

        The statistics capture table is a rollover table.  When
        rttMonStatsCaptureStartTimeIndex groups exceeds the
        rttMonStatisticsAdminNumHourGroups value, the oldest
        corresponding hourly group will be deleted and will be
        replaced with the new rttMonStatsCaptureStartTimeIndex
        hourly group.

        All other indices will fill to there maximum size.

        The statistics capture table has five indices.  Each
        described as follows:

          -  The first index correlates its entries to a
              conceptual RTT control row via the
              rttMonCtrlAdminIndex object.
          -  The second index is a rollover group and it
              uniquely identifies a 60 minute group. (The
              rttMonStatsCaptureStartTimeIndex object
              is used to make this value unique.)
          -  When the RttMonRttType is 'pathEcho', the third
              index uniquely identifies the paths in a
              statistics period.  (The period is 60
              minutes.)  A path will be created for each
              unique path through the network.  Note:  A
              path that does not contain the target is
              considered a different path than one which
              uses the exact same path, but does contain the
              target.  For all other values of RttMonRttType
              this index will be one.
          -  When the RttMonRttType is 'pathEcho', the fourth
              index uniquely identifies the hops in each path,
              as grouped by the third index.  This index does
              imply the order of the hops along the path to a
              target.  For all other values of RttMonRttType
              this index will be one.
          -  The fifth index uniquely creates a statistical
              distribution bucket."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonStatsCaptureStartTimeIndex,
                        rttMonStatsCapturePathIndex,
                        rttMonStatsCaptureHopIndex,
                        rttMonStatsCaptureDistIndex
                    }
    ::= { rttMonStatsCaptureTable 1 }

RttMonStatsCaptureEntry ::= SEQUENCE {
        rttMonStatsCaptureStartTimeIndex         TimeStamp,
        rttMonStatsCapturePathIndex              Integer32,
        rttMonStatsCaptureHopIndex               Integer32,
        rttMonStatsCaptureDistIndex              Integer32,
        rttMonStatsCaptureCompletions            Integer32,
        rttMonStatsCaptureOverThresholds         Integer32,
        rttMonStatsCaptureSumCompletionTime      Gauge32,
        rttMonStatsCaptureSumCompletionTime2Low  Gauge32,
        rttMonStatsCaptureSumCompletionTime2High Gauge32,
        rttMonStatsCaptureCompletionTimeMax      Gauge32,
        rttMonStatsCaptureCompletionTimeMin      Gauge32
}

rttMonStatsCaptureStartTimeIndex OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The time when this row was created.

        This object is the second index of the
        rttMonStatsCaptureTable Table.

        The the number of rttMonStatsCaptureStartTimeIndex
        groups exceeds the rttMonStatisticsAdminNumHourGroups
        value, the oldest rttMonStatsCaptureStartTimeIndex
        group will be removed and replaced with the new entry.

        When the RttMonRttType is 'pathEcho', this object also
        uniquely defines a group of paths.  See the
        rttMonStatsCaptureEntry object."
    ::= { rttMonStatsCaptureEntry 1 }

rttMonStatsCapturePathIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..128)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'pathEcho', this object
        uniquely defines a path for a given value of
        rttMonStatsCaptureStartTimeIndex.  For all other values
        of RttMonRttType, this object will be one.

        For a particular value of
        rttMonStatsCaptureStartTimeIndex, the agent assigns the
        first instance of a path a value of 1, then second
        instance a value of 2, and so on.  The sequence keeps
        incrementing until the number of paths equals
        rttMonStatisticsAdminNumPaths value, then no new paths
        are kept for the current rttMonStatsCaptureStartTimeIndex
        group.

        NOTE: A source to target rttMonStatsCapturePathIndex
              path will be created for each
              rttMonStatsCaptureStartTimeIndex to hold all
              errors that occur when a specific path or
              connection has not be setup.

        This value directly represents the path to
        a target. We can only support 128 paths."
    ::= { rttMonStatsCaptureEntry 2 }

rttMonStatsCaptureHopIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..30)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'pathEcho', this object
        uniquely defines a hop for a given value of
        rttMonStatsCapturePathIndex.  For all other values of
        RttMonRttType, this object will be one.

        For a particular value of rttMonStatsCapturePathIndex,
        the agent assigns the first instance of a hop
        a value of 1, then second instance a value of 2, and so
        on.  The sequence keeps incrementing until the number of
        hops equals rttMonStatisticsAdminNumHops value, then
        no new hops are kept for the current
        rttMonStatsCapturePathIndex.

        This value directly represents a hop along the path to
        a target, thus we can only support 30 hops.

        This value shows the order along the path to a target."
    ::= { rttMonStatsCaptureEntry 3 }

rttMonStatsCaptureDistIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..20)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object uniquely defines a statistical distribution
        bucket for a given value of rttMonStatsCaptureHopIndex.

        For a particular value of rttMonStatsCaptureHopIndex,
        the agent assigns the first instance of a distribution
        a value of 1, then second instance a value of 2, and so
        on.  The sequence keeps incrementing until the number of
        statistics distribution intervals equals
        rttMonStatisticsAdminNumDistBuckets value, then
        all values that fall above the last interval will
        be placed into the last interval.

        Each of these Statistics Distribution Buckets contain
        the results of each completion as defined by
        rttMonStatisticsAdminDistInterval object."
    ::= { rttMonStatsCaptureEntry 4 }

rttMonStatsCaptureCompletions OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT operations that have completed without
        an error and without timing out.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCaptureEntry 5 }

rttMonStatsCaptureOverThresholds OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT operations successfully completed, but
        in excess of rttMonCtrlAdminThreshold.  This number is a
        subset of the accumulation of all
        rttMonStatsCaptureCompletions.  The operation time
        of these completed operations will be accumulated.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCaptureEntry 6 }

rttMonStatsCaptureSumCompletionTime OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The accumulated completion time of RTT operations which
        complete successfully."
    ::= { rttMonStatsCaptureEntry 7 }

rttMonStatsCaptureSumCompletionTime2Low OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The low order 32 bits of the accumulated squares
        of completion times (in milliseconds) of RTT
        operations which complete successfully.

        Low/High order is defined where the binary number
        will look as follows:
        -------------------------------------------------
        | High order 32 bits    | Low order 32 bits     |
        -------------------------------------------------
        For example the number 4294967296 would have all
        Low order bits as '0' and the rightmost High
        order bit will be 1 (zeros,1)."
    ::= { rttMonStatsCaptureEntry 8 }

rttMonStatsCaptureSumCompletionTime2High OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The high order 32 bits of the accumulated squares
        of completion times (in milliseconds) of RTT
        operations which complete successfully.

        See the rttMonStatsCaptureSumCompletionTime2Low object
        for a definition of Low/High Order."
    ::= { rttMonStatsCaptureEntry 9 }

rttMonStatsCaptureCompletionTimeMax OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum completion time of any RTT operation which
        completes successfully."
    ::= { rttMonStatsCaptureEntry 10 }

rttMonStatsCaptureCompletionTimeMin OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum completion time of any RTT operation which
        completes successfully."
    ::= { rttMonStatsCaptureEntry 11 }


-- Statistics Collection Table

rttMonStatsCollectTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonStatsCollectEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The statistics collection database.

        This table has the exact same behavior as the
        rttMonStatsCaptureTable, except it does not keep
        statistical distribution information.

        For a complete table description see
        the rttMonStatsCaptureTable object."
    ::= { rttMonStats 2 }

rttMonStatsCollectEntry OBJECT-TYPE
    SYNTAX          RttMonStatsCollectEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects which accumulate the results of a
        series of RTT operations over a 60 minute time period.

        This entry has the exact same behavior as the
        rttMonStatsCaptureEntry, except it does not keep
        statistical distribution information.

        For a complete entry description see
        the rttMonStatsCaptureEntry object."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonStatsCaptureStartTimeIndex,
                        rttMonStatsCapturePathIndex,
                        rttMonStatsCaptureHopIndex
                    }
    ::= { rttMonStatsCollectTable 1 }

RttMonStatsCollectEntry ::= SEQUENCE {
        rttMonStatsCollectNumDisconnects Integer32,
        rttMonStatsCollectTimeouts       Integer32,
        rttMonStatsCollectBusies         Integer32,
        rttMonStatsCollectNoConnections  Integer32,
        rttMonStatsCollectDrops          Integer32,
        rttMonStatsCollectSequenceErrors Integer32,
        rttMonStatsCollectVerifyErrors   Integer32,
        rttMonStatsCollectAddress        RttMonTargetAddress,
        rttMonControlEnableErrors        Integer32,
        rttMonStatsRetrieveErrors        Integer32,
        rttMonStatsCollectCtrlEnErrors   Integer32,
        rttMonStatsCollectRetrieveErrors Integer32
}

rttMonStatsCollectNumDisconnects OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'echo' or pathEcho', this
        object represents the number of times that the target or
        hop along the path to a target became disconnected.  For
        all other values of RttMonRttType, this object will
        remain zero.

        For connectionless protocols this has no meaning,
        and will consequently remain 0.  When
        rttMonEchoAdminProtocol is one of snaRUEcho, this is
        the number of times that an LU-SSCP session was lost,
        for snaLU0EchoAppl, snaLU2EchoAppl, snaLu62Echo, and for
        snaLU62EchoAppl, this is the number of times that LU-LU
        session was lost.

        Since this error does not indicate any information about
        the failure of an RTT operation, no response time
        information for this instance will be recorded in the
        appropriate objects.

        If this error occurs and the rttMonStatsCapturePathIndex
        cannot be determined, this error will be accumulated in
        the source to target path, that will always exist.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 1 }

rttMonStatsCollectTimeouts OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of occasions when a RTT operation was not
        completed before a timeout occurred, i.e.
        rttMonCtrlAdminTimeout was exceeded.

        Since the RTT operation was never completed, the
        completion time of these operations are not accumulated,
        nor do they increment rttMonStatsCaptureCompletions (in
        any of the statistics distribution buckets).

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 2 }

rttMonStatsCollectBusies OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of occasions when a RTT operation could not
        be initiated because a previous RTT operation has not
        been completed.

        When the RttMonRttType is 'pathEcho' this can occur for
        both connection oriented protocols and connectionless
        protocols.

        When the RttMonRttType is 'echo' this can only occur for
        connection oriented protocols such as SNA.

        When the initiation of a new operation cannot be started,
        this object will be incremented and the operation will be
        omitted.  (The next operation will start at the next
        Frequency).  Since, a RTT operation was never initiated,
        the completion time of these operations is not
        accumulated, nor do they increment
        rttMonStatsCaptureCompletions.

        When the RttMonRttType is 'pathEcho', and this error
        occurs and the rttMonStatsCapturePathIndex cannot be
        determined, this error will be accumulated in the source
        to target path, that will always exist.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 3 }

rttMonStatsCollectNoConnections OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'echo' or 'pathEcho' this is
        the number of occasions when a RTT operation could not be
        initiated because the connection to the target has not
        been established.  For all other RttMonRttTypes this
        object will remain zero.

        This cannot occur for connectionless protocols, but may
        occur for connection oriented protocols, such as SNA.

        Since a RTT operation was never initiated, the completion
        time of these operations are not accumulated, nor do they
        increment rttMonStatsCaptureCompletions.

        If this error occurs and the rttMonStatsCapturePathIndex
        cannot be determined, this error will be accumulated
        in the source to target path, that will always exist.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 4 }

rttMonStatsCollectDrops OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of occasions when a RTT operation could not
        be initiated because some necessary internal resource
        (for example memory, or SNA subsystem) was not available,
        or the operation completion could not be recognized.

        Since a RTT operation was never initiated or was not
        recognized, the completion time of these operations
        are not accumulated, nor do they increment
        rttMonStatsCaptureCompletions (in the expected
        Distribution Bucket).

        When the RttMonRttType is 'pathEcho', and this error
        occurs and the rttMonStatsCapturePathIndex cannot be
        determined, this error will be accumulated in the
        source to target path, that will always exist.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 5 }

rttMonStatsCollectSequenceErrors OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'echo' of 'pathEcho' this is
        the number of RTT operation completions received with
        an unexpected sequence identifier.  For all other values
        of RttMonRttType this object will remain zero.

        When this has occurred some of the possible reasons may
        be:
           - a duplicate packet was received
           - a response was received after it had timed-out
           - a corrupted packet was received and was not detected

        The completion time of these operations are not
        accumulated, nor do they increment
        rttMonStatsCaptureCompletions (in the expected
        Distribution Bucket).

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 6 }

rttMonStatsCollectVerifyErrors OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT operation completions received with
        data that does not compare with the expected data.  The
        completion time of these operations are not accumulated,
        nor do they increment rttMonStatsCaptureCompletions (in
        the expected Distribution Bucket).

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 7 }

rttMonStatsCollectAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object only applies when the RttMonRttType is
        'echo', 'pathEcho', 'dlsw', 'udpEcho', 'tcpConnect'.
        For all other values of the RttMonRttType, this will be
        null.

        The object is a string which specifies the address of
        the target for the this RTT operation.

        This address will be the address of the hop along the
        path to the rttMonEchoAdminTargetAddress address,
        including rttMonEchoAdminTargetAddress address, or just
        the rttMonEchoAdminTargetAddress address, when the
        path information is not collected.  This behavior is
        defined by the rttMonCtrlAdminRttType object.

        The interpretation of this string depends on the type
        of RTT operation selected, as specified by the
        rttMonEchoAdminProtocol object."
    ::= { rttMonStatsCollectEntry 8 }

rttMonControlEnableErrors OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The number of occasions when control enable request failed.
        Currently it is used for multicast operation type.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object.
        rttMonControlEnableErrors object is superseded by
        rttMonStatsCollectCtrlEnErrors."
    ::= { rttMonStatsCollectEntry 9 }

rttMonStatsRetrieveErrors OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The number of occasions when stats retrieval request failed.
        Currently it is used for multicast operation type.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object.
        rttMonStatsRetrieveErrors object is superseded by
        rttMonStatsCollectRetrieveErrors."
    ::= { rttMonStatsCollectEntry 10 }

rttMonStatsCollectCtrlEnErrors OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object is same as rttMonControlEnableErrors,
        with corrected name for consistency.

        The number of occasions when control enable request failed.
        Currently it is used for multicast operation type.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 11 }

rttMonStatsCollectRetrieveErrors OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object is same as rttMonStatsRetrieveErrors,
        with corrected name for consistency.

        The number of occasions when stats retrieval request failed.
        Currently it is used for multicast operation type.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsCollectEntry 12 }


-- Statistics Totals Table

rttMonStatsTotalsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonStatsTotalsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The statistics totals database.

        This table has the exact same behavior as the
        rttMonStatsCaptureTable, except it only keeps
        60 minute group values.

        For a complete table description see
        the rttMonStatsCaptureTable object."
    ::= { rttMonStats 3 }

rttMonStatsTotalsEntry OBJECT-TYPE
    SYNTAX          RttMonStatsTotalsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects which accumulate the results of a
        series of RTT operations over a 60 minute time period.

        This entry has the exact same behavior as the
        rttMonStatsCaptureEntry, except it only keeps
        60 minute group values.

        For a complete entry description see
        the rttMonStatsCaptureEntry object."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonStatsCaptureStartTimeIndex
                    }
    ::= { rttMonStatsTotalsTable 1 }

RttMonStatsTotalsEntry ::= SEQUENCE {
        rttMonStatsTotalsElapsedTime TimeInterval,
        rttMonStatsTotalsInitiations Integer32
}

rttMonStatsTotalsElapsedTime OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The length of time since this conceptual statistics row
        was created."
    ::= { rttMonStatsTotalsEntry 1 }

rttMonStatsTotalsInitiations OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT operations that have been initiated.

        This number includes all RTT operations which succeed
        or fail for whatever reason.

        This object has the special behavior as defined by the
        ROLLOVER NOTE in the DESCRIPTION of the ciscoRttMonMIB
        object."
    ::= { rttMonStatsTotalsEntry 2 }


-- HTTP Statistics Table

rttMonHTTPStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonHTTPStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The HTTP statistics collection database.

        The HTTP statistics table contains summarized information of
        the results for a conceptual RTT control row. A rolling
        accumulated history of this information is maintained in a
        series of hourly 'group(s)'.

        The operation of this table is same as that of
        rttMonStatsCaptureTable, except that this table can only
        store a maximum of 2 hours of data."
    ::= { rttMonStats 4 }

rttMonHTTPStatsEntry OBJECT-TYPE
    SYNTAX          RttMonHTTPStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects which accumulate the results of a
        series of RTT operations over a 60 minute time period.

        This entry is created only if the rttMonCtrlAdminRttType
        is http. The operation of this table is same as that of
        rttMonStatsCaptureTable."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonHTTPStatsStartTimeIndex
                    }
    ::= { rttMonHTTPStatsTable 1 }

RttMonHTTPStatsEntry ::= SEQUENCE {
        rttMonHTTPStatsStartTimeIndex       TimeStamp,
        rttMonHTTPStatsCompletions          Counter32,
        rttMonHTTPStatsOverThresholds       Counter32,
        rttMonHTTPStatsRTTSum               Counter32,
        rttMonHTTPStatsRTTSum2Low           Counter32,
        rttMonHTTPStatsRTTSum2High          Counter32,
        rttMonHTTPStatsRTTMin               Gauge32,
        rttMonHTTPStatsRTTMax               Gauge32,
        rttMonHTTPStatsDNSRTTSum            Counter32,
        rttMonHTTPStatsTCPConnectRTTSum     Counter32,
        rttMonHTTPStatsTransactionRTTSum    Counter32,
        rttMonHTTPStatsMessageBodyOctetsSum Counter32,
        rttMonHTTPStatsDNSServerTimeout     Counter32,
        rttMonHTTPStatsTCPConnectTimeout    Counter32,
        rttMonHTTPStatsTransactionTimeout   Counter32,
        rttMonHTTPStatsDNSQueryError        Counter32,
        rttMonHTTPStatsHTTPError            Counter32,
        rttMonHTTPStatsError                Counter32,
        rttMonHTTPStatsBusies               Counter32
}

rttMonHTTPStatsStartTimeIndex OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This is the time when this row was created. This index
        uniquely identifies a HTTP Stats row in the
        rttMonHTTPStatsTable."
    ::= { rttMonHTTPStatsEntry 1 }

rttMonHTTPStatsCompletions OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of HTTP operations that have completed
        successfully."
    ::= { rttMonHTTPStatsEntry 2 }

rttMonHTTPStatsOverThresholds OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of HTTP operations that violate threshold."
    ::= { rttMonHTTPStatsEntry 3 }

rttMonHTTPStatsRTTSum OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of HTTP operations that are successfully measured."
    ::= { rttMonHTTPStatsEntry 4 }

rttMonHTTPStatsRTTSum2Low OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of the RTT's that are successfully
        measured (low order 32 bits)."
    ::= { rttMonHTTPStatsEntry 5 }

rttMonHTTPStatsRTTSum2High OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of the RTT's that are successfully
        measured (high order 32 bits)."
    ::= { rttMonHTTPStatsEntry 6 }

rttMonHTTPStatsRTTMin OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum RTT taken to perform HTTP operation."
    ::= { rttMonHTTPStatsEntry 7 }

rttMonHTTPStatsRTTMax OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum RTT taken to perform HTTP operation."
    ::= { rttMonHTTPStatsEntry 8 }

rttMonHTTPStatsDNSRTTSum OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT taken to perform DNS query within the
        HTTP operation."
    ::= { rttMonHTTPStatsEntry 9 }

rttMonHTTPStatsTCPConnectRTTSum OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT taken to connect to the HTTP server."
    ::= { rttMonHTTPStatsEntry 10 }

rttMonHTTPStatsTransactionRTTSum OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT taken to download the object specified by URL."
    ::= { rttMonHTTPStatsEntry 11 }

rttMonHTTPStatsMessageBodyOctetsSum OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of the size of the message body received as a
        response to the HTTP request."
    ::= { rttMonHTTPStatsEntry 12 }

rttMonHTTPStatsDNSServerTimeout OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of requests that could not connect to the
        DNS Server."
    ::= { rttMonHTTPStatsEntry 13 }

rttMonHTTPStatsTCPConnectTimeout OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of requests that could not connect to the
        the HTTP Server."
    ::= { rttMonHTTPStatsEntry 14 }

rttMonHTTPStatsTransactionTimeout OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of requests that timed out during HTTP
        transaction."
    ::= { rttMonHTTPStatsEntry 15 }

rttMonHTTPStatsDNSQueryError OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of requests that had DNS Query errors."
    ::= { rttMonHTTPStatsEntry 16 }

rttMonHTTPStatsHTTPError OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of requests that had HTTP errors while
        downloading the base page."
    ::= { rttMonHTTPStatsEntry 17 }

rttMonHTTPStatsError OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of occasions when a HTTP operation could not
        be initiated because an internal error"
    ::= { rttMonHTTPStatsEntry 18 }

rttMonHTTPStatsBusies OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of occasions when an HTTP operation could not
        be initiated because a previous HTTP operation has not
        been completed."
    ::= { rttMonHTTPStatsEntry 19 }


-- Jitter Statistics Table

rttMonJitterStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonJitterStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Jitter statistics collection database.

        The Jitter statistics table contains summarized information of
        the results for a conceptual RTT control row. A rolling
        accumulated history of this information is maintained in a
        series of hourly 'group(s)'.

        The operation of this table is same as that of
        rttMonStatsCaptureTable, except that this table will store
        2 hours of data."
    ::= { rttMonStats 5 }

rttMonJitterStatsEntry OBJECT-TYPE
    SYNTAX          RttMonJitterStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects which accumulate the results of a
        series of RTT operations over a 60 minute time period.

        This entry is created only if the rttMonCtrlAdminRttType
        is jitter. The operation of this table is same as that of
        rttMonStatsCaptureTable."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonJitterStatsStartTimeIndex
                    }
    ::= { rttMonJitterStatsTable 1 }

RttMonJitterStatsEntry ::= SEQUENCE {
        rttMonJitterStatsStartTimeIndex      TimeStamp,
        rttMonJitterStatsCompletions         Counter32,
        rttMonJitterStatsOverThresholds      Counter32,
        rttMonJitterStatsNumOfRTT            Counter32,
        rttMonJitterStatsRTTSum              Counter32,
        rttMonJitterStatsRTTSum2Low          Counter32,
        rttMonJitterStatsRTTSum2High         Counter32,
        rttMonJitterStatsRTTMin              Gauge32,
        rttMonJitterStatsRTTMax              Gauge32,
        rttMonJitterStatsMinOfPositivesSD    Gauge32,
        rttMonJitterStatsMaxOfPositivesSD    Gauge32,
        rttMonJitterStatsNumOfPositivesSD    Counter32,
        rttMonJitterStatsSumOfPositivesSD    Counter32,
        rttMonJitterStatsSum2PositivesSDLow  Counter32,
        rttMonJitterStatsSum2PositivesSDHigh Counter32,
        rttMonJitterStatsMinOfNegativesSD    Gauge32,
        rttMonJitterStatsMaxOfNegativesSD    Gauge32,
        rttMonJitterStatsNumOfNegativesSD    Counter32,
        rttMonJitterStatsSumOfNegativesSD    Counter32,
        rttMonJitterStatsSum2NegativesSDLow  Counter32,
        rttMonJitterStatsSum2NegativesSDHigh Counter32,
        rttMonJitterStatsMinOfPositivesDS    Gauge32,
        rttMonJitterStatsMaxOfPositivesDS    Gauge32,
        rttMonJitterStatsNumOfPositivesDS    Counter32,
        rttMonJitterStatsSumOfPositivesDS    Counter32,
        rttMonJitterStatsSum2PositivesDSLow  Counter32,
        rttMonJitterStatsSum2PositivesDSHigh Counter32,
        rttMonJitterStatsMinOfNegativesDS    Gauge32,
        rttMonJitterStatsMaxOfNegativesDS    Gauge32,
        rttMonJitterStatsNumOfNegativesDS    Counter32,
        rttMonJitterStatsSumOfNegativesDS    Counter32,
        rttMonJitterStatsSum2NegativesDSLow  Counter32,
        rttMonJitterStatsSum2NegativesDSHigh Counter32,
        rttMonJitterStatsPacketLossSD        Counter32,
        rttMonJitterStatsPacketLossDS        Counter32,
        rttMonJitterStatsPacketOutOfSequence Counter32,
        rttMonJitterStatsPacketMIA           Counter32,
        rttMonJitterStatsPacketLateArrival   Counter32,
        rttMonJitterStatsError               Counter32,
        rttMonJitterStatsBusies              Counter32,
        rttMonJitterStatsOWSumSD             Counter32,
        rttMonJitterStatsOWSum2SDLow         Counter32,
        rttMonJitterStatsOWSum2SDHigh        Counter32,
        rttMonJitterStatsOWMinSD             Counter32,
        rttMonJitterStatsOWMaxSD             Counter32,
        rttMonJitterStatsOWSumDS             Counter32,
        rttMonJitterStatsOWSum2DSLow         Counter32,
        rttMonJitterStatsOWSum2DSHigh        Counter32,
        rttMonJitterStatsOWMinDS             Counter32,
        rttMonJitterStatsOWMaxDS             Counter32,
        rttMonJitterStatsNumOfOW             Counter32,
        rttMonJitterStatsOWMinSDNew          Gauge32,
        rttMonJitterStatsOWMaxSDNew          Gauge32,
        rttMonJitterStatsOWMinDSNew          Gauge32,
        rttMonJitterStatsOWMaxDSNew          Gauge32,
        rttMonJitterStatsMinOfMOS            Gauge32,
        rttMonJitterStatsMaxOfMOS            Gauge32,
        rttMonJitterStatsMinOfICPIF          Gauge32,
        rttMonJitterStatsMaxOfICPIF          Gauge32,
        rttMonJitterStatsIAJOut              Gauge32,
        rttMonJitterStatsIAJIn               Gauge32,
        rttMonJitterStatsAvgJitter           Gauge32,
        rttMonJitterStatsAvgJitterSD         Gauge32,
        rttMonJitterStatsAvgJitterDS         Gauge32,
        rttMonJitterStatsUnSyncRTs           Counter32,
        rttMonJitterStatsRTTSumHigh          Counter32,
        rttMonJitterStatsOWSumSDHigh         Counter32,
        rttMonJitterStatsOWSumDSHigh         Counter32,
        rttMonJitterStatsNumOverThresh       Counter32
}

rttMonJitterStatsStartTimeIndex OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The time when this row was created."
    ::= { rttMonJitterStatsEntry 1 }

rttMonJitterStatsCompletions OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of jitter operation that have completed
        successfully."
    ::= { rttMonJitterStatsEntry 2 }

rttMonJitterStatsOverThresholds OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of jitter operations that violate threshold."
    ::= { rttMonJitterStatsEntry 3 }

rttMonJitterStatsNumOfRTT OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT's that are successfully measured."
    ::= { rttMonJitterStatsEntry 4 }

rttMonJitterStatsRTTSum OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's that are successfully measured (low order 32
        bits). The high order 32 bits are stored in
        rttMonJitterStatsRTTSumHigh."
    ::= { rttMonJitterStatsEntry 5 }

rttMonJitterStatsRTTSum2Low OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's that are successfully measured
        (low order 32 bits)."
    ::= { rttMonJitterStatsEntry 6 }

rttMonJitterStatsRTTSum2High OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's that are successfully measured
        (high order 32 bits)."
    ::= { rttMonJitterStatsEntry 7 }

rttMonJitterStatsRTTMin OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of RTT's that were successfully measured"
    ::= { rttMonJitterStatsEntry 8 }

rttMonJitterStatsRTTMax OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of RTT's that were successfully measured"
    ::= { rttMonJitterStatsEntry 9 }

rttMonJitterStatsMinOfPositivesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of absolute values of all positive jitter values
        from packets sent from source to destination."
    ::= { rttMonJitterStatsEntry 10 }

rttMonJitterStatsMaxOfPositivesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of absolute values of all positive jitter values
        from packets sent from source to destination."
    ::= { rttMonJitterStatsEntry 11 }

rttMonJitterStatsNumOfPositivesSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of number of all positive jitter values from packets
        sent from source to destination."
    ::= { rttMonJitterStatsEntry 12 }

rttMonJitterStatsSumOfPositivesSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of all positive jitter values from packets
        sent from source to destination."
    ::= { rttMonJitterStatsEntry 13 }

rttMonJitterStatsSum2PositivesSDLow OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of square of RTT's of all positive jitter values from
        packets sent from source to destination (low order 32 bits)."
    ::= { rttMonJitterStatsEntry 14 }

rttMonJitterStatsSum2PositivesSDHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of square of RTT's of all positive jitter values from
        packets sent from source to destination (high order 32 bits)."
    ::= { rttMonJitterStatsEntry 15 }

rttMonJitterStatsMinOfNegativesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all negative jitter values from packets sent
        from source to destination."
    ::= { rttMonJitterStatsEntry 16 }

rttMonJitterStatsMaxOfNegativesSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all negative jitter values from packets sent
        from source to destination."
    ::= { rttMonJitterStatsEntry 17 }

rttMonJitterStatsNumOfNegativesSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of number of all negative jitter values from packets
        sent from source to destination."
    ::= { rttMonJitterStatsEntry 18 }

rttMonJitterStatsSumOfNegativesSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's of all negative jitter values from packets
        sent from source to destination."
    ::= { rttMonJitterStatsEntry 19 }

rttMonJitterStatsSum2NegativesSDLow OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of square of RTT's of all negative jitter values from
        packets sent from source to destination (low order 32 bits)."
    ::= { rttMonJitterStatsEntry 20 }

rttMonJitterStatsSum2NegativesSDHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of square of RTT's of all negative jitter values from
        packets sent from source to destination (high order 32 bits)."
    ::= { rttMonJitterStatsEntry 21 }

rttMonJitterStatsMinOfPositivesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all positive jitter values from packets sent
        from destination to source."
    ::= { rttMonJitterStatsEntry 22 }

rttMonJitterStatsMaxOfPositivesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all positive jitter values from packets sent
        from destination to source."
    ::= { rttMonJitterStatsEntry 23 }

rttMonJitterStatsNumOfPositivesDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of number of all positive jitter values from packets
        sent from destination to source."
    ::= { rttMonJitterStatsEntry 24 }

rttMonJitterStatsSumOfPositivesDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's of all positive jitter values from packets
        sent from destination to source."
    ::= { rttMonJitterStatsEntry 25 }

rttMonJitterStatsSum2PositivesDSLow OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's of all positive jitter values from
        packets sent from destination to source (low order 32 bits)."
    ::= { rttMonJitterStatsEntry 26 }

rttMonJitterStatsSum2PositivesDSHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's of all positive jitter values from
        packets sent from destination to source (high order 32 bits)."
    ::= { rttMonJitterStatsEntry 27 }

rttMonJitterStatsMinOfNegativesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all negative jitter values from packets sent
        from destination to source."
    ::= { rttMonJitterStatsEntry 28 }

rttMonJitterStatsMaxOfNegativesDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all negative jitter values from packets sent
        from destination to source."
    ::= { rttMonJitterStatsEntry 29 }

rttMonJitterStatsNumOfNegativesDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of number of all negative jitter values from packets
        sent from destination to source."
    ::= { rttMonJitterStatsEntry 30 }

rttMonJitterStatsSumOfNegativesDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's of all negative jitter values from packets
        sent from destination to source."
    ::= { rttMonJitterStatsEntry 31 }

rttMonJitterStatsSum2NegativesDSLow OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's of all negative jitter values from
        packets sent from destination to source (low order 32 bits)."
    ::= { rttMonJitterStatsEntry 32 }

rttMonJitterStatsSum2NegativesDSHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of RTT's of all negative jitter values from
        packets sent from destination to source (high order 32 bits)."
    ::= { rttMonJitterStatsEntry 33 }

rttMonJitterStatsPacketLossSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets lost when sent from source to
        destination."
    ::= { rttMonJitterStatsEntry 34 }

rttMonJitterStatsPacketLossDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets lost when sent from destination to
        source."
    ::= { rttMonJitterStatsEntry 35 }

rttMonJitterStatsPacketOutOfSequence OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets arrived out of sequence."
    ::= { rttMonJitterStatsEntry 36 }

rttMonJitterStatsPacketMIA OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets that are lost for which we cannot
        determine the direction."
    ::= { rttMonJitterStatsEntry 37 }

rttMonJitterStatsPacketLateArrival OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets that arrived after the timeout."
    ::= { rttMonJitterStatsEntry 38 }

rttMonJitterStatsError OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of occasions when a jitter operation could not
        be initiated because an internal error"
    ::= { rttMonJitterStatsEntry 39 }

rttMonJitterStatsBusies OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of occasions when a jitter operation could not
        be initiated because a previous jitter operation has not
        been completed."
    ::= { rttMonJitterStatsEntry 40 }

rttMonJitterStatsOWSumSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way times from source to destination (low order
        32 bits). The high order 32 bits are stored in
        rttMonJitterStatsOWSumSDHigh."
    ::= { rttMonJitterStatsEntry 41 }

rttMonJitterStatsOWSum2SDLow OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way times from source to destination
        (low order 32 bits)."
    ::= { rttMonJitterStatsEntry 42 }

rttMonJitterStatsOWSum2SDHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way times from source to destination
        (high order 32 bits)."
    ::= { rttMonJitterStatsEntry 43 }

rttMonJitterStatsOWMinSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The minimum of all one way times from source to destination.
        rttMonJitterStatsOWMinSD object is superseded by
        rttMonJitterStatsOWMinSDNew."
    ::= { rttMonJitterStatsEntry 44 }

rttMonJitterStatsOWMaxSD OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The maximum of all one way times from source to destination.
        rttMonJitterStatsOWMaxSD object is superseded by
        rttMonJitterStatsOWMaxSDNew."
    ::= { rttMonJitterStatsEntry 45 }

rttMonJitterStatsOWSumDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way times from destination to source (low order
        32 bits). The high order 32 bits are stored in
        rttMonJitterStatsOWSumDSHigh."
    ::= { rttMonJitterStatsEntry 46 }

rttMonJitterStatsOWSum2DSLow OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way times from destination to source
        (low order 32 bits)."
    ::= { rttMonJitterStatsEntry 47 }

rttMonJitterStatsOWSum2DSHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of squares of one way times from destination to source
        (high order 32 bits)."
    ::= { rttMonJitterStatsEntry 48 }

rttMonJitterStatsOWMinDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The minimum of all one way times from destination to source.
        rttMonJitterStatsOWMinDS object is superseded by
        rttMonJitterStatsOWMinDSNew."
    ::= { rttMonJitterStatsEntry 49 }

rttMonJitterStatsOWMaxDS OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          deprecated
    DESCRIPTION
        "The maximum of all one way times from destination to source.
        rttMonJitterStatsOWMaxDS object is superseded by
        rttMonJitterStatsOWMaxDSNew."
    ::= { rttMonJitterStatsEntry 50 }

rttMonJitterStatsNumOfOW OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of one way times that are successfully measured."
    ::= { rttMonJitterStatsEntry 51 }

rttMonJitterStatsOWMinSDNew OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all one way times from source to destination.
        Replaces deprecated rttMonJitterStatsOWMinSD."
    ::= { rttMonJitterStatsEntry 52 }

rttMonJitterStatsOWMaxSDNew OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all one way times from source to destination.
        Replaces deprecated rttMonJitterStatsOWMaxSD."
    ::= { rttMonJitterStatsEntry 53 }

rttMonJitterStatsOWMinDSNew OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all one way times from destination to source.
        Replaces deprecated rttMonJitterStatsOWMinDS."
    ::= { rttMonJitterStatsEntry 54 }

rttMonJitterStatsOWMaxDSNew OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all one way times from destination to source.
        Replaces deprecated rttMonJitterStatsOWMaxDS"
    ::= { rttMonJitterStatsEntry 55 }

rttMonJitterStatsMinOfMOS OBJECT-TYPE
    SYNTAX          Gauge32 (0 | 100..500)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all MOS values for the jitter operations
        in hundreds.
        This value will be 0 if
         - rttMonEchoAdminCodecType of the operation is notApplicable
         - the operation is not started
         - the operation is started but failed
        This value will be 1 for packet loss of 10% or more."
    ::= { rttMonJitterStatsEntry 56 }

rttMonJitterStatsMaxOfMOS OBJECT-TYPE
    SYNTAX          Gauge32 (0 | 100..500)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all MOS values for the jitter operations
        in hunderds.
        This value will be 0 if
         - rttMonEchoAdminCodecType of the operation is notApplicable
         - the operation is not started
         - the operation is started but failed
        This value will be 1 for packet loss of 10% or more."
    ::= { rttMonJitterStatsEntry 57 }

rttMonJitterStatsMinOfICPIF OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of all ICPIF values for the jitter operations.

        This value will be 93 for packet loss of 10% or more."
    ::= { rttMonJitterStatsEntry 58 }

rttMonJitterStatsMaxOfICPIF OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of all ICPIF values for the jitter operations.

        This value will be 93 for packet loss of 10% or more."
    ::= { rttMonJitterStatsEntry 59 }

rttMonJitterStatsIAJOut OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Interarrival Jitter (RFC 1889) at responder"
    REFERENCE
        "Refer to the following documents for the definition: RFC 1889"
    ::= { rttMonJitterStatsEntry 60 }

rttMonJitterStatsIAJIn OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Interarrival Jitter (RFC 1889) at sender"
    REFERENCE
        "Refer to the following documents for the definition: RFC 1889"
    ::= { rttMonJitterStatsEntry 61 }

rttMonJitterStatsAvgJitter OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average of positive and negative jitter
        values for SD and DS direction."
    ::= { rttMonJitterStatsEntry 62 }

rttMonJitterStatsAvgJitterSD OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average of positive and negative jitter
        values in SD direction."
    ::= { rttMonJitterStatsEntry 63 }

rttMonJitterStatsAvgJitterDS OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average of positive and negative jitter
        values in DS direction."
    ::= { rttMonJitterStatsEntry 64 }

rttMonJitterStatsUnSyncRTs OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of RTT operations that have completed with
        sender and responder out of sync with NTP. The NTP sync means
        the total of NTP offset on sender and responder is within
        configured tolerance level."
    ::= { rttMonJitterStatsEntry 65 }

-- I think just like the rttMonJitterStatsRTTSum, this object is for
-- aggregated statistcs, so shouldn't be gauge32 as it will be only
-- increased.
-- Have incorporated other comments.

rttMonJitterStatsRTTSumHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of RTT's that are successfully measured
        (high order 32 bits). The low order 32 bits are
        stored in rttMonJitterStatsRTTSum."
    ::= { rttMonJitterStatsEntry 66 }

rttMonJitterStatsOWSumSDHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way times from source to destination
        (high order 32 bits). The low order 32 bits are
        stored in rttMonJitterStatsOWSumSD."
    ::= { rttMonJitterStatsEntry 67 }

rttMonJitterStatsOWSumDSHigh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The sum of one way times from destination to source
        (high order 32 bits). The low order 32 bits are stored
        in rttMonJitterStatsOWSumDS."
    ::= { rttMonJitterStatsEntry 68 }

rttMonJitterStatsNumOverThresh OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of RTTs that were over
        the threshold value."
    ::= { rttMonJitterStatsEntry 69 }


-- Auto SAA L3 MPLS VPN LPD Group Stats Collection Table

rttMonLpdGrpStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonLpdGrpStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The Auto SAA L3 MPLS VPN LPD Group Database.

        The LPD Group statistics table contains summarized performance
        statistics for the LPD group.

        LPD Group - The set of 'single probes' which are subset of the
        'lspGroup' probe traversing set of paths between two PE end
        points are grouped together and called as the LPD group. The
        LPD group will be uniquely referenced by the LPD Group ID.

        A rolling accumulated history of this information is maintained
        in a series of hourly 'group(s)'.

        Each conceptual statistics row has a current hourly group, into
        which RTT results are accumulated. At the end of each hour a new
        hourly group is created which then becomes current. The
        counters and accumulators in the new group are initialized to
        zero. The previous group(s) is kept in the table until the table
        contains rttMplsVpnMonTypeLpdStatHours groups for the
        conceptual statistics row;  at this point, the oldest group is
        discarded and is replaced by the newly created one. The hourly
        group is uniquely identified by the
        rttMonLpdGrpStatsStartTimeIndex object."
    ::= { rttMonStats 7 }

rttMonLpdGrpStatsEntry OBJECT-TYPE
    SYNTAX          RttMonLpdGrpStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of objects which accumulate the results of a set of RTT
        operations over a 60 minute time period.

        The LPD group statistics table is a rollover table. When
        rttMonLpdGrpStatsStartTimeIndex groups exceeds the
        rttMplsVpnMonTypeLpdStatHours value, the oldest corresponding
        hourly group will be deleted and will be replaced with the new
        rttMonLpdGrpStatsStartTimeIndex hourly group.

        The LPD group statistics table has two indices. Each described
        as follows:

        - The first index correlates its entries to a LPD group via the
           rttMonLpdGrpStatsGroupIndex object.
        - The second index is a rollover group and it uniquely
           identifies a 60 minute group. (The
           rttMonLpdGrpStatsStartTimeIndex is used to make this value
           unique.)"
    INDEX           {
                        rttMonLpdGrpStatsGroupIndex,
                        rttMonLpdGrpStatsStartTimeIndex
                    }
    ::= { rttMonLpdGrpStatsTable 1 }

RttMonLpdGrpStatsEntry ::= SEQUENCE {
        rttMonLpdGrpStatsGroupIndex      Integer32,
        rttMonLpdGrpStatsStartTimeIndex  TimeStamp,
        rttMonLpdGrpStatsTargetPE        RttMonTargetAddress,
        rttMonLpdGrpStatsNumOfPass       Integer32,
        rttMonLpdGrpStatsNumOfFail       Integer32,
        rttMonLpdGrpStatsNumOfTimeout    Integer32,
        rttMonLpdGrpStatsAvgRTT          Integer32,
        rttMonLpdGrpStatsMinRTT          Integer32,
        rttMonLpdGrpStatsMaxRTT          Integer32,
        rttMonLpdGrpStatsMinNumPaths     Integer32,
        rttMonLpdGrpStatsMaxNumPaths     Integer32,
        rttMonLpdGrpStatsLPDStartTime    TimeStamp,
        rttMonLpdGrpStatsLPDFailOccurred TruthValue,
        rttMonLpdGrpStatsLPDFailCause    RttMplsVpnMonLpdFailureSense,
        rttMonLpdGrpStatsLPDCompTime     Integer32,
        rttMonLpdGrpStatsGroupStatus     RttMplsVpnMonLpdGrpStatus,
        rttMonLpdGrpStatsGroupProbeIndex Integer32,
        rttMonLpdGrpStatsPathIds         DisplayString,
        rttMonLpdGrpStatsProbeStatus     DisplayString,
        rttMonLpdGrpStatsResetTime       TimeStamp
}

rttMonLpdGrpStatsGroupIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Uniquely identifies a row in rttMonLpdGrpStatsTable.

        This is a pseudo-random number which identifies a particular
        LPD group."
    ::= { rttMonLpdGrpStatsEntry 1 }

rttMonLpdGrpStatsStartTimeIndex OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The time when this row was created.

        This object is the second index of the rttMonLpdGrpStatsTable.
        When the number of rttMonLpdGrpStatsStartTimeIndex groups
        exceeds the rttMplsVpnMonTypeLpdStatHours value, the oldest
        rttMonLpdGrpStatsStartTimeIndex group will be removed and
        replaced with the new entry."
    ::= { rttMonLpdGrpStatsEntry 2 }

rttMonLpdGrpStatsTargetPE OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The object is a string that specifies the address of the
        target PE for this LPD group."
    ::= { rttMonLpdGrpStatsEntry 3 }

rttMonLpdGrpStatsNumOfPass OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "passes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the number of successfull completions
        of 'single probes' for all the set of paths in the LPD group.

        Whenever the rttMonLatestRttOperSense value is 'ok' for a
        particular probe in the LPD Group this object will be
        incremented.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 4 }

rttMonLpdGrpStatsNumOfFail OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the number of failed operations of
        'single probes' for all the set of paths in the LPD group.

        Whenever the rttMonLatestRttOperSense has a value other than
        'ok' or 'timeout' for a particular probe in the LPD Group this
        object will be incremented.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 5 }

rttMonLpdGrpStatsNumOfTimeout OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "timeouts"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the number of timed out operations of
        'single probes' for all the set of paths in the LPD group.

        Whenever the rttMonLatestRttOperSense has a value of 'timeout'
        for a particular probe in the LPD Group this object will be
        incremented.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 6 }

rttMonLpdGrpStatsAvgRTT OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The average RTT across all set of probes in the LPD group.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 7 }

rttMonLpdGrpStatsMinRTT OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum of RTT's for all set of probes in the LPD group
        that were successfully measured.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 8 }

rttMonLpdGrpStatsMaxRTT OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum of RTT's for all set of probes in the LPD group
        that were successfully measured.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 9 }

rttMonLpdGrpStatsMinNumPaths OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "paths"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The minimum number of active paths discovered to the
        rttMonLpdGrpStatsTargetPE target.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 10 }

rttMonLpdGrpStatsMaxNumPaths OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    UNITS           "paths"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum number of active paths discovered to the
        rttMonLpdGrpStatsTargetPE target.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 11 }

rttMonLpdGrpStatsLPDStartTime OBJECT-TYPE
    SYNTAX          TimeStamp
    UNITS           "tenths of milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The time when the last LSP Path Discovery to the group was
        attempted.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 12 }

rttMonLpdGrpStatsLPDFailOccurred OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is set to true when the LSP Path Discovery
        to the target PE i.e. rttMonLpdGrpStatsTargetPE fails, and
        set to false when the LSP Path Discovery succeeds.

        When this value changes and rttMplsVpnMonReactLpdNotifyType is
        set to 'lpdPathDiscovery' or 'lpdAll' a
        rttMonLpdDiscoveryNotification will be generated.

        This object will be set to 'FALSE' on reset."
    ::= { rttMonLpdGrpStatsEntry 13 }

rttMonLpdGrpStatsLPDFailCause OBJECT-TYPE
    SYNTAX          RttMplsVpnMonLpdFailureSense
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object identifies the cause of failure for the LSP Path
        Discovery last attempted. It will be only valid if
        rttMonLpdGrpStatsLPDFailOccurred is set to true.

        This object will be set to 'unknown' on reset."
    ::= { rttMonLpdGrpStatsEntry 14 }

rttMonLpdGrpStatsLPDCompTime OBJECT-TYPE
    SYNTAX          Integer32 (0..65535)
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The completion time of the last successfull LSP Path Discovery
        to the target PE.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 15 }

rttMonLpdGrpStatsGroupStatus OBJECT-TYPE
    SYNTAX          RttMplsVpnMonLpdGrpStatus
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object identifies the LPD Group status.

        When the LPD Group status changes and
        rttMplsVpnMonReactLpdNotifyType is set to 'lpdGroupStatus' or
        'lpdAll' a rttMonLpdGrpStatusNotification will be generated.

        When the LPD Group status value is 'unknown' or changes to
        'unknown' this notification will not be generated.

        When LSP Path Discovery is enabled for a particular row in
        rttMplsVpnMonCtrlTable, 'single probes' in the 'lspGroup' probe
        cannot generate notifications independently but will be
        generating depending on the state of the group. Notifications
        are only generated if the failure/restoration of an individual
        probe causes the state of the LPD Group to change.

        This object will be set to 'unknown' on reset."
    ::= { rttMonLpdGrpStatsEntry 16 }

rttMonLpdGrpStatsGroupProbeIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    UNITS           "identifier"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object identifies 'lspGroup' probe uniquely created for
        this particular LPD Group."
    ::= { rttMonLpdGrpStatsEntry 17 }

rttMonLpdGrpStatsPathIds OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string which holds the list of information to uniquely
        identify the paths to the target PE. This information is used
        by the 'single probes' when testing the paths.

        Following three parameters are needed to uniquely identify a
        path
          - lsp-selector (127.x.x.x)
          - outgoing-interface (i/f)
          - label-stack (s), if mutiple labels they will be colon (:)
            separated.

        These parameters will be hyphen (-) separated for a particular
        path. This set of information will be comma (,) separated for
        all the paths discovered as part of this LPD Group.

        For example: If there are 5 paths in the LPD group then this
        object will return all the identifier's to uniquely identify
        the path.

        The output will look like '127.0.0.1-Se3/0.1-20:18,
        *********-Se3/0.1-20,*********-Se3/0.1-20,*********-Se3/0.1-20,
        *********-Se3/0.1-20'.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 18 }

rttMonLpdGrpStatsProbeStatus OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A string which holds the latest operation return code for
        all the set of 'single probes' which are part of the LPD group.
        The return codes will be comma separated and will follow the
        same sequence of probes as followed in
        'rttMonLpdGrpStatsPathIds'. The latest operation return code
        will be mapped to 'up','down' or 'unkwown'.

        'up' - Probe state is up when the rttMonLatestRttOperSense
        value is 'ok'.
        'down' - Probe state is down when the rttMonLatestRttOperSense
        has value other then 'ok' and 'other'.
        'unknown' - Probe state is unkown when the
        rttMonLatestRttOperSense value is 'other'.

        For example: If there are 5 paths in the LPD group then this
        object output will look like 'ok,ok,ok,down,down'.

        This object will be set to '0' on reset."
    ::= { rttMonLpdGrpStatsEntry 19 }

rttMonLpdGrpStatsResetTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object specifies the time when this statistics row was
        last reset using the rttMonApplLpdGrpStatsReset object."
    ::= { rttMonLpdGrpStatsEntry 20 }


-- History Collection Table

rttMonHistoryCollectionTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF RttMonHistoryCollectionEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The history collection database.

        The history table contains a point by point rolling
        history of the most recent RTT operations for each
        conceptual RTT control row.  The rolling history of this
        information is maintained in a series of 'live(s)', each
        containing a series of 'bucket(s)', each 'bucket'
        contains a series of 'sample(s)'.

        Each conceptual history row can have lives.  A life is
        defined by the rttMonCtrlOperRttLife object.  A new life
        will be created when rttMonCtrlOperState transitions
        'active'.  When the number of lives become greater
        than rttMonHistoryAdminNumLives the oldest life will be
        discarded and a new life will be created by incrementing
        the index.

        The path exploration RTT operation will be kept as an
        entry in this table."
    ::= { rttMonHistory 1 }

rttMonHistoryCollectionEntry OBJECT-TYPE
    SYNTAX          RttMonHistoryCollectionEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A list of history objects that are recorded for each
        RTT operation.

        The history collection table has four indices.  Each
        described as follows:
          -  The first index correlates its entries to a
              conceptual RTT control row via the
              rttMonCtrlAdminIndex object.
          -  The second index uniquely identifies the results
              of each 'life' as defined by the
              rttMonCtrlOperRttLife object.
          -  The third index uniquely identifies the number of
              buckets in a life.  A bucket will contain one
              sample per bucket if the rttMonCtrlAdminRttType
              object is set to any value
              other than 'pathEcho'.  If the
              rttMonCtrlAdminRttType object is set to
              'pathEcho', a bucket will contain one sample per
              hop along a path to the target (including the
              target).
          -  The fourth index uniquely identifies the number of
              samples in a bucket.   Again, if the
              rttMonCtrlAdminRttType object is set to
              'pathEcho', this value is associated with each
              hop in an ascending order, thus for the
              first hop on a path, this index will be 1, the
              second will be 2 and so on.   For all other values
              of rttMonCtrlAdminRttType this will be 1."
    INDEX           {
                        rttMonCtrlAdminIndex,
                        rttMonHistoryCollectionLifeIndex,
                        rttMonHistoryCollectionBucketIndex,
                        rttMonHistoryCollectionSampleIndex
                    }
    ::= { rttMonHistoryCollectionTable 1 }

RttMonHistoryCollectionEntry ::= SEQUENCE {
        rttMonHistoryCollectionLifeIndex         Integer32,
        rttMonHistoryCollectionBucketIndex       Integer32,
        rttMonHistoryCollectionSampleIndex       Integer32,
        rttMonHistoryCollectionSampleTime        TimeStamp,
        rttMonHistoryCollectionAddress           RttMonTargetAddress,
        rttMonHistoryCollectionCompletionTime    Gauge32,
        rttMonHistoryCollectionSense             RttResponseSense,
        rttMonHistoryCollectionApplSpecificSense Integer32,
        rttMonHistoryCollectionSenseDescription  DisplayString
}

rttMonHistoryCollectionLifeIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This uniquely defines a life for a conceptual history
        row.

        For a particular value of rttMonHistoryCollectionLifeIndex,
        the agent assigns the first value of 1, the second value
        of 2, and so on.  The sequence keeps incrementing,
        despite older (lower) values being removed from the
        table."
    ::= { rttMonHistoryCollectionEntry 1 }

rttMonHistoryCollectionBucketIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'pathEcho', this uniquely
        defines a bucket for a given value of
        rttMonHistoryCollectionLifeIndex.  For all other
        RttMonRttType this value will be the number of
        operations per a lifetime.  Thus, this object
        increments on each operation attempt.

        For a particular value of
        rttMonHistoryCollectionLifeIndex, the agent assigns
        the first value of 1, the second value of 2, and so
        on.  The sequence keeps incrementing until the number
        of buckets equals rttMonHistoryAdminNumBuckets, after
        which the most recent rttMonHistoryAdminNumBuckets
        buckets are retained (the index is incremented though)."
    ::= { rttMonHistoryCollectionEntry 2 }

rttMonHistoryCollectionSampleIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..512)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This uniquely defines a row for a given value of
        rttMonHistoryCollectionBucketIndex.  This object
        represents a hop along a path to the Target.

        For a particular value of
        rttMonHistoryCollectionBucketIndex, the agent assigns
        the first value of 1, the second value of 2, and so on.
        The sequence keeps incrementing until the number of
        samples equals rttMonHistoryAdminNumSamples, then no
        new samples are created for the current
        rttMonHistoryCollectionBucketIndex.

        When the RttMonRttType is 'pathEcho', this value
        directly represents the number of hops along a
        path to a target, thus we can only support 512 hops.
        For all other values of RttMonRttType this object
        will be one."
    ::= { rttMonHistoryCollectionEntry 3 }

rttMonHistoryCollectionSampleTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The time that the RTT operation was initiated."
    ::= { rttMonHistoryCollectionEntry 4 }

rttMonHistoryCollectionAddress OBJECT-TYPE
    SYNTAX          RttMonTargetAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "When the RttMonRttType is 'echo' or 'pathEcho' this
        is a string which specifies the address of the target for
        the this RTT operation.  For all other values of
        RttMonRttType this string will be null.

        This address will be the address of the hop along the
        path to the rttMonEchoAdminTargetAddress address,
        including rttMonEchoAdminTargetAddress address, or just
        the rttMonEchoAdminTargetAddress address, when the
        path information is not collected.  This behavior is
        defined by the rttMonCtrlAdminRttType object.

        The interpretation of this string depends on the type
        of RTT operation selected, as specified by the
        rttMonEchoAdminProtocol object.

        See rttMonEchoAdminTargetAddress for a complete
        description."
    ::= { rttMonHistoryCollectionEntry 5 }

rttMonHistoryCollectionCompletionTime OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This is the operation completion time of the RTT
        operation.  If the RTT operation fails
        (rttMonHistoryCollectionSense is any
        value other than ok), this has a value of 0."
    ::= { rttMonHistoryCollectionEntry 6 }

rttMonHistoryCollectionSense OBJECT-TYPE
    SYNTAX          RttResponseSense
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A sense code for the completion status of the RTT
        operation."
    ::= { rttMonHistoryCollectionEntry 7 }

rttMonHistoryCollectionApplSpecificSense OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An application specific sense code for the
        completion status of the last RTT operation.  This
        object will only be valid when the
        rttMonHistoryCollectionSense object is set to
        'applicationSpecific'.  Otherwise, this object's
        value is not valid."
    ::= { rttMonHistoryCollectionEntry 8 }

rttMonHistoryCollectionSenseDescription OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A sense description for the completion status of
        the last RTT operation when the
        rttMonHistoryCollectionSense object is set to
        'applicationSpecific'."
    ::= { rttMonHistoryCollectionEntry 9 }


-- NOTIFICATION DEFINITIONS
--
-- This section defines the traps that
-- can be generated by the agent.

rttMonNotificationsPrefix  OBJECT IDENTIFIER
    ::= { ciscoRttMonMIB 2 }

rttMonNotifications  OBJECT IDENTIFIER
    ::= { rttMonNotificationsPrefix 0 }


rttMonConnectionChangeNotification NOTIFICATION-TYPE
    OBJECTS         {
                        rttMonCtrlAdminTag,
                        rttMonHistoryCollectionAddress,
                        rttMonCtrlOperConnectionLostOccurred
                    }
    STATUS          deprecated
    DESCRIPTION
        "This notification is only valid when the RttMonRttType
        is 'echo' or 'pathEcho'.

        A rttMonConnectionChangeNotification indicates that a
        connection to a target (not to a hop along the path
        to a target) has either failed on establishment or
        been lost and when reestablished.  Precisely, this
        has resulted in rttMonCtrlOperConnectionLostOccurred
        changing value.

        If History is not being collected, the instance values
        for the rttMonHistoryCollectionAddress object will not
        be valid.  When RttMonRttType is not 'echo' or 'pathEcho'
        the rttMonHistoryCollectionAddress object will be null.
        rttMonConnectionChangeNotification object is superseded by
        rttMonNotification."
   ::= { rttMonNotifications 1 }

rttMonTimeoutNotification NOTIFICATION-TYPE
    OBJECTS         {
                        rttMonCtrlAdminTag,
                        rttMonHistoryCollectionAddress,
                        rttMonCtrlOperTimeoutOccurred
                    }
    STATUS          deprecated
    DESCRIPTION
        "A rttMonTimeoutNotification indicates the occurrence of
        a timeout for a RTT operation, and it indicates the
        clearing of such a condition by a subsequent RTT
        operation. Precisely, this has resulted in
        rttMonCtrlOperTimeoutOccurred changing value.

        When the RttMonRttType is 'pathEcho', this
        notification will only be sent when the timeout
        occurs during an operation to the target and not to
        a hop along the path to the target.  This also
        applies to the clearing of the timeout.

        If History is not being collected, the instance values
        for the rttMonHistoryCollectionAddress object will not
        be valid.  When RttMonRttType is not 'echo' or 'pathEcho'
        the rttMonHistoryCollectionAddress object will be null.
        rttMonTimeoutNotification object is superseded by
        rttMonNotification."
   ::= { rttMonNotifications 2 }

rttMonThresholdNotification NOTIFICATION-TYPE
    OBJECTS         {
                        rttMonCtrlAdminTag,
                        rttMonHistoryCollectionAddress,
                        rttMonCtrlOperOverThresholdOccurred
                    }
    STATUS          deprecated
    DESCRIPTION
        "A rttMonThresholdNotification indicates the
        occurrence of a threshold violation for a RTT operation,
        and it indicates the previous violation has subsided for
        a subsequent RTT operation.  Precisely, this has resulted
        in rttMonCtrlOperOverThresholdOccurred changing value.

        When the RttMonRttType is 'pathEcho', this
        notification will only be sent when the threshold
        violation occurs during an operation to the target and
        not to a hop along the path to the target.  This also
        applies to the subsiding of a threshold condition.

        If History is not being collected, the instance values
        for the rttMonHistoryCollectionAddress object will not
        be valid.  When RttMonRttType is not 'echo' or 'pathEcho'
        the rttMonHistoryCollectionAddress object will be null.

        rttMonThresholdNotification object is superseded by
        rttMonNotification."
   ::= { rttMonNotifications 3 }

rttMonVerifyErrorNotification NOTIFICATION-TYPE
    OBJECTS         {
                        rttMonCtrlAdminTag,
                        rttMonHistoryCollectionAddress,
                        rttMonCtrlOperVerifyErrorOccurred
                    }
    STATUS          deprecated
    DESCRIPTION
        "A rttMonVerifyErrorNotification indicates the
        occurrence of a data corruption in an RTT operation.
        rttMonVerifyErrorNotification object is superseded by
        rttMonNotification."
   ::= { rttMonNotifications 4 }

-- %DNP% The rttMonEchoAdminLSPSelector object was added to address
-- %DNP% customer problem field trial. The prior version of the MIB
-- %DNP% has not been published externally.

rttMonNotification NOTIFICATION-TYPE
    OBJECTS         {
                        rttMonCtrlAdminTag,
                        rttMonHistoryCollectionAddress,
                        rttMonReactVar,
                        rttMonReactOccurred,
                        rttMonReactValue,
                        rttMonReactThresholdRising,
                        rttMonReactThresholdFalling,
                        rttMonEchoAdminLSPSelector
                    }
    STATUS          deprecated
    DESCRIPTION
        "A rttMonNotification indicates the occurrence of a
        threshold violation, and it indicates the previous
        violation has subsided for a subsequent operation.

        When the RttMonRttType is 'pathEcho', this
        notification will only be sent when the threshold
        violation occurs during an operation to the target and
        not to a hop along the path to the target. This also
        applies to the subsiding of a threshold condition.

        If History is not being collected, the instance values
        for the rttMonHistoryCollectionAddress object will not
        be valid. When RttMonRttType is not 'echo' or 'pathEcho'
        the rttMonHistoryCollectionAddress object will be null.

        rttMonReactVar defines the type of reaction that is
        configured for the probe ( e.g jitterAvg, rtt etc ).
        In the rttMonReactTable there are trap definitions
        for the probes and each probe may have more than
        one trap definitions for various types ( e.g rtt,
        jitterAvg, packetLoossSD etc ). So the object rttMonReactVar
        indicates the type ( e.g. rtt, packetLossSD, timeout etc )
        for which threshold violation traps has been generated.

        The object rttMonEchoAdminLSPSelector will be valid only
        for the probes based on 'mplsLspPingAppl' RttMonProtocol. For
        all other probes it will be null.

        rttMonNotification object is superseded by
        rttMonNotificationV2."
   ::= { rttMonNotifications 5 }

rttMonLpdDiscoveryNotification NOTIFICATION-TYPE
    OBJECTS         {
                        rttMplsVpnMonCtrlTag,
                        rttMonLpdGrpStatsTargetPE,
                        rttMonLpdGrpStatsLPDFailCause,
                        rttMonLpdGrpStatsLPDFailOccurred
                    }
    STATUS          current
    DESCRIPTION
        "A rttMonLpdDiscoveryNotification indicates that the LSP Path
        Discovery to the target PE has failed, and it also indicates
        the clearing of such condition. Precisely this has resulted in
        rttMonLpdGrpStatsLPDFailOccurred changing value.

        When the rttMonLpdGrpStatsLPDFailOccurred is 'false', the
        instance value for rttMonLpdGrpStatsLPDFailCause is not valid."
   ::= { rttMonNotifications 6 }

rttMonLpdGrpStatusNotification NOTIFICATION-TYPE
    OBJECTS         {
                        rttMplsVpnMonCtrlTag,
                        rttMonLpdGrpStatsTargetPE,
                        rttMonLpdGrpStatsGroupStatus
                    }
    STATUS          current
    DESCRIPTION
        "A rttMonLpdGrpStatusNotification indicates that the LPD
        Group status rttMonLpdGrpStatsGroupStatus has changed indicating
        some connectivity change to the target PE.
        This has resulted in rttMonLpdGrpStatsGroupStatus changing
        value."
   ::= { rttMonNotifications 7 }

rttMonNotificationV2 NOTIFICATION-TYPE
    OBJECTS         {
                        rttMonCtrlAdminLongTag,
                        rttMonHistoryCollectionAddress,
                        rttMonReactVar,
                        rttMonReactOccurred,
                        rttMonReactValue,
                        rttMonReactThresholdRising,
                        rttMonReactThresholdFalling,
                        rttMonEchoAdminLSPSelector
                    }
    STATUS          current
    DESCRIPTION
        "A rttMonNotification indicates the occurrence of a
        threshold violation, and it indicates the previous
        violation has subsided for a subsequent operation.

        Enhanced version of rttMonNotification which replaces
        rttMonCtrlAdminTag with rttMonCtrlAdminLongTag object."
   ::= { rttMonNotifications 8 }
-- Conformance Information

ciscoRttMonMibConformance  OBJECT IDENTIFIER
    ::= { ciscoRttMonMIB 3 }

ciscoRttMonMibCompliances  OBJECT IDENTIFIER
    ::= { ciscoRttMonMibConformance 1 }

ciscoRttMonMibGroups  OBJECT IDENTIFIER
    ::= { ciscoRttMonMibConformance 2 }


-- The following OIDs which were previously used to define
-- MODULE-COMPLIANCE statements are now obsolete:
-- ciscoRttMonMibCompliance     ...  ::= { ciscoRttMonMibCompliances 1 }
-- ciscoRttMonMibComplianceRev1 ...  ::= { ciscoRttMonMibCompliances 2 }
-- ciscoRttMonMibComplianceRev2 ...  ::= { ciscoRttMonMibCompliances 3 }
-- ciscoRttMonMibComplianceRev3 ...  ::= { ciscoRttMonMibCompliances 4 }
-- ciscoRttMonMibComplianceRev4 ...  ::= { ciscoRttMonMibCompliances 5 }
-- ciscoRttMonMibComplianceRev5 ...  ::= { ciscoRttMonMibCompliances 6 }
-- ciscoRttMonMibComplianceRev6 ...  ::= { ciscoRttMonMibCompliances 7 }
-- ciscoRttMonMibComplianceRev7 ...  ::= { ciscoRttMonMibCompliances 8 }
-- ciscoRttMonMibComplianceRev8 ...  ::= { ciscoRttMonMibCompliances 9 }
-- ciscoRttMonMibComplianceRev9 ... ::= { ciscoRttMonMibCompliances 10}
-- ciscoRttMonMibComplianceRev10 ... ::= { ciscoRttMonMibCompliances 11}
-- ciscoRttMonMibComplianceRev11 ... ::= { ciscoRttMonMibCompliances 12}
--

-- The following OIDs which were previously used to define
-- OBJECT-GROUPs are now obsolete:
-- ciscoCtrlGroup          ...   ::= { ciscoRttMonMibGroups 2 }
-- ciscoApplGroup          ...   ::= { ciscoRttMonMibGroups 1 }
-- ciscoApplGroupRev1      ...   ::= { ciscoRttMonMibGroups 9 }
-- ciscoCtrlGroupRev3      ...   ::= { ciscoRttMonMibGroups 10}
-- ciscoCtrlGroupRev5      ...   ::= { ciscoRttMonMibGroups 15}
-- ciscoStatsGroupRev2     ...   ::= { ciscoRttMonMibGroups 13}
-- ciscoNotificationGroup  ...   ::= { ciscoRttMonMibGroups 18 }

ciscoRttMonMibComplianceRev12 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for new MIB extensions for
        (1) supporting LSP Path Discovery for Auto SAA L3 MPLS VPN.
        (2) Group Scheduler Enhancement."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev9,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1
                    }

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."
    ::= { ciscoRttMonMibCompliances 12 }

ciscoRttMonMibComplianceRev13 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the deprecated groups."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoRttMonDeprecatedGroupRev1,
                        ciscoNotificationGroup
                    }
    ::= { ciscoRttMonMibCompliances 13 }

ciscoRttMonMibComplianceRev14 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for new MIB extensions for
        (1) supporting LSP Path Discovery for Auto SAA L3 MPLS VPN.
        (2) Group Scheduler Enhancement."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev9,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1
                    }

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."
    ::= { ciscoRttMonMibCompliances 14 }

ciscoRttMonMibComplianceRev15 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for new MIB extensions for
        supporting Ethernet CFM for Virtual Connection."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev9,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1
                    }

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."
    ::= { ciscoRttMonMibCompliances 15 }

ciscoRttMonMibComplianceRev16 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for new MIB extensions for
        supporting high order 32 bit of RTT and OW statistics
        for jitter probe."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8
                    }

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."
    ::= { ciscoRttMonMibCompliances 16 }

ciscoRttMonMibComplianceRev17 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for new MIB extensions for
        supporting high order 32 bit of RTT and OW statistics
        for jitter probe."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8
                    }

    GROUP           ciscoCtrlGroupRev19
    DESCRIPTION
        "ciscoCtrlGroupRev19 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection."

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."
    ::= { ciscoRttMonMibCompliances 17 }

ciscoRttMonMibComplianceRev18 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for new MIB extensions for
        supporting high order 32 bit of RTT and OW statistics
        for jitter probe."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8
                    }

    GROUP           ciscoCtrlGroupRev19
    DESCRIPTION
        "ciscoCtrlGroupRev19 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection."

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."

    GROUP           ciscoCtrlGroupRev20
    DESCRIPTION
        "ciscoCtrlGroupRev20 is only mandatory for the devices
        that support Video operations."
    ::= { ciscoRttMonMibCompliances 18 }

ciscoRttMonMibComplianceRev19 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for new MIB extensions for
        supporting high order 32 bit of RTT and OW statistics
        for jitter probe."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8
                    }

    GROUP           ciscoCtrlGroupRev19
    DESCRIPTION
        "ciscoCtrlGroupRev19 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection."

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."

    GROUP           ciscoCtrlGroupRev20
    DESCRIPTION
        "ciscoCtrlGroupRev20 is only mandatory for the devices
        that support Video operations."

    GROUP           ciscoCtrlGroupRev21
    DESCRIPTION
        "ciscoCtrlGroupRev21 is only mandatory for video operation with
        DSCP, DSP reservation, and source interface support."

    GROUP           ciscoCtrlGroupRev22
    DESCRIPTION
        "ciscoCtrlGroupRev22 is optional for video probes using emulate
        source address, port, target address, and port."
    ::= { ciscoRttMonMibCompliances 19 }

ciscoRttMonMibComplianceRev20 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "This compliance statement specifies the minimal
        requirements an implementation must meet in order to
        claim full compliance with the definition of the CISCO-
        RTTMON-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev10,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8,
                        ciscoCtrlGroupRev24,
                        ciscoCtrlGroupRev25,
                        ciscoStatsGroupRev2
                    }

    GROUP           ciscoCtrlGroupRev19
    DESCRIPTION
        "ciscoCtrlGroupRev19 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection."

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."

    GROUP           ciscoCtrlGroupRev20
    DESCRIPTION
        "ciscoCtrlGroupRev20 is only mandatory for the devices
        that support Video operations."

    GROUP           ciscoCtrlGroupRev21
    DESCRIPTION
        "ciscoCtrlGroupRev21 is only mandatory for video operation with
        DSCP, DSP reservation, and source interface support."

    GROUP           ciscoCtrlGroupRev22
    DESCRIPTION
        "ciscoCtrlGroupRev22 is optional for video probes using emulate
        source address, port, target address, and port."

    GROUP           ciscoCtrlGroupRev23
    DESCRIPTION
        "ciscoCtrlGroupRev22 is mandatory for Y1731 operation."
    ::= { ciscoRttMonMibCompliances 20 }

ciscoRttMonMibComplianceRev21 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "Please enter description here"
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8,
                        ciscoCtrlGroupRev24,
                        ciscoCtrlGroupRev25,
                        ciscoCtrlGroupRev28,
                        ciscoStatsGroupRev9
                    }

    GROUP           ciscoCtrlGroupRev19
    DESCRIPTION
        "ciscoCtrlGroupRev19 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection."

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."

    GROUP           ciscoCtrlGroupRev20
    DESCRIPTION
        "ciscoCtrlGroupRev20 is only mandatory for the devices
        that support Video operations."

    GROUP           ciscoCtrlGroupRev21
    DESCRIPTION
        "ciscoCtrlGroupRev21 is only mandatory for video operation with
        DSCP, DSP reservation, and source interface support."

    GROUP           ciscoCtrlGroupRev22
    DESCRIPTION
        "ciscoCtrlGroupRev22 is optional for video probes using emulate
        source address, port, target address, and port."

    GROUP           ciscoCtrlGroupRev23
    DESCRIPTION
        "ciscoCtrlGroupRev23 is mandatory for Y1731 operation."

    GROUP           ciscoCtrlGroupRev26
    DESCRIPTION
        "ciscoCtrlGroupRev26 is optional for Y1731 Synthetic Loss
        Measurement"

    GROUP           ciscoCtrlGroupRev27
    DESCRIPTION
        "ciscoCtrlGroupRev27 is optional for jitter probes to utilize
        lower layer (Hardware /Packet Processor) timstamping. Currently
        supported only for udp jitter probe."
    ::= { ciscoRttMonMibCompliances 21 }

ciscoRttMonMibComplianceRev22 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for CISCO-RTTMON-MIB"
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8,
                        ciscoCtrlGroupRev24,
                        ciscoCtrlGroupRev25,
                        ciscoCtrlGroupRev28,
                        ciscoStatsGroupRev9,
                        ciscoStatsGroupRev10
                    }

    GROUP           ciscoCtrlGroupRev19
    DESCRIPTION
        "ciscoCtrlGroupRev19 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection."

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."

    GROUP           ciscoCtrlGroupRev20
    DESCRIPTION
        "ciscoCtrlGroupRev20 is only mandatory for the devices
        that support Video operations."

    GROUP           ciscoCtrlGroupRev21
    DESCRIPTION
        "ciscoCtrlGroupRev21 is only mandatory for video operation with
        DSCP, DSP reservation, and source interface support."

    GROUP           ciscoCtrlGroupRev22
    DESCRIPTION
        "ciscoCtrlGroupRev22 is optional for video probes using emulate
        source address, port, target address, and port."

    GROUP           ciscoCtrlGroupRev23
    DESCRIPTION
        "ciscoCtrlGroupRev23 is mandatory for Y1731 operation."

    GROUP           ciscoCtrlGroupRev26
    DESCRIPTION
        "ciscoCtrlGroupRev26 is optional for Y1731 Synthetic Loss
        Measurement"

    GROUP           ciscoCtrlGroupRev27
    DESCRIPTION
        "ciscoCtrlGroupRev27 is optional for jitter probes to utilize
        lower layer (Hardware /Packet Processor) timstamping. Currently
        supported only for udp jitter probe."

    GROUP           ciscoCtrlGroupRev29
    DESCRIPTION
        "ciscoCtrlGroupRev29 is only mandatory for Fabric Path Echo
        probe."
    ::= { ciscoRttMonMibCompliances 22 }

ciscoRttMonMibComplianceRev23 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for CISCO-RTTMON-MIB"
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoStatsGroup,
                        ciscoHistoryGroup,
                        ciscoCtrlGroupRev1,
                        ciscoCtrlGroupRev2,
                        ciscoLatestOperGroupRev1,
                        ciscoStatsGroupRev1,
                        ciscoStatsGroupRev3,
                        ciscoStatsGroupRev4,
                        ciscoStatsGroupRev5,
                        ciscoApplGroupRev2,
                        ciscoApplGroupRev3,
                        ciscoCtrlGroupRev4,
                        ciscoCtrlGroupRev6,
                        ciscoCtrlGroupRev7,
                        ciscoCtrlGroupRev11,
                        ciscoCtrlGroupRev14,
                        ciscoNotificationGroupRev1,
                        ciscoCtrlGroupRev9,
                        ciscoStatsGroupRev8,
                        ciscoCtrlGroupRev24,
                        ciscoCtrlGroupRev25,
                        ciscoCtrlGroupRev28,
                        ciscoStatsGroupRev9,
                        ciscoStatsGroupRev10
                    }

    GROUP           ciscoCtrlGroupRev19
    DESCRIPTION
        "ciscoCtrlGroupRev19 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection."

    GROUP           ciscoCtrlGroupRev18
    DESCRIPTION
        "ciscoCtrlGroupRev18 is only mandatory for the devices
        that support Ethernet CFM for Virtual Connection ."

    GROUP           ciscoCtrlGroupRev17
    DESCRIPTION
        "ciscoCtrlGroupRev17 is only mandatory for the devices
        that support IP SLA auto measure feature."

    GROUP           ciscoCtrlGroupRev16
    DESCRIPTION
        "ciscoCtrlGroupRev16 is only mandatory for the devices
        that support LSP Ping over pseudowire."

    GROUP           ciscoCtrlGroupRev15
    DESCRIPTION
        "ciscoCtrlGroupRev15 is only mandatory for the devices
        that support Ethernet CFM."

    GROUP           ciscoCtrlGroupRev8
    DESCRIPTION
        "ciscoCtrlGroupRev8 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoStatsGroupRev7
    DESCRIPTION
        "ciscoStatsGroupRev7 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev13
    DESCRIPTION
        "ciscoCtrlGroupRev13 is only mandatory for the device
        that support MPLS."

    GROUP           ciscoNotificationGroupRev2
    DESCRIPTION
        "ciscoNotificationGroupRev2 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoApplGroupRev4
    DESCRIPTION
        "ciscoApplGroupRev4 is only mandatory for the devices
        that support MPLS."

    GROUP           ciscoCtrlGroupRev12
    DESCRIPTION
        "ciscoCtrlGroupRev12 is only mandatory for the devices
        that support voice."

    GROUP           ciscoCtrlGroupRev20
    DESCRIPTION
        "ciscoCtrlGroupRev20 is only mandatory for the devices
        that support Video operations."

    GROUP           ciscoCtrlGroupRev21
    DESCRIPTION
        "ciscoCtrlGroupRev21 is only mandatory for video operation with
        DSCP, DSP reservation, and source interface support."

    GROUP           ciscoCtrlGroupRev22
    DESCRIPTION
        "ciscoCtrlGroupRev22 is optional for video probes using emulate
        source address, port, target address, and port."

    GROUP           ciscoCtrlGroupRev23
    DESCRIPTION
        "ciscoCtrlGroupRev23 is mandatory for Y1731 operation."

    GROUP           ciscoCtrlGroupRev26
    DESCRIPTION
        "ciscoCtrlGroupRev26 is optional for Y1731 Synthetic Loss
        Measurement"

    GROUP           ciscoCtrlGroupRev27
    DESCRIPTION
        "ciscoCtrlGroupRev27 is optional for jitter probes to utilize
        lower layer (Hardware /Packet Processor) timstamping. Currently
        supported only for udp jitter probe."

    GROUP           ciscoCtrlGroupRev29
    DESCRIPTION
        "ciscoCtrlGroupRev29 is only mandatory for Fabric Path Echo
        probe."

    GROUP           ciscoCtrlGroupRev30
    DESCRIPTION
        "ciscoCtrlGroupRev30 is optional for classic and group sla
        scheduler."

    GROUP           ciscoCtrlGroupRev31
    DESCRIPTION
        "ciscoCtrlGroupRev31 is optional for specifying longer tag
        values when creating a conceptual RTT control row."

    GROUP           ciscoNotificationGroupRev3
    DESCRIPTION
        "ciscoNotificationGroupRev3 is optional for receiving
        notifications containing rttMonCtrlAdminLongTag."

    OBJECT          rttMonCtrlAdminFrequency
    SYNTAX          Integer32 (1..604800)
    DESCRIPTION
        "Value 0 is not valid for frequency"

    OBJECT          rttMonEchoAdminTargetPort
    SYNTAX          Integer32 (1..65536)
    DESCRIPTION
        "Value 0 is not valid for port"

    OBJECT          rttMonEchoAdminSourcePort
    SYNTAX          Integer32 (1..65536)
    DESCRIPTION
        "Value 0 is not valid for port"

    OBJECT          rttMonEchoAdminEmulateSourcePort
    SYNTAX          Integer32 (1..65536)
    DESCRIPTION
        "Value 0 is not valid for port"

    OBJECT          rttMonEchoAdminEmulateTargetPort
    SYNTAX          Integer32 (1..65536)
    DESCRIPTION
        "Value 0 is not valid for port"
    ::= { ciscoRttMonMibCompliances 23 }

ciscoStatsGroup OBJECT-GROUP
    OBJECTS         {
                        rttMonStatsCaptureCompletions,
                        rttMonStatsCaptureOverThresholds,
                        rttMonStatsCaptureSumCompletionTime,
                        rttMonStatsCaptureSumCompletionTime2Low,
                        rttMonStatsCaptureSumCompletionTime2High,
                        rttMonStatsCaptureCompletionTimeMax,
                        rttMonStatsCaptureCompletionTimeMin,
                        rttMonStatsCollectNumDisconnects,
                        rttMonStatsCollectTimeouts,
                        rttMonStatsCollectBusies,
                        rttMonStatsCollectNoConnections,
                        rttMonStatsCollectDrops,
                        rttMonStatsCollectSequenceErrors,
                        rttMonStatsCollectVerifyErrors,
                        rttMonStatsCollectAddress,
                        rttMonStatsTotalsElapsedTime,
                        rttMonStatsTotalsInitiations
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing accumulated statistical
        history."
    ::= { ciscoRttMonMibGroups 3 }

ciscoHistoryGroup OBJECT-GROUP
    OBJECTS         {
                        rttMonHistoryCollectionSampleTime,
                        rttMonHistoryCollectionAddress,
                        rttMonHistoryCollectionCompletionTime,
                        rttMonHistoryCollectionSense,
                        rttMonHistoryCollectionApplSpecificSense,
                        rttMonHistoryCollectionSenseDescription
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing point by point
        history of each RTT operation."
    ::= { ciscoRttMonMibGroups 4 }

ciscoCtrlGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminTargetPort,
                        rttMonEchoAdminSourceAddress,
                        rttMonEchoAdminSourcePort,
                        rttMonEchoAdminControlEnable,
                        rttMonEchoAdminTOS,
                        rttMonEchoAdminLSREnable,
                        rttMonEchoPathAdminHopAddress
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        functionality of the RTT application."
    ::= { ciscoRttMonMibGroups 5 }

ciscoCtrlGroupRev2 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminTargetAddressString,
                        rttMonEchoAdminNameServer,
                        rttMonEchoAdminOperation,
                        rttMonEchoAdminHTTPVersion,
                        rttMonEchoAdminURL,
                        rttMonEchoAdminCache,
                        rttMonEchoAdminInterval,
                        rttMonEchoAdminNumPackets,
                        rttMonEchoAdminProxy,
                        rttMonEchoAdminString1,
                        rttMonEchoAdminString2,
                        rttMonEchoAdminString3,
                        rttMonEchoAdminString4,
                        rttMonEchoAdminString5,
                        rttMonEchoAdminMode
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        functionality of the RTT application to configure HTTP, DNS and
        Jitter probes."
    ::= { ciscoRttMonMibGroups 6 }

ciscoLatestOperGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        rttMonLatestHTTPOperRTT,
                        rttMonLatestHTTPOperDNSRTT,
                        rttMonLatestHTTPOperTCPConnectRTT,
                        rttMonLatestHTTPOperTransactionRTT,
                        rttMonLatestHTTPOperMessageBodyOctets,
                        rttMonLatestHTTPOperSense,
                        rttMonLatestHTTPErrorSenseDescription,
                        rttMonLatestJitterOperNumOfRTT,
                        rttMonLatestJitterOperRTTSum,
                        rttMonLatestJitterOperRTTSum2,
                        rttMonLatestJitterOperRTTMin,
                        rttMonLatestJitterOperRTTMax,
                        rttMonLatestJitterOperMinOfPositivesSD,
                        rttMonLatestJitterOperMaxOfPositivesSD,
                        rttMonLatestJitterOperNumOfPositivesSD,
                        rttMonLatestJitterOperSumOfPositivesSD,
                        rttMonLatestJitterOperSum2PositivesSD,
                        rttMonLatestJitterOperMinOfNegativesSD,
                        rttMonLatestJitterOperMaxOfNegativesSD,
                        rttMonLatestJitterOperNumOfNegativesSD,
                        rttMonLatestJitterOperSumOfNegativesSD,
                        rttMonLatestJitterOperSum2NegativesSD,
                        rttMonLatestJitterOperMinOfPositivesDS,
                        rttMonLatestJitterOperMaxOfPositivesDS,
                        rttMonLatestJitterOperNumOfPositivesDS,
                        rttMonLatestJitterOperSumOfPositivesDS,
                        rttMonLatestJitterOperSum2PositivesDS,
                        rttMonLatestJitterOperMinOfNegativesDS,
                        rttMonLatestJitterOperMaxOfNegativesDS,
                        rttMonLatestJitterOperNumOfNegativesDS,
                        rttMonLatestJitterOperSumOfNegativesDS,
                        rttMonLatestJitterOperSum2NegativesDS,
                        rttMonLatestJitterOperPacketLossSD,
                        rttMonLatestJitterOperPacketLossDS,
                        rttMonLatestJitterOperPacketOutOfSequence,
                        rttMonLatestJitterOperPacketMIA,
                        rttMonLatestJitterOperPacketLateArrival,
                        rttMonLatestJitterOperSense,
                        rttMonLatestJitterErrorSenseDescription
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to store the latest
        operational results for HTTP, DNS and Jitter probes."
    ::= { ciscoRttMonMibGroups 7 }

ciscoStatsGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        rttMonHTTPStatsCompletions,
                        rttMonHTTPStatsOverThresholds,
                        rttMonHTTPStatsRTTSum,
                        rttMonHTTPStatsRTTSum2Low,
                        rttMonHTTPStatsRTTSum2High,
                        rttMonHTTPStatsRTTMin,
                        rttMonHTTPStatsRTTMax,
                        rttMonHTTPStatsDNSRTTSum,
                        rttMonHTTPStatsTCPConnectRTTSum,
                        rttMonHTTPStatsTransactionRTTSum,
                        rttMonHTTPStatsMessageBodyOctetsSum,
                        rttMonHTTPStatsDNSServerTimeout,
                        rttMonHTTPStatsTCPConnectTimeout,
                        rttMonHTTPStatsTransactionTimeout,
                        rttMonHTTPStatsDNSQueryError,
                        rttMonHTTPStatsHTTPError,
                        rttMonHTTPStatsError,
                        rttMonHTTPStatsBusies,
                        rttMonJitterStatsCompletions,
                        rttMonJitterStatsOverThresholds,
                        rttMonJitterStatsNumOfRTT,
                        rttMonJitterStatsRTTSum,
                        rttMonJitterStatsRTTSum2Low,
                        rttMonJitterStatsRTTSum2High,
                        rttMonJitterStatsRTTMin,
                        rttMonJitterStatsRTTMax,
                        rttMonJitterStatsMinOfPositivesSD,
                        rttMonJitterStatsMaxOfPositivesSD,
                        rttMonJitterStatsNumOfPositivesSD,
                        rttMonJitterStatsSumOfPositivesSD,
                        rttMonJitterStatsSum2PositivesSDLow,
                        rttMonJitterStatsSum2PositivesSDHigh,
                        rttMonJitterStatsMinOfNegativesSD,
                        rttMonJitterStatsMaxOfNegativesSD,
                        rttMonJitterStatsNumOfNegativesSD,
                        rttMonJitterStatsSumOfNegativesSD,
                        rttMonJitterStatsSum2NegativesSDLow,
                        rttMonJitterStatsSum2NegativesSDHigh,
                        rttMonJitterStatsMinOfPositivesDS,
                        rttMonJitterStatsMaxOfPositivesDS,
                        rttMonJitterStatsNumOfPositivesDS,
                        rttMonJitterStatsSumOfPositivesDS,
                        rttMonJitterStatsSum2PositivesDSLow,
                        rttMonJitterStatsSum2PositivesDSHigh,
                        rttMonJitterStatsMinOfNegativesDS,
                        rttMonJitterStatsMaxOfNegativesDS,
                        rttMonJitterStatsNumOfNegativesDS,
                        rttMonJitterStatsSumOfNegativesDS,
                        rttMonJitterStatsSum2NegativesDSLow,
                        rttMonJitterStatsSum2NegativesDSHigh,
                        rttMonJitterStatsPacketLossSD,
                        rttMonJitterStatsPacketLossDS,
                        rttMonJitterStatsPacketOutOfSequence,
                        rttMonJitterStatsPacketMIA,
                        rttMonJitterStatsPacketLateArrival,
                        rttMonJitterStatsError,
                        rttMonJitterStatsBusies
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        functionality of the RTT application to store 'HTTP' and
        'Jitter' probes statistics."
    ::= { ciscoRttMonMibGroups 8 }

ciscoApplGroupRev2 OBJECT-GROUP
    OBJECTS         {
                        rttMonApplResponder,
                        rttMonApplAuthKeyChain,
                        rttMonApplAuthKeyString1,
                        rttMonApplAuthKeyString2,
                        rttMonApplAuthKeyString3,
                        rttMonApplAuthKeyString4,
                        rttMonApplAuthKeyString5,
                        rttMonApplAuthStatus
                    }
    STATUS          current
    DESCRIPTION
        "These objects provide support for configuring responder
        on a router and also configure authentication information."
    ::= { ciscoRttMonMibGroups 11 }

ciscoCtrlGroupRev4 OBJECT-GROUP
    OBJECTS         { rttMonEchoAdminVrfName }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        functionality of the RTT application."
    ::= { ciscoRttMonMibGroups 12 }

ciscoStatsGroupRev3 OBJECT-GROUP
    OBJECTS         {
                        rttMonLatestJitterOperOWSumSD,
                        rttMonLatestJitterOperOWSum2SD,
                        rttMonLatestJitterOperOWMinSD,
                        rttMonLatestJitterOperOWMaxSD,
                        rttMonLatestJitterOperOWSumDS,
                        rttMonLatestJitterOperOWSum2DS,
                        rttMonLatestJitterOperOWMinDS,
                        rttMonLatestJitterOperOWMaxDS,
                        rttMonLatestJitterOperNumOfOW,
                        rttMonJitterStatsOWSumSD,
                        rttMonJitterStatsOWSum2SDLow,
                        rttMonJitterStatsOWSum2SDHigh,
                        rttMonJitterStatsOWSumDS,
                        rttMonJitterStatsOWSum2DSLow,
                        rttMonJitterStatsOWSum2DSHigh,
                        rttMonJitterStatsNumOfOW,
                        rttMonJitterStatsOWMinSDNew,
                        rttMonJitterStatsOWMaxSDNew,
                        rttMonJitterStatsOWMinDSNew,
                        rttMonJitterStatsOWMaxDSNew
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        functionality of the RTT application to store one way
        'Jitter' probes statistics."
    ::= { ciscoRttMonMibGroups 14 }

ciscoCtrlGroupRev6 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminCodecType,
                        rttMonEchoAdminCodecInterval,
                        rttMonEchoAdminCodecPayload,
                        rttMonEchoAdminCodecNumPackets,
                        rttMonEchoAdminICPIFAdvFactor
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        functionality of the RTT application."
    ::= { ciscoRttMonMibGroups 16 }

ciscoStatsGroupRev4 OBJECT-GROUP
    OBJECTS         {
                        rttMonLatestJitterOperMOS,
                        rttMonLatestJitterOperICPIF,
                        rttMonJitterStatsMinOfMOS,
                        rttMonJitterStatsMaxOfMOS,
                        rttMonJitterStatsMinOfICPIF,
                        rttMonJitterStatsMaxOfICPIF
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        functionality of the RTT application to store MOS and
        ICPIF for 'Jitter' probe statistics."
    ::= { ciscoRttMonMibGroups 17 }

ciscoNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        rttMonConnectionChangeNotification,
                        rttMonTimeoutNotification,
                        rttMonThresholdNotification,
                        rttMonVerifyErrorNotification
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of notifications.
        ciscoNotificationGroup object is superseded by
        ciscoNotificationGroupRev1."
    ::= { ciscoRttMonMibGroups 18 }

ciscoApplGroupRev3 OBJECT-GROUP
    OBJECTS         {
                        rttMonApplVersion,
                        rttMonApplMaxPacketDataSize,
                        rttMonApplTimeOfLastSet,
                        rttMonApplSupportedRttTypesValid,
                        rttMonApplSupportedProtocolsValid,
                        rttMonApplNumCtrlAdminEntry,
                        rttMonApplReset,
                        rttMonApplProbeCapacity,
                        rttMonApplFreeMemLowWaterMark,
                        rttMonApplLatestSetError
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the RTT Monitoring
        Application defaults.

        This group of information is provided to the agent when
        the Application starts."
    ::= { ciscoRttMonMibGroups 19 }

ciscoCtrlGroupRev7 OBJECT-GROUP
    OBJECTS         {
                        rttMonScheduleAdminRttRecurring,
                        rttMonGrpScheduleAdminProbes,
                        rttMonGrpScheduleAdminPeriod,
                        rttMonGrpScheduleAdminFrequency,
                        rttMonGrpScheduleAdminLife,
                        rttMonGrpScheduleAdminAgeout,
                        rttMonGrpScheduleAdminStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to enhance the
        scheduling functionality of the RTT application."
    ::= { ciscoRttMonMibGroups 20 }

ciscoCtrlGroupRev8 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminLSPFECType,
                        rttMonEchoAdminLSPSelector,
                        rttMonEchoAdminLSPReplyMode,
                        rttMonEchoAdminLSPTTL,
                        rttMonEchoAdminLSPExp,
                        rttMplsVpnMonCtrlRttType,
                        rttMplsVpnMonCtrlVrfName,
                        rttMplsVpnMonCtrlTag,
                        rttMplsVpnMonCtrlThreshold,
                        rttMplsVpnMonCtrlTimeout,
                        rttMplsVpnMonCtrlScanInterval,
                        rttMplsVpnMonCtrlDelScanFactor,
                        rttMplsVpnMonCtrlEXP,
                        rttMplsVpnMonCtrlRequestSize,
                        rttMplsVpnMonCtrlVerifyData,
                        rttMplsVpnMonCtrlStorageType,
                        rttMplsVpnMonCtrlProbeList,
                        rttMplsVpnMonCtrlStatus,
                        rttMplsVpnMonTypeInterval,
                        rttMplsVpnMonTypeNumPackets,
                        rttMplsVpnMonTypeDestPort,
                        rttMplsVpnMonTypeSecFreqType,
                        rttMplsVpnMonTypeSecFreqValue,
                        rttMplsVpnMonTypeLspSelector,
                        rttMplsVpnMonTypeLSPReplyMode,
                        rttMplsVpnMonTypeLSPTTL,
                        rttMplsVpnMonScheduleRttStartTime,
                        rttMplsVpnMonSchedulePeriod,
                        rttMplsVpnMonScheduleFrequency,
                        rttMplsVpnMonReactConnectionEnable,
                        rttMplsVpnMonReactTimeoutEnable,
                        rttMplsVpnMonReactThresholdType,
                        rttMplsVpnMonReactThresholdCount,
                        rttMplsVpnMonReactActionType
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to support
        (1) echo operations support based on MPLS LSP Ping,
        (2) pathEcho operations support based on MPLS LSP Ping and
        (3) Auto SAA L3 MPLS VPN enhancement."
    ::= { ciscoRttMonMibGroups 21 }

ciscoStatsGroupRev5 OBJECT-GROUP
    OBJECTS         {
                        rttMonJitterStatsIAJOut,
                        rttMonJitterStatsIAJIn,
                        rttMonJitterStatsAvgJitter,
                        rttMonJitterStatsAvgJitterSD,
                        rttMonJitterStatsAvgJitterDS,
                        rttMonJitterStatsUnSyncRTs,
                        rttMonLatestJitterOperIAJIn,
                        rttMonLatestJitterOperIAJOut,
                        rttMonLatestJitterOperAvgJitter,
                        rttMonLatestJitterOperAvgSDJ,
                        rttMonLatestJitterOperAvgDSJ,
                        rttMonLatestJitterOperOWAvgSD,
                        rttMonLatestJitterOperOWAvgDS,
                        rttMonLatestJitterOperNTPState,
                        rttMonLatestJitterOperUnSyncRTs
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that are added to report
        intera-rrival Jitter, average jitter and improve accuracy."
    ::= { ciscoRttMonMibGroups 22 }

ciscoCtrlGroupRev9 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminPrecision,
                        rttMonEchoAdminProbePakPriority,
                        rttMonEchoAdminOWNTPSyncTolAbs,
                        rttMonEchoAdminOWNTPSyncTolPct,
                        rttMonEchoAdminOWNTPSyncTolType,
                        rttMonEchoAdminCalledNumber,
                        rttMonEchoAdminDetectPoint,
                        rttMonEchoAdminGKRegistration
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that are added to enhance the
        jitter probe accuracy."
    ::= { ciscoRttMonMibGroups 23 }

ciscoCtrlGroupRev10 OBJECT-GROUP
    OBJECTS         {
                        rttMonCtrlAdminOwner,
                        rttMonCtrlAdminTag,
                        rttMonCtrlAdminRttType,
                        rttMonCtrlAdminThreshold,
                        rttMonCtrlAdminFrequency,
                        rttMonCtrlAdminTimeout,
                        rttMonCtrlAdminVerifyData,
                        rttMonCtrlAdminStatus,
                        rttMonCtrlAdminNvgen,
                        rttMonEchoAdminProtocol,
                        rttMonEchoAdminTargetAddress,
                        rttMonEchoAdminPktDataRequestSize,
                        rttMonEchoAdminPktDataResponseSize,
                        rttMonScheduleAdminRttLife,
                        rttMonScheduleAdminRttStartTime,
                        rttMonScheduleAdminConceptRowAgeout,
                        rttMonStatisticsAdminNumHourGroups,
                        rttMonStatisticsAdminNumPaths,
                        rttMonStatisticsAdminNumHops,
                        rttMonStatisticsAdminNumDistBuckets,
                        rttMonStatisticsAdminDistInterval,
                        rttMonHistoryAdminNumLives,
                        rttMonHistoryAdminNumBuckets,
                        rttMonHistoryAdminNumSamples,
                        rttMonHistoryAdminFilter,
                        rttMonCtrlOperModificationTime,
                        rttMonCtrlOperDiagText,
                        rttMonCtrlOperResetTime,
                        rttMonCtrlOperOctetsInUse,
                        rttMonCtrlOperConnectionLostOccurred,
                        rttMonCtrlOperTimeoutOccurred,
                        rttMonCtrlOperVerifyErrorOccurred,
                        rttMonCtrlOperOverThresholdOccurred,
                        rttMonCtrlOperNumRtts,
                        rttMonCtrlOperRttLife,
                        rttMonCtrlOperState,
                        rttMonLatestRttOperCompletionTime,
                        rttMonLatestRttOperSense,
                        rttMonLatestRttOperApplSpecificSense,
                        rttMonLatestRttOperSenseDescription,
                        rttMonLatestRttOperTime,
                        rttMonLatestRttOperAddress,
                        rttMonReactTriggerAdminStatus,
                        rttMonReactTriggerOperState
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing the Administration,
        Operational, Last Statistical values for the RTT
        Monitoring Application.
        ciscoCtrlGroupRev10 object is superseded by
        ciscoCtrlGroupRev28."
    ::= { ciscoRttMonMibGroups 24 }

ciscoCtrlGroupRev11 OBJECT-GROUP
    OBJECTS         {
                        rttMonReactVar,
                        rttMonReactThresholdType,
                        rttMonReactActionType,
                        rttMonReactThresholdRising,
                        rttMonReactThresholdFalling,
                        rttMonReactThresholdCountX,
                        rttMonReactThresholdCountY,
                        rttMonReactValue,
                        rttMonReactOccurred,
                        rttMonReactStatus,
                        rttMonGrpScheduleAdminFreqMax,
                        rttMonGrpScheduleAdminFreqMin
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added
        for the reaction configuration of probe."
    ::= { ciscoRttMonMibGroups 25 }

ciscoNotificationGroupRev1 NOTIFICATION-GROUP
   NOTIFICATIONS    { rttMonNotification }
    STATUS          current
    DESCRIPTION
        "A collection of notifications."
    ::= { ciscoRttMonMibGroups 26 }

ciscoCtrlGroupRev12 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminSourceVoicePort,
                        rttMonEchoAdminCallDuration
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added
        for the configuration of rtp operation."
    ::= { ciscoRttMonMibGroups 27 }

ciscoCtrlGroupRev13 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminLSPReplyDscp,
                        rttMonEchoAdminLSPNullShim,
                        rttMplsVpnMonCtrlLpd,
                        rttMplsVpnMonCtrlLpdGrpList,
                        rttMplsVpnMonCtrlLpdCompTime,
                        rttMplsVpnMonTypeLSPReplyDscp,
                        rttMplsVpnMonTypeLpdMaxSessions,
                        rttMplsVpnMonTypeLpdSessTimeout,
                        rttMplsVpnMonTypeLpdEchoTimeout,
                        rttMplsVpnMonTypeLpdEchoInterval,
                        rttMplsVpnMonTypeLpdEchoNullShim,
                        rttMplsVpnMonTypeLpdScanPeriod,
                        rttMplsVpnMonTypeLpdStatHours,
                        rttMplsVpnMonReactLpdNotifyType,
                        rttMplsVpnMonReactLpdRetryCount
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added
        for the parameters configuration of mpls based operations."
    ::= { ciscoRttMonMibGroups 29 }

ciscoStatsGroupRev7 OBJECT-GROUP
    OBJECTS         {
                        rttMonLpdGrpStatsTargetPE,
                        rttMonLpdGrpStatsNumOfPass,
                        rttMonLpdGrpStatsNumOfFail,
                        rttMonLpdGrpStatsNumOfTimeout,
                        rttMonLpdGrpStatsAvgRTT,
                        rttMonLpdGrpStatsMinRTT,
                        rttMonLpdGrpStatsMaxRTT,
                        rttMonLpdGrpStatsMinNumPaths,
                        rttMonLpdGrpStatsMaxNumPaths,
                        rttMonLpdGrpStatsLPDStartTime,
                        rttMonLpdGrpStatsLPDFailOccurred,
                        rttMonLpdGrpStatsLPDFailCause,
                        rttMonLpdGrpStatsLPDCompTime,
                        rttMonLpdGrpStatsGroupStatus,
                        rttMonLpdGrpStatsGroupProbeIndex,
                        rttMonLpdGrpStatsProbeStatus,
                        rttMonLpdGrpStatsPathIds,
                        rttMonLpdGrpStatsResetTime
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that are added for
        collecting the statistics for LSP Path Discovery Group."
    ::= { ciscoRttMonMibGroups 30 }

ciscoNotificationGroupRev2 NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        rttMonLpdDiscoveryNotification,
                        rttMonLpdGrpStatusNotification
                    }
    STATUS          current
    DESCRIPTION
        "A collection of notifications added for supporting LSP
        Path Discovery."
    ::= { ciscoRttMonMibGroups 31 }

ciscoApplGroupRev4 OBJECT-GROUP
    OBJECTS         { rttMonApplLpdGrpStatsReset }
    STATUS          current
    DESCRIPTION
        "This object is added to reset the LSP Path Discovery Stats."
    ::= { ciscoRttMonMibGroups 32 }

ciscoCtrlGroupRev14 OBJECT-GROUP
    OBJECTS         {
                        rttMonGrpScheduleAdminStartTime,
                        rttMonGrpScheduleAdminAdd,
                        rttMonGrpScheduleAdminDelete,
                        rttMonGrpScheduleAdminReset
                    }
    STATUS          current
    DESCRIPTION
        "This object is added for group scheduler enhancement"
    ::= { ciscoRttMonMibGroups 33 }

ciscoCtrlGroupRev15 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminTargetMPID,
                        rttMonEchoAdminTargetDomainName,
                        rttMonEchoAdminTargetVLAN,
                        rttMonEchoAdminEthernetCOS
                    }
    STATUS          current
    DESCRIPTION
        "These objects are added for Ethernet ping/jitter operation."
    ::= { ciscoRttMonMibGroups 34 }

ciscoRttMonObsoleteGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        rttMonApplPreConfigedReset,
                        rttMonApplPreConfigedValid,
                        rttMonFileIOAdminFilePath,
                        rttMonFileIOAdminSize,
                        rttMonFileIOAdminAction,
                        rttMonScriptAdminName,
                        rttMonScriptAdminCmdLineParams
                    }
    STATUS          obsolete
    DESCRIPTION
        "A collection of all objects that are obsolete."
    ::= { ciscoRttMonMibGroups 35 }

ciscoRttMonDeprecatedGroupRev1 OBJECT-GROUP
    OBJECTS         {
                        rttMonJitterStatsOWMinSD,
                        rttMonJitterStatsOWMaxSD,
                        rttMonJitterStatsOWMinDS,
                        rttMonJitterStatsOWMaxDS,
                        rttMonReactAdminConnectionEnable,
                        rttMonReactAdminTimeoutEnable,
                        rttMonReactAdminThresholdType,
                        rttMonReactAdminThresholdFalling,
                        rttMonReactAdminThresholdCount,
                        rttMonReactAdminThresholdCount2,
                        rttMonReactAdminActionType,
                        rttMonReactAdminVerifyErrorEnable
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of all objects that are deprecated.
        ciscoRttMonDeprecatedGroupRev1 object is superseded by
        ciscoCtrlGroupRev11."
    ::= { ciscoRttMonMibGroups 36 }

ciscoCtrlGroupRev16 OBJECT-GROUP
    OBJECTS         { rttMonEchoAdminLSPVccvID }
    STATUS          current
    DESCRIPTION
        "These objects are added for LSP Ping Pseudowire operation."
    ::= { ciscoRttMonMibGroups 37 }

ciscoCtrlGroupRev17 OBJECT-GROUP
    OBJECTS         { rttMonCtrlAdminGroupName }
    STATUS          current
    DESCRIPTION
        "This object is added for IP SLA Auto Measure project."
    ::= { ciscoRttMonMibGroups 38 }

ciscoCtrlGroupRev18 OBJECT-GROUP
    OBJECTS         { rttMonEchoAdminTargetEVC }
    STATUS          current
    DESCRIPTION
        "This object is added for Ethernet ping/jitter operation."
    ::= { ciscoRttMonMibGroups 39 }

ciscoCtrlGroupRev19 OBJECT-GROUP
    OBJECTS         { rttMonEchoAdminTargetMEPPort }
    STATUS          current
    DESCRIPTION
        "This object is added for Ethernet ping/jitter operation."
    ::= { ciscoRttMonMibGroups 41 }

ciscoStatsGroupRev8 OBJECT-GROUP
    OBJECTS         {
                        rttMonLatestJitterOperRTTSumHigh,
                        rttMonLatestJitterOperRTTSum2High,
                        rttMonLatestJitterOperOWSumSDHigh,
                        rttMonLatestJitterOperOWSum2SDHigh,
                        rttMonLatestJitterOperOWSumDSHigh,
                        rttMonLatestJitterOperOWSum2DSHigh,
                        rttMonJitterStatsRTTSumHigh,
                        rttMonJitterStatsOWSumSDHigh,
                        rttMonJitterStatsOWSumDSHigh
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects that were added to store the high order
        32 bits of RTT and one way latency statistics for 'jitter'
        probe."
    ::= { ciscoRttMonMibGroups 40 }

ciscoCtrlGroupRev20 OBJECT-GROUP
    OBJECTS         { rttMonEchoAdminVideoTrafficProfile }
    STATUS          current
    DESCRIPTION
        "This object has been added for video operation."
    ::= { ciscoRttMonMibGroups 43 }

ciscoCtrlGroupRev21 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminDscp,
                        rttMonEchoAdminReserveDsp,
                        rttMonEchoAdminInputInterface
                    }
    STATUS          current
    DESCRIPTION
        "This object has been added to support of DSCP marking, sender
        DSP reservation, and source interface for path congruence in
        video operation."
    ::= { ciscoRttMonMibGroups 44 }

ciscoCtrlGroupRev22 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminEmulateSourceAddress,
                        rttMonEchoAdminEmulateSourcePort,
                        rttMonEchoAdminEmulateTargetAddress,
                        rttMonEchoAdminEmulateTargetPort
                    }
    STATUS          current
    DESCRIPTION
        "This object has been added to support of emulate source
        address, emulate source port, emulate target address and emulate
        target port for path congruence in video operation."
    ::= { ciscoRttMonMibGroups 45 }

ciscoCtrlGroupRev23 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminTargetMacAddress,
                        rttMonEchoAdminSourceMacAddress,
                        rttMonEchoAdminSourceMPID
                    }
    STATUS          current
    DESCRIPTION
        "This group contains objects describing Y1731
        operation."
    ::= { ciscoRttMonMibGroups 46 }

ciscoCtrlGroupRev24 OBJECT-GROUP
    OBJECTS         { rttMonGeneratedOperCtrlAdminIndex }
    STATUS          current
    DESCRIPTION
        "This object has been added to for Generated Oper Table."
    ::= { ciscoRttMonMibGroups 47 }

ciscoCtrlGroupRev25 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminEndPointListName,
                        rttMonEchoAdminSSM,
                        rttMonEchoAdminControlRetry,
                        rttMonEchoAdminControlTimeout,
                        rttMonEchoAdminIgmpTreeInit
                    }
    STATUS          current
    DESCRIPTION
        "This object has been added to support Multicast operation."
    ::= { ciscoRttMonMibGroups 48 }

ciscoStatsGroupRev2 OBJECT-GROUP
    OBJECTS         {
                        rttMonControlEnableErrors,
                        rttMonStatsRetrieveErrors
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing Multicast control
        request information.
        ciscoStatsGroupRev2 object is superseded by
        ciscoStatsGroupRev9."
    ::= { ciscoRttMonMibGroups 49 }

ciscoCtrlGroupRev26 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminEnableBurst,
                        rttMonEchoAdminAggBurstCycles,
                        rttMonEchoAdminLossRatioNumFrames,
                        rttMonEchoAdminAvailNumFrames
                    }
    STATUS          current
    DESCRIPTION
        "New rttMonObjects related to the support of Y1731 Synthetic
        Loss Measurement."
    ::= { ciscoRttMonMibGroups 50 }

ciscoCtrlGroupRev27 OBJECT-GROUP
    OBJECTS         { rttMonEchoAdminTstampOptimization }
    STATUS          current
    DESCRIPTION
        "The object is added to utilize lower layer (Hardware/Packet
        Processor) timestamping for improving accuracy of jitter probe
        statistics.

        Currently the object is supported for udp jitter operations."
    ::= { ciscoRttMonMibGroups 51 }

ciscoCtrlGroupRev28 OBJECT-GROUP
    OBJECTS         {
                        rttMonCtrlAdminOwner,
                        rttMonCtrlAdminTag,
                        rttMonCtrlAdminRttType,
                        rttMonCtrlAdminThreshold,
                        rttMonCtrlAdminFrequency,
                        rttMonCtrlAdminTimeout,
                        rttMonCtrlAdminVerifyData,
                        rttMonCtrlAdminStatus,
                        rttMonCtrlAdminNvgen,
                        rttMonEchoAdminProtocol,
                        rttMonEchoAdminTargetAddress,
                        rttMonEchoAdminPktDataRequestSize,
                        rttMonEchoAdminPktDataResponseSize,
                        rttMonScheduleAdminRttLife,
                        rttMonScheduleAdminRttStartTime,
                        rttMonStatisticsAdminNumHourGroups,
                        rttMonStatisticsAdminNumPaths,
                        rttMonStatisticsAdminNumHops,
                        rttMonStatisticsAdminNumDistBuckets,
                        rttMonStatisticsAdminDistInterval,
                        rttMonHistoryAdminNumLives,
                        rttMonHistoryAdminNumBuckets,
                        rttMonHistoryAdminNumSamples,
                        rttMonHistoryAdminFilter,
                        rttMonCtrlOperModificationTime,
                        rttMonCtrlOperDiagText,
                        rttMonCtrlOperResetTime,
                        rttMonCtrlOperOctetsInUse,
                        rttMonCtrlOperConnectionLostOccurred,
                        rttMonCtrlOperTimeoutOccurred,
                        rttMonCtrlOperVerifyErrorOccurred,
                        rttMonCtrlOperOverThresholdOccurred,
                        rttMonCtrlOperNumRtts,
                        rttMonCtrlOperRttLife,
                        rttMonCtrlOperState,
                        rttMonLatestRttOperCompletionTime,
                        rttMonLatestRttOperSense,
                        rttMonLatestRttOperApplSpecificSense,
                        rttMonLatestRttOperSenseDescription,
                        rttMonLatestRttOperTime,
                        rttMonLatestRttOperAddress,
                        rttMonReactTriggerAdminStatus,
                        rttMonReactTriggerOperState,
                        rttMonScheduleAdminConceptRowAgeoutV2
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the Administration,
        Operational, Last Statistical values for the RTT
        Monitoring Application.

        Deprecated rttMonScheduleAdminConceptRowAgeout and added new
        rttMonScheduleAdminConceptRowAgeoutV2 to consider 0 as default
        age out value."
    ::= { ciscoRttMonMibGroups 52 }

ciscoStatsGroupRev9 OBJECT-GROUP
    OBJECTS         {
                        rttMonStatsCollectCtrlEnErrors,
                        rttMonStatsCollectRetrieveErrors
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing Multicast control request
        information."
    ::= { ciscoRttMonMibGroups 53 }

ciscoCtrlGroupRev29 OBJECT-GROUP
    OBJECTS         {
                        rttMonEchoAdminTargetSwitchId,
                        rttMonEchoAdminProfileId,
                        rttMonEchoAdminOutputInterface
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing Fabric Path Echo probe
        information."
    ::= { ciscoRttMonMibGroups 54 }

ciscoNotificationGroupRev3 NOTIFICATION-GROUP
   NOTIFICATIONS    { rttMonNotificationV2 }
    STATUS          current
    DESCRIPTION
        "Added notification rttMonNotificationV2 with the enhanced
        rttMonCtrlAdminLongTag object"
    ::= { ciscoRttMonMibGroups 55 }

ciscoCtrlGroupRev30 OBJECT-GROUP
    OBJECTS         {
                        rttMonScheduleAdminStartType,
                        rttMonScheduleAdminStartDelay,
                        rttMonGrpScheduleAdminStartType,
                        rttMonGrpScheduleAdminStartDelay
                    }
    STATUS          current
    DESCRIPTION
        "Object added to support Random Start-Time feature for
        group and sla probes."
    ::= { ciscoRttMonMibGroups 56 }

ciscoCtrlGroupRev31 OBJECT-GROUP
    OBJECTS         { rttMonCtrlAdminLongTag }
    STATUS          current
    DESCRIPTION
        "Object rttMonCtrlAdminLongTag added to enhance current
        tag object for capturing long descriptions."
    ::= { ciscoRttMonMibGroups 57 }

ciscoStatsGroupRev10 OBJECT-GROUP
    OBJECTS         {
                        rttMonLatestJitterOperNumOverThresh,
                        rttMonJitterStatsNumOverThresh
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing over threshold statistics
        for jitter probes."
    ::= { ciscoRttMonMibGroups 58 }

END


