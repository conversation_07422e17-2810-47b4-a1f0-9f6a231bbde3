-- *****************************************************************
-- CISCO-AAA-SERVER-MIB.my:  Cisco AAA Server MIB file.
--
-- Copyright (c) 1999-2003 by cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************



CISCO-AAA-SERVER-MIB DEFINITIONS ::= BEGIN


IMPORTS
	MODULE-IDENTITY,
	NOTIFICATION-TYPE,
	OBJECT-TYPE,
	<PERSON>32,
	Ip<PERSON><PERSON>ress, Unsigned32
		FROM SNMPv2-SMI
	MODULE-COMPLIANCE,
	OBJECT-GROUP, NOTIFICATION-GROUP
		FROM SNMPv2-CONF
	TimeInterval, Display<PERSON>tring,
	<PERSON>V<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
	TEXTUAL-CONVENTION
		FROM SNMPv2-TC
	ciscoExperiment
		FROM CISCO-SMI;


ciscoAAAServerMIB MODULE-IDENTITY
	LAST-UPDATED	"200311170000Z"
	ORGANIZATION	"Cisco Systems,	Inc."
	CONTACT-INFO
		"	Cisco Systems
			Customer Service

		Postal:	170 W Tasman Drive
			San Jose, CA  95134
			USA

		   Tel:	****** 553-NETS

		E-mail:	<EMAIL>"
	DESCRIPTION
		"The MIB module	for monitoring communications and status
		 of AAA	Server operation
		"
        REVISION    "200311170000Z"
        DESCRIPTION
           "Expanded the list of AAA protocols to include LDAP,
           Kerberos, NTLM and SDI; defined textual convention
           CiscoAAAProtocol to denote the type of AAA protocols.
           "

	REVISION	 "200203280000Z"
	DESCRIPTION
		"Imported Unsigned32 from SNMPv2-SMI instead of
		CISCO-TC"
	REVISION	 "200001200000Z"
	DESCRIPTION
		"Added objects to support AAA server configuration
			  casConfigTable
			     casProtocol
			     casIndex
			     casAddress
			     casAuthenPort
			     casAcctPort
			     casConfigRowStatus
		"
	::= { ciscoExperiment 56 }



--	  Overview of AAA Server MIB
--
--	  MIB description
--
--
--	This MIB provides configuration	and statistics reflecting the state
--	of AAA Server operation	within the device and AAA communications
--	with external servers.
--
--	AAA stands for authentication, authorization, and accounting
--
--	The AAA	Server MIB provides the	following information:
--	 1) A Table for	configuring AAA	servers
--	 2) Identities of external AAA servers
--	 3) Distinct statistics	for each AAA function
--	 4) Status of servers providing	AAA functions
--
--	A server is defined as a logical entity	which provides any
--	of the three AAA functions. A TACACS+ server consists of
--	all three functions with a single IP address and single	TCP
--	port.  A RADIUS	server consists	of the authentication/accounting
--	pair with a single IP address but distinct UDP ports, or
--	it may be just one of authentication or	accounting. It is
--	possible to have two distinct RADIUS servers at	the same IP
--	address, one providing authentication only, the	other accounting
--	only.
--

-- +++++++++++++++++++++++++++++++++++++++++++++++++++
-- Local Textual Conventions
-- +++++++++++++++++++++++++++++++++++++++++++++++++++

CiscoAAAProtocol ::= TEXTUAL-CONVENTION
        STATUS         current
        DESCRIPTION
            "Protocol used with this server.
                 tacacsplus(1) - TACACS+

                 radius(2)   - RADIUS

                 ldap(3)     -   Light Weight Directory Protocol

                 kerberos(4) -   Kerberos

                 ntlm(5)     -   Authentication/Authorization using
				 NT Domain

                 sdi(6)      -   Authentication/Authorization using
				 Secure ID

                 other(7)    -   Other protocols
                "
        REFERENCE
            "
             RFC 2138 Remote Authentication Dial In User Service 
		      (RADIUS)
             RFC 2139 RADIUS Accounting
             The TACACS+ Protocol Version 1.78, Internet Draft
            "
        SYNTAX      INTEGER {
                        tacacsplus(1),
                        radius(2),
                        ldap(3),
                        kerberos(4),
                        ntlm(5),
                        sdi(6),
                        other(7)
                    }


-- AAA Server MIB object definitions

cAAAServerMIBObjects	 OBJECT	IDENTIFIER ::= { ciscoAAAServerMIB 1 }


-- Configuration objects

casConfig		 OBJECT	IDENTIFIER ::= { cAAAServerMIBObjects 1	}


-- Statistics objects

casStatistics		 OBJECT	IDENTIFIER ::= { cAAAServerMIBObjects 2	}



--
-- Notification	Configuration
--

casServerStateChangeEnable OBJECT-TYPE
	SYNTAX	    TruthValue
	MAX-ACCESS  read-write
	STATUS	    current
	DESCRIPTION
	    "This variable controls the	generation of
	     casServerStateChange notification.

	     When this variable	is true(1), generation of
	     casServerStateChange notifications	is enabled.
	     When this variable	is false(2), generation	of
	     casServerStateChange notifications	is disabled.

	     The default value is false(2).
	    "
	::= { casConfig	1 }

--
-- Server Configuration	Table
--

casConfigTable OBJECT-TYPE
	SYNTAX	    SEQUENCE OF	CasConfigEntry
	MAX-ACCESS  not-accessible
	STATUS	    current
	DESCRIPTION
		"This table shows current configurations for each
		 AAA server, allows existing servers to	be removed
		 and new ones to be created.
		"
	::= { casConfig	2 }

casConfigEntry OBJECT-TYPE
	SYNTAX	    CasConfigEntry
	MAX-ACCESS  not-accessible
	STATUS	    current
	DESCRIPTION
	    "An	AAA server configuration identified by its protocol
	     and its index.

	     An	entry is created/removed when a	server is defined
	     or	undefined with IOS configuration commands via
	     CLI or by issuing appropriate sets	to this	table
	     using snmp.

	     A management station wishing to create an entry should
	     first generate a random number to be used as the index
	     to	this sparse table.  The	station	should then create the
	     associated	instance of the	row status and row index objects.
	     It	must also, either in the same or in successive PDUs,
	     create an instance	of casAddress where casAddress is the
	     IP	address	of the server to be added.

	     It	should also modify the default values for casAuthenPort,
	     casAcctPort if the	defaults are not appropriate.

	     If	casKey is a zero-length	string or is not explicitly set,
	     then the global key will be used.	Otherwise, this	value
	     is	used as	the key	for this server	instance.

	     Once the appropriate instance of all the configuration
	     objects have been created,	either by an explicit SNMP set
	     request or	by default, the	row status should be set to
	     active(1) to initiate the request.

	     After the AAA server is made active, the entry can	not be
	     modified -	the only allowed operation after this is to
	     destroy the entry by setting casConfigRowStatus to	destroy(6).

	     casPriority is automatically assigned once	the entry is
	     made active and reflects the relative priority of the
	     defined server with respect to already configured servers.
	     Newly-created servers will	be assigned the	lowest priority.
	     To	reassign server	priorities to existing server entries,
	     it	may be necessary to destroy and	recreate entries in order
	     of	priority.

	     Entries in	this table with	casConfigRowStatus equal to
	     active(1) remain in the table until destroyed.

	     Entries in	this table with	casConfigRowStatus equal to
	     values other than active(1) will be destroyed after timeout
	     (5	minutes).

	     If	a server address being created via SNMP	exists already
	     in	another	active casConfigEntry, then a newly created row
	     can not be	made active until the original row with	the
	     with the same server address value	is destroyed.

	     Upon reload, casIndex values may be changed, but the
	     priorities	that were saved	before reload will be retained,
	     with lowest priority number corresponding to the higher
	     priority servers.
	     "
	INDEX {	casProtocol,
		casIndex }
	::= { casConfigTable 1}

CasConfigEntry ::=
	SEQUENCE {
	    casProtocol			   CiscoAAAProtocol,
	    casIndex			   Unsigned32,
	    casAddress			   IpAddress,
	    casAuthenPort		   INTEGER,
	    casAcctPort			   INTEGER,
	    casKey			   DisplayString,
	    casPriority			   Unsigned32,
	    casConfigRowStatus		   RowStatus
	}

casProtocol OBJECT-TYPE
        SYNTAX      CiscoAAAProtocol
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The variable denotes the protocol used by the
             managed device with the AAA server corresponding to
	     this entry in the table.
            "
       ::= { casConfigEntry 1 }

casIndex OBJECT-TYPE
	SYNTAX	    Unsigned32 (1..**********)
	MAX-ACCESS  not-accessible
	STATUS	    current
	DESCRIPTION
	    "A management station wishing to initiate a	new AAA	server
	     configuration should use a	random value for this object
	     when creating an instance of casConfigEntry.

	     The RowStatus semantics of	the casConfigRowStatus object
	     will prevent access conflicts.

	     If	the randomly chosen casIndex value for row creation
	     is	already	in use by an existing entry, snmp set to the
	     casIndex value will fail.
	    "

	::= { casConfigEntry 2 }

casAddress OBJECT-TYPE
	SYNTAX	    IpAddress
	MAX-ACCESS  read-create
	STATUS	    current
	DESCRIPTION
	    "The IP address of the server.
	    "

	::= { casConfigEntry 3 }

casAuthenPort OBJECT-TYPE
	SYNTAX	    INTEGER (0..65535)
	MAX-ACCESS  read-create
	STATUS	    current
	DESCRIPTION
	    "UDP/TCP port used for authentication in the configuration

	     For TACACS+, this object should be	explictly set.

	     Default value is the IOS default for radius: 1645.
	    "
	DEFVAL	{ 1645 }
	::= { casConfigEntry 4 }

casAcctPort OBJECT-TYPE
	SYNTAX	    INTEGER (0..65535)
	MAX-ACCESS  read-create
	STATUS	    current
	DESCRIPTION
	    "UDP/TCP port used for accounting service in the configuration

	     For TACACS+, the value of casAcctPort is ignored.
	     casAuthenPort will	be used	instead.

	     Default value is the IOS default for radius: 1646.
	    "
	DEFVAL { 1646 }
	::= { casConfigEntry 5 }

casKey	OBJECT-TYPE
	SYNTAX	   DisplayString
	MAX-ACCESS read-create
	STATUS	   current
	DESCRIPTION
		"The server key	to be used with	this server.

		 Retrieving the	value of this object via SNMP will
		 return	an empty string	for security reasons.
		"
	DEFVAL { "" }
	::= { casConfigEntry 6 }

casPriority  OBJECT-TYPE
	SYNTAX	    Unsigned32 (1..**********)
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "A number that indicates the priority of the server	in
	     this entry.  Lower	numbers	indicate higher	priority.
	    "
	::= { casConfigEntry 7 }


casConfigRowStatus OBJECT-TYPE
	SYNTAX	    RowStatus
	MAX-ACCESS  read-create
	STATUS	    current
	DESCRIPTION
		"The status of this table entry.  Once the entry status	is
		 set to	active,	the associated entry cannot be modified
		 except	destroyed by setting this object to destroy(6).
		"
	::= { casConfigEntry 8 }

--
-- Server Statistics
--

casStatisticsTable OBJECT-TYPE
	SYNTAX	    SEQUENCE OF	CasStatisticsEntry
	MAX-ACCESS  not-accessible
	STATUS	    current
	DESCRIPTION
		"
		 Table providing statistics for	each server.
		"
	::= { casStatistics 1 }

casStatisticsEntry OBJECT-TYPE
	SYNTAX	    CasStatisticsEntry
	MAX-ACCESS  not-accessible
	STATUS	    current
	DESCRIPTION
	    "Statistical information about a particular	server.

	     Objects in	this table are read-only and appear
	     automatically whenever a row in the casConfigTable
	     is	made active.

	     Objects in	this table disappear when casConfigRowStatus
	     for the corresponding casConfigEntry is set to the
	     destroy(6)	state.
	    "
	AUGMENTS { casConfigEntry }
	::= { casStatisticsTable 1 }

CasStatisticsEntry::=
	SEQUENCE {
	    casAuthenRequests		   Counter32,
	    casAuthenRequestTimeouts	   Counter32,
	    casAuthenUnexpectedResponses   Counter32,
	    casAuthenServerErrorResponses  Counter32,
	    casAuthenIncorrectResponses	   Counter32,
	    casAuthenResponseTime	   TimeInterval,
	    casAuthenTransactionSuccesses  Counter32,
	    casAuthenTransactionFailures   Counter32,
	    casAuthorRequests		   Counter32,
	    casAuthorRequestTimeouts	   Counter32,
	    casAuthorUnexpectedResponses   Counter32,
	    casAuthorServerErrorResponses  Counter32,
	    casAuthorIncorrectResponses	   Counter32,
	    casAuthorResponseTime	   TimeInterval,
	    casAuthorTransactionSuccesses  Counter32,
	    casAuthorTransactionFailures   Counter32,
	    casAcctRequests		   Counter32,
	    casAcctRequestTimeouts	   Counter32,
	    casAcctUnexpectedResponses	   Counter32,
	    casAcctServerErrorResponses	   Counter32,
	    casAcctIncorrectResponses	   Counter32,
	    casAcctResponseTime		   TimeInterval,
	    casAcctTransactionSuccesses	   Counter32,
	    casAcctTransactionFailures	   Counter32,
	    casState			   INTEGER,
	    casCurrentStateDuration	   TimeInterval,
	    casPreviousStateDuration	   TimeInterval,
	    casTotalDeadTime		   TimeInterval,
	    casDeadCount		   Counter32
	}

--
-- Authentication statistics
--

casAuthenRequests OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authentication requests sent	to
	     this server since it is made active.

	     Retransmissions due to request timeouts are
	     counted as	distinct requests.
	    "
	::= { casStatisticsEntry 1 }

casAuthenRequestTimeouts OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authentication requests which have
	     timed out since it	is made	active.

	     A timeout results in a retransmission of the request
	     If	the maximum number of attempts has been	reached,
	     no	further	retransmissions	will be	attempted.
	    "
	::= { casStatisticsEntry 2 }

casAuthenUnexpectedResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of unexpected authentication responses received
	     from this server since it is made active.

	     An	example	is a delayed response to a request which had
	     already timed out.
	    "
	::= { casStatisticsEntry 3 }

casAuthenServerErrorResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of server ERROR	authentication responses
	     received from this	server since it	is made	active.

	     These are responses indicating that the server itself
	     has identified an error with its authentication
	     operation.
	    "
	::= { casStatisticsEntry 4 }

casAuthenIncorrectResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authentication responses which could	not
	     be	processed since	it is made active.

	     Reasons include inability to decrypt the response,
	     invalid fields, or	the response is	not valid based	on
	     the request.
	    "
	::= { casStatisticsEntry 5 }

casAuthenResponseTime OBJECT-TYPE
	SYNTAX	    TimeInterval
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "Average response time for authentication requests sent
	     to	this server, excluding timeouts, since system
	     re-initialization.
	    "
	::= { casStatisticsEntry 6 }

casAuthenTransactionSuccesses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authentication transactions with this
	     server which succeeded since it is	made active.

	     A transaction may include multiple	request
	     retransmissions if	timeouts occur.

	     A transaction is successful if the	server responds
	     with either an authentication pass	or fail.
	    "
	::= { casStatisticsEntry 7 }

casAuthenTransactionFailures OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authentication transactions with this
	     server which failed since it is made active.

	     A transaction may include multiple	request
	     retransmissions if	timeouts occur.

	     A transaction failure occurs if maximum resends have
	     been met or the server aborts the transaction.
	    "
	::= { casStatisticsEntry 8 }

--
-- Authorization statistics
--

casAuthorRequests OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authorization requests sent to
	     this server since it is made active.

	     Retransmissions due to request timeouts are
	     counted as	distinct requests.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 9 }

casAuthorRequestTimeouts OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authorization requests which	have
	     timed out since it	is made	active.

	     A timeout results in a retransmission of the request
	     If	the maximum number of attempts has been	reached,
	     no	further	retransmissions	will be	attempted.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 10 }

casAuthorUnexpectedResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of unexpected authorization responses received
	     from this server since it is made active.

	     An	example	is a delayed response to a request which
	     had already timed out.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 11 }

casAuthorServerErrorResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of server ERROR	authorization responses
	     received from this	server since it	is made	active.

	     These are responses indicating that the server itself
	     has identified an error with its authorization
	     operation.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 12 }

casAuthorIncorrectResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authorization responses which could not
	     be	processed since	it is made active.

	     Reasons include inability to decrypt the response,
	     invalid fields, or	the response is	not valid based	on
	     the request.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 13 }

casAuthorResponseTime OBJECT-TYPE
	SYNTAX	    TimeInterval
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "Average response time for authorization requests sent
	     to	this server, excluding timeouts, since system
	     re-initialization.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 14 }

casAuthorTransactionSuccesses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authorization transactions with this
	     server which succeeded since it is	made active.

	     A transaction may include multiple	request
	     retransmissions if	timeouts occur.

	     A transaction is successful if the	server responds
	     with either an authorization pass or fail.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 15 }

casAuthorTransactionFailures OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of authorization transactions with this
	     server which failed since it is made active.

	     A transaction may include multiple	request
	     retransmissions if	timeouts occur.

	     A transaction failure occurs if maximum resends have
	     been met or the server aborts the transaction.

	     This object is not	instantiated for protocols which do
	     not support a distinct authorization function.
	    "
	::= { casStatisticsEntry 16 }

--
-- Accounting statistics
--

casAcctRequests	OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of accounting requests sent to
	     this server since system re-initialization.

	     Retransmissions due to request timeouts are
	     counted as	distinct requests.
	    "
	::= { casStatisticsEntry 17 }

casAcctRequestTimeouts OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of accounting requests which have
	     timed out since system re-initialization.

	     A timeout results in a retransmission of the request
	     If	the maximum number of attempts has been	reached,
	     no	further	retransmissions	will be	attempted.

	    "
	::= { casStatisticsEntry 18 }

casAcctUnexpectedResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of unexpected accounting responses received
	     from this server since system re-initialization.

	     An	example	is a delayed response to a request which had
	     already timed out.
	    "
	::= { casStatisticsEntry 19 }

casAcctServerErrorResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of server ERROR	accounting responses received
	     from this server since system re-initialization.

	     These are responses indicating that the server itself
	     has identified an error with its accounting
	     operation.
	    "
	::= { casStatisticsEntry 20 }

casAcctIncorrectResponses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of accounting responses	which could not
	     be	processed since	system re-initialization.

	     Reasons include inability to decrypt the response,
	     invalid fields, or	the response is	not valid based	on
	     the request.
	    "
	::= { casStatisticsEntry 21 }

casAcctResponseTime OBJECT-TYPE
	SYNTAX	    TimeInterval
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "Average response time for accounting requests sent
	     to	this server,, since system re-initialization
	     excluding timeouts.
	    "
	::= { casStatisticsEntry 22 }

casAcctTransactionSuccesses OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of accounting transactions with	this
	     server which succeeded since system re-initialization.

	     A transaction may include multiple	request
	     retransmissions if	timeouts occur.

	     A transaction is successful if the	server responds
	     with either an accounting pass or fail.
	    "
	::= { casStatisticsEntry 23 }

casAcctTransactionFailures OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of accounting transactions with	this
	     server which failed since system re-initialization.

	     A transaction may include multiple	request
	     retransmissions if	timeouts occur.

	     A transaction failure occurs if maximum resends have
	     been met or the server aborts the transaction.
	    "
	::= { casStatisticsEntry 24 }

--
-- Server availability
--

casState OBJECT-TYPE
	SYNTAX	    INTEGER {
			up(1),
			dead(2)
		    }
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "Current state of this server.

		 up(1)	 - Server responding to	requests

		 dead(2) - Server failed to respond

	     A server is marked	dead if	it does	not respond after
	     maximum retransmissions.

	     A server is marked	up again either	after a	waiting
	     period or if some response	is received from it.

	     The initial value of casState is 'up(1)' at system
	     re-initialization.	This will only transistion to 'dead(2)'
	     if	an attempt to communicate fails.
	    "
	::= { casStatisticsEntry 25 }

casCurrentStateDuration	OBJECT-TYPE
	SYNTAX	    TimeInterval
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "This object provides the elapsed time the server has
	     been in its current state as shown	in casState.
	    "
	::= { casStatisticsEntry 26 }

casPreviousStateDuration OBJECT-TYPE
	SYNTAX	    TimeInterval
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "This object provides the elapsed time the server was
	     been in its previous state	prior to the most recent
	     transistion of casState.

	     This value	is zero	if the server has not changed state.
	    "
	::= { casStatisticsEntry 27 }

casTotalDeadTime OBJECT-TYPE
	SYNTAX	    TimeInterval
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The total elapsed time this server's casState has
	     had the value 'dead(2)' since system re-initialization.
	    "
	::= { casStatisticsEntry 28 }

casDeadCount OBJECT-TYPE
	SYNTAX	    Counter32
	MAX-ACCESS  read-only
	STATUS	    current
	DESCRIPTION
	    "The number	of times this server's casState	has
	     transitioned to 'dead(2)' since system re-initialization.
	    "
	::= { casStatisticsEntry 29 }




-- ******************************************************************
-- Notifications
-- ******************************************************************
cAAAServerMIBNotificationPrefix	 OBJECT	IDENTIFIER ::=
				  { ciscoAAAServerMIB 2	}

cAAAServerMIBNotifications	 OBJECT	IDENTIFIER ::=
				  { cAAAServerMIBNotificationPrefix 0 }

casServerStateChange NOTIFICATION-TYPE
    OBJECTS	{ casState,
		  casPreviousStateDuration,
		  casTotalDeadTime }
    STATUS	current
    DESCRIPTION
	"An AAA	server state change notification is generated
	 whenever casState changes value.
	"
    ::=	{ cAAAServerMIBNotifications 1 }



-- ******************************************************************
-- Conformance and Compliance
-- ******************************************************************
cAAAServerMIBConformance  OBJECT IDENTIFIER ::=	{ ciscoAAAServerMIB 3 }

casMIBCompliances  OBJECT IDENTIFIER ::= { cAAAServerMIBConformance 1 }
casMIBGroups	   OBJECT IDENTIFIER ::= { cAAAServerMIBConformance 2 }

-- compliance statements

casMIBCompliance MODULE-COMPLIANCE
	STATUS	    current
	DESCRIPTION
	    "The compliance statement for entities which
	     implement the CISCO AAA Server MIB"
	MODULE	    -- this module
	MANDATORY-GROUPS
	    {
	      casConfigGroup,
	      casStatisticsGroup,
	      casServerNotificationGroup
	    }

	    OBJECT casAddress
	    MIN-ACCESS read-only
	    DESCRIPTION
		 "Create/Write access is not required."

	    OBJECT casAuthenPort
	    MIN-ACCESS read-only
	    DESCRIPTION
		 "Create/Write access is not required."

	    OBJECT casAcctPort
	    MIN-ACCESS read-only
	    DESCRIPTION
		 "Create/Write access is not required."

	    OBJECT casKey
	    MIN-ACCESS read-only
	    DESCRIPTION
		 "Create/Write access is not required."

	    OBJECT casConfigRowStatus
	    MIN-ACCESS read-only
	    DESCRIPTION
		 "Create/Write access is not required."


	::= { casMIBCompliances	1 }


-- units of conformance

casStatisticsGroup OBJECT-GROUP
	OBJECTS	{
	    casAuthenRequests,
	    casAuthenRequestTimeouts,
	    casAuthenUnexpectedResponses,
	    casAuthenServerErrorResponses,
	    casAuthenIncorrectResponses,
	    casAuthenResponseTime,
	    casAuthenTransactionSuccesses,
	    casAuthenTransactionFailures,
	    casAuthorRequests,
	    casAuthorRequestTimeouts,
	    casAuthorUnexpectedResponses,
	    casAuthorServerErrorResponses,
	    casAuthorIncorrectResponses,
	    casAuthorResponseTime,
	    casAuthorTransactionSuccesses,
	    casAuthorTransactionFailures,
	    casAcctRequests,
	    casAcctRequestTimeouts,
	    casAcctUnexpectedResponses,
	    casAcctServerErrorResponses,
	    casAcctIncorrectResponses,
	    casAcctResponseTime,
	    casAcctTransactionSuccesses,
	    casAcctTransactionFailures,
	    casState,
	    casCurrentStateDuration,
	    casPreviousStateDuration,
	    casTotalDeadTime,
	    casDeadCount
	}
	STATUS	    current
	DESCRIPTION
	    "Objects for providing AAA server statistics and status.
	    "
	::= { casMIBGroups 1 }

casConfigGroup OBJECT-GROUP
	OBJECTS	{
	    casServerStateChangeEnable,
	    casAddress,
	    casAuthenPort,
	    casAcctPort,
	    casKey,
	    casPriority,
	    casConfigRowStatus
	}
	STATUS	    current
	DESCRIPTION
	    "Objects for configuring the AAA servers.
	    "
	::= { casMIBGroups 2 }

casServerNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS { casServerStateChange	}
   STATUS current
   DESCRIPTION
       "The collection of notifications	used for
	monitoring AAA server status"
   ::= { casMIBGroups 3	}



END

