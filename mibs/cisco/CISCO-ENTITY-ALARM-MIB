-- *****************************************************************
-- Definitions of managed objects supporting alarm monitoring.
--
-- March 1999, <PERSON>
--
-- Copyright (c) 1999 by cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-ENTITY-ALARM-MIB DEFINITIONS ::= BEGIN

IMPORTS
    Gauge32,
    MODULE-IDENTITY,
    NOTIFICATION-TYPE,
    OBJECT-TYPE                         FROM SNMPv2-SMI
    TEXTUAL-CONVENTION,
    AutonomousType,
    DisplayString,
    RowStatus,
    TimeStamp,
    TruthValue                          FROM SNMPv2-TC
    MODULE-COMPLIANCE,
--  NOTIFICATION-GROUP,
    OBJECT-GROUP                        FROM SNMPv2-CONF
    SnmpAdminString                     FROM SNMP-FRAMEWORK-MIB
    entPhysicalIndex,
    PhysicalIndex                       FROM ENTITY-MIB
    ciscoMgmt                           FROM CISCO-SMI
    Unsigned32                          FROM CISCO-TC
    ;

ciscoEntityAlarmMIB MODULE-IDENTITY
    LAST-UPDATED   "9907062150Z" -- 07/06/99 16:50 PM EDT
    ORGANIZATION   "Cisco Systems, Inc."
    CONTACT-INFO   "Cisco Systems
                    Customer Service

                    Postal: 170 W Tasman Drive
                    San Jose, CA 95134

                    Tel: ****** 553-NETS

                    E-mail: <EMAIL>"
    DESCRIPTION
        "This MIB module defines the managed objects that support the
        monitoring of alarms generated by physical entities contained
        by the system, including chassis, slots, modules, ports, power
        supplies, and fans.  In order to monitor alarms generated by a
        physical entity, it must be represented by a row in the
        entPhysicalTable (see ENTITY-MIB)."
    ::= { ciscoMgmt 138 }

AlarmType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An arbitrary integer value that uniquely identifies an event
        relative to a physical entity contained by a system."
    SYNTAX INTEGER (0..255)

AlarmSeverity ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "Each alarm type defined by a vendor type employed by the
        system has an associated severity.  Bellcore TR-NWT-000474
        defines these severities as follows:

        'critical'  An alarm used to indicate a severe, service-
                    affecting condition has occurred and that immediate
                    corrective action is imperative, regardless of the
                    time of day or day of the week.

        'major'     An alarm used for hardware or software conditions
                    that indicate a serious disruption of service or the
                    malfunctioning or failure of important hardware.
                    These troubles require the immediate attention and
                    response of a technician to restore or maintain
                    system capability.  The urgency is less than in
                    critical situations because of a lesser immediate
                    or impending effect on service or system
                    performance.

        'minor'     An alarm used for troubles that do not have a
                    serious effect on service to customers or for
                    troubles in hardware that are not essential to
                    the operation of the system.

        'info'      An indication used to raise attention to a condition
                    that could possibly be an impending problem or to 
                    notify the customer of an event that improves
                    operation."
    REFERENCE
        "Bellcore Technical Reference TR-NWT-000474 Issue 4, December
        1993, OTGR Section 4.  Network Maintenance: Alarm and Control -
        Network Element."
    SYNTAX INTEGER {
        critical(1),
        major(2),
        minor(3),
        info(4)
    }

AlarmSeverityOrZero ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "A value of either '0' or a valid alarm severity."
    SYNTAX INTEGER (0..4)

AlarmList ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "For each unique type of physical entity (i.e., for each set
        of physical entities sharing a unique entPhysicalVendorType
        OID), there an exists unique alarm space.  Observe that it
        is not necessary that all the alarms within a space be defined.

        An OCTET STRING represents an alarm list, in which each
        bit represents an alarm type.  The bits in the first octet
        represent alarm types identified by the integer values 1
        through 8, inclusive, The bits in the second octet represent
        alarm types identified by the integer values 9 through 16,
        inclusive, and so forth.  The least significant bit of an
        octet represents the alarm type identified by the lowest
        integer value, and the most significant bit represents the
        alarm type identified by the highest integer value.  The
        figure shown below illustrates the format of an alarm list.

         Octet 1             Octet 32

         7 6 5 4 3 2 1 0     7 6 5 4 3 2 1 0
        +-+-+-+-+-+-+-+-+   +-+-+-+-+-+-+-+-+
        |               |...|               |
        +-+-+-+-+-+-+-+-+   +-+-+-+-+-+-+-+-+
         | | | | | | | |     | | | | | | | |
         | | | | | | | |     | | | | | | | +- Alarm 248
         | | | | | | | |     | | | | | | +--- Alarm 249
         | | | | | | | |     | | | | | +----- Alarm 250
         | | | | | | | |     | | | | +------- Alarm 251
         | | | | | | | |     | | | +--------- Alarm 252
         | | | | | | | |     | | +----------- Alarm 253
         | | | | | | | |     | +------------- Alarm 254
         | | | | | | | |     +--------------- Alarm 255
         | | | | | | | |                          :
         | | | | | | | |                          :
         | | | | | | | +--------------------- Alarm 0
         | | | | | | +----------------------- Alarm 1
         | | | | | +------------------------- Alarm 2
         | | | | +--------------------------- Alarm 3
         | | | +----------------------------- Alarm 4
         | | +------------------------------- Alarm 5
         | +--------------------------------- Alarm 6
         +----------------------------------- Alarm 7

        An alarm list of length N, where N < 32, represents an alarm
        list for which alarms N*8 through 255 have the value of '0'.
        A special case is an alarm list having a length of '0', which
        represents an alarm list of all zeros."
    SYNTAX OCTET STRING (SIZE (0..32))

AlarmFilterProfileType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An integer value that uniquely identifies an alarm filter
        profile."
    SYNTAX Unsigned32

-- MIB Object Definitions

ciscoEntityAlarmMIBObjects   OBJECT IDENTIFIER ::= { ciscoEntityAlarmMIB 1 }

ceAlarmDescription  OBJECT IDENTIFIER ::= { ciscoEntityAlarmMIBObjects 1 }
ceAlarmMonitoring   OBJECT IDENTIFIER ::= { ciscoEntityAlarmMIBObjects 2 }
ceAlarmHistory      OBJECT IDENTIFIER ::= { ciscoEntityAlarmMIBObjects 3 }
ceAlarmFiltering    OBJECT IDENTIFIER ::= { ciscoEntityAlarmMIBObjects 4 }

-- Alarm Description Map Table

ceAlarmDescrMapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CeAlarmDescrMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "For each type of entity (represented entPhysicalVendorType
        OID), this table contains a mapping between a unique 
        ceAlarmDescrIndex and entPhysicalvendorType OID."
    ::= { ceAlarmDescription 1 }

ceAlarmDescrMapEntry OBJECT-TYPE
    SYNTAX      CeAlarmDescrMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A mapping between an alarm description and a vendor type."
    INDEX       { ceAlarmDescrIndex }
    ::= { ceAlarmDescrMapTable 1 }

CeAlarmDescrMapEntry ::= SEQUENCE {
    ceAlarmDescrIndex      Unsigned32,
    ceAlarmDescrVendorType AutonomousType
}

ceAlarmDescrIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This object uniquely identifies an alarm description."
    ::= { ceAlarmDescrMapEntry 1 }

ceAlarmDescrVendorType OBJECT-TYPE
    SYNTAX      AutonomousType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies an object identifier (typically an
        enterprise-specific OID) that uniquely identifies the vendor
        type of those physical entities that this alarm description
        applies to."
    ::= { ceAlarmDescrMapEntry 2 }

-- Alarm Description Table

ceAlarmDescrTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CeAlarmDescrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains a description for each alarm type
        defined by each vendor type employed by the system.
        Observe that this table is sparse in nature, as it is
        rarely the case that a physical entity type needs to 
        define every alarm in its alarm space."
    ::= { ceAlarmDescription 2 }

ceAlarmDescrEntry OBJECT-TYPE
    SYNTAX      CeAlarmDescrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A collection of attributes that describe an alarm type."
    INDEX       { ceAlarmDescrIndex, ceAlarmDescrAlarmType }
    ::= { ceAlarmDescrTable 1 }

CeAlarmDescrEntry ::= SEQUENCE {
    ceAlarmDescrAlarmType  AlarmType,
    ceAlarmDescrSeverity   AlarmSeverityOrZero,
    ceAlarmDescrText       SnmpAdminString
}

ceAlarmDescrAlarmType OBJECT-TYPE
    SYNTAX      AlarmType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This object specifies the alarm type being described."
    ::= { ceAlarmDescrEntry 1 }

ceAlarmDescrSeverity OBJECT-TYPE
    SYNTAX      AlarmSeverityOrZero
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This object specifies the severity associated with the
        alarm type.

        An implementation may chose to not allow dynamic severity
        assignment, in which case it would restrict access to this
        object to be read-only.

        If an implementation allows dynamic severity assignment, then
        a management client can revert to the default severity by
        writing the value '0' to this object.

        There exists a class of systems that should implement dynamic
        severity assignment.  For example, consider a DSLAM (Digital
        Subscriber Loop Access Multiplexor) designed for both the
        central office and pedestal environments.  A 'pedestal' is
        typically a dark-green metal box mounted on a concrete or stone
        foundation in which carrier-class companies house equipment.
        The central office typically controls the temperature and
        humidity of the environment, reducing reliance on a system's
        fans.  Thus, the customer probably has a desire to reduce the 
        severity of alarms indicating the failure of a fan.  However, a
        pedestal environment has a much greater reliance on a system's
        fans.  Thus, the customer probably has a desire to increase the
        severity of alarms indicating the failure of a fan."
    ::= { ceAlarmDescrEntry 2 }

ceAlarmDescrText OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies a human-readable message describing
        the alarm."
    ::= { ceAlarmDescrEntry 3 }

-- Alarm Monitoring

ceAlarmCriticalCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of this object specifies the number of alarms
        currently asserted with a severity of 'critical'."
    ::= { ceAlarmMonitoring 1 }

ceAlarmMajorCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of this object specifies the number of alarms
        currently asserted with a severity of 'major'."
    ::= { ceAlarmMonitoring 2 }

ceAlarmMinorCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of this object specifies the number of alarms
        currently asserted with a severity of 'minor'."
    ::= { ceAlarmMonitoring 3 }

ceAlarmCutOff OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If the management client writes a value of 'true' to this
        object, the agent stops signalling all external audible alarms
        under the control of the agent.  Reading this object should
        always result in a value of 'false'.

        Observe that alarm cutoff does not have an effect on monitoring,
        history logging, generation of notifications, or syslog message
        generation.  It also does not prevent the agent from signalling
        external audible alarms for alarms asserted after alarm-cutoff.

        This object emulates the 'alarm cut-off' mechanism typically
        installed in a central office (e.g., a big red button).  Observe
        this object should neither affect external visual alarms under
        the control of the agent, nor should it affect the current state
        of alarms being asserted by the system."
    ::= { ceAlarmMonitoring 4 }

-- Alarm Table

ceAlarmTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CeAlarmEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table specifies alarm control and status information
        related to each physical entity contained by the system,
        including the alarms currently being asserted by each physical
        entity capable of generating alarms."
    ::= { ceAlarmMonitoring 5 }

ceAlarmEntry OBJECT-TYPE
    SYNTAX      CeAlarmEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Alarm control and status information related to the 
        corresponding physical entity, including a list of those
        alarms currently being asserted by that physical entity."
    INDEX       { entPhysicalIndex }
    ::= { ceAlarmTable 1 }

CeAlarmEntry ::= SEQUENCE {
    ceAlarmFilterProfile AlarmFilterProfileType,
    ceAlarmSeverity      AlarmSeverityOrZero,
    ceAlarmList          AlarmList
}

ceAlarmFilterProfile OBJECT-TYPE
    SYNTAX      AlarmFilterProfileType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This object specifies the alarm filter profile associated
        with the corresponding physical entity.  An alarm filter
        profile controls which alarm types the agent will monitor
        and signal for the corresponding physical entity.

        If the value of this object is '0', then the agent monitors
        and signals all alarms associated with the corresponding
        physical entity."
    ::= { ceAlarmEntry 1 }

ceAlarmSeverity OBJECT-TYPE
    SYNTAX      AlarmSeverityOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies the highest severity alarm currently
        being asserted by the corresponding physical entity.  A value
        of '0' indicates that there the corresponding physical entity
        currently is not asserting any alarms."
    ::= { ceAlarmEntry 2 }

ceAlarmList OBJECT-TYPE
    SYNTAX      AlarmList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies those alarms currently being asserted
        by the corresponding physical entity.  Note, an alarm indicates
        a condition, not an event.  An alarm has two states:

            'asserted'  Indicates that the condition described by the
                        alarm exists.

            'cleared'   Indicates that the condition described by the
                        alarm does not exist.

        For example, a slot in a chassis may define an alarm that
        specifies whether the slot contains a module.  At the time of
        module insertion, the physical entity corresponding to the slot
        asserts this alarm, and the alarm remains asserted until the 
        slot becomes empty.

        If an alarm is being asserted by the physical entity, then the
        corresponding bit in the alarm list is set to a one. Observe
        that if the physical entity is not currently asserting any
        alarms, then the list will have a length of zero."
    ::= { ceAlarmEntry 3 }

-- Alarm History Table

ceAlarmHistTableSize OBJECT-TYPE
    SYNTAX      INTEGER (0..500)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This object specifies the number of entries that the 
        ceAlarmHistTable can contain.  When a physical entity
        generates an unfiltered alarm, and the capacity of the
        ceAlarmHistTable has reached the value specified by
        this object, then the agent deletes the oldest entity in
        order to accommodate the new entry. A value of '0' prevents
        any history from being retained.  "
    ::= { ceAlarmHistory 1 }

ceAlarmHistLastIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies the value of the ceAlarmHistIndex
        object corresponding to the last entry added to the table by the
        agent.

        If the management client uses the notifications defined by this
        module, then it can poll this object to determine whether it has
        missed a notification sent by the agent."
    ::= { ceAlarmHistory 2 }

ceAlarmHistTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CeAlarmHistEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains a history of ceAlarmIndicate and
        ceAlarmClear traps generated by the agent."
    ::= { ceAlarmHistory 3 }

ceAlarmHistEntry OBJECT-TYPE
    SYNTAX      CeAlarmHistEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The information conveyed by a ceAlarmIndicate or
        ceAlarmClear trap."
    INDEX       { ceAlarmHistIndex }
    ::= { ceAlarmHistTable 1 }

CeAlarmHistEntry ::= SEQUENCE {
    ceAlarmHistIndex            Unsigned32,
    ceAlarmHistType             INTEGER,
    ceAlarmHistEntPhysicalIndex PhysicalIndex,
    ceAlarmHistAlarmType        AlarmType,
    ceAlarmHistSeverity         AlarmSeverity,
    ceAlarmHistTimeStamp        TimeStamp
}

ceAlarmHistIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An integer value uniquely identifying the entry in the table.
        The value of this object starts at '1' and monotonically
        increases for each alarm condition transition monitored by the
        agent.  If the value of this object is '4294967295', the agent
        will reset it to '1' upon monitoring the next alarm condition
        transition."
    ::= { ceAlarmHistEntry 1 }

ceAlarmHistType OBJECT-TYPE
    SYNTAX      INTEGER { asserted(1), cleared(2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies whether the agent created the entry as
        the result of an alarm being asserted or cleared."
    ::= { ceAlarmHistEntry 2 }

ceAlarmHistEntPhysicalIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies the physical entity that generated
        the alarm."
    ::= { ceAlarmHistEntry 3 }

ceAlarmHistAlarmType OBJECT-TYPE
    SYNTAX      AlarmType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies the type of alarm generated."
    ::= { ceAlarmHistEntry 4 }
 
ceAlarmHistSeverity OBJECT-TYPE
    SYNTAX      AlarmSeverity
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies the severity of the alarm generated."
    ::= { ceAlarmHistEntry 5 }

ceAlarmHistTimeStamp OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object specifies the value of the sysUpTime object at
        the time the alarm was generated."
    ::= { ceAlarmHistEntry 6 }

-- Alarm Filter Profile Table

ceAlarmNotifiesEnable OBJECT-TYPE
    SYNTAX      AlarmSeverityOrZero
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This object specifies a severity threshold governing the
        generation of ceAlarmAsserted and ceAlarmCleared
        notifications.  For example, if the value of this object is
        set to 'major', then the agent generates these notifications
        if and only if the severity of the alarm being indicated is
        'major' or 'critical'.  The value of '0' disables the 
        generation of notifications.

        Observe that this setting overrides the value of the 
        ceAlarmFilterNotifiesEnabled object.

        This object affects notification generation only; that is, it
        does not affect monitoring, history logging, and syslog message
        generation."
    ::= { ceAlarmFiltering 1 }

ceAlarmSyslogEnable OBJECT-TYPE
    SYNTAX      AlarmSeverityOrZero
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This object specifies a severity threshold governing the
        generation of syslog messages corresponding to alarms.  For
        example, if the value of this object is set to 'major', then
        the agent generates these a syslog message if and only if the
        severity of the alarm being indicated is 'major' or 'critical'.
        The value of '0' disables the generation of syslog messages
        corresponding to alarms.

        Observe that this setting overrides the value of the 
        ceAlarmFilterSyslogEnabled object.

        This object affects syslog message generation only; that is, it
        does not have an effect on monitoring, history logging, and
        generation of notifications."
    ::= { ceAlarmFiltering 2 }

ceAlarmFilterProfileIndexNext OBJECT-TYPE
    SYNTAX      AlarmFilterProfileType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains an appropriate value to be used
        for ceAlarmFilterIndex when creating entries in the
        ceAlarmFilterProfileTable.  The value '0' indicates
        that no unassigned entries are available.  To obtain
        a ceAlarmFilterIndex, the management client issues
        a get request.  The agent has the responsibility of 
        modifying the value of this object following each 
        successful get request."
    ::= { ceAlarmFiltering 3 }

ceAlarmFilterProfileTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CeAlarmFilterProfileEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains a list of alarm filter profiles."
    ::= { ceAlarmFiltering 4 }

ceAlarmFilterProfileEntry OBJECT-TYPE
    SYNTAX      CeAlarmFilterProfileEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "When a physical entity asserts/clears an alarm AND the
        ceAlarmFilterProfile object is not '0', the agent applies
        the specified alarm filter profile in processing the alarm.
        The agent uses the following procedure in processing the
        transition of an alarm condition of a given type:

        1)  If the alarm list specified by the alarm filter profile's
            ceAlarmFilterAlarmsEnabled object specifies that the alarm
            type is disabled, then the agent performs no further
            processing.

        2)  The agent creates an entry in the ceAlarmHistTable.

        3)  If the alarm list specified by the alarm filter profile's 
            ceAlarmFilterNotifiesEnabled object specifies that the alarm
            type is enabled, then the agent generates the appropriate
            notification.

        4)  If the alarm list specified by the alarm filter profile's
            ceAlarmFilterSyslogEnabled object specifies that the alarm
            type is enabled, then the agent generates the appropriate
            syslog message."
    INDEX       { ceAlarmFilterIndex }
    ::= { ceAlarmFilterProfileTable 1 }

CeAlarmFilterProfileEntry ::= SEQUENCE {
    ceAlarmFilterIndex           AlarmFilterProfileType,
    ceAlarmFilterStatus          RowStatus,
    ceAlarmFilterAlias           DisplayString,
    ceAlarmFilterAlarmsEnabled   AlarmList,
    ceAlarmFilterNotifiesEnabled AlarmList,
    ceAlarmFilterSyslogEnabled   AlarmList
}

ceAlarmFilterIndex OBJECT-TYPE
    SYNTAX      AlarmFilterProfileType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This object uniquely identifies the alarm filter profile."
    ::= { ceAlarmFilterProfileEntry 1 }

ceAlarmFilterStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object facilitates the creation, modification, or 
        deletion of a conceptual row in this table.

        A management client can create a conceptual row in this
        table by setting this object to 'createAndWait' or 
        'createAndGo'.  If a request to create a conceptual row
        in this table fails, then the system is not capable of
        supporting any more alarm filters.

        Before modifying a conceptual row in this table, the 
        management client must set this object to 'notInService'.
        After modifying a conceptual row in this table, the 
        management client must set this object to 'active'.
        This operation causes the modifications made to an
        alarm filter profile to take effect.

        An implementation should not allow a conceptual row in
        this table to be deleted if one or more physical entities
        reference it."
    ::= { ceAlarmFilterProfileEntry 2 }

ceAlarmFilterAlias OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object specifies an arbitrary name associated with the
        alarm filter profile by the management client, and provides
        a non-volatile 'handle' for the alarm filter profile.

        On the first instantiation of an alarm filter profile, the
        value of this object is a zero-length string.  However, an
        agent may choose to set the value to a locally unique default
        value.

        If an implementation supports write access to this object,
        then the agent is responsible for ensuring the retention
        of any value written to this object until a management client
        deletes it.  The level of retention must span reboots and 
        reinitializations of the network management system, including
        those that result in different assignments to the value of
        the entPhysicalIndex associated with the physical entity."
    ::= { ceAlarmFilterProfileEntry 3 }

ceAlarmFilterAlarmsEnabled OBJECT-TYPE
    SYNTAX      AlarmList
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object specifies a list of alarms that are enabled."
    ::= { ceAlarmFilterProfileEntry 4 }

ceAlarmFilterNotifiesEnabled OBJECT-TYPE
    SYNTAX      AlarmList
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object specifies a list of alarms for which notification
        generation is enabled."
    ::= { ceAlarmFilterProfileEntry 5 }

ceAlarmFilterSyslogEnabled OBJECT-TYPE
    SYNTAX      AlarmList
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object specifies a list of alarms for which syslog
        message generation is enabled."
    ::= { ceAlarmFilterProfileEntry 6 }

-- MIB Notification Definitions

ciscoEntityAlarmMIBNotificationsPrefix OBJECT IDENTIFIER ::=
    { ciscoEntityAlarmMIB 2 }

ciscoEntityAlarmMIBNotifications      OBJECT IDENTIFIER ::= 
    { ciscoEntityAlarmMIBNotificationsPrefix 0 }

ceAlarmAsserted NOTIFICATION-TYPE
    OBJECTS     {
        ceAlarmHistEntPhysicalIndex,
        ceAlarmHistAlarmType,
        ceAlarmHistSeverity,
        ceAlarmHistTimeStamp
        }
    STATUS      current
    DESCRIPTION    
        "The agent generates this trap when a physical entity
        asserts an alarm."
    ::= { ciscoEntityAlarmMIBNotifications 1 }

ceAlarmCleared NOTIFICATION-TYPE
    OBJECTS     {
        ceAlarmHistEntPhysicalIndex,
        ceAlarmHistAlarmType,
        ceAlarmHistSeverity,
        ceAlarmHistTimeStamp
    }
    STATUS      current
    DESCRIPTION    
        "The agent generates this trap when a physical entity
        clears a previously asserted alarm."
    ::= { ciscoEntityAlarmMIBNotifications 2 }

-- MIB Conformance Statements

ciscoEntityAlarmMIBConformance OBJECT IDENTIFIER ::=
    { ciscoEntityAlarmMIB 3 }

ciscoEntityAlarmMIBCompliances OBJECT IDENTIFIER ::=
    { ciscoEntityAlarmMIBConformance 1 }

ciscoEntityAlarmMIBGroups OBJECT IDENTIFIER ::=
    { ciscoEntityAlarmMIBConformance 2 }

ceAlarmMIBCompliance MODULE-COMPLIANCE
    STATUS current
    DESCRIPTION
        "The compliance statement for entities that implement the
        CISCO-ALARM-MIB.  Implementation of this MIB module is strongly
        recommended for any platform targeted for a carrier-class
        environment."

    MODULE -- this module
        MANDATORY-GROUPS {
            ceAlarmDescriptionGroup,
            ceAlarmGroup,
            ceAlarmHistGroup,
--            ceAlarmNotificationsGroup,
            ceAlarmFilterGroup
        }

    GROUP ceAlarmFilterProfileGroup
    DESCRIPTION
        "This group is optional."
    ::= { ciscoEntityAlarmMIBCompliances 1 }

ceAlarmDescriptionGroup OBJECT-GROUP
    OBJECTS {
        ceAlarmDescrVendorType,
        ceAlarmDescrSeverity,
        ceAlarmDescrText
    }
    STATUS current
    DESCRIPTION
        "A collection of managed objects defining the description
        of alarms."
    ::= { ciscoEntityAlarmMIBGroups 1 }

ceAlarmGroup OBJECT-GROUP
    OBJECTS {
        ceAlarmCriticalCount,
        ceAlarmMajorCount,
        ceAlarmMinorCount,
        ceAlarmCutOff,
        ceAlarmFilterProfile,
        ceAlarmSeverity,
        ceAlarmList
    }
    STATUS current
    DESCRIPTION
        "A collection of managed objects defining alarm reporting
        by physical entity."
    ::= { ciscoEntityAlarmMIBGroups 2 }

ceAlarmHistGroup OBJECT-GROUP
    OBJECTS {
        ceAlarmHistTableSize,
        ceAlarmHistLastIndex,
        ceAlarmHistType,
        ceAlarmHistEntPhysicalIndex,
        ceAlarmHistAlarmType,
        ceAlarmHistSeverity,
        ceAlarmHistTimeStamp
    }
    STATUS current
    DESCRIPTION
        "A collection of managed objects defining alarm logging."
    ::= { ciscoEntityAlarmMIBGroups 3 }

ceAlarmFilterGroup OBJECT-GROUP
    OBJECTS {
        ceAlarmNotifiesEnable,
        ceAlarmSyslogEnable
    }
    STATUS current
    DESCRIPTION
        "A collection of managed objects that control the generation
        of all notifications and syslog messages."
    ::= { ciscoEntityAlarmMIBGroups 4 }

ceAlarmFilterProfileGroup OBJECT-GROUP
    OBJECTS {
        ceAlarmFilterProfileIndexNext,
        ceAlarmFilterStatus,
        ceAlarmFilterAlias,
        ceAlarmFilterAlarmsEnabled,
        ceAlarmFilterNotifiesEnabled,
        ceAlarmFilterSyslogEnabled
    }
    STATUS current
    DESCRIPTION
        "A collection of managed objects that support alarm filtering."
    ::= { ciscoEntityAlarmMIBGroups 5 }

--ceAlarmNotificationsGroup NOTIFICATION-GROUP
--    OBJECTS {
--        ceAlarmAsserted,
--        ceAlarmCleared
--    }
--    STATUS current
--    DESCRIPTION
--        "A collection of traps generated by the agent upon alarm
--        generation (whether an alarm is being asserted or cleared)."
--    ::= { ciscoEntityAlarmMIBGroups 7 }
--
END

