-- *****************************************************************
-- CISCO-ENTITY-EXT-MIB.my
--   
-- <PERSON><PERSON> Extension to ENTITY-MIB(RFC2737)
--   
-- April 2001, A <PERSON>
-- May  2004, <PERSON><PERSON><PERSON>
-- Mar  2015, <PERSON><PERSON><PERSON>
--   
-- Copyright (c) 2001-2018 by cisco Systems Inc.
-- All rights reserved.
--   
-- *****************************************************************

CISCO-ENTITY-EXT-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    Unsigned32,
    NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    OBJECT-GRO<PERSON>,
    NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    TEXTUAL-CONVENTION,
    TruthValue
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    entPhysicalIndex,
    entPhysicalEntry,
    entPhysicalDescr,
    entPhysicalName,
    entPhysicalContainedIn
        FROM ENTITY-MIB
    Unsigned64
        FROM CISCO-TC
    ciscoMgmt
        FROM CISCO-SMI;


ciscoEntityExtMIB MODULE-IDENTITY
    LAST-UPDATED    "201804040000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
            "Cisco Systems
            Customer Service

            Postal: 170 W. Tasman Drive
            San Jose, CA 95134
            USA

            Tel: ****** 553-NETS

            E-mail: <EMAIL>"
    DESCRIPTION
        "This MIB is an extension of the ENTITY-MIB
        specified in RFC2737.

        This MIB module contains  Cisco-defined  extensions 
        to the  entityPhysicalTable to represent information
        related to entities of class module(entPhysicalClass
        = 'module') which have a Processor.

        A processor module is defined as a physical entity
        that has a CPU, RAM and NVRAM  so that
        it can independently
           - load a bootable image
           - save configuration.
        This module is the entry point for external
        applications like SNMP Manager, CLI, FTP etc.

        Line card is an interface card         with at least a 
        Processor and RAM. This might be referred to as 
        Service Module in some cisco products.

        A configuration register is  a 16 bit 
        software register.
        The configuration register is mainly used to 
        check for instructions on where to find the Cisco 
        Operating System software.
        Some other functions of configuration register are:
         - To select a boot source and default boot filename.
         - To enable or disable the Break function.
         - To control broadcast addresses.
         - To set the console terminal baud rate.
         - To load operating software from Flash memory.
         - To allow us to manually boot the system using the 
           boot command at the bootstrap program prompt.

        Booting is the process of initializing the
        hardware and starting the Operating System."
    REVISION        "201804040000Z"
    DESCRIPTION
        "Added the following groups OBJECT-GROUP
         ceExtNVRAMOverflowGroup
         ceExtHCNVRAMGroup"
    REVISION        "201504170000Z"
    DESCRIPTION
        "Added ceExtUSBModemTable,
        ceExtUSBModemPlugInNotif, ceExtUSBModemPlugOutNotif,
        ceExtEntUsbModemNotifEnable."
    REVISION        "201409120000Z"
    DESCRIPTION
        "Changed the description ceExtEntDoorOpenNotif to
        include ceExtEntDoorOpenNotif instead 
        of ceExtEntDoorCloseNotif."
    REVISION        "201403270000Z"
    DESCRIPTION
        "Changed the Max Access of ceExtEntDoorNotifEnable from
        read-only to read-write."
    REVISION        "201308060000Z"
    DESCRIPTION
        "Added following object to ciscoEntityExtMIBNotifications
        ceExtBreakOutPortInserted
        ceExtBreakOutPortRemoved

        Added following object to ceExtNotificationControlObjects
        ceExtBreakOutPortNotifEnable

        Added the following new groups to ciscoEntityExtMIBGroups
        ceExtBreakOutPortNotifGroup
        ceExtBreakOutPortNotifControlGroup.

        Added a new compliance ciscoEntityExtMIBComplianceRev5 which
        deprecates ciscoEntityExtMIBComplianceRev4

        A port can be configured to act as multiple interfaces.
        Ex: One Hundred GigE port can act as 10 TenGigE interface
        Breakout is the term that can be used to define this behaviour.
        Alternatively slice term is also used. 

        When a breakout config is applied on the interface, the base 
        interface will be replaced with multiple breakout interfaces. 
        Reference to the base interface will be removed from the system 
        and breakout interface will be made available. Vice versa will 
        happen when breakout config will be removed from the system."
    REVISION        "201308050000Z"
    DESCRIPTION
        "Added following new objects to ciscoEntityExtMIBNotifications.
        ceExtEntDoorCloseNotif
        ceExtEntDoorOpenNotif

        Added following new groups to ciscoEntityExtMIBGroups.
        ceExtNotificationControlObjects.

        Added following new objects to ceExtNotificationControlObjects
        ceExtEntDoorNotifEnable.

        Added the following new groups to ciscoEntityExtMIBGroups
        ceExtEntDoorNotifGroup
        ceExtEntDoorNotifControlGroup.

        Added a new compliance ciscoEntityExtMIBComplianceRev4 which
        deprecates ciscoEntityExtMIBComplianceRev3."
    REVISION        "200811240000Z"
    DESCRIPTION
        "Added following new objects to ceExtPhysicalProcessorTable.
        ceExtProcessorRamOverflow and ceExtHCProcessorRam

        Added following new groups to ciscoEntityExtMIBGroups.
        ceExtPhyProcessorOverflowGroup and ceExtPhyProcessorHCGroup.

        Added a new compliance ciscoEntityExtMIBComplianceRev3 which
        deprecates ciscoEntityExtMIBComplianceRev2."
    REVISION        "200407060000Z"
    DESCRIPTION
        "Added a new table object 'ceExtEntPhysicalTable' ."
    REVISION        "200403030000Z"
    DESCRIPTION
        "Importing Unsigned32 from SNMPv2-SMI, instead of CISCO-TC."
    REVISION        "200401260000Z"
    DESCRIPTION
        "Added new enum to ceExtEntityLEDType."
    REVISION        "200308240000Z"
    DESCRIPTION
        "Added ceExtEntityLEDTable.  Added ceExtKickstartImageList to
        ceExtConfigRegTable.
        Added new group ciscoEntityExtLEDGroup.
        Added group ceExtSysBootImageListGroupRev1."
    REVISION        "200105170000Z"
    DESCRIPTION
        "Corrected the description for the TC
        ConfigRegisterValue."
    REVISION        "200104050000Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 195 }


ciscoEntityExtMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoEntityExtMIB 1 }


-- textual conventions

ConfigRegisterValue ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "2x"
    STATUS          current
    DESCRIPTION
        "An Integer containing the value of config register.

        The definition of individual bits from right to left 
        when set are as follows:

        00 to        
        03                Boot Fields(Refer below for
                       explanation).

        04 to 
        05                Not Used.

        06                Causes system software to ignore the 
                       contents of NVRAM.

        07                Enable the original equipment 
                       manufacturer (OEM) bit.

        08                The Break function is disabled.

        09                Not used.

        10                Broadcast based on 0.0.0.0 IP address.

        11 to 
        12                Defines the console baud rate as below:
                       Bits 11 & 12 unset: 9600 baud(default),
                       Bit 11 set & 12 unset: 4800 baud,
                       Bit 11 unset & 12 set: 1200 baud,
                       Bit 11 set & 12 set: 2400 baud

        13                Boots default Flash software if 
                       network boot fails.

        14                IP broadcasts do not have network 
                       numbers.

        15                Enables diagnostic messages and ignores 
                       the contents of NVRAM.


        Meanings for different values of Boot Fields
        (Bits 00 to 03) are explained below:

        Value(in hex)        Description
        ------------    -----------
        00                <USER> <GROUP> or reload, the system
                       remains at the ROM monitor prompt 
                       (rommon>), awaiting a user command 
                       to boot the system manually by means 
                       of the rommon boot command.

        01                On powerup or reload, the system loads
                       the system image found in onboard 
                       Flash memory.

        02 to                On powerup or reload, the system loads
        0F                the system image specified by 
                       ceExtSysBootImageList.
                       It tries to boot the
                       image in the order in which 
                       the image names are entered in 
                       ceExtSysBootImageList. If 
                       it cannot boot any image in the
                       ceExtSysBootImageList, it 
                       stays in ROM monitor mode."
    SYNTAX          OCTET STRING (SIZE (2))

BootImageList ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "This contains a list of  boot images,
        each separated by the ';' (semi-colon) character.  
        The following provides a syntax for parsing a single
        boot image list item.

        <device>:[filename] | <URL>

        <device> can be (but not limited to):
           flash, bootflash, slot0, rom, C
        The transfer protocol used in the <URL>
        can be (but not limited to):
            tftp, ftp, rcp, mop.

        The following is en example containing two boot image
        names:  
           disk0:c7100-ik2s-mz;tftp://dirt/c7100-ik2s-mz

        If the filename is not  specified, then the
        first file on the <device> will be used.

           If the last two characters in the returned value
        are ';+' this indicates that additional boot image
        items are configured on the device but can not
        be returned because the maximum string size limitation
        would be exceeded.

        For example:
           disk0:image1;disk0:image2;+"
    SYNTAX          OCTET STRING (SIZE (0..255))
-- ceExtPhysicalProcessorTable

ceExtPhysicalProcessorTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CeExtPhysicalProcessorEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The ceExtPhysicalProcessorTable extends
        the ENTITY-MIB entPhysicalTable for modules
        (Non FRUs(Field Replacable Units) or FRUs)."
    REFERENCE       "RFC2737: Section 2.12.1"
    ::= { ciscoEntityExtMIBObjects 1 }

ceExtPhysicalProcessorEntry OBJECT-TYPE
    SYNTAX          CeExtPhysicalProcessorEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A ceExtPhysicalProcessorTable entry extends
        a corresponding entPhysicalTable entry of class
        module(entPhysicalClass = 'module').

        A processor module or line card which 
        has a processor will have an entry in
        this table.

        A processor module or line card having
        multiple processors and is a SMP(Symmetric
        multi processor) system will have only 
        one entry corresponding to all the processors 
        since the resources defined below are shared.

        A processor module or line card having
        multiple processors and is not an SMP system
        would register the processors as separate entities.

        Entries are created by the agent at the system power-up
        or module insertion.

        Entries are removed when the module is reset or removed."
    INDEX           { entPhysicalIndex } 
    ::= { ceExtPhysicalProcessorTable 1 }

CeExtPhysicalProcessorEntry ::= SEQUENCE {
        ceExtProcessorRam               Unsigned32,
        ceExtNVRAMSize                  Unsigned32,
        ceExtNVRAMUsed                  Unsigned32,
        ceExtProcessorRamOverflow       Unsigned32,
        ceExtHCProcessorRam             Unsigned64,
        ceExtNVRAMSizeOverflow          Unsigned32,
        ceExtHCNVRAMSize                Unsigned64,
        ceExtNVRAMUsedOverflow          Unsigned32,
        ceExtHCNVRAMUsed                Unsigned64
}

ceExtProcessorRam OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of bytes of RAM available on the
        Processor." 
    ::= { ceExtPhysicalProcessorEntry 1 }

ceExtNVRAMSize OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of bytes of NVRAM in the entity.

        A value of 0 for this object means the entity
        does not support NVRAM or NVRAM information 
        is not available." 
    ::= { ceExtPhysicalProcessorEntry 2 }

ceExtNVRAMUsed OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of bytes of NVRAM in use. This object
        is irrelevant if ceExtNVRAMSize is 0." 
    ::= { ceExtPhysicalProcessorEntry 3 }

ceExtProcessorRamOverflow OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the upper 32-bit of ceExtProcessorRam.
        This object needs to be supported only if the available RAM
        bytes exceeds 32-bit, otherwise this object value would be set
        to 0." 
    ::= { ceExtPhysicalProcessorEntry 4 }

ceExtHCProcessorRam OBJECT-TYPE
    SYNTAX          Unsigned64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of bytes of RAM
        available on the Processor. This object is a 64-bit version
        of ceExtProcessorRam." 
    ::= { ceExtPhysicalProcessorEntry 5 }

ceExtNVRAMSizeOverflow OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the upper 32-bit of ceExtNVRAMSize.
        This object needs to be supported only if the available NVRAM
        bytes exceeds 32-bit, otherwise this object value would be set
        to 0." 
    ::= { ceExtPhysicalProcessorEntry 6 }

ceExtHCNVRAMSize OBJECT-TYPE
    SYNTAX          Unsigned64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of bytes of NVRAM
        available on the Processor. This object is a 64-bit version
        of ceExtNVRAMSize."
    ::= { ceExtPhysicalProcessorEntry 7 }
 
ceExtNVRAMUsedOverflow OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the upper 32-bit of ceExtNVRAMUsed.
        This object needs to be supported only if the used NVRAM
        bytes exceeds 32-bit, otherwise this object value would be set
        to 0." 
    ::= { ceExtPhysicalProcessorEntry 8 }

ceExtHCNVRAMUsed OBJECT-TYPE
    SYNTAX          Unsigned64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the number of bytes of NVRAM used
        on the Processor. This object is a 64-bit version of
        ceExtHCNVRAMUsed." 
    ::= { ceExtPhysicalProcessorEntry 9 }

ceExtConfigRegTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CeExtConfigRegEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The ceExtConfigRegTable extends
        the ENTITY-MIB entPhysicalTable."
    REFERENCE       "RFC2737: Section 2.12.1"
    ::= { ciscoEntityExtMIBObjects 2 }

ceExtConfigRegEntry OBJECT-TYPE
    SYNTAX          CeExtConfigRegEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A ceExtConfigRegTable entry extends
        a corresponding entPhysicalTable entry of class
        module which has a configuration register.

        Entries are created by the agent at the system power-up
        or module insertion.

        Entries are removed when the module is reset or 
        removed."
    INDEX           { entPhysicalIndex } 
    ::= { ceExtConfigRegTable 1 }

CeExtConfigRegEntry ::= SEQUENCE {
        ceExtConfigRegister     ConfigRegisterValue,
        ceExtConfigRegNext      ConfigRegisterValue,
        ceExtSysBootImageList   BootImageList,
        ceExtKickstartImageList BootImageList
}

ceExtConfigRegister OBJECT-TYPE
    SYNTAX          ConfigRegisterValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of configuration register with which
        the processor module booted." 
    ::= { ceExtConfigRegEntry 1 }

ceExtConfigRegNext OBJECT-TYPE
    SYNTAX          ConfigRegisterValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The value of configuration register in the
        processor module at next reboot. Just after 
        the reboot this has the same value as 
        ceExtConfigRegister." 
    ::= { ceExtConfigRegEntry 2 }

ceExtSysBootImageList OBJECT-TYPE
    SYNTAX          BootImageList
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The list of system boot images which
        can be used for booting."
    DEFVAL          { "" } 
    ::= { ceExtConfigRegEntry 3 }

ceExtKickstartImageList OBJECT-TYPE
    SYNTAX          BootImageList
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The list of system kickstart images which
        can be used for booting."
    DEFVAL          { "" } 
    ::= { ceExtConfigRegEntry 4 }
 

-- ceExtEntityLEDTable

ceExtEntityLEDTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CeExtEntityLEDEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing information of LED on an entity."
    ::= { ciscoEntityExtMIBObjects 3 }

ceExtEntityLEDEntry OBJECT-TYPE
    SYNTAX          CeExtEntityLEDEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the ceExtEntityLEDTable,
        containing information about an LED on an entity, identified by 
        entPhysicalIndex."
    INDEX           {
                        entPhysicalIndex,
                        ceExtEntityLEDType
                    } 
    ::= { ceExtEntityLEDTable 1 }

CeExtEntityLEDEntry ::= SEQUENCE {
        ceExtEntityLEDType  INTEGER,
        ceExtEntityLEDColor INTEGER
}

ceExtEntityLEDType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        status(1),
                        system(2),
                        active(3),
                        power(4),
                        battery(5)
                    }
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The type of LED on this entity.
        'status' - indicates the entity status.
        'system' - indicates the overall system status. 
        'active' - the redundancy status of a module, for e.g.
                   supervisor module. 
        'power'  - indicates sufficient power availability for all 
                   modules.
        'battery'- indicates the battery status."
    REFERENCE
        "Cisco MDS 9500 Series Hardware Installation Guide,
        Product Overview." 
    ::= { ceExtEntityLEDEntry 1 }

ceExtEntityLEDColor OBJECT-TYPE
    SYNTAX          INTEGER  {
                        off(1),
                        green(2),
                        amber(3),
                        red(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The color of the LED."
    REFERENCE
        "Cisco MDS 9500 Series Multilayer Switches, Product
        Overview." 
    ::= { ceExtEntityLEDEntry 2 }
 

-- ceExtEntityPhysicalTable

ceExtEntPhysicalTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CeExtEntPhysicalEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains additional information about
        a particular physical entity.
        This table augments the 'entPhysicalTable'  of
        the ENTITY-MIB."
    REFERENCE       "RFC2737: Section 2.12.1"
    ::= { ciscoEntityExtMIBObjects 4 }

ceExtEntPhysicalEntry OBJECT-TYPE
    SYNTAX          CeExtEntPhysicalEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the 'ceExtEntPhysicalTable',
        containing additional information about a physical entity, 
        identified by 'entPhysicalIndex'."
    AUGMENTS           { entPhysicalEntry  } 
    ::= { ceExtEntPhysicalTable 1 }

CeExtEntPhysicalEntry ::= SEQUENCE {
        ceEntPhysicalSecondSerialNum SnmpAdminString
}

ceEntPhysicalSecondSerialNum OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..32))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object represents the vendor-specific second
        serial number string for the physical entity. 
        The first serial number string of the physical
        entity is represented in the value of corresponding 
        instance of the 'entPhysicalSerialNum' object.

        On the first instantiation of an physical entity, the value
        of this object is the correct vendor-assigned second 
        serial number, if this information is available to the agent. 

        If the second serial number is unknown or non-existent, then 
        the value of this object will be a zero-length string instead.

        Note that implementations which can correctly identify the
        second serial numbers of all installed physical entities do 
        not need to provide write access to this object. 
        Agents which cannot provide non-volatile storage for the 
        second serial number strings are not required to implement 
        write access for this object.

        Not every physical component will have a serial number, or
        even need one.  Physical entities for which the associated
        value of the entPhysicalIsFRU object is equal to 'false(2)'
        (e.g., the repeater ports within a repeater module), do not
        need their own unique serial number. An agent does not have
        to provide write access for such entities, and may return a
        zero-length string.

        If write access is implemented for an instance of
        'ceEntPhysicalSecondSerialNum', and a value is written into 
        the instance, the agent must retain the supplied value in the
        'ceEntPhysicalSecondSerialNum' instance associated with the 
        same physical entity for as long as that entity remains
        instantiated. This includes instantiations across all re-
        initializations/reboots of the network management system,
        including those which result in a change of the physical
        entity's entPhysicalIndex value."
    DEFVAL          { "" } 
    ::= { ceExtEntPhysicalEntry 1 }
 

ceExtNotificationControlObjects  OBJECT IDENTIFIER
    ::= { ciscoEntityExtMIBObjects 5 }


ceExtEntDoorNotifEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object controls the generation of
        ceExtEntDoorCloseNotif and ceExtEntDoorOpenNotif 
        notifications as follows: 
        'true(1)' -  the generation of ceExtEntDoorCloseNotif
                     and ceExtEntDoorOpenNotif  
                     notifications are enabled. 
        'false(2)' - the generation of ceExtEntDoorCloseNotif
                     and ceExtEntDoorOpenNotif  
                     notifications are disabled."
    DEFVAL          { false } 
    ::= { ceExtNotificationControlObjects 1 }

ceExtEntBreakOutPortNotifEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object controls the generation of
        ceExtBreakOutPortInserted and
         ceExtBreakOutPortRemoved as follows:
        'true(1)'  - the generation of ceExtBreakOutPortInserted
                           and ceExtBreakOutPortRemoved
                           notifications is enabled.
        'false(2)' - the generation of ceExtBreakOutPortInserted
                           and ceExtBreakOutPortRemoved
                           notifications is disabled."
    DEFVAL          { false } 
    ::= { ceExtNotificationControlObjects 2 }

ceExtEntUsbModemNotifEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object controls the generation of
        ceExtUSBModemPlugInNotif and
         ceExtUSBModemPlugOutNotif as follows:
        'true(1)'  - the generation of ceExtUSBModemPlugInNotif
                           and ceExtUSBModemPlugOutNotif
                           notifications is enabled.
        'false(2)' - the generation of ceExtUSBModemPlugOutNotif
                           and ceExtUSBModemPlugOutNotif
                           notifications is disabled."
    DEFVAL          { false } 
    ::= { ceExtNotificationControlObjects 3 }
-- ceExtUSBModemTable

ceExtUSBModemTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CeExtUSBModemEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing information of USB MODEMs on an entity."
    ::= { ciscoEntityExtMIBObjects 6 }

ceExtUSBModemEntry OBJECT-TYPE
    SYNTAX          CeExtUSBModemEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the ceExtUSBModemTable,
        containing information about an USB MODEM on an entity, 
        identified by entPhysicalIndex."
    INDEX           { entPhysicalIndex } 
    ::= { ceExtUSBModemTable 1 }

CeExtUSBModemEntry ::= SEQUENCE {
        ceExtUSBModemIMEI            SnmpAdminString,
        ceExtUSBModemIMSI            SnmpAdminString,
        ceExtUSBModemServiceProvider SnmpAdminString,
        ceExtUSBModemSignalStrength  SnmpAdminString
}

ceExtUSBModemIMEI OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The International Mobile Equipment Identifier (IMEI) of the
        USB-MODEM"
    REFERENCE       "RFC 6155" 
    ::= { ceExtUSBModemEntry 1 }

ceExtUSBModemIMSI OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The International Mobile Subscriber Identifier(IMSI) -
        for a GSM USB MODEM."
    REFERENCE       "RFC 4186" 
    ::= { ceExtUSBModemEntry 2 }

ceExtUSBModemServiceProvider OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Service Provider name for a USB MODEM." 
    ::= { ceExtUSBModemEntry 3 }

ceExtUSBModemSignalStrength OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The signal strength of the USB MODEM." 
    ::= { ceExtUSBModemEntry 4 }
 

-- Notifications

ceExtMIBNotificationPrefix  OBJECT IDENTIFIER
    ::= { ciscoEntityExtMIB 2 }

ciscoEntityExtMIBNotifications  OBJECT IDENTIFIER
    ::= { ceExtMIBNotificationPrefix 0 }


ceExtEntDoorCloseNotif NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalDescr,
                        entPhysicalName
                    }
    STATUS          current
    DESCRIPTION
        "A ceExtEntDoorCloseNotif is generated if the door of an entity
        has been closed."
   ::= { ciscoEntityExtMIBNotifications 1 }

ceExtEntDoorOpenNotif NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalDescr,
                        entPhysicalName
                    }
    STATUS          current
    DESCRIPTION
        "A ceExtEntDoorOpenNotif is generated if the door of an entity
        has been opened."
   ::= { ciscoEntityExtMIBNotifications 2 }

ceExtBreakOutPortInserted NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalContainedIn,
                        entPhysicalName
                    }
    STATUS          current
    DESCRIPTION
        "The ceExtBreakOutPortInserted notification indicates
        that a Breakout port was inserted.  The varbind for
        this notification indicates the entPhysicalIndex of
        the inserted Breakout port, and the entPhysicalIndex
        of the BreakOut Port module that contains this port."
   ::= { ciscoEntityExtMIBNotifications 3 }

ceExtBreakOutPortRemoved NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalContainedIn,
                        entPhysicalName
                    }
    STATUS          current
    DESCRIPTION
        "The ceExtBreakOutPortRemoved  notification indicates
        that a Breakout Port was removed.  The varbind for this
        notification indicates the entPhysicalIndex of the
        removed Breakout port, and the entPhysicalIndex
        of the BreakOut Port module that contains this port."
   ::= { ciscoEntityExtMIBNotifications 4 }

ceExtUSBModemPlugInNotif NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalContainedIn,
                        entPhysicalDescr
                    }
    STATUS          current
    DESCRIPTION
        "The ceExtUSBModemPlugInNotif notification indicates that
        a USB MODEM was inserted. The varbind for this notification
        indicates the entPhysicalDescr of the inserted USB MODEM, 
        and the entPhysicalIndex of the USB MODEM's container."
   ::= { ciscoEntityExtMIBNotifications 5 }

ceExtUSBModemPlugOutNotif NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalContainedIn,
                        entPhysicalDescr
                    }
    STATUS          current
    DESCRIPTION
        "The ceExtUSBModemPlugOutNotif notification indicates that
        a USB MODEM was removed. The varbind for this notification
        indicates the entPhysicalDescr of the removed USB MODEM,
        and the entPhysicalIndex of the USB MODEM's container."
   ::= { ciscoEntityExtMIBNotifications 6 }
-- conformance information

ciscoEntityExtMIBConformance  OBJECT IDENTIFIER
    ::= { ciscoEntityExtMIB 3 }

ciscoEntityExtMIBCompliances  OBJECT IDENTIFIER
    ::= { ciscoEntityExtMIBConformance 1 }

ciscoEntityExtMIBGroups  OBJECT IDENTIFIER
    ::= { ciscoEntityExtMIBConformance 2 }


-- compliance statements

ciscoEntityExtMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "Compliance for SNMP Entities which support modules
        with CPU, NVRAM and configuration register."
    MODULE          -- this module
    MANDATORY-GROUPS { ceExtPhysicalProcessorGroup }

    GROUP           ciscoEntityExtConfigRegGroup
    DESCRIPTION
        "The ciscoEntityExtConfigRegGroup is an optional group
        containing objects providing information about
        configuration register."

    GROUP           ceExtSysBootImageListGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides information about the system boot images."
    ::= { ciscoEntityExtMIBCompliances 1 }

ciscoEntityExtMIBComplianceRev1 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "Compliance for SNMP Entities which support modules
        with CPU, NVRAM and configuration register."
    MODULE          -- this module
    MANDATORY-GROUPS { ceExtPhysicalProcessorGroup }

    GROUP           ciscoEntityExtConfigRegGroup
    DESCRIPTION
        "The ciscoEntityExtConfigRegGroup is an optional group
        containing objects providing information about
        configuration register."

    GROUP           ceExtSysBootImageListGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides information about the system boot images."

    GROUP           ciscoEntityExtLEDGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides LED information."

    GROUP           ceExtSysBootImageListGroupRev1
    DESCRIPTION
        "This group is an optional group containing an object
        which provides information about the system boot images."
    ::= { ciscoEntityExtMIBCompliances 2 }

ciscoEntityExtMIBComplianceRev2 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "Compliance for SNMP Entities which support modules
        with CPU, NVRAM and configuration register."
    MODULE          -- this module
    MANDATORY-GROUPS { ceExtPhysicalProcessorGroup }

    GROUP           ciscoEntityExtConfigRegGroup
    DESCRIPTION
        "The ciscoEntityExtConfigRegGroup is an optional group
        containing objects providing information about
        configuration register."

    GROUP           ceExtSysBootImageListGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides information about the system boot images."

    GROUP           ciscoEntityExtLEDGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides LED information."

    GROUP           ceExtSysBootImageListGroupRev1
    DESCRIPTION
        "This group is an optional group containing an object
        which provides information about the system boot images."

    GROUP           ciscoExtEntityPhysicalGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides additional information about the 
        physical entity."
    ::= { ciscoEntityExtMIBCompliances 3 }

ciscoEntityExtMIBComplianceRev3 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "Compliance for SNMP Entities which support modules
        with CPU, NVRAM and configuration register."
    MODULE          -- this module
    MANDATORY-GROUPS { ceExtPhysicalProcessorGroup }

    GROUP           ciscoEntityExtConfigRegGroup
    DESCRIPTION
        "The ciscoEntityExtConfigRegGroup is an optional group
        containing objects providing information about
        configuration register."

    GROUP           ceExtSysBootImageListGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides information about the system boot images."

    GROUP           ciscoEntityExtLEDGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides LED information."

    GROUP           ceExtSysBootImageListGroupRev1
    DESCRIPTION
        "This group is an optional group containing an object
        which provides information about the system boot images."

    GROUP           ciscoExtEntityPhysicalGroup
    DESCRIPTION
        "This group is an optional group containing an object
        which provides additional information about the 
        physical entity."

    GROUP           ceExtPhyProcessorOverflowGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."

    GROUP           ceExtPhyProcessorHCGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."
    ::= { ciscoEntityExtMIBCompliances 4 }

ciscoEntityExtMIBComplianceRev4 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "Compliance for SNMP Entities which support modules
        with CPU, NVRAM and configuration register."
    MODULE          -- this module
    MANDATORY-GROUPS { ceExtPhysicalProcessorGroup }

    GROUP           ciscoEntityExtConfigRegGroup
    DESCRIPTION
        "The ciscoEntityExtConfigRegGroup is an
        optional group containing objects providing 
        information about configuration register."

    GROUP           ceExtSysBootImageListGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides information about the system boot images."

    GROUP           ciscoEntityExtLEDGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides LED information."

    GROUP           ceExtSysBootImageListGroupRev1
    DESCRIPTION
        "This group is an optional group containing an object which
        provides information about the system boot images."

    GROUP           ciscoExtEntityPhysicalGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides additional information about the physical entity."

    GROUP           ceExtPhyProcessorOverflowGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."

    GROUP           ceExtPhyProcessorHCGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."

    GROUP           ceExtEntDoorNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support entity door
        open/close notifications."

    GROUP           ceExtEntDoorNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which support entity door
        open/close notifications."

    OBJECT          ceExtConfigRegNext
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          ceExtSysBootImageList
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          ceExtKickstartImageList
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          ceEntPhysicalSecondSerialNum
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          ceExtEntDoorNotifEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoEntityExtMIBCompliances 5 }

ciscoEntityExtMIBComplianceRev5 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "Compliance for SNMP Entities which support modules
        with CPU, NVRAM and configuration register."
    MODULE          -- this module
    MANDATORY-GROUPS { ceExtPhysicalProcessorGroup }

    GROUP           ciscoEntityExtConfigRegGroup
    DESCRIPTION
        "The ciscoEntityExtConfigRegGroup is an
        optional group containing objects providing
        information about configuration register."

    GROUP           ceExtSysBootImageListGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides information about the system boot images."

    GROUP           ciscoEntityExtLEDGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides LED information."

    GROUP           ceExtSysBootImageListGroupRev1
    DESCRIPTION
        "This group is an optional group containing an object which
        provides information about the system boot images."

    GROUP           ciscoExtEntityPhysicalGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides additional information about the physical entity."

    GROUP           ceExtPhyProcessorOverflowGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."

    GROUP           ceExtPhyProcessorHCGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."

    GROUP           ceExtEntDoorNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support entity door
        open/close notifications."

    GROUP           ceExtEntDoorNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which support entity door
        open/close notifications."

    GROUP           ceExtBreakOutPortNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support Breakout
        port notifications."

    GROUP           ceExtBreakOutPortNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which support Breakout
        port  notifications."

    OBJECT          ceExtEntBreakOutPortNotifEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoEntityExtMIBCompliances 6 }

ciscoEntityExtMIBComplianceRev6 MODULE-COMPLIANCE
    STATUS          current 
    DESCRIPTION
        "Compliance for SNMP Entities which support modules
        with CPU, NVRAM and configuration register."
    MODULE          -- this module
    MANDATORY-GROUPS { ceExtPhysicalProcessorGroup }

    GROUP           ciscoEntityExtConfigRegGroup
    DESCRIPTION
        "The ciscoEntityExtConfigRegGroup is an
        optional group containing objects providing
        information about configuration register."

    GROUP           ceExtSysBootImageListGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides information about the system boot images."

    GROUP           ciscoEntityExtLEDGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides LED information."

    GROUP           ceExtSysBootImageListGroupRev1
    DESCRIPTION
        "This group is an optional group containing an object which
        provides information about the system boot images."

    GROUP           ciscoExtEntityPhysicalGroup
    DESCRIPTION
        "This group is an optional group containing an object which
        provides additional information about the physical entity."

    GROUP           ceExtPhyProcessorOverflowGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."

    GROUP           ceExtPhyProcessorHCGroup
    DESCRIPTION
        "This group is an optional group for the physical entities which
        support 32-bit operating system."

    GROUP           ceExtEntDoorNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support entity door
        open/close notifications."

    GROUP           ceExtEntDoorNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which support entity door
        open/close notifications."

    GROUP           ceExtBreakOutPortNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support Breakout
        port notifications."

    GROUP           ceExtBreakOutPortNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which support Breakout
        port  notifications."

    GROUP           ceExtUSBModemGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        USB Modem management."

    GROUP           ceExtUsbModemNotificationsGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        USB Modem management."

    GROUP           ceExtUsbModemNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        USB Modem management."

    OBJECT          ceExtEntBreakOutPortNotifEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          ceExtEntUsbModemNotifEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoEntityExtMIBCompliances 7 }

-- units of conformance

ceExtPhysicalProcessorGroup OBJECT-GROUP
    OBJECTS         {
                        ceExtProcessorRam,
                        ceExtNVRAMSize,
                        ceExtNVRAMUsed
                    }
    STATUS          current
    DESCRIPTION
        "The collection of objects which give information about
        the Processor RAM and NVRAM."
    ::= { ciscoEntityExtMIBGroups 1 }

ciscoEntityExtConfigRegGroup OBJECT-GROUP
    OBJECTS         {
                        ceExtConfigRegister,
                        ceExtConfigRegNext
                    }
    STATUS          current
    DESCRIPTION
        "The collection of objects which give information about
        configuration register.

        Implementation of this group is optional."
    ::= { ciscoEntityExtMIBGroups 2 }

ceExtSysBootImageListGroup OBJECT-GROUP
    OBJECTS         { ceExtSysBootImageList }
    STATUS          current
    DESCRIPTION
        "A group containing an object providing information
        about the system boot images.

        Implementation of this group is optional."
    ::= { ciscoEntityExtMIBGroups 3 }

ciscoEntityExtLEDGroup OBJECT-GROUP
    OBJECTS         { ceExtEntityLEDColor }
    STATUS          current
    DESCRIPTION
        "A collection of objects for giving led information."
    ::= { ciscoEntityExtMIBGroups 4 }

ceExtSysBootImageListGroupRev1 OBJECT-GROUP
    OBJECTS         { ceExtKickstartImageList }
    STATUS          current
    DESCRIPTION
        "A group containing an object providing information
        about the system boot images.

        Implementation of this group is optional."
    ::= { ciscoEntityExtMIBGroups 5 }

ciscoExtEntityPhysicalGroup OBJECT-GROUP
    OBJECTS         { ceEntPhysicalSecondSerialNum }
    STATUS          current
    DESCRIPTION
        "The collection of objects for providing additional
        information about the physical entity.

        Implementation of this group is optional."
    ::= { ciscoEntityExtMIBGroups 6 }

ceExtPhyProcessorOverflowGroup OBJECT-GROUP
    OBJECTS         { ceExtProcessorRamOverflow }
    STATUS          current
    DESCRIPTION
        "The collection of Overflow (upper 32-bit) objects which
        provides information about physical processor, when the entity
        runs on 64-bit Operating System (OS). This group is optional for
        the entities which run on 32-bit OS."
    ::= { ciscoEntityExtMIBGroups 7 }

ceExtPhyProcessorHCGroup OBJECT-GROUP
    OBJECTS         { ceExtHCProcessorRam }
    STATUS          current
    DESCRIPTION
        "The collection of High Capacity (HC) objects which provides
        information about physical processor, when the entity runs on
        64-bit Operating System (OS). This group is optional for the
        entities which run on 32-bit OS."
    ::= { ciscoEntityExtMIBGroups 8 }

ceExtEntDoorNotifGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        ceExtEntDoorCloseNotif,
                        ceExtEntDoorOpenNotif
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the notification
        information for door entitites."
    ::= { ciscoEntityExtMIBGroups 9 }

ceExtEntDoorNotifControlGroup OBJECT-GROUP
    OBJECTS         { ceExtEntDoorNotifEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing control for entity door
        notifications."
    ::= { ciscoEntityExtMIBGroups 10 }

ceExtBreakOutPortNotifGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        ceExtBreakOutPortInserted,
                        ceExtBreakOutPortRemoved
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the notification
        information for supporting Breakout port feature."
    ::= { ciscoEntityExtMIBGroups 11 }

ceExtBreakOutPortNotifControlGroup OBJECT-GROUP
    OBJECTS         { ceExtEntBreakOutPortNotifEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing control for Breakout
        Port notifications."
    ::= { ciscoEntityExtMIBGroups 12 }

ceExtUSBModemGroup OBJECT-GROUP
    OBJECTS         {
                        ceExtUSBModemIMEI,
                        ceExtUSBModemIMSI,
                        ceExtUSBModemServiceProvider,
                        ceExtUSBModemSignalStrength
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for giving USB MODEM
        config information."
    ::= { ciscoEntityExtMIBGroups 13 }

ceExtUsbModemNotificationsGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        ceExtUSBModemPlugInNotif,
                        ceExtUSBModemPlugOutNotif
                    }
    STATUS          current
    DESCRIPTION
        "A collection of notifications for USB MODEM removal and
        insertion."
    ::= { ciscoEntityExtMIBGroups 14 }

ceExtUsbModemNotifControlGroup OBJECT-GROUP
    OBJECTS         { ceExtEntUsbModemNotifEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing control for USB
        MODEM notifications."
    ::= { ciscoEntityExtMIBGroups 15 }

ceExtNVRAMOverflowGroup OBJECT-GROUP
    OBJECTS         { 
                        ceExtNVRAMSizeOverflow,
                        ceExtNVRAMUsedOverflow
                    }
    STATUS          current
    DESCRIPTION
        "The collection of objects providing Overflow (upper 32-bit) objects 
        for processor NVRAM information."
    ::= { ciscoEntityExtMIBGroups 16 }

ceExtHCNVRAMGroup OBJECT-GROUP
    OBJECTS         { 
                        ceExtHCNVRAMSize, 
                        ceExtHCNVRAMUsed
                    }
    STATUS          current
    DESCRIPTION
        "The collection of objects providing High Capacity (HC) objects 
        for processor NVRAM information."
    ::= { ciscoEntityExtMIBGroups 17 }

END
