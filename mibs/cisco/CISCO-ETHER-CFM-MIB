--------------------------------------------------------------------
-- CISCO-ETHER-CFM-MIB.my : Cisco Ethernet Connectivity Fault
--                          Management (CFM) MIB 
--
-- December 2004, <PERSON><PERSON>
--
-- Copyright (c) 2004-2006 by Cisco Systems, Inc.
-- All rights reserved.
-------------------------------------------------------------------

CISCO-ETHER-CFM-MIB DEFINITIONS ::= BEGIN

IMPORTS
    Gauge32,
    Unsigned32,
    MODULE-IDENTITY,
    NOTIFICATION-TYPE,
    OBJECT-TYPE
        FROM SNMPv2-SMI

    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB

    NOTIFICATION-GROUP,
    OBJECT-GROUP,
    MODULE-COMPLIANCE
        FROM SNMPv2-CONF

    VlanId 
        FROM Q-BRIDGE-MIB

    TEXTUAL-CONVENTION,
    MacA<PERSON>ress,
    TimeStamp
        FROM SNMPv2-TC

    ciscoMgmt
        FROM CISCO-SMI;

ciscoEtherCfmMIB MODULE-IDENTITY
    LAST-UPDATED    "200412280000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO    
        "            Cisco Systems
                     Customer Service
             Postal: 170 W Tasman Drive
                     San Jose, CA 95134
                     USA
                Tel: ****** 553-NETS
             E-mail:  <EMAIL>"
    DESCRIPTION
        "This MIB module defines the managed objects 
        and notifications for Ethernet Connectivity 
        Fault Management (CFM).

        CFM is an end-to-end per service instance Ethernet layer 
        Operations, Administration and Management (OAM) protocol. 

        CFM events include: 

        - Maintenance End-Point (MEP) coming up: establishing 
          connectivity

        - Maintenance End-Point going down: losing connectivity

        - Maintenance End-Point unknown: unexpected

        - Maintenance End-Point missing: expected but not reachable

        - Continuity Check Configuration Error: collision in MEP IDs

        - Continuity Check Loop: forwarding loop in network

        - Continuity Check Cross-connect: cross-connected 
          forwarding path.

        The following acronyms are used in this module:

        - MEP: Maintenance End Point

        - MEPID: Maintenance End Point Identifier

        - CC: Continuity Check

        - CCDB: Continuity Check Database

        - SVLAN: Service Provider Virtual Local Area Network

        - VLAN: Virtual Local Area Network
   
        - CLI: Command Line Interface.

        - OAM: Operations Administration and Management."
    REVISION   "200412280000Z"
    DESCRIPTION
        "The initial revision of this MIB."
    ::={ ciscoMgmt 461 } 

ciscoEtherCfmMIBNotifs  OBJECT IDENTIFIER ::= { ciscoEtherCfmMIB 0 }
ciscoEtherCfmMIBObjects OBJECT IDENTIFIER ::= { ciscoEtherCfmMIB 1 }
ciscoEtherCfmMIBConform OBJECT IDENTIFIER ::= { ciscoEtherCfmMIB 2 }

cecCfmEvents OBJECT IDENTIFIER ::= { ciscoEtherCfmMIBObjects 1 }

-- Textual Conventions

CfmMepid ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION
        "The identifier of a maintenance end point."
    SYNTAX        Unsigned32 (0..8191)

-- MIB Object Definitions

cEtherCfmMaxEventIndex OBJECT-TYPE
    SYNTAX        Unsigned32 (1..**********)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This object specifies the maximum upper value supported 
        for the cEtherCfmEventIndex index by this agent."
    ::= { cecCfmEvents 1 }

cEtherCfmEventTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF CEtherCfmEventEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table contains a collection of Ethernet CFM notifications
        generated by the device. The notifications correspond to events
        recognized by the device and fall into the following classes:

        - MEP-Up

        - MEP-Down

        - Configuration Error

        - Forwarding Loop

        - Cross-connected Ethernet Connection

        - Crosscheck Missing MEP

        - Crosscheck Unknown MEP

        - Crosscheck Service Up

        A conceptual row is created in this table whenever the device 
        encounters one of the events listed above. Rows can only be
        created by the agent, and not at the request of the management
        station.

        Rows are deleted at the request of a management station by 
        setting the cEtherCfmEventDeleteRow object to 'delete'.
        Another way of deleting rows is through the CLI.

        Although this table may be indexed uniquely by the 
        cEtherCfmEventIndex index, the first two indices 
        (cEtherCfmEventDomainIndex and cEtherCfmEventSvlan) are used
        to speed-up queries per maintenance domain and per customer
        service instance. Furthermore, these two indices will help
        in defining the MIB views easily in order to restrict access
        to the MIB to particular entities (be it a service provider,
        or operator, or customer)."
    ::= { cecCfmEvents 2 }

cEtherCfmEventEntry OBJECT-TYPE
    SYNTAX        CEtherCfmEventEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "An entry in this table is created for every event reported
        by Ethernet CFM."
    INDEX    { cEtherCfmEventDomainIndex, 
               cEtherCfmEventSvlan, 
               cEtherCfmEventIndex }
    ::= { cEtherCfmEventTable 1 }

CEtherCfmEventEntry ::= SEQUENCE {
        cEtherCfmEventDomainIndex      Unsigned32,
        cEtherCfmEventSvlan            VlanId,
        cEtherCfmEventIndex            Unsigned32,
        cEtherCfmEventDomainName       SnmpAdminString,
        cEtherCfmEventType             INTEGER,
        cEtherCfmEventLastChange       TimeStamp,
        cEtherCfmEventServiceId        SnmpAdminString,
        cEtherCfmEventLclMepid         CfmMepid,
        cEtherCfmEventLclMacAddress    MacAddress,
        cEtherCfmEventLclMepCount      Gauge32,
        cEtherCfmEventLclIfCount       Gauge32,
        cEtherCfmEventRmtMepid         CfmMepid,
        cEtherCfmEventRmtMacAddress    MacAddress,
        cEtherCfmEventRmtPortState     INTEGER,
        cEtherCfmEventRmtServiceId     SnmpAdminString,
        cEtherCfmEventCode             INTEGER,
        cEtherCfmEventDeleteRow        INTEGER
    }

cEtherCfmEventDomainIndex OBJECT-TYPE
    SYNTAX        Unsigned32 (1..**********)
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This object represents the ID which uniquely identifies 
        a CFM maintenance domain on the device. Every domain can
        be uniquely identified by its user-defined 
        name (cEtherCfmEventDomainName) or device-assigned ID (this
        object)."

    ::= { cEtherCfmEventEntry 1 }

cEtherCfmEventSvlan OBJECT-TYPE
    SYNTAX        VlanId
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "The service VLAN identifier of the customer service 
        instance to which the event belongs."
    ::= { cEtherCfmEventEntry 2 }

cEtherCfmEventIndex OBJECT-TYPE
    SYNTAX        Unsigned32 (1..**********)
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "A monotonically increasing integer for the sole purpose of
        indexing CFM events.  When it reaches the maximum value 
        supported by the agent, as defined in the 
        cEtherCfmMaxEventIndex object, the agent wraps the value
        back to 1 and may flush existing entries."
    ::= { cEtherCfmEventEntry 3 }

cEtherCfmEventDomainName OBJECT-TYPE
    SYNTAX        SnmpAdminString
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The name of the CFM maintenance domain."
    ::= { cEtherCfmEventEntry 4 }

cEtherCfmEventType OBJECT-TYPE
    SYNTAX        INTEGER {
                      mepUp(1),
                      mepDown(2),
                      xconnect(3),
                      loop(4),
                      config(5),
                      xcheckMissing(6),
                      xcheckUnknown(7),
                      xcheckServiceUp(8)
                  }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This object informs the management station of how to interpret
        the rest of the objects within a row, as summarized in the 
        following table:

        Legend I: Ignored Object 
               V: Valid Object

        Object                                 cEtherCfmEventType
                                       | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8
        ================================================================
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventDomainIndex      | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventSvlan            | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventIndex            | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventLastChange       | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventServiceId        | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventDomainName       | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventLclMepid         | I | I | I | V | V | I | I | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventLclMacAddress    | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventLclMepCount      | V | V | I | I | I | I | I | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventLclIfCount       | V | V | I | I | I | I | I | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventRmtMepid         | V | V | V | I | I | V | V | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventRmtMacAddress    | V | V | V | I | V | V | V | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventRmtPortState     | V | I | I | I | I | I | I | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventRmtServiceId     | I | I | V | I | I | I | I | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventCode             | V | V | I | I | I | I | I | I
                                       |   |   |   |   |   |   |   |
        cEtherCfmEventDeleteRow        | V | V | V | V | V | V | V | V
                                       |   |   |   |   |   |   |   |
 
        Note: When reading any ignored object, a value of 0 will 
        be returned by the agent."
    ::= { cEtherCfmEventEntry 5 }

cEtherCfmEventLastChange OBJECT-TYPE
    SYNTAX        TimeStamp
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The value of sysUpTime at the time when this row was created."
    ::= { cEtherCfmEventEntry 6 }

cEtherCfmEventServiceId OBJECT-TYPE
    SYNTAX        SnmpAdminString (SIZE(1..100))
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The customer service instance to which the event belongs."
    ::= { cEtherCfmEventEntry 7 }

cEtherCfmEventLclMepid OBJECT-TYPE
    SYNTAX        CfmMepid
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The identifier of the local MEP impacted by the event."
    ::= { cEtherCfmEventEntry 8 }

cEtherCfmEventLclMacAddress OBJECT-TYPE
    SYNTAX        MacAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The MAC address of the device reporting the event." 
    ::= { cEtherCfmEventEntry 9 }

cEtherCfmEventLclMepCount OBJECT-TYPE
    SYNTAX        Gauge32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The number of local MEPs affected by the event."
    ::= { cEtherCfmEventEntry 10 }

cEtherCfmEventLclIfCount OBJECT-TYPE
    SYNTAX        Gauge32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The number of local interfaces affected by the event."
    ::= { cEtherCfmEventEntry 11 }

cEtherCfmEventRmtMepid OBJECT-TYPE
    SYNTAX        CfmMepid
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The maintenance end-point identifier of the remote 
        MEP causing the event entry to be logged."
    ::= { cEtherCfmEventEntry 12 }

cEtherCfmEventRmtMacAddress OBJECT-TYPE
    SYNTAX        MacAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The MAC address of the remote maintenance point for which
        the event entry is being logged."
    ::= { cEtherCfmEventEntry 13 }

cEtherCfmEventRmtPortState OBJECT-TYPE
    SYNTAX        INTEGER { 
                      up(1),
                      down(2),
                      adminDown(3),
                      test(4),
                      remoteExcessiveErrors(5),
                      localExcessiveErrors(6),
                      localNoData(7)
                  }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The operational state of the port on which the 
        remote MEP is configured. This information is 
        derived from the port-state as indicated in the 
        CC message. The possible values are:
        
        'up'                    - The port is operationally up.

        'down'                  - The port is operationally (but not
                                  administratively) down.
        
        'adminDown'             - The port is administratively down.
       
        'test'                  - The port is in test mode (perhaps 
                                  due to an IEEE Standard 802.3ah OAM
                                  intrusive loopback operation).

        'remoteExcessiveErrors' - 802.3ah OAM reports that the other 
                                  end of the link is receiving an 
                                  excessive number of invalid frames.

        'localExcessiveErrors'  - 802.3ah OAM reports that this end of
                                  the link is receiving an excessive 
                                  number of invalid frames.

        'localNoData'           - No data and no CFM messages have been
                                  received for an excessive length of 
                                  time."
    REFERENCE
        "IEEE 802.1ag Draft 1.0: Section ******** and 
        IEEE 802.3ah-2004: Clause 57."
    ::= { cEtherCfmEventEntry 14 }

cEtherCfmEventRmtServiceId OBJECT-TYPE
    SYNTAX        SnmpAdminString (SIZE(1..100))
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The ID that the remote device has configured for the 
        customer service instance (VLAN)."
    ::= { cEtherCfmEventEntry 15 }

cEtherCfmEventCode OBJECT-TYPE
    SYNTAX        INTEGER {
                      new(1),
                      returning(2),
                      portState(3),
                      lastGasp(4),
                      timeout(5),
                      configClear(6),
                      loopClear(7),
                      xconnectClear(8),
                      unknownClear(9)
                  }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This object is used in decoding 'mepUp' and 'mepDown' events. 

        ** For 'mepUp', the following codes are relevant:

            'new'           - This is the very first time the device 
                              receives a CC message from the remote MEP.

            'returning'     - The device received a CC message from a 
                              remote MEP for which it had an expired 
                              CCDB entry.

            'portState'     - The device received a CC message from a 
                              remote MEP for which it has a valid CCDB 
                              entry, and the message indicates a port 
                              status change.

        ** For 'mepDown', the following codes are relevant:

            'lastGasp'      - The device received a CC message from a
                              remote MEP with zero lifetime.

            'timeout'       - The local CCDB entry for the remote MEP 
                              expired.

            'configClear'   - A previous CC message from a MEP that
                              triggered a configuration error event
                              is cleared.
            
            'loopClear'     - A previous CC message from a MEP that
                              triggered a loop error event is cleared.

            'xconnectClear' - A previous CC message from a MEP that
                              triggered a crossconnect error event 
                              is cleared.

            'unknownClear'  - A previous CC message from a MEP that
                              triggered an unknown MEP event is 
                              cleared."
    ::= { cEtherCfmEventEntry 16 }

cEtherCfmEventDeleteRow OBJECT-TYPE
    SYNTAX        INTEGER {
                      noop(1), 
                      delete(2) 
                  } 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object allows the management station to 
        delete a row in the cEtherCfmEventTable in order
        to free system resources.
        
        When reading this object the value of 'noop' will be 
        returned. This object can only be set to 'delete'. 

        When this object is set to 'delete', the conceptual
        row corresponding to this object will be deleted to
        free system resources. This is equivalent to clearing
        the event log. Should the trigger that caused the event
        to be logged reoccur, the event will be re-asserted but
        in a different conceptual row."
    ::= { cEtherCfmEventEntry 17 }

-- Notifications

ciscoEtherCfmNotificationPrefix OBJECT IDENTIFIER ::= 
    { ciscoEtherCfmMIBNotifs 0 }

cEtherCfmCcMepUp NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId, 
        cEtherCfmEventLclMacAddress,
        cEtherCfmEventLclMepCount,
        cEtherCfmEventLclIfCount,                 
        cEtherCfmEventRmtMepid, 
        cEtherCfmEventRmtMacAddress,
        cEtherCfmEventCode,
        cEtherCfmEventRmtPortState 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated in the following cases:

        - when a remote MEP first comes up, that is when we receive 
        a CC message from that MEP for the first time.

        - when the device receives a CC message from a MEP for which 
        it has an expired CCDB entry.

        - when a CC message is received for a remote MEP for which
        the device already has a CCDB entry and the port-state in
        the received CC message is different from the cached 
        previous state."
    ::= { ciscoEtherCfmNotificationPrefix 1 }

cEtherCfmCcMepDown NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId, 
        cEtherCfmEventLclMacAddress,
        cEtherCfmEventLclMepCount,
        cEtherCfmEventLclIfCount,   
        cEtherCfmEventRmtMepid, 
        cEtherCfmEventRmtMacAddress,
        cEtherCfmEventCode 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated when a remote MEP goes down; 
        i.e. the entry in CCDB corresponding to this MEP times out 
        or the device receives a CC message with zero hold-time."
    ::= { ciscoEtherCfmNotificationPrefix 2 }

cEtherCfmCcCrossconnect NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId, 
        cEtherCfmEventLclMacAddress, 
        cEtherCfmEventRmtMepid, 
        cEtherCfmEventRmtMacAddress,
        cEtherCfmEventRmtServiceId 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated when a device receives a CC
        message with the service ID not matching the one locally 
        configured for the VLAN in question."
    ::= { ciscoEtherCfmNotificationPrefix 3 }

cEtherCfmCcLoop NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId, 
        cEtherCfmEventLclMacAddress,
        cEtherCfmEventLclMepid 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated when a device receives a CC
        message with the same MEPID and MAC address as those of
        the device itself, indicating that there is a forwarding
        loop and that the device is receiving its own CC messages."
    ::= { ciscoEtherCfmNotificationPrefix 4 }

cEtherCfmCcConfigError NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId,
        cEtherCfmEventLclMacAddress,
        cEtherCfmEventLclMepid,
        cEtherCfmEventRmtMacAddress 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated when a device receives a CC
        message with the same MEPID but different MAC address as 
        those of the device itself, indicating that there is a  
        mis-configuration in the network where a remote device
        has the same MEPID configured."
    ::= { ciscoEtherCfmNotificationPrefix 5 }

cEtherCfmXCheckMissing NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId, 
        cEtherCfmEventLclMacAddress, 
        cEtherCfmEventRmtMepid, 
        cEtherCfmEventRmtMacAddress 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated when an expected 
        (configured) MEP does not come up during the cross-check
        start timeout interval."
    ::= { ciscoEtherCfmNotificationPrefix 6 }

cEtherCfmXCheckUnknown NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId, 
        cEtherCfmEventLclMacAddress, 
        cEtherCfmEventRmtMepid, 
        cEtherCfmEventRmtMacAddress 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated when an unexpected MEP
        comes up."
    ::= { ciscoEtherCfmNotificationPrefix 7 }

cEtherCfmXCheckServiceUp NOTIFICATION-TYPE
    OBJECTS { 
        cEtherCfmEventServiceId, 
        cEtherCfmEventLclMacAddress 
    }
    STATUS        current
    DESCRIPTION
        "This notification is generated when all the MEPs belonging
        to a customer service instance come up before the expiration of
        the cross-check start timeout interval."
    ::= { ciscoEtherCfmNotificationPrefix 8 }

ciscoEtherCfmMIBCompliances OBJECT IDENTIFIER ::= 
    { ciscoEtherCfmMIBConform 1 }
ciscoEtherCfmMIBGroups      OBJECT IDENTIFIER ::= 
    { ciscoEtherCfmMIBConform 2 }

-- Conformance
ciscoEtherCfmMIBCompliance MODULE-COMPLIANCE
    STATUS        current
    DESCRIPTION
        "Compliance statement for agents that support the Ethernet
        CFM MIB."
    MODULE
        MANDATORY-GROUPS { 
            ciscoEtherCfmMIBEventGroup,
            ciscoEtherCfmMIBNotifGroup 
        }
    ::= { ciscoEtherCfmMIBCompliances 1 }

-- Units of Conformance
ciscoEtherCfmMIBEventGroup OBJECT-GROUP
    OBJECTS { 
        cEtherCfmMaxEventIndex,
        cEtherCfmEventDomainName,
        cEtherCfmEventType,
        cEtherCfmEventLastChange,
        cEtherCfmEventServiceId,
        cEtherCfmEventLclMepid,
        cEtherCfmEventLclMacAddress,
        cEtherCfmEventLclMepCount,
        cEtherCfmEventLclIfCount,  
        cEtherCfmEventRmtMepid,   
        cEtherCfmEventRmtMacAddress,
        cEtherCfmEventRmtPortState,
        cEtherCfmEventRmtServiceId, 
        cEtherCfmEventCode, 
        cEtherCfmEventDeleteRow
    }
    STATUS        current
    DESCRIPTION
        "Set of objects needed for CFM events."
    ::= { ciscoEtherCfmMIBGroups 1 }

ciscoEtherCfmMIBNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS { 
        cEtherCfmCcMepUp, 
        cEtherCfmCcMepDown, 
        cEtherCfmCcCrossconnect, 
        cEtherCfmCcLoop,
        cEtherCfmCcConfigError, 
        cEtherCfmXCheckMissing,
        cEtherCfmXCheckUnknown, 
        cEtherCfmXCheckServiceUp 
    }
    STATUS        current
    DESCRIPTION
        "Set of notifications implemented in this module."
    ::= { ciscoEtherCfmMIBGroups 2 }

END
