CISCOSB-MULTISESSIONTERMINAL-MIB DEFINITIONS ::= BEGIN

-- Title:                CIS<PERSON><PERSON> ROS
--                       Private Multi Session Terminal MIB
-- Version:              7.46
-- Date:                 15-Jan-2007

IMPORTS
    switch001                                               FROM CISCOSB-MIB
    OBJECT-TYPE, MODULE-IDENTITY                            FROM SNMPv2-SMI
    DisplayString                                           FROM SNMPv2-TC;

rlMultiSessionTerminal MODULE-IDENTITY
                LAST-UPDATED "200701020000Z"
                ORGANIZATION "Cisco Systems, Inc."

                CONTACT-INFO
                "Postal: 170 West Tasman Drive
                San Jose , CA 95134-1706
                USA

                
                Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

                DESCRIPTION
                      "This private MIB module defines Multi Session Terminal private MIBs."
                REVISION "200701020000Z"
                DESCRIPTION
                      "Initial revision."
        ::= { switch001 69 }

rlTerminalDebugModePassword OBJECT-TYPE
  SYNTAX DisplayString (SIZE(0..20))
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
           "When a user wants to change the terminal mode
            from debug mode to ASCII he must enter this password first"
    ::=  { rlMultiSessionTerminal 1 }

END
