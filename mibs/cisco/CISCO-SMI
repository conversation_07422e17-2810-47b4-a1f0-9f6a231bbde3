-- *****************************************************************
-- CISCO-SMI.my:  Cisco Enterprise Structure of Management Information
--   
-- April 1994, <PERSON>
--   
-- Copyright (c) 1994-1997, 2001, 2009, 2012, 2016 by cisco Systems Inc.
-- All rights reserved.
--   
-- ****************************************************************

CISCO-SMI DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-IDENTITY,
    enterprises
        FROM SNMPv2-SMI;


cisco MODULE-IDENTITY
    LAST-UPDATED    "201601150000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
            "Cisco Systems
            Customer Service

            Postal: 170 West Tasman Drive
            San Jose, CA  95134
            USA

            Tel: ****** 553-NETS

            E-mail: <EMAIL>"
    DESCRIPTION
        "The Structure of Management Information for the
        Cisco enterprise."
    REVISION        "201601150000Z"
    DESCRIPTION
        "Added ciscoLDAP under cisco"
    REVISION        "201208290000Z"
    DESCRIPTION
        "Added ciscoSMB under otherEnterprises"
    REVISION        "200902030000Z"
    DESCRIPTION
        "Added ciscoSB under otherEnterprises"
    REVISION        "200203210000Z"
    DESCRIPTION
        "Added ciscoPKI for PKI policy and extension OIDs"
    REVISION        "200105220000Z"
    DESCRIPTION
        "Added transport protocol domains."
    REVISION        "200011012246Z"
    DESCRIPTION
        "Added ciscoDomains to define new transports.  Also added
        ciscoCpeCIB, which will contain managed objects that
        contribute to the CPE Configuration Information Base (CIB)."
    REVISION        "200001110000Z"
    DESCRIPTION
        "Added ciscoPolicy, ciscoPolicyAuto, ciscoPIB, and
        ciscoPibToMib."
    REVISION        "9704090000Z"
    DESCRIPTION
        "Added ciscoPartnerProducts to generate sysObjectID
        for partner platforms"
    REVISION        "9505160000Z"
    DESCRIPTION
        "New oid assignments for Cisco REPEATER MIB and others."
    REVISION        "9404262000Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { enterprises 9 }



-- assigned by IANA

ciscoProducts OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoProducts is the root OBJECT IDENTIFIER from
        which sysObjectID values are assigned.  Actual
        values are defined in CISCO-PRODUCTS-MIB."
    ::= { cisco 1 }


local OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "Subtree beneath which pre-10.2 MIBS were built."
    ::= { cisco 2 }


temporary OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "Subtree beneath which pre-10.2 experiments were
        placed."
    ::= { cisco 3 }


pakmon OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "reserved for pakmon"
    ::= { cisco 4 }


workgroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "subtree reserved for use by the Workgroup Business Unit"
    ::= { cisco 5 }


otherEnterprises OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "otherEnterprises provides a root object identifier
        from which mibs produced by other companies may be
        placed.  mibs produced by other enterprises are
        typicially implemented with the object identifiers
        as defined in the mib, but if the mib is deemed to
        be uncontrolled, we may reroot the mib at this
        subtree in order to have a controlled version."
    ::= { cisco 6 }


ciscoSB OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoSB provides root Object Identifier for Management
        Information Base for products of Cisco Small Business.
        This includes products rebranded from linksys aquisition.
        MIB numbers under this root are managed and controlled
        by <EMAIL>."
    ::= { otherEnterprises 1 }


ciscoSMB OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoSMB provides root Object Identifier for Management
        Information Base for products of Cisco built for Small and 
        Medium Business market.The MIB numbers under this root are 
        managed and <NAME_EMAIL>"
    ::= { otherEnterprises 2 }


ciscoAgentCapability OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoAgentCapability provides a root object identifier
        from which AGENT-CAPABILITIES values may be assigned."
    ::= { cisco 7 }


ciscoConfig OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoConfig is the main subtree for configuration mibs."
    ::= { cisco 8 }


ciscoMgmt OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoMgmt is the main subtree for new mib development."
    ::= { cisco 9 }


ciscoExperiment OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoExperiment provides a root object identifier
        from which experimental mibs may be temporarily
        based.  mibs are typicially based here if they
        fall in one of two categories
        1) are IETF work-in-process mibs which have not
        been assigned a permanent object identifier by
        the IANA.
        2) are cisco work-in-process which has not been
        assigned a permanent object identifier by the
        cisco assigned number authority, typicially because
        the mib is not ready for deployment.

        NOTE WELL:  support for mibs in the ciscoExperiment
        subtree will be deleted when a permanent object
        identifier assignment is made."
    ::= { cisco 10 }


ciscoAdmin OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoAdmin is reserved for administratively assigned
        OBJECT IDENTIFIERS, i.e. those not associated with MIB
        objects"
    ::= { cisco 11 }


ciscoModules OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoModules provides a root object identifier
        from which MODULE-IDENTITY values may be assigned."
    ::= { cisco 12 }


lightstream OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "subtree reserved for use by Lightstream"
    ::= { cisco 13 }


ciscoworks OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoworks provides a root object identifier beneath
        which mibs applicable to the CiscoWorks family of network
        management products are defined."
    ::= { cisco 14 }


newport OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "subtree reserved for use by the former Newport Systems
        Solutions, now a portion of the Access Business Unit."
    ::= { cisco 15 }


ciscoPartnerProducts OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoPartnerProducts is the root OBJECT IDENTIFIER from
        which partner sysObjectID values may be assigned. Such 
        sysObjectID values are composed of the ciscoPartnerProducts
        prefix, followed by a single identifier that is unique for 
        each partner, followed by the value of sysObjectID of the
        Cisco product from which partner product is derived.  Note
        that the chassisPartner MIB object defines the value of the
        identifier assigned to each partner."
    ::= { cisco 16 }


ciscoPolicy OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoPolicy is the root of the Cisco-assigned OID
        subtree for use with Policy Management."
    ::= { cisco 17 }


-- Note that *******.********.1 is currently unassigned

ciscoPIB OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoPIB is the root of the Cisco-assigned OID
        subtree for assignment to PIB (Policy Information
        Base) modules."
    ::= { ciscoPolicy 2 }


ciscoPolicyAuto OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoPolicyAuto is the root of the Cisco-assigned
        OID subtree for OIDs which are automatically assigned
        for use in Policy Management."
    ::= { cisco 18 }


-- Note that *******.********.1 is currently unassigned

ciscoPibToMib OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoPibToMib is the root of the Cisco-assigned
        OID subtree for MIBs which are algorithmically
        generated/translated from Cisco PIBs with OIDs
        assigned under the ciscoPIB subtree.
        These generated MIBs allow management
        entities (other the current Policy Server) to
        read the downloaded policy.  By convention, for PIB
        'ciscoPIB.x', the generated MIB shall have the
        name 'ciscoPibToMib.x'."
    ::= { ciscoPolicyAuto 2 }


ciscoDomains OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoDomains provides a root object identifier from which
        different transport mapping values may be assigned."
    ::= { cisco 19 }


ciscoCIB OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoCIB is the root of the Cisco-assigned OID subtree for
        assignment to MIB modules describing managed objects that
        part of the CPE automatic configuration framework."
    ::= { cisco 20 }


ciscoCibMmiGroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoCibMmiGroup is the root of the Cisco-assigned OID
        subtree for assignment to MIB modules describing managed
        objects supporting the Modem Management Interface (MMI),
        the interface that facilitates CPE automatic configuration."
    ::= { ciscoCIB 1 }


ciscoCibProvGroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoCibStoreGroup is the root of the Cisco-assigned OID
        subtree for assignment to MIB modules describing managed
        objects contributing to the Configuration Information Base
        (CIB)."
    ::= { ciscoCIB 2 }


ciscoPKI OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoPKI is the root of cisco-assigned OID subtree for PKI
        Certificate Policies and Certificate Extensions."
    ::= { cisco 21 }


ciscoLDAP OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoLDAP is the root of the Cisco-assigned OID
        subtree for assignment to LDAP (Lightweight Directory
        Access Protocol) modules."
    ::= { cisco 22 }


-- ciscoAdmin assignments follow

ciscoProxy OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoProxy OBJECT IDENTIFIERS are used to uniquely name
        party mib records created to proxy for SNMPv1."
    ::= { ciscoAdmin 1 }

ciscoPartyProxy  OBJECT IDENTIFIER
    ::= { ciscoProxy 1 }

ciscoContextProxy  OBJECT IDENTIFIER
    ::= { ciscoProxy 2 }


-- Administrative assignments for repeaters

ciscoRptrGroupObjectID OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "ciscoRptrGroupObjectID OBJECT IDENTIFIERS are used to
        uniquely identify groups of repeater ports for use by the
        SNMP-REPEATER-MIB (RFC 1516) rptrGroupObjectID object."
    ::= { ciscoAdmin 2 }


ciscoUnknownRptrGroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The identity of an unknown repeater port group."
    ::= { ciscoRptrGroupObjectID 1 }


cisco2505RptrGroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The authoritative identity of the Cisco 2505 repeater
        port group."
    ::= { ciscoRptrGroupObjectID 2 }


cisco2507RptrGroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The authoritative identity of the Cisco 2507 repeater
        port group."
    ::= { ciscoRptrGroupObjectID 3 }


cisco2516RptrGroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The authoritative identity of the Cisco 2516 repeater
        port group."
    ::= { ciscoRptrGroupObjectID 4 }


ciscoWsx5020RptrGroup OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The authoritative identity of the wsx5020 repeater
        port group."
    ::= { ciscoRptrGroupObjectID 5 }


-- Administrative assignments for chip sets

ciscoChipSets OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "Numerous media-specific MIBS have an object, defined as
        an OBJECT IDENTIFIER, which is the identity of the chipset
        realizing the interface.  Cisco-specific chipsets have their 
        OBJECT IDENTIFIERS assigned under this subtree."
    ::= { ciscoAdmin 3 }


ciscoChipSetSaint1 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The identity of the Rev 1 SAINT ethernet chipset
        manufactured for cisco by LSI Logic."
    ::= { ciscoChipSets 1 }


ciscoChipSetSaint2 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The identity of the Rev 2 SAINT ethernet chipset
        manufactured for cisco by LSI Logic."
    ::= { ciscoChipSets 2 }


ciscoChipSetSaint3 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The identity of the Rev 3 SAINT ethernet chipset
        manufactured for cisco by Plessey."
    ::= { ciscoChipSets 3 }


ciscoChipSetSaint4 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The identity of the Rev 4 SAINT ethernet chipset
        manufactured for cisco by Mitsubishi."
    ::= { ciscoChipSets 4 }

-- Transport protocol domains
--   
-- The textual conventions for these domains are defined in CISCO-TM.

ciscoTDomains  OBJECT IDENTIFIER
    ::= { ciscoDomains 99999 }


ciscoTDomainUdpIpv4 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The UDP over IPv4 transport domain.  The corresponding
        transport address is of type CiscoTAddressIPv4."
    ::= { ciscoTDomains 1 }


ciscoTDomainUdpIpv6 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The UDP over IPv6 transport domain.  The corresponding
        transport address is of type CiscoTAddressIPv6 for global IPv6
        addresses and CiscoTAddressIPv6s for scoped IPv6 addresses."
    ::= { ciscoTDomains 2 }


ciscoTDomainTcpIpv4 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The TCP over IPv4 transport domain.  The corresponding
        transport address is of type CiscoTAddressIPv4."
    ::= { ciscoTDomains 3 }


ciscoTDomainTcpIpv6 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The TCP over IPv6 transport domain.  The corresponding
        transport address is of type CiscoTAddressIPv6 for global IPv6
        addresses and CiscoTAddressIPv6s for scoped IPv6 addresses."
    ::= { ciscoTDomains 4 }


ciscoTDomainLocal OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The Posix Local IPC transport domain. The corresponding
        transport address is of type CiscoTAddressLocal.  The Posix
        Local IPC transport domain incorporates the well known UNIX
        domain sockets."
    ::= { ciscoTDomains 5 }


ciscoTDomainClns OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The CLNS transport domain.  The corresponding transport
        address is of type CiscoTAddressOSI."
    ::= { ciscoTDomains 6 }


ciscoTDomainCons OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The CONS transport domain.  The corresponding transport
        address is of type CiscoTAddressOSI."
    ::= { ciscoTDomains 7 }


ciscoTDomainDdp OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The DDP transport domain.  The corresponding transport
        address is of type CiscoTAddressNBP."
    ::= { ciscoTDomains 8 }


ciscoTDomainIpx OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The IPX transport domain.  The corresponding transport
        address is of type CiscoTAddressIPX."
    ::= { ciscoTDomains 9 }


ciscoTDomainSctpIpv4 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The SCTP over IPv4 transport domain.  The corresponding
        transport address is of type CiscoTAddressIPv4."
    REFERENCE
        "RFC 2960 - Stream Control Transmission Protocol. R. Stewart,
        Q. Xie, K. Morneault, C. Sharp, H. Schwarzbauer, T. Taylor,
        I. Rytina, M. Kalla, L. Zhang, V. Paxson. October 2000."
    ::= { ciscoTDomains 10 }


ciscoTDomainSctpIpv6 OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION
        "The SCTP over IPv6 transport domain.  The corresponding
        transport address is of type CiscoTAddressIPv6 for global IPv6
        addresses and CiscoTAddressIPv6s for scoped IPv6 addresses."
    REFERENCE
        "RFC 2960 - Stream Control Transmission Protocol. R. Stewart,
        Q. Xie, K. Morneault, C. Sharp, H. Schwarzbauer, T. Taylor,
        I. Rytina, M. Kalla, L. Zhang, V. Paxson. October 2000."
    ::= { ciscoTDomains 11 }


END


