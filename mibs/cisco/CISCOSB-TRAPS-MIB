CISCOSB-TRAPS-MIB DEFINITIONS ::= BEGIN

-- Title:                <PERSON><PERSON><PERSON>SB ROS
--                       Private TRAPS MIB
-- Version:              7.46
-- Date:                 15-Jan-2007

IMPORTS
    NOTIFICATION-TYPE                                   FROM SNMPv2-SMI
    switch001                                           FROM CISCOSB-MIB
    rndErrorDesc, rndErrorSeverity                      FROM CISCOSB-DEVICEPARAMS-MIB
    rldot1dStpTrapVrblifIndex, rldot1dStpTrapVrblVID    FROM CISCOSB-BRIDGEMIBOBJECTS-MIB;

rndNotifications MODULE-IDENTITY
                LAST-UPDATED "201006250001Z"
				ORGANIZATION "Cisco Systems, Inc."

				CONTACT-INFO
				"Postal: 170 West Tasman Drive
				San Jose , CA 95134-1706
				USA

				
				Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

                DESCRIPTION
                      "This private MIB module defines switch private notifications"
                REVISION "201006250000Z"
                DESCRIPTION
                      "Initial revision."
        ::= { switch001 0 }

-- Enterprise-specific traps for switch001
-- Each enterprise-specific trap has two bound variables describing the exact nature of
-- the trap. rndErrorDesc provides a detailed description of the problem, including the
-- related interface number, board number or any other applicable variable.
-- rndErrorSeverity describes the severity of this problem.

-- Trap template
-- -------------
--    TRAP-TYPE
--       ENTERPRISE  rndNotifications
--       VARIABLES   { rndErrorDesc, rndErrorSeverity }
--       DESCRIPTION
--             ""
--       ::=
-- -------------

rxOverflowHWFault NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "An RX buffer overflow has occurred in one of the LAN or link
         interfaces. The bound variable rndErrorDesc provides the
         interface number."
    ::= { rndNotifications 3 }

txOverflowHWFault NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Interport queue overflow has occurred in one of the LAN or link interfaces.
         The bound variable rndErrorDesc provides the interface number."
    ::= { rndNotifications 4 }

routeTableOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "An overflow condition has occurred in the Routing Table. The Routing
         Table is used for IP routing algorithm (RIP)."
    ::= { rndNotifications 5 }

resetRequired NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that in order to perform the last SET request, a reset operation
         of the router/bridge is required. This occurs when the layer 2 routing algorithm is
         changed between SPF and Spanning Tree. The reset can be performed manually or
         using the variable rndAction."
    ::= { rndNotifications 10 }

endTftp  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that in the device finished a TFTP
         transaction with the management station.
         variable rndErrorDesc and rndErrorSeverity provides the actual message
         text and severity respectively."
    ::= { rndNotifications 12 }

abortTftp NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
       "This trap indicates that in the device aborted a TFTP session with
        the management station. Variable rndErrorDesc  and rndErrorSeverity
        provides the actual  message text  and severity  respectively."
    ::= { rndNotifications 13 }

startTftp NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that the device has intiated a
         TFTP session. rndErrorDesc will contain the file type in
         question"
    ::= { rndNotifications 14 }

faultBackUp NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Automantic switchover to backup link because of main link fault."
    ::= { rndNotifications 23 }

mainLinkUp NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Communication returened to main link."
    ::= { rndNotifications 24 }

ipxRipTblOverflow  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that in an OpenGate IPX RIP table overflow.
         The bound variable rndErrorDesc, rndErrorSeverity
         provides the actual  message text and severity respectively."
   ::= { rndNotifications 36 }

ipxSapTblOverflow  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that in an OpenGate IPX SAP table overflow.
         The bound variable rndErrorDesc, rndErrorSeverity
         provides the actual  message text and severity respectively."
    ::= { rndNotifications 37 }

facsAccessVoilation NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that message that fits FACS statenebt with
         operation blockAndReport was forward to the interface. The bound
         variable rndErrorDesc, rndErrorSeverity(== info ) and
         interface Number."
    ::= { rndNotifications 49 }

autoConfigurationCompleted NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that auto comfiguration completetd succssefully.
         The bound variable rndErrorDesc, rndErrorSeverity(== info )"
    ::= { rndNotifications 50 }

forwardingTabOverflow  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that an overflow condition has occurred in the
         layer II Forward Table.
         The bound variable rndErrorDesc, rndErrorSeverity(== warning )"
   ::={ rndNotifications  51 }

framRelaySwitchConnectionUp  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that a connection establish between the Frame
         relay Switch and the WanGate.
         The bound variable rndErrorDesc, rndErrorSeverity(== warning )"
    ::= { rndNotifications 53 }

framRelaySwitchConnectionDown  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that a connection between the Frame Relay Switch
         and the WanGate failed.
         The bound variable rndErrorDesc, rndErrorSeverity(== warning )"
    ::= { rndNotifications 54 }

errorsDuringInit  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that the an error occured during initialization
         The bound variable rndErrorDesc, rndErrorSeverity(== error )"
    ::=  { rndNotifications 61 }

vlanDynPortAdded  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         ""
    ::=  { rndNotifications 66 }

vlanDynPortRemoved  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         ""
    ::=  { rndNotifications 67 }

rsSDclientsTableOverflow  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This warning is generated when an overflow occurs in the clients table."
    ::=  { rndNotifications 68 }

rsSDinactiveServer  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This warning is generated when a server does not respond to the dispatchers polling
         and is thought to be inactive."
    ::=  { rndNotifications 69 }

rsIpZhrConnectionsTableOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "The Zero Hop Routing connections Table has been overflown."
    ::= { rndNotifications 70 }

rsIpZhrReqStaticConnNotAccepted NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "The requested static connection was not accepted because there is
          no available IP virtual address to allocate to it."
    ::= { rndNotifications 71 }

rsIpZhrVirtualIpAsSource NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "The virtual IP address appeared as a source IP.
          All the connections using it will be deleted and
          it will not be further allocated to new connections."
    ::=  { rndNotifications 72 }

rsIpZhrNotAllocVirtualIp NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "The source IP address sent an ARP specifying a virtual IP
          which was not allocated for this source.
          This virtual IP will not be allocated to connections of this
          specific source IP."
    ::= { rndNotifications 73 }

rsSnmpSetRequestInSpecialCfgState NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "An incoming SNMP SET request was rejected because no such
          requests (except action requests) are accepted after start of
          new configuration reception or during sending the current
          configuration to an NMS."
    ::= { rndNotifications 74 }

rsPingCompletion NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "A rsPingCompleted trap is sent at the completion
         of a sequence of pings if such a trap was requested
         when the sequence was initiated. The trap severity is info.
         The trap text will specify the following information:
         rsPingCompletionStatus, rsPingSentPackets, rsPingReceivedPackets
         In addition to the above listed objects (which are always present),
         the message will also specify the following quantities:
         if any responses were received:
            rsPingMinReturnTime
            rsPingAvgReturnTime
            rsPingMaxReturnTime"
    ::= { rndNotifications 136 }

pppSecurityViolation NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This trap indicates that a PPP link got an unrecognized secret.
         The bound variables rndErrorDesc,
         rndErrorSeverity(== warning ), interface Number. and pppSecurityIdentity"
    ::= { rndNotifications 137 }

frDLCIStatudChange NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        ""
    ::= { rndNotifications 138 }

papFailedCommunication NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        ""
    ::= { rndNotifications 139 }

chapFailedCommunication NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        ""
    ::= { rndNotifications 140 }

rsWSDRedundancySwitch NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Whenever main server fails and backup takes over or server comes
         up after failure a trap of this type is issued."
    ::= { rndNotifications 141 }

rsDhcpAllocationFailure NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "DHCP failed to allocate an IP address to a requesting host
         because of memory shortage or inadequate configuration of
         available IP addresses."
    ::= { rndNotifications 142 }

--  Traps 143 - 144 reserved for IP Multicast

rlIpFftStnOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "The IP SFFT overflow."
    ::= { rndNotifications 145 }

rlIpFftSubOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "The IP NFFT overflow."
    ::= { rndNotifications 146 }

rlIpxFftStnOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "The IPX SFFT overflow."
    ::= { rndNotifications 147 }

rlIpxFftSubOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "The IPX NFFT overflow."
    ::= { rndNotifications 148 }

rlIpmFftOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "The IPM FFT overflow."
    ::= { rndNotifications 149 }

rlPhysicalDescriptionChanged NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Indicates that the physical decription of the device has
         changed"
    ::= { rndNotifications 150 }

rldot1dStpPortStateForwarding NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity, rldot1dStpTrapVrblifIndex, rldot1dStpTrapVrblVID }
    STATUS  current
    DESCRIPTION
        "The trap is sent by a bridge when any of its configured ports
         transitions from the Learning state to the Forwarding state."
    ::= { rndNotifications 151 }

rldot1dStpPortStateNotForwarding NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity, rldot1dStpTrapVrblifIndex, rldot1dStpTrapVrblVID }
    STATUS  current
    DESCRIPTION
        "The trap is sent by a bridge when any of its configured ports
         transitions from the Forwarding state to the Blocking state."
    ::= { rndNotifications 152 }

rlPolicyDropPacketTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Indicates that the packet drop due to the policy "
    ::= { rndNotifications 153 }

rlPolicyForwardPacketTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Indicates that the packet has forward based on policy"
    ::= { rndNotifications 154 }

--  Trap 155 - 144 reserved for DVMRP

rlIgmpProxyTableOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "An IGMP PROXY Table overflow."
    ::= { rndNotifications 156 }

rlIgmpV1MsgReceived NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "An IGMP Message of v1 received on ifIndex. "
    ::= { rndNotifications 157 }

rlVrrpEntriesDeleted NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "One or more VRRP entries deleted due to IP interface deletion or transition. "
    ::= { rndNotifications 158 }

rlHotSwapTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Hot swap trap."
     ::= { rndNotifications 159 }

rlTrunkPortAddedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that a port is added to a trunk"
    ::= { rndNotifications 160 }

rlTrunkPortRemovedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "This warning is generated when a port removed from a trunk."
    ::=  { rndNotifications 161 }

rlTrunkPortNotCapableTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that a port can not be added to
         a trunk because of device limitations or diffrent swIfType."
    ::=  { rndNotifications 162 }

--  Traps 163 - 169 reserved for IP Multicast

rlLockPortTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that a locked port receive
        a frame with new source Mac Address."
    ::=  { rndNotifications 170 }

vlanDynVlanAdded  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "add gvrp dynamic vlan"
    ::=  { rndNotifications 171 }

vlanDynVlanRemoved  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "remove gvrp dynamic vlan"
    ::=  { rndNotifications 172 }

vlanDynamicToStatic  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "vlan status was changed from dynamic to static"
    ::=  { rndNotifications 173 }

vlanStaticToDynamic  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "vlan status was changed from static to dynamic"
    ::=  { rndNotifications 174 }

dstrSysLog  NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
         "Controller receive trap from member , and forward it as trap"
    ::=  { rndNotifications 175 }

rlEnvMonFanStateChange NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Trap indicating the fan state.
         rndErrorSeverity will be:
                0 for fan state  nomal, notPresent
                1 for fan state warning, notFunctioning
                2 for fan state critical
                3 for fan state fatal
         The error text will specify the fan index, fan description and the exact fan state"
    ::=  { rndNotifications 176 }

rlEnvMonPowerSupplyStateChange NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Trap indicating the power supply state.
         rndErrorSeverity will be:
                0 for power supply state  nomal, notPresent
                1 for power supply state warning, notFunctioning
                2 for power supply state critical
                3 for power supply state fatal
         The error text will specify the power supply index, power supply description and the exact power supply state"

    ::=  { rndNotifications 177 }

rlStackStateChange NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Trap indicating the stack connection state
                0 for stack state connected,
                1 for stack state disconnected "

    ::=  { rndNotifications 178 }

rlEnvMonTemperatureRisingAlarm NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Trap indicating that the temperature in the device has exceeded the
         device specific safe temperature threshold."

    ::=  { rndNotifications 179 }

--  Traps 180, 181 reserved for copy

--  Trap 182 reserved for IP Multicast

rlBrgMacAddFailedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that adding dynamic mac/s
         failed due to full hash chain."
    ::= { rndNotifications 183 }

rldot1xPortStatusAuthorizedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that port 802.1x status is authorized."
    ::= { rndNotifications 184 }

rldot1xPortStatusUnauthorizedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap is indicating that port 802.1x status is unAuthorized."
    ::=  { rndNotifications 185 }

-- 186-191 are used in the rlPhysicalDescription MIB

swIfTablePortLock NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap is indicating port lock hase began."
    ::=  { rndNotifications 192 }

swIfTablePortUnLock NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap is indicating port lock has ended."
    ::=  { rndNotifications 193 }

rlAAAUserLocked NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating that the user was locked after
         number of consecutives unsuccessful login attempts."
    ::= { rndNotifications 194 }

-- 195-199 are used in the rlPhysicalDescription MIB

bpduGuardPortSuspended NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - Port was suspended because there was BPDU Guard violation."
    ::= { rndNotifications 202 }

rldot1xSupplicantMacAuthorizedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that MAC authentication supplicant
         is authenticated and is allowed to access the network."
    ::= { rndNotifications 203 }

rldot1xSupplicantMacUnauthorizedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap is indicating that Radius server rejects
         MAC authentication supplicant."
    ::=  { rndNotifications 204 }

stpLoopbackDetection NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - A loopback was detected on port."
    ::= { rndNotifications 205 }

stpLoopbackDetectionResolved NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - A loopback detection resolved on port."
    ::= { rndNotifications 206 }

loopbackDetectionPortSuspended NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - A port has been suspended by Loopback Detection."
    ::= { rndNotifications 207 }


-- Trap 208 reserved for rlIgmpMldSnoopTriplePlayPort
-- 209-210 are used in the rllldp MIB
-- 211 used by rlCopySWFinished
-- 212 used by rlCopySWToUnits

rlPortSuspended NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - A port has been suspended by Any application."
    ::= { rndNotifications 213 }

rlSpecialBpduDbOverflow NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - Special BPDU DB Overflow."
    ::= { rndNotifications 214 }

rldot1xSupplicantLoggedOutTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap is indicating that supplicant was logged out
         Since the ports time-range state was changed to inactive."
    ::=  { rndNotifications 215 }

rldot1xPortControlModeNotAutoTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap is indicating that altough ports time-range
         Is active now, it can not start working in mode auto."
    ::=  { rndNotifications 216 }

rlEeeLldpMultipleNeighbours NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - Port has multiple LLDP remote link neighbours -
         EEE operational state will be FALSE."
    ::= { rndNotifications 217 }

rlEeeLldpSingleNeighbour NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating - Port has single LLDP remote link neighbour -
         EEE operational can be TRUE."
    ::= { rndNotifications 218 }

rldot1xSupplicantQuietPeriodTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap is indicating that supplicant quiet period timeout
         is active now."
    ::=  { rndNotifications 219 }

-- 220 used by rlCopyMirrorFileIllegal

rlStackVersionUpgradeTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that stack member is upgrading
         to the image or boot version of the stack controller."
    ::=  { rndNotifications 222 }

rlStackVersionDowngradeTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating that stack member is downgrading
         to the image or boot version of the stack controller."
    ::=  { rndNotifications 223 }

-- 224-226 used by CDP


-- Trap 227 reserved for rlbrgIgmpSnoopQrrDuplicateIP

-- 228-239 used by FHS

-- 240-245 used by PoE

-- 240 used by Poe Inrush - rlPethPowerPseInrushTestEnabled
pseInrushPort NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that port in PD does not meet the inrush-current standard."
    ::=  { rndNotifications 240 }

pseOverloadPort NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that Port (x) requires more power than it should require."
    ::=  { rndNotifications 241 }
	
poePowerNegotiationInfo NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating power negotiation resolved power in Watts."
    ::=  { rndNotifications 242 }
	
poePowerNegotiation4Wire NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating power negotiation 4-Wire status."
    ::=  { rndNotifications 243 }

poePowerHWFail NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating power HW fail status."
    ::=  { rndNotifications 244 }
	
poePowerNegotiationExpiredInfo NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating power negotiation expired."
    ::=  { rndNotifications 245 }
    
-- Storm control
rlStormControlMinRateTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating that port storm control rate limit is configured to minumum rate."
    ::=  { rndNotifications 246 }

-- AP
rlApBackplanePortResolutionTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating about backplane ethernet port resolution."
    ::=  { rndNotifications 247 }

-- Sfp
sfpPortPresent NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that gbic is now present on port (X)."
    ::=  { rndNotifications 248}

sfpPortNotPresent NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that gbic is not present anymore on port (X)."
    ::=  { rndNotifications 249 }

sfpPortLoss NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that loss Diagnosed on port (X)."
    ::=  { rndNotifications 250}

sfpPortNotLoss NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that loss isn't active anymore on port (X)."
    ::=  { rndNotifications 251 }

-- Storm control
rlStormControlOccursTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating that port storm control occurs on a port."
    ::=  { rndNotifications 252 }

-- Radius Server
rlRadiusServTrapAcct NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Radius server accounting event trap."
    ::=  { rndNotifications 255 }

rlRadiusServTrapAuthFailure NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Radius server authentication failure event trap."
    ::=  { rndNotifications 256 }

rlRadiusServTrapAuthSuccess NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Radius server authentication success event trap."
    ::=  { rndNotifications 257 }
   
rlRedundantFanStateChange NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Trap indicating the redundant fan status change."
    ::=  { rndNotifications 258 }

rldot1xSupplicantPortAuthorizedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating that 802.1x supplicant port status is authorized."
    ::= { rndNotifications 260 }

rldot1xSupplicantPortUnauthorizedTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating that 802.1x supplicant port status is unAuthorized."
    ::=  { rndNotifications 261 }

rldot1xCredentialTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating that credential is changed, rejected or not updated."
    ::=  { rndNotifications 262 }
	
poeNonPOEDetected NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating Non POE Detected unit"
    ::=  { rndNotifications 263}	

rlBoxUtilI2CReadFailureTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        ""
    ::=  { rndNotifications 264 }

rlBoxUtilI2CWriteFailureTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        ""
    ::=  { rndNotifications 265 }
	
rlHostHlibCpldUpdateTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Informational trap indicating CPLD update FW is in progress"
    ::=  { rndNotifications 266 }
	
rlBrgPvstInconsistencyEnterStateTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "The trap is sent by a bridge when any of its configured ports
         transitions to new inconsistency mode."
    ::= { rndNotifications 268 }
	
rlBrgPvstInconsistencyExitStateTrap NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "The trap is sent by a bridge when any of its configured ports
         transitions from inconsistency mode to resolved mode."
    ::= { rndNotifications 269 }

-- Sfp
sfpPortNonCompliant NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating that non-compliant SFP is inserted on port (X)."
    ::=  { rndNotifications 275 }
	
poePowerPortHWFail NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating power HW fail status on a port."
    ::=  { rndNotifications 276 }

-- HTTP Client
rlHttpClientCertAddressMismatch NOTIFICATION-TYPE
    OBJECTS   { rndErrorDesc, rndErrorSeverity }
    STATUS  current
    DESCRIPTION
        "Warning trap indicating HTTP-TLS X.509 certificate validation error of
	 Common-Name/Subject-Alternative-Name "
    ::=  { rndNotifications 278 }

	
END

