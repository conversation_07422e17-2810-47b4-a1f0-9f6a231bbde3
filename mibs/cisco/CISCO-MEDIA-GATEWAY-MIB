-- *********************************************************************
-- CISCO-MEDIA-GATEWAY-MIB
--
-- This MIB defines the attributes of a Media Gateway.
--
-- March 2003  <PERSON><PERSON><PERSON>
--
-- Copyright (c) 2003, 2004, 2005 by cisco Systems, Inc.
-- All rights reserved.
--
-- *********************************************************************

CISCO-MEDIA-GATEWAY-MIB  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Integer32, Gauge32, Unsigned32
        FROM SNMPv2-SMI
    RowStatus, TruthValue, TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    InterfaceIndexOrZero
        FROM IF-MIB
    InetAddress, InetAddressType,
    InetAddressPrefixLength,
    InetPortNumber
        FROM INET-ADDRESS-MIB    
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM SNMPv2-CONF  
    CiscoPort, EntPhysicalIndexOrZero
        FROM CISCO-TC
    ciscoMgmt
        FROM CISCO-SMI;

ciscoMediaGatewayMIB  MODULE-IDENTITY
    LAST-UPDATED "200902250000Z"
    ORGANIZATION "Cisco Systems, Inc."
    CONTACT-INFO
        "        Cisco Systems
                 Customer Service
        Postal: 170 W Tasman Drive
                San Jose, CA 95134
                USA
           Tel: ****** 553-NETS
        E-mail: <EMAIL>"

    DESCRIPTION
        "The MIB module for managing Trunk Media Gateway.  

         A Media Gateway is a network element that provides conversion 
         between the audio signals carried on telephone circuits and 
         data packets carried over the Internet or over other packet 
         data networks. 

         Trunk Media Gateway interface is between the telephone network 
         and a Voice over IP/ATM network. 
         The interface on a Trunk Gateway terminates a trunk connected 
         to PSTN switch (e.g., Class 5, Class 4, etc.).
         
         Media Gateways use a call control architecture where the call
         control 'intelligence' is outside the gateways and handled by
         external call control elements, called Media Gateway 
         Controllers (MGCs). 
         The MGCs or Call Agents, synchronize with each other to 
         send coherent commands to the gateways under their control.

         MGCs use master/slave protocols to command the gateways under 
         their control.  Examples of these protocols are:
           *  Simple Gateway Control Protocol
           *  Media Gateway Control Protocol
           *  Megaco (H.248)
           *  Simple Resource Control Protocol

         To connect MG to MGCs using these control protocols through 
         an IP/UDP Ports which must be configured. To resolve IP 
         Addresses, DNS name services may be used.
        "
    REVISION        "200902250000Z"
    DESCRIPTION
        "Added object cmgwV23Enabled to
        cMediaGwTable."
 
    REVISION "200606150000Z"
    DESCRIPTION
        "Added object cmgwLawInterceptEnabled to
         cMediaGwTable.
         Added object cMediaGwCcCfgDefRtpNamePrefix to
         cMediaGwCallControlConfigTable.
        " 
    REVISION "200509010000Z" 
    DESCRIPTION
        "Added object cmgwSrcFilterEnabled to
         cMediaGwTable.
         Added object cmgwSignalProtocolConfigVer
         to cmgwSignalProtocolTable.
         Added cMediaGwRscStatsTable."

    REVISION "200411190000Z"
    DESCRIPTION
        "Added object cmgwSignalProtocolPreference to
         cmgwSignalProtocolTable."

    REVISION "200407300000Z"
    DESCRIPTION
        "(1) Added the following objects: 
              cmgwVtMappingMode,
              cMediaGwCcCfgDefBearerTraffic,
              cmgwSignalMgcProtocolPort

         (2) Added new enum 'tgcp' to cmgwSignalProtocol 
        "

    REVISION "200304070000Z"
    DESCRIPTION
        "Initial version of this MIB module"
    ::= { ciscoMgmt 324 }

--
-- Object Identifiers used for Packetized Voice Switch Management
--

ciscoMediaGatewayMIBNotifs         OBJECT IDENTIFIER
                                ::= {ciscoMediaGatewayMIB 0}

ciscoMediaGatewayMIBObjects        OBJECT IDENTIFIER 
                                ::= { ciscoMediaGatewayMIB 1 }

cMediaGwConfig                     OBJECT IDENTIFIER 
                                ::= { ciscoMediaGatewayMIBObjects 1 }

cMediaGwStats                      OBJECT IDENTIFIER 
                                ::= { ciscoMediaGatewayMIBObjects 2 }

-- *********************************************************************
-- TEXTUAL CONVENTIONS USED IN THIS MIB
-- *********************************************************************
 
CGwServiceState ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION
       "This textual convention defines the service state of media 
        gateway.
        The possible service states are:

          inService:
            Gateway is ready to provide service. 
            In this state, Gateway will respond to connection control
            requests, send autonomous messages to the call agent
            as applicable, etc.

          forcedOutOfService:
            Gateway is in Out-Of-Service State.
            All calls destroyed on the GW. 
            A Service Change message with FORCED method is sent to CA.
            No new connections are allowed.

          gracefulOutOfService:
            Gateway is in Out-Of-Service State.
            All existing calls will not be affected. 
            A Service Change message with GRACEFUL method is sent to CA.
            No new connections are allowed."
    SYNTAX  INTEGER {
                     inService            (1),
                     forcedOutOfService   (2),
                     gracefulOutOfService (3)
                    }

CGwAdminState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
       "This textual convention defines the administrative state of  
        media gateway.

        The possible administrative states are as follows:
          inService: 
              Gateway would be restored to in-service status 
              and a ServiceChange with method RESTART message will be 
              sent to Call Agent
             
          forcefulOutOfService: 
              Gateway would be in Out-Of-Service State 
              Any existing connections on the GW will be deleted.
              A ServiceChange with method FORCED message will be 
              sent to call agent.
              New connections would be blocked.       

          gracefulOutOfService: 
              Gateway would be in in Out-Of-Service State 
              Any existing connections on the GW will not be affected.
              A ServiceChange with method GRACEFUL message will be 
              sent to call agent.
              New connections would be blocked."

    SYNTAX  INTEGER {
                    inService            (1),
                    forcedOutOfService   (2),
                    gracefulOutOfService (3)
                    }

GatewayLifNumber ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION
       "An index that uniquely identifies a LIF (Logical Interface)
        in the media gateway. 
        LIF is a logical interface which groups TDM(DS1) interfaces
        into packet resource partitions (PVCs) in the media gateway.  
        LIF is used for:
           AAL5 (VoIP) switching 
           AAL2 (VoATM) switching, only if support virtual gateway "
    SYNTAX  Unsigned32 (1..255)

CVoiceTonePlanIndex ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION       
       "This textual convention defines the type of index for 
        identifying a voice tone plane in a Media gateway."
    SYNTAX   Unsigned32 (1..65535) 

CVoiceTonePlanIndexOrZero ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION       
       "This textual convention uniquely identifies the voice tone plan 
        to be used in a voice DS0 group.

        The value of 0 means the default tone plan specified in
        the media gateway (the value of cMediaGwCcCfgDefaultTonePlanId)
        to be used.

        A value greater than 0 means the tone plan specified by the 
        index of the cvtcTonePlanTable to be used (same as 
        cvtcTonePlanId)."
    SYNTAX   Unsigned32 (0..65535) 

CCallControlProfileIndex ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION       
       "This textual convention defines the type of index that is 
        used for identifying a call control profile of XGCP and 
        H.248 protocol."
    SYNTAX   Unsigned32 (1..65535) 

CCallControlProfileIndexOrZero ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION       
       "This textual convention is an extension of the 
        CCallControlProfileIndex convention. 
        The latter defines a greater than zero value used to identify 
        a call control profile in a media gateway.  
        This extension permits the additional value of zero.
        The value of '0' means the default call control profile of the 
        media gateway."
    SYNTAX   Unsigned32 (0..65535) 

CCallControlJitterDelayMode ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION
       "This textual convention defines the jitter buffer mode in
        a call connection.

        adaptive(1) - means to use jitter nominal delay as the 
                      initial jitter buffers size and let the DSP
                      pick the optimal value of the jitter buffer
                      size between the range of jitter maximum delay
                      and jitter minimum delay.

        fixed(2) - means to use a constant jitter buffer size
                   which is specified by jitter nominal delay.
       "
    SYNTAX        INTEGER {
                      adaptive (1),
                      fixed    (2)
                  }

-- *********************************************************************
--  cMediaGwTable 
-- *********************************************************************

cMediaGwTable  OBJECT-TYPE
    SYNTAX         SEQUENCE OF CMediaGwEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "This table contains the global media gateway parameters
         information.
         It supports the modification of the global media gateway 
         parameters." 
    ::= { cMediaGwConfig 1 }

cMediaGwEntry  OBJECT-TYPE
    SYNTAX         CMediaGwEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "A Media Gateway Entry.  
         At system power-up, an entry is created by the agent 
         if the system detects a media gateway module has been added 
         to the system, and an entry is deleted if the entry associated
         media gateway module has been removed from the system."
    INDEX       { cmgwIndex }
    ::= { cMediaGwTable 1 }

CMediaGwEntry::= SEQUENCE {
                   cmgwIndex              Integer32,
                   cmgwDomainName         SnmpAdminString,
                   cmgwPhysicalIndex      EntPhysicalIndexOrZero,
                   cmgwServiceState       CGwServiceState,
                   cmgwAdminState         CGwAdminState,
                   cmgwGraceTime          Integer32,
                   cmgwVtMappingMode      INTEGER,
                   cmgwSrcFilterEnabled   TruthValue,
                   cmgwLawInterceptEnabled  TruthValue,
                   cmgwV23Enabled           TruthValue
                  }

cmgwIndex  OBJECT-TYPE
    SYNTAX         Integer32 (1..2147483647)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "An index that uniquely identifies an entry in the 
         cMediaGwTable."
    ::= { cMediaGwEntry 1 }

cmgwDomainName  OBJECT-TYPE
    SYNTAX         SnmpAdminString (SIZE (0..64)) 
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "This object is used to represent a domain name under which   
         the Media Gateway could also be registered in a DNS name
         server. 

         The value of this object reflects the value of 
         cmgwConfigDomainName from the entry with a value of 
         'gateway(1)' for object cmgwConfigDomainNameEntity of 
         cMediaGwDomainNameConfigTable.
 
         If there is no entry in cMediaGwDomainNameConfigTable with
         'gateway(1)' of cmgwConfigDomainNameEntity, then
         the value of this object will be empty string."
    ::= { cMediaGwEntry 2 }

cmgwPhysicalIndex  OBJECT-TYPE
    SYNTAX         EntPhysicalIndexOrZero 
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "This object represents the entPhysicalIndex of the
         card in which media gateway is running. It will contain
         value 0 if the entPhysicalIndex value is not available or 
         not applicable"
    ::= { cMediaGwEntry 3 }


cmgwServiceState  OBJECT-TYPE
    SYNTAX         CGwServiceState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "This object indicates the current service state of the Media 
         Gateway.
         This object is controlled by 'cmgwAdminState' 
         object."
    ::= { cMediaGwEntry 4 }

cmgwAdminState  OBJECT-TYPE
    SYNTAX         CGwAdminState
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION
        "This object is used to change the service state of 
         the Media Gateway from inService to outOfService or from 
         outOfService to inService. 
         The resulting service state of the gateway is represented  
         by 'cmgwServiceState'."
    ::= { cMediaGwEntry 5 }

cmgwGraceTime  OBJECT-TYPE
    SYNTAX         Integer32(-1..65535) 
    UNITS          "seconds"
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION
        "This object is used to represent grace period.
         The grace period (restart delay in RSIP message) is  
         expressed in a number of seconds. 
         It means how soon the gateway will be taken out of service.
         The value -1 indicates that the grace period time is
         disabled."
    DEFVAL { -1 }
    ::= { cMediaGwEntry 6 }

cmgwVtMappingMode  OBJECT-TYPE
    SYNTAX    INTEGER {
                  standard     (1),
                  titan        (2)
              } 
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION
        "This object is used to represent the VT (sonet Virtual
         Tributary) counting.

         standard - standard counting (based on Bellcore TR253)
         titan    - TITAN5500 counting (based on Tellabs TITAN 5500)

         Note: 'titan' is valid only if sonet line medium type 
               (sonetMediumType of SONET-MIB) is 'sonet' and 
               sonet path payload type (cspSonetPathPayload of
               CISCO-SONET-MIB) is 'vt15vc11'.
        "
     ::= { cMediaGwEntry 7 }

cmgwSrcFilterEnabled  OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable or disable the source IP
         and port filtering with MGC for security consideration
         as follows:
           'true'  - source IP and port filter is enabled 
           'false' - source IP and port filter is disable 
        "
    DEFVAL { false }
    ::= { cMediaGwEntry 8 }

cmgwLawInterceptEnabled  OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable or disable the lawful
         intercept for government.
         as follows:
           'true'  - enable lawful intercept
           'false' - disable lawful intercept
        "
    DEFVAL { false }
    ::= { cMediaGwEntry 9 }
cmgwV23Enabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is to enable or disable V23 tone.
         Setting the object value to 'true', will cause VXSM (Voice Switching
         Service Module) to detect V23 tone.
         "
          DEFVAL          { false } 
    ::= { cMediaGwEntry 10 }


-- *********************************************************************
-- Media Gateway Protocol Table
-- *********************************************************************

cmgwSignalProtocolTable  OBJECT-TYPE
    SYNTAX         SEQUENCE OF CmgwSignalProtocolEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "This table contains the available signaling protocols that
         are supported by the media gateway for communication with
         MGCs."
    ::= { cMediaGwConfig 2 }

cmgwSignalProtocolEntry  OBJECT-TYPE
    SYNTAX         CmgwSignalProtocolEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "Each entry represents an signaling protocol supported
         by the media gateway."
    INDEX       { cmgwIndex, cmgwSignalProtocolIndex }
    ::= { cmgwSignalProtocolTable 1 }

CmgwSignalProtocolEntry::= SEQUENCE {
                   cmgwSignalProtocolIndex        Integer32,
                   cmgwSignalProtocol             INTEGER,
                   cmgwSignalProtocolVersion      SnmpAdminString,
                   cmgwSignalProtocolPort         CiscoPort,
                   cmgwSignalMgcProtocolPort      InetPortNumber,
                   cmgwSignalProtocolPreference   Integer32,
                   cmgwSignalProtocolConfigVer    SnmpAdminString
                  }


cmgwSignalProtocolIndex  OBJECT-TYPE
    SYNTAX         Integer32(1..65535) 
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "An index that uniquely identifies an entry in
         cmgwSignalProtocolTable." 
    ::= { cmgwSignalProtocolEntry 1 }


cmgwSignalProtocol  OBJECT-TYPE
    SYNTAX    INTEGER {
                  other     (1),
                  mgcp      (2),
                  h248      (3),
                  tgcp      (4)
              } 
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "This object is used to represent the protocol type.
         other - None of the following types.
         mgcp  - Media Gateway Control Protocol
         h248 - Media Gateway Control (ITU H.248)
         tgcp - Trunking Gateway Control Protocol"
     ::= { cmgwSignalProtocolEntry 2 }


cmgwSignalProtocolVersion OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE (1..16))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is used to represent the protocol version. 
         For example cmgwSignalProtocol is 'mgcp(2)' and
         this object is string '1.0'. cmgwSignalProtocol is 
         'h248(3)' and this object is set to '2.0'."
    REFERENCE
        "MCGP 1.0 is documented in RFC2705."
     ::= { cmgwSignalProtocolEntry 3 }


cmgwSignalProtocolPort  OBJECT-TYPE
    SYNTAX         CiscoPort
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION
        "This object is used to represent the UDP port associated 
         with the protocol.
         If the value of cmgwSignalProtocol is 'mgcp(2)' and the
         value of cmgwSignalProtcolVersion is '1.0', the default
         value of this object is '2727'. 
         If the value of cmgwSignalProtocol is 'h248(3)' and the
         value of cmgwSignalProtcolVersion is '1.0', the default
         value of this object is '2944'."
    ::= { cmgwSignalProtocolEntry 4 }

cmgwSignalMgcProtocolPort  OBJECT-TYPE
    SYNTAX         InetPortNumber
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION
        "This object specifies the protocol port of the Media Gateway
         Controller (MGC).
         If the value of cmgwSignalProtocol is 'mgcp(2)' or 'tgcp(4)'
         and the value of cmgwSignalProtcolVersion is '1.0', the
         default value of this object is '2427'.
         If the value of cmgwSignalProtocol is 'h248(3)' and the
         value of cmgwSignalProtcolVersion is '1.0', the default
         value of this object is '2944'."
    ::= { cmgwSignalProtocolEntry 5 }

cmgwSignalProtocolPreference  OBJECT-TYPE
    SYNTAX         Integer32 (0..255)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION
        "This object specifies the preference of the signal protocol 
         supported in the media gateway.

         If this object is set to 0, the corresponding signal
         protocol will not be used by the gateway.
          
         The value of this object is unique within the corresponding
         gateway. The entry with lower value has higher preference."
 
    ::= { cmgwSignalProtocolEntry 6 }

cmgwSignalProtocolConfigVer  OBJECT-TYPE
    SYNTAX          SnmpAdminString (SIZE (1..16))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the protocol version
         used by the gateway in the messages to MGC
         in order to exchange the service capabilities.

         For example cmgwSignalProtocol is 'h248(3)' and
         this object can be string '1' or '1.0', '2' or '2.0'. 

         'MAX' is a special string indicating the gateway will
         use the highest protocol version supported in the 
         gateway, but it can be changed to lower version after 
         it negotiates with MGC. The final negotiated protocol
         version will be indicated in cmgwSignalProtocolVersion.

         The version strings other than 'MAX' can be specified for
         the gateway to communicate with the MGC which doesn't
         support service capabilities negotiation. For example if
         a MGC supports only version 1.0 MGCP, this object should
         be set to '1' to instruct the gateway using MGCP 
         version 1.0 format messages to communicate with MGC. "
    ::= { cmgwSignalProtocolEntry 7 }

-- *********************************************************************
-- cMediaGwIpConfigTable 
-- *********************************************************************

cMediaGwIpConfigTable     OBJECT-TYPE
    SYNTAX         SEQUENCE OF CMediaGwIpConfigEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "This table contains a list of media gateway IP address and
         the IP address associated interface information.

         If IP address associated interface is PVC, only 
         aal5 control PVC or aal5 bearer PVC are valid.       
         When the PVC is aal5 control, the IP address is used to 
         communicate to MGC; when the PVC is aal5 bearer, the IP
         address is used to communicate to other gateway.
         The PVC information is kept in cwAtmChanExtConfigTable:
          cwacChanPvcType:      aal5/aal2/aal1
          cwacChanApplication:  control/bearer/signaling

         If IP address associated interface is not PVC, refer to the 
         IP addresses associated interface table for the usage
         of IP address."
    ::= { cMediaGwConfig 3 }


cMediaGwIpConfigEntry     OBJECT-TYPE
    SYNTAX         CMediaGwIpConfigEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "A Media Gateway IP configuration entry. 
         Each entry represents a media gateway IP address for MGCs
         to communicate with the media gateway."
    INDEX       { cmgwIndex, cmgwIpConfigIndex }
    ::= { cMediaGwIpConfigTable 1 }


CMediaGwIpConfigEntry ::= SEQUENCE  {
    cmgwIpConfigIndex            Integer32,
    cmgwIpConfigIfIndex          InterfaceIndexOrZero,
    cmgwIpConfigVpi              Integer32,
    cmgwIpConfigVci              Integer32,
    cmgwIpConfigAddrType         InetAddressType,
    cmgwIpConfigAddress          InetAddress,
    cmgwIpConfigSubnetMask       InetAddressPrefixLength,
    cmgwIpConfigDefaultGwIp      TruthValue,
    cmgwIpConfigForRemoteMapping TruthValue,
    cmgwIpConfigRowStatus        RowStatus
}

cmgwIpConfigIndex     OBJECT-TYPE
    SYNTAX          Integer32 (1..64)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A unique index to identify each media gateway IP address."
    ::= { cMediaGwIpConfigEntry 1 }


cmgwIpConfigIfIndex    OBJECT-TYPE
    SYNTAX             InterfaceIndexOrZero
    MAX-ACCESS         read-create
    STATUS             current
    DESCRIPTION
        "This object is ifIndex of the interface which is associated
         to the media gateway IP address.

         For ATM interface, the IP address should be associated to
         an existing PVC:
            cmgwIpConfigIfIndex represents port of the PVC
            cmgwIpConfigVpi represents VPI of the PVC
            cmgwIpConfigVci represents VCI of the PVC
         And one PVC only can be associated with one IP address.

         If this object is set to zero which means the IP address
         is not associated to any interface."
    ::= { cMediaGwIpConfigEntry 2 }


cmgwIpConfigVpi    OBJECT-TYPE
    SYNTAX         Integer32 (-1..4095)
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION
        "This object represents VPI of the PVC which is associated
         to the IP address.
         If the IP address is not associated to PVC, the value 
         of this object is set to -1."
    ::= { cMediaGwIpConfigEntry 3 }

cmgwIpConfigVci    OBJECT-TYPE
    SYNTAX         Integer32 (-1..65535)
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION
        "This object represents VCI of the PVC which is associated
         to the IP address.
         If the IP address is not associated to PVC, the value
         of this object is set to -1."
    ::= { cMediaGwIpConfigEntry 4 }

cmgwIpConfigAddrType   OBJECT-TYPE
    SYNTAX             InetAddressType
    MAX-ACCESS         read-create 
    STATUS             current
    DESCRIPTION
        "This object is the IP address type.
        " 
    DEFVAL { ipv4 }
    ::= { cMediaGwIpConfigEntry 5 }

cmgwIpConfigAddress   OBJECT-TYPE
    SYNTAX            InetAddress 
    MAX-ACCESS        read-create
    STATUS            current
    DESCRIPTION
        "The configured IP address of media gateway.
         This object can not be modified.
        "
    ::= { cMediaGwIpConfigEntry 6 }


cmgwIpConfigSubnetMask   OBJECT-TYPE
    SYNTAX               InetAddressPrefixLength
    MAX-ACCESS           read-create
    STATUS               current
    DESCRIPTION
        "This object is used to specify the number of leading one   
         bits which from the mask to be logical-ANDed with the media  
         gateway address before being compared to the value in the 
         cmgwIpCofigAddress.

         Any assignment (implicit or otherwise) of an instance of
         this object to a value x must be rejected if the bitwise
         logical-AND of the mask formed from x with the value 
         of the corresponding instance of the cmgwIpCofigAddress 
         object is not equal to cmgwIpCofigAddress."
    ::= { cMediaGwIpConfigEntry 7 }

cmgwIpConfigDefaultGwIp  OBJECT-TYPE
    SYNTAX               TruthValue
    MAX-ACCESS           read-create
    STATUS               current
    DESCRIPTION
        "This object specifies cmgwIpConfigAddress of the entry
         will become the default gateway address.
         This object can be set to 'true' for only one entry in
         the table."
    DEFVAL { false }
    ::= { cMediaGwIpConfigEntry 8 }

cmgwIpConfigForRemoteMapping  OBJECT-TYPE
    SYNTAX               TruthValue
    MAX-ACCESS           read-create
    STATUS               current
    DESCRIPTION
        "This object specifies whether the address defined in
         cmgwIpConfigAddress is the address mapping at the
         remote end of this PVC. 

         If this object is set to 'true', the address defined
         in cmgwIpConfigAddress is for the remote end of the PVC.
         If this object is set to 'false', the address defined
         in cmgwIpConfigAddress is for the local end of the PVC."
    DEFVAL { false }
    ::= { cMediaGwIpConfigEntry 9 }

cmgwIpConfigRowStatus OBJECT-TYPE
    SYNTAX            RowStatus 
    MAX-ACCESS        read-create
    STATUS            current
    DESCRIPTION
        "This object is used to add and delete an entry.

         When an entry of the table is created, the following 
         objects are mandatory:
             cmgwIpConfigIfIndex
             cmgwIpConfigVpi
             cmgwIpConfigVci
             cmgwIpConfigAddress
             cmgwIpConfigSubnetMask

         These objects can not be modified after the value of this
         object is set to 'active'. 
         Modification can only be done by deleting and re-adding the 
         entry again.

         After the system verify the validity of the data, it
         will set the cmgwIpConfigRowStatus to 'active'." 
    ::= { cMediaGwIpConfigEntry 10 }


-- *********************************************************************
-- cMediaGwDomainNameConfigTable 
-- *********************************************************************

cMediaGwDomainNameConfigTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF CMediaGwDomainNameConfigEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "This table provides the domain names which are configured by 
         users. 
         The domain names can be used to represent IP addresses 
         for:
             gateway
             External DNS name server
             MGC (call agent) "
    ::= { cMediaGwConfig 4 }

 
cMediaGwDomainNameConfigEntry    OBJECT-TYPE
    SYNTAX  CMediaGwDomainNameConfigEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Each entry represents a domain name used in the system.

         Creation and deletion are supported. Modification
         is prohibited."
    INDEX       { cmgwIndex, cmgwConfigDomainNameIndex }
    ::= { cMediaGwDomainNameConfigTable 1 }


CMediaGwDomainNameConfigEntry ::=
    SEQUENCE{
              cmgwConfigDomainNameIndex          Integer32,
              cmgwConfigDomainNameEntity         INTEGER,
              cmgwConfigDomainName               SnmpAdminString,
              cmgwConfigDomainNameRowStatus      RowStatus
             }

cmgwConfigDomainNameIndex     OBJECT-TYPE
    SYNTAX        Integer32(1..128)
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "An index that is uniquely identifies a domain name
         configured in the system."
    ::= {cMediaGwDomainNameConfigEntry  1}

cmgwConfigDomainNameEntity OBJECT-TYPE
    SYNTAX        INTEGER {
                      gateway   (1),
                      dnsServer (2),
                      mgc       (3)
                  }

    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "This object indicates which entity to use this domain name.

         gateway(1)   - The domain name of media gateway.
                        With the same cmgwIndex, there is one and 
                        only one entry allowed with the value 
                        'gateway(1)' of this object.

         dnsServer(2) - The domain name of DNS name server that is used 
                        by Media gateway to find Internet Network 
                        Address from a DNS name.

         mgc(3)       - The domain name of a MGC (Media Gateway
                        Controller) associated with the media 
                        gateway. "
    ::= {cMediaGwDomainNameConfigEntry  2}

cmgwConfigDomainName     OBJECT-TYPE
    SYNTAX        SnmpAdminString (SIZE (1..64))
    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "This object specifies the domain name.

         The domain name should be unique if there are more than
         one entries having the same value in the object 
         cmgwConfigDomainNameEntity.
         For example, the gateway domain name should be unique 
         if the cmgwConfigDomainNameEntity has the value of 
         'gateway(1)'."
    ::= {cMediaGwDomainNameConfigEntry  3}

cmgwConfigDomainNameRowStatus     OBJECT-TYPE
    SYNTAX        RowStatus
    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "This object is used to add and delete an entry.

         When an entry is created, the following objects
         are mandatory:
              cmgwConfigDomainName
              cmgwConfigDomainNameEntity

         When deleting domain name of DNS name server
         (cmgwConfigDomainNameEntity is dnsServer (2)), the 
         cMediaGwDnsIpConfigTable should be empty.

         Adding/deleting entry with cmgwConfigDomainNameEntity
         of 'mgc' will cause adding/deleting entry in 
         cMgcConfigTable (CISCO-MGC-MIB) automatically.

         The cmgwConfigDomainName and cmgwConfigDomainNameEntity
         can not be modified if the value of this object is
         'active'. "
    ::= {cMediaGwDomainNameConfigEntry  4}

-- *********************************************************************
-- cMediaGwDnsIpConfigTable 
-- *********************************************************************

cMediaGwDnsIpConfigTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF CMediaGwDnsIpConfigEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "There is only one DNS name server on a gateway
         and the domain name of the DNS name server is put on 
         cMediaGwDomainNameConfigTable with 'dnsServer (2)'.

         There could be multi IP addresses are associated with the
         DNS name server, this table is used to store these IP 
         addresses.

         If any domain name using external resolution, the last entry
         of this table is not allowed to be deleted."
    ::= { cMediaGwConfig 5 }

 
cMediaGwDnsIpConfigEntry    OBJECT-TYPE
    SYNTAX  CMediaGwDnsIpConfigEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Each entry represents an IP address of the DNS name 
         server."
    INDEX       { cmgwIndex, cmgwDnsIpIndex }
    ::= { cMediaGwDnsIpConfigTable 1 }

CMediaGwDnsIpConfigEntry ::=
    SEQUENCE{
              cmgwDnsIpIndex          Integer32,
              cmgwDnsDomainName       SnmpAdminString, 
              cmgwDnsIpType           InetAddressType,
              cmgwDnsIp               InetAddress,
              cmgwDnsIpRowStatus      RowStatus
             }

cmgwDnsIpIndex    OBJECT-TYPE
    SYNTAX        Integer32(1..6)
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "An index that uniquely identifies an IP address of DNS
         name server."
    ::= {cMediaGwDnsIpConfigEntry  1}

cmgwDnsDomainName OBJECT-TYPE
    SYNTAX        SnmpAdminString (SIZE (1..64))
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The domain name of DNS name server.

         The value of this object reflects the value of 
         cmgwConfigDomainName from the entry with a value of 
         'dnsServer(2)' for object cmgwConfigDomainNameEntity of 
         cMediaGwDomainNameConfigTable.
 
         If there is no entry in cMediaGwDomainNameConfigTable with
         'dnsServer(2)' of cmgwConfigDomainNameEntity, then
         the value of this object will be empty string."
    ::= {cMediaGwDnsIpConfigEntry  2}

cmgwDnsIpType     OBJECT-TYPE
    SYNTAX        InetAddressType
    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "DNS name server IP address type."
    DEFVAL { ipv4 }
    ::= {cMediaGwDnsIpConfigEntry  3}

cmgwDnsIp         OBJECT-TYPE
    SYNTAX        InetAddress
    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "The IP address of DNS name server.
         The IP address of DNS name server must be unique
         in this table."
    ::= {cMediaGwDnsIpConfigEntry  4}

cmgwDnsIpRowStatus     OBJECT-TYPE
    SYNTAX        RowStatus
    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "This object is used to add and delete an entry.

         When an entry of the table is created, the value of
         this object should be set to 'createAndGo' and the
         following objects are mandatory:
             cmgwDnsIp

         When the user wants to delete the entry, the value of
         this object should be set to 'destroy'.

         The entry can not be modified if the value of this 
         object is 'active'."
    ::= {cMediaGwDnsIpConfigEntry  5}

-- *********************************************************************
--
-- A LIF (Logical InterFace) is a group of TDM ports 
-- (DSx1 lines) associated with a set of PVCs. 
--
-- *********************************************************************

cmgwLifTable       OBJECT-TYPE
    SYNTAX         SEQUENCE OF CmgwLifEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "This table is for managing LIF (Logical Interface) 
         in a media gateway. 

         LIF is a logical interface which groups the TDM 
         DSx1s associated with a set of packet resource partitions 
         (PVCs) in a media gateway.

         LIF is used for:
         1. VoIP switching 
         2. VoATM switching " 

    ::= { cMediaGwConfig 6 }

cmgwLifEntry       OBJECT-TYPE
    SYNTAX         CmgwLifEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "An entry of this table is created by the media gateway
         when it supports the VoIP/VoATM application."
    INDEX       { cmgwIndex, cmgwLifNumber }
    ::= { cmgwLifTable 1 }

CmgwLifEntry::= SEQUENCE  {
    cmgwLifNumber        GatewayLifNumber,
    cmgwLifPvcCount      Gauge32,  
    cmgwLifVoiceIfCount  Gauge32
}

cmgwLifNumber      OBJECT-TYPE
    SYNTAX         GatewayLifNumber
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "An index that uniquely identifies a LIF in the 
         media gateway."
    ::= { cmgwLifEntry 1 }

cmgwLifPvcCount     OBJECT-TYPE
    SYNTAX          Gauge32(0..10000)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of PVC within 
         this LIF.

         When users associate/disassociate a PVC with a LIF 
         by giving a non-zero/zero value of cwacChanLifNum
         in cwAtmChanExtConfigTable, the value of this object 
         will be incremented/decremented accordingly.

         The value zero means there is no PVC associated with 
         the LIF."
    ::= { cmgwLifEntry 2 }

cmgwLifVoiceIfCount OBJECT-TYPE
    SYNTAX          Gauge32(0..1000)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object represents the total number of Voice Interfaces
         within this LIF.

         When users associate/disassociate a Voice Interface with
         a LIF by giving a non-zero/zero value of 
         ccasVoiceCfgLifNumber for the DS0 group in 
         ccasVoiceExtCfgTable, the value of this object will be 
         incremented/decremented accordingly. 

         The value zero means there is no Voice Interface associated
         with the LIF."
    ::= { cmgwLifEntry 3 }

-- ********************************************************************  
-- 
-- cMediaGwCallControlConfigTable 
-- 
-- ********************************************************************  
cMediaGwCallControlConfigTable OBJECT-TYPE 
    SYNTAX        SEQUENCE OF CMediaGwCallControlConfigEntry 
    MAX-ACCESS    not-accessible 
    STATUS        current 
    DESCRIPTION 
        "This table defines general call control attributes for
         the media gateway."
    ::= { cMediaGwConfig 7 } 
      
cMediaGwCallControlConfigEntry OBJECT-TYPE 
    SYNTAX        CMediaGwCallControlConfigEntry 
    MAX-ACCESS    not-accessible 
    STATUS        current 
    DESCRIPTION 
        "One entry for each media gateway which supports call control 
         protocol."
    INDEX       { cmgwIndex } 
    ::= { cMediaGwCallControlConfigTable 1 } 
      
CMediaGwCallControlConfigEntry ::= SEQUENCE 
    { 
    cMediaGwCcCfgControlTos         Unsigned32,
    cMediaGwCcCfgBearerTos          Unsigned32,
    cMediaGwCcCfgNtePayload         Unsigned32,
    cMediaGwCcCfgNsePayload         Unsigned32,
    cMediaGwCcCfgNseRespTimer       Unsigned32,
    cMediaGwCcCfgVbdJitterDelayMode CCallControlJitterDelayMode,
    cMediaGwCcCfgVbdJitterMaxDelay  Unsigned32,
    cMediaGwCcCfgVbdJitterNomDelay  Unsigned32,
    cMediaGwCcCfgVbdJitterMinDelay  Unsigned32,
    cMediaGwCcCfgDefaultTonePlanId  CVoiceTonePlanIndex,
    cMediaGwCcCfgDescrInfoEnabled   TruthValue,
    cMediaGwCcCfgDsNamePrefix       SnmpAdminString,
    cMediaGwCcCfgRtpNamePrefix      SnmpAdminString,
    cMediaGwCcCfgAal1SvcNamePrefix  SnmpAdminString,
    cMediaGwCcCfgAal2SvcNamePrefix  SnmpAdminString,
    cMediaGwCcCfgClusterEnabled     INTEGER,
    cMediaGwCcCfgDefBearerTraffic   INTEGER,
    cMediaGwCcCfgDefRtpNamePrefix   SnmpAdminString 
    }

cMediaGwCcCfgControlTos OBJECT-TYPE 
    SYNTAX        Unsigned32 (0..255)
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies Type Of Service (TOS) field of
         IP header for the signaling control packet in VoIP
         application."
    DEFVAL { 96 }
    ::= { cMediaGwCallControlConfigEntry 1 }

cMediaGwCcCfgBearerTos  OBJECT-TYPE 
    SYNTAX        Unsigned32 (0..255)
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies Type Of Service (TOS) field
         of IP header for the voice payload packet in VoIP
         application."
    DEFVAL { 160 }
    ::= { cMediaGwCallControlConfigEntry 2 }

cMediaGwCcCfgNtePayload OBJECT-TYPE 
    SYNTAX        Unsigned32 (96..127)
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies NTE (Named Telephony Events)
         payload type."
    REFERENCE
        "RFC2833, 3. RTP Payload Format for Named Telephone Events"
    DEFVAL { 101 }
    ::= { cMediaGwCallControlConfigEntry 3 }

cMediaGwCcCfgNsePayload OBJECT-TYPE 
    SYNTAX        Unsigned32 (98..117)
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies NSE (Network Signaling Events)
         payload type."
    DEFVAL { 100 }
    ::= { cMediaGwCallControlConfigEntry 4 }

cMediaGwCcCfgNseRespTimer OBJECT-TYPE 
    SYNTAX        Unsigned32 (250..10000)
    UNITS         "milliseconds"
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies Network Signaling Event (NSE)
         timeout value."
    DEFVAL { 1000 }
    ::= { cMediaGwCallControlConfigEntry 5 }

cMediaGwCcCfgVbdJitterDelayMode OBJECT-TYPE 
    SYNTAX        CCallControlJitterDelayMode 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "The object specifies the jitter buffer mode applied to
         a VBD (Voice Band Data) call connection.

         adaptive - means to use cMediaGwCcCfgVbdJitterNomDelay as
                    the initial jitter buffers size and let the DSP
                    pick the optimal value of the jitter buffer
                    size between the range of
                    cMediaGwCcCfgVbcJitterMaxDelay and
                    cMediaGwCcCfgVbcJitterMinDelay.

         fixed - means to use a constant jitter buffer size
                 which is specified by cMediaGwCcCfgVbdJitterNomDelay.
        "
    DEFVAL { fixed }
    ::= { cMediaGwCallControlConfigEntry 6 }

cMediaGwCcCfgVbdJitterMaxDelay OBJECT-TYPE 
    SYNTAX        Unsigned32 (20..135)
    UNITS         "milliseconds"
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the maximum jitter buffer size 
         in VBD (Voice Band Data)"
    DEFVAL { 135 }
    ::= { cMediaGwCallControlConfigEntry 7 }

cMediaGwCcCfgVbdJitterNomDelay OBJECT-TYPE 
    SYNTAX        Unsigned32 (5..135)
    UNITS         "milliseconds"
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the nominal jitter buffer size 
         in VBD (Voice Band Data)"
    DEFVAL { 70 }
    ::= { cMediaGwCallControlConfigEntry 8 }

cMediaGwCcCfgVbdJitterMinDelay OBJECT-TYPE 
    SYNTAX        Unsigned32 (0..135)
    UNITS         "milliseconds"
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the nominal jitter buffer size 
         in VBD (Voice Band Data)"
    DEFVAL { 0 }
    ::= { cMediaGwCallControlConfigEntry 9 }

cMediaGwCcCfgDefaultTonePlanId OBJECT-TYPE 
    SYNTAX        CVoiceTonePlanIndex 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the default tone plan index
         (the value of cvtcTonePlanId) for the media gateway."
    DEFVAL { 1 }
    ::= { cMediaGwCallControlConfigEntry 10 }

cMediaGwCcCfgDescrInfoEnabled OBJECT-TYPE 
    SYNTAX        TruthValue 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies whether the media gateway supports
         descriptive suffix of the name schema for terminations.

         There are two parts in name schema of termination, prefix
         and suffix. For example the name schema for a DS (Digital
         Subscriber) termination, can be 'DS/OC3_2/DS1_6/DS0_24'.
         It represents DS type termination in 2nd OC3 line, 
         6th DS1 and 24th DS0 channel. In this example, 'DS' is 
         the prefix, 'OC3_2/DS1_6/DS0_24' is the suffix.

         The name schema in above example has a descriptive suffix.
         The non-descriptive suffix for the same termination is 
         '2/6/24' and name schema becomes 'DS/2/6/24'.

         This object can not be modified if there is any termination
         existing in the media gateway."
    DEFVAL { false }
    ::= { cMediaGwCallControlConfigEntry 11 }

cMediaGwCcCfgDsNamePrefix OBJECT-TYPE 
    SYNTAX        SnmpAdminString (SIZE (1..64)) 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the prefix of the name schema for
         DS (Digital Subscriber) terminations.
         The value of this object must be unique among the 
         following objects:
                cMediaGwCcCfgDsNamePrefix
                cMediaGwCcCfgRtpNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgDefRtpNamePrefix
         This object can not be modified when there is any
         DS termination existing in the media gateway.
         It is default to 'DS'."
    DEFVAL { '4453'H }
    ::= { cMediaGwCallControlConfigEntry 12 }

cMediaGwCcCfgRtpNamePrefix OBJECT-TYPE 
    SYNTAX        SnmpAdminString (SIZE (1..64)) 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the prefix of the name schema for
         RTP (Real-Time Transport Protocol) terminations.
         The value of this object must be unique among the 
         following objects:
                cMediaGwCcCfgDsNamePrefix
                cMediaGwCcCfgRtpNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgDefRtpNamePrefix
         This object can not be modified when there is any
         RTP termination type existing in the media gateway.
         It is default to 'RTP'."
    DEFVAL { '525450'H }
    ::= { cMediaGwCallControlConfigEntry 13 }

cMediaGwCcCfgAal1SvcNamePrefix OBJECT-TYPE 
    SYNTAX        SnmpAdminString (SIZE (1..64)) 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the prefix of the name schema for
         voice over AAL1 SVC (Switched Virtual Circuit)
         terminations.
         The value of this object must be unique among the 
         following objects:
                cMediaGwCcCfgDsNamePrefix
                cMediaGwCcCfgRtpNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgDefRtpNamePrefix
         This object can not be modified when there is any
         AAL1 SVC termination type existing in the media gateway.
         It is default to 'AAL1/SVC'."
    DEFVAL { '41414C312F535643'H }
    ::= { cMediaGwCallControlConfigEntry 14 }

cMediaGwCcCfgAal2SvcNamePrefix OBJECT-TYPE 
    SYNTAX        SnmpAdminString (SIZE (1..64)) 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the prefix of the name schema for
         voice over AAL2 SVC (Switched Virtual Circuit)
         terminations.
         The value of this object must be unique among the 
         following objects:
                cMediaGwCcCfgDsNamePrefix
                cMediaGwCcCfgRtpNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix
                cMediaGwCcCfgDefRtpNamePrefix
         This object can not be modified when there is any
         AAL2 SVC termination type existing in the media gateway.
         It is default to 'AAL2/SVC'."
    DEFVAL { '41414C322F535643'H }
    ::= { cMediaGwCallControlConfigEntry 15 }

cMediaGwCcCfgClusterEnabled OBJECT-TYPE 
    SYNTAX        INTEGER { 
                     disabled              (1),
                     enabled               (2),
                     conditionalEnabled    (3)
                  }
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the condition of the cluster generation
         in the call control.
         
         A cluster is a group of endpoints that share a particular
         bearer possibility for connections among each other.

         disabled(1) - The generation of the cluster attribute
                       is disabled.
         enabled(2) - Unconditionally generate the cluster
                      attribute.
         conditionalEnabled(3) - The generation of the cluster 
                       attribute is upon MGC request.
        "
    DEFVAL { disabled }
    ::= { cMediaGwCallControlConfigEntry 16 }


cMediaGwCcCfgDefBearerTraffic    OBJECT-TYPE
    SYNTAX                    INTEGER {
                                  ipPvcAal5 (1),
                                  atmPvcAal2 (2),
                                  atmSvcAal2 (3),
                                  atmSvcAal1 (4)
                              }
    MAX-ACCESS                read-write
    STATUS                    current
    DESCRIPTION
        "This object specifies the combination of the network
         type (IP/ATM), virtual circuit type (PVC/SVC) and
         ATM adaptation layer type (AAL1/AAL2/AAL5) for the
         connection used in transporting bearer traffic.

             ipPvcAal5 (1) - The bearer traffic is transported in
                             IP network, through Permanent Virtual
                             Circuit(PVC) over AAL5 adaptation layer.
             atmPvcAal2 (2) - The bearer traffic is transported in
                              ATM network, through Permanent Virtual
                              Circuit(PVC) over AAL2 adaptation layer.
             atmSvcAal2 (3) - The bearer traffic is transported in
                              ATM network, through Switching Virtual
                              Circuit(SVC) over AAL2 adaptation layer.
             atmSvcAal1 (4) - The bearer traffic is transported in
                              ATM network, through Switching Virtual
                              Circuit(SVC) over AAL1 adaptation layer.

         In MGCP, if the call agent specifies the bear traffic type 
         in the local connection options (CRCX request), 
         configuration of this object will have no effect, 
         otherwise the value of this object will be used when 
         media gateway sending CRCX response."
    DEFVAL { ipPvcAal5 }
    ::= { cMediaGwCallControlConfigEntry 17 }

cMediaGwCcCfgDefRtpNamePrefix OBJECT-TYPE 
    SYNTAX        SnmpAdminString (SIZE (1..64)) 
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "This object specifies the prefix of the name schema for
         default RTP terminations.
         The value of this object must be unique among the 
         following objects:
                cMediaGwCcCfgDsNamePrefix
                cMediaGwCcCfgRtpNamePrefix
                cMediaGwCcCfgAal1SvcNamePrefix
                cMediaGwCcCfgAal2SvcNamePrefix

         It is defaulted to 'TGWRTP'."
    DEFVAL { '544757525450'H }
    ::= { cMediaGwCallControlConfigEntry 18 }
    



--
-- GW resource statistics table
--

cMediaGwRscStatsTable  OBJECT-TYPE
    SYNTAX         SEQUENCE OF CMediaGwRscStatsEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "This table stores the gateway resource statistics
         information.
        "
    ::= { cMediaGwStats 1 }

cMediaGwRscStatsEntry  OBJECT-TYPE
    SYNTAX         CMediaGwRscStatsEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "Each entry stores the statistics
         information for a specific resource.
        "
    INDEX       { cmgwIndex,  cmgwRscStatsIndex}
    ::= { cMediaGwRscStatsTable 1 }

CMediaGwRscStatsEntry::= SEQUENCE {
         cmgwRscStatsIndex             INTEGER,
         cmgwRscMaximumUtilization     Gauge32,
         cmgwRscMinimumUtilization     Gauge32,
         cmgwRscAverageUtilization     Gauge32,
         cmgwRscSinceLastReset         Unsigned32
         }

cmgwRscStatsIndex  OBJECT-TYPE
    SYNTAX        INTEGER {
                  cpu             (1),
                  staticmemory    (2),
                  dynamicmemory   (3),
                  sysmemory       (4),
                  commbuffer      (5),
                  msgq            (6),
                  atmq            (7),
                  svccongestion   (8),
                  rsvpq           (9),
                  dspq            (10),
                  h248congestion  (11),
                  callpersec      (12),
                  smallipcbuffer  (13),
                  mediumipcbuffer (14),
                  largeipcbuffer  (15),
                  hugeipcbuffer   (16),
                  mblkipcbuffer   (17)
              }
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION
        "An index that uniquely identifies a specific gateway
         resource.
        "
    ::= { cMediaGwRscStatsEntry 1 }


cmgwRscMaximumUtilization  OBJECT-TYPE
    SYNTAX         Gauge32 
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "This object indicates the maximum utilization of the
         resource over the interval specified by the
         'cmgwRscSinceLastReset'.
        "
    ::= { cMediaGwRscStatsEntry 2 }

cmgwRscMinimumUtilization  OBJECT-TYPE
    SYNTAX         Gauge32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "This object indicates the minimum utilization of the
         resource over the interval specified by the
         'cmgwRscSinceLastReset'.
        "
    ::= { cMediaGwRscStatsEntry 3 }

cmgwRscAverageUtilization  OBJECT-TYPE
    SYNTAX         Gauge32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "This object indicates the average utilization of the
         resource over the interval specified by the
         'cmgwRscSinceLastReset'.
        "
    ::= { cMediaGwRscStatsEntry 4 }


cmgwRscSinceLastReset  OBJECT-TYPE
    SYNTAX         Unsigned32
    UNITS          "seconds"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "The elapsed time (in seconds) from the last periodic reset.

         The following objects are reset at the last reset:

             'cmgwRscMaximumUtilization'
             'cmgwRscMinimumUtilization'
             'cmgwRscAverageUtilization'
        "
    ::= { cMediaGwRscStatsEntry 5 }


--**********************************************************************
--Conformance 
--**********************************************************************

cMediaGwMIBConformance
     OBJECT IDENTIFIER ::= { ciscoMediaGatewayMIB 2 }

cMediaGwMIBCompliances
     OBJECT IDENTIFIER ::= { cMediaGwMIBConformance 1 }

cMediaGwMIBGroups
     OBJECT IDENTIFIER ::= { cMediaGwMIBConformance 2 }


--
--Conformance and compliance statements
--

cMediaGwMIBCompliance  MODULE-COMPLIANCE
    STATUS  deprecated
    DESCRIPTION
        "The compliance statement for the SNMP entities which implement
         Media Gateway MIB.
         This has been replaced by cMediaGwMIBComplianceRev1"
    MODULE  -- this module
    MANDATORY-GROUPS { 
        cMediaGwGroup
    }

    GROUP cmgwSignalProtocolGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more signaling protocol stacks."

    GROUP cmgwDomainNameGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more domain name."

    GROUP cMediaGwIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more IP address."

    GROUP cmgwDnsIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         DNS name server."

    GROUP cmgwLifGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         LIF (logical interface)."

    GROUP cmgwCallControlGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         any signaling protocol."

    OBJECT  cmgwIpConfigAddrType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwIpConfigAddress
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"

    OBJECT  cmgwDnsIpType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwDnsIp
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"


    ::= { cMediaGwMIBCompliances 1 }

cMediaGwMIBComplianceRev1  MODULE-COMPLIANCE
    STATUS  deprecated
    DESCRIPTION
        "The compliance statement for the SNMP entities which implement
         Media Gateway MIB.
         This has been replaced by cMediaGwMIBComplianceRev2."
    MODULE  -- this module
    MANDATORY-GROUPS { 
        cMediaGwGroupRev1
    }

    GROUP cmgwSignalProtocolGroupRev1
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more signaling protocol stacks."

    GROUP cmgwDomainNameGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more domain name."

    GROUP cMediaGwIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more IP address."

    GROUP cmgwDnsIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         DNS name server."

    GROUP cmgwLifGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         LIF (logical interface)."
    
    GROUP cmgwCallControlGroupRev1
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         any signaling protocol."


    OBJECT  cmgwIpConfigAddrType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwIpConfigAddress
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"

    OBJECT  cmgwDnsIpType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwDnsIp
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"

    ::= { cMediaGwMIBCompliances 2 }

cMediaGwMIBComplianceRev2  MODULE-COMPLIANCE
    STATUS  deprecated -- by cMediaGwMIBComplianceRev3 
    DESCRIPTION
        "The compliance statement for the SNMP entities which implement
         Media Gateway MIB."
    MODULE  -- this module
    MANDATORY-GROUPS { 
        cMediaGwGroupRev1
    }

    GROUP cmgwSignalProtocolGroupRev2
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more signaling protocol stacks."

    GROUP cmgwDomainNameGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more domain name."

    GROUP cMediaGwIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more IP address."

    GROUP cmgwDnsIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         DNS name server."

    GROUP cmgwLifGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         LIF (logical interface)."

    GROUP cmgwCallControlGroupRev1
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         any signaling protocol."

    OBJECT  cmgwIpConfigAddrType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwIpConfigAddress
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"

    OBJECT  cmgwDnsIpType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwDnsIp
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"

    ::= { cMediaGwMIBCompliances 3 }

cMediaGwMIBComplianceRev3  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for the SNMP entities which implement
         Media Gateway MIB."
    MODULE  -- this module
    MANDATORY-GROUPS { 
        cMediaGwGroupRev1,
        cMediaGwGroupExtra
    }

    GROUP cmgwSignalProtocolGroupRev2
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more signaling protocol stacks."

    GROUP cmgwSignalProtocolGroupRev3
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more signaling protocol stacks."

    GROUP cmgwDomainNameGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more domain name."

    GROUP cMediaGwIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         one or more IP address."

    GROUP cmgwDnsIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         DNS name server."

    GROUP cmgwLifGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         LIF (logical interface)."

    GROUP cmgwCallControlGroupRev1
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
         any signaling protocol."

    GROUP  cMediaGwRscStatsGroup
    DESCRIPTION
       "This group is mandatory for a media gateway which supports
        gateway resource statistics."

    OBJECT  cmgwIpConfigAddrType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwIpConfigAddress
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"

    OBJECT  cmgwDnsIpType
--  SYNTAX  InetAddressType { ipv4(1) }
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address type"

    OBJECT  cmgwDnsIp
    SYNTAX  InetAddress (SIZE(4))
    DESCRIPTION
       "The minimal requirement for supporting this object is 'ipv4' 
        address"

    ::= { cMediaGwMIBCompliances 4 }

cMediaGwMIBComplianceRev4 MODULE-COMPLIANCE
    STATUS  deprecated -- by cMediaGwMIBComplianceRev5
    DESCRIPTION
        "The compliance statement for the SNMP entities which implement
        Media Gateway MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cMediaGwGroupRev1,
                        cMediaGwGroupExtra
                    }

    GROUP           cmgwSignalProtocolGroupRev2
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more signaling protocol stacks."

    GROUP           cmgwSignalProtocolGroupRev3
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more signaling protocol stacks."

    GROUP           cmgwDomainNameGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more domain name."

    GROUP           cMediaGwIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more IP address."
      GROUP           cmgwDnsIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        DNS name server."

    GROUP           cmgwLifGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        LIF (logical interface)."

    GROUP           cmgwCallControlGroupRev2
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        any signaling protocol."

    GROUP           cMediaGwRscStatsGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        gateway resource statistics."
   
     OBJECT          cmgwIpConfigAddrType
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address type"

    OBJECT          cmgwIpConfigAddress
    SYNTAX          InetAddress (SIZE (4))
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address"

    OBJECT          cmgwDnsIpType
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address type"

    OBJECT          cmgwDnsIp
    SYNTAX          InetAddress (SIZE (4))
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address"
    ::= { cMediaGwMIBCompliances 5 }


cMediaGwMIBComplianceRev5 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the SNMP entities which implement
        Media Gateway MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cMediaGwGroupRev1,
                        cMediaGwGroupExtra
                     }

     GROUP           cMediaGwGroupRev2
    DESCRIPTION
        "This group is mendatory for media gateway which supports
        Enable/Disable V23 mode at gateway level."

    GROUP           cmgwSignalProtocolGroupRev2
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more signaling protocol stacks."

    GROUP           cmgwSignalProtocolGroupRev3
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more signaling protocol stacks."

    GROUP           cmgwDomainNameGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more domain name."

    GROUP           cMediaGwIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        one or more IP address."

    GROUP           cmgwDnsIpGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        DNS name server."

    GROUP           cmgwLifGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        LIF (logical interface)."

    GROUP           cmgwCallControlGroupRev2
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        any signaling protocol."

    GROUP           cMediaGwRscStatsGroup
    DESCRIPTION
        "This group is mandatory for a media gateway which supports
        gateway resource statistics."

    OBJECT          cmgwIpConfigAddrType
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address type"

    OBJECT          cmgwIpConfigAddress
    SYNTAX          InetAddress (SIZE (4))
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address"

    OBJECT          cmgwDnsIpType
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address type"

    OBJECT          cmgwDnsIp
    SYNTAX          InetAddress (SIZE (4))
    DESCRIPTION
        "The minimal requirement for supporting this object is 'ipv4'
        address"
    ::= { cMediaGwMIBCompliances 6 }
    
--
--units of conformance
--
--MIB Groups
--

cMediaGwGroup  OBJECT-GROUP
    OBJECTS {
        cmgwDomainName,
        cmgwPhysicalIndex,
        cmgwServiceState, 
        cmgwAdminState,
        cmgwGraceTime
    }
    STATUS    deprecated
    DESCRIPTION
        "This group contains objects that apply to the media gateway
         configuration table."
    ::= { cMediaGwMIBGroups 1 }

cmgwSignalProtocolGroup  OBJECT-GROUP
    OBJECTS {
        cmgwSignalProtocol,
        cmgwSignalProtocolVersion,
        cmgwSignalProtocolPort
    }
    STATUS    deprecated
    DESCRIPTION
        "A collection of objects providing signaling  
         protocol information a media gateway."
    ::= { cMediaGwMIBGroups 2 }


cmgwDomainNameGroup  OBJECT-GROUP
    OBJECTS {
        cmgwConfigDomainNameEntity,
        cmgwConfigDomainName,
        cmgwConfigDomainNameRowStatus
    }
    STATUS    current
    DESCRIPTION
        "A collection of objects providing DNS name 
         configuration for a media gateway."
    ::= { cMediaGwMIBGroups 3 }

cMediaGwIpGroup  OBJECT-GROUP
    OBJECTS {
        cmgwIpConfigIfIndex,
        cmgwIpConfigVpi,
        cmgwIpConfigVci,
        cmgwIpConfigAddrType,
        cmgwIpConfigAddress,
        cmgwIpConfigSubnetMask,
        cmgwIpConfigDefaultGwIp,
        cmgwIpConfigForRemoteMapping,
        cmgwIpConfigRowStatus
    }
    STATUS    current
    DESCRIPTION
        "A collection of objects providing IP address 
         configuration for a media gateway."
    ::= { cMediaGwMIBGroups 4 }

cmgwDnsIpGroup  OBJECT-GROUP
    OBJECTS {
        cmgwDnsDomainName,
        cmgwDnsIp,
        cmgwDnsIpType,
        cmgwDnsIpRowStatus
    }
    STATUS    current
    DESCRIPTION
        "A collection of objects providing DSN name server 
         IP address configuration for a media gateway."
    ::= { cMediaGwMIBGroups 5 }

cmgwLifGroup  OBJECT-GROUP
    OBJECTS {
        cmgwLifPvcCount,  
        cmgwLifVoiceIfCount
    }
    STATUS    current
    DESCRIPTION
        "A collection of objects providing LIF(logical interface)
         information for a media gateway."
    ::= { cMediaGwMIBGroups 6 }

cmgwCallControlGroup  OBJECT-GROUP
    OBJECTS {
        cMediaGwCcCfgControlTos,
        cMediaGwCcCfgBearerTos,
        cMediaGwCcCfgNtePayload,
        cMediaGwCcCfgNsePayload,
        cMediaGwCcCfgNseRespTimer,
        cMediaGwCcCfgVbdJitterDelayMode,
        cMediaGwCcCfgVbdJitterMaxDelay,
        cMediaGwCcCfgVbdJitterNomDelay,
        cMediaGwCcCfgVbdJitterMinDelay,
        cMediaGwCcCfgDefaultTonePlanId,
        cMediaGwCcCfgDescrInfoEnabled,
        cMediaGwCcCfgDsNamePrefix,
        cMediaGwCcCfgRtpNamePrefix,
        cMediaGwCcCfgAal1SvcNamePrefix,
        cMediaGwCcCfgAal2SvcNamePrefix,
        cMediaGwCcCfgClusterEnabled
    }
    STATUS    deprecated
    DESCRIPTION
        "A collection of objects providing general call control  
         information in a media gateway."
    ::= { cMediaGwMIBGroups 7 }

cMediaGwGroupRev1  OBJECT-GROUP
    OBJECTS {
        cmgwDomainName,
        cmgwPhysicalIndex,
        cmgwServiceState, 
        cmgwAdminState,
        cmgwGraceTime,
        cmgwVtMappingMode
    }
    STATUS    current
    DESCRIPTION
        "This group replaces cMediaGwGroup.
         It contains objects that apply to the media gateway
         configuration table."
    ::= { cMediaGwMIBGroups 8 }

cmgwCallControlGroupRev1  OBJECT-GROUP
    OBJECTS {
        cMediaGwCcCfgControlTos,
        cMediaGwCcCfgBearerTos,
        cMediaGwCcCfgNtePayload,
        cMediaGwCcCfgNsePayload,
        cMediaGwCcCfgNseRespTimer,
        cMediaGwCcCfgVbdJitterDelayMode,
        cMediaGwCcCfgVbdJitterMaxDelay,
        cMediaGwCcCfgVbdJitterNomDelay,
        cMediaGwCcCfgVbdJitterMinDelay,
        cMediaGwCcCfgDefaultTonePlanId,
        cMediaGwCcCfgDescrInfoEnabled,
        cMediaGwCcCfgDsNamePrefix,
        cMediaGwCcCfgRtpNamePrefix,
        cMediaGwCcCfgAal1SvcNamePrefix,
        cMediaGwCcCfgAal2SvcNamePrefix,
        cMediaGwCcCfgClusterEnabled,
        cMediaGwCcCfgDefBearerTraffic
    }
    STATUS    current
    DESCRIPTION
        "This group replaces cmgwCallControlGroup.
         It contains the objects providing general call control  
         information in a media gateway."
    ::= { cMediaGwMIBGroups 9 }

cmgwSignalProtocolGroupRev1  OBJECT-GROUP
    OBJECTS {
        cmgwSignalProtocol,
        cmgwSignalProtocolVersion,
        cmgwSignalProtocolPort,
        cmgwSignalMgcProtocolPort
    }
    STATUS    deprecated
    DESCRIPTION
        "This group replaces cmgwSignalProtocolGroup.
         It contains the objects providing signaling  
         protocol information a media gateway."
    ::= { cMediaGwMIBGroups 10 }

cmgwSignalProtocolGroupRev2  OBJECT-GROUP
    OBJECTS {
        cmgwSignalProtocol,
        cmgwSignalProtocolVersion,
        cmgwSignalProtocolPort,
        cmgwSignalMgcProtocolPort,
        cmgwSignalProtocolPreference
    }
    STATUS    current
    DESCRIPTION
        "This group replaces cmgwSignalProtocolGroupRev1.
         It contains the objects providing signaling  
         protocol information a media gateway."
    ::= { cMediaGwMIBGroups 11 }

cmgwSignalProtocolGroupRev3  OBJECT-GROUP
    OBJECTS {
        cmgwSignalProtocolConfigVer
    }
    STATUS    current
    DESCRIPTION
        "Additional objects for cmgwSignalProtocolGroupRev2."
    ::= { cMediaGwMIBGroups 12 }

cMediaGwRscStatsGroup  OBJECT-GROUP
    OBJECTS {
        cmgwRscMaximumUtilization,
        cmgwRscMinimumUtilization,
        cmgwRscAverageUtilization,
        cmgwRscSinceLastReset
    }
    STATUS    current
    DESCRIPTION
        "This group includes gateway resource statistics 
         information.
        "
    ::= { cMediaGwMIBGroups 13 }


cMediaGwGroupExtra  OBJECT-GROUP
    OBJECTS {
        cmgwSrcFilterEnabled,
        cmgwLawInterceptEnabled
    }
    STATUS    current
    DESCRIPTION
        "Additional objects for cMediaGwGroupRev1."
    ::= { cMediaGwMIBGroups 14 }

 cmgwCallControlGroupRev2  OBJECT-GROUP
    OBJECTS {
        cMediaGwCcCfgControlTos,
        cMediaGwCcCfgBearerTos,
        cMediaGwCcCfgNtePayload,
        cMediaGwCcCfgNsePayload,
        cMediaGwCcCfgNseRespTimer,
        cMediaGwCcCfgVbdJitterDelayMode,
        cMediaGwCcCfgVbdJitterMaxDelay,
        cMediaGwCcCfgVbdJitterNomDelay,
        cMediaGwCcCfgVbdJitterMinDelay,
        cMediaGwCcCfgDefaultTonePlanId,
        cMediaGwCcCfgDescrInfoEnabled,
        cMediaGwCcCfgDsNamePrefix,
        cMediaGwCcCfgRtpNamePrefix,
        cMediaGwCcCfgAal1SvcNamePrefix,
        cMediaGwCcCfgAal2SvcNamePrefix,
        cMediaGwCcCfgClusterEnabled,
        cMediaGwCcCfgDefBearerTraffic,
        cMediaGwCcCfgDefRtpNamePrefix
    }

    STATUS    current
    DESCRIPTION
        "This group replaces cmgwCallControlGroup.
         It contains the objects providing general call control  
         information in a media gateway."
    ::= { cMediaGwMIBGroups 15 }

 cMediaGwGroupRev2 OBJECT-GROUP
    OBJECTS { 
        cmgwDomainName,
        cmgwPhysicalIndex,
        cmgwServiceState, 
        cmgwAdminState,
        cmgwGraceTime,
        cmgwVtMappingMode,
        cmgwV23Enabled 
      }
    STATUS          current
    DESCRIPTION
        "Additional object cmgwV23Enabled in CMediaGwEntry Table."
    ::= { cMediaGwMIBGroups 16 }

END

