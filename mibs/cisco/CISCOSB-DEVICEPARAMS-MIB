CISCOSB-DEVICEPARAMS-MIB DEFINITIONS ::= BEGIN

-- Title:                CISCOSB ROS
--                       Private DEVICE PARAMETERS MIB
-- Version:              7.46
-- Date:                 15-Jan-2007

IMPORTS
    switch001                                               FROM CISCOSB-MIB
    OBJECT-TYPE, MODULE-IDENTITY, <PERSON><PERSON><PERSON><PERSON><PERSON>                 FROM SNMPv2-SMI
    TruthValue, DisplayString, PhysAddress                  FROM SNMPv2-TC;

rndDeviceParams MODULE-IDENTITY
                LAST-UPDATED "200701020001Z"
                ORGANIZATION "Cisco Systems, Inc."

                CONTACT-INFO
                "Postal: 170 West Tasman Drive
                San Jose , CA 95134-1706
                USA

                
                Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

                DESCRIPTION
                      "This private MIB module defines general Parameters private MIBs."
                REVISION "200701020000Z"
                DESCRIPTION
                      "Initial revision."
        ::= { switch001 2 }

-- rndDeviceParams group contains a number of general variables

rndBridgeType  OBJECT-TYPE
    SYNTAX  INTEGER {
       reb(1),
       ceb(2),
       ceblb(3),
       xeb(4),
       xeb1(5),
       rebsx(6),
       rtb(7),
       ltb(8),
       tre(9),
       rtre(10),      -- remote TRE
       xtb(11),
       ete(12),
       rete(13),      -- remote ETE
       ielb(30),
       leb(31),
       openGate12(32),
       openGate4(33),
       ran(34),
       itlb(35),
       gatelinx(36),
       openGate2(37),
       ogRanTR(38),
       rdapter(39),
       ogVan(40),
       wanGate(41),
       ogRubE(42),
       ogRubT(43),
       wanGateI(44),
       vGate4(45),
       lre(46),
       mrt(47),
       vGate2(48)
     }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
      "Identification of the switch001 bridge type."
    ::= { rndDeviceParams 1  }


rndInactiveArpTimeOut OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
      "This variable defines the maximum time period that can pass
       between ARP requests concerning an entry in the ARP table.
       After this time period, the entry is deleted from the table."
    ::= { rndDeviceParams 2  }

-- The following two variables define the format of switch001 specific
-- error messages issued by the switch001 devices

rndBridgeAlarm  OBJECT IDENTIFIER ::= { rndDeviceParams 3  }

rndErrorDesc OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
       "A textual description of the enterprise-specific trap sent
        to the Network Management Station by the switch001 managed device."
    ::= { rndBridgeAlarm 1  }

rndErrorSeverity OBJECT-TYPE
    SYNTAX INTEGER  {
         info(0),
         warning(1),
         error(2),
         fatal-error(3)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
      "The severity type of the enterprise-specific trap sent to the
      Network Management Station by the switch001 managed device."
    ::= { rndBridgeAlarm 2  }

rndBrgVersion OBJECT-TYPE
    SYNTAX  DisplayString
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
      "The bridge version."
   ::= { rndDeviceParams 4  }

rndBrgFeatures OBJECT-TYPE
    SYNTAX   OCTET STRING  -- SIZE 20
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
      "A bit mask that defines the features supported by a particular configuration
       of this network element:
       __________________________________________
       |  Byte 1|Byte 2 |Byte 3 | ....|Byte 20  |
       |********|                     | ********|
       |________|_______________________________|

 Byte1 :
      bit1: TX Block mask
      bit2: Source Routing Encapulation
      bit3: SNA/SDLC
      bit4: Frame Relay
      bit5: SNMP
      bit6: LAN Manager
      bit7: High Performance
      bit8: Translation
 Byte2 :
      bit1: DEC Router
      bit2: IPX Router
      bit3: IP  Router
 Byte3 :
      bit1: Dial Up Backup
      bit2: COD
      bit3: FACS
      bit4: Load Balance
      bit5: Remote Configuration
      bit6: RIP 2
      bit7: OSPF
      bit8: IPX RIP/SAP Filter
 Byte4 :
      bit1: BootP Server
      bit2: BootP Client
      bit3: Compression
      bit4: V25.bis
      bit5: ISDN
      bit6: CODv2
      bit7: NSPF
      bit8: UDP Relay

 Byte5
      bit1:VirtualLAN
      bit2:Static IP Multicast
      bit3:IP Redundancy
      bit4:CCM2
      bit5:ISDN Bonding
      bit6:Backup Link Selection -- for the  VAN/Rdapter ver 4.0
      bit7:IP/IPX Forwarding     -- for the  WANgate ver 4.0
      bit8:Improved COD

 Byte6
      bit1: Server Disptacher
      bit2: ISDN_US             -- for the  VANSX/WANGATE ver 5.0
      bit3: PPP
      bit4: IP Rip Filter       -- for Vgate3
      bit5: Zero Hop Routing    -- for Vgate3
      bit6: ISDN Japan
      bit7: PPP-Security

 Byte7
     bit1: With unmanaged Switch
     bit2: 2 LANs
     bit3: OSPF Ver 2.0
     bit4: FACS Ver 2.0
     bit5: Multiple WEB Farm
     bit6: Backup Server
     bit7: Check Connectivity
     bit8: WSD multiplexing

Byte8
     bit1: MRT3
     bit2: WSD Redundancy
     bit3: DHCP Server
     bit4: WSD Connection Limit
     bit5: WSD Distributed System
     bit6: WSD Load Report
     bit7: WSD super farm
     bit8: RadWiz leased line

Byte9
     bit1: PPP IP address negotiaton
     bit2: DNS
     bit3: Nat
     bit4: WSD Static proximity
     bit5: WSD Dynamic proximity
     bit6: WSD Proxy
     bit7: WSD Proximity client
     bit8: MAC Load balancing

Byte10
     bit1: Unnum Inf
     bit2: Power Supply redundancy
     bit3: MRT PPP Compression
     bit4: ZHR Apolo
     bit5: Copy port
     bit6: UDP Relay 2.0
     bit7: NVRAM
     bit8: URL table

Byte11
     bit1: URL super farm
     bit2: NAT on LAN
     bit3: Remote Con
     bit4: AP5000
     bit5: Session tracking
     bit6: Mirroring
     bit7: Alias IP
     bit8: CSD Nat

Byte12
     bit1: content check
     bit2: mlb virtual ip
     bit3: reserved CISCOSB
     bit4: csd nat exception
     bit5: statistics monitor
     bit6: reserved-for-radware          "

   ::= { rndDeviceParams 5  }

rndBrgLicense OBJECT-TYPE
    SYNTAX   OCTET STRING  -- SIZE 20
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
       "This parameter is used for entering a s/w license number for a device.
        A separate license number is supplied for each device."
   ::= { rndDeviceParams 6  }

-- IP Host Parameters

--All the new parameters under this section will be added to the
-- rndMng group, as rndIpHost sub-group.
--
rndIpHost  OBJECT IDENTIFIER   ::= { rndDeviceParams 7 }
--
--ICMP Transmission Enable\Disable
--The new variable will be

-- Obsolete
-- rndICMPTransmitionEnable   OBJECT-TYPE
--   SYNTAX INTEGER {
--      enable(1),
--      disable(2)
--  }
--   MAX-ACCESS  read-write
--   STATUS  current
--  DESCRIPTION
--    "This variable controlls the ability to transmit ICMP frames"
--    ::= { rndIpHost 1 }

rndCommunityTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RndCommunityEntry
   MAX-ACCESS not-accessible
   STATUS current
   DESCRIPTION
    "The community table of the agent"
     ::= { rndIpHost 2 }

rndCommunityEntry  OBJECT-TYPE
   SYNTAX RndCommunityEntry
   MAX-ACCESS not-accessible
   STATUS current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rndCommunityMngStationAddr,
           IMPLIED rndCommunityString }
   ::= { rndCommunityTable 1 }

RndCommunityEntry ::= SEQUENCE {
  rndCommunityMngStationAddr   IpAddress,
  rndCommunityString           DisplayString,
  rndCommunityAccess           INTEGER,
  rndCommunityTrapsEnable      INTEGER,
  rndCommunityStatus           INTEGER,
  rndCommunityPortSecurity     INTEGER,
  rndCommunityOwner            DisplayString,
  rndCommunityTrapDestPort     INTEGER
}


rndCommunityMngStationAddr OBJECT-TYPE
   SYNTAX IpAddress
   MAX-ACCESS   read-create
   STATUS current
   DESCRIPTION
    "The management station that will be allowed to
    communicate with the agent IP address"
     ::=  { rndCommunityEntry 1 }

rndCommunityString OBJECT-TYPE
   SYNTAX DisplayString (SIZE(1..20))
   MAX-ACCESS read-create
   STATUS current
   DESCRIPTION
    "The community string with which the management
     station will communicate with the agent"
     ::= { rndCommunityEntry 2 }

rndCommunityAccess OBJECT-TYPE
   SYNTAX INTEGER {
    readOnly(1),
    readWrite(2),
    super(3)
      }
   MAX-ACCESS read-create
   STATUS current
   DESCRIPTION
    "The allowed access to this management station"
     ::= { rndCommunityEntry 3}

rndCommunityTrapsEnable OBJECT-TYPE
   SYNTAX INTEGER {
        snmpV1(1),
        snmpV2(2),
        snmpV3(3),
        trapsDisable(4)
   }
   MAX-ACCESS   read-create
   STATUS current
   DESCRIPTION
    "Should the agent send traps to the management station,
     and what version is required"
      ::= { rndCommunityEntry 4 }

rndCommunityStatus OBJECT-TYPE
   SYNTAX INTEGER {
    enable(1),
    invalid(2)
     }
   MAX-ACCESS read-create
   STATUS current
   DESCRIPTION
    "The status of this entry. If the status is invalid the
    community entry will be deleted"
    ::= { rndCommunityEntry 5 }

rndCommunityPortSecurity OBJECT-TYPE
   SYNTAX INTEGER {
    enabled(1),
    disabled(2)
   }
   MAX-ACCESS read-create
   STATUS current
   DESCRIPTION
    "If enabled the device will only receive SNMP messages from the port,
     through which this NMS is reachable from the device."
   DEFVAL { disabled }
   ::= { rndCommunityEntry 6 }

rndCommunityOwner OBJECT-TYPE
   SYNTAX DisplayString (SIZE(1..32))
   MAX-ACCESS read-create
   STATUS current
   DESCRIPTION
    "The owner of this community"
     ::= { rndCommunityEntry 7 }

rndCommunityTrapDestPort OBJECT-TYPE
   SYNTAX INTEGER(0..65535)
   MAX-ACCESS read-create
   STATUS current
   DESCRIPTION
    "The transport protocol (usually UDP) port to which traps to the
    management station represebted by this entry will be sent. The default
    is the well-known IANA assigned port number for SNMP traps.
    This object is relevant only if rndCommunityTrapsEnable has a value
    different from trapsDisable."
   DEFVAL { 162 }
   ::= { rndCommunityEntry 8 }

rlMridTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlMridEntry
   MAX-ACCESS not-accessible
   STATUS current
   DESCRIPTION
    "The MRID related configurations table of the agent"
     ::= { rndIpHost 3 }

rlMridEntry  OBJECT-TYPE
   SYNTAX RlMridEntry
   MAX-ACCESS not-accessible
   STATUS current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rndCommunityMngStationAddr,
           IMPLIED rndCommunityString }
   ::= { rlMridTable 1 }

RlMridEntry ::= SEQUENCE {
       rlMridConnection    INTEGER,
       rlManagedMrid INTEGER
}

rlMridConnection OBJECT-TYPE
   SYNTAX INTEGER
   MAX-ACCESS read-write
   STATUS current
   DESCRIPTION
    "The router instance connecting the NMS who accessed the agent
     through the community table entry corresponding to the keys of this entry."
     ::=  { rlMridEntry 1 }

rlManagedMrid OBJECT-TYPE
   SYNTAX INTEGER
   MAX-ACCESS read-write
   STATUS current
   DESCRIPTION
    "The router instance currently managed by the NMS who accessed the agent
     through the community table entry corresponding to the keys of this entry "
     ::=  { rlMridEntry 2 }

rndManagedTime  OBJECT-TYPE
   SYNTAX DisplayString (SIZE(6))
   MAX-ACCESS read-write
   STATUS current
   DESCRIPTION
    "The time will be sent in the format hhmmss"
   ::= { rndDeviceParams 8 }

rndManagedDate  OBJECT-TYPE
   SYNTAX DisplayString (SIZE(6))
   MAX-ACCESS read-write
   STATUS current
   DESCRIPTION
    "The date will be sent in the format ddmmyy"
    ::= { rndDeviceParams 9 }

rndBaseBootVersion OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS  read-only
   STATUS  current
   DESCRIPTION
      "Defines the boot version of the product."
   ::= { rndDeviceParams 10 }

rndIpHostManagement  OBJECT IDENTIFIER   ::= { rndIpHost 4 }

rndIpHostManagementSupported OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
         "ifindex manage supported."
    ::= { rndIpHostManagement  1 }

rndIpHostManagementIfIndex OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
         "if supported manage , indicate ifindex, if 0 thaen disable"
    ::= { rndIpHostManagement  2 }

genGroup  OBJECT IDENTIFIER   ::= { rndDeviceParams 11 }
genGroupHWVersion OBJECT-TYPE
   SYNTAX  DisplayString
   MAX-ACCESS  read-only
   STATUS  current
   DESCRIPTION
        "Defines the HW version of the product."
   ::= { genGroup 1 }

genGroupConfigurationSymbol OBJECT-TYPE
      SYNTAX  DisplayString (SIZE(1))
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
      "Defines the Configuration Symbol attached to any hardware module
      manufactured by LANNET. One single character A..Z defines the CS
      version."
      ::= { genGroup 2 }

genGroupHWStatus OBJECT-TYPE
      SYNTAX  INTEGER {
        ok(1),
        hardwareProblems(2),
        notSupported(255)
      }
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
      "This attribute describes the status of the group hardware as detected
      by the sensors software."
      ::= { genGroup 3 }

rndBasePhysicalAddress OBJECT-TYPE
    SYNTAX PhysAddress
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
      "The Base physical (MAC) address of the device."
    ::= { rndDeviceParams 12  }

RlImageIdType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "SW images enumeration"
    SYNTAX      INTEGER { image1(1), image2(2) }

rndSoftwareFile  OBJECT IDENTIFIER ::= { rndDeviceParams 13 }

rndActiveSoftwareFileTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RndActiveSoftwareFileEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " The (conceptual) table listing the active software file of the
          requested unit."
    ::= {rndSoftwareFile 1 }

rndActiveSoftwareFileEntry  OBJECT-TYPE
    SYNTAX RndActiveSoftwareFileEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry (conceptual row) in the rndActiveSoftwareFileTable."
    INDEX {rndUnitNumber }
    ::= {rndActiveSoftwareFileTable  1 }

RndActiveSoftwareFileEntry ::= SEQUENCE {
    rndUnitNumber                      INTEGER,
    rndActiveSoftwareFile              RlImageIdType,
    rndActiveSoftwareFileAfterReset    INTEGER
}

rndUnitNumber OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS      read-write
    STATUS      current
    DESCRIPTION
        "The unit number (for stackable devices) or 1 for 'stand alone' device."
    ::= {rndActiveSoftwareFileEntry 1 }

rndActiveSoftwareFile OBJECT-TYPE
    SYNTAX      RlImageIdType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the current active software file, image1 or image2."
    ::= {rndActiveSoftwareFileEntry 2 }

rndActiveSoftwareFileAfterReset OBJECT-TYPE
    SYNTAX INTEGER {
        image1(1),
        image2(2),
        invalidImage(3)
    }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the software file that will be active after reset (image1 or image2).
         If an error occurred in the download process, resulting in the corruption
         of the single software file, The value 'invalidImage' will be returned.
         This value can not be set by the user."
    ::= {rndActiveSoftwareFileEntry 3 }

rlResetStatus
 OBJECT-TYPE
    SYNTAX  BITS {
            ok(0),
            no-stack-integrity(1),
            downgrade(2)
    }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
         "A bit mask that specifies system status before reset action is preformed.
         Reset is prohibited in stackable products if the images selected after
         reboot are not of the same release version in ALL stack units.
         Downgrade status specifies that there are some actions to be preformed
         on the CDB file before reset is preformed.
         0x0 - OK
         0x1 - noStackIntegrity
         0x2 - downgrade"
    ::= { rndSoftwareFile 2 }


rndImageSize OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS     read-only
    STATUS     current
    DESCRIPTION
        "Max number of sectors currently allocated for image(s)."
    ::= { rndDeviceParams 14 }

rndBackupConfigurationEnabled OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Specifies whether the device supports backup-config parameters in lcli commands."
    ::= { rndDeviceParams 15 }

rndImageInfo  OBJECT IDENTIFIER ::= { rndDeviceParams 16 }

rndImageInfoTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RndImageInfoEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " The table contains information about images."
    ::= {rndImageInfo 1 }

rndImageInfoEntry  OBJECT-TYPE
    SYNTAX RndImageInfoEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        " An entry in the rndImageInfoTable."
    INDEX {rndStackUnitNumber }
    ::= {rndImageInfoTable  1 }

RndImageInfoEntry ::= SEQUENCE {
    rndStackUnitNumber                 	INTEGER,
    rndImage1Name                      	DisplayString,
    rndImage2Name                      	DisplayString,
    rndImage1Version                   	DisplayString,
    rndImage2Version                   	DisplayString,	
    rndImage1Date                      	DisplayString,
    rndImage2Date                      	DisplayString,
    rndImage1Time                      	DisplayString,
    rndImage2Time                      	DisplayString,
    rndImage1Description               	DisplayString,
    rndImage2Description               	DisplayString,
    rndImage1InternalVersion		DisplayString,
    rndImage2InternalVersion		DisplayString	
}

rndStackUnitNumber OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS      read-write
    STATUS      current
    DESCRIPTION
        "The unit number (for stackable devices) or 1 for 'stand alone' device."
    ::= {rndImageInfoEntry 1 }

rndImage1Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the file name of image-1 in the system."
    ::= {rndImageInfoEntry 2 }

rndImage2Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the file name of image-2 (if present) in the system.If image-2 is not present show 'no info' text"
    ::= {rndImageInfoEntry 3 }


rndImage1Version OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the version of image-1 in the system."
    ::= {rndImageInfoEntry 4 }

rndImage2Version OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the version of image-2 (if present) in the system.If image-2 is not present show 'no info' text"
    ::= {rndImageInfoEntry 5 }

rndImage1Date OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the compilation date of image-1 in the system."
    ::= {rndImageInfoEntry 6 }

rndImage2Date OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the compilation date of image-2 (if present) in the system.If image-2 is not present show 'no info' text"
    ::= {rndImageInfoEntry 7 }

rndImage1Time OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the compilation time of image-1 in the system."
    ::= {rndImageInfoEntry 8 }

rndImage2Time OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "Indicates the compilation time of image-2 (if present) in the system.If image-2 is not present show 'no info' text"
    ::= {rndImageInfoEntry 9 }

rndImage1Description OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-write
    STATUS      current
    DESCRIPTION
        "Indicates the description of image-1 in the system."
    ::= {rndImageInfoEntry 10 }

rndImage2Description OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-write
    STATUS      current
    DESCRIPTION
        "Indicates the description of image-2 in the system."
    ::= {rndImageInfoEntry 11 }

rndImage1InternalVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "In case customer (external) version exists, this field is indicates image-1 internal version"
    ::= {rndImageInfoEntry 12 }
	
rndImage2InternalVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS      read-only
    STATUS      current
    DESCRIPTION
        "In case customer (external) version exists, this field is indicates image-2 internal version"
    ::= {rndImageInfoEntry 13 }
	
	
	
	
	
rlComponentsInfoTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlComponentsInfoEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The table contains information about components, their streams and baselines in the device images."
    ::= {rndImageInfo 2}

rlComponentsInfoEntry  OBJECT-TYPE
    SYNTAX RlComponentsInfoEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry in the rlComponentsInfoTable."
    INDEX {rlComponentsInfoStackUnitNumber, rlComponentsInfoImageId,
           IMPLIED rlComponentsInfoComponent}
    ::= {rlComponentsInfoTable  1 }

RlComponentsInfoEntry ::= SEQUENCE {
    rlComponentsInfoStackUnitNumber    INTEGER,
    rlComponentsInfoImageId            RlImageIdType,
    rlComponentsInfoComponent          DisplayString,
    rlComponentsInfoBaseline           DisplayString
}

rlComponentsInfoStackUnitNumber OBJECT-TYPE
    SYNTAX          INTEGER
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The unit number (for stackable devices) or 1 for 'stand alone' device."
    ::= {rlComponentsInfoEntry 1}


rlComponentsInfoImageId OBJECT-TYPE
    SYNTAX      RlImageIdType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Indicates the image id in the unit"
::= {rlComponentsInfoEntry 2}

rlComponentsInfoComponent OBJECT-TYPE
    SYNTAX      DisplayString(SIZE (1..40))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Indicates the SW component name."
    ::= {rlComponentsInfoEntry 3}

rlComponentsInfoBaseline OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the version control baseline of the SW component name."
    ::= {rlComponentsInfoEntry 5}


END

