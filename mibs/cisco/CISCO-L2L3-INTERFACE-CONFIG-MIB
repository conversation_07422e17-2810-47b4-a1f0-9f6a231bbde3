-- *****************************************************************
-- CISCO-L2L3-INTERFACE-CONFIG-MIB - 
--    configuration of switchport mode for interfaces
--
-- February 2000, <PERSON>journer
--
-- Copyright (c) 2000 by cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************
--

CISCO-L2L3-INTERFACE-CONFIG-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    OBJECT-GROUP
        FROM SNMPv2-CONF
    TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    ifIndex
        FROM IF-MIB
    ciscoMgmt
        FROM CISCO-SMI
    ;

ciscoL2L3IfConfigMIB MODULE-IDENTITY
    LAST-UPDATED        "200005101900Z"
    ORGANIZATION        "Cisco Systems, Inc."
    CONTACT-INFO
        "Cisco Systems
        Customer Service

        Postal: 170 W Tasman Drive
                San Jose, CA  95134
                USA

        Tel: ****** 553-NETS

        E-mail: <EMAIL>"
    DESCRIPTION
        "Interface switchport mode configuration management MIB.

        This MIB is used to monitor and control 
        configuration of interface switchport and routed mode."
    REVISION        "200005101900Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 151 }

ciscoL2L3IfConfigMIBObjects OBJECT IDENTIFIER ::=  { ciscoL2L3IfConfigMIB 1 }

cL2L3IfConfig OBJECT IDENTIFIER ::=  { ciscoL2L3IfConfigMIBObjects 1 }

--
-- Textual Conventions
--

CL2L3InterfaceMode ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION
        "The operational mode of the interface.

        For administrative and operational states, valid values are: 
        routed(1), switchport(2).
        
        routed(1): Routed mode interfaces direct traffic using 
        layer 3 protocols.

        switchport(2):  Switchport-mode interfaces direct traffic using 
        layer 2 protocols.  A switchport-mode interface can be in
        access mode, or trunk mode, or multi-mode.

        Switchport interface operating mode can be configured manually,
        or negotiated by Dynamic Trunking Protocol (DTP) or Dynamic 
        Inter-Switch Link (DISL).

        Access-mode interfaces carry one VLAN's traffic.  Access-mode
        interface parameters are configured in CISCO-VLAN-MEMBERSHIP-MIB.

        Trunk-mode interfaces carry one or more VLANs.  VLAN-related 
        trunk-mode interface parameters are configured in CISCO-VTP-MIB.

        Multi-mode interfaces carry one VLAN to each alias of a 
        single connected end-station.  VLAN-related multi-mode 
        interface parameters are configured in CISCO-VTP-MIB.
        "
    SYNTAX INTEGER { routed(1), switchport(2) }

--
-- switchport interface mode configuration table
--

cL2L3IfTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF CL2L3IfEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The table shows the administratively requested and
        actual operating configuration for switchport interfaces."
    ::= { cL2L3IfConfig 1 }

cL2L3IfEntry OBJECT-TYPE
    SYNTAX     CL2L3IfEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An entry represents the configuration and operation of a
        switchport interface.

        Entries are created and deleted automatically in tandem 
        with the corresponding ifEntries."
    INDEX      { ifIndex }
    ::= { cL2L3IfTable 1 }

CL2L3IfEntry ::= SEQUENCE {
    cL2L3IfModeAdmin    CL2L3InterfaceMode,
    cL2L3IfModeOper     CL2L3InterfaceMode
    } 

cL2L3IfModeAdmin OBJECT-TYPE
    SYNTAX     CL2L3InterfaceMode
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION 
        "The administratively desired interface mode."
    ::= { cL2L3IfEntry 1 }

cL2L3IfModeOper OBJECT-TYPE
    SYNTAX     CL2L3InterfaceMode
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION 
        "The operational interface mode."
    ::= { cL2L3IfEntry 2 }


--
-- Notifications
--

-- ciscoL2L3IfConfigMIBNotificationPrefix OBJECT IDENTIFIER ::= 
--     { ciscoL2L3IfConfigMIB 2 }
-- ciscoL2L3IfConfigMIBNotifications OBJECT IDENTIFIER ::=
--     { ciscoL2L3IfConfigMIBNotificationPrefix 0 }


--
-- Conformance
--

ciscoL2L3IfConfigMIBConformance OBJECT IDENTIFIER 
    ::= { ciscoL2L3IfConfigMIB 3 }
ciscoL2L3IfConfigMIBCompliances OBJECT IDENTIFIER 
    ::= { ciscoL2L3IfConfigMIBConformance 1 }
ciscoL2L3IfConfigMIBGroups      OBJECT IDENTIFIER 
    ::= { ciscoL2L3IfConfigMIBConformance 2 }

ciscoL2L3IfConfigMIBCompliance MODULE-COMPLIANCE
    STATUS current
    DESCRIPTION
        "The compliance statement for entities which implement
         the Cisco L2L3 Interface Configuration Management MIB"
    MODULE        -- this module
        MANDATORY-GROUPS { ciscoL2L3IfConfigMIBGroup }
    ::= { ciscoL2L3IfConfigMIBCompliances 1 }

--
-- Units of Conformance
--

ciscoL2L3IfConfigMIBGroup OBJECT-GROUP
    OBJECTS {
        cL2L3IfModeAdmin,
        cL2L3IfModeOper
    }
    STATUS current
    DESCRIPTION
        "Interface L2 & L3 mode objects"
    ::= { ciscoL2L3IfConfigMIBGroups 1 }

END
