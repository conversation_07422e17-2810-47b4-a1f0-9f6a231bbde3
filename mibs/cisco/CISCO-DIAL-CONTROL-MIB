-- *****************************************************************
-- The CISCO dial control mib. This is an extension to the RFC 2128.
--
-- <PERSON><PERSON><PERSON>, January, 1998
--
-- Copyright (c) 1998, 1999-2001, 2002, 2003, 2005 by
-- cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************
--
-- This mib is an extension to the RFC 2128
-- 
-- The RFC 2128 (Dial Control Mib) defines callHistoryTable, which is
-- the table that stores past call information. Unfortunately, the indices
-- to this table (callActiveSetupTime, callActiveIndex) are not appropriate
-- for the NMS to retrieve information on regular intervals.
-- 
-- The following changes in the RFC2128 will fix the INDEX of the
-- callHistoryTable. This will also make the mib useful for various types 
-- of calls, including ISDN data, voice, modem. VoIP etc. Right now, the 
-- NMS needs to poll three different mibs (CISCO-CALL-HISTORY-MIB.my, 
-- CISCO-POP-MGMT-MIB.my and the DIAL-CONTROL-MIB.my) for these calls.
--


CISCO-DIAL-CONTROL-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY,
        OBJECT-TYPE,
        Unsigned32 
                FROM SNMPv2-SMI
        DisplayString,
        TimeStamp
                 FROM SNMPv2-TC
        MODULE-COMPLIANCE,
        OBJECT-GROUP
                FROM SNMPv2-CONF
        ciscoExperiment                  
                FROM CISCO-SMI
        AbsoluteCounter32 
                FROM DIAL-CONTROL-MIB
        InterfaceIndexOrZero
                FROM IF-MIB
        SnmpAdminString                      
                FROM SNMP-FRAMEWORK-MIB;

ciscoDialControlMib MODULE-IDENTITY
        LAST-UPDATED    "200505260000Z"
        ORGANIZATION    "Cisco systems, Inc."
        CONTACT-INFO
            "        Bibek Das
             Postal: cisco Systems
                     170 West Tasman Drive
                     San Jose, CA 95134
                     U.S.A.
             Phone:  ****** 526 5225
             E-mail: <EMAIL>"
        DESCRIPTION  
            "The MIB module to describe call history information for
             demand access and possibly other kinds of interfaces."
        REVISION      "200505260000Z"
        DESCRIPTION
            "Modified DESCRIPTION of cPeerSearchType object.
            "
        REVISION      "200307100001Z"
        DESCRIPTION
            "1) Changed the SYNTAX of cCallHistoryLogicalIfIndex
                to be InterfaceIndexOrZero instead of 
                InterfaceIndex.
             2) Modified the DESCRIPTION of 
                cCallHistoryLogicalIfIndex."
        REVISION      "200208210001Z"
        DESCRIPTION
            "1) Deprecated ciscoDialControlMibComplianceRev3 
             2) Added a new table cCallHistoryIecTable
             3) Added a new module-compliance
                ciscoDialControlMibComplianceRev4 
             4) Added gatekeeper and externalGKTMPServer enum labels
                to cCallHistoryReleaseSrc
             5) Added cCallHistoryIecGroup OBJECT-GROUP."
        REVISION      "200205240001Z"
        DESCRIPTION
            "1) Deprecated ciscoDialControlMibComplianceRev2 
             2) Added a new object subtree (OID)
                cPeerGlobalConfiguration
             3) Added a new object cPeerSearchType 
             4) Added a new object-group cPeerGlobalConfigurationGroup
             5) Added a new module-compliance
                ciscoDialControlMibComplianceRev3." 
        REVISION      "200202201546Z"
        DESCRIPTION 
            "1) Deprecated cCallHistoryReleaseSource 
             2) Deprecated cCallHistoryGroupRev1 
             3) Deprecated ciscoDialControlMibComplianceRev1 
             3) Added a new object cCallHistoryReleaseSrc
             4) Added a new object cCallHistoryGroupRev2
             6) Added a new object ciscoDialControlMibComplianceRev2."
        REVISION      "200112131546Z"
        DESCRIPTION
            "Added a new object cCallHistoryReleaseSource in 
             CallHistory table."
        REVISION      "9801161546Z"
        DESCRIPTION
            "The initial version of the mib."
        ::= { ciscoExperiment 25 }


-- Cisco Dial Control Mib objects definitions

ciscoDialControlMibObjects OBJECT IDENTIFIER ::= { ciscoDialControlMib 1 }

--
-- the call history group
--

cCallHistory OBJECT IDENTIFIER ::= { ciscoDialControlMibObjects 4 }

-- cCallHistoryTable
-- Table to store the past call information. The Destination number
-- and the call connect and disconnect time, the disconnection cause
-- are stored. These calls could be circuit switched or they could
-- be virtual circuits. History of each and every call is stored,
-- of successful calls as well as of unsuccessful and rejected calls.
-- An entry will be created when a call is cleared.
-- The upper limit to the number of entries in this table is given
-- by callHistoryTableMaxLength, defined in RFC 2128.
-- The minimum time that an entry can be retained in this table is
-- given by callHistoryRetainTimer, defined in RFC 2128.

cCallHistoryTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CCallHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table containing information about specific
             calls to a specific destination."
        ::= { cCallHistory 3 }

cCallHistoryEntry OBJECT-TYPE
        SYNTAX      CCallHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The information regarding a single Connection."
        INDEX       { cCallHistoryIndex }
        ::= { cCallHistoryTable 1 }


CCallHistoryEntry ::=
        SEQUENCE {
            cCallHistoryIndex                     Unsigned32,
            cCallHistorySetupTime                 TimeStamp,
            cCallHistoryPeerAddress               DisplayString,
            cCallHistoryPeerSubAddress            DisplayString,
            cCallHistoryPeerId                    INTEGER,
            cCallHistoryPeerIfIndex               INTEGER,
            cCallHistoryLogicalIfIndex            InterfaceIndexOrZero,
            cCallHistoryDisconnectCause           OCTET STRING,
            cCallHistoryDisconnectText            DisplayString,
            cCallHistoryConnectTime               TimeStamp,
            cCallHistoryDisconnectTime            TimeStamp,
            cCallHistoryCallOrigin                INTEGER,
            cCallHistoryChargedUnits              AbsoluteCounter32,
            cCallHistoryInfoType                  INTEGER,
            cCallHistoryTransmitPackets           AbsoluteCounter32,
            cCallHistoryTransmitBytes             AbsoluteCounter32,
            cCallHistoryReceivePackets            AbsoluteCounter32,
            cCallHistoryReceiveBytes              AbsoluteCounter32,
            cCallHistoryReleaseSource             INTEGER,
            cCallHistoryReleaseSrc                INTEGER
        }

cCallHistoryIndex OBJECT-TYPE
        SYNTAX      Unsigned32 (1..4294967295)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A monotonically increasing integer for the sole purpose of
             indexing call disconnection events.  When it reaches the 
             maximum value, an extremely unlikely event, the agent wraps 
             the value back to 1 and may flush existing entries."
        ::= { cCallHistoryEntry 1 }
 
cCallHistorySetupTime OBJECT-TYPE
        SYNTAX      TimeStamp
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The value of sysUpTime when the call setup was started.
             This will be useful for an NMS to sort the call history
             entry with call setup time. Also, this object
             can be useful in finding large delays between the time the
             call was started and the time the call was connected.
             For ISDN media, this will be the time when the setup
             message was received from or sent to the network.
             The value of this object is the same as callActiveSetupTime
             in the callActiveTable"
        ::= { cCallHistoryEntry 2 }

cCallHistoryPeerAddress OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number this call was connected to. If the number is
             not available, then it will have a length of zero."
        ::= { cCallHistoryEntry 3 }

cCallHistoryPeerSubAddress OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The subaddress this call was connected to. If the subaddress
             is undefined or not available, this will be a zero length
             string."
        ::= { cCallHistoryEntry 4 }
cCallHistoryPeerId OBJECT-TYPE
        SYNTAX      INTEGER (0..2147483647)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This is the Id value of the peer table entry
             to which this call was made. If a peer table entry
             for this call does not exist, the value of this object
             will be zero."
        ::= { cCallHistoryEntry 5 }

cCallHistoryPeerIfIndex OBJECT-TYPE
        SYNTAX      INTEGER (0..2147483647)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This is the ifIndex value of the peer table entry
             to which this call was made. If a peer table entry
             for this call does not exist, the value of this object
             will be zero."
        ::= { cCallHistoryEntry 6 }

cCallHistoryLogicalIfIndex OBJECT-TYPE
        SYNTAX      InterfaceIndexOrZero
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This is the ifIndex value of the logical interface through
             which this call was made. For ISDN media, this would be
             the ifIndex of the B channel which was used for this call.
             If the ifIndex value is unknown, the value of this object 
             will be zero. For an IP call, the value will be zero." 
        ::= { cCallHistoryEntry 7 }

cCallHistoryDisconnectCause OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (0..4))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The encoded network cause value associated with this call.

             The value of this object will depend on the interface type
             as well as on the protocol and protocol version being
             used on this interface. Some references for possible cause
             values are given below."
        REFERENCE
            "- Bellcore SR-NWT-001953, Generic Guidelines for
               ISDN Terminal Equipment On Basic Access Interfaces,
               chapter *******.
             - Bellcore SR-NWT-002343, ISDN Primary Rate Interface
               Generic Guidelines for Customer Premises Equipment,
               chapter *******.
             - ITU-T Q.931, Appendix I.
             - ITU-T X.25, CAUSE and DIAGNOSTIC field values.
             - German Telekom FTZ 1TR6, chapter *******.4.4."
        ::= { cCallHistoryEntry 8 }

cCallHistoryDisconnectText OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "ASCII text describing the reason for call termination.

             This object exists because it would be impossible for
             a management station to store all possible cause values
             for all types of interfaces. It should be used only if
             a management station is unable to decode the value of
             dialCtlPeerStatsLastDisconnectCause."
        ::= { cCallHistoryEntry 9 }

cCallHistoryConnectTime OBJECT-TYPE
        SYNTAX      TimeStamp
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The value of sysUpTime when the call was connected."
        ::= { cCallHistoryEntry 10 }

cCallHistoryDisconnectTime OBJECT-TYPE
        SYNTAX      TimeStamp
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The value of sysUpTime when the call was disconnected."
        ::= { cCallHistoryEntry 11 }

cCallHistoryCallOrigin OBJECT-TYPE
        SYNTAX      INTEGER {
            originate(1),
            answer(2),
            callback(3)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The call origin."
        ::= { cCallHistoryEntry 12 }

cCallHistoryChargedUnits OBJECT-TYPE
        SYNTAX      AbsoluteCounter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of charged units for this connection.
             For incoming calls or if charging information is
             not supplied by the switch, the value of this object
             will be zero."
        ::= { cCallHistoryEntry 13 }

cCallHistoryInfoType OBJECT-TYPE
        SYNTAX      INTEGER {
            other(1),                   -- e.g. for non-isdn media
            speech(2),
            unrestrictedDigital(3),     -- 64k/s data
            unrestrictedDigital56(4),   -- with 56k rate adaption
            restrictedDigital(5),
            audio31(6),                 -- 3.1 kHz audio
            audio7(7),                  -- 7 kHz audio
            video(8),
            packetSwitched(9),
            fax(10)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The information type for this call."
        ::= { cCallHistoryEntry 14 }

cCallHistoryTransmitPackets OBJECT-TYPE
        SYNTAX      AbsoluteCounter32
        MAX-ACCESS  read-only
        STATUS     current
        DESCRIPTION
            "The number of packets which were transmitted while this
             call was active."
        ::= { cCallHistoryEntry 15 }

cCallHistoryTransmitBytes OBJECT-TYPE
        SYNTAX      AbsoluteCounter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of bytes which were transmitted while this
             call was active."
        ::= { cCallHistoryEntry 16 }

cCallHistoryReceivePackets OBJECT-TYPE
        SYNTAX      AbsoluteCounter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets which were received while this
             call was active."
        ::= { cCallHistoryEntry 17 }

cCallHistoryReceiveBytes OBJECT-TYPE
        SYNTAX      AbsoluteCounter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of bytes which were received while this
             call was active."
        ::= { cCallHistoryEntry 18 }

cCallHistoryReleaseSource OBJECT-TYPE
        SYNTAX      INTEGER {
            callingPartyInPstn(1),
            callingPartyInVoip(2),
            calledPartyInPstn(3),
            calledPartyInVoip(4),
            internalRelease(5),
            internalCallControlApp(6), -- for e.g. TCL or VXML script
            consoleCommand(7), -- for e.g. CLI or MML
            externalRadiusServer(8),
            externalNmsApp(9),
            externalCallControlAgent(10)  -- for e.g. GATEKEEPER
        }
        MAX-ACCESS  read-only
        STATUS      deprecated  -- superseded by cCallHistoryReleaseSrc
        DESCRIPTION "Originator of the call release."
        ::= { cCallHistoryEntry 19 }

cCallHistoryReleaseSrc OBJECT-TYPE
        SYNTAX      INTEGER {
            callingPartyInPstn(1),
            callingPartyInVoip(2),
            calledPartyInPstn(3),
            calledPartyInVoip(4),
            internalReleaseInPotsLeg(5),
            internalReleaseInVoipLeg(6),
            internalCallControlApp(7), 
            internalReleaseInVoipAAA(8),
            consoleCommand(9), 
            externalRadiusServer(10),
            externalNmsApp(11),
            externalCallControlAgent(12),
            gatekeeper(13), 
            externalGKTMPServer(14)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION 
            "Originator of the call release. Indicates the source of 
             the call release as follows : 
             1) callingPartyInPstn : Calling party in PSTN.
             2) callingPartyInVoip : Calling party in VoIP.
             3) calledPartyInPstn : Called party in PSTN.
             4) calledPartyInVoip : Called party in VoIP.
             5) internalReleaseInPotsLeg : Due to an internal error 
                in Telephony call leg.
             6) internalReleaseInVoipLeg : Due to an internal error
                in VoIP call leg.
             7) internalCallControlApp : Due to an internal error
                in Session Application or Tcl or VXML script originated
                release. 
             8) internalReleaseInVoipAAA : Due to an internal error
                in VoIP AAA module.
             9) consoleCommand : Due to CLI or MML.
             10) externalRadiusServer : Call failed during authorization
                 , authentication or due to receipt of POD from the 
                 RADIUS server.
             11) externalNmsApp : Due to SNMP request to clear 
                 the call.
             12) externalCallControlAgent : External Call Control Agent
                 initiated clear.
             13) gatekeeper : Gatekeeper initiated clear due to receipt
                 of Admission Reject, Disengage Request message.
             14) externalGKTMPServer : External GKTMP server initiated
                 clear due to receipt of Admission Reject message from
                 the gatekeeper, triggered by RESPONSE.ARJ message from
                 the GKTMP server."
        ::= { cCallHistoryEntry 20 }

-- cCallHistoryIecTable
-- Table to store one or more Internal Error Codes.
-- These IECs are generated if a call fails.

cCallHistoryIecTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CCallHistoryIecEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This table contains information about Internal Error
             Code(s) (IEC) which caused the call to fail."
        ::= { cCallHistory 4 }

cCallHistoryIecEntry OBJECT-TYPE
        SYNTAX      CCallHistoryIecEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The IEC information regarding a single call."
        INDEX       { cCallHistoryIndex, cCallHistoryIecIndex }
        ::= { cCallHistoryIecTable 1 }

CCallHistoryIecEntry ::=
        SEQUENCE {
            cCallHistoryIecIndex         Unsigned32,
            cCallHistoryIec              SnmpAdminString
        }

cCallHistoryIecIndex OBJECT-TYPE
        SYNTAX      Unsigned32 (1..1024)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This object is used to index one or more IECs in the
             context of a single call.  In most cases there will
             only be one IEC when a call fails.  However, it is
             possible for the software processing the call to 
             generate multiple IECs before the call ultimately fails.
             In that scenario, there will be multiple entries in
             this table related to a single call (cCallHistoryIndex)
             and this object will serve to uniquely identify each IEC."
        ::= { cCallHistoryIecEntry 1 }

cCallHistoryIec OBJECT-TYPE
        SYNTAX      SnmpAdminString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object reflects the Internal Error Code.
             The format is a string of dotted decimal numbers
             composed of the following components:

             Version.Entity.Category.Subsystem.Errorcode.Diagnostic

             Each component is defined as follows:
             Version     : The version of IEC software.
             Entity      : The network entity that originated
                           the error.
             Category    : The category of the error (eg, software
                           error, hardware resource unavailable, ...)
             Subsystem   : The subsystem in which the error occurred.
             Errorcode   : A subsytem-specific error code.
             Diagnostic  : An implementation-specific diagnostic code."
        ::= { cCallHistoryIecEntry 2 }

--
-- Peer Global Configuration Group
--

cPeerGlobalConfiguration
             OBJECT IDENTIFIER ::= { ciscoDialControlMibObjects 5 }

cPeerSearchType OBJECT-TYPE
        SYNTAX   INTEGER {
            none      (1),
            datavoice (2),
            voicedata (3)
        }
        MAX-ACCESS read-write
        STATUS     current
        DESCRIPTION
            "Specifies the peer search preference based on the
             type of peers in an universal/integrated port
             platform.

            none      - both voice and data peers are searched
                        in same preference.
            datavoice - search data peers first. If no data peers
                        are found, the voice peers are searched.
            voicedata - search voice peers first. If no voice peers
                        are found, the data peers are searched."
        DEFVAL     { datavoice }
        ::= { cPeerGlobalConfiguration 1 }

-- conformance information

ciscoDialControlMibConformance OBJECT IDENTIFIER ::=
                                { ciscoDialControlMib 3 }

ciscoDialControlMibCompliances OBJECT IDENTIFIER ::=
                                { ciscoDialControlMibConformance 1 }

ciscoDialControlMibGroups      OBJECT IDENTIFIER ::=
                                { ciscoDialControlMibConformance 2 }

-- compliance statements

ciscoDialControlMibCompliance MODULE-COMPLIANCE
        STATUS    deprecated  -- superceded by 
                              -- ciscoDialControlMibComplianceRev1
        DESCRIPTION
            "The compliance statement for entities which
             implement the DIAL CONTROL MIB"
        MODULE      -- this module
        MANDATORY-GROUPS
            { cCallHistoryGroup }
        ::= { ciscoDialControlMibCompliances 1 }

ciscoDialControlMibComplianceRev1 MODULE-COMPLIANCE
        STATUS    deprecated  -- superceded by 
                              -- ciscoDialControlMibComplianceRev2
        DESCRIPTION
            "The compliance statement for entities which
             implement the DIAL CONTROL MIB"
        MODULE      -- this module
        MANDATORY-GROUPS
            { cCallHistoryGroupRev1 }
        ::= { ciscoDialControlMibCompliances 2 }

ciscoDialControlMibComplianceRev2 MODULE-COMPLIANCE
        STATUS    deprecated  -- superceded by 
                              -- ciscoDialControlMibComplianceRev3
        DESCRIPTION
            "The compliance statement for entities which
             implement the DIAL CONTROL MIB"
        MODULE      -- this module
        MANDATORY-GROUPS
            { cCallHistoryGroupRev2 }
        ::= { ciscoDialControlMibCompliances 3 }

ciscoDialControlMibComplianceRev3 MODULE-COMPLIANCE
        STATUS    deprecated  -- superceded by 
                              -- ciscoDialControlMibComplianceRev4
        DESCRIPTION
            "The compliance statement for entities which
             implement the DIAL CONTROL MIB"
        MODULE      -- this module
        MANDATORY-GROUPS
            { cCallHistoryGroupRev2, cPeerGlobalConfigurationGroup }
        ::= { ciscoDialControlMibCompliances 4 }

ciscoDialControlMibComplianceRev4 MODULE-COMPLIANCE
        STATUS      current
        DESCRIPTION
            "The compliance statement for entities which
             implement the DIAL CONTROL MIB"
        MODULE      -- this module
        MANDATORY-GROUPS
            { cCallHistoryGroupRev2,
              cPeerGlobalConfigurationGroup,
              cCallHistoryIecGroup
        }
        ::= { ciscoDialControlMibCompliances 5 }

-- units of conformance


cCallHistoryGroup OBJECT-GROUP
        OBJECTS {
            cCallHistorySetupTime,
            cCallHistoryPeerAddress,
            cCallHistoryPeerSubAddress,
            cCallHistoryPeerId,
            cCallHistoryPeerIfIndex,
            cCallHistoryLogicalIfIndex,
            cCallHistoryDisconnectCause,
            cCallHistoryDisconnectText,
            cCallHistoryConnectTime,
            cCallHistoryDisconnectTime,
            cCallHistoryCallOrigin,
            cCallHistoryChargedUnits,
            cCallHistoryInfoType,
            cCallHistoryTransmitPackets,
            cCallHistoryTransmitBytes,
            cCallHistoryReceivePackets,
            cCallHistoryReceiveBytes
        }
        STATUS     deprecated  -- superceded by cCallHistoryGroupRev1
        DESCRIPTION
            "A collection of objects providing the Call History
             capability."
        ::= { ciscoDialControlMibGroups 1 }

cCallHistoryGroupRev1 OBJECT-GROUP
        OBJECTS {
            cCallHistorySetupTime,
            cCallHistoryPeerAddress,
            cCallHistoryPeerSubAddress,
            cCallHistoryPeerId,
            cCallHistoryPeerIfIndex,
            cCallHistoryLogicalIfIndex,
            cCallHistoryDisconnectCause,
            cCallHistoryDisconnectText,
            cCallHistoryConnectTime,
            cCallHistoryDisconnectTime,
            cCallHistoryCallOrigin,
            cCallHistoryChargedUnits,
            cCallHistoryInfoType,
            cCallHistoryTransmitPackets,
            cCallHistoryTransmitBytes,
            cCallHistoryReceivePackets,
            cCallHistoryReceiveBytes,
            cCallHistoryReleaseSource 
        }
        STATUS      deprecated  -- superceded by cCallHistoryGroupRev2
        DESCRIPTION
            "A collection of objects providing the Call History
             capability."
        ::= { ciscoDialControlMibGroups 2 }

cCallHistoryGroupRev2 OBJECT-GROUP
        OBJECTS {
            cCallHistorySetupTime,
            cCallHistoryPeerAddress,
            cCallHistoryPeerSubAddress,
            cCallHistoryPeerId,
            cCallHistoryPeerIfIndex,
            cCallHistoryLogicalIfIndex,
            cCallHistoryDisconnectCause,
            cCallHistoryDisconnectText,
            cCallHistoryConnectTime,
            cCallHistoryDisconnectTime,
            cCallHistoryCallOrigin,
            cCallHistoryChargedUnits,
            cCallHistoryInfoType,
            cCallHistoryTransmitPackets,
            cCallHistoryTransmitBytes,
            cCallHistoryReceivePackets,
            cCallHistoryReceiveBytes,
            cCallHistoryReleaseSrc 
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects providing the Call History
             capability."
        ::= { ciscoDialControlMibGroups 3 }

cPeerGlobalConfigurationGroup OBJECT-GROUP
        OBJECTS {
            cPeerSearchType
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects providing the Peer global
             configuration."
        ::= { ciscoDialControlMibGroups 4 }

cCallHistoryIecGroup OBJECT-GROUP
        OBJECTS {
            cCallHistoryIec
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects providing information about
             Internal Error Code."
        ::= { ciscoDialControlMibGroups 5 }

END
