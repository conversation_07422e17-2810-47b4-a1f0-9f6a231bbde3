-- *****************************************************************
-- CISCO-VOICE-DNIS-MIB.my: Voice DNIS MIB file
--
-- March 2000  <PERSON>
--
-- Copyright (c) 2000, 2001, 2002 by cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-VOICE-DNIS-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY,
        OBJECT-TYPE,
	NOTIFICATION-TYPE
                FROM SNMPv2-SMI
        TEXTUAL-CONVENTION,
        RowStatus,
        TruthValue,
        DisplayString
                FROM SNMPv2-TC
        MODULE-COMPLIANCE,
        OBJECT-<PERSON><PERSON><PERSON>,
        NOTIFICATION-GROUP
                FROM SNMPv2-CONF
        ciscoMgmt
                FROM CISCO-SMI;
 
ciscoVoiceDnisMIB MODULE-IDENTITY
        LAST-UPDATED    "200205010000Z"
        ORGANIZATION    "Cisco Systems, Inc."
        CONTACT-INFO
                "       Cisco Systems
                        Customer Service

                Postal: 170 W. Tasman Drive
                        San Jose, CA  95134
                        USA
 
                Tel: ****** 553-NETS
 
                E-mail: <EMAIL>"
        DESCRIPTION
                "The MIB module provides management support for Dialer
                 Number Information Service (DNIS) mapping.  A DNIS
                 entry is associated with a Voice XML (VXML) page to
                 provide audio play back features. Multiple DNIS
                 entries can be grouped together to form a DNIS
                 mapping with a unique map name.

                 *** ABBREVIATIONS, ACRONYMS, AND SYMBOLS ***
                 
                 DNIS - Dialer Number Information Service

                 XML  - Extensible Markup Language

                 VXML - Voice XML

                 URL  - Uniform Resource Locator  
                "
        ::= { ciscoMgmt 219 }

--
-- Textual Conventions
--

DnisMapname ::= TEXTUAL-CONVENTION
        DISPLAY-HINT "32a"
        STATUS       current
        DESCRIPTION
            "An identification for a DNIS map name or a DNIS name. A
             DNIS map name correspods to a group of individual DNIS
             names. The DNIS map names are unique in the system, and
             within each map name, individual DNIS names are unique.
            "
        SYNTAX       OCTET STRING (SIZE (0..32)) 

CvE164String ::= TEXTUAL-CONVENTION
        DISPLAY-HINT "32a"
        STATUS       current
        DESCRIPTION
            "A UTF-8 string limited to the character set defined for
             E.164, '0123456789*#,<quote>'.
             Note that <quote> represents the double quote which
             cannot be contained in a SMI description clause."
        SYNTAX       OCTET STRING (SIZE (1..32))

-- Voice DNIS MIB objects definitions

cvDnisMIBObjects OBJECT IDENTIFIER ::= { ciscoVoiceDnisMIB 1 }

-- The Voice DNIS MIB consists of the following group
-- [1] Voice Dnis Mapping (cvDnisMap)

cvDnisMap                   OBJECT IDENTIFIER ::= { cvDnisMIBObjects 1 }

--********************************************************************
-- Voice DNIS Mapping
--********************************************************************
-- The objects in this group are responsible for providing a relation
-- between a DNIS map name and the individual DNIS entries it
-- contains. 

--
-- Voice DNIS Mapping Table
--

cvDnisMappingTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvDnisMappingEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The table contains the map name and a url specifying
             a file name. The file contains DNIS entries that belong
             to the DNIS mapping.
            "
       ::= { cvDnisMap 1 }

cvDnisMappingEntry OBJECT-TYPE
        SYNTAX      CvDnisMappingEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Information about a single DNIS mapping. There is a
             unique DNIS map name. New DNIS mapping can be created
             using cvDnisMappingStatus.

             The entry can be created with or without a file location 
             specified by cvDnisMappingUrl. The mapping file contains
             DNIS name and VXML page per line. For example, a 
             cvDnisMappingUrl could be tftp://someserver/dnismap.txt.
             This file is a text file and the content format is
               dnis <dnisname> url <urlname>.
             An example of the contents of the file itself can be
               dnis 18004251234 url http://www.b.com/p/vwelcome.vxml
               dnis 18004253421 url http://www.c.com/j/vxmlintf.vxml
             If a mapping file location is specified, then new rows
             corresponding to this map name are created and populated
             in cvDnisNodeTable from the contents of the file. The
             rows corresponding to this map name in cvDnisNodeTable
             cannot be created or modified or deleted but can be
             read. 

             If a mapping file location is not specified in
             cvDnisMappingUrl, then individual DNIS entries
             corresponding to this map name can be created, modified
             and deleted in cvDnisNodeTable. 

             Deleting an entry deletes all the related entries in
             cvDnisNodeTable. 
            "
        INDEX       { IMPLIED cvDnisMappingName }
      ::= { cvDnisMappingTable 1 }

CvDnisMappingEntry ::= SEQUENCE {
            cvDnisMappingName               DnisMapname,
            cvDnisMappingUrl                DisplayString,
            cvDnisMappingRefresh            INTEGER,
            cvDnisMappingUrlAccessError     DisplayString,
            cvDnisMappingStatus             RowStatus
        }

cvDnisMappingName OBJECT-TYPE
        SYNTAX      DnisMapname (SIZE (1..32))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The name which uniquely identifies a DNIS mapping. 
            "
        ::= { cvDnisMappingEntry 1 }

cvDnisMappingUrl OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The url specifies a file location. The file contains
             individual DNIS entries that belong to the DNIS map 
             name specified by cvDnisMappingName.

             Once a url is created and associated with a map name (the
             association is complete when the row is made active(1)),
             it cannot be modified while cvDnisMappingStatus is
             active. If a different url needs to be associated with
             the current map name, the row status should be made
             notInService(2) and this object has to be modified to
             associate a new url. When a new association is made all
             the DNIS entries corresponding to the old association
             will be deleted from the cvDnisNodeTable.

             The url is read when the row status is made active(1) or
             when the row status is active and the object 
	     cvDnisMappingRefresh is explicitly set to refresh(2). 
	     If the url is not accessible then a
             cvDnisMappingUrlInaccessible notification will be
             generted. 
            "
        DEFVAL { "" }
        ::= { cvDnisMappingEntry 2 }

cvDnisMappingRefresh OBJECT-TYPE
        SYNTAX      INTEGER {
            idle     (1),
            refresh  (2)
        }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Whenever there is a need to re-read the contents of the
             file specified by cvDnisMappingUrl, this object can be
             set to refresh(2). This will cause the contents of the
             file to be re-read and correspondingly update the
             cvDnisNodeTable. After the completion of this operation,
             the value of this object is reset to idle(1). The only
             operation allowed on this object is setting it to
             refresh(2). This can only be done when the current value
             is idle(1) and the rowstatus is active(1).
 
             idle       - The refreshing process is idle and the user
                          can modify this object to refresh.
             refresh    - The refreshing process is currently busy and
                          the user have to wait till the object
                          becomes idle to issue new refresh.
            "
         DEFVAL { idle }
         ::= { cvDnisMappingEntry 3 }

cvDnisMappingUrlAccessError OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "ASCII text describing the error on last access of the url
             specified in cvDnisMappingUrl.

             If the url access does not succeed, then this object is
             populated with an error message indicating the reason for
             failure. If the url access succeeds, this object is set
             to null string.
            "
        DEFVAL { "" }
        ::= { cvDnisMappingEntry 4 }

cvDnisMappingStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object is used to create a new row or modify or
             delete an existing row in this table. When making the
             status active(1), if a valid cvDnisMappingUrl is present
             the contents of the url is downloaded and during that
             time cvDnisMappingRefresh is set to refresh(2). When
             cvDnisMappingRefresh is set to refresh(2), only the
             destroy(6) operation is allowed.
            "
        ::= { cvDnisMappingEntry 5 }


--
-- Voice DNIS Node Table
--

cvDnisNodeTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF CvDnisNodeEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The table contains a DNIS name and a url. The url is a
             pointer to a VXML page for the DNIS name. 
            "
       ::= { cvDnisMap 2 }

cvDnisNodeEntry OBJECT-TYPE
        SYNTAX      CvDnisNodeEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Each entry is a DNIS name and the location of the
             associated VXML page. New DNIS entries can be created or
             the existing entries can be modified or deleted only if
             the corresponding map name (defined in
             cvDnisMappingTable) does not have any file name provided
             in the cvDnisMappingUrl object. 

             If a file name is provided in cvDnisMappingUrl
             corresponding to this entry's map name, then this row
             will have read permission only.
            "
        INDEX       { cvDnisMappingName, IMPLIED cvDnisNumber }
      ::= { cvDnisNodeTable 1 }
            
CvDnisNodeEntry ::= SEQUENCE {
            cvDnisNumber                    CvE164String,
            cvDnisNodeUrl                   DisplayString,
            cvDnisNodeModifiable            TruthValue,
            cvDnisNodeStatus                RowStatus
        }

cvDnisNumber OBJECT-TYPE
        SYNTAX      CvE164String
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The individual DNIS name. It is unique within a DNIS
             mapping.
            " 
        ::= { cvDnisNodeEntry 1 }

cvDnisNodeUrl OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The url specifies a VXML page. This page contains
             voice XML links to play audio data.

             This url which is a VXML page is not read immediately
             when the row is made active(1), but only when a call that
             requires the use of this DNIS comes through.
            "
        DEFVAL { "" }
        ::= { cvDnisNodeEntry 2 }

cvDnisNodeModifiable OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object specifies whether the object in a particular
             row is modifiable. The object is set to true(1) if the
             corresponding map name (defined in cvDnisMappingTable)
             does not have any file name provided in the
             cvDnisMappingUrl object. Otherwise this object is set to
             false(2) and the row becomes read only.  
            "
        ::= { cvDnisNodeEntry 3 }

cvDnisNodeStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object is used to create a new row or modify or
             delete an existing row in this table. The objects in a
             row can be modified or deleted while the row status is
             active(1) and cvDnisNodeModifiable is true(1). The row
             status cannot be set to notInService(2) or
             createAndWait(5). 
            "
        ::= { cvDnisNodeEntry 4 }

--********************************************************************
-- Notifications
--********************************************************************
cvDnisMIBNotificationPrefix OBJECT IDENTIFIER ::=
                                { ciscoVoiceDnisMIB 2 }
cvDnisMIBNotifications      OBJECT IDENTIFIER ::=
                                { cvDnisMIBNotificationPrefix 0 }

cvDnisMappingUrlInaccessible NOTIFICATION-TYPE
        OBJECTS {
            cvDnisMappingUrl,
            cvDnisMappingUrlAccessError
        }
        STATUS current
        DESCRIPTION
            "Inaccessible DNIS mapping url notification. A
             cvDnisMappingUrlInaccessible notification is sent if the
             specified url is not accessible.
            "
        ::= { cvDnisMIBNotifications 1 }

cvDnisMIBConformance OBJECT IDENTIFIER ::=
                                { ciscoVoiceDnisMIB 3 }
cvDnisMIBCompliances OBJECT IDENTIFIER ::=
                                { cvDnisMIBConformance 1 }
cvDnisMIBGroups      OBJECT IDENTIFIER ::=
                                { cvDnisMIBConformance 2 }

-- compliance statements

cvDnisMIBCompliance MODULE-COMPLIANCE
        STATUS      current
        DESCRIPTION
            "The compliance statement for entities which
             implement the CISCO VOICE DNIS MIB"
        MODULE      -- this module

        GROUP cvDnisGroup
        DESCRIPTION
            "This group is mandatory for mapping of voice calls on an 
             agent through Dialer Number Information Service (DNIS).
            "

	GROUP cvDnisNotificationGroup
	DESCRIPTION
	    "This group is mandatory for notifying if the specified
	     url is not accessible.
	    "
        ::= { cvDnisMIBCompliances 1 }


-- units of conformance


cvDnisGroup OBJECT-GROUP
        OBJECTS {
            cvDnisMappingUrl,
            cvDnisMappingRefresh,
            cvDnisMappingUrlAccessError,
            cvDnisMappingStatus,
            cvDnisNodeUrl,
            cvDnisNodeModifiable,
            cvDnisNodeStatus
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects provides a relation between a
             DNIS map name and the DNIS entries belonging to that map
             name. 
            "  
        ::= { cvDnisMIBGroups 1 }

cvDnisNotificationGroup NOTIFICATION-GROUP
        NOTIFICATIONS {
            cvDnisMappingUrlInaccessible
        }
        STATUS      current
        DESCRIPTION
            "The notifications for CISCO-VOICE-DNIS-MIB.
            "
        ::= { cvDnisMIBGroups 2 }
END
