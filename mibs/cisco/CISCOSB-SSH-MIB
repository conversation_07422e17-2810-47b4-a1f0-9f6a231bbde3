CISCOSB-SSH-MIB DEFINITIONS ::= BEGIN

-- Title:      <PERSON><PERSON><PERSON><PERSON> PHY MIB
-- Version:    7.45
-- Date:       19 Dec 2006
--
IMPORTS
    OBJECT-TYPE, MODULE-IDENTITY,
    Unsigned32, Ip<PERSON>ddress                       FROM SNMPv2-<PERSON><PERSON>
    TEXTUAL-CONVENTION, <PERSON><PERSON>lay<PERSON>tring, RowStatus
                                                FROM SNMPv2-TC
    switch001                                   FROM CISCOSB-MIB
    InetAddressType, InetAddress                FROM INET-ADDRESS-MIB;


   rlSsh MODULE-IDENTITY
      LAST-UPDATED "202105170000Z"  -- September 30, 2002
      ORGANIZATION "Cisco Systems, Inc."

      CONTACT-INFO
      "Postal: 170 West Tasman Drive
      San Jose , CA 95134-1706
      USA

      
      Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

      DESCRIPTION
              "The MIB module describes the private MIB for SSH supported
               by CISCOSB's software and products."
      REVISION     "200301030024Z"  -- January 04, 2003
      DESCRIPTION
              "The second revision"
      REVISION     "200309210024Z"  -- September 21, 2003
      DESCRIPTION
              "Editorial changes."
      ::= { switch001 78  }

--
-- Textual Conventions
--
RlSshPublicKeyAlgorithm ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "This textual convention describes the various possible public key
         algorithms.  The key algorithm is used to select the PK to be generated
         and is also used when viewing the public keys."

    SYNTAX  INTEGER {
        rsa1(0),
        rsa(1),
        dsa(2),
        ec(3),
        none(999)
    }

RlSshPublicKeyDigestFormat ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "This textual convention describes the format used to display the public
         key fingerprint.  The hex format is the format used by PGP and OpenSSH.
         The bubble-babble format is used by SSH.com software."

    SYNTAX  INTEGER {
        hex(0),
        bubbleBabble(1)
    }

rlSshMibVersion OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The MIB version. The current version is 2"
    ::= { rlSsh 1 }

--
-- Server tables
--
rlSshServer OBJECT IDENTIFIER ::= { rlSsh 2 }

--
-- Host Public Key Table
--
rlSshServerHostPublicKeyTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshServerHostPublicKeyTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "This table contains the router's public key.  Each row in this table
     contains a fragment of the key, in printable binhex format.  There may
     be up to 160 characters in every fragment, and they are all combined
     to form one key.  The key is generated by writing to
     rlSshServerRegenerateHostKey.  To cause clients to connect to this router
     without printing warning messages (and also prevent active
     man-in-the-middle), the router's public key must printed out and inserted
     into the client's authorized_keys file"
    ::= { rlSshServer 1 }

rlSshServerHostPublicKeyTableEntry  OBJECT-TYPE
   SYNTAX RlSshServerHostPublicKeyTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshServerHostPublicKeyAlgorithm, rlSshServerHostPublicKeyFragmentId }
   ::= { rlSshServerHostPublicKeyTable 1 }

RlSshServerHostPublicKeyTableEntry ::= SEQUENCE {
  rlSshServerHostPublicKeyAlgorithm           RlSshPublicKeyAlgorithm,
  rlSshServerHostPublicKeyFragmentId          Unsigned32,
  rlSshServerHostPublicKeyFragmentText        DisplayString
}

rlSshServerHostPublicKeyAlgorithm   OBJECT-TYPE
   SYNTAX RlSshPublicKeyAlgorithm
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Identifies the type of public key to be displayed."
     ::=  { rlSshServerHostPublicKeyTableEntry  1 }

rlSshServerHostPublicKeyFragmentId   OBJECT-TYPE
   SYNTAX Unsigned32
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Identifies the index of this fragment in the final key.  All segments must
     be combined to form one big key."
     ::=  { rlSshServerHostPublicKeyTableEntry  2 }

rlSshServerHostPublicKeyFragmentText       OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "A part of the readable text entry for the server's public authorzation key."
     ::=  { rlSshServerHostPublicKeyTableEntry  3 }

--
-- Host Public Key Fingerprint Table
--
rlSshServerHostPublicKeyFingerprintTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshServerHostPublicKeyFingerprintTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "This table contains the fingerprint for the router's public key."
    ::= { rlSshServer 2 }

rlSshServerHostPublicKeyFingerprintTableEntry  OBJECT-TYPE
   SYNTAX RlSshServerHostPublicKeyFingerprintTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshServerHostPublicKeyFingerprintAlgorithm, rlSshServerHostPublicKeyFingerprintDigestFormat }
   ::= { rlSshServerHostPublicKeyFingerprintTable 1 }

RlSshServerHostPublicKeyFingerprintTableEntry ::= SEQUENCE {
  rlSshServerHostPublicKeyFingerprintAlgorithm           RlSshPublicKeyAlgorithm,
  rlSshServerHostPublicKeyFingerprintDigestFormat        RlSshPublicKeyDigestFormat,
  rlSshServerHostPublicKeyFingerprint                    DisplayString
}

rlSshServerHostPublicKeyFingerprintAlgorithm   OBJECT-TYPE
   SYNTAX RlSshPublicKeyAlgorithm
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Identifies the type of public key to be displayed."
     ::=  { rlSshServerHostPublicKeyFingerprintTableEntry  1 }

rlSshServerHostPublicKeyFingerprintDigestFormat   OBJECT-TYPE
   SYNTAX RlSshPublicKeyDigestFormat
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Format of the digest to be displayed (OpenSSH or SSH.com)."
     ::=  { rlSshServerHostPublicKeyFingerprintTableEntry 2 }

rlSshServerHostPublicKeyFingerprint   OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "SECSH format fingerprint of the server's public key.  To prevent man in
     the middle attacks, users should make sure the ssh Server's fingerprint,
     as printed in the connection process, is similar to the one printed here."
     ::=  { rlSshServerHostPublicKeyFingerprintTableEntry 3 }


--
-- Authorized Key Table
--
rlSshServerAuthorizedUsersPublicKeyTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshServerAuthorizedUsersPublicKeyTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "This table contains public keys for all users who are authorized to access
    the router.  For a user to be able to log in using SSH, the user name must
    appear in this table, and the user's public key must match the one found
    here."
     ::= { rlSshServer 3 }

rlSshServerAuthorizedUsersPublicKeyTableEntry  OBJECT-TYPE
   SYNTAX RlSshServerAuthorizedUsersPublicKeyTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshServerAuthorizedUserName,
           rlSshServerAuthorizedUserPublicKeyFragmentId }
   ::= { rlSshServerAuthorizedUsersPublicKeyTable 1 }

RlSshServerAuthorizedUsersPublicKeyTableEntry ::= SEQUENCE {
  rlSshServerAuthorizedUserName        DisplayString,
  rlSshServerAuthorizedUserPublicKeyFragmentId      Unsigned32,
  rlSshServerAuthorizedUserPublicKeyFragmentText    DisplayString,
  rlSshServerAuthorizedUserPublicKeyFragmentStatus  RowStatus
}

rlSshServerAuthorizedUserName   OBJECT-TYPE
   SYNTAX DisplayString (SIZE(1..48))
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "Name of the user who owns this public key.  Both the user name and the
    key bytes must match before a user is authenticated using this key."
     ::=  { rlSshServerAuthorizedUsersPublicKeyTableEntry  1 }

rlSshServerAuthorizedUserPublicKeyFragmentId   OBJECT-TYPE
   SYNTAX Unsigned32
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "Identifies the index of this fragment in the final key.  All segments must
     be combined to form one big key."
     ::=  { rlSshServerAuthorizedUsersPublicKeyTableEntry  2 }

rlSshServerAuthorizedUserPublicKeyFragmentText       OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "A part of the readable text entry for the user's public authorzation key."
     ::=  { rlSshServerAuthorizedUsersPublicKeyTableEntry  3 }

rlSshServerAuthorizedUserPublicKeyFragmentStatus       OBJECT-TYPE
   SYNTAX RowStatus
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "Create or delete a fragment of the user's public key.
    A user is deleted if it has no remaining fragments."
     ::=  { rlSshServerAuthorizedUsersPublicKeyTableEntry  4 }

--
-- New 01-01-03: Authorized user fingerprint table.
--
rlSshServerAuthorizedUsersPublicKeyFingerprintTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "This table contains the fingerprints of the public keys for all users who
     are authorized to access the router.
     To prevent man in the middle attacks, users should make sure the
     user's fingerprint, as printed in the connection process, is similar
     to the one printed here."
     ::= { rlSshServer 5 }

rlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry  OBJECT-TYPE
   SYNTAX RlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshServerAuthorizedUserFingerprintName,
           rlSshServerAuthorizedUserPublicKeyFingerprintDigestFormat }
   ::= { rlSshServerAuthorizedUsersPublicKeyFingerprintTable 1 }

RlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry ::= SEQUENCE {
      rlSshServerAuthorizedUserFingerprintName        DisplayString,
      rlSshServerAuthorizedUserPublicKeyFingerprintDigestFormat        RlSshPublicKeyDigestFormat,
      rlSshServerAuthorizedUserPublicKeyFingerprintAlgorithm           RlSshPublicKeyAlgorithm,
      rlSshServerAuthorizedUserPublicKeyFingerprint                    DisplayString
}

rlSshServerAuthorizedUserFingerprintName   OBJECT-TYPE
   SYNTAX DisplayString (SIZE(1..48))
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "Name of the user who owns this public key.  Both the user name and the
     key bytes must match before a user is authenticated using this key."
     ::=  { rlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry  1 }

rlSshServerAuthorizedUserPublicKeyFingerprintAlgorithm   OBJECT-TYPE
   SYNTAX RlSshPublicKeyAlgorithm
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Identifies the type of public key to be displayed."
     ::=  { rlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry  2 }

rlSshServerAuthorizedUserPublicKeyFingerprintDigestFormat   OBJECT-TYPE
   SYNTAX RlSshPublicKeyDigestFormat
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Format of the digest to be displayed (OpenSSH or SSH.com)."
     ::=  { rlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry 3 }

rlSshServerAuthorizedUserPublicKeyFingerprint   OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "SECSH format fingerprint of the user's public key.  To prevent man in
     the middle attacks, users should make sure their ssh fingerprint,
     as printed in the connection process, is similar to the one printed here."
     ::=  { rlSshServerAuthorizedUsersPublicKeyFingerprintTableEntry 4 }


--
-- Session Table
--
rlSshServerSessionTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshServerSessionTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "Each row in this table corresponds to an active SSH session with
     the server"
     ::= { rlSshServer 6 }

rlSshServerSessionTableEntry  OBJECT-TYPE
   SYNTAX RlSshServerSessionTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshServerSessionIdentifier }
   ::= { rlSshServerSessionTable 1 }

RlSshServerSessionTableEntry ::= SEQUENCE {
  rlSshServerSessionIdentifier      Unsigned32,
  rlSshServerSessionPeerAddress     IpAddress,
  rlSshServerSessionPeerPort        Unsigned32,
  rlSshServerSessionUsername        DisplayString,
  rlSshServerSessionPeerVersion     DisplayString,
  rlSshServerSessionCipher          DisplayString,
  rlSshServerSessionHMAC            DisplayString
}

rlSshServerSessionIdentifier   OBJECT-TYPE
   SYNTAX Unsigned32
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Identifies the connection to which this row corresponds."
     ::=  { rlSshServerSessionTableEntry 1 }

rlSshServerSessionPeerAddress   OBJECT-TYPE
   SYNTAX IpAddress
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "The network address of the remote host connected to the server."
     ::=  { rlSshServerSessionTableEntry 2 }

rlSshServerSessionPeerPort      OBJECT-TYPE
   SYNTAX Unsigned32
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "The source network port of the remote host connected to the server."
     ::=  { rlSshServerSessionTableEntry 3 }

rlSshServerSessionPeerVersion   OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "The client version of the remote host connected to the server."
     ::=  { rlSshServerSessionTableEntry 4 }

rlSshServerSessionUsername      OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "SSH authenticated name of user connected to the server."
     ::=  { rlSshServerSessionTableEntry 5 }

rlSshServerSessionCipher      OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Data encryption cipher used in this connection."
     ::=  { rlSshServerSessionTableEntry 6 }

rlSshServerSessionHMAC      OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-only
   STATUS current
   DESCRIPTION
    "Message authentication code used in this connection."
     ::=  { rlSshServerSessionTableEntry 7 }

--
-- Session Inet Table
--
rlSshServerSessionInetTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshServerSessionInetTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION  "Each row in this table corresponds to an active SSH session with the server"
     ::= { rlSshServer 7 }

rlSshServerSessionInetTableEntry  OBJECT-TYPE
   SYNTAX       RlSshServerSessionInetTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION  "The row definition for this table."
   INDEX { rlSshServerSessionInetIdentifier }
   ::= { rlSshServerSessionInetTable 1 }

RlSshServerSessionInetTableEntry ::= SEQUENCE {
  rlSshServerSessionInetIdentifier      Unsigned32,
  rlSshServerSessionInetPeerAddressType InetAddressType,
  rlSshServerSessionInetPeerAddress     InetAddress,
  rlSshServerSessionInetPeerPort        Unsigned32,
  rlSshServerSessionInetUsername        DisplayString,
  rlSshServerSessionInetPeerVersion     DisplayString,
  rlSshServerSessionInetCipher          DisplayString,
  rlSshServerSessionInetHMAC            DisplayString
}

rlSshServerSessionInetIdentifier   OBJECT-TYPE
   SYNTAX       Unsigned32
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "Identifies the connection to which this row corresponds."
     ::=  { rlSshServerSessionInetTableEntry 1 }

rlSshServerSessionInetPeerAddressType   OBJECT-TYPE
   SYNTAX       InetAddressType
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "The network address of the remote host connected to the server."
     ::=  { rlSshServerSessionInetTableEntry 2 }

rlSshServerSessionInetPeerAddress   OBJECT-TYPE
   SYNTAX       InetAddress
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "The network address of the remote host connected to the server."
     ::=  { rlSshServerSessionInetTableEntry 3 }

rlSshServerSessionInetPeerPort      OBJECT-TYPE
   SYNTAX       Unsigned32
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "The source network port of the remote host connected to the server."
     ::=  { rlSshServerSessionInetTableEntry 4 }

rlSshServerSessionInetPeerVersion   OBJECT-TYPE
   SYNTAX       DisplayString
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "The client version of the remote host connected to the server."
     ::=  { rlSshServerSessionInetTableEntry 5 }

rlSshServerSessionInetUsername      OBJECT-TYPE
   SYNTAX       DisplayString
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "SSH authenticated name of user connected to the server."
     ::=  { rlSshServerSessionInetTableEntry 6 }

rlSshServerSessionInetCipher      OBJECT-TYPE
   SYNTAX       DisplayString
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "Data encryption cipher used in this connection."
     ::=  { rlSshServerSessionInetTableEntry 7 }

rlSshServerSessionInetHMAC      OBJECT-TYPE
   SYNTAX       DisplayString
   MAX-ACCESS   read-only
   STATUS       current
   DESCRIPTION  "Message authentication code used in this connection."
     ::=  { rlSshServerSessionInetTableEntry 8 }

rlSshServerImportExportSelfKeyTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshServerImportExportSelfKeyEntry
   MAX-ACCESS     not-accessible
   STATUS     current
   DESCRIPTION
    "This table can be used for 2 purposes:
     1) Importing public/private key pair to serve as the device key when acting
     as SSH server. This is done by setting entries to this table, according to
     the specified format. When the last entry (footer) is set, the whole key
     pair is checked and if valid, stored in CDB.
     2) Exporting the device SSH server public/private key. This can be done by
     performing GetNext operations on this table."
    ::= { rlSshServer 8 }

rlSshServerImportExportSelfKeyEntry  OBJECT-TYPE
   SYNTAX RlSshServerImportExportSelfKeyEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshServerImportExportSelfKeyAlgorithm,
           rlSshServerImportExportSelfKeyFormat,
           rlSshServerImportExportSelfKeyFragmentId}
   ::= { rlSshServerImportExportSelfKeyTable 1 }

RlSshServerImportExportSelfKeyEntry ::= SEQUENCE {
  rlSshServerImportExportSelfKeyAlgorithm       RlSshPublicKeyAlgorithm,
  rlSshServerImportExportSelfKeyFormat          INTEGER,
  rlSshServerImportExportSelfKeyFragmentId      INTEGER,
  rlSshServerImportExportSelfKeyFragmentText    OCTET STRING
}

rlSshServerImportExportSelfKeyAlgorithm   OBJECT-TYPE
   SYNTAX RlSshPublicKeyAlgorithm
   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Identifies the type of key pair."
     ::=  { rlSshServerImportExportSelfKeyEntry  1 }

rlSshServerImportExportSelfKeyFormat   OBJECT-TYPE
   SYNTAX INTEGER {
            uuencoded-format(1)
           }

   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Specifies the format of public/key pair. The following formats are
    supported for import/export:
    1) uuencoded_format - in this format both private and public key are in
    uu-encoded format, and are seperated from one another by header and footer.
    An example of the concateneation of all fragments in this format is:
    -----BEGIN RSA PRIVATE KEY-----
    tDaNkZZoCxXfkLLsLDlZ6T9H8U4Gz637eRV7BYBpapkidAxY1UG0/qgfKLPkbId4wzht6ArV9bE4fMtX
    wSMOxL3q31pOmUdVhjbWYcgZQBDy1DjFSbuIEdP85c96B9bBE2nPv90mSvb/6j8R2NItT/KJeiYMtLtI
    N3m6/zESKJGIrX0jP1RFDjVZSS5chSAFgB0egsLZEyOQn7jAqpX/x/easD2J6F/OjPXlJ9Hff2tMb3NU
    QYyBoWH2J9IxhWB6Vo66R9Y04JGR18uL/rV2sMCtpg5ppkVTEpNTp9qE1yXocR2NmzUfNFap+GJ4IHj8
    CzkVfmJM/kEWaJsYgHbAgLyRg4QVyelfobv1B71aQ+u1z9KGu/QajkWdR04OQfsGOL1CvU2LGYDcRjfH
    jv+jl/UkDRRjoD9kt2WvouT+OL6esvKl0OJBqWbGNXg9TWv/VLtJIwgUno+MLaJuOM4Fh44+wpnqUXwQ
    TFtBFc8pzt5BoOwbv9gXpicTkq4/+GhwXWXxSVFebKhnHAvKSLT+Ba7K7ZeR8EIIxbXdDNFOiS45R2KI
    jxxXLXK44u6KGl5MygCKXUOFlJ+Zhgrq6ZH17z/RVJQ2CWqb5Ekn9GY3kH9QZ3mb4MDPfriWi2lHGXHY
    JmJd4SLQhpBdnOS5tu84QmyU3dNbAdzghDsR+dEY/6g7Cn0kcVkeHNZ0H+mCZik5f6XBD8eplkk43bdR
    FrkwTeAjwurGcKwdiKkR4DlfSq3DKssVBucTqUpqsKqPXLwTIL44rWKhEPXgGPB2XDG0VLvIRKkAgEGI
    LNTwOm091Ro=
    -----END RSA PRIVATE KEY-----

    -----BEGIN RSA PUBLIC KEY-----
    MIGHAoGBAOeIC9gRg3YaEGGMp3C00qNwLINAEDZV/J4BWM5WnWwCWZyHXDs2XiEmFu0ZONAD4gcT2f2f
    NNfCBPye39VVuOkKQuSV0MLLX5800LZ4y1BNcPzPZHpnGaCzl7iAjhfj9MolzAh5VRjeiYt0t1um4dm+
    q9A4BtfpJqDYxCW9uxd/AgEj
    -----END RSA PUBLIC KEY-----
    "
     ::=  { rlSshServerImportExportSelfKeyEntry  2 }

rlSshServerImportExportSelfKeyFragmentId   OBJECT-TYPE
   SYNTAX INTEGER
   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Identifies the index of this fragment in the key pair input/output."
     ::=  { rlSshServerImportExportSelfKeyEntry  3 }

rlSshServerImportExportSelfKeyFragmentText    OBJECT-TYPE
   SYNTAX OCTET STRING
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "A part of the readable text entry for the key pair input/output."
     ::=  { rlSshServerImportExportSelfKeyEntry  4 }

--
-- SSH Server Scalars
--
rlSshServerPort OBJECT-TYPE
    SYNTAX      Unsigned32(1..65535)
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "Specifies the TCP port used by the SSH 2 Server to listen for incoming
         connections."
    DEFVAL      { 22 }
    ::= { rlSshServer 101 }

rlSshServerEnable OBJECT-TYPE
    SYNTAX   INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "Enables listening for incoming SSH2 connections on the port defined in
        rlSshServerPort."
    ::= { rlSshServer 102 }

rlSshServerEnablePublicKeyAuthentication OBJECT-TYPE
    SYNTAX   INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "If PK authentication is enabled, incoming SSH connections are
        authenticated using public key authentication (using
        rlSshServerAuthorizedUsersPublicKeyTable), before authenticating using
        the router's standard AAA."
    ::= { rlSshServer 103 }

rlSshServerRegenerateHostKey OBJECT-TYPE
    SYNTAX  RlSshPublicKeyAlgorithm
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "Setting to a value other than none results in the Server (Host) Key
        being regenerated.  The key size is host-specific."
    ::= { rlSshServer 104 }

rlSshServerDefaultKeyFlag OBJECT-TYPE
    SYNTAX  INTEGER {
           rsa(1),
           dsa(2),
           rsa-dsa(3),
           ec(4),
           all(99),
           none(100)
    }
    MAX-ACCESS      read-only
    STATUS current
    DESCRIPTION
        "This scalar indicates which of the SSH server keys are default keys
         (automatically generated)."
    ::= { rlSshServer 105 }

rlSshServerDeleteSelfKey OBJECT-TYPE
    SYNTAX  RlSshPublicKeyAlgorithm
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
           "Setting to a value other than 'none' results in deleting the server self
           key of the chosen type. As a result a default key may be created
           (according to the MTSC..)"
      ::= { rlSshServer 106 }

rlSshServerEnablePublicKeyAuthAutoLogin OBJECT-TYPE
    SYNTAX   INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "If PK authentication is succesfull and AAA local DB method
        is enabled for SSH line, the username is checked with AAA
        local DB. If present, the user is authenticated automatically
        without prompting for username or password."
    ::= { rlSshServer 107 }

rlSshServerEnablePasswordAuthentication OBJECT-TYPE
    SYNTAX   INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "If password authentication is enabled, incoming SSH connections are
        authenticated with username/password authentication using
        the router's standard AAA."
    ::= { rlSshServer 108 }
--
-- SSH Client Scalars
--
rlSshClient OBJECT IDENTIFIER ::= { rlSsh 3 }

rlSshClientUserName OBJECT-TYPE
    SYNTAX      DisplayString(SIZE(1..70))
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "Specifies the default user name the ssh client will use when
        authenticating to a remote server. In SCP sessions this global
        user name will be used unless a specific user name has been specified
        for the operation.
        The value of this MIB must not contain charachters ':' or '@' (those
        are used to indicate parameters seperation in SCP copy)."
    DEFVAL { "anonymous" }
    ::= { rlSshClient 1 }

rlSshClientRegenerateSelfKey OBJECT-TYPE
    SYNTAX  RlSshPublicKeyAlgorithm
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "Setting to a value other than none results in the client self key
        being regenerated.  The key size is host-specific."
    ::= { rlSshClient 2 }

--
-- Client Self Public Key Table
--
rlSshClientSelfPublicKeyTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshClientSelfPublicKeyTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "This table contains the router's client self public key.  Each row in
    this table contains a fragment of the key, in printable binhex format.
    There may be up to 160 characters in every fragment, and they are all
    combined to form one key.  The key is generated by writing to
    rlSshClientRegenerateSelfKey.  To cause clients to connect to this router
    without printing warning messages (and also prevent active
    man-in-the-middle), the router's public key must printed out and
    inserted into the client's authorized_keys file"
    ::= { rlSshClient 3 }

rlSshClientSelfPublicKeyTableEntry  OBJECT-TYPE
   SYNTAX RlSshClientSelfPublicKeyTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshClientSelfPublicKeyAlgorithm, rlSshClientSelfPublicKeyFragmentId }
   ::= { rlSshClientSelfPublicKeyTable 1 }

RlSshClientSelfPublicKeyTableEntry ::= SEQUENCE {
  rlSshClientSelfPublicKeyFragmentId      Unsigned32,
  rlSshClientSelfPublicKeyAlgorithm       RlSshPublicKeyAlgorithm,
  rlSshClientSelfPublicKeyFragmentText        DisplayString
}

rlSshClientSelfPublicKeyFragmentId   OBJECT-TYPE
   SYNTAX Unsigned32
   MAX-ACCESS read-only
   STATUS current
   DESCRIPTION
    "Identifies the index of this fragment in the final key.  All segments must
     be combined to form one big key."
     ::=  { rlSshClientSelfPublicKeyTableEntry  1 }

rlSshClientSelfPublicKeyAlgorithm   OBJECT-TYPE
   SYNTAX RlSshPublicKeyAlgorithm
   MAX-ACCESS read-only
   STATUS current
   DESCRIPTION
    "Identifies the type of public key to be displayed."
     ::=  { rlSshClientSelfPublicKeyTableEntry  2 }

rlSshClientSelfPublicKeyFragmentText       OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "A part of the readable text entry for the router's client public
     authorization key."
     ::=  { rlSshClientSelfPublicKeyTableEntry  3 }

--
-- Client Self Key Fingerprint Table
--
rlSshClientSelfPublicKeyFingerprintTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshClientSelfPublicKeyFingerprintTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "This table contains the fingerprint for the client's self key.  "
    ::= { rlSshClient 4 }

rlSshClientSelfPublicKeyFingerprintTableEntry  OBJECT-TYPE
   SYNTAX RlSshClientSelfPublicKeyFingerprintTableEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshClientSelfPublicKeyFingerprintAlgorithm, rlSshClientSelfPublicKeyFingerprintDigestFormat }
   ::= { rlSshClientSelfPublicKeyFingerprintTable 1 }

RlSshClientSelfPublicKeyFingerprintTableEntry ::= SEQUENCE {
  rlSshClientSelfPublicKeyFingerprintAlgorithm           RlSshPublicKeyAlgorithm,
  rlSshClientSelfPublicKeyFingerprintDigestFormat        RlSshPublicKeyDigestFormat,
  rlSshClientSelfPublicKeyFingerprint                    DisplayString
}

rlSshClientSelfPublicKeyFingerprintAlgorithm   OBJECT-TYPE
   SYNTAX RlSshPublicKeyAlgorithm
   MAX-ACCESS read-only
   STATUS current
   DESCRIPTION
    "Identifies the type of public key to be displayed."
     ::=  { rlSshClientSelfPublicKeyFingerprintTableEntry  1 }

rlSshClientSelfPublicKeyFingerprintDigestFormat   OBJECT-TYPE
   SYNTAX RlSshPublicKeyDigestFormat
   MAX-ACCESS read-only
   STATUS current
   DESCRIPTION
    "Format of the digest to be displayed (OpenSSH or SSH.com)."
     ::=  { rlSshClientSelfPublicKeyFingerprintTableEntry 2 }

rlSshClientSelfPublicKeyFingerprint   OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS read-only
   STATUS current
   DESCRIPTION
    "SECSH format fingerprint of the client's self key.  To prevent man in
     the middle attacks, users should make sure the ssh Server's fingerprint,
     as printed in the connection process, is similar to the one printed here."
     ::=  { rlSshClientSelfPublicKeyFingerprintTableEntry 3 }

rlSshClientAuthenticationMethod OBJECT-TYPE
    SYNTAX   INTEGER {
        public-key-rsa(1),
        public-key-dsa(2),
        password(3),
        public-key-ec(4)
    }
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "Specifies the global authentication method for SSH client. SSH client
         will apply this authentication method upon connecting to a remote server,
         if no specific authentication method has been defined for this operation."
    ::= { rlSshClient 5 }


rlSshClientPassword OBJECT-TYPE
    SYNTAX DisplayString(SIZE(0..70))
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
        "Specifies the global password for SSH client, to be used for 'password'
         authentication. SSH client will use this password during authentication
         phase (when connecting remote server), in case no specific password has
         been specified for this operation.
         The value of this MIB must not contain charachters ':' or '@' (those
         are used to indicate parameters seperation in SCP copy).
         Upon setting the password, its length must be in range 1-70."
    ::= { rlSshClient 6 }


rlSshClientPasswordChangeTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshClientPasswordChangeEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
    "This table allows the user to change the password in a remote SSH server.
     It should be used when working with 'password' authentication with remote
     server in SCP sessions.
     Set operation on this table will initiate SCP session with the SSH server
     whose inet address is specified in the key rlSshClientPasswordChangeInetAddress.
     During this session the password for username rlSshClientPasswordChangeUsername
     will be changed in the server from rlSshClientPasswordChangeOldPassword to
     rlSshClientPasswordChangeNewPassword.
     The Set operation might fail, in case an SCP session is already being
     held. In case 'noError' is returned, the user can poll the fields
     rlSshClientPasswordChangeStatus and rlSshClientPasswordChangeFailureReason
     in order to get information on operation status and possible failure
     reason. Note: up to 3 status records can be saved."
    ::= { rlSshClient 7 }

rlSshClientPasswordChangeEntry  OBJECT-TYPE
   SYNTAX RlSshClientPasswordChangeEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshClientPasswordChangeInetAddrType,
           rlSshClientPasswordChangeInetAddr}
   ::= { rlSshClientPasswordChangeTable 1 }

RlSshClientPasswordChangeEntry ::= SEQUENCE {
  rlSshClientPasswordChangeInetAddrType   InetAddressType,
  rlSshClientPasswordChangeInetAddr       InetAddress,
  rlSshClientPasswordChangeUsername       DisplayString,
  rlSshClientPasswordChangeOldPassword    DisplayString,
  rlSshClientPasswordChangeNewPassword    DisplayString,
  rlSshClientPasswordChangeStatus         INTEGER,
  rlSshClientPasswordChangeFailureReason  DisplayString
}

rlSshClientPasswordChangeInetAddrType   OBJECT-TYPE
   SYNTAX InetAddressType
   MAX-ACCESS not-accessible
   STATUS current
   DESCRIPTION
    "Specifies the inet address type of remote SCP server"
     ::=  { rlSshClientPasswordChangeEntry  1 }

rlSshClientPasswordChangeInetAddr     OBJECT-TYPE
   SYNTAX InetAddress
   MAX-ACCESS not-accessible
   STATUS current
   DESCRIPTION
    "Specifies the inet address of remote SCP server"
     ::=  { rlSshClientPasswordChangeEntry  2 }

rlSshClientPasswordChangeUsername   OBJECT-TYPE
   SYNTAX DisplayString(SIZE(1..70))
   MAX-ACCESS read-write
   STATUS current
   DESCRIPTION
    "Specifies the username for which the password change is required."
     ::=  { rlSshClientPasswordChangeEntry  3 }

rlSshClientPasswordChangeOldPassword   OBJECT-TYPE
   SYNTAX DisplayString(SIZE(0..70))
   MAX-ACCESS read-write
   STATUS current
   DESCRIPTION
    "Specifies the old password that was in use for rlSshClientPasswordChangeUsername
     until now. Upon setting a value in this entry, length of this field value
     must be 6-70. Password of length 0 will always be returned on GET operations."
     ::=  { rlSshClientPasswordChangeEntry  4 }

rlSshClientPasswordChangeNewPassword   OBJECT-TYPE
   SYNTAX DisplayString(SIZE(0..70))
   MAX-ACCESS read-write
   STATUS current
   DESCRIPTION
    "Specifies the new password that should be in use for
     rlSshClientPasswordChangeUsername from now on. Upon setting a value in this
     entry, length of this field value must be 6-70.
     Password of length 0 will always be returned on GET operations."
     ::=  { rlSshClientPasswordChangeEntry  5 }

rlSshClientPasswordChangeStatus   OBJECT-TYPE
   SYNTAX INTEGER  {
        inProgress(1),
        succeeded(2),
        failed(3),
        noData(4)
   }
   MAX-ACCESS read-only
   STATUS current
   DESCRIPTION
    "Specifies the status of last password change operation on remote server
    identified by rlSshClientPasswordChangeInetAddress. In case this data was
    erased (3 or more password change operations have been performed since
    the operation on this server), the status will be 'noData'."
     ::=  { rlSshClientPasswordChangeEntry  6 }

rlSshClientPasswordChangeFailureReason   OBJECT-TYPE
   SYNTAX DisplayString
   MAX-ACCESS read-only
   STATUS current
   DESCRIPTION
    "In case the value of rlSshClientPasswordChangeStatus is 'failed', this
     field will contain the failure reason."
     ::=  { rlSshClientPasswordChangeEntry  7 }

rlSshClientDeleteSelfKey OBJECT-TYPE
    SYNTAX  RlSshPublicKeyAlgorithm
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
           "Setting to a value other than 'none' results in deleting the client self
           key of the chosen type."
      ::= { rlSshClient 8 }

rlSshClientImportExportSelfKeyTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshClientImportExportSelfKeyEntry
   MAX-ACCESS     not-accessible
   STATUS     current
   DESCRIPTION
    "This table can be used for 2 purposes:
     1) Importing public/private key pair to serve as the device key when acting
     as SSH client. This is done by setting entries to this table, according to
     the specified format. When the last entry (footer) is set, the whole key
     pair is checked and if valid, stored in CDB.
     2) Exporting the device SSH client public/private key. This can be done by
     performing GetNext operations on this table."
    ::= { rlSshClient 9 }

rlSshClientImportExportSelfKeyEntry  OBJECT-TYPE
   SYNTAX RlSshClientImportExportSelfKeyEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshClientImportExportSelfKeyAlgorithm,
           rlSshClientImportExportSelfKeyFormat,
           rlSshClientImportExportSelfKeyFragmentId}
   ::= { rlSshClientImportExportSelfKeyTable 1 }

RlSshClientImportExportSelfKeyEntry ::= SEQUENCE {
  rlSshClientImportExportSelfKeyAlgorithm       RlSshPublicKeyAlgorithm,
  rlSshClientImportExportSelfKeyFormat          INTEGER,
  rlSshClientImportExportSelfKeyFragmentId      INTEGER,
  rlSshClientImportExportSelfKeyFragmentText    OCTET STRING
}

rlSshClientImportExportSelfKeyAlgorithm   OBJECT-TYPE
   SYNTAX RlSshPublicKeyAlgorithm
   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Identifies the type of key pair."
     ::=  { rlSshClientImportExportSelfKeyEntry  1 }

rlSshClientImportExportSelfKeyFormat   OBJECT-TYPE
   SYNTAX INTEGER {
            uuencoded-format(1)
           }

   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Specifies the format of public/key pair. The following formats are
    supported for import/export:
    1) uuencoded_format - in this format both private and public key are in
    uu-encoded format, and are seperated from one another by header and footer.
    An example of the concateneation of all fragments in this format is:
    -----BEGIN ENCRYPTED RSA PRIVATE KEY-----
    tDaNkZZoCxXfkLLsLDlZ6T9H8U4Gz637eRV7BYBpapkidAxY1UG0/qgfKLPkbId4wzht6ArV9bE4fMtX
    wSMOxL3q31pOmUdVhjbWYcgZQBDy1DjFSbuIEdP85c96B9bBE2nPv90mSvb/6j8R2NItT/KJeiYMtLtI
    N3m6/zESKJGIrX0jP1RFDjVZSS5chSAFgB0egsLZEyOQn7jAqpX/x/easD2J6F/OjPXlJ9Hff2tMb3NU
    QYyBoWH2J9IxhWB6Vo66R9Y04JGR18uL/rV2sMCtpg5ppkVTEpNTp9qE1yXocR2NmzUfNFap+GJ4IHj8
    CzkVfmJM/kEWaJsYgHbAgLyRg4QVyelfobv1B71aQ+u1z9KGu/QajkWdR04OQfsGOL1CvU2LGYDcRjfH
    jv+jl/UkDRRjoD9kt2WvouT+OL6esvKl0OJBqWbGNXg9TWv/VLtJIwgUno+MLaJuOM4Fh44+wpnqUXwQ
    TFtBFc8pzt5BoOwbv9gXpicTkq4/+GhwXWXxSVFebKhnHAvKSLT+Ba7K7ZeR8EIIxbXdDNFOiS45R2KI
    jxxXLXK44u6KGl5MygCKXUOFlJ+Zhgrq6ZH17z/RVJQ2CWqb5Ekn9GY3kH9QZ3mb4MDPfriWi2lHGXHY
    JmJd4SLQhpBdnOS5tu84QmyU3dNbAdzghDsR+dEY/6g7Cn0kcVkeHNZ0H+mCZik5f6XBD8eplkk43bdR
    FrkwTeAjwurGcKwdiKkR4DlfSq3DKssVBucTqUpqsKqPXLwTIL44rWKhEPXgGPB2XDG0VLvIRKkAgEGI
    LNTwOm091Ro=
    -----END RSA PRIVATE KEY-----

    -----BEGIN RSA PUBLIC KEY-----
    MIGHAoGBAOeIC9gRg3YaEGGMp3C00qNwLINAEDZV/J4BWM5WnWwCWZyHXDs2XiEmFu0ZONAD4gcT2f2f
    NNfCBPye39VVuOkKQuSV0MLLX5800LZ4y1BNcPzPZHpnGaCzl7iAjhfj9MolzAh5VRjeiYt0t1um4dm+
    q9A4BtfpJqDYxCW9uxd/AgEj
    -----END RSA PUBLIC KEY-----
    "
     ::=  { rlSshClientImportExportSelfKeyEntry  2 }

rlSshClientImportExportSelfKeyFragmentId   OBJECT-TYPE
   SYNTAX INTEGER
   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Identifies the index of this fragment in the key pair input/output."
     ::=  { rlSshClientImportExportSelfKeyEntry  3 }

rlSshClientImportExportSelfKeyFragmentText    OBJECT-TYPE
   SYNTAX OCTET STRING
   MAX-ACCESS   read-write
   STATUS current
   DESCRIPTION
    "A part of the readable text entry for the key pair input/output."
     ::=  { rlSshClientImportExportSelfKeyEntry  4 }


rlSshClientRemoteServerPublicKeyFingerprintTable OBJECT-TYPE
   SYNTAX SEQUENCE OF RlSshClientRemoteServerPublicKeyFingerprintEntry
   MAX-ACCESS     not-accessible
   STATUS     current
   DESCRIPTION
    "Table for storing fingerprints of remote servers public keys. It is used for
     the purpose of server authentication. An entry can be inserted to this table
     either explicitly by user configuration, or during copy operation, after
     user's approval."
    ::= { rlSshClient 10 }

rlSshClientRemoteServerPublicKeyFingerprintEntry  OBJECT-TYPE
   SYNTAX RlSshClientRemoteServerPublicKeyFingerprintEntry
   MAX-ACCESS   not-accessible
   STATUS       current
   DESCRIPTION
       " The row definition for this table."
   INDEX { rlSshClientRemoteServerFingerprintInetAddrType,
           rlSshClientRemoteServerFingerprintInetAddr,
           rlSshClientRemoteServerFingerprint }
   ::= { rlSshClientRemoteServerPublicKeyFingerprintTable 1 }

RlSshClientRemoteServerPublicKeyFingerprintEntry ::= SEQUENCE {
  rlSshClientRemoteServerFingerprintInetAddrType  InetAddressType,
  rlSshClientRemoteServerFingerprintInetAddr      InetAddress,
  rlSshClientRemoteServerFingerprint              OCTET STRING,
  rlSshClientRemoteServerFingerprintStatus        RowStatus
}

rlSshClientRemoteServerFingerprintInetAddrType   OBJECT-TYPE
   SYNTAX InetAddressType
   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Remote server inet address type."
     ::=  { rlSshClientRemoteServerPublicKeyFingerprintEntry  1 }

rlSshClientRemoteServerFingerprintInetAddr     OBJECT-TYPE
   SYNTAX InetAddress
   MAX-ACCESS   not-accessible
   STATUS current
   DESCRIPTION
    "Remote server inet address."
     ::=  { rlSshClientRemoteServerPublicKeyFingerprintEntry  2 }

rlSshClientRemoteServerFingerprint     OBJECT-TYPE
   SYNTAX OCTET STRING(SIZE(16))
   MAX-ACCESS   read-create
   STATUS current
   DESCRIPTION
    "Remote server fingerprint."
     ::=  { rlSshClientRemoteServerPublicKeyFingerprintEntry  3 }

rlSshClientRemoteServerFingerprintStatus     OBJECT-TYPE
   SYNTAX RowStatus
   MAX-ACCESS   read-create
   STATUS current
   DESCRIPTION
    "Row status for this row."
     ::=  { rlSshClientRemoteServerPublicKeyFingerprintEntry  4 }

rlSshClientRemoteServersAuthenticationEnable OBJECT-TYPE
    SYNTAX   INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS      read-write
    STATUS current
    DESCRIPTION
     "Setting this field to 'enable' enables remote servers authentication.
     As a result, while opening a session with a remote server, the fingerprint
     of the remote server will be computed and looked for in the table
     rlSshClientRemoteServerPublicKeyFingerprintTable. If an entry with the key
     (server-inet-address, server-fingerprint) is found, the server is considered
     authenticated. Otherwise, the server is not authenticated (unless the user
     specifically approved this server can be trusted)."
      ::= { rlSshClient 11 }

rlSshClientDefaultKeyFlag OBJECT-TYPE
    SYNTAX  INTEGER {
           rsa(1),
           dsa(2),
           rsa-dsa(3),
           ec(4),
           all(99),
           none(100)
    }
    MAX-ACCESS      read-only
    STATUS current
    DESCRIPTION
        "This scalar indicates which of the SSH client keys are default keys
         (automatically generated)."
    ::= { rlSshClient 12 }
END

