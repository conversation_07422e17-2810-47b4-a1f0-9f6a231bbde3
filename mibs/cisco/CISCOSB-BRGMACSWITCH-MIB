CISCOSB-<PERSON><PERSON><PERSON>SWITCH-MIB DEFINITIONS ::= BEGIN

-- Title:                CISCOSB ROS
--                       Private BRIDGE MAC SWITCH MIB
-- Version:              7.46
-- Date:                 15-Jan-2007

IMPORTS
    switch001                                    FROM CISCOSB-MIB
    OBJECT-TYPE, MODULE-IDENTITY                 FROM SNMPv2-SMI
    TruthValue                                   FROM SNMPv2-TC;

rlBrgMacSwitch MODULE-IDENTITY
                LAST-UPDATED "200701020000Z"
                ORGANIZATION "Cisco Systems, Inc."

                CONTACT-INFO
                "Postal: 170 West Tasman Drive
                San Jose , CA 95134-1706
                USA

                
                Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

                DESCRIPTION
                      "This private MIB module defines Switching Parameters private MIBs."
                REVISION "200701020000Z"
                DESCRIPTION
                      "Initial revision."
        ::= { switch001 50 }

rlBrgMacSwVersion OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "MI<PERSON>'s version, the current version is 2."
    ::= { rlBrgMacSwitch 1 }

rlBrgMacSwMaxTableNumber OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Maximum number of MAC Tables supported by the device."
    ::= { rlBrgMacSwitch 2 }

rlBrgMacSwDynamicTables OBJECT-TYPE
    SYNTAX  INTEGER {
        supported (1),
        unsupported (2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Specifies whether the device supports port dynamic MAC tables."
    ::= { rlBrgMacSwitch 3 }

--rlBrgMacSwTblPollingTimeout  OBJECT-TYPE
--    SYNTAX  INTEGER
--    MAX-ACCESS  read-write
--    STATUS  current
--    DESCRIPTION
--        "The units of this MIB object is seconds."
--    ::= { rlBrgMacSwitch 4 }

rlBrgMacSwOldEntryDeleteMode  OBJECT-TYPE
    SYNTAX  INTEGER {
        refreshFlag (1),
        agingFlag(2),
        agingTime(3),
        boundaries(4)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The method used to delete old unused entries
         refresh_flag:
            The high level sets a flag when it adds or updates an entry,
            the Switch clears the flag each time, when it learns the
            station once more.
        aging_flag:
            The high level clears a flag when it adds or updates an entry,
            the Switch sets the flag each time, when it learns the station
            once more.
        aging_time:
            The high level defines a timeout, the Switch sends a message
            to the high level each time when the station doesn't send
            messages during interval between timeout and 2*timeout.
        Boundaries:
            The Switch doesn't support any mechanism, and the high level
            uses two boundaries: red and yellow. When the number of entries
            exceeds the red boundary, the high level deletes the oldest entries."
    ::= { rlBrgMacSwitch 5 }

rlBrgMacSwSpanningTree OBJECT-TYPE
    SYNTAX  INTEGER {
        supported (1),
        unsupported (2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Specifies whether the device supports port the Spanning Tree Protocol."
    ::= { rlBrgMacSwitch 6 }

rlBrgMacSwKeyType  OBJECT-TYPE
    SYNTAX  INTEGER {
        macOnly (1),
        tagAndMac(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Specified the used MAC Address Table kye format:
         macOnly:
            One global MAC Address Table per device (leaky VLANs).
         tagAndMac:
            MAC Address Table per each VLAN (strick VLANs)."
    ::= { rlBrgMacSwitch 7 }

rlBrgMacSwYellowBoundary  OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "The yellow boundary."
    ::= { rlBrgMacSwitch 8 }

rlBrgMacSwRedBoundary  OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "The yellow boundary."
    ::= { rlBrgMacSwitch 9 }

rlBrgMacSwTrapEnable OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "This variable indicates whether to create a SNMP trap
         if adding dynamic mac failed due to full hash chain."
    DEFVAL { false }
    ::= { rlBrgMacSwitch 10 }

rlBrgMacSwOperTrapCount  OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "This variable indicates the trap counter status
         (i.e. number of received MAC addresses since the last trap
         sent that haven't been inserted into the hash).
         It is relevant only when trap is enabled."
    ::= { rlBrgMacSwitch 11 }

rlBrgMacSwAdminTrapFrequency OBJECT-TYPE
    SYNTAX  INTEGER (1 .. 86400)
--    UNITS    "seconds"
    MAX-ACCESS   read-write
    STATUS   current
    DESCRIPTION
        "This variable indicates the minimal frequency (in seconds)
         of sending a trap.
         It is relevant only when trap is enabled."
    DEFVAL { 60 }
    ::= { rlBrgMacSwitch 12 }

END
