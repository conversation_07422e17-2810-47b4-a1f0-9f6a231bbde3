-- *****************************************************************
-- CISCO-SWITCH-QOS-MIB
--   
-- September 2006, <PERSON>
--   
-- Copyright (c) 2006, 2009-2011, 2013-2016 by cisco Systems Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-SWITCH-QOS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    Counter32,
    Counter64,
    Unsigned32
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    OBJECT-GROUP
        FROM SNMPv2-CONF
    TruthValue,
    TEXTUAL-CONVENTION,
    RowStatus
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    ifIndex
        FROM IF-MIB
    entPhysicalIndex
        FROM ENTITY-MIB
    Dscp
        FROM DIFFSERV-DSCP-TC
    IfDirection
        FROM DIFFSERV-MIB
    QosLayer2Cos,
    Percent
        FROM CISCO-QOS-PIB-MIB
    QosIpPrecedence,
    QosQueueNumber,
    QosThresholdNumber,
    QosMplsExpValue,
    QosMutationMapName,
    QosMutationMapNameOrEmpty,
    QosPolicerType
        FROM CISCO-QOS-TC-MIB
    ciscoMgmt
        FROM CISCO-SMI;


ciscoSwitchQosMIB MODULE-IDENTITY
    LAST-UPDATED    "201606300000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
            "Cisco Systems
            Customer Service

            Postal: 170 W Tasman Drive
            San Jose, CA 95134
            USA

            Tel: ****** 553-NETS

            E-mail: <EMAIL>"
    DESCRIPTION
        "This MIB module extends the CISCO-CLASS-BASED-QOS-MIB
        by defining configuration and statistics information
        specific to the quality of service (QoS) features of
        Layer2/3 switch functionality implemented in Cisco
        devices. It is applicable to a device which is fully
        within a single QoS domain, although one or more
        boundaries with other QoS domains can be immediately
        adjacent to this device.

        Configuration information available through this MIB
        includes:

        + Mappings between CoS, IP Precedence, MPLS-EXP value
          to DSCP value and vice versa for classification purpose.

        + Device level QoS configuration for DSCP rewrite,
          policing of ACL-redirected traffic, QoS port-queueing
          mode, statistics collection for policy that sets a 
          trust state. 

        + CoS, MPLS-EXP and DSCP mutation map name and mappings.
          These mutations can be configured so that they change
          the content of packets which cross QoS boundaries, either
          as they enter or leave this device. 

        + Interface QoS configuration such as default CoS value,
          trust state, packet assignment to queue and threshold
          based on CoS or DSCP value, drop algorithm and
          corresponding parameters, queue scheduling parameter
          such as WRR (Weighted Round Robin) weights, queue
          size allocation weight.

        Statistics available through this MIB includes:

        + Per module Multi-Layer Switching QoS statistics.

        + Per interface QoS queueing statistics.

        The following terms are used throughout this MIB:

        DSCP (Differentiated Services Code Point) is the six most
        significant bits of the ToS field in a IP packet header.

        DSCP Mutation: when a packet is being forwarded across an
        IP network, the previous hop(s) and the following hop(s)
        of a device may reside in a different QoS domain. A QoS
        domain refers to the set of QoS rules and conventions
        adopted by an administrative entity. For instance, a set
        of DSCP values may have a different meaning in different
        domains. DSCP mutation allows a DSCP set to be mutated or
        transformed in order to maintain semantic compatibility
        between adjacent domains. The mutation is done via mapping
        tables which maps the old DSCP value from one domain to a
        new DSCP value in the other domain. DSCP Mutation is applied 
        to egress traffic.

        IP precedence is the three most significant bits of the ToS
        field in a IP packet header.

        CoS (Class of Service) is the three bits in the layer 2
        header that indicates user priority value assigned to this
        packet.

        Trust state is a parameter configured at an interface to
        specify which QoS markings in packets arriving at that
        interface are acceptable as-is, rather than needing to be
        ignored/overwritten due to an 'untrusted' source or
        previous hop.

        BPDU (Bridge Protocol Data Unit) is used by bridges 
        in a network to exchange information regarding their
        status. The Spanning Tree Protocol uses the BPDU
        information to elect the root switch and root port
        for the switched network.

        MPLS-EXP:  MPLS experimental field in MPLS label.

        MTU: Maximum Transmission Unit."
    REVISION        "201606300000Z"
    DESCRIPTION
        "Updated QosStatsType to add new value
        ucastWatchdogDroppedPkts(20).
        Added ciscoSwitchQosHwServicePoolUsageGroup
        and ciscoSwitchQosServicePoolUsageTxGroup."
    REVISION        "201409190000Z"
    DESCRIPTION
        "Updated csqIfQosGroupInfoScheduling to add new
         values dwrr(3) and notApplicable(4). 
         Updated csqIfQosGroupInfoBandwidthUnits to add new
         value notApplicable(3). 
         Updated csqIfQosGroupInfoShapeUnits to add new
         value notApplicable(3). 
         Updated csqIfQosGroupInfoDropType to add new
         value notApplicable(3). 
         Updated QosStatsType to 
         add new values ucastSentOobfcPkts(16), 
         ucastSentOobfcBytes(17), ucastDroppedOobfcPkts(18), 
         ucastDroppedOobfcBytes(19). 
         Added ciscoSwitchQosIfQosGroupInfoShapeGroup. 
         Modified the description of the following objects to 
         add more information: 
         csqIfQosGroupInfoQueueSize, csqIfQosGroupInfoHwMTU,  
         csqIfQosGroupInfoMTU, csqIfQosGroupInfoResumeThresh, 
         csqIfQosGroupInfoPauseThresh, csqIfQosGroupInfoBandwidth."

    REVISION        "201309260000Z"
    DESCRIPTION
        "Add ciscoSwitchQosIfPriGrpInBufUsageGroup,
        ciscoSwitchQosServicePoolUsageGroup,
        ciscoSwitchQosServicePoolCellSizeGroup."
    REVISION        "201304220000Z"
    DESCRIPTION
        "Add ciscoSwitchQosIfQosGroupInfoGroup,
        ciscoSwitchQosIfQosGroupStatsGroup."
    REVISION        "201011170000Z"
    DESCRIPTION
        "Add ciscoSwitchQosModuleClassChangedGroup,
        ciscoSwitchQosTenGOnlyModeGroup, 
        ciscoSwitchQosIfQueueModeGroup,
        ciscoSwitchQosIfQueueSchedulingGroup,
        ciscoSwitchQosIfQueueingGroup,
        ciscoSwitchQosIfLanQueuingGroup and
        ciscoSwitchQosIfQueueBufferGroup."
    REVISION        "200907200000Z"
    DESCRIPTION
        "Add ciscoSwitchQosModuleDscpRewriteGroup."
    REVISION        "200902230000Z"
    DESCRIPTION
        "Add ciscoSwitchQosModuleStatsExtGroup and
        ciscoSwitchQosIfStatsExtGroup."
    REVISION        "200611200000Z"
    DESCRIPTION
        "Add ciscoSwitchQosPolicerUsageGroup."
    REVISION        "200609180000Z"
    DESCRIPTION
        "Initial revision of this MIB module."
    ::= { ciscoMgmt 580 }


-- MIB object definitions

ciscoSwitchQosMIBNotifs  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIB 0 }

ciscoSwitchQosMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIB 1 }

ciscoSwitchQosMIBConformance  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIB 2 }

csqGlobals  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBObjects 1 }

csqMappings  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBObjects 2 }

csqMutations  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBObjects 3 }

csqInterface  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBObjects 4 }

csqStatistics  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBObjects 5 }

csqPolicerUsage  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBObjects 6 }

csqModule  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBObjects 7 }


QosStatsType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "An integer indicating a specific statistics.

        ucastSentPkts(1): Unicast packets sent
        ucastSentBytes(2): Unicast bytes sent
        mcastSentPkts(3): Multicast packets sent
        mcastSentBytes(4): Multicast bytes sent
        ucastDroppedPkts(5): Unicast packets dropped
        ucastDroppedBytes(6): Unicast bytes dropped
        mcastDroppedPkts(7): Multicast packets dropped
        mcastDroppedBytes(8): Multicast bytes dropped
        sentPkts(9): Packets sent
        receivedPkts(10): Packets received
        droppedIngressPkts(11): Packets discarded on ingress
        ucastSentXbarPkts(12): Unicast packets sent to the cross-bar
        ucastRecvXbarPkts(13): Unicast packets received from the 
                               cross-bar
        mcastSentXbarPkts(14): Multicast packets sent to the cross-bar
        mcastRecvXbarPkts(15): Multicast packets received from the 
                               cross-bar
        ucastSentOobfcPkts(16): Unicast packets sent on OOBFC
        ucastSentOobfcBytes(17): Unicast bytes sent on OOBFC
        ucastDroppedOobfcPkts(18): Unicast packets dropped on OOBFC
        ucastDroppedOobfcBytes(19): Unicast bytes dropped on OOBFC
        ucastWatchdogDroppedPkts(20): Unicast packets dropped after watchdog
                                      triggered"
    SYNTAX          INTEGER  {
                        ucastSentPkts(1),
                        ucastSentBytes(2),
                        mcastSentPkts(3),
                        mcastSentBytes(4),
                        ucastDroppedPkts(5),
                        ucastDroppedBytes(6),
                        mcastDroppedPkts(7),
                        mcastDroppedBytes(8),
                        sentPkts(9),
                        receivedPkts(10),
                        droppedIngressPkts(11),
                        ucastSentXbarPkts(12),
                        ucastRecvXbarPkts(13),
                        mcastSentXbarPkts(14),
                        mcastRecvXbarPkts(15),
                        ucastSentOobfcPkts(16),
                        ucastSentOobfcBytes(17),
                        ucastDroppedOobfcPkts(18),
                        ucastDroppedOobfcBytes(19),
                        ucastWatchdogDroppedPkts(20)
                    }

-- Global group

csqDscpRewriteEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether DSCP rewrite is enabled
        at a device-level of granularity, i.e., 'true' = enabled
        and 'false' = disabled.  If no other objects specify whether
        DSCP rewrite is enabled at any different level of granularity,
        then this object's value is not subject to any modifiers.
        However, some devices might support other object(s) which
        specify whether DSCP rewrite is enabled at different level(s)
        of granularity.  For such devices, the value of this object
        takes precedence over the values of such other object(s) when
        the value of this object is 'false'; in contrast, when the
        value of this object is 'true', the values of such other
        objects take precedence over the value of this object.

        if 'true', all outgoing packets will have their DSCP
        value rewritten based on the result of classification,
        policing or DSCP mutation configured in the device.

        if 'false', all outgoing packets will have their DSCP
        values unchanged from they arrived." 
    ::= { csqGlobals 1 }

csqPoliceRedirectedTrafficEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether ACL-redirected traffic
        policing is enabled at a device-level of granularity,
        i.e., 'true' = enabled and 'false' = disabled.  If no
        other objects specify whether ACL-redirected traffic
        is enabled at any different level of granularity,
        then this object's value is not subject to any modifiers.
        However, some devices might support other object(s) which
        specify whether ACL-redirected traffic policing is enabled
        at different level(s) of granularity. For such devices,
        the value of this object takes precedence over the values
        of such other object(s) when the value of this object is
        'false'; in contrast, when the value of this object is 'true',
        the values of such other objects take precedence over the
        value of this object.

        if 'true', ACL-redirected traffic is subject to policing.
        if 'false', ACL-redirected traffic is not policed." 
    ::= { csqGlobals 2 }

csqPortQueueingModeEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether port-queueing mode is enabled
        at a device-level of granularity, i.e., 'true' = enabled
        and 'false' = disabled.  If no other objects specify whether
        port-queueing mode is enabled at any different level of
        granularity, then this object's value is not subject to any
        modifiers.  However, some devices might support other object(s)
        which specify whether port-queueing mode is enabled at
        different level(s) of granularity.  For such devices, the
        value of this object takes precedence over the values of such
        other object(s) when the value of this object is 'false'; in
        contrast, when the value of this object is 'true', the values
        of such other objects take precedence over the value of this
        object.

        if 'true', port-queueing mode is enabled. In port-queueing
        mode, marking and policing is disabled. All queueing
        on receiving and transmitting is based on QoS tag in the
        incoming packet. For 802.1Q or ISL-encapsulated packets,
        queueing is based on the CoS value. Otherwise, queueing
        is based on the default interface CoS value denoted by
        csqIfDefaultCos object.

        if 'false', port-queueing mode is disabled." 
    ::= { csqGlobals 3 }

csqMarkingStatisticsEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether statistics collection for
        policy that sets a trust state is enabled at a device-level
        of granularity, i.e., 'true' = enabled and 'false' = disabled.
        If no other objects specify whether statistics collection for
        policy that sets a trust state is enabled at any different
        level of granularity, then this object's value is not subject
        to any modifiers. However, some devices might support other
        object(s) which specify whether statistics collection for
        policy that sets a trust state is enabled at different
        level(s) of granularity.  For such devices, the value of this
        object takes precedence over the values of such other object(s)
        when the value of this object is 'false'; in contrast, when the
        value of this object is 'true', the values of such other
        objects take precedence over the value of this object.

        if 'true', statistics collection is enabled. 
        if 'false', statistics collection is disabled." 
    ::= { csqGlobals 4 }

csqTenGOnlyMode OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether only 10-Gigabit Ethernet uplink
        interfaces are used exclusively.

        'true' indicates that only the 10-Gigabit Ethernet uplink
        interfaces are used. The other uplink interfaces which are not
        of 10-Gigabit capacity will be in administratively down state.

        'false' indicates otherwise." 
    ::= { csqGlobals 5 }

csqServicePoolCellSize OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of bytes for a service pool
        cell." 
    ::= { csqGlobals 6 }
-- The csqCosToDscpTable

csqCosToDscpTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqCosToDscpEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains the mapping of CoS values to DSCP values.
        This mapping table consist of eight CoS values (0 through 7) and
        their corresponding DSCP values. The mapping given by this table
        is used for all packets received on an interface if and only if
        that interface has a trust state, as given by the value of
        csqIfTrustState for the interface, of 'trustCoS'."
    ::= { csqMappings 1 }

csqCosToDscpEntry OBJECT-TYPE
    SYNTAX          CsqCosToDscpEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the mapping from a CoS value to a DSCP
        value."
    INDEX           { csqCosToDscpCos } 
    ::= { csqCosToDscpTable 1 }

CsqCosToDscpEntry ::= SEQUENCE {
        csqCosToDscpCos  QosLayer2Cos,
        csqCosToDscpDscp Dscp
}

csqCosToDscpCos OBJECT-TYPE
    SYNTAX          QosLayer2Cos
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The CoS value being mapped to the DSCP value." 
    ::= { csqCosToDscpEntry 1 }

csqCosToDscpDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The DSCP value which the CoS value maps to." 
    ::= { csqCosToDscpEntry 2 }
 

-- CsqIpPrecToDscpTable

csqIpPrecToDscpTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIpPrecToDscpEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains the mapping of IP Precedence to DSCP.
        This mapping table consist of eight IpPrecedence values
        (0 through 7) and their corresponding DSCP values. The
        mapping given by this table is used for all packets received
        on an interface if and only if that interface has a trust state,
        as given by the value of csqIfTrustState for the interface,
        of 'trustIpPrec'."
    ::= { csqMappings 2 }

csqIpPrecToDscpEntry OBJECT-TYPE
    SYNTAX          CsqIpPrecToDscpEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the mapping from an IP Precedence value to
        a DSCP value."
    INDEX           { csqIpPrecToDscpIpPrec } 
    ::= { csqIpPrecToDscpTable 1 }

CsqIpPrecToDscpEntry ::= SEQUENCE {
        csqIpPrecToDscpIpPrec QosIpPrecedence,
        csqIpPrecToDscpDscp   Dscp
}

csqIpPrecToDscpIpPrec OBJECT-TYPE
    SYNTAX          QosIpPrecedence
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IP Precedence value being mapped to the DSCP value." 
    ::= { csqIpPrecToDscpEntry 1 }

csqIpPrecToDscpDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The DSCP value which the IP Precedence value maps to." 
    ::= { csqIpPrecToDscpEntry 2 }
 

-- The csqExpToDscpTable

csqExpToDscpTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqExpToDscpEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains the mapping of MPLS-EXP values to DSCP
        values.  This mapping table consist of eight MPLS-EXP values
        (0 through 7) and their corresponding DSCP values."
    ::= { csqMappings 3 }

csqExpToDscpEntry OBJECT-TYPE
    SYNTAX          CsqExpToDscpEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the mapping from a EXP value to a DSCP
        value."
    INDEX           { csqExpToDscpExp } 
    ::= { csqExpToDscpTable 1 }

CsqExpToDscpEntry ::= SEQUENCE {
        csqExpToDscpExp  QosMplsExpValue,
        csqExpToDscpDscp Dscp
}

csqExpToDscpExp OBJECT-TYPE
    SYNTAX          QosMplsExpValue
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The EXP value being mapped to the DSCP value." 
    ::= { csqExpToDscpEntry 1 }

csqExpToDscpDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The DSCP value which the EXP value maps to." 
    ::= { csqExpToDscpEntry 2 }
 

-- csqDscpMappingTable

csqDscpMappingTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqDscpMappingEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table always has 64 entries, one for each DSCP value. The
        table contains four mappings from the DSCP value assigned to
        a packet. One mapping is to the egress CoS to be stored in the
        layer-2 frame headers for output on 802.1Q or ISL interfaces.
        Another mapping is to the EXP value to be stored in MPLS label.
        The other two mappings are to the remarked (or 'marked down')
        DSCP values which are used when a policer requires that 
        a packet's DSCP value to be modified. Of these two mappings,
        one is for a normal burst, and the other is for maximum burst."
    ::= { csqMappings 4 }

csqDscpMappingEntry OBJECT-TYPE
    SYNTAX          CsqDscpMappingEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the mapping from DSCP value to CoS value,
        MPLS-EXP value and policed DSCP."
    INDEX           { csqDscpMappingDscp } 
    ::= { csqDscpMappingTable 1 }

CsqDscpMappingEntry ::= SEQUENCE {
        csqDscpMappingDscp            Dscp,
        csqDscpMappingCos             QosLayer2Cos,
        csqDscpMappingExp             QosMplsExpValue,
        csqDscpMappingNormalBurstDscp Dscp,
        csqDscpMappingMaxBurstDscp    Dscp
}

csqDscpMappingDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The DSCP value being mapped to the CoS, EXP and
        policed DSCP value." 
    ::= { csqDscpMappingEntry 1 }

csqDscpMappingCos OBJECT-TYPE
    SYNTAX          QosLayer2Cos
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The CoS value which the DSCP values maps to." 
    ::= { csqDscpMappingEntry 2 }

csqDscpMappingExp OBJECT-TYPE
    SYNTAX          QosMplsExpValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The MPLS-EXP value which the DSCP values maps to." 
    ::= { csqDscpMappingEntry 3 }

csqDscpMappingNormalBurstDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The normal burst policed DSCP value which the DSCP values maps
        to." 
    ::= { csqDscpMappingEntry 4 }

csqDscpMappingMaxBurstDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The maximum burst policed DSCP value which the DSCP values maps
        to." 
    ::= { csqDscpMappingEntry 5 }
 


-- csqMutations group

csqMaxCosMutationMap OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum number of CoS mutation map that can be supported
        in the device." 
    ::= { csqMutations 1 }

csqCosMutationTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqCosMutationEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table indicates CoS mutation maps in the
        device."
    ::= { csqMutations 2 }

csqCosMutationEntry OBJECT-TYPE
    SYNTAX          CsqCosMutationEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the status of this instance. A row
        instance can be created or removed by setting
        the appropriate value of its RowStatus object. Once
        the number of entries in this table reaches the
        maximum number of CoS mutation map supported in
        the device denoted by csqMaxCosMutationMap object, user
        must delete an existing entry in this table in order to
        create a new entry."
    INDEX           { IMPLIED csqCosMutationMapName } 
    ::= { csqCosMutationTable 1 }

CsqCosMutationEntry ::= SEQUENCE {
        csqCosMutationMapName   QosMutationMapName,
        csqCosMutationRowStatus RowStatus
}

csqCosMutationMapName OBJECT-TYPE
    SYNTAX          QosMutationMapName
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The name of the CoS mutation map." 
    ::= { csqCosMutationEntry 1 }

csqCosMutationRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to manage the creation and deletion
        of rows in this table." 
    ::= { csqCosMutationEntry 2 }
 

-- The csqCosMutationMappingTable

csqCosMutationMappingTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqCosMutationMappingEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides management information for CoS
        mutation mapping. CoS mutation is applied to ingress
        traffic. This mutation occurs before the CoS to
        DSCP mapping for applicable traffic as specified in
        csqCosToDscpTable."
    ::= { csqMutations 3 }

csqCosMutationMappingEntry OBJECT-TYPE
    SYNTAX          CsqCosMutationMappingEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the mapping from a CoS value to another CoS
        value. When the entry for csqCosMutationMapName in the
        csqCosMutationTable is created, corresponding entries in this
        table are initialized with a default mapping which is the
        identity function. When the entry for csqCosMutationMapName
        in the csqCosMutationTable is removed, corresponding entries
        in this table will also be deleted."
    INDEX           {
                        csqCosMutationMapName,
                        csqCosMutationFromCos
                    } 
    ::= { csqCosMutationMappingTable 1 }

CsqCosMutationMappingEntry ::= SEQUENCE {
        csqCosMutationFromCos QosLayer2Cos,
        csqCosMutationToCos   QosLayer2Cos
}

csqCosMutationFromCos OBJECT-TYPE
    SYNTAX          QosLayer2Cos
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The input CoS value being mapped to the output CoS value in
        this mutation map." 
    ::= { csqCosMutationMappingEntry 1 }

csqCosMutationToCos OBJECT-TYPE
    SYNTAX          QosLayer2Cos
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The output CoS value which the input CoS value maps to." 
    ::= { csqCosMutationMappingEntry 2 }
 


csqMaxDscpMutationMap OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum number of DSCP mutation map that can be supported
        in the device." 
    ::= { csqMutations 4 }
-- csqDscpMutationTable

csqDscpMutationTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqDscpMutationEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table indicates DSCP mutation maps in the
        device."
    ::= { csqMutations 5 }

csqDscpMutationEntry OBJECT-TYPE
    SYNTAX          CsqDscpMutationEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the status of this instance. A row
        instance can be created or removed by setting
        the appropriate value of its RowStatus object. Once
        the number of entries in this table reaches the
        maximum number of DSCP mutation map supported in
        the device denoted by csqMaxDscpMutationMap
        object, user must delete an existing entry in this table
        in order to create a new entry."
    INDEX           { IMPLIED csqDscpMutationMapName } 
    ::= { csqDscpMutationTable 1 }

CsqDscpMutationEntry ::= SEQUENCE {
        csqDscpMutationMapName   QosMutationMapName,
        csqDscpMutationRowStatus RowStatus
}

csqDscpMutationMapName OBJECT-TYPE
    SYNTAX          QosMutationMapName
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The name of the DSCP mutation map." 
    ::= { csqDscpMutationEntry 1 }

csqDscpMutationRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to manage the creation and deletion
        of rows in this table." 
    ::= { csqDscpMutationEntry 2 }
 

-- The csqDscpMutationMappingTable

csqDscpMutationMappingTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqDscpMutationMappingEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides management information for DSCP
        mutation mapping. DSCP mutation is applied to egress
        traffic. This mutation occurs after the mappings
        specified in csqDscpMappingTable."
    ::= { csqMutations 6 }

csqDscpMutationMappingEntry OBJECT-TYPE
    SYNTAX          CsqDscpMutationMappingEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the mapping from an input DSCP value to
        an output DSCP value. When the entry for csqDscpMutationMapName
        in the csqDscpMutationTable is created, corresponding entries
        in this table are initialized with a default mapping which is
        the identity function. When the entry for
        csqDscpMutationMapName in the csqDscpMutationTable is removed,
        corresponding entries in this table will also be deleted."
    INDEX           {
                        csqDscpMutationMapName,
                        csqDscpMutationFromDscp
                    } 
    ::= { csqDscpMutationMappingTable 1 }

CsqDscpMutationMappingEntry ::= SEQUENCE {
        csqDscpMutationFromDscp Dscp,
        csqDscpMutationToDscp   Dscp
}

csqDscpMutationFromDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The input DSCP value being mapped to the output DSCP value in
        this mutation map." 
    ::= { csqDscpMutationMappingEntry 1 }

csqDscpMutationToDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The output DSCP value which the input DSCP value maps to." 
    ::= { csqDscpMutationMappingEntry 2 }
 


csqMaxExpMutationMap OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The maximum number of EXP mutation can be supported
        in the device." 
    ::= { csqMutations 7 }
-- csqExpMutationTable

csqExpMutationTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqExpMutationEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table indicates EXP mutation maps in the
        device."
    ::= { csqMutations 8 }

csqExpMutationEntry OBJECT-TYPE
    SYNTAX          CsqExpMutationEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the status of this instance. A row
        instance can be created or removed by setting
        the appropriate value of its RowStatus object. Once
        the number of entries in this table reaches the
        maximum number of EXP mutation map supported in
        the device denoted by csqMaxExpMutationMap object, user
        must delete an existing entry in this table in order to
        create a new entry."
    INDEX           { IMPLIED csqExpMutationMapName } 
    ::= { csqExpMutationTable 1 }

CsqExpMutationEntry ::= SEQUENCE {
        csqExpMutationMapName   QosMutationMapName,
        csqExpMutationRowStatus RowStatus
}

csqExpMutationMapName OBJECT-TYPE
    SYNTAX          QosMutationMapName
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The name of the EXP mutation map." 
    ::= { csqExpMutationEntry 1 }

csqExpMutationRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to manage the creation and deletion
        of rows in this table." 
    ::= { csqExpMutationEntry 2 }
 

-- The csqExpMutationMappingTable

csqExpMutationMappingTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqExpMutationMappingEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides management information for EXP
        mutation mapping. EXP mutation is applied to egress
        traffic. This mutation occurs after the mapping
        specified in csqExpToDscpTable."
    ::= { csqMutations 9 }

csqExpMutationMappingEntry OBJECT-TYPE
    SYNTAX          CsqExpMutationMappingEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the mapping from input EXP to output EXP
        value. When the entry for csqExpMutationMapName in the
        csqExpMutationTable is created, corresponding entries in this
        table are initialized with a default mapping which is the
        identity function. When the entry for csqExpMutationMapName
        in the csqExpMutationTable is removed, corresponding entries
        in this table will also be deleted."
    INDEX           {
                        csqExpMutationMapName,
                        csqExpMutationFromExp
                    } 
    ::= { csqExpMutationMappingTable 1 }

CsqExpMutationMappingEntry ::= SEQUENCE {
        csqExpMutationFromExp QosMplsExpValue,
        csqExpMutationToExp   QosMplsExpValue
}

csqExpMutationFromExp OBJECT-TYPE
    SYNTAX          QosMplsExpValue
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The input EXP value being mapped to the output EXP value in
        this mutation map." 
    ::= { csqExpMutationMappingEntry 1 }

csqExpMutationToExp OBJECT-TYPE
    SYNTAX          QosMplsExpValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The output EXP value which the input EXP value maps to." 
    ::= { csqExpMutationMappingEntry 2 }
 

-- csqIfMutationConfigTable

csqIfMutationConfigTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfMutationConfigEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing the mutation configuration for
        mutation capable interface in the device. If a
        mutation capable interface does not have a row
        in this table, there is no mutation performed
        at such interface."
    ::= { csqMutations 10 }

csqIfMutationConfigEntry OBJECT-TYPE
    SYNTAX          CsqIfMutationConfigEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance contains the name of CoS, DSCP and EXP
        mutation map and RowStatus object."
    INDEX           { ifIndex } 
    ::= { csqIfMutationConfigTable 1 }

CsqIfMutationConfigEntry ::= SEQUENCE {
        csqIfCosMutationMap    QosMutationMapNameOrEmpty,
        csqIfDscpMutationMap   QosMutationMapNameOrEmpty,
        csqIfExpMutationMap    QosMutationMapNameOrEmpty,
        csqIfMutationRowStatus RowStatus
}

csqIfCosMutationMap OBJECT-TYPE
    SYNTAX          QosMutationMapNameOrEmpty
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the name of CoS mutation map
        applied at this interface. If CoS mutation is not performed
        at the interface, then the value of this object is the
        zero-length string; otherwise, the value of this object must
        be the name of a row in the csqCosMutationTable.  If a row
        in the csqCosMutationTable is deleted, all instances of this
        object which referenced the deleted row get changed to the
        zero-length string." 
    ::= { csqIfMutationConfigEntry 1 }

csqIfDscpMutationMap OBJECT-TYPE
    SYNTAX          QosMutationMapNameOrEmpty
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the name of DSCP mutation map
        applied at this interface. If DSCP mutation is not performed
        at the interface, then the value of this object is the
        zero-length string; otherwise, the value of this object must
        be the name of a row in the csqDscpMutationTable.  If a row
        in the csqDscpMutationTable is deleted, all instances of this
        object which referenced the deleted row get changed to the
        zero-length string." 
    ::= { csqIfMutationConfigEntry 2 }

csqIfExpMutationMap OBJECT-TYPE
    SYNTAX          QosMutationMapNameOrEmpty
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object specifies the name of EXP mutation map
        applied at this interface. If EXP mutation is not performed
        at the interface, then the value of this object is the
        zero-length string; otherwise, the value of this object must
        be the name of a row in the csqExpMutationTable.  If a row
        in the csqExpMutationTable is deleted, all instances of this
        object which referenced the deleted row get changed to the
        zero-length string." 
    ::= { csqIfMutationConfigEntry 3 }

csqIfMutationRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object is used to manage the creation, and deletion
        of rows in the table." 
    ::= { csqIfMutationConfigEntry 4 }
 

-- csqIfConfigTable

csqIfConfigTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfConfigEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides QoS configuration for QoS manageable
        interface in the device."
    ::= { csqInterface 1 }

csqIfConfigEntry OBJECT-TYPE
    SYNTAX          CsqIfConfigEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance contains the default CoS value and trust
        state of a Qos manageable interface."
    INDEX           { ifIndex } 
    ::= { csqIfConfigTable 1 }

CsqIfConfigEntry ::= SEQUENCE {
        csqIfDefaultCos            QosLayer2Cos,
        csqIfTrustState            INTEGER,
        csqIfQueueModeCpb          BITS,
        csqIfConfigQueueMode       INTEGER,
        csqIfIngressPolicyMap      SnmpAdminString,
        csqIfEgressPolicyMap       SnmpAdminString,
        csqIfIngressQueueingEnable TruthValue,
        csqIfEgressQueueingEnable  TruthValue,
        csqIfQueueingTrustState    INTEGER
}

csqIfDefaultCos OBJECT-TYPE
    SYNTAX          QosLayer2Cos
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the default CoS value configured
        at this physical interface. This default value will be
        assigned to packet which does not have a CoS value in
        its layer-2 header when the packet arrives at this
        interface or if the value of csqIfTrustState object
        for this physical interface is 'untrusted'." 
    ::= { csqIfConfigEntry 1 }

csqIfTrustState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        untrusted(1),
                        trustCoS(2),
                        trustIpPrec(3),
                        trustDscp(4)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to set the trust state of an interface.
        (whether the packets arriving at an interface are trusted to
        carry the correct data for classification.)

        If the object is untrusted(1), then the DSCP assigned to the
        packet is the layer2 CoS value denoted by csqIfDefaultCos
        object mapped to a DSCP by the CoS-to-DSCP mapping defined in
        object csqCosToDscpDscp.

        If this object is trustCoS(2), then the DSCP assigned
        to the packet is the layer2 CoS of the packet mapped to a
        DSCP by the CoS-to-DSCP mapping defined in object
        csqCosToDscpDscp.

        When this object is trustIpPrec(3), a DSCP is assigned to
        an IP packet according to the IP-Precedence-to-DSCP mapping
        defined by the values contained in csqIpPrecToDscpTable. For
        non-IP packets, trustIpPrec(3) has identical behavior as
        trustCoS(2).

        When this object is trustDscp(4), the DSCP contained in an IP
        packet is trusted as being the correct value to assign to it.
        For non-IP packets, trustDscp(4) has identical behavior as
        trustCoS(2)." 
    ::= { csqIfConfigEntry 2 }

csqIfQueueModeCpb OBJECT-TYPE
    SYNTAX          BITS {
                        cos(0),
                        dscp(1)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the queue mode capability
        at this interface. 

        'cos' indicates that the interface is capable of 
        queueing a packet based on the CoS value of the
        packet.

        'dscp' indicates that the interface is capable of
        queueing a packet based on the DSCP value of the
        packet." 
    ::= { csqIfConfigEntry 3 }

csqIfConfigQueueMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        cos(1),
                        dscp(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the queueing mode at this interface.

        'cos' indicates that the interface is queueing 
        a packet based on the CoS value of the packet.
        This value can only be set if the 'cos' bit
        of csqIfQueueModeCpb is set.

        'dscp' indicates that the interface is queueing 
        a packet based on the DSCP value of the packet. 
        This value can only be set if the 'dscp' bit
        of csqIfQueueModeCpb is set." 
    ::= { csqIfConfigEntry 4 }

csqIfIngressPolicyMap OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the name of an existing policy-map attached to
        this interface in ingress direction.
        If there is no such policy-map attached, the value of this
        object is zero-length string." 
    ::= { csqIfConfigEntry 5 }

csqIfEgressPolicyMap OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the name of an existing policy-map
        attached to this interface in egress direction.
        If there is no such policy-map attached, the value of this
        object is zero-length string." 
    ::= { csqIfConfigEntry 6 }

csqIfIngressQueueingEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates if ingress queueing is enabled at this
        interface.

        'true' indicates ingress queueing is enabled.

        'false' indicates ingress queueing is disabled." 
    ::= { csqIfConfigEntry 7 }

csqIfEgressQueueingEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates if egress queueing is enabled at this
        interface.

        'true' indicates egress queueing is enabled.

        'false' indicates egress queueing is disabled." 
    ::= { csqIfConfigEntry 8 }

csqIfQueueingTrustState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        untrusted(1),
                        trustCoS(2),
                        trustIpPrec(3),
                        trustDscp(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the queueing trust state of an interface.

        If the object is untrusted(1), then the DSCP assigned to the
        packet is the layer2 CoS value denoted by csqIfDefaultCos
        object mapped to a DSCP by the CoS-to-DSCP mapping defined in
        object csqCosToDscpDscp.

        If this object is trustCoS(2), then the DSCP assigned
        to the packet is the layer2 CoS of the packet mapped to a
        DSCP by the CoS-to-DSCP mapping defined in object
        csqCosToDscpDscp.

        When this object is trustIpPrec(3), a DSCP is assigned to
        an IP packet according to the IP-Precedence-to-DSCP mapping
        defined by the values contained in csqIpPrecToDscpTable. For
        non-IP packets, trustIpPrec(3) has identical behavior as
        trustCoS(2).

        When this object is trustDscp(4), the DSCP contained in an IP
        packet is trusted as being the correct value to assign to it.
        For non-IP packets, trustDscp(4) has identical behavior as
        trustCoS(2)." 
    ::= { csqIfConfigEntry 9 }
 

-- Cos to Queue assignment table

csqIfCosToQueueTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfCosToQueueEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides the information for and configuration of
        assigning packets to queues and thresholds based on their CoS
        value."
    ::= { csqInterface 2 }

csqIfCosToQueueEntry OBJECT-TYPE
    SYNTAX          CsqIfCosToQueueEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The assignment of packets at an interface to a pair of queue
        and threshold based on their CoS value and traffic direction."
    INDEX           {
                        ifIndex,
                        csqIfCosToQueueDirection,
                        csqIfCosToQueueCos
                    } 
    ::= { csqIfCosToQueueTable 1 }

CsqIfCosToQueueEntry ::= SEQUENCE {
        csqIfCosToQueueDirection       IfDirection,
        csqIfCosToQueueCos             QosLayer2Cos,
        csqIfCosToQueueQueueNumber     QosQueueNumber,
        csqIfCosToQueueThresholdNumber QosThresholdNumber
}

csqIfCosToQueueDirection OBJECT-TYPE
    SYNTAX          IfDirection
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The traffic direction of a packet." 
    ::= { csqIfCosToQueueEntry 1 }

csqIfCosToQueueCos OBJECT-TYPE
    SYNTAX          QosLayer2Cos
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The CoS value of the packet which the queue and threshold
        assignment is based on." 
    ::= { csqIfCosToQueueEntry 2 }

csqIfCosToQueueQueueNumber OBJECT-TYPE
    SYNTAX          QosQueueNumber
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The queue number where packet whose CoS value denoted by
        csqIfCosToQueueCos will be assigned to." 
    ::= { csqIfCosToQueueEntry 3 }

csqIfCosToQueueThresholdNumber OBJECT-TYPE
    SYNTAX          QosThresholdNumber
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The threshold number where packet whose CoS value denoted
        by csqIfCosToQueueCos will be assigned to." 
    ::= { csqIfCosToQueueEntry 4 }
 

-- DSCP to Queue assignment table

csqIfDscpToQueueTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfDscpToQueueEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides the information for and configuration of
        assigning packets to queues and thresholds based on their DSCP
        value and traffic direction."
    ::= { csqInterface 3 }

csqIfDscpToQueueEntry OBJECT-TYPE
    SYNTAX          CsqIfDscpToQueueEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The assignment of packets at an interface to a pair of queue
        and threshold based on their DSCP value and packets traffic
        direction."
    INDEX           {
                        ifIndex,
                        csqIfDscpToQueueDirection,
                        csqIfDscpToQueueDscp
                    } 
    ::= { csqIfDscpToQueueTable 1 }

CsqIfDscpToQueueEntry ::= SEQUENCE {
        csqIfDscpToQueueDirection       IfDirection,
        csqIfDscpToQueueDscp            Dscp,
        csqIfDscpToQueueQueueNumber     QosQueueNumber,
        csqIfDscpToQueueThresholdNumber QosThresholdNumber
}

csqIfDscpToQueueDirection OBJECT-TYPE
    SYNTAX          IfDirection
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The traffic direction of a packet." 
    ::= { csqIfDscpToQueueEntry 1 }

csqIfDscpToQueueDscp OBJECT-TYPE
    SYNTAX          Dscp
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The DSCP value of the packet which the queue and threshold
        assignment is based on." 
    ::= { csqIfDscpToQueueEntry 2 }

csqIfDscpToQueueQueueNumber OBJECT-TYPE
    SYNTAX          QosQueueNumber
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The queue number where packet whose DSCP value denoted by
        csqIfDscpToQueueDscp will be assigned to." 
    ::= { csqIfDscpToQueueEntry 3 }

csqIfDscpToQueueThresholdNumber OBJECT-TYPE
    SYNTAX          QosThresholdNumber
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The threshold number where packet whose DSCP value denoted by
        csqIfDscpToQueueDscp will be assigned to." 
    ::= { csqIfDscpToQueueEntry 4 }
 

-- The Interface Drop Table

csqIfDropConfigTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfDropConfigEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table maintains threshold parameters for the specified
        queue number and threshold number of an interface."
    ::= { csqInterface 4 }

csqIfDropConfigEntry OBJECT-TYPE
    SYNTAX          CsqIfDropConfigEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "For each threshold of a queue, there are parameters to set on
        the threshold. This entry contains the parameters."
    INDEX           {
                        ifIndex,
                        csqIfDropConfigDirection,
                        csqIfDropConfigQueueIndex,
                        csqIfDropConfigThresholdIndex
                    } 
    ::= { csqIfDropConfigTable 1 }

CsqIfDropConfigEntry ::= SEQUENCE {
        csqIfDropConfigDirection        IfDirection,
        csqIfDropConfigQueueIndex       QosQueueNumber,
        csqIfDropConfigThresholdIndex   QosThresholdNumber,
        csqIfDropConfigDropAlgorithm    INTEGER,
        csqIfDropConfigDropThreshold    Percent,
        csqIfDropConfigMinWredThreshold Percent,
        csqIfDropConfigMaxWredThreshold Percent,
        csqIfDropConfigQueueBuffer      INTEGER
}

csqIfDropConfigDirection OBJECT-TYPE
    SYNTAX          IfDirection
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the queue direction." 
    ::= { csqIfDropConfigEntry 1 }

csqIfDropConfigQueueIndex OBJECT-TYPE
    SYNTAX          QosQueueNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates queue number." 
    ::= { csqIfDropConfigEntry 2 }

csqIfDropConfigThresholdIndex OBJECT-TYPE
    SYNTAX          QosThresholdNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates threshold number." 
    ::= { csqIfDropConfigEntry 3 }

csqIfDropConfigDropAlgorithm OBJECT-TYPE
    SYNTAX          INTEGER  {
                        tailDrop(1),
                        wred(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the drop algorithm running at this queue and
        threshold.

        'tailDrop' indicates that this queue and threshold drops
        packet using tail-drop algorithm. This value is configurable 
        only if 'tailDrop' bit in the value of qosIfCapabilities object
        for the same ifIndex and traffic direction is set. 

        'wred' indicates that WRED algorithm is used. This value is
        configurable only if 'wred' bit in the value of 
        qosIfCapabilities object for the same ifIndex and traffic
        direction is set." 
    ::= { csqIfDropConfigEntry 4 }

csqIfDropConfigDropThreshold OBJECT-TYPE
    SYNTAX          Percent (1..100)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the drop threshold parameter for a
        pair of queue and threshold of an interface when the
        drop algorithm is tail drop. Once the packets in the buffer is
        more than the value of this object, the incoming packets of the
        buffer are dropped. The value is a percentage of the full
        buffer.

        This object is configurable only if 'tailDrop' bit in 
        the value of qosIfCapabilities for the same ifIndex and traffic
        direction is set. If value of csqIfDropConfigAlgorithm is not
        'tailDrop', this object value has no effect. If value of 
        csqIfDropConfigQueueBuffer is not 'percent',
        this object value has no effect." 
    ::= { csqIfDropConfigEntry 5 }

csqIfDropConfigMinWredThreshold OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the min WRED threshold parameter of a
        threshold number for the specific interface when WRED drop
        algorithm is used.

        WRED (Weighted Random Early Detect) is a mechanism which drops
        packets fairly during congestion so that adaptive applications
        can react to congestion. This object specifies a percentage of
        the buffer size.

        This object is configurable only if 'wred' bit in the value
        of qosIfCapabilities object for the same ifIndex and traffic
        direction is set. If value of csqIfDropConfigAlgorithm is not
        'wred', this object value has no effect." 
    ::= { csqIfDropConfigEntry 6 }

csqIfDropConfigMaxWredThreshold OBJECT-TYPE
    SYNTAX          Percent (1..100)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies the max WRED threshold parameter of a
        threshold number for the specific interface when WRED drop
        algorithm is used.

        This object is configurable only if 'wred' bit in the value
        of qosIfCapabilities object for the same ifIndex and traffic
        direction is set. If value of csqIfDropConfigAlgorithm is not
        'wred', this object value has no effect." 
    ::= { csqIfDropConfigEntry 7 }

csqIfDropConfigQueueBuffer OBJECT-TYPE
    SYNTAX          INTEGER  {
                        shared(1),
                        dedicated(2),
                        percent(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies how the queue buffer behaves
        when the drop algorithm is tail drop. 

        'shared' indicates that the queue buffer is shared
        among all queues at the interface. 

        'dedicated' indicates that each queue will be
        assigned a dedicated portion of the queue buffer.

        'percent' indicates that a percentage of the queue
        buffer can be configured for each queue. The percentage
        value can be configured via csqIfDropConfigDropThreshold
        object.

        This object is configurable only if 'tailDrop' bit in 
        the value of qosIfCapabilities for the same ifIndex and traffic
        direction is set. If value of csqIfDropConfigAlgorithm is not
        'tailDrop', this object value has no effect." 
    ::= { csqIfDropConfigEntry 8 }
 

-- The Queue Table

csqIfQueueTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfQueueEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing configuration parameter for each queue on
        a QOS managable interface."
    ::= { csqInterface 5 }

csqIfQueueEntry OBJECT-TYPE
    SYNTAX          CsqIfQueueEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A set of WRR weight and queue size allocation weight for
        ingress or egress of a specific queue."
    INDEX           {
                        ifIndex,
                        csqIfQueueDirection,
                        csqIfQueueNumber
                    } 
    ::= { csqIfQueueTable 1 }

CsqIfQueueEntry ::= SEQUENCE {
        csqIfQueueDirection        IfDirection,
        csqIfQueueNumber           QosQueueNumber,
        csqIfQueueWrrWeight        Unsigned32,
        csqIfQueueSizeWeight       Unsigned32,
        csqIfQueueStatsGranularity INTEGER,
        csqIfQueueClassMapName     SnmpAdminString,
        csqIfQueueScheduling       INTEGER,
        csqIfQueueSrrWeight        Unsigned32
}

csqIfQueueDirection OBJECT-TYPE
    SYNTAX          IfDirection
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the queue direction." 
    ::= { csqIfQueueEntry 1 }

csqIfQueueNumber OBJECT-TYPE
    SYNTAX          QosQueueNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates queue number." 
    ::= { csqIfQueueEntry 2 }

csqIfQueueWrrWeight OBJECT-TYPE
    SYNTAX          Unsigned32 (1..255)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the WRR weight. This object is configurable
        only if the value of csqIfQueueScheduling is 'wrr'.
        When the value of csqIfQueueScheduling is not 'wrr',
        the value of this object has no effect." 
    ::= { csqIfQueueEntry 3 }

csqIfQueueSizeWeight OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the queue size weight." 
    ::= { csqIfQueueEntry 4 }

csqIfQueueStatsGranularity OBJECT-TYPE
    SYNTAX          INTEGER  {
                        perQueue(1),
                        perQueueThresh(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates whether QoS statistics is maintained per queue or
        per queue per threshold.

        'perQueue' indicates that QoS statistics is maintained per
        queue.

        'perQueueThresh' indicates that QoS statistics is maintained
        per queue per threshold." 
    ::= { csqIfQueueEntry 5 }

csqIfQueueClassMapName OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the name of an existing class-map attached at
        this interface for a queue in the specified direction.
        If there is no such class-map attached, the value of this
        object is zero-length string." 
    ::= { csqIfQueueEntry 6 }

csqIfQueueScheduling OBJECT-TYPE
    SYNTAX          INTEGER  {
                        wrr(1),
                        srr(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the queue scheduling method.

        'wrr' indicates that the queue scheduling method is
        Weight Round Robin.

        'srr' indicates that the queue scheduling method is
        Shaped Round Robin." 
    ::= { csqIfQueueEntry 7 }

csqIfQueueSrrWeight OBJECT-TYPE
    SYNTAX          Unsigned32 (1..255)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the SRR weight. This object is configurable
        only if the value of csqIfQueueScheduling is 'srr'.
        When the value of csqIfQueueScheduling is not 'srr',
        the value of this object has no effect." 
    ::= { csqIfQueueEntry 8 }
 

-- The Interface Mode Config Table

csqIfModeConfigTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfModeConfigEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table used to configure the QoS mode for layer-2
        interface."
    ::= { csqInterface 6 }

csqIfModeConfigEntry OBJECT-TYPE
    SYNTAX          CsqIfModeConfigEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing QoS mode information of layer-2
        interface."
    INDEX           { ifIndex } 
    ::= { csqIfModeConfigTable 1 }

CsqIfModeConfigEntry ::= SEQUENCE {
        csqIfVlanBasedQosModeEnable TruthValue
}

csqIfVlanBasedQosModeEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies if VLAN-based mode is enabled or disabled at
        the specified interface.

        If 'true', policy map that is attached to this interface
        has no effect, and QoS is driven by the policy map that is
        attached to the corresponding VLAN interface that this
        interface belongs to. Otherwise, the value of this object
        is 'false'." 
    ::= { csqIfModeConfigEntry 1 }
 

-- The Interface Consistency Check Table

csqIfConsistencyCheckTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfConsistencyCheckEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table used to configure the QoS-port attribute consistency
        check for Port Channel interface identified by ifIndex.
        QoS-port attribute consistency check consists of but not
        limited to checking for members of a Port Channel interface
        having the same queue type."
    ::= { csqInterface 7 }

csqIfConsistencyCheckEntry OBJECT-TYPE
    SYNTAX          CsqIfConsistencyCheckEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing QoS-port attribute consistency check
        information of Port Channel interface."
    INDEX           { ifIndex } 
    ::= { csqIfConsistencyCheckTable 1 }

CsqIfConsistencyCheckEntry ::= SEQUENCE {
        csqIfConsistencyCheckEnable TruthValue
}

csqIfConsistencyCheckEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies if QoS-port attribute consitency check is enabled
        or disabled at the specified channel interface.

        If 'true', QoS-port attribute consistency check is enabled. 
        If 'false', QoS-port attribute consistency check is disabled." 
    ::= { csqIfConsistencyCheckEntry 1 }
 

-- csqIfQosGroupInfoTable

csqIfQosGroupInfoTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfQosGroupInfoEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides QoS group information for
        QoS manageable interfaces in the device."
    ::= { csqInterface 8 }

csqIfQosGroupInfoEntry OBJECT-TYPE
    SYNTAX          CsqIfQosGroupInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A row instance contains Qos group information, which are per
        interface (ifIndex), per traffic direction, per QoS group."
    INDEX           {
                        ifIndex,
                        csqIfQosGroupInfoDirection,
                        csqIfQosGroupInfoGroupNumber
                    } 
    ::= { csqIfQosGroupInfoTable 1 }

CsqIfQosGroupInfoEntry ::= SEQUENCE {
        csqIfQosGroupInfoDirection      IfDirection,
        csqIfQosGroupInfoGroupNumber    Unsigned32,
        csqIfQosGroupInfoQueueSize      Unsigned32,
        csqIfQosGroupInfoHwMTU          Unsigned32,
        csqIfQosGroupInfoMTU            Unsigned32,
        csqIfQosGroupInfoDropType       INTEGER,
        csqIfQosGroupInfoResumeThresh   Unsigned32,
        csqIfQosGroupInfoPauseThresh    Unsigned32,
        csqIfQosGroupInfoScheduling     INTEGER,
        csqIfQosGroupInfoBandwidth      Unsigned32,
        csqIfQosGroupInfoBandwidthUnits INTEGER,
        csqIfQosGroupInfoShapeMinThresh Unsigned32,
        csqIfQosGroupInfoShapeMaxThresh Unsigned32,
        csqIfQosGroupInfoShapeUnits     INTEGER
}

csqIfQosGroupInfoDirection OBJECT-TYPE
    SYNTAX          IfDirection
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates traffic direction." 
    ::= { csqIfQosGroupInfoEntry 1 }

csqIfQosGroupInfoGroupNumber OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates a specific QoS group." 
    ::= { csqIfQosGroupInfoEntry 2 }

csqIfQosGroupInfoQueueSize OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the ingress queue size.
        
        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 3 }

csqIfQosGroupInfoHwMTU OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the hardware MTU. 

        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 4 }

csqIfQosGroupInfoMTU OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the MTU applied via QoS policy. 
        
        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 5 }

csqIfQosGroupInfoDropType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        drop(1),
                        noDrop(2),
                        notApplicable(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the drop type." 
    ::= { csqIfQosGroupInfoEntry 6 }

csqIfQosGroupInfoResumeThresh OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the buffer limit (In Bytes) at which
        the port resumes the peer.
        
        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 7 }

csqIfQosGroupInfoPauseThresh OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the buffer limit (In Bytes) at which
        the port pauses the peer.
        
        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 8 }

csqIfQosGroupInfoScheduling OBJECT-TYPE
    SYNTAX          INTEGER  {
                        wrr(1),
                        priority(2),
                        dwrr(3),
                        notApplicable(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the scheduling type applied via QoS
        policy."
    ::= { csqIfQosGroupInfoEntry 9 }

csqIfQosGroupInfoBandwidth OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the bandwidth.
        
        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 10 }

csqIfQosGroupInfoBandwidthUnits OBJECT-TYPE
    SYNTAX          INTEGER  {
                        kbps(1),
                        percentage(2),
                        notApplicable(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the unit of csqIfQosGroupInfoBandwidth."
    ::= { csqIfQosGroupInfoEntry 11 }
 
csqIfQosGroupInfoShapeMinThresh OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the shape minimum threshold.
        
        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 12 }

csqIfQosGroupInfoShapeMaxThresh OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the shape maximum threshold.
        
        Value 0 indicates it's not applicable for this direction." 
    ::= { csqIfQosGroupInfoEntry 13 }

csqIfQosGroupInfoShapeUnits OBJECT-TYPE
    SYNTAX          INTEGER  {
                        kbps(1),
                        percentage(2),
                        notApplicable(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the unit of 
        csqIfQosGroupInfoShapeMinThresh and 
        csqIfQosGroupInfoShapeMaxThresh."
        
    ::= { csqIfQosGroupInfoEntry 14 }

-- The Interface Statistics Table

csqIfStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing QoS statistics counters per QoS manageable
        interface."
    ::= { csqStatistics 1 }

csqIfStatsEntry OBJECT-TYPE
    SYNTAX          CsqIfStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry contains statistics, such as a drop packet
        counter, which are per interface (ifIndex), per  
        direction, per queue and per threshold."
    INDEX           {
                        ifIndex,
                        csqIfStatsDirection,
                        csqIfStatsQueueNumber,
                        csqIfStatsThresholdNumber
                    } 
    ::= { csqIfStatsTable 1 }

CsqIfStatsEntry ::= SEQUENCE {
        csqIfStatsDirection       IfDirection,
        csqIfStatsQueueNumber     QosQueueNumber,
        csqIfStatsThresholdNumber QosThresholdNumber,
        csqIfStatsDropPkts        Counter64
}

csqIfStatsDirection OBJECT-TYPE
    SYNTAX          IfDirection
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates traffic direction of an interface." 
    ::= { csqIfStatsEntry 1 }

csqIfStatsQueueNumber OBJECT-TYPE
    SYNTAX          QosQueueNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the queue number of the interface for which
        statistics are collected. For example : if the interface 
        has a queue type of oneP2Q2t, this index value can
        be 1, 2, 3." 
    ::= { csqIfStatsEntry 2 }

csqIfStatsThresholdNumber OBJECT-TYPE
    SYNTAX          QosThresholdNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the threshold number of a queue on the interface for
        which statistics are collected. For example : if the interface 
        has a queue type of oneP2Q2t, this index value can be 1, 2. If
        the value of the corresponding csqIfQueueStatsGranularity for
        the queue that this csqIfStatsThresholdNumber belongs to is 
        'perQueue', this csqIfStatsThresholdNumber index value is
        always 1." 
    ::= { csqIfStatsEntry 3 }

csqIfStatsDropPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets that have been received then dropped
        from the interface because they exceeded the threshold value
        configured at this queue and threshold of this interface." 
    ::= { csqIfStatsEntry 4 }
 

-- The Module Statistics Table

csqModuleStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqModuleStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table decribes QoS statistics counters per module that
        is capable of providing this information. Such module is
        identified by the entPhysicalIndex in ENTITY-MIB."
    ::= { csqStatistics 2 }

csqModuleStatsEntry OBJECT-TYPE
    SYNTAX          CsqModuleStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry contains per-module (entPhysicalIndex) statistics such
        as the number of dropped packets due to policing, number of IP
        packets with their ToS and CoS value changed, number of non IP
        packets with their CoS value changed, and number of MPLS
        packets with their EXP value changed."
    INDEX           { entPhysicalIndex } 
    ::= { csqModuleStatsTable 1 }

CsqModuleStatsEntry ::= SEQUENCE {
        csqModuleDropByPolicingPackets  Counter64,
        csqModuleTosChangedIpPackets    Counter64,
        csqModuleCosChangedIpPackets    Counter64,
        csqModuleCosChangedNonIpPackets Counter64,
        csqModuleExpChangedMplsPackets  Counter64
}

csqModuleDropByPolicingPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets that have been dropped due to policing." 
    ::= { csqModuleStatsEntry 1 }

csqModuleTosChangedIpPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of IP packets that have the ToS value changed due
        to policing." 
    ::= { csqModuleStatsEntry 2 }

csqModuleCosChangedIpPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of IP packets that have the CoS value changed due
        to policing." 
    ::= { csqModuleStatsEntry 3 }

csqModuleCosChangedNonIpPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of non IP packets that have the CoS value changed
        due to policing." 
    ::= { csqModuleStatsEntry 4 }

csqModuleExpChangedMplsPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of MPLS packets have the EXP value change
        due to policing." 
    ::= { csqModuleStatsEntry 5 }
 


csqModuleStatsExtTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqModuleStatsExtEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table describes additional QoS statistics counters per
        module that is capable of providing this information. Such
        module is identified by the entPhysicalIndex in ENTITY-MIB."
    ::= { csqStatistics 3 }

csqModuleStatsExtEntry OBJECT-TYPE
    SYNTAX          CsqModuleStatsExtEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains additional per-module (entPhysicalIndex)
        QoS statistics."
    INDEX           { entPhysicalIndex } 
    ::= { csqModuleStatsExtTable 1 }

CsqModuleStatsExtEntry ::= SEQUENCE {
        csqModuleTunnelEncapPackets         Counter32,
        csqModuleTunnelDecapPackets         Counter32,
        csqModuleDropByPolicingInOctets     Counter64,
        csqModuleDropByPolicingOutOctets    Counter64,
        csqModuleFwdByPolicingInPackets     Counter32,
        csqModuleFwdByPolicingInOctets      Counter64,
        csqModuleFwdByPolicingOutPackets    Counter32,
        csqModuleFwdByPolicingOutOctets     Counter64,
        csqModuleHighExceedInPackets        Counter32,
        csqModuleHighExceedInOctets         Counter64,
        csqModuleHighExceedOutPackets       Counter32,
        csqModuleHighExceedOutOctets        Counter64,
        csqModuleLowExceedInPackets         Counter32,
        csqModuleLowExceedInOctets          Counter64,
        csqModuleLowExceedOutPackets        Counter32,
        csqModuleLowExceedOutOctets         Counter64,
        csqModuleDropByAggPolicerInPackets  Counter32,
        csqModuleDropByAggPolicerInOctets   Counter64,
        csqModuleDropByAggPolicerOutPackets Counter32,
        csqModuleDropByAggPolicerOutOctets  Counter64,
        csqModuleFwdByAggPolicerInPackets   Counter32,
        csqModuleFwdByAggPolicerInOctets    Counter64,
        csqModuleFwdByAggPolicerOutPackets  Counter32,
        csqModuleFwdByAggPolicerOutOctets   Counter64,
        csqModuleAggHighExceedInPackets     Counter32,
        csqModuleAggHighExceedInOctets      Counter64,
        csqModuleAggHighExceedOutPackets    Counter32,
        csqModuleAggHighExceedOutOctets     Counter64,
        csqModuleAggLowExceedInPackets      Counter32,
        csqModuleAggLowExceedInOctets       Counter64,
        csqModuleAggLowExceedOutPackets     Counter32,
        csqModuleAggLowExceedOutOctets      Counter64,
        csqModuleDropByNetflowInPackets     Counter32,
        csqModuleDropByNetflowInOctets      Counter64,
        csqModuleDropByNetflowOutPackets    Counter32,
        csqModuleDropByNetflowOutOctets     Counter64,
        csqModuleFwdByNetflowInPackets      Counter32,
        csqModuleFwdByNetflowInOctets       Counter64,
        csqModuleFwdByNetflowOutPackets     Counter32,
        csqModuleFwdByNetflowOutOctets      Counter64,
        csqModuleNetflowExceedInPackets     Counter32,
        csqModuleNetflowExceedInOctets      Counter64,
        csqModuleNetflowExceedOutPackets    Counter32,
        csqModuleNetflowExceedOutOctets     Counter64,
        csqModuleCosChangedPackets          Counter32,
        csqModuleTrafficClassChangedPackets Counter32
}

csqModuleTunnelEncapPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of tunnel encapsulated packets." 
    ::= { csqModuleStatsExtEntry 1 }

csqModuleTunnelDecapPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of tunnel decapsulated packets." 
    ::= { csqModuleStatsExtEntry 2 }

csqModuleDropByPolicingInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets which are dropped due
        to policing." 
    ::= { csqModuleStatsExtEntry 3 }

csqModuleDropByPolicingOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets which are dropped due
        to policing." 
    ::= { csqModuleStatsExtEntry 4 }

csqModuleFwdByPolicingInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of policed ingress packets which are
        forwarded." 
    ::= { csqModuleStatsExtEntry 5 }

csqModuleFwdByPolicingInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of policed ingress octets which are
        forwarded." 
    ::= { csqModuleStatsExtEntry 6 }

csqModuleFwdByPolicingOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of policed egress packets which are
        forwarded." 
    ::= { csqModuleStatsExtEntry 7 }

csqModuleFwdByPolicingOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of policed egress octets which are
        forwarded." 
    ::= { csqModuleStatsExtEntry 8 }

csqModuleHighExceedInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets exceeding the high
        level policing rate." 
    ::= { csqModuleStatsExtEntry 9 }

csqModuleHighExceedInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets exceeding the high level
        policing rate." 
    ::= { csqModuleStatsExtEntry 10 }

csqModuleHighExceedOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets exceeding the high level
        policing rate." 
    ::= { csqModuleStatsExtEntry 11 }

csqModuleHighExceedOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets exceeding the high level
        policing rate." 
    ::= { csqModuleStatsExtEntry 12 }

csqModuleLowExceedInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets exceeding the low level
        policing rate." 
    ::= { csqModuleStatsExtEntry 13 }

csqModuleLowExceedInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets exceeding the low level
        policing rate." 
    ::= { csqModuleStatsExtEntry 14 }

csqModuleLowExceedOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets exceeding the low level
        policing rate." 
    ::= { csqModuleStatsExtEntry 15 }

csqModuleLowExceedOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets exceeding the low level
        policing rate." 
    ::= { csqModuleStatsExtEntry 16 }

csqModuleDropByAggPolicerInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets which are dropped
        by aggregate policers." 
    ::= { csqModuleStatsExtEntry 17 }

csqModuleDropByAggPolicerInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets which are dropped
        by aggregate policer." 
    ::= { csqModuleStatsExtEntry 18 }

csqModuleDropByAggPolicerOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets which are dropped by
        aggregate policers." 
    ::= { csqModuleStatsExtEntry 19 }

csqModuleDropByAggPolicerOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets which are dropped by
        aggregate policers." 
    ::= { csqModuleStatsExtEntry 20 }

csqModuleFwdByAggPolicerInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets which are forwarded
        by aggregate policers." 
    ::= { csqModuleStatsExtEntry 21 }

csqModuleFwdByAggPolicerInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets which are forwarded
        by aggregate policers." 
    ::= { csqModuleStatsExtEntry 22 }

csqModuleFwdByAggPolicerOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets which are forwarded
        by aggregate policers." 
    ::= { csqModuleStatsExtEntry 23 }

csqModuleFwdByAggPolicerOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets which are forwarded
        by aggregate policers." 
    ::= { csqModuleStatsExtEntry 24 }

csqModuleAggHighExceedInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets (policed by
        aggregate policers) exceeding the high level policing 
        rate." 
    ::= { csqModuleStatsExtEntry 25 }

csqModuleAggHighExceedInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets (policed by
        aggregate policers) exceeding the high level policing 
        rate." 
    ::= { csqModuleStatsExtEntry 26 }

csqModuleAggHighExceedOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets (policed by aggregate
        policers) exceeding the high level policing rate." 
    ::= { csqModuleStatsExtEntry 27 }

csqModuleAggHighExceedOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets (policed by aggregate
        policers) exceeding the high level policing rate." 
    ::= { csqModuleStatsExtEntry 28 }

csqModuleAggLowExceedInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets (policed by aggregate
        policers) exceeding the low level policing rate." 
    ::= { csqModuleStatsExtEntry 29 }

csqModuleAggLowExceedInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets (policed by aggregate
        policers) exceeding the low level policing rate." 
    ::= { csqModuleStatsExtEntry 30 }

csqModuleAggLowExceedOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets (policed by aggregate
        policers) exceeding the low level policing rate." 
    ::= { csqModuleStatsExtEntry 31 }

csqModuleAggLowExceedOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets (policed by aggregate
        policers) exceeding the low level policing rate." 
    ::= { csqModuleStatsExtEntry 32 }

csqModuleDropByNetflowInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets which are dropped by
        the netflow feature." 
    ::= { csqModuleStatsExtEntry 33 }

csqModuleDropByNetflowInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets which are dropped by
        the netflow feature." 
    ::= { csqModuleStatsExtEntry 34 }

csqModuleDropByNetflowOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets which are dropped by
        the netflow feature." 
    ::= { csqModuleStatsExtEntry 35 }

csqModuleDropByNetflowOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets which are dropped by
        the netflow feature." 
    ::= { csqModuleStatsExtEntry 36 }

csqModuleFwdByNetflowInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets which are forwarded
        by the netflow feature." 
    ::= { csqModuleStatsExtEntry 37 }

csqModuleFwdByNetflowInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets which are forwarded
        by the netflow feature." 
    ::= { csqModuleStatsExtEntry 38 }

csqModuleFwdByNetflowOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets which are forwarded
        by the netflow feature." 
    ::= { csqModuleStatsExtEntry 39 }

csqModuleFwdByNetflowOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets which are forwarded
        by the netflow feature." 
    ::= { csqModuleStatsExtEntry 40 }

csqModuleNetflowExceedInPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress packets exceeding the netflow
        policing rate." 
    ::= { csqModuleStatsExtEntry 41 }

csqModuleNetflowExceedInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of ingress octets exceeding the netflow
        policing rate." 
    ::= { csqModuleStatsExtEntry 42 }

csqModuleNetflowExceedOutPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress packets exceeding the netflow
        policing rate." 
    ::= { csqModuleStatsExtEntry 43 }

csqModuleNetflowExceedOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of egress octets exceeding the netflow
        policing rate." 
    ::= { csqModuleStatsExtEntry 44 }

csqModuleCosChangedPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets (IP and non-IP) that have the CoS value
        changed due to policing." 
    ::= { csqModuleStatsExtEntry 45 }

csqModuleTrafficClassChangedPackets OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of packets that have the Traffic Class changed
        due to policing" 
    ::= { csqModuleStatsExtEntry 46 }
 

-- csqIfStatsExtTable

csqIfStatsExtTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfStatsExtEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing QoS statistics counters per QoS manageable
        interface."
    ::= { csqStatistics 4 }

csqIfStatsExtEntry OBJECT-TYPE
    SYNTAX          CsqIfStatsExtEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains statistics, such as a drop BPDU packet
        counter, which are per interface (ifIndex), per direction."
    INDEX           {
                        ifIndex,
                        csqIfStatsDirection
                    } 
    ::= { csqIfStatsExtTable 1 }

CsqIfStatsExtEntry ::= SEQUENCE {
        csqIfBpduDropPkts Counter64
}

csqIfBpduDropPkts OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of dropped BPDU packets." 
    ::= { csqIfStatsExtEntry 1 }
 

-- The Interface per Qos Group Statistics Table

csqIfQosGroupStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfQosGroupStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing QoS statistics counters on
        QoS manageable interfaces."
    ::= { csqStatistics 5 }

csqIfQosGroupStatsEntry OBJECT-TYPE
    SYNTAX          CsqIfQosGroupStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry contains a specific statistics, which are per
        interface (ifIndex), per traffic direction, per QoS group."
    INDEX           {
                        ifIndex,
                        csqIfQosGroupStatsDirection,
                        csqIfQosGroupStatsGroupNumber,
                        csqIfQosGroupStatsType
                    } 
    ::= { csqIfQosGroupStatsTable 1 }

CsqIfQosGroupStatsEntry ::= SEQUENCE {
        csqIfQosGroupStatsDirection   IfDirection,
        csqIfQosGroupStatsGroupNumber Unsigned32,
        csqIfQosGroupStatsType        QosStatsType,
        csqIfQosGroupStatsValue       Counter64
}

csqIfQosGroupStatsDirection OBJECT-TYPE
    SYNTAX          IfDirection
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates traffic direction." 
    ::= { csqIfQosGroupStatsEntry 1 }

csqIfQosGroupStatsGroupNumber OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates a specific QoS group on the interface." 
    ::= { csqIfQosGroupStatsEntry 2 }

csqIfQosGroupStatsType OBJECT-TYPE
    SYNTAX          QosStatsType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates a specific statistics counter type." 
    ::= { csqIfQosGroupStatsEntry 3 }

csqIfQosGroupStatsValue OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the value of the specific statistics
        counter." 
    ::= { csqIfQosGroupStatsEntry 4 }
 

-- - Per interface per priority group buffer usage

csqIfPriGrpInBufUsageTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqIfPriGrpInBufUsageEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table contains the utilization of the buffer allocated for
        a specific priority group on the ingress of the QoS manageable
        interfaces."
    ::= { csqStatistics 6 }

csqIfPriGrpInBufUsageEntry OBJECT-TYPE
    SYNTAX          CsqIfPriGrpInBufUsageEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry indicates the per interface per priority group
        buffer utilization on ingress direction. The unit of the
        value of each object is cell. A cell represents the number
        of bytes, which is indicated by scalar object 
        csqServicePoolCellSize."
    INDEX           {
                        ifIndex,
                        csqIfPriGrpInBufUsageGrpNo
                    } 
    ::= { csqIfPriGrpInBufUsageTable 1 }

CsqIfPriGrpInBufUsageEntry ::= SEQUENCE {
        csqIfPriGrpInBufUsageGrpNo               Unsigned32,
        csqIfPriGrpInBufUsageMinCount            Unsigned32,
        csqIfPriGrpInBufUsageSharedCount         Unsigned32,
        csqIfPriGrpInBufUsageHeadroomCount       Unsigned32,
        csqIfPriGrpInBufUsageGlobalHeadroomCount Unsigned32,
        csqIfPriGrpInBufUsageSharedPeekCount     Unsigned32,
        csqIfPriGrpInBufUsageHeadroomPeekCount   Unsigned32
}

csqIfPriGrpInBufUsageGrpNo OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates a specific priority group on the
        interface." 
    ::= { csqIfPriGrpInBufUsageEntry 1 }

csqIfPriGrpInBufUsageMinCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the current usage of cells used out of
        the minimum reserved buffer." 
    ::= { csqIfPriGrpInBufUsageEntry 2 }

csqIfPriGrpInBufUsageSharedCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the current usage of cells used out of
        the shared pool." 
    ::= { csqIfPriGrpInBufUsageEntry 3 }

csqIfPriGrpInBufUsageHeadroomCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates current usage of cells out of the
        reserved headroom buffer. Headroom buffer is reserved to 
        account for PFC control frame round trip delays." 
    ::= { csqIfPriGrpInBufUsageEntry 4 }

csqIfPriGrpInBufUsageGlobalHeadroomCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates current usage of cells out of the
        global headroom buffer. Global headroom buffer is reserved
        and shared across all interfaces." 
    ::= { csqIfPriGrpInBufUsageEntry 5 }

csqIfPriGrpInBufUsageSharedPeekCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates peak usage of cells out of the shared
        pool." 
    ::= { csqIfPriGrpInBufUsageEntry 6 }

csqIfPriGrpInBufUsageHeadroomPeekCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates peak usage of cells out of the
        reserved headroom buffer." 
    ::= { csqIfPriGrpInBufUsageEntry 7 }
 

-- - Shared Service Pool Usage table

csqSharedPoolUsageTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqSharedPoolUsageEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table contains the utilization of the shared service pool in
        the system."
    ::= { csqStatistics 7 }

csqSharedPoolUsageEntry OBJECT-TYPE
    SYNTAX          CsqSharedPoolUsageEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry indicates the utilization of an shared service pool
        on a particular physical device, which is identified by a 
        specific module(indicated by entPhysicalIndex in ENTITY-MIB) 
        and instance (indicated by csqSharedPoolUsageInstNo). The unit 
        of the value of the objects is cell. A cell represents the 
        number of bytes, which is indicated by scalar object 
        csqServicePoolCellSize."
    INDEX           {
                        entPhysicalIndex,
                        csqSharedPoolUsageInstNo,
                        csqSharedPoolUsagePoolNo
                    } 
    ::= { csqSharedPoolUsageTable 1 }

CsqSharedPoolUsageEntry ::= SEQUENCE {
        csqSharedPoolUsageInstNo Unsigned32,
        csqSharedPoolUsagePoolNo Unsigned32,
        csqSharedPoolUsageUsed   Unsigned32,
        csqSharedPoolUsageRemain Unsigned32,
        csqSharedPoolUsagePeak   Unsigned32,
        csqSharedPoolUsageTotal  Unsigned32,
        csqSharedPoolUsageUsedTx   Unsigned32,
        csqSharedPoolUsageRemainTx Unsigned32,
        csqSharedPoolUsagePeakTx   Unsigned32,
        csqSharedPoolUsageTotalTx  Unsigned32,
        csqSharedPoolUsageNameTx   SnmpAdminString
}

csqSharedPoolUsageInstNo OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates an arbitrary number which uniquely
        identifies the instance number of a specific internal device." 
    ::= { csqSharedPoolUsageEntry 1 }

csqSharedPoolUsagePoolNo OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates the service pool number." 
    ::= { csqSharedPoolUsageEntry 2 }

csqSharedPoolUsageUsed OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of used cells in a shared
        pool." 
    ::= { csqSharedPoolUsageEntry 3 }

csqSharedPoolUsageRemain OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the remaining cells in a shared pool." 
    ::= { csqSharedPoolUsageEntry 4 }

csqSharedPoolUsagePeak OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the peak used cells in a shared pool." 
    ::= { csqSharedPoolUsageEntry 5 }

csqSharedPoolUsageTotal OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the total cells in a shared pool." 
    ::= { csqSharedPoolUsageEntry 6 }
 
csqSharedPoolUsageUsedTx OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of used cells in a output shared
        pool."
    ::= { csqSharedPoolUsageEntry 7 }

csqSharedPoolUsageRemainTx OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the remaining cells in a output shared pool."
    ::= { csqSharedPoolUsageEntry 8 }

csqSharedPoolUsagePeakTx OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the peak used cells in a output shared pool."
    ::= { csqSharedPoolUsageEntry 9 }

csqSharedPoolUsageTotalTx OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the total cells in a output shared pool."
    ::= { csqSharedPoolUsageEntry 10 }

csqSharedPoolUsageNameTx OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the name of output shared pool."
    ::= { csqSharedPoolUsageEntry 11 }


-- - Hardware Shared Service Pool Usage table

csqHwSharedPoolUsageTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqHwSharedPoolUsageEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table contains the utilization of the shared service pool 
        for internal devices of a specific physical entity that is 
        capable of providing this information."
    ::= { csqStatistics 8 }

csqHwSharedPoolUsageEntry OBJECT-TYPE
    SYNTAX          CsqHwSharedPoolUsageEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry indicates the utilization of an shared service pool
        for an internal device on a particular physical device, which is 
        identified by a specific module(indicated by entPhysicalIndex in 
        ENTITY-MIB), device ID (indicated by csqHwSharedPoolDeviceId), traffic
        direction (indicated by csqHwSharedPoolStatsDirection), instance 
        (indicated by csqHwSharedPoolUsageInstNo) and statistic type 
        (indicated by csqHwSharedPoolStatsType). 
        The unit of the value of the objects is cell. A cell represents the 
        number of bytes, which is indicated by scalar object 
        csqServicePoolCellSize."
    INDEX           {
                        entPhysicalIndex,
                        csqHwSharedPoolDeviceId,
                        csqHwSharedPoolUsageInstNo,
                        csqHwSharedPoolStatsDirection,
                        csqHwSharedPoolStatsType
                    } 
    ::= { csqHwSharedPoolUsageTable 1 }

CsqHwSharedPoolUsageEntry ::= SEQUENCE {
        csqHwSharedPoolDeviceId           INTEGER,
        csqHwSharedPoolUsageInstNo        Unsigned32,
        csqHwSharedPoolStatsDirection     INTEGER,
        csqHwSharedPoolStatsType          INTEGER,
        csqHwSharedPoolUsageUsed          Unsigned32,
        csqHwSharedPoolUsageRemain        Unsigned32,
        csqHwSharedPoolUsageShared        Unsigned32,
        csqHwSharedPoolUsageTotal         Unsigned32 
}

csqHwSharedPoolDeviceId OBJECT-TYPE
    SYNTAX          INTEGER { 
                        northStar(1)
                    } 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates an arbitrary number which uniquely
        identifies a specific internal device."
    ::= { csqHwSharedPoolUsageEntry 1 }

csqHwSharedPoolUsageInstNo OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates an arbitrary number which uniquely
        identifies the instance number of a specific internal device." 
    ::= { csqHwSharedPoolUsageEntry 2 }

csqHwSharedPoolStatsDirection OBJECT-TYPE
    SYNTAX          INTEGER  {
                        inputStats-ingressStraight(1),
                        inputStats-ingressHairpin(2),
                        inputStats-egress(3),
                        outputStats-ingressStraight(4),
                        outputStats-ingressHairpin(5),
                        outputStats-egress(6)
                    }
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates the flow direction of a specific
        traffic statistics."
    ::= { csqHwSharedPoolUsageEntry 3 }

csqHwSharedPoolStatsType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        drop(1),
                        nodrop(2),
                        span(3),
                        sup(4)
                    }
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates the specific traffic classification type for 
         hardware shared pool.

         drop -  droppable traffic class
         nodrop - no drop traffic class
         span -  span traffic class
         sup -  sup traffic class."
    ::= { csqHwSharedPoolUsageEntry 4 }

csqHwSharedPoolUsageUsed OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of used cells in a hardware
        shared pool." 
    ::= { csqHwSharedPoolUsageEntry 5 }

csqHwSharedPoolUsageRemain OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the remaining cells in a hardware
         shared pool." 
    ::= { csqHwSharedPoolUsageEntry 6 }

csqHwSharedPoolUsageShared OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the shared used cells in a hardware
        shared pool." 
    ::= { csqHwSharedPoolUsageEntry 7 }

csqHwSharedPoolUsageTotal OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the total cells in a hardware shared pool." 
    ::= { csqHwSharedPoolUsageEntry 8 }


-- csqPolicerUsage group
--   

--   
-- The csqPolicerUsageTable

csqPolicerUsageTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqPolicerUsageEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains the usage of policers in the device."
    ::= { csqPolicerUsage 1 }

csqPolicerUsageEntry OBJECT-TYPE
    SYNTAX          CsqPolicerUsageEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each row contains the policer usage information for every
        module, denoted by its entPhysicalIndex, which is capable of
        providing this information."
    INDEX           {
                        entPhysicalIndex,
                        csqPolicerType
                    } 
    ::= { csqPolicerUsageTable 1 }

CsqPolicerUsageEntry ::= SEQUENCE {
        csqPolicerType  QosPolicerType,
        csqPolicerUsed  Unsigned32,
        csqPolicerTotal Unsigned32
}

csqPolicerType OBJECT-TYPE
    SYNTAX          QosPolicerType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This object indicates the policer type." 
    ::= { csqPolicerUsageEntry 1 }

csqPolicerUsed OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of policers that are currently used." 
    ::= { csqPolicerUsageEntry 2 }

csqPolicerTotal OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of policers." 
    ::= { csqPolicerUsageEntry 3 }
 

-- Module group
--   

--   
-- Module DSCP Rewrite Enable Table

csqModuleDscpRewriteEnableTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CsqModuleDscpRewriteEnableEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The table containing information of DSCP Rewrite Enable for
        each module. Such module is identified by the entPhysicalIndex
        in ENTITY-MIB. The value of each entry needs to be viewed 
        in association with the global value, csqDscpRewriteEnable."
    ::= { csqModule 1 }

csqModuleDscpRewriteEnableEntry OBJECT-TYPE
    SYNTAX          CsqModuleDscpRewriteEnableEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry contains the configuration of DSCP Rewrite Enable
        Status for a DSCP Rewrite managable module. such module is 
        of type entPhysicalClass module(9) in ENTITY-MIB. 

        Entries are created by the agent at the system power-up or
        module insertion. Entries are deleted by the agent upon module
        removal."
    INDEX           { entPhysicalIndex } 
    ::= { csqModuleDscpRewriteEnableTable 1 }

CsqModuleDscpRewriteEnableEntry ::= SEQUENCE {
        csqModuleDscpRewriteEnable TruthValue
}

csqModuleDscpRewriteEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object specifies whether DSCP rewrite is enabled on a
        particular module when the value of csqDscpRewriteEnable is 
        set to 'true'. The value of this object has no effect (DSCP 
        rewrite will be disabled on this module) when the value of 
        csqDscpRewriteEnable is set to 'false'." 
    ::= { csqModuleDscpRewriteEnableEntry 1 }
 

-- Conformance

ciscoSwitchQosMIBCompliances  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBConformance 1 }

ciscoSwitchQosMIBGroups  OBJECT IDENTIFIER
    ::= { ciscoSwitchQosMIBConformance 2 }


ciscoSwitchQosMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB.
        This compliance is superceded by 
        ciscoSwitchQosMIBComplianceRev2."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 1 }

-- ciscoSwitchQosMIBComplianceRev2

ciscoSwitchQosMIBComplianceRev2 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB.
        This compliance is superceded by 
        ciscoSwitchQosMIBComplianceRev3."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    GROUP           ciscoSwitchQosPolicerUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support policer usage."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 2 }

-- ciscoSwitchQosMIBComplianceRev3

ciscoSwitchQosMIBComplianceRev3 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB.
        This compliance is superceded by 
        ciscoSwitchQosMIBComplianceRev4."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    GROUP           ciscoSwitchQosPolicerUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support policer usage."

    GROUP           ciscoSwitchQosModuleStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional module QoS statistics."

    GROUP           ciscoSwitchQosIfStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional interface QoS statistics."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 3 }

-- ciscoSwitchQosMIBComplianceRev4

ciscoSwitchQosMIBComplianceRev4 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB.
        This compliance is deprecated and superceded by
        ciscoSwitchQosMIBComplianceRev5."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    GROUP           ciscoSwitchQosPolicerUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support policer usage."

    GROUP           ciscoSwitchQosModuleStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional module QoS statistics."

    GROUP           ciscoSwitchQosIfStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional interface QoS statistics."

    GROUP           ciscoSwitchQosModuleDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP Rewrite at module level."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqModuleDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 4 }

-- ciscoSwitchQosMIBComplianceRev5

ciscoSwitchQosMIBComplianceRev5 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    GROUP           ciscoSwitchQosPolicerUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support policer usage."

    GROUP           ciscoSwitchQosModuleStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional module QoS statistics."

    GROUP           ciscoSwitchQosIfStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional interface QoS statistics."

    GROUP           ciscoSwitchQosModuleDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP Rewrite at module level."

    GROUP           ciscoSwitchQosModuleClassChangedGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS/TrafficClass changed packets count
        for each module capable of providing this information."

    GROUP           ciscoSwitchQosTenGOnlyModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support 10-Gigabit Ethernet only mode."

    GROUP           ciscoSwitchQosIfQueueModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfLanQueuingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support LAN queuing configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueBufferGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueSchedulingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue scheduling configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queueing information at an interface."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqModuleDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqTenGOnlyMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConfigQueueMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueClassMapName
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfIngressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfEgressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigQueueBuffer
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueScheduling
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 5 }

-- ciscoSwitchQosMIBComplianceRev6

ciscoSwitchQosMIBComplianceRev6 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    GROUP           ciscoSwitchQosPolicerUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support policer usage."

    GROUP           ciscoSwitchQosModuleStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional module QoS statistics."

    GROUP           ciscoSwitchQosIfStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional interface QoS statistics."

    GROUP           ciscoSwitchQosModuleDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP Rewrite at module level."

    GROUP           ciscoSwitchQosModuleClassChangedGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS/TrafficClass changed packets count
        for each module capable of providing this information."

    GROUP           ciscoSwitchQosTenGOnlyModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support 10-Gigabit Ethernet only mode."

    GROUP           ciscoSwitchQosIfQueueModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfLanQueuingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support LAN queuing configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueBufferGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueSchedulingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue scheduling configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queueing information at an interface."

    GROUP           ciscoSwitchQosIfQosGroupInfoGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per QoS group configuration."

    GROUP           ciscoSwitchQosIfQosGroupStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per QoS group statictics."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqModuleDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqTenGOnlyMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConfigQueueMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueClassMapName
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfIngressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfEgressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigQueueBuffer
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueScheduling
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 6 }

-- ciscoSwitchQosMIBComplianceRev7

ciscoSwitchQosMIBComplianceRev7 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    GROUP           ciscoSwitchQosPolicerUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support policer usage."

    GROUP           ciscoSwitchQosModuleStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional module QoS statistics."

    GROUP           ciscoSwitchQosIfStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional interface QoS statistics."

    GROUP           ciscoSwitchQosModuleDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP Rewrite at module level."

    GROUP           ciscoSwitchQosModuleClassChangedGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS/TrafficClass changed packets count
        for each module capable of providing this information."

    GROUP           ciscoSwitchQosTenGOnlyModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support 10-Gigabit Ethernet only mode."

    GROUP           ciscoSwitchQosIfQueueModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfLanQueuingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support LAN queuing configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueBufferGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueSchedulingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue scheduling configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queueing information at an interface."

    GROUP           ciscoSwitchQosIfQosGroupInfoGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per QoS group configuration."

    GROUP           ciscoSwitchQosIfQosGroupStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per QoS group statictics."

    GROUP           ciscoSwitchQosIfPriGrpInBufUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per priority group statictics."

    GROUP           ciscoSwitchQosServicePoolUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support service pool statictics."

    GROUP           ciscoSwitchQosServicePoolCellSizeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support service pool statistics."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqModuleDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqTenGOnlyMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConfigQueueMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueClassMapName
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfIngressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfEgressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigQueueBuffer
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueScheduling
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 7 }

-- ciscoSwitchQosMIBComplianceRev8

ciscoSwitchQosMIBComplianceRev8 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the CISCO-SWITCH-QOS-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS { ciscoSwitchQosIfConfigGroup }

    GROUP           ciscoSwitchQosIfStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port statictics."

    GROUP           ciscoSwitchQosModuleStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support module statistics."

    GROUP           ciscoSwitchQosPortQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support port queueing mode."

    GROUP           ciscoSwitchQosRedirectPolicingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support policing of ACL-redirected packets."

    GROUP           ciscoSwitchQosDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP rewrite."

    GROUP           ciscoSwitchQosMappingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support mapping between CoS, IpPrecedence,
        MPLS-EXP and DSCP."

    GROUP           ciscoSwitchQosMutationGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS, MPLS-EXP and DSCP mutation."

    GROUP           ciscoSwitchQosIfCosToQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDscpAssignGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support Dscp to queue assignment at an interface."

    GROUP           ciscoSwitchQosIfDropConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support drop threshold configuration at an interface."

    GROUP           ciscoSwitchQosIfQueueGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support queue configuration at an interface."

    GROUP           ciscoSwitchQosMarkingStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support marking statistics at the device."

    GROUP           ciscoSwitchQosIfModeConfigGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support VLAN-based Qos mode configuration at an interface."

    GROUP           ciscoSwitchQosIfCCGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support consistency check at an interface."

    GROUP           ciscoSwitchQosPolicerUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support policer usage."

    GROUP           ciscoSwitchQosModuleStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional module QoS statistics."

    GROUP           ciscoSwitchQosIfStatsExtGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support additional interface QoS statistics."

    GROUP           ciscoSwitchQosModuleDscpRewriteGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support DSCP Rewrite at module level."

    GROUP           ciscoSwitchQosModuleClassChangedGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support CoS/TrafficClass changed packets count
        for each module capable of providing this information."

    GROUP           ciscoSwitchQosTenGOnlyModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support 10-Gigabit Ethernet only mode."

    GROUP           ciscoSwitchQosIfQueueModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfLanQueuingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support LAN queuing configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueBufferGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue mode configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueSchedulingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queue scheduling configuration at 
        an interface."

    GROUP           ciscoSwitchQosIfQueueingGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support queueing information at an interface."

    GROUP           ciscoSwitchQosIfQosGroupInfoGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per QoS group configuration."

    GROUP           ciscoSwitchQosIfQosGroupStatsGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per QoS group statictics."

    GROUP           ciscoSwitchQosIfPriGrpInBufUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support per priority group statictics."

    GROUP           ciscoSwitchQosServicePoolUsageGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support service pool statictics."

    GROUP           ciscoSwitchQosServicePoolCellSizeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support service pool statistics."

    GROUP           ciscoSwitchQosIfQosGroupInfoShapeGroup
    DESCRIPTION
        "This group is mandatory only for platforms
        which support shape information."

    OBJECT          csqCosMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqDscpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqExpMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfMutationRowStatus
    SYNTAX          INTEGER  {
                        active(1),
                        createAndGo(4),
                        destroy(6)
                    }
    MIN-ACCESS      read-only
    DESCRIPTION
        "Support for 'createAndWait' is not required."

    OBJECT          csqIfCosMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfExpMutationMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPoliceRedirectedTrafficEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqPortQueueingModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqMarkingStatisticsEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIpPrecToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpToDscpDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingNormalBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMappingMaxBurstDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqCosMutationToCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqDscpMutationToDscp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqExpMutationToExp
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDefaultCos
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfTrustState
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfCosToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueQueueNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDscpToQueueThresholdNumber
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropAlgorithm
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigDropThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMinWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigMaxWredThreshold
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueWrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSizeWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfVlanBasedQosModeEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConsistencyCheckEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqModuleDscpRewriteEnable
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqTenGOnlyMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfConfigQueueMode
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueClassMapName
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfIngressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfEgressPolicyMap
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfDropConfigQueueBuffer
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueScheduling
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          csqIfQueueSrrWeight
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { ciscoSwitchQosMIBCompliances 8 }

-- Units of Conformance

ciscoSwitchQosMappingGroup OBJECT-GROUP
    OBJECTS         {
                        csqCosToDscpDscp,
                        csqIpPrecToDscpDscp,
                        csqExpToDscpDscp,
                        csqDscpMappingCos,
                        csqDscpMappingExp,
                        csqDscpMappingNormalBurstDscp,
                        csqDscpMappingMaxBurstDscp
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides the QoS mapping
        information in the device."
    ::= { ciscoSwitchQosMIBGroups 1 }

ciscoSwitchQosMutationGroup OBJECT-GROUP
    OBJECTS         {
                        csqMaxCosMutationMap,
                        csqMaxDscpMutationMap,
                        csqMaxExpMutationMap,
                        csqCosMutationRowStatus,
                        csqDscpMutationRowStatus,
                        csqExpMutationRowStatus,
                        csqCosMutationToCos,
                        csqDscpMutationToDscp,
                        csqExpMutationToExp,
                        csqIfCosMutationMap,
                        csqIfDscpMutationMap,
                        csqIfExpMutationMap,
                        csqIfMutationRowStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides the QoS mutation
        information in the device."
    ::= { ciscoSwitchQosMIBGroups 2 }

ciscoSwitchQosIfConfigGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfDefaultCos,
                        csqIfTrustState
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides QoS configuration
        at an interface."
    ::= { ciscoSwitchQosMIBGroups 3 }

ciscoSwitchQosIfCosToQueueGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfCosToQueueQueueNumber,
                        csqIfCosToQueueThresholdNumber
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides CoS assignment
        information at an interface."
    ::= { ciscoSwitchQosMIBGroups 4 }

ciscoSwitchQosIfDropConfigGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfDropConfigDropAlgorithm,
                        csqIfDropConfigDropThreshold,
                        csqIfDropConfigMinWredThreshold,
                        csqIfDropConfigMaxWredThreshold
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides drop threshold
        information at an interface."
    ::= { ciscoSwitchQosMIBGroups 5 }

ciscoSwitchQosIfQueueGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfQueueWrrWeight,
                        csqIfQueueSizeWeight,
                        csqIfQueueStatsGranularity
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides queue information
        at an interface."
    ::= { ciscoSwitchQosMIBGroups 6 }

ciscoSwitchQosIfStatsGroup OBJECT-GROUP
    OBJECTS         { csqIfStatsDropPkts }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides drop packets
        information at an interface."
    ::= { ciscoSwitchQosMIBGroups 7 }

ciscoSwitchQosModuleStatsGroup OBJECT-GROUP
    OBJECTS         {
                        csqModuleDropByPolicingPackets,
                        csqModuleTosChangedIpPackets,
                        csqModuleCosChangedIpPackets,
                        csqModuleCosChangedNonIpPackets,
                        csqModuleExpChangedMplsPackets
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides QoS statistics
        information at each QoS capable module."
    ::= { ciscoSwitchQosMIBGroups 8 }

ciscoSwitchQosIfDscpAssignGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfDscpToQueueQueueNumber,
                        csqIfDscpToQueueThresholdNumber
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides DSCP to queue
        assignment information at an interface."
    ::= { ciscoSwitchQosMIBGroups 9 }

ciscoSwitchQosDscpRewriteGroup OBJECT-GROUP
    OBJECTS         { csqDscpRewriteEnable }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides DSCP rewrite
        information."
    ::= { ciscoSwitchQosMIBGroups 10 }

ciscoSwitchQosRedirectPolicingGroup OBJECT-GROUP
    OBJECTS         { csqPoliceRedirectedTrafficEnable }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides information on
        policing of ACL-redirected traffic."
    ::= { ciscoSwitchQosMIBGroups 11 }

ciscoSwitchQosPortQueueingGroup OBJECT-GROUP
    OBJECTS         { csqPortQueueingModeEnable }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides information on
        port-queuing mode."
    ::= { ciscoSwitchQosMIBGroups 12 }

ciscoSwitchQosMarkingStatsGroup OBJECT-GROUP
    OBJECTS         { csqMarkingStatisticsEnable }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides information on
        marking statistics configuration."
    ::= { ciscoSwitchQosMIBGroups 13 }

ciscoSwitchQosIfCCGroup OBJECT-GROUP
    OBJECTS         { csqIfConsistencyCheckEnable }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides information on
        interface consistency check configuration."
    ::= { ciscoSwitchQosMIBGroups 14 }

ciscoSwitchQosIfModeConfigGroup OBJECT-GROUP
    OBJECTS         { csqIfVlanBasedQosModeEnable }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides information on
        QoS mode configuration."
    ::= { ciscoSwitchQosMIBGroups 15 }

ciscoSwitchQosPolicerUsageGroup OBJECT-GROUP
    OBJECTS         {
                        csqPolicerUsed,
                        csqPolicerTotal
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides information on
        QoS policer usage."
    ::= { ciscoSwitchQosMIBGroups 16 }

ciscoSwitchQosModuleStatsExtGroup OBJECT-GROUP
    OBJECTS         {
                        csqModuleTunnelEncapPackets,
                        csqModuleTunnelDecapPackets,
                        csqModuleDropByPolicingInOctets,
                        csqModuleDropByPolicingOutOctets,
                        csqModuleFwdByPolicingInPackets,
                        csqModuleFwdByPolicingOutPackets,
                        csqModuleFwdByPolicingInOctets,
                        csqModuleFwdByPolicingOutOctets,
                        csqModuleHighExceedInPackets,
                        csqModuleHighExceedOutPackets,
                        csqModuleHighExceedInOctets,
                        csqModuleHighExceedOutOctets,
                        csqModuleLowExceedOutPackets,
                        csqModuleLowExceedInPackets,
                        csqModuleLowExceedInOctets,
                        csqModuleLowExceedOutOctets,
                        csqModuleDropByAggPolicerInPackets,
                        csqModuleDropByAggPolicerOutPackets,
                        csqModuleDropByAggPolicerInOctets,
                        csqModuleDropByAggPolicerOutOctets,
                        csqModuleFwdByAggPolicerInPackets,
                        csqModuleFwdByAggPolicerOutPackets,
                        csqModuleFwdByAggPolicerInOctets,
                        csqModuleFwdByAggPolicerOutOctets,
                        csqModuleAggHighExceedInPackets,
                        csqModuleAggHighExceedOutPackets,
                        csqModuleAggHighExceedInOctets,
                        csqModuleAggHighExceedOutOctets,
                        csqModuleAggLowExceedInPackets,
                        csqModuleAggLowExceedOutPackets,
                        csqModuleAggLowExceedInOctets,
                        csqModuleAggLowExceedOutOctets,
                        csqModuleDropByNetflowInPackets,
                        csqModuleDropByNetflowOutPackets,
                        csqModuleDropByNetflowInOctets,
                        csqModuleDropByNetflowOutOctets,
                        csqModuleFwdByNetflowInPackets,
                        csqModuleFwdByNetflowOutPackets,
                        csqModuleFwdByNetflowInOctets,
                        csqModuleFwdByNetflowOutOctets,
                        csqModuleNetflowExceedInPackets,
                        csqModuleNetflowExceedOutPackets,
                        csqModuleNetflowExceedInOctets,
                        csqModuleNetflowExceedOutOctets
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides additional QoS
        statistics information at each QoS capable module."
    ::= { ciscoSwitchQosMIBGroups 17 }

ciscoSwitchQosIfStatsExtGroup OBJECT-GROUP
    OBJECTS         { csqIfBpduDropPkts }
    STATUS          current
    DESCRIPTION
        "A collection of objects which provides additional
        interface QoS statistics information."
    ::= { ciscoSwitchQosMIBGroups 18 }

ciscoSwitchQosModuleDscpRewriteGroup OBJECT-GROUP
    OBJECTS         { csqModuleDscpRewriteEnable }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides DSCP
        rewrite information for each module."
    ::= { ciscoSwitchQosMIBGroups 19 }

ciscoSwitchQosModuleClassChangedGroup OBJECT-GROUP
    OBJECTS         {
                        csqModuleCosChangedPackets,
                        csqModuleTrafficClassChangedPackets
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides CoS/Traffic Class
        changed packets count information for each module."
    ::= { ciscoSwitchQosMIBGroups 20 }

ciscoSwitchQosTenGOnlyModeGroup OBJECT-GROUP
    OBJECTS         { csqTenGOnlyMode }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides 10Gigabit
        Ethernet only mode information."
    ::= { ciscoSwitchQosMIBGroups 21 }

ciscoSwitchQosIfQueueModeGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfQueueModeCpb,
                        csqIfConfigQueueMode
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides queue mode
        information for each QoS capable interface."
    ::= { ciscoSwitchQosMIBGroups 22 }

ciscoSwitchQosIfLanQueuingGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfQueueClassMapName,
                        csqIfIngressPolicyMap,
                        csqIfEgressPolicyMap
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides LAN queueing mapping
        information for each QoS capable interface."
    ::= { ciscoSwitchQosMIBGroups 23 }

ciscoSwitchQosIfQueueBufferGroup OBJECT-GROUP
    OBJECTS         { csqIfDropConfigQueueBuffer }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides tail drop queue buffer
        information for each QoS capable interface."
    ::= { ciscoSwitchQosMIBGroups 24 }

ciscoSwitchQosIfQueueSchedulingGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfQueueScheduling,
                        csqIfQueueSrrWeight
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides queue scheduling
        information for each QoS capable interface."
    ::= { ciscoSwitchQosMIBGroups 25 }

ciscoSwitchQosIfQueueingGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfIngressQueueingEnable,
                        csqIfEgressQueueingEnable,
                        csqIfQueueingTrustState
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides queueing
        information for each QoS capable interface."
    ::= { ciscoSwitchQosMIBGroups 26 }

ciscoSwitchQosIfQosGroupInfoGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfQosGroupInfoQueueSize,
                        csqIfQosGroupInfoHwMTU,
                        csqIfQosGroupInfoMTU,
                        csqIfQosGroupInfoDropType,
                        csqIfQosGroupInfoResumeThresh,
                        csqIfQosGroupInfoPauseThresh,
                        csqIfQosGroupInfoScheduling,
                        csqIfQosGroupInfoBandwidth,
                        csqIfQosGroupInfoBandwidthUnits
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides information for
        each QoS group on an interface."
    ::= { ciscoSwitchQosMIBGroups 27 }

ciscoSwitchQosIfQosGroupStatsGroup OBJECT-GROUP
    OBJECTS         { csqIfQosGroupStatsValue }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides the statistics
        of each QoS group on an interface."
    ::= { ciscoSwitchQosMIBGroups 28 }

ciscoSwitchQosIfPriGrpInBufUsageGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfPriGrpInBufUsageMinCount,
                        csqIfPriGrpInBufUsageSharedCount,
                        csqIfPriGrpInBufUsageHeadroomCount,
                        csqIfPriGrpInBufUsageGlobalHeadroomCount,
                        csqIfPriGrpInBufUsageSharedPeekCount,
                        csqIfPriGrpInBufUsageHeadroomPeekCount
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides the statistics
        of each priority group on the interface."
    ::= { ciscoSwitchQosMIBGroups 29 }

ciscoSwitchQosServicePoolUsageGroup OBJECT-GROUP
    OBJECTS         {
                        csqSharedPoolUsageUsed,
                        csqSharedPoolUsageRemain,
                        csqSharedPoolUsagePeak,
                        csqSharedPoolUsageTotal
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides the statistics
        of each service pool."
    ::= { ciscoSwitchQosMIBGroups 30 }

ciscoSwitchQosServicePoolCellSizeGroup OBJECT-GROUP
    OBJECTS         { csqServicePoolCellSize }
    STATUS          current
    DESCRIPTION
        "A collection of object which indicates the number of
        bytes for a service pool cell."
    ::= { ciscoSwitchQosMIBGroups 31 }

ciscoSwitchQosIfQosGroupInfoShapeGroup OBJECT-GROUP
    OBJECTS         {
                        csqIfQosGroupInfoShapeMinThresh,
                        csqIfQosGroupInfoShapeMaxThresh,
                        csqIfQosGroupInfoShapeUnits
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides shape information for
        each QoS group on an interface."
    ::= { ciscoSwitchQosMIBGroups 32 }

ciscoSwitchQosHwServicePoolUsageGroup OBJECT-GROUP
    OBJECTS         {
                        csqHwSharedPoolUsageUsed,
                        csqHwSharedPoolUsageRemain,
                        csqHwSharedPoolUsageShared,
                        csqHwSharedPoolUsageTotal
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides the statistics
        of each hardware service pool."
    ::= { ciscoSwitchQosMIBGroups 33 }

ciscoSwitchQosServicePoolUsageTxGroup OBJECT-GROUP
    OBJECTS         {
                        csqSharedPoolUsageUsedTx,
                        csqSharedPoolUsageRemainTx,
                        csqSharedPoolUsagePeakTx,
                        csqSharedPoolUsageTotalTx,
                        csqSharedPoolUsageNameTx
                    }
    STATUS          current
    DESCRIPTION
        "A collection of object which provides the statistics
        of each output service pool."
    ::= { ciscoSwitchQosMIBGroups 34 }
END



