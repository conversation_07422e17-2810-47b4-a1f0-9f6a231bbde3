-- *****************************************************************
-- CISCO-CAT6K-CROSSBAR-MIB.my
--   
-- Mar 2001, <PERSON>
-- Nov. 2002, <PERSON>/<PERSON>
-- March  2003, <PERSON>
-- June  2004, <PERSON>
--   
-- Copyright (c) 2001-2009, 2012-2013 by cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-CAT6K-CROSSBAR-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    Counter32,
    Unsigned32,
    Gauge32,
    NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    OBJECT-GRO<PERSON>,
    NOTIFICATION-GROUP
        FROM SNMPv2-CO<PERSON>
    TEXTUAL-CONVENTION,
    TruthValue,
    DisplayString,
    DateAndTime,
    TimeStamp
        FROM SNMPv2-TC
    entPhysicalName,
    entPhysicalVendorType,
    entPhysicalIndex,
    entPhysicalDescr
        FROM ENTITY-MIB
    Percent
        FROM CISCO-QOS-PIB-MIB
    ciscoMgmt
        FROM CISCO-SMI;


ciscoCat6kCrossbarMIB MODULE-IDENTITY
    LAST-UPDATED    "201309160000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
            "Cisco Systems
            Customer Service
            Postal: 170 W Tasman Drive
            San Jose, CA  95134
            USA
            Tel: ****** 553-NETS

            E-mail: <EMAIL>"
    DESCRIPTION
        "The Catalyst 6000 Crossbar MIB provides instrumentation for
        configuration and operation of the crossbar switching fabric
        module, and additional functionalities for switching bus 
        and internal bus, etc."
    REVISION        "201309160000Z"
    DESCRIPTION
        "Added two new enumerations 'pfc4xxl' and 'pfc4lite'
        in cc6kxbarSysCapPfcOperMode."
    REVISION        "201205310000Z"
    DESCRIPTION
        "Added following OBJECT-GROUP
        - cc6kxbarStatisticsLbusDropGroup
        Added new compliance
        - cc6kxbarMIBComplianceVer8.
        Updated the description for
        - cc6kxbarStatisticsPeakTmInUtil
        - cc6kxbarStatisticsPeakTmOutUtil."
    REVISION        "200902150000Z"
    DESCRIPTION
        "Added cc6kxbarErrRcvryPersLinkResGroup and
        cc6kxbarErrRcvryChResyncGroup,
        cc6kxbarTMSwBusUtilGroup, 
        cc6kxbarTMSwBusUtilNotifControlGroup,
        cc6kxbarTMSwBusUtilNotifObjectGroup,
        cc6kxbarTMSwBusUtilNotifGroup,
        cc6kxbarTMChUtilGroup,
        cc6kxbarTMChUtilNotifControlGroup,
        cc6kxbarTMChUtilNotifObjectGroup,
        cc6kxbarTMChUtilNotifGroup.

        Added two new enumerations 'pfc4' and 'pfc4xl' 
        in cc6kxbarSysCapPfcOperMode.

        Added DISPLAY-HINT in TEXTUAL-CONVENTION
        of ModuleSlotNumber and FabricChannelNumber."
    REVISION        "200712180000Z"
    DESCRIPTION
        "Added cc6kxbarSysCapPfcOperModeGroup,
        cc6kxbarSysCapSwitchResGroup, cc6kxbarChannelUtilGroup1,
        cc6kxbarSwBusGroup1, cc6kxbarErrorGroup,
        cc6kxbarErrRcvryThresholdGroup and 
        cc6kxbarErrRcvrySwitchoverGroup."
    REVISION        "200612190000Z"
    DESCRIPTION
        "Added cc6kxbarDcefOnlyModeAllowedGroup,
        cc6kxbarForceBusModeGroup,
        cc6kxbarFlowCtrlNotifCtrlGroup,
        cc6kxbarFlowCtrlBusThreshGroup,
        cc6kxbarFlowCtrlBusThrNotifGroup 
        and cc6kxbarBusOnlyModeOperGroup."
    REVISION        "200411190000Z"
    DESCRIPTION
        "Added cc6kxbarIntBusCRCErrGroup."
    REVISION        "200408110000Z"
    DESCRIPTION
        "Added cc6kxbarIntBusNotifControlGroup and
        cc6kxbarIntBusCRCErrNotifGroup."
    REVISION        "200406110000Z"
    DESCRIPTION
        "Added cc6kxbarTruncatedModeOper Object and changed the
        description of cc6kxbarTruncatedModeAllowed and 
        cc6kxbarMinFabricPresent."
    REVISION        "200309290000Z"
    DESCRIPTION
        "Changed the description of cc6kxbarTruncatedModeAllowed and
        cc6kxbarMinFabricPresent. Removed default value for 
        cc6kxbarTruncatedModeAllowed."
    REVISION        "200304020000Z"
    DESCRIPTION
        "Changed the SYNTAX and description of cc6kxbarMinFabricPresent
        and  description of cc6kxbarTruncatedModeAllowed."
    REVISION        "200212050000Z"
    DESCRIPTION
        "Added cc6kxbarModuleChannelSpeed, cc6kxbarSwBusSwitchingStatus,
        cc6kxbarSwBusFailureDuration, cc6kxbarSwBusNotifEnable 
        objects and cc6kxbarSwBusStallNotif notification."
    REVISION        "200106250000Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 217 }


ciscoCat6kXbarMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoCat6kCrossbarMIB 1 }

cc6kxbarConfiguration  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 1 }

cc6kxbarStatus  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 2 }

cc6kxbarStatistics  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 3 }

cc6kxbarSwBusObjects  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 4 }

cc6kxbarIntBusObjects  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 5 }

cc6kxbarFlowCtrlObjects  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 6 }

cc6kxbarSystemCapacityObjects  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 7 }

cc6kxbarErrorRecoveryObjects  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 8 }

cc6kxbarTrafficMonitorObjects  OBJECT IDENTIFIER
    ::= { ciscoCat6kXbarMIBObjects 9 }

cc6kxbarTrafficMonitorSwBusObjects  OBJECT IDENTIFIER
    ::= { cc6kxbarTrafficMonitorObjects 1 }

cc6kxbarTrafficMonitorChObjects  OBJECT IDENTIFIER
    ::= { cc6kxbarTrafficMonitorObjects 2 }


-- Textual Conventions

ModuleSlotNumber ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS          current
    DESCRIPTION
        "Slot number of the module."
    SYNTAX          Unsigned32

FabricChannelNumber ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS          current
    DESCRIPTION
        "Fabric channel number."
    SYNTAX          Unsigned32

ChannelStatus ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Status of the Channel."
    SYNTAX          INTEGER  {
                        ok(1),
                        syncError(2),
                        heartbeatError(3),
                        crcError(4),
                        bufferError(5),
                        timeoutError(6),
                        unused(7)
                    }

-- Configuration objects

cc6kxbarFallbackMode OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "cc6kxbarFallbackMode controls system fallback to bus-mode
        after loss of switching fabric module.
        'true'  - When the last fabric card is removed
                  system will adjust the supervisor and
                  line cards switching mode automatically without
                  resetting the system. With this feature enabled,
                  the switch might lose traffic while it's
                  changing the switching mode.
        'false'  - All the ports in the system will be
                  disabled when the last fabric card is removed."
    DEFVAL          { true } 
    ::= { cc6kxbarConfiguration 1 }

cc6kxbarBusOnlyModeAllowed OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether bus mode is administratively enabled on
        the device or not." 
    ::= { cc6kxbarConfiguration 2 }

cc6kxbarTruncatedModeAllowed OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether truncated mode is
        administratively enabled on the device or not." 
    ::= { cc6kxbarConfiguration 3 }

cc6kxbarMinFabricPresent OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates the minimum number
        of switching fabric capable modules to be present in the 
        system, for any switching fabric capable module to be in
        'crossbarmode' indicated by  cc6kxbarModuleModeSwitchingMode,
        when cc6kxbarTruncatedModeOper is 'false'." 
    ::= { cc6kxbarConfiguration 4 }

cc6kxbarLcdBannerTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarLcdBannerEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cc6kxbarLcdBannerTable configures the Lcd banner of the
        fabric module."
    ::= { cc6kxbarConfiguration 5 }

cc6kxbarLcdBannerEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarLcdBannerEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in cc6kxbarLcdBannerTable.
        Entries are created by the agent based on the
        size of the lcd banner."
    INDEX           { cc6kxbarLcdBannerIndex } 
    ::= { cc6kxbarLcdBannerTable 1 }

Cc6kxbarLcdBannerEntry ::= SEQUENCE {
        cc6kxbarLcdBannerIndex   Unsigned32,
        cc6kxbarLcdBannerBanner  DisplayString,
        cc6kxbarLcdBannerMaxSize Unsigned32
}

cc6kxbarLcdBannerIndex OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Object which specifies a unique entry in the
        table." 
    ::= { cc6kxbarLcdBannerEntry 1 }

cc6kxbarLcdBannerBanner OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Message that would be displayed on the fabric card LCD.
        The size of cc6kxbarLcdBannerBanner should be less than 
        or equal to the cc6kxbarLcdBannerMaxSize value." 
    ::= { cc6kxbarLcdBannerEntry 2 }

cc6kxbarLcdBannerMaxSize OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Maximum size of the associated cc6kxbarLcdBannerBanner
        object." 
    ::= { cc6kxbarLcdBannerEntry 3 }
 


cc6kxbarTruncatedModeOper OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates if the truncated mode is
        operationally enabled on the device or not.
        'true' - With the fabric module present in the system,
                 cc6kxbarModuleModeSwitchingMode will be
                 'crossbarmode' for all the fabric capable
                  modules.
        'false'- With the cc6kxbarMinFabricPresent fabric capable 
                 modules and the fabric module  present in the system,
                 cc6kxbarModuleModeSwitchingMode
                 will be 'crossbarmode' for fabric capable modules." 
    ::= { cc6kxbarConfiguration 6 }

cc6kxbarDcefOnlyModeAllowed OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether the dcef mode is enabled on the standby
        supervisor or not.
        'true' - Setting cc6kxbarDcefOnlyModeAllowed to 'true'
                 makes the  cc6kxbarModuleModeSwitchingMode of
                 all the active modules to 'dcefmode'.
                 With the active fabric module present in the system,
                 with the switch being in stateful switchover mode,
                 with all the cards that has no distributed Cisco
                 Express forwarding  switching support being
                 powered down and on some platforms with standby
                 supervisor ports being shutdown, changing
                 cc6kxbarDcefOnlyModeAllowed to 'true' will make the
                 standby supervisor module reloaded and booted up
                 as distributed forwarding module.
        'false'- Setting cc6kxbarDcefOnlyModeAllowed to 'false'
                 makes the cc6kxbarModuleModeSwitchingMode of the
                 standby supervisor module to the default value.
                 Changing cc6kxbarDcefOnlyModeAllowed  to 'false'
                 will make the standby supervisor reloaded and booted 
                 up with the default cc6kxbarModuleModeSwitchingMode." 
    ::= { cc6kxbarConfiguration 7 }

cc6kxbarForceBusMode OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether some affected modules are forced to be in
        'busmode' or not.
        'true' - Setting cc6kxbarForceBusMode to 'true' makes the
                 cc6kxbarModuleModeSwitchingMode of all
                 the affected modules to 'busmode'. Changing the
                 cc6kxbarForceBusMode to 'true' will make all the
                 affected modules reloaded and booted up as
                 non fabric capable modules.
        'false'- Setting cc6kxbarForceBusMode to 'false' makes the
                 cc6kxbarModuleModeSwitchingMode of all the affected
                 modules to the default value. Changing
                 cc6kxbarForceBusMode to 'false' will make all the
                 affected modules reloaded and booted up with the
                 default cc6kxbarModuleModeSwitchingMode." 
    ::= { cc6kxbarConfiguration 8 }

cc6kxbarBusOnlyModeOper OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates if the bus mode is operationally enabled on the
        device or not.

        'true' - Modules are allowed to run in 'busmode'.
        'false' - None of the modules are allowed to run in 'busmode'
                 i.e., All the non-fabric capable modules will
                 be powered off.  Absence of fabric module results
                 in all the fabric capable modules being powered
                 off." 
    ::= { cc6kxbarConfiguration 9 }

-- Status objects

cc6kxbarFabricActiveSlot OBJECT-TYPE
    SYNTAX          ModuleSlotNumber
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "cc6kxbarFabricActiveSlot indicates the slot number of the
        active switching fabric module. A value of zero indicates 
        that the active switching fabric module is either powered 
        down or not present in the chassis." 
    ::= { cc6kxbarStatus 1 }

cc6kxbarFabricBackupSlot OBJECT-TYPE
    SYNTAX          ModuleSlotNumber
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "cc6kxbarFabricBackupSlot indicates the slot number of the
        backup switching fabric module. A value of zero indicates 
        that the backup switching fabric module is either powered
        down or not present in the chassis." 
    ::= { cc6kxbarStatus 2 }

cc6kxbarModuleModeTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarModuleModeEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cc6kxbarModuleModeTable indicates the switching mode of each
        module in the system."
    ::= { cc6kxbarStatus 3 }

cc6kxbarModuleModeEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarModuleModeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in cc6kxbarModuleModeTable. The agent creates
        an entry when a non switching fabric module 
        is inserted in the chassis.
        The agent destroys an entry when a non switching fabric
        module is power down or reset or removed from the chassis."
    INDEX           { cc6kxbarModuleModeModule } 
    ::= { cc6kxbarModuleModeTable 1 }

Cc6kxbarModuleModeEntry ::= SEQUENCE {
        cc6kxbarModuleModeModule        ModuleSlotNumber,
        cc6kxbarModuleModeSwitchingMode INTEGER
}

cc6kxbarModuleModeModule OBJECT-TYPE
    SYNTAX          ModuleSlotNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The module slot number." 
    ::= { cc6kxbarModuleModeEntry 1 }

cc6kxbarModuleModeSwitchingMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        busmode(1),
                        crossbarmode(2),
                        dcefmode(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "cc6kxbarModuleModeSwitchingMode indicates switching mode
        of the cc6kxbarModuleModeModule.
        'busmode'     -  Modules  in this mode don't use fabric.
                         Backplane is used for both lookup and data
                         forwarding.
        'crossbarmode' -  Modules in this mode use backplane 
                          for forwarding decision and fabric for data 
                          forwarding
        'dcefmode'    -  Modules in this mode use fabric for data 
                         forwarding and local forwarding is enabled." 
    ::= { cc6kxbarModuleModeEntry 2 }
 


cc6kxbarModuleChannelTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarModuleChannelEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cc6kxbarModuleChannelTable shows the operational status
        of fabric channels. This table will be instantiated only when 
        cc6kxbarFabricActiveSlot has a valid value other than zero."
    ::= { cc6kxbarStatus 4 }

cc6kxbarModuleChannelEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarModuleChannelEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in cc6kxbarModuleChannelTable."
    INDEX           {
                        cc6kxbarModuleChannelModule,
                        cc6kxbarModuleChannelChannel
                    } 
    ::= { cc6kxbarModuleChannelTable 1 }

Cc6kxbarModuleChannelEntry ::= SEQUENCE {
        cc6kxbarModuleChannelModule    ModuleSlotNumber,
        cc6kxbarModuleChannelChannel   FabricChannelNumber,
        cc6kxbarModuleChannelModStatus ChannelStatus,
        cc6kxbarModuleChannelFabStatus ChannelStatus,
        cc6kxbarModuleChannelSpeed     Gauge32
}

cc6kxbarModuleChannelModule OBJECT-TYPE
    SYNTAX          ModuleSlotNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The module slot number." 
    ::= { cc6kxbarModuleChannelEntry 1 }

cc6kxbarModuleChannelChannel OBJECT-TYPE
    SYNTAX          FabricChannelNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Fabric channel number of the  cc6kxbarModuleChannelModule." 
    ::= { cc6kxbarModuleChannelEntry 2 }

cc6kxbarModuleChannelModStatus OBJECT-TYPE
    SYNTAX          ChannelStatus
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The status of the fabric channel at the
        cc6kxbarModuleChannelModule." 
    ::= { cc6kxbarModuleChannelEntry 3 }

cc6kxbarModuleChannelFabStatus OBJECT-TYPE
    SYNTAX          ChannelStatus
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The status of the fabric channel at the
        cc6kxbarFabricActiveSlot." 
    ::= { cc6kxbarModuleChannelEntry 4 }

cc6kxbarModuleChannelSpeed OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The speed in mega bits per second of the
        cc6kxbarModuleChannelChannel for cc6kxbarModuleChannelModule." 
    ::= { cc6kxbarModuleChannelEntry 5 }
 

-- Statistics Objects

cc6kxbarStatisticsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarStatisticsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cc6kxbarStatisticsTable gives the
        fabric channel counters and utilization
        information.  This table will be instantiated when 
        cc6kxbarFabricActiveSlot has a valid value other than
        zero."
    ::= { cc6kxbarStatistics 1 }

cc6kxbarStatisticsEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarStatisticsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in cc6kxbarStatisticsTable."
    INDEX           {
                        cc6kxbarStatisticsModule,
                        cc6kxbarStatisticsChannel
                    } 
    ::= { cc6kxbarStatisticsTable 1 }

Cc6kxbarStatisticsEntry ::= SEQUENCE {
        cc6kxbarStatisticsModule        ModuleSlotNumber,
        cc6kxbarStatisticsChannel       FabricChannelNumber,
        cc6kxbarStatisticsInErrors      Counter32,
        cc6kxbarStatisticsOutErrors     Counter32,
        cc6kxbarStatisticsOutDropped    Counter32,
        cc6kxbarStatisticsInUtil        Percent,
        cc6kxbarStatisticsOutUtil       Percent,
        cc6kxbarStatisticsPeakInUtil    Percent,
        cc6kxbarStatisticsPeakTmInUtil  DateAndTime,
        cc6kxbarStatisticsPeakOutUtil   Percent,
        cc6kxbarStatisticsPeakTmOutUtil DateAndTime,
        cc6kxbarStatisticsLbusDrops     Counter32
}

cc6kxbarStatisticsModule OBJECT-TYPE
    SYNTAX          ModuleSlotNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The module slot number." 
    ::= { cc6kxbarStatisticsEntry 1 }

cc6kxbarStatisticsChannel OBJECT-TYPE
    SYNTAX          FabricChannelNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Fabric channel number associated with the
        cc6kxbarStatisticsModule." 
    ::= { cc6kxbarStatisticsEntry 2 }

cc6kxbarStatisticsInErrors OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of error packets received on
        cc6kxbarStatisticsChannel for cc6kxbarStatisticsModule
        since this entry was last initialized." 
    ::= { cc6kxbarStatisticsEntry 3 }

cc6kxbarStatisticsOutErrors OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of error packets transmitted on
        cc6kxbarStatisticsModule through the 
        cc6kxbarStatisticsChannel since this entry was 
        last initialized." 
    ::= { cc6kxbarStatisticsEntry 4 }

cc6kxbarStatisticsOutDropped OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of  dropped packets transmitted on the
        cc6kxbarStatisticsChannel for cc6kxbarStatisticsModule
        since this entry was last initialized." 
    ::= { cc6kxbarStatisticsEntry 5 }

cc6kxbarStatisticsInUtil OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Input utilization of the cc6kxbarStatisticsChannel for
        cc6kxbarStatisticsModule." 
    ::= { cc6kxbarStatisticsEntry 6 }

cc6kxbarStatisticsOutUtil OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Output utilization of the cc6kxbarStatisticsChannel for
        cc6kxbarStatisticsModule." 
    ::= { cc6kxbarStatisticsEntry 7 }

cc6kxbarStatisticsPeakInUtil OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the peak value of cc6kxbarStatisticsInUtil of the
        cc6kxbarStatisticsChannel for cc6kxbarStatisticsModule." 
    ::= { cc6kxbarStatisticsEntry 8 }

cc6kxbarStatisticsPeakTmInUtil OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the time of most recent change in the corresponding
        instance value of cc6kxbarStatisticsPeakInUtil.

        This object contains value 0x0000010100000000 when the
        corresponding instance value of cc6kxbarStatisticsPeakInUtil 
        is '0'." 
    ::= { cc6kxbarStatisticsEntry 9 }

cc6kxbarStatisticsPeakOutUtil OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the peak value of cc6kxbarStatisticsOutUtil of the
        cc6kxbarStatisticsChannel for cc6kxbarStatisticsModule." 
    ::= { cc6kxbarStatisticsEntry 10 }

cc6kxbarStatisticsPeakTmOutUtil OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the time of most recent change in the corresponding
        instance value of cc6kxbarStatisticsPeakOutUtil.

        This object contains value 0x0000010100000000 when the
        corresponding instance value of cc6kxbarStatisticsPeakOutUtil 
        is '0'." 
    ::= { cc6kxbarStatisticsEntry 11 }

cc6kxbarStatisticsLbusDrops OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the number of packets that have been
        deterministically dropped due to the threshold on the local
        bus FIFO, errors and exception conditions." 
    ::= { cc6kxbarStatisticsEntry 12 }
 


cc6kxbarErrorTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarErrorEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cc6kxbarErrorTable provides the fabric channel error
        information. This table will be instantiated only when 
        cc6kxbarFabricActiveSlot has a valid value other than zero."
    ::= { cc6kxbarStatistics 2 }

cc6kxbarErrorEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarErrorEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in cc6kxbarErrorTable for a specific fabric channel
        on a specific module."
    INDEX           {
                        cc6kxbarErrorModule,
                        cc6kxbarErrorChannel
                    } 
    ::= { cc6kxbarErrorTable 1 }

Cc6kxbarErrorEntry ::= SEQUENCE {
        cc6kxbarErrorModule         ModuleSlotNumber,
        cc6kxbarErrorChannel        FabricChannelNumber,
        cc6kxbarErrorModuleCrc      Gauge32,
        cc6kxbarErrorModuleHbeat    Gauge32,
        cc6kxbarErrorModuleSync     Gauge32,
        cc6kxbarErrorModuleDDRSync  Gauge32,
        cc6kxbarErrorChannelSync    Gauge32,
        cc6kxbarErrorChannelBuffer  Gauge32,
        cc6kxbarErrorChannelTimeout Gauge32
}

cc6kxbarErrorModule OBJECT-TYPE
    SYNTAX          ModuleSlotNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The module slot number." 
    ::= { cc6kxbarErrorEntry 1 }

cc6kxbarErrorChannel OBJECT-TYPE
    SYNTAX          FabricChannelNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Fabric channel number associated with the
        cc6kxbarErrorModule." 
    ::= { cc6kxbarErrorEntry 2 }

cc6kxbarErrorModuleCrc OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of CRC error packets for the
        cc6kxbarErrorChannel of the cc6kxbarErrorModule." 
    ::= { cc6kxbarErrorEntry 3 }

cc6kxbarErrorModuleHbeat OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of heartbeat misses detected
        for the cc6kxbarErrorChannel of the cc6kxbarErrorModule." 
    ::= { cc6kxbarErrorEntry 4 }

cc6kxbarErrorModuleSync OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of times cc6kxbarErrorModule could
        not sync to the active switching fabric module." 
    ::= { cc6kxbarErrorEntry 5 }

cc6kxbarErrorModuleDDRSync OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of DDR(Double Data Rate) channel
        sync errors for the cc6kxbarErrorChannel of the 
        cc6kxbarErrorModule." 
    ::= { cc6kxbarErrorEntry 6 }

cc6kxbarErrorChannelSync OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of sync errors received at
        cc6kxbarErrorChannel." 
    ::= { cc6kxbarErrorEntry 7 }

cc6kxbarErrorChannelBuffer OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of times the memory buffer for
        cc6kxbarErrorChannel of the cc6kxbarErrorModule is full." 
    ::= { cc6kxbarErrorEntry 8 }

cc6kxbarErrorChannelTimeout OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the total number of timeouts for input from
        the cc6kxbarErrorChannel of the cc6kxbarErrorModule." 
    ::= { cc6kxbarErrorEntry 9 }
 


-- Switching Bus Objects

cc6kxbarSwBusSwitchingStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        normal(1),
                        stalled(2),
                        isolated(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the current switching status of the
        switching bus on the system. 

        normal -- the switching bus is under normal 
                  packet switching operation.

        stalled -- the switching bus has stopped packet 
                   switching but is still attached to
                   the system, i.e., the switching bus
                   is stalled. 

        isolated -- the switching bus has stopped packet 
                   switching and is detached from 
                   the system, i.e., the switching bus 
                   is isolated.

        The object is only instantiated on systems which 
        support switching bus switching detection feature." 
    ::= { cc6kxbarSwBusObjects 1 }

cc6kxbarSwBusFailureDuration OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the failure duration of the switching
        bus since the switching bus has transitioned 
        out of the normal(1) state as monitored by  
        cc6kxbarSwBusSwitchingStatus. If the object 
        value of cc6kxbarSwBusSwitchingStatus is 
        normal(1), then the object value of 
        cc6kxbarSwBusFailureDuration should be zero.

        The object is only instantiated on systems which
        support switching bus switching detection feature." 
    ::= { cc6kxbarSwBusObjects 2 }

cc6kxbarSwBusNotifEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether or not the notification
        cc6kxbarSwBusStatusChangeNotif should be generated 
        when there is change in the switching status of the 
        switching bus as monitored by 
        cc6kxbarSwBusSwitchingStatus.

        The object is only instantiated on systems which
        support switching bus switching detection feature." 
    ::= { cc6kxbarSwBusObjects 3 }

cc6kxbarSwBusUtilization OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the percentage of bandwidth utilization
        of the switching bus." 
    ::= { cc6kxbarSwBusObjects 4 }

cc6kxbarSwBusPeakUtilization OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the peak percentage value of bandwidth
        utilization of the switching bus since the system 
        started." 
    ::= { cc6kxbarSwBusObjects 5 }

cc6kxbarSwBusPeakTimeUtil OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the time at which the peak percentage value
        of bandwidth utilization of the switching bus value 
        occurred." 
    ::= { cc6kxbarSwBusObjects 6 }

-- Internal Bus Objects

cc6kxbarIntBusNotifEnable OBJECT-TYPE
    SYNTAX          BITS {
                        intBusCRCErrExcd(0),
                        intBusCRCErrRcvrd(1)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Indicates whether the generation of a specified
        notification for internal bus is enabled or not. 
        If a bit corresponding to a notification is set to 
        1, then that notification can be generated.

        intBusCRCErrExcd -- the cc6kxbarIntBusCRCErrExcdNotif 
                            notification.

        intBusCRCErrRcvrd -- the cc6kxbarIntBusCRCErrRcvrdNotif
                            notification." 
    ::= { cc6kxbarIntBusObjects 1 }

cc6kxbarIntBusCRCErrTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarIntBusCRCErrEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A table containing internal bus CRC error information
        on a list of physical entities (such as modules)."
    ::= { cc6kxbarIntBusObjects 2 }

cc6kxbarIntBusCRCErrEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarIntBusCRCErrEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry containing internal bus CRC error information
        on a physical entity (such as a module)."
    INDEX           { entPhysicalIndex } 
    ::= { cc6kxbarIntBusCRCErrTable 1 }

Cc6kxbarIntBusCRCErrEntry ::= SEQUENCE {
        cc6kxbarIntBusCRCErrExcdStatus TruthValue,
        cc6kxbarIntBusCRCErrExcdCount  Unsigned32
}

cc6kxbarIntBusCRCErrExcdStatus OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates whether the internal bus CRC error
        on a physical entity is currently exceeding the fatal 
        threshold or not." 
    ::= { cc6kxbarIntBusCRCErrEntry 1 }

cc6kxbarIntBusCRCErrExcdCount OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of times the internal bus CRC
        error on a physical entity has exceeded the fatal 
        threshold." 
    ::= { cc6kxbarIntBusCRCErrEntry 2 }
 


-- Flow Control Objects

cc6kxbarFlowCtrlNotifEnable OBJECT-TYPE
    SYNTAX          BITS {
                        busThreshExcd(0)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether the generation of a specified
        notification for flow control feature is enabled or
        not. If a bit corresponding to a notification is set
        to one, then that notification can be generated.

        busThreshExcd -- the cc6kxbarFlowCtrlBusThrExcdNotif
                            notification." 
    ::= { cc6kxbarFlowCtrlObjects 1 }

cc6kxbarFlowCtrlBusFIFOThrMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        off(1),
                        on(2),
                        auto(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the mode configuration of the system bus
        flow control FIFO threshold.

        off -- the flow control FIFO threshold is set to the
              original value defined by the system.

        on -- the flow control FIFO threshold is set to the
              lower value defined by the system.

        auto -- the system starts monitoring the system bus
              flow control FIFO threshold and will change the
              flow control FIFO threshold from the original
              value to the lower value when the system detects
              the first occurrence of the threshold exceeding
              on a physical entity (such as a module) over
              the system allowed limit." 
    ::= { cc6kxbarFlowCtrlObjects 2 }

cc6kxbarFlowCtrlBusFIFOThrValue OBJECT-TYPE
    SYNTAX          INTEGER  {
                        original(1),
                        lower(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the current value of the system bus flow
        control FIFO threshold, whether it is the original
        value or the lower value." 
    ::= { cc6kxbarFlowCtrlObjects 3 }

cc6kxbarFlowCtrlBusFIFOTransTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the value of sysUpTime at the time
        that the system bus flow control FIFO threshold
        is transitioned to lower value when the object value 
        of  cc6kxbarFlowCtrlBusFIFOThrValue is 'lower'. This
        object will take on the value of zero when the object
        value of cc6kxbarFlowCtrlBusFIFOThrValue is 
        'original'." 
    ::= { cc6kxbarFlowCtrlObjects 4 }

-- System Capacity Objects

cc6kxbarSysCapPfcOperMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        pfc3a(2),
                        pfc3b(3),
                        pfc3bxl(4),
                        pfc3cst1(5),
                        pfc3cxlst1(6),
                        pfc3c(7),
                        pfc3cxl(8),
                        pfc4(9),
                        pfc4xl(10),
                        pfc4xxl(11),
                        pfc4lite(12)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the operating mode of the policy feature
        card (PFC).

        unknown -- unknown.

        pfc3a -- Policy feature card 3A.

        pfc3b -- Policy feature card 3B.

        pfc3bxl -- Policy feature card 3B XL.

        pfc3cst1 -- Policy feature card 3C ST1.

        pfc3cxlst1 -- Policy feature card 3C XL ST1.

        pfc3c -- Policy feature card 3C.

        pfc3cxl -- Policy feature card 3C XL.  

        pfc4 -- Policy feature card 4.

        pfc4xl -- Policy feature card 4 XL. 

        pfc4xxl -- Policy feature card 4 XXL.  

        pfc4lite -- Policy feature card 4 Lite." 
    ::= { cc6kxbarSystemCapacityObjects 1 }

cc6kxbarSysCapSwitchResTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarSysCapSwitchResEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A cc6kxbarSysCapSwitchResTable entry lists
        the capacity information of the switching 
        resources for ENTITY-MIB entPhysicalTable entries 
        of manageable components with type entPhysicalClass 
        module(9)."
    ::= { cc6kxbarSystemCapacityObjects 2 }

cc6kxbarSysCapSwitchResEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarSysCapSwitchResEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "A cc6kxbarSysCapSwitchResEntry entry lists
        the capacity information of the switching 
        resources for ENTITY-MIB entPhysicalTable entries
        of manageable components with type entPhysicalClass 
        module(9).

        Entries are created by the agent at the system power-up
        or module insertion.

        Entries are deleted by the agent upon module removal."
    INDEX           { entPhysicalIndex } 
    ::= { cc6kxbarSysCapSwitchResTable 1 }

Cc6kxbarSysCapSwitchResEntry ::= SEQUENCE {
        cc6kxbarSysCapSwitchResSeries  INTEGER,
        cc6kxbarSysCapSwitchResCefMode INTEGER
}

cc6kxbarSysCapSwitchResSeries OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        supervisor(2),
                        classic(3),
                        fabric(4),
                        dcef720(5),
                        cef720(6),
                        dcef256(7),
                        cef256(8)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the module series for traffic forwarding options.

        unknown -- unknown.

        supervisor -- supervisor module.

        classic -- classic module.

        fabric -- fabric module.

        dcef720 -- dCEF720 module.

        cef720 -- CEF720 module.

        dcef256 -- dCEF256 module.

        cef256 -- CEF256 module." 
    ::= { cc6kxbarSysCapSwitchResEntry 1 }

cc6kxbarSysCapSwitchResCefMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        notApplicable(1),
                        dcef(2),
                        cef(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the Cisco Express Forwarding (CEF) mode of
        the module.  

        notApplicable -- not applicable for this module.

        dcef -- distributed CEF.

        cef -- CEF." 
    ::= { cc6kxbarSysCapSwitchResEntry 2 }
 


-- Fabric Error Recovery Objects

cc6kxbarErrRcvryThreshLink OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the threshold on fabric link errors occurring
        in a channel for the fabric error recovery feature." 
    ::= { cc6kxbarErrorRecoveryObjects 1 }

cc6kxbarErrRcvryThreshPersLink OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the threshold on persistent fabric link errors
        occurring in a channel for the fabric error recovery feature." 
    ::= { cc6kxbarErrorRecoveryObjects 2 }

cc6kxbarErrRcvrySwitchoverEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether the switchover to the standby fabric module
        is enabled or not on the detection of excessive fabric link 
        errors occurring in a channel for the fabric error recovery 
        feature." 
    ::= { cc6kxbarErrorRecoveryObjects 3 }

cc6kxbarErrRcvryPersLinkResync OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether the system will resync the channel or not
        on the detection of excessive persistent fabric link errors
        in a channel for the fabric error recovery feature." 
    ::= { cc6kxbarErrorRecoveryObjects 4 }

cc6kxbarErrRcvryChResyncCount OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the consecutive number of times of
        channel resync within the time interval as specified
        by cc6kxbarErrRcvryChResyncInterval, over which  
        the channel resync is considered as failure 
        for the fabric error recovery feature." 
    ::= { cc6kxbarErrorRecoveryObjects 5 }

cc6kxbarErrRcvryChResyncInterval OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    UNITS           "milliseconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the time interval for the consecutive
        counting of channel resync for the fabric error
        recovery feature." 
    ::= { cc6kxbarErrorRecoveryObjects 6 }

-- Traffic Monitor Objects

cc6kxbarTMSwBusUtilEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether the traffic monitor feature is
        enabled on the switching bus or not." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 1 }

cc6kxbarTMSwBusUtilInterval OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the interval at the end of which
        the switching bus traffic utilization is compared 
        against cc6kxbarTMSwBusUtilThreshold." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 2 }

cc6kxbarTMSwBusUtilThreshold OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the threshold of the switching bus
        traffic utilization." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 3 }

cc6kxbarTMSwBusUtilLogCount OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of times the following
        events are logged for the switching bus:

        1. the switching bus traffic utilization has 
        risen above the configured threshold (as 
        specified by cc6kxbarTMSwBusUtilThreshold),
        or has kept staying above the configured 
        threshold at a logging interval (as specified
        by cc6kxbarTMSwBusUtilLogInterval).

        2. the switching bus traffic utilization
        has fallen below the configured threshold." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 4 }

cc6kxbarTMSwBusUtilLastLogTime OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the last logging time of the
        events as counted in cc6kxbarTMSwBusUtilLogCount.  

        This object will contain 0-1-1,00:00:00.0 when
        the value of cc6kxbarTMSwBusUtilLogCount is zero." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 5 }

cc6kxbarTMSwBusUtilLogInterval OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the logging interval if the switching
        bus traffic utilization keeps staying above the
        cc6kxbarTMSwBusUtilThreshold." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 6 }

cc6kxbarTMSwBusUtilUtilization OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      accessible-for-notify
    STATUS          current
    DESCRIPTION
        "The switching bus traffic utilization included
        in the notification currently being sent." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 7 }

cc6kxbarTMSwBusUtilNotifEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether the generation of
        c6kxbarTMSwBusUtilAboveNotif and 
        c6kxbarTMSwBusUtilBelowNotif notifications
        is enabled or not.

        When this object is 'true', the generation of
        cc6kxbarTMSwBusUtilAboveNotif and 
        c6kxbarTMSwBusUtilBelowNotif notifications is
        enabled. When this object is 'false', the 
        generation of cc6kxbarTMSwBusUtilAboveNotif and 
        c6kxbarTMSwBusUtilBelowNotif notifications 
        is disabled." 
    ::= { cc6kxbarTrafficMonitorSwBusObjects 8 }

cc6kxbarTMChUtilTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF Cc6kxbarTMChUtilEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "cc6kxbarTrafficMntrChUtilTable provides the
        information on the traffic utilization of each 
        fabric channel associated with each module slot
        for a given traffic direction in the chassis."
    ::= { cc6kxbarTrafficMonitorChObjects 1 }

cc6kxbarTMChUtilEntry OBJECT-TYPE
    SYNTAX          Cc6kxbarTMChUtilEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry in cc6kxbarTMChUtilTable. Each traffic
        direction for each fabric channel associated with
        each module slot in the chassis will have an 
        entry in this table."
    INDEX           {
                        cc6kxbarTMChUtilModule,
                        cc6kxbarTMChUtilChannel,
                        cc6kxbarTMChUtilDirection
                    } 
    ::= { cc6kxbarTMChUtilTable 1 }

Cc6kxbarTMChUtilEntry ::= SEQUENCE {
        cc6kxbarTMChUtilModule      ModuleSlotNumber,
        cc6kxbarTMChUtilChannel     FabricChannelNumber,
        cc6kxbarTMChUtilDirection   INTEGER,
        cc6kxbarTMChUtilEnable      TruthValue,
        cc6kxbarTMChUtilInterval    Unsigned32,
        cc6kxbarTMChUtilThreshold   Percent,
        cc6kxbarTMChUtilLogCount    Counter32,
        cc6kxbarTMChUtilLastLogTime DateAndTime
}

cc6kxbarTMChUtilModule OBJECT-TYPE
    SYNTAX          ModuleSlotNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the module slot number in the chassis." 
    ::= { cc6kxbarTMChUtilEntry 1 }

cc6kxbarTMChUtilChannel OBJECT-TYPE
    SYNTAX          FabricChannelNumber
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the fabric channel number associated with
        the corresponding cc6kxbarTMChUtilModule." 
    ::= { cc6kxbarTMChUtilEntry 2 }

cc6kxbarTMChUtilDirection OBJECT-TYPE
    SYNTAX          INTEGER  {
                        ingress(1),
                        egress(2)
                    }
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Indicates the direction of traffic to be monitored." 
    ::= { cc6kxbarTMChUtilEntry 3 }

cc6kxbarTMChUtilEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether the traffic monitor feature is
        enabled on a fabric channel for a given traffic
        direction or not." 
    ::= { cc6kxbarTMChUtilEntry 4 }

cc6kxbarTMChUtilInterval OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the interval at the end of which the
        traffic utilization on a fabric channel for a 
        given traffic direction is compared against 
        its corresponding cc6kxbarTMChUtilThreshold." 
    ::= { cc6kxbarTMChUtilEntry 5 }

cc6kxbarTMChUtilThreshold OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the threshold of the traffic utilization
        on a fabric channel for a given traffic direction." 
    ::= { cc6kxbarTMChUtilEntry 6 }

cc6kxbarTMChUtilLogCount OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of times the following
        events are logged on a fabric channel
        for a given direction.

        1. the traffic utilization on a fabric channel 
        for a given traffic direction has risen above its 
        corresponding configured threshold (as specified 
        by cc6kxbarTMChUtilThreshold), or has kept 
        staying above the configured threshold at a 
        logging interval (as specified by 
        cc6kxbarTMChUtilLogInterval).

        2. the traffic utilization on a fabric channel
        for a given traffic direction has fallen below 
        its corresponding configured threshold." 
    ::= { cc6kxbarTMChUtilEntry 7 }

cc6kxbarTMChUtilLastLogTime OBJECT-TYPE
    SYNTAX          DateAndTime
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the last logging time of the
        events as counted in the corresponding
        cc6kxbarTMChUtilLogCount on a fabric channel 
        for a given traffic direction.

        This object will contain 0-1-1,00:00:00.0 when
        the corresponding cc6kxbarTMChUtilLogCount is zero." 
    ::= { cc6kxbarTMChUtilEntry 8 }
 


cc6kxbarTMChUtilLogInterval OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    UNITS           "seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies the logging interval if the traffic
        utilization for a fabric channel on a given
        traffic direction keeps staying above its 
        corresponding cc6kxbarTMChUtilThreshold." 
    ::= { cc6kxbarTrafficMonitorChObjects 2 }

cc6kxbarTMChUtilUtilization OBJECT-TYPE
    SYNTAX          Percent
    MAX-ACCESS      accessible-for-notify
    STATUS          current
    DESCRIPTION
        "The traffic utilization on a fabric channel
        for a given traffic direction included in the 
        notification currently being sent." 
    ::= { cc6kxbarTrafficMonitorChObjects 3 }

cc6kxbarTMChUtilNotifEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Specifies whether the generation of
        cc6kxbarTMChUtilAboveNotif and cc6kxbarTMChUtilBelowNotif
        notifications is enabled or not.

        When this object is 'true', the generation of
        cc6kxbarTMChUtilAboveNotif and cc6kxbarTMChUtilBelowNotif
        notifications is enabled. When this object is 'false', 
        the generation of cc6kxbarTMChUtilAboveNotif and 
        cc6kxbarTMChUtilBelowNotif notifications is disabled." 
    ::= { cc6kxbarTrafficMonitorChObjects 4 }
-- Notifications

ciscoCat6kXbarMIBNotifs  OBJECT IDENTIFIER
    ::= { ciscoCat6kCrossbarMIB 0 }


cc6kxbarSwBusStatusChangeNotif NOTIFICATION-TYPE
    OBJECTS         {
                        cc6kxbarSwBusSwitchingStatus,
                        cc6kxbarSwBusFailureDuration
                    }
    STATUS          current
    DESCRIPTION
        "A cc6kxbarSwBusStatusChangeNotif notification
        is sent by the device when there is change
        in switching status of the switching bus as 
        monitored by cc6kxbarSwBusSwitchingStatus."
   ::= { ciscoCat6kXbarMIBNotifs 1 }

cc6kxbarIntBusCRCErrExcdNotif NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalName,
                        entPhysicalVendorType
                    }
    STATUS          current
    DESCRIPTION
        "A cc6kxbarIntBusCRCThreshExcdNotif notification
        is sent by the system when the system has
        detected that the internal bus CRC error on
        a physical entity (such as a module) as specified
        by entPhysicalName has exceeded the fatal threshold."
   ::= { ciscoCat6kXbarMIBNotifs 2 }

cc6kxbarIntBusCRCErrRcvrdNotif NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalName,
                        entPhysicalVendorType
                    }
    STATUS          current
    DESCRIPTION
        "A cc6kxbarIntBusCRCErrRcvrdNotif notification
        is sent by the system when the system has
        recovered as the internal bus CRC error on
        a physical entity (such as a module) as
        specified by entPhysicalName has fallen below
        the normal threshold."
   ::= { ciscoCat6kXbarMIBNotifs 3 }

cc6kxbarFlowCtrlBusThrExcdNotif NOTIFICATION-TYPE
    OBJECTS         {
                        entPhysicalName,
                        entPhysicalVendorType
                    }
    STATUS          current
    DESCRIPTION
        "A cc6kxbarFlowCtrlBusThrExcdNotif notification
        is sent by the system when the system detects the
        first occurrence of the system bus flow control FIFO
        threshold exceeding on a physical entity
        (such as a module) over the system allowed limit
        when the object value of
        cc6kxbarFlowCtrlBusFIFOThrMode is 'auto'."
   ::= { ciscoCat6kXbarMIBNotifs 4 }

cc6kxbarTMSwBusUtilAboveNotif NOTIFICATION-TYPE
    OBJECTS         {
                        cc6kxbarTMSwBusUtilUtilization,
                        cc6kxbarTMSwBusUtilThreshold,
                        cc6kxbarTMSwBusUtilInterval,
                        entPhysicalDescr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is sent when the switching bus
        traffic utilization has risen above the configured 
        threshold (as specified by 
        cc6kxbarTMSwBusUtilThreshold), or has kept staying
        above the configured threshold at a logging interval 
        (as specified by cc6kxbarTMSwBusUtilLogInterval).
        The varbind entPhysicalDescr specifies the physical 
        entity (such as chassis) for which the 
        cc6kxbarTMSwBusUtilAboveNotif is sent."
   ::= { ciscoCat6kXbarMIBNotifs 5 }

cc6kxbarTMSwBusUtilBelowNotif NOTIFICATION-TYPE
    OBJECTS         {
                        cc6kxbarTMSwBusUtilUtilization,
                        cc6kxbarTMSwBusUtilThreshold,
                        cc6kxbarTMSwBusUtilInterval,
                        entPhysicalDescr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is sent when the switching bus
        traffic utilization has fallen below the configured 
        threshold (as specified by 
        cc6kxbarTMSwBusUtilThreshold). The varbind 
        entPhysicalDescr specifies the physical entity 
        (such as chassis) for which the 
        cc6kxbarTMSwBusUtilBelowNotif is sent."
   ::= { ciscoCat6kXbarMIBNotifs 6 }

cc6kxbarTMChUtilAboveNotif NOTIFICATION-TYPE
    OBJECTS         {
                        cc6kxbarTMChUtilUtilization,
                        cc6kxbarTMChUtilThreshold,
                        cc6kxbarTMChUtilInterval,
                        entPhysicalDescr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is sent when the traffic
        utilization on a fabric channel for a given 
        traffic direction has risen above its 
        corresponding configured threshold (as 
        specified by cc6kxbarTMChUtilThreshold), or has 
        kept staying above the configured threshold at a 
        logging interval (as specified by 
        cc6kxbarTMChUtilLogInterval). The varbind 
        entPhysicalDescr specifies the physical entity 
        (such as chassis) for which the 
        cc6kxbarTMChUtilAboveNotif is sent."
   ::= { ciscoCat6kXbarMIBNotifs 7 }

cc6kxbarTMChUtilBelowNotif NOTIFICATION-TYPE
    OBJECTS         {
                        cc6kxbarTMChUtilUtilization,
                        cc6kxbarTMChUtilThreshold,
                        cc6kxbarTMChUtilInterval,
                        entPhysicalDescr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is sent when the traffic
        utilization on a fabric channel for a given 
        traffic direction has fallen below its 
        corresponding configured threshold (as 
        specified by cc6kxbarTMChUtilThreshold). The 
        varbind entPhysicalDescr specifies the 
        physical entity (such as chassis) for which
        the cc6kxbarTMChUtilBelowNotif is sent."
   ::= { ciscoCat6kXbarMIBNotifs 8 }
-- Conformance and Compliance

cc6kxbarMIBConformance  OBJECT IDENTIFIER
    ::= { ciscoCat6kCrossbarMIB 3 }

cc6kxbarMIBCompliances  OBJECT IDENTIFIER
    ::= { cc6kxbarMIBConformance 1 }

cc6kxbarMIBGroups  OBJECT IDENTIFIER
    ::= { cc6kxbarMIBConformance 2 }


-- Conformance

cc6kxbarMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroup,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroup
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support busonly  mode feature."
    ::= { cc6kxbarMIBCompliances 2 }

cc6kxbarMIBComplianceVer1 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."
    ::= { cc6kxbarMIBCompliances 3 }

cc6kxbarMIBComplianceVer2 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."

    GROUP           cc6kxbarTruncatedOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational truncated mode."
    ::= { cc6kxbarMIBCompliances 4 }

cc6kxbarMIBComplianceVer3 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."

    GROUP           cc6kxbarTruncatedOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational truncated mode."

    GROUP           cc6kxbarIntBusNotifControlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus notifications."

    GROUP           cc6kxbarIntBusCRCErrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error notifications."
    ::= { cc6kxbarMIBCompliances 5 }

cc6kxbarMIBComplianceVer4 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."

    GROUP           cc6kxbarTruncatedOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational truncated mode."

    GROUP           cc6kxbarIntBusNotifControlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus notifications."

    GROUP           cc6kxbarIntBusCRCErrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error notifications."

    GROUP           cc6kxbarIntBusCRCErrGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error status."
    ::= { cc6kxbarMIBCompliances 6 }

cc6kxbarMIBComplianceVer5 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."

    GROUP           cc6kxbarTruncatedOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational truncated mode."

    GROUP           cc6kxbarIntBusNotifControlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus notifications."

    GROUP           cc6kxbarIntBusCRCErrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error notifications."

    GROUP           cc6kxbarIntBusCRCErrGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error status."

    GROUP           cc6kxbarDcefOnlyModeAllowedGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        dcef only mode feature."

    GROUP           cc6kxbarForceBusModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        force bus mode feature."

    GROUP           cc6kxbarFlowCtrlNotifCtrlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system flow control notification control."

    GROUP           cc6kxbarFlowCtrlBusThreshGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold configuration."

    GROUP           cc6kxbarFlowCtrlBusThrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold exceeding
        notification."

    GROUP           cc6kxbarBusOnlyModeOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational bus mode."
    ::= { cc6kxbarMIBCompliances 7 }

cc6kxbarMIBComplianceVer6 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."

    GROUP           cc6kxbarTruncatedOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational truncated mode."

    GROUP           cc6kxbarIntBusNotifControlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus notifications."

    GROUP           cc6kxbarIntBusCRCErrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error notifications."

    GROUP           cc6kxbarIntBusCRCErrGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error status."

    GROUP           cc6kxbarDcefOnlyModeAllowedGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        dcef only mode feature."

    GROUP           cc6kxbarForceBusModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        force bus mode feature."

    GROUP           cc6kxbarFlowCtrlNotifCtrlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system flow control notification control."

    GROUP           cc6kxbarFlowCtrlBusThreshGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold configuration."

    GROUP           cc6kxbarFlowCtrlBusThrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold exceeding
        notification."

    GROUP           cc6kxbarBusOnlyModeOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational bus mode."

    GROUP           cc6kxbarSysCapPfcOperModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support the system information of PFC operational mode."

    GROUP           cc6kxbarSysCapSwitchResGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support the capacity information of switching 
        resources in the system."

    GROUP           cc6kxbarChannelUtilGroup1
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support peak input and output traffic utilization on the
        fabric channel."

    GROUP           cc6kxbarSwBusGroup1
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus utilization."

    GROUP           cc6kxbarErrorGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support error statistics on the fabric channel."

    GROUP           cc6kxbarErrRcvryThresholdGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of thresholds on fabric link errors
        for the fabric error recovery feature."

    GROUP           cc6kxbarErrRcvrySwitchoverGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of fabric switchover on excessive fabric 
        link errors for the fabric error recovery feature."
    ::= { cc6kxbarMIBCompliances 8 }

cc6kxbarMIBComplianceVer7 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."

    GROUP           cc6kxbarTruncatedOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational truncated mode."

    GROUP           cc6kxbarIntBusNotifControlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus notifications."

    GROUP           cc6kxbarIntBusCRCErrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error notifications."

    GROUP           cc6kxbarIntBusCRCErrGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error status."

    GROUP           cc6kxbarDcefOnlyModeAllowedGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        dcef only mode feature."

    GROUP           cc6kxbarForceBusModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        force bus mode feature."

    GROUP           cc6kxbarFlowCtrlNotifCtrlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system flow control notification control."

    GROUP           cc6kxbarFlowCtrlBusThreshGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold configuration."

    GROUP           cc6kxbarFlowCtrlBusThrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold exceeding
        notification."

    GROUP           cc6kxbarBusOnlyModeOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational bus mode."

    GROUP           cc6kxbarSysCapPfcOperModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support the system information of PFC operational mode."

    GROUP           cc6kxbarSysCapSwitchResGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support the capacity information of switching 
        resources in the system."

    GROUP           cc6kxbarChannelUtilGroup1
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support peak input and output traffic utilization on the
        fabric channel."

    GROUP           cc6kxbarSwBusGroup1
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus utilization."

    GROUP           cc6kxbarErrorGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support error statistics on the fabric channel."

    GROUP           cc6kxbarErrRcvryThresholdGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of thresholds on fabric link errors
        for the fabric error recovery feature."

    GROUP           cc6kxbarErrRcvrySwitchoverGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of fabric switchover on excessive fabric 
        link errors for the fabric error recovery feature."

    GROUP           cc6kxbarErrRcvryPersLinkResGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of channel resync on excessive 
        persistent fabric link errors for the fabric error 
        recovery feature."

    GROUP           cc6kxbarErrRcvryChResyncGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of channel resync threshold for the 
        fabric error recovery feature."

    GROUP           cc6kxbarTMSwBusUtilGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration for traffic monitor feature on the 
        switching bus."

    GROUP           cc6kxbarTMSwBusUtilNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the notification control for traffic monitor 
        notifications on the switching bus."

    GROUP           cc6kxbarTMSwBusUtilNotifObjectGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on the switching bus."

    GROUP           cc6kxbarTMSwBusUtilNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on the switching bus."

    GROUP           cc6kxbarTMChUtilGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration for traffic monitor feature on  
        fabric channels."

    GROUP           cc6kxbarTMChUtilNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the notification control for traffic monitor 
        notifications on fabric channels."

    GROUP           cc6kxbarTMChUtilNotifObjectGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on fabric channels."

    GROUP           cc6kxbarTMChUtilNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on fabric channels."
    ::= { cc6kxbarMIBCompliances 9 }

cc6kxbarMIBComplianceVer8 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for
        the CISCO-CAT6K-CROSSBAR-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cc6kxbarModuleStatusGroupVer1,
                        cc6kxbarChannelStatusGroup,
                        cc6kxbarChannelStatisticsGroupVer1
                    }

    GROUP           cc6kxbarFallbackGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fallback mode feature."

    GROUP           cc6kxbarBusOnlyAllowedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support bus only mode feature."

    GROUP           cc6kxbarLcdBannerGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support lcd banner feature."

    GROUP           cc6kxbarChannelUtilGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support Input and Output traffic utilization on the 
        fabric channel."

    GROUP           cc6kxbarChannelSpeedGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support fabric channel speed feature."

    GROUP           cc6kxbarSwBusGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection feature."

    GROUP           cc6kxbarSwBusNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus switching detection notification 
        feature."

    GROUP           cc6kxbarTruncatedOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational truncated mode."

    GROUP           cc6kxbarIntBusNotifControlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus notifications."

    GROUP           cc6kxbarIntBusCRCErrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error notifications."

    GROUP           cc6kxbarIntBusCRCErrGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        internal bus CRC error status."

    GROUP           cc6kxbarDcefOnlyModeAllowedGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        dcef only mode feature."

    GROUP           cc6kxbarForceBusModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        force bus mode feature."

    GROUP           cc6kxbarFlowCtrlNotifCtrlGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system flow control notification control."

    GROUP           cc6kxbarFlowCtrlBusThreshGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold configuration."

    GROUP           cc6kxbarFlowCtrlBusThrNotifGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        the system bus flow control FIFO threshold exceeding
        notification."

    GROUP           cc6kxbarBusOnlyModeOperGroup
    DESCRIPTION
        "This group is mandatory only for platforms which support
        operational bus mode."

    GROUP           cc6kxbarSysCapPfcOperModeGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support the system information of PFC operational mode."

    GROUP           cc6kxbarSysCapSwitchResGroup
    DESCRIPTION
        "This group is mandatory only for platforms which
        support the capacity information of switching 
        resources in the system."

    GROUP           cc6kxbarChannelUtilGroup1
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support peak input and output traffic utilization on the
        fabric channel."

    GROUP           cc6kxbarSwBusGroup1
    DESCRIPTION
        "This group is mandatory only for platforms which support
        switching bus utilization."

    GROUP           cc6kxbarErrorGroup
    DESCRIPTION
        "This group is mandatory only for those platforms which
        support error statistics on the fabric channel."

    GROUP           cc6kxbarErrRcvryThresholdGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of thresholds on fabric link errors
        for the fabric error recovery feature."

    GROUP           cc6kxbarErrRcvrySwitchoverGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of fabric switchover on excessive fabric 
        link errors for the fabric error recovery feature."

    GROUP           cc6kxbarErrRcvryPersLinkResGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of channel resync on excessive 
        persistent fabric link errors for the fabric error 
        recovery feature."

    GROUP           cc6kxbarErrRcvryChResyncGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration of channel resync threshold for the 
        fabric error recovery feature."

    GROUP           cc6kxbarTMSwBusUtilGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration for traffic monitor feature on the 
        switching bus."

    GROUP           cc6kxbarTMSwBusUtilNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the notification control for traffic monitor 
        notifications on the switching bus."

    GROUP           cc6kxbarTMSwBusUtilNotifObjectGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on the switching bus."

    GROUP           cc6kxbarTMSwBusUtilNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on the switching bus."

    GROUP           cc6kxbarTMChUtilGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the configuration for traffic monitor feature on  
        fabric channels."

    GROUP           cc6kxbarTMChUtilNotifControlGroup
    DESCRIPTION
        "This group is mandatory for platforms which provide
        the notification control for traffic monitor 
        notifications on fabric channels."

    GROUP           cc6kxbarTMChUtilNotifObjectGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on fabric channels."

    GROUP           cc6kxbarTMChUtilNotifGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        traffic monitor notifications on fabric channels."

    GROUP           cc6kxbarStatisticsLbusDropGroup
    DESCRIPTION
        "This group is mandatory for platforms which support
        local bus packet drop statistics on fabric channels."
    ::= { cc6kxbarMIBCompliances 10 }

-- Units of Conformance

cc6kxbarModuleStatusGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarLcdBannerMaxSize,
                        cc6kxbarLcdBannerBanner,
                        cc6kxbarTruncatedModeAllowed,
                        cc6kxbarMinFabricPresent,
                        cc6kxbarFabricActiveSlot,
                        cc6kxbarFabricBackupSlot,
                        cc6kxbarModuleModeSwitchingMode
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing
        configuration and operation of the crossbar 
        switching fabric module and switching capable 
        module.
        cc6kxbarModuleStatusGroup object is superseded by cc6kxbarModuleStatusGroupVer1."
    ::= { cc6kxbarMIBGroups 1 }

cc6kxbarChannelStatusGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarModuleChannelModStatus,
                        cc6kxbarModuleChannelFabStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing
        status and configuration of the fabric channel."
    ::= { cc6kxbarMIBGroups 2 }

cc6kxbarChannelStatisticsGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarStatisticsInErrors,
                        cc6kxbarStatisticsOutErrors,
                        cc6kxbarStatisticsOutDropped,
                        cc6kxbarStatisticsInUtil,
                        cc6kxbarStatisticsOutUtil
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects providing statistics for
        the fabric channel.
        cc6kxbarChannelStatisticsGroup object is superseded by cc6kxbarChannelStatisticsGroupVer1."
    ::= { cc6kxbarMIBGroups 3 }

cc6kxbarFallbackGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarFallbackMode }
    STATUS          current
    DESCRIPTION
        "This object controls system fallback to bus-mode
        after loss of switching fabric module."
    ::= { cc6kxbarMIBGroups 4 }

cc6kxbarBusOnlyAllowedGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarBusOnlyModeAllowed }
    STATUS          current
    DESCRIPTION
        "This object determines the value of
        cc6kxbarModuleModeSwitchingMode of each module."
    ::= { cc6kxbarMIBGroups 5 }

cc6kxbarModuleStatusGroupVer1 OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarTruncatedModeAllowed,
                        cc6kxbarMinFabricPresent,
                        cc6kxbarFabricActiveSlot,
                        cc6kxbarFabricBackupSlot,
                        cc6kxbarModuleModeSwitchingMode
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing
        configuration and operation of the crossbar 
        switching fabric module and switching capable 
        module."
    ::= { cc6kxbarMIBGroups 6 }

cc6kxbarChannelStatisticsGroupVer1 OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarStatisticsInErrors,
                        cc6kxbarStatisticsOutErrors,
                        cc6kxbarStatisticsOutDropped
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing statistics for
        the fabric channel."
    ::= { cc6kxbarMIBGroups 7 }

cc6kxbarLcdBannerGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarLcdBannerMaxSize,
                        cc6kxbarLcdBannerBanner
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing configuration of
        the lcd Banner of the fabric module."
    ::= { cc6kxbarMIBGroups 8 }

cc6kxbarChannelUtilGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarStatisticsInUtil,
                        cc6kxbarStatisticsOutUtil
                    }
    STATUS          current
    DESCRIPTION
        "These object determines Input and Output Utilization of the
        fabric channel."
    ::= { cc6kxbarMIBGroups 9 }

cc6kxbarChannelSpeedGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarModuleChannelSpeed }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the
        speed of the fabric channel."
    ::= { cc6kxbarMIBGroups 10 }

cc6kxbarSwBusGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarSwBusSwitchingStatus,
                        cc6kxbarSwBusFailureDuration,
                        cc6kxbarSwBusNotifEnable
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for the switching bus switching
        detection feature."
    ::= { cc6kxbarMIBGroups 11 }

cc6kxbarSwBusNotifGroup NOTIFICATION-GROUP
   NOTIFICATIONS    { cc6kxbarSwBusStatusChangeNotif }
    STATUS          current
    DESCRIPTION
        "A collection of notifications for the switching
        bus switching detection feature."
    ::= { cc6kxbarMIBGroups 12 }

cc6kxbarTruncatedOperGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarTruncatedModeOper }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the
        operational truncated mode."
    ::= { cc6kxbarMIBGroups 13 }

cc6kxbarIntBusNotifControlGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarIntBusNotifEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects to control the notification
        generation for internal bus."
    ::= { cc6kxbarMIBGroups 14 }

cc6kxbarIntBusCRCErrNotifGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        cc6kxbarIntBusCRCErrExcdNotif,
                        cc6kxbarIntBusCRCErrRcvrdNotif
                    }
    STATUS          current
    DESCRIPTION
        "A collection of notifications for the
        internal bus CRC error."
    ::= { cc6kxbarMIBGroups 15 }

cc6kxbarIntBusCRCErrGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarIntBusCRCErrExcdStatus,
                        cc6kxbarIntBusCRCErrExcdCount
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects to indicate the internal bus
        CRC error information on a physical entity."
    ::= { cc6kxbarMIBGroups 16 }

cc6kxbarDcefOnlyModeAllowedGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarDcefOnlyModeAllowed }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        and information about the dcef only mode feature."
    ::= { cc6kxbarMIBGroups 17 }

cc6kxbarForceBusModeGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarForceBusMode }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        and information about the force bus mode feature."
    ::= { cc6kxbarMIBGroups 18 }

cc6kxbarFlowCtrlNotifCtrlGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarFlowCtrlNotifEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects to control the notification
        generation for the system flow control feature."
    ::= { cc6kxbarMIBGroups 19 }

cc6kxbarFlowCtrlBusThreshGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarFlowCtrlBusFIFOThrMode,
                        cc6kxbarFlowCtrlBusFIFOThrValue,
                        cc6kxbarFlowCtrlBusFIFOTransTime
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing configuration
        information about the system bus flow control FIFO
        threshold."
    ::= { cc6kxbarMIBGroups 20 }

cc6kxbarFlowCtrlBusThrNotifGroup NOTIFICATION-GROUP
   NOTIFICATIONS    { cc6kxbarFlowCtrlBusThrExcdNotif }
    STATUS          current
    DESCRIPTION
        "A collection of notifications for the system bus
        flow control FIFO threshold exceeding."
    ::= { cc6kxbarMIBGroups 21 }

cc6kxbarBusOnlyModeOperGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarBusOnlyModeOper }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the
        operational bus mode."
    ::= { cc6kxbarMIBGroups 22 }

cc6kxbarSysCapPfcOperModeGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarSysCapPfcOperMode }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the system
        information of PFC operational mode."
    ::= { cc6kxbarMIBGroups 23 }

cc6kxbarSysCapSwitchResGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarSysCapSwitchResSeries,
                        cc6kxbarSysCapSwitchResCefMode
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the capacity
        information of switching resources in the system."
    ::= { cc6kxbarMIBGroups 24 }

cc6kxbarChannelUtilGroup1 OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarStatisticsPeakInUtil,
                        cc6kxbarStatisticsPeakTmInUtil,
                        cc6kxbarStatisticsPeakOutUtil,
                        cc6kxbarStatisticsPeakTmOutUtil
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the information about peak
        input and output utilization of a specific fabric channel on 
        a specific module."
    ::= { cc6kxbarMIBGroups 25 }

cc6kxbarSwBusGroup1 OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarSwBusUtilization,
                        cc6kxbarSwBusPeakUtilization,
                        cc6kxbarSwBusPeakTimeUtil
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the information about
        switching bus utilization."
    ::= { cc6kxbarMIBGroups 26 }

cc6kxbarErrorGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarErrorModuleCrc,
                        cc6kxbarErrorModuleHbeat,
                        cc6kxbarErrorModuleSync,
                        cc6kxbarErrorModuleDDRSync,
                        cc6kxbarErrorChannelSync,
                        cc6kxbarErrorChannelBuffer,
                        cc6kxbarErrorChannelTimeout
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing error statistics for
        a specific fabric channel on a specific module."
    ::= { cc6kxbarMIBGroups 27 }

cc6kxbarErrRcvryThresholdGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarErrRcvryThreshLink,
                        cc6kxbarErrRcvryThreshPersLink
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        of thresholds on fabric link errors for the fabric
        error recovery feature."
    ::= { cc6kxbarMIBGroups 28 }

cc6kxbarErrRcvrySwitchoverGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarErrRcvrySwitchoverEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        of fabric switchover on excessive fabric link errors
        for the fabric error recovery feature."
    ::= { cc6kxbarMIBGroups 29 }

cc6kxbarErrRcvryPersLinkResGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarErrRcvryPersLinkResync }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        of channel resync on excessive persistent fabric link
        errors for the fabric error recovery feature."
    ::= { cc6kxbarMIBGroups 30 }

cc6kxbarErrRcvryChResyncGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarErrRcvryChResyncCount,
                        cc6kxbarErrRcvryChResyncInterval
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        for channel resync threshold for the fabric error
        recovery feature."
    ::= { cc6kxbarMIBGroups 31 }

cc6kxbarTMSwBusUtilGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarTMSwBusUtilEnable,
                        cc6kxbarTMSwBusUtilInterval,
                        cc6kxbarTMSwBusUtilThreshold,
                        cc6kxbarTMSwBusUtilLogCount,
                        cc6kxbarTMSwBusUtilLastLogTime,
                        cc6kxbarTMSwBusUtilLogInterval
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing configuration
        information for traffic monitor feature on 
        the switching bus."
    ::= { cc6kxbarMIBGroups 32 }

cc6kxbarTMSwBusUtilNotifControlGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarTMSwBusUtilNotifEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects to control the traffic monitor
        notification generation on the switching bus."
    ::= { cc6kxbarMIBGroups 33 }

cc6kxbarTMSwBusUtilNotifObjectGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarTMSwBusUtilUtilization }
    STATUS          current
    DESCRIPTION
        "A collection of objects included in the traffic monitor
        notifications on the switching bus."
    ::= { cc6kxbarMIBGroups 34 }

cc6kxbarTMSwBusUtilNotifGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        cc6kxbarTMSwBusUtilAboveNotif,
                        cc6kxbarTMSwBusUtilBelowNotif
                    }
    STATUS          current
    DESCRIPTION
        "A collection of traffic monitor notifications on the
        switching bus."
    ::= { cc6kxbarMIBGroups 35 }

cc6kxbarTMChUtilGroup OBJECT-GROUP
    OBJECTS         {
                        cc6kxbarTMChUtilEnable,
                        cc6kxbarTMChUtilInterval,
                        cc6kxbarTMChUtilThreshold,
                        cc6kxbarTMChUtilLogCount,
                        cc6kxbarTMChUtilLastLogTime,
                        cc6kxbarTMChUtilLogInterval
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing the configuration
        information for traffic monitor feature on fabric 
        channels."
    ::= { cc6kxbarMIBGroups 36 }

cc6kxbarTMChUtilNotifControlGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarTMChUtilNotifEnable }
    STATUS          current
    DESCRIPTION
        "A collection of objects to control the traffic monitor
        notification generation on fabric channels."
    ::= { cc6kxbarMIBGroups 37 }

cc6kxbarTMChUtilNotifObjectGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarTMChUtilUtilization }
    STATUS          current
    DESCRIPTION
        "A collection of objects included in the traffic monitor
        notifications on fabric channels."
    ::= { cc6kxbarMIBGroups 38 }

cc6kxbarTMChUtilNotifGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        cc6kxbarTMChUtilAboveNotif,
                        cc6kxbarTMChUtilBelowNotif
                    }
    STATUS          current
    DESCRIPTION
        "A collection of traffic monitor notifications on
        fabric channels."
    ::= { cc6kxbarMIBGroups 39 }

cc6kxbarStatisticsLbusDropGroup OBJECT-GROUP
    OBJECTS         { cc6kxbarStatisticsLbusDrops }
    STATUS          current
    DESCRIPTION
        "A collection of objects providing information for
        local bus drop packets statistics."
    ::= { cc6kxbarMIBGroups 40 }

END



