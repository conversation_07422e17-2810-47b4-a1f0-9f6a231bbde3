-- *------------------------------------------------------------------
-- * CISCO-IPSEC-FLOW-MONITOR-MIB.my:  IPSec Flow Monitoring MIB.
-- *
-- * April 2000, <PERSON><PERSON>
-- *
-- * Copyright (c) 2000, 2004, 2007 by Cisco Systems Inc.
-- * All rights reserved.
-- *
-- *------------------------------------------------------------------

CISCO-IPSEC-FLOW-MONITOR-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    NOTIFICATION-TYPE,
    Counter32,
    Counter64,
    Gauge32,
    Integer32,
    Unsigned32
        FROM SNMPv2-SMI
    MODULE-COMPLIANC<PERSON>,
    OBJECT-G<PERSON><PERSON>,
    NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    TEXTUAL-CONVENTION,
    DisplayString,
    TimeStamp,
    <PERSON>Interval,
    TruthValue
        FROM SNMPv2-TC
    cmgwIndex
        FROM CISCO-MEDIA-GATEWAY-MIB
    ciscoMgmt
        FROM CISCO-SMI;


ciscoIpSecFlowMonitorMIB MODULE-IDENTITY
    LAST-UPDATED    "200710240000Z"
    ORGANIZATION    "Tivoli Systems and Cisco Systems"
    CONTACT-INFO
            "Tivoli Systems
            Research Triangle Park, NC

            Cisco Systems
            170 W Tasman Drive
            San Jose, CA  95134
            USA

            Tel: ****** 553-NETS
            E-mail: <EMAIL>"
    DESCRIPTION
        "This is a MIB Module for monitoring the
        structures in IPSec-based Virtual Private Networks.
        The MIB has been designed to be adopted as an IETF
        standard. Hence Cisco-specific features of IPSec
        protocol are excluded from this MIB. 

        Acronyms
        The following acronyms are used in this document:

         IPSec:      Secure IP Protocol

         VPN:        Virtual Private Network

         ISAKMP:     Internet Security Association and Key Exchange
                     Protocol

         IKE:        Internet Key Exchange Protocol

         SA:         Security Association

         MM:         Main Mode - the process of setting up
                     a Phase 1 SA to secure the exchanges
                     required to setup Phase 2 SAs

         QM:         Quick Mode - the process of setting up
                     Phase 2 Security Associations using 
                     a Phase 1 SA.


         Overview of IPsec MIB

        The MIB contains six major groups of objects which are
        used to manage the IPSec Protocol. These groups include
        a Levels Group, a Phase-1 Group, a Phase-2 Group,
        a History Group, a Failure Group and a TRAP Control Group.
        The following table illustrates the structure of the
        IPSec MIB.

        The Phase 1 group models objects pertaining to
        IKE negotiations and tunnels.

        The Phase 2 group models objects pertaining to
        IPSec data tunnels.

        The History group is to aid applications that do
        trending analysis.

        The Failure group is to enable an operator to
        do troubleshooting and debugging of the VPN Router.
        Further, counters are supported to aid Intrusion 
        Detection.

        In addition to the five major MIB Groups, there are
        a number of Notifications. The following table
        illustrates the name and description of the 
        IPSec TRAPs.

        For a detailed discussion, please refer to the IETF
        draft draft-ietf-ipsec-flow-monitoring-mib-00.txt."
    REVISION        "200710240000Z"
    DESCRIPTION
        "In the description of cipSecTunHistHcInDecompOctets,
        cipSecTunHcInOctets has been changed to 
        cipSecTunHistHcInOctets.   

        In the description of cipSecTunHistOutUncompOctets,
        cipSecTunOutOctets has been changed to 
        cipSecTunHistOutOctets.

        In the description of cipSecTunHistHcOutUncompOctets,
        cipSecTunHcOutOctets has been changed to 
        cipSecTunHistHcOutOctets.

        In the description of cipSecTunHistInDecompOctets,
        cipSecTunInOctets has been changed to 
        cipSecTunHistInOctets."
    REVISION        "200410120000Z"
    DESCRIPTION
        "Added two table for media gateway stats
        information:
            cikePhase1GWStatsTable (phase-1 IKE)
            cipSecPhase2GWStatsTable (phase-2 IPsec)"
    REVISION        "200010131800Z"
    DESCRIPTION
        "Changed cipSecSpiValue to Unsigned32.
        Changed Protocol ranges to
        start at 0 instead of 1.
        Removed comment(s) incorrectly indicating
        this MIB was CiscoExperiment."
    REVISION        "200008171259Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 171 }



-- +++++++++++++++++++++++++++++++++++++++++++++++++++
-- Local Textual Conventions
-- +++++++++++++++++++++++++++++++++++++++++++++++++++

IPSIpAddress ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "An IP V4 or V6 Address."
    SYNTAX          OCTET STRING (SIZE (4  |  16))

-- IP V4 or V6 Address

IkePeerType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The type of IPsec Phase-1 IKE peer identity.
        The IKE peer may be identified by:
         1. an IP address, or
         2. a host name."
    SYNTAX          INTEGER  {
                        ipAddrPeer(1),
                        namePeer(2)
                    }

IkeNegoMode ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-1 IKE negotiation mode."
    SYNTAX          INTEGER  {
                        main(1),
                        aggressive(2)
                    }

IkeHashAlgo ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The hash algorithm used in IPsec Phase-1
        IKE negotiations."
    SYNTAX          INTEGER  {
                        none(1),
                        md5(2),
                        sha(3)
                    }

IkeAuthMethod ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The authentication method used in IPsec Phase-1 IKE
        negotiations."
    SYNTAX          INTEGER  {
                        none(1),
                        preSharedKey(2),
                        rsaSig(3),
                        rsaEncrypt(4),
                        revPublicKey(5)
                    }

DiffHellmanGrp ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The Diffie Hellman Group used in negotiations."
    SYNTAX          INTEGER  {
                        none(1),
                        dhGroup1(2),
                        dhGroup2(3)
                    }

KeyType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The type of key used by an IPsec Phase-2 Tunnel."
    SYNTAX          INTEGER  {
                        ike(1),
                        manual(2)
                    }

EncapMode ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The encapsulation mode used by an IPsec Phase-2
        Tunnel."
    SYNTAX          INTEGER  {
                        tunnel(1),
                        transport(2)
                    }

EncryptAlgo ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The encryption algorithm used in negotiations."
    SYNTAX          INTEGER  {
                        none(1),
                        des(2),
                        des3(3)
                    }

AuthAlgo ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by a
        security association of an IPsec Phase-2 Tunnel."
    SYNTAX          INTEGER  {
                        none(1),
                        hmacMd5(2),
                        hmacSha(3)
                    }

CompAlgo ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The compression algorithm used by a
        security association of an IPsec Phase-2 Tunnel."
    SYNTAX          INTEGER  {
                        none(1),
                        ldf(2)
                    }

EndPtType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The type of identity use to specify an IPsec End Point."
    SYNTAX          INTEGER  {
                        singleIpAddr(1),
                        ipAddrRange(2),
                        ipSubnet(3)
                    }

TunnelStatus ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The status of a Tunnel.  Objects of this type may
        be used to bring the tunnel down by setting
        value of this object to destroy(2).  Objects of this
        type cannot be used to create a Tunnel."
    SYNTAX          INTEGER  {
                        active(1),
                        destroy(2)
                    }

TrapStatus ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "The administrative status for sending a TRAP."
    SYNTAX          INTEGER  {
                        enabled(1),
                        disabled(2)
                    }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- IPsec MIB Object Groups
--   
-- This MIB module contains the following groups:
-- 1) IPsec Levels Group
-- 2) IPsec Phase-1 Group
-- 3) IPsec Phase-2 Group
-- 4) IPsec History Group
-- 5) IPsec Failure Group
-- 6) IPsec TRAP Control Group
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoIpSecFlowMonitorMIB 1 }

cipSecLevels  OBJECT IDENTIFIER
    ::= { cipSecMIBObjects 1 }

cipSecPhaseOne  OBJECT IDENTIFIER
    ::= { cipSecMIBObjects 2 }

cipSecPhaseTwo  OBJECT IDENTIFIER
    ::= { cipSecMIBObjects 3 }

cipSecHistory  OBJECT IDENTIFIER
    ::= { cipSecMIBObjects 4 }

cipSecFailures  OBJECT IDENTIFIER
    ::= { cipSecMIBObjects 5 }

cipSecTrapCntl  OBJECT IDENTIFIER
    ::= { cipSecMIBObjects 6 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- IPsec Levels Group
--   
-- This group consists of a:
-- 1) IPsec MIB Level
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecMibLevel OBJECT-TYPE
    SYNTAX          Integer32 (1..4096 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The level of the IPsec MIB." 
    ::= { cipSecLevels 1 }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-1 Internet Key Exchange (IKE) Group
--   
-- This group consists of:
-- 1) IPsec Phase-1 Global Statistics
-- 2) IPsec Phase-1 Peer Table
-- 3) IPsec Phase-1 Tunnel Table
-- 4) IPsec Phase-1 Correlation Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
--   
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-1 Global Statistics
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cikeGlobalStats  OBJECT IDENTIFIER
    ::= { cipSecPhaseOne 1 }


cikeGlobalActiveTunnels OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of currently active IPsec
        Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 1 }

cikeGlobalPreviousTunnels OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of previously active
        IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 2 }

cikeGlobalInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets received by all currently
        and previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 3 }

cikeGlobalInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received by all
        currently and previously active IPsec 
        Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 4 }

cikeGlobalInDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets which were
        dropped during receive processing by all 
        currently and previously
         active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 5 }

cikeGlobalInNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys received by
        all currently and previously active IPsec 
        Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 6 }

cikeGlobalInP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        received by all currently and previously 
        active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 7 }

cikeGlobalInP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were received and found to be invalid 
        by all currently and previously active IPsec 
        Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 8 }

cikeGlobalInP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were received and rejected by all 
        currently and previously active IPsec Phase-1 
        IKE Tunnels." 
    ::= { cikeGlobalStats 9 }

cikeGlobalInP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 security
        association delete requests received by all 
        currently and previously
         active and IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 10 }

cikeGlobalOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by all currently
        and previously active and IPsec Phase-1 
        IKE Tunnels." 
    ::= { cikeGlobalStats 11 }

cikeGlobalOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by all currently
        and previously active and IPsec Phase-1 
        Tunnels." 
    ::= { cikeGlobalStats 12 }

cikeGlobalOutDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets which were dropped
        during send processing by all currently 
        and previously
         active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 13 }

cikeGlobalOutNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys sent by all currently
        and previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 14 }

cikeGlobalOutP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were sent by all currently and previously 
        active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 15 }

cikeGlobalOutP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were sent and found to be invalid by 
        all currently and previously active IPsec Phase-1 
        Tunnels." 
    ::= { cikeGlobalStats 16 }

cikeGlobalOutP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were sent and rejected by all currently and
         previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 17 }

cikeGlobalOutP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 SA
        delete requests sent by all currently and 
        previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 18 }

cikeGlobalInitTunnels OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-1 IKE
        Tunnels which were locally initiated." 
    ::= { cikeGlobalStats 19 }

cikeGlobalInitTunnelFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-1 IKE Tunnels
        which were locally initiated and failed to activate." 
    ::= { cikeGlobalStats 20 }

cikeGlobalRespTunnelFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-1 IKE Tunnels
        which were remotely initiated and failed to activate." 
    ::= { cikeGlobalStats 21 }

cikeGlobalSysCapFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of system capacity failures
        which occurred during processing of all current 
        and previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 22 }

cikeGlobalAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of authentications which ended
        in failure by all current and previous IPsec Phase-1
        IKE Tunnels." 
    ::= { cikeGlobalStats 23 }

cikeGlobalDecryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of decryptions which ended
        in failure by all current and previous IPsec Phase-1
        IKE Tunnels." 
    ::= { cikeGlobalStats 24 }

cikeGlobalHashValidFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of hash validations which ended
        in failure by all current and previous IPsec Phase-1
        IKE Tunnels." 
    ::= { cikeGlobalStats 25 }

cikeGlobalNoSaFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of non-existent Security Association
        in failures which occurred during processing of 
        all current and previous IPsec Phase-1 IKE Tunnels." 
    ::= { cikeGlobalStats 26 }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-1 Internet Key Exchange Peer Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cikePeerTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CikePeerEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-1 Internet Key Exchange Peer Table.
        There is one entry in this table for each IPsec
        Phase-1 IKE peer association which is currently
        associated with an active IPsec Phase-1 Tunnel.
        The IPsec Phase-1 IKE Tunnel associated with this
        IPsec Phase-1 IKE peer association may or may not
        be currently active."
    ::= { cipSecPhaseOne 2 }

cikePeerEntry OBJECT-TYPE
    SYNTAX          CikePeerEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes associated
        with an IPsec Phase-1 IKE peer association."
    INDEX           {
                        cikePeerLocalType,
                        cikePeerLocalValue,
                        cikePeerRemoteType,
                        cikePeerRemoteValue,
                        cikePeerIntIndex
                    } 
    ::= { cikePeerTable 1 }

CikePeerEntry ::= SEQUENCE {
        cikePeerLocalType         IkePeerType,
        cikePeerLocalValue        DisplayString,
        cikePeerRemoteType        IkePeerType,
        cikePeerRemoteValue       DisplayString,
        cikePeerIntIndex          Integer32,
        cikePeerLocalAddr         IPSIpAddress,
        cikePeerRemoteAddr        IPSIpAddress,
        cikePeerActiveTime        TimeInterval,
        cikePeerActiveTunnelIndex Integer32
}

cikePeerLocalType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The type of local peer identity.  The local peer
        may be identified by:
        1. an IP address, or
        2. a host name." 
    ::= { cikePeerEntry 1 }

cikePeerLocalValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The value of the local peer identity.

        If the local peer type is an IP Address, then this
        is the IP Address used to identify the local peer.

        If the local peer type is a host name, then this is
        the host name used to identify the local peer." 
    ::= { cikePeerEntry 2 }

cikePeerRemoteType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The type of remote peer identity.  The remote peer
        may be identified by:
        1. an IP address, or
        2. a host name." 
    ::= { cikePeerEntry 3 }

cikePeerRemoteValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The value of the remote peer identity.

        If the remote peer type is an IP Address, then this
        is the IP Address used to identify the remote peer.

        If the remote peer type is a host name, then this is
        the host name used to identify the remote peer." 
    ::= { cikePeerEntry 4 }

cikePeerIntIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The internal index of the local-remote
        peer association.  This internal index is used 
        to uniquely identify multiple associations between 
        the local and remote peer." 
    ::= { cikePeerEntry 5 }

cikePeerLocalAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the local peer." 
    ::= { cikePeerEntry 6 }

cikePeerRemoteAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the remote peer." 
    ::= { cikePeerEntry 7 }

cikePeerActiveTime OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The length of time that the peer association has
        existed in hundredths of a second." 
    ::= { cikePeerEntry 8 }

cikePeerActiveTunnelIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index of the active IPsec Phase-1 IKE Tunnel
        (cikeTunIndex in the cikeTunnelTable) for this peer
        association.  If an IPsec Phase-1 IKE Tunnel is
        not currently active, then the value of this
        object will be zero." 
    ::= { cikePeerEntry 9 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-1 Internet Key Exchange Tunnel Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cikeTunnelTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CikeTunnelEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-1 Internet Key Exchange Tunnel Table.
        There is one entry in this table for each active IPsec
        Phase-1 IKE Tunnel."
    ::= { cipSecPhaseOne 3 }

cikeTunnelEntry OBJECT-TYPE
    SYNTAX          CikeTunnelEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes associated with
        an active IPsec Phase-1 IKE Tunnel."
    INDEX           { cikeTunIndex } 
    ::= { cikeTunnelTable 1 }

CikeTunnelEntry ::= SEQUENCE {
        cikeTunIndex              Integer32,
        cikeTunLocalType          IkePeerType,
        cikeTunLocalValue         DisplayString,
        cikeTunLocalAddr          IPSIpAddress,
        cikeTunLocalName          DisplayString,
        cikeTunRemoteType         IkePeerType,
        cikeTunRemoteValue        DisplayString,
        cikeTunRemoteAddr         IPSIpAddress,
        cikeTunRemoteName         DisplayString,
        cikeTunNegoMode           IkeNegoMode,
        cikeTunDiffHellmanGrp     DiffHellmanGrp,
        cikeTunEncryptAlgo        EncryptAlgo,
        cikeTunHashAlgo           IkeHashAlgo,
        cikeTunAuthMethod         IkeAuthMethod,
        cikeTunLifeTime           Integer32,
        cikeTunActiveTime         TimeInterval,
        cikeTunSaRefreshThreshold Integer32,
        cikeTunTotalRefreshes     Counter32,
        cikeTunInOctets           Counter32,
        cikeTunInPkts             Counter32,
        cikeTunInDropPkts         Counter32,
        cikeTunInNotifys          Counter32,
        cikeTunInP2Exchgs         Counter32,
        cikeTunInP2ExchgInvalids  Counter32,
        cikeTunInP2ExchgRejects   Counter32,
        cikeTunInP2SaDelRequests  Counter32,
        cikeTunOutOctets          Counter32,
        cikeTunOutPkts            Counter32,
        cikeTunOutDropPkts        Counter32,
        cikeTunOutNotifys         Counter32,
        cikeTunOutP2Exchgs        Counter32,
        cikeTunOutP2ExchgInvalids Counter32,
        cikeTunOutP2ExchgRejects  Counter32,
        cikeTunOutP2SaDelRequests Counter32,
        cikeTunStatus             TunnelStatus
}

cikeTunIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The index of the IPsec Phase-1 IKE Tunnel Table.
        The value of the index is a number which begins 
        at one and is incremented with each tunnel that 
        is created. The value of this object will 
        wrap at 2,147,483,647." 
    ::= { cikeTunnelEntry 1 }

cikeTunLocalType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of local peer identity.  The local
        peer may be identified by:
         1. an IP address, or
         2. a host name." 
    ::= { cikeTunnelEntry 2 }

cikeTunLocalValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the local peer identity.

        If the local peer type is an IP Address, then this
        is the IP Address used to identify the local peer.

        If the local peer type is a host name, then this is
        the host name used to identify the local peer." 
    ::= { cikeTunnelEntry 3 }

cikeTunLocalAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the local endpoint for the IPsec
        Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 4 }

cikeTunLocalName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the local IP address for
        the IPsec Phase-1 IKE Tunnel. If the DNS 
        name associated with the local tunnel endpoint 
        is not known, then the value of this
         object will be a NULL string." 
    ::= { cikeTunnelEntry 5 }

cikeTunRemoteType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of remote peer identity.
        The remote peer may be identified by:
         1. an IP address, or
         2. a host name." 
    ::= { cikeTunnelEntry 6 }

cikeTunRemoteValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the remote peer identity.

        If the remote peer type is an IP Address, then this
        is the IP Address used to identify the remote peer.

        If the remote peer type is a host name, then 
        this is the host name used to identify the 
        remote peer." 
    ::= { cikeTunnelEntry 7 }

cikeTunRemoteAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the remote endpoint for the IPsec
        Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 8 }

cikeTunRemoteName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the remote IP address of IPsec Phase-1
        IKE Tunnel. If the DNS name associated with the remote
        tunnel endpoint is not known, then the value of this
        object will be a NULL string." 
    ::= { cikeTunnelEntry 9 }

cikeTunNegoMode OBJECT-TYPE
    SYNTAX          IkeNegoMode
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiation mode of the IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 10 }

cikeTunDiffHellmanGrp OBJECT-TYPE
    SYNTAX          DiffHellmanGrp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Diffie Hellman Group used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelEntry 11 }

cikeTunEncryptAlgo OBJECT-TYPE
    SYNTAX          EncryptAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encryption algorithm used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelEntry 12 }

cikeTunHashAlgo OBJECT-TYPE
    SYNTAX          IkeHashAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The hash algorithm used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelEntry 13 }

cikeTunAuthMethod OBJECT-TYPE
    SYNTAX          IkeAuthMethod
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication method used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelEntry 14 }

cikeTunLifeTime OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated LifeTime of the IPsec Phase-1 IKE Tunnel
        in seconds." 
    ::= { cikeTunnelEntry 15 }

cikeTunActiveTime OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The length of time the IPsec Phase-1 IKE tunnel has been
        active in hundredths of seconds." 
    ::= { cikeTunnelEntry 16 }

cikeTunSaRefreshThreshold OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The security association refresh threshold in seconds." 
    ::= { cikeTunnelEntry 17 }

cikeTunTotalRefreshes OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "QM Exchanges"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of security associations
        refreshes performed." 
    ::= { cikeTunnelEntry 18 }

cikeTunInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets received by
        this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 19 }

cikeTunInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received by
        this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 20 }

cikeTunInDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped
        by this IPsec Phase-1 IKE Tunnel during 
        receive processing." 
    ::= { cikeTunnelEntry 21 }

cikeTunInNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys received by
        this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 22 }

cikeTunInP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2
        exchanges received by
         this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 23 }

cikeTunInP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2
        exchanges received and found to be invalid 
        by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 24 }

cikeTunInP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        received and rejected by this IPsec Phase-1 
        Tunnel." 
    ::= { cikeTunnelEntry 25 }

cikeTunInP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2
        security association delete requests received 
        by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 26 }

cikeTunOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by this IPsec Phase-1
        IKE Tunnel." 
    ::= { cikeTunnelEntry 27 }

cikeTunOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by this IPsec Phase-1
        IKE Tunnel." 
    ::= { cikeTunnelEntry 28 }

cikeTunOutDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped by this
        IPsec Phase-1 IKE Tunnel during send processing." 
    ::= { cikeTunnelEntry 29 }

cikeTunOutNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys sent by this
        IPsec Phase-1 Tunnel." 
    ::= { cikeTunnelEntry 30 }

cikeTunOutP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges sent by
        this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 31 }

cikeTunOutP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges sent and
        found to be invalid by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 32 }

cikeTunOutP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges sent and
        rejected by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 33 }

cikeTunOutP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 security association
        delete requests sent by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelEntry 34 }

cikeTunStatus OBJECT-TYPE
    SYNTAX          TunnelStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The status of the MIB table row.

        This object can be used to bring the tunnel down 
        by setting value of this object to destroy(2).

        This object cannot be used to create 
        a MIB table row." 
    ::= { cikeTunnelEntry 35 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The Internet Key Exchange Peer Association to
-- Phase-2 Tunnel Correlation Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cikePeerCorrTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CikePeerCorrEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-1 Internet Key Exchange Peer
        Association to IPsec Phase-2 Tunnel
        Correlation Table. There is one entry in
        this table for each active IPsec Phase-2
        Tunnel."
    ::= { cipSecPhaseOne 4 }

cikePeerCorrEntry OBJECT-TYPE
    SYNTAX          CikePeerCorrEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes of an
        IPsec Phase-1 IKE Peer Association to IPsec
        Phase-2 Tunnel Correlation."
    INDEX           {
                        cikePeerCorrLocalType,
                        cikePeerCorrLocalValue,
                        cikePeerCorrRemoteType,
                        cikePeerCorrRemoteValue,
                        cikePeerCorrIntIndex,
                        cikePeerCorrSeqNum
                    } 
    ::= { cikePeerCorrTable 1 }

CikePeerCorrEntry ::= SEQUENCE {
        cikePeerCorrLocalType     IkePeerType,
        cikePeerCorrLocalValue    DisplayString,
        cikePeerCorrRemoteType    IkePeerType,
        cikePeerCorrRemoteValue   DisplayString,
        cikePeerCorrIntIndex      Integer32,
        cikePeerCorrSeqNum        Integer32,
        cikePeerCorrIpSecTunIndex Integer32
}

cikePeerCorrLocalType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The type of local peer identity. The local peer
        may be identified by:
        1. an IP address, or
        2. a host name." 
    ::= { cikePeerCorrEntry 1 }

cikePeerCorrLocalValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The value of the local peer identity.

        If the local peer type is an IP Address, then this
        is the IP Address used to identify the local peer.

        If the local peer type is a host name, then this is
        the host name used to identify the local peer." 
    ::= { cikePeerCorrEntry 2 }

cikePeerCorrRemoteType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The type of remote peer identity. The remote peer
        may be identified by:
        1. an IP address, or
        2. a host name." 
    ::= { cikePeerCorrEntry 3 }

cikePeerCorrRemoteValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The value of the remote peer identity.

        If the remote peer type is an IP Address, then this
        is the IP Address used to identify the remote peer.

        If the remote peer type is a host name, then this is
        the host name used to identify the remote peer." 
    ::= { cikePeerCorrEntry 4 }

cikePeerCorrIntIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The internal index of the local-remote
        peer association.  This internal index is 
        used to uniquely identify multiple associations 
        between the local and remote peer." 
    ::= { cikePeerCorrEntry 5 }

cikePeerCorrSeqNum OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The sequence number of the local-remote
        peer association.  This sequence number is 
        used to uniquely identify multiple instances 
        of an unique association between
         the local and remote peer." 
    ::= { cikePeerCorrEntry 6 }

cikePeerCorrIpSecTunIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index of the active IPsec Phase-2 Tunnel
        (cipSecTunIndex in the cipSecTunnelTable) for this
        IPsec Phase-1 IKE Peer Association." 
    ::= { cikePeerCorrEntry 7 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
--   
-- cikePhase1GWStatsTable
--   
-- Gateway Phase-1 IKE stats information
--   
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cikePhase1GWStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CikePhase1GWStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Phase-1 IKE stats information is included in this table.
        Each entry is related to a specific gateway which is 
        identified by 'cmgwIndex'."
    ::= { cipSecPhaseOne 5 }

cikePhase1GWStatsEntry OBJECT-TYPE
    SYNTAX          CikePhase1GWStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes of an Phase-1 IKE stats
        information for the related gateway.

        There is only one entry for each gateway. The entry 
        is created when a gateway up and cannot be deleted."
    INDEX           { cmgwIndex } 
    ::= { cikePhase1GWStatsTable 1 }

CikePhase1GWStatsEntry ::= SEQUENCE {
        cikePhase1GWActiveTunnels      Gauge32,
        cikePhase1GWPreviousTunnels    Counter32,
        cikePhase1GWInOctets           Counter32,
        cikePhase1GWInPkts             Counter32,
        cikePhase1GWInDropPkts         Counter32,
        cikePhase1GWInNotifys          Counter32,
        cikePhase1GWInP2Exchgs         Counter32,
        cikePhase1GWInP2ExchgInvalids  Counter32,
        cikePhase1GWInP2ExchgRejects   Counter32,
        cikePhase1GWInP2SaDelRequests  Counter32,
        cikePhase1GWOutOctets          Counter32,
        cikePhase1GWOutPkts            Counter32,
        cikePhase1GWOutDropPkts        Counter32,
        cikePhase1GWOutNotifys         Counter32,
        cikePhase1GWOutP2Exchgs        Counter32,
        cikePhase1GWOutP2ExchgInvalids Counter32,
        cikePhase1GWOutP2ExchgRejects  Counter32,
        cikePhase1GWOutP2SaDelRequests Counter32,
        cikePhase1GWInitTunnels        Counter32,
        cikePhase1GWInitTunnelFails    Counter32,
        cikePhase1GWRespTunnelFails    Counter32,
        cikePhase1GWSysCapFails        Counter32,
        cikePhase1GWAuthFails          Counter32,
        cikePhase1GWDecryptFails       Counter32,
        cikePhase1GWHashValidFails     Counter32,
        cikePhase1GWNoSaFails          Counter32
}

cikePhase1GWActiveTunnels OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of currently active IPsec
        Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 1 }

cikePhase1GWPreviousTunnels OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of previously active
        IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 2 }

cikePhase1GWInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets received by all currently
        and previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 3 }

cikePhase1GWInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received by all
        currently and previously active IPsec 
        Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 4 }

cikePhase1GWInDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets which were
        dropped during receive processing by all 
        currently and previously
        active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 5 }

cikePhase1GWInNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys received by
        all currently and previously active IPsec 
        Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 6 }

cikePhase1GWInP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        received by all currently and previously 
        active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 7 }

cikePhase1GWInP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were received and found to be invalid 
        by all currently and previously active IPsec 
        Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 8 }

cikePhase1GWInP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were received and rejected by all 
        currently and previously active IPsec Phase-1 
        IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 9 }

cikePhase1GWInP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 'Security
        Association' delete requests received by all 
        currently and previously active and IPsec 
        Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 10 }

cikePhase1GWOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by all currently
        and previously active and IPsec Phase-1 
        IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 11 }

cikePhase1GWOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by all currently
        and previously active and IPsec Phase-1 
        Tunnels." 
    ::= { cikePhase1GWStatsEntry 12 }

cikePhase1GWOutDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets which were dropped
        during send processing by all currently 
        and previously
        active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 13 }

cikePhase1GWOutNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys sent by all currently
        and previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 14 }

cikePhase1GWOutP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were sent by all currently and previously 
        active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 15 }

cikePhase1GWOutP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were sent and found to be invalid by 
        all currently and previously active IPsec Phase-1 
        Tunnels." 
    ::= { cikePhase1GWStatsEntry 16 }

cikePhase1GWOutP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges
        which were sent and rejected by all currently and
        previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 17 }

cikePhase1GWOutP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 SA
        delete requests sent by all currently and 
        previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 18 }

cikePhase1GWInitTunnels OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-1 IKE
        Tunnels which were locally initiated." 
    ::= { cikePhase1GWStatsEntry 19 }

cikePhase1GWInitTunnelFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-1 IKE Tunnels
        which were locally initiated and failed to activate." 
    ::= { cikePhase1GWStatsEntry 20 }

cikePhase1GWRespTunnelFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-1 IKE Tunnels
        which were remotely initiated and failed to activate." 
    ::= { cikePhase1GWStatsEntry 21 }

cikePhase1GWSysCapFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of system capacity failures
        which occurred during processing of all current 
        and previously active IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 22 }

cikePhase1GWAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of authentications which ended
        in failure by all current and previous IPsec Phase-1
        IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 23 }

cikePhase1GWDecryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of decryptions which ended
        in failure by all current and previous IPsec Phase-1
        IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 24 }

cikePhase1GWHashValidFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of hash validations which ended
        in failure by all current and previous IPsec Phase-1
        IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 25 }

cikePhase1GWNoSaFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of non-existent 'Security Association'
        failures occurred during processing of current and 
        previous IPsec Phase-1 IKE Tunnels." 
    ::= { cikePhase1GWStatsEntry 26 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- IPsec Phase-2 Group
--   
-- This group consists of:
-- 1) IPsec Phase-2 Global Statistics
-- 2) IPsec Phase-2 Tunnel Table
-- 3) IPsec Phase-2 Endpoint Table
-- 4) IPsec Phase-2 Security Protection Index Table
-- 4) IPsec Phase-2 Security Protection Index Objects
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
--   
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-2 Global Tunnel Statistics
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecGlobalStats  OBJECT IDENTIFIER
    ::= { cipSecPhaseTwo 1 }


cipSecGlobalActiveTunnels OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of currently active
        IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 1 }

cipSecGlobalPreviousTunnels OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Phase-2 Tunnels"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of previously active
        IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 2 }

cipSecGlobalInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets received by all
        current and previous IPsec Phase-2 Tunnels. 
        This value is
        accumulated BEFORE determining whether or not
        the packet should be decompressed. See also
        cipSecGlobalInOctWraps for the number of times
        this counter has wrapped." 
    ::= { cipSecGlobalStats 3 }

cipSecGlobalHcInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of
        octets received by all current and previous
        IPsec Phase-2 Tunnels. This value is accumulated
        BEFORE determining whether or not the packet
        should be decompressed." 
    ::= { cipSecGlobalStats 4 }

cipSecGlobalInOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global octets received
        counter (cipSecGlobalInOctets) has wrapped." 
    ::= { cipSecGlobalStats 5 }

cipSecGlobalInDecompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of decompressed octets received
        by all current and previous IPsec Phase-2 Tunnels.  
        This value is accumulated AFTER the packet is 
        decompressed. If compression is not being used, 
        this value will match the value of cipSecGlobalInOctets. 
        See also cipSecGlobalInDecompOctWraps
         for the number of times this counter has wrapped." 
    ::= { cipSecGlobalStats 6 }

cipSecGlobalHcInDecompOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number
        of decompressed octets received by all current 
        and previous IPsec Phase-2 Tunnels.  This value 
        is accumulated AFTER the packet is decompressed.
         If compression is not being used, this value 
         will match the value of cipSecGlobalHcInOctets." 
    ::= { cipSecGlobalStats 7 }

cipSecGlobalInDecompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global decompressed
        octets received counter
         (cipSecGlobalInDecompOctets) has wrapped." 
    ::= { cipSecGlobalStats 8 }

cipSecGlobalInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received
        by all current and previous
         IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 9 }

cipSecGlobalInDrops OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped
        during receive processing by all current and previous 
        IPsec Phase-2 Tunnels. This count does
        NOT include packets dropped due to 
        Anti-Replay processing." 
    ::= { cipSecGlobalStats 10 }

cipSecGlobalInReplayDrops OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during
        receive processing due to Anti-Replay 
        processing by all current and previous IPsec
         Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 11 }

cipSecGlobalInAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound authentication's
        performed by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 12 }

cipSecGlobalInAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound authentication's
        which ended in failure by all current and previous 
        IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 13 }

cipSecGlobalInDecrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's
        performed by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 14 }

cipSecGlobalInDecryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's
        which ended in failure by all current and 
        previous IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 15 }

cipSecGlobalOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by all
        current and previous IPsec Phase-2 Tunnels.  
        This value is accumulated AFTER determining 
        whether or not the packet should be compressed.  
        See also cipSecGlobalOutOctWraps for the
         number of times this counter has wrapped." 
    ::= { cipSecGlobalStats 16 }

cipSecGlobalHcOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number
        of octets sent by all current and previous 
        IPsec Phase-2 Tunnels.  This value is accumulated 
        AFTER determining whether or not the packet should 
        be compressed." 
    ::= { cipSecGlobalStats 17 }

cipSecGlobalOutOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global octets sent counter
        (cipSecGlobalOutOctets) has wrapped." 
    ::= { cipSecGlobalStats 18 }

cipSecGlobalOutUncompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of uncompressed octets sent
        by all current and previous IPsec Phase-2 Tunnels.  
        This value is accumulated BEFORE the packet is 
        compressed. If compression is not being used, this 
        value will match the value of cipSecGlobalOutOctets. 
        See also cipSecGlobalOutDecompOctWraps for the number 
        of times this counter has wrapped." 
    ::= { cipSecGlobalStats 19 }

cipSecGlobalHcOutUncompOctets OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of
        uncompressed octets sent by all current and previous 
        IPsec Phase-2 Tunnels.  This value is accumulated 
        BEFORE the packet is compressed.  If compression is 
        not being used, this value will match the
              value of cipSecGlobalHcOutOctets." 
    ::= { cipSecGlobalStats 20 }

cipSecGlobalOutUncompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global uncompressed
        octets sent counter (cipSecGlobalOutUncompOctets) 
        has wrapped." 
    ::= { cipSecGlobalStats 21 }

cipSecGlobalOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by all
        current and previous
         IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 22 }

cipSecGlobalOutDrops OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during send
        processing by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 23 }

cipSecGlobalOutAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound authentication's
        performed by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 24 }

cipSecGlobalOutAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound authentication's
        which ended in failure
         by all current and previous IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 25 }

cipSecGlobalOutEncrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's performed
        by all current and previous IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 26 }

cipSecGlobalOutEncryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's
        which ended in failure by all current and 
        previous IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 27 }

cipSecGlobalProtocolUseFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of protocol use failures
        which occurred during processing of all current 
        and previously active IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 28 }

cipSecGlobalNoSaFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of non-existent
        Security Association in failures which occurred 
        during processing of all current
         and previous IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 29 }

cipSecGlobalSysCapFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of system capacity failures
        which occurred during processing of all current 
        and previously active IPsec Phase-2 Tunnels." 
    ::= { cipSecGlobalStats 30 }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-2 Tunnel Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecTunnelTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CipSecTunnelEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-2 Tunnel Table.
        There is one entry in this table for 
        each active IPsec Phase-2 Tunnel."
    ::= { cipSecPhaseTwo 2 }

cipSecTunnelEntry OBJECT-TYPE
    SYNTAX          CipSecTunnelEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes
        associated with an active IPsec Phase-2 Tunnel."
    INDEX           { cipSecTunIndex } 
    ::= { cipSecTunnelTable 1 }

CipSecTunnelEntry ::= SEQUENCE {
        cipSecTunIndex               Integer32,
        cipSecTunIkeTunnelIndex      Integer32,
        cipSecTunIkeTunnelAlive      TruthValue,
        cipSecTunLocalAddr           IPSIpAddress,
        cipSecTunRemoteAddr          IPSIpAddress,
        cipSecTunKeyType             KeyType,
        cipSecTunEncapMode           EncapMode,
        cipSecTunLifeSize            Integer32,
        cipSecTunLifeTime            Integer32,
        cipSecTunActiveTime          TimeInterval,
        cipSecTunSaLifeSizeThreshold Integer32,
        cipSecTunSaLifeTimeThreshold Integer32,
        cipSecTunTotalRefreshes      Counter32,
        cipSecTunExpiredSaInstances  Counter32,
        cipSecTunCurrentSaInstances  Gauge32,
        cipSecTunInSaDiffHellmanGrp  DiffHellmanGrp,
        cipSecTunInSaEncryptAlgo     EncryptAlgo,
        cipSecTunInSaAhAuthAlgo      AuthAlgo,
        cipSecTunInSaEspAuthAlgo     AuthAlgo,
        cipSecTunInSaDecompAlgo      CompAlgo,
        cipSecTunOutSaDiffHellmanGrp DiffHellmanGrp,
        cipSecTunOutSaEncryptAlgo    EncryptAlgo,
        cipSecTunOutSaAhAuthAlgo     AuthAlgo,
        cipSecTunOutSaEspAuthAlgo    AuthAlgo,
        cipSecTunOutSaCompAlgo       CompAlgo,
        cipSecTunInOctets            Counter32,
        cipSecTunHcInOctets          Counter64,
        cipSecTunInOctWraps          Counter32,
        cipSecTunInDecompOctets      Counter32,
        cipSecTunHcInDecompOctets    Counter64,
        cipSecTunInDecompOctWraps    Counter32,
        cipSecTunInPkts              Counter32,
        cipSecTunInDropPkts          Counter32,
        cipSecTunInReplayDropPkts    Counter32,
        cipSecTunInAuths             Counter32,
        cipSecTunInAuthFails         Counter32,
        cipSecTunInDecrypts          Counter32,
        cipSecTunInDecryptFails      Counter32,
        cipSecTunOutOctets           Counter32,
        cipSecTunHcOutOctets         Counter64,
        cipSecTunOutOctWraps         Counter32,
        cipSecTunOutUncompOctets     Counter32,
        cipSecTunHcOutUncompOctets   Counter64,
        cipSecTunOutUncompOctWraps   Counter32,
        cipSecTunOutPkts             Counter32,
        cipSecTunOutDropPkts         Counter32,
        cipSecTunOutAuths            Counter32,
        cipSecTunOutAuthFails        Counter32,
        cipSecTunOutEncrypts         Counter32,
        cipSecTunOutEncryptFails     Counter32,
        cipSecTunStatus              TunnelStatus
}

cipSecTunIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The index of the IPsec Phase-2 Tunnel Table.
        The value of the index is a number which begins 
        at one and is incremented with each tunnel that 
        is created. The value of this object will wrap 
        at 2,147,483,647." 
    ::= { cipSecTunnelEntry 1 }

cipSecTunIkeTunnelIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index of the associated IPsec Phase-1
        IKE Tunnel.
         (cikeTunIndex in the cikeTunnelTable)" 
    ::= { cipSecTunnelEntry 2 }

cipSecTunIkeTunnelAlive OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "An indicator which specifies whether or not the
        IPsec Phase-1 IKE Tunnel currently exists." 
    ::= { cipSecTunnelEntry 3 }

cipSecTunLocalAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the local endpoint for the IPsec
        Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 4 }

cipSecTunRemoteAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the remote endpoint for the IPsec
        Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 5 }

cipSecTunKeyType OBJECT-TYPE
    SYNTAX          KeyType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of key used by the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 6 }

cipSecTunEncapMode OBJECT-TYPE
    SYNTAX          EncapMode
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encapsulation mode used by the
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 7 }

cipSecTunLifeSize OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "KBytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated LifeSize of the
        IPsec Phase-2 Tunnel in kilobytes." 
    ::= { cipSecTunnelEntry 8 }

cipSecTunLifeTime OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "Seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated LifeTime of the
        IPsec Phase-2 Tunnel in seconds." 
    ::= { cipSecTunnelEntry 9 }

cipSecTunActiveTime OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The length of time the IPsec Phase-2
        Tunnel has been
         active in hundredths of seconds." 
    ::= { cipSecTunnelEntry 10 }

cipSecTunSaLifeSizeThreshold OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "KBytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The security association LifeSize refresh
        threshold in kilobytes." 
    ::= { cipSecTunnelEntry 11 }

cipSecTunSaLifeTimeThreshold OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "Seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The security association LifeTime refresh
        threshold in seconds." 
    ::= { cipSecTunnelEntry 12 }

cipSecTunTotalRefreshes OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "QM Exchanges"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of security
        association refreshes performed." 
    ::= { cipSecTunnelEntry 13 }

cipSecTunExpiredSaInstances OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of security associations
        which have expired." 
    ::= { cipSecTunnelEntry 14 }

cipSecTunCurrentSaInstances OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of security associations
        which are currently active or expiring." 
    ::= { cipSecTunnelEntry 15 }

cipSecTunInSaDiffHellmanGrp OBJECT-TYPE
    SYNTAX          DiffHellmanGrp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Diffie Hellman Group used
        by the inbound security association of the 
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 16 }

cipSecTunInSaEncryptAlgo OBJECT-TYPE
    SYNTAX          EncryptAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encryption algorithm used by the inbound security
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 17 }

cipSecTunInSaAhAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the inbound
        authentication header (AH) security association of
        the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 18 }

cipSecTunInSaEspAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the inbound
        encapsulation security protocol (ESP) security 
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 19 }

cipSecTunInSaDecompAlgo OBJECT-TYPE
    SYNTAX          CompAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The decompression algorithm used by the inbound
        security association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 20 }

cipSecTunOutSaDiffHellmanGrp OBJECT-TYPE
    SYNTAX          DiffHellmanGrp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Diffie Hellman Group used by the outbound security
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 21 }

cipSecTunOutSaEncryptAlgo OBJECT-TYPE
    SYNTAX          EncryptAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encryption algorithm used by the outbound security
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 22 }

cipSecTunOutSaAhAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the outbound
        authentication header (AH) security association of
        the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 23 }

cipSecTunOutSaEspAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the inbound
        encapsulation security protocol (ESP) 
        security association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 24 }

cipSecTunOutSaCompAlgo OBJECT-TYPE
    SYNTAX          CompAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The compression algorithm used by the inbound
        security association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 25 }

cipSecTunInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets received by this IPsec
        Phase-2 Tunnel.  This value is accumulated
        BEFORE determining whether or not the packet should be
        decompressed.  See also cipSecTunInOctWraps for the
        number of times this counter has wrapped." 
    ::= { cipSecTunnelEntry 26 }

cipSecTunHcInOctets OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of octets
        received by this IPsec Phase-2 Tunnel.  This value is
        accumulated BEFORE determining whether or not the packet
        should be decompressed." 
    ::= { cipSecTunnelEntry 27 }

cipSecTunInOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the octets received counter
        (cipSecTunInOctets) has wrapped." 
    ::= { cipSecTunnelEntry 28 }

cipSecTunInDecompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of decompressed octets received
        by this IPsec Phase-2 Tunnel. This value is 
        accumulated AFTER the packet is decompressed. 
        If compression is not being
         used, this value will match the value of 
         cipSecTunInOctets.  See also cipSecTunInDecompOctWraps 
         for the number of times
         this counter has wrapped." 
    ::= { cipSecTunnelEntry 29 }

cipSecTunHcInDecompOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of decompressed
        octets received by this IPsec Phase-2 Tunnel.  This value
        is accumulated AFTER the packet is decompressed. If
        compression is not being used, this value will match the
        value of cipSecTunHcInOctets." 
    ::= { cipSecTunnelEntry 30 }

cipSecTunInDecompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the decompressed
        octets received counter
         (cipSecTunInDecompOctets) has wrapped." 
    ::= { cipSecTunnelEntry 31 }

cipSecTunInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 32 }

cipSecTunInDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped
        during receive processing by this IPsec Phase-2 
        Tunnel. This count does NOT include
         packets dropped due to Anti-Replay processing." 
    ::= { cipSecTunnelEntry 33 }

cipSecTunInReplayDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during
        receive processing due to Anti-Replay processing 
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 34 }

cipSecTunInAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound
        authentication's performed by this 
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 35 }

cipSecTunInAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound authentication's
        which ended in
         failure by this IPsec Phase-2 Tunnel ." 
    ::= { cipSecTunnelEntry 36 }

cipSecTunInDecrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's performed
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 37 }

cipSecTunInDecryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's
        which ended in failure
         by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 38 }

cipSecTunOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by this IPsec
        Phase-2 Tunnel.  This value is accumulated
        AFTER determining whether or not the packet should 
        be compressed.  See also cipSecTunOutOctWraps for
        the number of times this counter has wrapped." 
    ::= { cipSecTunnelEntry 39 }

cipSecTunHcOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of octets
        sent by this IPsec Phase-2 Tunnel.  This value is
        accumulated AFTER determining whether or not the 
        packet
        should be compressed." 
    ::= { cipSecTunnelEntry 40 }

cipSecTunOutOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the out octets counter
        (cipSecTunOutOctets) has wrapped." 
    ::= { cipSecTunnelEntry 41 }

cipSecTunOutUncompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of uncompressed octets sent
        by this IPsec Phase-2 Tunnel.  This value 
        is accumulated BEFORE the packet is compressed. 
        If compression is not being used, this value 
        will match the value of cipSecTunOutOctets.
         See also cipSecTunOutDecompOctWraps for the 
         number of times this counter has wrapped." 
    ::= { cipSecTunnelEntry 42 }

cipSecTunHcOutUncompOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number
        of uncompressed octets sent by this IPsec 
        Phase-2 Tunnel.  This value is accumulated BEFORE 
        the packet is compressed. If compression
         is not being used, this value will match the value
         of cipSecTunHcOutOctets." 
    ::= { cipSecTunnelEntry 43 }

cipSecTunOutUncompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the uncompressed octets sent
        counter (cipSecTunOutUncompOctets) has wrapped." 
    ::= { cipSecTunnelEntry 44 }

cipSecTunOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by this
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 45 }

cipSecTunOutDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during
        send processing by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 46 }

cipSecTunOutAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound authentication's performed
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 47 }

cipSecTunOutAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound
        authentication's which ended in failure 
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 48 }

cipSecTunOutEncrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's performed
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 49 }

cipSecTunOutEncryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's
        which ended in failure by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelEntry 50 }

cipSecTunStatus OBJECT-TYPE
    SYNTAX          TunnelStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The status of the MIB table row.

        This object can be used to bring the tunnel down
        by setting value of this object to destroy(2).
        When the value is set to destroy(2), the SA
        bundle is destroyed and this row is deleted
        from this table.

        When this MIB value is queried, the value of
        active(1) is always returned, if the instance 
        exists.

        This object cannot be used to create a MIB 
        table row." 
    ::= { cipSecTunnelEntry 51 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-2 Tunnel Endpoint Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecEndPtTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CipSecEndPtEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-2 Tunnel Endpoint Table.
        This table contains an entry for each 
        active endpoint associated with an IPsec
         Phase-2 Tunnel."
    ::= { cipSecPhaseTwo 3 }

cipSecEndPtEntry OBJECT-TYPE
    SYNTAX          CipSecEndPtEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An IPsec Phase-2 Tunnel Endpoint entry."
    INDEX           {
                        cipSecTunIndex,
                        cipSecEndPtIndex
                    } 
    ::= { cipSecEndPtTable 1 }

CipSecEndPtEntry ::= SEQUENCE {
        cipSecEndPtIndex          Integer32,
        cipSecEndPtLocalName      DisplayString,
        cipSecEndPtLocalType      EndPtType,
        cipSecEndPtLocalAddr1     IPSIpAddress,
        cipSecEndPtLocalAddr2     IPSIpAddress,
        cipSecEndPtLocalProtocol  Integer32,
        cipSecEndPtLocalPort      Integer32,
        cipSecEndPtRemoteName     DisplayString,
        cipSecEndPtRemoteType     EndPtType,
        cipSecEndPtRemoteAddr1    IPSIpAddress,
        cipSecEndPtRemoteAddr2    IPSIpAddress,
        cipSecEndPtRemoteProtocol Integer32,
        cipSecEndPtRemotePort     Integer32
}

cipSecEndPtIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The number of the Endpoint associated with the
        IPsec Phase-2 Tunnel Table.  The value of this
        index is a number which begins at one and 
        is incremented with each Endpoint associated 
        with an IPsec Phase-2 Tunnel.
        The value of this object will wrap at 2,147,483,647." 
    ::= { cipSecEndPtEntry 1 }

cipSecEndPtLocalName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the local Endpoint." 
    ::= { cipSecEndPtEntry 2 }

cipSecEndPtLocalType OBJECT-TYPE
    SYNTAX          EndPtType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of identity for the local Endpoint.
        Possible values are:
        1) a single IP address, or
        2) an IP address range, or
        3) an IP subnet." 
    ::= { cipSecEndPtEntry 3 }

cipSecEndPtLocalAddr1 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The local Endpoint's first IP address specification.

        If the local Endpoint type is single IP address, 
        then this is the value of the IP address.

        If the local Endpoint type is IP subnet, then this
        is the value of the subnet.

        If the local Endpoint type is IP address range, 
        then this is the value of beginning IP address 
        of the range." 
    ::= { cipSecEndPtEntry 4 }

cipSecEndPtLocalAddr2 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The local Endpoint's second IP address specification.

        If the local Endpoint type is single IP address, 
        then this is the value of the IP address.

        If the local Endpoint type is IP subnet, then this
        is the value of the subnet mask.

        If the local Endpoint type is IP address range, 
        then this is the value of ending IP address 
        of the range." 
    ::= { cipSecEndPtEntry 5 }

cipSecEndPtLocalProtocol OBJECT-TYPE
    SYNTAX          Integer32 (0..255 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The protocol number of the local Endpoint's traffic." 
    ::= { cipSecEndPtEntry 6 }

cipSecEndPtLocalPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The port number of the local Endpoint's traffic." 
    ::= { cipSecEndPtEntry 7 }

cipSecEndPtRemoteName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the remote Endpoint." 
    ::= { cipSecEndPtEntry 8 }

cipSecEndPtRemoteType OBJECT-TYPE
    SYNTAX          EndPtType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of identity for the remote Endpoint.
        Possible values are:
        1) a single IP address, or
        2) an IP address range, or
        3) an IP subnet." 
    ::= { cipSecEndPtEntry 9 }

cipSecEndPtRemoteAddr1 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The remote Endpoint's first IP address specification.

        If the remote Endpoint type is single IP address, 
        then this is the value of the IP address.

        If the remote Endpoint type is IP subnet, then this
        is the value of the subnet.

        If the remote Endpoint type is IP address range, 
        then this is the value of beginning IP address 
        of the range." 
    ::= { cipSecEndPtEntry 10 }

cipSecEndPtRemoteAddr2 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The remote Endpoint's second IP address specification.

        If the remote Endpoint type is single IP address, 
        then this is the value of the IP address.

        If the remote Endpoint type is IP subnet, then this
        is the value of the subnet mask.

        If the remote Endpoint type is IP address range, 
        then this is the value of ending IP address of 
        the range." 
    ::= { cipSecEndPtEntry 11 }

cipSecEndPtRemoteProtocol OBJECT-TYPE
    SYNTAX          Integer32 (0..255 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The protocol number of the remote Endpoint's traffic." 
    ::= { cipSecEndPtEntry 12 }

cipSecEndPtRemotePort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The port number of the remote Endpoint's traffic." 
    ::= { cipSecEndPtEntry 13 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-2 Security Protection Index Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecSpiTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CipSecSpiEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-2 Security Protection Index Table.
        This table contains an entry for each active 
        and expiring security
         association."
    ::= { cipSecPhaseTwo 4 }

cipSecSpiEntry OBJECT-TYPE
    SYNTAX          CipSecSpiEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes associated with
        active and expiring IPsec Phase-2 
        security associations."
    INDEX           {
                        cipSecTunIndex,
                        cipSecSpiIndex
                    } 
    ::= { cipSecSpiTable 1 }

CipSecSpiEntry ::= SEQUENCE {
        cipSecSpiIndex     Integer32,
        cipSecSpiDirection INTEGER ,
        cipSecSpiValue     Unsigned32,
        cipSecSpiProtocol  INTEGER ,
        cipSecSpiStatus    INTEGER 
}

cipSecSpiIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The number of the SPI associated with the
        Phase-2 Tunnel Table.  The value of this 
        index is a number which begins at one and is 
        incremented with each SPI associated with an 
        IPsec Phase-2 Tunnel.  The value of this 
        object will wrap at 2,147,483,647." 
    ::= { cipSecSpiEntry 1 }

cipSecSpiDirection OBJECT-TYPE
    SYNTAX          INTEGER  {
                        in(1),
                        out(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The direction of the SPI." 
    ::= { cipSecSpiEntry 2 }

cipSecSpiValue OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the SPI." 
    ::= { cipSecSpiEntry 3 }

cipSecSpiProtocol OBJECT-TYPE
    SYNTAX          INTEGER  {
                        ah(1),
                        esp(2),
                        ipcomp(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The protocol of the SPI." 
    ::= { cipSecSpiEntry 4 }

cipSecSpiStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        active(1),
                        expiring(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The status of the SPI." 
    ::= { cipSecSpiEntry 5 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
--   
-- cipSecPhase2GWStatsTable
--   
-- Gateway Phase-2 IPsec stats information
--   
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecPhase2GWStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CipSecPhase2GWStatsEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Phase-2 IPsec stats information is included in this table.
        Each entry is related to a specific gateway which is 
        identified by 'cmgwIndex'"
    ::= { cipSecPhaseTwo 5 }

cipSecPhase2GWStatsEntry OBJECT-TYPE
    SYNTAX          CipSecPhase2GWStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes of an Phase-2 IPsec stats
        information for the related gateway.

        There is only one entry for each gateway. The entry 
        is created when a gateway up and cannot be deleted."
    INDEX           { cmgwIndex } 
    ::= { cipSecPhase2GWStatsTable 1 }

CipSecPhase2GWStatsEntry ::= SEQUENCE {
        cipSecPhase2GWActiveTunnels     Gauge32,
        cipSecPhase2GWPreviousTunnels   Counter32,
        cipSecPhase2GWInOctets          Counter32,
        cipSecPhase2GWInOctWraps        Counter32,
        cipSecPhase2GWInDecompOctets    Counter32,
        cipSecPhase2GWInDecompOctWraps  Counter32,
        cipSecPhase2GWInPkts            Counter32,
        cipSecPhase2GWInDrops           Counter32,
        cipSecPhase2GWInReplayDrops     Counter32,
        cipSecPhase2GWInAuths           Counter32,
        cipSecPhase2GWInAuthFails       Counter32,
        cipSecPhase2GWInDecrypts        Counter32,
        cipSecPhase2GWInDecryptFails    Counter32,
        cipSecPhase2GWOutOctets         Counter32,
        cipSecPhase2GWOutOctWraps       Counter32,
        cipSecPhase2GWOutUncompOctets   Counter32,
        cipSecPhase2GWOutUncompOctWraps Counter32,
        cipSecPhase2GWOutPkts           Counter32,
        cipSecPhase2GWOutDrops          Counter32,
        cipSecPhase2GWOutAuths          Counter32,
        cipSecPhase2GWOutAuthFails      Counter32,
        cipSecPhase2GWOutEncrypts       Counter32,
        cipSecPhase2GWOutEncryptFails   Counter32,
        cipSecPhase2GWProtocolUseFails  Counter32,
        cipSecPhase2GWNoSaFails         Counter32,
        cipSecPhase2GWSysCapFails       Counter32
}

cipSecPhase2GWActiveTunnels OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of currently active
        IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 1 }

cipSecPhase2GWPreviousTunnels OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Phase-2 Tunnels"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of previously active
        IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 2 }

cipSecPhase2GWInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets received by all
        current and previous IPsec Phase-2 Tunnels. 
        This value is accumulated BEFORE determining 
        whether or not the packet should be decompressed. 
        See also cipSecGlobalInOctWraps for the number
        of times this counter has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 3 }

cipSecPhase2GWInOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global octets received
        counter (cipSecGlobalInOctets) has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 4 }

cipSecPhase2GWInDecompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of decompressed octets received
        by all current and previous IPsec Phase-2 Tunnels.  
        This value is accumulated AFTER the packet is 
        decompressed. If compression is not being used, 
        this value will match the value of cipSecGlobalInOctets. 
        See also cipSecGlobalInDecompOctWraps
        for the number of times this counter has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 5 }

cipSecPhase2GWInDecompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global decompressed
        octets received counter (cipSecGlobalInDecompOctets) 
        has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 6 }

cipSecPhase2GWInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received
        by all current and previous IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 7 }

cipSecPhase2GWInDrops OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped
        during receive processing by all current and previous 
        IPsec Phase-2 Tunnels. This count does NOT include 
        packets dropped due to Anti-Replay processing." 
    ::= { cipSecPhase2GWStatsEntry 8 }

cipSecPhase2GWInReplayDrops OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during
        receive processing due to Anti-Replay 
        processing by all current and previous IPsec
        Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 9 }

cipSecPhase2GWInAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound authentication's
        performed by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 10 }

cipSecPhase2GWInAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound authentication's
        which ended in failure by all current and previous 
        IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 11 }

cipSecPhase2GWInDecrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's
        performed by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 12 }

cipSecPhase2GWInDecryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's
        which ended in failure by all current and 
        previous IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 13 }

cipSecPhase2GWOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by all
        current and previous IPsec Phase-2 Tunnels.  
        This value is accumulated AFTER determining 
        whether or not the packet should be compressed.  
        See also cipSecGlobalOutOctWraps for the
        number of times this counter has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 14 }

cipSecPhase2GWOutOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global octets sent counter
        (cipSecGlobalOutOctets) has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 15 }

cipSecPhase2GWOutUncompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of uncompressed octets sent
        by all current and previous IPsec Phase-2 Tunnels.  
        This value is accumulated BEFORE the packet is 
        compressed. If compression is not being used, this 
        value will match the value of cipSecGlobalOutOctets. 
        See also cipSecGlobalOutDecompOctWraps for the number 
        of times this counter has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 16 }

cipSecPhase2GWOutUncompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the global uncompressed
        octets sent counter (cipSecGlobalOutUncompOctets) 
        has wrapped." 
    ::= { cipSecPhase2GWStatsEntry 17 }

cipSecPhase2GWOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by all
        current and previous IPsec Phase-2 
        Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 18 }

cipSecPhase2GWOutDrops OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during send
        processing by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 19 }

cipSecPhase2GWOutAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound authentication's
        performed by all current and previous IPsec 
        Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 20 }

cipSecPhase2GWOutAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound authentication's
        which ended in failure
        by all current and previous IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 21 }

cipSecPhase2GWOutEncrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's performed
        by all current and previous IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 22 }

cipSecPhase2GWOutEncryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's
        which ended in failure by all current and 
        previous IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 23 }

cipSecPhase2GWProtocolUseFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of protocol use failures
        which occurred during processing of all current 
        and previously active IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 24 }

cipSecPhase2GWNoSaFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of non-existent
        Security Association in failures which occurred 
        during processing of all current
        and previous IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 25 }

cipSecPhase2GWSysCapFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of system capacity failures
        which occurred during processing of all current 
        and previously active IPsec Phase-2 Tunnels." 
    ::= { cipSecPhase2GWStatsEntry 26 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec History Group
--   
-- This group consists of a:
-- 1) IPsec History Global Objects
-- 2) IPsec Phase-1 History Objects
-- 3) IPsec Phase-2 History Objects
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecHistGlobal  OBJECT IDENTIFIER
    ::= { cipSecHistory 1 }

cipSecHistPhaseOne  OBJECT IDENTIFIER
    ::= { cipSecHistory 2 }

cipSecHistPhaseTwo  OBJECT IDENTIFIER
    ::= { cipSecHistory 3 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- IPsec History Global Control Objects
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecHistGlobalCntl  OBJECT IDENTIFIER
    ::= { cipSecHistGlobal 1 }


cipSecHistTableSize OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The window size of the IPsec Phase-1 and Phase-2
        History Tables.

        The IPsec Phase-1 and Phase-2 History Tables are
        implemented as a sliding window in which only the
        last n entries are maintained.  This object is used
        specify the number of entries which will be 
        maintained in the IPsec Phase-1 and 
        Phase-2 History Tables.

        An implementation may choose suitable minimum and 
        maximum values for this element based on the local 
        policy and available resources. If an SNMP SET request 
        specifies a value outside this window for this element, 
        a BAD VALUE may be returned." 
    ::= { cipSecHistGlobalCntl 1 }

cipSecHistCheckPoint OBJECT-TYPE
    SYNTAX          INTEGER  {
                        ready(1),
                        checkPoint(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The current state of check point processing.

        This object will return ready when the agent is 
        ready to create on-demand history entries for 
        active IPsec Tunnels or checkPoint when the 
        agent is currently creating on-demand history 
        entries for active IPsec Tunnels.

        By setting this value to checkPoint, the agent 
        will create:
        a) an entry in the IPsec Phase-1 Tunnel History 
           for each active IPsec Phase-1 Tunnel and
        b) an entry in the IPsec Phase-2 Tunnel History 
           Table and an entry in the IPsec Phase-2 
           Tunnel EndPoint History Table
           for each active IPsec Phase-2 Tunnel." 
    ::= { cipSecHistGlobalCntl 2 }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-1 Tunnel History Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cikeTunnelHistTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CikeTunnelHistEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-1 Internet Key Exchange Tunnel
        History Table.  This table is implemented as a 
        sliding window in which only the last n entries 
        are maintained.  The maximum number of entries
         is specified by the cipSecHistTableSize object."
    ::= { cipSecHistPhaseOne 1 }

cikeTunnelHistEntry OBJECT-TYPE
    SYNTAX          CikeTunnelHistEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes
        associated with a previously active IPsec 
        Phase-1 IKE Tunnel."
    INDEX           { cikeTunHistIndex } 
    ::= { cikeTunnelHistTable 1 }

CikeTunnelHistEntry ::= SEQUENCE {
        cikeTunHistIndex              Integer32,
        cikeTunHistTermReason         INTEGER ,
        cikeTunHistActiveIndex        Integer32,
        cikeTunHistPeerLocalType      IkePeerType,
        cikeTunHistPeerLocalValue     DisplayString,
        cikeTunHistPeerIntIndex       Integer32,
        cikeTunHistPeerRemoteType     IkePeerType,
        cikeTunHistPeerRemoteValue    DisplayString,
        cikeTunHistLocalAddr          IPSIpAddress,
        cikeTunHistLocalName          DisplayString,
        cikeTunHistRemoteAddr         IPSIpAddress,
        cikeTunHistRemoteName         DisplayString,
        cikeTunHistNegoMode           IkeNegoMode,
        cikeTunHistDiffHellmanGrp     DiffHellmanGrp,
        cikeTunHistEncryptAlgo        EncryptAlgo,
        cikeTunHistHashAlgo           IkeHashAlgo,
        cikeTunHistAuthMethod         IkeAuthMethod,
        cikeTunHistLifeTime           Integer32,
        cikeTunHistStartTime          TimeStamp,
        cikeTunHistActiveTime         TimeInterval,
        cikeTunHistTotalRefreshes     Counter32,
        cikeTunHistTotalSas           Counter32,
        cikeTunHistInOctets           Counter32,
        cikeTunHistInPkts             Counter32,
        cikeTunHistInDropPkts         Counter32,
        cikeTunHistInNotifys          Counter32,
        cikeTunHistInP2Exchgs         Counter32,
        cikeTunHistInP2ExchgInvalids  Counter32,
        cikeTunHistInP2ExchgRejects   Counter32,
        cikeTunHistInP2SaDelRequests  Counter32,
        cikeTunHistOutOctets          Counter32,
        cikeTunHistOutPkts            Counter32,
        cikeTunHistOutDropPkts        Counter32,
        cikeTunHistOutNotifys         Counter32,
        cikeTunHistOutP2Exchgs        Counter32,
        cikeTunHistOutP2ExchgInvalids Counter32,
        cikeTunHistOutP2ExchgRejects  Counter32,
        cikeTunHistOutP2SaDelRequests Counter32
}

cikeTunHistIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The index of the IPsec Phase-1 IKE Tunnel History
        Table.  The value of the index is a number which 
        begins at one and is incremented with each 
        tunnel that ends. The value of this object 
        will wrap at 2,147,483,647." 
    ::= { cikeTunnelHistEntry 1 }

cikeTunHistTermReason OBJECT-TYPE
    SYNTAX          INTEGER  {
                        other(1),
                        normal(2),
                        operRequest(3),
                        peerDelRequest(4),
                        peerLost(5),
                        localFailure(6),
                        checkPointReg(7)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The reason the IPsec Phase-1 IKE Tunnel was terminated.
        Possible reasons include:
        1 = other
        2 = normal termination
        3 = operator request
        4 = peer delete request was received
        5 = contact with peer was lost
        6 = local failure occurred.
        7 = operator initiated check point request" 
    ::= { cikeTunnelHistEntry 2 }

cikeTunHistActiveIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index of the previously active IPsec
        Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 3 }

cikeTunHistPeerLocalType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of local peer identity.  The local peer
        may be identified by:
         1. an IP address, or
         2. a host name." 
    ::= { cikeTunnelHistEntry 4 }

cikeTunHistPeerLocalValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the local peer identity.

        If the local peer type is an IP Address, then this
        is the IP Address used to identify the local peer.

        If the local peer type is a host name, then this is
        the host name used to identify the local peer." 
    ::= { cikeTunnelHistEntry 5 }

cikeTunHistPeerIntIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The internal index of the local-remote peer
        association.  This internal index is used to 
        uniquely identify multiple associations between 
        the local and remote peer." 
    ::= { cikeTunnelHistEntry 6 }

cikeTunHistPeerRemoteType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of remote peer identity.  The remote
        peer may be identified by:
         1. an IP address, or
         2. a host name." 
    ::= { cikeTunnelHistEntry 7 }

cikeTunHistPeerRemoteValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the remote peer identity.

        If the remote peer type is an IP Address, then this
        is the IP Address used to identify the remote peer.

        If the remote peer type is a host name, then this is
        the host name used to identify the remote peer." 
    ::= { cikeTunnelHistEntry 8 }

cikeTunHistLocalAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the local endpoint for the IPsec
        Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 9 }

cikeTunHistLocalName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the local IP address for
        the IPsec Phase-1 IKE Tunnel. If the DNS 
        name associated with the local tunnel endpoint 
        is not known, then the value of this
         object will be a NULL string." 
    ::= { cikeTunnelHistEntry 10 }

cikeTunHistRemoteAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the remote endpoint for the IPsec
        Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 11 }

cikeTunHistRemoteName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the remote IP address of IPsec Phase-1
        IKE Tunnel. If the DNS name associated with the remote
        tunnel endpoint is not known, then the value of this
        object will be a NULL string." 
    ::= { cikeTunnelHistEntry 12 }

cikeTunHistNegoMode OBJECT-TYPE
    SYNTAX          IkeNegoMode
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiation mode of the IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 13 }

cikeTunHistDiffHellmanGrp OBJECT-TYPE
    SYNTAX          DiffHellmanGrp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Diffie Hellman Group used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelHistEntry 14 }

cikeTunHistEncryptAlgo OBJECT-TYPE
    SYNTAX          EncryptAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encryption algorithm used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelHistEntry 15 }

cikeTunHistHashAlgo OBJECT-TYPE
    SYNTAX          IkeHashAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The hash algorithm used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelHistEntry 16 }

cikeTunHistAuthMethod OBJECT-TYPE
    SYNTAX          IkeAuthMethod
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication method used in IPsec Phase-1 IKE
        negotiations." 
    ::= { cikeTunnelHistEntry 17 }

cikeTunHistLifeTime OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated LifeTime of the IPsec Phase-1 IKE Tunnel
        in seconds." 
    ::= { cikeTunnelHistEntry 18 }

cikeTunHistStartTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of sysUpTime in hundredths of seconds
        when the IPsec Phase-1 IKE tunnel was started." 
    ::= { cikeTunnelHistEntry 19 }

cikeTunHistActiveTime OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The length of time the IPsec Phase-1 IKE tunnel was been
        active in hundredths of seconds." 
    ::= { cikeTunnelHistEntry 20 }

cikeTunHistTotalRefreshes OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "QM Exchanges"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of security associations
        refreshes performed." 
    ::= { cikeTunnelHistEntry 21 }

cikeTunHistTotalSas OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of security associations
        used during the
         life of the IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 22 }

cikeTunHistInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets
        received by this IPsec Phase-1
         IKE Tunnel." 
    ::= { cikeTunnelHistEntry 23 }

cikeTunHistInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received
        by this IPsec Phase-1
         IKE Tunnel." 
    ::= { cikeTunnelHistEntry 24 }

cikeTunHistInDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped
        by this IPsec Phase-1
         IKE Tunnel during receive processing." 
    ::= { cikeTunnelHistEntry 25 }

cikeTunHistInNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys received
        by this IPsec Phase-1
         IKE Tunnel." 
    ::= { cikeTunnelHistEntry 26 }

cikeTunHistInP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2
        exchanges received by
         this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 27 }

cikeTunHistInP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2
        exchanges received and
         found to be invalid by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 28 }

cikeTunHistInP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2
        exchanges received and
         rejected by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 29 }

cikeTunHistInP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 security association
        delete requests received by this IPsec 
        Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 30 }

cikeTunHistOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by this IPsec Phase-1
        IKE Tunnel." 
    ::= { cikeTunnelHistEntry 31 }

cikeTunHistOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by this IPsec Phase-1
        IKE Tunnel." 
    ::= { cikeTunnelHistEntry 32 }

cikeTunHistOutDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped
        by this IPsec Phase-1
         IKE Tunnel during send processing." 
    ::= { cikeTunnelHistEntry 33 }

cikeTunHistOutNotifys OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of notifys sent by this IPsec Phase-1
        IKE Tunnel." 
    ::= { cikeTunnelHistEntry 34 }

cikeTunHistOutP2Exchgs OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges sent by
        this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 35 }

cikeTunHistOutP2ExchgInvalids OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges sent and
        found to be invalid by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 36 }

cikeTunHistOutP2ExchgRejects OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SA Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 exchanges sent and
        rejected by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 37 }

cikeTunHistOutP2SaDelRequests OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Notification Payloads"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of IPsec Phase-2 security association
        delete requests sent by this IPsec Phase-1 IKE Tunnel." 
    ::= { cikeTunnelHistEntry 38 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-2 Tunnel History Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecTunnelHistTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CipSecTunnelHistEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-2 Tunnel History Table.
        This table is implemented as a sliding 
        window in which only the
        last n entries are maintained.  The maximum number 
        of entries
        is specified by the cipSecHistTableSize object."
    ::= { cipSecHistPhaseTwo 1 }

cipSecTunnelHistEntry OBJECT-TYPE
    SYNTAX          CipSecTunnelHistEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes associated with
        a previously active IPsec Phase-2 Tunnel."
    INDEX           { cipSecTunHistIndex } 
    ::= { cipSecTunnelHistTable 1 }

CipSecTunnelHistEntry ::= SEQUENCE {
        cipSecTunHistIndex               Integer32,
        cipSecTunHistTermReason          INTEGER ,
        cipSecTunHistActiveIndex         Integer32,
        cipSecTunHistIkeTunnelIndex      Integer32,
        cipSecTunHistLocalAddr           IPSIpAddress,
        cipSecTunHistRemoteAddr          IPSIpAddress,
        cipSecTunHistKeyType             KeyType,
        cipSecTunHistEncapMode           EncapMode,
        cipSecTunHistLifeSize            Integer32,
        cipSecTunHistLifeTime            Integer32,
        cipSecTunHistStartTime           TimeStamp,
        cipSecTunHistActiveTime          TimeInterval,
        cipSecTunHistTotalRefreshes      Counter32,
        cipSecTunHistTotalSas            Counter32,
        cipSecTunHistInSaDiffHellmanGrp  DiffHellmanGrp,
        cipSecTunHistInSaEncryptAlgo     EncryptAlgo,
        cipSecTunHistInSaAhAuthAlgo      AuthAlgo,
        cipSecTunHistInSaEspAuthAlgo     AuthAlgo,
        cipSecTunHistInSaDecompAlgo      CompAlgo,
        cipSecTunHistOutSaDiffHellmanGrp DiffHellmanGrp,
        cipSecTunHistOutSaEncryptAlgo    EncryptAlgo,
        cipSecTunHistOutSaAhAuthAlgo     AuthAlgo,
        cipSecTunHistOutSaEspAuthAlgo    AuthAlgo,
        cipSecTunHistOutSaCompAlgo       CompAlgo,
        cipSecTunHistInOctets            Counter32,
        cipSecTunHistHcInOctets          Counter64,
        cipSecTunHistInOctWraps          Counter32,
        cipSecTunHistInDecompOctets      Counter32,
        cipSecTunHistHcInDecompOctets    Counter64,
        cipSecTunHistInDecompOctWraps    Counter32,
        cipSecTunHistInPkts              Counter32,
        cipSecTunHistInDropPkts          Counter32,
        cipSecTunHistInReplayDropPkts    Counter32,
        cipSecTunHistInAuths             Counter32,
        cipSecTunHistInAuthFails         Counter32,
        cipSecTunHistInDecrypts          Counter32,
        cipSecTunHistInDecryptFails      Counter32,
        cipSecTunHistOutOctets           Counter32,
        cipSecTunHistHcOutOctets         Counter64,
        cipSecTunHistOutOctWraps         Counter32,
        cipSecTunHistOutUncompOctets     Counter32,
        cipSecTunHistHcOutUncompOctets   Counter64,
        cipSecTunHistOutUncompOctWraps   Counter32,
        cipSecTunHistOutPkts             Counter32,
        cipSecTunHistOutDropPkts         Counter32,
        cipSecTunHistOutAuths            Counter32,
        cipSecTunHistOutAuthFails        Counter32,
        cipSecTunHistOutEncrypts         Counter32,
        cipSecTunHistOutEncryptFails     Counter32
}

cipSecTunHistIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The index of the IPsec Phase-2 Tunnel History Table.
        The value of the index is a number which 
        begins at one and is incremented with each tunnel 
        that ends. The value
        of this object will wrap at 2,147,483,647." 
    ::= { cipSecTunnelHistEntry 1 }

cipSecTunHistTermReason OBJECT-TYPE
    SYNTAX          INTEGER  {
                        other(1),
                        normal(2),
                        operRequest(3),
                        peerDelRequest(4),
                        peerLost(5),
                        seqNumRollOver(6),
                        checkPointReq(7)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The reason the IPsec Phase-2 Tunnel was terminated.
        Possible reasons include:
        1 = other
        2 = normal termination
        3 = operator request
        4 = peer delete request was received
        5 = contact with peer was lost
        6 = local failure occurred
        7 = operator initiated check point request" 
    ::= { cipSecTunnelHistEntry 2 }

cipSecTunHistActiveIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index of the previously active
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 3 }

cipSecTunHistIkeTunnelIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index of the associated IPsec Phase-1 Tunnel
        (cikeTunIndex in the cikeTunnelTable)." 
    ::= { cipSecTunnelHistEntry 4 }

cipSecTunHistLocalAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the local endpoint for the IPsec
        Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 5 }

cipSecTunHistRemoteAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the remote endpoint for the IPsec
        Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 6 }

cipSecTunHistKeyType OBJECT-TYPE
    SYNTAX          KeyType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of key used by the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 7 }

cipSecTunHistEncapMode OBJECT-TYPE
    SYNTAX          EncapMode
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encapsulation mode used by the
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 8 }

cipSecTunHistLifeSize OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "KBytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated LifeSize of the IPsec Phase-2 Tunnel in
        kilobytes." 
    ::= { cipSecTunnelHistEntry 9 }

cipSecTunHistLifeTime OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    UNITS           "Seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The negotiated LifeTime of the IPsec Phase-2 Tunnel in
        seconds." 
    ::= { cipSecTunnelHistEntry 10 }

cipSecTunHistStartTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of sysUpTime in hundredths of seconds
        when the IPsec Phase-2 Tunnel was started." 
    ::= { cipSecTunnelHistEntry 11 }

cipSecTunHistActiveTime OBJECT-TYPE
    SYNTAX          TimeInterval
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The length of time the IPsec Phase-2 Tunnel has been
        active in hundredths of seconds." 
    ::= { cipSecTunnelHistEntry 12 }

cipSecTunHistTotalRefreshes OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "QM Exchanges"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of security association refreshes
        performed." 
    ::= { cipSecTunnelHistEntry 13 }

cipSecTunHistTotalSas OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "SAs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of security associations used
        during the
         life of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 14 }

cipSecTunHistInSaDiffHellmanGrp OBJECT-TYPE
    SYNTAX          DiffHellmanGrp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Diffie Hellman Group used by the inbound security
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 15 }

cipSecTunHistInSaEncryptAlgo OBJECT-TYPE
    SYNTAX          EncryptAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encryption algorithm used by the inbound security
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 16 }

cipSecTunHistInSaAhAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the inbound
        authentication header (AH) security association of
        the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 17 }

cipSecTunHistInSaEspAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the inbound
        encapsulation security protocol (ESP) 
        security association of
        the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 18 }

cipSecTunHistInSaDecompAlgo OBJECT-TYPE
    SYNTAX          CompAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The decompression algorithm used by the inbound
        security association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 19 }

cipSecTunHistOutSaDiffHellmanGrp OBJECT-TYPE
    SYNTAX          DiffHellmanGrp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Diffie Hellman Group used by the outbound security
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 20 }

cipSecTunHistOutSaEncryptAlgo OBJECT-TYPE
    SYNTAX          EncryptAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The encryption algorithm used by the outbound security
        association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 21 }

cipSecTunHistOutSaAhAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the outbound
        authentication header (AH) security association of
        the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 22 }

cipSecTunHistOutSaEspAuthAlgo OBJECT-TYPE
    SYNTAX          AuthAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The authentication algorithm used by the inbound
        encapsulation security protocol (ESP) 
        security association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 23 }

cipSecTunHistOutSaCompAlgo OBJECT-TYPE
    SYNTAX          CompAlgo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The compression algorithm used by the inbound
        security association of the IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 24 }

cipSecTunHistInOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets received by this IPsec
        Phase-2 Tunnel.  This value is accumulated
        BEFORE determining whether or not the packet should 
        be decompressed.  See also cipSecTunInOctWraps for 
        the number of times this counter has wrapped." 
    ::= { cipSecTunnelHistEntry 25 }

cipSecTunHistHcInOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of octets
        received by this IPsec Phase-2 Tunnel.  This value is
        accumulated BEFORE determining whether or not 
        the packet should be decompressed." 
    ::= { cipSecTunnelHistEntry 26 }

cipSecTunHistInOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the octets received counter
        (cipSecTunInOctets) has wrapped." 
    ::= { cipSecTunnelHistEntry 27 }

cipSecTunHistInDecompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of decompressed octets received by this
        IPsec Phase-2 Tunnel.  This value is accumulated AFTER
        the packet is decompressed. If compression is not being
        used, this value will match the value of cipSecTunHistInOctets.
        See also cipSecTunInDecompOctWraps for the number of times
        this counter has wrapped." 
    ::= { cipSecTunnelHistEntry 28 }

cipSecTunHistHcInDecompOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of decompressed
        octets received by this IPsec Phase-2 Tunnel.  This value
        is accumulated AFTER the packet is decompressed. If
        compression is not being used, this value will match the
        value of cipSecTunHistHcInOctets." 
    ::= { cipSecTunnelHistEntry 29 }

cipSecTunHistInDecompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the decompressed octets
        received counter (cipSecTunInDecompOctets) has wrapped." 
    ::= { cipSecTunnelHistEntry 30 }

cipSecTunHistInPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets received by this
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 31 }

cipSecTunHistInDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during
        receive processing by this IPsec Phase-2 Tunnel. 
        This count does NOT include packets
         dropped due to Anti-Replay processing." 
    ::= { cipSecTunnelHistEntry 32 }

cipSecTunHistInReplayDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped during
        receive processing due to Anti-Replay processing 
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 33 }

cipSecTunHistInAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound authentication's
        performed
         by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 34 }

cipSecTunHistInAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound authentication's
        which ended in
         failure by this IPsec Phase-2 Tunnel ." 
    ::= { cipSecTunnelHistEntry 35 }

cipSecTunHistInDecrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's performed
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 36 }

cipSecTunHistInDecryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of inbound decryption's
        which ended in failure
         by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 37 }

cipSecTunHistOutOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of octets sent by this IPsec
        Phase-2 Tunnel.  This value is accumulated
        AFTER determining whether or not the 
        packet should be
        compressed.  See also cipSecTunOutOctWraps for the
        number of times this counter has wrapped." 
    ::= { cipSecTunnelHistEntry 38 }

cipSecTunHistHcOutOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total number of octets
        sent by this IPsec Phase-2 Tunnel.  This value 
        is accumulated AFTER determining whether or not 
        the packet should be
        compressed." 
    ::= { cipSecTunnelHistEntry 39 }

cipSecTunHistOutOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the octets sent counter
        (cipSecTunOutOctets) has wrapped." 
    ::= { cipSecTunnelHistEntry 40 }

cipSecTunHistOutUncompOctets OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of uncompressed octets sent by this
        IPsec Phase-2 Tunnel.  This value is accumulated BEFORE
        the packet is compressed. If compression is not being
        used, this value will match the value of 
        cipSecTunHistOutOctets.  See also 
        cipSecTunOutDecompOctWraps for the number of times
        this counter has wrapped." 
    ::= { cipSecTunnelHistEntry 41 }

cipSecTunHistHcOutUncompOctets OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "Octets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "A high capacity count of the total
        number of uncompressed octets sent by this 
        IPsec Phase-2 Tunnel.  This value is accumulated 
        BEFORE the packet is compressed. If compression
        is not being used, this value will match the value of
        cipSecTunHistHcOutOctets." 
    ::= { cipSecTunnelHistEntry 42 }

cipSecTunHistOutUncompOctWraps OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Integral units"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of times the uncompressed octets sent counter
        (cipSecTunOutUncompOctets) has wrapped." 
    ::= { cipSecTunnelHistEntry 43 }

cipSecTunHistOutPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets sent by this
        IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 44 }

cipSecTunHistOutDropPkts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of packets dropped
        during send processing
         by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 45 }

cipSecTunHistOutAuths OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Events"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound authentication's performed
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 46 }

cipSecTunHistOutAuthFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound authentication's
        which ended in
         failure by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 47 }

cipSecTunHistOutEncrypts OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Packets"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's performed
        by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 48 }

cipSecTunHistOutEncryptFails OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "Failures"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The total number of outbound encryption's
        which ended in failure
         by this IPsec Phase-2 Tunnel." 
    ::= { cipSecTunnelHistEntry 49 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-2 Tunnel Endpoint History Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecEndPtHistTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CipSecEndPtHistEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-2 Tunnel Endpoint History Table.
        This table is implemented as a 
        sliding window in which only the
        last n entries are maintained.  
        The maximum number of entries
        is specified by the cipSecHistTableSize object."
    ::= { cipSecHistPhaseTwo 2 }

cipSecEndPtHistEntry OBJECT-TYPE
    SYNTAX          CipSecEndPtHistEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes associated with
        a previously active IPsec Phase-2 Tunnel Endpoint."
    INDEX           { cipSecEndPtHistIndex } 
    ::= { cipSecEndPtHistTable 1 }

CipSecEndPtHistEntry ::= SEQUENCE {
        cipSecEndPtHistIndex          Integer32,
        cipSecEndPtHistTunIndex       Integer32,
        cipSecEndPtHistActiveIndex    Integer32,
        cipSecEndPtHistLocalName      DisplayString,
        cipSecEndPtHistLocalType      EndPtType,
        cipSecEndPtHistLocalAddr1     IPSIpAddress,
        cipSecEndPtHistLocalAddr2     IPSIpAddress,
        cipSecEndPtHistLocalProtocol  Integer32,
        cipSecEndPtHistLocalPort      Integer32,
        cipSecEndPtHistRemoteName     DisplayString,
        cipSecEndPtHistRemoteType     EndPtType,
        cipSecEndPtHistRemoteAddr1    IPSIpAddress,
        cipSecEndPtHistRemoteAddr2    IPSIpAddress,
        cipSecEndPtHistRemoteProtocol Integer32,
        cipSecEndPtHistRemotePort     Integer32
}

cipSecEndPtHistIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The number of the previously active
        Endpoint associated
         with a IPsec Phase-2 Tunnel Table.  The value 
         of this index is a number which begins at 
         one and is incremented with each Endpoint 
         associated with an IPsec Phase-2 Tunnel.
         The value of this object will wrap at 2,147,483,647." 
    ::= { cipSecEndPtHistEntry 1 }

cipSecEndPtHistTunIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index  of the previously active IPsec
        Phase-2 Tunnel Table." 
    ::= { cipSecEndPtHistEntry 2 }

cipSecEndPtHistActiveIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The index  of the previously active Endpoint." 
    ::= { cipSecEndPtHistEntry 3 }

cipSecEndPtHistLocalName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the local Endpoint." 
    ::= { cipSecEndPtHistEntry 4 }

cipSecEndPtHistLocalType OBJECT-TYPE
    SYNTAX          EndPtType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of identity for the local Endpoint.
        Possible values are:
        1) a single IP address, or
        2) an IP address range, or
        3) an IP subnet." 
    ::= { cipSecEndPtHistEntry 5 }

cipSecEndPtHistLocalAddr1 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The local Endpoint's first IP address specification.

        If the local Endpoint type is single IP address, 
        then this is the value of the IP address.

        If the local Endpoint type is IP subnet, then this
        is the value of the subnet.

        If the local Endpoint type is IP address range, 
        then this is the value of beginning IP address of 
        the range." 
    ::= { cipSecEndPtHistEntry 6 }

cipSecEndPtHistLocalAddr2 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The local Endpoint's second IP address specification.

        If the local Endpoint type is single IP address, 
        then this is the value of the IP address.

        If the local Endpoint type is IP subnet, then this
        is the value of the subnet mask.

        If the local Endpoint type is IP address range, 
        then this
        is the value of ending IP address of the range." 
    ::= { cipSecEndPtHistEntry 7 }

cipSecEndPtHistLocalProtocol OBJECT-TYPE
    SYNTAX          Integer32 (0..255 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The protocol number of the local Endpoint's traffic." 
    ::= { cipSecEndPtHistEntry 8 }

cipSecEndPtHistLocalPort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The port number of the local Endpoint's traffic." 
    ::= { cipSecEndPtHistEntry 9 }

cipSecEndPtHistRemoteName OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The DNS name of the remote Endpoint." 
    ::= { cipSecEndPtHistEntry 10 }

cipSecEndPtHistRemoteType OBJECT-TYPE
    SYNTAX          EndPtType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of identity for the remote Endpoint.
        Possible values are:
        1) a single IP address, or
        2) an IP address range, or
        3) an IP subnet." 
    ::= { cipSecEndPtHistEntry 11 }

cipSecEndPtHistRemoteAddr1 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The remote Endpoint's first IP address specification.

        If the remote Endpoint type is single IP address, 
        then this
        is the value of the IP address.

        If the remote Endpoint type is IP subnet, then this
        is the value of the subnet.

        If the remote Endpoint type is IP address range, 
        then this
        is the value of beginning IP address of the range." 
    ::= { cipSecEndPtHistEntry 12 }

cipSecEndPtHistRemoteAddr2 OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The remote Endpoint's second IP address specification.

        If the remote Endpoint type is single IP address, 
        then this
        is the value of the IP address.

        If the remote Endpoint type is IP subnet, then this
        is the value of the subnet mask.

        If the remote Endpoint type is IP address range, 
        then this
        is the value of ending IP address of the range." 
    ::= { cipSecEndPtHistEntry 13 }

cipSecEndPtHistRemoteProtocol OBJECT-TYPE
    SYNTAX          Integer32 (0..255 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The protocol number of the remote Endpoint's traffic." 
    ::= { cipSecEndPtHistEntry 14 }

cipSecEndPtHistRemotePort OBJECT-TYPE
    SYNTAX          Integer32 (0..65535 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The port number of the remote Endpoint's traffic." 
    ::= { cipSecEndPtHistEntry 15 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Failure Group
--   
-- This group consists of a:
-- 1) IPsec Failure Global Objects
-- 2) IPsec Phase-1 Tunnel Failure Table
-- 3) IPsec Phase-2 Tunnel Failure Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecFailGlobal  OBJECT IDENTIFIER
    ::= { cipSecFailures 1 }

cipSecFailPhaseOne  OBJECT IDENTIFIER
    ::= { cipSecFailures 2 }

cipSecFailPhaseTwo  OBJECT IDENTIFIER
    ::= { cipSecFailures 3 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Failure Global Control Objects
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecFailGlobalCntl  OBJECT IDENTIFIER
    ::= { cipSecFailGlobal 1 }


cipSecFailTableSize OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The window size of the IPsec Phase-1 and Phase-2
        Failure Tables.

        The IPsec Phase-1 and Phase-2 Failure Tables are
        implemented as a sliding window in which only the
        last n entries are maintained.  This object is used
        specify the number of entries which will be 
        maintained in the IPsec Phase-1 and Phase-2 Failure 
        Tables.

        An implementation may choose suitable minimum and 
        maximum values for this element based on the local 
        policy and available resources. If an SNMP SET request 
        specifies a value outside this window for this element, 
        a BAD VALUE may be returned." 
    ::= { cipSecFailGlobalCntl 1 }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-1 Failure Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cikeFailTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CikeFailEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-1 Failure Table.
        This table is implemented as a sliding 
        window in which only the last n entries are 
        maintained.  The maximum number of entries
        is specified by the cipSecFailTableSize object."
    ::= { cipSecFailPhaseOne 1 }

cikeFailEntry OBJECT-TYPE
    SYNTAX          CikeFailEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes associated
        with
         an IPsec Phase-1 failure."
    INDEX           { cikeFailIndex } 
    ::= { cikeFailTable 1 }

CikeFailEntry ::= SEQUENCE {
        cikeFailIndex       Integer32,
        cikeFailReason      INTEGER ,
        cikeFailTime        TimeStamp,
        cikeFailLocalType   IkePeerType,
        cikeFailLocalValue  DisplayString,
        cikeFailRemoteType  IkePeerType,
        cikeFailRemoteValue DisplayString,
        cikeFailLocalAddr   IPSIpAddress,
        cikeFailRemoteAddr  IPSIpAddress
}

cikeFailIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-1 Failure Table index.
        The value of the index is a number which 
        begins at one and is incremented with each 
        IPsec Phase-1 failure. The value
        of this object will wrap at 2,147,483,647." 
    ::= { cikeFailEntry 1 }

cikeFailReason OBJECT-TYPE
    SYNTAX          INTEGER  {
                        other(1),
                        peerDelRequest(2),
                        peerLost(3),
                        localFailure(4),
                        authFailure(5),
                        hashValidation(6),
                        encryptFailure(7),
                        internalError(8),
                        sysCapExceeded(9),
                        proposalFailure(10),
                        peerCertUnavailable(11),
                        peerCertNotValid(12),
                        localCertExpired(13),
                        crlFailure(14),
                        peerEncodingError(15),
                        nonExistentSa(16),
                        operRequest(17)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The reason for the failure.  Possible reasons include:
        1 = other
        2 = peer delete request was received
        3 = contact with peer was lost
        4 = local failure occurred
        5 = authentication failure
        6 = hash validation failure
        7 = encryption failure
        8 = internal error occurred
        9 = system capacity failure
        10 = proposal failure
        11 = peer's certificate is unavailable
        12 = peer's certificate was found invalid
        13 = local certificate expired
        14 = certificate revoke list (crl) failure
        15 = peer encoding error
        16 = non-existent security association
        17 = operator requested termination." 
    ::= { cikeFailEntry 2 }

cikeFailTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of sysUpTime in hundredths of seconds
        at the time of the failure." 
    ::= { cikeFailEntry 3 }

cikeFailLocalType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of local peer identity.  The local peer
        may be identified by:
         1. an IP address, or
         2. a host name." 
    ::= { cikeFailEntry 4 }

cikeFailLocalValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the local peer identity.

        If the local peer type is an IP Address, then this
        is the IP Address used to identify the local peer.

        If the local peer type is a host name, then this is
        the host name used to identify the local peer." 
    ::= { cikeFailEntry 5 }

cikeFailRemoteType OBJECT-TYPE
    SYNTAX          IkePeerType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of remote peer identity.  The remote
        peer may be identified by:
         1. an IP address, or
         2. a host name." 
    ::= { cikeFailEntry 6 }

cikeFailRemoteValue OBJECT-TYPE
    SYNTAX          DisplayString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of the remote peer identity.

        If the remote peer type is an IP Address, then this
        is the IP Address used to identify the remote peer.

        If the remote peer type is a host name, then this is
        the host name used to identify the remote peer." 
    ::= { cikeFailEntry 7 }

cikeFailLocalAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the local peer." 
    ::= { cikeFailEntry 8 }

cikeFailRemoteAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The IP address of the remote peer." 
    ::= { cikeFailEntry 9 }
 

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec Phase-2 Failure Table
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecFailTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF CipSecFailEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-2 Failure Table.
        This table is implemented as a sliding window 
        in which only the last n entries are maintained.  
        The maximum number of entries
        is specified by the cipSecFailTableSize object."
    ::= { cipSecFailPhaseTwo 1 }

cipSecFailEntry OBJECT-TYPE
    SYNTAX          CipSecFailEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Each entry contains the attributes associated with
        an IPsec Phase-1 failure."
    INDEX           { cipSecFailIndex } 
    ::= { cipSecFailTable 1 }

CipSecFailEntry ::= SEQUENCE {
        cipSecFailIndex       Integer32,
        cipSecFailReason      INTEGER ,
        cipSecFailTime        TimeStamp,
        cipSecFailTunnelIndex Integer32,
        cipSecFailSaSpi       Integer32,
        cipSecFailPktSrcAddr  IPSIpAddress,
        cipSecFailPktDstAddr  IPSIpAddress
}

cipSecFailIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The IPsec Phase-2 Failure Table index.
        The value of the index is a number which 
        begins at one and is incremented with each 
        IPsec Phase-1 failure. The value
        of this object will wrap at 2,147,483,647." 
    ::= { cipSecFailEntry 1 }

cipSecFailReason OBJECT-TYPE
    SYNTAX          INTEGER  {
                        other(1),
                        internalError(2),
                        peerEncodingError(3),
                        proposalFailure(4),
                        protocolUseFail(5),
                        nonExistentSa(6),
                        decryptFailure(7),
                        encryptFailure(8),
                        inAuthFailure(9),
                        outAuthFailure(10),
                        compression(11),
                        sysCapExceeded(12),
                        peerDelRequest(13),
                        peerLost(14),
                        seqNumRollOver(15),
                        operRequest(16)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The reason for the failure.  Possible reasons
        include:
          1 = other
          2 = internal error occurred
          3 = peer encoding error
          4 = proposal failure
          5 = protocol use failure
          6 = non-existent security association
          7 = decryption failure
          8 = encryption failure
          9 = inbound authentication failure
         10 = outbound authentication failure
         11 = compression failure
         12 = system capacity failure
         13 = peer delete request was received
         14 = contact with peer was lost
         15 = sequence number rolled over
         16 = operator requested termination." 
    ::= { cipSecFailEntry 2 }

cipSecFailTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of sysUpTime in hundredths of seconds
        at the time of the failure." 
    ::= { cipSecFailEntry 3 }

cipSecFailTunnelIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The Phase-2 Tunnel index (cipSecTunIndex)." 
    ::= { cipSecFailEntry 4 }

cipSecFailSaSpi OBJECT-TYPE
    SYNTAX          Integer32 (0..2147483647 )
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The security association SPI value." 
    ::= { cipSecFailEntry 5 }

cipSecFailPktSrcAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The packet's source IP address." 
    ::= { cipSecFailEntry 6 }

cipSecFailPktDstAddr OBJECT-TYPE
    SYNTAX          IPSIpAddress
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The packet's destination IP address." 
    ::= { cipSecFailEntry 7 }
 


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The IPsec TRAP Control Group
--   
-- This group of objects controls the sending of IPsec TRAPs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecTrapCntlIkeTunnelStart OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state of
        sending the IPsec IKE Phase-1 Tunnel Start TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 1 }

cipSecTrapCntlIkeTunnelStop OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the
         IPsec IKE Phase-1 Tunnel Stop TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 2 }

cipSecTrapCntlIkeSysFailure OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the
         IPsec IKE Phase-1 System Failure TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 3 }

cipSecTrapCntlIkeCertCrlFailure OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative
        state of sending the
         IPsec IKE Phase-1 Certificate/CRL Failure TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 4 }

cipSecTrapCntlIkeProtocolFail OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative
        state of sending the
         IPsec IKE Phase-1 Protocol Failure TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 5 }

cipSecTrapCntlIkeNoSa OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative
        state of sending the
         IPsec IKE Phase-1 No Security Association TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 6 }

cipSecTrapCntlIpSecTunnelStart OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the IPsec
         Phase-2 Tunnel Start TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 7 }

cipSecTrapCntlIpSecTunnelStop OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative
        state of sending the IPsec
         Phase-2 Tunnel Stop TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 8 }

cipSecTrapCntlIpSecSysFailure OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the IPsec
         Phase-2 System Failure TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 9 }

cipSecTrapCntlIpSecSetUpFailure OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the IPsec
         Phase-2 Set Up Failure TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 10 }

cipSecTrapCntlIpSecEarlyTunTerm OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the IPsec
         Phase-2 Early Tunnel Termination TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 11 }

cipSecTrapCntlIpSecProtocolFail OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the IPsec
         Phase-2 Protocol Failure TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 12 }

cipSecTrapCntlIpSecNoSa OBJECT-TYPE
    SYNTAX          TrapStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object defines the administrative state
        of sending the IPsec
         Phase-2  No Security Association TRAP"
    DEFVAL          { disabled } 
    ::= { cipSecTrapCntl 13 }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- IPsec Notifications - TRAPs
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecMIBNotificationPrefix  OBJECT IDENTIFIER
    ::= { ciscoIpSecFlowMonitorMIB 2 }

cipSecMIBNotifications  OBJECT IDENTIFIER
    ::= { cipSecMIBNotificationPrefix 0 }


cikeTunnelStart NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr,
                        cikeTunLifeTime
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when an IPsec Phase-1
        IKE Tunnel becomes active."
   ::= { cipSecMIBNotifications 1 }

cikeTunnelStop NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr,
                        cikeTunActiveTime
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when an IPsec Phase-1
        IKE Tunnel becomes inactive."
   ::= { cipSecMIBNotifications 2 }

cikeSysFailure NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when the processing for
        an IPsec Phase-1 IKE Tunnel experiences an internal
        or system capacity error."
   ::= { cipSecMIBNotifications 3 }

cikeCertCrlFailure NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when the processing for
        an IPsec Phase-1 IKE Tunnel experiences a Certificate
        or a Certificate Revoke List (CRL) related error."
   ::= { cipSecMIBNotifications 4 }

cikeProtocolFailure NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when the processing for
        an IPsec Phase-1 IKE Tunnel experiences a protocol
        related error."
   ::= { cipSecMIBNotifications 5 }

cikeNoSa NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when the processing for
        an IPsec Phase-1 IKE Tunnel experiences a non-existent
        security association error."
   ::= { cipSecMIBNotifications 6 }

cipSecTunnelStart NOTIFICATION-TYPE
    OBJECTS         {
                        cipSecTunLifeTime,
                        cipSecTunLifeSize
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when an IPsec Phase-2
        Tunnel becomes active."
   ::= { cipSecMIBNotifications 7 }

cipSecTunnelStop NOTIFICATION-TYPE
    OBJECTS         { cipSecTunActiveTime }
    STATUS          current
    DESCRIPTION
        "This notification is generated when an IPsec Phase-2
        Tunnel becomes inactive."
   ::= { cipSecMIBNotifications 8 }

cipSecSysFailure NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr,
                        cipSecTunActiveTime,
                        cipSecSpiProtocol
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when the processing for
        an IPsec Phase-2 Tunnel experiences an internal
        or system capacity error."
   ::= { cipSecMIBNotifications 9 }

cipSecSetUpFailure NOTIFICATION-TYPE
    OBJECTS         {
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when the setup for
        an IPsec Phase-2 Tunnel fails."
   ::= { cipSecMIBNotifications 10 }

cipSecEarlyTunTerm NOTIFICATION-TYPE
    OBJECTS         {
                        cipSecTunActiveTime,
                        cipSecSpiProtocol
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when an an IPsec Phase-2
        Tunnel is terminated earily or before expected."
   ::= { cipSecMIBNotifications 11 }

cipSecProtocolFailure NOTIFICATION-TYPE
    OBJECTS         {
                        cipSecTunActiveTime,
                        cipSecSpiProtocol
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated when the processing for
        an IPsec Phase-2 Tunnel experiences a protocol
        related error."
   ::= { cipSecMIBNotifications 12 }

cipSecNoSa NOTIFICATION-TYPE
    STATUS          current
    DESCRIPTION
        "This notification is generated when the processing for
        an IPsec Phase-2 Tunnel experiences a non-existent
        security association error."
   ::= { cipSecMIBNotifications 13 }
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Conformance Information
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecMIBConformance  OBJECT IDENTIFIER
    ::= { ciscoIpSecFlowMonitorMIB 3 }

cipSecMIBGroups  OBJECT IDENTIFIER
    ::= { cipSecMIBConformance 1 }

cipSecMIBCompliances  OBJECT IDENTIFIER
    ::= { cipSecMIBConformance 2 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Compliance Statements
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for SNMP entities
        the IP Security Protocol.
        This has been replaced by cipSecMIBComplianceRev1."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cipSecLevelsGroup,
                        cipSecPhaseOneGroup,
                        cipSecPhaseTwoGroup
                    }

    OBJECT          cikeTunStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          cipSecTunStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { cipSecMIBCompliances 1 }

cipSecMIBComplianceRev1 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for SNMP entities
        the IP Security Protocol."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        cipSecLevelsGroup,
                        cipSecPhaseOneGroup,
                        cipSecPhaseTwoGroup
                    }

    GROUP           cipSecGWStatsGroup
    DESCRIPTION
        "Implementation of this group is for the
        gateway supporting IPSec statistics 
        information."

    OBJECT          cikeTunStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          cipSecTunStatus
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { cipSecMIBCompliances 2 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Units of Conformance
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++

cipSecLevelsGroup OBJECT-GROUP
    OBJECTS         { cipSecMibLevel }
    STATUS          current
    DESCRIPTION
        "This group consists of a:
        1) IPsec MIB Level"
    ::= { cipSecMIBGroups 1 }

cipSecPhaseOneGroup OBJECT-GROUP
    OBJECTS         {
                        cikeGlobalActiveTunnels,
                        cikeGlobalPreviousTunnels,
                        cikeGlobalInOctets,
                        cikeGlobalInPkts,
                        cikeGlobalInDropPkts,
                        cikeGlobalInNotifys,
                        cikeGlobalInP2Exchgs,
                        cikeGlobalInP2ExchgInvalids,
                        cikeGlobalInP2ExchgRejects,
                        cikeGlobalInP2SaDelRequests,
                        cikeGlobalOutOctets,
                        cikeGlobalOutPkts,
                        cikeGlobalOutDropPkts,
                        cikeGlobalOutNotifys,
                        cikeGlobalOutP2Exchgs,
                        cikeGlobalOutP2ExchgInvalids,
                        cikeGlobalOutP2ExchgRejects,
                        cikeGlobalOutP2SaDelRequests,
                        cikeGlobalInitTunnels,
                        cikeGlobalInitTunnelFails,
                        cikeGlobalRespTunnelFails,
                        cikeGlobalSysCapFails,
                        cikeGlobalAuthFails,
                        cikeGlobalDecryptFails,
                        cikeGlobalHashValidFails,
                        cikeGlobalNoSaFails,
                        cikePeerLocalAddr,
                        cikePeerRemoteAddr,
                        cikePeerActiveTime,
                        cikePeerActiveTunnelIndex,
                        cikeTunLocalType,
                        cikeTunLocalValue,
                        cikeTunLocalAddr,
                        cikeTunLocalName,
                        cikeTunRemoteType,
                        cikeTunRemoteValue,
                        cikeTunRemoteAddr,
                        cikeTunRemoteName,
                        cikeTunNegoMode,
                        cikeTunDiffHellmanGrp,
                        cikeTunEncryptAlgo,
                        cikeTunHashAlgo,
                        cikeTunAuthMethod,
                        cikeTunLifeTime,
                        cikeTunActiveTime,
                        cikeTunSaRefreshThreshold,
                        cikeTunTotalRefreshes,
                        cikeTunInOctets,
                        cikeTunInPkts,
                        cikeTunInDropPkts,
                        cikeTunInNotifys,
                        cikeTunInP2Exchgs,
                        cikeTunInP2ExchgInvalids,
                        cikeTunInP2ExchgRejects,
                        cikeTunInP2SaDelRequests,
                        cikeTunOutOctets,
                        cikeTunOutPkts,
                        cikeTunOutDropPkts,
                        cikeTunOutNotifys,
                        cikeTunOutP2Exchgs,
                        cikeTunOutP2ExchgInvalids,
                        cikeTunOutP2ExchgRejects,
                        cikeTunOutP2SaDelRequests,
                        cikeTunStatus,
                        cikePeerCorrIpSecTunIndex
                    }
    STATUS          current
    DESCRIPTION
        "This group consists of:
        1) IPsec Phase-1 Global Objects
        2) IPsec Phase-1 Peer Table
        3) IPsec Phase-1 Tunnel Table
        4) IPsec Phase-1 Correlation Table"
    ::= { cipSecMIBGroups 2 }

cipSecPhaseTwoGroup OBJECT-GROUP
    OBJECTS         {
                        cipSecGlobalActiveTunnels,
                        cipSecGlobalPreviousTunnels,
                        cipSecGlobalInOctets,
                        cipSecGlobalHcInOctets,
                        cipSecGlobalInOctWraps,
                        cipSecGlobalInDecompOctets,
                        cipSecGlobalHcInDecompOctets,
                        cipSecGlobalInDecompOctWraps,
                        cipSecGlobalInPkts,
                        cipSecGlobalInDrops,
                        cipSecGlobalInReplayDrops,
                        cipSecGlobalInAuths,
                        cipSecGlobalInAuthFails,
                        cipSecGlobalInDecrypts,
                        cipSecGlobalInDecryptFails,
                        cipSecGlobalOutOctets,
                        cipSecGlobalHcOutOctets,
                        cipSecGlobalOutOctWraps,
                        cipSecGlobalOutUncompOctets,
                        cipSecGlobalHcOutUncompOctets,
                        cipSecGlobalOutUncompOctWraps,
                        cipSecGlobalOutPkts,
                        cipSecGlobalOutDrops,
                        cipSecGlobalOutAuths,
                        cipSecGlobalOutAuthFails,
                        cipSecGlobalOutEncrypts,
                        cipSecGlobalOutEncryptFails,
                        cipSecGlobalProtocolUseFails,
                        cipSecGlobalNoSaFails,
                        cipSecGlobalSysCapFails,
                        cipSecTunIkeTunnelIndex,
                        cipSecTunIkeTunnelAlive,
                        cipSecTunLocalAddr,
                        cipSecTunRemoteAddr,
                        cipSecTunKeyType,
                        cipSecTunEncapMode,
                        cipSecTunLifeSize,
                        cipSecTunLifeTime,
                        cipSecTunActiveTime,
                        cipSecTunSaLifeSizeThreshold,
                        cipSecTunSaLifeTimeThreshold,
                        cipSecTunTotalRefreshes,
                        cipSecTunExpiredSaInstances,
                        cipSecTunCurrentSaInstances,
                        cipSecTunInSaDiffHellmanGrp,
                        cipSecTunInSaEncryptAlgo,
                        cipSecTunInSaAhAuthAlgo,
                        cipSecTunInSaEspAuthAlgo,
                        cipSecTunInSaDecompAlgo,
                        cipSecTunOutSaDiffHellmanGrp,
                        cipSecTunOutSaEncryptAlgo,
                        cipSecTunOutSaAhAuthAlgo,
                        cipSecTunOutSaEspAuthAlgo,
                        cipSecTunOutSaCompAlgo,
                        cipSecTunInOctets,
                        cipSecTunHcInOctets,
                        cipSecTunInOctWraps,
                        cipSecTunInDecompOctets,
                        cipSecTunHcInDecompOctets,
                        cipSecTunInDecompOctWraps,
                        cipSecTunInPkts,
                        cipSecTunInDropPkts,
                        cipSecTunInReplayDropPkts,
                        cipSecTunInAuths,
                        cipSecTunInAuthFails,
                        cipSecTunInDecrypts,
                        cipSecTunInDecryptFails,
                        cipSecTunOutOctets,
                        cipSecTunHcOutOctets,
                        cipSecTunOutOctWraps,
                        cipSecTunOutUncompOctets,
                        cipSecTunHcOutUncompOctets,
                        cipSecTunOutUncompOctWraps,
                        cipSecTunOutPkts,
                        cipSecTunOutDropPkts,
                        cipSecTunOutAuths,
                        cipSecTunOutAuthFails,
                        cipSecTunOutEncrypts,
                        cipSecTunOutEncryptFails,
                        cipSecTunStatus,
                        cipSecEndPtLocalName,
                        cipSecEndPtLocalType,
                        cipSecEndPtLocalAddr1,
                        cipSecEndPtLocalAddr2,
                        cipSecEndPtLocalProtocol,
                        cipSecEndPtLocalPort,
                        cipSecEndPtRemoteName,
                        cipSecEndPtRemoteType,
                        cipSecEndPtRemoteAddr1,
                        cipSecEndPtRemoteAddr2,
                        cipSecEndPtRemoteProtocol,
                        cipSecEndPtRemotePort,
                        cipSecSpiDirection,
                        cipSecSpiValue,
                        cipSecSpiProtocol,
                        cipSecSpiStatus
                    }
    STATUS          current
    DESCRIPTION
        "This group consists of:
        1) IPsec Phase-2 Global Statistics
        2) IPsec Phase-2 Tunnel Table
        3) IPsec Phase-2 Endpoint Table
        4) IPsec Phase-2 Security Protection Index Table"
    ::= { cipSecMIBGroups 3 }

cipSecHistoryGroup OBJECT-GROUP
    OBJECTS         {
                        cipSecHistTableSize,
                        cipSecHistCheckPoint,
                        cikeTunHistTermReason,
                        cikeTunHistActiveIndex,
                        cikeTunHistPeerLocalType,
                        cikeTunHistPeerLocalValue,
                        cikeTunHistPeerIntIndex,
                        cikeTunHistPeerRemoteType,
                        cikeTunHistPeerRemoteValue,
                        cikeTunHistLocalAddr,
                        cikeTunHistLocalName,
                        cikeTunHistRemoteAddr,
                        cikeTunHistRemoteName,
                        cikeTunHistNegoMode,
                        cikeTunHistDiffHellmanGrp,
                        cikeTunHistEncryptAlgo,
                        cikeTunHistHashAlgo,
                        cikeTunHistAuthMethod,
                        cikeTunHistLifeTime,
                        cikeTunHistStartTime,
                        cikeTunHistActiveTime,
                        cikeTunHistTotalRefreshes,
                        cikeTunHistTotalSas,
                        cikeTunHistInOctets,
                        cikeTunHistInPkts,
                        cikeTunHistInDropPkts,
                        cikeTunHistInNotifys,
                        cikeTunHistInP2Exchgs,
                        cikeTunHistInP2ExchgInvalids,
                        cikeTunHistInP2ExchgRejects,
                        cikeTunHistInP2SaDelRequests,
                        cikeTunHistOutOctets,
                        cikeTunHistOutPkts,
                        cikeTunHistOutDropPkts,
                        cikeTunHistOutNotifys,
                        cikeTunHistOutP2Exchgs,
                        cikeTunHistOutP2ExchgInvalids,
                        cikeTunHistOutP2ExchgRejects,
                        cikeTunHistOutP2SaDelRequests,
                        cipSecTunHistTermReason,
                        cipSecTunHistActiveIndex,
                        cipSecTunHistIkeTunnelIndex,
                        cipSecTunHistLocalAddr,
                        cipSecTunHistRemoteAddr,
                        cipSecTunHistKeyType,
                        cipSecTunHistEncapMode,
                        cipSecTunHistLifeSize,
                        cipSecTunHistLifeTime,
                        cipSecTunHistStartTime,
                        cipSecTunHistActiveTime,
                        cipSecTunHistTotalRefreshes,
                        cipSecTunHistTotalSas,
                        cipSecTunHistInSaDiffHellmanGrp,
                        cipSecTunHistInSaEncryptAlgo,
                        cipSecTunHistInSaAhAuthAlgo,
                        cipSecTunHistInSaEspAuthAlgo,
                        cipSecTunHistInSaDecompAlgo,
                        cipSecTunHistOutSaDiffHellmanGrp,
                        cipSecTunHistOutSaEncryptAlgo,
                        cipSecTunHistOutSaAhAuthAlgo,
                        cipSecTunHistOutSaEspAuthAlgo,
                        cipSecTunHistOutSaCompAlgo,
                        cipSecTunHistInOctets,
                        cipSecTunHistHcInOctets,
                        cipSecTunHistInOctWraps,
                        cipSecTunHistInDecompOctets,
                        cipSecTunHistHcInDecompOctets,
                        cipSecTunHistInDecompOctWraps,
                        cipSecTunHistInPkts,
                        cipSecTunHistInDropPkts,
                        cipSecTunHistInReplayDropPkts,
                        cipSecTunHistInAuths,
                        cipSecTunHistInAuthFails,
                        cipSecTunHistInDecrypts,
                        cipSecTunHistInDecryptFails,
                        cipSecTunHistOutOctets,
                        cipSecTunHistHcOutOctets,
                        cipSecTunHistOutOctWraps,
                        cipSecTunHistOutUncompOctets,
                        cipSecTunHistHcOutUncompOctets,
                        cipSecTunHistOutUncompOctWraps,
                        cipSecTunHistOutPkts,
                        cipSecTunHistOutDropPkts,
                        cipSecTunHistOutAuths,
                        cipSecTunHistOutAuthFails,
                        cipSecTunHistOutEncrypts,
                        cipSecTunHistOutEncryptFails,
                        cipSecEndPtHistTunIndex,
                        cipSecEndPtHistActiveIndex,
                        cipSecEndPtHistLocalName,
                        cipSecEndPtHistLocalType,
                        cipSecEndPtHistLocalAddr1,
                        cipSecEndPtHistLocalAddr2,
                        cipSecEndPtHistLocalProtocol,
                        cipSecEndPtHistLocalPort,
                        cipSecEndPtHistRemoteName,
                        cipSecEndPtHistRemoteType,
                        cipSecEndPtHistRemoteAddr1,
                        cipSecEndPtHistRemoteAddr2,
                        cipSecEndPtHistRemoteProtocol,
                        cipSecEndPtHistRemotePort
                    }
    STATUS          current
    DESCRIPTION
        "This group consists of:
        1) IPsec History Global Objects
        2) IPsec Phase-1 History Objects
        3) IPsec Phase-2 History Objects"
    ::= { cipSecMIBGroups 4 }

cipSecFailuresGroup OBJECT-GROUP
    OBJECTS         {
                        cipSecFailTableSize,
                        cikeFailReason,
                        cikeFailTime,
                        cikeFailLocalType,
                        cikeFailLocalValue,
                        cikeFailRemoteType,
                        cikeFailRemoteValue,
                        cikeFailLocalAddr,
                        cikeFailRemoteAddr,
                        cipSecFailReason,
                        cipSecFailTime,
                        cipSecFailTunnelIndex,
                        cipSecFailSaSpi,
                        cipSecFailPktSrcAddr,
                        cipSecFailPktDstAddr
                    }
    STATUS          current
    DESCRIPTION
        "This group consists of:
        1) IPsec Failure Global Objects
        2) IPsec Phase-1 Tunnel Failure Table
        3) IPsec Phase-2 Tunnel Failure Table"
    ::= { cipSecMIBGroups 5 }

cipSecTrapCntlGroup OBJECT-GROUP
    OBJECTS         {
                        cipSecTrapCntlIkeTunnelStart,
                        cipSecTrapCntlIkeTunnelStop,
                        cipSecTrapCntlIkeSysFailure,
                        cipSecTrapCntlIkeCertCrlFailure,
                        cipSecTrapCntlIkeProtocolFail,
                        cipSecTrapCntlIkeNoSa,
                        cipSecTrapCntlIpSecTunnelStart,
                        cipSecTrapCntlIpSecTunnelStop,
                        cipSecTrapCntlIpSecSysFailure,
                        cipSecTrapCntlIpSecSetUpFailure,
                        cipSecTrapCntlIpSecEarlyTunTerm,
                        cipSecTrapCntlIpSecProtocolFail,
                        cipSecTrapCntlIpSecNoSa
                    }
    STATUS          current
    DESCRIPTION
        "This group of objects controls the sending of IPsec TRAPs."
    ::= { cipSecMIBGroups 6 }

cipSecNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        cikeTunnelStart,
                        cikeTunnelStop,
                        cikeSysFailure,
                        cikeCertCrlFailure,
                        cikeProtocolFailure,
                        cikeNoSa,
                        cipSecTunnelStart,
                        cipSecTunnelStop,
                        cipSecSysFailure,
                        cipSecSetUpFailure,
                        cipSecEarlyTunTerm,
                        cipSecProtocolFailure,
                        cipSecNoSa
                    }
    STATUS          current
    DESCRIPTION
        "This group contains the notifications for the IPsec MIB."
    ::= { cipSecMIBGroups 7 }

cipSecGWStatsGroup OBJECT-GROUP
    OBJECTS         {
                        cikePhase1GWActiveTunnels,
                        cikePhase1GWPreviousTunnels,
                        cikePhase1GWInOctets,
                        cikePhase1GWInPkts,
                        cikePhase1GWInDropPkts,
                        cikePhase1GWInNotifys,
                        cikePhase1GWInP2Exchgs,
                        cikePhase1GWInP2ExchgInvalids,
                        cikePhase1GWInP2ExchgRejects,
                        cikePhase1GWInP2SaDelRequests,
                        cikePhase1GWOutOctets,
                        cikePhase1GWOutPkts,
                        cikePhase1GWOutDropPkts,
                        cikePhase1GWOutNotifys,
                        cikePhase1GWOutP2Exchgs,
                        cikePhase1GWOutP2ExchgInvalids,
                        cikePhase1GWOutP2ExchgRejects,
                        cikePhase1GWOutP2SaDelRequests,
                        cikePhase1GWInitTunnels,
                        cikePhase1GWInitTunnelFails,
                        cikePhase1GWRespTunnelFails,
                        cikePhase1GWSysCapFails,
                        cikePhase1GWAuthFails,
                        cikePhase1GWDecryptFails,
                        cikePhase1GWHashValidFails,
                        cikePhase1GWNoSaFails,
                        cipSecPhase2GWActiveTunnels,
                        cipSecPhase2GWPreviousTunnels,
                        cipSecPhase2GWInOctets,
                        cipSecPhase2GWInOctWraps,
                        cipSecPhase2GWInDecompOctets,
                        cipSecPhase2GWInDecompOctWraps,
                        cipSecPhase2GWInPkts,
                        cipSecPhase2GWInDrops,
                        cipSecPhase2GWInReplayDrops,
                        cipSecPhase2GWInAuths,
                        cipSecPhase2GWInAuthFails,
                        cipSecPhase2GWInDecrypts,
                        cipSecPhase2GWInDecryptFails,
                        cipSecPhase2GWOutOctets,
                        cipSecPhase2GWOutOctWraps,
                        cipSecPhase2GWOutUncompOctets,
                        cipSecPhase2GWOutUncompOctWraps,
                        cipSecPhase2GWOutPkts,
                        cipSecPhase2GWOutDrops,
                        cipSecPhase2GWOutAuths,
                        cipSecPhase2GWOutAuthFails,
                        cipSecPhase2GWOutEncrypts,
                        cipSecPhase2GWOutEncryptFails,
                        cipSecPhase2GWProtocolUseFails,
                        cipSecPhase2GWNoSaFails,
                        cipSecPhase2GWSysCapFails
                    }
    STATUS          current
    DESCRIPTION
        ""
    ::= { cipSecMIBGroups 8 }

END



