-- *****************************************************************
--   
-- February 2009.  <PERSON>
--   
-- Copyright (c) 2007-2013 by cisco Systems Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-WAN-3G-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    NOTIFICATION-TYPE,
    Integer32,
    Unsigned32,
    Counter64,
    Counter32,
    Gauge32
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    NOTIFICATION-GROUP,
    OBJECT-GROUP
        FROM SNMPv2-CONF
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    entPhysicalName,
    entPhysicalIndex
        FROM ENTITY-MIB
    ifIndex
        FROM IF-MIB
    Inet<PERSON>ddress,
    InetAddressType
        FROM INET-ADDRESS-MIB
    RowStatus,
    DisplayString,
    TEXTUAL-CONVENTION,
    TruthValue
        FROM SNMPv2-TC
    ciscoMgmt
        FROM CISCO-SMI;


ciscoWan3gMIB MODULE-IDENTITY
    LAST-UPDATED    "201308120000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
            "Cisco Systems
            Customer Service

            Postal: 170 W Tasman Drive
            San Jose, CA  95134
            USA

            Tel: ****** 553-NETS

            E-mail: <EMAIL>
                    <EMAIL>"
    DESCRIPTION
        "This MIB module provides network management
        support for Cisco cellular 3G and 4G LTE WAN products.

        *** ABBREVIATIONS, ACRONYMS, AND SYMBOLS ***

        1xRTT - 1 times Radio Transmission Technology.

        3G - Third generation of mobile phones standards and
        technologies.

        4G - Fourth generation of mobile phones standards and
        technologies

        Azimuth - Angle of rotation of a satellite Dish.

        BER - Bit Error Ratio.

        BS - Base Station.

        CDMA - Code Division Multiple Access.

        dB - decibel.

        dBm - power ratio in decibels (dB) of the measured power
              referenced to one milliwatt (mW).

        CnS - Control and Status proprietary protocol for
              managing the control and status of the modem.

        Ec/Io - ratio of received pilot energy, Ec, to total received
                energy or the total power spectral density, Io.

        EDGE - Enhanced Data rate for GSM Evolution.

        EPS  - Evolved Packet System

        EVDO - EVolution Data Optimized.

        FDD  - Frequency Division Duplexing

        GPRS - General Packet Radio Service.

        GSM - Global System for Mobile communications.

        GPS - Global Positioning System.

        HSDPA - High Speed Downlink Packet Access.

        HSPA - High Speed Packet Access.

        HSUPA - High Speed Uplink Packet Access.

        LBS - Location Based Service.

        LTE - Long Term Evolution

        MT - Mobile Termination.

        PDP - Packet Data Protocol.

        PLMN - Public Land Mobile Network. 

        QoS - Quality of Service.

        RSSI - Received Signal Strength Indication.

        SDU - Service Data Unit.

        SER - SDU Error Ratio.

        SIM - Subscriber Identity Module.

        SMS  -  Short Messaging Service.

        SNR  - Signal to Noise Ratio.

        TDD  - Time Division Duplexing

        UMTS - Universal Mobile Telecommunication System.

        WCDMA - Wideband Code Division Multiple Access."
    REVISION        "201308120000Z"
    DESCRIPTION
        "Added new notifications c3gModemTemperOnsetRecoveryNotif,
        c3gModemTemperAbateRecoveryNotif."
    REVISION        "201207250000Z"
    DESCRIPTION
        "Modified Description of the ciscoWan3gMIB
        module.

        Modified the description of c3gWanCommonTable,
        c3gWanLbsCommonTable, c3gSmsCommonTable and all its objects 
        to emphazise that it is technology independent.

        Added enumerated value lte to objects C3gServiceCapability, 
        c3gCurrentIdleDigitalMode, c3gGsmPacketService.

        Modified the descriptions of c3gGsmIdentityTable, and all its
        objects to emphazise that these objects are applicable to both
        3G and 4G-LTE Technology.

        Added new object c3gGpsState to the table c3gWanCommonTable.

        Added enumerated value lteBand for c3gGsmCurrentBand.

        Modified ciscoWan3gMIBGsmObjectGroup, 
        ciscoWan3gMIBSmsObjectGroup, ciscoWan3gMIBLbsObjectGroup,
        ciscoWan3gMIBGsmObjectGroup description in Module 
        ciscoWan3gMIBCompliance, ciscoWan3gMIBCompliance1.

        Added c3gGpsState to the group ciscoWan3gMIBCommonObjectGroup."
    REVISION        "201207100000Z"
    DESCRIPTION
        "Added enumerated values rate56kbps(12) to rate8dot4mbps(18) for
        C3gUmtsQosLinkBitRate."
    REVISION        "201008110000Z"
    DESCRIPTION
        "Fixed spelling errors."
    REVISION        "201008040000Z"
    DESCRIPTION
        "Added c3gWanLbs and c3gWanSms.

        Added ciscoWan3gMIBSmsObjectGroup and
        ciscoWan3gMIBSmsObjectGroup.

        Deprecated ciscoWan3gMIBCompliance and replaced
        by ciscoWan3gMIBCompliance1."
    REVISION        "200902050000Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 661 }



C3gServiceCapability ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "3G service capability:
        oneXRtt(0)   - 1xRTT
        evDoRel0(1)  - EVDO Revision 0
        evDoRelA(2)  - EVDO Revision A
        evDoRelB(3)  - EVDO Revision B
        gprs(4)      - GPRS
        edge(5)      - EDGE
        umtsWcdma(6) - UMTS/WCDMA
        hsdpa(7)     - HSDPA
        hsupa(8)     - HSUPA
        hspa(9)      - HSPA
        hspaPlus(10) - HSPA Plus
        lteTdd(11)   - LTE TDD
        lteFdd (12)  - LTE FDD"
    SYNTAX          BITS {
                        oneXRtt(0),
                        evDoRel0(1),
                        evDoRelA(2),
                        evDoRelB(3),
                        gprs(4),
                        edge(5),
                        umtsWcdma(6),
                        hsdpa(7),
                        hsupa(8),
                        hspa(9),
                        hspaPlus(10),
                        lteTdd(11),
                        lteFdd(12)
                    }

C3gRssi ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Generic RSSI range."
    SYNTAX          Integer32 (-150..0)

C3gEcIo ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Generic EcIo range."
    SYNTAX          Integer32 (-150..0)

C3gTemperature ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Generic temperature range."
    SYNTAX          Integer32 (-50..100)

C3gPdpType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Generic PDP type."
    SYNTAX          INTEGER  {
                        unknown(1),
                        ipV4(2),
                        ppp(3),
                        ipV6(4),
                        ipV4V6(5)
                    }

C3gUmtsQosTrafficClass ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS traffic classes:
        subscription(1)   - based on user's subscription
        conversational(2) - conversational
        streaming(3)      - streaming
        interactive(4)    - interactive
        background(5)     - background"
    SYNTAX          INTEGER  {
                        subscription(1),
                        conversational(2),
                        streaming(3),
                        interactive(4),
                        background(5)
                    }

C3gUmtsQosLinkBitRate ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS link bit rate:
        subscription(1)    - based on user's subscription
        rate16kbps(2)      - 16 Kbps
        rate32kbps(3)      - 32 Kbps
        rate64kbps(4)      - 64 Kbps
        rate128kbps(5)     - 128 Kbps
        rate256kbps(6)     - 256 Kbps
        rate384kbps(7)     - 384 Kbps
        rate1dot8mbps(8)   - 1.8 Mbps
        rate3dot6mbps(9)   - 3.6 Mbps
        rate7dot2mbps(10)  - 7.2 Mbps
        rate14dot4mbps(11) - 14.4 Mbps
        rate56kbps(12)     - 56 Kbps
        rate1dot15mbps(13) - 1.15 Mbps
        rate1dot6mbps(14)  - 1.6 Mbps
        rate2dot1mbps(15)  - 2.1 Mbps
        rate2dot8mbps(16)  - 2.8 Mbps
        rate4dot2mbps(17)  - 4.2 Mbps
        rate8dot4mbps(18)  - 8.4 Mbps"
    SYNTAX          INTEGER  {
                        subscription(1),
                        rate16kbps(2),
                        rate32kbps(3),
                        rate64kbps(4),
                        rate128kbps(5),
                        rate256kbps(6),
                        rate384kbps(7),
                        rate1dot8mbps(8),
                        rate3dot6mbps(9),
                        rate7dot2mbps(10),
                        rate14dot4mbps(11),
                        rate56kbps(12),
                        rate1dot15mbps(13),
                        rate1dot6mbps(14),
                        rate2dot1mbps(15),
                        rate2dot8mbps(16),
                        rate4dot2mbps(17),
                        rate8dot4mbps(18)
                    }

C3gUmtsQosOrder ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS delivery order:
        subscription(1)        - based on user's subscription
        withDeliverOrder(2)    - with delivery order
        withoutDeliverOrder(3) - without delivery order"
    SYNTAX          INTEGER  {
                        subscription(1),
                        withDeliverOrder(2),
                        withoutDeliverOrder(3)
                    }

C3gUmtsQosErroneousSdu ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS Delivery of Erroneous Service Data Unit(SDU):
        subscription(1)        - based on user's subscription
        noDetect(2)            - no detect
        errSduDeliver(3)       - erroneous SDUs are delivered
        errSduNotDeliver(4)    - erroneous SDUs are not delivered"
    SYNTAX          INTEGER  {
                        subscription(1),
                        noDetect(2),
                        errSduDeliver(3),
                        errSduNotDeliver(4)
                    }

C3gUmtsQosSer ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS SDU error ratio:
        subscription(1)   - based on user's subscription
        oneExpMinus2(2)   - 1E-2
        sevenExpMinus3(3) - 7E-3
        oneExpMinus3(4)   - 1E-3
        oneExpMinus4(5)   - 1E-4
        oneExpMinus5(6)   - 1E-5
        oneExpMinus6(7)   - 1E-6
        oneExpMinus1(8)   - 1E-1"
    SYNTAX          INTEGER  {
                        subscription(1),
                        oneExpMinus2(2),
                        sevenExpMinus3(3),
                        oneExpMinus3(4),
                        oneExpMinus4(5),
                        oneExpMinus5(6),
                        oneExpMinus6(7),
                        oneExpMinus1(8)
                    }

C3gUmtsQosBer ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS residual bit error ratio (BER):
        subscription(1)  - based on user's subscription
        fiveExpMinus2(2) - 5E-2
        oneExpMinus2(3)  - 1E-2
        fiveExpMinus3(4) - 5E-3
        fourExpMinus3(5) - 4E-3
        oneExpMinus3(6)  - 1E-3
        oneExpMinus4(7)  - 1E-4
        oneExpMinus5(8)  - 1E-5
        oneExpMinus6(9)  - 1E-6
        sixExpMinus8(10)  - 6E-8"
    SYNTAX          INTEGER  {
                        subscription(1),
                        fiveExpMinus2(2),
                        oneExpMinus2(3),
                        fiveExpMinus3(4),
                        fourExpMinus3(5),
                        oneExpMinus3(6),
                        oneExpMinus4(7),
                        oneExpMinus5(8),
                        oneExpMinus6(9),
                        sixExpMinus8(10)
                    }

C3gUmtsQosPriority ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS traffic handling priority:
        subscription(1) - based on user's subscription 
        level1(2)       - priority level 1
        level2(3)       - priority level 2
        level3(4)       - priority level 3"
    SYNTAX          INTEGER  {
                        subscription(1),
                        level1(2),
                        level2(3),
                        level3(4)
                    }

C3gUmtsQosSrcStatDescriptor ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS source statistics descriptor:
        unknown(1) - unknown 
        speech(2)  - speech"
    SYNTAX          INTEGER  {
                        unknown(1),
                        speech(2)
                    }

C3gUmtsQosSignalIndication ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "UMTS QoS signalling indication:
        notOptimized(1) - not optimized for signalling traffic 
        optimized(2)    - optimized for signalling traffic"
    SYNTAX          INTEGER  {
                        notOptimized(1),
                        optimized(2)
                    }

C3gGprsQosPrecedence ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "GPRS QoS precedence:
        subscription(1)   - based on user's subscription 
        highPriority(2)   - high priority
        normalPriority(3) - normal priority
        lowPriority(4)    - low priority"
    SYNTAX          INTEGER  {
                        subscription(1),
                        highPriority(2),
                        normalPriority(3),
                        lowPriority(4)
                    }

C3gGprsQosDelay ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "GPRS QoS delay classes:
        subscription(1) - based on user's subscription 
        delayClass1(2)  - delay class 1
        delayClass2(3)  - delay class 2
        delayClass3(4)  - delay class 3
        delayClass4(5)  - delay class 4"
    SYNTAX          INTEGER  {
                        subscription(1),
                        delayClass1(2),
                        delayClass2(3),
                        delayClass3(4),
                        delayClass4(5)
                    }

C3gGprsQosReliability ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "GPRS QoS reliability:
        subscription(1)              - based on user's subscription 
        ackGtpLlcRlcProtData(2)      - acknowledged GTP, LLC, and RLC;
                                       protected data
        unAckGtpAckLlcRlcProtData(3) - unacknowledged GTP, acknowledged
                                       LLC and RLC; protected data
        unAckGtpLlcAckRlcProtData(4) - unacknowledged GTP and LLC,
                                       acknowledged RLC; protected data
        unAckGtpLlcRlcProtData(5)    - unacknowledged GTP, LLC, and
                                       RLC; protected data
        unAckGtpLlcRlcUnProtData(6)  - unacknowledged GTP, LLC, and
                                       RLC; unprotected data"
    SYNTAX          INTEGER  {
                        subscription(1),
                        ackGtpLlcRlcProtData(2),
                        unAckGtpAckLlcRlcProtData(3),
                        unAckGtpLlcAckRlcProtData(4),
                        unAckGtpLlcRlcProtData(5),
                        unAckGtpLlcRlcUnProtData(6)
                    }

C3gGprsQosPeakRate ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "GPRS QoS peak rate:
        subscription(1) - based on user's subscription 
        upTo1kops(2)    - up to 1000 octet/second
        upTo2kops(3)    - up to 2000 octet/second
        upTo4kops(4)    - up to 4000 octet/second
        upTo8kops(5)    - up to 8000 octet/second
        upTo16kops(6)   - up to 16000 octet/second
        upTo32kops(7)   - up to 32000 octet/second
        upTo64kops(8)   - up to 64000 octet/second
        upTo128kops(9)  - up to 128000 octet/second
        upTo256kops(10) - up to 256000 octet/second"
    SYNTAX          INTEGER  {
                        subscription(1),
                        upTo1kops(2),
                        upTo2kops(3),
                        upTo4kops(4),
                        upTo8kops(5),
                        upTo16kops(6),
                        upTo32kops(7),
                        upTo64kops(8),
                        upTo128kops(9),
                        upTo256kops(10)
                    }

C3gGprsQosMeanRate ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "GPRS QoS mean rate:
        subscription(1) - based on user's subscription 
        rate100(2)      - 100 octet/hour
        rate200(3)      - 200 octet/hour
        rate500(4)      - 500 octet/hour
        rate1k(5)       - 1000 octet/hour
        rate2k(6)       - 2000 octet/hour
        rate5k(7)       - 5000 octet/hour
        rate10k(8)      - 10000 octet/hour
        rate20k(9)      - 20000 octet/hour
        rate50k(10)     - 50000 octet/hour
        rate100k(11)    - 100000 octet/hour
        rate200k(12)    - 200000 octet/hour
        rate500k(13)    - 500000 octet/hour
        rate1m(14)      - 1000000 octet/hour
        rate2m(15)      - 2000000 octet/hour
        rate5m(16)      - 5000000 octet/hour
        rate10m(17)     - 10000000 octet/hour
        rate20m(18)     - 20000000 octet/hour
        rate50m(19)     - 50000000 octet/hour
        resv1(20)       - reserved 1 (for future use)
        resv2(21)       - reserved 2 (for future use)
        resv3(22)       - reserved 3 (for future use)
        resv4(23)       - reserved 4 (for future use)
        resv5(24)       - reserved 5 (for future use)
        resv6(25)       - reserved 6 (for future use)
        resv7(26)       - reserved 7 (for future use)
        resv8(27)       - reserved 8 (for future use)
        resv9(28)       - reserved 9 (for future use)
        resv10(29)      - reserved 10 (for future use)
        resv11(30)      - reserved 11 (for future use)
        resv12(31)      - reserved 12 (for future use)
        bestEffort(32)  - best effort"
    SYNTAX          INTEGER  {
                        subscription(1),
                        rate100(2),
                        rate200(3),
                        rate500(4),
                        rate1k(5),
                        rate2k(6),
                        rate5k(7),
                        rate10k(8),
                        rate20k(9),
                        rate50k(10),
                        rate100k(11),
                        rate200k(12),
                        rate500k(13),
                        rate1m(14),
                        rate2m(15),
                        rate5m(16),
                        rate10m(17),
                        rate20m(18),
                        rate50m(19),
                        resv1(20),
                        resv2(21),
                        resv3(22),
                        resv4(23),
                        resv5(24),
                        resv6(25),
                        resv7(26),
                        resv8(27),
                        resv9(28),
                        resv10(29),
                        resv11(30),
                        resv12(31),
                        bestEffort(32)
                    }
-- Textual Conventions definition will be defined before this objects

ciscoWan3gMIBNotifs  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIB 0 }

ciscoWan3gMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIB 1 }

ciscoWan3gMIBConform  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIB 2 }


c3gWanCommonTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gWanCommonEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular common objects table which is technology
        independent."
    ::= { ciscoWan3gMIBObjects 1 }

c3gWanCommonEntry OBJECT-TYPE
    SYNTAX          C3gWanCommonEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gWanCommonTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gWanCommonTable 1 }

C3gWanCommonEntry ::= SEQUENCE {
        c3gStandard                         INTEGER,
        c3gCapability                       C3gServiceCapability,
        c3gModemState                       INTEGER,
        c3gPreviousServiceType              C3gServiceCapability,
        c3gCurrentServiceType               C3gServiceCapability,
        c3gRoamingStatus                    INTEGER,
        c3gCurrentSystemTime                DisplayString,
        c3gConnectionStatus                 INTEGER,
        c3gNotifRadioService                C3gServiceCapability,
        c3gNotifRssi                        C3gRssi,
        c3gNotifEcIo                        C3gEcIo,
        c3gModemTemperature                 C3gTemperature,
        c3gRssiOnsetNotifThreshold          C3gRssi,
        c3gRssiAbateNotifThreshold          C3gRssi,
        c3gEcIoOnsetNotifThreshold          C3gEcIo,
        c3gEcIoAbateNotifThreshold          C3gEcIo,
        c3gModemTemperOnsetNotifThreshold   C3gTemperature,
        c3gModemTemperAbateNotifThreshold   C3gTemperature,
        c3gModemReset                       INTEGER,
        c3gModemUpNotifEnabled              TruthValue,
        c3gModemDownNotifEnabled            TruthValue,
        c3gServiceChangedNotifEnabled       TruthValue,
        c3gNetworkChangedNotifEnabled       TruthValue,
        c3gConnectionStatusChangedNotifFlag BITS,
        c3gRssiOnsetNotifFlag               C3gServiceCapability,
        c3gRssiAbateNotifFlag               C3gServiceCapability,
        c3gEcIoOnsetNotifFlag               C3gServiceCapability,
        c3gEcIoAbateNotifFlag               C3gServiceCapability,
        c3gModemTemperOnsetNotifEnabled     TruthValue,
        c3gModemTemperAbateNotifEnabled     TruthValue,
        c3gGpsState                         TruthValue
}

c3gStandard OBJECT-TYPE
    SYNTAX          INTEGER  {
                        cdma(1),
                        gsm(2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Cellular Standard: GSM (Global System for Mobile
        communications, 3GPP),  CDMA (Code Division Multiple 
        Access, 3GPP-2). GSM standard also include 4G-LTE 
        technology mode" 
    ::= { c3gWanCommonEntry 1 }

c3gCapability OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Cellular service capability. It currently includes the 2G,
        3G and 4G-LTE standard." 
    ::= { c3gWanCommonEntry 2 }

c3gModemState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        up(2),
                        down(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Cellular modem state, up(2) indicates modem can be detected
        and down(3) indicates modem can not be detected."
    DEFVAL          { down } 
    ::= { c3gWanCommonEntry 3 }

c3gPreviousServiceType OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the previous service type when service
        type changes." 
    ::= { c3gWanCommonEntry 4 }

c3gCurrentServiceType OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the current service type when service
        type changes." 
    ::= { c3gWanCommonEntry 5 }

c3gRoamingStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        roaming(2),
                        home(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Cellular current roaming status." 
    ::= { c3gWanCommonEntry 6 }

c3gCurrentSystemTime OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Cellular current system time received from base station." 
    ::= { c3gWanCommonEntry 7 }

c3gConnectionStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        error(2),
                        connecting(3),
                        dormant(4),
                        connected(5),
                        disconnected(6),
                        idle(7),
                        active(8),
                        inactive(9)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the current connection status." 
    ::= { c3gWanCommonEntry 8 }

c3gNotifRadioService OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is used as one of the var-bind object when
        notification for RSSI or Ec/Io is generated.  This object
        indicates which service generates the notification." 
    ::= { c3gWanCommonEntry 9 }

c3gNotifRssi OBJECT-TYPE
    SYNTAX          C3gRssi
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is used as one of the var-bind object when
        notification for RSSI is generated.  The relevant RSSI will be
        copied into c3gNotifRssi which corresponds to the service
        indicated in c3gNotifRadioService object.  This object will
        reflect the value of one of the following objects:
        c3gCurrent1xRttRssi, c3gCurrentEvDoRssi and c3gCurrentGsmRssi. 
        User should not use this object to get the current RSSI value as
        this object is used to indicate the RSSI value that triggers
        c3gRssiOnsetNotif or c3gRssiAbateNotif notification." 
    ::= { c3gWanCommonEntry 10 }

c3gNotifEcIo OBJECT-TYPE
    SYNTAX          C3gEcIo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object is used as one of the var-bind object when
        notification for Ec/Io is generated.  The relevant Ec/Io will
        be copied into c3gNotifEcIo which corresponds to the service
        indicated in c3gNotifRadioService object.  This object will
        reflect the value of one of the following objects:
        c3gCurrent1xRttEcIo, c3gCurrentEvDoEcIo and c3gCurrentGsmEcIo. 
        User should not use this object to get the current Ec/Io value
        as this object is used to indicate the Ec/Io value that triggers
        c3gEcIoOnsetNotif or c3gEcIoAbateNotif notification." 
    ::= { c3gWanCommonEntry 11 }

c3gModemTemperature OBJECT-TYPE
    SYNTAX          C3gTemperature
    UNITS           "degrees Celsius"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The modem temperature." 
    ::= { c3gWanCommonEntry 12 }

c3gRssiOnsetNotifThreshold OBJECT-TYPE
    SYNTAX          C3gRssi
    UNITS           "dBm"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The RSSI onset threshold value.  If RSSI goes below the
        threshold and the service bit in c3gRssiOnsetNotifFlag is 
        set, the c3gRssiOnsetNotif notification for that service 
        will be sent.  The absolute value of 
        c3gRssiAbateNotifThreshold should be less than or equal to 
        the absolute value of c3gRssiOnsetNotifThreshold 
        (|c3gRssiAbateNotifThreshold| <= |c3gRssiOnsetNotifThreshold|).
        e.g. setting c3gRssiAbateNotifThreshold to -115 dBm and then
        setting c3gRssiOnsetNotifThreshold to -110 dBm is not allowed
        and will be rejected."
    DEFVAL          { -150 } 
    ::= { c3gWanCommonEntry 13 }

c3gRssiAbateNotifThreshold OBJECT-TYPE
    SYNTAX          C3gRssi
    UNITS           "dBm"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The RSSI abate threshold value.  If RSSI goes above the
        threshold and the service bit in c3gRssiAbateNotifFlag is 
        set, the c3gRssiAbateNotif notification for that service will 
        be sent.  The absolute value of c3gRssiAbateNotifThreshold 
        should be less than or equal to the absolute value of 
        c3gRssiOnsetNotifThreshold (|c3gRssiAbateNotifThreshold| <= 
        |c3gRssiOnsetNotifThreshold|).  e.g. setting 
        c3gRssiAbateNotifThreshold to -115 dBm and then setting 
        c3gRssiOnsetNotifThreshold to -110 dBm is not allowed
        and will be rejected."
    DEFVAL          { 0 } 
    ::= { c3gWanCommonEntry 14 }

c3gEcIoOnsetNotifThreshold OBJECT-TYPE
    SYNTAX          C3gEcIo
    UNITS           "dB"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The EcIo onset threshold value.  If EcIo goes below the
        threshold and the service bit in c3gEcIoOnsetNotifFlag is set,
        the c3gEcIoOnsetNotif notification for that service will be 
        sent.  The absolute value of c3gEcIoAbateNotifThreshold should 
        be less than or equal to the absolute value of
        c3gEcIoOnsetNotifThreshold (|c3gEcIoAbateNotifThreshold| <=
        |c3gEcIoOnsetNotifThreshold|). e.g. setting
        c3gEcIoAbateNotifThreshold to -15 dB and then setting
        c3gEcIoOnsetNotifThreshold to -10 dB is not allowed and will
        be rejected."
    DEFVAL          { -150 } 
    ::= { c3gWanCommonEntry 15 }

c3gEcIoAbateNotifThreshold OBJECT-TYPE
    SYNTAX          C3gEcIo
    UNITS           "dB"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The threshold value that if EcIo goes above the threshold and
        the service bit in c3gEcIoAbateNotifFlag is set, the
        c3gEcIoAbateNotif notification for that service will be sent. 
        The absolute value of c3gEcIoAbateNotifThreshold
        should be less than or equal to the absolute value of
        c3gEcIoOnsetNotifThreshold (|c3gEcIoAbateNotifThreshold| <=
        |c3gEcIoOnsetNotifThreshold|). e.g. setting
        c3gEcIoOnsetNotifThreshold to -15 dB and then setting
        c3gEcIoAbateNotifThreshold to -10 dB is not allowed and will
        be rejected."
    DEFVAL          { 0 } 
    ::= { c3gWanCommonEntry 16 }

c3gModemTemperOnsetNotifThreshold OBJECT-TYPE
    SYNTAX          C3gTemperature
    UNITS           "degrees Celsius"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The modem temperature onset threshold value.  If modem
        temperature goes above the threshold and the value of
        c3gModemTemperOnsetNotifEnabled is 'true',
        the c3gModemTemperOnsetNotif notification will be sent. 
        The value of c3gModemTemperAbateNotifThreshold should be less
        than or equal to the value of
        c3gModemTemperOnsetNotifThreshold. e.g. setting 
        c3gModemTemperAbateNotifThreshold to 50 degree Celsius and 
        then setting c3gModemTemperOnsetNotifThreshold to
        40 degree Celsius is not allowed and will be rejected."
    DEFVAL          { 100 } 
    ::= { c3gWanCommonEntry 17 }

c3gModemTemperAbateNotifThreshold OBJECT-TYPE
    SYNTAX          C3gTemperature
    UNITS           "degrees Celsius"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The modem temperature abate threshold value.  If modem
        temperature goes below the threshold and the value of
        c3gModemTemperAbateNotifEnabled is 'true', the 
        c3gModemTemperAbateNotif notification will be sent. The value
        of c3gModemTemperAbateNotifThreshold should be less than or 
        equal to the value of c3gModemTemperOnsetNotifThreshold.  
        e.g. setting c3gModemTemperAbateNotifThreshold to 50 degree
        Celsius and then setting c3gModemTemperOnsetNotifThreshold
        to 40 degree Celsius is not allowed and will be rejected."
    DEFVAL          { -50 } 
    ::= { c3gWanCommonEntry 18 }

c3gModemReset OBJECT-TYPE
    SYNTAX          INTEGER  {
                        reset(1),
                        powerCycle(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to reset or power-cycle the modem." 
    ::= { c3gWanCommonEntry 19 }

c3gModemUpNotifEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable/disable the generation of modem
        up notification c3gModemUpNotif."
    DEFVAL          { false } 
    ::= { c3gWanCommonEntry 20 }

c3gModemDownNotifEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable/disable the generation of modem
        down notification c3gModemDownNotif."
    DEFVAL          { false } 
    ::= { c3gWanCommonEntry 21 }

c3gServiceChangedNotifEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable/disable the generation of service
        changed notification c3gServiceChangedNotif."
    DEFVAL          { false } 
    ::= { c3gWanCommonEntry 22 }

c3gNetworkChangedNotifEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable/disable the generation of network
        changed notification c3gNetworkChangedNotif."
    DEFVAL          { false } 
    ::= { c3gWanCommonEntry 23 }

c3gConnectionStatusChangedNotifFlag OBJECT-TYPE
    SYNTAX          BITS {
                        unknown(0),
                        error(1),
                        connecting(2),
                        dormant(3),
                        connected(4),
                        disconnected(5),
                        idle(6),
                        active(7),
                        inactive(8)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is the flag bitmap to control the generation of
        notification c3gConnectionStatusChangedNotif.  e.g. setting bit
        0 (error(0)) to 1 and bit 4 (disconnected(4)) to 1 will cause
        the notification c3gConnectionStatusChangedNotif to be
        generated when object c3gConnetionStatus changes the status to 
        error or disconnected.  The default value of this object is 
        '00'H." 
    ::= { c3gWanCommonEntry 24 }

c3gRssiOnsetNotifFlag OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is the flag bitmap to control the generation of
        notification c3gRssiOnsetNotif.  Each bit represents a service
        as defined in C3gServiceCapability, set the bit value to 1 to
        enable (and 0 to disable) the generation of notification
        c3gRssiOnsetNotif for that service.  The default value of this
        object is all bits are 0." 
    ::= { c3gWanCommonEntry 25 }

c3gRssiAbateNotifFlag OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is the flag bitmap to control the generation of
        notification c3gRssiAbateNotif.  Each bit represents a service
        as defined in C3gServiceCapability, set the bit value to 1 to
        enable (and 0 to disable) the generation of notification
        c3gRssiAbateNotif for that service.  The default value of this
        object is all bits are 0." 
    ::= { c3gWanCommonEntry 26 }

c3gEcIoOnsetNotifFlag OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is the flag bitmap to control the generation of
        notification c3gEcIoOnsetNotif.  Each bit represents a service
        as defined in C3gServiceCapability, set the bit value to 1 to
        enable (and 0 to disable) the generation of notification
        c3gEcIoOnsetNotif for that service.  The default value of this
        object is all bits are 0." 
    ::= { c3gWanCommonEntry 27 }

c3gEcIoAbateNotifFlag OBJECT-TYPE
    SYNTAX          C3gServiceCapability
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is the flag bitmap to control the generation of
        notification c3gEcIoAbateNotif.  Each bit represents a service
        as defined in C3gServiceCapability, set the bit value to 1 to
        enable (and 0 to disable) the generation of notification
        c3gEcIoAbateNotif for that service.  The default value of this
        object is all bits are 0." 
    ::= { c3gWanCommonEntry 28 }

c3gModemTemperOnsetNotifEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable/disable the generation of
        c3gModemTemperOnsetNotif notification."
    DEFVAL          { false } 
    ::= { c3gWanCommonEntry 29 }

c3gModemTemperAbateNotifEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to enable/disable the generation of
        c3gModemTemperAbateNotif notification."
    DEFVAL          { false } 
    ::= { c3gWanCommonEntry 30 }

c3gGpsState OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object is used to determine or enable/disable
        the GPS state."
    DEFVAL          { false } 
    ::= { c3gWanCommonEntry 31 }
 

c3gWanCdma  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIBObjects 2 }

c3gWanGsm  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIBObjects 3 }

c3gWanLbs  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIBObjects 4 }

c3gWanSmsCommon  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIBObjects 5 }


c3gCdmaSessionTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaSessionEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table describes wireless session (link) created when a
        modem connects to a particular cellular network. One or more
        logical calls can be placed over wireless session(link).  These
        logical calls are represented in the c3gCdmaConnectionTable."
    ::= { c3gWanCdma 1 }

c3gCdmaSessionEntry OBJECT-TYPE
    SYNTAX          C3gCdmaSessionEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaSessionTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdmaSessionTable 1 }

C3gCdmaSessionEntry ::= SEQUENCE {
        c3gCdmaTotalCallDuration Counter64,
        c3gCdmaTotalTransmitted  Counter64,
        c3gCdmaTotalReceived     Counter64,
        c3gHdrDdtmPreference     INTEGER
}

c3gCdmaTotalCallDuration OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total duration of all calls." 
    ::= { c3gCdmaSessionEntry 1 }

c3gCdmaTotalTransmitted OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total data transmitted for all calls.  It is the total amount
        of data transmitted by modem, not to be confused with the number
        of bytes transmitted through the interface." 
    ::= { c3gCdmaSessionEntry 2 }

c3gCdmaTotalReceived OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total data received for all calls.  It is the total amount of
        data received by modem, not to be confused with the number of
        bytes received from the interface." 
    ::= { c3gCdmaSessionEntry 3 }

c3gHdrDdtmPreference OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        off(2),
                        on(3),
                        noChange(4)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "HDR Data Dedicated Transmission Mode (DDTM) preference:
        unknown(1)  - DDTM preference is unknown
        off(2)      - DDTM preference set to OFF
        on(3)       - DDTM preference set to ON
        noChange(4) - DDTM preference is no change"
    DEFVAL          { unknown } 
    ::= { c3gCdmaSessionEntry 4 }
 


c3gCdmaConnectionTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaConnectionEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA connection table.  This table describes
        logical connections/calls over wireless link, the wireless link
        is described in c3gCdmaSessionTable."
    ::= { c3gWanCdma 2 }

c3gCdmaConnectionEntry OBJECT-TYPE
    SYNTAX          C3gCdmaConnectionEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaConnectionTable."
    INDEX           { ifIndex } 
    ::= { c3gCdmaConnectionTable 1 }

C3gCdmaConnectionEntry ::= SEQUENCE {
        c3gOutgoingCallNumber        DisplayString,
        c3gHdrAtState                INTEGER,
        c3gHdrSessionState           INTEGER,
        c3gUati                      DisplayString,
        c3gColorCode                 Unsigned32,
        c3gRati                      Unsigned32,
        c3gHdrSessionDuration        Unsigned32,
        c3gHdrSessionStart           Unsigned32,
        c3gHdrSessionEnd             Unsigned32,
        c3gAuthStatus                INTEGER,
        c3gHdrDrc                    Unsigned32,
        c3gHdrDrcCover               Unsigned32,
        c3gHdrRri                    INTEGER,
        c3gMobileIpErrorCode         Integer32,
        c3gCdmaCurrentTransmitted    Counter64,
        c3gCdmaCurrentReceived       Counter64,
        c3gCdmaCurrentCallStatus     INTEGER,
        c3gCdmaCurrentCallDuration   Unsigned32,
        c3gCdmaCurrentCallType       INTEGER,
        c3gCdmaLastCallDisconnReason INTEGER,
        c3gCdmaLastConnError         INTEGER
}

c3gOutgoingCallNumber OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Phone number of outgoing call." 
    ::= { c3gCdmaConnectionEntry 1 }

c3gHdrAtState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        inactive(2),
                        acquisition(3),
                        sync(4),
                        idle(5),
                        access(6),
                        connected(7)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "High Data Rate (HDR) Access Terminal (AT) state."
    DEFVAL          { unknown } 
    ::= { c3gCdmaConnectionEntry 2 }

c3gHdrSessionState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        open(2),
                        close(3),
                        addressManagementProtocolSetup(4),
                        atInitiated(5),
                        anInitiated(6)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "High Data Rate (HDR) session state." 
    ::= { c3gCdmaConnectionEntry 3 }

c3gUati OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Unicast Access Terminal Identifier (UATI), AT seeking access
        to the 1 times EV-DO system receives a UATI allocated from the 
        system after setting up a radio traffic channel with a base 
        station." 
    ::= { c3gCdmaConnectionEntry 4 }

c3gColorCode OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Color code.  A sync channel may be used by the base station
        to communicate administrative information to a mobile station. 
        For example, a base station may transmit a base station ID to a
        user, a color code and administrative information identifying 
        system status." 
    ::= { c3gCdmaConnectionEntry 5 }

c3gRati OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Random Access Terminal Identifier (RATI).  AT transmits a
        UATI request message to the ANC using the RATI to make a UATI 
        allocation request." 
    ::= { c3gCdmaConnectionEntry 6 }

c3gHdrSessionDuration OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "HDR connection session duration.  It is the duration
        between c3gHdrSessionStart and c3gHdrSessionEnd."
    DEFVAL          { 0 } 
    ::= { c3gCdmaConnectionEntry 7 }

c3gHdrSessionStart OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "HDR connection session starting time."
    DEFVAL          { 0 } 
    ::= { c3gCdmaConnectionEntry 8 }

c3gHdrSessionEnd OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "HDR connection session ending time."
    DEFVAL          { 0 } 
    ::= { c3gCdmaConnectionEntry 9 }

c3gAuthStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        notAuthenticated(2),
                        authenticated(3),
                        failed(4),
                        authenticationDisabled(5)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Connection authentication status:
        unknown(1)                - authentication status is unknown
        notAuthenticated(2)       - not yet authenticated.
        authenticated(3)          - authenticated.
        failed(4)                 - authentication failed.
        authenticationDisabled(5) - authentication disabled"
    DEFVAL          { unknown } 
    ::= { c3gCdmaConnectionEntry 10 }

c3gHdrDrc OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "High Data Rate (HDR) Data Rate Control (DRC).  AT provides
        requests for data transmissions by sending a Data Rate Control,
        DRC, message via a specific channel referred to as the DRC 
        channel."
    DEFVAL          { 0 } 
    ::= { c3gCdmaConnectionEntry 11 }

c3gHdrDrcCover OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "HDR DRC cover.  The DRC cover is a coding applied to
        identify the sector from which the data is to be transmitted. 
        In one embodiment, the DRC cover is a Walsh code applied to 
        the DRC value, wherein a unique code corresponds to each sector
        in the Active Set of the AT."
    DEFVAL          { 0 } 
    ::= { c3gCdmaConnectionEntry 12 }

c3gHdrRri OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        pilotOnly(2),
                        rri9dot6kbps(3),
                        rri19dot2kbps(4),
                        rri38dot4kbps(5),
                        rri76dot8kbps(6),
                        rri153dot6kbps(7)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "HDR Rate Request Indicator (RRI).  RRI provides the
        structure of a frame currently being transmitted when frames 
        are transmitted at different rates. Services at different rates
        are reliably provided by the RRI:
           unknown(1)        - RRI unknown
           pilotOnly(2)      - pilot channel only
           rri9dot6kbps(3)   - RRI is 9.6 Kbit/s
           rri19dot2kbps(4)  - RRI is 19.2 Kbit/s
           rri38dot4kbps(5)  - RRI is 38.4 Kbit/s
           rri76dot8kbps(6)  - RRI is 76.8 Kbit/s
           rri153dot6kbps(7) - RRI is 153.6 Kbit/s"
    DEFVAL          { unknown } 
    ::= { c3gCdmaConnectionEntry 13 }

c3gMobileIpErrorCode OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Mobile IP error code (please refer to RFC 2002)." 
    ::= { c3gCdmaConnectionEntry 14 }

c3gCdmaCurrentTransmitted OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current number of bytes transmitted by modem for current
        connection." 
    ::= { c3gCdmaConnectionEntry 15 }

c3gCdmaCurrentReceived OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current number of bytes received by modem for current
        connection." 
    ::= { c3gCdmaConnectionEntry 16 }

c3gCdmaCurrentCallStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        error(2),
                        connecting(3),
                        dormant(4),
                        connected(5),
                        disconnected(6)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current call status." 
    ::= { c3gCdmaConnectionEntry 17 }

c3gCdmaCurrentCallDuration OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current call duration."
    DEFVAL          { 0 } 
    ::= { c3gCdmaConnectionEntry 18 }

c3gCdmaCurrentCallType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        asyncData(2),
                        voiceCall(3),
                        packet1xRttCall(4),
                        atAsyncDataCall(5),
                        atVoiceCall(6),
                        atPacketCall(7),
                        faxCall(8),
                        smsCall(9),
                        otaCall(10),
                        testCall(11),
                        callWaiting(12),
                        positionDetermination(13),
                        dormant(14),
                        e911Call(15)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current call type." 
    ::= { c3gCdmaConnectionEntry 19 }

c3gCdmaLastCallDisconnReason OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        modemOffline(2),
                        modemCdmaLocTilPowCyc(3),
                        noService(4),
                        abnormalCallEnd(5),
                        baseStatIntercept(6),
                        baseStatRelease(7),
                        baseStatReleaseNoReas(8),
                        baseStatReleaseSoRej(9),
                        incomingCall(10),
                        baseStatAlertStop(11),
                        clientEndedCall(12),
                        activationEndedOtasp(13),
                        ndssFailure(14),
                        maxAccesProbTransmit(15),
                        persistTestFailure(16),
                        ruimNotPresent(17),
                        accessAttemptInProg(18),
                        reasonUnspecified(19),
                        recdRetryOrder(20),
                        modemLocked(21),
                        gpsCallEnded(22),
                        smsCallEnded(23),
                        noConcurrentService(24),
                        noResponseFromBs(25),
                        rejectedByBs(26),
                        notCompatConcurServ(27),
                        accessBlockedByBs(28),
                        alreadyOnTraffChann(29),
                        emergencyCall(30),
                        dataCallEnded(31),
                        busyHdr(32),
                        billingOrAuthErrHdr(33),
                        sysChangeDueToPrlHdr(34),
                        hdrExitDueToPrl(35),
                        noSessionHdr(36),
                        callEndedHdr(37)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Last call disconnect reason:
        unknown(1)               - Unknown
        modemOffline(2)          - Modem offline
        modemCdmaLocTilPowCyc(3) - Modem CDMA locked till power cycle
        noService(4)             - No service
        abnormalCallEnd(5)       - Abnormal call end
        baseStatIntercept(6)     - Base station intercept
        baseStatRelease(7)       - Base station release
        baseStatReleaseNoReas(8) - Base station release (No reason)
        baseStatReleaseSoRej(9)  - Base station release (SO reject)
        incomingCall(10)         - Incoming call
        baseStatAlertStop(11)    - Base station alert stop
        clientEndedCall(12)      - Client ended call
        activationEndedOtasp(13) - Activation ended OTASP (Over-
                                   The-Air Service Provisioning)
        ndssFailure(14)          - NDSS (Network and Distributed 
                                   System Security) failure
        maxAccesProbTransmit(15) - Max access probes transmitted
        persistTestFailure(16)   - Persistence test failure
        ruimNotPresent(17)       - RUIM (Removable User Identity 
                                   Module) not present
        accessAttemptInProg(18)  - Access attempt in progress
        reasonUnspecified(19)    - Reason unspecified
        recdRetryOrder(20)       - Recd retry order
        modemLocked(21)          - Modem Locked
        gpsCallEnded(22)         - GPS call ended
        smsCallEnded(23)         - SMS (Short Message Service) 
                                   call ended
        noConcurrentService(24)  - No concurrent service
        noResponseFromBs(25)     - No response from BS (Base station)
        rejectedByBs(26)         - Rejected by BS
        notCompatConcurServ(27)  - Not compatible concurrent service
        accessBlockedByBs(28)    - Access blocked by BS
        alreadyOnTraffChann(29)  - Already on Traffic channel
        emergencyCall(30)        - Emergency call
        dataCallEnded(31)        - Data call ended
        busyHdr(32)              - Busy (HDR)
        billingOrAuthErrHdr(33)  - Billing or Auth error (HDR)
        sysChangeDueToPrlHdr(34) - System change due to PRL (HDR)
        hdrExitDueToPrl(35)      - HDR exit due to PRL (HDR)
        noSessionHdr(36)         - No Session (HDR)
        callEndedHdr(37)         - Call ended (HDR)" 
    ::= { c3gCdmaConnectionEntry 20 }

c3gCdmaLastConnError OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        invalidClientId(2),
                        badCallType(3),
                        badServiceType(4),
                        expectingNumber(5),
                        nullNumberBuffer(6),
                        invalidDigits(7),
                        outOfRangeNumber(8),
                        nullAalphaBuffer(9),
                        outOfRangeAlphaNumber(10),
                        invalidOtaspActivatCode(11),
                        modemOffline(12),
                        modemLocked(13),
                        unsupportedFlash(14),
                        dialedNumberProhibited(15),
                        onlyE911Calls(16),
                        modemInUse(17),
                        unsupportedServiceType(18),
                        wrongCallType(19),
                        invalidCommandCallState(20),
                        invalidCommandModemState(21),
                        noValidService(22),
                        cannotAnswerIncomingCall(23),
                        badPrivacySetting(24),
                        noCommandBuffers(25),
                        communicationProblem(26),
                        unspecifiedError(27),
                        invalidLastActiveNetwork(28),
                        noCollocatedHdr(29),
                        uimNotPresent(30)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Last connect error:
        none(1)                      - None
        invalidClientId(2)           - Invalid client ID
        badCallType(3)               - Bad call type
        badServiceType(4)            - Bad service type
        expectingNumber(5)           - Expecting number
        nullNumberBuffer(6)          - Null number buffer
        invalidDigits(7)             - Invalid digits
        outOfRangeNumber(8)          - Out of range number
        nullAalphaBuffer(9)          - Null alpha buffer
        outOfRangeAlphaNumber(10)    - Out of range alpha number
        invalidOtaspActivatCode(11)  - Invalid OTASP activation code
        modemOffline(12)             - Modem offline
        modemLocked(13)              - Modem locked
        unsupportedFlash(14)         - Unsupported flash
        dialedNumberProhibited(15)   - Dialed number prohibited
        onlyE911Calls(16)            - Only E911 calls
        modemInUse(17)               - Modem in use
        unsupportedServiceType(18)   - Unsupported service type
        wrongCallType(19)            - Wrong call type
        invalidCommandCallState(20)  - Invalid command (call state)
        invalidCommandModemState(21) - Invalid command (modem state)
        noValidService(22)           - No valid service
        cannotAnswerIncomingCall(23) - Cannot answer incoming call
        badPrivacySetting(24)        - Bad privacy setting
        noCommandBuffers(25)         - No command buffers
        communicationProblem(26)     - Communication problem
        unspecifiedError(27)         - Unspecified error
        invalidLastActiveNetwork(28) - Invalid last active network
        noCollocatedHdr(29)          - No collocated HDR
        uimNotPresent(30)            - UIM (User Identity Module) not   
                                       present" 
    ::= { c3gCdmaConnectionEntry 21 }
 


c3gCdmaIdentityTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaIdentityEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA identity table."
    ::= { c3gWanCdma 3 }

c3gCdmaIdentityEntry OBJECT-TYPE
    SYNTAX          C3gCdmaIdentityEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaIdentityTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdmaIdentityTable 1 }

C3gCdmaIdentityEntry ::= SEQUENCE {
        c3gEsn                   DisplayString,
        c3gModemActivationStatus INTEGER,
        c3gAccountActivationDate DisplayString,
        c3gCdmaRoamingPreference INTEGER,
        c3gPrlVersion            Unsigned32,
        c3gMdn                   DisplayString,
        c3gMsid                  DisplayString,
        c3gMsl                   DisplayString
}

c3gEsn OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..128))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates Electronic Serial Number (ESN)." 
    ::= { c3gCdmaIdentityEntry 1 }

c3gModemActivationStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        activated(2),
                        notActivated(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Modem activation status."
    DEFVAL          { unknown } 
    ::= { c3gCdmaIdentityEntry 2 }

c3gAccountActivationDate OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..128))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Modem account activation date." 
    ::= { c3gCdmaIdentityEntry 3 }

c3gCdmaRoamingPreference OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        home(2),
                        affiliated(3),
                        any(4)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates the roaming preference:
        unknown(1)    - preference unknown
        home(2)       - home networks only
        affiliated(3) - roaming on affiliated networks
        any(4)        - roaming on any network"
    DEFVAL          { any } 
    ::= { c3gCdmaIdentityEntry 4 }

c3gPrlVersion OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Preferred Roaming List (PRL) version."
    DEFVAL          { 0 } 
    ::= { c3gCdmaIdentityEntry 5 }

c3gMdn OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Directory Number (MDN), a dialable number assigned to a
        wireless phone." 
    ::= { c3gCdmaIdentityEntry 6 }

c3gMsid OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Station Identifier (MSID), MSID is utilized to
        distinguish the mobile station being programmed from other 
        mobile stations during messaging and paging processes, 
        including the downloading of programming information to the 
        mobile station." 
    ::= { c3gCdmaIdentityEntry 7 }

c3gMsl OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Subscriber Lock (MSL)." 
    ::= { c3gCdmaIdentityEntry 8 }
 


c3gCdmaNetworkTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaNetworkEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA network table."
    ::= { c3gWanCdma 4 }

c3gCdmaNetworkEntry OBJECT-TYPE
    SYNTAX          C3gCdmaNetworkEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaNetworkTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdmaNetworkTable 1 }

C3gCdmaNetworkEntry ::= SEQUENCE {
        c3gCdmaCurrentServiceStatus    DisplayString,
        c3gCdmaHybridModePreference    INTEGER,
        c3gCdmaCurrentRoamingStatus    INTEGER,
        c3gCurrentIdleDigitalMode      INTEGER,
        c3gCurrentSid                  Integer32,
        c3gCurrentNid                  Integer32,
        c3gCurrentCallSetupMode        INTEGER,
        c3gSipUsername                 DisplayString,
        c3gSipPassword                 DisplayString,
        c3gServingBaseStationLongitude DisplayString,
        c3gServingBaseStationLatitude  DisplayString
}

c3gCdmaCurrentServiceStatus OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current service status." 
    ::= { c3gCdmaNetworkEntry 1 }

c3gCdmaHybridModePreference OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        hybrid(2),
                        evDoOnly(3),
                        oneXRttOnly(4)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates the hybrid mode preference:
        unknown(1)     - preference unknown
        hybrid(2)      - connect to EV-DO/1xRTT services
        evDoOnly(3)    - connect to only EV-DO service
        oneXRttOnly(4) - connect to only 1xRTT service"
    DEFVAL          { unknown } 
    ::= { c3gCdmaNetworkEntry 2 }

c3gCdmaCurrentRoamingStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        home(2),
                        roamingWithSid(3),
                        roamingWithoutSid(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current 1xRTT roaming status, roaming is a general term in
        wireless telecommunications that refers to the extending of 
        connectivity service in a location that is different from the 
        home location where the service was registered:
           unknown(1)           - roaming status is unknown.
           home(2)              - connectivity service in home location.
           roamingWithSid(3)    - roaming with SID.
           roamingWithoutSid(4) - roaming without SID"
    DEFVAL          { unknown } 
    ::= { c3gCdmaNetworkEntry 3 }

c3gCurrentIdleDigitalMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        noService(2),
                        amps(3),
                        cdma(4),
                        gsm(5),
                        hdr(6),
                        wcdma(7),
                        gps(8),
                        lte(9)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current idle digital mode:
        unknown(1)   - service is unknown
        noService(2) - no service
        amps(3)      - Advanced Mobile Phone Service (AMPS)
        cdma(4)      - Code Division Multiple Access (CDMA)
        gsm(5)       - Global System for Mobile communications (GSM)
        hdr(6)       - High Data Rate (HDR)
        wcdma(7)     - Wideband Code-Division Multiple-Access (WCDMA)
        gps(8)       - Global Positioning System (GPS)
        lte(9)       - Long Term Evolution (LTE)"
    DEFVAL          { noService } 
    ::= { c3gCdmaNetworkEntry 4 }

c3gCurrentSid OBJECT-TYPE
    SYNTAX          Integer32 (-1..32767)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Current System Identifier (SID), SID is a 15-bit numeric
        identifiers used by cellular systems to identify the 
        home system of a cellular telephone and by the cellular 
        telephone to determine its roaming status.  Value of '-1'
        indicates SID is 'Not Applicable'." 
    ::= { c3gCdmaNetworkEntry 5 }

c3gCurrentNid OBJECT-TYPE
    SYNTAX          Integer32 (-1..65535)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Current Network Identification (NID), NID is a 16-bit numeric
        identifiers used by cellular systems.  Value of '-1' indicates
        NID is 'Not Applicable'." 
    ::= { c3gCdmaNetworkEntry 6 }

c3gCurrentCallSetupMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        simpleIpOnly(2),
                        mobileIpPreferWithSipFallback(3),
                        mobileIpOnly(4)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Current call setup mode.  The 1xEV-DO system supports packet
        data connections to a public or private data network using 
        either mobile IP or simple IP protocol.  For simple IP 
        protocol, moving from the coverage area of one PDSN to another
        PDSN constitutes a change in packet data session.  For mobile 
        IP protocol, a packet data session can span several PDSNs as 
        long as the user continuously maintains mobility bindings at 
        the Home Agent (the IP address is persistent).  The modes are:
        unknown(1)                       - mode is unknown
        simpleIpOnly(2)                  - simple IP only
        mobileIpPreferWithSipFallback(3) - prefer mobile IP with 
                                           simple IP as fallback mode
        mobileIpOnly(4)                  - mobile IP only"
    DEFVAL          { unknown } 
    ::= { c3gCdmaNetworkEntry 7 }

c3gSipUsername OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Simple IP (SIP) user name." 
    ::= { c3gCdmaNetworkEntry 8 }

c3gSipPassword OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Simple IP (SIP) password." 
    ::= { c3gCdmaNetworkEntry 9 }

c3gServingBaseStationLongitude OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Longitude of the serving base station." 
    ::= { c3gCdmaNetworkEntry 10 }

c3gServingBaseStationLatitude OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Latitude of the serving base station." 
    ::= { c3gCdmaNetworkEntry 11 }
 

c3gCdmaProfile  OBJECT IDENTIFIER
    ::= { c3gWanCdma 5 }


c3gCdmaProfileCommonTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaProfileCommonEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA profile common table."
    ::= { c3gCdmaProfile 1 }

c3gCdmaProfileCommonEntry OBJECT-TYPE
    SYNTAX          C3gCdmaProfileCommonEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaProfileCommonTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdmaProfileCommonTable 1 }

C3gCdmaProfileCommonEntry ::= SEQUENCE {
        c3gNumberOfDataProfileConfigurable Unsigned32,
        c3gCurrentActiveDataProfile        Unsigned32
}

c3gNumberOfDataProfileConfigurable OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The number of data profiles configurable."
    DEFVAL          { 0 } 
    ::= { c3gCdmaProfileCommonEntry 1 }

c3gCurrentActiveDataProfile OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The current active data profile." 
    ::= { c3gCdmaProfileCommonEntry 2 }
 


c3gCdmaProfileTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaProfileEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA profile table."
    ::= { c3gCdmaProfile 2 }

c3gCdmaProfileEntry OBJECT-TYPE
    SYNTAX          C3gCdmaProfileEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaProfileTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gCdmaProfileIndex
                    } 
    ::= { c3gCdmaProfileTable 1 }

C3gCdmaProfileEntry ::= SEQUENCE {
        c3gCdmaProfileIndex        Integer32,
        c3gNai                     DisplayString,
        c3gAaaPassword             DisplayString,
        c3gMnHaSs                  INTEGER,
        c3gMnHaSpi                 Unsigned32,
        c3gMnAaaSs                 INTEGER,
        c3gMnAaaSpi                Unsigned32,
        c3gReverseTunnelPreference INTEGER,
        c3gHomeAddrType            InetAddressType,
        c3gHomeAddr                InetAddress,
        c3gPriHaAddrType           InetAddressType,
        c3gPriHaAddr               InetAddress,
        c3gSecHaAddrType           InetAddressType,
        c3gSecHaAddr               InetAddress
}

c3gCdmaProfileIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..65535)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Profile index, combined with entPhysicalIndex to access
        the profile table c3gCdmaProfileTable." 
    ::= { c3gCdmaProfileEntry 1 }

c3gNai OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Network Access Identifier (NAI).  NAI is required to identify
        the mobile user and the network the mobile user intended to
        access.  The NAI is provided by the mobile node to the dialed
        ISP during PPP authentication." 
    ::= { c3gCdmaProfileEntry 2 }

c3gAaaPassword OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates the password for AAA." 
    ::= { c3gCdmaProfileEntry 3 }

c3gMnHaSs OBJECT-TYPE
    SYNTAX          INTEGER  {
                        set(1),
                        notSet(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Node (MN) Home Agent (HA) Shared Secret (SS) setting:
        set(1)    - shared secret is set
        notSet(2) - shared secret is not set"
    DEFVAL          { notSet } 
    ::= { c3gCdmaProfileEntry 4 }

c3gMnHaSpi OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Node (MN) Home Agent (HA) Security Parameter Index
        (SPI)." 
    ::= { c3gCdmaProfileEntry 5 }

c3gMnAaaSs OBJECT-TYPE
    SYNTAX          INTEGER  {
                        set(1),
                        notSet(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Node (MN) Authentication Authorization Accounting (AAA)
        Shared Secret (SS) setting:
        set(1)    - shared secret is set
        notSet(2) - shared secret is not set"
    DEFVAL          { notSet } 
    ::= { c3gCdmaProfileEntry 6 }

c3gMnAaaSpi OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Node (MN) Authentication Authorization Accounting (AAA)
        Security Parameter Index (SPI)." 
    ::= { c3gCdmaProfileEntry 7 }

c3gReverseTunnelPreference OBJECT-TYPE
    SYNTAX          INTEGER  {
                        set(1),
                        notSet(2)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Reverse tunnel preference." 
    ::= { c3gCdmaProfileEntry 8 }

c3gHomeAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "A value that represents the type of the IP Address stored in
        the object c3gHomeAddr." 
    ::= { c3gCdmaProfileEntry 9 }

c3gHomeAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "A unicast routable address assigned to a Mobile Node, used as
        the permanent address of the Mobile Node." 
    ::= { c3gCdmaProfileEntry 10 }

c3gPriHaAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "A value that represents the type of the IP Address stored in
        the object c3gPriHaAddr." 
    ::= { c3gCdmaProfileEntry 11 }

c3gPriHaAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The primary home agent address." 
    ::= { c3gCdmaProfileEntry 12 }

c3gSecHaAddrType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "A value that represents the type of the IP Address stored in
        the object c3gSecHaAddr." 
    ::= { c3gCdmaProfileEntry 13 }

c3gSecHaAddr OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The secondary home agent address." 
    ::= { c3gCdmaProfileEntry 14 }
 

c3gCdmaRadio  OBJECT IDENTIFIER
    ::= { c3gWanCdma 6 }


c3gCdma1xRttRadioTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdma1xRttRadioEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA 1xRTT radio table."
    ::= { c3gCdmaRadio 1 }

c3gCdma1xRttRadioEntry OBJECT-TYPE
    SYNTAX          C3gCdma1xRttRadioEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdma1xRttRadioTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdma1xRttRadioTable 1 }

C3gCdma1xRttRadioEntry ::= SEQUENCE {
        c3gCurrent1xRttRssi          C3gRssi,
        c3gCurrent1xRttEcIo          C3gEcIo,
        c3gCurrent1xRttChannelNumber Integer32,
        c3gCurrent1xRttChannelState  INTEGER
}

c3gCurrent1xRttRssi OBJECT-TYPE
    SYNTAX          C3gRssi
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current 1xRTT RSSI value." 
    ::= { c3gCdma1xRttRadioEntry 1 }

c3gCurrent1xRttEcIo OBJECT-TYPE
    SYNTAX          C3gEcIo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current 1xRTT Ec/Io value." 
    ::= { c3gCdma1xRttRadioEntry 2 }

c3gCurrent1xRttChannelNumber OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current 1xRTT channel number.  Current channel number to which
        the modem is attached to the base station.  Value of '-1'
        indicates 'No Service'." 
    ::= { c3gCdma1xRttRadioEntry 3 }

c3gCurrent1xRttChannelState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        notAcquired(2),
                        acquired(3),
                        scanning(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current 1xRTT channel state.  Indicates whether the modem is
        scanning or has acquired the channel."
    DEFVAL          { notAcquired } 
    ::= { c3gCdma1xRttRadioEntry 4 }
 


c3gCdma1xRttBandClassTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdma1xRttBandClassEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA 1xRTT band class table.  This table contains
        band class information for each available band."
    ::= { c3gCdmaRadio 2 }

c3gCdma1xRttBandClassEntry OBJECT-TYPE
    SYNTAX          C3gCdma1xRttBandClassEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdma1xRttBandClassTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gBandClassIndex
                    } 
    ::= { c3gCdma1xRttBandClassTable 1 }

C3gCdma1xRttBandClassEntry ::= SEQUENCE {
        c3gBandClassIndex Integer32,
        c3g1xRttBandClass Unsigned32
}

c3gBandClassIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..32)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Band class index, combined with entPhysicalIndex to access
        the band class table." 
    ::= { c3gCdma1xRttBandClassEntry 1 }

c3g1xRttBandClass OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object contains 1xRTT band class." 
    ::= { c3gCdma1xRttBandClassEntry 2 }
 


c3gCdmaEvDoRadioTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaEvDoRadioEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA EV-DO radio table."
    ::= { c3gCdmaRadio 3 }

c3gCdmaEvDoRadioEntry OBJECT-TYPE
    SYNTAX          C3gCdmaEvDoRadioEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaEvDoRadioTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdmaEvDoRadioTable 1 }

C3gCdmaEvDoRadioEntry ::= SEQUENCE {
        c3gCurrentEvDoRssi            C3gRssi,
        c3gCurrentEvDoEcIo            C3gEcIo,
        c3gCurrentEvDoChannelNumber   Integer32,
        c3gSectorId                   DisplayString,
        c3gSubnetMask                 Unsigned32,
        c3gHdrColorCode               Unsigned32,
        c3gPnOffset                   Unsigned32,
        c3gRxMainGainControl          Integer32,
        c3gRxDiversityGainControl     Integer32,
        c3gTxTotalPower               Integer32,
        c3gTxGainAdjust               Integer32,
        c3gCarrierToInterferenceRatio Unsigned32
}

c3gCurrentEvDoRssi OBJECT-TYPE
    SYNTAX          C3gRssi
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current EV-DO RSSI value." 
    ::= { c3gCdmaEvDoRadioEntry 1 }

c3gCurrentEvDoEcIo OBJECT-TYPE
    SYNTAX          C3gEcIo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current EV-DO Ec/Io value." 
    ::= { c3gCdmaEvDoRadioEntry 2 }

c3gCurrentEvDoChannelNumber OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current EV-DO channel number.  Current channel number to
        which the modem is attached to the base station.  Value of '-1'
        indicates 'No Service'." 
    ::= { c3gCdmaEvDoRadioEntry 3 }

c3gSectorId OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Sector ID of the base station to which the modem is attached." 
    ::= { c3gCdmaEvDoRadioEntry 4 }

c3gSubnetMask OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Subnet mask of the sector, not to be confused with the IP
        subnet mask."
    REFERENCE       "3GPP2 C.S0024-B v1.0" 
    ::= { c3gCdmaEvDoRadioEntry 5 }

c3gHdrColorCode OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Color code of the sector."
    REFERENCE       "3GPP2 C.S0024-B v1.0" 
    ::= { c3gCdmaEvDoRadioEntry 6 }

c3gPnOffset OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "PN offset. PN offset is a time offset from the beginning of
        the well-known pseudo-random noise sequence that is used to 
        spread the signal from the base station."
    REFERENCE       "3GPP2 C.S0024-B v1.0" 
    ::= { c3gCdmaEvDoRadioEntry 7 }

c3gRxMainGainControl OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Received main gain control for the modem. value of '-1'
        indicates the received main gain control is unavailable." 
    ::= { c3gCdmaEvDoRadioEntry 8 }

c3gRxDiversityGainControl OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Received diversity for the modem. value of '-1' indicates the
        received diversity is unavailable." 
    ::= { c3gCdmaEvDoRadioEntry 9 }

c3gTxTotalPower OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Transmit total power." 
    ::= { c3gCdmaEvDoRadioEntry 10 }

c3gTxGainAdjust OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Transmit gain adjust." 
    ::= { c3gCdmaEvDoRadioEntry 11 }

c3gCarrierToInterferenceRatio OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Carrier to interference ratio.  Carrier-to-Interference
        ratio (C/I) is the ratio of power in an RF carrier to the 
        interference power in the channel." 
    ::= { c3gCdmaEvDoRadioEntry 12 }
 


c3gCdmaEvDoBandClassTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaEvDoBandClassEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA EV-DO band class table.  This table contains
        band class information for each available band."
    ::= { c3gCdmaRadio 4 }

c3gCdmaEvDoBandClassEntry OBJECT-TYPE
    SYNTAX          C3gCdmaEvDoBandClassEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaEvDoBandClassTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gBandClassIndex
                    } 
    ::= { c3gCdmaEvDoBandClassTable 1 }

C3gCdmaEvDoBandClassEntry ::= SEQUENCE {
        c3gEvDoBandClass Unsigned32
}

c3gEvDoBandClass OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object contains EV-DO band class." 
    ::= { c3gCdmaEvDoBandClassEntry 1 }
 


c3gCdmaHistoryTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaHistoryEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA history table.  The history of RSSI are
        carried in an octet of string.  Each octet in the octet string
        has a value from 0 to 150 and the 255 value is reserved to
        indicate an uninitialized (Invalid) value.  The format of the
        octet string with n octets is as following:
           [ octet 0 is latest,
             octet 1 is latest-1,
             .
             .
             octet n-2 is oldest-1,
             octet n-1 is oldest ] 

        To convert the provided value into dBm the following formula
        should be used:
           dBm = (-1)*value;"
    ::= { c3gCdmaRadio 5 }

c3gCdmaHistoryEntry OBJECT-TYPE
    SYNTAX          C3gCdmaHistoryEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaHistoryTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdmaHistoryTable 1 }

C3gCdmaHistoryEntry ::= SEQUENCE {
        c3gCdmaHistory1xRttRssiPerSecond OCTET STRING,
        c3gCdmaHistory1xRttRssiPerMinute OCTET STRING,
        c3gCdmaHistory1xRttRssiPerHour   OCTET STRING,
        c3gCdmaHistoryEvDoRssiPerSecond  OCTET STRING,
        c3gCdmaHistoryEvDoRssiPerMinute  OCTET STRING,
        c3gCdmaHistoryEvDoRssiPerHour    OCTET STRING
}

c3gCdmaHistory1xRttRssiPerSecond OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (60))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-second 1xRTT RSSI history.  This object contains a
        per-second history of 1xRTT RSSI values for the last 60
        seconds." 
    ::= { c3gCdmaHistoryEntry 1 }

c3gCdmaHistory1xRttRssiPerMinute OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (60))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-minute 1xRTT weakest RSSI value history.  This
        object contains a per-minute history of 1xRTT weakest RSSI
        values for the last 60 minutes. The octet in the string
        is the weakest RSSI value measured in a minute interval." 
    ::= { c3gCdmaHistoryEntry 2 }

c3gCdmaHistory1xRttRssiPerHour OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (72))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-hour 1xRTT weakest RSSI value history.  This object
        contains a per-hour history of 1xRTT weakest RSSI values for the
        last 72 hours.  The octet in the string is the weakest RSSI
        value measured in an hour interval." 
    ::= { c3gCdmaHistoryEntry 3 }

c3gCdmaHistoryEvDoRssiPerSecond OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (60))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-second EV-DO RSSI history.  This object contains a
        per-second history of EV-DO RSSI values for the last 60
        seconds." 
    ::= { c3gCdmaHistoryEntry 4 }

c3gCdmaHistoryEvDoRssiPerMinute OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (60))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-minute EV-DO weakest RSSI value history.  This
        object contains a per-minute history of EV-DO weakest RSSI
        values for the last 60 minutes.  The octet in the string is the
        weakest RSSI value measured in a minute interval." 
    ::= { c3gCdmaHistoryEntry 5 }

c3gCdmaHistoryEvDoRssiPerHour OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (72))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-hour EV-DO weakest RSSI value history.  This object
        contains a per-hour history of EV-DO weakest RSSI values for the
        last 72 hours.  The octet in the string is the weakest RSSI
        value measured in an hour interval." 
    ::= { c3gCdmaHistoryEntry 6 }
 

c3gCdmaSecurity  OBJECT IDENTIFIER
    ::= { c3gWanCdma 7 }


c3gCdmaSecurityTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gCdmaSecurityEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G CDMA security table."
    ::= { c3gCdmaSecurity 1 }

c3gCdmaSecurityEntry OBJECT-TYPE
    SYNTAX          C3gCdmaSecurityEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gCdmaSecurityTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gCdmaSecurityTable 1 }

C3gCdmaSecurityEntry ::= SEQUENCE {
        c3gCdmaPinSecurityStatus INTEGER,
        c3gCdmaPowerUpLockStatus INTEGER
}

c3gCdmaPinSecurityStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        locked(2),
                        unlocked(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "CDMA Personal Identification Number (PIN) security:
        unknown(1)  - PIN security unknown
        locked(2)   - PIN security is locked
        unlocked(3) - PIN security is unlocked"
    DEFVAL          { unlocked } 
    ::= { c3gCdmaSecurityEntry 1 }

c3gCdmaPowerUpLockStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        enabled(2),
                        disabled(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "CDMA power up lock:
        unknown(1)  - power up lock unknown
        enabled(2)  - power up lock enabled
        disabled(3) - power up lock disabled"
    DEFVAL          { disabled } 
    ::= { c3gCdmaSecurityEntry 2 }
 


c3gGsmIdentityTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmIdentityEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "."
    ::= { c3gWanGsm 1 }

c3gGsmIdentityEntry OBJECT-TYPE
    SYNTAX          C3gGsmIdentityEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmIdentityTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gGsmIdentityTable 1 }

C3gGsmIdentityEntry ::= SEQUENCE {
        c3gImsi                 DisplayString,
        c3gImei                 DisplayString,
        c3gIccId                DisplayString,
        c3gMsisdn               DisplayString,
        c3gFsn                  DisplayString,
        c3gModemStatus          INTEGER,
        c3gGsmRoamingPreference INTEGER
}

c3gImsi OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "International Mobile Subscriber Identifier (IMSI), a unique
        15-digit code used to identify an individual user on a GSM/LTE
        network." 
    ::= { c3gGsmIdentityEntry 1 }

c3gImei OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "International Mobile Equipment Identifier (IMEI), a unique 15
        or 17 digit code used to identify an individual mobile station
        to a GSM, UMTS or LTE network." 
    ::= { c3gGsmIdentityEntry 2 }

c3gIccId OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the Integrated Circuit Card ID (ICCID).
        The ICCID is defined by the ITU-T recommendation E.118. 
        ICCIDs are stored in the SIM cards and are also engraved or 
        printed on the SIM card body during a process called 
        personalization." 
    ::= { c3gGsmIdentityEntry 3 }

c3gMsisdn OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates the Mobile Subscriber Integrated Services
        Digital Network Number (MSISDN). It is a number uniquely
        identifying a subscription in a GSM, UMTS or LTE mobile
        network. It represents the telephone number to the SIM 
        card in a mobile/cellular phone." 
    ::= { c3gGsmIdentityEntry 4 }

c3gFsn OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Forward Sequence Number (FSN), a message acknowledgement
        method using sequence number in the forward direction." 
    ::= { c3gGsmIdentityEntry 5 }

c3gModemStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        offLine(2),
                        onLine(3),
                        lowPowerMode(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Modem status:
        unknown(1)      - modem status is unknown
        offLine(2)      - modem is off line
        onLine(3)       - modem is on line
        lowPowerMode(4) - modem is in the low power mode"
    DEFVAL          { unknown } 
    ::= { c3gGsmIdentityEntry 6 }

c3gGsmRoamingPreference OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        home(2),
                        roaming(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This object indicates the roaming preference." 
    ::= { c3gGsmIdentityEntry 7 }
 


c3gGsmNetworkTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmNetworkEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular GSM Network table. This table is applicable in both
        3G and 4G-LTE technology mode"
    ::= { c3gWanGsm 2 }

c3gGsmNetworkEntry OBJECT-TYPE
    SYNTAX          C3gGsmNetworkEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmNetworkTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gGsmNetworkTable 1 }

C3gGsmNetworkEntry ::= SEQUENCE {
        c3gGsmLac                          Unsigned32,
        c3gGsmCurrentServiceStatus         INTEGER,
        c3gGsmCurrentServiceError          INTEGER,
        c3gGsmCurrentService               INTEGER,
        c3gGsmPacketService                INTEGER,
        c3gGsmCurrentRoamingStatus         INTEGER,
        c3gGsmNetworkSelectionMode         INTEGER,
        c3gGsmCountry                      DisplayString,
        c3gGsmNetwork                      DisplayString,
        c3gGsmMcc                          Integer32,
        c3gGsmMnc                          Integer32,
        c3gGsmRac                          Unsigned32,
        c3gGsmCurrentCellId                Unsigned32,
        c3gGsmCurrentPrimaryScramblingCode Unsigned32,
        c3gGsmPlmnSelection                INTEGER,
        c3gGsmRegPlmn                      DisplayString,
        c3gGsmPlmnAbbr                     DisplayString,
        c3gGsmServiceProvider              DisplayString,
        c3gGsmTotalByteTransmitted         Counter64,
        c3gGsmTotalByteReceived            Counter64
}

c3gGsmLac OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Location Area Code (LAC), also called as Tracking
        Area Code (TAC) in LTE standard.  LAC/TAC is given 
        by the base station."
    DEFVAL          { 0 } 
    ::= { c3gGsmNetworkEntry 1 }

c3gGsmCurrentServiceStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        noService(2),
                        normal(3),
                        emergencyOnly(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current Service Status:
        unknown(1)       - current service status is unknown
        noService(2)     - no service
        normal(3)        - service status is normal
        emergencyOnly(4) - emergency service only"
    DEFVAL          { unknown } 
    ::= { c3gGsmNetworkEntry 2 }

c3gGsmCurrentServiceError OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        none(2),
                        imsiUnknownInHlr(3),
                        illegalMs(4),
                        imsiUnknownInVlr(5),
                        imeiNotAccepted(6),
                        illegalMe(7),
                        gprsServNotAllowed(8),
                        gprsNonGprsServNotAllow(9),
                        msIdentUnknown(10),
                        implicitlyDetached(11),
                        plmnNotAllowed(12),
                        lacNotAllowed(13),
                        roamingNotAllowed(14),
                        gprsServNotAllowInPlmn(15),
                        noSuitableCellInLa(16),
                        mscTempNotReachable(17),
                        networkFailure(18),
                        macFailure(19),
                        synchFailure(20),
                        congestion(21),
                        gsmAuthenNotAccept(22),
                        servOptionNotSupport(23),
                        reqServOptionNotSub(24),
                        servOptionOutOfOrder(25),
                        callCannotIdentified(26),
                        noPdpContextActivated(27),
                        invalidMandatInfo(28),
                        unpsecProtErr(29)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current service error:
        unknown(1)                 - Unknown
        none(2)                    - None
        imsiUnknownInHlr(3)        - IMSI unknown in HLR (Home 
                                     Location Register)
        illegalMs(4)               - Illegal MS (Mobile Station)
        imsiUnknownInVlr(5)        - IMSI unknown in VLR (Visitor 
                                     Location Register)
        imeiNotAccepted(6)         - IMEI not accepted
        illegalMe(7)               - Illegal ME (Mobile Entity)
        gprsServNotAllowed(8)      - GPRS services not allowed
        gprsNonGprsServNotAllow(9) - GPRS and non-GPRS services 
                                     not allowed
        msIdentUnknown(10)         - MS identity unknown
        implicitlyDetached(11)     - Implicitly detached
        plmnNotAllowed(12)         - PLMN not allowed
        lacNotAllowed(13)          - LAC not allowed
        roamingNotAllowed(14)      - Roaming not allowed
        gprsServNotAllowInPlmn(15) - GPRS services not allowed 
                                     in this PLMN
        noSuitableCellInLa(16)     - No suitable cells in LA (Location 
                                     Area)
        mscTempNotReachable(17)    - MSC (Mobile Switching Center) 
                                     temporarily not reachable
        networkFailure(18)         - Network failure
        macFailure(19)             - MAC failure
        synchFailure(20)           - Synch failure
        congestion(21)             - Congestion
        gsmAuthenNotAccept(22)     - GSM/LTE Authentication not accepted
        servOptionNotSupport(23)   - Service option not supported
        reqServOptionNotSub(24)    - Requested service option not 
                                     subscribed
        servOptionOutOfOrder(25)   - Service option out of order
        callCannotIdentified(26)   - Call cannot be identified
        noPdpContextActivated(27)  - No PDP context activated
        invalidMandatInfo(28)      - Invalid mandatory info
        unpsecProtErr(29)          - Unspecified protocol error" 
    ::= { c3gGsmNetworkEntry 3 }

c3gGsmCurrentService OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        invalid(2),
                        circuitSwitched(3),
                        packetSwitched(4),
                        combined(5)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current service type:
        unknown(1)         - service type is unknown
        invalid(2)         - no service
        circuitSwitched(3) - circuit switched service
        packetSwitched(4)  - packet switched service
        combined(5)        - combination of circuit and packet 
                             switched service"
    DEFVAL          { unknown } 
    ::= { c3gGsmNetworkEntry 4 }

c3gGsmPacketService OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        none(2),
                        gprs(3),
                        edge(4),
                        umtsWcdma(5),
                        hsdpa(6),
                        hsupa(7),
                        hspa(8),
                        hspaPlus(9),
                        lte(10)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Packet Service type:
        unknown(1)   - service type is unknown.
        none(2)      - no service.
        gprs(3)      - General Packet Radio Service (GPRS).
        edge(4)      - Enhanced Data rates for GSM Evolution (EDGE).
        umtsWcdma(5) - Universal Mobile Telecommunications 
                       System (UMTS) / Wideband Code-Division 
                       Multiple-Access (W-CDMA).
        hsdpa(6)     - High-Speed Downlink Packet Access (HSDPA)
        hsdpa(7)     - High-Speed Uplink Packet Access (HSUPA)
        hspa(8)      - High-Speed Packet Access (HSPA)
        hspaPlus(9)  - High-Speed Packet Access (HSPA) Plus
        lte(10)      - Long Term Evolution"
    DEFVAL          { unknown } 
    ::= { c3gGsmNetworkEntry 5 }

c3gGsmCurrentRoamingStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        roaming(2),
                        home(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates whether the modem is in the home network
        or is roaming."
    DEFVAL          { home } 
    ::= { c3gGsmNetworkEntry 6 }

c3gGsmNetworkSelectionMode OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        automatic(2),
                        manual(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Network selection mode.  Can be manual selection mode or
        automatic selection mode. Set to automatic by default."
    DEFVAL          { automatic } 
    ::= { c3gGsmNetworkEntry 7 }

c3gGsmCountry OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Country code.  Country code string is given by the base
        station." 
    ::= { c3gGsmNetworkEntry 8 }

c3gGsmNetwork OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Network code.  Network Code string is given by the base
        station." 
    ::= { c3gGsmNetworkEntry 9 }

c3gGsmMcc OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Country Code (MCC).  Value of '-1' indicates MCC is 'Not
        Applicable'."
    DEFVAL          { 0 } 
    ::= { c3gGsmNetworkEntry 10 }

c3gGsmMnc OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Mobile Network Code (MNC). Value of '-1' indicates MNC is 'Not
        Applicable'."
    DEFVAL          { 0 } 
    ::= { c3gGsmNetworkEntry 11 }

c3gGsmRac OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Routing Area Code (RAC).  RAC is given by the base station."
    DEFVAL          { 0 } 
    ::= { c3gGsmNetworkEntry 12 }

c3gGsmCurrentCellId OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Cell Identifier of current cell.  Cell ID is given by the
        base station."
    DEFVAL          { 0 } 
    ::= { c3gGsmNetworkEntry 13 }

c3gGsmCurrentPrimaryScramblingCode OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Primary scrambling code of current cell.  The primary
        scrambling code is typically identified through 
        symbol-by-symbol correlation over the CPICH (Common Pilot 
        Channel) with all codes within the code group, after the 
        primary scrambling code has been identified, 
        the Primary CCPCH can be detected and the system and cell
        specific BCH information can be read."
    DEFVAL          { 0 } 
    ::= { c3gGsmNetworkEntry 14 }

c3gGsmPlmnSelection OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        manual(2),
                        automatic(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Public Land Mobile Network (PLMN) selection.  Can be manual
        selection mode or automatic selection mode. Set to automatic by
        default."
    DEFVAL          { automatic } 
    ::= { c3gGsmNetworkEntry 15 }

c3gGsmRegPlmn OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Registered PLMN." 
    ::= { c3gGsmNetworkEntry 16 }

c3gGsmPlmnAbbr OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "PLMN abbreviated number." 
    ::= { c3gGsmNetworkEntry 17 }

c3gGsmServiceProvider OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Service provider." 
    ::= { c3gGsmNetworkEntry 18 }

c3gGsmTotalByteTransmitted OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of bytes transmitted for all calls.  It is the
        total number of bytes transmitted by modem, not to be confused
        with the number of bytes transmitted through the interface." 
    ::= { c3gGsmNetworkEntry 19 }

c3gGsmTotalByteReceived OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Total number of bytes received for all calls.  It is the total
        number of bytes received by modem, not to be confused with the
        number of bytes received from the interface." 
    ::= { c3gGsmNetworkEntry 20 }
 

c3gGsmPdpProfile  OBJECT IDENTIFIER
    ::= { c3gWanGsm 3 }


c3gGsmPdpProfileTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmPdpProfileEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular GSM PDP profiles table.  Cellular device contains
        multiple profile entries which can be used to establish 
        cellular data connections (PDP contexts). Users can choose 
        any of available PDP profiles to establish data connections.
        Data connections are described in c3gGsmPacketSessionTable.
        This table is applicable in only 3G technology mode. Refer
        CISCO-WAN-CELL-EXT-MIB when in 4G-LTE mode."
    ::= { c3gGsmPdpProfile 1 }

c3gGsmPdpProfileEntry OBJECT-TYPE
    SYNTAX          C3gGsmPdpProfileEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmPdpProfileTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmPdpProfileTable 1 }

C3gGsmPdpProfileEntry ::= SEQUENCE {
        c3gGsmPdpProfileIndex      Integer32,
        c3gGsmPdpProfileType       C3gPdpType,
        c3gGsmPdpProfileAddr       DisplayString,
        c3gGsmPdpProfileApn        DisplayString,
        c3gGsmPdpProfileAuthenType INTEGER,
        c3gGsmPdpProfileUsername   DisplayString,
        c3gGsmPdpProfilePassword   DisplayString,
        c3gGsmPdpProfileRowStatus  RowStatus
}

c3gGsmPdpProfileIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..1000)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Profile index, combined with entPhysicalIndex to
        access profile table." 
    ::= { c3gGsmPdpProfileEntry 1 }

c3gGsmPdpProfileType OBJECT-TYPE
    SYNTAX          C3gPdpType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object indicates configured Packet Data Protocol (PDP)
        type."
    DEFVAL          { unknown } 
    ::= { c3gGsmPdpProfileEntry 2 }

c3gGsmPdpProfileAddr OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Configured PDP/EPS Bearer address.  PDP type is
        defined in c3gGsmPdpProfileType." 
    ::= { c3gGsmPdpProfileEntry 3 }

c3gGsmPdpProfileApn OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object indicates profile Access Point Name (APN). This
        information is provided by the service provider." 
    ::= { c3gGsmPdpProfileEntry 4 }

c3gGsmPdpProfileAuthenType OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        none(2),
                        chap(3),
                        pap(4)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object indicates PDP authentication type
        supported. CHAP and PAP are supported in GSM. The type of
        authentication to be used is provided by the service 
        provider."
    DEFVAL          { unknown } 
    ::= { c3gGsmPdpProfileEntry 5 }

c3gGsmPdpProfileUsername OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object indicates the username to be used for PDP
        authentication. This information is provided by the service
        provider." 
    ::= { c3gGsmPdpProfileEntry 6 }

c3gGsmPdpProfilePassword OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object indicates the password to be used for PDP
        authentication.  This information is provided by the service
        provider." 
    ::= { c3gGsmPdpProfileEntry 7 }

c3gGsmPdpProfileRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row. This object is used to
        manage creation, modification and deletion of rows in this
        table." 
    ::= { c3gGsmPdpProfileEntry 8 }
 


c3gGsmPacketSessionTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmPacketSessionEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G GSM packet session table. This table is applicable
        in only 3G technology mode. Refer CISCO-WAN-CELL-EXT-MIB 
        when in 4G-LTE mode."
    ::= { c3gGsmPdpProfile 2 }

c3gGsmPacketSessionEntry OBJECT-TYPE
    SYNTAX          C3gGsmPacketSessionEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmPacketSessionTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmPacketSessionTable 1 }

C3gGsmPacketSessionEntry ::= SEQUENCE {
        c3gGsmPacketSessionStatus INTEGER,
        c3gGsmPdpType             C3gPdpType,
        c3gGsmPdpAddress          DisplayString
}

c3gGsmPacketSessionStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        active(2),
                        inactive(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates PDP session status
        of the profile. This is active when the call is established
        and PDP contextr has become active."
    DEFVAL          { inactive } 
    ::= { c3gGsmPacketSessionEntry 1 }

c3gGsmPdpType OBJECT-TYPE
    SYNTAX          C3gPdpType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates current session PDP type." 
    ::= { c3gGsmPacketSessionEntry 2 }

c3gGsmPdpAddress OBJECT-TYPE
    SYNTAX          DisplayString (SIZE  (0..64))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Current session PDP context/EPS Bearer address.  PDP type is
        obtained from c3gGsmPdpType." 
    ::= { c3gGsmPacketSessionEntry 3 }
 


c3gGsmReqUmtsQosTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmReqUmtsQosEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Requested UMTS QoS parameters table.  This table contains UMTS
        QoS parameters requested by modem to the cellular network
        via PDP Context Activation Request message.  The requested UMTS
        QoS profile is optional. This table is applicable only in 3G 
        technology mode."
    ::= { c3gGsmPdpProfile 3 }

c3gGsmReqUmtsQosEntry OBJECT-TYPE
    SYNTAX          C3gGsmReqUmtsQosEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmReqUmtsQosTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmReqUmtsQosTable 1 }

C3gGsmReqUmtsQosEntry ::= SEQUENCE {
        c3gGsmReqUmtsQosTrafficClass       C3gUmtsQosTrafficClass,
        c3gGsmReqUmtsQosMaxUpLinkBitRate   C3gUmtsQosLinkBitRate,
        c3gGsmReqUmtsQosMaxDownLinkBitRate C3gUmtsQosLinkBitRate,
        c3gGsmReqUmtsQosGuaUpLinkBitRate   C3gUmtsQosLinkBitRate,
        c3gGsmReqUmtsQosGuaDownLinkBitRate C3gUmtsQosLinkBitRate,
        c3gGsmReqUmtsQosOrder              C3gUmtsQosOrder,
        c3gGsmReqUmtsQosErroneousSdu       C3gUmtsQosErroneousSdu,
        c3gGsmReqUmtsQosMaxSduSize         Unsigned32,
        c3gGsmReqUmtsQosSer                C3gUmtsQosSer,
        c3gGsmReqUmtsQosBer                C3gUmtsQosBer,
        c3gGsmReqUmtsQosDelay              Unsigned32,
        c3gGsmReqUmtsQosPriority           C3gUmtsQosPriority,
        c3gGsmReqUmtsQosSrcStatDescriptor  C3gUmtsQosSrcStatDescriptor,
        c3gGsmReqUmtsQosSignalIndication   C3gUmtsQosSignalIndication,
        c3gGsmReqUmtsQosRowStatus          RowStatus
}

c3gGsmReqUmtsQosTrafficClass OBJECT-TYPE
    SYNTAX          C3gUmtsQosTrafficClass
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS traffic classes." 
    ::= { c3gGsmReqUmtsQosEntry 2 }

c3gGsmReqUmtsQosMaxUpLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS maximum uplink bit rate." 
    ::= { c3gGsmReqUmtsQosEntry 3 }

c3gGsmReqUmtsQosMaxDownLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS maximum downlink bit rate." 
    ::= { c3gGsmReqUmtsQosEntry 4 }

c3gGsmReqUmtsQosGuaUpLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS guaranteed uplink bit rate." 
    ::= { c3gGsmReqUmtsQosEntry 5 }

c3gGsmReqUmtsQosGuaDownLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS guaranteed downlink bit rate." 
    ::= { c3gGsmReqUmtsQosEntry 6 }

c3gGsmReqUmtsQosOrder OBJECT-TYPE
    SYNTAX          C3gUmtsQosOrder
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS deliver order." 
    ::= { c3gGsmReqUmtsQosEntry 7 }

c3gGsmReqUmtsQosErroneousSdu OBJECT-TYPE
    SYNTAX          C3gUmtsQosErroneousSdu
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS Delivery of Erroneous SDU." 
    ::= { c3gGsmReqUmtsQosEntry 8 }

c3gGsmReqUmtsQosMaxSduSize OBJECT-TYPE
    SYNTAX          Unsigned32 (0..1520)
    UNITS           "bytes"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS maximum SDU size, the valid range is between 1
        and 1520 bytes.  Value of '0' indicates the maximum SDU size is
        unspecified." 
    ::= { c3gGsmReqUmtsQosEntry 9 }

c3gGsmReqUmtsQosSer OBJECT-TYPE
    SYNTAX          C3gUmtsQosSer
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS SDU error ratio." 
    ::= { c3gGsmReqUmtsQosEntry 10 }

c3gGsmReqUmtsQosBer OBJECT-TYPE
    SYNTAX          C3gUmtsQosBer
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS residual bit error ratio." 
    ::= { c3gGsmReqUmtsQosEntry 11 }

c3gGsmReqUmtsQosDelay OBJECT-TYPE
    SYNTAX          Unsigned32 (0..4000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS transfer delay in ms, the valid range
        is between 1 and 4000 ms. Value of '0' indicates the 
        QoS delay is unspecified." 
    ::= { c3gGsmReqUmtsQosEntry 12 }

c3gGsmReqUmtsQosPriority OBJECT-TYPE
    SYNTAX          C3gUmtsQosPriority
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS traffic handling priority." 
    ::= { c3gGsmReqUmtsQosEntry 13 }

c3gGsmReqUmtsQosSrcStatDescriptor OBJECT-TYPE
    SYNTAX          C3gUmtsQosSrcStatDescriptor
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS source statistics descriptor." 
    ::= { c3gGsmReqUmtsQosEntry 14 }

c3gGsmReqUmtsQosSignalIndication OBJECT-TYPE
    SYNTAX          C3gUmtsQosSignalIndication
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request UMTS QoS signalling indication." 
    ::= { c3gGsmReqUmtsQosEntry 15 }

c3gGsmReqUmtsQosRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row. This object is used to
        manage creation, modification and deletion of rows in this
        table." 
    ::= { c3gGsmReqUmtsQosEntry 16 }
 


c3gGsmMinUmtsQosTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmMinUmtsQosEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Minimum acceptable UMTS QoS table.  This table contains minimum
        acceptable UMTS QoS parameters which is checked by the MT
        (Mobile Termination) against the negotiated profile returned in
        the Activate PDP Context Accept message.  The minimum acceptable
        UMTS QoS profile is optional. This table is applicable only in
        3G technology mode."
    ::= { c3gGsmPdpProfile 4 }

c3gGsmMinUmtsQosEntry OBJECT-TYPE
    SYNTAX          C3gGsmMinUmtsQosEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmMinUmtsQosTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmMinUmtsQosTable 1 }

C3gGsmMinUmtsQosEntry ::= SEQUENCE {
        c3gGsmMinUmtsQosTrafficClass       C3gUmtsQosTrafficClass,
        c3gGsmMinUmtsQosMaxUpLinkBitRate   C3gUmtsQosLinkBitRate,
        c3gGsmMinUmtsQosMaxDownLinkBitRate C3gUmtsQosLinkBitRate,
        c3gGsmMinUmtsQosGuaUpLinkBitRate   C3gUmtsQosLinkBitRate,
        c3gGsmMinUmtsQosGuaDownLinkBitRate C3gUmtsQosLinkBitRate,
        c3gGsmMinUmtsQosOrder              C3gUmtsQosOrder,
        c3gGsmMinUmtsQosErroneousSdu       C3gUmtsQosErroneousSdu,
        c3gGsmMinUmtsQosMaxSduSize         Unsigned32,
        c3gGsmMinUmtsQosSer                C3gUmtsQosSer,
        c3gGsmMinUmtsQosBer                C3gUmtsQosBer,
        c3gGsmMinUmtsQosDelay              Unsigned32,
        c3gGsmMinUmtsQosPriority           C3gUmtsQosPriority,
        c3gGsmMinUmtsQosSrcStatDescriptor  C3gUmtsQosSrcStatDescriptor,
        c3gGsmMinUmtsQosSignalIndication   C3gUmtsQosSignalIndication,
        c3gGsmMinUmtsQosRowStatus          RowStatus
}

c3gGsmMinUmtsQosTrafficClass OBJECT-TYPE
    SYNTAX          C3gUmtsQosTrafficClass
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS traffic classes." 
    ::= { c3gGsmMinUmtsQosEntry 1 }

c3gGsmMinUmtsQosMaxUpLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS maximum uplink bit rate." 
    ::= { c3gGsmMinUmtsQosEntry 2 }

c3gGsmMinUmtsQosMaxDownLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS maximum downlink bit rate." 
    ::= { c3gGsmMinUmtsQosEntry 3 }

c3gGsmMinUmtsQosGuaUpLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS guaranteed uplink bit rate." 
    ::= { c3gGsmMinUmtsQosEntry 4 }

c3gGsmMinUmtsQosGuaDownLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS guaranteed downlink bit rate." 
    ::= { c3gGsmMinUmtsQosEntry 5 }

c3gGsmMinUmtsQosOrder OBJECT-TYPE
    SYNTAX          C3gUmtsQosOrder
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS deliver order." 
    ::= { c3gGsmMinUmtsQosEntry 6 }

c3gGsmMinUmtsQosErroneousSdu OBJECT-TYPE
    SYNTAX          C3gUmtsQosErroneousSdu
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS Delivery of Erroneous SDU." 
    ::= { c3gGsmMinUmtsQosEntry 7 }

c3gGsmMinUmtsQosMaxSduSize OBJECT-TYPE
    SYNTAX          Unsigned32 (0..1520)
    UNITS           "bytes"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS maximum SDU size, the valid range is between 1 and
        1520 bytes. Value of '0' indicates the maximum SDU size is
        unspecified." 
    ::= { c3gGsmMinUmtsQosEntry 8 }

c3gGsmMinUmtsQosSer OBJECT-TYPE
    SYNTAX          C3gUmtsQosSer
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS SDU error ratio." 
    ::= { c3gGsmMinUmtsQosEntry 9 }

c3gGsmMinUmtsQosBer OBJECT-TYPE
    SYNTAX          C3gUmtsQosBer
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS residual bit error ratio." 
    ::= { c3gGsmMinUmtsQosEntry 10 }

c3gGsmMinUmtsQosDelay OBJECT-TYPE
    SYNTAX          Unsigned32 (0..4000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS transfer delay in ms, the valid range
        is between 1 and 4000 ms. Value of '0' indicates the 
        QoS delay is unspecified." 
    ::= { c3gGsmMinUmtsQosEntry 11 }

c3gGsmMinUmtsQosPriority OBJECT-TYPE
    SYNTAX          C3gUmtsQosPriority
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS traffic handling priority." 
    ::= { c3gGsmMinUmtsQosEntry 12 }

c3gGsmMinUmtsQosSrcStatDescriptor OBJECT-TYPE
    SYNTAX          C3gUmtsQosSrcStatDescriptor
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS source statistics descriptor." 
    ::= { c3gGsmMinUmtsQosEntry 13 }

c3gGsmMinUmtsQosSignalIndication OBJECT-TYPE
    SYNTAX          C3gUmtsQosSignalIndication
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum UMTS QoS signalling indication." 
    ::= { c3gGsmMinUmtsQosEntry 14 }

c3gGsmMinUmtsQosRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row. This object is used to
        manage creation, modification and deletion of rows in this
        table." 
    ::= { c3gGsmMinUmtsQosEntry 15 }
 


c3gGsmNegoUmtsQosTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmNegoUmtsQosEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS table.  This table contains negotiated UMTS
        QoS parameters returned in the Activate PDP Context Accept
        message.  The objects in this table are valid only if the value
        of object c3gGsmPacketSessionStatus defined in
        c3gGsmPacketSessionTable is 'active'. This table is applicable
        only in 3G technology mode."
    ::= { c3gGsmPdpProfile 5 }

c3gGsmNegoUmtsQosEntry OBJECT-TYPE
    SYNTAX          C3gGsmNegoUmtsQosEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmNegoUmtsQosTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmNegoUmtsQosTable 1 }

C3gGsmNegoUmtsQosEntry ::= SEQUENCE {
        c3gGsmNegoUmtsQosTrafficClass       C3gUmtsQosTrafficClass,
        c3gGsmNegoUmtsQosMaxUpLinkBitRate   C3gUmtsQosLinkBitRate,
        c3gGsmNegoUmtsQosMaxDownLinkBitRate C3gUmtsQosLinkBitRate,
        c3gGsmNegoUmtsQosGuaUpLinkBitRate   C3gUmtsQosLinkBitRate,
        c3gGsmNegoUmtsQosGuaDownLinkBitRate C3gUmtsQosLinkBitRate,
        c3gGsmNegoUmtsQosOrder              C3gUmtsQosOrder,
        c3gGsmNegoUmtsQosErroneousSdu       C3gUmtsQosErroneousSdu,
        c3gGsmNegoUmtsQosMaxSduSize         Unsigned32,
        c3gGsmNegoUmtsQosSer                C3gUmtsQosSer,
        c3gGsmNegoUmtsQosBer                C3gUmtsQosBer,
        c3gGsmNegoUmtsQosDelay              Unsigned32,
        c3gGsmNegoUmtsQosPriority           C3gUmtsQosPriority,
        c3gGsmNegoUmtsQosSrcStatDescriptor  C3gUmtsQosSrcStatDescriptor,
        c3gGsmNegoUmtsQosSignalIndication   C3gUmtsQosSignalIndication
}

c3gGsmNegoUmtsQosTrafficClass OBJECT-TYPE
    SYNTAX          C3gUmtsQosTrafficClass
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS traffic classes." 
    ::= { c3gGsmNegoUmtsQosEntry 1 }

c3gGsmNegoUmtsQosMaxUpLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS maximum uplink bit rate." 
    ::= { c3gGsmNegoUmtsQosEntry 2 }

c3gGsmNegoUmtsQosMaxDownLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS maximum downlink bit rate." 
    ::= { c3gGsmNegoUmtsQosEntry 3 }

c3gGsmNegoUmtsQosGuaUpLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS guaranteed uplink bit rate." 
    ::= { c3gGsmNegoUmtsQosEntry 4 }

c3gGsmNegoUmtsQosGuaDownLinkBitRate OBJECT-TYPE
    SYNTAX          C3gUmtsQosLinkBitRate
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS guaranteed downlink bit rate." 
    ::= { c3gGsmNegoUmtsQosEntry 5 }

c3gGsmNegoUmtsQosOrder OBJECT-TYPE
    SYNTAX          C3gUmtsQosOrder
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS deliver order." 
    ::= { c3gGsmNegoUmtsQosEntry 6 }

c3gGsmNegoUmtsQosErroneousSdu OBJECT-TYPE
    SYNTAX          C3gUmtsQosErroneousSdu
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS Delivery of Erroneous SDU." 
    ::= { c3gGsmNegoUmtsQosEntry 7 }

c3gGsmNegoUmtsQosMaxSduSize OBJECT-TYPE
    SYNTAX          Unsigned32 (0..1520)
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS maximum SDU size, the valid range is
        between 1 and 1520 bytes.  Value of '0' indicates the maximum
        SDU size is subscribed." 
    ::= { c3gGsmNegoUmtsQosEntry 8 }

c3gGsmNegoUmtsQosSer OBJECT-TYPE
    SYNTAX          C3gUmtsQosSer
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS SDU error ratio." 
    ::= { c3gGsmNegoUmtsQosEntry 9 }

c3gGsmNegoUmtsQosBer OBJECT-TYPE
    SYNTAX          C3gUmtsQosBer
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS residual bit error ratio." 
    ::= { c3gGsmNegoUmtsQosEntry 10 }

c3gGsmNegoUmtsQosDelay OBJECT-TYPE
    SYNTAX          Unsigned32 (0..4000)
    UNITS           "milliseconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS transfer delay in ms, the valid range
        is between 1 and 4000 ms. Value of '0' indicates the 
        QoS delay is subscribed." 
    ::= { c3gGsmNegoUmtsQosEntry 11 }

c3gGsmNegoUmtsQosPriority OBJECT-TYPE
    SYNTAX          C3gUmtsQosPriority
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS traffic handling priority." 
    ::= { c3gGsmNegoUmtsQosEntry 12 }

c3gGsmNegoUmtsQosSrcStatDescriptor OBJECT-TYPE
    SYNTAX          C3gUmtsQosSrcStatDescriptor
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS source statistics descriptor." 
    ::= { c3gGsmNegoUmtsQosEntry 13 }

c3gGsmNegoUmtsQosSignalIndication OBJECT-TYPE
    SYNTAX          C3gUmtsQosSignalIndication
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated UMTS QoS signalling indication." 
    ::= { c3gGsmNegoUmtsQosEntry 14 }
 


c3gGsmReqGprsQosTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmReqGprsQosEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Requested GPRS QoS parameters table.  This table contains GPRS
        QoS parameters requested by modem to the cellular network
        via PDP Context Request message.  The requested GPRS QoS
        profile is optional. This table is applicable only in
        3G technology mode."
    ::= { c3gGsmPdpProfile 6 }

c3gGsmReqGprsQosEntry OBJECT-TYPE
    SYNTAX          C3gGsmReqGprsQosEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmReqGprsQosTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmReqGprsQosTable 1 }

C3gGsmReqGprsQosEntry ::= SEQUENCE {
        c3gGsmReqGprsQosPrecedence  C3gGprsQosPrecedence,
        c3gGsmReqGprsQosDelay       C3gGprsQosDelay,
        c3gGsmReqGprsQosReliability C3gGprsQosReliability,
        c3gGsmReqGprsQosPeakRate    C3gGprsQosPeakRate,
        c3gGsmReqGprsQosMeanRate    C3gGprsQosMeanRate,
        c3gGsmReqGprsQosRowStatus   RowStatus
}

c3gGsmReqGprsQosPrecedence OBJECT-TYPE
    SYNTAX          C3gGprsQosPrecedence
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request GPRS QoS precedence." 
    ::= { c3gGsmReqGprsQosEntry 1 }

c3gGsmReqGprsQosDelay OBJECT-TYPE
    SYNTAX          C3gGprsQosDelay
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request GPRS QoS delay classes." 
    ::= { c3gGsmReqGprsQosEntry 2 }

c3gGsmReqGprsQosReliability OBJECT-TYPE
    SYNTAX          C3gGprsQosReliability
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request GPRS QoS reliability." 
    ::= { c3gGsmReqGprsQosEntry 3 }

c3gGsmReqGprsQosPeakRate OBJECT-TYPE
    SYNTAX          C3gGprsQosPeakRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request GPRS QoS peak rate." 
    ::= { c3gGsmReqGprsQosEntry 4 }

c3gGsmReqGprsQosMeanRate OBJECT-TYPE
    SYNTAX          C3gGprsQosMeanRate
    UNITS           "octet-per-hour"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Request GPRS QoS mean rate." 
    ::= { c3gGsmReqGprsQosEntry 5 }

c3gGsmReqGprsQosRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row. This object is used to
        manage creation, modification and deletion of rows in this
        table." 
    ::= { c3gGsmReqGprsQosEntry 6 }
 


c3gGsmMinGprsQosTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmMinGprsQosEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Minimum acceptable GPRS QoS table.  This table contains minimum
        acceptable GPRS QoS parameters which is checked by the MT
        (Mobile Termination) against the negotiated profile returned in
        the Activate PDP Context Accept message.  The minimum acceptable
        GPRS QoS profile is optional. This table is applicable only in
        3G technology mode."
    ::= { c3gGsmPdpProfile 7 }

c3gGsmMinGprsQosEntry OBJECT-TYPE
    SYNTAX          C3gGsmMinGprsQosEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmMinGprsQosTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmMinGprsQosTable 1 }

C3gGsmMinGprsQosEntry ::= SEQUENCE {
        c3gGsmMinGprsQosPrecedence  C3gGprsQosPrecedence,
        c3gGsmMinGprsQosDelay       C3gGprsQosDelay,
        c3gGsmMinGprsQosReliability C3gGprsQosReliability,
        c3gGsmMinGprsQosPeakRate    C3gGprsQosPeakRate,
        c3gGsmMinGprsQosMeanRate    C3gGprsQosMeanRate,
        c3gGsmMinGprsQosRowStatus   RowStatus
}

c3gGsmMinGprsQosPrecedence OBJECT-TYPE
    SYNTAX          C3gGprsQosPrecedence
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum GPRS QoS precedence." 
    ::= { c3gGsmMinGprsQosEntry 1 }

c3gGsmMinGprsQosDelay OBJECT-TYPE
    SYNTAX          C3gGprsQosDelay
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum GPRS QoS delay classes." 
    ::= { c3gGsmMinGprsQosEntry 2 }

c3gGsmMinGprsQosReliability OBJECT-TYPE
    SYNTAX          C3gGprsQosReliability
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum GPRS QoS reliability." 
    ::= { c3gGsmMinGprsQosEntry 3 }

c3gGsmMinGprsQosPeakRate OBJECT-TYPE
    SYNTAX          C3gGprsQosPeakRate
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum GPRS QoS peak rate." 
    ::= { c3gGsmMinGprsQosEntry 4 }

c3gGsmMinGprsQosMeanRate OBJECT-TYPE
    SYNTAX          C3gGprsQosMeanRate
    UNITS           "octet-per-hour"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "Minimum GPRS QoS mean rate." 
    ::= { c3gGsmMinGprsQosEntry 5 }

c3gGsmMinGprsQosRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row. This object is used to
        manage creation, modification and deletion of rows in this
        table." 
    ::= { c3gGsmMinGprsQosEntry 6 }
 


c3gGsmNegoGprsQosTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmNegoGprsQosEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Negotiated GPRS QoS table.  This table contains negotiated GPRS
        QoS parameters returned in the Activate PDP Context Accept
        message.  The objects in this table are valid only if the value
        of object c3gGsmPacketSessionStatus defined in
        c3gGsmPacketSessionTable is 'active'. This table is applicable 
        only in 3G technology mode."
    ::= { c3gGsmPdpProfile 8 }

c3gGsmNegoGprsQosEntry OBJECT-TYPE
    SYNTAX          C3gGsmNegoGprsQosEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmNegoGprsQosTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmPdpProfileIndex
                    } 
    ::= { c3gGsmNegoGprsQosTable 1 }

C3gGsmNegoGprsQosEntry ::= SEQUENCE {
        c3gGsmNegoGprsQosPrecedence  C3gGprsQosPrecedence,
        c3gGsmNegoGprsQosDelay       C3gGprsQosDelay,
        c3gGsmNegoGprsQosReliability C3gGprsQosReliability,
        c3gGsmNegoGprsQosPeakRate    C3gGprsQosPeakRate,
        c3gGsmNegoGprsQosMeanRate    C3gGprsQosMeanRate
}

c3gGsmNegoGprsQosPrecedence OBJECT-TYPE
    SYNTAX          C3gGprsQosPrecedence
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated GPRS QoS precedence." 
    ::= { c3gGsmNegoGprsQosEntry 1 }

c3gGsmNegoGprsQosDelay OBJECT-TYPE
    SYNTAX          C3gGprsQosDelay
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated GPRS QoS delay classes." 
    ::= { c3gGsmNegoGprsQosEntry 2 }

c3gGsmNegoGprsQosReliability OBJECT-TYPE
    SYNTAX          C3gGprsQosReliability
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated GPRS QoS reliability." 
    ::= { c3gGsmNegoGprsQosEntry 3 }

c3gGsmNegoGprsQosPeakRate OBJECT-TYPE
    SYNTAX          C3gGprsQosPeakRate
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated GPRS QoS peak rate." 
    ::= { c3gGsmNegoGprsQosEntry 4 }

c3gGsmNegoGprsQosMeanRate OBJECT-TYPE
    SYNTAX          C3gGprsQosMeanRate
    UNITS           "octet-per-hour"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Negotiated GPRS QoS mean rate." 
    ::= { c3gGsmNegoGprsQosEntry 5 }
 

c3gGsmRadio  OBJECT IDENTIFIER
    ::= { c3gWanGsm 4 }


c3gGsmRadioTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmRadioEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G GSM/4G-LTE radio table."
    ::= { c3gGsmRadio 1 }

c3gGsmRadioEntry OBJECT-TYPE
    SYNTAX          C3gGsmRadioEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmRadioTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gGsmRadioTable 1 }

C3gGsmRadioEntry ::= SEQUENCE {
        c3gCurrentGsmRssi        C3gRssi,
        c3gCurrentGsmEcIo        C3gEcIo,
        c3gGsmCurrentBand        INTEGER,
        c3gGsmChannelNumber      Unsigned32,
        c3gGsmNumberOfNearbyCell Unsigned32
}

c3gCurrentGsmRssi OBJECT-TYPE
    SYNTAX          C3gRssi
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The GPRS/UMTS/LTE RSSI value." 
    ::= { c3gGsmRadioEntry 1 }

c3gCurrentGsmEcIo OBJECT-TYPE
    SYNTAX          C3gEcIo
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The GPRS, UMTS or LTE Ec/Io value." 
    ::= { c3gGsmRadioEntry 2 }

c3gGsmCurrentBand OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        invalid(2),
                        none(3),
                        gsm850(4),
                        gsm900(5),
                        gsm1800(6),
                        gsm1900(7),
                        wcdma800(8),
                        wcdma850(9),
                        wcdma1900(10),
                        wcdma2100(11),
                        lteBand(12)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "GPRS/UMTS/LTE band to which the modem is attached.
        Refer CISCO-WAN-CELL-EXT-MIB for LTE band number when
        in LTE mode."
    DEFVAL          { unknown } 
    ::= { c3gGsmRadioEntry 3 }

c3gGsmChannelNumber OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Channel number to which the modem is attached. This is only
        applicable in 3G technology mode. Refer CISCO-WAN-CELL-EXT-MIB 
        for the LTE uplink and downlink channel values"
    DEFVAL          { 0 } 
    ::= { c3gGsmRadioEntry 4 }

c3gGsmNumberOfNearbyCell OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the current total number of
        nearby cell in the c3gGsmNearbyCellTable.  User can 
        poll this object to get the total number of nearby
        cell before polling c3gGsmNearbyCellTable."
    DEFVAL          { 0 } 
    ::= { c3gGsmRadioEntry 5 }
 


c3gGsmNearbyCellTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmNearbyCellEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular GSM/4G-LTE nearby cell table.  Object
        c3gGsmNumberOfNearbyCell indicates the total 
        number of nearby cell in this table."
    ::= { c3gGsmRadio 2 }

c3gGsmNearbyCellEntry OBJECT-TYPE
    SYNTAX          C3gGsmNearbyCellEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmNearbyCellTable."
    INDEX           {
                        entPhysicalIndex,
                        c3gGsmNearbyCellIndex
                    } 
    ::= { c3gGsmNearbyCellTable 1 }

C3gGsmNearbyCellEntry ::= SEQUENCE {
        c3gGsmNearbyCellIndex                 Integer32,
        c3gGsmNearbyCellPrimaryScramblingCode Unsigned32,
        c3gGsmNearbyCellRscp                  Integer32,
        c3gGsmNearbyCellEcIoMeasurement       C3gEcIo
}

c3gGsmNearbyCellIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..100)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Nearby cell index, combined with entPhysicalIndex to access
        the Nearby cell table c3gGsmNearbyCellTable." 
    ::= { c3gGsmNearbyCellEntry 1 }

c3gGsmNearbyCellPrimaryScramblingCode OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Nearby cell primary scrambling code." 
    ::= { c3gGsmNearbyCellEntry 2 }

c3gGsmNearbyCellRscp OBJECT-TYPE
    SYNTAX          Integer32 (-150..0)
    UNITS           "dB"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Nearby cell Received Signal Code Power (RSCP)." 
    ::= { c3gGsmNearbyCellEntry 3 }

c3gGsmNearbyCellEcIoMeasurement OBJECT-TYPE
    SYNTAX          C3gEcIo
    UNITS           "dB"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Nearby cell Ec/Io measurement." 
    ::= { c3gGsmNearbyCellEntry 4 }
 


c3gGsmHistoryTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmHistoryEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G GSM/4G-LTE RSSI history table. The history of RSSI
        are carried in an octet of string.  Each octet in the octet
        string has a value from 0 to 150 and the 255 value is reserved
        to indicate an uninitialized (Invalid) value.  The format of 
        the octet string with n octets is as following:
           [ octet 0 is latest,
             octet 1 is latest-1,
             .
             .
             octet n-2 is oldest-1,
             octet n-1 is oldest ] 

        To convert the provided value into dBm the following formula
        should be used:
           dBm = (-1)*value;"
    ::= { c3gGsmRadio 3 }

c3gGsmHistoryEntry OBJECT-TYPE
    SYNTAX          C3gGsmHistoryEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmHistoryTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gGsmHistoryTable 1 }

C3gGsmHistoryEntry ::= SEQUENCE {
        c3gGsmHistoryRssiPerSecond OCTET STRING,
        c3gGsmHistoryRssiPerMinute OCTET STRING,
        c3gGsmHistoryRssiPerHour   OCTET STRING
}

c3gGsmHistoryRssiPerSecond OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (60))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-second RSSI history.  This object contains a
        per-second history of RSSI values for the last 60 seconds." 
    ::= { c3gGsmHistoryEntry 1 }

c3gGsmHistoryRssiPerMinute OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (60))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-minute weakest RSSI value history.  This object
        contains a per-minute history of weakest RSSI values for the
        last 60 minutes.  The octet in the string is the weakest RSSI
        value measured in a minute interval." 
    ::= { c3gGsmHistoryEntry 2 }

c3gGsmHistoryRssiPerHour OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE  (72))
    UNITS           "-dBm"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Per-hour weakest RSSI value history.  This object
        contains a per-hour history of weakest RSSI values for the last
        72 hours.  The octet in the string is the weakest RSSI value
        measured in an hour interval." 
    ::= { c3gGsmHistoryEntry 3 }
 

c3gGsmSecurity  OBJECT IDENTIFIER
    ::= { c3gWanGsm 5 }


c3gGsmSecurityTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gGsmSecurityEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "Cellular 3G GSM/4G-LTE security table."
    ::= { c3gGsmSecurity 1 }

c3gGsmSecurityEntry OBJECT-TYPE
    SYNTAX          C3gGsmSecurityEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry (conceptual row) in the c3gGsmSecurityTable."
    INDEX           { entPhysicalIndex } 
    ::= { c3gGsmSecurityTable 1 }

C3gGsmSecurityEntry ::= SEQUENCE {
        c3gGsmChv1                     INTEGER,
        c3gGsmSimStatus                INTEGER,
        c3gGsmSimUserOperationRequired INTEGER,
        c3gGsmNumberOfRetriesRemaining Unsigned32
}

c3gGsmChv1 OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        disabled(2),
                        enabled(3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "Card Holder Verification 1 (CHV1), if enabled, the PIN will be
        verified, if disabled, the PIN will not be verified."
    DEFVAL          { disabled } 
    ::= { c3gGsmSecurityEntry 1 }

c3gGsmSimStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        ok(2),
                        notInserted(3),
                        removed(4),
                        initFailure(5),
                        generalFailure(6),
                        locked(7),
                        chv1Blocked(8),
                        chv2Blocked(9),
                        chv1Rejected(10),
                        chv2Rejected(11),
                        mepLocked(12),
                        networkRejected(13)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "SIM status. Indicates whether the SIM is present or removed
        from the SIM socket, and its current status."
    DEFVAL          { unknown } 
    ::= { c3gGsmSecurityEntry 2 }

c3gGsmSimUserOperationRequired OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        none(2),
                        enterChv1(3),
                        enterChv2(4),
                        enterUnblockChv1(5),
                        enterUnblockChv2(6),
                        enterMepCode(7)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "If the SIM is protected (for example, because of CHV1 enabled),
        it will indicate the type of user operation required."
    DEFVAL          { unknown } 
    ::= { c3gGsmSecurityEntry 3 }

c3gGsmNumberOfRetriesRemaining OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Indicates the number of attempts remaining in case the SIM is
        locked. If the number of retries becomes zero, the SIM is
        blocked and becomes unusable."
    DEFVAL          { 0 } 
    ::= { c3gGsmSecurityEntry 4 }
 

c3gWanLbsCommon  OBJECT IDENTIFIER
    ::= { c3gWanLbs 1 }


c3gWanLbsCommonTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gWanLbsCommonEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains information about the Cellular Location
        Based service feature. This GPS data is provided by the wireless
        modem upon GPS configuration."
    ::= { c3gWanLbsCommon 1 }

c3gWanLbsCommonEntry OBJECT-TYPE
    SYNTAX          C3gWanLbsCommonEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This entry contains information about the
        Cellular Location Based service
        variables returned by the wireless modem."
    INDEX           { entPhysicalIndex } 
    ::= { c3gWanLbsCommonTable 1 }

C3gWanLbsCommonEntry ::= SEQUENCE {
        c3gLbsModeSelected           INTEGER,
        c3gLbsState                  INTEGER,
        c3gLbsLocFixError            INTEGER,
        c3gLbsLatitude               SnmpAdminString,
        c3gLbsLongitude              SnmpAdminString,
        c3gLbsTimeStamp              SnmpAdminString,
        c3gLbsLocUncertaintyAngle    Unsigned32,
        c3gLbsLocUncertaintyA        Unsigned32,
        c3gLbsLocUncertaintyPos      Unsigned32,
        c3gLbsFixtype                INTEGER,
        c3gLbsHeightValid            TruthValue,
        c3gLbsHeight                 Integer32,
        c3gLbsLocUncertaintyVertical Unsigned32,
        c3gLbsVelocityValid          TruthValue,
        c3gLbsHeading                Unsigned32,
        c3gLbsVelocityHorizontal     Unsigned32,
        c3gLbsVelocityVertical       Unsigned32,
        c3gLbsHepe                   Unsigned32,
        c3gLbsNumSatellites          Gauge32
}

c3gLbsModeSelected OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unKnown(1),
                        standAlone(2),
                        msBased(3),
                        msAssist(4),
                        reserved(5)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Mode of Location base service selected.

        unKnown    - mode selection unkown
        standAlone - Standalone mode 
        msBased    - MS-Based mode
        msAssist   - MS-Assist mode
        reserved   - reserved for future use"
    DEFVAL          { unKnown } 
    ::= { c3gWanLbsCommonEntry 1 }

c3gLbsState OBJECT-TYPE
    SYNTAX          INTEGER  {
                        gpsDisabled(1),
                        gpsAcquiring(2),
                        gpsEnabled(3),
                        gpsLocError(4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Location base service state.

        gpsDisabled - GPS is disabled
        gpsEnabled  - GPS is enabled
        gpsLocError - GPS encounters error
        gpsAcquiring - GPS is acquiring fix"
    DEFVAL          { gpsDisabled } 
    ::= { c3gWanLbsCommonEntry 2 }

c3gLbsLocFixError OBJECT-TYPE
    SYNTAX          INTEGER  {
                        offline(1),
                        noService(2),
                        noConnection(3),
                        noData(4),
                        sessionBusy(5),
                        reserved(6),
                        gpsDisabled(7),
                        connectionFailed(8),
                        errorState(9),
                        clientEnded(10),
                        uiEnded(11),
                        networkEnded(12),
                        timeout(13),
                        privacyLevel(14),
                        networkAccessError(15),
                        fixError(16),
                        pdeRejected(17),
                        trafficChannelExited(18),
                        e911(19),
                        serverError(20),
                        staleBSinformation(21),
                        resourceContention(22),
                        authenticationParameterFailed(23),
                        authenticationFailedLocal(24),
                        authenticationFailedNetwork(25),
                        vxLcsAgentAuthFail(26),
                        unknownSystemError(27),
                        unsupportedService(28),
                        subscriptionViolation(29),
                        desiredFixMethodFailed(30),
                        antennaSwitch(31),
                        noTxConfirmationReceived(32),
                        normalEndOfSession(33),
                        noErrorFromNetwork(34),
                        noResourcesLeftOnNetwork(35),
                        positionServerNotAvailable(36),
                        unsupportedProtocolVersion(37),
                        ssmolrErrorSystemFailure(38),
                        ssmolrErrorUnexpectedDataValue(39),
                        ssmolrErrorDataMissing(40),
                        ssmolrErrorFacilityNotSupported(41),
                        ssmolrErrorSsSubscriptionViolation(42),
                        ssmolrErrorPositionMethodFailure(43),
                        ssmolrErrorUndefined(44),
                        smlcTimeout(45),
                        mtGguardTimeExpired(46),
                        additionalAssistanceNeeded(47),
                        noFixError(48)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Location base service fix error code."
    REFERENCE
        "Refer to the following documents for the error code's full
        definitions. Sierra Wireless CDMA EVDO CnS Reference_1.2.pdf 
        under Location Based Services section."
    DEFVAL          { noFixError } 
    ::= { c3gWanLbsCommonEntry 3 }

c3gLbsLatitude OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Location base service Latitude." 
    ::= { c3gWanLbsCommonEntry 4 }

c3gLbsLongitude OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Location base service longitude." 
    ::= { c3gWanLbsCommonEntry 5 }

c3gLbsTimeStamp OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Location base service timestamp." 
    ::= { c3gWanLbsCommonEntry 6 }

c3gLbsLocUncertaintyAngle OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "degrees"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "GPS Uncertainty parameter Angle, in degrees
        for the Uncertainty info returned by the GPS
        device while doing a location fix."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 7 }

c3gLbsLocUncertaintyA OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "meters"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "GPS Uncertainty parameter A, value in meters
        for the Uncertainty info returned by the GPS
        device while doing a location fix."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 8 }

c3gLbsLocUncertaintyPos OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "meters"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "GPS Uncertainty parameter position, value in meters
        for the Uncertainty info returned by the GPS device
        while doing a location fix."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 9 }

c3gLbsFixtype OBJECT-TYPE
    SYNTAX          INTEGER  {
                        none(1),
                        twoDimension(2),
                        threeDimension(3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The type of location fix in Location Base service.

        none          -  default case, while LBS is not enabled.
        twoDimension   - 2D location fix.
        threeDimension - 3D location fix."
    DEFVAL          { none } 
    ::= { c3gWanLbsCommonEntry 10 }

c3gLbsHeightValid OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates whether the height returned by
        the GPS device is valid during location fix."
    DEFVAL          { false } 
    ::= { c3gWanLbsCommonEntry 11 }

c3gLbsHeight OBJECT-TYPE
    SYNTAX          Integer32 (-500..500)
    UNITS           "meters"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the GPS height parameter returned
        by the GPS device while performing location fix."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 12 }

c3gLbsLocUncertaintyVertical OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "meters"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "GPS parameter vertical velocity parameter returned by
        the GPS device while performing location fix."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 13 }

c3gLbsVelocityValid OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates whether the Velocity value
        returned by the GPS device is valid."
    DEFVAL          { false } 
    ::= { c3gWanLbsCommonEntry 14 }

c3gLbsHeading OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "degrees"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The compass direction toward which the GPS receiver
        is (or should be) moving."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 15 }

c3gLbsVelocityHorizontal OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "meters per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Horizontal Velocity in meters per second the GPS
        device is heading. This is the value returned by
        the GPS satellite relative to the last horizontal 
        location of the GPS device. If at Time X satellite 
        sees the location of GPS device is L1 and then at 
        Time Y satellite sees the location is L2 then 
        speed is (L2 - L1) / ( Y - X)."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 16 }

c3gLbsVelocityVertical OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "meters per second"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Vertical Velocity in meters per second
        the GPS device is heading. This is the value returned by
        the GPS satellite relative to the last vertical location of
        the GPS device. If at Time X satellite sees the location 
        of GPS device is L1 and then at Time Y satellite sees
        the location is L2 then speed is (L2 - L1) / ( Y - X)."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 17 }

c3gLbsHepe OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "centimeters"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Horizontal Estimated Position Error returned by the
        GPS satellite for current position of the GPS device."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 18 }

c3gLbsNumSatellites OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of GPS satellites in vision to the modem
        while GPS tracking is on."
    DEFVAL          { 0 } 
    ::= { c3gWanLbsCommonEntry 19 }
 

c3gWanLbsSatelliteInfo  OBJECT IDENTIFIER
    ::= { c3gWanLbs 2 }


c3gWanLbsSatelliteInfoTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gWanLbsSatelliteInfoEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides information on each satellite that is
        visible to the modem during the location fix. These 
        satellites guide the device to acquire a 2D or 3D 
        location fix."
    ::= { c3gWanLbsSatelliteInfo 1 }

c3gWanLbsSatelliteInfoEntry OBJECT-TYPE
    SYNTAX          C3gWanLbsSatelliteInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table provides information about each satellite's
        elevation, azimuth, Signal to Noise ratio (SNR) and 
        its reference number local to the router."
    INDEX           {
                        entPhysicalIndex,
                        c3gWanLbsSatelliteInfoIndex
                    } 
    ::= { c3gWanLbsSatelliteInfoTable 1 }

C3gWanLbsSatelliteInfoEntry ::= SEQUENCE {
        c3gWanLbsSatelliteInfoIndex            Integer32,
        c3gWanLbsSatelliteNumber               Integer32,
        c3gWanLbsSatelliteElevation            Integer32,
        c3gWanLbsSatelliteAzimuth              Integer32,
        c3gWanLbsSatelliteInfoSignalNoiseRatio Integer32,
        c3gWanLbsSatelliteUsed                 TruthValue,
        c3gWanLbsSatelliteInfoRowStatus        RowStatus
}

c3gWanLbsSatelliteInfoIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..1000)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An index that is assigned to each satellite under a modem
        and in combination with entPhysicalIndex uniquely identify it. 
        This index is assigned arbitrarily by the engine and is not 
        saved over reboots." 
    ::= { c3gWanLbsSatelliteInfoEntry 1 }

c3gWanLbsSatelliteNumber OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Each Satellite is assigned a unique number
        within this device.This object can be used 
        to locate a particular satellite under 
        a modem."
    REFERENCE
        "Refer to the following documents for detailed
        information of Satellites. 
        Sierra Wireless CDMA EVDO CnS Reference_1.2.pdf under
        Location Based Services section" 
    ::= { c3gWanLbsSatelliteInfoEntry 2 }

c3gWanLbsSatelliteElevation OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "degrees"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Angle of Elevation between the GPS antenna pointing direction,
        directly towards the satellite, and the local horizontal plane.
        It is the up-down angle"
    REFERENCE
        "Refer to the following documents for detailed
        information of Satellites elevation.
        Sierra Wireless CDMA EVDO CnS Reference_1.2.pdf under
        Location Based Services section" 
    ::= { c3gWanLbsSatelliteInfoEntry 3 }

c3gWanLbsSatelliteAzimuth OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "degrees"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Azimuth of the current satellite in context
        referenced by the Satellite InfoIndex.
        Azimuth is the degree of rotation of the
        satellites dish on its vertical plane."
    REFERENCE
        "Refer to the following documents for detailed
        information of Satellites Azimuth.
        Sierra Wireless CDMA EVDO CnS Reference_1.2.pdf under
        Location Based Services section" 
    ::= { c3gWanLbsSatelliteInfoEntry 4 }

c3gWanLbsSatelliteInfoSignalNoiseRatio OBJECT-TYPE
    SYNTAX          Integer32
    UNITS           "db"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Signal to Noise Ratio(SNR) of received GPS signal.
        SNR is refered to as the signal strength in
        GPS standards."
    REFERENCE
        "Refer to the following documents for detailed
        information of signal to noise ration in LBS.
        Sierra Wireless CDMA EVDO CnS Reference_1.2.pdf under
        Location Based Services section" 
    ::= { c3gWanLbsSatelliteInfoEntry 5 }

c3gWanLbsSatelliteUsed OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Is this satellite in line of sight to the
        modem used in calculating the GPS location?"
    DEFVAL          { false } 
    ::= { c3gWanLbsSatelliteInfoEntry 6 }

c3gWanLbsSatelliteInfoRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The status of this conceptual row. This object is used to
        manage creation, modification and deletion of rows in this
        table." 
    ::= { c3gWanLbsSatelliteInfoEntry 7 }
 

c3gWanSms  OBJECT IDENTIFIER
    ::= { c3gWanSmsCommon 1 }


c3gSmsCommonTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF C3gSmsCommonEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table contains Cellular SMS management
        MIB objects."
    ::= { c3gWanSms 1 }

c3gSmsCommonEntry OBJECT-TYPE
    SYNTAX          C3gSmsCommonEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entry contains counters for the SMS messages
        received, placed, errored and archived on CDMA,
        GSM or LTE based modems."
    INDEX           { entPhysicalIndex } 
    ::= { c3gSmsCommonTable 1 }

C3gSmsCommonEntry ::= SEQUENCE {
        c3gSmsServiceAvailable        TruthValue,
        c3gSmsOutSmsCount             Counter32,
        c3gSmsOutSmsErrorCount        Counter32,
        c3gSmsInSmsStorageUsed        Gauge32,
        c3gSmsInSmsStorageUnused      Gauge32,
        c3gSmsInSmsArchiveCount       Gauge32,
        c3gSmsInSmsArchiveErrorCount  Gauge32,
        c3gSmsArchiveUrl              SnmpAdminString,
        c3gSmsOutSmsStatus            INTEGER,
        c3gSmsInSmsCount              Counter32,
        c3gSmsInSmsDeleted            Counter32,
        c3gSmsInSmsStorageMax         Counter64,
        c3gSmsInSmsCallBack           Counter32,
        c3gSmsOutSmsPendingCount      Gauge32,
        c3gSmsOutSmsArchiveCount      Gauge32,
        c3gSmsOutSmsArchiveErrorCount Gauge32,
        c3gSmsInSmsArchived           Gauge32
}

c3gSmsServiceAvailable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object indicates the availability of SMS Service."
    DEFVAL          { false } 
    ::= { c3gSmsCommonEntry 1 }

c3gSmsOutSmsCount OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of SMS messages which have been sent
        successfully." 
    ::= { c3gSmsCommonEntry 2 }

c3gSmsOutSmsErrorCount OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of SMS message that could not be sent." 
    ::= { c3gSmsCommonEntry 3 }

c3gSmsInSmsStorageUsed OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of SMS message records space used in the
        Incoming SMS message storage. One standard
        SMS message (cdma or gsm) occupies 1 unit of 
        record storage space. A big SMS message can span
        'n' sms record space but still be called as 1 SMS 
        message. Storage used can be greater than or equal
        to total number of Incoming SMS received." 
    ::= { c3gSmsCommonEntry 4 }

c3gSmsInSmsStorageUnused OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of SMS messages record space left
        unused in the Incoming SMS message storage. 
        This is equal to c3gSmsInSmsStorageMax - 
        c3gSmsInSmsStorageUsed." 
    ::= { c3gSmsCommonEntry 5 }

c3gSmsInSmsArchiveCount OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of successful archive of Incoming SMS messages
        since router reload. Each SMS message occupies x bytes of
        space. So if the incoming message is huge, then it is 
        archived as multiple of x bytes but still called as one
        SMS message. This is the difference between 
        c3gSmsInSmsArchiveCount and c3gSmsInSmsArchived." 
    ::= { c3gSmsCommonEntry 6 }

c3gSmsInSmsArchiveErrorCount OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of Incoming SMS messages that could not be
        archived since device was reloaded." 
    ::= { c3gSmsCommonEntry 7 }

c3gSmsArchiveUrl OBJECT-TYPE
    SYNTAX          SnmpAdminString
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "URL of the sms archive directory on the ftp server.
        The url will be of this format
        ftp://x.y.z.k/user/dirname" 
    ::= { c3gSmsCommonEntry 8 }

c3gSmsOutSmsStatus OBJECT-TYPE
    SYNTAX          INTEGER  {
                        unknown(1),
                        success(2),
                        copySmsHeader(3),
                        copySmsBody(4),
                        sent(5),
                        receivedSentNotification(6),
                        receivedOutMsgNumber(7),
                        receivedOutMsgStatus(8)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Status of the last send operation of outgoing
        SMS message to the network." 
    ::= { c3gSmsCommonEntry 9 }

c3gSmsInSmsCount OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of SMS messages which have been
        received successfully and stored in router.
        These SMS's are a mirror copy of SMS stored in
        Modem or SIM" 
    ::= { c3gSmsCommonEntry 10 }

c3gSmsInSmsDeleted OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of SMS messages which have been
        deleted since router boot up. This does 
        not include SMS messages that are already 
        archived." 
    ::= { c3gSmsCommonEntry 11 }

c3gSmsInSmsStorageMax OBJECT-TYPE
    SYNTAX          Counter64
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of SMS message records space allocated
        in the router's DRAM to store Incoming SMS messages." 
    ::= { c3gSmsCommonEntry 12 }

c3gSmsInSmsCallBack OBJECT-TYPE
    SYNTAX          Counter32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of incoming SMS messages that triggered
        callback." 
    ::= { c3gSmsCommonEntry 13 }

c3gSmsOutSmsPendingCount OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of outgoing SMS messages that are in
        pending queue of the router." 
    ::= { c3gSmsCommonEntry 14 }

c3gSmsOutSmsArchiveCount OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of successfull archive of outgoing SMS messages
        since router reload." 
    ::= { c3gSmsCommonEntry 15 }

c3gSmsOutSmsArchiveErrorCount OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of failed archive of outgoing SMS messages
        since router reload." 
    ::= { c3gSmsCommonEntry 16 }

c3gSmsInSmsArchived OBJECT-TYPE
    SYNTAX          Gauge32
    UNITS           "msgs"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "Number of Incoming SMS messages that are successfully
        archived since router reload." 
    ::= { c3gSmsCommonEntry 17 }
 


-- Default Notification Type

c3gModemUpNotif NOTIFICATION-TYPE
    OBJECTS         { entPhysicalName }
    STATUS          current
    DESCRIPTION
        "This is the notification that the modem has been detected by
        host interface. Users can enable or disable the generation of
        this notification by using object c3gModemUpNotifEnabled."
   ::= { ciscoWan3gMIBNotifs 1 }

c3gModemDownNotif NOTIFICATION-TYPE
    OBJECTS         { entPhysicalName }
    STATUS          current
    DESCRIPTION
        "This is the notification that the modem has not been detected
        by host interface, or has been disconnected from host interface.
        Users can enable or disable the generation of this notification
        by using object c3gModemDownNotifEnabled."
   ::= { ciscoWan3gMIBNotifs 2 }

c3gServiceChangedNotif NOTIFICATION-TYPE
    OBJECTS         {
                        c3gPreviousServiceType,
                        c3gCurrentServiceType
                    }
    STATUS          current
    DESCRIPTION
        "Notification for service change event.  Objects
        c3gPreviousServiceType and c3gCurrentServiceType will be
        included in the notification.  Users can enable or disable the
        generation of this notification by using object
        c3gServiceChangedNotifEnabled."
   ::= { ciscoWan3gMIBNotifs 3 }

c3gNetworkChangedNotif NOTIFICATION-TYPE
    OBJECTS         {
                        c3gCurrentSid,
                        c3gCurrentNid,
                        c3gGsmMcc,
                        c3gGsmMnc,
                        c3gRoamingStatus
                    }
    STATUS          current
    DESCRIPTION
        "Notification for network change event.  Objects c3gCurrentSid,
        c3gCurrentNid, c3gGsmMcc, c3gGsmMnc and c3gRoamingStatus will
        be included in the notification.  Users can enable or disable
        the generation of this notification by using object
        c3gNetworkChangedNotifEnabled."
   ::= { ciscoWan3gMIBNotifs 4 }

c3gConnectionStatusChangedNotif NOTIFICATION-TYPE
    OBJECTS         {
                        c3gConnectionStatus,
                        c3gCurrentServiceType
                    }
    STATUS          current
    DESCRIPTION
        "Notification for connection status change event.  Objects
        c3gConnectionStatus and c3gCurrentServiceType will be included
        in the notification.  Users can use object
        c3gConnectionStatusChangedNotifFlag to control what connection
        status changes will cause the generation of this notification."
   ::= { ciscoWan3gMIBNotifs 5 }

c3gRssiOnsetNotif NOTIFICATION-TYPE
    OBJECTS         {
                        c3gNotifRadioService,
                        c3gNotifRssi
                    }
    STATUS          current
    DESCRIPTION
        "If RSSI goes below c3gRssiOnsetNotifThreshold and the service
        bit in c3gRssiOnsetNotifFlag is set, this notification will be
        generated.  Object c3gNotifRadioService will indicate which 
        service generates this notification and the associated RSSI 
        will be reported in c3gNotifRssi.  Please note that c3gNotifRssi
        is used to indicate the RSSI value that triggers the
        notification, user should go to the corresponding radio table to
        get the current RSSI value."
   ::= { ciscoWan3gMIBNotifs 6 }

c3gRssiAbateNotif NOTIFICATION-TYPE
    OBJECTS         {
                        c3gNotifRadioService,
                        c3gNotifRssi
                    }
    STATUS          current
    DESCRIPTION
        "If RSSI goes above c3gRssiAbateNotifThreshold and the service
        bit in c3gRssiAbateNotifFlag is set, this notification will be
        generated.  Object c3gNotifRadioService will indicate which 
        service generates this notification and the associated RSSI 
        will be reported in c3gNotifRssi.  Please note that c3gNotifRssi
        is used to indicate the RSSI value that triggers the
        notification, user should go to the corresponding radio table to
        get the current RSSI value."
   ::= { ciscoWan3gMIBNotifs 7 }

c3gEcIoOnsetNotif NOTIFICATION-TYPE
    OBJECTS         {
                        c3gNotifRadioService,
                        c3gNotifEcIo
                    }
    STATUS          current
    DESCRIPTION
        "If Ec/Io goes below c3gEcIoOnsetNotifThreshold and the service
        bit in c3gEcIoOnsetNotifFlag is set, this notification will be
        generated.  Object c3gNotifRadioService will indicate which 
        service generates this notification and the associated Ec/Io
        will be reported in c3gNotifEcIo.  Please note that c3gNotifEcIo
        is used to indicate the Ec/Io value that triggers the
        notification, user should go to the corresponding radio table to
        get the current Ec/Io value."
   ::= { ciscoWan3gMIBNotifs 8 }

c3gEcIoAbateNotif NOTIFICATION-TYPE
    OBJECTS         {
                        c3gNotifRadioService,
                        c3gNotifEcIo
                    }
    STATUS          current
    DESCRIPTION
        "If Ec/Io goes above c3gEcIoAbateNotifThreshold and the service
        bit in c3gEcIoAbateNotifFlag is set, this notification will be
        generated.  Object c3gNotifRadioService will indicate which 
        service generates this notification and the associated Ec/Io
        will be reported in c3gNotifEcIo.  Please note that c3gNotifEcIo
        is used to indicate the Ec/Io value that triggers the
        notification, user should go to the corresponding radio table to
        get the current Ec/Io value."
   ::= { ciscoWan3gMIBNotifs 9 }

c3gModemTemperOnsetNotif NOTIFICATION-TYPE
    OBJECTS         { c3gModemTemperature }
    STATUS          current
    DESCRIPTION
        "If modem temperature goes above
        c3gModemTemperOnsetNotifThreshold and the value of
        c3gModemTemperOnsetNotifEnabled is 'true', this notification
        will be generated and the current value of c3gModemTemperature
        will be included in this notification."
   ::= { ciscoWan3gMIBNotifs 10 }

c3gModemTemperAbateNotif NOTIFICATION-TYPE
    OBJECTS         { c3gModemTemperature }
    STATUS          current
    DESCRIPTION
        "If modem temperature goes below
        c3gModemTemperAbateNotifThreshold and the value of
        c3gModemTemperAbateNotifEnabled is 'true', this notification
        will be generated and the current value of c3gModemTemperature
        will be included in this notification."
   ::= { ciscoWan3gMIBNotifs 11 }

c3gModemTemperOnsetRecoveryNotif NOTIFICATION-TYPE
    OBJECTS         { c3gModemTemperature }
    STATUS          current
    DESCRIPTION
        "This trap is generated as a recovery notification for
        c3gModemTemperOnsetNotif.This trap is generated when the current
        value of c3gModemTemperature goes below
        c3gModemTemperOnsetNotifThreshold once it has generated the
        c3gModemTemperOnsetNotif and the value of
        c3gModemTemperOnsetNotifEnabled is 'true'.

        c3gModemTemperature contains the current value of modem
        temperature."
   ::= { ciscoWan3gMIBNotifs 12 }

c3gModemTemperAbateRecoveryNotif NOTIFICATION-TYPE
    OBJECTS         { c3gModemTemperature }
    STATUS          current
    DESCRIPTION
        "This trap is generated as a recovery notification for
        c3gModemTemperAbateNotif.This trap is generated when the current
        value of c3gModemTemperature goes above
        c3gModemTemperAbateNotifThreshold once it has generated the
        c3gModemTemperAbateNotif and the value of
        c3gModemTemperAbateNotifEnabled is 'true'

        c3gModemTemperature contains the current value of modem
        temperature"
   ::= { ciscoWan3gMIBNotifs 13 }
-- Conformance

ciscoWan3gMIBCompliances  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIBConform 1 }

ciscoWan3gMIBGroups  OBJECT IDENTIFIER
    ::= { ciscoWan3gMIBConform 2 }


ciscoWan3gMIBCompliance MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "This is a default module-compliance
        containing default object groups."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoWan3gMIBNotificationGroup,
                        ciscoWan3gMIBCommonObjectGroup
                    }

    GROUP           ciscoWan3gMIBCdmaObjectGroup
    DESCRIPTION
        "This object group should be included for CDMA standard."

    GROUP           ciscoWan3gMIBGsmObjectGroup
    DESCRIPTION
        "This object group should be included for GSM/LTE  standard."

    GROUP           ciscoWan3gMIBSmsObjectGroup
    DESCRIPTION
        "This object group should be included
        for SMS service on CDMA, GSM and LTE standard."

    GROUP           ciscoWan3gMIBLbsObjectGroup
    DESCRIPTION
        "This object group should be included for
        Location based service on CDMA, GSM  and LTE standard."
    ::= { ciscoWan3gMIBCompliances 1 }

ciscoWan3gMIBCompliance1 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "The compliance statement for the CISCO-WAN-3G-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoWan3gMIBNotificationGroup,
                        ciscoWan3gMIBCommonObjectGroup
                    }

    GROUP           ciscoWan3gMIBCdmaObjectGroup
    DESCRIPTION
        "This object group should be included for CDMA standard."

    GROUP           ciscoWan3gMIBGsmObjectGroup
    DESCRIPTION
        "This object group should be included for GSM and
        LTE standard."

    GROUP           ciscoWan3gMIBSmsObjectGroup
    DESCRIPTION
        "This object group should be included
        for SMS service on CDMA, GSM and LTE standard."

    GROUP           ciscoWan3gMIBLbsObjectGroup
    DESCRIPTION
        "This object group should be included for
        Location based service on CDMA, GSM  and LTE standard."
    ::= { ciscoWan3gMIBCompliances 2 }

ciscoWan3gMIBComplianceRev1 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "This is a default module-compliance
        containing default object groups."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoWan3gMIBNotificationGroupRev1,
                        ciscoWan3gMIBCommonObjectGroup
                    }

    GROUP           ciscoWan3gMIBCdmaObjectGroup
    DESCRIPTION
        "This object group should be included for CDMA standard."

    GROUP           ciscoWan3gMIBGsmObjectGroup
    DESCRIPTION
        "This object group should be included for GSM/LTE  standard."

    GROUP           ciscoWan3gMIBSmsObjectGroup
    DESCRIPTION
        "This object group should be included
        for SMS service on CDMA, GSM and LTE standard."

    GROUP           ciscoWan3gMIBLbsObjectGroup
    DESCRIPTION
        "This object group should be included for
        Location based service on CDMA, GSM  and LTE standard."
    ::= { ciscoWan3gMIBCompliances 3 }

ciscoWan3gMIBCompliance1Rev1 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "The compliance statement for the CISCO-WAN-3G-MIB."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        ciscoWan3gMIBNotificationGroupRev1,
                        ciscoWan3gMIBCommonObjectGroup
                    }

    GROUP           ciscoWan3gMIBCdmaObjectGroup
    DESCRIPTION
        "This object group should be included for CDMA standard."

    GROUP           ciscoWan3gMIBGsmObjectGroup
    DESCRIPTION
        "This object group should be included for GSM and
        LTE standard."

    GROUP           ciscoWan3gMIBSmsObjectGroup
    DESCRIPTION
        "This object group should be included
        for SMS service on CDMA, GSM and LTE standard."

    GROUP           ciscoWan3gMIBLbsObjectGroup
    DESCRIPTION
        "This object group should be included for
        Location based service on CDMA, GSM  and LTE standard."
    ::= { ciscoWan3gMIBCompliances 4 }

-- Units of Conformance

ciscoWan3gMIBCommonObjectGroup OBJECT-GROUP
    OBJECTS         {
                        c3gStandard,
                        c3gCapability,
                        c3gModemState,
                        c3gPreviousServiceType,
                        c3gCurrentServiceType,
                        c3gRoamingStatus,
                        c3gCurrentSystemTime,
                        c3gConnectionStatus,
                        c3gNotifRadioService,
                        c3gNotifRssi,
                        c3gNotifEcIo,
                        c3gModemTemperature,
                        c3gRssiOnsetNotifThreshold,
                        c3gRssiAbateNotifThreshold,
                        c3gEcIoOnsetNotifThreshold,
                        c3gEcIoAbateNotifThreshold,
                        c3gModemTemperOnsetNotifThreshold,
                        c3gModemTemperAbateNotifThreshold,
                        c3gModemReset,
                        c3gModemUpNotifEnabled,
                        c3gModemDownNotifEnabled,
                        c3gServiceChangedNotifEnabled,
                        c3gNetworkChangedNotifEnabled,
                        c3gConnectionStatusChangedNotifFlag,
                        c3gRssiOnsetNotifFlag,
                        c3gRssiAbateNotifFlag,
                        c3gEcIoOnsetNotifFlag,
                        c3gEcIoAbateNotifFlag,
                        c3gModemTemperOnsetNotifEnabled,
                        c3gModemTemperAbateNotifEnabled,
                        c3gGpsState
                    }
    STATUS          current
    DESCRIPTION
        "A collection of common objects for Cellular interface."
    ::= { ciscoWan3gMIBGroups 1 }

ciscoWan3gMIBCdmaObjectGroup OBJECT-GROUP
    OBJECTS         {
                        c3gCdmaTotalCallDuration,
                        c3gCdmaTotalTransmitted,
                        c3gCdmaTotalReceived,
                        c3gHdrDdtmPreference,
                        c3gOutgoingCallNumber,
                        c3gHdrAtState,
                        c3gHdrSessionState,
                        c3gUati,
                        c3gColorCode,
                        c3gRati,
                        c3gHdrSessionDuration,
                        c3gHdrSessionStart,
                        c3gHdrSessionEnd,
                        c3gAuthStatus,
                        c3gHdrDrc,
                        c3gHdrDrcCover,
                        c3gHdrRri,
                        c3gCdmaCurrentTransmitted,
                        c3gCdmaCurrentReceived,
                        c3gCdmaCurrentCallStatus,
                        c3gCdmaCurrentCallDuration,
                        c3gCdmaCurrentCallType,
                        c3gCdmaLastCallDisconnReason,
                        c3gCdmaLastConnError,
                        c3gMobileIpErrorCode,
                        c3gEsn,
                        c3gModemActivationStatus,
                        c3gAccountActivationDate,
                        c3gCdmaRoamingPreference,
                        c3gPrlVersion,
                        c3gMdn,
                        c3gMsid,
                        c3gMsl,
                        c3gCdmaCurrentServiceStatus,
                        c3gCdmaHybridModePreference,
                        c3gCdmaCurrentRoamingStatus,
                        c3gCurrentIdleDigitalMode,
                        c3gCurrentSid,
                        c3gCurrentNid,
                        c3gCurrentCallSetupMode,
                        c3gSipUsername,
                        c3gSipPassword,
                        c3gServingBaseStationLongitude,
                        c3gServingBaseStationLatitude,
                        c3gNumberOfDataProfileConfigurable,
                        c3gCurrentActiveDataProfile,
                        c3gNai,
                        c3gAaaPassword,
                        c3gMnHaSs,
                        c3gMnHaSpi,
                        c3gMnAaaSs,
                        c3gMnAaaSpi,
                        c3gReverseTunnelPreference,
                        c3gHomeAddrType,
                        c3gHomeAddr,
                        c3gPriHaAddrType,
                        c3gPriHaAddr,
                        c3gSecHaAddrType,
                        c3gSecHaAddr,
                        c3gCurrent1xRttRssi,
                        c3gCurrent1xRttEcIo,
                        c3gCurrent1xRttChannelNumber,
                        c3gCurrent1xRttChannelState,
                        c3gCurrentEvDoRssi,
                        c3gCurrentEvDoEcIo,
                        c3gCurrentEvDoChannelNumber,
                        c3gSectorId,
                        c3gSubnetMask,
                        c3gHdrColorCode,
                        c3gPnOffset,
                        c3gRxMainGainControl,
                        c3gRxDiversityGainControl,
                        c3gTxTotalPower,
                        c3gTxGainAdjust,
                        c3gCarrierToInterferenceRatio,
                        c3g1xRttBandClass,
                        c3gEvDoBandClass,
                        c3gCdmaHistory1xRttRssiPerSecond,
                        c3gCdmaHistory1xRttRssiPerMinute,
                        c3gCdmaHistory1xRttRssiPerHour,
                        c3gCdmaHistoryEvDoRssiPerSecond,
                        c3gCdmaHistoryEvDoRssiPerMinute,
                        c3gCdmaHistoryEvDoRssiPerHour,
                        c3gCdmaPinSecurityStatus,
                        c3gCdmaPowerUpLockStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for Cellular 3G CDMA."
    ::= { ciscoWan3gMIBGroups 2 }

ciscoWan3gMIBGsmObjectGroup OBJECT-GROUP
    OBJECTS         {
                        c3gGsmTotalByteTransmitted,
                        c3gGsmTotalByteReceived,
                        c3gGsmPacketSessionStatus,
                        c3gGsmPdpType,
                        c3gGsmPdpAddress,
                        c3gGsmNegoUmtsQosTrafficClass,
                        c3gGsmNegoUmtsQosMaxUpLinkBitRate,
                        c3gGsmNegoUmtsQosMaxDownLinkBitRate,
                        c3gGsmNegoUmtsQosGuaUpLinkBitRate,
                        c3gGsmNegoUmtsQosGuaDownLinkBitRate,
                        c3gGsmNegoUmtsQosOrder,
                        c3gGsmNegoUmtsQosErroneousSdu,
                        c3gGsmNegoUmtsQosMaxSduSize,
                        c3gGsmNegoUmtsQosSer,
                        c3gGsmNegoUmtsQosBer,
                        c3gGsmNegoUmtsQosDelay,
                        c3gGsmNegoUmtsQosPriority,
                        c3gGsmNegoUmtsQosSrcStatDescriptor,
                        c3gGsmNegoUmtsQosSignalIndication,
                        c3gGsmNegoGprsQosPrecedence,
                        c3gGsmNegoGprsQosDelay,
                        c3gGsmNegoGprsQosReliability,
                        c3gGsmNegoGprsQosPeakRate,
                        c3gGsmNegoGprsQosMeanRate,
                        c3gImsi,
                        c3gImei,
                        c3gIccId,
                        c3gMsisdn,
                        c3gFsn,
                        c3gModemStatus,
                        c3gGsmRoamingPreference,
                        c3gGsmLac,
                        c3gGsmCurrentServiceStatus,
                        c3gGsmCurrentServiceError,
                        c3gGsmCurrentService,
                        c3gGsmPacketService,
                        c3gGsmCurrentRoamingStatus,
                        c3gGsmNetworkSelectionMode,
                        c3gGsmCountry,
                        c3gGsmNetwork,
                        c3gGsmMcc,
                        c3gGsmMnc,
                        c3gGsmRac,
                        c3gGsmCurrentCellId,
                        c3gGsmCurrentPrimaryScramblingCode,
                        c3gGsmPlmnSelection,
                        c3gGsmRegPlmn,
                        c3gGsmPlmnAbbr,
                        c3gGsmServiceProvider,
                        c3gGsmPdpProfileType,
                        c3gGsmPdpProfileAddr,
                        c3gGsmPdpProfileApn,
                        c3gGsmPdpProfileAuthenType,
                        c3gGsmPdpProfileUsername,
                        c3gGsmPdpProfilePassword,
                        c3gGsmPdpProfileRowStatus,
                        c3gGsmReqUmtsQosTrafficClass,
                        c3gGsmReqUmtsQosMaxUpLinkBitRate,
                        c3gGsmReqUmtsQosMaxDownLinkBitRate,
                        c3gGsmReqUmtsQosGuaUpLinkBitRate,
                        c3gGsmReqUmtsQosGuaDownLinkBitRate,
                        c3gGsmReqUmtsQosOrder,
                        c3gGsmReqUmtsQosErroneousSdu,
                        c3gGsmReqUmtsQosMaxSduSize,
                        c3gGsmReqUmtsQosSer,
                        c3gGsmReqUmtsQosBer,
                        c3gGsmReqUmtsQosDelay,
                        c3gGsmReqUmtsQosPriority,
                        c3gGsmReqUmtsQosSrcStatDescriptor,
                        c3gGsmReqUmtsQosSignalIndication,
                        c3gGsmReqUmtsQosRowStatus,
                        c3gGsmMinUmtsQosTrafficClass,
                        c3gGsmMinUmtsQosMaxUpLinkBitRate,
                        c3gGsmMinUmtsQosMaxDownLinkBitRate,
                        c3gGsmMinUmtsQosGuaUpLinkBitRate,
                        c3gGsmMinUmtsQosGuaDownLinkBitRate,
                        c3gGsmMinUmtsQosOrder,
                        c3gGsmMinUmtsQosErroneousSdu,
                        c3gGsmMinUmtsQosMaxSduSize,
                        c3gGsmMinUmtsQosSer,
                        c3gGsmMinUmtsQosBer,
                        c3gGsmMinUmtsQosDelay,
                        c3gGsmMinUmtsQosPriority,
                        c3gGsmMinUmtsQosSrcStatDescriptor,
                        c3gGsmMinUmtsQosSignalIndication,
                        c3gGsmMinUmtsQosRowStatus,
                        c3gGsmReqGprsQosPrecedence,
                        c3gGsmReqGprsQosDelay,
                        c3gGsmReqGprsQosReliability,
                        c3gGsmReqGprsQosPeakRate,
                        c3gGsmReqGprsQosMeanRate,
                        c3gGsmReqGprsQosRowStatus,
                        c3gGsmMinGprsQosPrecedence,
                        c3gGsmMinGprsQosDelay,
                        c3gGsmMinGprsQosReliability,
                        c3gGsmMinGprsQosPeakRate,
                        c3gGsmMinGprsQosMeanRate,
                        c3gGsmMinGprsQosRowStatus,
                        c3gCurrentGsmRssi,
                        c3gCurrentGsmEcIo,
                        c3gGsmCurrentBand,
                        c3gGsmChannelNumber,
                        c3gGsmNumberOfNearbyCell,
                        c3gGsmNearbyCellPrimaryScramblingCode,
                        c3gGsmNearbyCellRscp,
                        c3gGsmNearbyCellEcIoMeasurement,
                        c3gGsmHistoryRssiPerSecond,
                        c3gGsmHistoryRssiPerMinute,
                        c3gGsmHistoryRssiPerHour,
                        c3gGsmChv1,
                        c3gGsmSimStatus,
                        c3gGsmSimUserOperationRequired,
                        c3gGsmNumberOfRetriesRemaining
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for Cellular 3G GSM and LTE."
    ::= { ciscoWan3gMIBGroups 3 }

ciscoWan3gMIBNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        c3gModemUpNotif,
                        c3gModemDownNotif,
                        c3gServiceChangedNotif,
                        c3gNetworkChangedNotif,
                        c3gConnectionStatusChangedNotif,
                        c3gRssiOnsetNotif,
                        c3gEcIoOnsetNotif,
                        c3gRssiAbateNotif,
                        c3gEcIoAbateNotif,
                        c3gModemTemperOnsetNotif,
                        c3gModemTemperAbateNotif
                    }
    STATUS          deprecated
    DESCRIPTION
        "A collection of objects for Cellular WAN notifications.
        ciscoWan3gMIBNotificationGroup object is superseded by
        ciscoWan3gMIBNotificationGroupRev1."
    ::= { ciscoWan3gMIBGroups 4 }

ciscoWan3gMIBLbsObjectGroup OBJECT-GROUP
    OBJECTS         {
                        c3gLbsModeSelected,
                        c3gLbsState,
                        c3gLbsLocFixError,
                        c3gLbsLatitude,
                        c3gLbsLongitude,
                        c3gLbsTimeStamp,
                        c3gLbsLocUncertaintyAngle,
                        c3gLbsLocUncertaintyA,
                        c3gLbsLocUncertaintyPos,
                        c3gLbsFixtype,
                        c3gLbsHeightValid,
                        c3gLbsHeight,
                        c3gLbsLocUncertaintyVertical,
                        c3gLbsVelocityValid,
                        c3gLbsHeading,
                        c3gLbsVelocityHorizontal,
                        c3gLbsVelocityVertical,
                        c3gLbsHepe,
                        c3gLbsNumSatellites,
                        c3gWanLbsSatelliteNumber,
                        c3gWanLbsSatelliteElevation,
                        c3gWanLbsSatelliteAzimuth,
                        c3gWanLbsSatelliteUsed,
                        c3gWanLbsSatelliteInfoSignalNoiseRatio,
                        c3gWanLbsSatelliteInfoRowStatus
                    }
    STATUS          current
    DESCRIPTION
        "A collection of common objects for
        Cellular Location Based Service."
    ::= { ciscoWan3gMIBGroups 5 }

ciscoWan3gMIBSmsObjectGroup OBJECT-GROUP
    OBJECTS         {
                        c3gSmsServiceAvailable,
                        c3gSmsOutSmsCount,
                        c3gSmsOutSmsErrorCount,
                        c3gSmsInSmsStorageUsed,
                        c3gSmsInSmsStorageUnused,
                        c3gSmsInSmsArchiveCount,
                        c3gSmsInSmsArchiveErrorCount,
                        c3gSmsInSmsArchived,
                        c3gSmsArchiveUrl,
                        c3gSmsOutSmsStatus,
                        c3gSmsInSmsCount,
                        c3gSmsInSmsDeleted,
                        c3gSmsInSmsStorageMax,
                        c3gSmsInSmsCallBack,
                        c3gSmsOutSmsPendingCount,
                        c3gSmsOutSmsArchiveCount,
                        c3gSmsOutSmsArchiveErrorCount
                    }
    STATUS          current
    DESCRIPTION
        "A collection of common objects for
        Cellular Short Messaging Service."
    ::= { ciscoWan3gMIBGroups 6 }

ciscoWan3gMIBNotificationGroupRev1 NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        c3gModemUpNotif,
                        c3gModemDownNotif,
                        c3gServiceChangedNotif,
                        c3gNetworkChangedNotif,
                        c3gConnectionStatusChangedNotif,
                        c3gRssiOnsetNotif,
                        c3gEcIoOnsetNotif,
                        c3gRssiAbateNotif,
                        c3gEcIoAbateNotif,
                        c3gModemTemperOnsetNotif,
                        c3gModemTemperAbateNotif,
                        c3gModemTemperOnsetRecoveryNotif,
                        c3gModemTemperAbateRecoveryNotif
                    }
    STATUS          current
    DESCRIPTION
        "A collection of objects for Cellular WAN notifications."
    ::= { ciscoWan3gMIBGroups 7 }

END






