-- *********************************************************************
-- CISCO-UNIFIED-COMPUTING-COMPUTE-MIB.my
-- 
-- MIB representation of the Cisco Unified Computing System
-- COMPUTE management information model package
-- 
-- Created January 2016 by <PERSON>
-- 
-- Copyright (c) 2005-2016 Cisco Systems, Inc. All rights reserved.
-- 
-- *********************************************************************

CISCO-UNIFIED-COMPUTING-COMPUTE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    Counter32,
    Gauge32,
    TimeTicks,
    Counter64,
    Unsigned32
        FROM SNMPv2-SMI
    TEXTUAL-CONVENT<PERSON>,
    <PERSON><PERSON>ointer,
    DateAndTime,
    DisplayString,
    MacAddress,
    TimeInterval,
    TimeStamp,
    TruthValue
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    InetAddressIPv4,
    InetAddressIPv6
        FROM INET-ADDRESS-MIB
    ciscoMgmt
        FROM CISCO-SMI
    CiscoNetworkAddress,
    Unsigned64,
    CiscoInetAddressMask,
    CiscoAlarmSeverity,
    TimeIntervalSec
        FROM CISCO-TC
    ciscoUnifiedComputingMIBObjects,
    CucsManagedObjectId,
    CucsManagedObjectDn
        FROM CISCO-UNIFIED-COMPUTING-MIB
    CucsComputeABoardPower,
    CucsComputeAdminLinkAggregation,
    CucsComputeAdminMemoryState,
    CucsComputeAdminPowerState,
    CucsComputeAdminState,
    CucsComputeAdminTrigger,
    CucsComputeAlarmSeverity,
    CucsComputeAssociation,
    CucsComputeAvailability,
    CucsComputeBlackListing,
    CucsComputeBladeEpId,
    CucsComputeBladeEpSlotId,
    CucsComputeBladeFsmCurrentFsm,
    CucsComputeBladeFsmStageName,
    CucsComputeBladeFsmTaskFlags,
    CucsComputeBladeFsmTaskItem,
    CucsComputeBladeSlotId,
    CucsComputeCartridgeDiscovery,
    CucsComputeCartridgeSlotId,
    CucsComputeChassisConnPolicyChassisId,
    CucsComputeChassisDiscAction,
    CucsComputeChassisDiscPolicyMulticastHwHash,
    CucsComputeChassisQualMaxId,
    CucsComputeChassisQualMinId,
    CucsComputeCheckPoint,
    CucsComputeConnectivityRebalancing,
    CucsComputeDiscovery,
    CucsComputeEquipmentConstraintType,
    CucsComputeIOHubEnvStatsHistThresholded,
    CucsComputeIOHubEnvStatsThresholded,
    CucsComputeInstanceIdQualMaxId,
    CucsComputeInstanceIdQualMinId,
    CucsComputeIssues,
    CucsComputeKvmMgmtPolicyVmediaEncryption,
    CucsComputeLinkAggregation,
    CucsComputeMbPowerStatsHistThresholded,
    CucsComputeMbPowerStatsThresholded,
    CucsComputeMbTempStatsHistThresholded,
    CucsComputeMbTempStatsThresholded,
    CucsComputeMemoryUnitConstraintType,
    CucsComputeMode,
    CucsComputeOwner,
    CucsComputePCIeFatalCompletionStatsThresholded,
    CucsComputePCIeFatalProtocolStatsThresholded,
    CucsComputePCIeFatalReceiveStatsThresholded,
    CucsComputePCIeFatalStatsThresholded,
    CucsComputePciCapOrder,
    CucsComputePhysicalFsmCurrentFsm,
    CucsComputePhysicalFsmStageName,
    CucsComputePhysicalFsmTaskFlags,
    CucsComputePhysicalFsmTaskItem,
    CucsComputePhysicalLowVoltageMemory,
    CucsComputePooledEnclosureComputeSlotServerInstanceId,
    CucsComputePooledEnclosureComputeSlotSlotId,
    CucsComputePooledRackUnitId,
    CucsComputePooledSlotSlotId,
    CucsComputePowerTransitionSrc,
    CucsComputePsuClusterState,
    CucsComputePsuControlRedundancy,
    CucsComputePsuRedundancy,
    CucsComputePsuRedundancyOperQualifier,
    CucsComputePsuRedundancyOperState,
    CucsComputeRackQualMaxId,
    CucsComputeRackQualMinId,
    CucsComputeRackUnitFsmCurrentFsm,
    CucsComputeRackUnitFsmStageName,
    CucsComputeRackUnitFsmTaskFlags,
    CucsComputeRackUnitFsmTaskItem,
    CucsComputeRackUnitId,
    CucsComputeRackUnitMbTempStatsHistThresholded,
    CucsComputeRackUnitMbTempStatsThresholded,
    CucsComputeScrubAction,
    CucsComputeServerDiscPolicyFsmCurrentFsm,
    CucsComputeServerDiscPolicyFsmStageName,
    CucsComputeServerDiscPolicyFsmTaskItem,
    CucsComputeServerMgmtDiscAction,
    CucsComputeServerTypeCapType,
    CucsComputeServerUnitChassisId,
    CucsComputeServerUnitFsmCurrentFsm,
    CucsComputeServerUnitFsmStageName,
    CucsComputeServerUnitFsmTaskFlags,
    CucsComputeServerUnitFsmTaskItem,
    CucsComputeServerUnitServerInstanceId,
    CucsComputeServerUnitSlotId,
    CucsComputeSlotQualMaxId,
    CucsComputeSlotQualMinId,
    CucsComputeUpgradeStatus,
    CucsConditionRemoteInvRslt,
    CucsEquipmentBoardAggregationRole,
    CucsEquipmentBoardConnectorType,
    CucsEquipmentConnectionStatus,
    CucsEquipmentFanSpeedPolicyFault,
    CucsEquipmentOperability,
    CucsEquipmentPowerState,
    CucsEquipmentPresence,
    CucsEquipmentSensorThresholdStatus,
    CucsEquipmentSlotStatus,
    CucsFsmCompletion,
    CucsFsmFlags,
    CucsFsmFsmStageStatus,
    CucsLsOperState,
    CucsNetworkSwitchId,
    CucsPolicyPolicyOwner,
    CucsPoolPoolAssignmentOrder,
    CucsTrigAckChangeDetails,
    CucsTrigAckChanges,
    CucsTrigAckDisr,
    CucsTrigAckOperState,
    CucsTrigAckPrevOperState,
    CucsTrigAdminState,
    CucsTrigTrigState
        FROM CISCO-UNIFIED-COMPUTING-TC-MIB;

cucsComputeObjects MODULE-IDENTITY
    LAST-UPDATED    "201601180000Z"
    ORGANIZATION    "Cisco Systems Inc."
    CONTACT-INFO
        "Cisco Systems
        Customer Service
        
        Postal: 170 W Tasman Drive
        San Jose, CA  95134
        USA
        
        Tel: ****** 553 -NETS
        
        E-mail: <EMAIL>, <EMAIL>"
    DESCRIPTION
        "MIB representation of the Cisco Unified Computing System
        COMPUTE management information model package"
    ::= { ciscoUnifiedComputingMIBObjects 9 }

cucsComputeAutoconfigPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeAutoconfigPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy managed object table"
    ::= { cucsComputeObjects 1 }

cucsComputeAutoconfigPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeAutoconfigPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeAutoconfigPolicyTable table."
    INDEX { cucsComputeAutoconfigPolicyInstanceId }
    ::= { cucsComputeAutoconfigPolicyTable 1 }

CucsComputeAutoconfigPolicyEntry ::= SEQUENCE {
    cucsComputeAutoconfigPolicyInstanceId                            CucsManagedObjectId,
    cucsComputeAutoconfigPolicyDn                                    CucsManagedObjectDn,
    cucsComputeAutoconfigPolicyRn                                    SnmpAdminString,
    cucsComputeAutoconfigPolicyDescr                                 SnmpAdminString,
    cucsComputeAutoconfigPolicyDstDn                                 SnmpAdminString,
    cucsComputeAutoconfigPolicyIntId                                 SnmpAdminString,
    cucsComputeAutoconfigPolicyName                                  SnmpAdminString,
    cucsComputeAutoconfigPolicyQualifier                             SnmpAdminString,
    cucsComputeAutoconfigPolicySrcTemplName                          SnmpAdminString,
    cucsComputeAutoconfigPolicyPolicyLevel                           Gauge32,
    cucsComputeAutoconfigPolicyPolicyOwner                           CucsPolicyPolicyOwner,
    cucsComputeAutoconfigPolicyOperQualifier                         SnmpAdminString
}

cucsComputeAutoconfigPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeAutoconfigPolicyEntry 1 }

cucsComputeAutoconfigPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:dn managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 2 }

cucsComputeAutoconfigPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:rn managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 3 }

cucsComputeAutoconfigPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:descr managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 4 }

cucsComputeAutoconfigPolicyDstDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:dstDn managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 5 }

cucsComputeAutoconfigPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:intId managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 6 }

cucsComputeAutoconfigPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:name managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 7 }

cucsComputeAutoconfigPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:qualifier managed
        object property"
    ::= { cucsComputeAutoconfigPolicyEntry 8 }

cucsComputeAutoconfigPolicySrcTemplName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:srcTemplName
        managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 9 }

cucsComputeAutoconfigPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:policyLevel
        managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 10 }

cucsComputeAutoconfigPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:policyOwner
        managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 11 }

cucsComputeAutoconfigPolicyOperQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:AutoconfigPolicy:operQualifier
        managed object property"
    ::= { cucsComputeAutoconfigPolicyEntry 12 }

cucsComputeBladeTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBladeEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Blade managed object table"
    ::= { cucsComputeObjects 2 }

cucsComputeBladeEntry OBJECT-TYPE
    SYNTAX           CucsComputeBladeEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBladeTable table."
    INDEX { cucsComputeBladeInstanceId }
    ::= { cucsComputeBladeTable 1 }

CucsComputeBladeEntry ::= SEQUENCE {
    cucsComputeBladeInstanceId                                       CucsManagedObjectId,
    cucsComputeBladeDn                                               CucsManagedObjectDn,
    cucsComputeBladeRn                                               SnmpAdminString,
    cucsComputeBladeAdminPower                                       CucsComputeAdminPowerState,
    cucsComputeBladeAdminState                                       CucsComputeAdminState,
    cucsComputeBladeAssignedToDn                                     SnmpAdminString,
    cucsComputeBladeAssociation                                      CucsComputeAssociation,
    cucsComputeBladeAvailability                                     CucsComputeAvailability,
    cucsComputeBladeAvailableMemory                                  Gauge32,
    cucsComputeBladeChassisId                                        Gauge32,
    cucsComputeBladeCheckPoint                                       CucsComputeCheckPoint,
    cucsComputeBladeConnPath                                         CucsEquipmentConnectionStatus,
    cucsComputeBladeConnStatus                                       CucsEquipmentConnectionStatus,
    cucsComputeBladeDescr                                            SnmpAdminString,
    cucsComputeBladeDiscovery                                        CucsComputeDiscovery,
    cucsComputeBladeFltAggr                                          Unsigned64,
    cucsComputeBladeFsmDescr                                         SnmpAdminString,
    cucsComputeBladeFsmFlags                                         SnmpAdminString,
    cucsComputeBladeFsmPrev                                          SnmpAdminString,
    cucsComputeBladeFsmProgr                                         Gauge32,
    cucsComputeBladeFsmRmtInvErrCode                                 Gauge32,
    cucsComputeBladeFsmRmtInvErrDescr                                SnmpAdminString,
    cucsComputeBladeFsmRmtInvRslt                                    CucsConditionRemoteInvRslt,
    cucsComputeBladeFsmStageDescr                                    SnmpAdminString,
    cucsComputeBladeFsmStamp                                         DateAndTime,
    cucsComputeBladeFsmStatus                                        SnmpAdminString,
    cucsComputeBladeFsmTry                                           Gauge32,
    cucsComputeBladeIntId                                            SnmpAdminString,
    cucsComputeBladeLc                                               CucsComputeAdminTrigger,
    cucsComputeBladeLcTs                                             DateAndTime,
    cucsComputeBladeManagingInst                                     CucsNetworkSwitchId,
    cucsComputeBladeModel                                            SnmpAdminString,
    cucsComputeBladeName                                             SnmpAdminString,
    cucsComputeBladeNumOfAdaptors                                    Gauge32,
    cucsComputeBladeNumOfCores                                       Gauge32,
    cucsComputeBladeNumOfCpus                                        Gauge32,
    cucsComputeBladeNumOfEthHostIfs                                  Gauge32,
    cucsComputeBladeNumOfFcHostIfs                                   Gauge32,
    cucsComputeBladeNumOfThreads                                     Gauge32,
    cucsComputeBladeOperPower                                        CucsEquipmentPowerState,
    cucsComputeBladeOperQualifier                                    CucsComputeIssues,
    cucsComputeBladeOperState                                        CucsLsOperState,
    cucsComputeBladeOperability                                      CucsEquipmentOperability,
    cucsComputeBladeOriginalUuid                                     SnmpAdminString,
    cucsComputeBladePresence                                         CucsEquipmentSlotStatus,
    cucsComputeBladeRevision                                         SnmpAdminString,
    cucsComputeBladeSerial                                           SnmpAdminString,
    cucsComputeBladeServerId                                         SnmpAdminString,
    cucsComputeBladeSlotId                                           CucsComputeBladeSlotId,
    cucsComputeBladeTotalMemory                                      Gauge32,
    cucsComputeBladeUsrLbl                                           SnmpAdminString,
    cucsComputeBladeUuid                                             SnmpAdminString,
    cucsComputeBladeVendor                                           SnmpAdminString,
    cucsComputeBladeNumOfCoresEnabled                                Gauge32,
    cucsComputeBladeLowVoltageMemory                                 CucsComputePhysicalLowVoltageMemory,
    cucsComputeBladeMemorySpeed                                      Gauge32,
    cucsComputeBladeMfgTime                                          DateAndTime,
    cucsComputeBladePartNumber                                       SnmpAdminString,
    cucsComputeBladeVid                                              SnmpAdminString,
    cucsComputeBladePolicyLevel                                      Gauge32,
    cucsComputeBladePolicyOwner                                      CucsPolicyPolicyOwner,
    cucsComputeBladeLocalId                                          SnmpAdminString,
    cucsComputeBladeScaledMode                                       CucsComputeMode,
    cucsComputeBladeUpgradeScenario                                  CucsComputeUpgradeStatus,
    cucsComputeBladeOperPwrTransSrc                                  CucsComputePowerTransitionSrc,
    cucsComputeBladeDiscoveryStatus                                  CucsEquipmentConnectionStatus,
    cucsComputeBladeNumOf40GAdaptorsWithOldFw                        Gauge32,
    cucsComputeBladeNumOf40GAdaptorsWithUnknownFw                    Gauge32
}

cucsComputeBladeInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBladeEntry 1 }

cucsComputeBladeDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:dn managed object property"
    ::= { cucsComputeBladeEntry 2 }

cucsComputeBladeRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:rn managed object property"
    ::= { cucsComputeBladeEntry 3 }

cucsComputeBladeAdminPower OBJECT-TYPE
    SYNTAX       CucsComputeAdminPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:adminPower managed object property"
    ::= { cucsComputeBladeEntry 4 }

cucsComputeBladeAdminState OBJECT-TYPE
    SYNTAX       CucsComputeAdminState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:adminState managed object property"
    ::= { cucsComputeBladeEntry 5 }

cucsComputeBladeAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:assignedToDn managed object property"
    ::= { cucsComputeBladeEntry 6 }

cucsComputeBladeAssociation OBJECT-TYPE
    SYNTAX       CucsComputeAssociation
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:association managed object property"
    ::= { cucsComputeBladeEntry 7 }

cucsComputeBladeAvailability OBJECT-TYPE
    SYNTAX       CucsComputeAvailability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:availability managed object property"
    ::= { cucsComputeBladeEntry 8 }

cucsComputeBladeAvailableMemory OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:availableMemory managed object property"
    ::= { cucsComputeBladeEntry 9 }

cucsComputeBladeChassisId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:chassisId managed object property"
    ::= { cucsComputeBladeEntry 10 }

cucsComputeBladeCheckPoint OBJECT-TYPE
    SYNTAX       CucsComputeCheckPoint
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:checkPoint managed object property"
    ::= { cucsComputeBladeEntry 11 }

cucsComputeBladeConnPath OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:connPath managed object property"
    ::= { cucsComputeBladeEntry 12 }

cucsComputeBladeConnStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:connStatus managed object property"
    ::= { cucsComputeBladeEntry 13 }

cucsComputeBladeDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:descr managed object property"
    ::= { cucsComputeBladeEntry 14 }

cucsComputeBladeDiscovery OBJECT-TYPE
    SYNTAX       CucsComputeDiscovery
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:discovery managed object property"
    ::= { cucsComputeBladeEntry 15 }

cucsComputeBladeFltAggr OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fltAggr managed object property"
    ::= { cucsComputeBladeEntry 16 }

cucsComputeBladeFsmDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmDescr managed object property"
    ::= { cucsComputeBladeEntry 17 }

cucsComputeBladeFsmFlags OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmFlags managed object property"
    ::= { cucsComputeBladeEntry 18 }

cucsComputeBladeFsmPrev OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmPrev managed object property"
    ::= { cucsComputeBladeEntry 19 }

cucsComputeBladeFsmProgr OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmProgr managed object property"
    ::= { cucsComputeBladeEntry 20 }

cucsComputeBladeFsmRmtInvErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmRmtInvErrCode managed object property"
    ::= { cucsComputeBladeEntry 21 }

cucsComputeBladeFsmRmtInvErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmRmtInvErrDescr managed
        object property"
    ::= { cucsComputeBladeEntry 22 }

cucsComputeBladeFsmRmtInvRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmRmtInvRslt managed object property"
    ::= { cucsComputeBladeEntry 23 }

cucsComputeBladeFsmStageDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmStageDescr managed object property"
    ::= { cucsComputeBladeEntry 24 }

cucsComputeBladeFsmStamp OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmStamp managed object property"
    ::= { cucsComputeBladeEntry 25 }

cucsComputeBladeFsmStatus OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmStatus managed object property"
    ::= { cucsComputeBladeEntry 26 }

cucsComputeBladeFsmTry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:fsmTry managed object property"
    ::= { cucsComputeBladeEntry 27 }

cucsComputeBladeIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:intId managed object property"
    ::= { cucsComputeBladeEntry 28 }

cucsComputeBladeLc OBJECT-TYPE
    SYNTAX       CucsComputeAdminTrigger
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:lc managed object property"
    ::= { cucsComputeBladeEntry 29 }

cucsComputeBladeLcTs OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:lcTs managed object property"
    ::= { cucsComputeBladeEntry 30 }

cucsComputeBladeManagingInst OBJECT-TYPE
    SYNTAX       CucsNetworkSwitchId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:managingInst managed object property"
    ::= { cucsComputeBladeEntry 31 }

cucsComputeBladeModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:model managed object property"
    ::= { cucsComputeBladeEntry 32 }

cucsComputeBladeName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:name managed object property"
    ::= { cucsComputeBladeEntry 33 }

cucsComputeBladeNumOfAdaptors OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOfAdaptors managed object property"
    ::= { cucsComputeBladeEntry 34 }

cucsComputeBladeNumOfCores OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOfCores managed object property"
    ::= { cucsComputeBladeEntry 35 }

cucsComputeBladeNumOfCpus OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOfCpus managed object property"
    ::= { cucsComputeBladeEntry 36 }

cucsComputeBladeNumOfEthHostIfs OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOfEthHostIfs managed object property"
    ::= { cucsComputeBladeEntry 37 }

cucsComputeBladeNumOfFcHostIfs OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOfFcHostIfs managed object property"
    ::= { cucsComputeBladeEntry 38 }

cucsComputeBladeNumOfThreads OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOfThreads managed object property"
    ::= { cucsComputeBladeEntry 39 }

cucsComputeBladeOperPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:operPower managed object property"
    ::= { cucsComputeBladeEntry 40 }

cucsComputeBladeOperQualifier OBJECT-TYPE
    SYNTAX       CucsComputeIssues
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:operQualifier managed object property"
    ::= { cucsComputeBladeEntry 41 }

cucsComputeBladeOperState OBJECT-TYPE
    SYNTAX       CucsLsOperState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:operState managed object property"
    ::= { cucsComputeBladeEntry 42 }

cucsComputeBladeOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:operability managed object property"
    ::= { cucsComputeBladeEntry 43 }

cucsComputeBladeOriginalUuid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:originalUuid managed object property"
    ::= { cucsComputeBladeEntry 44 }

cucsComputeBladePresence OBJECT-TYPE
    SYNTAX       CucsEquipmentSlotStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:presence managed object property"
    ::= { cucsComputeBladeEntry 45 }

cucsComputeBladeRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:revision managed object property"
    ::= { cucsComputeBladeEntry 46 }

cucsComputeBladeSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:serial managed object property"
    ::= { cucsComputeBladeEntry 47 }

cucsComputeBladeServerId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:serverId managed object property"
    ::= { cucsComputeBladeEntry 48 }

cucsComputeBladeSlotId OBJECT-TYPE
    SYNTAX       CucsComputeBladeSlotId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:slotId managed object property"
    ::= { cucsComputeBladeEntry 49 }

cucsComputeBladeTotalMemory OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:totalMemory managed object property"
    ::= { cucsComputeBladeEntry 50 }

cucsComputeBladeUsrLbl OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:usrLbl managed object property"
    ::= { cucsComputeBladeEntry 51 }

cucsComputeBladeUuid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:uuid managed object property"
    ::= { cucsComputeBladeEntry 52 }

cucsComputeBladeVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:vendor managed object property"
    ::= { cucsComputeBladeEntry 53 }

cucsComputeBladeNumOfCoresEnabled OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOfCoresEnabled managed
        object property"
    ::= { cucsComputeBladeEntry 54 }

cucsComputeBladeLowVoltageMemory OBJECT-TYPE
    SYNTAX       CucsComputePhysicalLowVoltageMemory
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:lowVoltageMemory managed object property"
    ::= { cucsComputeBladeEntry 55 }

cucsComputeBladeMemorySpeed OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:memorySpeed managed object property"
    ::= { cucsComputeBladeEntry 56 }

cucsComputeBladeMfgTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:mfgTime managed object property"
    ::= { cucsComputeBladeEntry 57 }

cucsComputeBladePartNumber OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:partNumber managed object property"
    ::= { cucsComputeBladeEntry 58 }

cucsComputeBladeVid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:vid managed object property"
    ::= { cucsComputeBladeEntry 59 }

cucsComputeBladePolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:policyLevel managed object property"
    ::= { cucsComputeBladeEntry 60 }

cucsComputeBladePolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:policyOwner managed object property"
    ::= { cucsComputeBladeEntry 61 }

cucsComputeBladeLocalId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:localId managed object property"
    ::= { cucsComputeBladeEntry 62 }

cucsComputeBladeScaledMode OBJECT-TYPE
    SYNTAX       CucsComputeMode
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:scaledMode managed object property"
    ::= { cucsComputeBladeEntry 63 }

cucsComputeBladeUpgradeScenario OBJECT-TYPE
    SYNTAX       CucsComputeUpgradeStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:upgradeScenario managed object property"
    ::= { cucsComputeBladeEntry 64 }

cucsComputeBladeOperPwrTransSrc OBJECT-TYPE
    SYNTAX       CucsComputePowerTransitionSrc
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:operPwrTransSrc managed object property"
    ::= { cucsComputeBladeEntry 65 }

cucsComputeBladeDiscoveryStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:discoveryStatus managed object property"
    ::= { cucsComputeBladeEntry 66 }

cucsComputeBladeNumOf40GAdaptorsWithOldFw OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOf40GAdaptorsWithOldFw
        managed object property"
    ::= { cucsComputeBladeEntry 68 }

cucsComputeBladeNumOf40GAdaptorsWithUnknownFw OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Blade:numOf40GAdaptorsWithUnknownFw
        managed object property"
    ::= { cucsComputeBladeEntry 69 }

cucsComputeBladeDiscPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBladeDiscPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy managed object table"
    ::= { cucsComputeObjects 3 }

cucsComputeBladeDiscPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeBladeDiscPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBladeDiscPolicyTable table."
    INDEX { cucsComputeBladeDiscPolicyInstanceId }
    ::= { cucsComputeBladeDiscPolicyTable 1 }

CucsComputeBladeDiscPolicyEntry ::= SEQUENCE {
    cucsComputeBladeDiscPolicyInstanceId                             CucsManagedObjectId,
    cucsComputeBladeDiscPolicyDn                                     CucsManagedObjectDn,
    cucsComputeBladeDiscPolicyRn                                     SnmpAdminString,
    cucsComputeBladeDiscPolicyAction                                 SnmpAdminString,
    cucsComputeBladeDiscPolicyDescr                                  SnmpAdminString,
    cucsComputeBladeDiscPolicyIntId                                  SnmpAdminString,
    cucsComputeBladeDiscPolicyName                                   SnmpAdminString,
    cucsComputeBladeDiscPolicyQualifier                              SnmpAdminString,
    cucsComputeBladeDiscPolicyScrubPolicyName                        SnmpAdminString,
    cucsComputeBladeDiscPolicyPolicyLevel                            Gauge32,
    cucsComputeBladeDiscPolicyPolicyOwner                            CucsPolicyPolicyOwner
}

cucsComputeBladeDiscPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBladeDiscPolicyEntry 1 }

cucsComputeBladeDiscPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:dn managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 2 }

cucsComputeBladeDiscPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:rn managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 3 }

cucsComputeBladeDiscPolicyAction OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:action managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 4 }

cucsComputeBladeDiscPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:descr managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 5 }

cucsComputeBladeDiscPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:intId managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 6 }

cucsComputeBladeDiscPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:name managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 7 }

cucsComputeBladeDiscPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:qualifier managed
        object property"
    ::= { cucsComputeBladeDiscPolicyEntry 8 }

cucsComputeBladeDiscPolicyScrubPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:scrubPolicyName
        managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 9 }

cucsComputeBladeDiscPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:policyLevel
        managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 10 }

cucsComputeBladeDiscPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeDiscPolicy:policyOwner
        managed object property"
    ::= { cucsComputeBladeDiscPolicyEntry 11 }

cucsComputeBladeEpTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBladeEpEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BladeEp managed object table"
    ::= { cucsComputeObjects 66 }

cucsComputeBladeEpEntry OBJECT-TYPE
    SYNTAX           CucsComputeBladeEpEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBladeEpTable table."
    INDEX { cucsComputeBladeEpInstanceId }
    ::= { cucsComputeBladeEpTable 1 }

CucsComputeBladeEpEntry ::= SEQUENCE {
    cucsComputeBladeEpInstanceId                                     CucsManagedObjectId,
    cucsComputeBladeEpDn                                             CucsManagedObjectDn,
    cucsComputeBladeEpRn                                             SnmpAdminString,
    cucsComputeBladeEpAdminState                                     CucsComputeAdminState,
    cucsComputeBladeEpChassisId                                      Gauge32,
    cucsComputeBladeEpEpDn                                           SnmpAdminString,
    cucsComputeBladeEpId                                             CucsComputeBladeEpId,
    cucsComputeBladeEpOperQualifierReason                            SnmpAdminString,
    cucsComputeBladeEpOperState                                      CucsLsOperState,
    cucsComputeBladeEpPeerPresence                                   CucsEquipmentSlotStatus,
    cucsComputeBladeEpPresence                                       CucsEquipmentSlotStatus,
    cucsComputeBladeEpSlotId                                         CucsComputeBladeEpSlotId
}

cucsComputeBladeEpInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBladeEpEntry 1 }

cucsComputeBladeEpDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:dn managed object property"
    ::= { cucsComputeBladeEpEntry 2 }

cucsComputeBladeEpRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:rn managed object property"
    ::= { cucsComputeBladeEpEntry 3 }

cucsComputeBladeEpAdminState OBJECT-TYPE
    SYNTAX       CucsComputeAdminState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:adminState managed object property"
    ::= { cucsComputeBladeEpEntry 4 }

cucsComputeBladeEpChassisId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:chassisId managed object property"
    ::= { cucsComputeBladeEpEntry 5 }

cucsComputeBladeEpEpDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:epDn managed object property"
    ::= { cucsComputeBladeEpEntry 6 }

cucsComputeBladeEpId OBJECT-TYPE
    SYNTAX       CucsComputeBladeEpId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:id managed object property"
    ::= { cucsComputeBladeEpEntry 7 }

cucsComputeBladeEpOperQualifierReason OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:operQualifierReason
        managed object property"
    ::= { cucsComputeBladeEpEntry 8 }

cucsComputeBladeEpOperState OBJECT-TYPE
    SYNTAX       CucsLsOperState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:operState managed object property"
    ::= { cucsComputeBladeEpEntry 9 }

cucsComputeBladeEpPeerPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentSlotStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:peerPresence managed object property"
    ::= { cucsComputeBladeEpEntry 10 }

cucsComputeBladeEpPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentSlotStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:presence managed object property"
    ::= { cucsComputeBladeEpEntry 11 }

cucsComputeBladeEpSlotId OBJECT-TYPE
    SYNTAX       CucsComputeBladeEpSlotId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeEp:slotId managed object property"
    ::= { cucsComputeBladeEpEntry 12 }

cucsComputeBladeFsmTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBladeFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm managed object table"
    ::= { cucsComputeObjects 48 }

cucsComputeBladeFsmEntry OBJECT-TYPE
    SYNTAX           CucsComputeBladeFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBladeFsmTable table."
    INDEX { cucsComputeBladeFsmInstanceId }
    ::= { cucsComputeBladeFsmTable 1 }

CucsComputeBladeFsmEntry ::= SEQUENCE {
    cucsComputeBladeFsmInstanceId                                    CucsManagedObjectId,
    cucsComputeBladeFsmDn                                            CucsManagedObjectDn,
    cucsComputeBladeFsmRn                                            SnmpAdminString,
    cucsComputeBladeFsmCompletionTime                                DateAndTime,
    cucsComputeBladeFsmCurrentFsm                                    CucsComputeBladeFsmCurrentFsm,
    cucsComputeBladeFsmDescrData                                     SnmpAdminString,
    cucsComputeBladeFsmFsmStatus                                     CucsFsmFsmStageStatus,
    cucsComputeBladeFsmProgress                                      Gauge32,
    cucsComputeBladeFsmRmtErrCode                                    Gauge32,
    cucsComputeBladeFsmRmtErrDescr                                   SnmpAdminString,
    cucsComputeBladeFsmRmtRslt                                       CucsConditionRemoteInvRslt
}

cucsComputeBladeFsmInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBladeFsmEntry 1 }

cucsComputeBladeFsmDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:dn managed object property"
    ::= { cucsComputeBladeFsmEntry 2 }

cucsComputeBladeFsmRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:rn managed object property"
    ::= { cucsComputeBladeFsmEntry 3 }

cucsComputeBladeFsmCompletionTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:completionTime managed
        object property"
    ::= { cucsComputeBladeFsmEntry 4 }

cucsComputeBladeFsmCurrentFsm OBJECT-TYPE
    SYNTAX       CucsComputeBladeFsmCurrentFsm
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:currentFsm managed object property"
    ::= { cucsComputeBladeFsmEntry 5 }

cucsComputeBladeFsmDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:descr managed object property"
    ::= { cucsComputeBladeFsmEntry 6 }

cucsComputeBladeFsmFsmStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:fsmStatus managed object property"
    ::= { cucsComputeBladeFsmEntry 7 }

cucsComputeBladeFsmProgress OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:progress managed object property"
    ::= { cucsComputeBladeFsmEntry 8 }

cucsComputeBladeFsmRmtErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:rmtErrCode managed object property"
    ::= { cucsComputeBladeFsmEntry 9 }

cucsComputeBladeFsmRmtErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:rmtErrDescr managed object property"
    ::= { cucsComputeBladeFsmEntry 10 }

cucsComputeBladeFsmRmtRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsm:rmtRslt managed object property"
    ::= { cucsComputeBladeFsmEntry 11 }

cucsComputeBladeFsmStageTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBladeFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage managed object table"
    ::= { cucsComputeObjects 49 }

cucsComputeBladeFsmStageEntry OBJECT-TYPE
    SYNTAX           CucsComputeBladeFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBladeFsmStageTable table."
    INDEX { cucsComputeBladeFsmStageInstanceId }
    ::= { cucsComputeBladeFsmStageTable 1 }

CucsComputeBladeFsmStageEntry ::= SEQUENCE {
    cucsComputeBladeFsmStageInstanceId                               CucsManagedObjectId,
    cucsComputeBladeFsmStageDn                                       CucsManagedObjectDn,
    cucsComputeBladeFsmStageRn                                       SnmpAdminString,
    cucsComputeBladeFsmStageDescrData                                SnmpAdminString,
    cucsComputeBladeFsmStageLastUpdateTime                           DateAndTime,
    cucsComputeBladeFsmStageName                                     CucsComputeBladeFsmStageName,
    cucsComputeBladeFsmStageOrder                                    Gauge32,
    cucsComputeBladeFsmStageRetry                                    Gauge32,
    cucsComputeBladeFsmStageStageStatus                              CucsFsmFsmStageStatus
}

cucsComputeBladeFsmStageInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBladeFsmStageEntry 1 }

cucsComputeBladeFsmStageDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:dn managed object property"
    ::= { cucsComputeBladeFsmStageEntry 2 }

cucsComputeBladeFsmStageRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:rn managed object property"
    ::= { cucsComputeBladeFsmStageEntry 3 }

cucsComputeBladeFsmStageDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:descr managed object property"
    ::= { cucsComputeBladeFsmStageEntry 4 }

cucsComputeBladeFsmStageLastUpdateTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:lastUpdateTime
        managed object property"
    ::= { cucsComputeBladeFsmStageEntry 5 }

cucsComputeBladeFsmStageName OBJECT-TYPE
    SYNTAX       CucsComputeBladeFsmStageName
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:name managed object property"
    ::= { cucsComputeBladeFsmStageEntry 6 }

cucsComputeBladeFsmStageOrder OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:order managed object property"
    ::= { cucsComputeBladeFsmStageEntry 7 }

cucsComputeBladeFsmStageRetry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:retry managed object property"
    ::= { cucsComputeBladeFsmStageEntry 8 }

cucsComputeBladeFsmStageStageStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmStage:stageStatus managed
        object property"
    ::= { cucsComputeBladeFsmStageEntry 9 }

cucsComputeBladeFsmTaskTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBladeFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmTask managed object table"
    ::= { cucsComputeObjects 4 }

cucsComputeBladeFsmTaskEntry OBJECT-TYPE
    SYNTAX           CucsComputeBladeFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBladeFsmTaskTable table."
    INDEX { cucsComputeBladeFsmTaskInstanceId }
    ::= { cucsComputeBladeFsmTaskTable 1 }

CucsComputeBladeFsmTaskEntry ::= SEQUENCE {
    cucsComputeBladeFsmTaskInstanceId                                CucsManagedObjectId,
    cucsComputeBladeFsmTaskDn                                        CucsManagedObjectDn,
    cucsComputeBladeFsmTaskRn                                        SnmpAdminString,
    cucsComputeBladeFsmTaskCompletion                                CucsFsmCompletion,
    cucsComputeBladeFsmTaskFlags                                     CucsComputeBladeFsmTaskFlags,
    cucsComputeBladeFsmTaskItem                                      CucsComputeBladeFsmTaskItem,
    cucsComputeBladeFsmTaskSeqId                                     Gauge32
}

cucsComputeBladeFsmTaskInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBladeFsmTaskEntry 1 }

cucsComputeBladeFsmTaskDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmTask:dn managed object property"
    ::= { cucsComputeBladeFsmTaskEntry 2 }

cucsComputeBladeFsmTaskRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmTask:rn managed object property"
    ::= { cucsComputeBladeFsmTaskEntry 3 }

cucsComputeBladeFsmTaskCompletion OBJECT-TYPE
    SYNTAX       CucsFsmCompletion
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmTask:completion managed
        object property"
    ::= { cucsComputeBladeFsmTaskEntry 4 }

cucsComputeBladeFsmTaskFlags OBJECT-TYPE
    SYNTAX       CucsComputeBladeFsmTaskFlags
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmTask:flags managed object property"
    ::= { cucsComputeBladeFsmTaskEntry 5 }

cucsComputeBladeFsmTaskItem OBJECT-TYPE
    SYNTAX       CucsComputeBladeFsmTaskItem
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmTask:item managed object property"
    ::= { cucsComputeBladeFsmTaskEntry 6 }

cucsComputeBladeFsmTaskSeqId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeFsmTask:seqId managed object property"
    ::= { cucsComputeBladeFsmTaskEntry 7 }

cucsComputeBladeInheritPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBladeInheritPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy managed object table"
    ::= { cucsComputeObjects 5 }

cucsComputeBladeInheritPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeBladeInheritPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBladeInheritPolicyTable table."
    INDEX { cucsComputeBladeInheritPolicyInstanceId }
    ::= { cucsComputeBladeInheritPolicyTable 1 }

CucsComputeBladeInheritPolicyEntry ::= SEQUENCE {
    cucsComputeBladeInheritPolicyInstanceId                          CucsManagedObjectId,
    cucsComputeBladeInheritPolicyDn                                  CucsManagedObjectDn,
    cucsComputeBladeInheritPolicyRn                                  SnmpAdminString,
    cucsComputeBladeInheritPolicyDescr                               SnmpAdminString,
    cucsComputeBladeInheritPolicyDstDn                               SnmpAdminString,
    cucsComputeBladeInheritPolicyIntId                               SnmpAdminString,
    cucsComputeBladeInheritPolicyName                                SnmpAdminString,
    cucsComputeBladeInheritPolicyQualifier                           SnmpAdminString,
    cucsComputeBladeInheritPolicyPolicyLevel                         Gauge32,
    cucsComputeBladeInheritPolicyPolicyOwner                         CucsPolicyPolicyOwner,
    cucsComputeBladeInheritPolicyOperQualifier                       SnmpAdminString
}

cucsComputeBladeInheritPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBladeInheritPolicyEntry 1 }

cucsComputeBladeInheritPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:dn managed object property"
    ::= { cucsComputeBladeInheritPolicyEntry 2 }

cucsComputeBladeInheritPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:rn managed object property"
    ::= { cucsComputeBladeInheritPolicyEntry 3 }

cucsComputeBladeInheritPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:descr managed
        object property"
    ::= { cucsComputeBladeInheritPolicyEntry 4 }

cucsComputeBladeInheritPolicyDstDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:dstDn managed
        object property"
    ::= { cucsComputeBladeInheritPolicyEntry 5 }

cucsComputeBladeInheritPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:intId managed
        object property"
    ::= { cucsComputeBladeInheritPolicyEntry 6 }

cucsComputeBladeInheritPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:name managed
        object property"
    ::= { cucsComputeBladeInheritPolicyEntry 7 }

cucsComputeBladeInheritPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:qualifier
        managed object property"
    ::= { cucsComputeBladeInheritPolicyEntry 8 }

cucsComputeBladeInheritPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:policyLevel
        managed object property"
    ::= { cucsComputeBladeInheritPolicyEntry 9 }

cucsComputeBladeInheritPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:policyOwner
        managed object property"
    ::= { cucsComputeBladeInheritPolicyEntry 10 }

cucsComputeBladeInheritPolicyOperQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BladeInheritPolicy:operQualifier
        managed object property"
    ::= { cucsComputeBladeInheritPolicyEntry 11 }

cucsComputeBoardTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBoardEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Board managed object table"
    ::= { cucsComputeObjects 6 }

cucsComputeBoardEntry OBJECT-TYPE
    SYNTAX           CucsComputeBoardEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBoardTable table."
    INDEX { cucsComputeBoardInstanceId }
    ::= { cucsComputeBoardTable 1 }

CucsComputeBoardEntry ::= SEQUENCE {
    cucsComputeBoardInstanceId                                       CucsManagedObjectId,
    cucsComputeBoardDn                                               CucsManagedObjectDn,
    cucsComputeBoardRn                                               SnmpAdminString,
    cucsComputeBoardCmosVoltage                                      CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardId                                               Gauge32,
    cucsComputeBoardModel                                            SnmpAdminString,
    cucsComputeBoardOperPower                                        CucsEquipmentPowerState,
    cucsComputeBoardOperState                                        CucsEquipmentOperability,
    cucsComputeBoardOperability                                      CucsEquipmentOperability,
    cucsComputeBoardPerf                                             CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardPower                                            CucsComputeABoardPower,
    cucsComputeBoardPresence                                         CucsEquipmentPresence,
    cucsComputeBoardRevision                                         SnmpAdminString,
    cucsComputeBoardSerial                                           SnmpAdminString,
    cucsComputeBoardThermal                                          CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardVendor                                           SnmpAdminString,
    cucsComputeBoardVoltage                                          CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardOperQualifierReason                              SnmpAdminString,
    cucsComputeBoardPowerUsage                                       CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardFaultQualifier                                   SnmpAdminString,
    cucsComputeBoardLocationDn                                       SnmpAdminString,
    cucsComputeBoardCpuTypeDescription                               SnmpAdminString
}

cucsComputeBoardInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBoardEntry 1 }

cucsComputeBoardDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:dn managed object property"
    ::= { cucsComputeBoardEntry 2 }

cucsComputeBoardRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:rn managed object property"
    ::= { cucsComputeBoardEntry 3 }

cucsComputeBoardCmosVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:cmosVoltage managed object property"
    ::= { cucsComputeBoardEntry 4 }

cucsComputeBoardId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:id managed object property"
    ::= { cucsComputeBoardEntry 5 }

cucsComputeBoardModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:model managed object property"
    ::= { cucsComputeBoardEntry 6 }

cucsComputeBoardOperPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:operPower managed object property"
    ::= { cucsComputeBoardEntry 7 }

cucsComputeBoardOperState OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:operState managed object property"
    ::= { cucsComputeBoardEntry 8 }

cucsComputeBoardOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:operability managed object property"
    ::= { cucsComputeBoardEntry 9 }

cucsComputeBoardPerf OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:perf managed object property"
    ::= { cucsComputeBoardEntry 10 }

cucsComputeBoardPower OBJECT-TYPE
    SYNTAX       CucsComputeABoardPower
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:power managed object property"
    ::= { cucsComputeBoardEntry 11 }

cucsComputeBoardPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentPresence
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:presence managed object property"
    ::= { cucsComputeBoardEntry 12 }

cucsComputeBoardRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:revision managed object property"
    ::= { cucsComputeBoardEntry 13 }

cucsComputeBoardSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:serial managed object property"
    ::= { cucsComputeBoardEntry 14 }

cucsComputeBoardThermal OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:thermal managed object property"
    ::= { cucsComputeBoardEntry 15 }

cucsComputeBoardVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:vendor managed object property"
    ::= { cucsComputeBoardEntry 16 }

cucsComputeBoardVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:voltage managed object property"
    ::= { cucsComputeBoardEntry 17 }

cucsComputeBoardOperQualifierReason OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:operQualifierReason managed
        object property"
    ::= { cucsComputeBoardEntry 18 }

cucsComputeBoardPowerUsage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:powerUsage managed object property"
    ::= { cucsComputeBoardEntry 19 }

cucsComputeBoardFaultQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:faultQualifier managed object property"
    ::= { cucsComputeBoardEntry 20 }

cucsComputeBoardLocationDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:locationDn managed object property"
    ::= { cucsComputeBoardEntry 21 }

cucsComputeBoardCpuTypeDescription OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Board:cpuTypeDescription managed
        object property"
    ::= { cucsComputeBoardEntry 23 }

cucsComputeBoardConnectorTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBoardConnectorEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BoardConnector managed object table"
    ::= { cucsComputeObjects 63 }

cucsComputeBoardConnectorEntry OBJECT-TYPE
    SYNTAX           CucsComputeBoardConnectorEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBoardConnectorTable table."
    INDEX { cucsComputeBoardConnectorInstanceId }
    ::= { cucsComputeBoardConnectorTable 1 }

CucsComputeBoardConnectorEntry ::= SEQUENCE {
    cucsComputeBoardConnectorInstanceId                              CucsManagedObjectId,
    cucsComputeBoardConnectorDn                                      CucsManagedObjectDn,
    cucsComputeBoardConnectorRn                                      SnmpAdminString,
    cucsComputeBoardConnectorBoardConnectorType                      CucsEquipmentBoardConnectorType,
    cucsComputeBoardConnectorMasterSlotId                            Gauge32,
    cucsComputeBoardConnectorSlaveSlotId                             Gauge32
}

cucsComputeBoardConnectorInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBoardConnectorEntry 1 }

cucsComputeBoardConnectorDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardConnector:dn managed object property"
    ::= { cucsComputeBoardConnectorEntry 2 }

cucsComputeBoardConnectorRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardConnector:rn managed object property"
    ::= { cucsComputeBoardConnectorEntry 3 }

cucsComputeBoardConnectorBoardConnectorType OBJECT-TYPE
    SYNTAX       CucsEquipmentBoardConnectorType
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardConnector:boardConnectorType
        managed object property"
    ::= { cucsComputeBoardConnectorEntry 4 }

cucsComputeBoardConnectorMasterSlotId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardConnector:masterSlotId
        managed object property"
    ::= { cucsComputeBoardConnectorEntry 5 }

cucsComputeBoardConnectorSlaveSlotId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardConnector:slaveSlotId managed
        object property"
    ::= { cucsComputeBoardConnectorEntry 6 }

cucsComputeBoardControllerTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeBoardControllerEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:BoardController managed object table"
    ::= { cucsComputeObjects 7 }

cucsComputeBoardControllerEntry OBJECT-TYPE
    SYNTAX           CucsComputeBoardControllerEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeBoardControllerTable table."
    INDEX { cucsComputeBoardControllerInstanceId }
    ::= { cucsComputeBoardControllerTable 1 }

CucsComputeBoardControllerEntry ::= SEQUENCE {
    cucsComputeBoardControllerInstanceId                             CucsManagedObjectId,
    cucsComputeBoardControllerDn                                     CucsManagedObjectDn,
    cucsComputeBoardControllerRn                                     SnmpAdminString,
    cucsComputeBoardControllerId                                     Gauge32,
    cucsComputeBoardControllerModel                                  SnmpAdminString,
    cucsComputeBoardControllerOperState                              CucsEquipmentOperability,
    cucsComputeBoardControllerOperability                            CucsEquipmentOperability,
    cucsComputeBoardControllerPerf                                   CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardControllerPower                                  CucsEquipmentPowerState,
    cucsComputeBoardControllerPresence                               CucsEquipmentPresence,
    cucsComputeBoardControllerRevision                               SnmpAdminString,
    cucsComputeBoardControllerSerial                                 SnmpAdminString,
    cucsComputeBoardControllerThermal                                CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardControllerVendor                                 SnmpAdminString,
    cucsComputeBoardControllerVoltage                                CucsEquipmentSensorThresholdStatus,
    cucsComputeBoardControllerOperQualifierReason                    SnmpAdminString,
    cucsComputeBoardControllerLocationDn                             SnmpAdminString
}

cucsComputeBoardControllerInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeBoardControllerEntry 1 }

cucsComputeBoardControllerDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:dn managed object property"
    ::= { cucsComputeBoardControllerEntry 2 }

cucsComputeBoardControllerRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:rn managed object property"
    ::= { cucsComputeBoardControllerEntry 3 }

cucsComputeBoardControllerId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:id managed object property"
    ::= { cucsComputeBoardControllerEntry 4 }

cucsComputeBoardControllerModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:model managed object property"
    ::= { cucsComputeBoardControllerEntry 5 }

cucsComputeBoardControllerOperState OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:operState managed
        object property"
    ::= { cucsComputeBoardControllerEntry 6 }

cucsComputeBoardControllerOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:operability
        managed object property"
    ::= { cucsComputeBoardControllerEntry 7 }

cucsComputeBoardControllerPerf OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:perf managed object property"
    ::= { cucsComputeBoardControllerEntry 8 }

cucsComputeBoardControllerPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:power managed object property"
    ::= { cucsComputeBoardControllerEntry 9 }

cucsComputeBoardControllerPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentPresence
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:presence managed
        object property"
    ::= { cucsComputeBoardControllerEntry 10 }

cucsComputeBoardControllerRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:revision managed
        object property"
    ::= { cucsComputeBoardControllerEntry 11 }

cucsComputeBoardControllerSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:serial managed object property"
    ::= { cucsComputeBoardControllerEntry 12 }

cucsComputeBoardControllerThermal OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:thermal managed
        object property"
    ::= { cucsComputeBoardControllerEntry 13 }

cucsComputeBoardControllerVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:vendor managed object property"
    ::= { cucsComputeBoardControllerEntry 14 }

cucsComputeBoardControllerVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:voltage managed
        object property"
    ::= { cucsComputeBoardControllerEntry 15 }

cucsComputeBoardControllerOperQualifierReason OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:operQualifierReason
        managed object property"
    ::= { cucsComputeBoardControllerEntry 16 }

cucsComputeBoardControllerLocationDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:BoardController:locationDn managed
        object property"
    ::= { cucsComputeBoardControllerEntry 17 }

cucsComputeCartridgeTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeCartridgeEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Cartridge managed object table"
    ::= { cucsComputeObjects 71 }

cucsComputeCartridgeEntry OBJECT-TYPE
    SYNTAX           CucsComputeCartridgeEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeCartridgeTable table."
    INDEX { cucsComputeCartridgeInstanceId }
    ::= { cucsComputeCartridgeTable 1 }

CucsComputeCartridgeEntry ::= SEQUENCE {
    cucsComputeCartridgeInstanceId                                   CucsManagedObjectId,
    cucsComputeCartridgeDn                                           CucsManagedObjectDn,
    cucsComputeCartridgeRn                                           SnmpAdminString,
    cucsComputeCartridgeChassisId                                    Gauge32,
    cucsComputeCartridgeDiscovery                                    CucsComputeCartridgeDiscovery,
    cucsComputeCartridgeFltAggr                                      Unsigned64,
    cucsComputeCartridgeId                                           Gauge32,
    cucsComputeCartridgeLc                                           CucsComputeAdminTrigger,
    cucsComputeCartridgeLcTs                                         DateAndTime,
    cucsComputeCartridgeModel                                        SnmpAdminString,
    cucsComputeCartridgeOperQualifierReason                          SnmpAdminString,
    cucsComputeCartridgeOperState                                    CucsEquipmentOperability,
    cucsComputeCartridgeOperability                                  CucsEquipmentOperability,
    cucsComputeCartridgePerf                                         CucsEquipmentSensorThresholdStatus,
    cucsComputeCartridgePower                                        CucsEquipmentPowerState,
    cucsComputeCartridgePresence                                     CucsEquipmentPresence,
    cucsComputeCartridgeRevision                                     SnmpAdminString,
    cucsComputeCartridgeSerial                                       SnmpAdminString,
    cucsComputeCartridgeSlotId                                       CucsComputeCartridgeSlotId,
    cucsComputeCartridgeThermal                                      CucsEquipmentSensorThresholdStatus,
    cucsComputeCartridgeVendor                                       SnmpAdminString,
    cucsComputeCartridgeVoltage                                      CucsEquipmentSensorThresholdStatus
}

cucsComputeCartridgeInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeCartridgeEntry 1 }

cucsComputeCartridgeDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:dn managed object property"
    ::= { cucsComputeCartridgeEntry 2 }

cucsComputeCartridgeRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:rn managed object property"
    ::= { cucsComputeCartridgeEntry 3 }

cucsComputeCartridgeChassisId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:chassisId managed object property"
    ::= { cucsComputeCartridgeEntry 4 }

cucsComputeCartridgeDiscovery OBJECT-TYPE
    SYNTAX       CucsComputeCartridgeDiscovery
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:discovery managed object property"
    ::= { cucsComputeCartridgeEntry 5 }

cucsComputeCartridgeFltAggr OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:fltAggr managed object property"
    ::= { cucsComputeCartridgeEntry 6 }

cucsComputeCartridgeId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:id managed object property"
    ::= { cucsComputeCartridgeEntry 7 }

cucsComputeCartridgeLc OBJECT-TYPE
    SYNTAX       CucsComputeAdminTrigger
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:lc managed object property"
    ::= { cucsComputeCartridgeEntry 8 }

cucsComputeCartridgeLcTs OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:lcTs managed object property"
    ::= { cucsComputeCartridgeEntry 9 }

cucsComputeCartridgeModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:model managed object property"
    ::= { cucsComputeCartridgeEntry 10 }

cucsComputeCartridgeOperQualifierReason OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:operQualifierReason
        managed object property"
    ::= { cucsComputeCartridgeEntry 11 }

cucsComputeCartridgeOperState OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:operState managed object property"
    ::= { cucsComputeCartridgeEntry 12 }

cucsComputeCartridgeOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:operability managed object property"
    ::= { cucsComputeCartridgeEntry 13 }

cucsComputeCartridgePerf OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:perf managed object property"
    ::= { cucsComputeCartridgeEntry 14 }

cucsComputeCartridgePower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:power managed object property"
    ::= { cucsComputeCartridgeEntry 15 }

cucsComputeCartridgePresence OBJECT-TYPE
    SYNTAX       CucsEquipmentPresence
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:presence managed object property"
    ::= { cucsComputeCartridgeEntry 16 }

cucsComputeCartridgeRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:revision managed object property"
    ::= { cucsComputeCartridgeEntry 17 }

cucsComputeCartridgeSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:serial managed object property"
    ::= { cucsComputeCartridgeEntry 18 }

cucsComputeCartridgeSlotId OBJECT-TYPE
    SYNTAX       CucsComputeCartridgeSlotId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:slotId managed object property"
    ::= { cucsComputeCartridgeEntry 19 }

cucsComputeCartridgeThermal OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:thermal managed object property"
    ::= { cucsComputeCartridgeEntry 20 }

cucsComputeCartridgeVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:vendor managed object property"
    ::= { cucsComputeCartridgeEntry 21 }

cucsComputeCartridgeVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Cartridge:voltage managed object property"
    ::= { cucsComputeCartridgeEntry 22 }

cucsComputeChassisConnPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeChassisConnPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy managed object table"
    ::= { cucsComputeObjects 46 }

cucsComputeChassisConnPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeChassisConnPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeChassisConnPolicyTable table."
    INDEX { cucsComputeChassisConnPolicyInstanceId }
    ::= { cucsComputeChassisConnPolicyTable 1 }

CucsComputeChassisConnPolicyEntry ::= SEQUENCE {
    cucsComputeChassisConnPolicyInstanceId                           CucsManagedObjectId,
    cucsComputeChassisConnPolicyDn                                   CucsManagedObjectDn,
    cucsComputeChassisConnPolicyRn                                   SnmpAdminString,
    cucsComputeChassisConnPolicyAdminState                           CucsComputeAdminLinkAggregation,
    cucsComputeChassisConnPolicyChassisId                            CucsComputeChassisConnPolicyChassisId,
    cucsComputeChassisConnPolicyDescr                                SnmpAdminString,
    cucsComputeChassisConnPolicyIntId                                SnmpAdminString,
    cucsComputeChassisConnPolicyName                                 SnmpAdminString,
    cucsComputeChassisConnPolicyQualifier                            SnmpAdminString,
    cucsComputeChassisConnPolicySwitchId                             CucsNetworkSwitchId,
    cucsComputeChassisConnPolicyPolicyLevel                          Gauge32,
    cucsComputeChassisConnPolicyPolicyOwner                          CucsPolicyPolicyOwner
}

cucsComputeChassisConnPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeChassisConnPolicyEntry 1 }

cucsComputeChassisConnPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:dn managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 2 }

cucsComputeChassisConnPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:rn managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 3 }

cucsComputeChassisConnPolicyAdminState OBJECT-TYPE
    SYNTAX       CucsComputeAdminLinkAggregation
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:adminState
        managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 4 }

cucsComputeChassisConnPolicyChassisId OBJECT-TYPE
    SYNTAX       CucsComputeChassisConnPolicyChassisId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:chassisId
        managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 5 }

cucsComputeChassisConnPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:descr managed
        object property"
    ::= { cucsComputeChassisConnPolicyEntry 6 }

cucsComputeChassisConnPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:intId managed
        object property"
    ::= { cucsComputeChassisConnPolicyEntry 7 }

cucsComputeChassisConnPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:name managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 8 }

cucsComputeChassisConnPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:qualifier
        managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 9 }

cucsComputeChassisConnPolicySwitchId OBJECT-TYPE
    SYNTAX       CucsNetworkSwitchId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:switchId managed
        object property"
    ::= { cucsComputeChassisConnPolicyEntry 10 }

cucsComputeChassisConnPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:policyLevel
        managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 11 }

cucsComputeChassisConnPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisConnPolicy:policyOwner
        managed object property"
    ::= { cucsComputeChassisConnPolicyEntry 12 }

cucsComputeChassisDiscPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeChassisDiscPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy managed object table"
    ::= { cucsComputeObjects 8 }

cucsComputeChassisDiscPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeChassisDiscPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeChassisDiscPolicyTable table."
    INDEX { cucsComputeChassisDiscPolicyInstanceId }
    ::= { cucsComputeChassisDiscPolicyTable 1 }

CucsComputeChassisDiscPolicyEntry ::= SEQUENCE {
    cucsComputeChassisDiscPolicyInstanceId                           CucsManagedObjectId,
    cucsComputeChassisDiscPolicyDn                                   CucsManagedObjectDn,
    cucsComputeChassisDiscPolicyRn                                   SnmpAdminString,
    cucsComputeChassisDiscPolicyAction                               CucsComputeChassisDiscAction,
    cucsComputeChassisDiscPolicyDescr                                SnmpAdminString,
    cucsComputeChassisDiscPolicyIntId                                SnmpAdminString,
    cucsComputeChassisDiscPolicyName                                 SnmpAdminString,
    cucsComputeChassisDiscPolicyQualifier                            SnmpAdminString,
    cucsComputeChassisDiscPolicyRebalance                            CucsComputeConnectivityRebalancing,
    cucsComputeChassisDiscPolicyLinkAggregationPref                  CucsComputeLinkAggregation,
    cucsComputeChassisDiscPolicyPolicyLevel                          Gauge32,
    cucsComputeChassisDiscPolicyPolicyOwner                          CucsPolicyPolicyOwner,
    cucsComputeChassisDiscPolicyMulticastHwHash                      CucsComputeChassisDiscPolicyMulticastHwHash
}

cucsComputeChassisDiscPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeChassisDiscPolicyEntry 1 }

cucsComputeChassisDiscPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:dn managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 2 }

cucsComputeChassisDiscPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:rn managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 3 }

cucsComputeChassisDiscPolicyAction OBJECT-TYPE
    SYNTAX       CucsComputeChassisDiscAction
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:action managed
        object property"
    ::= { cucsComputeChassisDiscPolicyEntry 4 }

cucsComputeChassisDiscPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:descr managed
        object property"
    ::= { cucsComputeChassisDiscPolicyEntry 5 }

cucsComputeChassisDiscPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:intId managed
        object property"
    ::= { cucsComputeChassisDiscPolicyEntry 6 }

cucsComputeChassisDiscPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:name managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 7 }

cucsComputeChassisDiscPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:qualifier
        managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 8 }

cucsComputeChassisDiscPolicyRebalance OBJECT-TYPE
    SYNTAX       CucsComputeConnectivityRebalancing
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:rebalance
        managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 9 }

cucsComputeChassisDiscPolicyLinkAggregationPref OBJECT-TYPE
    SYNTAX       CucsComputeLinkAggregation
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:linkAggregationPref
        managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 10 }

cucsComputeChassisDiscPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:policyLevel
        managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 11 }

cucsComputeChassisDiscPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:policyOwner
        managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 12 }

cucsComputeChassisDiscPolicyMulticastHwHash OBJECT-TYPE
    SYNTAX       CucsComputeChassisDiscPolicyMulticastHwHash
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisDiscPolicy:multicastHwHash
        managed object property"
    ::= { cucsComputeChassisDiscPolicyEntry 13 }

cucsComputeChassisQualTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeChassisQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ChassisQual managed object table"
    ::= { cucsComputeObjects 9 }

cucsComputeChassisQualEntry OBJECT-TYPE
    SYNTAX           CucsComputeChassisQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeChassisQualTable table."
    INDEX { cucsComputeChassisQualInstanceId }
    ::= { cucsComputeChassisQualTable 1 }

CucsComputeChassisQualEntry ::= SEQUENCE {
    cucsComputeChassisQualInstanceId                                 CucsManagedObjectId,
    cucsComputeChassisQualDn                                         CucsManagedObjectDn,
    cucsComputeChassisQualRn                                         SnmpAdminString,
    cucsComputeChassisQualMaxId                                      CucsComputeChassisQualMaxId,
    cucsComputeChassisQualMinId                                      CucsComputeChassisQualMinId
}

cucsComputeChassisQualInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeChassisQualEntry 1 }

cucsComputeChassisQualDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisQual:dn managed object property"
    ::= { cucsComputeChassisQualEntry 2 }

cucsComputeChassisQualRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisQual:rn managed object property"
    ::= { cucsComputeChassisQualEntry 3 }

cucsComputeChassisQualMaxId OBJECT-TYPE
    SYNTAX       CucsComputeChassisQualMaxId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisQual:maxId managed object property"
    ::= { cucsComputeChassisQualEntry 4 }

cucsComputeChassisQualMinId OBJECT-TYPE
    SYNTAX       CucsComputeChassisQualMinId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ChassisQual:minId managed object property"
    ::= { cucsComputeChassisQualEntry 5 }

cucsComputeConstraintDefTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeConstraintDefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef managed object table"
    ::= { cucsComputeObjects 67 }

cucsComputeConstraintDefEntry OBJECT-TYPE
    SYNTAX           CucsComputeConstraintDefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeConstraintDefTable table."
    INDEX { cucsComputeConstraintDefInstanceId }
    ::= { cucsComputeConstraintDefTable 1 }

CucsComputeConstraintDefEntry ::= SEQUENCE {
    cucsComputeConstraintDefInstanceId                               CucsManagedObjectId,
    cucsComputeConstraintDefDn                                       CucsManagedObjectDn,
    cucsComputeConstraintDefRn                                       SnmpAdminString,
    cucsComputeConstraintDefConstraintType                           CucsComputeEquipmentConstraintType,
    cucsComputeConstraintDefDescr                                    SnmpAdminString,
    cucsComputeConstraintDefHwModel                                  SnmpAdminString,
    cucsComputeConstraintDefHwRevision                               SnmpAdminString,
    cucsComputeConstraintDefHwVendor                                 SnmpAdminString,
    cucsComputeConstraintDefIntId                                    SnmpAdminString,
    cucsComputeConstraintDefName                                     SnmpAdminString,
    cucsComputeConstraintDefPolicyLevel                              Gauge32,
    cucsComputeConstraintDefPolicyOwner                              CucsPolicyPolicyOwner
}

cucsComputeConstraintDefInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeConstraintDefEntry 1 }

cucsComputeConstraintDefDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:dn managed object property"
    ::= { cucsComputeConstraintDefEntry 2 }

cucsComputeConstraintDefRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:rn managed object property"
    ::= { cucsComputeConstraintDefEntry 3 }

cucsComputeConstraintDefConstraintType OBJECT-TYPE
    SYNTAX       CucsComputeEquipmentConstraintType
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:constraintType
        managed object property"
    ::= { cucsComputeConstraintDefEntry 4 }

cucsComputeConstraintDefDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:descr managed object property"
    ::= { cucsComputeConstraintDefEntry 5 }

cucsComputeConstraintDefHwModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:hwModel managed object property"
    ::= { cucsComputeConstraintDefEntry 6 }

cucsComputeConstraintDefHwRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:hwRevision managed
        object property"
    ::= { cucsComputeConstraintDefEntry 7 }

cucsComputeConstraintDefHwVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:hwVendor managed object property"
    ::= { cucsComputeConstraintDefEntry 8 }

cucsComputeConstraintDefIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:intId managed object property"
    ::= { cucsComputeConstraintDefEntry 9 }

cucsComputeConstraintDefName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:name managed object property"
    ::= { cucsComputeConstraintDefEntry 10 }

cucsComputeConstraintDefPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:policyLevel managed
        object property"
    ::= { cucsComputeConstraintDefEntry 11 }

cucsComputeConstraintDefPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ConstraintDef:policyOwner managed
        object property"
    ::= { cucsComputeConstraintDefEntry 12 }

cucsComputeDefaultsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeDefaultsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Defaults managed object table"
    ::= { cucsComputeObjects 10 }

cucsComputeDefaultsEntry OBJECT-TYPE
    SYNTAX           CucsComputeDefaultsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeDefaultsTable table."
    INDEX { cucsComputeDefaultsInstanceId }
    ::= { cucsComputeDefaultsTable 1 }

CucsComputeDefaultsEntry ::= SEQUENCE {
    cucsComputeDefaultsInstanceId                                    CucsManagedObjectId,
    cucsComputeDefaultsDn                                            CucsManagedObjectDn,
    cucsComputeDefaultsRn                                            SnmpAdminString
}

cucsComputeDefaultsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeDefaultsEntry 1 }

cucsComputeDefaultsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Defaults:dn managed object property"
    ::= { cucsComputeDefaultsEntry 2 }

cucsComputeDefaultsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Defaults:rn managed object property"
    ::= { cucsComputeDefaultsEntry 3 }

cucsComputeExtBoardTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeExtBoardEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard managed object table"
    ::= { cucsComputeObjects 64 }

cucsComputeExtBoardEntry OBJECT-TYPE
    SYNTAX           CucsComputeExtBoardEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeExtBoardTable table."
    INDEX { cucsComputeExtBoardInstanceId }
    ::= { cucsComputeExtBoardTable 1 }

CucsComputeExtBoardEntry ::= SEQUENCE {
    cucsComputeExtBoardInstanceId                                    CucsManagedObjectId,
    cucsComputeExtBoardDn                                            CucsManagedObjectDn,
    cucsComputeExtBoardRn                                            SnmpAdminString,
    cucsComputeExtBoardBoardAggregationRole                          CucsEquipmentBoardAggregationRole,
    cucsComputeExtBoardChassisId                                     Gauge32,
    cucsComputeExtBoardCmosVoltage                                   CucsEquipmentSensorThresholdStatus,
    cucsComputeExtBoardConnPath                                      CucsEquipmentConnectionStatus,
    cucsComputeExtBoardConnStatus                                    CucsEquipmentConnectionStatus,
    cucsComputeExtBoardFaultQualifier                                SnmpAdminString,
    cucsComputeExtBoardId                                            Gauge32,
    cucsComputeExtBoardLocationDn                                    SnmpAdminString,
    cucsComputeExtBoardManagingInst                                  CucsNetworkSwitchId,
    cucsComputeExtBoardModel                                         SnmpAdminString,
    cucsComputeExtBoardOperPower                                     CucsEquipmentPowerState,
    cucsComputeExtBoardOperQualifierReason                           SnmpAdminString,
    cucsComputeExtBoardOperState                                     CucsEquipmentOperability,
    cucsComputeExtBoardOperability                                   CucsEquipmentOperability,
    cucsComputeExtBoardPerf                                          CucsEquipmentSensorThresholdStatus,
    cucsComputeExtBoardPower                                         CucsComputeABoardPower,
    cucsComputeExtBoardPowerUsage                                    CucsEquipmentSensorThresholdStatus,
    cucsComputeExtBoardPresence                                      CucsEquipmentPresence,
    cucsComputeExtBoardRevision                                      SnmpAdminString,
    cucsComputeExtBoardSerial                                        SnmpAdminString,
    cucsComputeExtBoardSlotId                                        Gauge32,
    cucsComputeExtBoardThermal                                       CucsEquipmentSensorThresholdStatus,
    cucsComputeExtBoardVendor                                        SnmpAdminString,
    cucsComputeExtBoardVoltage                                       CucsEquipmentSensorThresholdStatus,
    cucsComputeExtBoardDiscoveryStatus                               CucsEquipmentConnectionStatus
}

cucsComputeExtBoardInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeExtBoardEntry 1 }

cucsComputeExtBoardDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:dn managed object property"
    ::= { cucsComputeExtBoardEntry 2 }

cucsComputeExtBoardRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:rn managed object property"
    ::= { cucsComputeExtBoardEntry 3 }

cucsComputeExtBoardBoardAggregationRole OBJECT-TYPE
    SYNTAX       CucsEquipmentBoardAggregationRole
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:boardAggregationRole
        managed object property"
    ::= { cucsComputeExtBoardEntry 4 }

cucsComputeExtBoardChassisId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:chassisId managed object property"
    ::= { cucsComputeExtBoardEntry 5 }

cucsComputeExtBoardCmosVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:cmosVoltage managed object property"
    ::= { cucsComputeExtBoardEntry 6 }

cucsComputeExtBoardConnPath OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:connPath managed object property"
    ::= { cucsComputeExtBoardEntry 7 }

cucsComputeExtBoardConnStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:connStatus managed object property"
    ::= { cucsComputeExtBoardEntry 8 }

cucsComputeExtBoardFaultQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:faultQualifier managed
        object property"
    ::= { cucsComputeExtBoardEntry 9 }

cucsComputeExtBoardId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:id managed object property"
    ::= { cucsComputeExtBoardEntry 10 }

cucsComputeExtBoardLocationDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:locationDn managed object property"
    ::= { cucsComputeExtBoardEntry 11 }

cucsComputeExtBoardManagingInst OBJECT-TYPE
    SYNTAX       CucsNetworkSwitchId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:managingInst managed object property"
    ::= { cucsComputeExtBoardEntry 12 }

cucsComputeExtBoardModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:model managed object property"
    ::= { cucsComputeExtBoardEntry 13 }

cucsComputeExtBoardOperPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:operPower managed object property"
    ::= { cucsComputeExtBoardEntry 14 }

cucsComputeExtBoardOperQualifierReason OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:operQualifierReason
        managed object property"
    ::= { cucsComputeExtBoardEntry 15 }

cucsComputeExtBoardOperState OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:operState managed object property"
    ::= { cucsComputeExtBoardEntry 16 }

cucsComputeExtBoardOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:operability managed object property"
    ::= { cucsComputeExtBoardEntry 17 }

cucsComputeExtBoardPerf OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:perf managed object property"
    ::= { cucsComputeExtBoardEntry 18 }

cucsComputeExtBoardPower OBJECT-TYPE
    SYNTAX       CucsComputeABoardPower
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:power managed object property"
    ::= { cucsComputeExtBoardEntry 19 }

cucsComputeExtBoardPowerUsage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:powerUsage managed object property"
    ::= { cucsComputeExtBoardEntry 20 }

cucsComputeExtBoardPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentPresence
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:presence managed object property"
    ::= { cucsComputeExtBoardEntry 21 }

cucsComputeExtBoardRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:revision managed object property"
    ::= { cucsComputeExtBoardEntry 22 }

cucsComputeExtBoardSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:serial managed object property"
    ::= { cucsComputeExtBoardEntry 23 }

cucsComputeExtBoardSlotId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:slotId managed object property"
    ::= { cucsComputeExtBoardEntry 24 }

cucsComputeExtBoardThermal OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:thermal managed object property"
    ::= { cucsComputeExtBoardEntry 25 }

cucsComputeExtBoardVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:vendor managed object property"
    ::= { cucsComputeExtBoardEntry 26 }

cucsComputeExtBoardVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:voltage managed object property"
    ::= { cucsComputeExtBoardEntry 27 }

cucsComputeExtBoardDiscoveryStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ExtBoard:discoveryStatus managed
        object property"
    ::= { cucsComputeExtBoardEntry 28 }

cucsComputeFwSyncAckTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeFwSyncAckEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck managed object table"
    ::= { cucsComputeObjects 60 }

cucsComputeFwSyncAckEntry OBJECT-TYPE
    SYNTAX           CucsComputeFwSyncAckEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeFwSyncAckTable table."
    INDEX { cucsComputeFwSyncAckInstanceId }
    ::= { cucsComputeFwSyncAckTable 1 }

CucsComputeFwSyncAckEntry ::= SEQUENCE {
    cucsComputeFwSyncAckInstanceId                                   CucsManagedObjectId,
    cucsComputeFwSyncAckDn                                           CucsManagedObjectDn,
    cucsComputeFwSyncAckRn                                           SnmpAdminString,
    cucsComputeFwSyncAckAcked                                        DateAndTime,
    cucsComputeFwSyncAckAckedBy                                      SnmpAdminString,
    cucsComputeFwSyncAckAdminState                                   CucsTrigAdminState,
    cucsComputeFwSyncAckAutoDelete                                   TruthValue,
    cucsComputeFwSyncAckChangeBy                                     SnmpAdminString,
    cucsComputeFwSyncAckChangeDetails                                CucsTrigAckChangeDetails,
    cucsComputeFwSyncAckChanges                                      CucsTrigAckChanges,
    cucsComputeFwSyncAckDescr                                        SnmpAdminString,
    cucsComputeFwSyncAckDisr                                         CucsTrigAckDisr,
    cucsComputeFwSyncAckIgnoreCap                                    TruthValue,
    cucsComputeFwSyncAckIntId                                        SnmpAdminString,
    cucsComputeFwSyncAckModified                                     DateAndTime,
    cucsComputeFwSyncAckName                                         SnmpAdminString,
    cucsComputeFwSyncAckOperScheduler                                SnmpAdminString,
    cucsComputeFwSyncAckOperState                                    CucsTrigAckOperState,
    cucsComputeFwSyncAckPolicyLevel                                  Gauge32,
    cucsComputeFwSyncAckPolicyOwner                                  CucsPolicyPolicyOwner,
    cucsComputeFwSyncAckPrevOperState                                CucsTrigAckPrevOperState,
    cucsComputeFwSyncAckScheduler                                    SnmpAdminString,
    cucsComputeFwSyncAckTriggerConfigState                           CucsTrigTrigState
}

cucsComputeFwSyncAckInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeFwSyncAckEntry 1 }

cucsComputeFwSyncAckDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:dn managed object property"
    ::= { cucsComputeFwSyncAckEntry 2 }

cucsComputeFwSyncAckRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:rn managed object property"
    ::= { cucsComputeFwSyncAckEntry 3 }

cucsComputeFwSyncAckAcked OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:acked managed object property"
    ::= { cucsComputeFwSyncAckEntry 4 }

cucsComputeFwSyncAckAckedBy OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:ackedBy managed object property"
    ::= { cucsComputeFwSyncAckEntry 5 }

cucsComputeFwSyncAckAdminState OBJECT-TYPE
    SYNTAX       CucsTrigAdminState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:adminState managed object property"
    ::= { cucsComputeFwSyncAckEntry 6 }

cucsComputeFwSyncAckAutoDelete OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:autoDelete managed object property"
    ::= { cucsComputeFwSyncAckEntry 7 }

cucsComputeFwSyncAckChangeBy OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:changeBy managed object property"
    ::= { cucsComputeFwSyncAckEntry 8 }

cucsComputeFwSyncAckChangeDetails OBJECT-TYPE
    SYNTAX       CucsTrigAckChangeDetails
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:changeDetails managed
        object property"
    ::= { cucsComputeFwSyncAckEntry 9 }

cucsComputeFwSyncAckChanges OBJECT-TYPE
    SYNTAX       CucsTrigAckChanges
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:changes managed object property"
    ::= { cucsComputeFwSyncAckEntry 10 }

cucsComputeFwSyncAckDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:descr managed object property"
    ::= { cucsComputeFwSyncAckEntry 11 }

cucsComputeFwSyncAckDisr OBJECT-TYPE
    SYNTAX       CucsTrigAckDisr
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:disr managed object property"
    ::= { cucsComputeFwSyncAckEntry 12 }

cucsComputeFwSyncAckIgnoreCap OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:ignoreCap managed object property"
    ::= { cucsComputeFwSyncAckEntry 13 }

cucsComputeFwSyncAckIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:intId managed object property"
    ::= { cucsComputeFwSyncAckEntry 14 }

cucsComputeFwSyncAckModified OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:modified managed object property"
    ::= { cucsComputeFwSyncAckEntry 15 }

cucsComputeFwSyncAckName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:name managed object property"
    ::= { cucsComputeFwSyncAckEntry 16 }

cucsComputeFwSyncAckOperScheduler OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:operScheduler managed
        object property"
    ::= { cucsComputeFwSyncAckEntry 17 }

cucsComputeFwSyncAckOperState OBJECT-TYPE
    SYNTAX       CucsTrigAckOperState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:operState managed object property"
    ::= { cucsComputeFwSyncAckEntry 18 }

cucsComputeFwSyncAckPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:policyLevel managed object property"
    ::= { cucsComputeFwSyncAckEntry 19 }

cucsComputeFwSyncAckPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:policyOwner managed object property"
    ::= { cucsComputeFwSyncAckEntry 20 }

cucsComputeFwSyncAckPrevOperState OBJECT-TYPE
    SYNTAX       CucsTrigAckPrevOperState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:prevOperState managed
        object property"
    ::= { cucsComputeFwSyncAckEntry 21 }

cucsComputeFwSyncAckScheduler OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:scheduler managed object property"
    ::= { cucsComputeFwSyncAckEntry 22 }

cucsComputeFwSyncAckTriggerConfigState OBJECT-TYPE
    SYNTAX       CucsTrigTrigState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:FwSyncAck:triggerConfigState
        managed object property"
    ::= { cucsComputeFwSyncAckEntry 23 }

cucsComputeHealthLedSensorAlarmTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeHealthLedSensorAlarmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:HealthLedSensorAlarm managed object table"
    ::= { cucsComputeObjects 59 }

cucsComputeHealthLedSensorAlarmEntry OBJECT-TYPE
    SYNTAX           CucsComputeHealthLedSensorAlarmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeHealthLedSensorAlarmTable table."
    INDEX { cucsComputeHealthLedSensorAlarmInstanceId }
    ::= { cucsComputeHealthLedSensorAlarmTable 1 }

CucsComputeHealthLedSensorAlarmEntry ::= SEQUENCE {
    cucsComputeHealthLedSensorAlarmInstanceId                        CucsManagedObjectId,
    cucsComputeHealthLedSensorAlarmDn                                CucsManagedObjectDn,
    cucsComputeHealthLedSensorAlarmRn                                SnmpAdminString,
    cucsComputeHealthLedSensorAlarmAlarmDesc                         SnmpAdminString,
    cucsComputeHealthLedSensorAlarmAlarmSeverity                     CucsComputeAlarmSeverity,
    cucsComputeHealthLedSensorAlarmSensorId                          Gauge32,
    cucsComputeHealthLedSensorAlarmSensorName                        SnmpAdminString
}

cucsComputeHealthLedSensorAlarmInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeHealthLedSensorAlarmEntry 1 }

cucsComputeHealthLedSensorAlarmDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:HealthLedSensorAlarm:dn managed
        object property"
    ::= { cucsComputeHealthLedSensorAlarmEntry 2 }

cucsComputeHealthLedSensorAlarmRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:HealthLedSensorAlarm:rn managed
        object property"
    ::= { cucsComputeHealthLedSensorAlarmEntry 3 }

cucsComputeHealthLedSensorAlarmAlarmDesc OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:HealthLedSensorAlarm:alarmDesc
        managed object property"
    ::= { cucsComputeHealthLedSensorAlarmEntry 4 }

cucsComputeHealthLedSensorAlarmAlarmSeverity OBJECT-TYPE
    SYNTAX       CucsComputeAlarmSeverity
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:HealthLedSensorAlarm:alarmSeverity
        managed object property"
    ::= { cucsComputeHealthLedSensorAlarmEntry 5 }

cucsComputeHealthLedSensorAlarmSensorId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:HealthLedSensorAlarm:sensorId
        managed object property"
    ::= { cucsComputeHealthLedSensorAlarmEntry 6 }

cucsComputeHealthLedSensorAlarmSensorName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:HealthLedSensorAlarm:sensorName
        managed object property"
    ::= { cucsComputeHealthLedSensorAlarmEntry 7 }

cucsComputeIOHubTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeIOHubEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:IOHub managed object table"
    ::= { cucsComputeObjects 11 }

cucsComputeIOHubEntry OBJECT-TYPE
    SYNTAX           CucsComputeIOHubEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeIOHubTable table."
    INDEX { cucsComputeIOHubInstanceId }
    ::= { cucsComputeIOHubTable 1 }

CucsComputeIOHubEntry ::= SEQUENCE {
    cucsComputeIOHubInstanceId                                       CucsManagedObjectId,
    cucsComputeIOHubDn                                               CucsManagedObjectDn,
    cucsComputeIOHubRn                                               SnmpAdminString,
    cucsComputeIOHubId                                               Gauge32,
    cucsComputeIOHubModel                                            SnmpAdminString,
    cucsComputeIOHubOperState                                        CucsEquipmentOperability,
    cucsComputeIOHubOperability                                      CucsEquipmentOperability,
    cucsComputeIOHubPerf                                             CucsEquipmentSensorThresholdStatus,
    cucsComputeIOHubPower                                            CucsEquipmentPowerState,
    cucsComputeIOHubPresence                                         CucsEquipmentPresence,
    cucsComputeIOHubRevision                                         SnmpAdminString,
    cucsComputeIOHubSerial                                           SnmpAdminString,
    cucsComputeIOHubThermal                                          CucsEquipmentSensorThresholdStatus,
    cucsComputeIOHubVendor                                           SnmpAdminString,
    cucsComputeIOHubVoltage                                          CucsEquipmentSensorThresholdStatus,
    cucsComputeIOHubOperQualifierReason                              SnmpAdminString,
    cucsComputeIOHubLocationDn                                       SnmpAdminString
}

cucsComputeIOHubInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeIOHubEntry 1 }

cucsComputeIOHubDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:dn managed object property"
    ::= { cucsComputeIOHubEntry 2 }

cucsComputeIOHubRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:rn managed object property"
    ::= { cucsComputeIOHubEntry 3 }

cucsComputeIOHubId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:id managed object property"
    ::= { cucsComputeIOHubEntry 4 }

cucsComputeIOHubModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:model managed object property"
    ::= { cucsComputeIOHubEntry 5 }

cucsComputeIOHubOperState OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:operState managed object property"
    ::= { cucsComputeIOHubEntry 6 }

cucsComputeIOHubOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:operability managed object property"
    ::= { cucsComputeIOHubEntry 7 }

cucsComputeIOHubPerf OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:perf managed object property"
    ::= { cucsComputeIOHubEntry 8 }

cucsComputeIOHubPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:power managed object property"
    ::= { cucsComputeIOHubEntry 9 }

cucsComputeIOHubPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentPresence
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:presence managed object property"
    ::= { cucsComputeIOHubEntry 10 }

cucsComputeIOHubRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:revision managed object property"
    ::= { cucsComputeIOHubEntry 11 }

cucsComputeIOHubSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:serial managed object property"
    ::= { cucsComputeIOHubEntry 12 }

cucsComputeIOHubThermal OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:thermal managed object property"
    ::= { cucsComputeIOHubEntry 13 }

cucsComputeIOHubVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:vendor managed object property"
    ::= { cucsComputeIOHubEntry 14 }

cucsComputeIOHubVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:voltage managed object property"
    ::= { cucsComputeIOHubEntry 15 }

cucsComputeIOHubOperQualifierReason OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:operQualifierReason managed
        object property"
    ::= { cucsComputeIOHubEntry 16 }

cucsComputeIOHubLocationDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHub:locationDn managed object property"
    ::= { cucsComputeIOHubEntry 17 }

cucsComputeIOHubEnvStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeIOHubEnvStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats managed object table"
    ::= { cucsComputeObjects 12 }

cucsComputeIOHubEnvStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputeIOHubEnvStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeIOHubEnvStatsTable table."
    INDEX { cucsComputeIOHubEnvStatsInstanceId }
    ::= { cucsComputeIOHubEnvStatsTable 1 }

CucsComputeIOHubEnvStatsEntry ::= SEQUENCE {
    cucsComputeIOHubEnvStatsInstanceId                               CucsManagedObjectId,
    cucsComputeIOHubEnvStatsDn                                       CucsManagedObjectDn,
    cucsComputeIOHubEnvStatsRn                                       SnmpAdminString,
    cucsComputeIOHubEnvStatsIntervals                                Gauge32,
    cucsComputeIOHubEnvStatsSuspect                                  TruthValue,
    cucsComputeIOHubEnvStatsTemperature                              INTEGER,
    cucsComputeIOHubEnvStatsTemperatureAvg                           INTEGER,
    cucsComputeIOHubEnvStatsTemperatureMax                           INTEGER,
    cucsComputeIOHubEnvStatsTemperatureMin                           INTEGER,
    cucsComputeIOHubEnvStatsThresholded                              CucsComputeIOHubEnvStatsThresholded,
    cucsComputeIOHubEnvStatsTimeCollected                            DateAndTime,
    cucsComputeIOHubEnvStatsUpdate                                   Gauge32
}

cucsComputeIOHubEnvStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeIOHubEnvStatsEntry 1 }

cucsComputeIOHubEnvStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:dn managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 2 }

cucsComputeIOHubEnvStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:rn managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 3 }

cucsComputeIOHubEnvStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:intervals managed
        object property"
    ::= { cucsComputeIOHubEnvStatsEntry 4 }

cucsComputeIOHubEnvStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:suspect managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 5 }

cucsComputeIOHubEnvStatsTemperature OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:temperature managed
        object property"
    ::= { cucsComputeIOHubEnvStatsEntry 6 }

cucsComputeIOHubEnvStatsTemperatureAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:temperatureAvg
        managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 7 }

cucsComputeIOHubEnvStatsTemperatureMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:temperatureMax
        managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 8 }

cucsComputeIOHubEnvStatsTemperatureMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:temperatureMin
        managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 9 }

cucsComputeIOHubEnvStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputeIOHubEnvStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:thresholded managed
        object property"
    ::= { cucsComputeIOHubEnvStatsEntry 10 }

cucsComputeIOHubEnvStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:timeCollected
        managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 11 }

cucsComputeIOHubEnvStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStats:update managed object property"
    ::= { cucsComputeIOHubEnvStatsEntry 12 }

cucsComputeIOHubEnvStatsHistTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeIOHubEnvStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist managed object table"
    ::= { cucsComputeObjects 13 }

cucsComputeIOHubEnvStatsHistEntry OBJECT-TYPE
    SYNTAX           CucsComputeIOHubEnvStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeIOHubEnvStatsHistTable table."
    INDEX { cucsComputeIOHubEnvStatsHistInstanceId }
    ::= { cucsComputeIOHubEnvStatsHistTable 1 }

CucsComputeIOHubEnvStatsHistEntry ::= SEQUENCE {
    cucsComputeIOHubEnvStatsHistInstanceId                           CucsManagedObjectId,
    cucsComputeIOHubEnvStatsHistDn                                   CucsManagedObjectDn,
    cucsComputeIOHubEnvStatsHistRn                                   SnmpAdminString,
    cucsComputeIOHubEnvStatsHistId                                   Unsigned64,
    cucsComputeIOHubEnvStatsHistMostRecent                           TruthValue,
    cucsComputeIOHubEnvStatsHistSuspect                              TruthValue,
    cucsComputeIOHubEnvStatsHistTemperature                          INTEGER,
    cucsComputeIOHubEnvStatsHistTemperatureAvg                       INTEGER,
    cucsComputeIOHubEnvStatsHistTemperatureMax                       INTEGER,
    cucsComputeIOHubEnvStatsHistTemperatureMin                       INTEGER,
    cucsComputeIOHubEnvStatsHistThresholded                          CucsComputeIOHubEnvStatsHistThresholded,
    cucsComputeIOHubEnvStatsHistTimeCollected                        DateAndTime
}

cucsComputeIOHubEnvStatsHistInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeIOHubEnvStatsHistEntry 1 }

cucsComputeIOHubEnvStatsHistDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:dn managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 2 }

cucsComputeIOHubEnvStatsHistRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:rn managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 3 }

cucsComputeIOHubEnvStatsHistId OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:id managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 4 }

cucsComputeIOHubEnvStatsHistMostRecent OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:mostRecent
        managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 5 }

cucsComputeIOHubEnvStatsHistSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:suspect managed
        object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 6 }

cucsComputeIOHubEnvStatsHistTemperature OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:temperature
        managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 7 }

cucsComputeIOHubEnvStatsHistTemperatureAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:temperatureAvg
        managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 8 }

cucsComputeIOHubEnvStatsHistTemperatureMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:temperatureMax
        managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 9 }

cucsComputeIOHubEnvStatsHistTemperatureMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:temperatureMin
        managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 10 }

cucsComputeIOHubEnvStatsHistThresholded OBJECT-TYPE
    SYNTAX       CucsComputeIOHubEnvStatsHistThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:thresholded
        managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 11 }

cucsComputeIOHubEnvStatsHistTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:IOHubEnvStatsHist:timeCollected
        managed object property"
    ::= { cucsComputeIOHubEnvStatsHistEntry 12 }

cucsComputeInstanceIdQualTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeInstanceIdQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:InstanceIdQual managed object table"
    ::= { cucsComputeObjects 72 }

cucsComputeInstanceIdQualEntry OBJECT-TYPE
    SYNTAX           CucsComputeInstanceIdQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeInstanceIdQualTable table."
    INDEX { cucsComputeInstanceIdQualInstanceId }
    ::= { cucsComputeInstanceIdQualTable 1 }

CucsComputeInstanceIdQualEntry ::= SEQUENCE {
    cucsComputeInstanceIdQualInstanceId                              CucsManagedObjectId,
    cucsComputeInstanceIdQualDn                                      CucsManagedObjectDn,
    cucsComputeInstanceIdQualRn                                      SnmpAdminString,
    cucsComputeInstanceIdQualMaxId                                   CucsComputeInstanceIdQualMaxId,
    cucsComputeInstanceIdQualMinId                                   CucsComputeInstanceIdQualMinId
}

cucsComputeInstanceIdQualInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeInstanceIdQualEntry 1 }

cucsComputeInstanceIdQualDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:InstanceIdQual:dn managed object property"
    ::= { cucsComputeInstanceIdQualEntry 2 }

cucsComputeInstanceIdQualRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:InstanceIdQual:rn managed object property"
    ::= { cucsComputeInstanceIdQualEntry 3 }

cucsComputeInstanceIdQualMaxId OBJECT-TYPE
    SYNTAX       CucsComputeInstanceIdQualMaxId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:InstanceIdQual:maxId managed object property"
    ::= { cucsComputeInstanceIdQualEntry 4 }

cucsComputeInstanceIdQualMinId OBJECT-TYPE
    SYNTAX       CucsComputeInstanceIdQualMinId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:InstanceIdQual:minId managed object property"
    ::= { cucsComputeInstanceIdQualEntry 5 }

cucsComputeKvmMgmtPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeKvmMgmtPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy managed object table"
    ::= { cucsComputeObjects 65 }

cucsComputeKvmMgmtPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeKvmMgmtPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeKvmMgmtPolicyTable table."
    INDEX { cucsComputeKvmMgmtPolicyInstanceId }
    ::= { cucsComputeKvmMgmtPolicyTable 1 }

CucsComputeKvmMgmtPolicyEntry ::= SEQUENCE {
    cucsComputeKvmMgmtPolicyInstanceId                               CucsManagedObjectId,
    cucsComputeKvmMgmtPolicyDn                                       CucsManagedObjectDn,
    cucsComputeKvmMgmtPolicyRn                                       SnmpAdminString,
    cucsComputeKvmMgmtPolicyDescr                                    SnmpAdminString,
    cucsComputeKvmMgmtPolicyIntId                                    SnmpAdminString,
    cucsComputeKvmMgmtPolicyName                                     SnmpAdminString,
    cucsComputeKvmMgmtPolicyPolicyLevel                              Gauge32,
    cucsComputeKvmMgmtPolicyPolicyOwner                              CucsPolicyPolicyOwner,
    cucsComputeKvmMgmtPolicyVmediaEncryption                         CucsComputeKvmMgmtPolicyVmediaEncryption
}

cucsComputeKvmMgmtPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeKvmMgmtPolicyEntry 1 }

cucsComputeKvmMgmtPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:dn managed object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 2 }

cucsComputeKvmMgmtPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:rn managed object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 3 }

cucsComputeKvmMgmtPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:descr managed object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 4 }

cucsComputeKvmMgmtPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:intId managed object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 5 }

cucsComputeKvmMgmtPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:name managed object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 6 }

cucsComputeKvmMgmtPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:policyLevel managed
        object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 7 }

cucsComputeKvmMgmtPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:policyOwner managed
        object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 8 }

cucsComputeKvmMgmtPolicyVmediaEncryption OBJECT-TYPE
    SYNTAX       CucsComputeKvmMgmtPolicyVmediaEncryption
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:KvmMgmtPolicy:vmediaEncryption
        managed object property"
    ::= { cucsComputeKvmMgmtPolicyEntry 9 }

cucsComputeMbPowerStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeMbPowerStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats managed object table"
    ::= { cucsComputeObjects 14 }

cucsComputeMbPowerStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputeMbPowerStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeMbPowerStatsTable table."
    INDEX { cucsComputeMbPowerStatsInstanceId }
    ::= { cucsComputeMbPowerStatsTable 1 }

CucsComputeMbPowerStatsEntry ::= SEQUENCE {
    cucsComputeMbPowerStatsInstanceId                                CucsManagedObjectId,
    cucsComputeMbPowerStatsDn                                        CucsManagedObjectDn,
    cucsComputeMbPowerStatsRn                                        SnmpAdminString,
    cucsComputeMbPowerStatsConsumedPower                             INTEGER,
    cucsComputeMbPowerStatsConsumedPowerAvg                          INTEGER,
    cucsComputeMbPowerStatsConsumedPowerMax                          INTEGER,
    cucsComputeMbPowerStatsConsumedPowerMin                          INTEGER,
    cucsComputeMbPowerStatsInputCurrent                              INTEGER,
    cucsComputeMbPowerStatsInputCurrentAvg                           INTEGER,
    cucsComputeMbPowerStatsInputCurrentMax                           INTEGER,
    cucsComputeMbPowerStatsInputCurrentMin                           INTEGER,
    cucsComputeMbPowerStatsInputVoltage                              INTEGER,
    cucsComputeMbPowerStatsInputVoltageAvg                           INTEGER,
    cucsComputeMbPowerStatsInputVoltageMax                           INTEGER,
    cucsComputeMbPowerStatsInputVoltageMin                           INTEGER,
    cucsComputeMbPowerStatsIntervals                                 Gauge32,
    cucsComputeMbPowerStatsSuspect                                   TruthValue,
    cucsComputeMbPowerStatsThresholded                               CucsComputeMbPowerStatsThresholded,
    cucsComputeMbPowerStatsTimeCollected                             DateAndTime,
    cucsComputeMbPowerStatsUpdate                                    Gauge32
}

cucsComputeMbPowerStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeMbPowerStatsEntry 1 }

cucsComputeMbPowerStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:dn managed object property"
    ::= { cucsComputeMbPowerStatsEntry 2 }

cucsComputeMbPowerStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:rn managed object property"
    ::= { cucsComputeMbPowerStatsEntry 3 }

cucsComputeMbPowerStatsConsumedPower OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:consumedPower managed
        object property"
    ::= { cucsComputeMbPowerStatsEntry 4 }

cucsComputeMbPowerStatsConsumedPowerAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:consumedPowerAvg
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 5 }

cucsComputeMbPowerStatsConsumedPowerMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:consumedPowerMax
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 6 }

cucsComputeMbPowerStatsConsumedPowerMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:consumedPowerMin
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 7 }

cucsComputeMbPowerStatsInputCurrent OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputCurrent managed
        object property"
    ::= { cucsComputeMbPowerStatsEntry 8 }

cucsComputeMbPowerStatsInputCurrentAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputCurrentAvg
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 9 }

cucsComputeMbPowerStatsInputCurrentMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputCurrentMax
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 10 }

cucsComputeMbPowerStatsInputCurrentMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputCurrentMin
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 11 }

cucsComputeMbPowerStatsInputVoltage OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputVoltage managed
        object property"
    ::= { cucsComputeMbPowerStatsEntry 12 }

cucsComputeMbPowerStatsInputVoltageAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputVoltageAvg
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 13 }

cucsComputeMbPowerStatsInputVoltageMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputVoltageMax
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 14 }

cucsComputeMbPowerStatsInputVoltageMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:inputVoltageMin
        managed object property"
    ::= { cucsComputeMbPowerStatsEntry 15 }

cucsComputeMbPowerStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:intervals managed object property"
    ::= { cucsComputeMbPowerStatsEntry 16 }

cucsComputeMbPowerStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:suspect managed object property"
    ::= { cucsComputeMbPowerStatsEntry 17 }

cucsComputeMbPowerStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputeMbPowerStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:thresholded managed
        object property"
    ::= { cucsComputeMbPowerStatsEntry 18 }

cucsComputeMbPowerStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:timeCollected managed
        object property"
    ::= { cucsComputeMbPowerStatsEntry 19 }

cucsComputeMbPowerStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStats:update managed object property"
    ::= { cucsComputeMbPowerStatsEntry 20 }

cucsComputeMbPowerStatsHistTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeMbPowerStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist managed object table"
    ::= { cucsComputeObjects 15 }

cucsComputeMbPowerStatsHistEntry OBJECT-TYPE
    SYNTAX           CucsComputeMbPowerStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeMbPowerStatsHistTable table."
    INDEX { cucsComputeMbPowerStatsHistInstanceId }
    ::= { cucsComputeMbPowerStatsHistTable 1 }

CucsComputeMbPowerStatsHistEntry ::= SEQUENCE {
    cucsComputeMbPowerStatsHistInstanceId                            CucsManagedObjectId,
    cucsComputeMbPowerStatsHistDn                                    CucsManagedObjectDn,
    cucsComputeMbPowerStatsHistRn                                    SnmpAdminString,
    cucsComputeMbPowerStatsHistConsumedPower                         INTEGER,
    cucsComputeMbPowerStatsHistConsumedPowerAvg                      INTEGER,
    cucsComputeMbPowerStatsHistConsumedPowerMax                      INTEGER,
    cucsComputeMbPowerStatsHistConsumedPowerMin                      INTEGER,
    cucsComputeMbPowerStatsHistId                                    Unsigned64,
    cucsComputeMbPowerStatsHistInputCurrent                          INTEGER,
    cucsComputeMbPowerStatsHistInputCurrentAvg                       INTEGER,
    cucsComputeMbPowerStatsHistInputCurrentMax                       INTEGER,
    cucsComputeMbPowerStatsHistInputCurrentMin                       INTEGER,
    cucsComputeMbPowerStatsHistInputVoltage                          INTEGER,
    cucsComputeMbPowerStatsHistInputVoltageAvg                       INTEGER,
    cucsComputeMbPowerStatsHistInputVoltageMax                       INTEGER,
    cucsComputeMbPowerStatsHistInputVoltageMin                       INTEGER,
    cucsComputeMbPowerStatsHistMostRecent                            TruthValue,
    cucsComputeMbPowerStatsHistSuspect                               TruthValue,
    cucsComputeMbPowerStatsHistThresholded                           CucsComputeMbPowerStatsHistThresholded,
    cucsComputeMbPowerStatsHistTimeCollected                         DateAndTime
}

cucsComputeMbPowerStatsHistInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeMbPowerStatsHistEntry 1 }

cucsComputeMbPowerStatsHistDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:dn managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 2 }

cucsComputeMbPowerStatsHistRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:rn managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 3 }

cucsComputeMbPowerStatsHistConsumedPower OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:consumedPower
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 4 }

cucsComputeMbPowerStatsHistConsumedPowerAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:consumedPowerAvg
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 5 }

cucsComputeMbPowerStatsHistConsumedPowerMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:consumedPowerMax
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 6 }

cucsComputeMbPowerStatsHistConsumedPowerMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:consumedPowerMin
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 7 }

cucsComputeMbPowerStatsHistId OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:id managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 8 }

cucsComputeMbPowerStatsHistInputCurrent OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputCurrent
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 9 }

cucsComputeMbPowerStatsHistInputCurrentAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputCurrentAvg
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 10 }

cucsComputeMbPowerStatsHistInputCurrentMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputCurrentMax
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 11 }

cucsComputeMbPowerStatsHistInputCurrentMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputCurrentMin
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 12 }

cucsComputeMbPowerStatsHistInputVoltage OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputVoltage
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 13 }

cucsComputeMbPowerStatsHistInputVoltageAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputVoltageAvg
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 14 }

cucsComputeMbPowerStatsHistInputVoltageMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputVoltageMax
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 15 }

cucsComputeMbPowerStatsHistInputVoltageMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:inputVoltageMin
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 16 }

cucsComputeMbPowerStatsHistMostRecent OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:mostRecent
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 17 }

cucsComputeMbPowerStatsHistSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:suspect managed
        object property"
    ::= { cucsComputeMbPowerStatsHistEntry 18 }

cucsComputeMbPowerStatsHistThresholded OBJECT-TYPE
    SYNTAX       CucsComputeMbPowerStatsHistThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:thresholded
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 19 }

cucsComputeMbPowerStatsHistTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbPowerStatsHist:timeCollected
        managed object property"
    ::= { cucsComputeMbPowerStatsHistEntry 20 }

cucsComputeMbTempStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeMbTempStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats managed object table"
    ::= { cucsComputeObjects 16 }

cucsComputeMbTempStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputeMbTempStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeMbTempStatsTable table."
    INDEX { cucsComputeMbTempStatsInstanceId }
    ::= { cucsComputeMbTempStatsTable 1 }

CucsComputeMbTempStatsEntry ::= SEQUENCE {
    cucsComputeMbTempStatsInstanceId                                 CucsManagedObjectId,
    cucsComputeMbTempStatsDn                                         CucsManagedObjectDn,
    cucsComputeMbTempStatsRn                                         SnmpAdminString,
    cucsComputeMbTempStatsFmTempSenIo                                INTEGER,
    cucsComputeMbTempStatsFmTempSenIoAvg                             INTEGER,
    cucsComputeMbTempStatsFmTempSenIoMax                             INTEGER,
    cucsComputeMbTempStatsFmTempSenIoMin                             INTEGER,
    cucsComputeMbTempStatsFmTempSenRear                              INTEGER,
    cucsComputeMbTempStatsFmTempSenRearAvg                           INTEGER,
    cucsComputeMbTempStatsFmTempSenRearL                             INTEGER,
    cucsComputeMbTempStatsFmTempSenRearLAvg                          INTEGER,
    cucsComputeMbTempStatsFmTempSenRearLMax                          INTEGER,
    cucsComputeMbTempStatsFmTempSenRearLMin                          INTEGER,
    cucsComputeMbTempStatsFmTempSenRearMax                           INTEGER,
    cucsComputeMbTempStatsFmTempSenRearMin                           INTEGER,
    cucsComputeMbTempStatsFmTempSenRearR                             INTEGER,
    cucsComputeMbTempStatsFmTempSenRearRAvg                          INTEGER,
    cucsComputeMbTempStatsFmTempSenRearRMax                          INTEGER,
    cucsComputeMbTempStatsFmTempSenRearRMin                          INTEGER,
    cucsComputeMbTempStatsIntervals                                  Gauge32,
    cucsComputeMbTempStatsSuspect                                    TruthValue,
    cucsComputeMbTempStatsThresholded                                CucsComputeMbTempStatsThresholded,
    cucsComputeMbTempStatsTimeCollected                              DateAndTime,
    cucsComputeMbTempStatsUpdate                                     Gauge32
}

cucsComputeMbTempStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeMbTempStatsEntry 1 }

cucsComputeMbTempStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:dn managed object property"
    ::= { cucsComputeMbTempStatsEntry 2 }

cucsComputeMbTempStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:rn managed object property"
    ::= { cucsComputeMbTempStatsEntry 3 }

cucsComputeMbTempStatsFmTempSenIo OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenIo managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 4 }

cucsComputeMbTempStatsFmTempSenIoAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenIoAvg managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 5 }

cucsComputeMbTempStatsFmTempSenIoMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenIoMax managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 6 }

cucsComputeMbTempStatsFmTempSenIoMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenIoMin managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 7 }

cucsComputeMbTempStatsFmTempSenRear OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRear managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 8 }

cucsComputeMbTempStatsFmTempSenRearAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearAvg
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 9 }

cucsComputeMbTempStatsFmTempSenRearL OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearL managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 10 }

cucsComputeMbTempStatsFmTempSenRearLAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearLAvg
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 11 }

cucsComputeMbTempStatsFmTempSenRearLMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearLMax
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 12 }

cucsComputeMbTempStatsFmTempSenRearLMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearLMin
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 13 }

cucsComputeMbTempStatsFmTempSenRearMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearMax
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 14 }

cucsComputeMbTempStatsFmTempSenRearMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearMin
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 15 }

cucsComputeMbTempStatsFmTempSenRearR OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearR managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 16 }

cucsComputeMbTempStatsFmTempSenRearRAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearRAvg
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 17 }

cucsComputeMbTempStatsFmTempSenRearRMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearRMax
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 18 }

cucsComputeMbTempStatsFmTempSenRearRMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:fmTempSenRearRMin
        managed object property"
    ::= { cucsComputeMbTempStatsEntry 19 }

cucsComputeMbTempStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:intervals managed object property"
    ::= { cucsComputeMbTempStatsEntry 20 }

cucsComputeMbTempStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:suspect managed object property"
    ::= { cucsComputeMbTempStatsEntry 21 }

cucsComputeMbTempStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputeMbTempStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:thresholded managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 22 }

cucsComputeMbTempStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:timeCollected managed
        object property"
    ::= { cucsComputeMbTempStatsEntry 23 }

cucsComputeMbTempStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStats:update managed object property"
    ::= { cucsComputeMbTempStatsEntry 24 }

cucsComputeMbTempStatsHistTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeMbTempStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist managed object table"
    ::= { cucsComputeObjects 17 }

cucsComputeMbTempStatsHistEntry OBJECT-TYPE
    SYNTAX           CucsComputeMbTempStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeMbTempStatsHistTable table."
    INDEX { cucsComputeMbTempStatsHistInstanceId }
    ::= { cucsComputeMbTempStatsHistTable 1 }

CucsComputeMbTempStatsHistEntry ::= SEQUENCE {
    cucsComputeMbTempStatsHistInstanceId                             CucsManagedObjectId,
    cucsComputeMbTempStatsHistDn                                     CucsManagedObjectDn,
    cucsComputeMbTempStatsHistRn                                     SnmpAdminString,
    cucsComputeMbTempStatsHistFmTempSenIo                            INTEGER,
    cucsComputeMbTempStatsHistFmTempSenIoAvg                         INTEGER,
    cucsComputeMbTempStatsHistFmTempSenIoMax                         INTEGER,
    cucsComputeMbTempStatsHistFmTempSenIoMin                         INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRear                          INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearAvg                       INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearL                         INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearLAvg                      INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearLMax                      INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearLMin                      INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearMax                       INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearMin                       INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearR                         INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearRAvg                      INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearRMax                      INTEGER,
    cucsComputeMbTempStatsHistFmTempSenRearRMin                      INTEGER,
    cucsComputeMbTempStatsHistId                                     Unsigned64,
    cucsComputeMbTempStatsHistMostRecent                             TruthValue,
    cucsComputeMbTempStatsHistSuspect                                TruthValue,
    cucsComputeMbTempStatsHistThresholded                            CucsComputeMbTempStatsHistThresholded,
    cucsComputeMbTempStatsHistTimeCollected                          DateAndTime
}

cucsComputeMbTempStatsHistInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeMbTempStatsHistEntry 1 }

cucsComputeMbTempStatsHistDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:dn managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 2 }

cucsComputeMbTempStatsHistRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:rn managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 3 }

cucsComputeMbTempStatsHistFmTempSenIo OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenIo
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 4 }

cucsComputeMbTempStatsHistFmTempSenIoAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenIoAvg
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 5 }

cucsComputeMbTempStatsHistFmTempSenIoMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenIoMax
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 6 }

cucsComputeMbTempStatsHistFmTempSenIoMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenIoMin
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 7 }

cucsComputeMbTempStatsHistFmTempSenRear OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRear
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 8 }

cucsComputeMbTempStatsHistFmTempSenRearAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearAvg
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 9 }

cucsComputeMbTempStatsHistFmTempSenRearL OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearL
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 10 }

cucsComputeMbTempStatsHistFmTempSenRearLAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearLAvg
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 11 }

cucsComputeMbTempStatsHistFmTempSenRearLMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearLMax
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 12 }

cucsComputeMbTempStatsHistFmTempSenRearLMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearLMin
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 13 }

cucsComputeMbTempStatsHistFmTempSenRearMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearMax
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 14 }

cucsComputeMbTempStatsHistFmTempSenRearMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearMin
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 15 }

cucsComputeMbTempStatsHistFmTempSenRearR OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearR
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 16 }

cucsComputeMbTempStatsHistFmTempSenRearRAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearRAvg
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 17 }

cucsComputeMbTempStatsHistFmTempSenRearRMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearRMax
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 18 }

cucsComputeMbTempStatsHistFmTempSenRearRMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:fmTempSenRearRMin
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 19 }

cucsComputeMbTempStatsHistId OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:id managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 20 }

cucsComputeMbTempStatsHistMostRecent OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:mostRecent managed
        object property"
    ::= { cucsComputeMbTempStatsHistEntry 21 }

cucsComputeMbTempStatsHistSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:suspect managed
        object property"
    ::= { cucsComputeMbTempStatsHistEntry 22 }

cucsComputeMbTempStatsHistThresholded OBJECT-TYPE
    SYNTAX       CucsComputeMbTempStatsHistThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:thresholded
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 23 }

cucsComputeMbTempStatsHistTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MbTempStatsHist:timeCollected
        managed object property"
    ::= { cucsComputeMbTempStatsHistEntry 24 }

cucsComputeMemoryConfigPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeMemoryConfigPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy managed object table"
    ::= { cucsComputeObjects 61 }

cucsComputeMemoryConfigPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeMemoryConfigPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeMemoryConfigPolicyTable table."
    INDEX { cucsComputeMemoryConfigPolicyInstanceId }
    ::= { cucsComputeMemoryConfigPolicyTable 1 }

CucsComputeMemoryConfigPolicyEntry ::= SEQUENCE {
    cucsComputeMemoryConfigPolicyInstanceId                          CucsManagedObjectId,
    cucsComputeMemoryConfigPolicyDn                                  CucsManagedObjectDn,
    cucsComputeMemoryConfigPolicyRn                                  SnmpAdminString,
    cucsComputeMemoryConfigPolicyBlackListing                        CucsComputeBlackListing,
    cucsComputeMemoryConfigPolicyDescr                               SnmpAdminString,
    cucsComputeMemoryConfigPolicyIntId                               SnmpAdminString,
    cucsComputeMemoryConfigPolicyName                                SnmpAdminString,
    cucsComputeMemoryConfigPolicyPolicyLevel                         Gauge32,
    cucsComputeMemoryConfigPolicyPolicyOwner                         CucsPolicyPolicyOwner
}

cucsComputeMemoryConfigPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeMemoryConfigPolicyEntry 1 }

cucsComputeMemoryConfigPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:dn managed object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 2 }

cucsComputeMemoryConfigPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:rn managed object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 3 }

cucsComputeMemoryConfigPolicyBlackListing OBJECT-TYPE
    SYNTAX       CucsComputeBlackListing
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:blackListing
        managed object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 4 }

cucsComputeMemoryConfigPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:descr managed
        object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 5 }

cucsComputeMemoryConfigPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:intId managed
        object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 6 }

cucsComputeMemoryConfigPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:name managed
        object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 7 }

cucsComputeMemoryConfigPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:policyLevel
        managed object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 8 }

cucsComputeMemoryConfigPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfigPolicy:policyOwner
        managed object property"
    ::= { cucsComputeMemoryConfigPolicyEntry 9 }

cucsComputeMemoryConfigurationTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeMemoryConfigurationEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfiguration managed object table"
    ::= { cucsComputeObjects 62 }

cucsComputeMemoryConfigurationEntry OBJECT-TYPE
    SYNTAX           CucsComputeMemoryConfigurationEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeMemoryConfigurationTable table."
    INDEX { cucsComputeMemoryConfigurationInstanceId }
    ::= { cucsComputeMemoryConfigurationTable 1 }

CucsComputeMemoryConfigurationEntry ::= SEQUENCE {
    cucsComputeMemoryConfigurationInstanceId                         CucsManagedObjectId,
    cucsComputeMemoryConfigurationDn                                 CucsManagedObjectDn,
    cucsComputeMemoryConfigurationRn                                 SnmpAdminString,
    cucsComputeMemoryConfigurationAdminMemoryState                   CucsComputeAdminMemoryState,
    cucsComputeMemoryConfigurationBlackListing                       CucsComputeBlackListing
}

cucsComputeMemoryConfigurationInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeMemoryConfigurationEntry 1 }

cucsComputeMemoryConfigurationDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfiguration:dn managed object property"
    ::= { cucsComputeMemoryConfigurationEntry 2 }

cucsComputeMemoryConfigurationRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfiguration:rn managed object property"
    ::= { cucsComputeMemoryConfigurationEntry 3 }

cucsComputeMemoryConfigurationAdminMemoryState OBJECT-TYPE
    SYNTAX       CucsComputeAdminMemoryState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfiguration:adminMemoryState
        managed object property"
    ::= { cucsComputeMemoryConfigurationEntry 4 }

cucsComputeMemoryConfigurationBlackListing OBJECT-TYPE
    SYNTAX       CucsComputeBlackListing
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryConfiguration:blackListing
        managed object property"
    ::= { cucsComputeMemoryConfigurationEntry 5 }

cucsComputeMemoryUnitConstraintDefTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeMemoryUnitConstraintDefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef managed object table"
    ::= { cucsComputeObjects 18 }

cucsComputeMemoryUnitConstraintDefEntry OBJECT-TYPE
    SYNTAX           CucsComputeMemoryUnitConstraintDefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeMemoryUnitConstraintDefTable table."
    INDEX { cucsComputeMemoryUnitConstraintDefInstanceId }
    ::= { cucsComputeMemoryUnitConstraintDefTable 1 }

CucsComputeMemoryUnitConstraintDefEntry ::= SEQUENCE {
    cucsComputeMemoryUnitConstraintDefInstanceId                     CucsManagedObjectId,
    cucsComputeMemoryUnitConstraintDefDn                             CucsManagedObjectDn,
    cucsComputeMemoryUnitConstraintDefRn                             SnmpAdminString,
    cucsComputeMemoryUnitConstraintDefDescr                          SnmpAdminString,
    cucsComputeMemoryUnitConstraintDefIntId                          SnmpAdminString,
    cucsComputeMemoryUnitConstraintDefName                           SnmpAdminString,
    cucsComputeMemoryUnitConstraintDefType                           CucsComputeMemoryUnitConstraintType,
    cucsComputeMemoryUnitConstraintDefRevisionModifier               SnmpAdminString,
    cucsComputeMemoryUnitConstraintDefPolicyLevel                    Gauge32,
    cucsComputeMemoryUnitConstraintDefPolicyOwner                    CucsPolicyPolicyOwner
}

cucsComputeMemoryUnitConstraintDefInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeMemoryUnitConstraintDefEntry 1 }

cucsComputeMemoryUnitConstraintDefDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:dn managed
        object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 2 }

cucsComputeMemoryUnitConstraintDefRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:rn managed
        object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 3 }

cucsComputeMemoryUnitConstraintDefDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:descr
        managed object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 4 }

cucsComputeMemoryUnitConstraintDefIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:intId
        managed object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 5 }

cucsComputeMemoryUnitConstraintDefName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:name
        managed object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 6 }

cucsComputeMemoryUnitConstraintDefType OBJECT-TYPE
    SYNTAX       CucsComputeMemoryUnitConstraintType
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:type
        managed object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 7 }

cucsComputeMemoryUnitConstraintDefRevisionModifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:revisionModifier
        managed object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 8 }

cucsComputeMemoryUnitConstraintDefPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:policyLevel
        managed object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 9 }

cucsComputeMemoryUnitConstraintDefPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:MemoryUnitConstraintDef:policyOwner
        managed object property"
    ::= { cucsComputeMemoryUnitConstraintDefEntry 10 }

cucsComputePCIeFatalCompletionStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePCIeFatalCompletionStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats managed object table"
    ::= { cucsComputeObjects 19 }

cucsComputePCIeFatalCompletionStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputePCIeFatalCompletionStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePCIeFatalCompletionStatsTable table."
    INDEX { cucsComputePCIeFatalCompletionStatsInstanceId }
    ::= { cucsComputePCIeFatalCompletionStatsTable 1 }

CucsComputePCIeFatalCompletionStatsEntry ::= SEQUENCE {
    cucsComputePCIeFatalCompletionStatsInstanceId                    CucsManagedObjectId,
    cucsComputePCIeFatalCompletionStatsDn                            CucsManagedObjectDn,
    cucsComputePCIeFatalCompletionStatsRn                            SnmpAdminString,
    cucsComputePCIeFatalCompletionStatsAbortErrors                   Counter32,
    cucsComputePCIeFatalCompletionStatsAbortErrors15Min              Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors15MinH             Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors1Day               Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors1DayH              Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors1Hour              Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors1HourH             Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors1Week              Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors1WeekH             Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors                 Counter32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors15Min            Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors15MinH           Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors1Day             Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors1DayH            Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors1Hour            Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors1HourH           Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors1Week            Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors1WeekH           Gauge32,
    cucsComputePCIeFatalCompletionStatsIntervals                     Gauge32,
    cucsComputePCIeFatalCompletionStatsSuspect                       TruthValue,
    cucsComputePCIeFatalCompletionStatsThresholded                   CucsComputePCIeFatalCompletionStatsThresholded,
    cucsComputePCIeFatalCompletionStatsTimeCollected                 DateAndTime,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors              Counter32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors15Min         Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors15MinH        Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors1Day          Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors1DayH         Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors1Hour         Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors1HourH        Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors1Week         Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors1WeekH        Gauge32,
    cucsComputePCIeFatalCompletionStatsUpdate                        Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors2Weeks             Gauge32,
    cucsComputePCIeFatalCompletionStatsAbortErrors2WeeksH            Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors2Weeks           Gauge32,
    cucsComputePCIeFatalCompletionStatsTimeoutErrors2WeeksH          Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors2Weeks        Gauge32,
    cucsComputePCIeFatalCompletionStatsUnexpectedErrors2WeeksH       Gauge32
}

cucsComputePCIeFatalCompletionStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePCIeFatalCompletionStatsEntry 1 }

cucsComputePCIeFatalCompletionStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:dn
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 2 }

cucsComputePCIeFatalCompletionStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:rn
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 3 }

cucsComputePCIeFatalCompletionStatsAbortErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 4 }

cucsComputePCIeFatalCompletionStatsAbortErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 5 }

cucsComputePCIeFatalCompletionStatsAbortErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 6 }

cucsComputePCIeFatalCompletionStatsAbortErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 7 }

cucsComputePCIeFatalCompletionStatsAbortErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 8 }

cucsComputePCIeFatalCompletionStatsAbortErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 9 }

cucsComputePCIeFatalCompletionStatsAbortErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 10 }

cucsComputePCIeFatalCompletionStatsAbortErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 11 }

cucsComputePCIeFatalCompletionStatsAbortErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 12 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 13 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 14 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 15 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 16 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 17 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 18 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 19 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 20 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 21 }

cucsComputePCIeFatalCompletionStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:intervals
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 22 }

cucsComputePCIeFatalCompletionStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:suspect
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 23 }

cucsComputePCIeFatalCompletionStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputePCIeFatalCompletionStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:thresholded
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 24 }

cucsComputePCIeFatalCompletionStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:timeCollected
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 25 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 26 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 27 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors15MinH
         managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 28 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 29 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 30 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 31 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors1HourH
         managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 32 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 33 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors1WeekH
         managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 34 }

cucsComputePCIeFatalCompletionStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:update
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 35 }

cucsComputePCIeFatalCompletionStatsAbortErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 36 }

cucsComputePCIeFatalCompletionStatsAbortErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:AbortErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 37 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 38 }

cucsComputePCIeFatalCompletionStatsTimeoutErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:TimeoutErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 39 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors2Weeks
         managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 40 }

cucsComputePCIeFatalCompletionStatsUnexpectedErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalCompletionStats:unexpectedErrors2Weeks
        H managed object property"
    ::= { cucsComputePCIeFatalCompletionStatsEntry 41 }

cucsComputePCIeFatalProtocolStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePCIeFatalProtocolStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats managed object table"
    ::= { cucsComputeObjects 20 }

cucsComputePCIeFatalProtocolStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputePCIeFatalProtocolStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePCIeFatalProtocolStatsTable table."
    INDEX { cucsComputePCIeFatalProtocolStatsInstanceId }
    ::= { cucsComputePCIeFatalProtocolStatsTable 1 }

CucsComputePCIeFatalProtocolStatsEntry ::= SEQUENCE {
    cucsComputePCIeFatalProtocolStatsInstanceId                      CucsManagedObjectId,
    cucsComputePCIeFatalProtocolStatsDn                              CucsManagedObjectDn,
    cucsComputePCIeFatalProtocolStatsRn                              SnmpAdminString,
    cucsComputePCIeFatalProtocolStatsDllpErrors                      Counter32,
    cucsComputePCIeFatalProtocolStatsDllpErrors15Min                 Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors15MinH                Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors1Day                  Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors1DayH                 Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors1Hour                 Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors1HourH                Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors1Week                 Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors1WeekH                Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors               Counter32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors15Min          Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors15MinH         Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors1Day           Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors1DayH          Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors1Hour          Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors1HourH         Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors1Week          Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors1WeekH         Gauge32,
    cucsComputePCIeFatalProtocolStatsIntervals                       Gauge32,
    cucsComputePCIeFatalProtocolStatsSuspect                         TruthValue,
    cucsComputePCIeFatalProtocolStatsThresholded                     CucsComputePCIeFatalProtocolStatsThresholded,
    cucsComputePCIeFatalProtocolStatsTimeCollected                   DateAndTime,
    cucsComputePCIeFatalProtocolStatsUpdate                          Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors2Weeks                Gauge32,
    cucsComputePCIeFatalProtocolStatsDllpErrors2WeeksH               Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors2Weeks         Gauge32,
    cucsComputePCIeFatalProtocolStatsFlowControlErrors2WeeksH        Gauge32
}

cucsComputePCIeFatalProtocolStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePCIeFatalProtocolStatsEntry 1 }

cucsComputePCIeFatalProtocolStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dn managed
        object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 2 }

cucsComputePCIeFatalProtocolStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:rn managed
        object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 3 }

cucsComputePCIeFatalProtocolStatsDllpErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 4 }

cucsComputePCIeFatalProtocolStatsDllpErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 5 }

cucsComputePCIeFatalProtocolStatsDllpErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 6 }

cucsComputePCIeFatalProtocolStatsDllpErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 7 }

cucsComputePCIeFatalProtocolStatsDllpErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 8 }

cucsComputePCIeFatalProtocolStatsDllpErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 9 }

cucsComputePCIeFatalProtocolStatsDllpErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 10 }

cucsComputePCIeFatalProtocolStatsDllpErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 11 }

cucsComputePCIeFatalProtocolStatsDllpErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 12 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 13 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 14 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 15 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 16 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 17 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 18 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 19 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 20 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 21 }

cucsComputePCIeFatalProtocolStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:intervals
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 22 }

cucsComputePCIeFatalProtocolStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:suspect
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 23 }

cucsComputePCIeFatalProtocolStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputePCIeFatalProtocolStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:thresholded
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 24 }

cucsComputePCIeFatalProtocolStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:timeCollected
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 25 }

cucsComputePCIeFatalProtocolStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:update
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 26 }

cucsComputePCIeFatalProtocolStatsDllpErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 27 }

cucsComputePCIeFatalProtocolStatsDllpErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:dllpErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 28 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 29 }

cucsComputePCIeFatalProtocolStatsFlowControlErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalProtocolStats:flowControlErrors2WeeksH
         managed object property"
    ::= { cucsComputePCIeFatalProtocolStatsEntry 30 }

cucsComputePCIeFatalReceiveStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePCIeFatalReceiveStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats managed object table"
    ::= { cucsComputeObjects 21 }

cucsComputePCIeFatalReceiveStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputePCIeFatalReceiveStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePCIeFatalReceiveStatsTable table."
    INDEX { cucsComputePCIeFatalReceiveStatsInstanceId }
    ::= { cucsComputePCIeFatalReceiveStatsTable 1 }

CucsComputePCIeFatalReceiveStatsEntry ::= SEQUENCE {
    cucsComputePCIeFatalReceiveStatsInstanceId                       CucsManagedObjectId,
    cucsComputePCIeFatalReceiveStatsDn                               CucsManagedObjectDn,
    cucsComputePCIeFatalReceiveStatsRn                               SnmpAdminString,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors             Counter32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors15Min        Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors15MinH       Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1Day         Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1DayH        Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1Hour        Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1HourH       Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1Week        Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1WeekH       Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors                   Counter32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors15Min              Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors15MinH             Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors1Day               Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors1DayH              Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors1Hour              Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors1HourH             Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors1Week              Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors1WeekH             Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors                Counter32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors15Min           Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors15MinH          Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1Day            Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1DayH           Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1Hour           Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1HourH          Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1Week           Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1WeekH          Gauge32,
    cucsComputePCIeFatalReceiveStatsIntervals                        Gauge32,
    cucsComputePCIeFatalReceiveStatsSuspect                          TruthValue,
    cucsComputePCIeFatalReceiveStatsThresholded                      CucsComputePCIeFatalReceiveStatsThresholded,
    cucsComputePCIeFatalReceiveStatsTimeCollected                    DateAndTime,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors         Counter32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors15Min    Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors15MinH   Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1Day     Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1DayH    Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1Hour    Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1HourH   Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1Week    Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1WeekH   Gauge32,
    cucsComputePCIeFatalReceiveStatsUpdate                           Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors2Weeks       Gauge32,
    cucsComputePCIeFatalReceiveStatsBufferOverflowErrors2WeeksH      Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors2Weeks             Gauge32,
    cucsComputePCIeFatalReceiveStatsErrFatalErrors2WeeksH            Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors2Weeks          Gauge32,
    cucsComputePCIeFatalReceiveStatsErrNonFatalErrors2WeeksH         Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors2Weeks   Gauge32,
    cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors2WeeksH  Gauge32
}

cucsComputePCIeFatalReceiveStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePCIeFatalReceiveStatsEntry 1 }

cucsComputePCIeFatalReceiveStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:dn managed
        object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 2 }

cucsComputePCIeFatalReceiveStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:rn managed
        object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 3 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 4 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors15Min
         managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 5 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors15Min
        H managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 6 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 7 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors1DayH
         managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 8 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors1Hour
         managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 9 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors1Hour
        H managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 10 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors1Week
         managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 11 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors1Week
        H managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 12 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 13 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 14 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 15 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 16 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 17 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 18 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 19 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 20 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 21 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 22 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 23 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 24 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 25 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 26 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 27 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 28 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 29 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 30 }

cucsComputePCIeFatalReceiveStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:intervals
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 31 }

cucsComputePCIeFatalReceiveStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:suspect
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 32 }

cucsComputePCIeFatalReceiveStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputePCIeFatalReceiveStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:thresholded
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 33 }

cucsComputePCIeFatalReceiveStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:timeCollected
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 34 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 35 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        5Min managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 36 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        5MinH managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 37 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        Day managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 38 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        DayH managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 39 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        Hour managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 40 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        HourH managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 41 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        Week managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 42 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors1
        WeekH managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 43 }

cucsComputePCIeFatalReceiveStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:update
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 44 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors2Week
        s managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 45 }

cucsComputePCIeFatalReceiveStatsBufferOverflowErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:bufferOverflowErrors2Week
        sH managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 46 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 47 }

cucsComputePCIeFatalReceiveStatsErrFatalErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errFatalErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 48 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 49 }

cucsComputePCIeFatalReceiveStatsErrNonFatalErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:errNonFatalErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 50 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors2
        Weeks managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 51 }

cucsComputePCIeFatalReceiveStatsUnsupportedRequestErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalReceiveStats:unsupportedRequestErrors2
        WeeksH managed object property"
    ::= { cucsComputePCIeFatalReceiveStatsEntry 52 }

cucsComputePCIeFatalStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePCIeFatalStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats managed object table"
    ::= { cucsComputeObjects 22 }

cucsComputePCIeFatalStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputePCIeFatalStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePCIeFatalStatsTable table."
    INDEX { cucsComputePCIeFatalStatsInstanceId }
    ::= { cucsComputePCIeFatalStatsTable 1 }

CucsComputePCIeFatalStatsEntry ::= SEQUENCE {
    cucsComputePCIeFatalStatsInstanceId                              CucsManagedObjectId,
    cucsComputePCIeFatalStatsDn                                      CucsManagedObjectDn,
    cucsComputePCIeFatalStatsRn                                      SnmpAdminString,
    cucsComputePCIeFatalStatsAcsViolationErrors                      Counter32,
    cucsComputePCIeFatalStatsAcsViolationErrors15Min                 Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors15MinH                Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors1Day                  Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors1DayH                 Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors1Hour                 Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors1HourH                Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors1Week                 Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors1WeekH                Gauge32,
    cucsComputePCIeFatalStatsIntervals                               Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors                      Counter32,
    cucsComputePCIeFatalStatsMalformedTLPErrors15Min                 Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors15MinH                Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors1Day                  Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors1DayH                 Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors1Hour                 Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors1HourH                Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors1Week                 Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors1WeekH                Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors                       Counter32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors15Min                  Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors15MinH                 Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors1Day                   Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors1DayH                  Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors1Hour                  Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors1HourH                 Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors1Week                  Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors1WeekH                 Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors                  Counter32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors15Min             Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors15MinH            Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors1Day              Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors1DayH             Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors1Hour             Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors1HourH            Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors1Week             Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors1WeekH            Gauge32,
    cucsComputePCIeFatalStatsSuspect                                 TruthValue,
    cucsComputePCIeFatalStatsThresholded                             CucsComputePCIeFatalStatsThresholded,
    cucsComputePCIeFatalStatsTimeCollected                           DateAndTime,
    cucsComputePCIeFatalStatsUpdate                                  Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors2Weeks                Gauge32,
    cucsComputePCIeFatalStatsAcsViolationErrors2WeeksH               Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors2Weeks                Gauge32,
    cucsComputePCIeFatalStatsMalformedTLPErrors2WeeksH               Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors2Weeks                 Gauge32,
    cucsComputePCIeFatalStatsPoisonedTLPErrors2WeeksH                Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors2Weeks            Gauge32,
    cucsComputePCIeFatalStatsSurpriseLinkDownErrors2WeeksH           Gauge32
}

cucsComputePCIeFatalStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePCIeFatalStatsEntry 1 }

cucsComputePCIeFatalStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:dn managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 2 }

cucsComputePCIeFatalStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:rn managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 3 }

cucsComputePCIeFatalStatsAcsViolationErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 4 }

cucsComputePCIeFatalStatsAcsViolationErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 5 }

cucsComputePCIeFatalStatsAcsViolationErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 6 }

cucsComputePCIeFatalStatsAcsViolationErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 7 }

cucsComputePCIeFatalStatsAcsViolationErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 8 }

cucsComputePCIeFatalStatsAcsViolationErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 9 }

cucsComputePCIeFatalStatsAcsViolationErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 10 }

cucsComputePCIeFatalStatsAcsViolationErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 11 }

cucsComputePCIeFatalStatsAcsViolationErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 12 }

cucsComputePCIeFatalStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:intervals managed
        object property"
    ::= { cucsComputePCIeFatalStatsEntry 13 }

cucsComputePCIeFatalStatsMalformedTLPErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 14 }

cucsComputePCIeFatalStatsMalformedTLPErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 15 }

cucsComputePCIeFatalStatsMalformedTLPErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 16 }

cucsComputePCIeFatalStatsMalformedTLPErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 17 }

cucsComputePCIeFatalStatsMalformedTLPErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 18 }

cucsComputePCIeFatalStatsMalformedTLPErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 19 }

cucsComputePCIeFatalStatsMalformedTLPErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 20 }

cucsComputePCIeFatalStatsMalformedTLPErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 21 }

cucsComputePCIeFatalStatsMalformedTLPErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 22 }

cucsComputePCIeFatalStatsPoisonedTLPErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 23 }

cucsComputePCIeFatalStatsPoisonedTLPErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 24 }

cucsComputePCIeFatalStatsPoisonedTLPErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 25 }

cucsComputePCIeFatalStatsPoisonedTLPErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 26 }

cucsComputePCIeFatalStatsPoisonedTLPErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 27 }

cucsComputePCIeFatalStatsPoisonedTLPErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 28 }

cucsComputePCIeFatalStatsPoisonedTLPErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 29 }

cucsComputePCIeFatalStatsPoisonedTLPErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 30 }

cucsComputePCIeFatalStatsPoisonedTLPErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 31 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors OBJECT-TYPE
    SYNTAX       Counter32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 32 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors15Min OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors15Min
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 33 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors15MinH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors15MinH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 34 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors1Day OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors1Day
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 35 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors1DayH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors1DayH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 36 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors1Hour OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors1Hour
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 37 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors1HourH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors1HourH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 38 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors1Week OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors1Week
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 39 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors1WeekH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors1WeekH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 40 }

cucsComputePCIeFatalStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:suspect managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 41 }

cucsComputePCIeFatalStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputePCIeFatalStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:thresholded managed
        object property"
    ::= { cucsComputePCIeFatalStatsEntry 42 }

cucsComputePCIeFatalStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:timeCollected
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 43 }

cucsComputePCIeFatalStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:update managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 44 }

cucsComputePCIeFatalStatsAcsViolationErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 45 }

cucsComputePCIeFatalStatsAcsViolationErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:acsViolationErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 46 }

cucsComputePCIeFatalStatsMalformedTLPErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 47 }

cucsComputePCIeFatalStatsMalformedTLPErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:malformedTLPErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 48 }

cucsComputePCIeFatalStatsPoisonedTLPErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 49 }

cucsComputePCIeFatalStatsPoisonedTLPErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:poisonedTLPErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 50 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors2Weeks OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors2Weeks
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 51 }

cucsComputePCIeFatalStatsSurpriseLinkDownErrors2WeeksH OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PCIeFatalStats:surpriseLinkDownErrors2WeeksH
        managed object property"
    ::= { cucsComputePCIeFatalStatsEntry 52 }

cucsComputePciCapTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePciCapEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PciCap managed object table"
    ::= { cucsComputeObjects 23 }

cucsComputePciCapEntry OBJECT-TYPE
    SYNTAX           CucsComputePciCapEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePciCapTable table."
    INDEX { cucsComputePciCapInstanceId }
    ::= { cucsComputePciCapTable 1 }

CucsComputePciCapEntry ::= SEQUENCE {
    cucsComputePciCapInstanceId                                      CucsManagedObjectId,
    cucsComputePciCapDn                                              CucsManagedObjectDn,
    cucsComputePciCapRn                                              SnmpAdminString,
    cucsComputePciCapNumOfPhysSlots                                  Gauge32,
    cucsComputePciCapOrder                                           CucsComputePciCapOrder,
    cucsComputePciCapStartsWith                                      Gauge32,
    cucsComputePciCapMaxBusIdPerSlot                                 Gauge32
}

cucsComputePciCapInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePciCapEntry 1 }

cucsComputePciCapDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciCap:dn managed object property"
    ::= { cucsComputePciCapEntry 2 }

cucsComputePciCapRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciCap:rn managed object property"
    ::= { cucsComputePciCapEntry 3 }

cucsComputePciCapNumOfPhysSlots OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciCap:numOfPhysSlots managed object property"
    ::= { cucsComputePciCapEntry 4 }

cucsComputePciCapOrder OBJECT-TYPE
    SYNTAX       CucsComputePciCapOrder
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciCap:order managed object property"
    ::= { cucsComputePciCapEntry 5 }

cucsComputePciCapStartsWith OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciCap:startsWith managed object property"
    ::= { cucsComputePciCapEntry 6 }

cucsComputePciCapMaxBusIdPerSlot OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciCap:maxBusIdPerSlot managed object property"
    ::= { cucsComputePciCapEntry 7 }

cucsComputePciSlotScanDefTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePciSlotScanDefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef managed object table"
    ::= { cucsComputeObjects 58 }

cucsComputePciSlotScanDefEntry OBJECT-TYPE
    SYNTAX           CucsComputePciSlotScanDefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePciSlotScanDefTable table."
    INDEX { cucsComputePciSlotScanDefInstanceId }
    ::= { cucsComputePciSlotScanDefTable 1 }

CucsComputePciSlotScanDefEntry ::= SEQUENCE {
    cucsComputePciSlotScanDefInstanceId                              CucsManagedObjectId,
    cucsComputePciSlotScanDefDn                                      CucsManagedObjectDn,
    cucsComputePciSlotScanDefRn                                      SnmpAdminString,
    cucsComputePciSlotScanDefDescr                                   SnmpAdminString,
    cucsComputePciSlotScanDefIntId                                   SnmpAdminString,
    cucsComputePciSlotScanDefName                                    SnmpAdminString,
    cucsComputePciSlotScanDefPolicyLevel                             Gauge32,
    cucsComputePciSlotScanDefPolicyOwner                             CucsPolicyPolicyOwner,
    cucsComputePciSlotScanDefScanOrder                               Gauge32,
    cucsComputePciSlotScanDefSlotId                                  Gauge32
}

cucsComputePciSlotScanDefInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePciSlotScanDefEntry 1 }

cucsComputePciSlotScanDefDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:dn managed object property"
    ::= { cucsComputePciSlotScanDefEntry 2 }

cucsComputePciSlotScanDefRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:rn managed object property"
    ::= { cucsComputePciSlotScanDefEntry 3 }

cucsComputePciSlotScanDefDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:descr managed object property"
    ::= { cucsComputePciSlotScanDefEntry 4 }

cucsComputePciSlotScanDefIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:intId managed object property"
    ::= { cucsComputePciSlotScanDefEntry 5 }

cucsComputePciSlotScanDefName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:name managed object property"
    ::= { cucsComputePciSlotScanDefEntry 6 }

cucsComputePciSlotScanDefPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:policyLevel managed
        object property"
    ::= { cucsComputePciSlotScanDefEntry 7 }

cucsComputePciSlotScanDefPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:policyOwner managed
        object property"
    ::= { cucsComputePciSlotScanDefEntry 8 }

cucsComputePciSlotScanDefScanOrder OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:scanOrder managed
        object property"
    ::= { cucsComputePciSlotScanDefEntry 9 }

cucsComputePciSlotScanDefSlotId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PciSlotScanDef:slotId managed object property"
    ::= { cucsComputePciSlotScanDefEntry 10 }

cucsComputePhysicalAssocCtxTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePhysicalAssocCtxEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PhysicalAssocCtx managed object table"
    ::= { cucsComputeObjects 41 }

cucsComputePhysicalAssocCtxEntry OBJECT-TYPE
    SYNTAX           CucsComputePhysicalAssocCtxEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePhysicalAssocCtxTable table."
    INDEX { cucsComputePhysicalAssocCtxInstanceId }
    ::= { cucsComputePhysicalAssocCtxTable 1 }

CucsComputePhysicalAssocCtxEntry ::= SEQUENCE {
    cucsComputePhysicalAssocCtxInstanceId                            CucsManagedObjectId,
    cucsComputePhysicalAssocCtxDn                                    CucsManagedObjectDn,
    cucsComputePhysicalAssocCtxRn                                    SnmpAdminString,
    cucsComputePhysicalAssocCtxFruCapDn                              SnmpAdminString
}

cucsComputePhysicalAssocCtxInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePhysicalAssocCtxEntry 1 }

cucsComputePhysicalAssocCtxDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalAssocCtx:dn managed object property"
    ::= { cucsComputePhysicalAssocCtxEntry 2 }

cucsComputePhysicalAssocCtxRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalAssocCtx:rn managed object property"
    ::= { cucsComputePhysicalAssocCtxEntry 3 }

cucsComputePhysicalAssocCtxFruCapDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalAssocCtx:fruCapDn managed
        object property"
    ::= { cucsComputePhysicalAssocCtxEntry 4 }

cucsComputePhysicalFsmTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePhysicalFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm managed object table"
    ::= { cucsComputeObjects 50 }

cucsComputePhysicalFsmEntry OBJECT-TYPE
    SYNTAX           CucsComputePhysicalFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePhysicalFsmTable table."
    INDEX { cucsComputePhysicalFsmInstanceId }
    ::= { cucsComputePhysicalFsmTable 1 }

CucsComputePhysicalFsmEntry ::= SEQUENCE {
    cucsComputePhysicalFsmInstanceId                                 CucsManagedObjectId,
    cucsComputePhysicalFsmDn                                         CucsManagedObjectDn,
    cucsComputePhysicalFsmRn                                         SnmpAdminString,
    cucsComputePhysicalFsmCompletionTime                             DateAndTime,
    cucsComputePhysicalFsmCurrentFsm                                 CucsComputePhysicalFsmCurrentFsm,
    cucsComputePhysicalFsmDescr                                      SnmpAdminString,
    cucsComputePhysicalFsmFsmStatus                                  CucsFsmFsmStageStatus,
    cucsComputePhysicalFsmProgress                                   Gauge32,
    cucsComputePhysicalFsmRmtErrCode                                 Gauge32,
    cucsComputePhysicalFsmRmtErrDescr                                SnmpAdminString,
    cucsComputePhysicalFsmRmtRslt                                    CucsConditionRemoteInvRslt
}

cucsComputePhysicalFsmInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePhysicalFsmEntry 1 }

cucsComputePhysicalFsmDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:dn managed object property"
    ::= { cucsComputePhysicalFsmEntry 2 }

cucsComputePhysicalFsmRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:rn managed object property"
    ::= { cucsComputePhysicalFsmEntry 3 }

cucsComputePhysicalFsmCompletionTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:completionTime managed
        object property"
    ::= { cucsComputePhysicalFsmEntry 4 }

cucsComputePhysicalFsmCurrentFsm OBJECT-TYPE
    SYNTAX       CucsComputePhysicalFsmCurrentFsm
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:currentFsm managed object property"
    ::= { cucsComputePhysicalFsmEntry 5 }

cucsComputePhysicalFsmDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:descr managed object property"
    ::= { cucsComputePhysicalFsmEntry 6 }

cucsComputePhysicalFsmFsmStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:fsmStatus managed object property"
    ::= { cucsComputePhysicalFsmEntry 7 }

cucsComputePhysicalFsmProgress OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:progress managed object property"
    ::= { cucsComputePhysicalFsmEntry 8 }

cucsComputePhysicalFsmRmtErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:rmtErrCode managed object property"
    ::= { cucsComputePhysicalFsmEntry 9 }

cucsComputePhysicalFsmRmtErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:rmtErrDescr managed
        object property"
    ::= { cucsComputePhysicalFsmEntry 10 }

cucsComputePhysicalFsmRmtRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsm:rmtRslt managed object property"
    ::= { cucsComputePhysicalFsmEntry 11 }

cucsComputePhysicalFsmStageTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePhysicalFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage managed object table"
    ::= { cucsComputeObjects 51 }

cucsComputePhysicalFsmStageEntry OBJECT-TYPE
    SYNTAX           CucsComputePhysicalFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePhysicalFsmStageTable table."
    INDEX { cucsComputePhysicalFsmStageInstanceId }
    ::= { cucsComputePhysicalFsmStageTable 1 }

CucsComputePhysicalFsmStageEntry ::= SEQUENCE {
    cucsComputePhysicalFsmStageInstanceId                            CucsManagedObjectId,
    cucsComputePhysicalFsmStageDn                                    CucsManagedObjectDn,
    cucsComputePhysicalFsmStageRn                                    SnmpAdminString,
    cucsComputePhysicalFsmStageDescr                                 SnmpAdminString,
    cucsComputePhysicalFsmStageLastUpdateTime                        DateAndTime,
    cucsComputePhysicalFsmStageName                                  CucsComputePhysicalFsmStageName,
    cucsComputePhysicalFsmStageOrder                                 Gauge32,
    cucsComputePhysicalFsmStageRetry                                 Gauge32,
    cucsComputePhysicalFsmStageStageStatus                           CucsFsmFsmStageStatus
}

cucsComputePhysicalFsmStageInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePhysicalFsmStageEntry 1 }

cucsComputePhysicalFsmStageDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:dn managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 2 }

cucsComputePhysicalFsmStageRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:rn managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 3 }

cucsComputePhysicalFsmStageDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:descr managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 4 }

cucsComputePhysicalFsmStageLastUpdateTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:lastUpdateTime
        managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 5 }

cucsComputePhysicalFsmStageName OBJECT-TYPE
    SYNTAX       CucsComputePhysicalFsmStageName
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:name managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 6 }

cucsComputePhysicalFsmStageOrder OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:order managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 7 }

cucsComputePhysicalFsmStageRetry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:retry managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 8 }

cucsComputePhysicalFsmStageStageStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmStage:stageStatus
        managed object property"
    ::= { cucsComputePhysicalFsmStageEntry 9 }

cucsComputePhysicalFsmTaskTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePhysicalFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmTask managed object table"
    ::= { cucsComputeObjects 24 }

cucsComputePhysicalFsmTaskEntry OBJECT-TYPE
    SYNTAX           CucsComputePhysicalFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePhysicalFsmTaskTable table."
    INDEX { cucsComputePhysicalFsmTaskInstanceId }
    ::= { cucsComputePhysicalFsmTaskTable 1 }

CucsComputePhysicalFsmTaskEntry ::= SEQUENCE {
    cucsComputePhysicalFsmTaskInstanceId                             CucsManagedObjectId,
    cucsComputePhysicalFsmTaskDn                                     CucsManagedObjectDn,
    cucsComputePhysicalFsmTaskRn                                     SnmpAdminString,
    cucsComputePhysicalFsmTaskCompletion                             CucsFsmCompletion,
    cucsComputePhysicalFsmTaskFlags                                  CucsComputePhysicalFsmTaskFlags,
    cucsComputePhysicalFsmTaskItem                                   CucsComputePhysicalFsmTaskItem,
    cucsComputePhysicalFsmTaskSeqId                                  Gauge32
}

cucsComputePhysicalFsmTaskInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePhysicalFsmTaskEntry 1 }

cucsComputePhysicalFsmTaskDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmTask:dn managed object property"
    ::= { cucsComputePhysicalFsmTaskEntry 2 }

cucsComputePhysicalFsmTaskRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmTask:rn managed object property"
    ::= { cucsComputePhysicalFsmTaskEntry 3 }

cucsComputePhysicalFsmTaskCompletion OBJECT-TYPE
    SYNTAX       CucsFsmCompletion
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmTask:completion managed
        object property"
    ::= { cucsComputePhysicalFsmTaskEntry 4 }

cucsComputePhysicalFsmTaskFlags OBJECT-TYPE
    SYNTAX       CucsComputePhysicalFsmTaskFlags
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmTask:flags managed object property"
    ::= { cucsComputePhysicalFsmTaskEntry 5 }

cucsComputePhysicalFsmTaskItem OBJECT-TYPE
    SYNTAX       CucsComputePhysicalFsmTaskItem
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmTask:item managed object property"
    ::= { cucsComputePhysicalFsmTaskEntry 6 }

cucsComputePhysicalFsmTaskSeqId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalFsmTask:seqId managed object property"
    ::= { cucsComputePhysicalFsmTaskEntry 7 }

cucsComputePhysicalQualTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePhysicalQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PhysicalQual managed object table"
    ::= { cucsComputeObjects 25 }

cucsComputePhysicalQualEntry OBJECT-TYPE
    SYNTAX           CucsComputePhysicalQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePhysicalQualTable table."
    INDEX { cucsComputePhysicalQualInstanceId }
    ::= { cucsComputePhysicalQualTable 1 }

CucsComputePhysicalQualEntry ::= SEQUENCE {
    cucsComputePhysicalQualInstanceId                                CucsManagedObjectId,
    cucsComputePhysicalQualDn                                        CucsManagedObjectDn,
    cucsComputePhysicalQualRn                                        SnmpAdminString,
    cucsComputePhysicalQualModel                                     SnmpAdminString,
    cucsComputePhysicalQualPropAcl                                   Unsigned64
}

cucsComputePhysicalQualInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePhysicalQualEntry 1 }

cucsComputePhysicalQualDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalQual:dn managed object property"
    ::= { cucsComputePhysicalQualEntry 2 }

cucsComputePhysicalQualRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalQual:rn managed object property"
    ::= { cucsComputePhysicalQualEntry 3 }

cucsComputePhysicalQualModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalQual:model managed object property"
    ::= { cucsComputePhysicalQualEntry 4 }

cucsComputePhysicalQualPropAcl OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PhysicalQual:propAcl managed object property"
    ::= { cucsComputePhysicalQualEntry 5 }

cucsComputePlatformTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePlatformEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Platform managed object table"
    ::= { cucsComputeObjects 26 }

cucsComputePlatformEntry OBJECT-TYPE
    SYNTAX           CucsComputePlatformEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePlatformTable table."
    INDEX { cucsComputePlatformInstanceId }
    ::= { cucsComputePlatformTable 1 }

CucsComputePlatformEntry ::= SEQUENCE {
    cucsComputePlatformInstanceId                                    CucsManagedObjectId,
    cucsComputePlatformDn                                            CucsManagedObjectDn,
    cucsComputePlatformRn                                            SnmpAdminString,
    cucsComputePlatformModel                                         SnmpAdminString,
    cucsComputePlatformRevision                                      SnmpAdminString,
    cucsComputePlatformVendor                                        SnmpAdminString,
    cucsComputePlatformProductName                                   SnmpAdminString,
    cucsComputePlatformPropAcl                                       Unsigned64
}

cucsComputePlatformInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePlatformEntry 1 }

cucsComputePlatformDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Platform:dn managed object property"
    ::= { cucsComputePlatformEntry 2 }

cucsComputePlatformRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Platform:rn managed object property"
    ::= { cucsComputePlatformEntry 3 }

cucsComputePlatformModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Platform:model managed object property"
    ::= { cucsComputePlatformEntry 4 }

cucsComputePlatformRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Platform:revision managed object property"
    ::= { cucsComputePlatformEntry 5 }

cucsComputePlatformVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Platform:vendor managed object property"
    ::= { cucsComputePlatformEntry 6 }

cucsComputePlatformProductName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Platform:productName managed object property"
    ::= { cucsComputePlatformEntry 7 }

cucsComputePlatformPropAcl OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Platform:propAcl managed object property"
    ::= { cucsComputePlatformEntry 8 }

cucsComputePnuOSImageTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePnuOSImageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PnuOSImage managed object table"
    ::= { cucsComputeObjects 47 }

cucsComputePnuOSImageEntry OBJECT-TYPE
    SYNTAX           CucsComputePnuOSImageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePnuOSImageTable table."
    INDEX { cucsComputePnuOSImageInstanceId }
    ::= { cucsComputePnuOSImageTable 1 }

CucsComputePnuOSImageEntry ::= SEQUENCE {
    cucsComputePnuOSImageInstanceId                                  CucsManagedObjectId,
    cucsComputePnuOSImageDn                                          CucsManagedObjectDn,
    cucsComputePnuOSImageRn                                          SnmpAdminString,
    cucsComputePnuOSImageImgLoc                                      SnmpAdminString,
    cucsComputePnuOSImageImgName                                     SnmpAdminString
}

cucsComputePnuOSImageInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePnuOSImageEntry 1 }

cucsComputePnuOSImageDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PnuOSImage:dn managed object property"
    ::= { cucsComputePnuOSImageEntry 2 }

cucsComputePnuOSImageRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PnuOSImage:rn managed object property"
    ::= { cucsComputePnuOSImageEntry 3 }

cucsComputePnuOSImageImgLoc OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PnuOSImage:imgLoc managed object property"
    ::= { cucsComputePnuOSImageEntry 4 }

cucsComputePnuOSImageImgName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PnuOSImage:imgName managed object property"
    ::= { cucsComputePnuOSImageEntry 5 }

cucsComputePoolTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePoolEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Pool managed object table"
    ::= { cucsComputeObjects 27 }

cucsComputePoolEntry OBJECT-TYPE
    SYNTAX           CucsComputePoolEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePoolTable table."
    INDEX { cucsComputePoolInstanceId }
    ::= { cucsComputePoolTable 1 }

CucsComputePoolEntry ::= SEQUENCE {
    cucsComputePoolInstanceId                                        CucsManagedObjectId,
    cucsComputePoolDn                                                CucsManagedObjectDn,
    cucsComputePoolRn                                                SnmpAdminString,
    cucsComputePoolAssigned                                          Gauge32,
    cucsComputePoolDescr                                             SnmpAdminString,
    cucsComputePoolIntId                                             SnmpAdminString,
    cucsComputePoolName                                              SnmpAdminString,
    cucsComputePoolSize                                              Gauge32,
    cucsComputePoolAssignmentOrder                                   CucsPoolPoolAssignmentOrder,
    cucsComputePoolPolicyLevel                                       Gauge32,
    cucsComputePoolPolicyOwner                                       CucsPolicyPolicyOwner
}

cucsComputePoolInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePoolEntry 1 }

cucsComputePoolDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:dn managed object property"
    ::= { cucsComputePoolEntry 2 }

cucsComputePoolRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:rn managed object property"
    ::= { cucsComputePoolEntry 3 }

cucsComputePoolAssigned OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:assigned managed object property"
    ::= { cucsComputePoolEntry 4 }

cucsComputePoolDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:descr managed object property"
    ::= { cucsComputePoolEntry 5 }

cucsComputePoolIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:intId managed object property"
    ::= { cucsComputePoolEntry 6 }

cucsComputePoolName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:name managed object property"
    ::= { cucsComputePoolEntry 7 }

cucsComputePoolSize OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:size managed object property"
    ::= { cucsComputePoolEntry 8 }

cucsComputePoolAssignmentOrder OBJECT-TYPE
    SYNTAX       CucsPoolPoolAssignmentOrder
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:assignmentOrder managed object property"
    ::= { cucsComputePoolEntry 9 }

cucsComputePoolPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:policyLevel managed object property"
    ::= { cucsComputePoolEntry 10 }

cucsComputePoolPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Pool:policyOwner managed object property"
    ::= { cucsComputePoolEntry 11 }

cucsComputePoolPolicyRefTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePoolPolicyRefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PoolPolicyRef managed object table"
    ::= { cucsComputeObjects 42 }

cucsComputePoolPolicyRefEntry OBJECT-TYPE
    SYNTAX           CucsComputePoolPolicyRefEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePoolPolicyRefTable table."
    INDEX { cucsComputePoolPolicyRefInstanceId }
    ::= { cucsComputePoolPolicyRefTable 1 }

CucsComputePoolPolicyRefEntry ::= SEQUENCE {
    cucsComputePoolPolicyRefInstanceId                               CucsManagedObjectId,
    cucsComputePoolPolicyRefDn                                       CucsManagedObjectDn,
    cucsComputePoolPolicyRefRn                                       SnmpAdminString,
    cucsComputePoolPolicyRefId                                       Unsigned64,
    cucsComputePoolPolicyRefPolicyDn                                 SnmpAdminString
}

cucsComputePoolPolicyRefInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePoolPolicyRefEntry 1 }

cucsComputePoolPolicyRefDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolPolicyRef:dn managed object property"
    ::= { cucsComputePoolPolicyRefEntry 2 }

cucsComputePoolPolicyRefRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolPolicyRef:rn managed object property"
    ::= { cucsComputePoolPolicyRefEntry 3 }

cucsComputePoolPolicyRefId OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolPolicyRef:id managed object property"
    ::= { cucsComputePoolPolicyRefEntry 4 }

cucsComputePoolPolicyRefPolicyDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolPolicyRef:policyDn managed object property"
    ::= { cucsComputePoolPolicyRefEntry 5 }

cucsComputePoolableTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePoolableEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Poolable managed object table"
    ::= { cucsComputeObjects 28 }

cucsComputePoolableEntry OBJECT-TYPE
    SYNTAX           CucsComputePoolableEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePoolableTable table."
    INDEX { cucsComputePoolableInstanceId }
    ::= { cucsComputePoolableTable 1 }

CucsComputePoolableEntry ::= SEQUENCE {
    cucsComputePoolableInstanceId                                    CucsManagedObjectId,
    cucsComputePoolableDn                                            CucsManagedObjectDn,
    cucsComputePoolableRn                                            SnmpAdminString,
    cucsComputePoolableId                                            Unsigned64,
    cucsComputePoolablePoolDn                                        SnmpAdminString
}

cucsComputePoolableInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePoolableEntry 1 }

cucsComputePoolableDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Poolable:dn managed object property"
    ::= { cucsComputePoolableEntry 2 }

cucsComputePoolableRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Poolable:rn managed object property"
    ::= { cucsComputePoolableEntry 3 }

cucsComputePoolableId OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Poolable:id managed object property"
    ::= { cucsComputePoolableEntry 4 }

cucsComputePoolablePoolDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Poolable:poolDn managed object property"
    ::= { cucsComputePoolableEntry 5 }

cucsComputePooledEnclosureComputeSlotTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePooledEnclosureComputeSlotEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot managed
        object table"
    ::= { cucsComputeObjects 73 }

cucsComputePooledEnclosureComputeSlotEntry OBJECT-TYPE
    SYNTAX           CucsComputePooledEnclosureComputeSlotEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePooledEnclosureComputeSlotTable table."
    INDEX { cucsComputePooledEnclosureComputeSlotInstanceId }
    ::= { cucsComputePooledEnclosureComputeSlotTable 1 }

CucsComputePooledEnclosureComputeSlotEntry ::= SEQUENCE {
    cucsComputePooledEnclosureComputeSlotInstanceId                  CucsManagedObjectId,
    cucsComputePooledEnclosureComputeSlotDn                          CucsManagedObjectDn,
    cucsComputePooledEnclosureComputeSlotRn                          SnmpAdminString,
    cucsComputePooledEnclosureComputeSlotAssigned                    TruthValue,
    cucsComputePooledEnclosureComputeSlotAssignedToDn                SnmpAdminString,
    cucsComputePooledEnclosureComputeSlotChassisId                   Gauge32,
    cucsComputePooledEnclosureComputeSlotOwner                       CucsComputeOwner,
    cucsComputePooledEnclosureComputeSlotPoolableDn                  SnmpAdminString,
    cucsComputePooledEnclosureComputeSlotPrevAssignedToDn            SnmpAdminString,
    cucsComputePooledEnclosureComputeSlotServerInstanceId            CucsComputePooledEnclosureComputeSlotServerInstanceId,
    cucsComputePooledEnclosureComputeSlotSlotId                      CucsComputePooledEnclosureComputeSlotSlotId
}

cucsComputePooledEnclosureComputeSlotInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePooledEnclosureComputeSlotEntry 1 }

cucsComputePooledEnclosureComputeSlotDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:dn
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 2 }

cucsComputePooledEnclosureComputeSlotRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:rn
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 3 }

cucsComputePooledEnclosureComputeSlotAssigned OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:assigned
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 4 }

cucsComputePooledEnclosureComputeSlotAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:assignedToDn
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 5 }

cucsComputePooledEnclosureComputeSlotChassisId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:chassisId
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 6 }

cucsComputePooledEnclosureComputeSlotOwner OBJECT-TYPE
    SYNTAX       CucsComputeOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:owner
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 7 }

cucsComputePooledEnclosureComputeSlotPoolableDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:poolableDn
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 8 }

cucsComputePooledEnclosureComputeSlotPrevAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:prevAssignedToDn
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 9 }

cucsComputePooledEnclosureComputeSlotServerInstanceId OBJECT-TYPE
    SYNTAX       CucsComputePooledEnclosureComputeSlotServerInstanceId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:serverInstanceId
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 10 }

cucsComputePooledEnclosureComputeSlotSlotId OBJECT-TYPE
    SYNTAX       CucsComputePooledEnclosureComputeSlotSlotId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledEnclosureComputeSlot:slotId
        managed object property"
    ::= { cucsComputePooledEnclosureComputeSlotEntry 11 }

cucsComputePooledRackUnitTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePooledRackUnitEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit managed object table"
    ::= { cucsComputeObjects 29 }

cucsComputePooledRackUnitEntry OBJECT-TYPE
    SYNTAX           CucsComputePooledRackUnitEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePooledRackUnitTable table."
    INDEX { cucsComputePooledRackUnitInstanceId }
    ::= { cucsComputePooledRackUnitTable 1 }

CucsComputePooledRackUnitEntry ::= SEQUENCE {
    cucsComputePooledRackUnitInstanceId                              CucsManagedObjectId,
    cucsComputePooledRackUnitDn                                      CucsManagedObjectDn,
    cucsComputePooledRackUnitRn                                      SnmpAdminString,
    cucsComputePooledRackUnitAssigned                                TruthValue,
    cucsComputePooledRackUnitAssignedToDn                            SnmpAdminString,
    cucsComputePooledRackUnitId                                      CucsComputePooledRackUnitId,
    cucsComputePooledRackUnitOwner                                   CucsComputeOwner,
    cucsComputePooledRackUnitPoolableDn                              SnmpAdminString,
    cucsComputePooledRackUnitPrevAssignedToDn                        SnmpAdminString
}

cucsComputePooledRackUnitInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePooledRackUnitEntry 1 }

cucsComputePooledRackUnitDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:dn managed object property"
    ::= { cucsComputePooledRackUnitEntry 2 }

cucsComputePooledRackUnitRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:rn managed object property"
    ::= { cucsComputePooledRackUnitEntry 3 }

cucsComputePooledRackUnitAssigned OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:assigned managed
        object property"
    ::= { cucsComputePooledRackUnitEntry 4 }

cucsComputePooledRackUnitAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:assignedToDn
        managed object property"
    ::= { cucsComputePooledRackUnitEntry 5 }

cucsComputePooledRackUnitId OBJECT-TYPE
    SYNTAX       CucsComputePooledRackUnitId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:id managed object property"
    ::= { cucsComputePooledRackUnitEntry 6 }

cucsComputePooledRackUnitOwner OBJECT-TYPE
    SYNTAX       CucsComputeOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:owner managed object property"
    ::= { cucsComputePooledRackUnitEntry 7 }

cucsComputePooledRackUnitPoolableDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:poolableDn managed
        object property"
    ::= { cucsComputePooledRackUnitEntry 8 }

cucsComputePooledRackUnitPrevAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledRackUnit:prevAssignedToDn
        managed object property"
    ::= { cucsComputePooledRackUnitEntry 9 }

cucsComputePooledSlotTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePooledSlotEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot managed object table"
    ::= { cucsComputeObjects 30 }

cucsComputePooledSlotEntry OBJECT-TYPE
    SYNTAX           CucsComputePooledSlotEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePooledSlotTable table."
    INDEX { cucsComputePooledSlotInstanceId }
    ::= { cucsComputePooledSlotTable 1 }

CucsComputePooledSlotEntry ::= SEQUENCE {
    cucsComputePooledSlotInstanceId                                  CucsManagedObjectId,
    cucsComputePooledSlotDn                                          CucsManagedObjectDn,
    cucsComputePooledSlotRn                                          SnmpAdminString,
    cucsComputePooledSlotAssigned                                    TruthValue,
    cucsComputePooledSlotAssignedToDn                                SnmpAdminString,
    cucsComputePooledSlotChassisId                                   Gauge32,
    cucsComputePooledSlotOwner                                       CucsComputeOwner,
    cucsComputePooledSlotPoolableDn                                  SnmpAdminString,
    cucsComputePooledSlotPrevAssignedToDn                            SnmpAdminString,
    cucsComputePooledSlotSlotId                                      CucsComputePooledSlotSlotId
}

cucsComputePooledSlotInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePooledSlotEntry 1 }

cucsComputePooledSlotDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:dn managed object property"
    ::= { cucsComputePooledSlotEntry 2 }

cucsComputePooledSlotRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:rn managed object property"
    ::= { cucsComputePooledSlotEntry 3 }

cucsComputePooledSlotAssigned OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:assigned managed object property"
    ::= { cucsComputePooledSlotEntry 4 }

cucsComputePooledSlotAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:assignedToDn managed
        object property"
    ::= { cucsComputePooledSlotEntry 5 }

cucsComputePooledSlotChassisId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:chassisId managed object property"
    ::= { cucsComputePooledSlotEntry 6 }

cucsComputePooledSlotOwner OBJECT-TYPE
    SYNTAX       CucsComputeOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:owner managed object property"
    ::= { cucsComputePooledSlotEntry 7 }

cucsComputePooledSlotPoolableDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:poolableDn managed object property"
    ::= { cucsComputePooledSlotEntry 8 }

cucsComputePooledSlotPrevAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:prevAssignedToDn
        managed object property"
    ::= { cucsComputePooledSlotEntry 9 }

cucsComputePooledSlotSlotId OBJECT-TYPE
    SYNTAX       CucsComputePooledSlotSlotId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PooledSlot:slotId managed object property"
    ::= { cucsComputePooledSlotEntry 10 }

cucsComputePoolingPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePoolingPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy managed object table"
    ::= { cucsComputeObjects 31 }

cucsComputePoolingPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputePoolingPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePoolingPolicyTable table."
    INDEX { cucsComputePoolingPolicyInstanceId }
    ::= { cucsComputePoolingPolicyTable 1 }

CucsComputePoolingPolicyEntry ::= SEQUENCE {
    cucsComputePoolingPolicyInstanceId                               CucsManagedObjectId,
    cucsComputePoolingPolicyDn                                       CucsManagedObjectDn,
    cucsComputePoolingPolicyRn                                       SnmpAdminString,
    cucsComputePoolingPolicyDescr                                    SnmpAdminString,
    cucsComputePoolingPolicyIntId                                    SnmpAdminString,
    cucsComputePoolingPolicyName                                     SnmpAdminString,
    cucsComputePoolingPolicyPoolDn                                   SnmpAdminString,
    cucsComputePoolingPolicyQualifier                                SnmpAdminString,
    cucsComputePoolingPolicyPolicyLevel                              Gauge32,
    cucsComputePoolingPolicyPolicyOwner                              CucsPolicyPolicyOwner
}

cucsComputePoolingPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePoolingPolicyEntry 1 }

cucsComputePoolingPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:dn managed object property"
    ::= { cucsComputePoolingPolicyEntry 2 }

cucsComputePoolingPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:rn managed object property"
    ::= { cucsComputePoolingPolicyEntry 3 }

cucsComputePoolingPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:descr managed object property"
    ::= { cucsComputePoolingPolicyEntry 4 }

cucsComputePoolingPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:intId managed object property"
    ::= { cucsComputePoolingPolicyEntry 5 }

cucsComputePoolingPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:name managed object property"
    ::= { cucsComputePoolingPolicyEntry 6 }

cucsComputePoolingPolicyPoolDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:poolDn managed object property"
    ::= { cucsComputePoolingPolicyEntry 7 }

cucsComputePoolingPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:qualifier managed
        object property"
    ::= { cucsComputePoolingPolicyEntry 8 }

cucsComputePoolingPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:policyLevel managed
        object property"
    ::= { cucsComputePoolingPolicyEntry 9 }

cucsComputePoolingPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PoolingPolicy:policyOwner managed
        object property"
    ::= { cucsComputePoolingPolicyEntry 10 }

cucsComputePsuControlTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePsuControlEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PsuControl managed object table"
    ::= { cucsComputeObjects 32 }

cucsComputePsuControlEntry OBJECT-TYPE
    SYNTAX           CucsComputePsuControlEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePsuControlTable table."
    INDEX { cucsComputePsuControlInstanceId }
    ::= { cucsComputePsuControlTable 1 }

CucsComputePsuControlEntry ::= SEQUENCE {
    cucsComputePsuControlInstanceId                                  CucsManagedObjectId,
    cucsComputePsuControlDn                                          CucsManagedObjectDn,
    cucsComputePsuControlRn                                          SnmpAdminString,
    cucsComputePsuControlClusterState                                CucsComputePsuClusterState,
    cucsComputePsuControlDescr                                       SnmpAdminString,
    cucsComputePsuControlInputPowerState                             CucsEquipmentSensorThresholdStatus,
    cucsComputePsuControlIntId                                       SnmpAdminString,
    cucsComputePsuControlName                                        SnmpAdminString,
    cucsComputePsuControlOperQualifier                               CucsComputePsuRedundancyOperQualifier,
    cucsComputePsuControlOperState                                   CucsComputePsuRedundancyOperState,
    cucsComputePsuControlOutputPowerState                            CucsEquipmentSensorThresholdStatus,
    cucsComputePsuControlRedundancy                                  CucsComputePsuControlRedundancy,
    cucsComputePsuControlPolicyLevel                                 Gauge32,
    cucsComputePsuControlPolicyOwner                                 CucsPolicyPolicyOwner
}

cucsComputePsuControlInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePsuControlEntry 1 }

cucsComputePsuControlDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:dn managed object property"
    ::= { cucsComputePsuControlEntry 2 }

cucsComputePsuControlRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:rn managed object property"
    ::= { cucsComputePsuControlEntry 3 }

cucsComputePsuControlClusterState OBJECT-TYPE
    SYNTAX       CucsComputePsuClusterState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:clusterState managed
        object property"
    ::= { cucsComputePsuControlEntry 4 }

cucsComputePsuControlDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:descr managed object property"
    ::= { cucsComputePsuControlEntry 5 }

cucsComputePsuControlInputPowerState OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:inputPowerState managed
        object property"
    ::= { cucsComputePsuControlEntry 6 }

cucsComputePsuControlIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:intId managed object property"
    ::= { cucsComputePsuControlEntry 7 }

cucsComputePsuControlName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:name managed object property"
    ::= { cucsComputePsuControlEntry 8 }

cucsComputePsuControlOperQualifier OBJECT-TYPE
    SYNTAX       CucsComputePsuRedundancyOperQualifier
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:operQualifier managed
        object property"
    ::= { cucsComputePsuControlEntry 9 }

cucsComputePsuControlOperState OBJECT-TYPE
    SYNTAX       CucsComputePsuRedundancyOperState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:operState managed object property"
    ::= { cucsComputePsuControlEntry 10 }

cucsComputePsuControlOutputPowerState OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:outputPowerState
        managed object property"
    ::= { cucsComputePsuControlEntry 11 }

cucsComputePsuControlRedundancy OBJECT-TYPE
    SYNTAX       CucsComputePsuControlRedundancy
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:redundancy managed object property"
    ::= { cucsComputePsuControlEntry 12 }

cucsComputePsuControlPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:policyLevel managed object property"
    ::= { cucsComputePsuControlEntry 13 }

cucsComputePsuControlPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuControl:policyOwner managed object property"
    ::= { cucsComputePsuControlEntry 14 }

cucsComputePsuPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputePsuPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy managed object table"
    ::= { cucsComputeObjects 33 }

cucsComputePsuPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputePsuPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputePsuPolicyTable table."
    INDEX { cucsComputePsuPolicyInstanceId }
    ::= { cucsComputePsuPolicyTable 1 }

CucsComputePsuPolicyEntry ::= SEQUENCE {
    cucsComputePsuPolicyInstanceId                                   CucsManagedObjectId,
    cucsComputePsuPolicyDn                                           CucsManagedObjectDn,
    cucsComputePsuPolicyRn                                           SnmpAdminString,
    cucsComputePsuPolicyDescr                                        SnmpAdminString,
    cucsComputePsuPolicyIntId                                        SnmpAdminString,
    cucsComputePsuPolicyName                                         SnmpAdminString,
    cucsComputePsuPolicyRedundancy                                   CucsComputePsuRedundancy,
    cucsComputePsuPolicyPolicyLevel                                  Gauge32,
    cucsComputePsuPolicyPolicyOwner                                  CucsPolicyPolicyOwner
}

cucsComputePsuPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputePsuPolicyEntry 1 }

cucsComputePsuPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:dn managed object property"
    ::= { cucsComputePsuPolicyEntry 2 }

cucsComputePsuPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:rn managed object property"
    ::= { cucsComputePsuPolicyEntry 3 }

cucsComputePsuPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:descr managed object property"
    ::= { cucsComputePsuPolicyEntry 4 }

cucsComputePsuPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:intId managed object property"
    ::= { cucsComputePsuPolicyEntry 5 }

cucsComputePsuPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:name managed object property"
    ::= { cucsComputePsuPolicyEntry 6 }

cucsComputePsuPolicyRedundancy OBJECT-TYPE
    SYNTAX       CucsComputePsuRedundancy
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:redundancy managed object property"
    ::= { cucsComputePsuPolicyEntry 7 }

cucsComputePsuPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:policyLevel managed object property"
    ::= { cucsComputePsuPolicyEntry 8 }

cucsComputePsuPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:PsuPolicy:policyOwner managed object property"
    ::= { cucsComputePsuPolicyEntry 9 }

cucsComputeQualTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:Qual managed object table"
    ::= { cucsComputeObjects 34 }

cucsComputeQualEntry OBJECT-TYPE
    SYNTAX           CucsComputeQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeQualTable table."
    INDEX { cucsComputeQualInstanceId }
    ::= { cucsComputeQualTable 1 }

CucsComputeQualEntry ::= SEQUENCE {
    cucsComputeQualInstanceId                                        CucsManagedObjectId,
    cucsComputeQualDn                                                CucsManagedObjectDn,
    cucsComputeQualRn                                                SnmpAdminString,
    cucsComputeQualDescr                                             SnmpAdminString,
    cucsComputeQualIntId                                             SnmpAdminString,
    cucsComputeQualName                                              SnmpAdminString,
    cucsComputeQualPolicyLevel                                       Gauge32,
    cucsComputeQualPolicyOwner                                       CucsPolicyPolicyOwner
}

cucsComputeQualInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeQualEntry 1 }

cucsComputeQualDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Qual:dn managed object property"
    ::= { cucsComputeQualEntry 2 }

cucsComputeQualRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Qual:rn managed object property"
    ::= { cucsComputeQualEntry 3 }

cucsComputeQualDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Qual:descr managed object property"
    ::= { cucsComputeQualEntry 4 }

cucsComputeQualIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Qual:intId managed object property"
    ::= { cucsComputeQualEntry 5 }

cucsComputeQualName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Qual:name managed object property"
    ::= { cucsComputeQualEntry 6 }

cucsComputeQualPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Qual:policyLevel managed object property"
    ::= { cucsComputeQualEntry 7 }

cucsComputeQualPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:Qual:policyOwner managed object property"
    ::= { cucsComputeQualEntry 8 }

cucsComputeRackQualTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRackQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RackQual managed object table"
    ::= { cucsComputeObjects 43 }

cucsComputeRackQualEntry OBJECT-TYPE
    SYNTAX           CucsComputeRackQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRackQualTable table."
    INDEX { cucsComputeRackQualInstanceId }
    ::= { cucsComputeRackQualTable 1 }

CucsComputeRackQualEntry ::= SEQUENCE {
    cucsComputeRackQualInstanceId                                    CucsManagedObjectId,
    cucsComputeRackQualDn                                            CucsManagedObjectDn,
    cucsComputeRackQualRn                                            SnmpAdminString,
    cucsComputeRackQualMaxId                                         CucsComputeRackQualMaxId,
    cucsComputeRackQualMinId                                         CucsComputeRackQualMinId
}

cucsComputeRackQualInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRackQualEntry 1 }

cucsComputeRackQualDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackQual:dn managed object property"
    ::= { cucsComputeRackQualEntry 2 }

cucsComputeRackQualRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackQual:rn managed object property"
    ::= { cucsComputeRackQualEntry 3 }

cucsComputeRackQualMaxId OBJECT-TYPE
    SYNTAX       CucsComputeRackQualMaxId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackQual:maxId managed object property"
    ::= { cucsComputeRackQualEntry 4 }

cucsComputeRackQualMinId OBJECT-TYPE
    SYNTAX       CucsComputeRackQualMinId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackQual:minId managed object property"
    ::= { cucsComputeRackQualEntry 5 }

cucsComputeRackUnitTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRackUnitEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RackUnit managed object table"
    ::= { cucsComputeObjects 35 }

cucsComputeRackUnitEntry OBJECT-TYPE
    SYNTAX           CucsComputeRackUnitEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRackUnitTable table."
    INDEX { cucsComputeRackUnitInstanceId }
    ::= { cucsComputeRackUnitTable 1 }

CucsComputeRackUnitEntry ::= SEQUENCE {
    cucsComputeRackUnitInstanceId                                    CucsManagedObjectId,
    cucsComputeRackUnitDn                                            CucsManagedObjectDn,
    cucsComputeRackUnitRn                                            SnmpAdminString,
    cucsComputeRackUnitAdminPower                                    CucsComputeAdminPowerState,
    cucsComputeRackUnitAdminState                                    CucsComputeAdminState,
    cucsComputeRackUnitAssignedToDn                                  SnmpAdminString,
    cucsComputeRackUnitAssociation                                   CucsComputeAssociation,
    cucsComputeRackUnitAvailability                                  CucsComputeAvailability,
    cucsComputeRackUnitAvailableMemory                               Gauge32,
    cucsComputeRackUnitCheckPoint                                    CucsComputeCheckPoint,
    cucsComputeRackUnitConnPath                                      CucsEquipmentConnectionStatus,
    cucsComputeRackUnitConnStatus                                    CucsEquipmentConnectionStatus,
    cucsComputeRackUnitDescr                                         SnmpAdminString,
    cucsComputeRackUnitDiscovery                                     CucsComputeDiscovery,
    cucsComputeRackUnitFltAggr                                       Unsigned64,
    cucsComputeRackUnitFsmDescr                                      SnmpAdminString,
    cucsComputeRackUnitFsmFlags                                      SnmpAdminString,
    cucsComputeRackUnitFsmPrev                                       SnmpAdminString,
    cucsComputeRackUnitFsmProgr                                      Gauge32,
    cucsComputeRackUnitFsmRmtInvErrCode                              Gauge32,
    cucsComputeRackUnitFsmRmtInvErrDescr                             SnmpAdminString,
    cucsComputeRackUnitFsmRmtInvRslt                                 CucsConditionRemoteInvRslt,
    cucsComputeRackUnitFsmStageDescr                                 SnmpAdminString,
    cucsComputeRackUnitFsmStamp                                      DateAndTime,
    cucsComputeRackUnitFsmStatus                                     SnmpAdminString,
    cucsComputeRackUnitFsmTry                                        Gauge32,
    cucsComputeRackUnitId                                            CucsComputeRackUnitId,
    cucsComputeRackUnitIntId                                         SnmpAdminString,
    cucsComputeRackUnitLc                                            CucsComputeAdminTrigger,
    cucsComputeRackUnitLcTs                                          DateAndTime,
    cucsComputeRackUnitManagingInst                                  CucsNetworkSwitchId,
    cucsComputeRackUnitModel                                         SnmpAdminString,
    cucsComputeRackUnitName                                          SnmpAdminString,
    cucsComputeRackUnitNumOfAdaptors                                 Gauge32,
    cucsComputeRackUnitNumOfCores                                    Gauge32,
    cucsComputeRackUnitNumOfCpus                                     Gauge32,
    cucsComputeRackUnitNumOfEthHostIfs                               Gauge32,
    cucsComputeRackUnitNumOfFcHostIfs                                Gauge32,
    cucsComputeRackUnitNumOfThreads                                  Gauge32,
    cucsComputeRackUnitOperPower                                     CucsEquipmentPowerState,
    cucsComputeRackUnitOperQualifier                                 CucsComputeIssues,
    cucsComputeRackUnitOperState                                     CucsLsOperState,
    cucsComputeRackUnitOperability                                   CucsEquipmentOperability,
    cucsComputeRackUnitOriginalUuid                                  SnmpAdminString,
    cucsComputeRackUnitPresence                                      CucsEquipmentSlotStatus,
    cucsComputeRackUnitRevision                                      SnmpAdminString,
    cucsComputeRackUnitSerial                                        SnmpAdminString,
    cucsComputeRackUnitServerId                                      SnmpAdminString,
    cucsComputeRackUnitTotalMemory                                   Gauge32,
    cucsComputeRackUnitUuid                                          SnmpAdminString,
    cucsComputeRackUnitVendor                                        SnmpAdminString,
    cucsComputeRackUnitVersionHolder                                 TruthValue,
    cucsComputeRackUnitNumOfCoresEnabled                             Gauge32,
    cucsComputeRackUnitLowVoltageMemory                              CucsComputePhysicalLowVoltageMemory,
    cucsComputeRackUnitMemorySpeed                                   Gauge32,
    cucsComputeRackUnitUsrLbl                                        SnmpAdminString,
    cucsComputeRackUnitMfgTime                                       DateAndTime,
    cucsComputeRackUnitPartNumber                                    SnmpAdminString,
    cucsComputeRackUnitVid                                           SnmpAdminString,
    cucsComputeRackUnitPolicyLevel                                   Gauge32,
    cucsComputeRackUnitPolicyOwner                                   CucsPolicyPolicyOwner,
    cucsComputeRackUnitLocalId                                       SnmpAdminString,
    cucsComputeRackUnitOperPwrTransSrc                               CucsComputePowerTransitionSrc,
    cucsComputeRackUnitDiscoveryStatus                               CucsEquipmentConnectionStatus,
    cucsComputeRackUnitNumOf40GAdaptorsWithOldFw                     Gauge32,
    cucsComputeRackUnitNumOf40GAdaptorsWithUnknownFw                 Gauge32,
    cucsComputeRackUnitFanSpeedConfigStatus                          SnmpAdminString,
    cucsComputeRackUnitFanSpeedPolicyFault                           CucsEquipmentFanSpeedPolicyFault
}

cucsComputeRackUnitInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRackUnitEntry 1 }

cucsComputeRackUnitDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:dn managed object property"
    ::= { cucsComputeRackUnitEntry 2 }

cucsComputeRackUnitRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:rn managed object property"
    ::= { cucsComputeRackUnitEntry 3 }

cucsComputeRackUnitAdminPower OBJECT-TYPE
    SYNTAX       CucsComputeAdminPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:adminPower managed object property"
    ::= { cucsComputeRackUnitEntry 4 }

cucsComputeRackUnitAdminState OBJECT-TYPE
    SYNTAX       CucsComputeAdminState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:adminState managed object property"
    ::= { cucsComputeRackUnitEntry 5 }

cucsComputeRackUnitAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:assignedToDn managed object property"
    ::= { cucsComputeRackUnitEntry 6 }

cucsComputeRackUnitAssociation OBJECT-TYPE
    SYNTAX       CucsComputeAssociation
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:association managed object property"
    ::= { cucsComputeRackUnitEntry 7 }

cucsComputeRackUnitAvailability OBJECT-TYPE
    SYNTAX       CucsComputeAvailability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:availability managed object property"
    ::= { cucsComputeRackUnitEntry 8 }

cucsComputeRackUnitAvailableMemory OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:availableMemory managed
        object property"
    ::= { cucsComputeRackUnitEntry 9 }

cucsComputeRackUnitCheckPoint OBJECT-TYPE
    SYNTAX       CucsComputeCheckPoint
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:checkPoint managed object property"
    ::= { cucsComputeRackUnitEntry 10 }

cucsComputeRackUnitConnPath OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:connPath managed object property"
    ::= { cucsComputeRackUnitEntry 11 }

cucsComputeRackUnitConnStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:connStatus managed object property"
    ::= { cucsComputeRackUnitEntry 12 }

cucsComputeRackUnitDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:descr managed object property"
    ::= { cucsComputeRackUnitEntry 13 }

cucsComputeRackUnitDiscovery OBJECT-TYPE
    SYNTAX       CucsComputeDiscovery
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:discovery managed object property"
    ::= { cucsComputeRackUnitEntry 14 }

cucsComputeRackUnitFltAggr OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fltAggr managed object property"
    ::= { cucsComputeRackUnitEntry 15 }

cucsComputeRackUnitFsmDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmDescr managed object property"
    ::= { cucsComputeRackUnitEntry 16 }

cucsComputeRackUnitFsmFlags OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmFlags managed object property"
    ::= { cucsComputeRackUnitEntry 17 }

cucsComputeRackUnitFsmPrev OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmPrev managed object property"
    ::= { cucsComputeRackUnitEntry 18 }

cucsComputeRackUnitFsmProgr OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmProgr managed object property"
    ::= { cucsComputeRackUnitEntry 19 }

cucsComputeRackUnitFsmRmtInvErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmRmtInvErrCode managed
        object property"
    ::= { cucsComputeRackUnitEntry 20 }

cucsComputeRackUnitFsmRmtInvErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmRmtInvErrDescr managed
        object property"
    ::= { cucsComputeRackUnitEntry 21 }

cucsComputeRackUnitFsmRmtInvRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmRmtInvRslt managed object property"
    ::= { cucsComputeRackUnitEntry 22 }

cucsComputeRackUnitFsmStageDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmStageDescr managed object property"
    ::= { cucsComputeRackUnitEntry 23 }

cucsComputeRackUnitFsmStamp OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmStamp managed object property"
    ::= { cucsComputeRackUnitEntry 24 }

cucsComputeRackUnitFsmStatus OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmStatus managed object property"
    ::= { cucsComputeRackUnitEntry 25 }

cucsComputeRackUnitFsmTry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fsmTry managed object property"
    ::= { cucsComputeRackUnitEntry 26 }

cucsComputeRackUnitId OBJECT-TYPE
    SYNTAX       CucsComputeRackUnitId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:id managed object property"
    ::= { cucsComputeRackUnitEntry 27 }

cucsComputeRackUnitIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:intId managed object property"
    ::= { cucsComputeRackUnitEntry 28 }

cucsComputeRackUnitLc OBJECT-TYPE
    SYNTAX       CucsComputeAdminTrigger
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:lc managed object property"
    ::= { cucsComputeRackUnitEntry 29 }

cucsComputeRackUnitLcTs OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:lcTs managed object property"
    ::= { cucsComputeRackUnitEntry 30 }

cucsComputeRackUnitManagingInst OBJECT-TYPE
    SYNTAX       CucsNetworkSwitchId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:managingInst managed object property"
    ::= { cucsComputeRackUnitEntry 31 }

cucsComputeRackUnitModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:model managed object property"
    ::= { cucsComputeRackUnitEntry 32 }

cucsComputeRackUnitName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:name managed object property"
    ::= { cucsComputeRackUnitEntry 33 }

cucsComputeRackUnitNumOfAdaptors OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOfAdaptors managed object property"
    ::= { cucsComputeRackUnitEntry 34 }

cucsComputeRackUnitNumOfCores OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOfCores managed object property"
    ::= { cucsComputeRackUnitEntry 35 }

cucsComputeRackUnitNumOfCpus OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOfCpus managed object property"
    ::= { cucsComputeRackUnitEntry 36 }

cucsComputeRackUnitNumOfEthHostIfs OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOfEthHostIfs managed
        object property"
    ::= { cucsComputeRackUnitEntry 37 }

cucsComputeRackUnitNumOfFcHostIfs OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOfFcHostIfs managed
        object property"
    ::= { cucsComputeRackUnitEntry 38 }

cucsComputeRackUnitNumOfThreads OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOfThreads managed object property"
    ::= { cucsComputeRackUnitEntry 39 }

cucsComputeRackUnitOperPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:operPower managed object property"
    ::= { cucsComputeRackUnitEntry 40 }

cucsComputeRackUnitOperQualifier OBJECT-TYPE
    SYNTAX       CucsComputeIssues
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:operQualifier managed object property"
    ::= { cucsComputeRackUnitEntry 41 }

cucsComputeRackUnitOperState OBJECT-TYPE
    SYNTAX       CucsLsOperState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:operState managed object property"
    ::= { cucsComputeRackUnitEntry 42 }

cucsComputeRackUnitOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:operability managed object property"
    ::= { cucsComputeRackUnitEntry 43 }

cucsComputeRackUnitOriginalUuid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:originalUuid managed object property"
    ::= { cucsComputeRackUnitEntry 44 }

cucsComputeRackUnitPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentSlotStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:presence managed object property"
    ::= { cucsComputeRackUnitEntry 45 }

cucsComputeRackUnitRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:revision managed object property"
    ::= { cucsComputeRackUnitEntry 46 }

cucsComputeRackUnitSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:serial managed object property"
    ::= { cucsComputeRackUnitEntry 47 }

cucsComputeRackUnitServerId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:serverId managed object property"
    ::= { cucsComputeRackUnitEntry 48 }

cucsComputeRackUnitTotalMemory OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:totalMemory managed object property"
    ::= { cucsComputeRackUnitEntry 49 }

cucsComputeRackUnitUuid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:uuid managed object property"
    ::= { cucsComputeRackUnitEntry 50 }

cucsComputeRackUnitVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:vendor managed object property"
    ::= { cucsComputeRackUnitEntry 51 }

cucsComputeRackUnitVersionHolder OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:versionHolder managed object property"
    ::= { cucsComputeRackUnitEntry 52 }

cucsComputeRackUnitNumOfCoresEnabled OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOfCoresEnabled managed
        object property"
    ::= { cucsComputeRackUnitEntry 53 }

cucsComputeRackUnitLowVoltageMemory OBJECT-TYPE
    SYNTAX       CucsComputePhysicalLowVoltageMemory
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:lowVoltageMemory managed
        object property"
    ::= { cucsComputeRackUnitEntry 54 }

cucsComputeRackUnitMemorySpeed OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:memorySpeed managed object property"
    ::= { cucsComputeRackUnitEntry 55 }

cucsComputeRackUnitUsrLbl OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:usrLbl managed object property"
    ::= { cucsComputeRackUnitEntry 56 }

cucsComputeRackUnitMfgTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:mfgTime managed object property"
    ::= { cucsComputeRackUnitEntry 57 }

cucsComputeRackUnitPartNumber OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:partNumber managed object property"
    ::= { cucsComputeRackUnitEntry 58 }

cucsComputeRackUnitVid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:vid managed object property"
    ::= { cucsComputeRackUnitEntry 59 }

cucsComputeRackUnitPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:policyLevel managed object property"
    ::= { cucsComputeRackUnitEntry 60 }

cucsComputeRackUnitPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:policyOwner managed object property"
    ::= { cucsComputeRackUnitEntry 61 }

cucsComputeRackUnitLocalId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:localId managed object property"
    ::= { cucsComputeRackUnitEntry 62 }

cucsComputeRackUnitOperPwrTransSrc OBJECT-TYPE
    SYNTAX       CucsComputePowerTransitionSrc
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:operPwrTransSrc managed
        object property"
    ::= { cucsComputeRackUnitEntry 63 }

cucsComputeRackUnitDiscoveryStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:discoveryStatus managed
        object property"
    ::= { cucsComputeRackUnitEntry 64 }

cucsComputeRackUnitNumOf40GAdaptorsWithOldFw OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOf40GAdaptorsWithOldFw
        managed object property"
    ::= { cucsComputeRackUnitEntry 66 }

cucsComputeRackUnitNumOf40GAdaptorsWithUnknownFw OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:numOf40GAdaptorsWithUnknownFw
        managed object property"
    ::= { cucsComputeRackUnitEntry 67 }

cucsComputeRackUnitFanSpeedConfigStatus OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fanSpeedConfigStatus
        managed object property"
    ::= { cucsComputeRackUnitEntry 68 }

cucsComputeRackUnitFanSpeedPolicyFault OBJECT-TYPE
    SYNTAX       CucsEquipmentFanSpeedPolicyFault
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnit:fanSpeedPolicyFault
        managed object property"
    ::= { cucsComputeRackUnitEntry 69 }

cucsComputeRackUnitFsmTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRackUnitFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm managed object table"
    ::= { cucsComputeObjects 52 }

cucsComputeRackUnitFsmEntry OBJECT-TYPE
    SYNTAX           CucsComputeRackUnitFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRackUnitFsmTable table."
    INDEX { cucsComputeRackUnitFsmInstanceId }
    ::= { cucsComputeRackUnitFsmTable 1 }

CucsComputeRackUnitFsmEntry ::= SEQUENCE {
    cucsComputeRackUnitFsmInstanceId                                 CucsManagedObjectId,
    cucsComputeRackUnitFsmDn                                         CucsManagedObjectDn,
    cucsComputeRackUnitFsmRn                                         SnmpAdminString,
    cucsComputeRackUnitFsmCompletionTime                             DateAndTime,
    cucsComputeRackUnitFsmCurrentFsm                                 CucsComputeRackUnitFsmCurrentFsm,
    cucsComputeRackUnitFsmDescrData                                  SnmpAdminString,
    cucsComputeRackUnitFsmFsmStatus                                  CucsFsmFsmStageStatus,
    cucsComputeRackUnitFsmProgress                                   Gauge32,
    cucsComputeRackUnitFsmRmtErrCode                                 Gauge32,
    cucsComputeRackUnitFsmRmtErrDescr                                SnmpAdminString,
    cucsComputeRackUnitFsmRmtRslt                                    CucsConditionRemoteInvRslt
}

cucsComputeRackUnitFsmInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRackUnitFsmEntry 1 }

cucsComputeRackUnitFsmDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:dn managed object property"
    ::= { cucsComputeRackUnitFsmEntry 2 }

cucsComputeRackUnitFsmRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:rn managed object property"
    ::= { cucsComputeRackUnitFsmEntry 3 }

cucsComputeRackUnitFsmCompletionTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:completionTime managed
        object property"
    ::= { cucsComputeRackUnitFsmEntry 4 }

cucsComputeRackUnitFsmCurrentFsm OBJECT-TYPE
    SYNTAX       CucsComputeRackUnitFsmCurrentFsm
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:currentFsm managed object property"
    ::= { cucsComputeRackUnitFsmEntry 5 }

cucsComputeRackUnitFsmDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:descr managed object property"
    ::= { cucsComputeRackUnitFsmEntry 6 }

cucsComputeRackUnitFsmFsmStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:fsmStatus managed object property"
    ::= { cucsComputeRackUnitFsmEntry 7 }

cucsComputeRackUnitFsmProgress OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:progress managed object property"
    ::= { cucsComputeRackUnitFsmEntry 8 }

cucsComputeRackUnitFsmRmtErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:rmtErrCode managed object property"
    ::= { cucsComputeRackUnitFsmEntry 9 }

cucsComputeRackUnitFsmRmtErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:rmtErrDescr managed
        object property"
    ::= { cucsComputeRackUnitFsmEntry 10 }

cucsComputeRackUnitFsmRmtRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsm:rmtRslt managed object property"
    ::= { cucsComputeRackUnitFsmEntry 11 }

cucsComputeRackUnitFsmStageTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRackUnitFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage managed object table"
    ::= { cucsComputeObjects 53 }

cucsComputeRackUnitFsmStageEntry OBJECT-TYPE
    SYNTAX           CucsComputeRackUnitFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRackUnitFsmStageTable table."
    INDEX { cucsComputeRackUnitFsmStageInstanceId }
    ::= { cucsComputeRackUnitFsmStageTable 1 }

CucsComputeRackUnitFsmStageEntry ::= SEQUENCE {
    cucsComputeRackUnitFsmStageInstanceId                            CucsManagedObjectId,
    cucsComputeRackUnitFsmStageDn                                    CucsManagedObjectDn,
    cucsComputeRackUnitFsmStageRn                                    SnmpAdminString,
    cucsComputeRackUnitFsmStageDescrData                             SnmpAdminString,
    cucsComputeRackUnitFsmStageLastUpdateTime                        DateAndTime,
    cucsComputeRackUnitFsmStageName                                  CucsComputeRackUnitFsmStageName,
    cucsComputeRackUnitFsmStageOrder                                 Gauge32,
    cucsComputeRackUnitFsmStageRetry                                 Gauge32,
    cucsComputeRackUnitFsmStageStageStatus                           CucsFsmFsmStageStatus
}

cucsComputeRackUnitFsmStageInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRackUnitFsmStageEntry 1 }

cucsComputeRackUnitFsmStageDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:dn managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 2 }

cucsComputeRackUnitFsmStageRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:rn managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 3 }

cucsComputeRackUnitFsmStageDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:descr managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 4 }

cucsComputeRackUnitFsmStageLastUpdateTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:lastUpdateTime
        managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 5 }

cucsComputeRackUnitFsmStageName OBJECT-TYPE
    SYNTAX       CucsComputeRackUnitFsmStageName
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:name managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 6 }

cucsComputeRackUnitFsmStageOrder OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:order managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 7 }

cucsComputeRackUnitFsmStageRetry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:retry managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 8 }

cucsComputeRackUnitFsmStageStageStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmStage:stageStatus
        managed object property"
    ::= { cucsComputeRackUnitFsmStageEntry 9 }

cucsComputeRackUnitFsmTaskTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRackUnitFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmTask managed object table"
    ::= { cucsComputeObjects 36 }

cucsComputeRackUnitFsmTaskEntry OBJECT-TYPE
    SYNTAX           CucsComputeRackUnitFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRackUnitFsmTaskTable table."
    INDEX { cucsComputeRackUnitFsmTaskInstanceId }
    ::= { cucsComputeRackUnitFsmTaskTable 1 }

CucsComputeRackUnitFsmTaskEntry ::= SEQUENCE {
    cucsComputeRackUnitFsmTaskInstanceId                             CucsManagedObjectId,
    cucsComputeRackUnitFsmTaskDn                                     CucsManagedObjectDn,
    cucsComputeRackUnitFsmTaskRn                                     SnmpAdminString,
    cucsComputeRackUnitFsmTaskCompletion                             CucsFsmCompletion,
    cucsComputeRackUnitFsmTaskFlags                                  CucsComputeRackUnitFsmTaskFlags,
    cucsComputeRackUnitFsmTaskItem                                   CucsComputeRackUnitFsmTaskItem,
    cucsComputeRackUnitFsmTaskSeqId                                  Gauge32
}

cucsComputeRackUnitFsmTaskInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRackUnitFsmTaskEntry 1 }

cucsComputeRackUnitFsmTaskDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmTask:dn managed object property"
    ::= { cucsComputeRackUnitFsmTaskEntry 2 }

cucsComputeRackUnitFsmTaskRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmTask:rn managed object property"
    ::= { cucsComputeRackUnitFsmTaskEntry 3 }

cucsComputeRackUnitFsmTaskCompletion OBJECT-TYPE
    SYNTAX       CucsFsmCompletion
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmTask:completion managed
        object property"
    ::= { cucsComputeRackUnitFsmTaskEntry 4 }

cucsComputeRackUnitFsmTaskFlags OBJECT-TYPE
    SYNTAX       CucsComputeRackUnitFsmTaskFlags
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmTask:flags managed object property"
    ::= { cucsComputeRackUnitFsmTaskEntry 5 }

cucsComputeRackUnitFsmTaskItem OBJECT-TYPE
    SYNTAX       CucsComputeRackUnitFsmTaskItem
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmTask:item managed object property"
    ::= { cucsComputeRackUnitFsmTaskEntry 6 }

cucsComputeRackUnitFsmTaskSeqId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitFsmTask:seqId managed object property"
    ::= { cucsComputeRackUnitFsmTaskEntry 7 }

cucsComputeRackUnitMbTempStatsTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRackUnitMbTempStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats managed object table"
    ::= { cucsComputeObjects 44 }

cucsComputeRackUnitMbTempStatsEntry OBJECT-TYPE
    SYNTAX           CucsComputeRackUnitMbTempStatsEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRackUnitMbTempStatsTable table."
    INDEX { cucsComputeRackUnitMbTempStatsInstanceId }
    ::= { cucsComputeRackUnitMbTempStatsTable 1 }

CucsComputeRackUnitMbTempStatsEntry ::= SEQUENCE {
    cucsComputeRackUnitMbTempStatsInstanceId                         CucsManagedObjectId,
    cucsComputeRackUnitMbTempStatsDn                                 CucsManagedObjectDn,
    cucsComputeRackUnitMbTempStatsRn                                 SnmpAdminString,
    cucsComputeRackUnitMbTempStatsAmbientTemp                        INTEGER,
    cucsComputeRackUnitMbTempStatsAmbientTempAvg                     INTEGER,
    cucsComputeRackUnitMbTempStatsAmbientTempMax                     INTEGER,
    cucsComputeRackUnitMbTempStatsAmbientTempMin                     INTEGER,
    cucsComputeRackUnitMbTempStatsFrontTemp                          INTEGER,
    cucsComputeRackUnitMbTempStatsFrontTempAvg                       INTEGER,
    cucsComputeRackUnitMbTempStatsFrontTempMax                       INTEGER,
    cucsComputeRackUnitMbTempStatsFrontTempMin                       INTEGER,
    cucsComputeRackUnitMbTempStatsIntervals                          Gauge32,
    cucsComputeRackUnitMbTempStatsIoh1Temp                           INTEGER,
    cucsComputeRackUnitMbTempStatsIoh1TempAvg                        INTEGER,
    cucsComputeRackUnitMbTempStatsIoh1TempMax                        INTEGER,
    cucsComputeRackUnitMbTempStatsIoh1TempMin                        INTEGER,
    cucsComputeRackUnitMbTempStatsIoh2Temp                           INTEGER,
    cucsComputeRackUnitMbTempStatsIoh2TempAvg                        INTEGER,
    cucsComputeRackUnitMbTempStatsIoh2TempMax                        INTEGER,
    cucsComputeRackUnitMbTempStatsIoh2TempMin                        INTEGER,
    cucsComputeRackUnitMbTempStatsRearTemp                           INTEGER,
    cucsComputeRackUnitMbTempStatsRearTempAvg                        INTEGER,
    cucsComputeRackUnitMbTempStatsRearTempMax                        INTEGER,
    cucsComputeRackUnitMbTempStatsRearTempMin                        INTEGER,
    cucsComputeRackUnitMbTempStatsSuspect                            TruthValue,
    cucsComputeRackUnitMbTempStatsThresholded                        CucsComputeRackUnitMbTempStatsThresholded,
    cucsComputeRackUnitMbTempStatsTimeCollected                      DateAndTime,
    cucsComputeRackUnitMbTempStatsUpdate                             Gauge32
}

cucsComputeRackUnitMbTempStatsInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRackUnitMbTempStatsEntry 1 }

cucsComputeRackUnitMbTempStatsDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:dn managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 2 }

cucsComputeRackUnitMbTempStatsRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:rn managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 3 }

cucsComputeRackUnitMbTempStatsAmbientTemp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ambientTemp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 4 }

cucsComputeRackUnitMbTempStatsAmbientTempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ambientTempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 5 }

cucsComputeRackUnitMbTempStatsAmbientTempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ambientTempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 6 }

cucsComputeRackUnitMbTempStatsAmbientTempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ambientTempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 7 }

cucsComputeRackUnitMbTempStatsFrontTemp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:frontTemp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 8 }

cucsComputeRackUnitMbTempStatsFrontTempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:frontTempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 9 }

cucsComputeRackUnitMbTempStatsFrontTempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:frontTempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 10 }

cucsComputeRackUnitMbTempStatsFrontTempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:frontTempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 11 }

cucsComputeRackUnitMbTempStatsIntervals OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:intervals
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 12 }

cucsComputeRackUnitMbTempStatsIoh1Temp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh1Temp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 13 }

cucsComputeRackUnitMbTempStatsIoh1TempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh1TempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 14 }

cucsComputeRackUnitMbTempStatsIoh1TempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh1TempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 15 }

cucsComputeRackUnitMbTempStatsIoh1TempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh1TempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 16 }

cucsComputeRackUnitMbTempStatsIoh2Temp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh2Temp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 17 }

cucsComputeRackUnitMbTempStatsIoh2TempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh2TempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 18 }

cucsComputeRackUnitMbTempStatsIoh2TempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh2TempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 19 }

cucsComputeRackUnitMbTempStatsIoh2TempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:ioh2TempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 20 }

cucsComputeRackUnitMbTempStatsRearTemp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:rearTemp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 21 }

cucsComputeRackUnitMbTempStatsRearTempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:rearTempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 22 }

cucsComputeRackUnitMbTempStatsRearTempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:rearTempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 23 }

cucsComputeRackUnitMbTempStatsRearTempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:rearTempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 24 }

cucsComputeRackUnitMbTempStatsSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:suspect
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 25 }

cucsComputeRackUnitMbTempStatsThresholded OBJECT-TYPE
    SYNTAX       CucsComputeRackUnitMbTempStatsThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:thresholded
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 26 }

cucsComputeRackUnitMbTempStatsTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:timeCollected
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 27 }

cucsComputeRackUnitMbTempStatsUpdate OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStats:update managed
        object property"
    ::= { cucsComputeRackUnitMbTempStatsEntry 28 }

cucsComputeRackUnitMbTempStatsHistTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRackUnitMbTempStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist managed object table"
    ::= { cucsComputeObjects 45 }

cucsComputeRackUnitMbTempStatsHistEntry OBJECT-TYPE
    SYNTAX           CucsComputeRackUnitMbTempStatsHistEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRackUnitMbTempStatsHistTable table."
    INDEX { cucsComputeRackUnitMbTempStatsHistInstanceId }
    ::= { cucsComputeRackUnitMbTempStatsHistTable 1 }

CucsComputeRackUnitMbTempStatsHistEntry ::= SEQUENCE {
    cucsComputeRackUnitMbTempStatsHistInstanceId                     CucsManagedObjectId,
    cucsComputeRackUnitMbTempStatsHistDn                             CucsManagedObjectDn,
    cucsComputeRackUnitMbTempStatsHistRn                             SnmpAdminString,
    cucsComputeRackUnitMbTempStatsHistAmbientTemp                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistAmbientTempAvg                 INTEGER,
    cucsComputeRackUnitMbTempStatsHistAmbientTempMax                 INTEGER,
    cucsComputeRackUnitMbTempStatsHistAmbientTempMin                 INTEGER,
    cucsComputeRackUnitMbTempStatsHistFrontTemp                      INTEGER,
    cucsComputeRackUnitMbTempStatsHistFrontTempAvg                   INTEGER,
    cucsComputeRackUnitMbTempStatsHistFrontTempMax                   INTEGER,
    cucsComputeRackUnitMbTempStatsHistFrontTempMin                   INTEGER,
    cucsComputeRackUnitMbTempStatsHistId                             Unsigned64,
    cucsComputeRackUnitMbTempStatsHistIoh1Temp                       INTEGER,
    cucsComputeRackUnitMbTempStatsHistIoh1TempAvg                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistIoh1TempMax                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistIoh1TempMin                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistIoh2Temp                       INTEGER,
    cucsComputeRackUnitMbTempStatsHistIoh2TempAvg                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistIoh2TempMax                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistIoh2TempMin                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistMostRecent                     TruthValue,
    cucsComputeRackUnitMbTempStatsHistRearTemp                       INTEGER,
    cucsComputeRackUnitMbTempStatsHistRearTempAvg                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistRearTempMax                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistRearTempMin                    INTEGER,
    cucsComputeRackUnitMbTempStatsHistSuspect                        TruthValue,
    cucsComputeRackUnitMbTempStatsHistThresholded                    CucsComputeRackUnitMbTempStatsHistThresholded,
    cucsComputeRackUnitMbTempStatsHistTimeCollected                  DateAndTime
}

cucsComputeRackUnitMbTempStatsHistInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 1 }

cucsComputeRackUnitMbTempStatsHistDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:dn managed
        object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 2 }

cucsComputeRackUnitMbTempStatsHistRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:rn managed
        object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 3 }

cucsComputeRackUnitMbTempStatsHistAmbientTemp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ambientTemp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 4 }

cucsComputeRackUnitMbTempStatsHistAmbientTempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ambientTempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 5 }

cucsComputeRackUnitMbTempStatsHistAmbientTempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ambientTempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 6 }

cucsComputeRackUnitMbTempStatsHistAmbientTempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ambientTempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 7 }

cucsComputeRackUnitMbTempStatsHistFrontTemp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:frontTemp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 8 }

cucsComputeRackUnitMbTempStatsHistFrontTempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:frontTempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 9 }

cucsComputeRackUnitMbTempStatsHistFrontTempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:frontTempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 10 }

cucsComputeRackUnitMbTempStatsHistFrontTempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:frontTempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 11 }

cucsComputeRackUnitMbTempStatsHistId OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:id managed
        object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 12 }

cucsComputeRackUnitMbTempStatsHistIoh1Temp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh1Temp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 13 }

cucsComputeRackUnitMbTempStatsHistIoh1TempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh1TempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 14 }

cucsComputeRackUnitMbTempStatsHistIoh1TempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh1TempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 15 }

cucsComputeRackUnitMbTempStatsHistIoh1TempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh1TempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 16 }

cucsComputeRackUnitMbTempStatsHistIoh2Temp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh2Temp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 17 }

cucsComputeRackUnitMbTempStatsHistIoh2TempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh2TempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 18 }

cucsComputeRackUnitMbTempStatsHistIoh2TempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh2TempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 19 }

cucsComputeRackUnitMbTempStatsHistIoh2TempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:ioh2TempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 20 }

cucsComputeRackUnitMbTempStatsHistMostRecent OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:mostRecent
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 21 }

cucsComputeRackUnitMbTempStatsHistRearTemp OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:rearTemp
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 22 }

cucsComputeRackUnitMbTempStatsHistRearTempAvg OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:rearTempAvg
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 23 }

cucsComputeRackUnitMbTempStatsHistRearTempMax OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:rearTempMax
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 24 }

cucsComputeRackUnitMbTempStatsHistRearTempMin OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:rearTempMin
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 25 }

cucsComputeRackUnitMbTempStatsHistSuspect OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:suspect
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 26 }

cucsComputeRackUnitMbTempStatsHistThresholded OBJECT-TYPE
    SYNTAX       CucsComputeRackUnitMbTempStatsHistThresholded
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:thresholded
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 27 }

cucsComputeRackUnitMbTempStatsHistTimeCollected OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RackUnitMbTempStatsHist:timeCollected
        managed object property"
    ::= { cucsComputeRackUnitMbTempStatsHistEntry 28 }

cucsComputeRtcBatteryTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeRtcBatteryEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery managed object table"
    ::= { cucsComputeObjects 37 }

cucsComputeRtcBatteryEntry OBJECT-TYPE
    SYNTAX           CucsComputeRtcBatteryEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeRtcBatteryTable table."
    INDEX { cucsComputeRtcBatteryInstanceId }
    ::= { cucsComputeRtcBatteryTable 1 }

CucsComputeRtcBatteryEntry ::= SEQUENCE {
    cucsComputeRtcBatteryInstanceId                                  CucsManagedObjectId,
    cucsComputeRtcBatteryDn                                          CucsManagedObjectDn,
    cucsComputeRtcBatteryRn                                          SnmpAdminString,
    cucsComputeRtcBatteryId                                          Gauge32,
    cucsComputeRtcBatteryModel                                       SnmpAdminString,
    cucsComputeRtcBatteryOperState                                   CucsEquipmentOperability,
    cucsComputeRtcBatteryOperability                                 CucsEquipmentOperability,
    cucsComputeRtcBatteryPerf                                        CucsEquipmentSensorThresholdStatus,
    cucsComputeRtcBatteryPower                                       CucsEquipmentPowerState,
    cucsComputeRtcBatteryPresence                                    CucsEquipmentPresence,
    cucsComputeRtcBatteryRevision                                    SnmpAdminString,
    cucsComputeRtcBatterySerial                                      SnmpAdminString,
    cucsComputeRtcBatteryThermal                                     CucsEquipmentSensorThresholdStatus,
    cucsComputeRtcBatteryVendor                                      SnmpAdminString,
    cucsComputeRtcBatteryVoltage                                     CucsEquipmentSensorThresholdStatus,
    cucsComputeRtcBatteryOperQualifierReason                         SnmpAdminString,
    cucsComputeRtcBatteryLocationDn                                  SnmpAdminString
}

cucsComputeRtcBatteryInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeRtcBatteryEntry 1 }

cucsComputeRtcBatteryDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:dn managed object property"
    ::= { cucsComputeRtcBatteryEntry 2 }

cucsComputeRtcBatteryRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:rn managed object property"
    ::= { cucsComputeRtcBatteryEntry 3 }

cucsComputeRtcBatteryId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:id managed object property"
    ::= { cucsComputeRtcBatteryEntry 4 }

cucsComputeRtcBatteryModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:model managed object property"
    ::= { cucsComputeRtcBatteryEntry 5 }

cucsComputeRtcBatteryOperState OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:operState managed object property"
    ::= { cucsComputeRtcBatteryEntry 6 }

cucsComputeRtcBatteryOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:operability managed object property"
    ::= { cucsComputeRtcBatteryEntry 7 }

cucsComputeRtcBatteryPerf OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:perf managed object property"
    ::= { cucsComputeRtcBatteryEntry 8 }

cucsComputeRtcBatteryPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:power managed object property"
    ::= { cucsComputeRtcBatteryEntry 9 }

cucsComputeRtcBatteryPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentPresence
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:presence managed object property"
    ::= { cucsComputeRtcBatteryEntry 10 }

cucsComputeRtcBatteryRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:revision managed object property"
    ::= { cucsComputeRtcBatteryEntry 11 }

cucsComputeRtcBatterySerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:serial managed object property"
    ::= { cucsComputeRtcBatteryEntry 12 }

cucsComputeRtcBatteryThermal OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:thermal managed object property"
    ::= { cucsComputeRtcBatteryEntry 13 }

cucsComputeRtcBatteryVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:vendor managed object property"
    ::= { cucsComputeRtcBatteryEntry 14 }

cucsComputeRtcBatteryVoltage OBJECT-TYPE
    SYNTAX       CucsEquipmentSensorThresholdStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:voltage managed object property"
    ::= { cucsComputeRtcBatteryEntry 15 }

cucsComputeRtcBatteryOperQualifierReason OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:operQualifierReason
        managed object property"
    ::= { cucsComputeRtcBatteryEntry 16 }

cucsComputeRtcBatteryLocationDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:RtcBattery:locationDn managed object property"
    ::= { cucsComputeRtcBatteryEntry 17 }

cucsComputeScrubPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeScrubPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy managed object table"
    ::= { cucsComputeObjects 38 }

cucsComputeScrubPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeScrubPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeScrubPolicyTable table."
    INDEX { cucsComputeScrubPolicyInstanceId }
    ::= { cucsComputeScrubPolicyTable 1 }

CucsComputeScrubPolicyEntry ::= SEQUENCE {
    cucsComputeScrubPolicyInstanceId                                 CucsManagedObjectId,
    cucsComputeScrubPolicyDn                                         CucsManagedObjectDn,
    cucsComputeScrubPolicyRn                                         SnmpAdminString,
    cucsComputeScrubPolicyBiosSettingsScrub                          CucsComputeScrubAction,
    cucsComputeScrubPolicyDescr                                      SnmpAdminString,
    cucsComputeScrubPolicyDiskScrub                                  CucsComputeScrubAction,
    cucsComputeScrubPolicyIntId                                      SnmpAdminString,
    cucsComputeScrubPolicyName                                       SnmpAdminString,
    cucsComputeScrubPolicyPolicyLevel                                Gauge32,
    cucsComputeScrubPolicyPolicyOwner                                CucsPolicyPolicyOwner,
    cucsComputeScrubPolicyFlexFlashScrub                             CucsComputeScrubAction
}

cucsComputeScrubPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeScrubPolicyEntry 1 }

cucsComputeScrubPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:dn managed object property"
    ::= { cucsComputeScrubPolicyEntry 2 }

cucsComputeScrubPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:rn managed object property"
    ::= { cucsComputeScrubPolicyEntry 3 }

cucsComputeScrubPolicyBiosSettingsScrub OBJECT-TYPE
    SYNTAX       CucsComputeScrubAction
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:biosSettingsScrub
        managed object property"
    ::= { cucsComputeScrubPolicyEntry 4 }

cucsComputeScrubPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:descr managed object property"
    ::= { cucsComputeScrubPolicyEntry 5 }

cucsComputeScrubPolicyDiskScrub OBJECT-TYPE
    SYNTAX       CucsComputeScrubAction
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:diskScrub managed object property"
    ::= { cucsComputeScrubPolicyEntry 6 }

cucsComputeScrubPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:intId managed object property"
    ::= { cucsComputeScrubPolicyEntry 7 }

cucsComputeScrubPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:name managed object property"
    ::= { cucsComputeScrubPolicyEntry 8 }

cucsComputeScrubPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:policyLevel managed
        object property"
    ::= { cucsComputeScrubPolicyEntry 9 }

cucsComputeScrubPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:policyOwner managed
        object property"
    ::= { cucsComputeScrubPolicyEntry 10 }

cucsComputeScrubPolicyFlexFlashScrub OBJECT-TYPE
    SYNTAX       CucsComputeScrubAction
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ScrubPolicy:flexFlashScrub managed
        object property"
    ::= { cucsComputeScrubPolicyEntry 11 }

cucsComputeServerDiscPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerDiscPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy managed object table"
    ::= { cucsComputeObjects 39 }

cucsComputeServerDiscPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerDiscPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerDiscPolicyTable table."
    INDEX { cucsComputeServerDiscPolicyInstanceId }
    ::= { cucsComputeServerDiscPolicyTable 1 }

CucsComputeServerDiscPolicyEntry ::= SEQUENCE {
    cucsComputeServerDiscPolicyInstanceId                            CucsManagedObjectId,
    cucsComputeServerDiscPolicyDn                                    CucsManagedObjectDn,
    cucsComputeServerDiscPolicyRn                                    SnmpAdminString,
    cucsComputeServerDiscPolicyAction                                SnmpAdminString,
    cucsComputeServerDiscPolicyDescr                                 SnmpAdminString,
    cucsComputeServerDiscPolicyIntId                                 SnmpAdminString,
    cucsComputeServerDiscPolicyName                                  SnmpAdminString,
    cucsComputeServerDiscPolicyQualifier                             SnmpAdminString,
    cucsComputeServerDiscPolicyScrubPolicyName                       SnmpAdminString,
    cucsComputeServerDiscPolicyFsmDescr                              SnmpAdminString,
    cucsComputeServerDiscPolicyFsmPrev                               SnmpAdminString,
    cucsComputeServerDiscPolicyFsmProgr                              Gauge32,
    cucsComputeServerDiscPolicyFsmRmtInvErrCode                      Gauge32,
    cucsComputeServerDiscPolicyFsmRmtInvErrDescr                     SnmpAdminString,
    cucsComputeServerDiscPolicyFsmRmtInvRslt                         CucsConditionRemoteInvRslt,
    cucsComputeServerDiscPolicyFsmStageDescr                         SnmpAdminString,
    cucsComputeServerDiscPolicyFsmStamp                              DateAndTime,
    cucsComputeServerDiscPolicyFsmStatus                             SnmpAdminString,
    cucsComputeServerDiscPolicyFsmTry                                Gauge32,
    cucsComputeServerDiscPolicyPolicyLevel                           Gauge32,
    cucsComputeServerDiscPolicyPolicyOwner                           CucsPolicyPolicyOwner
}

cucsComputeServerDiscPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerDiscPolicyEntry 1 }

cucsComputeServerDiscPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:dn managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 2 }

cucsComputeServerDiscPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:rn managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 3 }

cucsComputeServerDiscPolicyAction OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:action managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 4 }

cucsComputeServerDiscPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:descr managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 5 }

cucsComputeServerDiscPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:intId managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 6 }

cucsComputeServerDiscPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:name managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 7 }

cucsComputeServerDiscPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:qualifier managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 8 }

cucsComputeServerDiscPolicyScrubPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:scrubPolicyName
        managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 9 }

cucsComputeServerDiscPolicyFsmDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmDescr managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 10 }

cucsComputeServerDiscPolicyFsmPrev OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmPrev managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 11 }

cucsComputeServerDiscPolicyFsmProgr OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmProgr managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 12 }

cucsComputeServerDiscPolicyFsmRmtInvErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmRmtInvErrCode
        managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 13 }

cucsComputeServerDiscPolicyFsmRmtInvErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmRmtInvErrDescr
        managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 14 }

cucsComputeServerDiscPolicyFsmRmtInvRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmRmtInvRslt
        managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 15 }

cucsComputeServerDiscPolicyFsmStageDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmStageDescr
        managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 16 }

cucsComputeServerDiscPolicyFsmStamp OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmStamp managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 17 }

cucsComputeServerDiscPolicyFsmStatus OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmStatus managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 18 }

cucsComputeServerDiscPolicyFsmTry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:fsmTry managed
        object property"
    ::= { cucsComputeServerDiscPolicyEntry 19 }

cucsComputeServerDiscPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:policyLevel
        managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 20 }

cucsComputeServerDiscPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicy:policyOwner
        managed object property"
    ::= { cucsComputeServerDiscPolicyEntry 21 }

cucsComputeServerDiscPolicyFsmTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerDiscPolicyFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm managed object table"
    ::= { cucsComputeObjects 54 }

cucsComputeServerDiscPolicyFsmEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerDiscPolicyFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerDiscPolicyFsmTable table."
    INDEX { cucsComputeServerDiscPolicyFsmInstanceId }
    ::= { cucsComputeServerDiscPolicyFsmTable 1 }

CucsComputeServerDiscPolicyFsmEntry ::= SEQUENCE {
    cucsComputeServerDiscPolicyFsmInstanceId                         CucsManagedObjectId,
    cucsComputeServerDiscPolicyFsmDn                                 CucsManagedObjectDn,
    cucsComputeServerDiscPolicyFsmRn                                 SnmpAdminString,
    cucsComputeServerDiscPolicyFsmCompletionTime                     DateAndTime,
    cucsComputeServerDiscPolicyFsmCurrentFsm                         CucsComputeServerDiscPolicyFsmCurrentFsm,
    cucsComputeServerDiscPolicyFsmDescrData                          SnmpAdminString,
    cucsComputeServerDiscPolicyFsmFsmStatus                          CucsFsmFsmStageStatus,
    cucsComputeServerDiscPolicyFsmProgress                           Gauge32,
    cucsComputeServerDiscPolicyFsmRmtErrCode                         Gauge32,
    cucsComputeServerDiscPolicyFsmRmtErrDescr                        SnmpAdminString,
    cucsComputeServerDiscPolicyFsmRmtRslt                            CucsConditionRemoteInvRslt
}

cucsComputeServerDiscPolicyFsmInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerDiscPolicyFsmEntry 1 }

cucsComputeServerDiscPolicyFsmDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:dn managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 2 }

cucsComputeServerDiscPolicyFsmRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:rn managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 3 }

cucsComputeServerDiscPolicyFsmCompletionTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:completionTime
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 4 }

cucsComputeServerDiscPolicyFsmCurrentFsm OBJECT-TYPE
    SYNTAX       CucsComputeServerDiscPolicyFsmCurrentFsm
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:currentFsm
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 5 }

cucsComputeServerDiscPolicyFsmDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:descr managed
        object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 6 }

cucsComputeServerDiscPolicyFsmFsmStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:fsmStatus
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 7 }

cucsComputeServerDiscPolicyFsmProgress OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:progress
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 8 }

cucsComputeServerDiscPolicyFsmRmtErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:rmtErrCode
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 9 }

cucsComputeServerDiscPolicyFsmRmtErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:rmtErrDescr
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 10 }

cucsComputeServerDiscPolicyFsmRmtRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsm:rmtRslt
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmEntry 11 }

cucsComputeServerDiscPolicyFsmStageTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerDiscPolicyFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage managed object table"
    ::= { cucsComputeObjects 55 }

cucsComputeServerDiscPolicyFsmStageEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerDiscPolicyFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerDiscPolicyFsmStageTable table."
    INDEX { cucsComputeServerDiscPolicyFsmStageInstanceId }
    ::= { cucsComputeServerDiscPolicyFsmStageTable 1 }

CucsComputeServerDiscPolicyFsmStageEntry ::= SEQUENCE {
    cucsComputeServerDiscPolicyFsmStageInstanceId                    CucsManagedObjectId,
    cucsComputeServerDiscPolicyFsmStageDn                            CucsManagedObjectDn,
    cucsComputeServerDiscPolicyFsmStageRn                            SnmpAdminString,
    cucsComputeServerDiscPolicyFsmStageDescrData                     SnmpAdminString,
    cucsComputeServerDiscPolicyFsmStageLastUpdateTime                DateAndTime,
    cucsComputeServerDiscPolicyFsmStageName                          CucsComputeServerDiscPolicyFsmStageName,
    cucsComputeServerDiscPolicyFsmStageOrder                         Gauge32,
    cucsComputeServerDiscPolicyFsmStageRetry                         Gauge32,
    cucsComputeServerDiscPolicyFsmStageStageStatus                   CucsFsmFsmStageStatus
}

cucsComputeServerDiscPolicyFsmStageInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 1 }

cucsComputeServerDiscPolicyFsmStageDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:dn
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 2 }

cucsComputeServerDiscPolicyFsmStageRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:rn
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 3 }

cucsComputeServerDiscPolicyFsmStageDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:descr
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 4 }

cucsComputeServerDiscPolicyFsmStageLastUpdateTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:lastUpdateTime
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 5 }

cucsComputeServerDiscPolicyFsmStageName OBJECT-TYPE
    SYNTAX       CucsComputeServerDiscPolicyFsmStageName
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:name
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 6 }

cucsComputeServerDiscPolicyFsmStageOrder OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:order
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 7 }

cucsComputeServerDiscPolicyFsmStageRetry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:retry
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 8 }

cucsComputeServerDiscPolicyFsmStageStageStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmStage:stageStatus
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmStageEntry 9 }

cucsComputeServerDiscPolicyFsmTaskTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerDiscPolicyFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmTask managed object table"
    ::= { cucsComputeObjects 56 }

cucsComputeServerDiscPolicyFsmTaskEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerDiscPolicyFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerDiscPolicyFsmTaskTable table."
    INDEX { cucsComputeServerDiscPolicyFsmTaskInstanceId }
    ::= { cucsComputeServerDiscPolicyFsmTaskTable 1 }

CucsComputeServerDiscPolicyFsmTaskEntry ::= SEQUENCE {
    cucsComputeServerDiscPolicyFsmTaskInstanceId                     CucsManagedObjectId,
    cucsComputeServerDiscPolicyFsmTaskDn                             CucsManagedObjectDn,
    cucsComputeServerDiscPolicyFsmTaskRn                             SnmpAdminString,
    cucsComputeServerDiscPolicyFsmTaskCompletion                     CucsFsmCompletion,
    cucsComputeServerDiscPolicyFsmTaskFlags                          CucsFsmFlags,
    cucsComputeServerDiscPolicyFsmTaskItem                           CucsComputeServerDiscPolicyFsmTaskItem,
    cucsComputeServerDiscPolicyFsmTaskSeqId                          Gauge32
}

cucsComputeServerDiscPolicyFsmTaskInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerDiscPolicyFsmTaskEntry 1 }

cucsComputeServerDiscPolicyFsmTaskDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmTask:dn managed
        object property"
    ::= { cucsComputeServerDiscPolicyFsmTaskEntry 2 }

cucsComputeServerDiscPolicyFsmTaskRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmTask:rn managed
        object property"
    ::= { cucsComputeServerDiscPolicyFsmTaskEntry 3 }

cucsComputeServerDiscPolicyFsmTaskCompletion OBJECT-TYPE
    SYNTAX       CucsFsmCompletion
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmTask:completion
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmTaskEntry 4 }

cucsComputeServerDiscPolicyFsmTaskFlags OBJECT-TYPE
    SYNTAX       CucsFsmFlags
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmTask:flags
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmTaskEntry 5 }

cucsComputeServerDiscPolicyFsmTaskItem OBJECT-TYPE
    SYNTAX       CucsComputeServerDiscPolicyFsmTaskItem
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmTask:item
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmTaskEntry 6 }

cucsComputeServerDiscPolicyFsmTaskSeqId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerDiscPolicyFsmTask:seqId
        managed object property"
    ::= { cucsComputeServerDiscPolicyFsmTaskEntry 7 }

cucsComputeServerMgmtPolicyTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerMgmtPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy managed object table"
    ::= { cucsComputeObjects 57 }

cucsComputeServerMgmtPolicyEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerMgmtPolicyEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerMgmtPolicyTable table."
    INDEX { cucsComputeServerMgmtPolicyInstanceId }
    ::= { cucsComputeServerMgmtPolicyTable 1 }

CucsComputeServerMgmtPolicyEntry ::= SEQUENCE {
    cucsComputeServerMgmtPolicyInstanceId                            CucsManagedObjectId,
    cucsComputeServerMgmtPolicyDn                                    CucsManagedObjectDn,
    cucsComputeServerMgmtPolicyRn                                    SnmpAdminString,
    cucsComputeServerMgmtPolicyAction                                CucsComputeServerMgmtDiscAction,
    cucsComputeServerMgmtPolicyDescr                                 SnmpAdminString,
    cucsComputeServerMgmtPolicyIntId                                 SnmpAdminString,
    cucsComputeServerMgmtPolicyName                                  SnmpAdminString,
    cucsComputeServerMgmtPolicyPolicyLevel                           Gauge32,
    cucsComputeServerMgmtPolicyPolicyOwner                           CucsPolicyPolicyOwner,
    cucsComputeServerMgmtPolicyQualifier                             SnmpAdminString
}

cucsComputeServerMgmtPolicyInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerMgmtPolicyEntry 1 }

cucsComputeServerMgmtPolicyDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:dn managed object property"
    ::= { cucsComputeServerMgmtPolicyEntry 2 }

cucsComputeServerMgmtPolicyRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:rn managed object property"
    ::= { cucsComputeServerMgmtPolicyEntry 3 }

cucsComputeServerMgmtPolicyAction OBJECT-TYPE
    SYNTAX       CucsComputeServerMgmtDiscAction
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:action managed
        object property"
    ::= { cucsComputeServerMgmtPolicyEntry 4 }

cucsComputeServerMgmtPolicyDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:descr managed object property"
    ::= { cucsComputeServerMgmtPolicyEntry 5 }

cucsComputeServerMgmtPolicyIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:intId managed object property"
    ::= { cucsComputeServerMgmtPolicyEntry 6 }

cucsComputeServerMgmtPolicyName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:name managed object property"
    ::= { cucsComputeServerMgmtPolicyEntry 7 }

cucsComputeServerMgmtPolicyPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:policyLevel
        managed object property"
    ::= { cucsComputeServerMgmtPolicyEntry 8 }

cucsComputeServerMgmtPolicyPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:policyOwner
        managed object property"
    ::= { cucsComputeServerMgmtPolicyEntry 9 }

cucsComputeServerMgmtPolicyQualifier OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerMgmtPolicy:qualifier managed
        object property"
    ::= { cucsComputeServerMgmtPolicyEntry 10 }

cucsComputeServerTypeCapTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerTypeCapEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerTypeCap managed object table"
    ::= { cucsComputeObjects 68 }

cucsComputeServerTypeCapEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerTypeCapEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerTypeCapTable table."
    INDEX { cucsComputeServerTypeCapInstanceId }
    ::= { cucsComputeServerTypeCapTable 1 }

CucsComputeServerTypeCapEntry ::= SEQUENCE {
    cucsComputeServerTypeCapInstanceId                               CucsManagedObjectId,
    cucsComputeServerTypeCapDn                                       CucsManagedObjectDn,
    cucsComputeServerTypeCapRn                                       SnmpAdminString,
    cucsComputeServerTypeCapType                                     CucsComputeServerTypeCapType
}

cucsComputeServerTypeCapInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerTypeCapEntry 1 }

cucsComputeServerTypeCapDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerTypeCap:dn managed object property"
    ::= { cucsComputeServerTypeCapEntry 2 }

cucsComputeServerTypeCapRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerTypeCap:rn managed object property"
    ::= { cucsComputeServerTypeCapEntry 3 }

cucsComputeServerTypeCapType OBJECT-TYPE
    SYNTAX       CucsComputeServerTypeCapType
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerTypeCap:type managed object property"
    ::= { cucsComputeServerTypeCapEntry 4 }

cucsComputeServerUnitTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerUnitEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit managed object table"
    ::= { cucsComputeObjects 74 }

cucsComputeServerUnitEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerUnitEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerUnitTable table."
    INDEX { cucsComputeServerUnitInstanceId }
    ::= { cucsComputeServerUnitTable 1 }

CucsComputeServerUnitEntry ::= SEQUENCE {
    cucsComputeServerUnitInstanceId                                  CucsManagedObjectId,
    cucsComputeServerUnitDn                                          CucsManagedObjectDn,
    cucsComputeServerUnitRn                                          SnmpAdminString,
    cucsComputeServerUnitAdminPower                                  CucsComputeAdminPowerState,
    cucsComputeServerUnitAdminState                                  CucsComputeAdminState,
    cucsComputeServerUnitAssignedToDn                                SnmpAdminString,
    cucsComputeServerUnitAssociation                                 CucsComputeAssociation,
    cucsComputeServerUnitAvailability                                CucsComputeAvailability,
    cucsComputeServerUnitAvailableMemory                             Gauge32,
    cucsComputeServerUnitChassisId                                   CucsComputeServerUnitChassisId,
    cucsComputeServerUnitCheckPoint                                  CucsComputeCheckPoint,
    cucsComputeServerUnitConnPath                                    CucsEquipmentConnectionStatus,
    cucsComputeServerUnitConnStatus                                  CucsEquipmentConnectionStatus,
    cucsComputeServerUnitDescr                                       SnmpAdminString,
    cucsComputeServerUnitDiscovery                                   CucsComputeDiscovery,
    cucsComputeServerUnitDiscoveryStatus                             CucsEquipmentConnectionStatus,
    cucsComputeServerUnitFltAggr                                     Unsigned64,
    cucsComputeServerUnitFsmDescr                                    SnmpAdminString,
    cucsComputeServerUnitFsmFlags                                    SnmpAdminString,
    cucsComputeServerUnitFsmPrev                                     SnmpAdminString,
    cucsComputeServerUnitFsmProgr                                    Gauge32,
    cucsComputeServerUnitFsmRmtInvErrCode                            Gauge32,
    cucsComputeServerUnitFsmRmtInvErrDescr                           SnmpAdminString,
    cucsComputeServerUnitFsmRmtInvRslt                               CucsConditionRemoteInvRslt,
    cucsComputeServerUnitFsmStageDescr                               SnmpAdminString,
    cucsComputeServerUnitFsmStamp                                    DateAndTime,
    cucsComputeServerUnitFsmStatus                                   SnmpAdminString,
    cucsComputeServerUnitFsmTry                                      Gauge32,
    cucsComputeServerUnitIntId                                       SnmpAdminString,
    cucsComputeServerUnitLc                                          CucsComputeAdminTrigger,
    cucsComputeServerUnitLcTs                                        DateAndTime,
    cucsComputeServerUnitLocalId                                     SnmpAdminString,
    cucsComputeServerUnitLowVoltageMemory                            CucsComputePhysicalLowVoltageMemory,
    cucsComputeServerUnitManagingInst                                CucsNetworkSwitchId,
    cucsComputeServerUnitMemorySpeed                                 Gauge32,
    cucsComputeServerUnitMfgTime                                     DateAndTime,
    cucsComputeServerUnitModel                                       SnmpAdminString,
    cucsComputeServerUnitName                                        SnmpAdminString,
    cucsComputeServerUnitNumOfAdaptors                               Gauge32,
    cucsComputeServerUnitNumOfCores                                  Gauge32,
    cucsComputeServerUnitNumOfCoresEnabled                           Gauge32,
    cucsComputeServerUnitNumOfCpus                                   Gauge32,
    cucsComputeServerUnitNumOfEthHostIfs                             Gauge32,
    cucsComputeServerUnitNumOfFcHostIfs                              Gauge32,
    cucsComputeServerUnitNumOfThreads                                Gauge32,
    cucsComputeServerUnitOperPower                                   CucsEquipmentPowerState,
    cucsComputeServerUnitOperQualifier                               CucsComputeIssues,
    cucsComputeServerUnitOperState                                   CucsLsOperState,
    cucsComputeServerUnitOperability                                 CucsEquipmentOperability,
    cucsComputeServerUnitOriginalUuid                                SnmpAdminString,
    cucsComputeServerUnitPartNumber                                  SnmpAdminString,
    cucsComputeServerUnitPolicyLevel                                 Gauge32,
    cucsComputeServerUnitPolicyOwner                                 CucsPolicyPolicyOwner,
    cucsComputeServerUnitPresence                                    CucsEquipmentSlotStatus,
    cucsComputeServerUnitRevision                                    SnmpAdminString,
    cucsComputeServerUnitSerial                                      SnmpAdminString,
    cucsComputeServerUnitServerId                                    SnmpAdminString,
    cucsComputeServerUnitServerInstanceId                            CucsComputeServerUnitServerInstanceId,
    cucsComputeServerUnitSlotId                                      CucsComputeServerUnitSlotId,
    cucsComputeServerUnitTotalMemory                                 Gauge32,
    cucsComputeServerUnitUsrLbl                                      SnmpAdminString,
    cucsComputeServerUnitUuid                                        SnmpAdminString,
    cucsComputeServerUnitVendor                                      SnmpAdminString,
    cucsComputeServerUnitVid                                         SnmpAdminString,
    cucsComputeServerUnitNumOf40GAdaptorsWithOldFw                   Gauge32,
    cucsComputeServerUnitNumOf40GAdaptorsWithUnknownFw               Gauge32,
    cucsComputeServerUnitOperPwrTransSrc                             CucsComputePowerTransitionSrc
}

cucsComputeServerUnitInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerUnitEntry 1 }

cucsComputeServerUnitDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:dn managed object property"
    ::= { cucsComputeServerUnitEntry 2 }

cucsComputeServerUnitRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:rn managed object property"
    ::= { cucsComputeServerUnitEntry 3 }

cucsComputeServerUnitAdminPower OBJECT-TYPE
    SYNTAX       CucsComputeAdminPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:adminPower managed object property"
    ::= { cucsComputeServerUnitEntry 4 }

cucsComputeServerUnitAdminState OBJECT-TYPE
    SYNTAX       CucsComputeAdminState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:adminState managed object property"
    ::= { cucsComputeServerUnitEntry 5 }

cucsComputeServerUnitAssignedToDn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:assignedToDn managed
        object property"
    ::= { cucsComputeServerUnitEntry 6 }

cucsComputeServerUnitAssociation OBJECT-TYPE
    SYNTAX       CucsComputeAssociation
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:association managed object property"
    ::= { cucsComputeServerUnitEntry 7 }

cucsComputeServerUnitAvailability OBJECT-TYPE
    SYNTAX       CucsComputeAvailability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:availability managed
        object property"
    ::= { cucsComputeServerUnitEntry 8 }

cucsComputeServerUnitAvailableMemory OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:availableMemory managed
        object property"
    ::= { cucsComputeServerUnitEntry 9 }

cucsComputeServerUnitChassisId OBJECT-TYPE
    SYNTAX       CucsComputeServerUnitChassisId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:chassisId managed object property"
    ::= { cucsComputeServerUnitEntry 10 }

cucsComputeServerUnitCheckPoint OBJECT-TYPE
    SYNTAX       CucsComputeCheckPoint
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:checkPoint managed object property"
    ::= { cucsComputeServerUnitEntry 11 }

cucsComputeServerUnitConnPath OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:connPath managed object property"
    ::= { cucsComputeServerUnitEntry 12 }

cucsComputeServerUnitConnStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:connStatus managed object property"
    ::= { cucsComputeServerUnitEntry 13 }

cucsComputeServerUnitDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:descr managed object property"
    ::= { cucsComputeServerUnitEntry 14 }

cucsComputeServerUnitDiscovery OBJECT-TYPE
    SYNTAX       CucsComputeDiscovery
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:discovery managed object property"
    ::= { cucsComputeServerUnitEntry 15 }

cucsComputeServerUnitDiscoveryStatus OBJECT-TYPE
    SYNTAX       CucsEquipmentConnectionStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:discoveryStatus managed
        object property"
    ::= { cucsComputeServerUnitEntry 16 }

cucsComputeServerUnitFltAggr OBJECT-TYPE
    SYNTAX       Unsigned64
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fltAggr managed object property"
    ::= { cucsComputeServerUnitEntry 17 }

cucsComputeServerUnitFsmDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmDescr managed object property"
    ::= { cucsComputeServerUnitEntry 18 }

cucsComputeServerUnitFsmFlags OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmFlags managed object property"
    ::= { cucsComputeServerUnitEntry 19 }

cucsComputeServerUnitFsmPrev OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmPrev managed object property"
    ::= { cucsComputeServerUnitEntry 20 }

cucsComputeServerUnitFsmProgr OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmProgr managed object property"
    ::= { cucsComputeServerUnitEntry 21 }

cucsComputeServerUnitFsmRmtInvErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmRmtInvErrCode
        managed object property"
    ::= { cucsComputeServerUnitEntry 22 }

cucsComputeServerUnitFsmRmtInvErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmRmtInvErrDescr
        managed object property"
    ::= { cucsComputeServerUnitEntry 23 }

cucsComputeServerUnitFsmRmtInvRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmRmtInvRslt managed
        object property"
    ::= { cucsComputeServerUnitEntry 24 }

cucsComputeServerUnitFsmStageDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmStageDescr managed
        object property"
    ::= { cucsComputeServerUnitEntry 25 }

cucsComputeServerUnitFsmStamp OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmStamp managed object property"
    ::= { cucsComputeServerUnitEntry 26 }

cucsComputeServerUnitFsmStatus OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmStatus managed object property"
    ::= { cucsComputeServerUnitEntry 27 }

cucsComputeServerUnitFsmTry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:fsmTry managed object property"
    ::= { cucsComputeServerUnitEntry 28 }

cucsComputeServerUnitIntId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:intId managed object property"
    ::= { cucsComputeServerUnitEntry 29 }

cucsComputeServerUnitLc OBJECT-TYPE
    SYNTAX       CucsComputeAdminTrigger
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:lc managed object property"
    ::= { cucsComputeServerUnitEntry 30 }

cucsComputeServerUnitLcTs OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:lcTs managed object property"
    ::= { cucsComputeServerUnitEntry 31 }

cucsComputeServerUnitLocalId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:localId managed object property"
    ::= { cucsComputeServerUnitEntry 32 }

cucsComputeServerUnitLowVoltageMemory OBJECT-TYPE
    SYNTAX       CucsComputePhysicalLowVoltageMemory
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:lowVoltageMemory
        managed object property"
    ::= { cucsComputeServerUnitEntry 33 }

cucsComputeServerUnitManagingInst OBJECT-TYPE
    SYNTAX       CucsNetworkSwitchId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:managingInst managed
        object property"
    ::= { cucsComputeServerUnitEntry 34 }

cucsComputeServerUnitMemorySpeed OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:memorySpeed managed object property"
    ::= { cucsComputeServerUnitEntry 35 }

cucsComputeServerUnitMfgTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:mfgTime managed object property"
    ::= { cucsComputeServerUnitEntry 36 }

cucsComputeServerUnitModel OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:model managed object property"
    ::= { cucsComputeServerUnitEntry 37 }

cucsComputeServerUnitName OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:name managed object property"
    ::= { cucsComputeServerUnitEntry 38 }

cucsComputeServerUnitNumOfAdaptors OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOfAdaptors managed
        object property"
    ::= { cucsComputeServerUnitEntry 39 }

cucsComputeServerUnitNumOfCores OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOfCores managed object property"
    ::= { cucsComputeServerUnitEntry 40 }

cucsComputeServerUnitNumOfCoresEnabled OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOfCoresEnabled
        managed object property"
    ::= { cucsComputeServerUnitEntry 41 }

cucsComputeServerUnitNumOfCpus OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOfCpus managed object property"
    ::= { cucsComputeServerUnitEntry 42 }

cucsComputeServerUnitNumOfEthHostIfs OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOfEthHostIfs managed
        object property"
    ::= { cucsComputeServerUnitEntry 43 }

cucsComputeServerUnitNumOfFcHostIfs OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOfFcHostIfs managed
        object property"
    ::= { cucsComputeServerUnitEntry 44 }

cucsComputeServerUnitNumOfThreads OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOfThreads managed
        object property"
    ::= { cucsComputeServerUnitEntry 45 }

cucsComputeServerUnitOperPower OBJECT-TYPE
    SYNTAX       CucsEquipmentPowerState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:operPower managed object property"
    ::= { cucsComputeServerUnitEntry 46 }

cucsComputeServerUnitOperQualifier OBJECT-TYPE
    SYNTAX       CucsComputeIssues
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:operQualifier managed
        object property"
    ::= { cucsComputeServerUnitEntry 47 }

cucsComputeServerUnitOperState OBJECT-TYPE
    SYNTAX       CucsLsOperState
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:operState managed object property"
    ::= { cucsComputeServerUnitEntry 48 }

cucsComputeServerUnitOperability OBJECT-TYPE
    SYNTAX       CucsEquipmentOperability
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:operability managed object property"
    ::= { cucsComputeServerUnitEntry 49 }

cucsComputeServerUnitOriginalUuid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:originalUuid managed
        object property"
    ::= { cucsComputeServerUnitEntry 50 }

cucsComputeServerUnitPartNumber OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:partNumber managed object property"
    ::= { cucsComputeServerUnitEntry 51 }

cucsComputeServerUnitPolicyLevel OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:policyLevel managed object property"
    ::= { cucsComputeServerUnitEntry 52 }

cucsComputeServerUnitPolicyOwner OBJECT-TYPE
    SYNTAX       CucsPolicyPolicyOwner
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:policyOwner managed object property"
    ::= { cucsComputeServerUnitEntry 53 }

cucsComputeServerUnitPresence OBJECT-TYPE
    SYNTAX       CucsEquipmentSlotStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:presence managed object property"
    ::= { cucsComputeServerUnitEntry 54 }

cucsComputeServerUnitRevision OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:revision managed object property"
    ::= { cucsComputeServerUnitEntry 55 }

cucsComputeServerUnitSerial OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:serial managed object property"
    ::= { cucsComputeServerUnitEntry 56 }

cucsComputeServerUnitServerId OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:serverId managed object property"
    ::= { cucsComputeServerUnitEntry 57 }

cucsComputeServerUnitServerInstanceId OBJECT-TYPE
    SYNTAX       CucsComputeServerUnitServerInstanceId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:serverInstanceId
        managed object property"
    ::= { cucsComputeServerUnitEntry 58 }

cucsComputeServerUnitSlotId OBJECT-TYPE
    SYNTAX       CucsComputeServerUnitSlotId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:slotId managed object property"
    ::= { cucsComputeServerUnitEntry 59 }

cucsComputeServerUnitTotalMemory OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:totalMemory managed object property"
    ::= { cucsComputeServerUnitEntry 60 }

cucsComputeServerUnitUsrLbl OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:usrLbl managed object property"
    ::= { cucsComputeServerUnitEntry 61 }

cucsComputeServerUnitUuid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:uuid managed object property"
    ::= { cucsComputeServerUnitEntry 62 }

cucsComputeServerUnitVendor OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:vendor managed object property"
    ::= { cucsComputeServerUnitEntry 63 }

cucsComputeServerUnitVid OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:vid managed object property"
    ::= { cucsComputeServerUnitEntry 64 }

cucsComputeServerUnitNumOf40GAdaptorsWithOldFw OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOf40GAdaptorsWithOldFw
        managed object property"
    ::= { cucsComputeServerUnitEntry 66 }

cucsComputeServerUnitNumOf40GAdaptorsWithUnknownFw OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:numOf40GAdaptorsWithUnknownFw
        managed object property"
    ::= { cucsComputeServerUnitEntry 67 }

cucsComputeServerUnitOperPwrTransSrc OBJECT-TYPE
    SYNTAX       CucsComputePowerTransitionSrc
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnit:operPwrTransSrc managed
        object property"
    ::= { cucsComputeServerUnitEntry 68 }

cucsComputeServerUnitFsmTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerUnitFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm managed object table"
    ::= { cucsComputeObjects 75 }

cucsComputeServerUnitFsmEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerUnitFsmEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerUnitFsmTable table."
    INDEX { cucsComputeServerUnitFsmInstanceId }
    ::= { cucsComputeServerUnitFsmTable 1 }

CucsComputeServerUnitFsmEntry ::= SEQUENCE {
    cucsComputeServerUnitFsmInstanceId                               CucsManagedObjectId,
    cucsComputeServerUnitFsmDn                                       CucsManagedObjectDn,
    cucsComputeServerUnitFsmRn                                       SnmpAdminString,
    cucsComputeServerUnitFsmCompletionTime                           DateAndTime,
    cucsComputeServerUnitFsmCurrentFsm                               CucsComputeServerUnitFsmCurrentFsm,
    cucsComputeServerUnitFsmDescrData                                SnmpAdminString,
    cucsComputeServerUnitFsmFsmStatus                                CucsFsmFsmStageStatus,
    cucsComputeServerUnitFsmProgress                                 Gauge32,
    cucsComputeServerUnitFsmRmtErrCode                               Gauge32,
    cucsComputeServerUnitFsmRmtErrDescr                              SnmpAdminString,
    cucsComputeServerUnitFsmRmtRslt                                  CucsConditionRemoteInvRslt
}

cucsComputeServerUnitFsmInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerUnitFsmEntry 1 }

cucsComputeServerUnitFsmDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:dn managed object property"
    ::= { cucsComputeServerUnitFsmEntry 2 }

cucsComputeServerUnitFsmRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:rn managed object property"
    ::= { cucsComputeServerUnitFsmEntry 3 }

cucsComputeServerUnitFsmCompletionTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:completionTime
        managed object property"
    ::= { cucsComputeServerUnitFsmEntry 4 }

cucsComputeServerUnitFsmCurrentFsm OBJECT-TYPE
    SYNTAX       CucsComputeServerUnitFsmCurrentFsm
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:currentFsm managed
        object property"
    ::= { cucsComputeServerUnitFsmEntry 5 }

cucsComputeServerUnitFsmDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:descr managed object property"
    ::= { cucsComputeServerUnitFsmEntry 6 }

cucsComputeServerUnitFsmFsmStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:fsmStatus managed
        object property"
    ::= { cucsComputeServerUnitFsmEntry 7 }

cucsComputeServerUnitFsmProgress OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:progress managed object property"
    ::= { cucsComputeServerUnitFsmEntry 8 }

cucsComputeServerUnitFsmRmtErrCode OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:rmtErrCode managed
        object property"
    ::= { cucsComputeServerUnitFsmEntry 9 }

cucsComputeServerUnitFsmRmtErrDescr OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:rmtErrDescr managed
        object property"
    ::= { cucsComputeServerUnitFsmEntry 10 }

cucsComputeServerUnitFsmRmtRslt OBJECT-TYPE
    SYNTAX       CucsConditionRemoteInvRslt
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsm:rmtRslt managed object property"
    ::= { cucsComputeServerUnitFsmEntry 11 }

cucsComputeServerUnitFsmStageTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerUnitFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage managed object table"
    ::= { cucsComputeObjects 76 }

cucsComputeServerUnitFsmStageEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerUnitFsmStageEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerUnitFsmStageTable table."
    INDEX { cucsComputeServerUnitFsmStageInstanceId }
    ::= { cucsComputeServerUnitFsmStageTable 1 }

CucsComputeServerUnitFsmStageEntry ::= SEQUENCE {
    cucsComputeServerUnitFsmStageInstanceId                          CucsManagedObjectId,
    cucsComputeServerUnitFsmStageDn                                  CucsManagedObjectDn,
    cucsComputeServerUnitFsmStageRn                                  SnmpAdminString,
    cucsComputeServerUnitFsmStageDescrData                           SnmpAdminString,
    cucsComputeServerUnitFsmStageLastUpdateTime                      DateAndTime,
    cucsComputeServerUnitFsmStageName                                CucsComputeServerUnitFsmStageName,
    cucsComputeServerUnitFsmStageOrder                               Gauge32,
    cucsComputeServerUnitFsmStageRetry                               Gauge32,
    cucsComputeServerUnitFsmStageStageStatus                         CucsFsmFsmStageStatus
}

cucsComputeServerUnitFsmStageInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerUnitFsmStageEntry 1 }

cucsComputeServerUnitFsmStageDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:dn managed object property"
    ::= { cucsComputeServerUnitFsmStageEntry 2 }

cucsComputeServerUnitFsmStageRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:rn managed object property"
    ::= { cucsComputeServerUnitFsmStageEntry 3 }

cucsComputeServerUnitFsmStageDescrData OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:descr managed
        object property"
    ::= { cucsComputeServerUnitFsmStageEntry 4 }

cucsComputeServerUnitFsmStageLastUpdateTime OBJECT-TYPE
    SYNTAX       DateAndTime
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:lastUpdateTime
        managed object property"
    ::= { cucsComputeServerUnitFsmStageEntry 5 }

cucsComputeServerUnitFsmStageName OBJECT-TYPE
    SYNTAX       CucsComputeServerUnitFsmStageName
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:name managed
        object property"
    ::= { cucsComputeServerUnitFsmStageEntry 6 }

cucsComputeServerUnitFsmStageOrder OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:order managed
        object property"
    ::= { cucsComputeServerUnitFsmStageEntry 7 }

cucsComputeServerUnitFsmStageRetry OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:retry managed
        object property"
    ::= { cucsComputeServerUnitFsmStageEntry 8 }

cucsComputeServerUnitFsmStageStageStatus OBJECT-TYPE
    SYNTAX       CucsFsmFsmStageStatus
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmStage:stageStatus
        managed object property"
    ::= { cucsComputeServerUnitFsmStageEntry 9 }

cucsComputeServerUnitFsmTaskTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeServerUnitFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmTask managed object table"
    ::= { cucsComputeObjects 77 }

cucsComputeServerUnitFsmTaskEntry OBJECT-TYPE
    SYNTAX           CucsComputeServerUnitFsmTaskEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeServerUnitFsmTaskTable table."
    INDEX { cucsComputeServerUnitFsmTaskInstanceId }
    ::= { cucsComputeServerUnitFsmTaskTable 1 }

CucsComputeServerUnitFsmTaskEntry ::= SEQUENCE {
    cucsComputeServerUnitFsmTaskInstanceId                           CucsManagedObjectId,
    cucsComputeServerUnitFsmTaskDn                                   CucsManagedObjectDn,
    cucsComputeServerUnitFsmTaskRn                                   SnmpAdminString,
    cucsComputeServerUnitFsmTaskCompletion                           CucsFsmCompletion,
    cucsComputeServerUnitFsmTaskFlags                                CucsComputeServerUnitFsmTaskFlags,
    cucsComputeServerUnitFsmTaskItem                                 CucsComputeServerUnitFsmTaskItem,
    cucsComputeServerUnitFsmTaskSeqId                                Gauge32
}

cucsComputeServerUnitFsmTaskInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeServerUnitFsmTaskEntry 1 }

cucsComputeServerUnitFsmTaskDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmTask:dn managed object property"
    ::= { cucsComputeServerUnitFsmTaskEntry 2 }

cucsComputeServerUnitFsmTaskRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmTask:rn managed object property"
    ::= { cucsComputeServerUnitFsmTaskEntry 3 }

cucsComputeServerUnitFsmTaskCompletion OBJECT-TYPE
    SYNTAX       CucsFsmCompletion
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmTask:completion
        managed object property"
    ::= { cucsComputeServerUnitFsmTaskEntry 4 }

cucsComputeServerUnitFsmTaskFlags OBJECT-TYPE
    SYNTAX       CucsComputeServerUnitFsmTaskFlags
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmTask:flags managed
        object property"
    ::= { cucsComputeServerUnitFsmTaskEntry 5 }

cucsComputeServerUnitFsmTaskItem OBJECT-TYPE
    SYNTAX       CucsComputeServerUnitFsmTaskItem
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmTask:item managed object property"
    ::= { cucsComputeServerUnitFsmTaskEntry 6 }

cucsComputeServerUnitFsmTaskSeqId OBJECT-TYPE
    SYNTAX       Gauge32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:ServerUnitFsmTask:seqId managed
        object property"
    ::= { cucsComputeServerUnitFsmTaskEntry 7 }

cucsComputeSlotQualTable OBJECT-TYPE
    SYNTAX           SEQUENCE OF CucsComputeSlotQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Cisco UCS compute:SlotQual managed object table"
    ::= { cucsComputeObjects 40 }

cucsComputeSlotQualEntry OBJECT-TYPE
    SYNTAX           CucsComputeSlotQualEntry
    MAX-ACCESS       not-accessible
    STATUS           current
    DESCRIPTION
        "Entry for the cucsComputeSlotQualTable table."
    INDEX { cucsComputeSlotQualInstanceId }
    ::= { cucsComputeSlotQualTable 1 }

CucsComputeSlotQualEntry ::= SEQUENCE {
    cucsComputeSlotQualInstanceId                                    CucsManagedObjectId,
    cucsComputeSlotQualDn                                            CucsManagedObjectDn,
    cucsComputeSlotQualRn                                            SnmpAdminString,
    cucsComputeSlotQualMaxId                                         CucsComputeSlotQualMaxId,
    cucsComputeSlotQualMinId                                         CucsComputeSlotQualMinId
}

cucsComputeSlotQualInstanceId OBJECT-TYPE
    SYNTAX       CucsManagedObjectId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Instance identifier of the managed object."
    ::= { cucsComputeSlotQualEntry 1 }

cucsComputeSlotQualDn OBJECT-TYPE
    SYNTAX       CucsManagedObjectDn
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:SlotQual:dn managed object property"
    ::= { cucsComputeSlotQualEntry 2 }

cucsComputeSlotQualRn OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:SlotQual:rn managed object property"
    ::= { cucsComputeSlotQualEntry 3 }

cucsComputeSlotQualMaxId OBJECT-TYPE
    SYNTAX       CucsComputeSlotQualMaxId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:SlotQual:maxId managed object property"
    ::= { cucsComputeSlotQualEntry 4 }

cucsComputeSlotQualMinId OBJECT-TYPE
    SYNTAX       CucsComputeSlotQualMinId
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Cisco UCS compute:SlotQual:minId managed object property"
    ::= { cucsComputeSlotQualEntry 5 }

END
