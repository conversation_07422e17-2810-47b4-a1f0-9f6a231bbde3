-- *****************************************************************
-- CISCO-ENTITY-SENSOR-MIB
--   
-- November 1997, <PERSON>journer
--   
-- Copyright (c) 1998-2003-2006, 2013, 2015 by cisco Systems Inc.
-- All rights reserved.
-- *****************************************************************

CISCO-ENTITY-SENSOR-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    NOTIFICATION-TYPE,
    Integer32
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    OBJECT-GROUP,
    NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    TEXTUAL-CONVENTION,
    TimeStamp,
    TruthValue
        FROM SNMPv2-TC
    entPhysicalIndex
        FROM ENTITY-MIB
    EntPhysicalIndexOrZero
        FROM CISCO-TC
    ciscoMgmt
        FROM CISCO-SMI;


ciscoEntitySensorMIB MODULE-IDENTITY
    LAST-UPDATED    "201501150000Z"
    ORGANIZATION    "Cisco Systems, Inc."
    CONTACT-INFO
            "Postal: Cisco Systems, Inc.
            170 West Tasman Drive
            San Jose, CA 95134-1706
            USA

            Tel: ****** 526 4000

            E-mail: <EMAIL>"
    DESCRIPTION
        "The CISCO-ENTITY-SENSOR-MIB is used to monitor
        the values of sensors in the Entity-MIB (RFC 2037) 
        entPhysicalTable."
    REVISION        "201501150000Z"
    DESCRIPTION
        "Corrected the definition of entSensorPrecision."
    REVISION        "201309210000Z"
    DESCRIPTION
        "Added entSensorThresholdRecoveryNotification to
        entitySensorMIBNotifications.
        Added entSensorThresholdSeverity as a varbind
        object to entSensorThresholdNotification.
        Added entitySensorNotificationGroup 
        and deprecated 
        entitySensorThresholdNotificationGroup.
        Added entSensorThresholdNotification and
        entSensorThresholdRecoveryNotification to
        entitySensorNotificationGroup.
        Added entitySensorMIBComplianceV05 and 
        deprecated entitySensorMIBComplianceV04."
    REVISION        "200711120000Z"
    DESCRIPTION
        "Added entitySensorNotifCtrlGlobalGroup."
    REVISION        "200601010000Z"
    DESCRIPTION
        "Add new object entSensorMeasuredEntity to
        entSensorValueTable."
    REVISION        "200509080000Z"
    DESCRIPTION
        "Change the module descriptor name from entitySensorMIB
        to ciscoEntitySensorMIB since ENTITY-SENSOR-MIB also
        uses the same name and there is a conflict."
    REVISION        "200301070000Z"
    DESCRIPTION
        "[1] Add dBm(14) in SensorDataType."
    REVISION        "200210160000Z"
    DESCRIPTION
        "[1] Add critical(30) in CSensorThresholdSeverity.
        [2] Change to MAX-ACCESS read-write for 3 objects.
        [3] Add entitySensorMIBComplianceV02."
    REVISION        "200006200000Z"
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { ciscoMgmt 91 }


entitySensorMIBObjects  OBJECT IDENTIFIER
    ::= { ciscoEntitySensorMIB 1 }

entitySensorMIBNotificationPrefix  OBJECT IDENTIFIER
    ::= { ciscoEntitySensorMIB 2 }

entitySensorMIBConformance  OBJECT IDENTIFIER
    ::= { ciscoEntitySensorMIB 3 }


-- textual conventions

SensorDataType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "sensor measurement data types.  valid values are:
        other(1):        a measure other than those listed below
        unknown(2):      unknown measurement, or 
                         arbitrary, relative numbers    
        voltsAC(3):      electric potential
        voltsDC(4):      electric potential
        amperes(5):      electric current
        watts(6):        power
        hertz(7):        frequency
        celsius(8):      temperature
        percentRH(9):    percent relative humidity
        rpm(10):         shaft revolutions per minute
        cmm(11),:        cubic meters per minute (airflow)
        truthvalue(12):  value takes { true(1), false(2) }
        specialEnum(13): value takes user defined enumerated values
        dBm(14):         dB relative to 1mW of power"
    SYNTAX          INTEGER  {
                        other(1),
                        unknown(2),
                        voltsAC(3),
                        voltsDC(4),
                        amperes(5),
                        watts(6),
                        hertz(7),
                        celsius(8),
                        percentRH(9),
                        rpm(10),
                        cmm(11),
                        truthvalue(12),
                        specialEnum(13),
                        dBm(14)
                    }

SensorDataScale ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "International System of Units (SI) prefixes."
    SYNTAX          INTEGER  {
                        yocto(1), -- 10^-24
                        zepto(2), -- 10^-21
                        atto(3), -- 10^-18
                        femto(4), -- 10^-15
                        pico(5), -- 10^-12
                        nano(6), -- 10^-9
                        micro(7), -- 10^-6
                        milli(8), -- 10^-3
                        units(9), -- 10^0
                        kilo(10), -- 10^3
                        mega(11), -- 10^6
                        giga(12), -- 10^9
                        tera(13), -- 10^12
                        exa(14), -- 10^15
                        peta(15), -- 10^18
                        zetta(16), -- 10^21
                        yotta(17) -- 10^24                        
                    }

SensorPrecision ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "When in the range 1 to 9, SensorPrecision is the number
        of decimal places in the fractional part of 
        a SensorValue fixed-point number.  When in the range -8 to
        -1, SensorPrecision is the number of accurate digits in 
        a SensorValue fixed-point number.

        SensorPrecision is 0 for non-fixed-point numbers.

        Agent implementors must choose a value for SensorPrecision 
        so that the precision and accuracy of a SensorValue is 
        correctly indicated.

        For example, a temperature sensor that can measure 0o to 
        100o C in 0.1o increments, +/- 0.05o, would have a 
        SensorPrecision of 1, a SensorDataScale of units(0), and a 
        SensorValue ranging from 0 to 1000.  
        The SensorValue would be interpreted as (degrees C * 10).

        If that temperature sensor's precision were 0.1o but its 
        accuracy were only +/- 0.5o, then the SensorPrecision would 
        be 0. The SensorValue would be interpreted as degrees C.

        Another example: a fan rotation speed sensor that measures RPM 
        from 0 to 10,000 in 100 RPM increments, with an accuracy of 
        +50/-37 RPM, would have a SensorPrecision of -2, a 
        SensorDataScale of units(9), and a SensorValue ranging from 0 
        to 10000. The 10s and 1s digits of SensorValue would always 
        be 0."
    SYNTAX          INTEGER (-8..9)

SensorValue ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "For sensors that measure voltsAC, voltsDC,
        amperes, watts, hertz, celsius, cmm
        this item is a fixed point number ranging from 
        -999,999,999 to +999,999,999.  Use the value 
        -1000000000 to indicate underflow. Use the value 
        +1000000000 to indicate overflow.  Use SensorPrecision
        to indicate how many fractional digits the SensorValue
        has.


        For sensors that measure percentRH, this item
        is a number ranging from 0 to 100.

        For sensors that measure rpm, this item
        can take only nonnegative values, 0..*********.

        For sensors of type truthvalue, this item 
        can take only two values: true(1), false(2).

        For sensors of type specialEnum, this item
        can take any value in the range (-1000000000..1000000000),
        but the meaning of each value is specific to the 
        sensor.

        For sensors of type other and unknown, 
        this item can take any value in the range 
        (-1000000000..1000000000), but the meaning of the values 
        are specific to the sensor.

        Use Entity-MIB entPhysicalTable.entPhysicalVendorType
        to learn about the sensor type."
    SYNTAX          INTEGER (-1000000000..1000000000)

SensorStatus ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Indicates the operational status of the sensor.

        ok(1) means the agent can read the sensor 
        value.

        unavailable(2) means that the agent presently 
        can not report the sensor value.

        nonoperational(3) means that the agent believes
        the sensor is broken.  The sensor could have a 
        hard failure (disconnected wire), or a soft failure
        such as out-of-range, jittery, or wildly fluctuating
        readings."
    SYNTAX          INTEGER  {
                        ok(1),
                        unavailable(2),
                        nonoperational(3)
                    }

SensorValueUpdateRate ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "Indicates the interval in seconds between updates to the
        sensor's value.  

        The value zero indicates:
        - the sensor value is updated on demand (when polled by the 
          agent for a get-request), 
        - or when the sensor value changes (event-driven), 
        - or the agent does not know the rate"
    SYNTAX          INTEGER (0..*********)

SensorThresholdSeverity ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "sensor threshold severity.  Valid values are:

        other(1)    : a severity other than those listed below.
        minor(10)   : Minor Problem threshold.
        major(20)   : Major Problem threshold.
        critical(30): Critical problem threshold. A system might shut
                      down the sensor associated FRU automatically if
                      the sensor value reach the critical problem
                      threshold."
    SYNTAX          INTEGER  {
                        other(1),
                        minor(10),
                        major(20),
                        critical(30)
                    }

SensorThresholdRelation ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "sensor threshold relational operator types.  valid values are:

        lessThan(1):        if the sensor value is less than
                            the threshold value
        lessOrEqual(2):     if the sensor value is less than or equal to
                            the threshold value
        greaterThan(3):     if the sensor value is greater than 
                            the threshold value
        greaterOrEqual(4):  if the sensor value is greater than or equal
                            to the threshold value
        equalTo(5):         if the sensor value is equal to
                            the threshold value
        notEqualTo(6):      if the sensor value is not equal to
                            the threshold value"
    SYNTAX          INTEGER  {
                        lessThan(1),
                        lessOrEqual(2),
                        greaterThan(3),
                        greaterOrEqual(4),
                        equalTo(5),
                        notEqualTo(6)
                    }
-- MIB variables

entSensorValues  OBJECT IDENTIFIER
    ::= { entitySensorMIBObjects 1 }

entSensorThresholds  OBJECT IDENTIFIER
    ::= { entitySensorMIBObjects 2 }

entSensorGlobalObjects  OBJECT IDENTIFIER
    ::= { entitySensorMIBObjects 3 }

-- entSensorValueTable

entSensorValueTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF EntSensorValueEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table lists the type, scale, and present value
        of a sensor listed in the Entity-MIB entPhysicalTable."
    ::= { entSensorValues 1 }

entSensorValueEntry OBJECT-TYPE
    SYNTAX          EntSensorValueEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entSensorValueTable entry describes the
        present reading of a sensor, the measurement units
        and scale, and sensor operational status."
    INDEX           { entPhysicalIndex } 
    ::= { entSensorValueTable 1 }

EntSensorValueEntry ::= SEQUENCE {
        entSensorType            SensorDataType,
        entSensorScale           SensorDataScale,
        entSensorPrecision       SensorPrecision,
        entSensorValue           SensorValue,
        entSensorStatus          SensorStatus,
        entSensorValueTimeStamp  TimeStamp,
        entSensorValueUpdateRate SensorValueUpdateRate,
        entSensorMeasuredEntity  EntPhysicalIndexOrZero
}

entSensorType OBJECT-TYPE
    SYNTAX          SensorDataType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable indicates the type of data
        reported by the entSensorValue.

        This variable is set by the agent at start-up
        and the value does not change during operation." 
    ::= { entSensorValueEntry 1 }

entSensorScale OBJECT-TYPE
    SYNTAX          SensorDataScale
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable indicates the exponent to apply
        to sensor values reported by entSensorValue.

        This variable is set by the agent at start-up
        and the value does not change during operation." 
    ::= { entSensorValueEntry 2 }

entSensorPrecision OBJECT-TYPE
    SYNTAX          SensorPrecision
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable indicates the number of decimal
        places of precision in fixed-point
        sensor values reported by entSensorValue.

        This variable is set to 0 when entSensorType
        is not a fixed-point type: e.g.'percentRH(9)', 
        'rpm(10)', 'cmm(11)', or 'truthvalue(12)'.

        This variable is set by the agent at start-up
        and the value does not change during operation." 
    ::= { entSensorValueEntry 3 }

entSensorValue OBJECT-TYPE
    SYNTAX          SensorValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable reports the most recent measurement seen
        by the sensor.

        To correctly display or interpret this variable's value, 
        you must also know entSensorType, entSensorScale, and 
        entSensorPrecision.

        However, you can compare entSensorValue with the threshold
        values given in entSensorThresholdTable without any semantic
        knowledge." 
    ::= { entSensorValueEntry 4 }

entSensorStatus OBJECT-TYPE
    SYNTAX          SensorStatus
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable indicates the present operational status
        of the sensor." 
    ::= { entSensorValueEntry 5 }

entSensorValueTimeStamp OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable indicates the age of the value reported by
        entSensorValue" 
    ::= { entSensorValueEntry 6 }

entSensorValueUpdateRate OBJECT-TYPE
    SYNTAX          SensorValueUpdateRate
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable indicates the rate that the agent
        updates entSensorValue." 
    ::= { entSensorValueEntry 7 }

entSensorMeasuredEntity OBJECT-TYPE
    SYNTAX          EntPhysicalIndexOrZero
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This object identifies the physical entity for which the
        sensor is taking measurements.  For example, for a sensor
        measuring the voltage output of a power-supply, this object
        would be the entPhysicalIndex of that power-supply; for a sensor
        measuring the temperature inside one chassis of a multi-chassis
        system, this object would be the enPhysicalIndex of that
        chassis.

        This object has a value of zero when the physical entity
        for which the sensor is taking measurements can not be
        represented by any one row in the entPhysicalTable, or that
        there is no such physical entity." 
    ::= { entSensorValueEntry 8 }
 

-- entSensorThresholdTable

entSensorThresholdTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF EntSensorThresholdEntry 
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This table lists the threshold severity, relation, and
        comparison value, for a sensor listed in the Entity-MIB 
        entPhysicalTable."
    ::= { entSensorThresholds 1 }

entSensorThresholdEntry OBJECT-TYPE
    SYNTAX          EntSensorThresholdEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An entSensorThresholdTable entry describes the
        thresholds for a sensor: the threshold severity,
        the threshold value, the relation, and the 
        evaluation of the threshold.

        Only entities of type sensor(8) are listed in this table.
        Only pre-configured thresholds are listed in this table.

        Users can create sensor-value monitoring instruments
        in different ways, such as RMON alarms, Expression-MIB, etc.

        Entries are created by the agent at system startup and
        FRU insertion.  Entries are deleted by the agent at
        FRU removal."
    INDEX           {
                        entPhysicalIndex,
                        entSensorThresholdIndex
                    } 
    ::= { entSensorThresholdTable 1 }

EntSensorThresholdEntry ::= SEQUENCE {
        entSensorThresholdIndex              Integer32,
        entSensorThresholdSeverity           SensorThresholdSeverity,
        entSensorThresholdRelation           SensorThresholdRelation,
        entSensorThresholdValue              SensorValue,
        entSensorThresholdEvaluation         TruthValue,
        entSensorThresholdNotificationEnable TruthValue
}

entSensorThresholdIndex OBJECT-TYPE
    SYNTAX          Integer32 (1..99999999)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An index that uniquely identifies an entry
        in the entSensorThresholdTable. This index
        permits the same sensor to have several 
        different thresholds." 
    ::= { entSensorThresholdEntry 1 }

entSensorThresholdSeverity OBJECT-TYPE
    SYNTAX          SensorThresholdSeverity
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This variable indicates the severity of this threshold." 
    ::= { entSensorThresholdEntry 2 }

entSensorThresholdRelation OBJECT-TYPE
    SYNTAX          SensorThresholdRelation
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This variable indicates the relation between sensor value
        (entSensorValue) and threshold value (entSensorThresholdValue), 
        required to trigger the alarm.  when evaluating the relation, 
        entSensorValue is on the left of entSensorThresholdRelation, 
        entSensorThresholdValue is on the right. 

        in pseudo-code, the evaluation-alarm mechanism is:

        ...
        if (entSensorStatus == ok) then
            if (evaluate(entSensorValue, entSensorThresholdRelation,  
                entSensorThresholdValue)) 
            then
                if (entSensorThresholdNotificationEnable == true)) 
                then
                    raise_alarm(sensor's entPhysicalIndex);
                endif
            endif
        endif
        ..." 
    ::= { entSensorThresholdEntry 3 }

entSensorThresholdValue OBJECT-TYPE
    SYNTAX          SensorValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This variable indicates the value of the threshold.

        To correctly display or interpret this variable's value, 
        you must also know entSensorType, entSensorScale, and 
        entSensorPrecision.

        However, you can directly compare entSensorValue 
        with the threshold values given in entSensorThresholdTable 
        without any semantic knowledge." 
    ::= { entSensorThresholdEntry 4 }

entSensorThresholdEvaluation OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "This variable indicates the result of the most
        recent evaluation of the threshold.  If the threshold
        condition is true, entSensorThresholdEvaluation 
        is true(1).  If the threshold condition is false, 
        entSensorThresholdEvaluation is false(2).

        Thresholds are evaluated at the rate indicated by 
        entSensorValueUpdateRate." 
    ::= { entSensorThresholdEntry 5 }

entSensorThresholdNotificationEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This variable controls generation of
        entSensorThresholdNotification for this threshold.

        When this variable is 'true', generation of 
        entSensorThresholdNotification is enabled for this
        threshold. When this variable is 'false', 
        generation of entSensorThresholdNotification is
        disabled for this threshold." 
    ::= { entSensorThresholdEntry 6 }
 


-- Entity Sensor Global Objects

entSensorThreshNotifGlobalEnable OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "This variable enables the generation of
        entSensorThresholdNotification globally
        on the device. If this object value is
        'false', then no entSensorThresholdNotification
        will be generated on this device. If this object
        value is 'true', then whether a 
        entSensorThresholdNotification for a threshold will
        be generated or not depends on the instance value of
        entSensorThresholdNotificationEnable for that
        threshold." 
    ::= { entSensorGlobalObjects 1 }
-- notifications

entitySensorMIBNotifications  OBJECT IDENTIFIER
    ::= { entitySensorMIBNotificationPrefix 0 }


entSensorThresholdNotification NOTIFICATION-TYPE
    OBJECTS         {
                        entSensorThresholdValue,
                        entSensorValue,
                        entSensorThresholdSeverity
                    }
    STATUS          current
    DESCRIPTION
        "The notification is generated when
        the sensor value entSensorValue crosses 
        the threshold value entSensorThresholdValue and
        the value of entSensorThreshNotifGlobalEnable is true.

        entSensorThresholdSeverity indicates the severity 
        of this threshold.

        The agent implementation guarantees prompt, timely
        evaluation of threshold and generation of this
        notification."
   ::= { entitySensorMIBNotifications 1 }

entSensorThresholdRecoveryNotification NOTIFICATION-TYPE
    OBJECTS         {
                        entSensorValue,
                        entSensorThresholdSeverity,
                        entSensorThresholdValue
                    }
    STATUS          current
    DESCRIPTION
        "This notification is generated
        as a recovery notification when 
        the sensor value entSensorValue goes below 
        the threshold value entSensorThresholdValue
        once it has generated entSensorThresholdNotification.
        The value of entSensorThreshNotifGlobalEnable needs
        to be true.

        entSensorThresholdSeverity indicates the severity 
        of this threshold.

        The agent implementation guarantees prompt, timely
        evaluation of threshold and generation of this
        notification."
   ::= { entitySensorMIBNotifications 2 }
-- conformance information

entitySensorMIBCompliances  OBJECT IDENTIFIER
    ::= { entitySensorMIBConformance 1 }

entitySensorMIBGroups  OBJECT IDENTIFIER
    ::= { entitySensorMIBConformance 2 }


-- compliance statements

entitySensorMIBComplianceV01 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "An Entity-MIB implementation that lists
        sensors in its entPhysicalTable must implement
        this group."
    MODULE          -- this module
    MANDATORY-GROUPS {
                        entitySensorValueGroup,
                        entitySensorThresholdGroup,
                        entitySensorThresholdNotificationGroup
                    }
    ::= { entitySensorMIBCompliances 1 }

entitySensorMIBComplianceV02 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "An Entity-MIB implementation that lists
        sensors in its entPhysicalTable must implement
        this group."
    MODULE          -- this module
    MANDATORY-GROUPS { entitySensorThresholdGroup }

    GROUP           entitySensorValueGroup
    DESCRIPTION
        "This group is mandatory for the systems which don't
        support IETF version of ENTITY-SENSOR-MIB."

    GROUP           entitySensorThresholdNotificationGroup
    DESCRIPTION
        "This group is mandatory for the systems which support
        entitySensorValueGroup group."

    OBJECT          entSensorThresholdSeverity
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdRelation
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdValue
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { entitySensorMIBCompliances 2 }

entitySensorMIBComplianceV03 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "An Entity-MIB implementation that lists
        sensors in its entPhysicalTable must implement
        this group."
    MODULE          -- this module
    MANDATORY-GROUPS { entitySensorThresholdGroup }

    GROUP           entitySensorValueGroup
    DESCRIPTION
        "This group is mandatory for the systems which don't
        support IETF version of ENTITY-SENSOR-MIB."

    GROUP           entitySensorThresholdNotificationGroup
    DESCRIPTION
        "This group is mandatory for the systems which support
        entitySensorValueGroup group."

    GROUP           entitySensorValueGroupSup1
    DESCRIPTION
        "This group is mandatory for the systems which support
        the correlation between sensor and its measured
        physical entity."

    OBJECT          entSensorThresholdSeverity
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdRelation
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdValue
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { entitySensorMIBCompliances 3 }

entitySensorMIBComplianceV04 MODULE-COMPLIANCE
    STATUS          deprecated
    DESCRIPTION
        "An Entity-MIB implementation that lists
        sensors in its entPhysicalTable must implement
        this group."
    MODULE          -- this module
    MANDATORY-GROUPS { entitySensorThresholdGroup }

    GROUP           entitySensorValueGroup
    DESCRIPTION
        "This group is mandatory for the systems which don't
        support IETF version of ENTITY-SENSOR-MIB."

    GROUP           entitySensorThresholdNotificationGroup
    DESCRIPTION
        "This group is mandatory for the systems which support
        entitySensorValueGroup group."

    GROUP           entitySensorValueGroupSup1
    DESCRIPTION
        "This group is mandatory for the systems which support
        the correlation between sensor and its measured
        physical entity."

    GROUP           entitySensorNotifCtrlGlobalGroup
    DESCRIPTION
        "This group is mandatory for the platforms which support
        global notification control on 
        entSensorThresholdNotification."

    OBJECT          entSensorThresholdSeverity
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdRelation
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdValue
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { entitySensorMIBCompliances 4 }

entitySensorMIBComplianceV05 MODULE-COMPLIANCE
    STATUS          current
    DESCRIPTION
        "An Entity-MIB implementation that lists
        sensors in its entPhysicalTable must implement
        this group."
    MODULE          -- this module
    MANDATORY-GROUPS { entitySensorThresholdGroup }

    GROUP           entitySensorValueGroup
    DESCRIPTION
        "This group is mandatory for the systems
        which don't support IETF version of 
        ENTITY-SENSOR-MIB."

    GROUP           entitySensorValueGroupSup1
    DESCRIPTION
        "This group is mandatory for the systems
        which don't support IETF version of 
        ENTITY-SENSOR-MIB."

    GROUP           entitySensorNotifCtrlGlobalGroup
    DESCRIPTION
        "This group is mandatory for the systems
        which don't support IETF version of 
        ENTITY-SENSOR-MIB."

    GROUP           entitySensorNotificationGroup
    DESCRIPTION
        "This group is mandatory for the systems
        which don't support IETF version of 
        ENTITY-SENSOR-MIB."

    OBJECT          entSensorThresholdSeverity
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdRelation
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."

    OBJECT          entSensorThresholdValue
    MIN-ACCESS      read-only
    DESCRIPTION
        "Write access is not required."
    ::= { entitySensorMIBCompliances 5 }

-- units of conformance

entitySensorValueGroup OBJECT-GROUP
    OBJECTS         {
                        entSensorType,
                        entSensorScale,
                        entSensorPrecision,
                        entSensorValue,
                        entSensorStatus,
                        entSensorValueTimeStamp,
                        entSensorValueUpdateRate
                    }
    STATUS          current
    DESCRIPTION
        "The collection of objects which are used
        to describe and monitor values of Entity-MIB 
        entPhysicalTable entries of sensors."
    ::= { entitySensorMIBGroups 1 }

entitySensorThresholdGroup OBJECT-GROUP
    OBJECTS         {
                        entSensorThresholdSeverity,
                        entSensorThresholdRelation,
                        entSensorThresholdValue,
                        entSensorThresholdEvaluation,
                        entSensorThresholdNotificationEnable
                    }
    STATUS          current
    DESCRIPTION
        "The collection of objects which are used
        to describe and monitor thresholds for
        sensors."
    ::= { entitySensorMIBGroups 2 }

entitySensorThresholdNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS    { entSensorThresholdNotification }
    STATUS          deprecated
    DESCRIPTION
        "The collection of notifications used for
        monitoring sensor threshold activity.
        entitySensorThresholdNotificationGroup 
        object is superseded by 
        entitySensorNotificationGroup."
    ::= { entitySensorMIBGroups 3 }

entitySensorValueGroupSup1 OBJECT-GROUP
    OBJECTS         { entSensorMeasuredEntity }
    STATUS          current
    DESCRIPTION
        "The collection of objects which are used to describe and track
        the measured entities of ENTITY-MIB entPhysicalTable."
    ::= { entitySensorMIBGroups 4 }

entitySensorNotifCtrlGlobalGroup OBJECT-GROUP
    OBJECTS         { entSensorThreshNotifGlobalEnable }
    STATUS          current
    DESCRIPTION
        "The collection of objects which provide the global
        notification control on entSensorThresholdNotification."
    ::= { entitySensorMIBGroups 5 }

entitySensorNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS    {
                        entSensorThresholdNotification,
                        entSensorThresholdRecoveryNotification
                    }
    STATUS          current
    DESCRIPTION
        "The collection of notifications used for
        monitoring sensor threshold activity."
    ::= { entitySensorMIBGroups 6 }

END



