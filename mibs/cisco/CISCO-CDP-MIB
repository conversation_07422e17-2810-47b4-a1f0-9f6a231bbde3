-- *****************************************************************
-- CISCO-CDP-MIB.mib:  CDP MIB file
--
-- Oct 1994, <PERSON><PERSON>
-- November 2001, <PERSON>
-- July 2004, <PERSON><PERSON><PERSON>
-- March 2005, <PERSON><PERSON><PERSON> k <PERSON>
-- March 2005, <PERSON><PERSON><PERSON><PERSON>
-- Copyright (c) 1994-1998, 1999, 2001, 2004, 2005 by cisco Systems, Inc.
-- All rights reserved.
-- *****************************************************************
--
CISCO-CDP-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE,
        Integer32, Unsigned32
                FROM SNMPv2-SMI
        MODULE-COMPLIANCE, OBJECT-GROUP
                FROM SNMPv2-CONF
        TruthValue, DisplayString, TimeStamp
                FROM SNMPv2-TC
        ciscoMgmt
                FROM CISCO-SMI
        CiscoNetworkProtocol, CiscoNetworkAddress
                FROM CISCO-TC
--        VlanIndex
--              FROM CISCO-VTP-MIB
-- 19-Nov-08 Changed by RevitalR: to allow vlanid of range 1-4095 as in Pirouette project.
        VlanIndex
                FROM Q-BRIDGE-MIB
        ifIndex
                FROM IF-MIB
        ;

ciscoCdpMIB   MODULE-IDENTITY
        LAST-UPDATED    "200503210000Z"
		ORGANIZATION "Cisco Systems, Inc."

		CONTACT-INFO
		"Postal: 170 West Tasman Drive
		San Jose , CA 95134-1706
		USA

		
		Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

        DESCRIPTION
                "The MIB module for management of the Cisco Discovery
                Protocol in Cisco devices."
        REVISION        "200503210000Z"
        DESCRIPTION
                "Added the following object:
                 cdpInterfaceName."
        REVISION        "200503140000Z"
        DESCRIPTION
                "Added cdpCtAddressTable which contains the following
                 objects:
                 cdpCtAddressIndex,
                 cdpCtAddressType,
                 cdpCtAddress."
        REVISION        "200111230000Z"
        DESCRIPTION
                "Added cdpInterfaceExtTable which contains the following
                 objects:
                 cdpInterfaceExtendedTrust,
                 cdpInterfaceCosForUntrustedPort."
        REVISION        "200104230000Z"
        DESCRIPTION
                "Added the following objects:
                 cdpGlobalDeviceIdFormatCpb,
                 cdpGlobalDeviceIdFormat."
        REVISION        "200011220000Z"
        DESCRIPTION
                "Added the following objects:
                 cdpCacheApplianceID,
                 cdpCacheVlanID,
                 cdpCachePowerConsumption,
                 cdpCacheMTU,
                 cdpCachePrimaryMgmtAddrType,
                 cdpCachePrimaryMgmtAddr,
                 cdpCacheSecondaryMgmtAddrType,
                 cdpCacheSecondaryMgmtAddr,
                 cdpCacheLastChange,
                 cdpCachePhysLocation,
                 cdpCacheSysName,
                 cdpCacheSysObjectID,
                 cdpGlobalLastChange"
        REVISION        "9812100000Z"
        DESCRIPTION
                "Added cdpGlobalDeviceId object."
        REVISION        "9809160000Z"
        DESCRIPTION
                "added these objects to cdpCacheTable:
                cdpCacheVTPMgmtDomain,
                cdpCacheNativeVLAN,
                cdpCacheDuplex.
                "
        REVISION        "9607080000Z"
        DESCRIPTION
                "Obsolete cdpInterfaceMessageInterval and newly
                define cdpGlobal object."
        REVISION        "9508150000Z"
        DESCRIPTION
                "Specify a correct (non-negative) range for several
                index objects."
        REVISION        "9507270000Z"
        DESCRIPTION
                "Correct range of cdpInterfaceMessageInterval."
        REVISION        "9501250000Z"
        DESCRIPTION
                "Move from ciscoExperiment to ciscoMgmt oid subtree."
        ::= { ciscoMgmt 23 }


ciscoCdpMIBObjects OBJECT IDENTIFIER ::= { ciscoCdpMIB 1 }

cdpInterface   OBJECT IDENTIFIER ::= { ciscoCdpMIBObjects 1 }
cdpCache       OBJECT IDENTIFIER ::= { ciscoCdpMIBObjects 2 }
cdpGlobal      OBJECT IDENTIFIER ::= { ciscoCdpMIBObjects 3 }

--
-- The CDP Interface Group
--
cdpInterfaceTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF CdpInterfaceEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table containing the status of CDP on
            the device's interfaces."
    ::= { cdpInterface 1 }

cdpInterfaceEntry OBJECT-TYPE
    SYNTAX     CdpInterfaceEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cdpInterfaceTable,
            containing the status of CDP on an interface."
    INDEX      { cdpInterfaceIfIndex }
    ::= { cdpInterfaceTable 1 }

CdpInterfaceEntry ::= SEQUENCE {
    cdpInterfaceIfIndex           Integer32,
    cdpInterfaceEnable            TruthValue,
    cdpInterfaceMessageInterval   INTEGER,
    cdpInterfaceGroup             Integer32,
    cdpInterfacePort              Integer32,
    cdpInterfaceName              DisplayString
}

cdpInterfaceIfIndex OBJECT-TYPE
    SYNTAX     Integer32 (0..2147483647)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The ifIndex value of the local interface.

            For 802.3 Repeaters on which the repeater ports do not
            have ifIndex values assigned, this value is a unique
            value for the port, and greater than any ifIndex value
            supported by the repeater; in this case, the specific
            port is indicated by corresponding values of
            cdpInterfaceGroup and cdpInterfacePort, where these
            values correspond to the group number and port number
            values of RFC 1516."
    ::= { cdpInterfaceEntry 1 }

cdpInterfaceEnable OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "An indication of whether the Cisco Discovery Protocol
            is currently running on this interface.  This variable
            has no effect when CDP is disabled (cdpGlobalRun = FALSE)."
    ::= { cdpInterfaceEntry 2 }

cdpInterfaceMessageInterval OBJECT-TYPE
    SYNTAX     INTEGER (5..254)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     obsolete   -- replaced by cdpGlobalMessageInterval
                          --  this object should be applied to the
                          --  whole system instead of per interface
    DESCRIPTION
            "The interval at which CDP messages are to be generated
            on this interface.  The default value is 60 seconds."
    ::= { cdpInterfaceEntry 3 }

cdpInterfaceGroup OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "This object is only relevant to interfaces which are
            repeater ports on 802.3 repeaters.  In this situation,
            it indicates the RFC1516 group number of the repeater
            port which corresponds to this interface."
    ::= { cdpInterfaceEntry 4 }

cdpInterfacePort OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "This object is only relevant to interfaces which are
            repeater ports on 802.3 repeaters.  In this situation,
            it indicates the RFC1516 port number of the repeater
            port which corresponds to this interface."
    ::= { cdpInterfaceEntry 5 }

cdpInterfaceName OBJECT-TYPE
    SYNTAX     DisplayString
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The name of the local interface as advertised by
             CDP in the Port-ID TLV"
    ::= { cdpInterfaceEntry 6 }

cdpInterfaceExtTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF CdpInterfaceExtEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "This table contains the additional CDP configuration on
         the device's interfaces."
    ::= { cdpInterface 2 }

cdpInterfaceExtEntry OBJECT-TYPE
    SYNTAX     CdpInterfaceExtEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An entry in the cdpInterfaceExtTable contains the values
         configured for Extented Trust TLV and COS (Class of Service)
         for Untrusted Ports TLV on an interface which supports the
         sending of these TLVs."
    INDEX      { ifIndex }
    ::= { cdpInterfaceExtTable 1 }

CdpInterfaceExtEntry ::= SEQUENCE {
    cdpInterfaceExtendedTrust        INTEGER,
    cdpInterfaceCosForUntrustedPort  Unsigned32
}

cdpInterfaceExtendedTrust OBJECT-TYPE
    SYNTAX     INTEGER {
                   trusted(1),
                   noTrust(2)
               }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Indicates the value to be sent by Extended Trust TLV.

         If trusted(1) is configured, the value of Extended Trust TLV
         is one byte in length with its least significant bit equal to
         1 to indicate extended trust. All other bits are 0.

         If noTrust(2) is configured, the value of Extended Trust TLV
         is one byte in length with its least significant bit equal to
         0 to indicate no extended trust. All other bits are 0."
    ::= { cdpInterfaceExtEntry 1 }

cdpInterfaceCosForUntrustedPort OBJECT-TYPE
    SYNTAX     Unsigned32 (0..7)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Indicates the value to be sent by COS for Untrusted Ports TLV."
    ::= { cdpInterfaceExtEntry 2 }

--
-- The CDP Cache Group
--

cdpCacheTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF CdpCacheEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table containing the cached
            information obtained via receiving CDP messages."
    ::= { cdpCache 1 }

cdpCacheEntry OBJECT-TYPE
    SYNTAX     CdpCacheEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cdpCacheTable,
            containing the information received via CDP on one
            interface from one device.  Entries appear when
            a CDP advertisement is received from a neighbor
            device.  Entries disappear when CDP is disabled
            on the interface, or globally."
    INDEX      { cdpCacheIfIndex, cdpCacheDeviceIndex }
    ::= { cdpCacheTable 1 }

CdpCacheEntry ::= SEQUENCE {
    cdpCacheIfIndex                 Integer32,
    cdpCacheDeviceIndex             Integer32,
    cdpCacheAddressType             CiscoNetworkProtocol,
    cdpCacheAddress                 CiscoNetworkAddress,
    cdpCacheVersion                 DisplayString,
    cdpCacheDeviceId                DisplayString,
    cdpCacheDevicePort              DisplayString,
    cdpCachePlatform                DisplayString,
    cdpCacheCapabilities            OCTET STRING,
    cdpCacheVTPMgmtDomain           DisplayString,
    cdpCacheNativeVLAN              VlanIndex,
    cdpCacheDuplex                  INTEGER,
    cdpCacheApplianceID             Unsigned32,
    cdpCacheVlanID                  Unsigned32,
    cdpCachePowerConsumption        Unsigned32,
    cdpCacheMTU                     Unsigned32,
    cdpCacheSysName                 DisplayString,
    cdpCacheSysObjectID             OBJECT IDENTIFIER,
    cdpCachePrimaryMgmtAddrType     CiscoNetworkProtocol,
    cdpCachePrimaryMgmtAddr         CiscoNetworkAddress,
    cdpCacheSecondaryMgmtAddrType   CiscoNetworkProtocol,
    cdpCacheSecondaryMgmtAddr       CiscoNetworkAddress,
    cdpCachePhysLocation            DisplayString,
    cdpCacheLastChange               TimeStamp
}

cdpCacheIfIndex OBJECT-TYPE
    SYNTAX     Integer32 (0..2147483647)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "Normally, the ifIndex value of the local interface.
            For 802.3 Repeaters for which the repeater ports do not
            have ifIndex values assigned, this value is a unique
            value for the port, and greater than any ifIndex value
            supported by the repeater; the specific port number in
            this case, is given by the corresponding value of
            cdpInterfacePort."
    ::= { cdpCacheEntry 1 }

cdpCacheDeviceIndex OBJECT-TYPE
    SYNTAX     Integer32 (0..2147483647)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "A unique value for each device from which CDP messages
            are being received."
    ::= { cdpCacheEntry 2 }

cdpCacheAddressType OBJECT-TYPE
    SYNTAX     CiscoNetworkProtocol
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "An indication of the type of address contained in the
            corresponding instance of cdpCacheAddress."
    ::= { cdpCacheEntry 3 }

cdpCacheAddress OBJECT-TYPE
    SYNTAX     CiscoNetworkAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
          "The (first) network-layer address of the device
          as reported in the Address TLV of the most recently received
          CDP message.  For example, if the corresponding instance of
          cacheAddressType had the value 'ip(1)', then this object
          would be an IPv4-address.  If the neighbor device is
          SNMP-manageable, it is supposed to generate its CDP messages
          such that this address is one at which it will receive SNMP
          messages. Use cdpCtAddressTable to extract the remaining
          addresses from the Address TLV received most recently."
    ::= { cdpCacheEntry 4 }

cdpCacheVersion OBJECT-TYPE
    SYNTAX     DisplayString
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The Version string as reported in the most recent CDP
            message.  The zero-length string indicates no Version
            field (TLV) was reported in the most recent CDP
            message."
    ::= { cdpCacheEntry 5 }

cdpCacheDeviceId OBJECT-TYPE
    SYNTAX     DisplayString
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The Device-ID string as reported in the most recent CDP
            message.  The zero-length string indicates no Device-ID
            field (TLV) was reported in the most recent CDP
            message."
    ::= { cdpCacheEntry 6 }

cdpCacheDevicePort OBJECT-TYPE
    SYNTAX     DisplayString
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The Port-ID string as reported in the most recent CDP
            message.  This will typically be the value of the ifName
            object (e.g., 'Ethernet0').  The zero-length string
            indicates no Port-ID field (TLV) was reported in the
            most recent CDP message."
    ::= { cdpCacheEntry 7 }

cdpCachePlatform OBJECT-TYPE
    SYNTAX     DisplayString
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The Device's Hardware Platform as reported in the most
            recent CDP message.  The zero-length string indicates
            that no Platform field (TLV) was reported in the most
            recent CDP message."
    ::= { cdpCacheEntry 8 }

cdpCacheCapabilities OBJECT-TYPE
    SYNTAX     OCTET STRING (SIZE (0..4))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The Device's Functional Capabilities as reported in the
            most recent CDP message.  For latest set of specific
            values, see the latest version of the CDP specification.
            The zero-length string indicates no Capabilities field
            (TLV) was reported in the most recent CDP message."
    REFERENCE "Cisco Discovery Protocol Specification, 10/19/94."

    ::= { cdpCacheEntry 9 }

cdpCacheVTPMgmtDomain   OBJECT-TYPE
    SYNTAX    DisplayString (SIZE (0..32))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The VTP Management Domain for the remote device's interface,
        as reported in the most recently received CDP message.
        This object is not instantiated if no VTP Management Domain field
        (TLV) was reported in the most recently received CDP message."
    REFERENCE "managementDomainName in CISCO-VTP-MIB"
    ::= { cdpCacheEntry 10 }

cdpCacheNativeVLAN OBJECT-TYPE
    SYNTAX    VlanIndex
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The remote device's interface's native VLAN, as reported in the
        most recent CDP message.  The value 0 indicates
        no native VLAN field (TLV) was reported in the most
        recent CDP message."
    ::= { cdpCacheEntry 11 }

cdpCacheDuplex OBJECT-TYPE
    SYNTAX    INTEGER {
                  unknown(1),
                  halfduplex(2),
                  fullduplex(3)
              }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The remote device's interface's duplex mode, as reported in the
        most recent CDP message.  The value unknown(1) indicates
        no duplex mode field (TLV) was reported in the most
        recent CDP message."
    ::= { cdpCacheEntry 12 }

cdpCacheApplianceID OBJECT-TYPE
    SYNTAX     Unsigned32 (0..255)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The remote device's Appliance ID, as reported in the
        most recent CDP message. This object is not instantiated if
        no Appliance VLAN-ID field (TLV) was reported in the most
        recently received CDP message."
    ::= { cdpCacheEntry 13 }

cdpCacheVlanID OBJECT-TYPE
    SYNTAX     Unsigned32 (0..4095)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The remote device's VoIP VLAN ID, as reported in the
        most recent CDP message. This object is not instantiated if
        no Appliance VLAN-ID field (TLV) was reported in the most
        recently received CDP message."
    ::= { cdpCacheEntry 14 }

cdpCachePowerConsumption OBJECT-TYPE
    SYNTAX     Unsigned32
    UNITS      "milliwatts"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The amount of power consumed by remote device, as reported
        in the most recent CDP message. This object is not instantiated
        if no Power Consumption field (TLV) was reported in the most
        recently received CDP message."
    ::= { cdpCacheEntry 15 }

cdpCacheMTU OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Indicates the size of the largest datagram that can be
        sent/received by remote device, as reported in the most recent
        CDP message. This object is not instantiated if no MTU field
        (TLV) was reported in the most recently received CDP message."
    ::= { cdpCacheEntry 16 }

cdpCacheSysName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..255))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Indicates the value of the remote device's sysName MIB object.
        By convention, it is the device's fully qualified domain name.
        This object is not instantiated if no sysName field (TLV) was
        reported in the most recently received CDP message."
    ::= { cdpCacheEntry 17 }

cdpCacheSysObjectID OBJECT-TYPE
    SYNTAX     OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Indicates the value of the remote device's sysObjectID MIB
        object. This object is not instantiated if no sysObjectID field
        (TLV) was reported in the most recently received CDP message."
    ::= { cdpCacheEntry 18 }

cdpCachePrimaryMgmtAddrType OBJECT-TYPE
    SYNTAX     CiscoNetworkProtocol
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "An indication of the type of address contained in the
            corresponding instance of cdpCachePrimaryMgmtAddress."
    ::= { cdpCacheEntry 19 }

cdpCachePrimaryMgmtAddr OBJECT-TYPE
    SYNTAX     CiscoNetworkAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "This object indicates the (first) network layer
            address at which the device will accept SNMP messages
            as reported in the first address in the
            Management-Address TLV of the most recently received
            CDP message.  If the corresponding instance of
            cdpCachePrimaryMgmtAddrType has the value 'ip(1)',
            then this object would be an IP-address. If the
            remote device is not currently manageable via any
            network protocol, then it reports the special value
            of the IPv4 address 0.0.0.0, and that address is
            recorded in this object.  If the most recently received
            CDP message did not contain the Management-Address
            TLV, then this object is not instanstiated."
    ::= { cdpCacheEntry 20 }

cdpCacheSecondaryMgmtAddrType OBJECT-TYPE
    SYNTAX     CiscoNetworkProtocol
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "An indication of the type of address contained in the
            corresponding instance of cdpCacheSecondaryMgmtAddress."
    ::= { cdpCacheEntry 21 }

cdpCacheSecondaryMgmtAddr OBJECT-TYPE
    SYNTAX     CiscoNetworkAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "This object indicates the alternate network layer
            address at which the device will accept SNMP messages
            as reported in the second address in the
            Management-Address TLV of the most recently received
            CDP message.  If the corresponding instance of
            cdpCacheSecondaryMgmtAddrType has the value 'ip(1)',
            then this object would be an IP-address. If the
            remote device reports the special value of the
            IPv4 address 0.0.0.0, that address is recorded in
            this object.  If the most recently received CDP
            message did not contain the Management-Address
            TLV, or if that TLV contained only one address, then
            this object is not instanstiated."
    ::= { cdpCacheEntry 22 }

cdpCachePhysLocation OBJECT-TYPE
    SYNTAX     DisplayString
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Indicates the physical location, as reported by the most recent
        CDP message, of a connector which is on, or physically connected
        to, the remote device's interface over which the CDP packet is
        sent. This object is not instantiated if no Physical Location
        field (TLV) was reported by the most recently received CDP
        message."
    ::= { cdpCacheEntry 23 }

cdpCacheLastChange OBJECT-TYPE
    SYNTAX     TimeStamp
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Indicates the time when this cache entry was last changed.
        This object is initialised to the current time when the entry
        gets created and updated to the current time whenever the value
        of any (other) object instance in the corresponding row is
        modified."
    ::= { cdpCacheEntry 24 }

cdpCtAddressTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF CdpCtAddressEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table containing the list of
            network-layer addresses of a neighbor interface,
            as reported in the Address TLV of the most recently
            received CDP message. The first address included in
            the Address TLV is saved in cdpCacheAddress.  This
            table contains the remainder of the addresses in the
            Address TLV."
    ::= { cdpCache 2 }

cdpCtAddressEntry OBJECT-TYPE
    SYNTAX     CdpCtAddressEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cdpCtAddressTable,
            containing the information on one address received via CDP
            on one interface from one device.  Entries appear
            when a CDP advertisement is received from a neighbor
            device, with an Address TLV.  Entries disappear when
            CDP is disabled on the interface, or globally. An entry
            or entries would also disappear if the most recently
            received CDP packet contain fewer address entries in the
            Address TLV, than are currently present in the CDP cache."
    INDEX      { cdpCacheIfIndex, cdpCacheDeviceIndex,
                 cdpCtAddressIndex }
    ::= { cdpCtAddressTable 1 }

CdpCtAddressEntry ::= SEQUENCE {
    cdpCtAddressIndex       Integer32,
    cdpCtAddressType        CiscoNetworkProtocol,
    cdpCtAddress            CiscoNetworkAddress
}

cdpCtAddressIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..2147483647)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The index of the address entry for a given
            cdpCacheIfIndex,cdpCacheDeviceIndex pair. It
            has the value N-1 for the N-th address in the
            Address TLV"
    ::= { cdpCtAddressEntry 3 }

cdpCtAddressType OBJECT-TYPE
    SYNTAX     CiscoNetworkProtocol
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "An indication of the type of address contained in the
            corresponding instance of cdpCtAddress."
    ::= { cdpCtAddressEntry 4 }

cdpCtAddress OBJECT-TYPE
    SYNTAX     CiscoNetworkAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The N-th network-layer address of the device as reported
            in the most recent CDP message's Address TLV, where N-1 is
            given by the value of cdpCtAddressIndex. For example, if
            the the corresponding instance of cdpCtAddressType had the
            value 'ip(1)', then this object would be an IPv4-address.
            NOTE - The 1st address received in the Address TLV is
                   available using cdpCacheAddress"
    ::= { cdpCtAddressEntry 5 }


--
-- The CDP Global Group
--

cdpGlobalRun OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "An indication of whether the Cisco Discovery Protocol
            is currently running.  Entries in cdpCacheTable are
            deleted when CDP is disabled."
    DEFVAL     { true }
    ::= { cdpGlobal 1 }

cdpGlobalMessageInterval OBJECT-TYPE
    SYNTAX     INTEGER (5..254)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The interval at which CDP messages are to be generated.
            The default value is 60 seconds."
    DEFVAL     { 60 }
    ::= { cdpGlobal 2 }

cdpGlobalHoldTime OBJECT-TYPE
    SYNTAX     INTEGER (10..255)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The time for the receiving device holds CDP message.
            The default value is 180 seconds."
    DEFVAL     { 180 }
    ::= { cdpGlobal 3 }

cdpGlobalDeviceId OBJECT-TYPE
    SYNTAX     DisplayString
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The device ID advertised by this device. The format of this
         device id is characterized by the value of
         cdpGlobalDeviceIdFormat object."
    ::= { cdpGlobal 4 }

cdpGlobalLastChange  OBJECT-TYPE
    SYNTAX     TimeStamp
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Indicates the time when the cache table was last changed. It
        is the most recent time at which any row was last created,
        modified or deleted."
    ::= { cdpGlobal 5 }

cdpGlobalDeviceIdFormatCpb  OBJECT-TYPE
    SYNTAX     BITS {
                 serialNumber(0),
                 macAddress(1),
                 other (2)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Indicate the Device-Id format capability of the device.

         serialNumber(0) indicates that the device supports using
         serial number as the format for its DeviceId.

         macAddress(1) indicates that the device supports using
         layer 2 MAC address as the format for its DeviceId.

         other(2) indicates that the device supports using its
         platform specific format as the format for its DeviceId."
    ::= { cdpGlobal 6 }

cdpGlobalDeviceIdFormat  OBJECT-TYPE
    SYNTAX     INTEGER {
                 serialNumber(1),
                 macAddress(2),
                 other(3)
               }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "An indication of the format of Device-Id contained in the
        corresponding instance of cdpGlobalDeviceId. User can only
        specify the formats that the device is capable of as
        denoted in cdpGlobalDeviceIdFormatCpb object.

        serialNumber(1) indicates that the value of cdpGlobalDeviceId
        object is in the form of an ASCII string contain the device
        serial number.

        macAddress(2) indicates that the value of cdpGlobalDeviceId
        object is in the form of Layer 2 MAC address.

        other(3) indicates that the value of cdpGlobalDeviceId object
        is in the form of a platform specific ASCII string contain
        info that identifies the device. For example: ASCII string
        contains serialNumber appended/prepened with system name."
    ::= { cdpGlobal 7 }

-- conformance information

ciscoCdpMIBConformance
              OBJECT IDENTIFIER ::= { ciscoCdpMIB 2 }
ciscoCdpMIBCompliances
              OBJECT IDENTIFIER ::= { ciscoCdpMIBConformance 1 }
ciscoCdpMIBGroups
              OBJECT IDENTIFIER ::= { ciscoCdpMIBConformance 2 }


-- compliance statements

ciscoCdpMIBCompliance MODULE-COMPLIANCE
    STATUS  obsolete  -- superseded by ciscoCdpMIBComplianceV11R01
    DESCRIPTION
            "The compliance statement for the CDP MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { ciscoCdpMIBGroup }

    ::= { ciscoCdpMIBCompliances 1 }

ciscoCdpMIBComplianceV11R01 MODULE-COMPLIANCE
    STATUS  obsolete -- superseded by ciscoCdpMIBComplianceV11R02
    DESCRIPTION
            "The compliance statement for the CDP MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { ciscoCdpMIBGroupV11R01 }

    ::= { ciscoCdpMIBCompliances 2 }

ciscoCdpMIBComplianceV11R02 MODULE-COMPLIANCE
    STATUS  obsolete -- superseded by ciscoCdpMIBComplianceV12R02
    DESCRIPTION
            "The compliance statement for the CDP MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { ciscoCdpMIBGroupV11R02 }

    ::= { ciscoCdpMIBCompliances 3 }

ciscoCdpMIBComplianceV12R02 MODULE-COMPLIANCE
    STATUS  obsolete -- superseded by ciscoCdpMIBCompliance5
    DESCRIPTION
            "The compliance statement for the CDP MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { ciscoCdpMIBGroupV12R02 }

    ::= { ciscoCdpMIBCompliances 4 }

ciscoCdpMIBCompliance5 MODULE-COMPLIANCE
    STATUS  deprecated -- superseded by ciscoCdpMIBComplianceV12R03
    DESCRIPTION
            "The compliance statement for the CDP MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { ciscoCdpMIBGroupV12R02 }

    ::= { ciscoCdpMIBCompliances 5 }

ciscoCdpMIBComplianceV12R03 MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for the CDP MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { ciscoCdpMIBGroupV12R03,
                           ciscoCdpCtAddressGroup
                         }

    GROUP ciscoCdpV2MIBGroup
    DESCRIPTION
        "This group is mandatory for agents implementing CDP version 2"

    GROUP ciscoCdpV2IfExtGroup
    DESCRIPTION
        "This group is mandatory for agents implementing Extended Trust
         TLV and COS for Untrusted Port TLV of CDP version 2."

   ::= { ciscoCdpMIBCompliances 6 }

-- units of conformance

ciscoCdpMIBGroup  OBJECT-GROUP
    OBJECTS { cdpInterfaceEnable, cdpInterfaceMessageInterval,
              cdpCacheAddressType, cdpCacheAddress, cdpCacheVersion,
              cdpCacheDeviceId, cdpCacheDevicePort,
              cdpCacheCapabilities, cdpCachePlatform
            }
    STATUS  obsolete -- superseded by ciscoCdpMIBGroupV11R01
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol."
    ::= { ciscoCdpMIBGroups 1 }

ciscoCdpMIBGroupV11R01  OBJECT-GROUP
    OBJECTS { cdpInterfaceEnable, cdpInterfaceMessageInterval,
              cdpInterfaceGroup, cdpInterfacePort,
              cdpCacheAddressType, cdpCacheAddress, cdpCacheVersion,
              cdpCacheDeviceId, cdpCacheDevicePort,
              cdpCacheCapabilities, cdpCachePlatform
            }
    STATUS  obsolete -- superseded by ciscoCdpMIBGroupV11R02
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol."
    ::= { ciscoCdpMIBGroups 2 }

ciscoCdpMIBGroupV11R02  OBJECT-GROUP
    OBJECTS { cdpInterfaceEnable,
              cdpInterfaceGroup, cdpInterfacePort,
              cdpCacheAddressType, cdpCacheAddress, cdpCacheVersion,
              cdpCacheDeviceId, cdpCacheDevicePort,
              cdpCacheCapabilities, cdpCachePlatform,
              cdpGlobalRun, cdpGlobalMessageInterval, cdpGlobalHoldTime
            }
    STATUS  obsolete -- superseded by ciscoCdpMIBGroupV12R02
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol."
    ::= { ciscoCdpMIBGroups 3 }

ciscoCdpMIBGroupV12R02 OBJECT-GROUP
    OBJECTS { cdpInterfaceEnable,
              cdpInterfaceGroup, cdpInterfacePort,
              cdpCacheAddressType, cdpCacheAddress, cdpCacheVersion,
              cdpCacheDeviceId, cdpCacheDevicePort,
              cdpCacheCapabilities, cdpCachePlatform,
              cdpCacheVTPMgmtDomain, cdpCacheNativeVLAN, cdpCacheDuplex,
              cdpGlobalRun, cdpGlobalMessageInterval, cdpGlobalHoldTime,
              cdpGlobalDeviceId
            }
    STATUS  deprecated -- superseded by ciscoCdpMIBGroupV12R03
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol."
    ::= { ciscoCdpMIBGroups 5 }

ciscoCdpV2MIBGroup OBJECT-GROUP
    OBJECTS {
              cdpCacheApplianceID, cdpCacheVlanID,
              cdpCachePowerConsumption, cdpCacheMTU,
              cdpCacheSysName, cdpCacheSysObjectID,
              cdpCacheLastChange, cdpCachePhysLocation,
              cdpCachePrimaryMgmtAddrType,
              cdpCachePrimaryMgmtAddr,
              cdpCacheSecondaryMgmtAddrType,
              cdpCacheSecondaryMgmtAddr,
              cdpGlobalLastChange, cdpGlobalDeviceIdFormatCpb,
              cdpGlobalDeviceIdFormat
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol version 2."
    ::= { ciscoCdpMIBGroups 6 }

ciscoCdpV2IfExtGroup OBJECT-GROUP
    OBJECTS {
              cdpInterfaceExtendedTrust,
              cdpInterfaceCosForUntrustedPort
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol version 2 to configure the value
            for Extended Trust TLV and COS for Untrusted Port TLV."
    ::= { ciscoCdpMIBGroups 7 }

ciscoCdpCtAddressGroup OBJECT-GROUP
    OBJECTS {
              cdpCtAddressType,
              cdpCtAddress
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol to obtain the addresses from
            the Address TLV of a received CDP packet."
    ::= { ciscoCdpMIBGroups 8 }

ciscoCdpMIBGroupV12R03 OBJECT-GROUP
    OBJECTS { cdpInterfaceEnable,
              cdpInterfaceGroup, cdpInterfacePort, cdpInterfaceName,
              cdpCacheAddressType, cdpCacheAddress, cdpCacheVersion,
              cdpCacheDeviceId, cdpCacheDevicePort,
              cdpCacheCapabilities, cdpCachePlatform,
              cdpCacheVTPMgmtDomain, cdpCacheNativeVLAN, cdpCacheDuplex,
              cdpGlobalRun, cdpGlobalMessageInterval, cdpGlobalHoldTime,
              cdpGlobalDeviceId
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects for use with the Cisco
            Discovery Protocol."
    ::= { ciscoCdpMIBGroups 9 }

END

