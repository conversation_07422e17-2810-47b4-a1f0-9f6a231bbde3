CISCOSB-IP-SLA DEFINITIONS ::= BEGIN

-- Title:                CISCOSB
--                       SLA MIB
-- Date:                 24-Mar-2016

IMPORTS
    switch001                                       FROM CISCOSB-MIB
    MODULE-IDENTITY, OBJECT-TYPE,
    Unsigned32,<PERSON><PERSON><PERSON><PERSON><PERSON>                            FROM SNMPv2-SMI
    RowStatus, TEXTUAL-CONVENTION, MacAddress,
    DisplayString, TruthValue                       FROM SNMPv2-TC
    InetAddressType,InetAddress                     FROM INET-ADDRESS-MIB; -- RFC2851;

rlSLA   MODULE-IDENTITY
                LAST-UPDATED "201101050000Z"
      ORGANIZATION "Cisco Systems, Inc."

      CONTACT-INFO
      "Postal: 170 West Tasman Drive
      San Jose , CA 95134-1706
      USA

      
      Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

                DESCRIPTION
                      "This private MIB module is used for definition of Service Level Agreements"
		REVISION "201602280000Z"
                DESCRIPTION
                "Added this MODULE-IDENTITY clause."
        ::= { switch001 231 }

-- =======================================================
-- IP SLA operation Table
-- =======================================================

rlSLAOperTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RLSLAOperEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table contains configured ip SLA operations"
    ::= { rlSLA 1}

rlSLAOperEntry OBJECT-TYPE
    SYNTAX RLSLAOperEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The row definition for this table."
    INDEX { rlSLAOperId }
    ::= { rlSLAOperTable 1 }

RLSLAOperEntry::= SEQUENCE {
        rlSLAOperId							Unsigned32,
        rlSLAOperType						INTEGER,
		rlSLAOperState						INTEGER,
		rlSLAOperTimeout					Unsigned32,
		rlSLAOperFrequency					Unsigned32,
		rlSLAOperReqDataSize				Unsigned32,
		rlSLAOperReturnCode					INTEGER,
		rlSLAOperDestAddrType				InetAddressType,
		rlSLAOperDestAddr					InetAddress,
        rlSLAOperSrcAddr					IpAddress,
		rlSLAOperSuccessCounter				Unsigned32,
		rlSLAOperFailureCounter				Unsigned32,
		rlSLAICMPEchoRequestCounter			Unsigned32,
		rlSLAICMPEchoReplyCounter			Unsigned32,
		rlSLAICMPErrorCounter				Unsigned32,
        rlSLARowStatus						RowStatus,
		rlSLAOperNextHopAddr				IpAddress,
		rlSLAICMPEchoHostUnreachCounter		Unsigned32,
		rlSLAICMPEchoNonHostUnreachCounter	Unsigned32
    }

rlSLAOperId OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "IP SLA operation index"
    ::= { rlSLAOperEntry 1 }

rlSLAOperType OBJECT-TYPE
    SYNTAX INTEGER {icmp-echo(1)}
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IP SLA operation type."
    ::= { rlSLAOperEntry 2 }

rlSLAOperState    OBJECT-TYPE
    SYNTAX INTEGER {pending(0), scheduled(1)}
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IP SLA operation state."
    DEFVAL {0}
    ::= { rlSLAOperEntry 3 }

rlSLAOperTimeout       OBJECT-TYPE
    SYNTAX Unsigned32 
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
       "The time in milliseconds an IP SLA operation waits for
         a response."
    ::= { rlSLAOperEntry 4  }

rlSLAOperFrequency     OBJECT-TYPE
    SYNTAX Unsigned32 
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
       "The rate (in seconds) at which a specified operation repeats."
    ::= { rlSLAOperEntry 5  }

rlSLAOperReqDataSize        OBJECT-TYPE
    SYNTAX Unsigned32 (28..1472)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
       "The payload size of the request packet of the IP SLA operation."
    DEFVAL {28}
    ::= { rlSLAOperEntry 6  }

rlSLAOperReturnCode        OBJECT-TYPE
    SYNTAX INTEGER {success(0), error(1)}
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP SLA operation return code."
    DEFVAL {0}
    ::= { rlSLAOperEntry 7 }

rlSLAOperDestAddrType  OBJECT-TYPE
    SYNTAX  InetAddressType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
       "The inet type of destination address"
    ::= { rlSLAOperEntry 8  }

rlSLAOperDestAddr OBJECT-TYPE
    SYNTAX InetAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
       "The destination address of operation (i.e. icmp-echo)"
    ::= { rlSLAOperEntry 9  }

rlSLAOperSrcAddr  OBJECT-TYPE
    SYNTAX  IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
       "The source ip address of operation"
        DEFVAL { '00000000'H }
    ::= { rlSLAOperEntry 10  }


rlSLAOperSuccessCounter        OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP SLA operation success counter."
    ::= { rlSLAOperEntry 11 }

rlSLAOperFailureCounter        OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP SLA operation failure counter."
    ::= { rlSLAOperEntry 12 }

rlSLAICMPEchoRequestCounter        OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP SLA operation ICMP-echo request counter."
    ::= { rlSLAOperEntry 13 }

rlSLAICMPEchoReplyCounter                OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP SLA operation ICMP-echo reply counter."
    ::= { rlSLAOperEntry 14 }

rlSLAICMPErrorCounter         OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP SLA operation ICMP-echo error counter."
    ::= { rlSLAOperEntry 15 }

rlSLARowStatus       OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
       "This variable displays the validity or invalidity of the entry.
         Setting it to 'destroy' has the effect of rendering it inoperative.
         The internal effect (row removal) is implementation dependent."
    ::= { rlSLAOperEntry   16}

rlSLAOperNextHopAddr   OBJECT-TYPE
    SYNTAX  IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
       "The next hop ip address"
        DEFVAL { '00000000'H }
    ::= { rlSLAOperEntry 17  }

rlSLAICMPEchoHostUnreachCounter		OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ICMP-echo host unreachable counter."
    ::= { rlSLAOperEntry 18 }

rlSLAICMPEchoNonHostUnreachCounter         OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ICMP-echo non host unreachable error counter."
    ::= { rlSLAOperEntry 19 }

-- =======================================================
-- IP SLA Track Table
-- =======================================================
rlSLATrackTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RLSLATrackEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of ip SLA operations track objects"
    ::= { rlSLA 2}

rlSLATrackEntry OBJECT-TYPE
    SYNTAX RLSLATrackEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The row definition for this table."
    INDEX { rlSLATrackObj }
    ::= { rlSLATrackTable 1 }

RLSLATrackEntry::= SEQUENCE {
    rlSLATrackObj                Unsigned32,
    rlSLATrackOperId    Unsigned32,
        rlSLAUpDelay                Unsigned32,
        rlSLADownDelay                Unsigned32,
        rlSLADelayReminder         Unsigned32,
        rlSLATrackObjState        INTEGER,
    rlSLATrackRowStatus RowStatus
    }

rlSLATrackObj OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "IP SLA track object identifier"
    ::= { rlSLATrackEntry 1 }

rlSLATrackOperId OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "IP SLA operation index"
    ::= { rlSLATrackEntry 2 }

rlSLAUpDelay OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Delay time moving from down state to up state"
        DEFVAL {0}
    ::= { rlSLATrackEntry 3 }

rlSLADownDelay OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Delay time moving from up state to down state"
        DEFVAL {0}
    ::= { rlSLATrackEntry 4 }

rlSLADelayReminder OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Delay time moving from up state to down state"
    ::= { rlSLATrackEntry 5 }

rlSLATrackObjState    OBJECT-TYPE
    SYNTAX INTEGER {up(0), down(1)}
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The track object current state (Up or Down, default is: Up)."
    DEFVAL {0}
    ::= { rlSLATrackEntry 6 }

rlSLATrackRowStatus       OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
       "This variable displays the validity or invalidity of the entry.
         Setting it to 'destroy' has the effect of rendering it inoperative.
         The internal effect (row removal) is implementation dependent."
    ::= { rlSLATrackEntry   7}

rlSLAClearCounters        OBJECT-TYPE
    SYNTAX      INTEGER(-1..64)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Clear SLA counters.
           Setting a value of zero clears counters of all operations.
           Setting a non-zero value clears the specific operation counters."
    ::= { rlSLA 3 }

END

