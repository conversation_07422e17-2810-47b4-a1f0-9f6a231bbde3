MG-SNMP-UPS-MIB DEFINITIONS ::= BEGIN

-- Title:       UPS MIB
-- Date:        October 11, 1995
-- Author:      <PERSON><PERSON> UPS SYSTEMS
-- Release:     V1.6

-- Date:        October 23, 2003
-- Author:      MGE UPS SYSTEMS
-- Release:     V1.7 AB - Add variables for Environment sensor, see comments beginning with AB Release

-- Date:        November 22, 2004
-- Author:      MGE UPS SYSTEMS - PV
-- Release:     V1.7 AC - Add compatibility with SMI V2  

-- Date:        December 19, 2006
-- Author:      MGE UPS SYSTEMS
-- Release:     V1.7 AD - Add traps 65 and 66 about Redundancy

-- Date:        August 21, 2012
-- Author:      EATON POWER QUALITY
-- Release:     V1.7 AE - Add traps 67 and 68 about Protection Lost

IMPORTS
        enterprises,IpAddress,TimeTicks
                FROM RFC1155-SMI
        DisplayString
                FROM RFC1213-MIB
        OBJECT-TYPE
                FROM RFC-1212
        TRAP-TYPE
                FROM RFC-1215;

----
-- Path to the root
----
merlinGerin     OBJECT IDENTIFIER ::= { enterprises 705 }
upsmg          OBJECT IDENTIFIER ::= { merlinGerin 1 }

-- Management and Configuration groups of the MIB
upsmgIdent        OBJECT IDENTIFIER ::= { upsmg 1 }
upsmgManagement   OBJECT IDENTIFIER ::= { upsmg 2 }
upsmgReceptacle   OBJECT IDENTIFIER ::= { upsmg 3 }
upsmgConfig       OBJECT IDENTIFIER ::= { upsmg 4 }

-- UPS Monitoring groups of the MIB
upsmgBattery      OBJECT IDENTIFIER ::= { upsmg 5 }
upsmgInput        OBJECT IDENTIFIER ::= { upsmg 6 }
upsmgOutput       OBJECT IDENTIFIER ::= { upsmg 7 }
upsmgEnviron      OBJECT IDENTIFIER ::= { upsmg 8 }

-- UPS Controlling groups of the MIB
upsmgControl      OBJECT IDENTIFIER ::= { upsmg 9 }
upsmgTest         OBJECT IDENTIFIER ::= { upsmg 10 }
upsmgTraps        OBJECT IDENTIFIER ::= { upsmg 11 }

upsmgAgent        OBJECT IDENTIFIER ::= { upsmg 12 }
upsmgRemote       OBJECT IDENTIFIER ::= { upsmg 13 }

----
-- Definition of object types
----

-- Management and Configuration groups of the MIB:
-- upsmgIdent(1), upsmgManagement(2), upsmgReceptacle(3), upsmgConfig(4)

--------------------------
-- the upsmgIdent group --
--------------------------

upsmgIdentFamilyName OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS family name (e.g. `PULSAR', `COMET', `GALAXY', ...)."
        ::= { upsmgIdent 1 }

upsmgIdentModelName OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS model name (e.g. `PSX20', `SV9', ... )."
        ::= { upsmgIdent 2 }

upsmgIdentRevisionLevel OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS revision level."
        ::= { upsmgIdent 3 }

upsmgIdentFirmwareVersion OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS microprocessor firmware version number."
        ::= { upsmgIdent 4 }

upsmgIdentUserID OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS user identification (set by administrator)."
        ::= { upsmgIdent 5 }

upsmgIdentInstallationDate OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS installation date in dd/mm/yy format."
        ::= { upsmgIdent 6 }

upsmgIdentSerialNumber OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS manufacturing serial number."
        ::= { upsmgIdent 7 }

--------------------------
-- the upsmgManagement group --
--------------------------

-- This group gives objects for description of a list of Managers.
-- A management platform is identified, can be powered by the UPS,
-- and is allowed to a certain level of management.

upsmgManagersNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The number of managers that could manage the UPS."
        ::= { upsmgManagement 1 }

upsmgManagersTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgManagersEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of managers."
        ::= { upsmgManagement 2 }

upsmgManagersEntry OBJECT-TYPE
        SYNTAX UpsmgManagersEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of a manager entry in the managers table."
        INDEX   { mgmanagerIndex }
        ::= { upsmgManagersTable 1 }

UpsmgManagersEntry ::= SEQUENCE { -- ASN.1 type definition
        mgmanagerIndex INTEGER,
        mgmanagerDeviceNumber INTEGER,
        mgmanagerNMSType INTEGER,
        mgmanagerCommType INTEGER,
        mgmanagerDescr DisplayString,
        mgmanagerAddress IpAddress,
        mgmanagerCommunity DisplayString,
        mgmanagerSeverityLevel INTEGER,
        mgmanagerTrapAck INTEGER
}

mgmanagerIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "A unique value for each manager. Its value ranges between 1
                and the value of upsmgManagersNum."
        ::= { upsmgManagersEntry 1 }

mgmanagerDeviceNumber OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "This object is the number of the manager device in the
                DevicesTable of the Config group. This means that the
                manager is powered by the UPS. If not the default value is 0."
        ::= { upsmgManagersEntry 2 }

mgmanagerNMSType OBJECT-TYPE
        SYNTAX INTEGER {
                umclient(1),
                decnetview(2),
                umview(3),
                dview(4),
                hpopenview(5),
                sunnetmanager(6),
                novellnms(7),
                ibmnetview(8),
                other(9),
                autolearning(10)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The type of manager according to the NMS platform. We
                include in the manager class all systems which will be
                notified by the agent via receiving traps (e.g. basic
                to complex NMS software)."
        ::= { upsmgManagersEntry 3 }

mgmanagerCommType OBJECT-TYPE
        SYNTAX INTEGER {
                other(1),       -- none of the following
                invalid(2),     -- an invalidated manager
                cmip(3),        -- OSI CMIP
                snmpv1(4),      -- SNMPv1
                snmpv2(5)       -- SNMPv2
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The level of communication protocol for that manager.
                Setting this object to value invalid(2) has the effect of
                invalidating the corresponding entry in the upsmgManagersTable.
                It is an implementation-specific matter as to wether the agent
                removes an invalidated entry from the table. Accordingly,
                management stations must be prepared to receive tabular
                information from agents that corresponds to entry not currently
                in use. Proper interpretation of such entries requires
                examination of the relevant mgmanagerCommType object."
        ::= { upsmgManagersEntry 4 }

mgmanagerDescr OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "A textual string containing information about the manager.
                (manufacturer and version description)."
        ::= { upsmgManagersEntry 5 }

mgmanagerAddress OBJECT-TYPE
        SYNTAX IpAddress
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The IP address of the manager station receiving traps. The
                value of this object is meaningful if and only if the value
                of the relevant mgmanagerCommType object is snmp type."
        ::= { upsmgManagersEntry 6 }

mgmanagerCommunity OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The manager community string."
        ::= { upsmgManagersEntry 7 }

mgmanagerSeverityLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The severity level of traps to be received by this manager."
        ::= { upsmgManagersEntry 8 }

mgmanagerTrapAck OBJECT-TYPE
        SYNTAX INTEGER {
                mgack(1),
                mgnoack(2),
                stdnomg(3),
                mgacks(4),
                cpqnoack(5)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The manager Trap Acknowledgement status. Setting
                this object to mgnoack(2) or stdnomg(3) or cpqnoack(5)
                disables the trap acknowledgement from the related
                manager. The MG enterprise specific trap should then
                be sent only once by the agent to this manager in case
                of mgnoack(2), and should not be sent by the agent to
                this manager in case of stdnomg(3) or cpqnoack(5).
                mgack(1) allows a basic mecanism for acknowledged
                traps using upsmgAgentTrapAck object set by the
                manager to the enterprise specific number of the trap.
                mgacks(5) allows the enhanced mecanism using the
                upsmgAgentTrapSignature object set by the manager
                to the signature received within the trap var binding."
        ::= { upsmgManagersEntry 9 }

--------------------------
-- the upsmgReceptacle group --
--------------------------

-- This group gives objects for description of the receptacles of the
-- UPS. The receptacle notion has been extended to many configurations:
-- For a small simple UPS, there is only one receptacle which corresponds
-- to the output of the UPS.
-- For a more complex UPS with powershare capabilities, there is a one-level
-- receptacle table allowing to distribute ON/OFF control on the receptacles.
-- For others (EHQ, big UPS with Distributed Power Units), there is a
-- two-level receptacle table allowing to distribute control and management
-- of the receptacles plugs into one of the first level receptacles.

upsmgReceptaclesNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The total number of receptacles controlled by the UPS."
        ::= { upsmgReceptacle 1 }

upsmgReceptaclesTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgReceptaclesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of receptacles that are controlled by the UPS."
        ::= { upsmgReceptacle 2 }

upsmgReceptaclesEntry OBJECT-TYPE
        SYNTAX UpsmgReceptaclesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of a receptacle in the table."
        INDEX   { mgreceptacleIndex }
        ::= { upsmgReceptaclesTable 1 }

UpsmgReceptaclesEntry ::= SEQUENCE { -- ASN.1 type definition
        mgreceptacleIndex INTEGER,
        mgreceptacleLevel INTEGER,
        mgreceptacleType DisplayString,
        mgreceptacleIdent DisplayString,
        mgreceptacleState INTEGER,
        mgreceptacleReceptacle INTEGER,
        mgreceptaclePowerCons INTEGER,
        mgreceptacleOverload INTEGER,
        mgreceptacleAutonomy INTEGER
 }

mgreceptacleIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "A unique value for each receptacle. Its value ranges between
                1 and the value of upsmgReceptaclesNum."
        ::= { upsmgReceptaclesEntry 1 }

mgreceptacleLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The level of receptacle.

                Setting this object to value invalid(2) has the effect of
                invalidating the corresponding entry in the upsmgReceptaclesTable.
                It is an implementation-specific matter as to wether the agent
                removes an invalidated entry from the table. Accordingly,
                management stations must be prepared to receive tabular
                information from agents that corresponds to entry not currently
                in use. Proper interpretation of such entries requires
                examination of the relevant receptacleLevel object.
                Values 1 and 4 are reserved, value 3 is used to identify a simple
                one-level receptacle, and values greater than 4 represent a
                group of equivalent receptacles."
        ::= { upsmgReceptaclesEntry 2 }

mgreceptacleType OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "A textual string for the receptacle type."
        ::= { upsmgReceptaclesEntry 3 }

mgreceptacleIdent OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "A textual string identification for the user."
        ::= { upsmgReceptaclesEntry 4 }

mgreceptacleState OBJECT-TYPE
        SYNTAX INTEGER {
                manualON(1),    -- after manual turn on
                manualOFF(2),   -- after manual turn off
                normalON(3),    -- after normal restart sequence
                normalOFF(4),   -- after normal shutoff sequence
                controlON(5),   -- after upsControl on command
                controlOFF(6),  -- after upsControl off command
                scheduleON(7),  -- after schedule turn on
                scheduleOFF(8)  -- after schedule turn off
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The ON/OFF status of the receptacle."
        ::= { upsmgReceptaclesEntry 5 }

mgreceptacleReceptacle OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The receptacle number of the second level receptacle father.

                The value of this object is meaningful if and only if the value
                of the relevant mgreceptacleType object is related to a two-level
                receptacle class. Otherwise the default value is 0."
        ::= { upsmgReceptaclesEntry 6 }

mgreceptaclePowerCons OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The volt-ampere rating of the receptacle.

                The value of this object is meaningful if and only if the value
                of the relevant mgreceptacleType object is related to a two-level
                receptacle class. Otherwise the default value is 0."
        ::= { upsmgReceptaclesEntry 7 }

mgreceptacleOverload OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The overload status of the receptacle.

                The value of this object is meaningful if and only if the value
                of the relevant mgreceptacleType object is related to a two-level
                receptacle class. Otherwise the default value is 0."
        ::= { upsmgReceptaclesEntry 8 }

mgreceptacleAutonomy OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The receptacle autonomy.

                The value of this object is meaningful if and only if the value
                of the relevant mgreceptacleType object is related to a two-level
                receptacle class. Otherwise the default value is 0."
        ::= { upsmgReceptaclesEntry 9 }

--------------------------
-- the upsmgConfig group --
--------------------------

-- This group gives objects for describing:
--  Battery characteristics and administrative status,
--  Configuration set points for normal shutoff and restart,
--  Nominal voltage and Input low/high transfer point,
--  Output and By Pass characteristics,
--  Devices table (powered systems),
--  Receptacles default table.
--  Environ external alarms UserID table.

--  Battery characteristics and administrative status.

upsmgConfigBatteryInstalled OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The battery installed status."
        ::= { upsmgConfig 1 }

upsmgConfigNominalBatteryVoltage OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The nominal battery voltage."
        ::= { upsmgConfig 2 }

upsmgConfigNominalBatteryTime OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The full load battery autonomy time."
        ::= { upsmgConfig 3 }

upsmgConfigNominalRechargeTime OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The recharge time to go from low battery cut off
                to full charge at full load."
        ::= { upsmgConfig 4 }

--  Configuration set points for normal shutoff and restart.

upsmgConfigMinRechargeLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The minimum battery level required to supply power on the
                 loads after low battery condition turn off and restart on
                 main return."
        ::= { upsmgConfig 5 }

upsmgConfigMaxRechargeTime OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The maximum time delay of battery recharge before supply
                power to the loads."
        ::= { upsmgConfig 6 }

upsmgConfigLowBatteryTime OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set remaining time value for low battery condition."
        ::= { upsmgConfig 7 }

upsmgConfigLowBatteryLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set battery level value for low battery condition."
        ::= { upsmgConfig 8 }

upsmgConfigAutoRestart OBJECT-TYPE
        SYNTAX INTEGER {
                always(1),
                never(2),
                onmain(3)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The automatic restart enabling status."
        ::= { upsmgConfig 9 }

upsmgConfigShutdownTimer OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set ups shutdown timer from on battery condition."
        ::= { upsmgConfig 10 }

upsmgConfigSysShutDuration OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set ups shutdown delay (Sn delay)."
        ::= { upsmgConfig 11 }

--  Nominal voltage and Input low/high transfer point.

upsmgConfigVARating OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS nominal VA rating."
        ::= { upsmgConfig 12 }

upsmgConfigLowTransfer OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The low voltage transfer point."
        ::= { upsmgConfig 13 }

upsmgConfigHighTransfer OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The high voltage transfer point."
        ::= { upsmgConfig 14 }

--  Output and By Pass characteristics.

upsmgConfigOutputNominalVoltage OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The nominal output voltage."
        ::= { upsmgConfig 15 }

upsmgConfigOutputNominalCurrent OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The nominal output current."
        ::= { upsmgConfig 16 }

upsmgConfigOutputNomFrequency OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The nominal output frequency."
        ::= { upsmgConfig 17 }

upsmgConfigByPassType OBJECT-TYPE
        SYNTAX INTEGER {
                none(1),
                relay(2),
                static(3)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The UPS by pass switch type."
        ::= { upsmgConfig 18 }

upsmgConfigAlarmAudible OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set audible alarm status."
        ::= { upsmgConfig 19 }

upsmgConfigAlarmTimeDelay OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set audible alarm time delay."
        ::= { upsmgConfig 20 }

--  Devices table (powered systems).

upsmgConfigDevicesNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The number of devices that are powered by the UPS."
        ::= { upsmgConfig 21 }

upsmgConfigDevicesTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgConfigDevicesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of devices that are powered by the UPS."
        ::= { upsmgConfig 22 }

upsmgConfigDevicesEntry OBJECT-TYPE
        SYNTAX UpsmgConfigDevicesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of a device entry in the devices table."
        INDEX   { mgdeviceIndex }
        ::= { upsmgConfigDevicesTable 1 }

UpsmgConfigDevicesEntry ::= SEQUENCE { -- ASN.1 type definition
        mgdeviceIndex INTEGER,
        mgdeviceReceptacleNum INTEGER,
        mgdeviceIdent DisplayString,
        mgdeviceVaRating INTEGER,
        mgdeviceSequenceOff INTEGER,
        mgdeviceSequenceOn INTEGER,
        mgdeviceShutdownDuration INTEGER,
        mgdeviceBootUpDuration INTEGER
}

mgdeviceIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "A unique value for each device. Its value ranges between 1
                and the value of upsmgDevicesNum."
        ::= { upsmgConfigDevicesEntry 1 }

mgdeviceReceptacleNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The receptacle number of the device plugged into the UPS.
                This value gives index entry in the ReceptaclesTable object
                of the Receptacle group. A value of 0 invalidates the entry
                in the DevicesTable."
        ::= { upsmgConfigDevicesEntry 2 }

mgdeviceIdent OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "A textual string for device user identification."
        ::= { upsmgConfigDevicesEntry 3 }

mgdeviceVaRating OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The volt-ampere rating of the device plugged into the UPS."
        ::= { upsmgConfigDevicesEntry 4 }

mgdeviceSequenceOff OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set the device position in the remote shutdown sequence."
        ::= { upsmgConfigDevicesEntry 5 }

mgdeviceSequenceOn OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set the device position in the remote reboot sequence."
        ::= { upsmgConfigDevicesEntry 6 }

mgdeviceShutdownDuration OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set the duration from start of computer shut-down
                to complete."
        ::= { upsmgConfigDevicesEntry 7 }

mgdeviceBootUpDuration OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set the duration from start of computer boot-up
                to complete."
        ::= { upsmgConfigDevicesEntry 8 }

--  Receptacles default table.

-- This table gives for every receptacle the power return condition for
-- each failure cause.

upsmgConfigReceptaclesTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgConfigReceptaclesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of receptacles."
        ::= { upsmgConfig 23 }

upsmgConfigReceptaclesEntry OBJECT-TYPE
        SYNTAX UpsmgConfigReceptaclesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of a receptacle entry in the table."
        INDEX   { mgreceptacleIndexb }
        ::= { upsmgConfigReceptaclesTable 1 }

UpsmgConfigReceptaclesEntry ::= SEQUENCE { -- ASN.1 type definition
        mgreceptacleIndexb INTEGER,
        mgreceptacleStateTurnOn INTEGER,
        mgreceptacleStateMainReturn INTEGER,
        mgreceptacleStateDischarge INTEGER,
        mgreceptacleShutoffLevel INTEGER,
        mgreceptacleShutoffTimer INTEGER,
        mgreceptacleRestartLevel INTEGER,
        mgreceptacleRestartDelay INTEGER,
        mgreceptacleShutdownDuration INTEGER,
        mgreceptacleBootUpDuration INTEGER
}

mgreceptacleIndexb OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "A unique value for each receptacle. Its value ranges between 1
                and the value of upsmgReceptaclesNum."
        ::= { upsmgConfigReceptaclesEntry 1 }

mgreceptacleStateTurnOn OBJECT-TYPE
        SYNTAX INTEGER {
                on(1),
                off(2),
                last(3),
                schedule(4)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Default return state at turn on."
        ::= { upsmgConfigReceptaclesEntry 2 }

mgreceptacleStateMainReturn OBJECT-TYPE
        SYNTAX INTEGER {
                on(1),
                off(2),
                last(3),
                schedule(4)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Default return state on main return."
        ::= { upsmgConfigReceptaclesEntry 3 }

mgreceptacleStateDischarge OBJECT-TYPE
        SYNTAX INTEGER {
                on(1),
                off(2),
                last(3),
                schedule(4)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Default return state on main return from battery discharge."
        ::= { upsmgConfigReceptaclesEntry 4 }

mgreceptacleShutoffLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Battery level for starting shutoff sequence."
        ::= { upsmgConfigReceptaclesEntry 5 }

mgreceptacleShutoffTimer OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Time delay from on battery for starting shutoff sequence."
        ::= { upsmgConfigReceptaclesEntry 6 }

mgreceptacleRestartLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Battery level for starting restart sequence."
        ::= { upsmgConfigReceptaclesEntry 7 }

mgreceptacleRestartDelay OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Time delay for starting restart sequence."
        ::= { upsmgConfigReceptaclesEntry 8 }

mgreceptacleShutdownDuration OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "Maximum Shutdown Duration for systems powered by this plug."
        ::= { upsmgConfigReceptaclesEntry 9 }

mgreceptacleBootUpDuration OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "Maximum Reboot Duration for systems powered by this plug."
        ::= { upsmgConfigReceptaclesEntry 10 }

--  Environ external alarms UserID table.

upsmgConfigExtAlarmNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The number of environ external alarm, value is
                  between 8 and 32.."
        ::= { upsmgConfig 24 }

upsmgConfigExtAlarmTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgConfigExtAlarmEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of environmental external alarms."
        ::= { upsmgConfig 25 }

upsmgConfigExtAlarmEntry OBJECT-TYPE
        SYNTAX UpsmgConfigExtAlarmEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of an external alarm in the table."
        INDEX   { mgextAlarmIndex }
        ::= { upsmgConfigExtAlarmTable 1 }

UpsmgConfigExtAlarmEntry ::= SEQUENCE { -- ASN.1 type definition
        mgextAlarmIndex INTEGER,
        mgextAlarmUID DisplayString
}

mgextAlarmIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "A unique value for each external alarm. Its value ranges
                between 1 and the value of upsmgConfigExtAlarmNum."
        ::= { upsmgConfigExtAlarmEntry 1 }

mgextAlarmUID OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "A textual string for alarm state user identification."
        ::= { upsmgConfigExtAlarmEntry 2 }

upsmgConfigEmergencyTestFail OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The emergency condition status on Battery test fail."
        ::= { upsmgConfig 26 }

upsmgConfigEmergencyOnByPass OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The emergency condition status on By Pass."
        ::= { upsmgConfig 27 }

upsmgConfigEmergencyOverload OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The emergency condition status on overload."
        ::= { upsmgConfig 28 }

--  Schedule on/off daily time table.

upsmgConfigControlDayTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgConfigControlDayEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of daily on/off time."
        ::= { upsmgConfig 29 }

upsmgConfigControlDayEntry OBJECT-TYPE
        SYNTAX UpsmgConfigControlDayEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of a daily on/off time schedule in the table."
        INDEX   { mgcontrolDayIndex }
        ::= { upsmgConfigControlDayTable 1 }

UpsmgConfigControlDayEntry ::= SEQUENCE { -- ASN.1 type definition
        mgcontrolDayIndex INTEGER,
        mgcontrolDayOn INTEGER,
        mgcontrolDayOff INTEGER
}

mgcontrolDayIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The day index. It is a number between 1 and 7, 1 for
                Sunday, 2 for Monday, ..."
        ::= { upsmgConfigControlDayEntry 1 }

mgcontrolDayOn OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set daily power on time. Any value greater than 86400
                is to disable the Day On."
        ::= { upsmgConfigControlDayEntry 2 }

mgcontrolDayOff OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set daily power off time. Any value greater than 86400
                is to disable the Day Off."
        ::= { upsmgConfigControlDayEntry 3 }

upsmgConfigLowBooster OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set UPS booster low threshold."
        ::= { upsmgConfig 30 }

upsmgConfigHighBooster OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set UPS booster high threshold."
        ::= { upsmgConfig 31 }

upsmgConfigLowFader OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set UPS fader low threshold."
        ::= { upsmgConfig 32 }

upsmgConfigHighFader OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To set UPS fader high threshold."
        ::= { upsmgConfig 33 }


-- AB Release on 2003/10/23 : Add Configuration of the environment sensor.

upsmgConfigEnvironmentTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgConfigEnvironmentEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table containing the configuration of the environment sensor."
        ::= { upsmgConfig 34 }

upsmgConfigEnvironmentEntry OBJECT-TYPE
        SYNTAX UpsmgConfigEnvironmentEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of an entry in the table."
        INDEX   { upsmgConfigSensorIndex }
        ::= { upsmgConfigEnvironmentTable 1 }

UpsmgConfigEnvironmentEntry ::= SEQUENCE { -- ASN.1 type definition
		upsmgConfigSensorIndex INTEGER,
        upsmgConfigSensorName DisplayString,
		upsmgConfigTemperatureLow INTEGER,
        upsmgConfigTemperatureHigh INTEGER,
        upsmgConfigTemperatureHysteresis INTEGER,
		upsmgConfigHumidityLow INTEGER,
        upsmgConfigHumidityHigh INTEGER,
        upsmgConfigHumidityHysteresis INTEGER,
        upsmgConfigInput1Name DisplayString,
        upsmgConfigInput1ClosedLabel DisplayString,
        upsmgConfigInput1OpenLabel DisplayString,
        upsmgConfigInput2Name DisplayString,
        upsmgConfigInput2ClosedLabel DisplayString,
        upsmgConfigInput2OpenLabel DisplayString 
}

upsmgConfigSensorIndex OBJECT-TYPE
        SYNTAX INTEGER (0..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The sensor index, ranging from 1 to upsmgEnvironmentNum."
        ::= { upsmgConfigEnvironmentEntry 1 }

upsmgConfigSensorName OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..42))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The sensor user-friendly name."
        ::= { upsmgConfigEnvironmentEntry 2 }

upsmgConfigTemperatureLow OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The low temperature threshold in unit 0.1 Cel."
        ::= { upsmgConfigEnvironmentEntry 3 }

upsmgConfigTemperatureHigh OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The high temperature threshold in unit 0.1 Cel."
        ::= { upsmgConfigEnvironmentEntry 4 }

upsmgConfigTemperatureHysteresis OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The temperature hysteresys used for threshold test in unit 0.1 Cel."
        ::= { upsmgConfigEnvironmentEntry 5 }

upsmgConfigHumidityLow OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The low humidity threshold in unit 1 %."
        ::= { upsmgConfigEnvironmentEntry 6 }

upsmgConfigHumidityHigh OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The high humidity threshold in unit 1 %."
        ::= { upsmgConfigEnvironmentEntry 7 }

upsmgConfigHumidityHysteresis OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The humidity hysteresys used for threshold test in unit 1 %."
        ::= { upsmgConfigEnvironmentEntry 8 }

upsmgConfigInput1Name OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..22))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The Input #1 user-friendly name."
        ::= { upsmgConfigEnvironmentEntry 9 }

upsmgConfigInput1ClosedLabel OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..18))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The Input #1 label for closed position."
        ::= { upsmgConfigEnvironmentEntry 10 }

upsmgConfigInput1OpenLabel OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..18))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The Input #1 label for open position."
        ::= { upsmgConfigEnvironmentEntry 11 }

upsmgConfigInput2Name OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..22))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The Input #2 user-friendly name."
        ::= { upsmgConfigEnvironmentEntry 12 }

upsmgConfigInput2ClosedLabel OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..18))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The Input #2 label for closed position."
        ::= { upsmgConfigEnvironmentEntry 13 }

upsmgConfigInput2OpenLabel OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..18))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The Input #2 label for open position."
        ::= { upsmgConfigEnvironmentEntry 14 }


-- UPS Monitoring groups of the MIB:
-- upsmgBattery(5), upsmgInput(6), upsmgOutput(7), upsmgEnviron(8)

----------------------------
-- the upsmgBattery group --
----------------------------

upsmgBatteryRemainingTime OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The time remaining actual charge vs actual load (dynamic)."
        ::= { upsmgBattery 1 }

upsmgBatteryLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The battery level as a percentage of charge."
        ::= { upsmgBattery 2 }

upsmgBatteryRechargeTime OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The actual operational battery recharge time to set point
                equal to upsmgConfigRechargeLevel. This value is computed from
                upsmgConfigRechargeLevel (level to reach), upsmgBatteryLevel (the
                actual reached level) and the UPS charge rate."
        ::= { upsmgBattery 3 }

upsmgBatteryRechargeLevel OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The actual value of the battery recharge level that will be
                reached during the time delay necessary to restart after power
                returns. This value is computed from upsmgConfigRechargeTime
                (the delay), upsmgBatteryLevel (the actual level) and the UPS
                charge rate."
        ::= { upsmgBattery 4 }

upsmgBatteryVoltage OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The actual battery voltage."
        ::= { upsmgBattery 5 }

upsmgBatteryCurrent OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The actual battery current."
        ::= { upsmgBattery 6 }

upsmgBatteryTemperature OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The battery temperature."
        ::= { upsmgBattery 7 }

upsmgBatteryFullRechargeTime OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The battery full recharge time (from 0 to 100 percent)
                at current load."
        ::= { upsmgBattery 8 }

upsmgBatteryFaultBattery OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The battery fault status."
        ::= { upsmgBattery 9 }

upsmgBatteryNoBattery OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS has no Battery status."
        ::= { upsmgBattery 10 }

upsmgBatteryReplacement OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS Battery to be replaced status."
        ::= { upsmgBattery 11 }

upsmgBatteryUnavailableBattery OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS Battery is unavailable status."
        ::= { upsmgBattery 12 }

upsmgBatteryNotHighCharge OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS Battery quit high condition status."
        ::= { upsmgBattery 13 }

upsmgBatteryLowBattery OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The battery low status."
        ::= { upsmgBattery 14 }

upsmgBatteryChargerFault OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS Battery is not charging status."
        ::= { upsmgBattery 15 }

upsmgBatteryLowCondition OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS is at low condition status."
        ::= { upsmgBattery 16 }

upsmgBatteryLowRecharge OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS Battery is not recharged status."
        ::= { upsmgBattery 17 }

--------------------------
-- the upsmgInput group --
--------------------------

upsmgInputPhaseNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The number of input phases."
        ::= { upsmgInput 1 }

upsmgInputPhaseTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgInputPhaseEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of input phases."
        ::= { upsmgInput 2 }

upsmgInputPhaseEntry OBJECT-TYPE
        SYNTAX UpsmgInputPhaseEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of an input phase."
        INDEX   { mginputIndex }
        ::= { upsmgInputPhaseTable 1 }

UpsmgInputPhaseEntry ::= SEQUENCE { -- ASN.1 type definition
        mginputIndex INTEGER,
        mginputVoltage INTEGER,
        mginputFrequency INTEGER,
        mginputMinimumVoltage INTEGER,
        mginputMaximumVoltage INTEGER,
        mginputCurrent INTEGER
}

mginputIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The input phase voltage index. It is a number between 1
                and upsmgInputPhaseNum."
        ::= { upsmgInputPhaseEntry 1 }

mginputVoltage OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The input phase voltage."
        ::= { upsmgInputPhaseEntry 2 }

mginputFrequency OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The input phase frequency."
        ::= { upsmgInputPhaseEntry 3 }

mginputMinimumVoltage OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The input phase minimum voltage over the last minute."
        ::= { upsmgInputPhaseEntry 4 }

mginputMaximumVoltage OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The input phase maximum voltage over the last minute."
        ::= { upsmgInputPhaseEntry 5 }

mginputCurrent OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The input phase current."
        ::= { upsmgInputPhaseEntry 6 }

upsmgInputBadStatus OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The utility power bad voltage or bad frequency status."
        ::= { upsmgInput 3 }

upsmgInputLineFailCause OBJECT-TYPE
        SYNTAX INTEGER {
                no(1),
                outoftolvolt(2),
                outoftolfreq(3),
                utilityoff(4)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The utility power failure cause:
                no(1) is for no utility failure,
                outoftolvolt(2) is for voltage out of tolerance,
                outoftolfreq(3) is for frequency out of tolerance,
                utilityoff(4) is for no voltage at all."
        ::= { upsmgInput 4 }

--------------------------
-- the upsmgOutput group --
--------------------------

upsmgOutputPhaseNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The number of output phases."
        ::= { upsmgOutput 1 }

upsmgOutputPhaseTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgOutputPhaseEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of output phases."
        ::= { upsmgOutput 2 }

upsmgOutputPhaseEntry OBJECT-TYPE
        SYNTAX UpsmgOutputPhaseEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of an output phase."
        INDEX   { mgoutputPhaseIndex }
        ::= { upsmgOutputPhaseTable 1 }

UpsmgOutputPhaseEntry ::= SEQUENCE { -- ASN.1 type definition
        mgoutputPhaseIndex INTEGER,
        mgoutputVoltage INTEGER,
        mgoutputFrequency INTEGER,
        mgoutputLoadPerPhase INTEGER,
        mgoutputCurrent INTEGER
}

mgoutputPhaseIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The output phase voltage index. It is a number between 1
                and upsmgOutputPhaseNum."
        ::= { upsmgOutputPhaseEntry 1 }

mgoutputVoltage OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The output phase voltage."
        ::= { upsmgOutputPhaseEntry 2 }

mgoutputFrequency OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The output phase frequency."
        ::= { upsmgOutputPhaseEntry 3 }

mgoutputLoadPerPhase OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The output load per phase."
        ::= { upsmgOutputPhaseEntry 4 }

mgoutputCurrent OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The output phase current."
        ::= { upsmgOutputPhaseEntry 5 }

upsmgOutputOnBattery OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS on battery / on main status."
        ::= { upsmgOutput 3 }

upsmgOutputOnByPass OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS on bypass status."
        ::= { upsmgOutput 4 }

upsmgOutputUnavailableByPass OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The by pass unavailable status."
        ::= { upsmgOutput 5 }

upsmgOutputNoByPass OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS bypass installed status."
        ::= { upsmgOutput 6 }

upsmgOutputUtilityOff OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS utility off status."
        ::= { upsmgOutput 7 }

upsmgOutputOnBoost OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS on booster status."
        ::= { upsmgOutput 8 }

upsmgOutputInverterOff OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS Inverter on / off status."
        ::= { upsmgOutput 9 }

upsmgOutputOverLoad OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The output over load status."
        ::= { upsmgOutput 10 }

upsmgOutputOverTemp OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The ups over temperature status."
        ::= { upsmgOutput 11 }

upsmgOutputOnBuck OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The UPS on fader status."
        ::= { upsmgOutput 12 }

----------------------------
-- the upsmgEnviron group --
----------------------------

upsmgEnvironAmbientTemp OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The ambient temperature."
        ::= { upsmgEnviron 1 }

upsmgEnvironAmbientHumidity OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The ambient humidity."
        ::= { upsmgEnviron 2 }

upsmgEnvironExtAlarmTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgEnvironExtAlarmEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of external alarms."
        ::= { upsmgEnviron 3 }

upsmgEnvironExtAlarmEntry OBJECT-TYPE
        SYNTAX UpsmgEnvironExtAlarmEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of an external alarm."
        INDEX   { mgalarmNum }
        ::= { upsmgEnvironExtAlarmTable 1 }

UpsmgEnvironExtAlarmEntry ::= SEQUENCE { -- ASN.1 type definition
        mgalarmNum INTEGER,
        mgalarmState INTEGER
}

mgalarmNum OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The external alarm number. A number between 1 and
                upsmgConfigExtAlarmNum."
        ::= { upsmgEnvironExtAlarmEntry 1 }

mgalarmState OBJECT-TYPE
        SYNTAX INTEGER {
                active(1),
                inactive(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The external alarm state (active or inactive)."
        ::= { upsmgEnvironExtAlarmEntry 2 }

upsmgEnvironSensorNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The sensor unit number (0 to 4)."
        ::= { upsmgEnviron 4 }

upsmgEnvironSensorTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgEnvironSensorEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of sensor units."
        ::= { upsmgEnviron 5 }

upsmgEnvironSensorEntry OBJECT-TYPE
        SYNTAX UpsmgEnvironSensorEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of a sensor unit."
        INDEX   { mgsensorNum }
        ::= { upsmgEnvironSensorTable 1 }

UpsmgEnvironSensorEntry ::= SEQUENCE { -- ASN.1 type definition
        mgsensorNum INTEGER,
        mgsensorTemp INTEGER,
        mgsensorHumidity INTEGER
}

mgsensorNum OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The unit number. A number between 1 and 4."
        ::= { upsmgEnvironSensorEntry 1 }

mgsensorTemp OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The measured temperature of the unit."
        ::= { upsmgEnvironSensorEntry 2 }

mgsensorHumidity OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The measured humidity of the unit."
        ::= { upsmgEnvironSensorEntry 3 }

-- AB Release on 2003/10/23 : Add Measurements of the environment sensor.

upsmgEnvironmentNum OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "Number of Environment sensor connected."
        ::= { upsmgEnviron 6 }

upsmgEnvironmentSensorTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgEnvironmentSensorEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table containing the measurements and alarms made by Environment sensor units."
        ::= { upsmgEnviron 7 }

upsmgEnvironmentSensorEntry OBJECT-TYPE
        SYNTAX UpsmgEnvironmentSensorEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of an entry in the measurement table."
        INDEX   { upsmgEnvironmentIndex }
        ::= { upsmgEnvironmentSensorTable 1 }

UpsmgEnvironmentSensorEntry ::= SEQUENCE { -- ASN.1 type definition
		upsmgEnvironmentIndex INTEGER,
        upsmgEnvironmentComFailure INTEGER,
		upsmgEnvironmentTemperature INTEGER,
		upsmgEnvironmentTemperatureLow INTEGER,
		upsmgEnvironmentTemperatureHigh INTEGER,
		upsmgEnvironmentHumidity INTEGER,
		upsmgEnvironmentHumidityLow INTEGER,
		upsmgEnvironmentHumidityHigh INTEGER,
		upsmgEnvironmentInput1State INTEGER,
		upsmgEnvironmentInput2State INTEGER
}

upsmgEnvironmentIndex OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The sensor index, ranging from 1 to upsmgEnvironmentNum."
        ::= { upsmgEnvironmentSensorEntry 1 }

upsmgEnvironmentComFailure OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The sensor communication failure : yes(1), no(2)."
        ::= { upsmgEnvironmentSensorEntry 2 }

upsmgEnvironmentTemperature OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The temperature measurement in unit 0.1 Cel."
        ::= { upsmgEnvironmentSensorEntry 3 }

upsmgEnvironmentTemperatureLow OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "Temperature is below low theshold : yes(1), no(2)."
        ::= { upsmgEnvironmentSensorEntry 4 }

upsmgEnvironmentTemperatureHigh OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "Temperature is above high threshold : yes(1), no(2)."
        ::= { upsmgEnvironmentSensorEntry 5 }

upsmgEnvironmentHumidity OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The humidity measurement in unit 0.1 %."
        ::= { upsmgEnvironmentSensorEntry 6 }

upsmgEnvironmentHumidityLow OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "Humidity is below low threshold : yes(1), no(2)."
        ::= { upsmgEnvironmentSensorEntry 7 }

upsmgEnvironmentHumidityHigh OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "Humidity is above high threshold : yes(1), no(2)."
        ::= { upsmgEnvironmentSensorEntry 8 }

upsmgEnvironmentInput1State OBJECT-TYPE
        SYNTAX INTEGER {
                closed(1),
                open(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "State of Input#1 : closed(1), open(2)."
        ::= { upsmgEnvironmentSensorEntry 9 }

upsmgEnvironmentInput2State OBJECT-TYPE
        SYNTAX INTEGER {
                closed(1),
                open(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "State of Input#2 : closed(1), open(2)."
        ::= { upsmgEnvironmentSensorEntry 10 }


-- UPS Controlling groups of the MIB:
-- upsmgControl(9), upsmgTest(10), upsmgTraps(11)

----------------------------
-- the upsmgControl group --
----------------------------

--  Receptacles control table.

-- This table gives for every receptacle the ON/OFF control objects.

upsmgControlReceptaclesTable OBJECT-TYPE
        SYNTAX SEQUENCE OF UpsmgControlReceptaclesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The table of receptacles."
        ::= { upsmgControl 1 }

upsmgControlReceptaclesEntry OBJECT-TYPE
        SYNTAX UpsmgControlReceptaclesEntry 
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
                "The description of a receptacle entry in the table."
        INDEX   { mgreceptacleIndexc }
        ::= { upsmgControlReceptaclesTable 1 }

UpsmgControlReceptaclesEntry ::= SEQUENCE { -- ASN.1 type definition
        mgreceptacleIndexc INTEGER,
        mgreceptacleOnDelay INTEGER,
        mgreceptacleOnCtrl INTEGER,
        mgreceptacleOnStatus INTEGER,
        mgreceptacleOffDelay INTEGER,
        mgreceptacleOffCtrl INTEGER,
        mgreceptacleOffStatus INTEGER,
        mgreceptacleToggleDelay INTEGER,
        mgreceptacleToggleCtrl INTEGER,
        mgreceptacleToggleStatus INTEGER,
        mgreceptacleToggleDuration INTEGER
}

mgreceptacleIndexc OBJECT-TYPE
        SYNTAX INTEGER (1..50)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "A unique value for each receptacle. Its value ranges between 1
                and the value of upsmgReceptaclesNum."
        ::= { upsmgControlReceptaclesEntry 1 }

mgreceptacleOnDelay OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Turn on UPS receptacle w/delay=?."
        ::= { upsmgControlReceptaclesEntry 2 }

mgreceptacleOnCtrl OBJECT-TYPE
        SYNTAX INTEGER {
                nothing(1),
                start(2),
                stop(3)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The receptacle control on action."
        ::= { upsmgControlReceptaclesEntry 3 }

mgreceptacleOnStatus OBJECT-TYPE
        SYNTAX INTEGER {
                none(1),
                started(2),
                inprogressinups(3),
                completed(4)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The receptacle control on status."
        ::= { upsmgControlReceptaclesEntry 4 }

mgreceptacleOffDelay OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Turn off UPS receptacle w/delay=?."
        ::= { upsmgControlReceptaclesEntry 5 }

mgreceptacleOffCtrl OBJECT-TYPE
        SYNTAX INTEGER {
                nothing(1),
                start(2),
                stop(3)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The receptacle control off action."
        ::= { upsmgControlReceptaclesEntry 6 }

mgreceptacleOffStatus OBJECT-TYPE
        SYNTAX INTEGER {
                none(1),
                started(2),
                inprogressinups(3),
                completed(4)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The receptacle control off status."
        ::= { upsmgControlReceptaclesEntry 7 }

mgreceptacleToggleDelay OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Turn off then on UPS receptacle w/delay=?."
        ::= { upsmgControlReceptaclesEntry 8 }

mgreceptacleToggleCtrl OBJECT-TYPE
        SYNTAX INTEGER {
                nothing(1),
                start(2),
                stop(3)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The receptacle control toggle action."
        ::= { upsmgControlReceptaclesEntry 9 }

mgreceptacleToggleStatus OBJECT-TYPE
        SYNTAX INTEGER {
                none(1),
                started(2),
                inprogressinups(3),
                completed(4)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The receptacle control toggle status."
        ::= { upsmgControlReceptaclesEntry 10 }

mgreceptacleToggleDuration OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "Turn off then on UPS receptacle w/duration=?."
        ::= { upsmgControlReceptaclesEntry 11 }

upsmgControlDayOff OBJECT-TYPE
        SYNTAX INTEGER {
                sunday(1), monday(2), tuesday(3), wednesday(4),
                thursday(5), friday(6), saterday(7), none(8)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The schedule off control object. Default value is none(8).
                Value 1 means to start scheduled off on Sunday,
                value 2 to start scheduled off on Monday, and so on."
        ::= { upsmgControl 2 }

upsmgControlDayOn OBJECT-TYPE
        SYNTAX INTEGER {
                sunday(1), monday(2), tuesday(3), wednesday(4),
                thursday(5), friday(6), saterday(7), none(8)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The schedule on control object. Default value is none(8).
                Value 1 means to start scheduled on on Sunday,
                value 2 to start scheduled on on Monday, and so on.
                This object is meaningfull only for those agent that do not
                include internal clock. In such a case the object is set by
                a manager to start the schedule on sequence."
        ::= { upsmgControl 3 }

--------------------------
-- the upsmgTest group --
--------------------------

upsmgTestBatterySchedule OBJECT-TYPE
        SYNTAX INTEGER {
                unknown(1),
                weekly(2),
                monthly(3),
                atturnon(4),
                none(5),
                dayly(6)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "The automatic battery test schedule."
        ::= { upsmgTest 1 }

upsmgTestDiagnostics OBJECT-TYPE
        SYNTAX INTEGER {
                default(1),
                start(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To run self test diagnostics."
        ::= { upsmgTest 2 }

upsmgTestDiagResult OBJECT-TYPE
        SYNTAX INTEGER {
                success(1),
                failed(2),
                none(3)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The automatic test diagnostic result."
        ::= { upsmgTest 3 }

upsmgTestBatteryCalibration OBJECT-TYPE
        SYNTAX INTEGER {
                default(1),
                start(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To run battery test calibration."
        ::= { upsmgTest 4 }

upsmgTestLastCalibration OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The last battery calibration test date."
        ::= { upsmgTest 5 }

upsmgTestIndicators OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To run UPS indicators test."
        ::= { upsmgTest 6 }

upsmgTestCommandLine OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To send ASCII command line to UPS."
        ::= { upsmgTest 7 }

upsmgTestCommandReady OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "To advise UPS that command line has been prepared."
        ::= { upsmgTest 8 }

upsmgTestResponseLine OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "To get ASCII protocol response from UPS."
        ::= { upsmgTest 9 }

upsmgTestResponseReady OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "To advise agent that response line has been received."
        ::= { upsmgTest 10 }

upsmgTestBatteryResult OBJECT-TYPE
        SYNTAX INTEGER {
                msuccess(1),
                mfailed(2),
                ssuccess(3),
                sfailed(4),
                none(5)
        }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                "The test battery result:
                msuccess(1) is for manual battery test success,
                ssuccess(3) is for scheduled battery test success,
                mfailed(2) is for manual battery test failed,
                sfailed(4) is for scheduled battery test failed."
        ::= { upsmgTest 11 }

--------------------------
-- the upsmgTraps group --
--------------------------

-- This group defines objects and traps, so that for each trap, simple
-- get request on related objects (one or many) allow to confirm actual
-- status of the trap.

-- TRAPS NOTIFICATIONS

upsmgBatteryFault TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS Battery entering Fault status." 
       --#SEVERITY MAJOR
::= 1

upsmgBatteryOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS Battery returned to OK status."
       --#SEVERITY INFORMATIONAL
::= 2

upsmgBatteryReplacementIndicated TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS battery need to be replaced."
       --#SEVERITY MINOR
::= 3

upsmgBatteryReplaceNotIndicated TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS battery does not need to be replaced."
       --#SEVERITY INFORMATIONAL
::= 4

upsmgAtLowBattery TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS battery reached low condition."
       --#SEVERITY CRITICAL
::= 5

upsmgFromLowBattery TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS battery returned from low condition."
       --#SEVERITY INFORMATIONAL
::= 6

upsmgChargerFault TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS battery charger is not active."
       --#SEVERITY MINOR
::= 7

upsmgChargerOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS battery charger is active."
       --#SEVERITY INFORMATIONAL
::= 8

upsmgAtLowCondition TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS entering low condition."
       --#SEVERITY CRITICAL
::= 9

upsmgFromLowCondition TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS exiting low condition."
       --#SEVERITY INFORMATIONAL
::= 10

upsmgOnBattery TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS is on Battery." 
       --#SEVERITY CRITICAL
::= 11

upsmgReturnFromBattery TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS returned from battery."
       --#SEVERITY INFORMATIONAL
::= 12

upsmgOnByPass TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS is on by pass."
       --#SEVERITY MAJOR
::= 13

upsmgReturnFromByPass TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS return from by pass."
       --#SEVERITY INFORMATIONAL
::= 14

upsmgByPassUnavailable TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS by pass unavailable."
       --#SEVERITY MINOR
::= 15

upsmgByPassAvailable TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS by pass available."
       --#SEVERITY INFORMATIONAL
::= 16

upsmgUtilityFailure TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS utility power failed."
       --#SEVERITY MAJOR
::= 17

upsmgUtilityRestored TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS utility power restored."
       --#SEVERITY INFORMATIONAL
::= 18

upsmgOnBoost TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS is on boost."
       --#SEVERITY MINOR
::= 19

upsmgReturnFromBoost TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS has returned from boost."
       --#SEVERITY INFORMATIONAL
::= 20

upsmgOverLoad TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS has overload condition."
       --#SEVERITY MAJOR
::= 21

upsmgLoadOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS quit overload condition."
       --#SEVERITY INFORMATIONAL
::= 22

upsmgOverTemperature TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS has overtemperature condition."
       --#SEVERITY MAJOR
::= 23

upsmgTemperatureOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS quit overtemperature condition."
       --#SEVERITY INFORMATIONAL
::= 24

upsmgOnToStart TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleState, mgreceptacleOnDelay,
                        mgreceptacleRestartDelay }
        DESCRIPTION
                "Trap UPS on sequence started."
       --#SEVERITY MAJOR
::= 25

upsmgOnAbort TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleOnDelay }
        DESCRIPTION
                "Trap UPS on sequence cancelled."
       --#SEVERITY INFORMATIONAL
::= 26

upsmgOnInProgress TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleState,
                        mgreceptacleBootUpDuration }
        DESCRIPTION
                "Trap UPS on sequence in progress in ups."
       --#SEVERITY CRITICAL
::= 27

upsmgOnComplete TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleOnDelay }
        DESCRIPTION
                "Trap UPS on sequence completed."
       --#SEVERITY INFORMATIONAL
::= 28

upsmgOffToStart TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleState, mgreceptacleOffDelay,
                        mgreceptacleShutoffTimer, upsmgConfigSysShutDuration }
        DESCRIPTION
                "Trap UPS off sequence started."
       --#SEVERITY MAJOR
::= 29

upsmgOffAbort TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleOffDelay }
        DESCRIPTION
                "Trap UPS off sequence cancelled."
       --#SEVERITY INFORMATIONAL
::= 30

upsmgOffInProgress TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleState,
                        mgreceptacleShutdownDuration }
        DESCRIPTION
                "Trap UPS off sequence in progress in ups."
       --#SEVERITY CRITICAL
::= 31

upsmgOffComplete TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleOffDelay }
        DESCRIPTION
                "Trap UPS off sequence completed."
       --#SEVERITY INFORMATIONAL
::= 32

upsmgToggleToStart TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleToggleDelay }
        DESCRIPTION
                "Trap UPS toggle (off/on) sequence started."
       --#SEVERITY MAJOR
::= 33

upsmgToggleAbort TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleToggleDelay }
        DESCRIPTION
                "Trap UPS toggle (off/on) sequence cancelled."
       --#SEVERITY INFORMATIONAL
::= 34

upsmgToggleInProgress TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleToggleDuration }
        DESCRIPTION
                "Trap UPS toggle (off/on) sequence in progress in ups."
       --#SEVERITY MAJOR
::= 35

upsmgToggleComplete TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgreceptacleIndex, mgreceptacleToggleDuration }
        DESCRIPTION
                "Trap UPS toggle (off/on) sequence completed."
       --#SEVERITY INFORMATIONAL
::= 36

upsmgCommunicationFailure TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS serial communication failed."
       --#SEVERITY CRITICAL
::= 37

upsmgCommunicationRestored TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS serial communication restored."
       --#SEVERITY INFORMATIONAL
::= 38

upsmgInputBad TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS input has bad condition."
       --#SEVERITY MINOR
::= 39

upsmgInputOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS input quit bad condition."
       --#SEVERITY INFORMATIONAL
::= 40

upsmgBatteryUnavailable TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS battery is unavailable."
       --#SEVERITY MINOR
::= 41

upsmgBatteryAvailable TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS battery return from unavailable condition."
       --#SEVERITY INFORMATIONAL
::= 42

upsmgAtLowRecharge TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS waiting for battery charging condition."
       --#SEVERITY INFORMATIONAL
::= 43

upsmgFromLowRecharge TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { upsmgBatteryRemainingTime, upsmgBatteryLevel }
        DESCRIPTION
                "Trap UPS reached battery charging condition."
       --#SEVERITY INFORMATIONAL
::= 44

upsmgDiagnosticTestFail TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS self test failed."
       --#SEVERITY MINOR
::= 45

upsmgDiagnosticTestOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS self test succeeded."
       --#SEVERITY INFORMATIONAL
::= 46

upsmgBatteryTestOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS passed battery test."
       --#SEVERITY INFORMATIONAL
::= 47

upsmgBatteryTestFail TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS failed battery test."
       --#SEVERITY MAJOR
::= 48

upsmgExternalAlarmActive TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgextAlarmIndex, mgextAlarmUID }
        DESCRIPTION
                "Trap UPS enter environment external alarm."
       --#SEVERITY MAJOR
::= 49

upsmgExternalAlarmInactive TRAP-TYPE
        ENTERPRISE upsmgTraps
        VARIABLES { mgextAlarmIndex, mgextAlarmUID }
        DESCRIPTION
                "Trap UPS exit environment external alarm."
       --#SEVERITY INFORMATIONAL
::= 50

upsmgOnBuck TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS is on buck."
       --#SEVERITY MINOR
::= 51

upsmgReturnFromBuck TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Trap UPS has returned from buck."
       --#SEVERITY INFORMATIONAL
::= 52

-- AB Release on 2003/10/23 : Traps 53 to 64 added for the environment sensor.

upsmgEnvironComFailure TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Environment Probe communication failure."
       --#SEVERITY MAJOR
::= 53

upsmgEnvironComOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Environment Probe communication restored."
       --#SEVERITY INFORMATIONAL
::= 54

upsmgEnvironTemperatureLow TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Temperature is below low threshold."
       --#SEVERITY MAJOR
::= 55

upsmgEnvironTemperatureHigh TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Temperature is above high threshold."
       --#SEVERITY MAJOR
::= 56

upsmgEnvironTemperatureOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Temperature is in normal range."
       --#SEVERITY INFORMATIONAL
::= 57

upsmgEnvironHumidityLow TRAP-TYPE
       ENTERPRISE upsmgTraps
       DESCRIPTION
                "Humidity is below low threshold."
       --#SEVERITY MAJOR
::= 58

upsmgEnvironHumidityHigh TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Humidity is above high threshold."
       --#SEVERITY MAJOR
::= 59

upsmgEnvironHumidityOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Humidity is in normal range."
       --#SEVERITY INFORMATIONAL
::= 60

upsmgEnvironInput1Closed TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Input #1 is Closed."
       --#SEVERITY INFORMATIONAL
::= 61

upsmgEnvironInput1Open TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Input #1 is Open."
       --#SEVERITY INFORMATIONAL
::= 62

upsmgEnvironInput2Closed TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Input #2 is Closed."
       --#SEVERITY INFORMATIONAL
::= 63

upsmgEnvironInput2Open TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Input #2 is Open."
       --#SEVERITY INFORMATIONAL
::= 64

-- AD Release on 2006/12/19 : Traps 65 and 66 added about redundancy.

upsRedundancyLost TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Redundancy Lost." 
       --#SEVERITY MAJOR
::= 65

upsRedundancyOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Redundancy Recovered."
       --#SEVERITY INFORMATIONAL
::= 66

-- AE Release on 2012/08/21 : Traps 67 and 68 added about protection lost.

upsProtectionLost TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Protection Lost." 
       --#SEVERITY MAJOR
::= 67

upsProtectionOK TRAP-TYPE
        ENTERPRISE upsmgTraps
        DESCRIPTION
                "Protection Recovered."
       --#SEVERITY INFORMATIONAL
::= 68



----------------------
-- upsmgAgent group --
----------------------

--  following objects are configuration variables defined for
--  UPS proxy agent that perform UPS management protocol
--  and support SNMP management function

upsmgAgentIpaddress OBJECT-TYPE
        SYNTAX IpAddress
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The ip address that NMS can identify the managed
                device"
        ::= { upsmgAgent 1}

upsmgAgentSubnetMask OBJECT-TYPE
        SYNTAX IpAddress
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " Internet address subnet mask"
        ::= { upsmgAgent 2}

upsmgAgentDefGateway OBJECT-TYPE
        SYNTAX IpAddress
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The default gateway that allow device managed
                through routers "
        ::= { upsmgAgent 3}

upsmgAgentBaudRate OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " the serial port communication speed. only 2400,
                4800, and 9600 allowed"
        ::= { upsmgAgent 4}

upsmgAgentPollRate OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The frequency that proxy agent polls the connected
                UPS in ASCII protocol. (unit : second)"
        ::= { upsmgAgent 5}

upsmgAgentType OBJECT-TYPE
        SYNTAX INTEGER {
                deviceEth(1),
                deviceTR(2),
                proxyEth(3),
                proxyTR(4),
                other(5)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                 " The type of agent."
        ::= { upsmgAgent 6}

upsmgAgentTrapAlarmDelay OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The interval for the Trap packet retransmission,
                while the TRAP Acknowledge is implemented and
                the ack is received within the interval."
        ::= { upsmgAgent 7}

upsmgAgentTrapAlarmRetry OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                 " The retry count for retransmitting Trap
                 packet while the TRAP acknowledge is
                 implemented."
        ::= { upsmgAgent 8}

upsmgAgentReset OBJECT-TYPE
        SYNTAX INTEGER {
                reset (1),
                nothing(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                 " The variable initiates reset operation  in
                 agent software, ie. warm start. write reset(1)
                 to any one of UPS MIB array, will result in
                 the same reset operation."
        ::= { upsmgAgent 9}

upsmgAgentFactReset OBJECT-TYPE
        SYNTAX INTEGER {
                reset (1),
                nothing(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                 " The variable initiates reset operation in
                 agent software, ie. warm start, and load
                 default data (Factory setting) to EEPROM
                 and to runtime parameters associated with
                 the UPS."
        ::= { upsmgAgent 10}

upsmgAgentMibVersion OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                " The version of the MIB implemented in agent."
        ::= { upsmgAgent 11}

upsmgAgentFirmwareVersion OBJECT-TYPE
        SYNTAX DisplayString(SIZE(0..32))
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                " The agent firmware version."
        ::= { upsmgAgent 12}

upsmgAgentCommUPS OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                " The status of communication between agent and ups.
                Even value means no communication, and odd value
                means a certain level of communication with devices
                daisy-chained on the serial port. The value is the
                following: 1000*NSE + 100*NSW + 10*UPSW + UPST with
                UPST is UPS type:       5 if no UPS;
                                        3 if PI (Protocol Interface)
                                        1 if U-Talk UPS;
                UPSW is the number of switchable plugs of the UPS;
                NSW is the number of UM-Switch devices;
                NSE is the number of UM-Sensor devices."
        ::= { upsmgAgent 13}

upsmgAgentTrapAck OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The last Trap number that has been received by UM-View."
        ::= { upsmgAgent 14}

upsmgAgentAutoLearning OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The enabled/disabled configuration of the Auto Learning
                process of the agent, default value is yes(1) e.g. enabled."
        ::= { upsmgAgent 15}

upsmgAgentBootP OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The enabled/disabled configuration of the BootP process,
                default value is yes(1) e.g. enabled."
        ::= { upsmgAgent 16}

upsmgAgentTFTP OBJECT-TYPE
        SYNTAX INTEGER {
                yes(1),
                no(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                " The enabled/disabled configuration of the TFTP process,
                default value is no(2) e.g. disabled."
        ::= { upsmgAgent 17}

upsmgAgentTrapSignature OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
                " The signature of the trap acknowledged by a manager."
        ::= { upsmgAgent 18}

------------------------
-- upsmgRemote group --
------------------------

upsmgRemoteOnBattery OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "An object to inform an ups-less agent that the remote
                 UPS is on autonomy. This object could be set by
                 a management application."
        ::= { upsmgRemote 1}

upsmgRemoteIpAddress OBJECT-TYPE
        SYNTAX IpAddress
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
                "An object to give the IP address of the agent managing
                 the remote UPS. This object could be read by managers
                 to perform management of receptacle dependencies
                 between two different agents."
        ::= { upsmgRemote 2}

END
