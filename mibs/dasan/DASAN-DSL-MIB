--
-- DASAN-DSL-MIB2.my
-- MIB generated by MG-<PERSON><PERSON><PERSON> Visual MIB Builder Version 6.0  Build 88
-- Wednesday, December 26, 2007 at 13:55:53
--

--  DASAN-DSL-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 3.0 Build 285
-- Tuesday, April 03, 2007 at 18:00:02
-- 

	DASAN-DSL-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			dasanMgmt			
				FROM DASAN-SMI			
			ifIndex			
				FROM IF-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, Integer32, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>32, 
			<PERSON>64, OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SM<PERSON>			
			DisplayString, PhysAddress, RowStatus			
				FROM SNMPv2-TC;
	
	
--  *******.4.1.6296.9.3
-- April 19, 2001 at 00:00 GMT
		-- *******.4.1.6296.9.3
		dasanDslMIB MODULE-IDENTITY 
			LAST-UPDATED "200104190000Z"		-- April 19, 2001 at 00:00 GMT
			ORGANIZATION 
				"Dasan Co., Ltd."
			CONTACT-INFO 
				"Dasan Co., Ltd."
			DESCRIPTION 
				"The MIB module to describe SDSL product."
			::= { dasanMgmt 3 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.9.3.1
		dasanDslMIBObjects OBJECT IDENTIFIER ::= { dasanDslMIB 1 }

		
		-- *******.4.1.6296.*******
		dsDslSystem OBJECT IDENTIFIER ::= { dasanDslMIBObjects 1 }

		
		-- *******.4.1.6296.*******
		dsDslModules OBJECT IDENTIFIER ::= { dasanDslMIBObjects 2 }

		
		-- *******.4.1.6296.*******.1
		moduleNumber OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The number of DSL modules (regardless of their
				current state) present on this system."
			::= { dsDslModules 1 }

		
--  
-- 
-- 
		-- *******.4.1.6296.*******.2
		moduleTableLastChange OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The value of sysUpTime at the time of the last
				creation or deletion of an entry in the moduleTable.  If
				the number of entries has been unchanged since the
				last re-initialization of the local network management
				subsystem, then this object contains a zero value."
			::= { dsDslModules 2 }

		
		-- *******.4.1.6296.*******.3
		moduleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF ModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of module entries.  The number of entries
				is given by the value of moduleNumber."
			::= { dsDslModules 3 }

		
		-- *******.4.1.6296.*******.3.1
		moduleEntry OBJECT-TYPE
			SYNTAX ModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular module."
			INDEX { moduleIndex }
			::= { moduleTable 1 }

		
		ModuleEntry ::=
			SEQUENCE { 
				moduleIndex
					INTEGER,
				moduleDescr
					DisplayString,
				moduleType
					DisplayString,
				modulePhysAddress
					PhysAddress,
				moduleOperStatus
					INTEGER,
				moduleUpTime
					DisplayString,
				moduleAvailableMemory
					INTEGER,
				modulePortNumber
					INTEGER,
				modulePortIndexBase
					INTEGER,
				moduleSpecific
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*******.3.1.1
		moduleIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { moduleEntry 1 }

		
		-- *******.4.1.6296.*******.3.1.2
		moduleDescr OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"A textual string containing information about the
				module.  This string should include the name of the
				manufacturer, the product name and the version of the
				interface hardware/software."
			::= { moduleEntry 2 }

		
		-- *******.4.1.6296.*******.3.1.3
		moduleType OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The type of module. This string defines the type
				of DSL module type. For instance, this can be
				SDSL, VDSL or SDSL."
			::= { moduleEntry 3 }

		
-- 
-- 
		-- *******.4.1.6296.*******.3.1.4
		modulePhysAddress OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The module's address at its ethernet interface.
				For example, this object normally contains a MAC address."
			::= { moduleEntry 4 }

		
		-- *******.4.1.6296.*******.3.1.5
		moduleOperStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The current operational state of the module."
			::= { moduleEntry 5 }

		
		-- *******.4.1.6296.*******.3.1.6
		moduleUpTime OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"Elasped time since this module has been boot up."
			::= { moduleEntry 6 }

		
		-- *******.4.1.6296.*******.3.1.7
		moduleAvailableMemory OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The amount of free memory in this module."
			::= { moduleEntry 7 }

		
		-- *******.4.1.6296.*******.3.1.8
		modulePortNumber OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The number of ethernet ports and ethernet-like DSL ports
				(regardless of their current state) present on this module."
			::= { moduleEntry 8 }

		
		-- *******.4.1.6296.*******.3.1.9
		modulePortIndexBase OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"Port index base for this module. Port index range of this module
				is from modulePortIndexBase to (modulePortInexBase + modulePortNumber - 1)."
			::= { moduleEntry 9 }

		
		-- *******.4.1.6296.*******.3.1.10
		moduleSpecific OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"A reference to MIB definitions specific to the
				particular media being used to realize the module.
				It is recommended that this value point to an instance
				of a MIB object in the media-specific MIB, i.e., that
				this object have the semantics associated with the
				InstancePointer textual convention defined in RFC
				1903.  In fact, it is recommended that the module-
				specific MIB specify what value moduleSpecific should/can
				take for values of moduleType.  If no MIB definitions
				specific to the particular media are available, the
				value should be set to the OBJECT IDENTIFIER { 0 0 }."
			::= { moduleEntry 10 }

		
		-- *******.4.1.6296.*******
		dsDslPorts OBJECT IDENTIFIER ::= { dasanDslMIBObjects 3 }

		
		-- *******.4.1.6296.*******.1
		dsDslPortNumber OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The number of DSL modules (regardless of their
				current state) present on this system."
			::= { dsDslPorts 1 }

		
		-- *******.4.1.6296.*******.2
		dsDslPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsDslPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of module entries.  The number of entries
				is given by the value of moduleNumber."
			::= { dsDslPorts 2 }

		
		-- *******.4.1.6296.*******.2.1
		dsDslPortEntry OBJECT-TYPE
			SYNTAX DsDslPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular port."
			INDEX { portIndex }
			::= { dsDslPortTable 1 }

		
		DsDslPortEntry ::=
			SEQUENCE { 
				portIndex
					INTEGER,
				portDescr
					DisplayString,
				portType
					DisplayString,
				portIfIndex
					INTEGER,
				portAdminStatus
					INTEGER,
				portOperStatus
					INTEGER,
				portModemStatus
					INTEGER,
				portLineQuality
					DisplayString,
				portUpStreamSpeed
					INTEGER,
				portDownStreamSpeed
					INTEGER,
				portLastModemStatusChange
					DisplayString,
				portSpecific
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*******.2.1.1
		portIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsDslPortEntry 1 }

		
		-- *******.4.1.6296.*******.2.1.2
		portDescr OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"A textual string containing information about the port."
			::= { dsDslPortEntry 2 }

		
		-- *******.4.1.6296.*******.2.1.3
		portType OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The type of communication port. This string defines the type
				of port. Each module has one or more ethernet port(s) and
				xDSL ports."
			::= { dsDslPortEntry 3 }

		
		-- *******.4.1.6296.*******.2.1.4
		portIfIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"ifIndex which is defined in interface mib."
			::= { dsDslPortEntry 4 }

		
		-- *******.4.1.6296.*******.2.1.5
		portAdminStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(26)
				}
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The current administrative state of the port."
			::= { dsDslPortEntry 5 }

		
		-- *******.4.1.6296.*******.2.1.6
		portOperStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The current operational state of the port."
			::= { dsDslPortEntry 6 }

		
		-- *******.4.1.6296.*******.2.1.7
		portModemStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				idle(1),
				trying(2),
				established(3)
				}
			MAX-ACCESS read-write
			STATUS obsolete
			DESCRIPTION
				"idle   : system does not try to establish modem link.
				trying : system is trying to establish modem link.
				established : modem link has been established.
				If trying is written, modem will be reset and the system
				will try to re-establish modem link.
				If idle is set, system will not manage modem connection.
				When established is written, it will be ignored silently."
			::= { dsDslPortEntry 7 }

		
		-- *******.4.1.6296.*******.2.1.8
		portLineQuality OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The ascii string which describes the quality of link.
				Description can be distinct for DSL link type."
			::= { dsDslPortEntry 8 }

		
		-- *******.4.1.6296.*******.2.1.9
		portUpStreamSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"An estimate of the port's current bandwidth in
				bits per second.  For interfaces which do not vary in
				bandwidth or for those where no accurate estimation
				can be made, this object should contain the nominal
				bandwidth.  If the bandwidth of the interface is
				greater than the maximum value reportable by this
				object then this object should report its maximum
				value (4,294,967,295).  For a sub-layer which
				has no concept of bandwidth, this object should be
				zero."
			::= { dsDslPortEntry 9 }

		
		-- *******.4.1.6296.*******.2.1.10
		portDownStreamSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"An estimate of the port's current bandwidth in
				bits per second.  For interfaces which do not vary in
				bandwidth or for those where no accurate estimation
				can be made, this object should contain the nominal
				bandwidth.  If the bandwidth of the interface is
				greater than the maximum value reportable by this
				object then this object should report its maximum
				value (4,294,967,295).  For a sub-layer which
				has no concept of bandwidth, this object should be
				zero."
			::= { dsDslPortEntry 10 }

		
		-- *******.4.1.6296.*******.2.1.11
		portLastModemStatusChange OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"Elasped time since modem status has been changed."
			::= { dsDslPortEntry 11 }

		
		-- *******.4.1.6296.*******.2.1.12
		portSpecific OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"A reference to MIB definitions specific to the
				particular media being used to realize the module.
				It is recommended that this value point to an instance
				of a MIB object in the media-specific MIB, i.e., that
				this object have the semantics associated with the
				InstancePointer textual convention defined in RFC
				1903.  In fact, it is recommended that the module-
				specific MIB specify what value moduleSpecific should/can
				take for values of moduleType.  If no MIB definitions
				specific to the particular media are available, the
				value should be set to the OBJECT IDENTIFIER { 0 0 }."
			::= { dsDslPortEntry 12 }

		
		-- *******.4.1.6296.*******
		dsDslLearnedMacs OBJECT IDENTIFIER ::= { dasanDslMIBObjects 4 }

		
		-- *******.4.1.6296.*******.1
		macNumber OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"The number of DSL modules (regardless of their
				current state) present on this system."
			::= { dsDslLearnedMacs 1 }

		
		-- *******.4.1.6296.*******.2
		macTable OBJECT-TYPE
			SYNTAX SEQUENCE OF MacEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of module entries.  The number of entries
				is given by the value of moduleNumber."
			::= { dsDslLearnedMacs 2 }

		
		-- *******.4.1.6296.*******.2.1
		macEntry OBJECT-TYPE
			SYNTAX MacEntry
			MAX-ACCESS not-accessible
			STATUS deprecated
			DESCRIPTION
				"Each entry contains one MAC address and its aging time."
			INDEX { macIndex }
			::= { macTable 1 }

		
		MacEntry ::=
			SEQUENCE { 
				macIndex
					INTEGER,
				macAddress
					PhysAddress,
				agingTime
					INTEGER
			 }

		-- *******.4.1.6296.*******.2.1.1
		macIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"MAC table entry index."
			::= { macEntry 1 }

		
		-- *******.4.1.6296.*******.2.1.2
		macAddress OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"MAC address learned on this port."
			::= { macEntry 2 }

		
		-- *******.4.1.6296.*******.2.1.3
		agingTime OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS obsolete
			DESCRIPTION
				"Elapased time since last RX activity from this MAC-addressed host."
			::= { macEntry 3 }

		
--  
-- 
-- 
		-- *******.4.1.62********
		dasanDslMIBObjects2 OBJECT IDENTIFIER ::= { dasanDslMIB 2 }

		
--  
-- 
-- 
		-- *******.4.1.6296.*******
		dsVdslNotifications OBJECT IDENTIFIER ::= { dasanDslMIBObjects2 0 }

		
--  
-- 
-- 
		-- *******.4.1.6296.*******
		dsVdslSystemBaseInfo OBJECT IDENTIFIER ::= { dasanDslMIBObjects2 1 }

		
--  
-- 
		-- *******.4.1.6296.*******.1
		dsVdslSystemFWVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslSystemBaseInfo 1 }

		
		-- *******.4.1.6296.*******.2
		dsVdslSystemLowPowerMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslSystemBaseInfo 2 }

		
		-- *******.4.1.6296.*******.3
		dsVdslSystemBmeReset OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"select bme-number to reset"
			::= { dsVdslSystemBaseInfo 3 }

		
-- 
-- 
		-- *******.4.1.6296.*******.50
		dsVdslSystemUpboConfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslSystemUpboConfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslSystemBaseInfo 50 }

		
		-- *******.4.1.6296.*******.50.1
		dsVdslSystemUpboConfEntry OBJECT-TYPE
			SYNTAX DsVdslSystemUpboConfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { dsVdslSystemUpboConfLength }
			::= { dsVdslSystemUpboConfTable 1 }

		
		DsVdslSystemUpboConfEntry ::=
			SEQUENCE { 
				dsVdslSystemUpboConfLength
					INTEGER,
				dsVdslSystemUpboConfArrayK1
					OCTET STRING,
				dsVdslSystemUpboConfArrayK2
					OCTET STRING
			 }

		-- *******.4.1.6296.*******.50.1.1
		dsVdslSystemUpboConfLength OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslSystemUpboConfEntry 1 }

		
		-- *******.4.1.6296.*******.50.1.2
		dsVdslSystemUpboConfArrayK1 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslSystemUpboConfEntry 2 }

		
		-- *******.4.1.6296.*******.50.1.3
		dsVdslSystemUpboConfArrayK2 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslSystemUpboConfEntry 3 }

		
		-- *******.4.1.6296.*******
		dsVdslStatus OBJECT IDENTIFIER ::= { dasanDslMIBObjects2 2 }

		
		-- *******.4.1.6296.*******.1
		dsVdslStatusBitAlloc OBJECT IDENTIFIER ::= { dsVdslStatus 1 }

		
		-- *******.4.1.6296.*******.1.1
		dsVdslStatusBAIfindex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBitAlloc 1 }

		
		-- *******.4.1.6296.*******.1.2
		dsVdslStatusBAPhySide OBJECT-TYPE
			SYNTAX INTEGER
				{
				rx(1),
				tx(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBitAlloc 2 }

		
		-- *******.4.1.6296.*******.1.3
		dsVdslStatusBASectionSize OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBitAlloc 3 }

		
		-- *******.4.1.6296.*******.1.4
		dsVdslStatusBAAction OBJECT-TYPE
			SYNTAX INTEGER { get(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBitAlloc 4 }

		
		-- *******.4.1.6296.*******.1.5
		dsVdslStatusBAStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				idle(1),
				busy(2),
				passed(3),
				failed(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBitAlloc 5 }

		
		-- *******.4.1.6296.*******.1.6
		dsVdslStatusBALastTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBitAlloc 6 }

		
		-- *******.4.1.6296.*******.1.7
		dsVdslStatusBATable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslStatusBAEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBitAlloc 7 }

		
		-- *******.4.1.6296.*******.1.7.1
		dsVdslStatusBAEntry OBJECT-TYPE
			SYNTAX DsVdslStatusBAEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			INDEX { dsVdslStatusBASectionIndex }
			::= { dsVdslStatusBATable 1 }

		
		DsVdslStatusBAEntry ::=
			SEQUENCE { 
				dsVdslStatusBASectionIndex
					INTEGER,
				dsVdslStatusBABitLoading
					Integer32
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslStatusBASectionIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBAEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslStatusBABitLoading OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslStatusBAEntry 2 }

		
		-- *******.4.1.6296.*******
		dsVdslLine OBJECT IDENTIFIER ::= { dasanDslMIBObjects2 3 }

		
		-- *******.4.1.6296.*******.1
		dsVdslLinePsdShaping OBJECT IDENTIFIER ::= { dsVdslLine 1 }

		
		-- *******.4.1.6296.*******.1.1
		dsVdslLinePsdShapingTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslLinePsdShapingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShaping 1 }

		
		-- *******.4.1.6296.*******.1.1.1
		dsVdslLinePsdShapingEntry OBJECT-TYPE
			SYNTAX DsVdslLinePsdShapingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex }
			::= { dsVdslLinePsdShapingTable 1 }

		
		DsVdslLinePsdShapingEntry ::=
			SEQUENCE { 
				dsVdslLinePsdShapingUsFlag
					INTEGER,
				dsVdslLinePsdShapingUsEwlName
					INTEGER,
				dsVdslLinePsdShapingUsEwlLength
					Integer32,
				dsVdslLinePsdShapingUsRetryCount
					Integer32,
				dsVdslLinePsdShapingUsAutoStatus
					INTEGER,
				dsVdslLinePsdShapingUsAutoDr
					INTEGER,
				dsVdslLinePsdShapingUsCurrentTryCount
					Integer32,
				dsVdslLinePsdShapingUsEwlOnAutoDr
					Integer32,
				dsVdslLinePsdShapingUsClearAutoDr
					INTEGER,
				dsVdslLinePsdShapingUsMethodType
					INTEGER,
				dsVdslLinePsdShapingUsStepCount
					Integer32,
				dsVdslLinePsdShapingDsFlag
					INTEGER,
				dsVdslLinePsdShapingDsEwlName
					INTEGER,
				dsVdslLinePsdShapingDsEwlLength
					Integer32,
				dsVdslLinePsdShapingDsFreqMin
					Integer32,
				dsVdslLinePsdShapingDsFreqMax
					Integer32
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enableAuto(1),
				enableManual(2),
				enableStandard(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsEwlName OBJECT-TYPE
			SYNTAX INTEGER
				{
				us0(0),
				us50(1),
				us100(2),
				us150(3),
				us200(4),
				us250(5),
				us300(6),
				us350(7),
				us400(8),
				us450(9),
				us500(10),
				us550(11),
				us600(12),
				us650(13),
				us700(14),
				us750(15),
				us800(16),
				us850(17),
				us900(18),
				us950(19),
				usFull(20)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsEwlLength OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsRetryCount OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsAutoStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				userClear(1),
				linkOn(2),
				training(3),
				adminDown(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsAutoDr OBJECT-TYPE
			SYNTAX INTEGER
				{
				us0(0),
				us50(1),
				us100(2),
				us150(3),
				us200(4),
				us250(5),
				us300(6),
				us350(7),
				us400(8),
				us450(9),
				us500(10),
				us550(11),
				us600(12),
				us650(13),
				us700(14),
				us750(15),
				us800(16),
				us850(17),
				us900(18),
				us950(19),
				usFull(20)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsCurrentTryCount OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 7 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLinePsdShapingUsEwlOnAutoDr OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 8 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslLinePsdShapingUsClearAutoDr OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 11 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslLinePsdShapingUsMethodType OBJECT-TYPE
			SYNTAX INTEGER
				{
				methodNormal(0),
				method50mTo950m(1),
				method50mToFull(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 13 }

		
		-- *******.4.1.6296.*******.*******4
		dsVdslLinePsdShapingUsStepCount OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 14 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslLinePsdShapingDsFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 21 }

		
		-- *******.4.1.6296.*******.*******2
		dsVdslLinePsdShapingDsEwlName OBJECT-TYPE
			SYNTAX INTEGER
				{
				ds0(0),
				ds250(1),
				ds500(2),
				ds750(3),
				ds1000(4),
				ds1250(5),
				ds1500(6),
				ds1750(7),
				ds2000(8),
				ds2250(9),
				ds2500(10),
				ds2750(11),
				ds3000(12),
				ds3250(13),
				ds3500(14),
				ds3750(15),
				ds4000(16),
				ds4250(17),
				ds4500(18),
				ds4750(19)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 22 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslLinePsdShapingDsEwlLength OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 23 }

		
		-- *******.4.1.6296.*******.*******4
		dsVdslLinePsdShapingDsFreqMin OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 24 }

		
		-- *******.4.1.6296.*******.*******5
		dsVdslLinePsdShapingDsFreqMax OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLinePsdShapingEntry 25 }

		
		-- *******.4.1.6296.*******.2
		dsVdslLineConfig OBJECT IDENTIFIER ::= { dsVdslLine 2 }

		
		-- *******.4.1.6296.*******.2.1
		dsVdslLineConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslLineConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfig 1 }

		
		-- *******.4.1.6296.*******.2.1.1
		dsVdslLineConfigEntry OBJECT-TYPE
			SYNTAX DsVdslLineConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex }
			::= { dsVdslLineConfigTable 1 }

		
		DsVdslLineConfigEntry ::=
			SEQUENCE { 
				dsVdslLineConfMicroCut
					INTEGER,
				dsVdslLineConfigMicroCutThreshold
					Integer32,
				dsVdslLineConfigMicroCutStatTotal
					Integer32,
				dsVdslLineConfigMicroCutStatCurrent
					Integer32,
				dsVdslLineConfigMicroCutStatLinkdown
					Integer32,
				dsVdslLineConfigMicroCutStatCleared
					INTEGER,
				dsVdslLineConfigTrustSnr
					INTEGER,
				dsVdslLineConfigTrustSnrThreshUpMargin
					Integer32,
				dsVdslLineConfigTrustSnrThreshUpTime
					Integer32,
				dsVdslLineConfigTrustSnrThreshDownMargin
					Integer32,
				dsVdslLineConfigTrustSnrThreshDownTime
					Integer32,
				dsVdslLineConfigTrustSnrStatUpRunning
					Integer32,
				dsVdslLineConfigTrustSnrStatUpTime
					Integer32,
				dsVdslLineConfigTrustSnrStatDownRunning
					Integer32,
				dsVdslLineConfigTrustSnrStatDownTime
					Integer32,
				dsVdslLineConfigTrustSnrStatLinkdown
					Integer32,
				dsVdslLineConfigTrustSnrStatCleared
					INTEGER
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslLineConfMicroCut OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigMicroCutThreshold OBJECT-TYPE
			SYNTAX Integer32 (1..10000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"-1 means default threshold value."
			::= { dsVdslLineConfigEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigMicroCutStatTotal OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigMicroCutStatCurrent OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigMicroCutStatLinkdown OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigMicroCutStatCleared OBJECT-TYPE
			SYNTAX INTEGER
				{
				clearAll(1),
				clearTotal(2),
				clearCurrent(3),
				clearLinkdown(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigTrustSnr OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(0),
				disable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 7 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigTrustSnrThreshUpMargin OBJECT-TYPE
			SYNTAX Integer32 (0..31)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"-1 means default value"
			::= { dsVdslLineConfigEntry 8 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineConfigTrustSnrThreshUpTime OBJECT-TYPE
			SYNTAX Integer32 (1..10000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"-1 means default value"
			::= { dsVdslLineConfigEntry 9 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslLineConfigTrustSnrThreshDownMargin OBJECT-TYPE
			SYNTAX Integer32 (0..31)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"-1 means default value"
			::= { dsVdslLineConfigEntry 10 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslLineConfigTrustSnrThreshDownTime OBJECT-TYPE
			SYNTAX Integer32 (1..10000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"-1 means default value"
			::= { dsVdslLineConfigEntry 11 }

		
		-- *******.4.1.6296.*******.*******2
		dsVdslLineConfigTrustSnrStatUpRunning OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 12 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslLineConfigTrustSnrStatUpTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 13 }

		
		-- *******.4.1.6296.*******.*******4
		dsVdslLineConfigTrustSnrStatDownRunning OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 14 }

		
		-- *******.4.1.6296.*******.*******5
		dsVdslLineConfigTrustSnrStatDownTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 15 }

		
		-- *******.4.1.6296.*******.*******6
		dsVdslLineConfigTrustSnrStatLinkdown OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 16 }

		
		-- *******.4.1.6296.*******.*******7
		dsVdslLineConfigTrustSnrStatCleared OBJECT-TYPE
			SYNTAX INTEGER
				{
				clearAll(1),
				clearUpRunning(2),
				clearUpTime(3),
				clearDownRunning(4),
				clearDownTime(5),
				clearLinkdown(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineConfigEntry 17 }

		
		-- *******.4.1.6296.*******.11
		dsVdslLineStatus OBJECT IDENTIFIER ::= { dsVdslLine 11 }

		
		-- *******.4.1.6296.*******.11.1
		dsVdslLineStatusTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslLineStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineStatus 1 }

		
		-- *******.4.1.6296.*******.11.1.1
		dsVdslLineStatusEntry OBJECT-TYPE
			SYNTAX DsVdslLineStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex }
			::= { dsVdslLineStatusTable 1 }

		
		DsVdslLineStatusEntry ::=
			SEQUENCE { 
				dsVdslLineStatusCpeLinkdownReason
					INTEGER
			 }

		-- *******.4.1.6296.*******.********
		dsVdslLineStatusCpeLinkdownReason OBJECT-TYPE
			SYNTAX INTEGER
				{
				notSupport(-1),
				lossOfPower(0),
				portStop(1),
				lossOfSignalPM(2),
				lossOfSignalNonPM(3),
				lossOfFramePM(4),
				lossOfFrameNonPM(5),
				excessiveSevereError(6),
				iniCRC(7),
				cRC(8),
				feLPR(9),
				feLOS(10),
				feLOF(11),
				feESE(12),
				feIniCRC(13),
				feCRC(14),
				unknownFailure(15)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslLineStatusEntry 1 }

		
		-- *******.4.1.6296.*******
		dsVdslPM OBJECT IDENTIFIER ::= { dasanDslMIBObjects2 5 }

		
		-- *******.4.1.6296.*******.1
		dsVdslPMMediaAdaptor OBJECT IDENTIFIER ::= { dsVdslPM 1 }

		
		-- *******.4.1.6296.*******.1.1
		dsVdslPMMATable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslPMMAEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMediaAdaptor 1 }

		
		-- *******.4.1.6296.*******.1.1.1
		dsVdslPMMAEntry OBJECT-TYPE
			SYNTAX DsVdslPMMAEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			INDEX { ifIndex }
			::= { dsVdslPMMATable 1 }

		
		DsVdslPMMAEntry ::=
			SEQUENCE { 
				dsVdslPMMARxFrameCount
					Counter64,
				dsVdslPMMARxCRCErr
					Counter64,
				dsVdslPMMARxDrop
					Counter64,
				dsVdslPMMATxFrameCount
					Counter64,
				dsVdslPMMATxDrop
					Counter64,
				dsVdslPMMAEnetCrcErrCnt
					Counter64,
				dsVdslPMMACleared
					TimeTicks
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslPMMARxFrameCount OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMAEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMMARxCRCErr OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMAEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMMARxDrop OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMAEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMMATxFrameCount OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMAEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMMATxDrop OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMAEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMMAEnetCrcErrCnt OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMAEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMMACleared OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPMMAEntry 7 }

		
		-- *******.4.1.6296.*******.2
		dsVdslPMStatCount OBJECT IDENTIFIER ::= { dsVdslPM 2 }

		
		-- *******.4.1.6296.*******.2.1
		dsVdslPMCoStatTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslPMCoStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMStatCount 1 }

		
		-- *******.4.1.6296.*******.2.1.1
		dsVdslPMCoStatEntry OBJECT-TYPE
			SYNTAX DsVdslPMCoStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex }
			::= { dsVdslPMCoStatTable 1 }

		
		DsVdslPMCoStatEntry ::=
			SEQUENCE { 
				dsVdslPMLos
					Unsigned32,
				dsVdslPMLof
					Unsigned32,
				dsVdslPMLol
					Unsigned32,
				dsVdslPMCorrBlk
					Unsigned32,
				dsVdslPMUnCorrBlk
					Unsigned32,
				dsVdslPMCRC
					Unsigned32,
				dsVdslPMServiceError
					Unsigned32,
				dsVdslPMLoss
					Unsigned32,
				dsVdslPMLofs
					Unsigned32,
				dsVdslPMLols
					Unsigned32,
				dsVdslPMESs
					Unsigned32,
				dsVdslPMSESs
					Unsigned32,
				dsVdslPMUASs
					Unsigned32,
				dsVdslPMCRCs
					Unsigned32,
				dsVdslPM15minElapsedTime
					Gauge32,
				dsVdslPM1dayElapsedTime
					Gauge32,
				dsVdslPMClear
					INTEGER
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslPMLos OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The LOS total statistic value. 
				set to 0 to clear count."
			::= { dsVdslPMCoStatEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMLof OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMLol OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMUnCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMCRC OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMServiceError OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 7 }

		
		-- *******.4.1.6296.*******.*******8
		dsVdslPMLoss OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 18 }

		
		-- *******.4.1.6296.*******.*******9
		dsVdslPMLofs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 19 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslPMLols OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 20 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslPMESs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 21 }

		
		-- *******.4.1.6296.*******.*******2
		dsVdslPMSESs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 22 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslPMUASs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 23 }

		
		-- *******.4.1.6296.*******.*******4
		dsVdslPMCRCs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 24 }

		
		-- *******.4.1.6296.*******.*******8
		dsVdslPM15minElapsedTime OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 28 }

		
		-- *******.4.1.6296.*******.*******9
		dsVdslPM1dayElapsedTime OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStatEntry 29 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslPMClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"clear all error statistic counter with timer.
				"
			::= { dsVdslPMCoStatEntry 30 }

		
		-- *******.4.1.6296.*******.2.2
		dsVdslPMCoStat15minTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslPMCoStat15minEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMStatCount 2 }

		
		-- *******.4.1.6296.*******.2.2.1
		dsVdslPMCoStat15minEntry OBJECT-TYPE
			SYNTAX DsVdslPMCoStat15minEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex, dsVdslPM15minInterval }
			::= { dsVdslPMCoStat15minTable 1 }

		
		DsVdslPMCoStat15minEntry ::=
			SEQUENCE { 
				dsVdslPM15minInterval
					Integer32,
				dsVdslPM15minLos
					Unsigned32,
				dsVdslPM15minLof
					Unsigned32,
				dsVdslPM15minLol
					Unsigned32,
				dsVdslPM15minCorrBlk
					Unsigned32,
				dsVdslPM15minUnCorrBlk
					Unsigned32,
				dsVdslPM15minCRC
					Unsigned32,
				dsVdslPM15minServiceError
					Unsigned32,
				dsVdslPM15minLoss
					Unsigned32,
				dsVdslPM15minLofs
					Unsigned32,
				dsVdslPM15minLols
					Unsigned32,
				dsVdslPM15minESs
					Unsigned32,
				dsVdslPM15minSESs
					Unsigned32,
				dsVdslPM15minUASs
					Unsigned32,
				dsVdslPM15minCRCs
					Unsigned32
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslPM15minInterval OBJECT-TYPE
			SYNTAX Integer32 (0..1)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Performance Data Interval number 1 is the most recent 
				previous interval; 
				interval 0 is current.
				"
			::= { dsVdslPMCoStat15minEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM15minLos OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM15minLof OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM15minLol OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM15minCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM15minUnCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM15minCRC OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 7 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM15minServiceError OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 8 }

		
		-- *******.4.1.6296.*******.*******9
		dsVdslPM15minLoss OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 19 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslPM15minLofs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 20 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslPM15minLols OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 21 }

		
		-- *******.4.1.6296.*******.*******2
		dsVdslPM15minESs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 22 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslPM15minSESs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 23 }

		
		-- *******.4.1.6296.*******.*******4
		dsVdslPM15minUASs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 24 }

		
		-- *******.4.1.6296.*******.*******5
		dsVdslPM15minCRCs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat15minEntry 25 }

		
		-- *******.4.1.6296.*******.2.3
		dsVdslPMCoStat1dayTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslPMCoStat1dayEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMStatCount 3 }

		
		-- *******.4.1.6296.*******.2.3.1
		dsVdslPMCoStat1dayEntry OBJECT-TYPE
			SYNTAX DsVdslPMCoStat1dayEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex, dsVdslPM1dayInterval }
			::= { dsVdslPMCoStat1dayTable 1 }

		
		DsVdslPMCoStat1dayEntry ::=
			SEQUENCE { 
				dsVdslPM1dayInterval
					Integer32,
				dsVdslPM1dayLos
					Unsigned32,
				dsVdslPM1dayLof
					Unsigned32,
				dsVdslPM1dayLol
					Unsigned32,
				dsVdslPM1dayCorrBlk
					Unsigned32,
				dsVdslPM1dayUnCorrBlk
					Unsigned32,
				dsVdslPM1dayCRC
					Unsigned32,
				dsVdslPMdayServiceError
					Unsigned32,
				dsVdslPM1dayLoss
					Unsigned32,
				dsVdslPM1dayLofs
					Unsigned32,
				dsVdslPM1dayLols
					Unsigned32,
				dsVdslPM1dayESs
					Unsigned32,
				dsVdslPM1daySESs
					Unsigned32,
				dsVdslPM1dayUASs
					Unsigned32,
				dsVdslPM1dayCRCs
					Unsigned32
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslPM1dayInterval OBJECT-TYPE
			SYNTAX Integer32 (0..1)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Performance Data Interval number 1 is the most recent 
				previous interval; 
				interval 0 is current.
				"
			::= { dsVdslPMCoStat1dayEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM1dayLos OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM1dayLof OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM1dayLol OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM1dayCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM1dayUnCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPM1dayCRC OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 7 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslPMdayServiceError OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 8 }

		
		-- *******.4.1.6296.*******.*******9
		dsVdslPM1dayLoss OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 19 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslPM1dayLofs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 20 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslPM1dayLols OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 21 }

		
		-- *******.4.1.6296.*******.*******2
		dsVdslPM1dayESs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 22 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslPM1daySESs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 23 }

		
		-- *******.4.1.6296.*******.*******4
		dsVdslPM1dayUASs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 24 }

		
		-- *******.4.1.6296.*******.*******5
		dsVdslPM1dayCRCs OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCoStat1dayEntry 25 }

		
		-- *******.4.1.6296.*******.2.4
		dsVdslPMCpeStatTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslPMCpeStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMStatCount 4 }

		
		-- *******.4.1.6296.*******.2.4.1
		dsVdslPMCpeStatEntry OBJECT-TYPE
			SYNTAX DsVdslPMCpeStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex }
			::= { dsVdslPMCpeStatTable 1 }

		
		DsVdslPMCpeStatEntry ::=
			SEQUENCE { 
				dsVdslPMCpeLos
					Unsigned32,
				dsVdslPMCpeLof
					Unsigned32,
				dsVdslPMCpeCorrBlk
					Unsigned32,
				dsVdslPMCpeUnCorrBlk
					Unsigned32,
				dsVdslPMCpeCRC
					Unsigned32,
				dsVdslPMCpeClear
					INTEGER
			 }

		-- *******.4.1.6296.*******.2.4.1.1
		dsVdslPMCpeLos OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCpeStatEntry 1 }

		
		-- *******.4.1.6296.*******.2.4.1.2
		dsVdslPMCpeLof OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCpeStatEntry 2 }

		
		-- *******.4.1.6296.*******.2.4.1.3
		dsVdslPMCpeCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCpeStatEntry 3 }

		
		-- *******.4.1.6296.*******.2.4.1.4
		dsVdslPMCpeUnCorrBlk OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCpeStatEntry 4 }

		
		-- *******.4.1.6296.*******.2.4.1.5
		dsVdslPMCpeCRC OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCpeStatEntry 5 }

		
		-- *******.4.1.6296.*******.2.4.1.6
		dsVdslPMCpeClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVdslPMCpeStatEntry 6 }

		
		-- *******.4.1.6296.*******
		dsVdslProfile OBJECT IDENTIFIER ::= { dasanDslMIBObjects2 6 }

		
		-- *******.4.1.6296.*******.1
		dsVdslLineCfgProfile OBJECT IDENTIFIER ::= { dsVdslProfile 1 }

		
		-- *******.4.1.6296.*******.1.1
		dsVdslLineCfgProfileTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslLineCfgProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table contains information on the VDSL line
				configuration.  One entry in this table reflects a
				profile defined by a manager which can be used to
				configure the VDSL line.
				
				Entries in this table MUST be maintained in a 
				persistent manner."
			::= { dsVdslLineCfgProfile 1 }

		
		-- *******.4.1.62********.*******
		dsVdslLineCfgProfileEntry OBJECT-TYPE
			SYNTAX DsVdslLineCfgProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Each entry consists of a list of parameters that
				represents the configuration of a VDSL line.   
				
				A default profile with an index of 'DEFVAL', will 
				always exist and its parameters will be set to vendor 
				specific values, unless otherwise specified in this 
				document."
			INDEX { dsVdslLineCfgProfileName }
			::= { dsVdslLineCfgProfileTable 1 }

		
		DsVdslLineCfgProfileEntry ::=
			SEQUENCE { 
				dsVdslLineCfgProfileName
					DisplayString,
				dsVdslLineCfgDownMaxSnrMgn
					Unsigned32,
				dsVdslLineCfgDownMinSnrMgn
					Unsigned32,
				dsVdslLineCfgDownTargetSnrMgn
					Unsigned32,
				dsVdslLineCfgUpMaxSnrMgn
					Unsigned32,
				dsVdslLineCfgUpMinSnrMgn
					Unsigned32,
				dsVdslLineCfgUpTargetSnrMgn
					Unsigned32,
				dsVdslLineCfgDownMaxDataRate
					Unsigned32,
				dsVdslLineCfgDownMinDataRate
					Unsigned32,
				dsVdslLineCfgUpMaxDataRate
					Unsigned32,
				dsVdslLineCfgUpMinDataRate
					Unsigned32,
				dsVdslLineCfgDownMaxInterDelay
					INTEGER,
				dsVdslLineCfgUpMaxInterDelay
					INTEGER,
				dsVdslLineCfgHamband
					Counter64,
				dsVdslLineCfgDownINP
					INTEGER,
				dsVdslLineCfgUpINP
					INTEGER,
				dsVdslLineCfgPBOLength
					INTEGER,
				dsVdslLineCfgPSDMaskLevel
					INTEGER,
				dsVdslLineCfgTCMAdmin
					INTEGER,
				dsVdslLineCfgUpboEnable
					INTEGER,
				dsVdslLineCfgChannel
					INTEGER,
				dsVdslLineCfgStandard
					INTEGER,
				dsVdslLineCfgLineProfile
					INTEGER,
				dsVdslLineCfgToneDisableMode
					INTEGER,
				dsVdslLineCfgOptionband
					INTEGER,
				dsVdslLineCfgProfileAction
					INTEGER,
				dsVdslLineCfgProfRowStatus
					RowStatus
			 }

		-- *******.4.1.6296.*******.*******
		dsVdslLineCfgProfileName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object identifies a row in this table.  
				
				A default profile with an index of 'DEFVAL', will 
				always exist and its parameters will be set to vendor 
				specific values, unless otherwise specified in this 
				document."
			::= { dsVdslLineCfgProfileEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineCfgDownMaxSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum downstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineCfgDownMinSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum downstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineCfgDownTargetSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the target downstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB.
				This is the Noise Margin the transceivers must achieve 
				with a BER of 10^-7 or better to successfully complete 
				initialization."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsVdslLineCfgUpMaxSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum upstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 7 }

		
		-- *******.4.1.62********.*******.8
		dsVdslLineCfgUpMinSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum upstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 8 }

		
		-- *******.4.1.62********.*******.9
		dsVdslLineCfgUpTargetSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the target upstream Signal/Noise Margin in 
				units of 0.25 dB, for a range of 0 to 31.75 dB.  This 
				is the Noise Margin the transceivers must achieve with 
				a BER of 10^-7 or better to successfully complete 
				initialization."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 9 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslLineCfgDownMaxDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum downstream slow channel
				data rate in steps of 1000 bits/second.
				
				The maximum aggregate downstream transmit speed
				of the line can be derived from the sum of maximum
				downstream fast and slow channel data rates."
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 10 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslLineCfgDownMinDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum downstream slow channel
				data rate in steps of 1000 bits/second.
				
				The minimum aggregate downstream transmit speed
				of the line can be derived from the sum of minimum
				downstream fast and slow channel data rates."
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 11 }

		
		-- *******.4.1.6296.*******.*******2
		dsVdslLineCfgUpMaxDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum upstream slow channel
				data rate in steps of 1000 bits/second."
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 12 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslLineCfgUpMinDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum upstream slow channel
				data rate in steps of 1000 bits/second."
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 13 }

		
		-- *******.4.1.6296.*******.*******6
		dsVdslLineCfgDownMaxInterDelay OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			UNITS "milliseconds"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum interleave delay for the
				downstream slow channel."
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 16 }

		
		-- *******.4.1.6296.*******.*******7
		dsVdslLineCfgUpMaxInterDelay OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			UNITS "milliseconds"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum interleave delay for the
				upstream slow channel."
			DEFVAL { 0 }
			::= { dsVdslLineCfgProfileEntry 17 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslLineCfgHamband OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Amature Radio(HAM) band ID.
				The value of Ham band is combination of the following value.
				If you want to set band1 and band10, you should set value 257(band1 + band10)
				to the Ham band.   
				band1   [  1.800  -  1.810  ] MHz : RFI Notch                (1)
				band2   [  1.800  -  1.825  ] MHz : KOREA HAM-BAND           (2)
				band3   [  1.810  -  1.825  ] MHz : ANNEX F                  (4)
				band4   [  1.810  -  2.000  ] MHz : ETSI, T1E1               (8)
				band5   [  1.9075 -  1.9125 ] MHz : ANNEX F                  (16)
				band6   [  2.173  -  2.191  ] MHz : DT GMDSS                 (2097152)					0x0000 0020 0000
				band7   [  3.500  -  3.550  ] MHz : KOREA HAM-BAND           (32)
				band8   [  3.500  -  3.575  ] MHz : ANNEX F                  (64) 
				band9   [  3.500  -  3.800  ] MHz : ETSI                     (128)
				band10  [  3.500  -  4.000  ] MHz : T1E1                     (256)
				band11  [  3.747  -  3.754  ] MHz : ANNEX F                  (512)
				band12  [  3.790  -  3.800  ] MHz : KOREA HAM-BAND           (1024)             
				band13  [  3.791  -  3.805  ] MHz : ANNEX F                  (2048)
				band14  [  4.200  -  4.215  ] MHz : DT GMDSS                 (4194304)					0x0000 0040 0000
				band15  [  5.900  -  6.200  ] MHz : DT DRM radio (Broadcasting)           (8388608)		0x0000 0080 0000
				band16  [  6.300  -  6.320  ] MHz : DT GMDSS                              (16777216)		0x0000 0100 0000
				band17  [  7.000  -  7.100  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI         (4096)
				band18  [  7.000  -  7.300  ] MHz : T1E1                                  (8192)
				band19  [  7.100  -  7.350  ] MHz : DT DRM radio (Broadcasting)           (33554432)		0x0000 0200 0000
				band20  [  8.405  -  8.420  ] MHz : DT GMDSS                              (67108864)		0x0000 0400 0000
				band21  [  9.400  -  9.900  ] MHz : DT DRM radio (Broadcasting)           (134217728)	0x0000 0800 0000   
				band22  [ 10.100  - 10.150  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI, T1E1   (16384)
				band23  [ 11.600  - 12.100  ] MHz : DT DRM radio (Broadcasting)           (268435456)	0x0000 1000 0000
				band24  [ 12.570  - 12.585  ] MHz : DT GMDSS                              (536870912)	0x0000 2000 0000
				band25  [ 13.570  - 13.870  ] MHz : DT DRM radio (Broadcasting)           (1073741824)	0x0000 4000 0000
				band26  [ 14.000  - 14.350  ] MHz : ANNEX F, ETSI, T1E1                   (32768)
				band27  [ 15.100  - 15.800  ] MHz : DT DRM radio (Broadcasting)           (2147483648)	0x0000 8000 0000
				band28  [ 16.795  - 16.810  ] MHz : DT GMDSS                              (4294967296)	0x0001 0000 0000
				band29  [ 17.480  - 17.900  ] MHz : DT DRM radio (Broadcasting)           (8589934592)	0x0002 0000 0000
				band30  [ 18.068  - 18.168  ] MHz : ANNEX F, ETSI, T1E1                   (65536)
				band31  [ 21.000  - 21.450  ] MHz : ANNEX F, ETSI, T1E1                   (131072)		0x0000 0002 0000
				band32  [ 24.890  - 24.990  ] MHz : ANNEX F, ETST, T1E1                   (262144)		0x0000 0004 0000
				band33  [ 28.000  - 29.100  ] MHz : ETSI                                  (524288)		0x0000 0008 0000
				band34  [ 28.000  - 29.700  ] MHz : ANNEX F, ETSI, T1E1                   (1048576)		0x0000 0010 0000
				"
			::= { dsVdslLineCfgProfileEntry 20 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslLineCfgDownINP OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			UNITS "unit of 125usec"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Vdsl downstream INP ."
			::= { dsVdslLineCfgProfileEntry 21 }

		
		-- *******.4.1.6296.*******.*******2
		dsVdslLineCfgUpINP OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			UNITS "unit of 125usec"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Vdsl upstream INP."
			::= { dsVdslLineCfgProfileEntry 22 }

		
-- 
-- 
		-- *******.4.1.6296.*******.*******3
		dsVdslLineCfgPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9),
				pbo1000m(10)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)
				1000m(10)                       
				"
			::= { dsVdslLineCfgProfileEntry 23 }

		
		-- *******.4.1.6296.*******.*******4
		dsVdslLineCfgPSDMaskLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				old-gains(0),
				aNSI-M1(1),
				aNSI-M2(2),
				eTSI-M1(3),
				eTSI-M2(4),
				aNNEX-F-8-5(5),
				aNSI-M1-EX(6),
				aNSI-M2-EX(7),
				eTSI-M1-EX(8),
				eTSI-M2-EX(9),
				aNNEX-F-11-5(10),
				pSD-K(11)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl PSD Mask Level.
				notavailable(-1)
				old gains(0)
				ANSI M1(1)
				ANSI M2(2) 
				ETSI M1(3)
				ETSI M2(4)
				ANNEX F 8.5(5) 
				ANSI M1 EX(6)
				ANSI M2 EX(7)
				ETSI M1 EX(8)
				ETSI M2 EX(9)
				ANNEX F 11.5(10)                                      
				PSD-K(11)
				"
			::= { dsVdslLineCfgProfileEntry 24 }

		
		-- *******.4.1.6296.*******.*******5
		dsVdslLineCfgTCMAdmin OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(0),
				disable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslLineCfgProfileEntry 25 }

		
		-- *******.4.1.6296.*******.*******6
		dsVdslLineCfgUpboEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslLineCfgProfileEntry 26 }

		
		-- *******.4.1.6296.*******.*******7
		dsVdslLineCfgChannel OBJECT-TYPE
			SYNTAX INTEGER
				{
				fast(0),
				slow(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslLineCfgProfileEntry 27 }

		
		-- *******.4.1.6296.*******.*******8
		dsVdslLineCfgStandard OBJECT-TYPE
			SYNTAX INTEGER
				{
				vdsl(1),
				vdsl2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VDSL Standard."
			::= { dsVdslLineCfgProfileEntry 28 }

		
		-- *******.4.1.6296.*******.*******9
		dsVdslLineCfgLineProfile OBJECT-TYPE
			SYNTAX INTEGER
				{
				sym25(1),
				asym50-3b(2),
				asym50-4b(3),
				asym70(4),
				asym100(5),
				sym100(6),
				sym50(7),
				v8a-998(8),
				v8b-998(9),
				v8c-998(10),
				v8d-998(11),
				v12a-998(12),
				v12b-998(13),
				v17a-998(14),
				v30a-998(15),
				v12a-997(16),
				v12b-997(17),
				v17a-998-8k(18)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"        sym25(1),
				asym50-3b(2), 
				asym50-4b(3), 
				asym70(4), 
				asym100(5), 
				sym100(6),
				8a(8),
				8b(9),
				8c(10),
				8d(11),
				12a(12),
				12b(13),
				17a(14),
				30a(15),
				12a997(16),
				12b997(17),
				v17a_8k(18)  
				"
			::= { dsVdslLineCfgProfileEntry 29 }

		
		-- *******.4.1.6296.*******.*******0
		dsVdslLineCfgToneDisableMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				isdn(2),
				adsl(3),
				adsl2(4),
				t-lan(5),
				adsl-s(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"                
				normal(1),
				isdn(2), 
				adsl(3), 
				adsl2(4), 
				t-lan(5),
				adsl-safe(6)                          
				"
			::= { dsVdslLineCfgProfileEntry 30 }

		
		-- *******.4.1.6296.*******.*******1
		dsVdslLineCfgOptionband OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				annex-B-6-64(1),
				annex-A-6-32(2),
				annex-B-32-64(3),
				exclude(4),
				annex-M(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Optional band ID.
				Annex B 6-64 (1)
				Annex A 6-32 (2)
				Annex B 32-64 (3)
				Exclude Option Band (4)
				Annex M
				"
			::= { dsVdslLineCfgProfileEntry 31 }

		
		-- *******.4.1.6296.*******.*******3
		dsVdslLineCfgProfileAction OBJECT-TYPE
			SYNTAX INTEGER { set(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"real set to profile parameter at the time.
				profile parameter is followings.  
				dsVdslLineCfgStandard,
				dsVdslLineCfgLineProfile,
				dsVdslLineCfgToneDisableMode,
				dsVdslLineCfgOptionband.
				"
			::= { dsVdslLineCfgProfileEntry 33 }

		
		-- *******.4.1.6296.*******.*******00
		dsVdslLineCfgProfRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to create a new row or modify or
				delete an existing row in this table.
				
				A profile activated by setting this object to 'active'.  
				When 'active' is set, the system will validate the profile.
				Before a profile can be deleted or taken out of service, 
				(by setting this object to 'destroy' or 'outOfService') 
				it must be first unreferenced from all associated lines."
			::= { dsVdslLineCfgProfileEntry 100 }

		
		-- *******.4.1.6296.*******
		dsVdslCpeNosAutoUp OBJECT IDENTIFIER ::= { dasanDslMIBObjects2 9 }

		
		-- *******.4.1.6296.*******.1
		dsCpeNosAutoUpInfo OBJECT IDENTIFIER ::= { dsVdslCpeNosAutoUp 1 }

		
		-- *******.4.1.6296.*******.1.1
		dsCpeNosAutoUpBase OBJECT IDENTIFIER ::= { dsCpeNosAutoUpInfo 1 }

		
		-- *******.4.1.6296.*******.1.1.1
		dsCpeNosAutoUpConfStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpBase 1 }

		
		-- *******.4.1.6296.*******.1.2
		dsCpeNosAutoUpControl OBJECT IDENTIFIER ::= { dsCpeNosAutoUpInfo 2 }

		
		-- *******.4.1.6296.*******.1.2.1
		dsCpeNosAutoUpControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setConfStatus(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpControl 1 }

		
		-- *******.4.1.6296.*******.1.2.2
		dsCpeNosAutoUpControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpControl 2 }

		
		-- *******.4.1.6296.*******.1.2.3
		dsCpeNosAutoUpControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpControl 3 }

		
		-- *******.4.1.6296.*******.1.2.4
		dsCpeNosAutoUpControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpControl 4 }

		
		-- *******.4.1.6296.*******.1.2.5
		dsCpeNosAutoUpControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpControl 5 }

		
		-- *******.4.1.6296.*******.1.2.6
		dsCpeNosAutoUpControlConfStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpControl 6 }

		
		-- *******.4.1.6296.*******.2
		dsCpeNosAutoUpCtrl OBJECT IDENTIFIER ::= { dsVdslCpeNosAutoUp 2 }

		
		-- *******.4.1.6296.*******.2.1
		dsCpeNosAutoUpCtrlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsCpeNosAutoUpCtrlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrl 1 }

		
		-- *******.4.1.6296.*******.2.1.1
		dsCpeNosAutoUpCtrlEntry OBJECT-TYPE
			SYNTAX DsCpeNosAutoUpCtrlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { dsCpeNosAutoUpCtrlName }
			::= { dsCpeNosAutoUpCtrlTable 1 }

		
		DsCpeNosAutoUpCtrlEntry ::=
			SEQUENCE { 
				dsCpeNosAutoUpCtrlName
					OCTET STRING,
				dsCpeNosAutoUpCtrlHW
					OCTET STRING,
				dsCpeNosAutoUpCtrlOldFW
					OCTET STRING,
				dsCpeNosAutoUpCtrlNewFW
					OCTET STRING,
				dsCpeNosAutoUpCtrlFWSize
					Unsigned32
			 }

		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpCtrlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpCtrlHW OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpCtrlOldFW OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpCtrlNewFW OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpCtrlFWSize OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlEntry 5 }

		
		-- *******.4.1.6296.*******.2.2
		dsCpeNosAutoUpCtrlControl OBJECT IDENTIFIER ::= { dsCpeNosAutoUpCtrl 2 }

		
		-- *******.4.1.6296.*******.2.2.1
		dsCpeNosAutoUpCtrlControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createControlList(1),
				deleteControlList(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 1 }

		
		-- *******.4.1.6296.*******.2.2.2
		dsCpeNosAutoUpCtrlControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 2 }

		
		-- *******.4.1.6296.*******.2.2.3
		dsCpeNosAutoUpCtrlControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 3 }

		
		-- *******.4.1.6296.*******.2.2.4
		dsCpeNosAutoUpCtrlControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 4 }

		
		-- *******.4.1.6296.*******.2.2.5
		dsCpeNosAutoUpCtrlControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 5 }

		
		-- *******.4.1.6296.*******.2.2.6
		dsCpeNosAutoUpCtrlControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 6 }

		
		-- *******.4.1.6296.*******.2.2.7
		dsCpeNosAutoUpCtrlControlHW OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 7 }

		
		-- *******.4.1.6296.*******.2.2.8
		dsCpeNosAutoUpCtrlControlOldFW OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 8 }

		
		-- *******.4.1.6296.*******.2.2.9
		dsCpeNosAutoUpCtrlControlNewFW OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 9 }

		
		-- *******.4.1.6296.*******.2.2.10
		dsCpeNosAutoUpCtrlControlFWSize OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpCtrlControl 10 }

		
		-- *******.4.1.6296.*******.3
		dsCpeNosAutoUpSched OBJECT IDENTIFIER ::= { dsVdslCpeNosAutoUp 3 }

		
		-- *******.4.1.6296.*******.3.1
		dsCpeNosAutoUpSchedTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsCpeNosAutoUpSchedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSched 1 }

		
		-- *******.4.1.62********.*******
		dsCpeNosAutoUpSchedEntry OBJECT-TYPE
			SYNTAX DsCpeNosAutoUpSchedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { dsCpeNosAutoUpSchedName }
			::= { dsCpeNosAutoUpSchedTable 1 }

		
		DsCpeNosAutoUpSchedEntry ::=
			SEQUENCE { 
				dsCpeNosAutoUpSchedName
					OCTET STRING,
				dsCpeNosAutoUpSchedType
					INTEGER,
				dsCpeNosAutoUpSchedSec
					Integer32,
				dsCpeNosAutoUpSchedInterval
					Integer32,
				dsCpeNosAutoUpSchedYear
					Integer32,
				dsCpeNosAutoUpSchedMonth
					Integer32,
				dsCpeNosAutoUpSchedDay
					Integer32,
				dsCpeNosAutoUpSchedHour
					Integer32,
				dsCpeNosAutoUpSchedMinute
					Integer32,
				dsCpeNosAutoUpSchedPortMap
					OCTET STRING,
				dsCpeNosAutoUpSchedCtrlMap
					OCTET STRING
			 }

		-- *******.4.1.62********.*******.1
		dsCpeNosAutoUpSchedName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 1 }

		
		-- *******.4.1.62********.*******.2
		dsCpeNosAutoUpSchedType OBJECT-TYPE
			SYNTAX INTEGER
				{
				afterNsec(1),
				period(2),
				daily(3),
				at(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 2 }

		
		-- *******.4.1.62********.*******.3
		dsCpeNosAutoUpSchedSec OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 3 }

		
		-- *******.4.1.62********.*******.4
		dsCpeNosAutoUpSchedInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 4 }

		
		-- *******.4.1.62********.*******.5
		dsCpeNosAutoUpSchedYear OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 5 }

		
		-- *******.4.1.62********.*******.6
		dsCpeNosAutoUpSchedMonth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 6 }

		
		-- *******.4.1.62********.*******.7
		dsCpeNosAutoUpSchedDay OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 7 }

		
		-- *******.4.1.62********.*******.8
		dsCpeNosAutoUpSchedHour OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 8 }

		
		-- *******.4.1.62********.*******.9
		dsCpeNosAutoUpSchedMinute OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 9 }

		
		-- *******.4.1.62********.*******.10
		dsCpeNosAutoUpSchedPortMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 10 }

		
		-- *******.4.1.62********.*******.11
		dsCpeNosAutoUpSchedCtrlMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedEntry 11 }

		
		-- *******.4.1.6296.*******.3.2
		dsCpeNosAutoUpSchedControl OBJECT IDENTIFIER ::= { dsCpeNosAutoUpSched 2 }

		
		-- *******.4.1.6296.*******.3.2.1
		dsCpeNosAutoUpSchedControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createScheduleList(1),
				deleteScheduleList(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 1 }

		
		-- *******.4.1.62********.*******
		dsCpeNosAutoUpSchedControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 2 }

		
		-- *******.4.1.62********.*******
		dsCpeNosAutoUpSchedControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 3 }

		
		-- *******.4.1.6296.*******.3.2.4
		dsCpeNosAutoUpSchedControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 4 }

		
		-- *******.4.1.6296.*******.3.2.5
		dsCpeNosAutoUpSchedControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 5 }

		
		-- *******.4.1.6296.*******.3.2.6
		dsCpeNosAutoUpSchedControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 6 }

		
		-- *******.4.1.6296.*******.3.2.7
		dsCpeNosAutoUpSchedControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				afterNsec(1),
				period(2),
				daily(3),
				at(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 7 }

		
		-- *******.4.1.6296.*******.3.2.8
		dsCpeNosAutoUpSchedControlSec OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 8 }

		
		-- *******.4.1.6296.*******.3.2.9
		dsCpeNosAutoUpSchedControlInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 9 }

		
		-- *******.4.1.6296.*******.3.2.10
		dsCpeNosAutoUpSchedControlYear OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 10 }

		
		-- *******.4.1.6296.*******.3.2.11
		dsCpeNosAutoUpSchedControlMonth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 11 }

		
		-- *******.4.1.6296.*******.3.2.12
		dsCpeNosAutoupSchedControlDay OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 12 }

		
		-- *******.4.1.6296.*******.3.2.13
		dsCpeNosAutoUpSchedControlHour OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 13 }

		
		-- *******.4.1.6296.*******.3.2.14
		dsCpeNosAutoUpSchedControlMinute OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 14 }

		
		-- *******.4.1.6296.*******.3.2.15
		dsCpeNosAutoUpSchedControlPortMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 15 }

		
		-- *******.4.1.6296.*******.3.2.16
		dsCpeNosAutoUpSchedControlCtrlMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedControl 16 }

		
		-- *******.4.1.6296.*******.4
		dsCpeNosAutoUpPort OBJECT IDENTIFIER ::= { dsVdslCpeNosAutoUp 4 }

		
		-- *******.4.1.6296.*******.4.1
		dsCpeNosAutoUpPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsCpeNosAutoUpPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPort 1 }

		
		-- *******.4.1.6296.*******.4.1.1
		dsCpeNosAutoUpPortEntry OBJECT-TYPE
			SYNTAX DsCpeNosAutoUpPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex }
			::= { dsCpeNosAutoUpPortTable 1 }

		
		DsCpeNosAutoUpPortEntry ::=
			SEQUENCE { 
				dsCpeNosAutoUpPortPortNum
					Integer32,
				dsCpeNosAutoUpPortRetry
					Integer32,
				dsCpeNosAutoUpPortTimeout
					Integer32,
				dsCpeNosAutoUpPortSchedMap
					OCTET STRING,
				dsCpeNosAutoUpPortCurrState
					Integer32,
				dsCpeNosAutoUpPortCurrSched
					Integer32,
				dsCpeNosAutoUpPortCurrCtrl
					Integer32
			 }

		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpPortPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpPortRetry OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpPortTimeout OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpPortSchedMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortEntry 4 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpPortCurrState OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortEntry 5 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpPortCurrSched OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortEntry 6 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpPortCurrCtrl OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortEntry 7 }

		
		-- *******.4.1.6296.*******.4.2
		dsCpeNosAutoUpPortControl OBJECT IDENTIFIER ::= { dsCpeNosAutoUpPort 2 }

		
		-- *******.4.1.6296.*******.4.2.1
		dsCpeNosAutoUpPortControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setSchedule(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 1 }

		
		-- *******.4.1.6296.*******.4.2.2
		dsCpeNosAutoUpPortControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 2 }

		
		-- *******.4.1.6296.*******.4.2.3
		dsCpeNosAutoUpPortControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 3 }

		
		-- *******.4.1.6296.*******.4.2.4
		dsCpeNosAutoUpPortControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 4 }

		
		-- *******.4.1.6296.*******.4.2.5
		dsCpeNosAutoUpPortControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 5 }

		
		-- *******.4.1.6296.*******.4.2.6
		dsCpeNosAutoUpPortControlPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 6 }

		
		-- *******.4.1.6296.*******.4.2.7
		dsCpeNosAutoUpPortControlRetry OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 7 }

		
		-- *******.4.1.6296.*******.4.2.8
		dsCpeNosAutoUpPortControlTimeout OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpPortControl 8 }

		
		-- *******.4.1.6296.*******.5
		dsCpeNosAutoUpNos OBJECT IDENTIFIER ::= { dsVdslCpeNosAutoUp 5 }

		
		-- *******.4.1.6296.*******.5.1
		dsCpeNosAutoUpNosTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsCpeNosAutoUpNosEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNos 1 }

		
		-- *******.4.1.6296.*******.5.1.1
		dsCpeNosAutoUpNosEntry OBJECT-TYPE
			SYNTAX DsCpeNosAutoUpNosEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { dsCpeNosAutoUpNosName }
			::= { dsCpeNosAutoUpNosTable 1 }

		
		DsCpeNosAutoUpNosEntry ::=
			SEQUENCE { 
				dsCpeNosAutoUpNosName
					OCTET STRING,
				dsCpeNosAutoUpNosSize
					Integer32
			 }

		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpNosName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpNosSize OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosEntry 2 }

		
		-- *******.4.1.6296.*******.5.2
		dsCpeNosAutoUpNosControl OBJECT IDENTIFIER ::= { dsCpeNosAutoUpNos 2 }

		
		-- *******.4.1.6296.*******.5.2.1
		dsCpeNosAutoUpNosControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				ftpGet(1),
				tftpGet(2),
				delete(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 1 }

		
		-- *******.4.1.6296.*******.5.2.2
		dsCpeNosAutoUpNosControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 2 }

		
		-- *******.4.1.6296.*******.5.2.3
		dsCpeNosAutoUpNosControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 3 }

		
		-- *******.4.1.6296.*******.5.2.4
		dsCpeNosAutoUpNosControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 4 }

		
		-- *******.4.1.6296.*******.5.2.5
		dsCpeNosAutoUpNosControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 5 }

		
		-- *******.4.1.6296.*******.5.2.6
		dsCpeNosAutoUpNosControlServerIP OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 6 }

		
		-- *******.4.1.6296.*******.5.2.7
		dsCpeNosAutoUpNosControlUserID OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 7 }

		
		-- *******.4.1.6296.*******.5.2.8
		dsCpeNosAutoUpNosControlPasswd OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 8 }

		
		-- *******.4.1.6296.*******.5.2.9
		dsCpeNosAutoUpNosControlSrcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 9 }

		
		-- *******.4.1.6296.*******.5.2.10
		dsCpeNosAutoUpNosControlDestName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpNosControl 10 }

		
		-- *******.4.1.6296.*******.6
		dsCpeNosAutoUpResultLog OBJECT IDENTIFIER ::= { dsVdslCpeNosAutoUp 6 }

		
		-- *******.4.1.6296.*******.6.1
		dsCpeNosAutoUpRLogTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsCpeNosAutoUpRLogEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpResultLog 1 }

		
		-- *******.4.1.6296.*******.6.1.1
		dsCpeNosAutoUpRLogEntry OBJECT-TYPE
			SYNTAX DsCpeNosAutoUpRLogEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { dsCpeNosAutoUpRLogName }
			::= { dsCpeNosAutoUpRLogTable 1 }

		
		DsCpeNosAutoUpRLogEntry ::=
			SEQUENCE { 
				dsCpeNosAutoUpRLogName
					OCTET STRING,
				dsCpeNosAutoUpRLogValue
					OCTET STRING
			 }

		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpRLogName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpRLogValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogEntry 2 }

		
		-- *******.4.1.6296.*******.6.2
		dsCpeNosAutoUpRLogControl OBJECT IDENTIFIER ::= { dsCpeNosAutoUpResultLog 2 }

		
		-- *******.4.1.6296.*******.6.2.1
		dsCpeNosAutoUpRLogControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				ftp-put(1),
				tftp-put(2),
				delete(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 1 }

		
		-- *******.4.1.6296.*******.6.2.2
		dsCpeNosAutoUpRLogControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 2 }

		
		-- *******.4.1.6296.*******.6.2.3
		dsCpeNosAutoUpRLogControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 3 }

		
		-- *******.4.1.6296.*******.6.2.4
		dsCpeNosAutoUpRLogControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 4 }

		
		-- *******.4.1.6296.*******.6.2.5
		dsCpeNosAutoUpRLogControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 5 }

		
		-- *******.4.1.6296.*******.6.2.6
		dsCpeNosAutoUpRLogControlServerIP OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 6 }

		
		-- *******.4.1.6296.*******.6.2.7
		dsCpeNosAutoUpRLogControlUserID OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 7 }

		
		-- *******.4.1.6296.*******.6.2.8
		dsCpeNosAutoUpRLogControlPasswd OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 8 }

		
		-- *******.4.1.6296.*******.6.2.9
		dsCpeNosAutoUpRLogControlSrcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 9 }

		
		-- *******.4.1.6296.*******.6.2.10
		dsCpeNosAutoUpRLogControlDestName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpRLogControl 10 }

		
		-- *******.4.1.6296.*******.7
		dsCpeNosAutoUpSchedCount OBJECT IDENTIFIER ::= { dsVdslCpeNosAutoUp 7 }

		
		-- *******.4.1.6296.*******.7.1
		dsCpeNosAutoUpSchedCountTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsCpeNosAutoUpSchedCountEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCount 1 }

		
		-- *******.4.1.6296.*******.7.1.1
		dsCpeNosAutoUpSchedCountEntry OBJECT-TYPE
			SYNTAX DsCpeNosAutoUpSchedCountEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { dsCpeNosAutoUpSchedCountPort, dsCpeNosAutoUpSchedCountSched }
			::= { dsCpeNosAutoUpSchedCountTable 1 }

		
		DsCpeNosAutoUpSchedCountEntry ::=
			SEQUENCE { 
				dsCpeNosAutoUpSchedCountPort
					Integer32,
				dsCpeNosAutoUpSchedCountSched
					Integer32,
				dsCpeNosAutoUpSchedCountFail
					Integer32,
				dsCpeNosAutoUpSchedCountTotal
					Integer32
			 }

		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpSchedCountPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountEntry 1 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpSchedCountSched OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountEntry 2 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpSchedCountFail OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountEntry 3 }

		
		-- *******.4.1.6296.*******.*******
		dsCpeNosAutoUpSchedCountTotal OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountEntry 4 }

		
		-- *******.4.1.6296.*******.7.2
		dsCpeNosAutoUpSchedCountControl OBJECT IDENTIFIER ::= { dsCpeNosAutoUpSchedCount 2 }

		
		-- *******.4.1.6296.*******.7.2.1
		dsCpeNosAutoUpSchedCountControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearStat(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountControl 1 }

		
		-- *******.4.1.6296.*******.7.2.2
		dsCpeNosAutoUpSchedCountControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountControl 2 }

		
		-- *******.4.1.6296.*******.7.2.3
		dsCpeNosAutoUpSchedCountControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountControl 3 }

		
		-- *******.4.1.6296.*******.7.2.4
		dsCpeNosAutoUpSchedCountControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountControl 4 }

		
		-- *******.4.1.6296.*******.7.2.5
		dsCpeNosAutoUpSchedCountControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountControl 5 }

		
		-- *******.4.1.6296.*******.7.2.6
		dsCpeNosAutoUpSchedCountControlPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsCpeNosAutoUpSchedCountControl 6 }

		
--  *******.4.1.62********.8
		-- *******.4.1.6296.*******0
		dsVdslObjectGroup OBJECT-GROUP
			OBJECTS { dsVdslSystemFWVersion, dsVdslStatusBAIfindex, dsVdslStatusBAPhySide, dsVdslStatusBASectionSize, dsVdslStatusBAAction, 
				dsVdslStatusBAStatus, dsVdslStatusBALastTime, dsVdslStatusBABitLoading, dsVdslPMMARxFrameCount, dsVdslPMMARxCRCErr, 
				dsVdslPMMARxDrop, dsVdslPMMATxFrameCount, dsVdslPMMATxDrop, dsVdslPMMAEnetCrcErrCnt, dsVdslPMMACleared, 
				dsVdslPMLos, dsVdslPMLof, dsVdslPMLol, dsVdslPMCorrBlk, dsVdslPMUnCorrBlk, 
				dsVdslPMCRC, dsVdslPMServiceError, dsVdslPMLoss, dsVdslPMLofs, dsVdslPMLols, 
				dsVdslPMESs, dsVdslPMSESs, dsVdslPMUASs, dsVdslPMCRCs, dsVdslPM15minElapsedTime, 
				dsVdslPM1dayElapsedTime, dsVdslPMClear, dsVdslPM15minLos, dsVdslPM15minLof, dsVdslPM15minLol, 
				dsVdslPM15minCorrBlk, dsVdslPM15minUnCorrBlk, dsVdslPM15minCRC, dsVdslPM15minServiceError, dsVdslPM15minLoss, 
				dsVdslPM15minLofs, dsVdslPM15minLols, dsVdslPM15minESs, dsVdslPM15minSESs, dsVdslPM15minUASs, 
				dsVdslPM15minCRCs, dsVdslPM1dayLos, dsVdslPM1dayLof, dsVdslPM1dayLol, dsVdslPM1dayCorrBlk, 
				dsVdslPM1dayUnCorrBlk, dsVdslPM1dayCRC, dsVdslPMdayServiceError, dsVdslPM1dayLoss, dsVdslPM1dayLofs, 
				dsVdslPM1dayLols, dsVdslPM1dayESs, dsVdslPM1daySESs, dsVdslPM1dayUASs, dsVdslPM1dayCRCs, 
				dsVdslPMCpeLos, dsVdslPMCpeLof, dsVdslPMCpeCorrBlk, dsVdslPMCpeUnCorrBlk, dsVdslPMCpeCRC, 
				dsVdslLineCfgDownMaxSnrMgn, dsVdslLineCfgDownMinSnrMgn, dsVdslLineCfgDownTargetSnrMgn, dsVdslLineCfgUpMaxSnrMgn, dsVdslLineCfgUpTargetSnrMgn, 
				dsVdslLineCfgDownMaxDataRate, dsVdslLineCfgDownMinDataRate, dsVdslLineCfgUpMaxDataRate, dsVdslLineCfgUpMinDataRate, dsVdslLineCfgDownMaxInterDelay, 
				dsVdslLineCfgUpMaxInterDelay, dsVdslLineCfgHamband, dsVdslLineCfgDownINP, dsVdslLineCfgUpINP, dsVdslLineCfgPBOLength, 
				dsVdslLineCfgPSDMaskLevel, dsVdslLineCfgTCMAdmin, dsVdslLineCfgUpboEnable, dsVdslLineCfgChannel, dsVdslLineCfgStandard, 
				dsVdslLineCfgLineProfile, dsVdslLineCfgToneDisableMode, dsVdslLineCfgOptionband, dsVdslLineCfgProfRowStatus, dsVdslPMCpeClear, 
				dsVdslSystemUpboConfLength, dsVdslSystemUpboConfArrayK1, dsVdslSystemUpboConfArrayK2, dsVdslLinePsdShapingUsFlag, dsVdslLinePsdShapingUsEwlName, 
				dsVdslLinePsdShapingUsEwlLength, dsVdslLinePsdShapingUsRetryCount, dsVdslLinePsdShapingUsAutoStatus, dsVdslLinePsdShapingUsAutoDr, dsVdslLinePsdShapingUsCurrentTryCount, 
				dsVdslLinePsdShapingUsEwlOnAutoDr, dsVdslLinePsdShapingUsClearAutoDr, dsVdslLinePsdShapingDsFlag, dsVdslLinePsdShapingDsEwlName, dsVdslLinePsdShapingDsEwlLength, 
				dsVdslLinePsdShapingDsFreqMin, dsVdslLinePsdShapingDsFreqMax, dsVdslLineConfMicroCut, dsVdslLineConfigMicroCutThreshold, dsVdslLineConfigMicroCutStatTotal, 
				dsVdslLineConfigMicroCutStatCurrent, dsVdslLineConfigMicroCutStatLinkdown, dsVdslLineConfigMicroCutStatCleared, dsVdslLineConfigTrustSnr, dsVdslLineConfigTrustSnrStatLinkdown, 
				dsVdslLineConfigTrustSnrStatCleared, dsCpeNosAutoUpConfStatus, dsCpeNosAutoUpControlRequest, dsCpeNosAutoUpControlStatus, dsCpeNosAutoUpControlTimer, 
				dsCpeNosAutoUpControlTimeStamp, dsCpeNosAutoUpControlReqResult, dsCpeNosAutoUpControlConfStatus, dsCpeNosAutoUpCtrlName, dsCpeNosAutoUpCtrlHW, 
				dsCpeNosAutoUpCtrlOldFW, dsCpeNosAutoUpCtrlNewFW, dsCpeNosAutoUpCtrlFWSize, dsCpeNosAutoUpCtrlControlRequest, dsCpeNosAutoUpCtrlControlStatus, 
				dsCpeNosAutoUpCtrlControlTimer, dsCpeNosAutoUpCtrlControlTimeStamp, dsCpeNosAutoUpCtrlControlReqResult, dsCpeNosAutoUpCtrlControlName, dsCpeNosAutoUpCtrlControlHW, 
				dsCpeNosAutoUpCtrlControlOldFW, dsCpeNosAutoUpCtrlControlNewFW, dsCpeNosAutoUpCtrlControlFWSize, dsCpeNosAutoUpSchedName, dsCpeNosAutoUpSchedType, 
				dsCpeNosAutoUpSchedSec, dsCpeNosAutoUpSchedInterval, dsCpeNosAutoUpSchedYear, dsCpeNosAutoUpSchedMonth, dsCpeNosAutoUpSchedDay, 
				dsCpeNosAutoUpSchedHour, dsCpeNosAutoUpSchedMinute, dsCpeNosAutoUpSchedPortMap, dsCpeNosAutoUpSchedCtrlMap, dsCpeNosAutoUpSchedControlRequest, 
				dsCpeNosAutoUpSchedControlStatus, dsCpeNosAutoUpSchedControlTimer, dsCpeNosAutoUpSchedControlTimeStamp, dsCpeNosAutoUpSchedControlReqResult, dsCpeNosAutoUpSchedControlName, 
				dsCpeNosAutoUpSchedControlType, dsCpeNosAutoUpSchedControlSec, dsCpeNosAutoUpSchedControlInterval, dsCpeNosAutoUpSchedControlYear, dsCpeNosAutoUpSchedControlMonth, 
				dsCpeNosAutoupSchedControlDay, dsCpeNosAutoUpSchedControlHour, dsCpeNosAutoUpSchedControlMinute, dsCpeNosAutoUpSchedControlPortMap, dsCpeNosAutoUpSchedControlCtrlMap, 
				dsCpeNosAutoUpPortPortNum, dsCpeNosAutoUpPortRetry, dsCpeNosAutoUpPortTimeout, dsCpeNosAutoUpPortSchedMap, dsCpeNosAutoUpPortCurrState, 
				dsCpeNosAutoUpPortCurrSched, dsCpeNosAutoUpPortCurrCtrl, dsCpeNosAutoUpPortControlRequest, dsCpeNosAutoUpPortControlStatus, dsCpeNosAutoUpPortControlTimer, 
				dsCpeNosAutoUpPortControlTimeStamp, dsCpeNosAutoUpPortControlReqResult, dsCpeNosAutoUpPortControlPortNum, dsCpeNosAutoUpPortControlRetry, dsCpeNosAutoUpPortControlTimeout, 
				dsCpeNosAutoUpNosName, dsCpeNosAutoUpNosSize, dsCpeNosAutoUpNosControlRequest, dsCpeNosAutoUpNosControlStatus, dsCpeNosAutoUpNosControlTimer, 
				dsCpeNosAutoUpNosControlTimeStamp, dsCpeNosAutoUpNosControlReqResult, dsCpeNosAutoUpNosControlServerIP, dsCpeNosAutoUpNosControlUserID, dsCpeNosAutoUpNosControlPasswd, 
				dsCpeNosAutoUpNosControlSrcName, dsCpeNosAutoUpNosControlDestName, dsCpeNosAutoUpRLogName, dsCpeNosAutoUpRLogValue, dsCpeNosAutoUpRLogControlRequest, 
				dsCpeNosAutoUpRLogControlStatus, dsCpeNosAutoUpRLogControlTimer, dsCpeNosAutoUpRLogControlTimeStamp, dsCpeNosAutoUpRLogControlReqResult, dsCpeNosAutoUpRLogControlServerIP, 
				dsCpeNosAutoUpRLogControlUserID, dsCpeNosAutoUpRLogControlPasswd, dsCpeNosAutoUpRLogControlSrcName, dsCpeNosAutoUpRLogControlDestName, dsCpeNosAutoUpSchedCountPort, 
				dsCpeNosAutoUpSchedCountSched, dsCpeNosAutoUpSchedCountFail, dsCpeNosAutoUpSchedCountTotal, dsCpeNosAutoUpSchedCountControlRequest, dsCpeNosAutoUpSchedCountControlStatus, 
				dsCpeNosAutoUpSchedCountControlTimer, dsCpeNosAutoUpSchedCountControlTimeStamp, dsCpeNosAutoUpSchedCountControlReqResult, dsVdslLineConfigTrustSnrThreshCoMargin, dsVdslLineCfgUpMinSnrMgn, 
				dsVdslLineConfigTrustSnrThreshCoTime, dsVdslLineCfgProfileAction, dsVdslLineConfigTrustSnrThreshCpeMargin, dsCpeNosAutoUpSchedCountControlPort, dsVdslSystemLowPowerMode, 
				dsVdslStatusBASectionIndex, dsVdslLinePsdShapingUsMethodType, dsVdslLinePsdShapingUsStepCount, dsVdslLineConfigTrustSnrStatDownTime, dsVdslLineConfigTrustSnrStatDownRunning, 
				dsVdslLineConfigTrustSnrStatUpRunning, dsVdslLineConfigTrustSnrStatUpTime, dsVdslLineConfigTrustSnrThreshUpMargin, dsVdslLineConfigTrustSnrThreshUpTime, dsVdslLineConfigTrustSnrThreshDownTime, 
				dsVdslLineConfigTrustSnrThreshDownMargin }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { dasanDslMIBObjects2 20 }

		
	
	END

--
-- DASAN-DSL-MIB2.my
--
