--
-- DASAN-MCAST-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 3.0 Build 285
-- Thursday, March 08, 2007 at 16:25:57
--

	DASAN-MCAST-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			dsSwitchModules			
				FROM DASAN-SWITCH-MIB			
			ifIndex, InterfaceIndex			
				FROM IF-<PERSON>B			
			InetAddress			
				FROM INET-ADDRESS-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			IpAddress, Integer32, Unsigned32, Counter32, OBJECT-TYPE, 
			MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TruthValue, RowStatus, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
--  November 08, 2005 at 00:00 GMT
		-- *******.4.1.6296.*******.24
		dsMcastMIB MODULE-IDENTITY 
			LAST-UPDATED "200511080000Z"		-- November 08, 2005 at 00:00 GMT
			ORGANIZATION 
				"dasannetworks, Inc."
			CONTACT-INFO 
				"dasannetworks"
			DESCRIPTION 
				"The MIB module for Multicast Feature."
			::= { dsSwitchModules 24 }

		
	
	
--
-- Textual conventions
--
	
		DsIgmpVersion ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"A value that represents the version of IGMP:
				
				version1(1)     : Version 1 of IGMP
				version2(2)     : Version 2 of IGMP
				version3(3)     : Version 3 of IGMP."
			SYNTAX INTEGER
				{
				version1(1),
				version2(2),
				version3(3)
				}

		DsIgmpFilterMode ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"."
			SYNTAX INTEGER
				{
				includeAllow(1),
				includeBlock(2),
				excludeAllow(3),
				excludeBlock(4)
				}

	
--
-- Node definitions
--
	
		-- *******.4.1.6296.*******.24.0
		dsMcastNotification OBJECT IDENTIFIER::= { dsMcastMIB 0 }

		
		-- *******.4.1.6296.*******.24.0.1
		dsIgmpSnoopingChanged NOTIFICATION-TYPE
			OBJECTS { dsIgmpSnoopingEnabled }
			STATUS current
			DESCRIPTION 
				"A dsIgmpSnoopingEnableChanged should be generated when Igmp Snooping is enable or disable"
			::= { dsMcastNotification 1 }

		
		-- *******.4.1.6296.*******.24.0.2
		dsIgmpSnoopingVlanChanged NOTIFICATION-TYPE
			OBJECTS { dsVlanIgmpSnoopingEnabled, dsVlanID }
			STATUS current
			DESCRIPTION 
				"A dsIgmpSnoopingVlanChanged should be generated when Igmp Snooping Vlan is enable or disable"
			::= { dsMcastNotification 2 }

		
		-- *******.4.1.6296.*******.24.1
		dsIgmpSnoopingMIBObject OBJECT IDENTIFIER::= { dsMcastMIB 1 }

		
		-- *******.4.1.6296.*******.24.1.1
		dsSystemInfo OBJECT IDENTIFIER::= { dsIgmpSnoopingMIBObject 1 }

		
		-- *******.4.1.6296.*******.********
		dsIgmpSnoopingEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object indicates whether IGMP Snooping has been 
				enabled for the system."
			::= { dsSystemInfo 1 }

		
		-- *******.4.1.6296.*******.********
		dsFastLeaveEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object indicates whether Fast-Leave mechanism has been
				configured to be enabled in the system. If Fast-Leave is 
				enabled in the switch, IGMP Snooping will prune the port on 
				which an IGMP leave message has been received without waiting 
				for the Group Specific Query to timeout to determine whether 
				there are any more hosts on that port for that group. If 
				the value of dsV3ProcessEnabledOperStatus is 'false',
				this object will not have any effect."
			::= { dsSystemInfo 2 }

		
		-- *******.4.1.6296.*******.********
		dsFastBlockEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The object indicates whether Fast-Block mechanism has been 
				enabled for the system. This object only has effect if the
				value of dsV3ProcessEnabledOperStatus is 'true'."
			::= { dsSystemInfo 3 }

		
		-- *******.4.1.6296.*******.********
		dsReportSuppressionEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"When this object is set to 'true', IGMP Snooping will 
				suppress duplicate IGMP Reports.  When this object is set 
				to 'false', all IGMP Reports are forwarded to all multicast
				routers in the VLAN."
			::= { dsSystemInfo 4 }

		
		-- *******.4.1.6296.*******.********
		dsTopoChangeFloodQueryCount OBJECT-TYPE
			SYNTAX Unsigned32 (1..10)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object specifies the flooding period for multicast
				traffic upon receiving Topology Change Notifications (TCN).  
				IGMP Snooping will flood multicast traffic until
				dsTopoChangeFloodQueryCount number of IGMP General Queries
				are received by IGMP Snooping."
			::= { dsSystemInfo 5 }

		
		-- *******.4.1.6296.*******.********
		dsTopoChangeQuerySolicitEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object specifies whether the device running IGMP
				Snooping will solicit IGMP General Queries from the 
				Querier upon receiving a Topology Change Notification (TCN).
				The root device will always solicit IGMP General Queries 
				on TCN irrespective of the value of 
				dsTopoChangeQuerySolicitEnabled."
			::= { dsSystemInfo 6 }

		
		-- *******.4.1.6296.*******.********
		dsV3SnoopingSupport OBJECT-TYPE
			SYNTAX INTEGER
				{
				basic(1),
				full(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates IGMP Snooping support for 
				IGMPv3 as described below:
				basic(1): Basic support for IGMPv3. IGMPv3 packets are 
				          processed similar to IGMPv2 packets.  In other words
				          Source list information is not used.  Although this
				          does not break multicast traffic to IGMPv3 hosts,
				          it does not provide any other benefits such as Fast
				          Leave for IGMPv3 hosts, Explicit Host Tracking and
				          Source based filtering.
				full(2):  Full support for IGMPv3.  Provides full IGMPv3 
				          Snooping support.  This includes processing of
				          IGMPv3 source list information along with group
				          information.  Provides support for features such
				          as Fast Leave, Explicit Host Tracking and Proxy
				          Reporting and a potential to do Source based 
				          filtering."
			::= { dsSystemInfo 7 }

		
		-- *******.4.1.6296.*******.********
		dsExplicitTrackingEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSystemInfo 8 }

		-- *******.4.1.6296.*******.********
		dsIgmpSnoopingGroupCount OBJECT-TYPE
		       SYNTAX      INTEGER
		       MAX-ACCESS  read-only
		       STATUS      current
		       DESCRIPTION
               		"The count of igmp snooping group entry."
       		::= { dsSystemInfo 9 }

		-- *******.4.1.6296.*******.********0
		dsMrouteJoinedCount OBJECT-TYPE
		       SYNTAX      INTEGER
		       MAX-ACCESS  read-only
		       STATUS      current
		       DESCRIPTION
               		"The count of Mroute Joined channel."
       		::= { dsSystemInfo 10 }

	

		
		-- *******.4.1.6296.*******.24.1.2
		dsStatisticsInfo OBJECT IDENTIFIER::= { dsIgmpSnoopingMIBObject 2 }

		
		-- *******.4.1.6296.*******.********
		dsInterfaceStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsInterfaceStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Table contains statistics information for IGMP snooping. An 
				entry appears in this table for each IGMP Snooping capable 
				interface in the device."
			::= { dsStatisticsInfo 1 }

		
		-- *******.4.1.6296.*******.********.1
		dsInterfaceStatsEntry OBJECT-TYPE
			SYNTAX DsInterfaceStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry contains statistics information for a specific IGMP
				Snooping capable interface. It provides information about
				IGMP messages and reports that have been transmitted and
				received at the interface."
			INDEX { ifIndex }
			::= { dsInterfaceStatsTable 1 }

		
		DsInterfaceStatsEntry ::=
			SEQUENCE { 
				dsTxGeneralQueries
					Counter32,
				dsTxGroupSpecificQueries
					Counter32,
				dsTxReports
					Counter32,
				dsTxLeaves
					Counter32,
				dsRxGeneralQueries
					Counter32,
				dsRxGroupSpecificQueries
					Counter32,
				dsRxReports
					Counter32,
				dsRxLeaves
					Counter32,
				dsRxValidPackets
					Counter32,
				dsRxInvalidPackets
					Counter32,
				dsRxOtherPackets
					Counter32,
				dsRxTopoNotifications
					Counter32,
				dsV3Allows
					Counter32,
				dsV3Blocks
					Counter32,
				dsV3IsIncluded
					Counter32,
				dsV3IsExcluded
					Counter32,
				dsV3ToIncluded
					Counter32,
				dsV3ToExcluded
					Counter32
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsTxGeneralQueries OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP General Queries that have been transmitted through
				an interface."
			::= { dsInterfaceStatsEntry 1 }

		
		-- *******.4.1.6296.*******.********.1.2
		dsTxGroupSpecificQueries OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP Group-Specific Queries that have been transmitted
				through an interface."
			::= { dsInterfaceStatsEntry 2 }

		
		-- *******.4.1.6296.*******.********.1.3
		dsTxReports OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP Membership Reports that have been transmitted
				through an interface."
			::= { dsInterfaceStatsEntry 3 }

		
		-- *******.4.1.6296.*******.********.1.4
		dsTxLeaves OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP Leave messages that have been transmitted through
				an interface."
			::= { dsInterfaceStatsEntry 4 }

		
		-- *******.4.1.6296.*******.********.1.5
		dsRxGeneralQueries OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP General Queries that have been received at an 
				interface."
			::= { dsInterfaceStatsEntry 5 }

		
		-- *******.4.1.6296.*******.********.1.6
		dsRxGroupSpecificQueries OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP Group-Specific Queries that have been received at 
				an interface."
			::= { dsInterfaceStatsEntry 6 }

		
		-- *******.4.1.6296.*******.********.1.7
		dsRxReports OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP Membership Reports that have been received at an 
				interface."
			::= { dsInterfaceStatsEntry 7 }

		
		-- *******.4.1.6296.*******.********.1.8
		dsRxLeaves OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total IGMP Leave messages that have been received at an 
				interface."
			::= { dsInterfaceStatsEntry 8 }

		
		-- *******.4.1.6296.*******.********.1.9
		dsRxValidPackets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total valid IGMP packets have been received at an interface."
			::= { dsInterfaceStatsEntry 9 }

		
		-- *******.4.1.6296.*******.********.1.10
		dsRxInvalidPackets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total packets those are not valid IGMP messages received
				at an interface."
			::= { dsInterfaceStatsEntry 10 }

		
		-- *******.4.1.6296.*******.********.1.11
		dsRxOtherPackets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total non IGMP messages messages that have been received
				at an interface, comprising cgmp join, pim hello, dvmrp 
				and mospf messages."
			::= { dsInterfaceStatsEntry 11 }

		
		-- *******.4.1.6296.*******.********.1.12
		dsRxTopoNotifications OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total Topology Change Notifications that have been received
				at an interface."
			::= { dsInterfaceStatsEntry 12 }

		
		-- *******.4.1.6296.*******.********.1.13
		dsV3Allows OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of Source-List-Change records with the record type 
				ALLOW_NEW_SOURCES that have been sent from hosts connected 
				to an interface. This record type indicates that the Source
				Address fields in this Group Record contain a list of 
				additional sources that the system wishes to hear from, for
				packets sent to the specified multicast address."
			::= { dsInterfaceStatsEntry 13 }

		
		-- *******.4.1.6296.*******.********.1.14
		dsV3Blocks OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of Source-List-Change records with the record type
				BLOCK_OLD_SOURCE that have been sent from hosts connected to
				an interface. This record type indicates that the Source
				Address fields in this Group Record contain a list of the 
				sources that the system no longer wishes to hear from, for 
				packets sent to the specified multicast address."
			::= { dsInterfaceStatsEntry 14 }

		
		-- *******.4.1.6296.*******.********.1.15
		dsV3IsIncluded OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of Current-State records with the state MODE_IS_INCLUDE 
				that have been sent from hosts in response to a Query received
				at an interface. This state indicates that the interface has a 
				filter mode of INCLUDE for the specified multicast address."
			::= { dsInterfaceStatsEntry 15 }

		
		-- *******.4.1.6296.*******.********.1.16
		dsV3IsExcluded OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of Current-State records with the state MODE_IS_EXCLUDE
				that have been sent from hosts in response to a Query received
				at an interfaces. This state indicates that the interface has a
				filter mode of EXCLUDE for the specified multicast address."
			::= { dsInterfaceStatsEntry 16 }

		
		-- *******.4.1.6296.*******.********.1.17
		dsV3ToIncluded OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of Filter-Mode-Change records with the record type 
				CHANGE_TO_INCLUDE_MODE that have been sent through an 
				interface. This type of record indicates that the filter mode
				has been changed to INCLUDE mode for the specified multicast 
				address, and the Source Address fields in this Group Record 
				will contain the new source list for the specified multicast
				address, if it is not empty."
			::= { dsInterfaceStatsEntry 17 }

		
		-- *******.4.1.6296.*******.********.1.18
		dsV3ToExcluded OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of Filter-Mode-Change records with the record type
				CHANGE_TO_EXCLUDE_MODE that have been sent through an 
				interface. This type of record indicates that the filter mode
				has been changed to EXCLUDE mode for the specified multicast
				address, and the Source Address fields in this Group Record
				will contain the new source list for the specified multicast
				address, if it is not empty."
			::= { dsInterfaceStatsEntry 18 }

		
		-- *******.4.1.6296.*******.24.1.3
		dsVlanConfigInfo OBJECT IDENTIFIER::= { dsIgmpSnoopingMIBObject 3 }

		
		-- *******.4.1.6296.*******.********
		dsVlanConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVlanConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Table contains VLAN based configuration information
				for IGMP Snooping."
			::= { dsVlanConfigInfo 1 }

		
		-- *******.4.1.6296.*******.********.1
		dsVlanConfigEntry OBJECT-TYPE
			SYNTAX DsVlanConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry is created for each active VLAN in the device 
				and deleted when the VLAN becomes inactive."
			INDEX { dsVlanID }
			::= { dsVlanConfigTable 1 }

		
		DsVlanConfigEntry ::=
			SEQUENCE { 
				dsVlanID
					INTEGER,
				dsVlanIgmpSnoopingEnabled
					TruthValue,
				dsVlanFastLeaveEnabled
					TruthValue,
				dsVlanFastBlockEnabled
					TruthValue,
				dsVlanSnoopingLearningModePim
					TruthValue,
				dsVlanExplicitTrackingEnabled
					TruthValue
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsVlanID OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"This object indicates the VLAN in which IGMP Snooping
				is configured."
			::= { dsVlanConfigEntry 1 }

		
		-- *******.4.1.6296.*******.********.1.2
		dsVlanIgmpSnoopingEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"When this object is set to 'true' IGMP Snooping
				is enabled on this VLAN else disabled."
			::= { dsVlanConfigEntry 2 }

		
		-- *******.4.1.6296.*******.********.1.3
		dsVlanFastLeaveEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object specifies whether Fast-Leave mechanism 
				(also known as Immediate-Leave) is to be performed by IGMP
				Snooping or not.  When enabled, IGMP Snooping will remove
				the interface from the group mentioned in the IGMP Leave
				message received on that interface without waiting for the
				IGMP Group-Specific Query to timeout to determine whether 
				there are any more hosts on that interface for that group."
			::= { dsVlanConfigEntry 3 }

		
		-- *******.4.1.6296.*******.********.1.4
		dsVlanFastBlockEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVlanConfigEntry 4 }

		
		-- *******.4.1.6296.*******.********.1.5
		dsVlanSnoopingLearningModePim OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsVlanConfigEntry 5 }

		
		-- *******.4.1.6296.*******.********.1.6
		dsVlanExplicitTrackingEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVlanConfigEntry 6 }

		
		-- *******.4.1.6296.*******.********
		dsIgmpQuerierTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsIgmpQuerierEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Table containing information regarding the IGMP Querier in
				the VLAN.  The device can be configured to be the IGMP Querier
				for the VLAN. An IGMP Querier for the VLAN is selected by 
				using a Querier Election process."
			::= { dsVlanConfigInfo 2 }

		
		-- *******.4.1.6296.*******.********.1
		dsIgmpQuerierEntry OBJECT-TYPE
			SYNTAX DsIgmpQuerierEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry is created for each active VLAN in the device and
				contains IGMP Querier configuration information related to
				IGMP Snooping for that VLAN. An entry is deleted when the 
				VLAN becomes inactive."
			INDEX { dsIgmpQuerierVlanIndex }
			::= { dsIgmpQuerierTable 1 }

		
		DsIgmpQuerierEntry ::=
			SEQUENCE { 
				dsIgmpQuerierVlanIndex
					INTEGER,
				dsIgmpQuerierEnabled
					TruthValue,
				dsIgmpQuerierState
					INTEGER,
				dsIgmpQuerierVersion
					DsIgmpVersion,
				dsIgmpQuerierAddress
					InetAddress,
				dsIgmpQuerierPort
					InterfaceIndex
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsIgmpQuerierVlanIndex OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The object indicates the VLAN that the Querier will send
				IGMP queries on."
			::= { dsIgmpQuerierEntry 1 }

		
		-- *******.4.1.6296.*******.********.1.2
		dsIgmpQuerierEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The object indicates whether the device will participate in
				the IGMP Querier election in a VLAN. If the object is set
				to 'true', the device will participate to an election
				process to be a Querier. If the object is set to 'false'
				while the device is acting as the Querier in a VLAN, a new
				election will be activated to choose a different Querier."
			::= { dsIgmpQuerierEntry 2 }

		
		-- *******.4.1.6296.*******.********.1.3
		dsIgmpQuerierState OBJECT-TYPE
			SYNTAX INTEGER
				{
				disabled(1),
				electing(2),
				querier(3),
				nonQuerier(4),
				inactive(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The object indicates the current state of the device as an 
				IGMP Querier in a VLAN.
				
				disabled(1)  : Querier function is disabled for this device
				               in this VLAN.
				electing(2)  : The device is in the election process of the
				               Querier.
				querier(3)   : The device is the current Querier in this 
				               VLAN.
				nonQuerier(4): The device has lost the election process of
				               the Querier.
				inactive(5)  : VLAN is inactive or not an Ethernet VLAN."
			::= { dsIgmpQuerierEntry 3 }

		
		-- *******.4.1.6296.*******.********.1.4
		dsIgmpQuerierVersion OBJECT-TYPE
			SYNTAX DsIgmpVersion
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The object indicates IGMP version of the Querier for the VLAN.
				IGMP version of the Querier is determined by the type of IGMP
				General Query received by the device."
			REFERENCE
				"RFC 3376"
			::= { dsIgmpQuerierEntry 4 }

		
		-- *******.4.1.6296.*******.********.1.5
		dsIgmpQuerierAddress OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The object indicates IP address of the IGMP Querier
				for the VLAN."
			::= { dsIgmpQuerierEntry 5 }

		
		-- *******.4.1.6296.*******.********.1.6
		dsIgmpQuerierPort OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The object indicates the interface on which IGMP Querier is
				detected for the VLAN.  The value of this object is zero
				if the device itself is acting as IGMP Querier for the VLAN."
			::= { dsIgmpQuerierEntry 6 }

		
		-- *******.4.1.6296.*******.24.1.4
		dsIfConfigInfo OBJECT IDENTIFIER::= { dsIgmpSnoopingMIBObject 4 }

		
		-- *******.4.1.6296.*******.********
		dsIfConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsIfConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Table containing configuration information for IGMP 
				Snooping on a layer two interface."
			::= { dsIfConfigInfo 1 }

		
		-- *******.4.1.6296.*******.********.1
		dsIfConfigEntry OBJECT-TYPE
			SYNTAX DsIfConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry is created for each interface layer two 
				interface."
			INDEX { ifIndex }
			::= { dsIfConfigTable 1 }

		
		DsIfConfigEntry ::=
			SEQUENCE { 
				dsIfTopoChangeFloodEnabled
					TruthValue
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsIfTopoChangeFloodEnabled OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"When this object is set to 'true' multicast traffic will be
				flooded on the port for dsTopoChangeFloodQueryCount of Igmp
				General Queries upon receiving a Topology Change 
				Notification (TCN) for the VLAN to which the port belongs."
			::= { dsIfConfigEntry 1 }

		
		-- *******.4.1.6296.*******.********
		dsIfPortConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsIfPortConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			::= { dsIfConfigInfo 2 }

		
		-- *******.4.1.6296.*******.********.1
		dsIfPortConfigEntry OBJECT-TYPE
			SYNTAX DsIfPortConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			INDEX { ifIndex }
			::= { dsIfPortConfigTable 1 }

		
		DsIfPortConfigEntry ::=
			SEQUENCE { 
				dsIfPortExplicitTrackingMaxhost
					Integer32
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsIfPortExplicitTrackingMaxhost OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsIfPortConfigEntry 1 }

		
		-- *******.4.1.6296.*******.24.1.5
		dsMulticastRouterInfo OBJECT IDENTIFIER::= { dsIgmpSnoopingMIBObject 5 }

		
		-- *******.4.1.6296.*******.********
		dsMcastRouterCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsMcastRouterCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Table containing multicast router configuration information
				for IGMP Snooping."
			::= { dsMulticastRouterInfo 1 }

		
		-- *******.4.1.6296.*******.********.1
		dsMcastRouterCfgEntry OBJECT-TYPE
			SYNTAX DsMcastRouterCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry is created for each interface in the VLAN that is
				either learned or configured as multicast router port."
			INDEX { dsMcastRouterPortIndex, dsMcastRouterVlanIndex }
			::= { dsMcastRouterCfgTable 1 }

		
		DsMcastRouterCfgEntry ::=
			SEQUENCE { 
				dsMcastRouterPortIndex
					INTEGER,
				dsMcastRouterVlanIndex
					INTEGER,
				dsMcastRouterType
					INTEGER,
				dsMcastRouterRowStatus
					RowStatus
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsMcastRouterPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsMcastRouterCfgEntry 1 }

		
		-- *******.4.1.6296.*******.********.1.2
		dsMcastRouterVlanIndex OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object indicates the VLAN to which the multicast router
				port belongs."
			::= { dsMcastRouterCfgEntry 2 }

		
		-- *******.4.1.6296.*******.********.1.3
		dsMcastRouterType OBJECT-TYPE
			SYNTAX INTEGER
				{
				static(1),
				dynamic(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates if the multicast router port is
				a configured (static) or learned (dynamic) port."
			::= { dsMcastRouterCfgEntry 3 }

		
		-- *******.4.1.6296.*******.********.1.4
		dsMcastRouterRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is a conceptual row entry that allows to add
				or delete entries to or from the dsMcastRouterCfgTable.
				When creating an entry in this table 'createAndGo' method
				is used and the value of this object is set to 'active'.
				Deactivation of an 'active' entry is not allowed. When
				deleting an entry in this table 'destroy' method is
				used."
			::= { dsMcastRouterCfgEntry 4 }

		
		-- *******.4.1.6296.*******.24.1.6
		dsMulticastGroupInfo OBJECT IDENTIFIER::= { dsIgmpSnoopingMIBObject 6 }

		
		-- *******.4.1.6296.*******.********
		dsMcastGroupTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsMcastGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Table containing multicast group address information for
				IGMP Snooping."
			::= { dsMulticastGroupInfo 1 }

		
		-- *******.4.1.6296.*******.********.1
		dsMcastGroupEntry OBJECT-TYPE
			SYNTAX DsMcastGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry is created by IGMP Snooping for each group learned
				in the VLAN."
			INDEX { dsMcastGroupVlanIndex, dsMcastGroupAddress, dsMcastGroupPortIndex }
			::= { dsMcastGroupTable 1 }

		
		DsMcastGroupEntry ::=
			SEQUENCE { 
				dsMcastGroupVlanIndex
					INTEGER,
				dsMcastGroupAddress
					InetAddress,
				dsMcastGroupPortIndex
					INTEGER,
				dsMcastGroupFilterMode
					INTEGER,
				dsMcastGroupIgmpVersion
					DsIgmpVersion
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsMcastGroupVlanIndex OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object indicates the VLAN in which the group is learned."
			::= { dsMcastGroupEntry 1 }

		
		-- *******.4.1.6296.*******.********.1.2
		dsMcastGroupAddress OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object indicates IP multicast address learned by 
				IGMP Snooping."
			::= { dsMcastGroupEntry 2 }

		
		-- *******.4.1.6296.*******.********.1.3
		dsMcastGroupPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the set of ports on which IGMP
				Membership Reports are received for the group indicating
				interest to receive traffic sent to the group."
			::= { dsMcastGroupEntry 3 }

		
		-- *******.4.1.6296.*******.********.1.4
		dsMcastGroupFilterMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				include(1),
				exclude(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates a the IGMP filter mode for the group.
				
				include(1) : reception of multicast packets sent to the group
				             specified by cisMcastGroupAddress is requested
				             only from the specified IPv4 source addresses
				             listed in the IGMPv3 Membership Reports.
				exclude(2) : reception of multicast packets sent to the group
				             specified by cisMcastGroupAddress is requested
				             from all except from the list of IPv4 source
				             addresses specified in the IGMPv3 Membership
				              Reports.
				
				The filter mode is determined by the type of Group Record
				received in the IGMP Membership Report received by the 
				device.  Groups for which IGMPv1/v2 Membership Reports 
				are received are considered to have a cisMcastGroupFilterMode
				of 'exclude'.
				"
			::= { dsMcastGroupEntry 4 }

		
		-- *******.4.1.6296.*******.********.1.5
		dsMcastGroupIgmpVersion OBJECT-TYPE
			SYNTAX DsIgmpVersion
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the IGMP version for the group.
				This is determined by the type of IGMP Membership Report
				received by the device."
			::= { dsMcastGroupEntry 5 }

		
		-- *******.4.1.6296.*******.********
		dsMcastIgmpGroupTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsMcastIgmpGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Table containing multicast group address information for
				IGMP Snooping."
			::= { dsMulticastGroupInfo 2 }

		
		-- *******.4.1.6296.*******.********.1
		dsMcastIgmpGroupEntry OBJECT-TYPE
			SYNTAX DsMcastIgmpGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry is created by IGMP Snooping for each group learned
				in the VLAN."
			INDEX { dsMcastIgmpGroupVlanIndex, dsMcastIgmpGroupAddress, dsMcastIgmpGroupSource, dsMcastIgmpGroupPortIndex, dsMcastIgmpGroupReporter, 
				dsMcastIgmpGroupVersion, dsMcastIgmpGroupFilterMode }
			::= { dsMcastIgmpGroupTable 1 }

		
		DsMcastIgmpGroupEntry ::=
			SEQUENCE { 
				dsMcastIgmpGroupVlanIndex
					INTEGER,
				dsMcastIgmpGroupAddress
					IpAddress,
				dsMcastIgmpGroupSource
					IpAddress,
				dsMcastIgmpGroupPortIndex
					INTEGER,
				dsMcastIgmpGroupReporter
					IpAddress,
				dsMcastIgmpGroupVersion
					DsIgmpVersion,
				dsMcastIgmpGroupFilterMode
					DsIgmpFilterMode,
				dsMcastIgmpGroupExpire
					Integer32
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsMcastIgmpGroupVlanIndex OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 1 }

		
		-- *******.4.1.6296.*******.********.1.2
		dsMcastIgmpGroupAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 2 }

		
		-- *******.4.1.6296.*******.********.1.3
		dsMcastIgmpGroupSource OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 3 }

		
		-- *******.4.1.6296.*******.********.1.4
		dsMcastIgmpGroupPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 4 }

		
		-- *******.4.1.6296.*******.********.1.5
		dsMcastIgmpGroupReporter OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 5 }

		
		-- *******.4.1.6296.*******.********.1.6
		dsMcastIgmpGroupVersion OBJECT-TYPE
			SYNTAX DsIgmpVersion
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 6 }

		
		-- *******.4.1.6296.*******.********.1.7
		dsMcastIgmpGroupFilterMode OBJECT-TYPE
			SYNTAX DsIgmpFilterMode
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 7 }

		
		-- *******.4.1.6296.*******.********.1.8
		dsMcastIgmpGroupExpire OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsMcastIgmpGroupEntry 8 }

		
		-- *******.4.1.6296.*******.24.2
		dsIgmpMIBObject OBJECT IDENTIFIER::= { dsMcastMIB 2 }

		
		-- *******.4.1.6296.*******.24.2.1
		dsIgmpBaseInfo OBJECT IDENTIFIER::= { dsIgmpMIBObject 1 }

		-- *******.4.1.6296.*******.********
	       dsIgmpGroupCount	OBJECT-TYPE
		       SYNTAX	 INTEGER 
		       MAX-ACCESS  read-only
		       STATUS      current
		       DESCRIPTION
		               "The count of igmp group entry."
		       ::= { dsIgmpBaseInfo 1 }

		-- *******.4.1.6296.*******.24.2.2
		dsIgmpStaticGroupInfo OBJECT IDENTIFIER::= { dsIgmpMIBObject 2 }

		
		-- *******.4.1.6296.*******.********
		dsIgmpStaticGroupTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsIgmpStaticGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			::= { dsIgmpStaticGroupInfo 1 }

		
		-- *******.4.1.6296.*******.********.1
		dsIgmpStaticGroupEntry OBJECT-TYPE
			SYNTAX DsIgmpStaticGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			INDEX { dsIgmpStaticGroupAddress, dsIgmpStaticGroupPort, dsIgmpStaticGroupReporter, dsIgmpStaticGroupVlanID }
			::= { dsIgmpStaticGroupTable 1 }

		
		DsIgmpStaticGroupEntry ::=
			SEQUENCE { 
				dsIgmpStaticGroupAddress
					IpAddress,
				dsIgmpStaticGroupVlanID
					INTEGER,
				dsIgmpStaticGroupPort
					INTEGER,
				dsIgmpStaticGroupReporter
					IpAddress,
				dsMcastIgmpGroupState
					INTEGER,
				dsMcastIgmpGroupStatus
					RowStatus
			 }

		-- *******.4.1.6296.*******.********.1.1
		dsIgmpStaticGroupAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsIgmpStaticGroupEntry 1 }

		
		-- *******.4.1.6296.*******.********.1.2
		dsIgmpStaticGroupVlanID OBJECT-TYPE
			SYNTAX INTEGER (1..4096)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsIgmpStaticGroupEntry 2 }

		
		-- *******.4.1.6296.*******.********.1.3
		dsIgmpStaticGroupPort OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsIgmpStaticGroupEntry 3 }

		
		-- *******.4.1.6296.*******.********.1.4
		dsIgmpStaticGroupReporter OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsIgmpStaticGroupEntry 4 }

		
		-- *******.4.1.6296.*******.********.1.5
		dsMcastIgmpGroupState OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(0),
				joined(1),
				joinPending(2),
				leavePending(3),
				offline(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsIgmpStaticGroupEntry 5 }

		
		-- *******.4.1.6296.*******.********.1.10
		dsMcastIgmpGroupStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"."
			::= { dsIgmpStaticGroupEntry 10 }

		
		-- *******.4.1.6296.*******.24.3
		dsMcastGroup OBJECT-GROUP
			OBJECTS { dsIgmpSnoopingEnabled, dsFastLeaveEnabled, dsFastBlockEnabled, dsReportSuppressionEnabled, dsTopoChangeFloodQueryCount, 
				dsTopoChangeQuerySolicitEnabled, dsV3SnoopingSupport, dsTxGeneralQueries, dsTxGroupSpecificQueries, dsTxReports, 
				dsTxLeaves, dsRxGeneralQueries, dsRxGroupSpecificQueries, dsRxReports, dsRxLeaves, 
				dsRxValidPackets, dsRxInvalidPackets, dsRxOtherPackets, dsRxTopoNotifications, dsV3Allows, 
				dsV3Blocks, dsV3IsIncluded, dsV3IsExcluded, dsV3ToIncluded, dsV3ToExcluded, 
				dsVlanIgmpSnoopingEnabled, dsVlanFastLeaveEnabled, dsIgmpQuerierEnabled, dsIgmpQuerierState, dsIgmpQuerierVersion, 
				dsIgmpQuerierAddress, dsIfTopoChangeFloodEnabled, dsMcastRouterType, dsMcastRouterRowStatus, dsMcastGroupFilterMode, 
				dsVlanSnoopingLearningModePim, dsVlanFastBlockEnabled, dsMcastGroupIgmpVersion, dsMcastGroupPortIndex, dsExplicitTrackingEnabled, 
				dsVlanExplicitTrackingEnabled, dsMcastIgmpGroupSource, dsMcastIgmpGroupPortIndex, dsMcastIgmpGroupReporter, dsMcastIgmpGroupVersion, 
				dsMcastIgmpGroupFilterMode, dsMcastIgmpGroupExpire, dsMcastIgmpGroupVlanIndex, dsMcastIgmpGroupAddress, dsIgmpQuerierPort, 
				dsVlanID, dsIfPortExplicitTrackingMaxhost, dsIgmpStaticGroupAddress, dsIgmpStaticGroupVlanID, dsIgmpStaticGroupPort, 
				dsIgmpStaticGroupReporter, dsMcastIgmpGroupState, dsMcastIgmpGroupStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { dsMcastMIB 3 }

		
		-- *******.4.1.6296.*******.24.4
		dsMcastNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { dsIgmpSnoopingChanged, dsIgmpSnoopingVlanChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { dsMcastMIB 4 }

		
	
	END

--
-- DASAN-MCAST-MIB.my
--
