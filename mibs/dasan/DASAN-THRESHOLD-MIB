--
-- DASAN-THRESHOLD-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, June 04, 2008 at 10:28:24
--

	DASAN-THRESHOLD-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			dsSwitchModules			
				FROM DASAN-SWITCH-MIB			
			ifIndex			
				FROM IF-MIB			
			OBJECT-GROUP			
				FROM SNMPv2-CONF			
			Integer32, Counter64, OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.*******.16
		dsSwitchThreshold MODULE-IDENTITY 
			LAST-UPDATED "200602121527Z"		-- February 12, 2006 at 15:27 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { dsSwitchModules 16 }

		
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.*******.16.1
		dsPortThresholdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsPortThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsSwitchThreshold 1 }

		
		-- *******.4.1.6296.*******.16.1.1
		dsPortThresholdEntry OBJECT-TYPE
			SYNTAX DsPortThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ifIndex, dsPortThresholdIndex }
			::= { dsPortThresholdTable 1 }

		
		DsPortThresholdEntry ::=
			SEQUENCE { 
				dsPortThresholdIndex
					INTEGER,
				dsPortThresholdDuration
					Integer32,
				dsPortThresholdInterval
					Integer32,
				dsPortThresholdValue
					Counter64,
				dsPortThresholdDirection
					INTEGER
			 }

		-- *******.4.1.6296.*******.********
		dsPortThresholdIndex OBJECT-TYPE
			SYNTAX INTEGER (1..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port number"
			::= { dsPortThresholdEntry 1 }

		
		-- *******.4.1.6296.*******.********
		dsPortThresholdDuration OBJECT-TYPE
			SYNTAX Integer32
			UNITS "10minute"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ThresholdDuration describes amonunt of time which is USER 
				want to monitor traffic for the time.
				"
			::= { dsPortThresholdEntry 2 }

		
		-- *******.4.1.6296.*******.********
		dsPortThresholdInterval OBJECT-TYPE
			SYNTAX Integer32
			UNITS "10minute"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ThresholdInverval describes amonunt of time which is USER 
				want to monitor traffic for the time after the duration time.
				"
			::= { dsPortThresholdEntry 3 }

		
		-- *******.4.1.6296.*******.********
		dsPortThresholdValue OBJECT-TYPE
			SYNTAX Counter64
			UNITS "Mbps"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value of traffic threshold.
				"
			::= { dsPortThresholdEntry 4 }

		
		-- *******.4.1.6296.*******.********
		dsPortThresholdDirection OBJECT-TYPE
			SYNTAX INTEGER
				{
				rx(1),
				tx(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Traffic direction(rx/tx)"
			::= { dsPortThresholdEntry 5 }

		
		-- *******.4.1.6296.*******.16.2
		dsPortThresholdGroup OBJECT-GROUP
			OBJECTS { dsPortThresholdDirection, dsPortThresholdInterval, dsPortThresholdDuration, dsPortThresholdIndex, dsPortThresholdValue
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { dsSwitchThreshold 2 }

		
	
	END

--
-- DASAN-THRESHOLD-MIB.my
--
