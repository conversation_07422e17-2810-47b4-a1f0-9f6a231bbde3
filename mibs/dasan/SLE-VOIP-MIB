--
-- sle-voip-mib.mib
-- MIB generated by MG-S<PERSON><PERSON> Visual MIB Builder Version 6.0  Build 88
-- Tuesday, April 03, 2012 at 09:18:50
--

--  sle-voip-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, November 25, 2011 at 17:07:08
-- 
--  sle-voip-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, November 24, 2011 at 23:01:34
-- 
--  sle-voip-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, November 24, 2011 at 16:10:05
-- 
--  sle-voip-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, November 23, 2011 at 20:42:05
-- 
--  sle-voip2-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, November 23, 2011 at 20:39:44
-- 
--  SLE-DEBUG-MIB.my
-- MIB generated by MG-SO<PERSON> Visual MIB Builder Version 3.0 Build 285
-- Monday, January 22, 2007 at 17:04:05
-- 

	SLE-VOIP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, IpAddress, Integer32, Unsigned32, Gauge32, 
			OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.25
		sleVoip MODULE-IDENTITY 
			LAST-UPDATED "201112141814Z"		-- December 14, 2011 at 18:14 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"This MIB contains all needed informations about rmon and
				all supported sle rmon features."
			::= { sleMgmt 25 }

		
	
--
-- Type definitions
--
	
		OwnerString ::= OCTET STRING (SIZE (0..255))

		EnableDisableState ::= INTEGER
			{
			disable(0),
			enable(1)
			}

	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.25.1
		sleVoipBase OBJECT IDENTIFIER ::= { sleVoip 1 }

		
		-- *******.4.1.6296.**********
		sleVoipBaseInfo OBJECT IDENTIFIER ::= { sleVoipBase 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Display host interface name."
			::= { sleVoipBaseInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipProtocol OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				sip(1),
				mgcp(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"
				Display protocol (none|sip|mgcp) is set."
			::= { sleVoipBaseInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipRtcp OBJECT-TYPE
			SYNTAX INTEGER
				{
				voipRtcpDisable(0),
				voipRtcpEnable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipBaseInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipFaxmode OBJECT-TYPE
			SYNTAX INTEGER
				{
				voipFaxmodeT30(0),
				voipFaxmodeT38(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipBaseInfo 4 }

		
		-- *******.4.1.6296.**********
		sleVoipBaseControl OBJECT IDENTIFIER ::= { sleVoipBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setVoipInterface(1),
				setVoipProtocol(2),
				voipRestartStackOnly(3),
				voipRestartAll(4),
				setVoipRtcp(5),
				setVoipFaxmode(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipBaseControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleVoipBaseControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleVoipBaseControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleVoipBaseControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description"
			::= { sleVoipBaseControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipBaseControlInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				Host interface name. Explain each group,up to 32 characters string include Null character."
			::= { sleVoipBaseControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipBaseControlProtocol OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				sip(1),
				mgcp(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				Protocol(none|sip|mgcp).(Optional) to explain each group,up to 32 characters string include Null character."
			::= { sleVoipBaseControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipBaseControlRtcp OBJECT-TYPE
			SYNTAX INTEGER
				{
				voipRtcpDisable(0),
				voipRtcpEnable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipBaseControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleVoipBaseControlFaxmode OBJECT-TYPE
			SYNTAX INTEGER
				{
				voipFaxmodeT30(0),
				voipFaxmodeT38(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipBaseControl 9 }

		
		-- *******.4.1.6296.**********
		sleVoipBaseNotification OBJECT IDENTIFIER ::= { sleVoipBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipBaseInterfaceChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipBaseControlRequest, sleVoipBaseControlTimeStamp, sleVoipBaseControlReqResult, sleVoipBaseControlInterface }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipBaseNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipBaseProtocolChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipBaseControlRequest, sleVoipBaseControlTimeStamp, sleVoipBaseControlReqResult, sleVoipBaseControlProtocol }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipBaseNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipBaseRestartStackOnly NOTIFICATION-TYPE
			OBJECTS { sleVoipBaseControlRequest, sleVoipBaseControlTimeStamp, sleVoipBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"voipRestartStackOnly"
			::= { sleVoipBaseNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipBaseRestartAll NOTIFICATION-TYPE
			OBJECTS { sleVoipBaseControlRequest, sleVoipBaseControlTimeStamp, sleVoipBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"voipRestartAll"
			::= { sleVoipBaseNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipBaseRtcpChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipBaseControlRequest, sleVoipBaseControlTimeStamp, sleVoipBaseControlReqResult, sleVoipBaseControlRtcp }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipBaseNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipBaseFaxmodeChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipBaseControlRequest, sleVoipBaseControlTimeStamp, sleVoipBaseControlReqResult, sleVoipBaseControlFaxmode }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipBaseNotification 6 }

		
		-- *******.4.1.6296.101.25.2
		sleVoipVoice OBJECT IDENTIFIER ::= { sleVoip 2 }

		
		-- *******.4.1.6296.**********
		sleVoipVoiceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipVoiceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipVoice 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipVoiceEntry OBJECT-TYPE
			SYNTAX SleVoipVoiceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipVoiceIndex }
			::= { sleVoipVoiceTable 1 }

		
		SleVoipVoiceEntry ::=
			SEQUENCE { 
				sleVoipVoiceIndex
					Integer32,
				sleVoipVoiceEncoding
					INTEGER,
				sleVoipVoiceUserEncoding
					INTEGER,
				sleVoipVoiceComfortNoise
					INTEGER,
				sleVoipVoiceEchoCancel
					INTEGER,
				sleVoipVoicePacketLoss
					INTEGER,
				sleVoipVoiceVad
					INTEGER,
				sleVoipVoiceDSCP
					Unsigned32,
				sleVoipVoiceJitter
					Unsigned32
			 }

		-- *******.4.1.6296.**********.1.1
		sleVoipVoiceIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"DESCRIPTION:
				The index of the voice. <1-8>"
			::= { sleVoipVoiceEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleVoipVoiceEncoding OBJECT-TYPE
			SYNTAX INTEGER
				{
				a(0),
				m(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Line coding (0:a-law,1:mu-law)."
			::= { sleVoipVoiceEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleVoipVoiceUserEncoding OBJECT-TYPE
			SYNTAX INTEGER
				{
				a(0),
				m(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"a-law telephony companding algorithm,mu-law telephony companding algorithm."
			::= { sleVoipVoiceEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleVoipVoiceComfortNoise OBJECT-TYPE
			SYNTAX INTEGER
				{
				on(0),
				off(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Comfort Noise (0: on;1: off)"
			::= { sleVoipVoiceEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleVoipVoiceEchoCancel OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Echo cancellation,(on|off)."
			::= { sleVoipVoiceEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleVoipVoicePacketLoss OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packet Loss Concealment,(on|off). 
				Index: Channel Index,Range is from 0 to 7.
				Packetloss: Packet Loss Concealment, (1:on,0:off).
				"
			::= { sleVoipVoiceEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleVoipVoiceVad OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Voice Activity Detection (on(0)|off(1))."
			::= { sleVoipVoiceEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleVoipVoiceDSCP OBJECT-TYPE
			SYNTAX Unsigned32 (0..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" DSCP number, Range is from 0 to 63."
			::= { sleVoipVoiceEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleVoipVoiceJitter OBJECT-TYPE
			SYNTAX Unsigned32 (0..500)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Jitter Buffer parameter (0-500ms).Default value is 500ms."
			::= { sleVoipVoiceEntry 9 }

		
		-- *******.4.1.6296.**********
		sleVoipVoiceControl OBJECT IDENTIFIER ::= { sleVoipVoice 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipVoiceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableVoiceVoip(1),
				disableVoiceVoip(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleVoipVoiceControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipVoiceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command."
			::= { sleVoipVoiceControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipVoiceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command.
				Apply your setting."
			::= { sleVoipVoiceControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipVoiceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)."
			::= { sleVoipVoiceControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipVoiceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleVoipVoiceControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipVoiceControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of the voice. <1-8>"
			::= { sleVoipVoiceControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipVoiceControlEncoding OBJECT-TYPE
			SYNTAX INTEGER
				{
				alaw(0),
				mulaw(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Line coding (0:a-law,1:mu-law).(Optional) to explain each group,up to 32 characters string include Null character."
			::= { sleVoipVoiceControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipVoiceControlUserEncoding OBJECT-TYPE
			SYNTAX INTEGER
				{
				alaw(0),
				mulaw(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				(Optional) to explain each group,up to 32 characters string include NULL character. a-law telephony companding algorithm,mu-law telephony companding algorithm."
			::= { sleVoipVoiceControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleVoipVoiceControlComfortNoise OBJECT-TYPE
			SYNTAX INTEGER
				{
				on(0),
				off(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				(Optional) to explain each group,up to 32 characters string include NULL character.( on:turn on this function, off:turn off this function)"
			::= { sleVoipVoiceControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleVoipVoiceControlEchoCancel OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				(Optional) to explain each group,up to 32 characters string include NULL character. Echo cancellation,(on|off)."
			::= { sleVoipVoiceControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleVoipVoiceControlPacketLoss OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				(Optional) to explain each group,up to 32 characters string include NULL character. Packet Loss Concealment,(on|off). 
				Index: Channel Index,Range is from 0 to 7.
				Packetloss: Packet Loss Concealment, (1:on,0:off).
				"
			::= { sleVoipVoiceControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleVoipVoiceControlVad OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				(Optional) to explain each group,up to 32 characters string include NULL character. Voice Activity Detection (on(1)|off(0)).
				"
			::= { sleVoipVoiceControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleVoipVoiceControlDSCP OBJECT-TYPE
			SYNTAX Unsigned32 (0..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				DSCP number, Range is from 0 to 63."
			::= { sleVoipVoiceControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleVoipVoiceControlJitter OBJECT-TYPE
			SYNTAX Unsigned32 (0..500)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				(Optional) to explain each group,up to 32 characters string include NULL character. Jitter Buffer parameter (0-500ms).Default value is 500ms."
			::= { sleVoipVoiceControl 14 }

		
		-- *******.4.1.6296.**********
		sleVoiceNotification OBJECT IDENTIFIER ::= { sleVoipVoice 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipVoiceEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipVoiceControlRequest, sleVoipVoiceControlTimeStamp, sleVoipVoiceControlReqResult, sleVoipVoiceControlIndex, sleVoipVoiceControlVad, 
				sleVoipVoiceControlDSCP, sleVoipVoiceControlJitter, sleVoipVoiceControlPacketLoss, sleVoipVoiceControlEchoCancel, sleVoipVoiceControlComfortNoise, 
				sleVoipVoiceControlUserEncoding, sleVoipVoiceControlEncoding }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoiceNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipVoiceDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipVoiceControlRequest, sleVoipVoiceControlTimeStamp, sleVoipVoiceControlReqResult, sleVoipVoiceControlIndex }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoiceNotification 2 }

		
		-- *******.4.1.6296.101.25.3
		sleVoipSipUser OBJECT IDENTIFIER ::= { sleVoip 3 }

		
		-- *******.4.1.6296.**********
		sleVoipSipUserTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipSipUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipSipUser 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipSipUserEntry OBJECT-TYPE
			SYNTAX SleVoipSipUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipSipUserIndex }
			::= { sleVoipSipUserTable 1 }

		
		SleVoipSipUserEntry ::=
			SEQUENCE { 
				sleVoipSipUserIndex
					Unsigned32,
				sleVoipSipUserAgent
					INTEGER,
				sleVoipSipUserAOR
					OCTET STRING,
				sleVoipSipUserDisplayName
					OCTET STRING,
				sleVoipSipUserUserName
					OCTET STRING,
				sleVoipSipUserPassword
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleVoipSipUserIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of Sip user.(1..8)"
			::= { sleVoipSipUserEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleVoipSipUserAgent OBJECT-TYPE
			SYNTAX INTEGER (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Display the reference to sip which agent.Range is from 1 to 8."
			::= { sleVoipSipUserEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleVoipSipUserAOR OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Display address of record."
			::= { sleVoipSipUserEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleVoipSipUserDisplayName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The username for sip account."
			::= { sleVoipSipUserEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleVoipSipUserUserName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Display user name for sip account."
			::= { sleVoipSipUserEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleVoipSipUserPassword OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The password for sip account."
			::= { sleVoipSipUserEntry 6 }

		
		-- *******.4.1.6296.**********
		sleVoipSipUserControl OBJECT IDENTIFIER ::= { sleVoipSipUser 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipSipUserControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableVoipSipUser(1),
				disableVoipSipUser(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleVoipSipUserControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipSipUserControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command."
			::= { sleVoipSipUserControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipSipUserControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleVoipSipUserControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipSipUserControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Time stamp attribute set at the end of command"
			::= { sleVoipSipUserControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipSipUserControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of last command"
			::= { sleVoipSipUserControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipSipUserControlIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Setting Sip user index <1-8>"
			::= { sleVoipSipUserControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipSipUserControlAgent OBJECT-TYPE
			SYNTAX INTEGER (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				Setting the reference to sip which agent.Range is from 1 to 8. (Optional) to explain each group,up to 32 characters string include NULL character.
				"
			::= { sleVoipSipUserControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipSipUserControlAOR OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				Setting address of record."
			::= { sleVoipSipUserControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleVoipSipUserControlDisplayName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Setting display-name."
			::= { sleVoipSipUserControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleVoipSipUserControlUserName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Setting user name for sip account."
			::= { sleVoipSipUserControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleVoipSipUserControlPassword OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Setting password for sip account."
			::= { sleVoipSipUserControl 11 }

		
		-- *******.4.1.6296.**********
		sleVoipSipUserNotification OBJECT IDENTIFIER ::= { sleVoipSipUser 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipSipUserEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipSipUserControlRequest, sleVoipSipUserControlTimeStamp, sleVoipSipUserControlReqResult, sleVoipSipUserControlIndex, sleVoipSipUserControlAgent, 
				sleVoipSipUserControlAOR, sleVoipSipUserControlDisplayName, sleVoipSipUserControlUserName, sleVoipSipUserControlPassword }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipSipUserNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipSipUserDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipSipUserControlRequest, sleVoipSipUserControlTimeStamp, sleVoipSipUserControlReqResult, sleVoipSipUserControlIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipSipUserNotification 2 }

		
		-- *******.4.1.6296.101.25.4
		sleVoipSipAgent OBJECT IDENTIFIER ::= { sleVoip 4 }

		
		-- *******.4.1.6296.**********
		sleVoipSipAgentTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipSipAgentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipSipAgent 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipSipAgentEntry OBJECT-TYPE
			SYNTAX SleVoipSipAgentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipSipAgentIndex }
			::= { sleVoipSipAgentTable 1 }

		
		SleVoipSipAgentEntry ::=
			SEQUENCE { 
				sleVoipSipAgentIndex
					Integer32,
				sleVoipSipAgentServer
					OCTET STRING,
				sleVoipSipAgentPort
					Integer32,
				sleVoipSipAgentTypeOfService
					Integer32,
				sleVoipSipAgentTransport
					INTEGER,
				sleVoipSipAgentRegistrar
					OCTET STRING,
				sleVoipSipAgentPrimary
					OCTET STRING,
				sleVoipSipAgentSecondary
					OCTET STRING,
				sleVoipSipAgentProfileName
					OCTET STRING,
				sleVoipSipAgentExpireTimer
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1
		sleVoipSipAgentIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of Sip agent. Range from 1 to 8."
			::= { sleVoipSipAgentEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleVoipSipAgentServer OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set Server for SIP service.IP destination prefix <A.B.C.D> or Domain name."
			DEFVAL { "" }
			::= { sleVoipSipAgentEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleVoipSipAgentPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port number (0-65535)."
			::= { sleVoipSipAgentEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleVoipSipAgentTypeOfService OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Type of service, value is <0-63>."
			::= { sleVoipSipAgentEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleVoipSipAgentTransport OBJECT-TYPE
			SYNTAX INTEGER
				{
				tcp(6),
				udp(17)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Transport protocol (TCP(6)|UDP(17))"
			::= { sleVoipSipAgentEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleVoipSipAgentRegistrar OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Register Domain name. <A.B.C.D> or DNS."
			::= { sleVoipSipAgentEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleVoipSipAgentPrimary OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set IPv4 destination prefix.<A.B.C.D> or primary domain name."
			::= { sleVoipSipAgentEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleVoipSipAgentSecondary OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set IPv4 destination prefix.<A.B.C.D> or secondary domain name."
			::= { sleVoipSipAgentEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleVoipSipAgentProfileName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Profile name."
			::= { sleVoipSipAgentEntry 9 }

		
		-- *******.4.1.6296.**********.1.10
		sleVoipSipAgentExpireTimer OBJECT-TYPE
			SYNTAX Integer32 (300..360)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Register expire timer. Range from 300 to 360s."
			::= { sleVoipSipAgentEntry 10 }

		
		-- *******.4.1.6296.**********
		sleVoipSipAgentControl OBJECT IDENTIFIER ::= { sleVoipSipAgent 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipSipAgentControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableSipAgent(1),
				disableSipAgent(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleVoipSipAgentControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipSipAgentControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleVoipSipAgentControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipSipAgentControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleVoipSipAgentControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipSipAgentControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Time stamp attribute set at the end of command"
			::= { sleVoipSipAgentControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipSipAgentControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of last command"
			::= { sleVoipSipAgentControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipSipAgentControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of SIP agent.Range from 1 to 8."
			::= { sleVoipSipAgentControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipSipAgentControlServer OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				(Optional) to explain each group,up to 32 characters string include NULL character. Set IPv4 address destination prefix <A.B.C.D> or Domain name."
			::= { sleVoipSipAgentControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipSipAgentControlPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				(Optional) to explain each group,up to 32 characters string include NULL character.Port number (0-65535)."
			::= { sleVoipSipAgentControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleVoipSipAgentControlTypeOfService OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Type of service, value is Range from 0 to 63.."
			::= { sleVoipSipAgentControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleVoipSipAgentControlTransport OBJECT-TYPE
			SYNTAX INTEGER
				{
				tcp(6),
				udp(17)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Transport protocol (TCP|UDP)."
			::= { sleVoipSipAgentControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleVoipSipAgentControlRegistrar OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Register Domain name. <A.B.C.D> or DNS..(Optional) to explain each group,up to 32 characters string include NULL character.
				 "
			::= { sleVoipSipAgentControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleVoipSipAgentControlPrimary OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				(Optional) to explain each group,up to 32 characters string include NULL character. Set IPv4 address destination prefix <A.B.C.D> or primary Domain name."
			::= { sleVoipSipAgentControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleVoipSipAgentControlSecondary OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				(Optional) to explain each group,up to 32 characters string include NULL character. Set IPv4 address destination prefix <A.B.C.D> or secondary Domain name."
			::= { sleVoipSipAgentControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleVoipSipAgentControlProfileName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Profile name setting"
			::= { sleVoipSipAgentControl 14 }

		
		-- *******.4.1.6296.**********.15
		sleVoipSipAgentControlExpireTimer OBJECT-TYPE
			SYNTAX Integer32 (300..360)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Register expire timer setting. Range from 300 to 360ms."
			::= { sleVoipSipAgentControl 15 }

		
		-- *******.4.1.6296.**********
		sleVoipSipAgentNotification OBJECT IDENTIFIER ::= { sleVoipSipAgent 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipSipAgentEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipSipAgentControlRequest, sleVoipSipAgentControlTimeStamp, sleVoipSipAgentControlReqResult, sleVoipSipAgentControlIndex, sleVoipSipAgentControlServer, 
				sleVoipSipAgentControlPort, sleVoipSipAgentControlTypeOfService, sleVoipSipAgentControlTransport, sleVoipSipAgentControlRegistrar, sleVoipSipAgentControlPrimary, 
				sleVoipSipAgentControlSecondary, sleVoipSipAgentControlProfileName, sleVoipSipAgentControlExpireTimer }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipSipAgentNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipSipAgentDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipSipAgentControlRequest, sleVoipSipAgentControlTimeStamp, sleVoipSipAgentControlReqResult, sleVoipSipAgentControlIndex }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipSipAgentNotification 2 }

		
		-- *******.4.1.6296.101.25.5
		sleVoipMgcpEndpoint OBJECT IDENTIFIER ::= { sleVoip 5 }

		
		-- *******.4.1.6296.**********
		sleVoipMgcpEndpointTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipMgcpEndpointEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipMgcpEndpoint 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMgcpEndpointEntry OBJECT-TYPE
			SYNTAX SleVoipMgcpEndpointEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipMgcpEndpointIndex }
			::= { sleVoipMgcpEndpointTable 1 }

		
		SleVoipMgcpEndpointEntry ::=
			SEQUENCE { 
				sleVoipMgcpEndpointIndex
					Unsigned32,
				sleVoipMgcpEndpointAgent
					Integer32,
				sleVoipMgcpEndpointName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleVoipMgcpEndpointIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of mgcp endpoint Range from 1 to 8."
			::= { sleVoipMgcpEndpointEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleVoipMgcpEndpointAgent OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Setting the reference to sip which agent,default is 1
				Range from 1 to 8."
			::= { sleVoipMgcpEndpointEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleVoipMgcpEndpointName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Setting endpoint name for mgcp."
			::= { sleVoipMgcpEndpointEntry 3 }

		
		-- *******.4.1.6296.**********
		sleVoipMgcpEndpointControl OBJECT IDENTIFIER ::= { sleVoipMgcpEndpoint 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMgcpEndpointControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableMgcpEndpoint(1),
				disableMgcpEndpoint(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The cpu-pkt-filter request.
				
				"
			::= { sleVoipMgcpEndpointControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipMgcpEndpointControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleVoipMgcpEndpointControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipMgcpEndpointControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleVoipMgcpEndpointControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipMgcpEndpointControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Time stamp attribute set at the end of command"
			::= { sleVoipMgcpEndpointControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipMgcpEndpointControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of last command"
			::= { sleVoipMgcpEndpointControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipMgcpEndpointControlIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of mgcp endpoint
				Range from 1 to 8."
			::= { sleVoipMgcpEndpointControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipMgcpEndpointControlAgent OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Setting the reference to sip which agent,default is 1
				Range from 1 to 8."
			::= { sleVoipMgcpEndpointControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipMgcpEndpointControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"(Optional) to explain each group,up to 32 characters string include NULL character. Setting endpoint name for mgcp."
			::= { sleVoipMgcpEndpointControl 8 }

		
		-- *******.4.1.6296.**********
		sleVoipMgcpEndpointNotification OBJECT IDENTIFIER ::= { sleVoipMgcpEndpoint 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMgcpEndpointEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipMgcpEndpointControlRequest, sleVoipMgcpEndpointControlTimeStamp, sleVoipMgcpEndpointControlReqResult, sleVoipMgcpEndpointControlIndex, sleVoipMgcpEndpointControlAgent, 
				sleVoipMgcpEndpointControlName }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipMgcpEndpointNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipMgcpEndpointDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipMgcpEndpointControlRequest, sleVoipMgcpEndpointControlTimeStamp, sleVoipMgcpEndpointControlReqResult, sleVoipMgcpEndpointControlIndex }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipMgcpEndpointNotification 2 }

		
		-- *******.4.1.6296.101.25.6
		sleVoipMgcpAgent OBJECT IDENTIFIER ::= { sleVoip 6 }

		
		-- *******.4.1.6296.**********
		sleVoipMgcpAgentTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipMgcpAgentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipMgcpAgent 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMgcpAgentEntry OBJECT-TYPE
			SYNTAX SleVoipMgcpAgentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipMgcpAgentIndex }
			::= { sleVoipMgcpAgentTable 1 }

		
		SleVoipMgcpAgentEntry ::=
			SEQUENCE { 
				sleVoipMgcpAgentIndex
					Integer32,
				sleVoipMgcpCallAgent
					IpAddress,
				sleVoipMgcpCallAgentPort
					Integer32,
				sleVoipMgcpAgentPort
					Integer32,
				sleVoipMgcpAgentTOS
					Integer32,
				sleVoipMgcpAgentVendor
					INTEGER,
				sleVoipMgcpAgentDNS
					OCTET STRING,
				sleVoipMgcpAgentProfileName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleVoipMgcpAgentIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"DESCRIPTION:
				Display mgcp agent index .Range from 1 to 8."
			::= { sleVoipMgcpAgentEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleVoipMgcpCallAgent OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IP address of destination prefix "
			::= { sleVoipMgcpAgentEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleVoipMgcpCallAgentPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Agent-Port number, value is from 1-65535."
			::= { sleVoipMgcpAgentEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleVoipMgcpAgentPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Local-Port number, value is from 1-65535."
			::= { sleVoipMgcpAgentEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleVoipMgcpAgentTOS OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Type of service .value is from 0-63."
			::= { sleVoipMgcpAgentEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleVoipMgcpAgentVendor OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(0),
				dssw(1),
				mssw(2),
				g6sw(3),
				cssw(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Type of vendor (DSSW:DasanSwitch,MSSW:MetaSwitch,G6SW:G6Switch,CSSW:C15Switch)"
			::= { sleVoipMgcpAgentEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleVoipMgcpAgentDNS OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set Domain name to MGCP agent."
			::= { sleVoipMgcpAgentEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleVoipMgcpAgentProfileName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The profile name"
			::= { sleVoipMgcpAgentEntry 8 }

		
		-- *******.4.1.6296.**********
		sleVoipMgcpAgentControl OBJECT IDENTIFIER ::= { sleVoipMgcpAgent 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMgcpAgentControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableVoipMgcpAgent(1),
				disableVoipMgcpAgent(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleVoipMgcpAgentControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipMgcpAgentControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleVoipMgcpAgentControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipMgcpAgentControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleVoipMgcpAgentControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipMgcpAgentControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)"
			::= { sleVoipMgcpAgentControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipMgcpAgentControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleVoipMgcpAgentControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipMgcpAgentControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of mgcp agent.
				Setting mgcp agent index .Range from 1 to 8."
			::= { sleVoipMgcpAgentControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipMgcpAgentControlCallAgent OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				Setting IP address of destination prefix "
			::= { sleVoipMgcpAgentControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipMgcpAgentControlCallAgentPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Setting Agent-Port number, value is from 1-65535."
			::= { sleVoipMgcpAgentControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleVoipMgcpAgentControlPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Setting local-Port number, value is from 1-65535."
			::= { sleVoipMgcpAgentControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleVoipMgcpAgentControlTOS OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Setting Type of service .value is from 0-63."
			::= { sleVoipMgcpAgentControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleVoipMgcpAgentControlVendor OBJECT-TYPE
			SYNTAX INTEGER
				{
				dssw(1),
				mssw(2),
				g6sw(3),
				cssw(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				Setting Type of vendor (DSSW:DasanSwitch,MSSW:MetaSwitch,G6SW:G6Switch,CSSW:C15Switch)"
			::= { sleVoipMgcpAgentControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleVoipMgcpAgentControlDNS OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set domain name to Mgcp Agent. (Optional) to explain each group, up to 32 characters string include Null character."
			::= { sleVoipMgcpAgentControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleVoipMgcpAgentControlProfileName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The profile name"
			::= { sleVoipMgcpAgentControl 13 }

		
		-- *******.4.1.6296.**********
		sleVoipMgcpAgentNotification OBJECT IDENTIFIER ::= { sleVoipMgcpAgent 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMgcpAgentEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipMgcpAgentControlRequest, sleVoipMgcpAgentControlTimeStamp, sleVoipMgcpAgentControlReqResult, sleVoipMgcpAgentControlIndex, sleVoipMgcpAgentControlCallAgent, 
				sleVoipMgcpAgentControlPort, sleVoipMgcpAgentControlTOS, sleVoipMgcpAgentControlVendor, sleVoipMgcpAgentControlProfileName, sleVoipMgcpAgentControlDNS, 
				sleVoipMgcpAgentControlCallAgentPort }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipMgcpAgentNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipMgcpAgentDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipMgcpAgentControlRequest, sleVoipMgcpAgentControlTimeStamp, sleVoipMgcpAgentControlReqResult, sleVoipMgcpAgentControlIndex }
			STATUS current
			DESCRIPTION 
				"DESCRIPTION:"
			::= { sleVoipMgcpAgentNotification 2 }

		
		-- *******.4.1.6296.101.25.7
		sleVoipPots OBJECT IDENTIFIER ::= { sleVoip 7 }

		
		-- *******.4.1.6296.**********
		sleVoipPotsStatusTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipPotsStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipPots 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipPotsStatusEntry OBJECT-TYPE
			SYNTAX SleVoipPotsStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipSipAgentIndex }
			::= { sleVoipPotsStatusTable 1 }

		
		SleVoipPotsStatusEntry ::=
			SEQUENCE { 
				sleVoipPotsStatusIndex
					Unsigned32,
				sleVoipPotsStatus
					INTEGER,
				sleVoipPotsStatusHookState
					INTEGER,
				sleVoipPotsStatusRegisterState
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleVoipPotsStatusIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"DESCRIPTION:
				Display status of ports <1-8>"
			::= { sleVoipPotsStatusEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleVoipPotsStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				idle(0),
				twoway(1),
				threeway(2),
				fax(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"
				DESCRIPTION:
				Setting pots status.Value is idle(0)|twoway(1)|threeway(2)|fax(3)"
			::= { sleVoipPotsStatusEntry 2 }

		
-- Register Status. Value is on(0)|off(1)
		-- *******.4.1.6296.**********.1.3
		sleVoipPotsStatusHookState OBJECT-TYPE
			SYNTAX INTEGER
				{
				on(0),
				off(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Hook status. Value is on(0)|off(1)"
			::= { sleVoipPotsStatusEntry 3 }

		
-- Register Status. Value is out(0)|in(1)
		-- *******.4.1.6296.**********.1.4
		sleVoipPotsStatusRegisterState OBJECT-TYPE
			SYNTAX INTEGER
				{
				out(0),
				in(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Register Status. Value is out(0)|in(1)"
			::= { sleVoipPotsStatusEntry 4 }

		
		-- *******.4.1.6296.101.25.8
		sleVoipMediaProfile OBJECT IDENTIFIER ::= { sleVoip 8 }

		
		-- *******.4.1.6296.**********
		sleVoipMediaProfileTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipMediaProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipMediaProfile 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMediaProfileEntry OBJECT-TYPE
			SYNTAX SleVoipMediaProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipMediaProfileIndex }
			::= { sleVoipMediaProfileTable 1 }

		
		SleVoipMediaProfileEntry ::=
			SEQUENCE { 
				sleVoipMediaProfileIndex
					Integer32,
				sleVoipMediaProfileCodecSelection
					INTEGER,
				sleVoipMediaProfilePacketPeriod
					INTEGER,
				sleVoipMediaProfileSilenceSuppression
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleVoipMediaProfileIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of Media Profile (1..8)"
			::= { sleVoipMediaProfileEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleVoipMediaProfileCodecSelection OBJECT-TYPE
			SYNTAX INTEGER
				{
				pcmu(0),
				g723(4),
				pcma(8),
				g729(18)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Voice codec setting : G711 a-law codec, G711 mu-law codec, G.723 Codec standard, G.729 Codec standard."
			::= { sleVoipMediaProfileEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleVoipMediaProfilePacketPeriod OBJECT-TYPE
			SYNTAX INTEGER (10..30)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packet Period setting. Range value is from 10 to 30 ms. Default value is 10ms."
			::= { sleVoipMediaProfileEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleVoipMediaProfileSilenceSuppression OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Silence suppression setting. Off(0), on(1)"
			::= { sleVoipMediaProfileEntry 4 }

		
		-- *******.4.1.6296.**********
		sleVoipMediaProfileControl OBJECT IDENTIFIER ::= { sleVoipMediaProfile 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMediaProfileControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableVoipMediaProfile(1),
				disableVoipMediaProfile(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The request of a user command"
			::= { sleVoipMediaProfileControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipMediaProfileControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The status of user command"
			::= { sleVoipMediaProfileControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipMediaProfileControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The maximum time for the manager for a long running user command"
			::= { sleVoipMediaProfileControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipMediaProfileControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The time stamp of the last command(end of command)"
			::= { sleVoipMediaProfileControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipMediaProfileControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The result of the last user command"
			::= { sleVoipMediaProfileControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipMediaProfileControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of Media Profile (1.8)"
			::= { sleVoipMediaProfileControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipMediaProfileControlCodecSelection OBJECT-TYPE
			SYNTAX INTEGER
				{
				pcmu(0),
				g723(4),
				pcma(8),
				g729(18)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Voice codec : G711 a-law codec, G711 mu-law codec, G.723 Codec standard, G.729 Codec standard."
			::= { sleVoipMediaProfileControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipMediaProfileControlPacketPeriod OBJECT-TYPE
			SYNTAX Integer32 (10..30)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Packet Period setting. Range value is from 10 to 30 ms. Default value is 10ms."
			::= { sleVoipMediaProfileControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleVoipMediaProfileControlSilenceSuppression OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Silence suppression setting. Off(0), on(1)"
			::= { sleVoipMediaProfileControl 9 }

		
		-- *******.4.1.6296.**********
		sleVoipMediaProfileNotification OBJECT IDENTIFIER ::= { sleVoipMediaProfile 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipMediaProfileEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipMediaProfileControlRequest, sleVoipMediaProfileControlTimeStamp, sleVoipMediaProfileControlReqResult, sleVoipMediaProfileControlIndex, sleVoipMediaProfileControlSilenceSuppression, 
				sleVoipMediaProfileControlCodecSelection, sleVoipMediaProfileControlPacketPeriod }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipMediaProfileNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipMediaProfileDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipMediaProfileControlRequest, sleVoipMediaProfileControlTimeStamp, sleVoipMediaProfileControlReqResult, sleVoipMediaProfileControlIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipMediaProfileNotification 2 }

		
		-- *******.4.1.6296.101.25.9
		sleVoipRtpProfile OBJECT IDENTIFIER ::= { sleVoip 9 }

		
		-- *******.4.1.6296.**********
		sleVoipRtpProfileInfo OBJECT IDENTIFIER ::= { sleVoipRtpProfile 1 }

		
		-- *******.4.1.6296.**********.1
		sleVoipRtpProfilePortMin OBJECT-TYPE
			SYNTAX Integer32 (30000..60000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set the starting local port for RTP. Default value is 30000."
			::= { sleVoipRtpProfileInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipRtpProfileDscpMark OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Marks the DSCP with the given number. Default value is 0."
			::= { sleVoipRtpProfileInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipRtpProfileOob OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Enable Out-of-band DTMF. When this option is enable, DTMF can be tranferred via RTP or signalling packet depending on the value of dtmf-event is enable or disable,respectively."
			::= { sleVoipRtpProfileInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipRtpProfileDtmfEvent OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Enable DTMF transfer via RTP. If oob-dtmf is disable, this value is ignored."
			::= { sleVoipRtpProfileInfo 4 }

		
		-- *******.4.1.6296.**********
		sleVoipRtpProfileControl OBJECT IDENTIFIER ::= { sleVoipRtpProfile 2 }

		
		-- *******.4.1.6296.**********.1
		sleVoipRtpProfileControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setVoipRtpPortMin(1),
				setVoipRtpDscp(2),
				setVoipRtpOob(3),
				setVoipRtpDtmf(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The request of a user command"
			::= { sleVoipRtpProfileControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipRtpProfileControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The status of user command"
			::= { sleVoipRtpProfileControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipRtpProfileControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The maximum time for the manager for a long running user command"
			::= { sleVoipRtpProfileControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipRtpProfileControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The time stamp of the last command(end of command)"
			::= { sleVoipRtpProfileControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleVoipRtpProfileControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The result of the last user command"
			::= { sleVoipRtpProfileControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleVoipRtpProfileControlPortMin OBJECT-TYPE
			SYNTAX Integer32 (30000..60000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set the starting local port for RTP. Default value is 30000."
			::= { sleVoipRtpProfileControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleVoipRtpProfileControlDscpMark OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Marks the DSCP with the given number. Default value is 0."
			::= { sleVoipRtpProfileControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleVoipRtpProfileControlOob OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable Out-of-band DTMF. When this option is enable, DTMF can be tranferred via RTP or signalling packet depending on the value of dtmf-event is enable or disable,respectively."
			::= { sleVoipRtpProfileControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleVoipRtpProfileControlDtmfEvent OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable DTMF transfer via RTP. If oob-dtmf is disable, this value is ignored."
			::= { sleVoipRtpProfileControl 9 }

		
		-- *******.4.1.6296.**********
		sleVoipRtpProfileNotification OBJECT IDENTIFIER ::= { sleVoipRtpProfile 3 }

		
		-- *******.4.1.6296.**********.1
		sleVoipRtpProfilePortMinChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipRtpProfileControlRequest, sleVoipRtpProfileControlTimeStamp, sleVoipRtpProfileControlReqResult, sleVoipRtpProfileControlPortMin }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipRtpProfileNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleVoipRtpProfileDscpMarkChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipRtpProfileControlRequest, sleVoipRtpProfileControlTimeStamp, sleVoipRtpProfileControlReqResult, sleVoipRtpProfileControlDscpMark }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipRtpProfileNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleVoipRtpProfileOobChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipRtpProfileControlRequest, sleVoipRtpProfileControlTimeStamp, sleVoipRtpProfileControlReqResult, sleVoipRtpProfileControlOob }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipRtpProfileNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleVoipRtpProfileDtmfEventChanged NOTIFICATION-TYPE
			OBJECTS { sleVoipRtpProfileControlRequest, sleVoipRtpProfileControlTimeStamp, sleVoipRtpProfileControlReqResult, sleVoipRtpProfileControlDtmfEvent }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipRtpProfileNotification 4 }

		
		-- *******.4.1.6296.101.25.10
		sleVoipTimer OBJECT IDENTIFIER ::= { sleVoip 10 }

		
		-- *******.4.1.6296.***********
		sleVoipTimerTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipTimerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipTimer 1 }

		
		-- *******.4.1.6296.***********.1
		sleVoipTimerEntry OBJECT-TYPE
			SYNTAX SleVoipTimerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipTimerIndex }
			::= { sleVoipTimerTable 1 }

		
		SleVoipTimerEntry ::=
			SEQUENCE { 
				sleVoipTimerIndex
					Integer32,
				sleVoipTimerCriticalTimeout
					Integer32,
				sleVoipTimerFirstDigit
					Integer32,
				sleVoipTimerIntraDigit
					Integer32
			 }

		-- *******.4.1.6296.***********.1.1
		sleVoipTimerIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Display Timer index. Value is range from 1 to 8"
			::= { sleVoipTimerEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleVoipTimerCriticalTimeout OBJECT-TYPE
			SYNTAX Integer32 (0..15000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Critical timeout value (default 0ms)"
			::= { sleVoipTimerEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleVoipTimerFirstDigit OBJECT-TYPE
			SYNTAX Integer32 (10000..30000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"First Digit Value (default 15000ms)"
			::= { sleVoipTimerEntry 3 }

		
		-- *******.4.1.6296.***********.1.4
		sleVoipTimerIntraDigit OBJECT-TYPE
			SYNTAX Integer32 (5000..15000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Intra Digit value(Default 5000ms)"
			::= { sleVoipTimerEntry 4 }

		
		-- *******.4.1.6296.***********
		sleVoipTimerControl OBJECT IDENTIFIER ::= { sleVoipTimer 2 }

		
		-- *******.4.1.6296.***********.1
		sleVoipTimerControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableVoipTimer(1),
				disableVoipTimer(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The request of a user command"
			::= { sleVoipTimerControl 1 }

		
		-- *******.4.1.6296.***********.2
		sleVoipTimerControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The status of user command"
			::= { sleVoipTimerControl 2 }

		
		-- *******.4.1.6296.***********.3
		sleVoipTimerControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The maximum time for the manager for a long running user command"
			::= { sleVoipTimerControl 3 }

		
		-- *******.4.1.6296.***********.4
		sleVoipTimerControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The time stamp of the last command(end of command)"
			::= { sleVoipTimerControl 4 }

		
		-- *******.4.1.6296.***********.5
		sleVoipTimerControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The result of the last user command"
			::= { sleVoipTimerControl 5 }

		
		-- *******.4.1.6296.***********.6
		sleVoipTimerControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Setting Timer index. Value is range from 1 to 8"
			::= { sleVoipTimerControl 6 }

		
		-- *******.4.1.6296.***********.7
		sleVoipTimerControlCriticalTimeout OBJECT-TYPE
			SYNTAX Integer32 (0..15000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Critical timeout value (default 0ms)"
			::= { sleVoipTimerControl 7 }

		
		-- *******.4.1.6296.***********.8
		sleVoipTimerControlFirstDigit OBJECT-TYPE
			SYNTAX Integer32 (10000..30000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				First Digit Value (default 15000ms)"
			::= { sleVoipTimerControl 8 }

		
		-- *******.4.1.6296.***********.9
		sleVoipTimerControlIntraDigit OBJECT-TYPE
			SYNTAX Integer32 (5000..15000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Intra Digit value(Default 5000ms)"
			::= { sleVoipTimerControl 9 }

		
		-- *******.4.1.6296.***********
		sleVoipTimerNotification OBJECT IDENTIFIER ::= { sleVoipTimer 3 }

		
		-- *******.4.1.6296.***********.1
		sleVoipTimerEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipTimerControlRequest, sleVoipTimerControlTimeStamp, sleVoipTimerControlReqResult, sleVoipTimerControlIndex, sleVoipTimerControlCriticalTimeout, 
				sleVoipTimerControlFirstDigit, sleVoipTimerControlIntraDigit }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipTimerNotification 1 }

		
		-- *******.4.1.6296.***********.2
		sleVoipTimerDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipTimerControlRequest, sleVoipTimerControlTimeStamp, sleVoipTimerControlReqResult, sleVoipTimerControlIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipTimerNotification 2 }

		
		-- *******.4.1.6296.101.25.11
		sleVoipDialPlan OBJECT IDENTIFIER ::= { sleVoip 11 }

		
		-- *******.4.1.6296.***********
		sleVoipDialPlanTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipDialPlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipDialPlan 1 }

		
		-- *******.4.1.6296.***********.1
		sleVoipDialPlanEntry OBJECT-TYPE
			SYNTAX SleVoipDialPlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipDialPlanIndex }
			::= { sleVoipDialPlanTable 1 }

		
		SleVoipDialPlanEntry ::=
			SEQUENCE { 
				sleVoipDialPlanPotStatusIndex
					Integer32,
				sleVoipDialPlanIndex
					Integer32,
				sleVoipDialPlanDigitMap
					OCTET STRING
			 }

		-- *******.4.1.6296.***********.1.1
		sleVoipDialPlanPotStatusIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Display pots status index. Value is range from 1 to 8"
			::= { sleVoipDialPlanEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleVoipDialPlanIndex OBJECT-TYPE
			SYNTAX Integer32 (1..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Dial-plan index. Value is range from 1 to 16"
			::= { sleVoipDialPlanEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleVoipDialPlanDigitMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Digit Map string. The format is 0123456789.x"
			::= { sleVoipDialPlanEntry 3 }

		
		-- *******.4.1.6296.***********
		sleVoipDialPlanControl OBJECT IDENTIFIER ::= { sleVoipDialPlan 2 }

		
		-- *******.4.1.6296.***********.1
		sleVoipDialPlanControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				enableVoipDialPlan(1),
				disableVoipDialPlan(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The request of a user command"
			::= { sleVoipDialPlanControl 1 }

		
		-- *******.4.1.6296.***********.2
		sleVoipDialPlanControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The status of user command"
			::= { sleVoipDialPlanControl 2 }

		
		-- *******.4.1.6296.***********.3
		sleVoipDialPlanControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The maximum time for the manager for a long running user command"
			::= { sleVoipDialPlanControl 3 }

		
		-- *******.4.1.6296.***********.4
		sleVoipDialPlanControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The time stamp of the last command(end of command)"
			::= { sleVoipDialPlanControl 4 }

		
		-- *******.4.1.6296.***********.5
		sleVoipDialPlanControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				The result of the last user command"
			::= { sleVoipDialPlanControl 5 }

		
		-- *******.4.1.6296.***********.6
		sleVoipDialPlanControlPotStatusIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Pots status index. Value is range from 1 to 8"
			::= { sleVoipDialPlanControl 6 }

		
		-- *******.4.1.6296.***********.7
		sleVoipDialPlanControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..16)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Dial-plan index. Value is range from 1 to 16"
			::= { sleVoipDialPlanControl 7 }

		
		-- *******.4.1.6296.***********.8
		sleVoipDialPlanControlDigitMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.
				Digit Map string. The format is 0123456789.x. Characters can be 0123456789.x-[]T"
			::= { sleVoipDialPlanControl 8 }

		
		-- *******.4.1.6296.***********
		sleVoipDialPlanNotification OBJECT IDENTIFIER ::= { sleVoipDialPlan 3 }

		
		-- *******.4.1.6296.***********.1
		sleVoipDialPlanEnabled NOTIFICATION-TYPE
			OBJECTS { sleVoipDialPlanControlRequest, sleVoipDialPlanControlTimeStamp, sleVoipDialPlanControlReqResult, sleVoipDialPlanControlPotStatusIndex, sleVoipDialPlanControlIndex, 
				sleVoipDialPlanControlDigitMap }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipDialPlanNotification 1 }

		
		-- *******.4.1.6296.***********.2
		sleVoipDialPlanDisabled NOTIFICATION-TYPE
			OBJECTS { sleVoipDialPlanControlRequest, sleVoipDialPlanControlTimeStamp, sleVoipDialPlanControlReqResult, sleVoipDialPlanControlIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoipDialPlanNotification 2 }

		
		-- *******.4.1.6296.101.25.12
		sleVoipStatistics OBJECT IDENTIFIER ::= { sleVoip 12 }

		
		-- *******.4.1.6296.***********
		sleVoipStatisticsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVoipStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipStatistics 1 }

		
		-- *******.4.1.6296.***********.1
		sleVoipStatisticsEntry OBJECT-TYPE
			SYNTAX SleVoipStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVoipStatisticsIndex }
			::= { sleVoipStatisticsTable 1 }

		
		SleVoipStatisticsEntry ::=
			SEQUENCE { 
				sleVoipStatisticsIndex
					Integer32,
				sleVoipStatisticsFormat
					Unsigned32,
				sleVoipStatisticsCallTimer
					Unsigned32,
				sleVoipStatisticsCurrentPlayoutDelay
					Unsigned32,
				sleVoipStatisticsMinPlayoutDelay
					Unsigned32,
				sleVoipStatisticsMaxPlayoutDelay
					Unsigned32,
				sleVoipStatisticsClockOffset
					Unsigned32,
				sleVoipStatisticsPeakJitter
					Unsigned32,
				sleVoipStatisticsInterpolativeConcealment
					Unsigned32,
				sleVoipStatisticsSilenceConcealment
					Unsigned32,
				sleVoipStatisticsOverflowDiscard
					Unsigned32,
				sleVoipStatisticsEndpointDetectionErrors
					Unsigned32,
				sleVoipStatisticsTxVoicePackets
					Unsigned32,
				sleVoipStatisticsTxSignalingPackets
					Unsigned32,
				sleVoipStatisticsTxComfortNoisePackets
					Unsigned32,
				sleVoipStatisticsTotalTransmitDuration
					Unsigned32,
				sleVoipStatisticsVoiceTransmitDuration
					Unsigned32,
				sleVoipStatisticsRxVoicePackets
					Unsigned32,
				sleVoipStatisticsRxSignalingPackets
					Unsigned32,
				sleVoipStatisticsRxComfortNoisePackets
					Unsigned32,
				sleVoipStatisticsTotalReceiveDuration
					Unsigned32,
				sleVoipStatisticsVoiceReceiveDuration
					Unsigned32,
				sleVoipStatisticsPacketsOutSequence
					Unsigned32,
				sleVoipStatisticsBadProtocolHeaders
					Unsigned32,
				sleVoipStatisticsLatePackets
					Unsigned32,
				sleVoipStatisticsEarlyPackets
					Unsigned32,
				sleVoipStatisticsRxVoiceOctets
					Unsigned32,
				sleVoipStatisticsLostPackets
					Unsigned32,
				sleVoipStatisticsCurrentTransmitPower
					Unsigned32,
				sleVoipStatisticsMeanTransmitPower
					Unsigned32,
				sleVoipStatisticsCurrentReceivePower
					Unsigned32,
				sleVoipStatisticsMeanReceivePower
					Unsigned32,
				sleVoipStatisticsBackgroundNoise
					Unsigned32,
				sleVoipStatisticsErlLevel
					Unsigned32,
				sleVoipStatisticsAcomLevel
					Unsigned32,
				sleVoipStatisticsCurrentTransmitActivity
					Unsigned32,
				sleVoipStatisticsCurrentReceiveActivity
					Unsigned32
			 }

		-- *******.4.1.6296.***********.1.1
		sleVoipStatisticsIndex OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of voip pots. Value is range from 1 to 8"
			::= { sleVoipStatisticsEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleVoipStatisticsFormat OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Format revision"
			::= { sleVoipStatisticsEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleVoipStatisticsCallTimer OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Call Timer is number of Rx Voice bytes"
			::= { sleVoipStatisticsEntry 3 }

		
		-- *******.4.1.6296.***********.1.4
		sleVoipStatisticsCurrentPlayoutDelay OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current delay, measured in milliseconds"
			::= { sleVoipStatisticsEntry 4 }

		
		-- *******.4.1.6296.***********.1.5
		sleVoipStatisticsMinPlayoutDelay OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The minimum playout delay observed during the current voice session"
			::= { sleVoipStatisticsEntry 5 }

		
		-- *******.4.1.6296.***********.1.6
		sleVoipStatisticsMaxPlayoutDelay OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The maximum playout delay observed during the current voice session"
			::= { sleVoipStatisticsEntry 6 }

		
		-- *******.4.1.6296.***********.1.7
		sleVoipStatisticsClockOffset OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The time offset between the remote source and the current source"
			::= { sleVoipStatisticsEntry 7 }

		
		-- *******.4.1.6296.***********.1.8
		sleVoipStatisticsPeakJitter OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVoipStatisticsEntry 8 }

		
		-- *******.4.1.6296.***********.1.9
		sleVoipStatisticsInterpolativeConcealment OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The duration of voice interpolation to PCM is incremented when the voice decoder 
				interpolates the missing frames to preserve audio continuity.
				"
			::= { sleVoipStatisticsEntry 9 }

		
		-- *******.4.1.6296.***********.1.10
		sleVoipStatisticsSilenceConcealment OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The duration of silence interpolation to PCM is incremented when the voice decoder 
				performs silence concealment of a missing frame.
				"
			::= { sleVoipStatisticsEntry 10 }

		
		-- *******.4.1.6296.***********.1.11
		sleVoipStatisticsOverflowDiscard OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The duration of Jitter Buffer Overflow Discard"
			::= { sleVoipStatisticsEntry 11 }

		
		-- *******.4.1.6296.***********.1.12
		sleVoipStatisticsEndpointDetectionErrors OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of End-point Detection errors "
			::= { sleVoipStatisticsEntry 12 }

		
		-- *******.4.1.6296.***********.1.13
		sleVoipStatisticsTxVoicePackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The number of voice packets sent to IP."
			::= { sleVoipStatisticsEntry 13 }

		
		-- *******.4.1.6296.***********.1.14
		sleVoipStatisticsTxSignalingPackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The number of packets with payload type Redundant, DTMF or NTE 
				packets sent to IP.
				"
			::= { sleVoipStatisticsEntry 14 }

		
		-- *******.4.1.6296.***********.1.15
		sleVoipStatisticsTxComfortNoisePackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Transmit Comfort Noise Packets. The number of SID packets sent to IP."
			::= { sleVoipStatisticsEntry 15 }

		
		-- *******.4.1.6296.***********.1.16
		sleVoipStatisticsTotalTransmitDuration OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The total duration of the voice transmission."
			::= { sleVoipStatisticsEntry 16 }

		
		-- *******.4.1.6296.***********.1.17
		sleVoipStatisticsVoiceTransmitDuration OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The duration of voice transmission when voice is active."
			::= { sleVoipStatisticsEntry 17 }

		
		-- *******.4.1.6296.***********.1.18
		sleVoipStatisticsRxVoicePackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The number of voice packets received from IP."
			::= { sleVoipStatisticsEntry 18 }

		
		-- *******.4.1.6296.***********.1.19
		sleVoipStatisticsRxSignalingPackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The number of packets with payload type Redundant or DTMF or NTE packets received from IP."
			::= { sleVoipStatisticsEntry 19 }

		
		-- *******.4.1.6296.***********.1.20
		sleVoipStatisticsRxComfortNoisePackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Receive Comfort Noise Packets. The number of SID packets received from IP."
			::= { sleVoipStatisticsEntry 20 }

		
		-- *******.4.1.6296.***********.1.21
		sleVoipStatisticsTotalReceiveDuration OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The total duration of the voice transmission."
			::= { sleVoipStatisticsEntry 21 }

		
		-- *******.4.1.6296.***********.1.22
		sleVoipStatisticsVoiceReceiveDuration OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The total duration of the voice receive."
			::= { sleVoipStatisticsEntry 22 }

		
		-- *******.4.1.6296.***********.1.23
		sleVoipStatisticsPacketsOutSequence OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The number of out of sequence packets received from IP."
			::= { sleVoipStatisticsEntry 23 }

		
		-- *******.4.1.6296.***********.1.24
		sleVoipStatisticsBadProtocolHeaders OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Bad Protocol headers. Following packets will be rejected because of a bad protocol header"
			::= { sleVoipStatisticsEntry 24 }

		
		-- *******.4.1.6296.***********.1.25
		sleVoipStatisticsLatePackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Real Late Packets are defined as packets with sequence numbers that are received after having been declared late."
			::= { sleVoipStatisticsEntry 25 }

		
		-- *******.4.1.6296.***********.1.26
		sleVoipStatisticsEarlyPackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Early Packets"
			::= { sleVoipStatisticsEntry 26 }

		
		-- *******.4.1.6296.***********.1.27
		sleVoipStatisticsRxVoiceOctets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Total number of bytes in the received voice packets."
			::= { sleVoipStatisticsEntry 27 }

		
		-- *******.4.1.6296.***********.1.28
		sleVoipStatisticsLostPackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The Lost Packet Count is calculated as: Declared Late Packets - Real Late Packets"
			::= { sleVoipStatisticsEntry 28 }

		
		-- *******.4.1.6296.***********.1.29
		sleVoipStatisticsCurrentTransmitPower OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Measured power (in dBov) of the signal being sent by the Mindspeed Comcerto Device over the circuit switched network."
			::= { sleVoipStatisticsEntry 29 }

		
		-- *******.4.1.6296.***********.1.30
		sleVoipStatisticsMeanTransmitPower OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Mean Transmit Power. Running mean (average) of measured Tx Powers."
			::= { sleVoipStatisticsEntry 30 }

		
		-- *******.4.1.6296.***********.1.31
		sleVoipStatisticsCurrentReceivePower OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Current Receive Power. Measured power (in dBov) of the signal being received by the Mindspeed Comcerto Device from the circuit switched network."
			::= { sleVoipStatisticsEntry 31 }

		
		-- *******.4.1.6296.***********.1.32
		sleVoipStatisticsMeanReceivePower OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Mean Receive Power .Running mean (average) of measured Rx Powers."
			::= { sleVoipStatisticsEntry 32 }

		
		-- *******.4.1.6296.***********.1.33
		sleVoipStatisticsBackgroundNoise OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Measured power (in dBov) of the estimated background noise level sent by the Comcerto 
				Device over the circuit switched network.
				"
			::= { sleVoipStatisticsEntry 33 }

		
		-- *******.4.1.6296.***********.1.34
		sleVoipStatisticsErlLevel OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The ERL is the attenuation of a signal from the receive-out port to the send-in port of an 
				echo canceller, due to transmission and hybrid loss.
				"
			::= { sleVoipStatisticsEntry 34 }

		
		-- *******.4.1.6296.***********.1.35
		sleVoipStatisticsAcomLevel OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				The ACOM is the attenuation of a signal from the receive-out port to the send-out port of an echo canceller, "
			::= { sleVoipStatisticsEntry 35 }

		
		-- *******.4.1.6296.***********.1.36
		sleVoipStatisticsCurrentTransmitActivity OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Binary decision indicating whether the signal currently being sent by the Mindspeed 
				Comcerto Device over the circuit switched network is voice (1) or silence (0).
				"
			::= { sleVoipStatisticsEntry 36 }

		
		-- *******.4.1.6296.***********.1.37
		sleVoipStatisticsCurrentReceiveActivity OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.
				Binary decision indicating whether the signal currently being received by the Mindspeed Comcerto Device from the circuit switched network is voice (1) or silence (0)."
			::= { sleVoipStatisticsEntry 37 }

		
		-- *******.4.1.6296.101.25.13
		sleVoipGroup OBJECT-GROUP
			OBJECTS { sleVoipBaseControlRequest, sleVoipBaseControlStatus, sleVoipBaseControlTimer, sleVoipProtocol, sleVoipBaseControlTimeStamp, 
				sleVoipBaseControlReqResult, sleVoipVoiceDSCP, sleVoipVoiceJitter, sleVoipVoiceControlRequest, sleVoipVoiceControlStatus, 
				sleVoipVoiceControlTimer, sleVoipVoiceControlTimeStamp, sleVoipVoiceControlReqResult, sleVoipVoiceControlIndex, sleVoipVoiceControlEncoding, 
				sleVoipVoiceControlUserEncoding, sleVoipVoiceControlComfortNoise, sleVoipVoiceControlEchoCancel, sleVoipVoiceControlPacketLoss, sleVoipVoiceControlVad, 
				sleVoipVoiceControlDSCP, sleVoipVoiceControlJitter, sleVoipSipUserIndex, sleVoipSipUserAgent, sleVoipSipUserAOR, 
				sleVoipSipUserDisplayName, sleVoipSipUserUserName, sleVoipSipUserPassword, sleVoipSipUserControlRequest, sleVoipSipUserControlStatus, 
				sleVoipSipUserControlTimer, sleVoipSipUserControlTimeStamp, sleVoipSipUserControlReqResult, sleVoipSipUserControlIndex, sleVoipSipUserControlAgent, 
				sleVoipSipUserControlAOR, sleVoipSipUserControlDisplayName, sleVoipSipUserControlUserName, sleVoipSipUserControlPassword, sleVoipSipAgentIndex, 
				sleVoipSipAgentServer, sleVoipSipAgentPort, sleVoipSipAgentTypeOfService, sleVoipSipAgentTransport, sleVoipSipAgentRegistrar, 
				sleVoipSipAgentPrimary, sleVoipSipAgentSecondary, sleVoipSipAgentProfileName, sleVoipSipAgentExpireTimer, sleVoipSipAgentControlRequest, 
				sleVoipSipAgentControlStatus, sleVoipSipAgentControlTimer, sleVoipSipAgentControlTimeStamp, sleVoipSipAgentControlReqResult, sleVoipSipAgentControlIndex, 
				sleVoipSipAgentControlServer, sleVoipSipAgentControlPort, sleVoipSipAgentControlTypeOfService, sleVoipSipAgentControlTransport, sleVoipSipAgentControlRegistrar, 
				sleVoipSipAgentControlPrimary, sleVoipSipAgentControlSecondary, sleVoipSipAgentControlProfileName, sleVoipSipAgentControlExpireTimer, sleVoipVoiceIndex, 
				sleVoipVoiceEncoding, sleVoipVoiceUserEncoding, sleVoipVoiceComfortNoise, sleVoipVoiceEchoCancel, sleVoipVoicePacketLoss, 
				sleVoipVoiceVad, sleVoipMgcpEndpointIndex, sleVoipMgcpEndpointAgent, sleVoipMgcpEndpointName, sleVoipMgcpEndpointControlRequest, 
				sleVoipMgcpEndpointControlStatus, sleVoipMgcpEndpointControlTimer, sleVoipMgcpEndpointControlTimeStamp, sleVoipMgcpEndpointControlReqResult, sleVoipMgcpEndpointControlIndex, 
				sleVoipMgcpEndpointControlAgent, sleVoipMgcpEndpointControlName, sleVoipMgcpAgentIndex, sleVoipMgcpCallAgent, sleVoipMgcpAgentPort, 
				sleVoipMgcpAgentTOS, sleVoipMgcpAgentVendor, sleVoipMgcpAgentDNS, sleVoipMgcpAgentControlRequest, sleVoipMgcpAgentControlStatus, 
				sleVoipMgcpAgentControlTimer, sleVoipMgcpAgentControlTimeStamp, sleVoipMgcpAgentControlReqResult, sleVoipMgcpAgentControlIndex, sleVoipMgcpAgentControlCallAgent, 
				sleVoipMgcpAgentControlPort, sleVoipMgcpAgentControlTOS, sleVoipMgcpAgentControlVendor, sleVoipMgcpAgentControlDNS, sleVoipPotsStatusIndex, 
				sleVoipInterface, sleVoipMediaProfileIndex, sleVoipMediaProfileCodecSelection, sleVoipMediaProfilePacketPeriod, sleVoipMediaProfileSilenceSuppression, 
				sleVoipMediaProfileControlRequest, sleVoipMediaProfileControlStatus, sleVoipMediaProfileControlTimer, sleVoipMediaProfileControlTimeStamp, sleVoipMediaProfileControlIndex, 
				sleVoipMediaProfileControlCodecSelection, sleVoipMediaProfileControlPacketPeriod, sleVoipMediaProfileControlSilenceSuppression, sleVoipRtpProfilePortMin, sleVoipRtpProfileDscpMark, 
				sleVoipRtpProfileOob, sleVoipRtpProfileDtmfEvent, sleVoipRtpProfileControlRequest, sleVoipRtpProfileControlStatus, sleVoipRtpProfileControlTimer, 
				sleVoipRtpProfileControlTimeStamp, sleVoipRtpProfileControlReqResult, sleVoipRtpProfileControlPortMin, sleVoipRtpProfileControlDscpMark, sleVoipRtpProfileControlOob, 
				sleVoipRtpProfileControlDtmfEvent, sleVoipMgcpCallAgentPort, sleVoipMgcpAgentControlCallAgentPort, sleVoipMgcpAgentProfileName, sleVoipMgcpAgentControlProfileName, 
				sleVoipTimerIndex, sleVoipTimerCriticalTimeout, sleVoipTimerFirstDigit, sleVoipTimerIntraDigit, sleVoipTimerControlRequest, 
				sleVoipTimerControlStatus, sleVoipTimerControlTimer, sleVoipTimerControlTimeStamp, sleVoipTimerControlReqResult, sleVoipTimerControlIndex, 
				sleVoipTimerControlCriticalTimeout, sleVoipTimerControlFirstDigit, sleVoipTimerControlIntraDigit, sleVoipStatisticsIndex, sleVoipStatisticsFormat, 
				sleVoipStatisticsCallTimer, sleVoipStatisticsCurrentPlayoutDelay, sleVoipStatisticsMinPlayoutDelay, sleVoipStatisticsMaxPlayoutDelay, sleVoipStatisticsClockOffset, 
				sleVoipStatisticsPeakJitter, sleVoipStatisticsInterpolativeConcealment, sleVoipStatisticsSilenceConcealment, sleVoipStatisticsOverflowDiscard, sleVoipStatisticsEndpointDetectionErrors, 
				sleVoipStatisticsTxVoicePackets, sleVoipStatisticsTxSignalingPackets, sleVoipStatisticsTxComfortNoisePackets, sleVoipStatisticsTotalTransmitDuration, sleVoipStatisticsVoiceTransmitDuration, 
				sleVoipStatisticsRxVoicePackets, sleVoipStatisticsRxSignalingPackets, sleVoipStatisticsRxComfortNoisePackets, sleVoipStatisticsTotalReceiveDuration, sleVoipStatisticsVoiceReceiveDuration, 
				sleVoipStatisticsPacketsOutSequence, sleVoipStatisticsBadProtocolHeaders, sleVoipStatisticsLatePackets, sleVoipStatisticsEarlyPackets, sleVoipStatisticsRxVoiceOctets, 
				sleVoipStatisticsLostPackets, sleVoipStatisticsCurrentTransmitPower, sleVoipStatisticsMeanTransmitPower, sleVoipStatisticsCurrentReceivePower, sleVoipStatisticsMeanReceivePower, 
				sleVoipStatisticsBackgroundNoise, sleVoipStatisticsErlLevel, sleVoipStatisticsAcomLevel, sleVoipStatisticsCurrentTransmitActivity, sleVoipStatisticsCurrentReceiveActivity, 
				sleVoipDialPlanIndex, sleVoipDialPlanDigitMap, sleVoipDialPlanControlRequest, sleVoipDialPlanControlStatus, sleVoipDialPlanControlTimer, 
				sleVoipDialPlanControlReqResult, sleVoipDialPlanControlIndex, sleVoipDialPlanControlDigitMap, sleVoipDialPlanPotStatusIndex, sleVoipDialPlanControlPotStatusIndex, 
				sleVoipPotsStatusHookState, sleVoipPotsStatusRegisterState, sleVoipRtcp, sleVoipPotsStatus, sleVoipBaseControlInterface, 
				sleVoipBaseControlProtocol, sleVoipMediaProfileControlReqResult, sleVoipBaseControlFaxmode, sleVoipFaxmode, sleVoipDialPlanControlTimeStamp, 
				sleVoipBaseControlRtcp }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoip 13 }

		
		-- *******.4.1.6296.101.25.14
		sleVoipNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleVoipBaseInterfaceChanged, sleVoipBaseProtocolChanged, sleVoipVoiceEnabled, sleVoipVoiceDisabled, sleVoipSipUserEnabled, 
				sleVoipSipUserDisabled, sleVoipSipAgentEnabled, sleVoipSipAgentDisabled, sleVoipMgcpEndpointEnabled, sleVoipMgcpEndpointDisabled, 
				sleVoipMgcpAgentEnabled, sleVoipMgcpAgentDisabled, sleVoipMediaProfileEnabled, sleVoipMediaProfileDisabled, sleVoipRtpProfilePortMinChanged, 
				sleVoipRtpProfileDscpMarkChanged, sleVoipRtpProfileOobChanged, sleVoipRtpProfileDtmfEventChanged, sleVoipTimerEnabled, sleVoipTimerDisabled, 
				sleVoipDialPlanEnabled, sleVoipBaseRtcpChanged, sleVoipBaseFaxmodeChanged, sleVoipDialPlanDisabled, sleVoipBaseRestartStackOnly, 
				sleVoipBaseRestartAll }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVoip 14 }

		
	
	END

--
-- sle-voip-mib.mib
--
