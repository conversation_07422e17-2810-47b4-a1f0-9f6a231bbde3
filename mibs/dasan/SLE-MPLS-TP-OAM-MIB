--
-- sle-mpls-tp-oam-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, February 05, 2016 at 11:01:54
--

	SLE-MPLS-TP-OAM-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			ifGeneralInformationGroup, ifCounterDiscontinuityGroup			
				FROM IF-MIB			
			mplsStdMIB			
				FROM MPLS-TC-STD-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			SnmpAdminString			
				FROM SNMP-FRAMEWORK-MIB			
			zeroDotZero, TimeTicks, Unsigned32, Gauge32, OBJECT-TYPE, 
			MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpOam MODULE-IDENTITY 
			LAST-UPDATED "201510070000Z"		-- October 07, 2015 at 00:00 GMT
			ORGANIZATION 
				" DASAN Networks"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				DongChel Shin (Chris)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"sleMpls"
			REVISION "201301080000Z"		-- January 08, 2013 at 00:00 GMT
			DESCRIPTION 
				" "
			::= { sleMpls 17 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MPLS."
			::= { sleMgmt 16 }

		
		sleMplsTpOamMeg OBJECT IDENTIFIER ::= { sleMplsTpOam 1 }

		
		sleMplsTpOamMegInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpOamMegInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This table contains information about the Maintenance
				Entity Groups (MEG).
				
				MEG as mentioned in MPLS-TP OAM framework defines a set
				of one or more maintenance entities (ME).
				Maintenance Entities define a relationship between any
				two points of a transport path in an OAM domain to which
				maintenance and monitoring operations apply."
			::= { sleMplsTpOamMeg 1 }

		
		sleMplsTpOamMegInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpOamMegInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"An entry in this table represents MPLS-TP MEG.
				An entry can be created by a network administrator
				or by an SNMP agent as instructed by an MPLS-TP OAM
				Framework.
				
				When a new entry is created with
				sleMplsTpOamMegOperatorType set to ipCompatible (1),
				then as per [RFC6370] (MEG_ID for LSP is LSP_ID and
				MEG_ID for PW is PW_Path_ID), MEP_ID can be
				automatically formed.
				
				For co-routed bidirectional LSP, MEG_ID is
				A1-{Global_ID::Node_ID::Tunnel_Num}::Z9-{Global_ID::
				Node_ID::Tunnel_Num}::LSP_Num.
				
				For associated bidirectional LSP, MEG_ID is A1-
				{Global_ID::Node_ID::Tunnel_Num::LSP_Num}:: Z9-
				{Global_ID::Node_ID::Tunnel_Num::LSP_Num}
				
				For LSP, MEP_ID is formed using,
				Global_ID::Node_ID::Tunnel_Num::LSP_Num
				
				For PW, MEG_ID is formed using AGI::A1-
				{Global_ID::Node_ID::AC_ID}:: Z9-
				{Global_ID::Node_ID::AC_ID}.
				
				For PW, MEP_ID is formed using
				AGI::Global_ID::Node_ID::AC_ID
				
				MEP_ID is retrieved from the sleMplsTpOamMegServicePointer
				object based on the sleMplsTpOamMegServiceType value.
				ICC MEG_ID for LSP and PW is formed using the objects
				sleMplsTpOamMegIdIcc and sleMplsTpOamMegIdUmc.
				
				MEP_ID can be formed using MEG_ID::MEP_Index."
			REFERENCE
				"1. RFC 5860, Requirements for OAM in MPLS Transport
				Networks, May 2010.
				2. RFC 6371, Operations, Administration, and Maintenance
				Framework for MPLS-Based Transport Networks,
				September 2011.
				3. RFC 6370, MPLS Transport Profile (MPLS-TP) Identifiers.
				4. MPLS-TP Identifiers Following ITU-T Conventions
				[TP-ITUIDS]."
			INDEX { sleMplsTpOamMegInfoIndex }
			::= { sleMplsTpOamMegInfoTable 1 }

		
		SleMplsTpOamMegInfoEntry ::=
			SEQUENCE { 
				sleMplsTpOamMegInfoIndex
					Unsigned32,
				sleMplsTpOamMegInfoName
					OCTET STRING,
				sleMplsTpOamMegInfoOperatorType
					INTEGER,
				sleMplsTpOamMegInfoServiceType
					INTEGER,
				sleMplsTpOamMegInfoMegLevel
					INTEGER,
				sleMplsTpOamMegInfoOperStatus
					INTEGER
			 }

		sleMplsTpOamMegInfoIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index for the conceptual row identifying a MEG within
				
				this MEG table."
			::= { sleMplsTpOamMegInfoEntry 1 }

		
		sleMplsTpOamMegInfoName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Each Maintenance Entity Group has unique name amongst
				all those used or available to a service provider or
				operator. It facilitates easy identification of
				administrative responsibility for each MEG.
				IETF : MEG Name Max. 48 characters long
				ITUT : MEG Name Max 5 characters(Excluding /)."
			::= { sleMplsTpOamMegInfoEntry 2 }

		
		sleMplsTpOamMegInfoOperatorType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the IETF or ITUT.	"
			REFERENCE
				"1. RFC 6370, MPLS Transport Profile (MPLS-TP)
				Identifiers.
				2. MPLS-TP Identifiers Following ITU-T Conventions
				[TP-ITUIDS]."
			::= { sleMplsTpOamMegInfoEntry 3 }

		
		sleMplsTpOamMegInfoServiceType OBJECT-TYPE
			SYNTAX INTEGER
				{
				tunnel(1),
				vc(2),
				datalink(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates the service type for which the MEG is created.
				If the service type indicates lsp, the service pointer
				in sleMplsOamMe points to the TE tunnel table entry.
				
				If the value is pseudowire service type, the service
				pointer in sleMplsOamMe points to the pseudowire
				table entry.
				
				If the value is section service type, the service
				pointer in sleMplsOamMe points to a section entry."
			::= { sleMplsTpOamMegInfoEntry 4 }

		
		sleMplsTpOamMegInfoMegLevel OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This variable is used to for level 
				for ITUT. When a row in this table is in active object cannot
				be changed."
			::= { sleMplsTpOamMegInfoEntry 5 }

		
		sleMplsTpOamMegInfoOperStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the operational status of the
				Maintenance Entity Group (MEG). This object is used to
				send the notification to the SNMP manager about the MEG.
				
				The value up (1) indicates that the MEG and its monitored
				path are operationally up. The value down (2) indicates
				that the MEG is operationally down.
				
				When the value of mplsOamIdMegOperStatus is up(1), all 
				the bits of mplsOamIdMegSubOperStatus must be cleared.
				When the value of mplsOamIdMegOperStatus is down(2), 
				at least one bit of mplsOamIdMegSubOperStatus must be
				set."
			::= { sleMplsTpOamMegInfoEntry 6 }

		
		sleMplsTpOamMegControl OBJECT IDENTIFIER ::= { sleMplsTpOamMeg 2 }

		
		sleMplsTpOamMegControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createsleMplsTpOamMegControlEntry(1),
				deletesleMplsTpOamMegControlEntry(2),
				setsleMplsTpOamMegControlServiceType(3),
				setsleMplsTpOamMegControlLevel(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure
				functions via setting this entry as proper value."
			::= { sleMplsTpOamMegControl 1 }

		
		sleMplsTpOamMegControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. 
				or .idle. before do setRequest."
			::= { sleMplsTpOamMegControl 2 }

		
		sleMplsTpOamMegControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, 
				this value is 0"
			::= { sleMplsTpOamMegControl 3 }

		
		sleMplsTpOamMegControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMplsTpOamMegControl 4 }

		
		sleMplsTpOamMegControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMplsTpOamMegControl 5 }

		
		sleMplsTpOamMegControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..48))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Each Maintenance Entity Group has unique name amongst
				all those used or available to a service provider or
				operator. It facilitates easy identification of
				administrative responsibility for each MEG."
			::= { sleMplsTpOamMegControl 6 }

		
		sleMplsTpOamMegControlOperatorType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates the operator type for MEG. Conceptual rows
				having 'iccBased' as operator type, should have valid
				values for the objects sleMplsTpOamMegControlIdIcc and
				sleMplsTpOamMegControlIdUmc while making the row status active."
			REFERENCE
				"1. RFC 6370, MPLS Transport Profile (MPLS-TP)
				Identifiers.
				2. MPLS-TP Identifiers Following ITU-T Conventions
				[TP-ITUIDS]."
			::= { sleMplsTpOamMegControl 7 }

		
		sleMplsTpOamMegControlServiceType OBJECT-TYPE
			SYNTAX INTEGER
				{
				tunnel(1),
				vc(2),
				datalink(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates the service type for which the MEG is created.
				If the service type indicates lsp, the service pointer
				in sleMplsOamMe points to the TE tunnel table entry.
				
				If the value is pseudowire service type, the service
				pointer in sleMplsOamMe points to the pseudowire
				table entry.
				
				If the value is section service type, the service
				pointer in sleMplsOamMe points to a section entry."
			::= { sleMplsTpOamMegControl 8 }

		
		sleMplsTpOamMegControlLevel OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This variable is used to for level 
				for ITUT. When a row in this table is in active object cannot
				be changed."
			::= { sleMplsTpOamMegControl 9 }

		
		sleMplsTpOamMaintananceEntity OBJECT IDENTIFIER ::= { sleMplsTpOam 2 }

		
		sleMplsTpOamMaintanceEntityInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpOamMaintanceEntityInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This table contains MPLS-TP maintenance entity
				information.
				
				ME is some portion of a transport path that requires
				management bounded by two points (called MEPs), and the
				relationship between those points to which maintenance
				and monitoring operations apply.
				
				This table is generic enough to handle MEPs and MIPs
				information within a MEG."
			::= { sleMplsTpOamMaintananceEntity 1 }

		
		sleMplsTpOamMaintanceEntityInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpOamMaintanceEntityInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"An entry in this table represents MPLS-TP maintenance
				entity. This entry represents the ME if the source and
				sink MEPs are defined.
				
				A ME is a p2p entity. One ME has two such MEPs.
				A MEG is a group of one or more MEs. One MEG can have
				two or more MEPs.
				
				For P2P LSP, one MEG has one ME and this ME is associated
				two MEPs (source and sink MEPs) within a MEG.
				Each sleMplsOamMeIndex value denotes the ME within a MEG.
				
				In case of unidirectional point-to-point transport paths,
				a single unidirectional Maintenance Entity is defined to
				monitor it and sleMplsOamMeServicePointer points to
				unidirectional point-to-point path.
				
				In case of associated bidirectional point-to-point
				transport paths, two independent unidirectional
				Maintenance Entities are defined to independently monitor
				each direction and each sleMplsOamMeServicePointer MIB
				object points to unique unidirectional transport path.
				This has implications for transactions that terminate at
				or query a MIP, as a return path from MIP to source MEP
				does not necessarily exist within the MEG.
				
				In case of co-routed bidirectional point-to-point
				transport paths, a single bidirectional Maintenance Entity
				
				
				
				is defined to monitor both directions congruently and
				sleMplsOamMeServicePointer MIB object points to co-routed
				bidirectional point-to-point transport path.
				
				In case of unidirectional point-to-multipoint transport
				paths, a single unidirectional Maintenance entity for each
				leaf is defined to monitor the transport path from the
				root to that leaf and each leaf has different transport
				path information in sleMplsOamMeServicePointer MIB object."
			INDEX { sleMplsTpOamMaintanceEntityInfoMeIndex, sleMplsTpOamMaintanceEntityInfoMpIndex }
			::= { sleMplsTpOamMaintanceEntityInfoTable 1 }

		
		SleMplsTpOamMaintanceEntityInfoEntry ::=
			SEQUENCE { 
				sleMplsTpOamMaintanceEntityInfoMeIndex
					Unsigned32,
				sleMplsTpOamMaintanceEntityInfoMpIndex
					Unsigned32,
				sleMplsOamMaintenanceEntityInfoMeName
					SnmpAdminString,
				sleMplsTpOamMaintanceEntityInfoMpType
					INTEGER,
				sleMplsOamMaintenanceEntityInfoServiceTunnelName
					OCTET STRING,
				sleMplsOamMaintenanceEntityInfoServiceVcId
					Unsigned32,
				sleMplsOamMaintenanceEntityInfoServiceDatalink
					OCTET STRING,
				sleMplsTpOamMaintanceEntityInfoCcInterval
					Unsigned32,
				sleMplsTpOamMaintanceEntityInfoRemoteMpId
					Unsigned32,
				sleMplsTpOamMaintanceEntityInfoRemoteCc
					OCTET STRING,
				sleMplsTpOamMaintanceEntityInfoRemoteIcc
					OCTET STRING,
				sleMplsTpOamMaintanceEntityInfoRemoteMeg
					OCTET STRING,
				sleMplsTpOamMaintanceEntityInfoRemoteMpdirection
					INTEGER
			 }

		sleMplsTpOamMaintanceEntityInfoMeIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEG."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 1 }

		
		sleMplsTpOamMaintanceEntityInfoMpIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..8191)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Indicates the maintenance point index, used to create
				multiple MEPs in a node of single ME. The value of this
				object can be MEP index or MIP index. Managers should 
				obtain new values for row creation in this table by reading
				mplsOamIdMeMpIndexNext."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 2 }

		
		sleMplsOamMaintenanceEntityInfoMeName OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object denotes the ME name, each
				Maintenance Entity has unique name within MEG."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 3 }

		
		sleMplsTpOamMaintanceEntityInfoMpType OBJECT-TYPE
			SYNTAX INTEGER
				{
				mep(1),
				mip(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates the maintenance point type within the MEG.
				The object should have the value mep (1),  only in the
				Ingress or Egress nodes of the transport path.
				The object can have the value mip (2),
				in the intermediate nodes and possibly in the end nodes
				of the transport path."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 4 }

		
		sleMplsOamMaintenanceEntityInfoServiceTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This variable represents a pointer to the MPLS-TP
				transport path. This value may point at an entry in the
				sleMplsTunnelEntry ifsleMplsOamMaintenanceEntitygServiceType is configured
				as lsp (1) or at an entry in the pwEntry if
				sleMplsOamMaintenanceEntitygServiceType is configured as pseudowire (2).
				
				Note: This service pointer object, is placed in ME table
				instead of MEG table, since it will be useful in case of
				point-to-multipoint, where each ME will point to different
				branches of a P2MP tree."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 5 }

		
		sleMplsOamMaintenanceEntityInfoServiceVcId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This variable represents a pointer to the MPLS-TP
				transport path. This value may point at an entry in the
				sleMplsTunnelEntry ifsleMplsOamMaintenanceEntitygServiceType is configured
				as lsp (1) or at an entry in the pwEntry if
				sleMplsOamMaintenanceEntitygServiceType is configured as pseudowire (2).
				
				Note: This service pointer object, is placed in ME table
				instead of MEG table, since it will be useful in case of
				point-to-multipoint, where each ME will point to different
				branches of a P2MP tree."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 6 }

		
		sleMplsOamMaintenanceEntityInfoServiceDatalink OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This variable represents a pointer to the MPLS-TP
				transport path. This value may point at an entry in the
				sleMplsTunnelEntry ifsleMplsOamMaintenanceEntitygServiceType is configured
				as lsp (1) or at an entry in the pwEntry if
				sleMplsOamMaintenanceEntitygServiceType is configured as pseudowire (2).
				
				Note: This service pointer object, is placed in ME table
				instead of MEG table, since it will be useful in case of
				point-to-multipoint, where each ME will point to different
				branches of a P2MP tree."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 7 }

		
		sleMplsTpOamMaintanceEntityInfoCcInterval OBJECT-TYPE
			SYNTAX Unsigned32 (1..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This variable is used to keep the continuty check interval
				for ITUT. When a row in this table is in active object cannot
				be changed."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 8 }

		
		sleMplsTpOamMaintanceEntityInfoRemoteMpId OBJECT-TYPE
			SYNTAX Unsigned32 (1..8191)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This variable is used for setting the remote mep id "
			::= { sleMplsTpOamMaintanceEntityInfoEntry 9 }

		
		sleMplsTpOamMaintanceEntityInfoRemoteCc OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..2))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Global uniqueness is assured by concatenating the ICC 
				with a Country Code (CC).  The Country Code (alpha-2) 
				is a string of two alphabetic characters represented 
				with upper case letters (i.e., A-Z).
				
				This object MUST contain a non-null ICC value if
				the MplsOamIdMegOperatorType value is iccBased(2),
				otherwise a null ICC value with octet size 0
				should be assigned."
			REFERENCE
				"RFC6923, MPLS Transport Profile (MPLS-TP) Identifiers
				Following ITU-T Conventions. Section 3."
			DEFVAL { "" }
			::= { sleMplsTpOamMaintanceEntityInfoEntry 10 }

		
		sleMplsTpOamMaintanceEntityInfoRemoteIcc OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..6))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Unique code assigned to Network Operator or Service
				Provider maintained by ITU-T. The ITU Carrier Code
				used to form MEGID.
				
				This object MUST contain a non-null ICC value if
				the MplsOamIdMegOperatorType value is iccBased(2),
				otherwise a null ICC value with octet size 0
				should be assigned."
			REFERENCE
				"RFC6923, MPLS Transport Profile (MPLS-TP) Identifiers
				Following ITU-T Conventions. Section 3.1."
			DEFVAL { "" }
			::= { sleMplsTpOamMaintanceEntityInfoEntry 11 }

		
		sleMplsTpOamMaintanceEntityInfoRemoteMeg OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..6))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Unique code assigned by Network Operator or Service
				Provider and is appended to mplsOamIdMegIdIcc to form
				the MEGID.
				This object MUST contain a non-null ICC value if
				the MplsOamIdMegOperatorType value is iccBased(2),
				otherwise a null ICC value with octet size 0
				should be assigned."
			REFERENCE
				"RFC6923, MPLS Transport Profile (MPLS-TP) Identifiers
				Following ITU-T Conventions. Section 7.1."
			DEFVAL { "" }
			::= { sleMplsTpOamMaintanceEntityInfoEntry 12 }

		
		sleMplsTpOamMaintanceEntityInfoRemoteMpdirection OBJECT-TYPE
			SYNTAX INTEGER
				{
				forward(1),
				reverse(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates the maintenance point type within the MEG.
				This object is used to display the forward and reverse remote mep of 
				Transist router ."
			::= { sleMplsTpOamMaintanceEntityInfoEntry 13 }

		
		sleMplsTpOamMaintanceEntityControl OBJECT IDENTIFIER ::= { sleMplsTpOamMaintananceEntity 2 }

		
		sleMplsTpOamMaintanceEntityRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createsleMplsTpOamMaintanceEntityControlEntry(1),
				deletesleMplsTpOamMaintanceEntityControlEntry(2),
				setsleMplsTpOamMaintanceEntityControlServiceValue(3),
				setSleMplsTpOamMepControlCCInterval(4),
				unsetSleMplsTpOamMepControlCCInterval(5),
				setsleMplsTpOamMaintanceEntityControlRmepId(6),
				unsetsleMplsTpOamMaintanceEntityControlRmepId(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions 
				via setting this entry as proper value."
			::= { sleMplsTpOamMaintanceEntityControl 1 }

		
		sleMplsTpOamMaintanceEntityControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as 
				.busy. or .idle. before do setRequest."
			::= { sleMplsTpOamMaintanceEntityControl 2 }

		
		sleMplsTpOamMaintanceEntityControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time
				command, this value is 0"
			::= { sleMplsTpOamMaintanceEntityControl 3 }

		
		sleMplsTpOamMaintanceEntityControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMplsTpOamMaintanceEntityControl 4 }

		
		sleMplsTpOamMaintanceEntityControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMplsTpOamMaintanceEntityControl 5 }

		
		sleMplsTpOamMaintanceEntityControlMegName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEG."
			::= { sleMplsTpOamMaintanceEntityControl 6 }

		
		sleMplsTpOamMaintanceEntityControlMeName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..48))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object denotes the ME name, each
				Maintenance Entity has unique name within MEG."
			::= { sleMplsTpOamMaintanceEntityControl 7 }

		
		sleMplsTpOamMaintanceEntityControlMepId OBJECT-TYPE
			SYNTAX Unsigned32 (1..8191)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEP ."
			::= { sleMplsTpOamMaintanceEntityControl 8 }

		
		sleMplsTpOamMaintanceEntityControlMpType OBJECT-TYPE
			SYNTAX INTEGER
				{
				mep(1),
				mip(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates the maintenance point type within the MEG.
				
				The object should have the value mep (1),  only in the
				Ingress or Egress nodes of the transport path.
				
				The object can have the value mip (2),
				in the intermediate nodes and possibly in the end nodes
				of the transport path."
			::= { sleMplsTpOamMaintanceEntityControl 9 }

		
		sleMplsTpOamMaintanceEntityControlServiceValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This variable represents a pointer to the MPLS-TP
				transport path. This value may point at an entry in the
				sleMplsTunnelEntry if sleMplsOamMeControlgServiceType is configured
				as lsp (1) or at an entry in the pwEntry if
				sleMplsOamMeControlgServiceType is configured as pseudowire (2).
				
				Note: This service pointer object, is placed in ME table
				instead of MEG table, since it will be useful in case of
				point-to-multipoint, where each ME will point to different
				branches of a P2MP tree."
			::= { sleMplsTpOamMaintanceEntityControl 10 }

		
		sleMplsTpOamMaintanceEntityControlCcInterval OBJECT-TYPE
			SYNTAX Unsigned32 (1..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This variable is used to keep the continuty check interval
				for ITUT. When a row in this table is in active object cannot
				be changed."
			::= { sleMplsTpOamMaintanceEntityControl 11 }

		
		sleMplsTpOamMaintanceEntityControlRemoteMpId OBJECT-TYPE
			SYNTAX Unsigned32 (1..8191)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This variable is used for setting the remote mep id "
			::= { sleMplsTpOamMaintanceEntityControl 12 }

		
		sleMplsTpOamMaintanceEntityControlRemoteCc OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..2))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Global uniqueness is assured by concatenating the ICC 
				with a Country Code (CC).  The Country Code (alpha-2) 
				is a string of two alphabetic characters represented 
				with upper case letters (i.e., A-Z).
				
				This object MUST contain a non-null ICC value if
				the MplsOamIdMegOperatorType value is iccBased(2),
				otherwise a null ICC value with octet size 0
				should be assigned."
			REFERENCE
				"RFC6923, MPLS Transport Profile (MPLS-TP) Identifiers
				Following ITU-T Conventions. Section 3."
			DEFVAL { "" }
			::= { sleMplsTpOamMaintanceEntityControl 13 }

		
		sleMplsTpOamMaintanceEntityControlRemoteIcc OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..6))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Unique code assigned to Network Operator or Service
				Provider maintained by ITU-T. The ITU Carrier Code
				used to form MEGID.
				
				This object MUST contain a non-null ICC value if
				the MplsOamIdMegOperatorType value is iccBased(2),
				otherwise a null ICC value with octet size 0
				should be assigned."
			REFERENCE
				"RFC6923, MPLS Transport Profile (MPLS-TP) Identifiers
				Following ITU-T Conventions. Section 3.1."
			DEFVAL { "" }
			::= { sleMplsTpOamMaintanceEntityControl 14 }

		
		sleMplsTpOamMaintanceEntityControlRemoteMeg OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..7))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Unique code assigned by Network Operator or Service
				Provider and is appended to mplsOamIdMegIdIcc to form
				the MEGID.
				This object MUST contain a non-null ICC value if
				the MplsOamIdMegOperatorType value is iccBased(2),
				otherwise a null ICC value with octet size 0
				should be assigned."
			REFERENCE
				"RFC6923, MPLS Transport Profile (MPLS-TP) Identifiers
				Following ITU-T Conventions. Section 7.1."
			DEFVAL { "" }
			::= { sleMplsTpOamMaintanceEntityControl 15 }

		
		sleMplsTpOamMaintanceEntityControlRemoteMpDirection OBJECT-TYPE
			SYNTAX INTEGER
				{
				fwd(1),
				rev(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates the direction of the MEP. This object
				should be configured if sleMplsOamMeControlMpType is
				configured as mep (1)."
			::= { sleMplsTpOamMaintanceEntityControl 16 }

		
		sleMplsTpOamFm OBJECT IDENTIFIER ::= { sleMplsTpOam 3 }

		
		sleMplsTpOamFmInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpOamFmInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION			" "
			::= { sleMplsTpOamFm 1 }

		
		sleMplsTpOamFmInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpOamFmInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"An entry in this table represents MPLS-TP maintenance
				entity."
			INDEX { sleMplsTpOamFmInfoMegIndex, sleMplsTpOamFmInfoMeIndex, sleMplsTpOamFmInfoMpIndex }
			::= { sleMplsTpOamFmInfoTable 1 }

		
		SleMplsTpOamFmInfoEntry ::=
			SEQUENCE { 
				sleMplsTpOamFmInfoMeIndex
					Unsigned32,
				sleMplsTpOamFmInfoMpIndex
					Unsigned32,
				sleMplsTpOamFmInfoFaultManagement
					INTEGER,
				sleMplsTpOamFmInfoRefreshTime
					Unsigned32,
				sleMplsTpOamFmInfoLockInstruct
					INTEGER,
				sleMplsTpOamFmInfoLockInstructRefreshTime
					Unsigned32,
				sleMplsTpOamFmInfoAlarmIndication
					INTEGER,
				sleMplsTpOamFmInfoAlarmIndicationInterval
					INTEGER,
				sleMplsTpOamFmInfoAlarmIndicationLevel
					INTEGER,
				sleMplsTpOamFmInfoLock
					INTEGER,
				sleMplsTpOamFmInfoLockInterval
					INTEGER,
				sleMplsTpOamFmInfoLockLevel
					INTEGER,
				sleMplsTpOamFmInfoLoopBack
					INTEGER,
				sleMplsTpOamFmInfoLoopBackStatus
					INTEGER,
				sleMplsTpOamFmInfoLockInstructStatus
					INTEGER,
				sleMplsTpOamFmInfoFaultManagementStatus
					INTEGER,
				sleMplsTpOamFmInfoCcCvStatus
					INTEGER,
				sleMplsTpOamFmInfoStatus
					INTEGER
			 }

		sleMplsTpOamFmInfoMeIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEG."
			::= { sleMplsTpOamFmInfoEntry 1 }

		
		sleMplsTpOamFmInfoMpIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..8191)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Indicates the maintenance point index, used to create
				multiple MEPs in a node of single ME. The value of this
				object can be MEP index or MIP index. Managers should 
				obtain new values for row creation in this table by reading
				mplsOamIdMeMpIndexNext."
			::= { sleMplsTpOamFmInfoEntry 2 }

		
		sleMplsTpOamFmInfoFaultManagement OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Fault Managment for IETF shows enable or disable"
			DEFVAL { disable }
			::= { sleMplsTpOamFmInfoEntry 3 }

		
		sleMplsTpOamFmInfoRefreshTime OBJECT-TYPE
			SYNTAX Unsigned32 (1..20)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used in IETF-based Fault Management RefreshTimer."
			DEFVAL { 0 }
			::= { sleMplsTpOamFmInfoEntry 4 }

		
		sleMplsTpOamFmInfoLockInstruct OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Lock instruct for IETF shows enable or disable"
			DEFVAL { disable }
			::= { sleMplsTpOamFmInfoEntry 5 }

		
		sleMplsTpOamFmInfoLockInstructRefreshTime OBJECT-TYPE
			SYNTAX Unsigned32 (1..20)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used in IETF-based lock RefreshTimer."
			DEFVAL { 0 }
			::= { sleMplsTpOamFmInfoEntry 6 }

		
		sleMplsTpOamFmInfoAlarmIndication OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm indication for ITUT shows enable or disable."
			DEFVAL { disable }
			::= { sleMplsTpOamFmInfoEntry 7 }

		
		sleMplsTpOamFmInfoAlarmIndicationInterval OBJECT-TYPE
			SYNTAX INTEGER
				{
				oneSecond(1),
				sixtySeconds(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This is object to display the itut alarm indication interval value. "
			::= { sleMplsTpOamFmInfoEntry 8 }

		
		sleMplsTpOamFmInfoAlarmIndicationLevel OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object to display the Itut alarm indication Level value. "
			::= { sleMplsTpOamFmInfoEntry 9 }

		
		sleMplsTpOamFmInfoLock OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used in ITUT-based lock. This object will enable/disable the lock"
			DEFVAL { disable }
			::= { sleMplsTpOamFmInfoEntry 10 }

		
		sleMplsTpOamFmInfoLockInterval OBJECT-TYPE
			SYNTAX INTEGER
				{
				oneSecond(1),
				sixtySeconds(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the ITUT based Lock Interval. "
			::= { sleMplsTpOamFmInfoEntry 11 }

		
		sleMplsTpOamFmInfoLockLevel OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to Display the ITUT based Lock Level. "
			::= { sleMplsTpOamFmInfoEntry 12 }

		
		sleMplsTpOamFmInfoLoopBack OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used in IETF-based loop back. This will show enable/disable loop back ."
			DEFVAL { 2 }
			::= { sleMplsTpOamFmInfoEntry 13 }

		
		sleMplsTpOamFmInfoLoopBackStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object to display the Status of IETF Based LoopBackStatus."
			DEFVAL { 2 }
			::= { sleMplsTpOamFmInfoEntry 14 }

		
		sleMplsTpOamFmInfoLockInstructStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object to display the Status of IETF Based LockInstruct."
			DEFVAL { 2 }
			::= { sleMplsTpOamFmInfoEntry 15 }

		
		sleMplsTpOamFmInfoFaultManagementStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object to display the Status of IETF Based FaultManagement."
			DEFVAL { 2 }
			::= { sleMplsTpOamFmInfoEntry 16 }

		
		sleMplsTpOamFmInfoCcCvStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enabled(1),
				disabled(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object to display the Status of IETF Based CC-CV."
			DEFVAL { 2 }
			::= { sleMplsTpOamFmInfoEntry 17 }

		
		sleMplsTpOamFmInfoStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				pathNotAssociated(1),
				pathAssociateWithAnotherMe(2),
				pathDown(3),
				receivedAisFmMessage(4),
				receviedLkrFmMessage(5),
				bfdDetecLoc(6),
				serverLayerDown(7),
				invalidMe(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the IETF Status  
				1- path not Associated.
				2- path associated with another me.
				3- path down
				4- received AIS Message.
				5- received LKR FM Message.
				6- Bfd Detected Loc.
				7- Server Layer Down.
				8- Invalid Me."
			::= { sleMplsTpOamFmInfoEntry 18 }

		
		sleMplsTpOamFmControlTable OBJECT IDENTIFIER ::= { sleMplsTpOamFm 2 }

		
		sleMplsTpOamFmControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSleMplsTpOamFmControlInit(1),
				unsetSleMplsTpOamFmControlInit(2),
				setSleMplsTpOamItutAis(3),
				unsetSleMplsTpOamItutAis(4),
				setSleMplsTpOamItutLockInterval(5),
				unsetSleMplsTpOamItutLockInterval(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMplsTpOamFmControlTable 1 }

		
		sleMplsTpOamFmControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMplsTpOamFmControlTable 2 }

		
		sleMplsTpOamFmControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMplsTpOamFmControlTable 3 }

		
		sleMplsTpOamFmControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMplsTpOamFmControlTable 4 }

		
		sleMplsTpOamFmControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMplsTpOamFmControlTable 5 }

		
		sleMplsTpOamFmControlMegName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEG."
			::= { sleMplsTpOamFmControlTable 6 }

		
		sleMplsTpOamFmControlMeName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a ME."
			::= { sleMplsTpOamFmControlTable 7 }

		
		sleMplsTpOamFmControlMepId OBJECT-TYPE
			SYNTAX Unsigned32 (1..8191)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEP."
			::= { sleMplsTpOamFmControlTable 8 }

		
		sleMplsTpOamFmInit OBJECT-TYPE
			SYNTAX INTEGER
				{
				faultMeasurment(1),
				lockInstruct(2),
				loopBack(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This variable is used for setting the fault Measurments for IETF/ITUT based upon gloable configuration
				1) Fault measurment is for IETF 
				2) Lock-Instruct is for IETF 
				3) LoopBack to configure for IETF "
			::= { sleMplsTpOamFmControlTable 9 }

		
		sleMplsTpOamFmControlRefreshTime OBJECT-TYPE
			SYNTAX Unsigned32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This varible is used to set the Refresh Time for IETF of 2 objects ie., Fault managment and Lock Instruct
				1) Fault managment range (1-20)
				2) Lock-Instruct range (1-255)"
			::= { sleMplsTpOamFmControlTable 10 }

		
		sleMplsTpOamFmControlInterval OBJECT-TYPE
			SYNTAX INTEGER
				{
				oneOne(1),
				sixtyOne(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This variable is used to set the interval for 2 alarm-inidaction and lock objects of ITUT"
			::= { sleMplsTpOamFmControlTable 11 }

		
		sleMplsTpOamFmControlLevel OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This varible is used to set the level for Alarm indication and lock objects of ITUT"
			::= { sleMplsTpOamFmControlTable 12 }

		
	
	END

--
-- sle-mpls-tp-oam-mib.mib
--
