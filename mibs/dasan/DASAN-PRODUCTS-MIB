-- *****************************************************************
-- DASAN-PRODUCTS-MIB.my:  Dasan Product Object Identifier Assignments
--
--
-- April 2001, <PERSON><PERSON><PERSON>  
-- May 2003, <PERSON><PERSON><PERSON>  
-- May 2004, <PERSON><PERSON>
-- Copyright (c) 2001 by Dasan Co., Ltd.
-- All rights reserved.
-- 
-- *****************************************************************

-- ********************************************************************************
--  Log for dasanProducts MIB.
--
--  July 29 2003
--    1. add V1708 switch model with value 11.
--                                      
--  Aug 7 2003
--    1. add V1500, V2500, V2600 Router Model
--
--  Aug 10 2003
--    1. add V2108, V2116, V2124            
--
--  Aug 26 2003
--    1. add V1008 L2 switch
--
--  Dec 16 2003
--    1. add V5916DMT50 VDSL model                 
--  Feb 26 2003
--    1. add V5916DMT70 VDSL model      
--  Mar 26 2004
--    1. add V1124C L2 ethernet switch model      
--  April 28 2004
--    1. add V5916 DMT-100      
--  June 12 2004
--    1. add V5908 DMT-50, DMT-100
--    2. add V1716        
--  
--
--  Aug 17 2005
--    1. add V5216 of V5100 series   
--
--  Sept 6 2005
--    1. add AccessGateWay(VoIP) Products. 
--       V4604S, V4610S, V4664    
--
--  Oct 20 2005
--   1. add v1624MD 
--          v5424G
--          v1824
--   2. change hiX5630M800 -> hiX5630M600 
--             hiD6615M223 -> hiD6615M323
--      add hiD6615M223 (18)
--      add hiX5630V-M600V
--
--  Nov 1 2005
--   1. add v1816, v1808
--  Nov 3 2005
--   1. add v5916SB     
--
--  Nov 9 2005
--   1. add v5625M400
--
--  Dec 12 2005
--   1. add v1824EL
--
--  Dec 13 2005
--   1. Change hix5625-M400 to hiX5625M400 of v5800 17
--   2. remove   
--      hiX5630-M600      OBJECT IDENTIFIER ::= { hiX 11 }
--      hiX5635-M1200     OBJECT IDENTIFIER ::= { hiX 12 }
--      hiX5630V-M600V    OBJECT IDENTIFIER ::= { hiX 13 }
--      hiX5625-M400           
--
--  Dec 22 2005
--   1. Add V5700 and V5724G
--                                
--  Jan 6 2006
--	 1. Add V5908B  
--   2. Change V5916SB -> V5916B    
--
--  Jan 9 2006
--   1. Add V4208       
--
--  Feb 5 2006
--   1. Add V1624CWDM            
--
--  Feb 24 2006
--   1. Add hiX5830     
--
--  Mar 14 2006
--   1. Add V6424EL , OP
--
--  Mar 28 2006
--   1. Add V5924B    
--   
--  Mar 29 2006
--   1. Add V1824MD
--
--  May 10 2006
--   1. Change hix5830 -> hix5430
--   2. Add V2124J
--
--  June 14 2006
--   1. Add hiD6615-S324  (V5424G)
--
--
--  August 17 2006
--   1. Add V5924N, V5924C, V5948  
--
-- ******************************************************************************** 


DASAN-PRODUCTS-MIB DEFINITIONS ::= BEGIN

IMPORTS
	MODULE-IDENTITY
		FROM SNMPv2-SMI
	dasanModules,
	dasanProducts
		FROM DASAN-SMI;

dasanProductsMIB MODULE-IDENTITY
	LAST-UPDATED	"9505310000Z"
	ORGANIZATION	"Dasan Co., Ltd."
	CONTACT-INFO
		"       Dasan Co., Ltd."
	DESCRIPTION
		"This module defines the object identifiers that are
		assigned to various hardware platforms, and hence are
		returned as values for sysObjectID"
 	REVISION	"0104190000Z"
  	DESCRIPTION
		"Miscellaneous updates."
	::= { dasanModules 2 }

dasanRouter     OBJECT IDENTIFIER ::= { dasanProducts 1 }
dasanSwitch     OBJECT IDENTIFIER ::= { dasanProducts 2 }
dasanAccessSDSL OBJECT IDENTIFIER ::= { dasanProducts 3 }
dasanAccessVDSL OBJECT IDENTIFIER ::= { dasanProducts 4 }
dasanVERTEX5124 OBJECT IDENTIFIER ::= { dasanProducts 5 }  
dasanXDSL	OBJECT IDENTIFIER ::= { dasanProducts 6 }
dasanGEPON	OBJECT IDENTIFIER ::= { dasanProducts 10 }
dasanAccessGateway OBJECT IDENTIFIER ::= { dasanProducts 11 }
mSeries		OBJECT IDENTIFIER ::= { dasanProducts 12 }
sSeries		OBJECT IDENTIFIER ::= { dasanProducts 13 }
custom		OBJECT IDENTIFIER ::= { dasanProducts 20 }

surpass		OBJECT IDENTIFIER ::= { dasanProducts 21 }
wireless	OBJECT IDENTIFIER ::= { dasanProducts 22 }


--  
-- May 2003, dhlee  : add product types 
--                                  
             
-- switch
v5100  OBJECT IDENTIFIER ::= { dasanSwitch 1 }   
v1100  OBJECT IDENTIFIER ::= { dasanSwitch 2 }   
v6100  OBJECT IDENTIFIER ::= { dasanSwitch 3 }   
v8000  OBJECT IDENTIFIER ::= { dasanSwitch 4 }   
v5200  OBJECT IDENTIFIER ::= { dasanSwitch 5 }
v2100  OBJECT IDENTIFIER ::= { dasanSwitch 6 }
v1000  OBJECT IDENTIFIER ::= { dasanSwitch 7 } 
v5500  OBJECT IDENTIFIER ::= { dasanSwitch 8 }
v6300  OBJECT IDENTIFIER ::= { dasanSwitch 9 }
v5700  OBJECT IDENTIFIER ::= { dasanSwitch 10 }
v4200  OBJECT IDENTIFIER ::= { dasanSwitch 11 }
esba    OBJECT IDENTIFIER ::= { dasanSwitch 12 }
hModel OBJECT IDENTIFIER ::= { dasanSwitch 13 }
aModel	OBJECT IDENTIFIER ::= { dasanSwitch 14 }
v3000  OBJECT IDENTIFIER ::= { dasanSwitch 15 }
    
--
-- V5xxx_V1xxx Strata AON
--
v5124  OBJECT IDENTIFIER ::= { v5100 1 }
v5108F OBJECT IDENTIFIER ::= { v5100 2 }
v5116F OBJECT IDENTIFIER ::= { v5100 3 }
v5124F OBJECT IDENTIFIER ::= { v5100 4 }
v1724  OBJECT IDENTIFIER ::= { v5100 5 }
v1708F OBJECT IDENTIFIER ::= { v5100 6 }
v5224  OBJECT IDENTIFIER ::= { v5100 7 }
v5216F OBJECT IDENTIFIER ::= { v5100 8 }
v5324  OBJECT IDENTIFIER ::= { v5100 9 }
v5124E OBJECT IDENTIFIER ::= { v5100 10 }
v1708  OBJECT IDENTIFIER ::= { v5100 11 }  
v1716  OBJECT IDENTIFIER ::= { v5100 12 } 
v1724plus  OBJECT IDENTIFIER ::= { v5100 13 }
v1624  OBJECT IDENTIFIER ::= { v5100 14 }
v1616  OBJECT IDENTIFIER ::= { v5100 15 }  
v1608  OBJECT IDENTIFIER ::= { v5100 16 }
v5216  OBJECT IDENTIFIER ::= { v5100 17 } 
v1624MD OBJECT IDENTIFIER ::= { v5100 18 }    
v1624CWDM OBJECT IDENTIFIER ::= { v5100 19 }
v2624 OBJECT IDENTIFIER ::= { v5100 20 }
v2616 OBJECT IDENTIFIER ::= { v5100 21 }
v2608 OBJECT IDENTIFIER ::= { v5100 22 }


--
-- V1xxx AON
--
v1124  OBJECT IDENTIFIER ::= { v1100 1 }
v1108F OBJECT IDENTIFIER ::= { v1100 2 }
v1224  OBJECT IDENTIFIER ::= { v1100 3 } 
v1124C OBJECT IDENTIFIER ::= { v1100 4 }   
v1324  OBJECT IDENTIFIER ::= { v1100 5 }
v1424G OBJECT IDENTIFIER ::= { v1100 6 }
v1916GR OBJECT IDENTIFIER ::= { v1100 7 }

--
-- V6xxx CXE AON
--
v6124  OBJECT IDENTIFIER ::= { v6100 1 }
v6108  OBJECT IDENTIFIER ::= { v6100 2 }
v6124F OBJECT IDENTIFIER ::= { v6100 3 }
v6108F OBJECT IDENTIFIER ::= { v6100 4 }    
v6216G OBJECT IDENTIFIER ::= { v6100 5 }    
v6116G OBJECT IDENTIFIER ::= { v6100 6 }    
v6224  OBJECT IDENTIFIER ::= { v6100 7 }
v6108G OBJECT IDENTIFIER ::= { v6100 8 }     

--
-- V5xxx IRIS AON
--
v5208G OBJECT IDENTIFIER ::= { v5200 1 }  
v5212G OBJECT IDENTIFIER ::= { v5200 2 }  
v5424G OBJECT IDENTIFIER ::= { v5200 3 }
v5224G OBJECT IDENTIFIER ::= { v5200 4 }
v5324G OBJECT IDENTIFIER ::= { v5200 5 }
v5524G OBJECT IDENTIFIER ::= { v5200 6 }  
v5548G OBJECT IDENTIFIER ::= { v5200 7 }  
v5524XG OBJECT IDENTIFIER ::= { v5200 9 }  
v5848G OBJECT IDENTIFIER ::= { v5200 10 }  
v5504XG OBJECT IDENTIFIER ::= { v5200 11 }  
v5812G OBJECT IDENTIFIER ::= { v5200 12 }
v5524GS OBJECT IDENTIFIER ::= { v5200 13 }
v5806 OBJECT IDENTIFIER ::= { v5200 14 }
gpm4-2G OBJECT IDENTIFIER ::= { v5200 15 }
v5824G OBJECT IDENTIFIER ::= { v5200 16 }
v5648G OBJECT IDENTIFIER ::= { v5200 17 }
v5836G OBJECT IDENTIFIER ::= { v5200 18 }

--
-- V2xxx AON
--
v2108  OBJECT IDENTIFIER ::= { v2100 1 }
v2116  OBJECT IDENTIFIER ::= { v2100 2 }
v2124  OBJECT IDENTIFIER ::= { v2100 3 }
v2308  OBJECT IDENTIFIER ::= { v2100 4 }
v2316  OBJECT IDENTIFIER ::= { v2100 5 }
v2324  OBJECT IDENTIFIER ::= { v2100 6 }
v2116J OBJECT IDENTIFIER ::= { v2100 7 }
v2124J OBJECT IDENTIFIER ::= { v2100 8 }
v2424POE  OBJECT IDENTIFIER ::= { v2100 9 }
v2324G OBJECT IDENTIFIER ::= { v2100 10 }
v2348G OBJECT IDENTIFIER ::= { v2100 11 }
v2124G OBJECT IDENTIFIER ::= { v2100 12 }
v2708  OBJECT IDENTIFIER ::= { v2100 13 }
v2724  OBJECT IDENTIFIER ::= { v2100 14 }
v2824  OBJECT IDENTIFIER ::= { v2100 15 }
v2524G  OBJECT IDENTIFIER ::= { v2100 16 }
v2808  OBJECT IDENTIFIER ::= { v2100 17 }
v2808K  OBJECT IDENTIFIER ::= { v2100 18 }

v2724G  OBJECT IDENTIFIER ::= { v2100 19 }
v2724GPOE  OBJECT IDENTIFIER ::= { v2100 20 }
v2624G  OBJECT IDENTIFIER ::= { v2100 21 }
v2624GPOE  OBJECT IDENTIFIER ::= { v2100 22 }
v2716GPOE OBJECT IDENTIFIER ::= { v2100 23 }
v2708GPOE OBJECT IDENTIFIER ::= { v2100 24 }
--v2724 OBJECT IDENTIFIER ::= { v2100 25 }
v2716G OBJECT IDENTIFIER ::= { v2100 26 }
v2624GPOES OBJECT IDENTIFIER ::= { v2100 27 }
v2808GPOE  OBJECT IDENTIFIER ::= { v2100 28 }
v2814GPOE  OBJECT IDENTIFIER ::= { v2100 29 }
v2424G  OBJECT IDENTIFIER ::= { v2100 30 }
v2624GPOEK  OBJECT IDENTIFIER ::= { v2100 31 }
v2724GC  OBJECT IDENTIFIER ::= { v2100 32 }
v2716GC  OBJECT IDENTIFIER ::= { v2100 33 }
v2708GC  OBJECT IDENTIFIER ::= { v2100 34 }
v2809GPOE  OBJECT IDENTIFIER ::= { v2100 35 }
v2810P  OBJECT IDENTIFIER ::= { v2100 36 }
v2816P  OBJECT IDENTIFIER ::= { v2100 37 }
v2824GPOE  OBJECT IDENTIFIER ::= { v2100 38 }
v2224G-OP  OBJECT IDENTIFIER ::= { v2100 39 }
v2208G OBJECT IDENTIFIER ::= { v2100 40 }
v2216G OBJECT IDENTIFIER ::= { v2100 41 }
v2224GA OBJECT IDENTIFIER ::= { v2100 42 }
v2724GA OBJECT IDENTIFIER ::= { v2100 43 }
v2724GB OBJECT IDENTIFIER ::= { v2100 44 }
v2708GM OBJECT IDENTIFIER ::= { v2100 45 }
v2808GV2 OBJECT IDENTIFIER ::= { v2100 46 }
v2708GB OBJECT IDENTIFIER ::= { v2100 47 }
v2716GB OBJECT IDENTIFIER ::= { v2100 48 }
v2224GB OBJECT IDENTIFIER ::= { v2100 49 }
v2824G OBJECT IDENTIFIER ::= { v2100 50 }

    
v1008  OBJECT IDENTIFIER ::= { v1000 1 }
                                             
--
-- V5xxx_V18xx ANIMA AON
--
v5524    OBJECT IDENTIFIER ::= { v5500 1 }
v5516    OBJECT IDENTIFIER ::= { v5500 2 }
v5508    OBJECT IDENTIFIER ::= { v5500 3 } 
v5524OP  OBJECT IDENTIFIER ::= { v5500 4 }
v5524EL  OBJECT IDENTIFIER ::= { v5500 5 }
v5624F   OBJECT IDENTIFIER ::= { v5500 6 } 
v1824    OBJECT IDENTIFIER ::= { v5500 7 }
v1816    OBJECT IDENTIFIER ::= { v5500 8 }
v1808    OBJECT IDENTIFIER ::= { v5500 9 }
v1824EL  OBJECT IDENTIFIER ::= { v5500 10 }  
v5616F   OBJECT IDENTIFIER ::= { v5500 11 }    
v1824MD  OBJECT IDENTIFIER ::= { v5500 12 }   
v1816MD  OBJECT IDENTIFIER ::= { v5500 13 }   
v1808MD  OBJECT IDENTIFIER ::= { v5500 14 }   
v1824E	 OBJECT IDENTIFIER ::= { v5500 15 }
v1816EL  OBJECT IDENTIFIER ::= { v5500 16 }  
v1808EL  OBJECT IDENTIFIER ::= { v5500 17 }  
v1816E   OBJECT IDENTIFIER ::= { v5500 18 }  
v1808E   OBJECT IDENTIFIER ::= { v5500 19 }  
v1824OP  OBJECT IDENTIFIER ::= { v5500 20 }  
v1816R3  OBJECT IDENTIFIER ::= { v5500 21 }  
v1816R3MD  OBJECT IDENTIFIER ::= { v5500 22 }  
v1824R3  OBJECT IDENTIFIER ::= { v5500 23 }  
v1848    OBJECT IDENTIFIER ::= { v5500 24 }  
v1824R3MD OBJECT IDENTIFIER ::= { v5500 25 }  
v1824MDWDM OBJECT IDENTIFIER ::= { v5500 26 }  
v1816R4MD OBJECT IDENTIFIER ::= { v5500 27 }
v5624G OBJECT IDENTIFIER ::= { v5500 28}

--
-- V8xxx  Massive OLT/Switch
-- 
v8240  OBJECT IDENTIFIER ::= { v8000 1 }   
v8272  OBJECT IDENTIFIER ::= { v8000 2 }   
v8500  OBJECT IDENTIFIER ::= { v8000 3 }   
v8300  OBJECT IDENTIFIER ::= { v8000 4 }   
v8102  OBJECT IDENTIFIER ::= { v8000 5 }   
v8400  OBJECT IDENTIFIER ::= { v8000 6 }   
v8106  OBJECT IDENTIFIER ::= { v8000 7 }   
v8600  OBJECT IDENTIFIER ::= { v8000 8 }   


--
-- V8600
--
v8605  OBJECT IDENTIFIER ::= { v8600 1 }   
v8607  OBJECT IDENTIFIER ::= { v8600 2 }   
v8610  OBJECT IDENTIFIER ::= { v8600 3 }   

v8600-IU OBJECT IDENTIFIER ::= { v8600 101 }

v8600-CU OBJECT IDENTIFIER ::= { v8600-IU 1 }
v8600-IU-GE24-GT8 OBJECT IDENTIFIER ::= { v8600-IU 2 }
v8600-IU-GT24-GE8 OBJECT IDENTIFIER ::= { v8600-IU 3 }
v8600-IU-GE44-10GE4 OBJECT IDENTIFIER ::= { v8600-IU 4 }
v8600-IU-GT48 OBJECT IDENTIFIER ::= { v8600-IU 5 }
v8600-IU-GT24-GE20-10GE4 OBJECT IDENTIFIER ::= { v8600-IU 6 }
v8600-IU-10GE8 OBJECT IDENTIFIER ::= { v8600-IU 7 }
v8600-IU-GT48P OBJECT IDENTIFIER ::= { v8600-IU 8 }
v8600-IU-10GE8F OBJECT IDENTIFIER ::= { v8600-IU 9 }
v8600-IU-GE44-10GE4F OBJECT IDENTIFIER ::= { v8600-IU 10 }
v8600-IU-GT48F OBJECT IDENTIFIER ::= { v8600-IU 11 }
v8600-IU-10GE48 OBJECT IDENTIFIER ::= { v8600-IU 12 }
v8600-IU-40GE12 OBJECT IDENTIFIER ::= { v8600-IU 13 }
v8600-IU-10GE24-40GE4 OBJECT IDENTIFIER ::= { v8600-IU 14 }
ds-PA600I OBJECT IDENTIFIER ::= { v8600-IU 15 }
ds-PD600I OBJECT IDENTIFIER ::= { v8600-IU 16 }
ds-PA1600I OBJECT IDENTIFIER ::= { v8600-IU 17 }
ds-PD1600I OBJECT IDENTIFIER ::= { v8600-IU 18 }
ds-PA1600I-PL OBJECT IDENTIFIER ::= { v8600-IU 19 }
ds-PA3000I-PL  OBJECT IDENTIFIER ::= { v8600-IU 20 }


--
-- V6xxx IRIS AON
--
v6324F   OBJECT IDENTIFIER ::= { v6300 1 }
v6308F   OBJECT IDENTIFIER ::= { v6300 2 }
v6424    OBJECT IDENTIFIER ::= { v6300 3 }   
v6424EL  OBJECT IDENTIFIER ::= { v6300 4 } 
v6424OP  OBJECT IDENTIFIER ::= { v6300 5 } 
v6416F   OBJECT IDENTIFIER ::= { v6300 6 } 
v6224F   OBJECT IDENTIFIER ::= { v6300 7 } 
v6024OP  OBJECT IDENTIFIER ::= { v6300 8 } 
v6424F   OBJECT IDENTIFIER ::= { v6300 9 } 
v6524G   OBJECT IDENTIFIER ::= { v6300 10 } 
v6424G   OBJECT IDENTIFIER ::= { v6300 11 } 
v6748XG   OBJECT IDENTIFIER ::= { v6300 12 } 
v6744XG   OBJECT IDENTIFIER ::= { v6300 13 } 
v6848XG   OBJECT IDENTIFIER ::= { v6300 14 } 
v6648XG   OBJECT IDENTIFIER ::= { v6300 15 }
v6824XG   OBJECT IDENTIFIER ::= { v6300 16 }
--
-- V5xxx IRIS PON
--
v5724G   OBJECT IDENTIFIER ::= { v5700 1 }
v5700G OBJECT IDENTIFIER ::= { v5700 2 }
v5708 OBJECT IDENTIFIER ::= { v5700 3 }

v5724G-10G OBJECT IDENTIFIER ::= { v5700 4 }

-- QoS 
v4208    OBJECT IDENTIFIER ::= { v4200 1 }

-- ESB
esb24-d    OBJECT IDENTIFIER ::= { esba 1 }
esa40-a    OBJECT IDENTIFIER ::= { esba 2 }


-- AHub seriese
a1100	OBJECT IDENTIFIER ::= { aModel 1 }
a1200	OBJECT IDENTIFIER ::= { aModel 2 }


-- V3000 serise 
v3208G  OBJECT IDENTIFIER ::= { v3000 1 }
v3220G  OBJECT IDENTIFIER ::= { v3000 2 }
    
                                             
m3000       OBJECT IDENTIFIER ::= { mSeries 1 }
m2200       OBJECT IDENTIFIER ::= { mSeries 2 }
m3100       OBJECT IDENTIFIER ::= { mSeries 3 }
m3200       OBJECT IDENTIFIER ::= { mSeries 4 }
m2400       OBJECT IDENTIFIER ::= { mSeries 5 }
m1200       OBJECT IDENTIFIER ::= { mSeries 6 }

-- s series
s6804X      OBJECT IDENTIFIER ::= { sSeries 1 }
s2000-O-8G      OBJECT IDENTIFIER ::= { sSeries 2 }
s2000-8G      OBJECT IDENTIFIER ::= { sSeries 3 }
s2228POE-SYD      OBJECT IDENTIFIER ::= { sSeries 4 }
s2000-24G      OBJECT IDENTIFIER ::= { sSeries 5 }
s4424       OBJECT IDENTIFIER ::= { sSeries 6 }
s2224G       OBJECT IDENTIFIER ::= { sSeries 7 }
s4424G       OBJECT IDENTIFIER ::= { sSeries 8 }
s4424GP       OBJECT IDENTIFIER ::= { sSeries 9 }
s4524G       OBJECT IDENTIFIER ::= { sSeries 10 }
s4524GP       OBJECT IDENTIFIER ::= { sSeries 11 }
s4224G       OBJECT IDENTIFIER ::= { sSeries 12 }
s4224GP       OBJECT IDENTIFIER ::= { sSeries 13 }
s4324G       OBJECT IDENTIFIER ::= { sSeries 14 }
s4324GP       OBJECT IDENTIFIER ::= { sSeries 15 }

-- ds series
ds2410      OBJECT IDENTIFIER ::= { custom 1 }
ds2210      OBJECT IDENTIFIER ::= { custom 2 }
ds1610      OBJECT IDENTIFIER ::= { custom 3 }
ds0810      OBJECT IDENTIFIER ::= { custom 4 }
fk-OLT-G1040      OBJECT IDENTIFIER ::= { custom 5 }


-- xDSL
v5900       OBJECT IDENTIFIER ::= { dasanXDSL 1 }

v5924LRE    OBJECT IDENTIFIER ::= { v5900 1 }
v5924E      OBJECT IDENTIFIER ::= { v5900 2 }
v5972       OBJECT IDENTIFIER ::= { v5900 3 }
v5972QAM50  OBJECT IDENTIFIER ::= { v5900 4 }
v5972DMT50  OBJECT IDENTIFIER ::= { v5900 5 }
v5924DMT50  OBJECT IDENTIFIER ::= { v5900 6 }
v5916DMT50  OBJECT IDENTIFIER ::= { v5900 7 }
v5916DMT70  OBJECT IDENTIFIER ::= { v5900 8 }
v5916DMT100 OBJECT IDENTIFIER ::= { v5900 9 }
v5908       OBJECT IDENTIFIER ::= { v5900 10 } 
v5908DMT50  OBJECT IDENTIFIER ::= { v5900 11 }
v5908DMT100 OBJECT IDENTIFIER ::= { v5900 12 } 
v5924LR50   OBJECT IDENTIFIER ::= { v5900 13 }

v5924SB     OBJECT IDENTIFIER ::= { v5900 15 }  
v5916B	    OBJECT IDENTIFIER ::= { v5900 16 }
v5908B	    OBJECT IDENTIFIER ::= { v5900 17 }
v5924B	    OBJECT IDENTIFIER ::= { v5900 18 }
v5908L	    OBJECT IDENTIFIER ::= { v5900 19 }

v5924N	    OBJECT IDENTIFIER ::= { v5900 20 }
v5924C	    OBJECT IDENTIFIER ::= { v5900 21 }
v5948	    OBJECT IDENTIFIER ::= { v5900 22 }
v5924MD         OBJECT IDENTIFIER ::= { v5900 23 }  
v5924P          OBJECT IDENTIFIER ::= { v5900 24 }
v5908P          OBJECT IDENTIFIER ::= { v5900 25 }
v5924C-R        OBJECT IDENTIFIER ::= { v5900 26 }
smc7824MVSW    OBJECT IDENTIFIER ::= { v5900 27 }
v5924O         OBJECT IDENTIFIER ::= { v5900 28 }  
v5908O         OBJECT IDENTIFIER ::= { v5900 29 }  
v5904         OBJECT IDENTIFIER ::= { v5900 30 }  
v5906         OBJECT IDENTIFIER ::= { v5900 31 }  
v5912         OBJECT IDENTIFIER ::= { v5900 32 }  
v5917         OBJECT IDENTIFIER ::= { v5900 33 }  
v5916T         OBJECT IDENTIFIER ::= { v5900 34 }  

h645s  OBJECT IDENTIFIER ::= { hModel 1}

-- ADSL
v5800       OBJECT IDENTIFIER ::= { dasanXDSL 2 }

v5809       OBJECT IDENTIFIER ::= { v5800 1 }
v5824       OBJECT IDENTIFIER ::= { v5800 2 }
v5848       OBJECT IDENTIFIER ::= { v5800 3 }
v5810       OBJECT IDENTIFIER ::= { v5800 5 }
v5817       OBJECT IDENTIFIER ::= { v5800 6 }

hiX5630M600     OBJECT IDENTIFIER ::= { v5800 15 }
hiX5635M1200    OBJECT IDENTIFIER ::= { v5800 16 }
hiX5625M400	OBJECT IDENTIFIER ::= { v5800 17 }
                
-- voip tmp                 
v5804SV     OBJECT IDENTIFIER ::= { v5800 13 }
                
-- dasanGEPON	
v6600       OBJECT IDENTIFIER ::= { dasanGEPON	1 }
v6608       OBJECT IDENTIFIER ::= { v6600 1 }
v6616       OBJECT IDENTIFIER ::= { v6600 2 }
v6624       OBJECT IDENTIFIER ::= { v6600 3 }

v6500       OBJECT IDENTIFIER ::= { dasanGEPON	2 }
v6501T      OBJECT IDENTIFIER ::= { v6500 1 }
v6501P      OBJECT IDENTIFIER ::= { v6500 2 }
v6504T      OBJECT IDENTIFIER ::= { v6500 3 }
v6504P      OBJECT IDENTIFIER ::= { v6500 4 }     

-- dasanAccessGateWay(VoIP)  
v4600       OBJECT IDENTIFIER ::= { dasanAccessGateway 1 }
v4604S      OBJECT IDENTIFIER ::= { v4600 1 }
v4610S      OBJECT IDENTIFIER ::= { v4600 2 }
v4664       OBJECT IDENTIFIER ::= { v4600 3 }
v4602       OBJECT IDENTIFIER ::= { v4600 4 }

-- SURPASS hi 
hiD         OBJECT IDENTIFIER ::= { surpass 1 }
hiX         OBJECT IDENTIFIER ::= { surpass 2 }
aHub	    OBJECT IDENTIFIER ::= { surpass 3 }

hiD6610-S212       OBJECT IDENTIFIER ::= { hiD 1 }  -- V1824
hiD6610-S213       OBJECT IDENTIFIER ::= { hiD 2 }
hiD6610-S214       OBJECT IDENTIFIER ::= { hiD 3 }
hiD6610-S215       OBJECT IDENTIFIER ::= { hiD 4 }  -- V2824
hiD6610-S224       OBJECT IDENTIFIER ::= { hiD 5 }  -- V1824OP
hiD6610-S312       OBJECT IDENTIFIER ::= { hiD 6 }  -- V6424
hiD6610-S322       OBJECT IDENTIFIER ::= { hiD 7 }  -- V6424F

hiD6610-S311       OBJECT IDENTIFIER ::= { hiD 11 }
hiD6610-S321       OBJECT IDENTIFIER ::= { hiD 12 }  
hiD6610-S331       OBJECT IDENTIFIER ::= { hiD 13 }

hiD6615-S323       OBJECT IDENTIFIER ::= { hiD 16 }
hiD6615-S223       OBJECT IDENTIFIER ::= { hiD 17 }  -- V5212G
hiD6615-S324       OBJECT IDENTIFIER ::= { hiD 18 }  -- V5424G
hiD6615-S411       OBJECT IDENTIFIER ::= { hiD 19 }  -- V5548G

hiD6620-S312       OBJECT IDENTIFIER ::= { hiD 21 }
hiD6620-S313       OBJECT IDENTIFIER ::= { hiD 22 }
hiD6620-S332       OBJECT IDENTIFIER ::= { hiD 23 }
hiD6620-S335       OBJECT IDENTIFIER ::= { hiD 24 }
hiD6620-S336       OBJECT IDENTIFIER ::= { hiD 25 }

hiD6625-S333       OBJECT IDENTIFIER ::= { hiD 31 }
hiD6625-S334       OBJECT IDENTIFIER ::= { hiD 32 }

hiD6615-S331       OBJECT IDENTIFIER ::= { hiD 33 }  -- V5504XG
hiD6615-S325       OBJECT IDENTIFIER ::= { hiD 34 }  -- V5524XG
hiD6615-S332       OBJECT IDENTIFIER ::= { hiD 35 }  -- V6524G
hiD6615-S511       OBJECT IDENTIFIER ::= { hiD 36 }  -- V5848G
hiD6608-V5924C-R   OBJECT IDENTIFIER ::= { hiD 37 }  -- V5924CR

hiD6615-S540       OBJECT IDENTIFIER ::= { hiD 38 }  -- V8240
hiD6615-S611       OBJECT IDENTIFIER ::= { hiD 39 }  -- V8272

hiD6615-S340       OBJECT IDENTIFIER ::= { hiD 40 }  -- V6744XG
hiD6615-S344       OBJECT IDENTIFIER ::= { hiD 41 }  -- V6748XG

hiX5620-A50       OBJECT IDENTIFIER ::= { hiX 1 }
hiX5620-V25       OBJECT IDENTIFIER ::= { hiX 2 }
hiX5620-V24       OBJECT IDENTIFIER ::= { hiX 3 } 
hiX5430		  OBJECT IDENTIFIER ::= { hiX 4 }	-- V5724G
hiX5750		  OBJECT IDENTIFIER ::= { hiX 5 }       -- V5848G

--hiX5630-M600      OBJECT IDENTIFIER ::= { hiX 11 }
--hiX5635-M1200     OBJECT IDENTIFIER ::= { hiX 12 }
--hiX5630V-M600V    OBJECT IDENTIFIER ::= { hiX 13 }
--hiX5625-M400	  OBJECT IDENTIFIER ::= { hiX 14 }

aHub4A		OBJECT IDENTIFIER ::= { aHub 1 }  
aHub3B		OBJECT IDENTIFIER ::= { aHub 2 }  
  

-- Router 
v1500  OBJECT IDENTIFIER ::= { dasanRouter 1 }
v2500  OBJECT IDENTIFIER ::= { dasanRouter 2 }
v2600  OBJECT IDENTIFIER ::= { dasanRouter 3 }
v3100  OBJECT IDENTIFIER ::= { dasanRouter 4 }
v3300  OBJECT IDENTIFIER ::= { dasanRouter 5 }

v3104  OBJECT IDENTIFIER ::= { v3100 1 }
v3108  OBJECT IDENTIFIER ::= { v3100 2 }
v3112  OBJECT IDENTIFIER ::= { v3100 3 }
v3302  OBJECT IDENTIFIER ::= { v3300 1 }       

v1501  OBJECT IDENTIFIER ::= { v1500 1 }
v1502T OBJECT IDENTIFIER ::= { v1500 2 }

v2501  OBJECT IDENTIFIER ::= { v2500 1 }
v2501T OBJECT IDENTIFIER ::= { v2500 2 }
v2502T OBJECT IDENTIFIER ::= { v2500 3 }
v2503  OBJECT IDENTIFIER ::= { v2500 4 }

v2602T OBJECT IDENTIFIER ::= { v2600 1 }
v2602D OBJECT IDENTIFIER ::= { v2600 2 }
v2608T OBJECT IDENTIFIER ::= { v2600 3 }
v2602A OBJECT IDENTIFIER ::= { v2600 4 }

-- wireless
wirelessAC   OBJECT IDENTIFIER ::= { wireless 1 }
wirelessAP   OBJECT IDENTIFIER ::= { wireless 2 }

w7200 	     OBJECT IDENTIFIER ::= { wirelessAC 1 }
w7300        OBJECT IDENTIFIER ::= { wirelessAC 2 }

w110         OBJECT IDENTIFIER ::= { wirelessAP 1 }
w120         OBJECT IDENTIFIER ::= { wirelessAP 2 }

END
