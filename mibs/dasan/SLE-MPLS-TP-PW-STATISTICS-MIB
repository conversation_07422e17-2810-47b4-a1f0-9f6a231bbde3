--
-- sle-mpls-tp-pw-statistics-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, January 21, 2016 at 14:29:43
--

	SLE-MPLS-TP-PW-STATISTICS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			PwIDType			
				FROM PW-TC-STD-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			SnmpAdminString			
				FROM SNMP-FRAMEWORK-MIB			
			zeroDotZero, TimeTicks, Unsigned32, <PERSON><PERSON><PERSON>32, Counter64, 
			OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpPwStats MODULE-IDENTITY 
			LAST-UPDATED "201501280000Z"		-- January 28, 2015 at 00:00 GMT
			ORGANIZATION 
				"Multiprotocol Label Switching (MPLS) Working Group"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				Dong<PERSON><PERSON> Shin (Chris)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"This mib contains the managed objects for PW statistics.
				It shows the statistics of the PW from it created. Will 
				display only on the PE."
			REVISION "201501280000Z"		-- January 28, 2015 at 00:00 GMT
			DESCRIPTION 
				"SLE PW Statistics mib."
			::= { sleMpls 21 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT IDENTIFIER ::= { sleMgmt 16 }

		
		sleMplsTpPwStatsTable OBJECT IDENTIFIER ::= { sleMplsTpPwStats 1 }

		
		sleMplsTpPwStatsInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpPwStatsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table contains information about the PW statistics."
			::= { sleMplsTpPwStatsTable 1 }

		
		sleMplsTpPwStatsInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpPwStatsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in this table represents the statistics of PW.
				The PW created by network administator or SNMP agent as 
				instructed by the MPLS."
			INDEX { sleMplsTpPwStatsInfoPwId }
			::= { sleMplsTpPwStatsInfoTable 1 }

		
		SleMplsTpPwStatsInfoEntry ::=
			SEQUENCE { 
				sleMplsTpPwStatsInfoPwId
					PwIDType,
				sleMplsTpPwStatsInfoPwName
					SnmpAdminString,
				sleMplsTpPwStatsInfoTxPkts
					Counter64,
				sleMplsTpPwStatsInfoRxPkts
					Counter64,
				sleMplsTpPwStatsInfoTxBytes
					Counter64,
				sleMplsTpPwStatsInfoRxBytes
					Counter64
			 }

		sleMplsTpPwStatsInfoPwId OBJECT-TYPE
			SYNTAX PwIDType (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Pw ID."
			::= { sleMplsTpPwStatsInfoEntry 1 }

		
		sleMplsTpPwStatsInfoPwName OBJECT-TYPE
			SYNTAX SnmpAdminString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is displaying PW NAME."
			::= { sleMplsTpPwStatsInfoEntry 2 }

		
		sleMplsTpPwStatsInfoTxPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Transmitted packet count."
			::= { sleMplsTpPwStatsInfoEntry 3 }

		
		sleMplsTpPwStatsInfoRxPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received packet count."
			::= { sleMplsTpPwStatsInfoEntry 4 }

		
		sleMplsTpPwStatsInfoTxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Transmitted bytes count."
			::= { sleMplsTpPwStatsInfoEntry 5 }

		
		sleMplsTpPwStatsInfoRxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received bytes count"
			::= { sleMplsTpPwStatsInfoEntry 6 }

		
		sleMplsTpPwStatsControl OBJECT IDENTIFIER ::= { sleMplsTpPwStatsTable 2 }

		
		sleMplsTpPwStatsControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setToClearPwStats(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be 
				modified in the Vrf table. For each read-write column of Vrf 
				table, a Set Operation control value is added in this object."
			::= { sleMplsTpPwStatsControl 1 }

		
		sleMplsTpPwStatsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpPwStatsControl 2 }

		
		sleMplsTpPwStatsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured 
				for every control table."
			::= { sleMplsTpPwStatsControl 3 }

		
		sleMplsTpPwStatsControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpPwStatsControl 4 }

		
		sleMplsTpPwStatsReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpPwStatsControl 5 }

		
		sleMplsTpPwStatsControlPwId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW index.
				
				Index zero to clear all the VPWS statistics. Greater than zero to
				clear the specific VPWS statistics.
				"
			::= { sleMplsTpPwStatsControl 6 }

		
	
	END

--
-- sle-mpls-tp-pw-statistics-mib.mib
--
