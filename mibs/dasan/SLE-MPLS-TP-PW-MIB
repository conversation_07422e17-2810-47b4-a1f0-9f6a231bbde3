--
-- sle-mpls-tp-pw-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, February 05, 2016 at 10:43:40
--

	SLE-MPLS-TP-PW-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			IANAPwTypeTC			
				FROM IANA-PWE3-MIB			
			InterfaceIndexOrZero			
				FROM IF-MIB			
			MplsCcId, MplsIccId			
				FROM MPLS-TC-EXT-STD-MIB			
			MplsLabel			
				FROM MPLS-TC-STD-MIB			
			PwIDType, PwGroupID			
				FROM PW-TC-STD-MIB			
			<PERSON>lanIdOrAnyOrNone			
				FROM Q-BRIDGE-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			SnmpAdminString			
				FROM SNMP-FRAMEWORK-MIB			
			transmission, TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 
			O<PERSON><PERSON><PERSON>T-TYP<PERSON>, MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpPw MODULE-IDENTITY 
			LAST-UPDATED "201511030000Z"		-- November 03, 2015 at 00:00 GMT
			ORGANIZATION 
				"Dasan Networks"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				DongChel Shin (Chris)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"This MIB module containg the managed object definition for
				MPLS-TP pseudowire(PW) operation.
				
				copyright (c) 2015 Dasan Networks and the persons identified
				as authors of the code. All rights reserved."
			REVISION "201511030000Z"		-- November 03, 2015 at 00:00 GMT
			DESCRIPTION 
				"Initial version."
			::= { sleMpls 15 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MPLS."
			::= { sleMgmt 16 }

		
		sleMplsTpPwCfg OBJECT IDENTIFIER ::= { sleMplsTpPw 1 }

		
		sleMplsTpPwCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpPwCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for configuring and
				status monitoring that is common to all service types
				and PSN types."
			::= { sleMplsTpPwCfg 1 }

		
		sleMplsTpPwCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpPwCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpPwCfgInfoId."
			INDEX { sleMplsTpPwCfgInfoId }
			::= { sleMplsTpPwCfgInfoTable 1 }

		
		SleMplsTpPwCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpPwCfgInfoId
					PwIDType,
				sleMplsTpPwCfgInfoName
					OCTET STRING,
				sleMplsTpPwCfgInfoOwner
					INTEGER,
				sleMplsTpPwCfgInfoType
					IANAPwTypeTC,
				sleMplsTpPwCfgInfoControlWord
					INTEGER,
				sleMplsTpPwCfgInfoPeerIdType
					INTEGER,
				sleMplsTpPwCfgInfoPeerGolbalId
					Unsigned32,
				sleMplsTpPwCfgInfoPeerCc
					MplsCcId,
				sleMplsTpPwCfgInfoPeerIcc
					MplsIccId,
				sleMplsTpPwCfgInfoPeerNodeId
					IpAddress,
				sleMplsTpPwCfgInfoPeerAcId
					Unsigned32,
				sleMplsTpPwCfgInfoGroupName
					SnmpAdminString,
				sleMplsTpPwCfgInfoGroupId
					PwGroupID,
				sleMplsTpPwCfgInfoOperMode
					INTEGER,
				sleMplsTpPwCfgInfoSvlanId
					VlanIdOrAnyOrNone,
				sleMplsTpPwCfgInfoPwStatus
					INTEGER,
				sleMplsTpPwCfgInfoInlabel
					MplsLabel,
				sleMplsTpPwCfgInfoOutLabel
					MplsLabel,
				sleMplsTpPwCfgInfoTunnelName
					OCTET STRING,
				sleMplsTpPwCfgInfoAcInterfaceIndex
					InterfaceIndexOrZero,
				sleMplsTpPwCfgInfoVcStitchName
					OCTET STRING,
				sleMplsTpPwCfgInfoPriority
					INTEGER,
				sleMplsTpPwCfgInfostate
					INTEGER,
				sleMplsTpPwCfgInfoDescription
					OCTET STRING,
				sleMplsTpPwCfgInfoLocalRefreshTimer
					INTEGER,
				sleMplsTpPwCfgInfoQosServicePolicy
					OCTET STRING
			 }

		sleMplsTpPwCfgInfoId OBJECT-TYPE
			SYNTAX PwIDType (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Virtual Circuit ID."
			::= { sleMplsTpPwCfgInfoEntry 1 }

		
		sleMplsTpPwCfgInfoName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Virtual Circuit Name. It identifies an entry
				on this table."
			::= { sleMplsTpPwCfgInfoEntry 2 }

		
		sleMplsTpPwCfgInfoOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(1),
				pwIdFecSignaling(2),
				genFecSignaling(3),
				l2tpControlProtocol(4),
				other(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use to indentify the owner of the PW."
			::= { sleMplsTpPwCfgInfoEntry 3 }

		
		sleMplsTpPwCfgInfoType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Service type of the PW."
			::= { sleMplsTpPwCfgInfoEntry 4 }

		
		sleMplsTpPwCfgInfoControlWord OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Status of Control Word negotiation."
			::= { sleMplsTpPwCfgInfoEntry 5 }

		
		sleMplsTpPwCfgInfoPeerIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Peer Id Type"
			::= { sleMplsTpPwCfgInfoEntry 6 }

		
		sleMplsTpPwCfgInfoPeerGolbalId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Peer Global ID."
			::= { sleMplsTpPwCfgInfoEntry 7 }

		
		sleMplsTpPwCfgInfoPeerCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Peer Country Code(cc)."
			::= { sleMplsTpPwCfgInfoEntry 8 }

		
		sleMplsTpPwCfgInfoPeerIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Peer ICC."
			::= { sleMplsTpPwCfgInfoEntry 9 }

		
		sleMplsTpPwCfgInfoPeerNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Peer Node Id."
			::= { sleMplsTpPwCfgInfoEntry 10 }

		
		sleMplsTpPwCfgInfoPeerAcId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Peer Attachment circuit ID."
			::= { sleMplsTpPwCfgInfoEntry 11 }

		
		sleMplsTpPwCfgInfoGroupName OBJECT-TYPE
			SYNTAX SnmpAdminString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Local Group name."
			::= { sleMplsTpPwCfgInfoEntry 12 }

		
		sleMplsTpPwCfgInfoGroupId OBJECT-TYPE
			SYNTAX PwGroupID
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Local Group Id."
			::= { sleMplsTpPwCfgInfoEntry 13 }

		
		sleMplsTpPwCfgInfoOperMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				pwRawMode(1),
				pwTaggedMode(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Current Operation mode."
			::= { sleMplsTpPwCfgInfoEntry 14 }

		
		sleMplsTpPwCfgInfoSvlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW SVlan. This should be a valid VLAN-ID when 
				sleMplsTpPwCfgOperMode is pwTaggedMode(2)."
			DEFVAL { 0 }
			::= { sleMplsTpPwCfgInfoEntry 15 }

		
		sleMplsTpPwCfgInfoPwStatus OBJECT-TYPE
			SYNTAX INTEGER { enable(1) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the pw status tlv."
			::= { sleMplsTpPwCfgInfoEntry 17 }

		
		sleMplsTpPwCfgInfoInlabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW FIB Entry OutLabel."
			::= { sleMplsTpPwCfgInfoEntry 18 }

		
		sleMplsTpPwCfgInfoOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW FIB entry InLabel."
			::= { sleMplsTpPwCfgInfoEntry 19 }

		
		sleMplsTpPwCfgInfoTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0 | 1..16))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW FIB Entry Tunnel Name."
			::= { sleMplsTpPwCfgInfoEntry 20 }

		
		sleMplsTpPwCfgInfoAcInterfaceIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW FIB Entry Interface Index."
			::= { sleMplsTpPwCfgInfoEntry 21 }

		
		sleMplsTpPwCfgInfoVcStitchName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Multi-segment PW FIB entry stitch name."
			::= { sleMplsTpPwCfgInfoEntry 22 }

		
		sleMplsTpPwCfgInfoPriority OBJECT-TYPE
			SYNTAX INTEGER
				{
				primary(1),
				secondary(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Priority. In MPLS-TP case, primary only supported."
			::= { sleMplsTpPwCfgInfoEntry 23 }

		
		sleMplsTpPwCfgInfostate OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Pw state "
			DEFVAL { down }
			::= { sleMplsTpPwCfgInfoEntry 24 }

		
		sleMplsTpPwCfgInfoDescription OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Description."
			::= { sleMplsTpPwCfgInfoEntry 25 }

		
		sleMplsTpPwCfgInfoLocalRefreshTimer OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"pw local refresh timer."
			::= { sleMplsTpPwCfgInfoEntry 26 }

		
		sleMplsTpPwCfgInfoQosServicePolicy OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"pw qos input service policy."
			::= { sleMplsTpPwCfgInfoEntry 27 }

		
		sleMplsTpPwCfgControl OBJECT IDENTIFIER ::= { sleMplsTpPwCfg 2 }

		
		sleMplsTpPwCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createPw(1),
				createVpwsWithGroupId(2),
				createRawModeVpwsWithGroupId(3),
				createQInQVpwsWithGroupId(4),
				createRawModeVpws(5),
				createQInQVpws(6),
				deletePw(7),
				setPwFibEntry(8),
				setPwVcStitchFibEntry(9),
				unsetPwFibEntry(10),
				setPwDescription(11),
				createVpwswithPwStatus(12),
				setPwQosServicePolicy(13),
				unsetPwQosServicePolicy(14)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the Pw table. For each read-write column of Pw table, a Set 
				Operation control value is added in this object."
			::= { sleMplsTpPwCfgControl 1 }

		
		sleMplsTpPwCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpPwCfgControl 2 }

		
		sleMplsTpPwCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpPwCfgControl 3 }

		
		sleMplsTpPwCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpPwCfgControl 4 }

		
		sleMplsTpPwCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpPwCfgControl 5 }

		
		sleMplsTpPwCfgControlId OBJECT-TYPE
			SYNTAX PwIDType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Virtual Circuit ID."
			::= { sleMplsTpPwCfgControl 6 }

		
		sleMplsTpPwCfgControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Virtual Circuit Name. It identifies an entry
				on this table."
			::= { sleMplsTpPwCfgControl 7 }

		
		sleMplsTpPwCfgControlOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(1),
				pwIdFecSignaling(2),
				genFecSignaling(3),
				l2tpControlProtocol(4),
				other(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use to indentify the owner of the PW."
			::= { sleMplsTpPwCfgControl 8 }

		
		sleMplsTpPwCfgControlPeerIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Peer Id Type"
			::= { sleMplsTpPwCfgControl 9 }

		
		sleMplsTpPwCfgControlPeerGolbalId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Peer Global ID."
			::= { sleMplsTpPwCfgControl 10 }

		
		sleMplsTpPwCfgControlPeerCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Peer Country Code(cc)."
			::= { sleMplsTpPwCfgControl 11 }

		
		sleMplsTpPwCfgControlPeerIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Peer ICC."
			::= { sleMplsTpPwCfgControl 12 }

		
		sleMplsTpPwCfgControlPeerNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Peer Node Id."
			::= { sleMplsTpPwCfgControl 13 }

		
		sleMplsTpPwCfgControlPeerAcId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Peer Attachment circuit ID."
			::= { sleMplsTpPwCfgControl 14 }

		
		sleMplsTpPwCfgControlGroupName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Local Group name."
			::= { sleMplsTpPwCfgControl 15 }

		
		sleMplsTpPwCfgControlGroupId OBJECT-TYPE
			SYNTAX PwGroupID
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Local Group Id."
			::= { sleMplsTpPwCfgControl 16 }

		
		sleMplsTpPwCfgControlOperMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				pwRawMode(1),
				pwTaggedMode(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Current Operation mode."
			::= { sleMplsTpPwCfgControl 17 }

		
		sleMplsTpPwCfgControlSvlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW SVlan. This should be a valid VLAN-ID when 
				sleMplsTpPwCfgOperMode is pwTaggedMode(2)."
			::= { sleMplsTpPwCfgControl 18 }

		
		sleMplsTpPwCfgControlInlabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW FIB Entry OutLabel."
			::= { sleMplsTpPwCfgControl 20 }

		
		sleMplsTpPwCfgControlOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW FIB entry InLabel."
			::= { sleMplsTpPwCfgControl 21 }

		
		sleMplsTpPwCfgControlTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0 | 1..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW FIB Entry Tunnel Name."
			::= { sleMplsTpPwCfgControl 22 }

		
		sleMplsTpPwCfgControlAcInterfaceIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW FIB Entry Interface Index."
			::= { sleMplsTpPwCfgControl 23 }

		
		sleMplsTpPwCfgControlVcStitchName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Multi-segmen PW FIB Entry stitch name."
			::= { sleMplsTpPwCfgControl 24 }

		
		sleMplsTpPwCfgControlDescription OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Description."
			::= { sleMplsTpPwCfgControl 26 }

		
		sleMplsTpPwCfgControlPwStatus OBJECT-TYPE
			SYNTAX INTEGER { enable(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the pw status tlv."
			::= { sleMplsTpPwCfgControl 27 }

		
		sleMplsTpPwCfgControlLocalRefreshTimer OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Status Local Refresh Timer."
			::= { sleMplsTpPwCfgControl 28 }

		
		sleMplsTpPwCfgControlQosServicePolicy OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW qos input service policy."
			::= { sleMplsTpPwCfgControl 29 }

		
		sleMplsTpPwAcCfg OBJECT IDENTIFIER ::= { sleMplsTpPw 2 }

		
		sleMplsTpPwAcCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpPwAcCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for binding the PW on 
				the MPLS-TP service provider interface."
			::= { sleMplsTpPwAcCfg 1 }

		
		sleMplsTpPwAcCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpPwAcCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpPwIfIndexInfo."
			INDEX { sleMplsTpPwAcCfgInfoIndex }
			::= { sleMplsTpPwAcCfgInfoTable 1 }

		
		SleMplsTpPwAcCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpPwAcCfgInfoIndex
					InterfaceIndexOrZero,
				sleMplsTpPwAcCfgInfoLocalAcId
					Unsigned32
			 }

		sleMplsTpPwAcCfgInfoIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Customer-facing/service-provider Interface Index."
			::= { sleMplsTpPwAcCfgInfoEntry 1 }

		
		sleMplsTpPwAcCfgInfoLocalAcId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Local AC Id of MPLS-TP service provider interface."
			::= { sleMplsTpPwAcCfgInfoEntry 2 }

		
		sleMplsTpPwAcCfgControl OBJECT IDENTIFIER ::= { sleMplsTpPwAcCfg 2 }

		
		sleMplsTpPwAcCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setPwLocalACId(1),
				unsetPwIfLocalACId(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpPwIfCfg table. For each read-write column of 
				sleMplsTpPwIfCfg table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpPwAcCfgControl 1 }

		
		sleMplsTpPwAcCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpPwAcCfgControl 2 }

		
		sleMplsTpPwAcCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpPwAcCfgControl 3 }

		
		sleMplsTpPwAcCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpPwAcCfgControl 4 }

		
		sleMplsTpPwAcCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpPwAcCfgControl 5 }

		
		sleMplsTpPwAcCfgControlIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Customer-facing/service-provider Interface Index."
			::= { sleMplsTpPwAcCfgControl 6 }

		
		sleMplsTpPwAcCfgControlLocalAcId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Local AC Id of MPLS-TP service provider interface."
			::= { sleMplsTpPwAcCfgControl 7 }

		
		sleMplsTpPwIfCfg OBJECT IDENTIFIER ::= { sleMplsTpPw 3 }

		
		sleMplsTpPwIfCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpPwIfCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for binding the PW on 
				the MPLS-TP service provider interface."
			::= { sleMplsTpPwIfCfg 1 }

		
		sleMplsTpPwIfCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpPwIfCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpPwIfIndexInfo."
			INDEX { sleMplsTpPwIfCfgInfoIndex, sleMplsTpPwIfCfgInfoVcName }
			::= { sleMplsTpPwIfCfgInfoTable 1 }

		
		SleMplsTpPwIfCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpPwIfCfgInfoIndex
					InterfaceIndexOrZero,
				sleMplsTpPwIfCfgInfoVcName
					OCTET STRING,
				sleMplsTpPwIfCfgInfoServiceType
					IANAPwTypeTC,
				sleMplsTpPwIfCfgInfoVlanId
					VlanIdOrAnyOrNone,
				sleMplsTpPwIfCfgInfoPriority
					INTEGER,
				sleMplsTpPwIfCfgInfoSVlanId
					VlanIdOrAnyOrNone,
				sleMplsTpPwIfCfgInfoInnerVlanId
					VlanIdOrAnyOrNone,
				sleMplsTpPwIfCfgInfoAction
					INTEGER
			 }

		sleMplsTpPwIfCfgInfoIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Customer-facing/service-provider Interface Index."
			::= { sleMplsTpPwIfCfgInfoEntry 1 }

		
		sleMplsTpPwIfCfgInfoVcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PW Name which bind on interface."
			::= { sleMplsTpPwIfCfgInfoEntry 2 }

		
		sleMplsTpPwIfCfgInfoServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Service Type of PW bind on interface."
			::= { sleMplsTpPwIfCfgInfoEntry 3 }

		
		sleMplsTpPwIfCfgInfoVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VLAN ID of bind PW. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpPwIfCfgInfoEntry 4 }

		
		sleMplsTpPwIfCfgInfoPriority OBJECT-TYPE
			SYNTAX INTEGER
				{
				primary(1),
				secondary(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Priority of bind PW."
			::= { sleMplsTpPwIfCfgInfoEntry 5 }

		
		sleMplsTpPwIfCfgInfoSVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VLAN ID of bind PW. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpPwIfCfgInfoEntry 6 }

		
		sleMplsTpPwIfCfgInfoInnerVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VLAN ID of bind PW. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpPwIfCfgInfoEntry 7 }

		
		sleMplsTpPwIfCfgInfoAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				noOp(1),
				addSvlan(2),
				remove(3),
				replace(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Action of Pw."
			::= { sleMplsTpPwIfCfgInfoEntry 8 }

		
		sleMplsTpPwIfCfgControl OBJECT IDENTIFIER ::= { sleMplsTpPwIfCfg 2 }

		
		sleMplsTpPwIfCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setPwBindwithRaw(1),
				setPwBindwithTagged(2),
				unsetPwBindwithRaw(3),
				unsetPwBindwithTagged(4),
				setPWBindWithRawSvlanIdAction(5),
				setPWBindWithRawSvlanIdTPIDActionPriority(6),
				setPWBindWithTaggedTpidAction(7),
				setPwbindWithTaggedTpidActionPriority(8),
				setPWBindWithQinQ(9),
				setPWBindWithQinQPrority(10),
				setPWBindWithQinQTpidAction(11),
				setPWBindWithQinQTpidActionPriority(12)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpPwIfCfg table. For each read-write column of 
				sleMplsTpPwIfCfg table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpPwIfCfgControl 1 }

		
		sleMplsTpPwIfCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpPwIfCfgControl 2 }

		
		sleMplsTpPwIfCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpPwIfCfgControl 3 }

		
		sleMplsTpPwIfCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpPwIfCfgControl 4 }

		
		sleMplsTpPwIfCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpPwIfCfgControl 5 }

		
		sleMplsTpPwIfCfgControlIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Customer-facing/service-provider Interface Index."
			::= { sleMplsTpPwIfCfgControl 6 }

		
		sleMplsTpPwIfCfgControlVcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PW Name which bind on interface."
			::= { sleMplsTpPwIfCfgControl 7 }

		
		sleMplsTpPwIfCfgControlServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Service Type of PW bind on interface."
			::= { sleMplsTpPwIfCfgControl 8 }

		
		sleMplsTpPwIfCfgControlVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VLAN ID of bind PW. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpPwIfCfgControl 9 }

		
		sleMplsTpPwIfCfgControlPriority OBJECT-TYPE
			SYNTAX INTEGER
				{
				primary(1),
				secondary(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Priority of bind PW."
			::= { sleMplsTpPwIfCfgControl 10 }

		
		sleMplsTpPwIfCfgControlSVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VLAN ID of bind PW. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpPwIfCfgControl 11 }

		
		sleMplsTpPwIfCfgControlInnerVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VLAN ID of bind PW. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpPwIfCfgControl 12 }

		
		sleMplsTpPwIfCfgControlAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				noOp(1),
				addSvlan(2),
				remove(3),
				replace(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Action of Pw."
			::= { sleMplsTpPwIfCfgControl 13 }

		
		sleMplsTpMsPwCfg OBJECT IDENTIFIER ::= { sleMplsTpPw 4 }

		
		sleMplsTpMsPwCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpMsPwCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for Multisegment
				PW."
			::= { sleMplsTpMsPwCfg 1 }

		
		sleMplsTpMsPwCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpMsPwCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpMsPwCfgName."
			INDEX { sleMplsTpMsPwCfgInfoName }
			::= { sleMplsTpMsPwCfgInfoTable 1 }

		
		SleMplsTpMsPwCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpMsPwCfgInfoName
					OCTET STRING,
				sleMplsTpMsPwCfgInfoSegment1Name
					OCTET STRING,
				sleMplsTpMsPwCfgInfoSegment2Name
					OCTET STRING,
				sleMplsTpMsPwCfgInfoDescription
					OCTET STRING,
				sleMplsTpMsPwCfgInfoMtu
					INTEGER,
				sleMplsTpMsPwCfgInfoServiceType
					IANAPwTypeTC,
				sleMplsTpMsPwCfgInfoVlanId
					VlanIdOrAnyOrNone
			 }

		sleMplsTpMsPwCfgInfoName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Multi-segment PW stitch name."
			::= { sleMplsTpMsPwCfgInfoEntry 1 }

		
		sleMplsTpMsPwCfgInfoSegment1Name OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Segment 1."
			::= { sleMplsTpMsPwCfgInfoEntry 2 }

		
		sleMplsTpMsPwCfgInfoSegment2Name OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW segment 2."
			::= { sleMplsTpMsPwCfgInfoEntry 3 }

		
		sleMplsTpMsPwCfgInfoDescription OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..80))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Multi-segment stitch description."
			::= { sleMplsTpMsPwCfgInfoEntry 4 }

		
		sleMplsTpMsPwCfgInfoMtu OBJECT-TYPE
			SYNTAX INTEGER (68..9216)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"MTU size.
				
				Interface MTU size  - needed when one vc is not signaled.
				"
			::= { sleMplsTpMsPwCfgInfoEntry 5 }

		
		sleMplsTpMsPwCfgInfoServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Service type."
			::= { sleMplsTpMsPwCfgInfoEntry 6 }

		
		sleMplsTpMsPwCfgInfoVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VLAN Id."
			::= { sleMplsTpMsPwCfgInfoEntry 7 }

		
		sleMplsTpMsPwCfgControl OBJECT IDENTIFIER ::= { sleMplsTpMsPwCfg 2 }

		
		sleMplsTpMsPwCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createMsPw(1),
				createMsPwWithMtuAndServiceType(2),
				deleteMsPw(3),
				setMsPwDescription(4),
				unsetMsPwDescription(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpMsPwCfg table. For each read-write column of 
				sleMplsTpMsPwCfg table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpMsPwCfgControl 1 }

		
		sleMplsTpMsPwCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpMsPwCfgControl 2 }

		
		sleMplsTpMsPwCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpMsPwCfgControl 3 }

		
		sleMplsTpMsPwCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpMsPwCfgControl 4 }

		
		sleMplsTpMsPwCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpMsPwCfgControl 5 }

		
		sleMplsTpMsPwCfgControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Multi-segment PW stitch name."
			::= { sleMplsTpMsPwCfgControl 6 }

		
		sleMplsTpMsPwCfgControlSegment1Name OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Segment 1."
			::= { sleMplsTpMsPwCfgControl 7 }

		
		sleMplsTpMsPwCfgControlSegment2Name OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PW Segment 2."
			::= { sleMplsTpMsPwCfgControl 8 }

		
		sleMplsTpMsPwCfgControlDescription OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..80))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Multi-segment stitch description."
			::= { sleMplsTpMsPwCfgControl 9 }

		
		sleMplsTpMsPwCfgControlMtu OBJECT-TYPE
			SYNTAX INTEGER (68..9216)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"MTU size.
				
				Interface MTU size  - needed when one vc is not signaled."
			::= { sleMplsTpMsPwCfgControl 10 }

		
		sleMplsTpMsPwCfgControlServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Service Type."
			::= { sleMplsTpMsPwCfgControl 11 }

		
		sleMplsTpMsPwCfgControlVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VLAN Id."
			::= { sleMplsTpMsPwCfgControl 12 }

		
	
	END

--
-- sle-mpls-tp-pw-mib.mib
--
