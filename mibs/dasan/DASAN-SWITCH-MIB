--
-- dasan-switch-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, November 25, 2011 at 19:57:53
--

--  *****************************************************************
-- dasanSwitch MIB -  The MIB for Switch Product
-- 
-- April 2001, <PERSON>hee Lee
-- December 2002, <PERSON><PERSON><PERSON>
-- May 2003, <PERSON><PERSON><PERSON>                                    
-- Feb 2004, <PERSON><PERSON><PERSON>  
-- May 2004, <PERSON><PERSON>
-- May 2004, <PERSON><PERSON><PERSON><PERSON>
-- 
-- Copyright (c) 2001, 2002, 2003 by Dasan Co., Ltd.
-- Copyright (c) 2004  Dasan Networks Inc, 
-- All rights reserved.
-- *****************************************************************
-- ********************************************************************************
--  Log for dasanSwitch MIB.
-- 
--  July 23 2003
--    1. add 15mElapsedTimeSeconds & 1dayElapsedTimeSeconds in dsVdslPortTable.
--    2. add service profile(dsVdslPortUpServiceProfile, dsVdslPortDownServiceProfile) 
--       in dsVdslPortTable.
--    3. add service profile(dsVdslSlotUpServiceProfile, dsVdslSlotDownServicePrile)
--       in dsVdslSlotTable.           
--                     
--  Dec 19 2003
--    1. Add Vdsl profile         
--                           
--  Feb 13 2004
--    1. Set the value of MAX-ACCESS to obsolete between portProfile and portWanOperStatus.                                         
--      (Include both)
--    2. Add dsVdslPortModemReset to the dsVdslPortTable              
--   3. Add dsVdslSlotLIUReboot to the dsVdslSlotTable
-- 
--  Mar 8 2004
--    1. add User Mac table                                         
--  
--  Mar 12 2004    
--    1. Add hardwareVersion to dsSwitchSystem.
--    2. add dsVdslPortLineActiveTimePrev1Day / dsVdslPortLineActiveTimeCurr1Day to dsVdslPortTable.
--                            
--  April 7 2004
--    1. add dsSwitchAtTable that support ARP information with physical port.
-- 
--  April 16 2004
--    1. change description of portIngress: including QAM 20 case.
-- 
--  May 10 2004
--    1. add some PSDMaskLevel entries in dsVdslPortTable & dsVdslSlotTable.  
-- 
--  May 20 2004
--    1. Add dsTcTable 
--     
--  July 26 2005
--    1. Add dsUserMacAddress5 ~ 8 
-- 
--  July 30 2005
--    1. Add dsSerialNumber
--  
--  Dec 13 2005
--    1. Add dsFirmwareVersion
-- 
--  Dec 17 2005
--    1. change dsSerialNumber as DisplayString
-- 
--  Feb 15 2006
--    1. Add dsPortTrunkId
--    2. Add dsPortCRCCount     
-- 
--  March 21 2006
--    1. Add dsVdslPortStandard ~ dsVdslPortTCMAdmin
-- 
-- 
--  August 17 2006
--    1. Add dsHardwareAddress
-- 
-- 
--  June 24 2010
--    1. Add dsVdslCpeInfoTable
--    2. Change the type of dsVdslPortSuperFrameRxCount and dsVdslPortSuperFrameTxCount from INTEGER to Unsigned32
-- ********************************************************************************  

	DASAN-SWITCH-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			dasanSwitchMIBObjects, dsSubnetConfName			
				FROM DASAN-DHCP-MIB			
			dasanEvents, dasanMgmt			
				FROM DASAN-SMI			
			ifIndex			
				FROM IF-MIB			
			InetAddressType, InetAddress			
				FROM INET-ADDRESS-MIB			
			TimeTicks, IpAddress, Unsigned32, Gauge32, Counter32, 
			Counter64, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			DisplayString, MacAddress, PhysAddress, RowStatus			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.6296.9.1
		dasanSwitchMIB MODULE-IDENTITY 
			LAST-UPDATED "200107270000Z"		-- July 27, 2001 at 00:00 GMT
			ORGANIZATION 
				"Dasan Co., Ltd."
			CONTACT-INFO 
				"Dasan Co., Ltd."
			DESCRIPTION 
				"The MIB module to describe Switch product."
			::= { dasanMgmt 1 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.*******
		dsSwitchSystem OBJECT IDENTIFIER ::= { dasanSwitchMIBObjects 1 }

		
--   dsSwitchSystem 
-- 
		-- *******.4.1.6296.*******.1
		dsResetSystem OBJECT-TYPE
			SYNTAX INTEGER { reset(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Send system-reset to this system."
			::= { dsSwitchSystem 1 }

		
		-- *******.4.1.6296.*******.2
		dsWriteConfig OBJECT-TYPE
			SYNTAX INTEGER { write(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Write current running configuration to flash memory."
			::= { dsSwitchSystem 2 }

		
		-- *******.4.1.6296.*******.3
		dsOsVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The version of running OS."
			::= { dsSwitchSystem 3 }

		
		-- *******.4.1.6296.*******.4
		dsTftpServer OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The IP address or hostname of TFTP server."
			::= { dsSwitchSystem 4 }

		
		-- *******.4.1.6296.*******.5
		dsTftpFile OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Remote file name"
			::= { dsSwitchSystem 5 }

		
		-- *******.4.1.6296.*******.6
		dsTftpStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				idle(0),
				get(1),
				unknown(2),
				busy(3),
				failed(4),
				succeeded(5),
				abort(6),
				put(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"TFTP transaction status. To receive a file from remote server,
				get is used. To send OS or Configuration to remote server,
				put is used. Abort can be used to terminate running tftp client."
			::= { dsSwitchSystem 6 }

		
		-- *******.4.1.6296.*******.7
		dsTftpFileType OBJECT-TYPE
			SYNTAX INTEGER
				{
				operatingSystem(0),
				configuration(1),
				cpeNos(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"TFTP file type.
				The value of cpe-nos(2) is only available, in case of dsVdsl."
			::= { dsSwitchSystem 7 }

		
--   appended :-) <EMAIL> ,  Last updated  2003/02/03
-- 
		-- *******.4.1.6296.*******.8
		dsCpuLoad5s OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Avg. Value of CPU Load for 5 seconds"
			::= { dsSwitchSystem 8 }

		
		-- *******.4.1.6296.*******.9
		dsCpuLoad1m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Avg. Value of CPU Load for 1 minute"
			::= { dsSwitchSystem 9 }

		
		-- *******.4.1.6296.*******.10
		dsCpuLoad10m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Avg. Value of CPU Load for 10 minutes"
			::= { dsSwitchSystem 10 }

		
		-- *******.4.1.6296.*******.11
		dsCpuLoad5sisr OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Avg. Value of CPU Load for 5 seconds(interupt service routine)"
			::= { dsSwitchSystem 11 }

		
		-- *******.4.1.6296.*******.12
		dsCpuLoad1misr OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Avg. Value of CPU Load for 1 minute(interupt service routine)"
			::= { dsSwitchSystem 12 }

		
		-- *******.4.1.6296.*******.13
		dsCpuLoad10misr OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Avg. Value of CPU Load for 10 minutes(interupt service routine)"
			::= { dsSwitchSystem 13 }

		
		-- *******.4.1.6296.*******.14
		dsTotalMem OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Total memory of system"
			::= { dsSwitchSystem 14 }

		
		-- *******.4.1.6296.*******.15
		dsUsedMem OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Used memory of system"
			::= { dsSwitchSystem 15 }

		
		-- *******.4.1.6296.*******.16
		dsFreeMem OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Free memory of sytem"
			::= { dsSwitchSystem 16 }

		
		-- *******.4.1.6296.*******.17
		dsHardwareVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Hardware version of system."
			::= { dsSwitchSystem 17 }

		
		-- *******.4.1.6296.*******.18
		dsSoftwareCompatibility OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Software Compatibility"
			::= { dsSwitchSystem 18 }

		
		-- *******.4.1.6296.*******.19
		dsSerialNumber OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Serial number of system"
			::= { dsSwitchSystem 19 }

		
		-- *******.4.1.6296.*******.20
		dsFirmwareVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Firmware version of system"
			::= { dsSwitchSystem 20 }

		
		-- *******.4.1.6296.*******.21
		dsHardwareAddress OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Firmware version of system"
			::= { dsSwitchSystem 21 }

		
		-- *******.4.1.6296.*******.22
		dsTimeZone OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Time zone."
			::= { dsSwitchSystem 22 }

		
--  dsSystemNOSInfo  
-- 
		-- *******.4.1.6296.*******.30
		dsSystemNOSInfo OBJECT IDENTIFIER ::= { dsSwitchSystem 30 }

		
		-- *******.4.1.6296.*******.30.1
		dsSystemNOSTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsSystemNOSEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsSystemNOSEntry entries."
			::= { dsSystemNOSInfo 1 }

		
		-- *******.4.1.6296.*******.30.1.1
		dsSystemNOSEntry OBJECT-TYPE
			SYNTAX DsSystemNOSEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular ."
			INDEX { dsSystemNOSIndex }
			::= { dsSystemNOSTable 1 }

		
		DsSystemNOSEntry ::=
			SEQUENCE { 
				dsSystemNOSIndex
					INTEGER,
				dsSystemNOSName
					OCTET STRING,
				dsSystemNOSVersion
					OCTET STRING,
				dsSystemNOSSize
					INTEGER
			 }

		-- *******.4.1.6296.*******.********
		dsSystemNOSIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsSystemNOSEntry 1 }

		
		-- *******.4.1.6296.*******.********
		dsSystemNOSName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsSystemNOSEntry 2 }

		
		-- *******.4.1.6296.*******.********
		dsSystemNOSVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsSystemNOSEntry 3 }

		
		-- *******.4.1.6296.*******.********
		dsSystemNOSSize OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsSystemNOSEntry 4 }

		
		-- *******.4.1.6296.*******.30.2
		dsSystemUpgradeInfo OBJECT IDENTIFIER ::= { dsSystemNOSInfo 2 }

		
		-- *******.4.1.6296.*******.30.2.1
		dsSystemUpgradeMethod OBJECT-TYPE
			SYNTAX INTEGER
				{
				tftp(0),
				ftp(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"FTP or TFTP."
			::= { dsSystemUpgradeInfo 1 }

		
		-- *******.4.1.6296.*******.30.2.2
		dsSystemUpgradeAddressType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Determine whether using IPv4 or IPv6, etc."
			::= { dsSystemUpgradeInfo 2 }

		
		-- *******.4.1.6296.*******.30.2.3
		dsSystemUpgradeAddress OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The server address."
			::= { dsSystemUpgradeInfo 3 }

		
		-- *******.4.1.6296.*******.30.2.4
		dsSystemUpgradeUser OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The user name."
			::= { dsSystemUpgradeInfo 4 }

		
		-- *******.4.1.6296.*******.30.2.5
		dsSystemUpgradePasswd OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The password. In case of ftp, need password."
			::= { dsSystemUpgradeInfo 5 }

		
		-- *******.4.1.6296.*******.30.2.6
		dsSystemUpgradePath OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicate where NOS exist."
			::= { dsSystemUpgradeInfo 6 }

		
		-- *******.4.1.6296.*******.30.2.7
		dsSystemUpgradeStorage OBJECT-TYPE
			SYNTAX INTEGER
				{
				os1(1),
				os2(2),
				cpe(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The type of NOS."
			::= { dsSystemUpgradeInfo 7 }

		
		-- *******.4.1.6296.*******.30.2.8
		dsSystemUpgradeValue OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Additional information.
				ex) the slot-number of xDSL,... ."
			::= { dsSystemUpgradeInfo 8 }

		
		-- *******.4.1.6296.*******.30.2.9
		dsSystemUpgradeStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(0),
				idle(1),
				upload(2),
				download(3),
				busy(4),
				success(5),
				fail(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSystemUpgradeInfo 9 }

		
		-- *******.4.1.6296.*******.30.2.15
		dsSystemUpgradeNotification OBJECT IDENTIFIER ::= { dsSystemUpgradeInfo 15 }

		
		-- *******.4.1.6296.*******.*********
		dsSystemUpgraded NOTIFICATION-TYPE
			OBJECTS { dsSystemUpgradeMethod, dsSystemUpgradeAddressType, dsSystemUpgradeAddress, dsSystemUpgradePath, dsSystemUpgradeStorage, 
				dsSystemUpgradeStatus }
			STATUS current
			DESCRIPTION 
				"."
			::= { dsSystemUpgradeNotification 1 }

		
		-- *******.4.1.6296.*******
		dsSwitchModules OBJECT IDENTIFIER ::= { dasanSwitchMIBObjects 2 }

		
--  the Ports table
-- The Ports table contains All Ports Information on each Node.
		-- *******.4.1.6296.*******.4
		dsPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsPortEntry entries."
			::= { dsSwitchModules 4 }

		
		-- *******.4.1.6296.*******.4.1
		dsPortEntry OBJECT-TYPE
			SYNTAX DsPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular port."
			INDEX { dsPortModuleIndex, dsPortPortIndex }
			::= { dsPortTable 1 }

		
		DsPortEntry ::=
			SEQUENCE { 
				dsPortModuleIndex
					INTEGER,
				dsPortPortIndex
					INTEGER,
				dsPortType
					DisplayString,
				dsPortIfIndex
					OBJECT IDENTIFIER,
				dsPortPVID
					INTEGER,
				dsPortShared
					INTEGER,
				dsPortNego
					INTEGER,
				dsPortDuplex
					INTEGER,
				dsPortUpSpeed
					INTEGER,
				dsPortDownSpeed
					INTEGER,
				dsPortFlowControl
					INTEGER,
				dsPortIngressrate
					INTEGER,
				dsPortEgressrate
					INTEGER,
				dsPortInstallStatus
					INTEGER,
				dsPortCurrentHost
					INTEGER,
				dsPortMaxHost
					INTEGER,
				dsPortUpUtil5s
					INTEGER,
				dsPortUpUtil1m
					INTEGER,
				dsPortUpUtil10m
					INTEGER,
				dsPortDownUtil5s
					INTEGER,
				dsPortDownUtil1m
					INTEGER,
				dsPortDownUtil10m
					INTEGER,
				dsPortDescription
					DisplayString,
				dsPortWanAdminStatus
					INTEGER,
				dsPortWanOperStatus
					INTEGER,
				dsPortReset
					INTEGER,
				dsPortSpeed
					INTEGER,
				dsPortCurrUpTime
					TimeTicks,
				dsPortPrevUpTime
					TimeTicks,
				dsPortTrunkID
					INTEGER,
				dsPortCRCCount
					INTEGER,
				dsPortRateLimitUnit
					Counter32,
				dsPortRateLimitIngressThreshold
					Counter32,
				dsPortRateLimitEgressThreshold
					Counter32,
				dsPortCableLength
					DisplayString
			 }

		-- *******.4.1.6296.*******.4.1.1
		dsPortModuleIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsPortEntry 1 }

		
		-- *******.4.1.6296.*******.4.1.2
		dsPortPortIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				port.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsPortEntry 2 }

		
		-- *******.4.1.6296.*******.4.1.3
		dsPortType OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of communication port. This string defines the type
				of port."
			::= { dsPortEntry 3 }

		
		-- *******.4.1.6296.*******.4.1.4
		dsPortIfIndex OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"matching ifIndex oid which is defined in interface mib."
			::= { dsPortEntry 4 }

		
--  SYNTAX      INTEGER (SIZE (1..4094)) 
		-- *******.4.1.6296.*******.4.1.5
		dsPortPVID OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The PVID of port."
			::= { dsPortEntry 5 }

		
		-- *******.4.1.6296.*******.4.1.6
		dsPortShared OBJECT-TYPE
			SYNTAX INTEGER
				{
				enabled(1),
				disabled(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Shared status of port."
			::= { dsPortEntry 6 }

		
		-- *******.4.1.6296.*******.4.1.7
		dsPortNego OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(1),
				forced(2),
				other(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Nego mode of port."
			::= { dsPortEntry 7 }

		
		-- *******.4.1.6296.*******.4.1.8
		dsPortDuplex OBJECT-TYPE
			SYNTAX INTEGER
				{
				full(1),
				half(2),
				other(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The duplex status of port."
			::= { dsPortEntry 8 }

		
		-- *******.4.1.6296.*******.4.1.9
		dsPortUpSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Upstream speed of port. bits/second "
			::= { dsPortEntry 9 }

		
		-- *******.4.1.6296.*******.4.1.10
		dsPortDownSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Downstream speed of port. bits/second "
			::= { dsPortEntry 10 }

		
		-- *******.4.1.6296.*******.4.1.11
		dsPortFlowControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				enabled(1),
				disabled(2),
				notavailable(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The flow control mode of port."
			::= { dsPortEntry 11 }

		
		-- *******.4.1.6296.*******.4.1.12
		dsPortIngressrate OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A incomming rate limit control value.
				In case of Vdsl QAM20, it means constellation value of each port."
			::= { dsPortEntry 12 }

		
		-- *******.4.1.6296.*******.4.1.13
		dsPortEgressrate OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A outgoing rate limit control value."
			::= { dsPortEntry 13 }

		
		-- *******.4.1.6296.*******.4.1.14
		dsPortInstallStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				removed(0),
				installed(1),
				uninstalled(2),
				gbicinstalled(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The install status of optional module."
			::= { dsPortEntry 14 }

		
		-- *******.4.1.6296.*******.4.1.15
		dsPortCurrentHost OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number hosts which is allowed to be used."
			::= { dsPortEntry 15 }

		
		-- *******.4.1.6296.*******.4.1.16
		dsPortMaxHost OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum number hosts which can be allowed to be used.
				'-1' means the unlimited max-host value. "
			::= { dsPortEntry 16 }

		
-- ------------------------------------------------------------------------------------
-- 
-- dsPortProfile OBJECT-TYPE
--     SYNTAX      INTEGER {
--                      notavailable(-1),
--                     a-sym11(0),
--                      asym11(1),
--                      sym11(2),
--                      a-asym11(3),
--                      a-sym13(4),
--                      sym16(5),
--                      a-asym13(6),
--                      tl13(7),
--                      tl16(8),
--                      tl16a(9),
--                      asym24(11),
--                      asym18(12),
--                      asym25(13),
--                      asym26(14),
--                      asym50-998n(15),
--                      asym50-998e(16),
--                      sym25-997(17),
--                      asym50-998a(18),
--                      asym50-998i(19),
--                      asym50-998t(20),
--                      asym50-998s(21)                        
-- 		}
--     MAX-ACCESS  read-write
--     STATUS      obsolete
--     DESCRIPTION
--             "Vdsl profile ID
--              sym11 (2)     Symmetric 11/11
--              asym11 (1)    Asymmetric 3/11
--              a-sym11 (0)   ADSL friendly symmetric 11/11
--              a-asym11 (3)  ADSL friendly asymmetric 3/11
--              a-sym13 (4)   ADSL friendly symmetric 13/13
--              a-asym13 (6)  ADSL friendly asymmetric 3/13
--              sym16 (5)     Symmetric 16/16
--              tl13 (7)      T-LAN friendly 13/13
--              tl16 (8)      T-LAN friendly 16/16
--              tl16a (9)     T-LAN friendly 13/16A (QAM20 ONLY)
--              asym24 (11)   PLAN998 Asymmetric 8/24 (QAM20 ONLY)
--              asym18 (12)   PLAN998 Asymmetric 8/18 (QAM20 ONLY)
--              asym25 (13)   PLAN998 Asymmetric Enhanced 8/25(QAM20 ONLY)
--              asym26 (14)   PLAN998 Asymmetric Enhanced 8/26(QAM20 ONLY)
--              asym50-998n (15)  PLAN998 Asymmetric
--              asym50-998e (16)  PLAN998E Asymmetric
--              sym25-997 (17)    PLAN997 Asymmetric
--              asym50-998a (18)  ADSL friendly PLAN 998 Asymetric
--              asym50-998i (19)  ISDN friendly PLAN 998 Asymetric
--              asym50-998t (20)  TLAN friendly PLAN 998 Asymetric
--              asym50-998s (21)  ADSL soft mode Asymetric"
--     ::= { dsPortEntry 17 }
-- 
-- dsPortUpSNR OBJECT-TYPE
--     SYNTAX      INTEGER 
--     MAX-ACCESS  read-only
--     STATUS      obsolete
--     DESCRIPTION
--             "Vdsl upstream SNR * 1000."
--     ::= { dsPortEntry 18 }
-- 
-- dsPortDownSNR OBJECT-TYPE
--     SYNTAX      INTEGER 
--     MAX-ACCESS  read-only
--     STATUS      obsolete
--     DESCRIPTION
--             "Vdsl downstream SNR * 1000."
--     ::= { dsPortEntry 19 }
-- 
-- dsPortWanInPkt OBJECT-TYPE
--     SYNTAX      INTEGER 
--     MAX-ACCESS  read-only
--     STATUS      obsolete
--     DESCRIPTION
--             "Vdsl WAN in-packet count."
--     ::= { dsPortEntry 20 }
-- 
-- dsPortWanInErr OBJECT-TYPE
--     SYNTAX      INTEGER 
--     MAX-ACCESS  read-only
--     STATUS      obsolete
--     DESCRIPTION
--            "Vdsl WAN error packet count."
--     ::= { dsPortEntry 21 }
-- 
-- dsPortWanOutPkt OBJECT-TYPE
--     SYNTAX      INTEGER 
--     MAX-ACCESS  read-only
--     STATUS      obsolete
--     DESCRIPTION
--             "Vdsl WAN out-packet count."
--     ::= { dsPortEntry 22 }
-- 
-- dsPortWanReset OBJECT-TYPE
--     SYNTAX      INTEGER  { reset(1) }
--     MAX-ACCESS  read-write
--     STATUS      obsolete
--     DESCRIPTION
--             "Reaquire Vdsl line SYNC."
--     ::= { dsPortEntry 23 }
-- 
-- dsPortWanLoopbackAction OBJECT-TYPE
--     SYNTAX      INTEGER  { loopbacktest(1) }
--     MAX-ACCESS  read-write
--     STATUS      obsolete
--     DESCRIPTION
--             "Perform remote loopback ping test."
--     ::= { dsPortEntry 24 }
-- 
-- dsPortWanLoopbackStatus OBJECT-TYPE
--     SYNTAX      INTEGER  { never(0), failed(1), succeeded(2) }
--     MAX-ACCESS  read-only
--     STATUS      obsolete
--     DESCRIPTION
--             "Latest remote loopback test result."
--     ::= { dsPortEntry 25 }
-- ------------------------------------------------------------------------------------
		-- *******.4.1.6296.*******.4.1.26
		dsPortUpUtil5s OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Upstream traffic utilization during last 5 seconfs. Unit: bps"
			::= { dsPortEntry 26 }

		
		-- *******.4.1.6296.*******.4.1.27
		dsPortUpUtil1m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Upstream traffic utilization during last 1 minute. Unit: bps"
			::= { dsPortEntry 27 }

		
		-- *******.4.1.6296.*******.4.1.28
		dsPortUpUtil10m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Upstream traffic utilization during last 10 minute. Unit: bps"
			::= { dsPortEntry 28 }

		
		-- *******.4.1.6296.*******.4.1.29
		dsPortDownUtil5s OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Downstream traffic utilization during last 5 seconfs. Unit: bps"
			::= { dsPortEntry 29 }

		
		-- *******.4.1.6296.*******.4.1.30
		dsPortDownUtil1m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Downstream traffic utilization during last 1 minute. Unit: bps"
			::= { dsPortEntry 30 }

		
		-- *******.4.1.6296.*******.4.1.31
		dsPortDownUtil10m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Downstream traffic utilization during last 10 minute. Unit: bps"
			::= { dsPortEntry 31 }

		
		-- *******.4.1.6296.*******.4.1.32
		dsPortDescription OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Port description string"
			::= { dsPortEntry 32 }

		
		-- *******.4.1.6296.*******.4.1.33
		dsPortWanAdminStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				down(0),
				up(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Administrative status of WAN port"
			::= { dsPortEntry 33 }

		
		-- *******.4.1.6296.*******.4.1.34
		dsPortWanOperStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				down(0),
				up(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Operational status of WAN port"
			::= { dsPortEntry 34 }

		
		-- *******.4.1.6296.*******.4.1.35
		dsPortReset OBJECT-TYPE
			SYNTAX INTEGER { reset(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Operational Reset of port"
			::= { dsPortEntry 35 }

		
		-- *******.4.1.6296.*******.4.1.40
		dsPortSpeed OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "bps"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"switch port speed. bits/second.
				these value is vaild, 10000000,*********,*********0. "
			::= { dsPortEntry 40 }

		
		-- *******.4.1.6296.*******.4.1.41
		dsPortCurrUpTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Port's link-up time within the current one day interval."
			::= { dsPortEntry 41 }

		
		-- *******.4.1.6296.*******.4.1.42
		dsPortPrevUpTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Port's link-up time for the previous one day ."
			::= { dsPortEntry 42 }

		
		-- *******.4.1.6296.*******.4.1.43
		dsPortTrunkID OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The trunk ID that this port belong to."
			::= { dsPortEntry 43 }

		
		-- *******.4.1.6296.*******.4.1.44
		dsPortCRCCount OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The trunk ID that this port belong to."
			::= { dsPortEntry 44 }

		
		-- *******.4.1.6296.*******.4.1.45
		dsPortRateLimitUnit OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The rate-limit unit value. only for V2XXX."
			::= { dsPortEntry 45 }

		
		-- *******.4.1.6296.*******.4.1.46
		dsPortRateLimitIngressThreshold OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The rate-limit ingress threshold value. only for V2XXX."
			::= { dsPortEntry 46 }

		
		-- *******.4.1.6296.*******.4.1.48
		dsPortRateLimitEgressThreshold OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The rate-limit egress threshold value. only for V2XXX."
			::= { dsPortEntry 48 }

		
		-- *******.4.1.6296.*******.4.1.49
		dsPortCableLength OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsPortEntry 49 }

		
--   June 2003 dhlee 
-- 
		-- *******.4.1.6296.*******.5
		dsTemperatureTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsTemperatureEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsTemperatureEntry entries."
			::= { dsSwitchModules 5 }

		
		-- *******.4.1.6296.*******.5.1
		dsTemperatureEntry OBJECT-TYPE
			SYNTAX DsTemperatureEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing temperature information."
			INDEX { dsTemperatureNodeIndex, dsTemperatureIndex }
			::= { dsTemperatureTable 1 }

		
		DsTemperatureEntry ::=
			SEQUENCE { 
				dsTemperatureNodeIndex
					INTEGER,
				dsTemperatureIndex
					INTEGER,
				dsTemperatureStatus
					INTEGER
			 }

		-- *******.4.1.6296.*******.5.1.1
		dsTemperatureNodeIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsTemperatureEntry 1 }

		
		-- *******.4.1.6296.*******.5.1.2
		dsTemperatureIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsTemperatureEntry 2 }

		
		-- *******.4.1.6296.*******.5.1.3
		dsTemperatureStatus OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsTemperature status information"
			::= { dsTemperatureEntry 3 }

		
		-- *******.4.1.6296.*******.6
		dsPowerTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsPowerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsPowerEntry entries."
			::= { dsSwitchModules 6 }

		
		-- *******.4.1.6296.*******.6.1
		dsPowerEntry OBJECT-TYPE
			SYNTAX DsPowerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing dsPower information."
			INDEX { dsPowerNodeIndex, dsPowerIndex }
			::= { dsPowerTable 1 }

		
		DsPowerEntry ::=
			SEQUENCE { 
				dsPowerNodeIndex
					INTEGER,
				dsPowerIndex
					INTEGER,
				dsPowerstatus
					INTEGER,
				dsPowerinstalled
					INTEGER
			 }

		-- *******.4.1.6296.*******.6.1.1
		dsPowerNodeIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsPowerEntry 1 }

		
		-- *******.4.1.6296.*******.6.1.2
		dsPowerIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsPowerEntry 2 }

		
		-- *******.4.1.6296.*******.6.1.3
		dsPowerstatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				ok(1),
				fail(2),
				other(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsPower status information"
			::= { dsPowerEntry 3 }

		
		-- *******.4.1.6296.*******.6.1.4
		dsPowerinstalled OBJECT-TYPE
			SYNTAX INTEGER
				{
				removed(0),
				installed(1),
				uninstalled(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsPower install information"
			::= { dsPowerEntry 4 }

		
		-- *******.4.1.6296.*******.7
		dsFanTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsFanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsFanEntry entries."
			::= { dsSwitchModules 7 }

		
		-- *******.4.1.6296.*******.7.1
		dsFanEntry OBJECT-TYPE
			SYNTAX DsFanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing fan information."
			INDEX { dsFanNodeIndex, dsFanIndex }
			::= { dsFanTable 1 }

		
		DsFanEntry ::=
			SEQUENCE { 
				dsFanNodeIndex
					INTEGER,
				dsFanIndex
					INTEGER,
				dsFanstatus
					INTEGER,
				dsFaninstalled
					INTEGER
			 }

		-- *******.4.1.6296.*******.7.1.1
		dsFanNodeIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsFanEntry 1 }

		
		-- *******.4.1.6296.*******.7.1.2
		dsFanIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsFanEntry 2 }

		
		-- *******.4.1.6296.*******.7.1.3
		dsFanstatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				ok(1),
				fail(2),
				other(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsFan status information"
			::= { dsFanEntry 3 }

		
		-- *******.4.1.6296.*******.7.1.4
		dsFaninstalled OBJECT-TYPE
			SYNTAX INTEGER
				{
				removed(0),
				installed(1),
				uninstalled(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsFan install information"
			::= { dsFanEntry 4 }

		
--  May 2003
-- <EMAIL>  : dsVdsl ports informations
-- 
		-- *******.4.1.6296.*******.8
		dsVdslPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsVdslPortEntry entries."
			::= { dsSwitchModules 8 }

		
		-- *******.4.1.6296.*******.8.1
		dsVdslPortEntry OBJECT-TYPE
			SYNTAX DsVdslPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular dsVdsl port."
			INDEX { dsVdslModuleIndex, dsVdslPortIndex }
			::= { dsVdslPortTable 1 }

		
		DsVdslPortEntry ::=
			SEQUENCE { 
				dsVdslModuleIndex
					INTEGER,
				dsVdslPortIndex
					INTEGER,
				dsVdslPortType
					DisplayString,
				dsVdslPortIfIndex
					OBJECT IDENTIFIER,
				dsVdslPortUpProfile
					INTEGER,
				dsVdslPortDownProfile
					INTEGER,
				dsVdslPortUpPhysicalSpeed
					INTEGER,
				dsVdslPortDownPhysicalSpeed
					INTEGER,
				dsVdslPortUpSpeed
					INTEGER,
				dsVdslPortDownSpeed
					INTEGER,
				dsVdslPortServiceError
					INTEGER,
				dsVdslPortErrorCRC
					INTEGER,
				dsVdslPortErrorSecond
					INTEGER,
				dsVdslPortErrorLOF
					INTEGER,
				dsVdslPortErrorLOS
					INTEGER,
				dsVdslPortErrorLPR
					INTEGER,
				dsVdslPortErrorLOL
					INTEGER,
				dsVdslPort15mElapsedTime
					INTEGER,
				dsVdslPort1dayElapsedTime
					INTEGER,
				dsVdslPortServiceErrorPrev15m
					INTEGER,
				dsVdslPortServiceErrorCurr15m
					INTEGER,
				dsVdslPortErrorCRCPrev15m
					INTEGER,
				dsVdslPortErrorCRCCurr15m
					INTEGER,
				dsVdslPortErrorSecondPrev15m
					INTEGER,
				dsVdslPortErrorSecondCurr15m
					INTEGER,
				dsVdslPortErrorLOFPrev15m
					INTEGER,
				dsVdslPortErrorLOFCurr15m
					INTEGER,
				dsVdslPortErrorLOSPrev15m
					INTEGER,
				dsVdslPortErrorLOSCurr15m
					INTEGER,
				dsVdslPortErrorLPRPrev15m
					INTEGER,
				dsVdslPortErrorLPRCurr15m
					INTEGER,
				dsVdslPortErrorLOLPrev15m
					INTEGER,
				dsVdslPortErrorLOLCurr15m
					INTEGER,
				dsVdslPortServiceErrorPrev1day
					INTEGER,
				dsVdslPortServiceErrorCurr1day
					INTEGER,
				dsVdslPortErrorCRCPrev1day
					INTEGER,
				dsVdslPortErrorCRCCurr1day
					INTEGER,
				dsVdslPortErrorSecondPrev1day
					INTEGER,
				dsVdslPortErrorSecondCurr1day
					INTEGER,
				dsVdslPortErrorLOFPrev1day
					INTEGER,
				dsVdslPortErrorLOFCurr1day
					INTEGER,
				dsVdslPortErrorLOSPrev1day
					INTEGER,
				dsVdslPortErrorLOSCurr1day
					INTEGER,
				dsVdslPortErrorLPRPrev1day
					INTEGER,
				dsVdslPortErrorLPRCurr1day
					INTEGER,
				dsVdslPortErrorLOLPrev1day
					INTEGER,
				dsVdslPortErrorLOLCurr1day
					INTEGER,
				dsVdslPortUpSNR
					INTEGER,
				dsVdslPortDownSNR
					INTEGER,
				dsVdslPortUpSNRMargin
					INTEGER,
				dsVdslPortDownSNRMargin
					INTEGER,
				dsVdslPortUpAttenuation
					INTEGER,
				dsVdslPortDownAttenuation
					INTEGER,
				dsVdslPortOutPower
					INTEGER,
				dsVdslPortWanInPkt
					INTEGER,
				dsVdslPortWanInErr
					INTEGER,
				dsVdslPortWanOutPkt
					INTEGER,
				dsVdslPortWanReset
					INTEGER,
				dsVdslPortWanLoopbackAction
					INTEGER,
				dsVdslPortWanLoopbackStatus
					INTEGER,
				dsVdslPortWanAdminStatus
					INTEGER,
				dsVdslPortWanOperStatus
					INTEGER,
				dsVdslPortVOCLinkStatus
					INTEGER,
				dsVdslPortVOCLinkDuplex
					INTEGER,
				dsVdslPortVOCLinkSpeed
					INTEGER,
				dsVdslPortOptionband
					INTEGER,
				dsVdslPortHamband
					Counter64,
				dsVdslPortUpInterleave
					INTEGER,
				dsVdslPortDownInterleave
					INTEGER,
				dsVdslPortTargetUpSNRMargin
					INTEGER,
				dsVdslPortTargetDownSNRMargin
					INTEGER,
				dsVdslPortPSDMaskLevel
					INTEGER,
				dsVdslPortPBOLength
					INTEGER,
				dsVdslPort15mElapsedTimeSeconds
					INTEGER,
				dsVdslPort1dayElapsedTimeSeconds
					INTEGER,
				dsVdslPortUpServiceProfile
					INTEGER,
				dsVdslPortDownServiceProfile
					INTEGER,
				dsVdslPortModemReset
					INTEGER,
				dsVdslPortCPEVersion
					DisplayString,
				dsVdslPortCPEDownload
					INTEGER,
				dsVdslPortLineActiveTimePrev1Day
					INTEGER,
				dsVdslPortLineActiveTimeCurr1Day
					INTEGER,
				dsVdslPortLineConfProfileClear
					INTEGER,
				dsVdslPortAlarmConfProfileClear
					INTEGER,
				dsVdslPortEwl
					INTEGER,
				dsVdslPortGhsNearEndEwl
					INTEGER,
				dsVdslPortGhsFarEndEwl
					INTEGER,
				dsVdslPortLineActiveTimeClear
					INTEGER,
				dsVdslPortServiceErrorClear
					INTEGER,
				dsVdslPortErrorCRCClear
					INTEGER,
				dsVdslPortErrorSecondClear
					INTEGER,
				dsVdslPortErrorLOFClear
					INTEGER,
				dsVdslPortErrorLOSClear
					INTEGER,
				dsVdslPortErrorLPRClear
					INTEGER,
				dsVdslPortErrorLOLClear
					INTEGER,
				dsVdslPortErrorCountClear
					INTEGER,
				dsVdslPortCpeErrorCRC
					INTEGER,
				dsVdslPortCpePerfLOF
					INTEGER,
				dsVdslPortCpePerfLos
					INTEGER,
				dsVdslPortCpePerfLol
					INTEGER,
				dsVdslPortCpePerfCountClear
					INTEGER,
				dsVdslPortSuperFrameRxCount
					Unsigned32,
				dsVdslPortSuperFrameTxCount
					Unsigned32,
				dsVdslPortSuperFrameCountClear
					INTEGER,
				dsVdslPortPerfRxCRC
					INTEGER,
				dsVdslPortPerfTxCRC
					INTEGER,
				dsVdslPortStandard
					INTEGER,
				dsVdslPortLineProfile
					INTEGER,
				dsVdslPortMode
					INTEGER,
				dsVdslPortProfileSet
					INTEGER,
				dsVdslPortUpboEnable
					INTEGER,
				dsVdslPortChannel
					INTEGER,
				dsVdslPortTCMAdmin
					INTEGER,
				dsVdslPortUpSNRMinMargin
					INTEGER,
				dsVdslPortDownSNRMinMargin
					INTEGER,
				dsVdslPortUpINP
					INTEGER,
				dsVdslPortDownINP
					INTEGER,
				dsVdslPortMiiInBytes
					INTEGER,
				dsVdslPortMiiInBPS
					INTEGER,
				dsVdslPortMiiOutBytes
					INTEGER,
				dsVdslPortMiiOutBPS
					INTEGER,
				dsVdslPortHardwareAddress
					DisplayString,
				dsVdslPortRedundancySet
					INTEGER,
				dsVdslCpeNegoState
					INTEGER,
				dsVdslCpeDuplex
					INTEGER,
				dsVdslCpeSpeed
					INTEGER,
				dsVdslPortLinkUpTime
					INTEGER,
				dsVdslPortLastRetraingReason
					INTEGER,
				dsVdslPortU0BandPBOLength
					INTEGER,
				dsVdslPortU1BandPBOLength
					INTEGER,
				dsVdslPortU2BandPBOLength
					INTEGER,
				dsVdslPortU3BandPBOLength
					INTEGER,
				dsVdslPortDownCurrAttainableRate
					Gauge32,
				dsVdslPortUpCurrAttainableRate
					Gauge32
			 }

--  speed
-- service error  : raw 
-- Elapsed Time 
-- service error  : 15m
-- service error  : 1day
-- 
-- June 2003, newly add --
-- July 2003, newly add --       
-- Feb 2004, newly add CPE-Modem Management--          
-- following mib are referenced upper style..
-- following mib are not decided to include or not..
-- 		   dsVdslPortErrorUncorrectCRCClear      INTEGER,
-- 		   dsVdslPortErrorCorrectCRCClear        INTEGER,
-- 		   dsVdslPortErrorServiceErrorSecondClear	INTEGER,
-- 		   dsVdslPortErrorCRCSecondClear         INTEGER,
-- 		   dsVdslPortErrorLOFSecondClear         INTEGER,
-- 		   dsVdslPortErrorLOSSecondClear         INTEGER,
-- 		   dsVdslPortErrorLOLSecondClear         INTEGER,
-- 		   dsVdslPortErrorSESSecondClear         INTEGER,
-- 		   dsVdslPortErrorUASSecondClear         INTEGER,
--        dsVdslPortCpeErrorSecond				 INTEGER,
--        dsVdslPortCpePerfLPR					 INTEGER,
		-- *******.4.1.6296.*******.8.1.1
		dsVdslModuleIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsVdslPortEntry 1 }

		
		-- *******.4.1.6296.*******.8.1.2
		dsVdslPortIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				port.  It is recommended that values are assigned
				contiguously starting from 1.  The value for each
				interface sub-layer must remain constant at least from
				one re-initialization of the entity's network
				management system to the next re-initialization."
			::= { dsVdslPortEntry 2 }

		
		-- *******.4.1.6296.*******.8.1.3
		dsVdslPortType OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of dsVdsl port. This string defines the type
				of port."
			::= { dsVdslPortEntry 3 }

		
		-- *******.4.1.6296.*******.8.1.4
		dsVdslPortIfIndex OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"matching ifIndex oid which is defined in interface mib."
			::= { dsVdslPortEntry 4 }

		
		-- *******.4.1.6296.*******.8.1.5
		dsVdslPortUpProfile OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				a-sym11(0),
				asym11(1),
				sym11(2),
				a-asym11(3),
				a-sym13(4),
				sym16(5),
				a-asym13(6),
				tl13(7),
				tl16(8),
				tl16a(9),
				asym24(11),
				asym18(12),
				asym25(13),
				asym26(14),
				asym50-998n(51),
				asym50-998e(52),
				sym25-997(53),
				asym50-998a(54),
				asym50-998i(55),
				asym50-998t(56),
				asym50-998s(57),
				asym50-998n-4b(58),
				asym50-998i-4b(59),
				asym50-998a-4b(60),
				sym25-997a(61),
				sym25-997i(62),
				asym70-998n(63),
				asym70-998i(64),
				asym70-998a(65),
				asym100-998n(66),
				asym100-998i(67),
				asym100-998a(68),
				sym100-100-998n(69),
				sym100-100-998i(70),
				sym100-100-998a(71)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Upstream profile ID.
				sym11 (2)     Symmetric 11/11
				asym11 (1)    Asymmetric 3/11
				a-sym11 (0)   ADSL friendly symmetric 11/11
				a-asym11 (3)  ADSL friendly asymmetric 3/11
				a-sym13 (4)   ADSL friendly symmetric 13/13
				a-asym13 (6)  ADSL friendly asymmetric 3/13
				sym16 (5)     Symmetric 16/16
				tl13 (7)      T-LAN friendly 13/13
				tl16 (8)      T-LAN friendly 16/16
				tl16a (9)     T-LAN friendly 13/16A (QAM20 ONLY)
				asym24 (11)   PLAN998 Asymmetric 8/24 (QAM20 ONLY)
				asym18 (12)   PLAN998 Asymmetric 8/18 (QAM20 ONLY)
				asym25 (13)   PLAN998 Asymmetric Enhanced 8/25(QAM20 ONLY)
				asym26 (14)   PLAN998 Asymmetric Enhanced 8/26(QAM20 ONLY)
				asym50-998n (51)  PLAN998 Asymmetric
				asym50-998e (52)  PLAN998E Asymmetric
				sym25-997 (53)    PLAN997 Asymmetric
				asym50-998a (54)  ADSL friendly PLAN 998 Asymetric
				asym50-998i (55)  ISDN friendly PLAN 998 Asymetric
				asym50-998t (56)  TLAN friendly PLAN 998 Asymetric
				asym50-998s (57)  ADSL soft mode Asymetric
				asym50-998n-4b(58) PLAN998 Asymmetric 13/50 4 band
				asym50-998i-4b(59) ISDN friendly PLAN 998 13/50 4 band
				asym50-998a-4b(60) ADSL friendly PLAN 998 13/50 4 band
				sym25-997a(61)  ADSL friendly PLAN 997 Symmetric 25/25     
				sym25-997i(62)  ISDN friendly PLAN 997 symmetric 25/25
				asym70-998n(63) PLAN998 Asymmetric 30/70 5 band
				asym70-998i(64) ISDN friendly PLAN 998 asymmetric 30/70 5 band        
				asym70-998a(65) ADSL friendly PLAN 998 asymmetric 30/70 5 band
				asym100-998n(66) PLAN 998n Asymmetric for 6Band DMT 100M 
				asym100-998i(67) PLAN 998i Asymmetric for 6Band DMT 100M
				asym100-998a(68) PLAN 998a Asymmetric for 6Band DMT 100M
				sym100-100-998n(69) PLAN 998n Symmetric for 6Band DMT 100/100M 
				sym100-100-998i(70) PLAN 998i Symmetric for 6Band DMT 100/100M  
				sym100-100-998a(71) PLAN 998a Symmetric for 6Band DMT 100/100M 
				"
			::= { dsVdslPortEntry 5 }

		
		-- *******.4.1.6296.*******.8.1.6
		dsVdslPortDownProfile OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				a-sym11(0),
				asym11(1),
				sym11(2),
				a-asym11(3),
				a-sym13(4),
				sym16(5),
				a-asym13(6),
				tl13(7),
				tl16(8),
				tl16a(9),
				asym24(11),
				asym18(12),
				asym25(13),
				asym26(14),
				asym50-998n(51),
				asym50-998e(52),
				sym25-997(53),
				asym50-998a(54),
				asym50-998i(55),
				asym50-998t(56),
				asym50-998s(57),
				asym50-998n-4b(58),
				asym50-998i-4b(59),
				asym50-998a-4b(60),
				sym25-997a(61),
				sym25-997i(62),
				asym70-998n(63),
				asym70-998i(64),
				asym70-998a(65),
				asym100-998n(66),
				asym100-998i(67),
				asym100-998a(68),
				sym100-100-998n(69),
				sym100-100-998i(70),
				sym100-100-998a(71)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Downstream profile ID.
				sym11 (2)     Symmetric 11/11
				asym11 (1)    Asymmetric 3/11
				a-sym11 (0)   ADSL friendly symmetric 11/11
				a-asym11 (3)  ADSL friendly asymmetric 3/11
				a-sym13 (4)   ADSL friendly symmetric 13/13
				a-asym13 (6)  ADSL friendly asymmetric 3/13
				sym16 (5)     Symmetric 16/16
				tl13 (7)      T-LAN friendly 13/13
				tl16 (8)      T-LAN friendly 16/16
				tl16a (9)     T-LAN friendly 13/16A (QAM20 ONLY)
				asym24 (11)   PLAN998 Asymmetric 8/24 (QAM20 ONLY)
				asym18 (12)   PLAN998 Asymmetric 8/18 (QAM20 ONLY)
				asym25 (13)   PLAN998 Asymmetric Enhanced 8/25(QAM20 ONLY)
				asym26 (14)   PLAN998 Asymmetric Enhanced 8/26(QAM20 ONLY)
				asym50-998n (51)  PLAN998 Asymmetric
				asym50-998e (52)  PLAN998E Asymmetric
				sym25-997 (53)    PLAN997 Asymmetric
				asym50-998a (54)  ADSL friendly PLAN 998 Asymetric
				asym50-998i (55)  ISDN friendly PLAN 998 Asymetric
				asym50-998t (56)  TLAN friendly PLAN 998 Asymetric
				asym50-998s (57)  ADSL soft mode Asymetric
				asym50-998n-4b(58) PLAN998 Asymmetric 13/50 4 band
				asym50-998i-4b(59) ISDN friendly PLAN 998 13/50 4 band
				asym50-998a-4b(60) ADSL friendly PLAN 998 13/50 4 band
				sym25-997a(61)  ADSL friendly PLAN 997 Symmetric 25/25     
				sym25-997i(62)  ISDN friendly PLAN 997 symmetric 25/25
				asym70-998n(63) PLAN998 Asymmetric 30/70 5 band
				asym70-998i(64) ISDN friendly PLAN 998 asymmetric 30/70 5 band         
				asym70-998a(65) ADSL friendly PLAN 998 asymmetric 30/70 5 band
				asym100-998n(66) PLAN 998n Asymmetric for 6Band DMT 100M 
				asym100-998i(67) PLAN 998i Asymmetric for 6Band DMT 100M
				asym100-998a(68) PLAN 998a Asymmetric for 6Band DMT 100M
				sym100-100-998n(69) PLAN 998n Symmetric for 6Band DMT 100/100M 
				sym100-100-998i(70) PLAN 998i Symmetric for 6Band DMT 100/100M  
				sym100-100-998a(71) PLAN 998a Symmetric for 6Band DMT 100/100M 
				"
			::= { dsVdslPortEntry 6 }

		
		-- *******.4.1.6296.*******.8.1.7
		dsVdslPortUpPhysicalSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Upstream attainable speed of port. bits/second."
			::= { dsVdslPortEntry 7 }

		
		-- *******.4.1.6296.*******.8.1.8
		dsVdslPortDownPhysicalSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Downstream attainable speed of port. bits/second."
			::= { dsVdslPortEntry 8 }

		
		-- *******.4.1.6296.*******.8.1.9
		dsVdslPortUpSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Upstream speed of port. bits/second."
			::= { dsVdslPortEntry 9 }

		
		-- *******.4.1.6296.*******.8.1.10
		dsVdslPortDownSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Downstream speed of port. bits/second."
			::= { dsVdslPortEntry 10 }

		
		-- *******.4.1.6296.*******.8.1.11
		dsVdslPortServiceError OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The raw accumulating dsVdsl service Error count."
			::= { dsVdslPortEntry 11 }

		
		-- *******.4.1.6296.*******.8.1.12
		dsVdslPortErrorCRC OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The raw accumulating dsVdsl CRC Error count."
			::= { dsVdslPortEntry 12 }

		
		-- *******.4.1.6296.*******.8.1.13
		dsVdslPortErrorSecond OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The raw accumulating dsVdsl Error Second."
			::= { dsVdslPortEntry 13 }

		
		-- *******.4.1.6296.*******.8.1.14
		dsVdslPortErrorLOF OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The raw accumulating dsVdsl loss of frame."
			::= { dsVdslPortEntry 14 }

		
		-- *******.4.1.6296.*******.8.1.15
		dsVdslPortErrorLOS OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The raw accumulating dsVdsl loss of signal."
			::= { dsVdslPortEntry 15 }

		
		-- *******.4.1.6296.*******.8.1.16
		dsVdslPortErrorLPR OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The raw accumulating dsVdsl loss of power."
			::= { dsVdslPortEntry 16 }

		
		-- *******.4.1.6296.*******.8.1.17
		dsVdslPortErrorLOL OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The raw accumulating dsVdsl loss of link."
			::= { dsVdslPortEntry 17 }

		
--  TimeTicks
		-- *******.4.1.6296.*******.8.1.18
		dsVdslPort15mElapsedTime OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "1 minute"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current elapsed time from reinitializing point per 15 minutes."
			::= { dsVdslPortEntry 18 }

		
--  TimeTicks
		-- *******.4.1.6296.*******.8.1.19
		dsVdslPort1dayElapsedTime OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "1 minute"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current elapsed time from reinitializing point per 1 day."
			::= { dsVdslPortEntry 19 }

		
		-- *******.4.1.6296.*******.8.1.20
		dsVdslPortServiceErrorPrev15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating Serivce-Error count of previous 15 minute."
			::= { dsVdslPortEntry 20 }

		
		-- *******.4.1.6296.*******.8.1.21
		dsVdslPortServiceErrorCurr15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating Service-Error count of current 15 minute."
			::= { dsVdslPortEntry 21 }

		
		-- *******.4.1.6296.*******.8.1.22
		dsVdslPortErrorCRCPrev15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating CRC-error count of previous 15 minute."
			::= { dsVdslPortEntry 22 }

		
		-- *******.4.1.6296.*******.8.1.23
		dsVdslPortErrorCRCCurr15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating CRC-error count of current 15 minute."
			::= { dsVdslPortEntry 23 }

		
		-- *******.4.1.6296.*******.8.1.24
		dsVdslPortErrorSecondPrev15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating errored-second count of previous 15 minute."
			::= { dsVdslPortEntry 24 }

		
		-- *******.4.1.6296.*******.8.1.25
		dsVdslPortErrorSecondCurr15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating errored-second count of current 15 minute."
			::= { dsVdslPortEntry 25 }

		
		-- *******.4.1.6296.*******.8.1.26
		dsVdslPortErrorLOFPrev15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-frame count of previous 15 minute."
			::= { dsVdslPortEntry 26 }

		
		-- *******.4.1.6296.*******.8.1.27
		dsVdslPortErrorLOFCurr15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-frame count of current 15 minute."
			::= { dsVdslPortEntry 27 }

		
		-- *******.4.1.6296.*******.8.1.28
		dsVdslPortErrorLOSPrev15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-signal count of previous 15 minute."
			::= { dsVdslPortEntry 28 }

		
		-- *******.4.1.6296.*******.8.1.29
		dsVdslPortErrorLOSCurr15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-signal count of current 15 minute."
			::= { dsVdslPortEntry 29 }

		
		-- *******.4.1.6296.*******.8.1.30
		dsVdslPortErrorLPRPrev15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-power count of previous 15 minute."
			::= { dsVdslPortEntry 30 }

		
		-- *******.4.1.6296.*******.8.1.31
		dsVdslPortErrorLPRCurr15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-power count of current 15 minute."
			::= { dsVdslPortEntry 31 }

		
		-- *******.4.1.6296.*******.8.1.32
		dsVdslPortErrorLOLPrev15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-link count of previous 15 minute."
			::= { dsVdslPortEntry 32 }

		
		-- *******.4.1.6296.*******.8.1.33
		dsVdslPortErrorLOLCurr15m OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-link count of current 15 minute."
			::= { dsVdslPortEntry 33 }

		
		-- *******.4.1.6296.*******.8.1.34
		dsVdslPortServiceErrorPrev1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating Serivce-Error count of previous 1 day."
			::= { dsVdslPortEntry 34 }

		
		-- *******.4.1.6296.*******.8.1.35
		dsVdslPortServiceErrorCurr1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating Service-Error count of current 1 day."
			::= { dsVdslPortEntry 35 }

		
		-- *******.4.1.6296.*******.8.1.36
		dsVdslPortErrorCRCPrev1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating CRC-error count of previous 1 day."
			::= { dsVdslPortEntry 36 }

		
		-- *******.4.1.6296.*******.8.1.37
		dsVdslPortErrorCRCCurr1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating CRC-error count of current 1 day."
			::= { dsVdslPortEntry 37 }

		
		-- *******.4.1.6296.*******.8.1.38
		dsVdslPortErrorSecondPrev1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating errored-second count of previous 1 day."
			::= { dsVdslPortEntry 38 }

		
		-- *******.4.1.6296.*******.8.1.39
		dsVdslPortErrorSecondCurr1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating errored-second count of current 1 day."
			::= { dsVdslPortEntry 39 }

		
		-- *******.4.1.6296.*******.8.1.40
		dsVdslPortErrorLOFPrev1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-frame count of previous 1 day."
			::= { dsVdslPortEntry 40 }

		
		-- *******.4.1.6296.*******.8.1.41
		dsVdslPortErrorLOFCurr1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-frame count of current 1 day."
			::= { dsVdslPortEntry 41 }

		
		-- *******.4.1.6296.*******.8.1.42
		dsVdslPortErrorLOSPrev1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-signal count of previous 1 day."
			::= { dsVdslPortEntry 42 }

		
		-- *******.4.1.6296.*******.8.1.43
		dsVdslPortErrorLOSCurr1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-signal count of current 1 day."
			::= { dsVdslPortEntry 43 }

		
		-- *******.4.1.6296.*******.8.1.44
		dsVdslPortErrorLPRPrev1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-power count of previous 1 day."
			::= { dsVdslPortEntry 44 }

		
		-- *******.4.1.6296.*******.8.1.45
		dsVdslPortErrorLPRCurr1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-power count of current 1 day."
			::= { dsVdslPortEntry 45 }

		
		-- *******.4.1.6296.*******.8.1.46
		dsVdslPortErrorLOLPrev1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-link count of previous 1 day."
			::= { dsVdslPortEntry 46 }

		
		-- *******.4.1.6296.*******.8.1.47
		dsVdslPortErrorLOLCurr1day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating loss-of-link count of current 1 day"
			::= { dsVdslPortEntry 47 }

		
		-- *******.4.1.6296.*******.8.1.48
		dsVdslPortUpSNR OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl upstream SNR * 1000."
			::= { dsVdslPortEntry 48 }

		
		-- *******.4.1.6296.*******.8.1.49
		dsVdslPortDownSNR OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl downstream SNR * 1000."
			::= { dsVdslPortEntry 49 }

		
		-- *******.4.1.6296.*******.8.1.50
		dsVdslPortUpSNRMargin OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "0.1db"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl upstream SNR Margin(CO SNR Margin)."
			::= { dsVdslPortEntry 50 }

		
		-- *******.4.1.6296.*******.8.1.51
		dsVdslPortDownSNRMargin OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "0.1db"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl downstream SNR Margin(CPE SNR Margin)."
			::= { dsVdslPortEntry 51 }

		
		-- *******.4.1.6296.*******.8.1.52
		dsVdslPortUpAttenuation OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "0.1db"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl upstream port attenuation(CO Attenuation)."
			::= { dsVdslPortEntry 52 }

		
		-- *******.4.1.6296.*******.8.1.53
		dsVdslPortDownAttenuation OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "0.1db"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl downstream port attenuation(CPE Attenuation)."
			::= { dsVdslPortEntry 53 }

		
		-- *******.4.1.6296.*******.8.1.54
		dsVdslPortOutPower OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "0.1db"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl port Output Power."
			::= { dsVdslPortEntry 54 }

		
		-- *******.4.1.6296.*******.8.1.55
		dsVdslPortWanInPkt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl WAN in-packet count."
			::= { dsVdslPortEntry 55 }

		
		-- *******.4.1.6296.*******.8.1.56
		dsVdslPortWanInErr OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl WAN error packet count."
			::= { dsVdslPortEntry 56 }

		
		-- *******.4.1.6296.*******.8.1.57
		dsVdslPortWanOutPkt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl WAN out-packet count."
			::= { dsVdslPortEntry 57 }

		
		-- *******.4.1.6296.*******.8.1.58
		dsVdslPortWanReset OBJECT-TYPE
			SYNTAX INTEGER { reset(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Reaquire dsVdsl line SYNC."
			::= { dsVdslPortEntry 58 }

		
		-- *******.4.1.6296.*******.8.1.59
		dsVdslPortWanLoopbackAction OBJECT-TYPE
			SYNTAX INTEGER { loopbacktest(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Perform remote loopback ping test."
			::= { dsVdslPortEntry 59 }

		
		-- *******.4.1.6296.*******.8.1.60
		dsVdslPortWanLoopbackStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				other(0),
				failed(1),
				succeeded(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Latest remote loopback test result."
			::= { dsVdslPortEntry 60 }

		
		-- *******.4.1.6296.*******.8.1.61
		dsVdslPortWanAdminStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				other(-1),
				down(0),
				up(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Administrative status of WAN port"
			::= { dsVdslPortEntry 61 }

		
		-- *******.4.1.6296.*******.8.1.62
		dsVdslPortWanOperStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				other(-1),
				down(0),
				up(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Operational status of WAN port"
			::= { dsVdslPortEntry 62 }

		
--  VOC information
		-- *******.4.1.6296.*******.8.1.63
		dsVdslPortVOCLinkStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PC Ethernet link status through VOC channel."
			::= { dsVdslPortEntry 63 }

		
		-- *******.4.1.6296.*******.8.1.64
		dsVdslPortVOCLinkDuplex OBJECT-TYPE
			SYNTAX INTEGER
				{
				full(1),
				half(2),
				notavailable(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PC link duplex through VOC channel."
			::= { dsVdslPortEntry 64 }

		
		-- *******.4.1.6296.*******.8.1.65
		dsVdslPortVOCLinkSpeed OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PC link speed through VOC channel. bits/second(bps)."
			::= { dsVdslPortEntry 65 }

		
--  June 2003 dhlee 
-- 
		-- *******.4.1.6296.*******.8.1.66
		dsVdslPortOptionband OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				annex-B-6-64(1),
				annex-A-6-32(2),
				annex-B-32-64(3),
				exclude(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Optional band ID.
				Annex B 6-64 (1)
				Annex A 6-32 (2)
				Annex B 32-64 (3)
				Exclude Option Band (4)
				"
			::= { dsVdslPortEntry 66 }

		
		-- *******.4.1.6296.*******.8.1.67
		dsVdslPortHamband OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Amature Radio(HAM) band ID.
				The value of Ham band is combination of the following value.
				If you want to set band1 and band10, you should set value 257(band1 + band10)
				to the Ham band.   
				band1   [  1.800  -  1.810  ] MHz : RFI Notch                (1)
				band2   [  1.800  -  1.825  ] MHz : KOREA HAM-BAND           (2)
				band3   [  1.810  -  1.825  ] MHz : ANNEX F                  (4)
				band4   [  1.810  -  2.000  ] MHz : ETSI, T1E1               (8)
				band5   [  1.9075 -  1.9125 ] MHz : ANNEX F                  (16)
				band6   [  2.173  -  2.191  ] MHz : DT GMDSS                 (2097152)					0x0000 0020 0000
				band7   [  3.500  -  3.550  ] MHz : KOREA HAM-BAND           (32)
				band8   [  3.500  -  3.575  ] MHz : ANNEX F                  (64) 
				band9   [  3.500  -  3.800  ] MHz : ETSI                     (128)
				band10  [  3.500  -  4.000  ] MHz : T1E1                     (256)
				band11  [  3.747  -  3.754  ] MHz : ANNEX F                  (512)
				band12  [  3.790  -  3.800  ] MHz : KOREA HAM-BAND           (1024)             
				band13  [  3.791  -  3.805  ] MHz : ANNEX F                  (2048)
				band14  [  4.200  -  4.215  ] MHz : DT GMDSS                 (4194304)					0x0000 0040 0000
				band15  [  5.900  -  6.200  ] MHz : DT DRM radio (Broadcasting)           (8388608)		0x0000 0080 0000
				band16  [  6.300  -  6.320  ] MHz : DT GMDSS                              (16777216)		0x0000 0100 0000
				band17  [  7.000  -  7.100  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI         (4096)
				band18  [  7.000  -  7.300  ] MHz : T1E1                                  (8192)
				band19  [  7.100  -  7.350  ] MHz : DT DRM radio (Broadcasting)           (33554432)		0x0000 0200 0000
				band20  [  8.405  -  8.420  ] MHz : DT GMDSS                              (67108864)		0x0000 0400 0000
				band21  [  9.400  -  9.900  ] MHz : DT DRM radio (Broadcasting)           (134217728)	0x0000 0800 0000   
				band22  [ 10.100  - 10.150  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI, T1E1   (16384)
				band23  [ 11.600  - 12.100  ] MHz : DT DRM radio (Broadcasting)           (268435456)	0x0000 1000 0000
				band24  [ 12.570  - 12.585  ] MHz : DT GMDSS                              (536870912)	0x0000 2000 0000
				band25  [ 13.570  - 13.870  ] MHz : DT DRM radio (Broadcasting)           (1073741824)	0x0000 4000 0000
				band26  [ 14.000  - 14.350  ] MHz : ANNEX F, ETSI, T1E1                   (32768)
				band27  [ 15.100  - 15.800  ] MHz : DT DRM radio (Broadcasting)           (2147483648)	0x0000 8000 0000
				band28  [ 16.795  - 16.810  ] MHz : DT GMDSS                              (4294967296)	0x0001 0000 0000
				band29  [ 17.480  - 17.900  ] MHz : DT DRM radio (Broadcasting)           (8589934592)	0x0002 0000 0000
				band30  [ 18.068  - 18.168  ] MHz : ANNEX F, ETSI, T1E1                   (65536)
				band31  [ 21.000  - 21.450  ] MHz : ANNEX F, ETSI, T1E1                   (131072)		0x0000 0002 0000
				band32  [ 24.890  - 24.990  ] MHz : ANNEX F, ETST, T1E1                   (262144)		0x0000 0004 0000
				band33  [ 28.000  - 29.100  ] MHz : ETSI                                  (524288)		0x0000 0008 0000
				band34  [ 28.000  - 29.700  ] MHz : ANNEX F, ETSI, T1E1                   (1048576)		0x0000 0010 0000
				"
			::= { dsVdslPortEntry 67 }

		
		-- *******.4.1.6296.*******.8.1.68
		dsVdslPortUpInterleave OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			UNITS "1 ms"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" dsVdslPort Upstream Interleave delay."
			::= { dsVdslPortEntry 68 }

		
		-- *******.4.1.6296.*******.8.1.69
		dsVdslPortDownInterleave OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			UNITS "1 ms"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" dsVdslPort Downstream Interleave delay."
			::= { dsVdslPortEntry 69 }

		
		-- *******.4.1.6296.*******.8.1.70
		dsVdslPortTargetUpSNRMargin OBJECT-TYPE
			SYNTAX INTEGER (0..31)
			UNITS "1db"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Target upstream SNR Margin(CO SNR Margin)."
			::= { dsVdslPortEntry 70 }

		
		-- *******.4.1.6296.*******.8.1.71
		dsVdslPortTargetDownSNRMargin OBJECT-TYPE
			SYNTAX INTEGER (0..31)
			UNITS "1db"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Target downstream SNR Margin(CPE SNR Margin)."
			::= { dsVdslPortEntry 71 }

		
		-- *******.4.1.6296.*******.8.1.72
		dsVdslPortPSDMaskLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				old-gains(0),
				aNSI-M1(1),
				aNSI-M2(2),
				eTSI-M1(3),
				eTSI-M2(4),
				aNNEX-F-8-5(5),
				aNSI-M1-EX(6),
				aNSI-M2-EX(7),
				eTSI-M1-EX(8),
				eTSI-M2-EX(9),
				aNNEX-F-11-5(10),
				pSD-K(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl PSD Mask Level.
				notavailable(-1)
				old gains(0)
				ANSI M1(1)
				ANSI M2(2) 
				ETSI M1(3)
				ETSI M2(4)
				ANNEX F 8.5(5) 
				ANSI M1 EX(6)
				ANSI M2 EX(7)
				ETSI M1 EX(8)
				ETSI M2 EX(9)
				ANNEX F 11.5(10)                                      
				PSD-K(11)
				"
			::= { dsVdslPortEntry 72 }

		
		-- *******.4.1.6296.*******.8.1.73
		dsVdslPortPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9),
				pbo1000m(10)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)
				1000m(10)                       
				"
			::= { dsVdslPortEntry 73 }

		
		-- *******.4.1.6296.*******.8.1.74
		dsVdslPort15mElapsedTimeSeconds OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "1 second"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current elapsed time from reinitializing point per 15 minutes in seconds."
			::= { dsVdslPortEntry 74 }

		
		-- *******.4.1.6296.*******.8.1.75
		dsVdslPort1dayElapsedTimeSeconds OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "1 second"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current elapsed time from reinitializing point per 1 day in seconds."
			::= { dsVdslPortEntry 75 }

		
		-- *******.4.1.6296.*******.8.1.76
		dsVdslPortUpServiceProfile OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdslPort upstream service profile."
			::= { dsVdslPortEntry 76 }

		
		-- *******.4.1.6296.*******.8.1.77
		dsVdslPortDownServiceProfile OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdslPort downstream service profile."
			::= { dsVdslPortEntry 77 }

		
		-- *******.4.1.6296.*******.8.1.78
		dsVdslPortModemReset OBJECT-TYPE
			SYNTAX INTEGER { reset(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Reset the Modem."
			::= { dsVdslPortEntry 78 }

		
		-- *******.4.1.6296.*******.8.1.79
		dsVdslPortCPEVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdslPort CPE NOS informations.
				The format of value is 'OS1/OS2/ActiveOS'."
			::= { dsVdslPortEntry 79 }

		
		-- *******.4.1.6296.*******.8.1.80
		dsVdslPortCPEDownload OBJECT-TYPE
			SYNTAX INTEGER
				{
				no(0),
				yes(1),
				done(2),
				fail(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Download the CPE NOS.
				no(0)   : No CPE NOS download.
				yes(1)  : Now downloading.
				done(2) : Download completed.
				fail(3) : Download fail."
			::= { dsVdslPortEntry 80 }

		
		-- *******.4.1.6296.*******.8.1.81
		dsVdslPortLineActiveTimePrev1Day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating Line Active time of previous 1 day."
			::= { dsVdslPortEntry 81 }

		
		-- *******.4.1.6296.*******.8.1.82
		dsVdslPortLineActiveTimeCurr1Day OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accumulating Line Active time of current 1 day."
			::= { dsVdslPortEntry 82 }

		
		-- *******.4.1.6296.*******.8.1.83
		dsVdslPortLineConfProfileClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To remove the line configure profile assigned to 
				a physical port for a particular feature. This value
				            should have 1 that means clear "
			::= { dsVdslPortEntry 83 }

		
		-- *******.4.1.6296.*******.8.1.84
		dsVdslPortAlarmConfProfileClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To remove the alarm configure profile assigned to 
				a physical port for a particular feature. This value
				            should have 1 that means clear "
			::= { dsVdslPortEntry 84 }

		
		-- *******.4.1.6296.*******.8.1.86
		dsVdslPortEwl OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Vdsl Port wire length."
			::= { dsVdslPortEntry 86 }

		
		-- *******.4.1.6296.*******.8.1.87
		dsVdslPortGhsNearEndEwl OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Vdsl Port Near End wire length."
			::= { dsVdslPortEntry 87 }

		
		-- *******.4.1.6296.*******.8.1.88
		dsVdslPortGhsFarEndEwl OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Vdsl Port Far End wire length."
			::= { dsVdslPortEntry 88 }

		
		-- *******.4.1.6296.*******.8.1.89
		dsVdslPortLineActiveTimeClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize Line Active time."
			::= { dsVdslPortEntry 89 }

		
		-- *******.4.1.6296.*******.8.1.90
		dsVdslPortServiceErrorClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize  ."
			::= { dsVdslPortEntry 90 }

		
		-- *******.4.1.6296.*******.8.1.91
		dsVdslPortErrorCRCClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize  ."
			::= { dsVdslPortEntry 91 }

		
		-- *******.4.1.6296.*******.8.1.92
		dsVdslPortErrorSecondClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize Count of ErrorSecond ."
			::= { dsVdslPortEntry 92 }

		
		-- *******.4.1.6296.*******.8.1.93
		dsVdslPortErrorLOFClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize Count of the number of LOF ."
			::= { dsVdslPortEntry 93 }

		
		-- *******.4.1.6296.*******.8.1.94
		dsVdslPortErrorLOSClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize Count of the number of LOS ."
			::= { dsVdslPortEntry 94 }

		
		-- *******.4.1.6296.*******.8.1.95
		dsVdslPortErrorLPRClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize Count of the number of LPR ."
			::= { dsVdslPortEntry 95 }

		
		-- *******.4.1.6296.*******.8.1.96
		dsVdslPortErrorLOLClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize Count of the number of LOL ."
			::= { dsVdslPortEntry 96 }

		
-- 	dsVdslPortErrorUncorrectCRCClear   OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize Count of the number of  ."
-- ::= { dsVdslPortEntry 97 }
-- dsVdslPortErrorCorrectCRCClear    OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize Count of the number of  ."
-- ::= { dsVdslPortEntry 98 }
-- dsVdslPortErrorServiceErrorSecondClear	OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize  ."
-- ::= { dsVdslPortEntry 99 }
-- dsVdslPortErrorCRCSecondClear     OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize  ."
-- ::= { dsVdslPortEntry 100 }
-- dsVdslPortErrorLOFSecondClear    OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize  ."
-- ::= { dsVdslPortEntry 101 }
-- dsVdslPortErrorLOSSecondClear    OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize ."
-- ::= { dsVdslPortEntry 102 }
-- dsVdslPortErrorLOLSecondClear    OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize  ."
-- ::= { dsVdslPortEntry 103 }
-- dsVdslPortErrorSESSecondClear    OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--        "To initialize  ."
-- ::= { dsVdslPortEntry 104 }
-- dsVdslPortErrorUASSecondClear   OBJECT-TYPE
-- SYNTAX      INTEGER  { clear(1)}
-- MAX-ACCESS  read-write
-- STATUS      current
-- DESCRIPTION
--         "To initialize  ."
-- ::= { dsVdslPortEntry 105 }
		-- *******.4.1.6296.*******.8.1.106
		dsVdslPortErrorCountClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"To initialize all error-count-value."
			::= { dsVdslPortEntry 106 }

		
		-- *******.4.1.6296.*******.8.1.108
		dsVdslPortCpeErrorCRC OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 108 }

		
--   dsVdslPortCpeErrorSecond	OBJECT-TYPE
-- SYNTAX		INTEGER
-- MAX-ACCESS	read-only
-- STATUS		current
--   	DESCRIPTION
--   			"."
--   	::= { dsVdslPortEntry 109 }
		-- *******.4.1.6296.*******.8.1.110
		dsVdslPortCpePerfLOF OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 110 }

		
		-- *******.4.1.6296.*******.8.1.111
		dsVdslPortCpePerfLos OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 111 }

		
--   dsVdslPortCpePerfLPR		OBJECT-TYPE
-- SYNTAX		INTEGER,
-- MAX-ACCESS	read-only
-- STATUS		current
-- DESCRIPTION
-- 		"."
-- ::= { dsVdslPortEntry 112 }
		-- *******.4.1.6296.*******.8.1.113
		dsVdslPortCpePerfLol OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 113 }

		
		-- *******.4.1.6296.*******.8.1.114
		dsVdslPortCpePerfCountClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 114 }

		
		-- *******.4.1.6296.*******.8.1.115
		dsVdslPortSuperFrameRxCount OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Rx SuperFrame Count."
			::= { dsVdslPortEntry 115 }

		
		-- *******.4.1.6296.*******.8.1.116
		dsVdslPortSuperFrameTxCount OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Tx SuperFrame Count."
			::= { dsVdslPortEntry 116 }

		
		-- *******.4.1.6296.*******.8.1.117
		dsVdslPortSuperFrameCountClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Rx SuperFrame Count Clear."
			::= { dsVdslPortEntry 117 }

		
		-- *******.4.1.6296.*******.8.1.119
		dsVdslPortPerfRxCRC OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Rx CRC Count."
			::= { dsVdslPortEntry 119 }

		
		-- *******.4.1.6296.*******.8.1.120
		dsVdslPortPerfTxCRC OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Tx CRC Count."
			::= { dsVdslPortEntry 120 }

		
		-- *******.4.1.6296.*******.8.1.121
		dsVdslPortStandard OBJECT-TYPE
			SYNTAX INTEGER
				{
				vdsl(1),
				vdsl2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VDSL Standard."
			::= { dsVdslPortEntry 121 }

		
		-- *******.4.1.6296.*******.8.1.122
		dsVdslPortLineProfile OBJECT-TYPE
			SYNTAX INTEGER
				{
				sym25(1),
				asym50-3b(2),
				asym50-4b(3),
				asym70(4),
				asym100(5),
				sym100(6),
				sym50(7),
				v8a(8),
				v8b(9),
				v8c(10),
				v8d(11),
				v12a(12),
				v12b(13),
				v17a(14),
				v30a(15),
				v12a997(16),
				v12b997(17),
				v17a8k(18)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"        sym25(1),
				asym50-3b(2), 
				asym50-4b(3), 
				asym70(4), 
				asym100(5), 
				sym100(6),
				8a(8),
				8b(9),
				8c(10),
				8d(11),
				12a(12),
				12b(13),
				17a(14),
				30a(15),
				12a997(16),
				12b997(17),
				v17a_8k(18)  
				"
			::= { dsVdslPortEntry 122 }

		
		-- *******.4.1.6296.*******.8.1.123
		dsVdslPortMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				isdn(2),
				adsl(3),
				adsl2(4),
				t-lan(5),
				adsl-s(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"                
				normal(1),
				isdn(2), 
				adsl(3), 
				adsl2(4), 
				t-lan(5),
				adsl-safe(6)                          
				"
			::= { dsVdslPortEntry 123 }

		
		-- *******.4.1.6296.*******.8.1.124
		dsVdslPortProfileSet OBJECT-TYPE
			SYNTAX INTEGER { write(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 124 }

		
		-- *******.4.1.6296.*******.8.1.126
		dsVdslPortUpboEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 126 }

		
		-- *******.4.1.6296.*******.8.1.127
		dsVdslPortChannel OBJECT-TYPE
			SYNTAX INTEGER
				{
				fast(0),
				slow(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 127 }

		
		-- *******.4.1.6296.*******.8.1.128
		dsVdslPortTCMAdmin OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(0),
				disable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 128 }

		
		-- *******.4.1.6296.*******.8.1.129
		dsVdslPortUpSNRMinMargin OBJECT-TYPE
			SYNTAX INTEGER (0..127)
			UNITS "0.25db"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl upstream Min SNR Margin(CO Min SNR Margin)."
			::= { dsVdslPortEntry 129 }

		
		-- *******.4.1.6296.*******.8.1.130
		dsVdslPortDownSNRMinMargin OBJECT-TYPE
			SYNTAX INTEGER (0..127)
			UNITS "0.25db"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl downstream Min SNR Margin(CPE Min SNR Margin)."
			::= { dsVdslPortEntry 130 }

		
		-- *******.4.1.6296.*******.8.1.131
		dsVdslPortUpINP OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			UNITS "unit of 125usec"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl upstream INP."
			::= { dsVdslPortEntry 131 }

		
		-- *******.4.1.6296.*******.8.1.132
		dsVdslPortDownINP OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			UNITS "unit of 125usec"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl downstream INP ."
			::= { dsVdslPortEntry 132 }

		
		-- *******.4.1.6296.*******.8.1.133
		dsVdslPortMiiInBytes OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 133 }

		
		-- *******.4.1.6296.*******.8.1.134
		dsVdslPortMiiInBPS OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 134 }

		
		-- *******.4.1.6296.*******.8.1.135
		dsVdslPortMiiOutBytes OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 135 }

		
		-- *******.4.1.6296.*******.8.1.136
		dsVdslPortMiiOutBPS OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 136 }

		
		-- *******.4.1.6296.*******.8.1.137
		dsVdslPortHardwareAddress OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Hardware Address of User."
			::= { dsVdslPortEntry 137 }

		
		-- *******.4.1.6296.*******.8.1.138
		dsVdslPortRedundancySet OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Redundancy Port Set."
			::= { dsVdslPortEntry 138 }

		
		-- *******.4.1.6296.*******.8.1.139
		dsVdslCpeNegoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				force(0),
				auto(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Nego mode of CPE Ethernet port."
			::= { dsVdslPortEntry 139 }

		
		-- *******.4.1.6296.*******.8.1.140
		dsVdslCpeDuplex OBJECT-TYPE
			SYNTAX INTEGER
				{
				full(0),
				half(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Duplex state of CPE Ethernet port."
			::= { dsVdslPortEntry 140 }

		
		-- *******.4.1.6296.*******.8.1.141
		dsVdslCpeSpeed OBJECT-TYPE
			SYNTAX INTEGER
				{
				100(0),
				10(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Speed of CPE Ethernet port."
			::= { dsVdslPortEntry 141 }

		
		-- *******.4.1.6296.*******.8.1.142
		dsVdslPortLinkUpTime OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "1 minute"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"LinkUp duration time of port."
			::= { dsVdslPortEntry 142 }

		
		-- *******.4.1.6296.*******.8.1.143
		dsVdslPortLastRetraingReason OBJECT-TYPE
			SYNTAX INTEGER
				{
				adminControl(1),
				reProvision(2),
				lprFail(3),
				losFail(4),
				lofFail(5),
				crcFail(6),
				inicrcFail(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslPortEntry 143 }

		
		-- *******.4.1.6296.*******.8.1.145
		dsVdslPortU0BandPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9),
				pbo1000m(10)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl U0 Band Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)
				1000m(10)                       
				"
			::= { dsVdslPortEntry 145 }

		
		-- *******.4.1.6296.*******.8.1.146
		dsVdslPortU1BandPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9),
				pbo1000m(10)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl U1 Band Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)
				1000m(10)                       
				"
			::= { dsVdslPortEntry 146 }

		
		-- *******.4.1.6296.*******.8.1.147
		dsVdslPortU2BandPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9),
				pbo1000m(10)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl U2 Band Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)
				1000m(10)                       
				"
			::= { dsVdslPortEntry 147 }

		
		-- *******.4.1.6296.*******.8.1.148
		dsVdslPortU3BandPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9),
				pbo1000m(10)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl U3 Band Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)
				1000m(10)                       
				"
			::= { dsVdslPortEntry 148 }

		
		-- *******.4.1.6296.*******.8.1.149
		dsVdslPortDownCurrAttainableRate OBJECT-TYPE
			SYNTAX Gauge32
			UNITS "kbps"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates the maximum currently down attainable data rate
				in steps of 1000 bits/second by the Vtu.  This value 
				will be equal to or greater than vdslPhysCurrLineRate.
				Note that for SCM, the minimum and maximum data rates 
				are equal.  Note: 1 kbps = 1000 bps."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			::= { dsVdslPortEntry 149 }

		
		-- *******.4.1.6296.*******.8.1.150
		dsVdslPortUpCurrAttainableRate OBJECT-TYPE
			SYNTAX Gauge32
			UNITS "kbps"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates the maximum currently up attainable data rate
				in steps of 1000 bits/second by the Vtu.  This value 
				will be equal to or greater than vdslPhysCurrLineRate.
				Note that for SCM, the minimum and maximum data rates 
				are equal.  Note: 1 kbps = 1000 bps."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			::= { dsVdslPortEntry 150 }

		
--                                            
-- June 2003 dhlee add dsVdslSlotTable. 
-- For V5972 DMT 50 , V5916-DMT50, V5924-DMT50 etc.
--      
		-- *******.4.1.6296.*******.9
		dsVdslSlotTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslSlotEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsVdslSlotEntry entries."
			::= { dsSwitchModules 9 }

		
		-- *******.4.1.6296.*******.9.1
		dsVdslSlotEntry OBJECT-TYPE
			SYNTAX DsVdslSlotEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular dsVdsl Slot."
			INDEX { dsVdslSlotModuleIndex, dsVdslSlotIndex }
			::= { dsVdslSlotTable 1 }

		
		DsVdslSlotEntry ::=
			SEQUENCE { 
				dsVdslSlotModuleIndex
					INTEGER,
				dsVdslSlotIndex
					INTEGER,
				dsVdslSlotType
					DisplayString,
				dsVdslSlotInstallStatus
					INTEGER,
				dsVdslSlotProfile
					INTEGER,
				dsVdslSlotOptionband
					INTEGER,
				dsVdslSlotHamband
					Counter64,
				dsVdslSlotPSDMaskLevel
					INTEGER,
				dsVdslSlotPBOLength
					INTEGER,
				dsVdslSlotUpServiceProfile
					INTEGER,
				dsVdslSlotDownServiceProfile
					INTEGER,
				dsVdslSlotLIUReboot
					INTEGER,
				dsVdslSlotLIUCPENOSStore
					INTEGER
			 }

--  1 ~ 4 
-- June 2003 newly add --
		-- *******.4.1.6296.*******.9.1.1
		dsVdslSlotModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module(Node).  It is recommended that values are assigned
				contiguously starting from 1."
			::= { dsVdslSlotEntry 1 }

		
		-- *******.4.1.6296.*******.9.1.2
		dsVdslSlotIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				slot.  It is recommended that values are assigned
				contiguously starting from 1."
			::= { dsVdslSlotEntry 2 }

		
--        SYNTAX      DisplayString (SIZE (0..64))    
		-- *******.4.1.6296.*******.9.1.3
		dsVdslSlotType OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of each slot. This string defines the type of slot 
				ex) Vdsl, GBIC."
			::= { dsVdslSlotEntry 3 }

		
		-- *******.4.1.6296.*******.9.1.4
		dsVdslSlotInstallStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				installed(1),
				removed(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current install status of each slot."
			::= { dsVdslSlotEntry 4 }

		
		-- *******.4.1.6296.*******.9.1.5
		dsVdslSlotProfile OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				asym50-998n(51),
				asym50-998e(52),
				sym25-997(53),
				asym50-998a(54),
				asym50-998i(55),
				asym50-998t(56),
				asym50-998s(57),
				asym50-998n-4b(58),
				asym50-998i-4b(59),
				asym50-998a-4b(60),
				sym25-997a(61),
				sym25-997i(62),
				asym70-998n(63),
				asym70-998i(64),
				asym70-998a(65),
				asym100-998n(66),
				asym100-998i(67),
				asym100-998a(68),
				sym100-100-998n(69),
				sym100-100-998i(70),
				sym100-100-998a(71)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl profile ID.
				asym50-998n (51)  PLAN998 Asymmetric
				asym50-998e (52)  PLAN998E Asymmetric
				sym25-997 (53)    PLAN997 Asymmetric
				asym50-998a (54)  ADSL friendly PLAN 998 Asymetric
				asym50-998i (55)  ISDN friendly PLAN 998 Asymetric
				asym50-998t (56)  TLAN friendly PLAN 998 Asymetric
				asym50-998s (57)  ADSL soft mode Asymetric
				asym50-998n-4b(58) PLAN998 Asymmetric 13/50 4 band
				asym50-998i-4b(59) ISDN friendly PLAN 998 13/50 4 band
				asym50-998a-4b(60) ADSL friendly PLAN 998 13/50 4 band
				sym25-997a(61)  ADSL friendly PLAN 997 Symmetric 25/25     
				sym25-997i(62)  ISDN friendly PLAN 997 symmetric 25/25
				asym70-998n(63) PLAN998 Asymmetric 30/70 5 band
				asym70-998i(64) ISDN friendly PLAN 998 asymmetric 30/70 5 band        
				asym70-998a(65) ADSL friendly PLAN 998 asymmetric 30/70 5 band
				asym100-998n(66) PLAN 998n Asymmetric for 6Band DMT 100M 
				asym100-998i(67) PLAN 998i Asymmetric for 6Band DMT 100M
				asym100-998a(68) PLAN 998a Asymmetric for 6Band DMT 100M
				sym100-100-998n(69) PLAN 998n Symmetric for 6Band DMT 100/100M 
				sym100-100-998i(70) PLAN 998i Symmetric for 6Band DMT 100/100M  
				sym100-100-998a(71) PLAN 998a Symmetric for 6Band DMT 100/100M"
			::= { dsVdslSlotEntry 5 }

		
		-- *******.4.1.6296.*******.9.1.6
		dsVdslSlotOptionband OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				annex-B-6-64(1),
				annex-A-6-32(2),
				annex-B-32-64(3),
				exclude(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Optional band ID.
				Annex B 6-64 (1)
				Annex A 6-32 (2)
				Annex B 32-64 (3)
				Exclude Option Band (4)
				"
			::= { dsVdslSlotEntry 6 }

		
		-- *******.4.1.6296.*******.9.1.7
		dsVdslSlotHamband OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Amature Radio(HAM) band ID.
				The value of Ham band is combination of the following value.
				If you want to set band1 and band10, you should set value 257(band1 + band10)
				to the Ham band.   
				band1   [  1.800  -  1.810  ] MHz : RFI Notch                (1)
				band2   [  1.800  -  1.825  ] MHz : KOREA HAM-BAND           (2)
				band3   [  1.810  -  1.825  ] MHz : ANNEX F                  (4)
				band4   [  1.810  -  2.000  ] MHz : ETSI, T1E1               (8)
				band5   [  1.9075 -  1.9125 ] MHz : ANNEX F                  (16)
				band6   [  2.173  -  2.191  ] MHz : DT GMDSS                 (2097152)   0x0000 0020 0000
				band7   [  3.500  -  3.550  ] MHz : KOREA HAM-BAND           (32)
				band8   [  3.500  -  3.575  ] MHz : ANNEX F                  (64) 
				band9   [  3.500  -  3.800  ] MHz : ETSI                     (128)
				band10  [  3.500  -  4.000  ] MHz : T1E1                     (256)
				band11  [  3.747  -  3.754  ] MHz : ANNEX F                  (512)
				band12  [  3.790  -  3.800  ] MHz : KOREA HAM-BAND           (1024)             
				band13  [  3.791  -  3.805  ] MHz : ANNEX F                  (2048)
				band14  [  4.200  -  4.215  ] MHz : DT GMDSS                 (4194304)                   0x0000 0040 0000
				band15  [  5.900  -  6.200  ] MHz : DT DRM radio (Broadcasting)           (8388608)      0x0000 0080 0000
				band16  [  6.300  -  6.320  ] MHz : DT GMDSS                              (16777216)     0x0000 0100 0000
				band17  [  7.000  -  7.100  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI         (4096)
				band18  [  7.000  -  7.300  ] MHz : T1E1                                  (8192)
				band19  [  7.100  -  7.350  ] MHz : DT DRM radio (Broadcasting)           (33554432)     0x0000 0200 0000
				band20  [  8.405  -  8.420  ] MHz : DT GMDSS                              (67108864)     0x0000 0400 0000
				band21  [  9.400  -  9.900  ] MHz : DT DRM radio (Broadcasting)           (134217728)    0x0000 0800 0000   
				band22  [ 10.100  - 10.150  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI, T1E1   (16384)
				band23  [ 11.600  - 12.100  ] MHz : DT DRM radio (Broadcasting)           (268435456)    0x0000 1000 0000
				band24  [ 12.570  - 12.585  ] MHz : DT GMDSS                              (536870912)    0x0000 2000 0000
				band25  [ 13.570  - 13.870  ] MHz : DT DRM radio (Broadcasting)           (1073741824)   0x0000 4000 0000
				band26  [ 14.000  - 14.350  ] MHz : ANNEX F, ETSI, T1E1                   (32768)
				band27  [ 15.100  - 15.800  ] MHz : DT DRM radio (Broadcasting)           (2147483648)   0x0000 8000 0000
				band28  [ 16.795  - 16.810  ] MHz : DT GMDSS                              (4294967296)   0x0001 0000 0000
				band29  [ 17.480  - 17.900  ] MHz : DT DRM radio (Broadcasting)           (8589934592)   0x0002 0000 0000
				band30  [ 18.068  - 18.168  ] MHz : ANNEX F, ETSI, T1E1                   (65536)
				band31  [ 21.000  - 21.450  ] MHz : ANNEX F, ETSI, T1E1                   (131072)       0x0000 0002 0000
				band32  [ 24.890  - 24.990  ] MHz : ANNEX F, ETST, T1E1                   (262144)       0x0000 0004 0000
				band33  [ 28.000  - 29.100  ] MHz : ETSI                                  (524288)       0x0000 0008 0000
				band34  [ 28.000  - 29.700  ] MHz : ANNEX F, ETSI, T1E1                   (1048576)      0x0000 0010 0000
				"
			::= { dsVdslSlotEntry 7 }

		
		-- *******.4.1.6296.*******.9.1.8
		dsVdslSlotPSDMaskLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				old-gains(0),
				aNSI-M1(1),
				aNSI-M2(2),
				eTSI-M1(3),
				eTSI-M2(4),
				aNNEX-F-8-5(5),
				aNSI-M1-EX(6),
				aNSI-M2-EX(7),
				eTSI-M1-EX(8),
				eTSI-M2-EX(9),
				aNNEX-F-11-5(10),
				pSD-K(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl PSD Mask Level.
				notavailable(-1)
				old gains(0)
				ANSI M1(1)
				ANSI M2(2) 
				ETSI M1(3)
				ETSI M2(4)
				ANNEX F 8.5(5) 
				ANSI M1 EX(6)
				ANSI M2 EX(7)
				ETSI M1 EX(8)
				ETSI M2 EX(9)
				ANNEX F 11.5(10)
				PSD-K(11)                  
				"
			::= { dsVdslSlotEntry 8 }

		
		-- *******.4.1.6296.*******.9.1.9
		dsVdslSlotPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)                       
				"
			::= { dsVdslSlotEntry 9 }

		
		-- *******.4.1.6296.*******.9.1.10
		dsVdslSlotUpServiceProfile OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl slot upstream service profile."
			::= { dsVdslSlotEntry 10 }

		
		-- *******.4.1.6296.*******.9.1.11
		dsVdslSlotDownServiceProfile OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl slot downstream service profile."
			::= { dsVdslSlotEntry 11 }

		
		-- *******.4.1.6296.*******.9.1.12
		dsVdslSlotLIUReboot OBJECT-TYPE
			SYNTAX INTEGER { reboot(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl LIU slot reboot."
			::= { dsVdslSlotEntry 12 }

		
		-- *******.4.1.6296.*******.9.1.13
		dsVdslSlotLIUCPENOSStore OBJECT-TYPE
			SYNTAX INTEGER
				{
				no(0),
				yes(1),
				done(2),
				fail(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Store CPE NOS into each dsVdsl slot.
				no(0)   : No CPE NOS store.
				yes(1)  : Now storing.
				done(2) : Storing completed.
				fail(3) : Storing fail"
			::= { dsVdslSlotEntry 13 }

		
--  VDSL 
-- 
-- June 2003 dhlee dsSystemSlotTable
-- Just for V51xxF,                                           
-- 
		-- *******.4.1.6296.*******.10
		dsSystemSlotTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsSystemSlotEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of slotEntry entries."
			::= { dsSwitchModules 10 }

		
		-- *******.4.1.6296.*******.10.1
		dsSystemSlotEntry OBJECT-TYPE
			SYNTAX DsSystemSlotEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular switch Slot."
			INDEX { dsSystemSlotModuleIndex, dsSystemSlotIndex }
			::= { dsSystemSlotTable 1 }

		
		DsSystemSlotEntry ::=
			SEQUENCE { 
				dsSystemSlotModuleIndex
					INTEGER,
				dsSystemSlotIndex
					INTEGER,
				dsSystemSlotType
					DisplayString,
				dsSystemSlotInstallStatus
					INTEGER,
				dsSystemSlotStatus
					INTEGER,
				dsSystemSlotPortNum
					INTEGER
			 }

		-- *******.4.1.6296.*******.10.1.1
		dsSystemSlotModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module(Node).  It is recommended that values are assigned
				contiguously starting from 1."
			::= { dsSystemSlotEntry 1 }

		
		-- *******.4.1.6296.*******.10.1.2
		dsSystemSlotIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				slot.  It is recommended that values are assigned
				contiguously starting from 1."
			::= { dsSystemSlotEntry 2 }

		
		-- *******.4.1.6296.*******.10.1.3
		dsSystemSlotType OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of each slot. This string defines the type of slot
				ex) TX-10/100, FX-100, GBIC, SWU, ADSL2+, ADSL, SHDSL, Vdsl, Ethernet"
			::= { dsSystemSlotEntry 3 }

		
		-- *******.4.1.6296.*******.10.1.4
		dsSystemSlotInstallStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				removed(0),
				installed(1),
				uninstalled(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current install status of each slot."
			::= { dsSystemSlotEntry 4 }

		
		-- *******.4.1.6296.*******.10.1.5
		dsSystemSlotStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				idle(0),
				equip(1),
				init(2),
				ready(3),
				run(4),
				reset(5),
				max(6)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current slot status "
			::= { dsSystemSlotEntry 5 }

		
		-- *******.4.1.6296.*******.10.1.6
		dsSystemSlotPortNum OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of port on the slot."
			::= { dsSystemSlotEntry 6 }

		
--  Lastest user MAC table
-- 
		-- *******.4.1.6296.*******.11
		dsSwitchUserMacTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsSwitchUserMacEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsSwitchUserMacEntry entries."
			::= { dsSwitchModules 11 }

		
		-- *******.4.1.6296.*******.11.1
		dsSwitchUserMacEntry OBJECT-TYPE
			SYNTAX DsSwitchUserMacEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular switch User mac address."
			INDEX { dsUserMacModuleIndex, dsUserMacPortIndex }
			::= { dsSwitchUserMacTable 1 }

		
		DsSwitchUserMacEntry ::=
			SEQUENCE { 
				dsUserMacModuleIndex
					INTEGER,
				dsUserMacPortIndex
					INTEGER,
				dsUserMacAddress1
					PhysAddress,
				dsUserMacAddress2
					PhysAddress,
				dsUserMacAddress3
					PhysAddress,
				dsUserMacAddress4
					PhysAddress,
				dsUserMacAddress5
					PhysAddress,
				dsUserMacAddress6
					PhysAddress,
				dsUserMacAddress7
					PhysAddress,
				dsUserMacAddress8
					PhysAddress
			 }

		-- *******.4.1.6296.*******.11.1.1
		dsUserMacModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				module(Node).  It is recommended that values are assigned
				contiguously starting from 1."
			::= { dsSwitchUserMacEntry 1 }

		
		-- *******.4.1.6296.*******.11.1.2
		dsUserMacPortIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				port.  It is recommended that values are assigned
				contiguously starting from 1."
			::= { dsSwitchUserMacEntry 2 }

		
		-- *******.4.1.6296.*******.11.1.3
		dsUserMacAddress1 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The first entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 3 }

		
		-- *******.4.1.6296.*******.11.1.4
		dsUserMacAddress2 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The second entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 4 }

		
		-- *******.4.1.6296.*******.11.1.5
		dsUserMacAddress3 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The third entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 5 }

		
		-- *******.4.1.6296.*******.11.1.6
		dsUserMacAddress4 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The fourth entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 6 }

		
		-- *******.4.1.6296.*******.11.1.7
		dsUserMacAddress5 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The fifth entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 7 }

		
		-- *******.4.1.6296.*******.11.1.8
		dsUserMacAddress6 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The sixth entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 8 }

		
		-- *******.4.1.6296.*******.11.1.9
		dsUserMacAddress7 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The seventh entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 9 }

		
		-- *******.4.1.6296.*******.11.1.10
		dsUserMacAddress8 OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The eighth entry of lastest user mac address."
			::= { dsSwitchUserMacEntry 10 }

		
--  ARP informations.
-- 
		-- *******.4.1.6296.*******.12
		dsSwitchAtTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsSwitchAtEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsSwitchUserMacEntry entries."
			::= { dsSwitchModules 12 }

		
		-- *******.4.1.6296.*******.12.1
		dsSwitchAtEntry OBJECT-TYPE
			SYNTAX DsSwitchAtEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing management information applicable
				to a particular switch ARP."
			INDEX { dsSwitchAtifIndex, dsSwitchAtNetAddress }
			::= { dsSwitchAtTable 1 }

		
		DsSwitchAtEntry ::=
			SEQUENCE { 
				dsSwitchAtifIndex
					INTEGER,
				dsSwitchAtNetAddress
					IpAddress,
				dsSwitchAtMacAddress
					PhysAddress,
				dsSwitchAtVID
					INTEGER
			 }

		-- *******.4.1.6296.*******.12.1.1
		dsSwitchAtifIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It means that the ifIndex in MIB-II Interface of physical port.."
			::= { dsSwitchAtEntry 1 }

		
		-- *******.4.1.6296.*******.12.1.2
		dsSwitchAtNetAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The IP address in ARP informations."
			::= { dsSwitchAtEntry 2 }

		
		-- *******.4.1.6296.*******.12.1.3
		dsSwitchAtMacAddress OBJECT-TYPE
			SYNTAX PhysAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The physical address in ARP informations."
			::= { dsSwitchAtEntry 3 }

		
		-- *******.4.1.6296.*******.12.1.4
		dsSwitchAtVID OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The VLAN ID in ARP informations."
			::= { dsSwitchAtEntry 4 }

		
--  Network Time Protocol (NTP, SNTP)
-- 
		-- *******.4.1.6296.*******.17
		dsNetworkTimeProtocolInfo OBJECT IDENTIFIER ::= { dsSwitchModules 17 }

		
		-- *******.4.1.6296.*******.17.1
		dsNTPMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				ntp(1),
				sntp(2),
				disable(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The mode of Network Time Protocol.."
			::= { dsNetworkTimeProtocolInfo 1 }

		
		-- *******.4.1.6296.*******.17.2
		dsNTPBindAddressType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The bind-address Type of Network Time Protocol.."
			::= { dsNetworkTimeProtocolInfo 2 }

		
		-- *******.4.1.6296.*******.17.3
		dsNTPBindAddress OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The bind-address of Network Time Protocol.."
			::= { dsNetworkTimeProtocolInfo 3 }

		
		-- *******.4.1.6296.*******.17.10
		dsNTPServerTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsNTPServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsNTPServerEntry entries."
			::= { dsNetworkTimeProtocolInfo 10 }

		
		-- *******.4.1.6296.*******.17.10.1
		dsNTPServerEntry OBJECT-TYPE
			SYNTAX DsNTPServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing Network Time Protocol information."
			INDEX { dsNTPServerIndex }
			::= { dsNTPServerTable 1 }

		
		DsNTPServerEntry ::=
			SEQUENCE { 
				dsNTPServerIndex
					INTEGER,
				dsNTPServerName
					OCTET STRING,
				dsNTPServerRowStatus
					INTEGER
			 }

		-- *******.4.1.6296.*******.*********
		dsNTPServerIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				node. It is recommended that values are assinged 
				contiguously starting from 1."
			::= { dsNTPServerEntry 1 }

		
		-- *******.4.1.6296.*******.*********
		dsNTPServerName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsNTPServerEntry 2 }

		
		-- *******.4.1.6296.*******.*********
		dsNTPServerRowStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				active(1),
				create(2),
				destroy(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsNTPServerEntry 3 }

		
--  Domain Name Service (DNS)
-- 
		-- *******.4.1.6296.*******.18
		dsDomainNameServiceInfo OBJECT IDENTIFIER ::= { dsSwitchModules 18 }

		
		-- *******.4.1.6296.*******.18.1
		dsDNSDomainName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsDomainNameServiceInfo 1 }

		
		-- *******.4.1.6296.*******.18.10
		dsDNSServerTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsDNSServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsNTPServerEntry entries."
			::= { dsDomainNameServiceInfo 10 }

		
		-- *******.4.1.6296.*******.18.10.1
		dsDNSServerEntry OBJECT-TYPE
			SYNTAX DsDNSServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing Network Time Protocol information."
			INDEX { dsDNSServerIndex }
			::= { dsDNSServerTable 1 }

		
		DsDNSServerEntry ::=
			SEQUENCE { 
				dsDNSServerIndex
					INTEGER,
				dsDNSServerAddressType
					InetAddressType,
				dsDNSServerAddress
					InetAddress,
				dsDNSServerRowStatus
					INTEGER
			 }

		-- *******.4.1.6296.*******.*********
		dsDNSServerIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				node. It is recommended that values are assinged 
				contiguously starting from 1."
			::= { dsDNSServerEntry 1 }

		
		-- *******.4.1.6296.*******.*********
		dsDNSServerAddressType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsDNSServerEntry 2 }

		
		-- *******.4.1.6296.*******.*********
		dsDNSServerAddress OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsDNSServerEntry 3 }

		
		-- *******.4.1.6296.*******.*********
		dsDNSServerRowStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				active(1),
				create(2),
				destroy(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsDNSServerEntry 4 }

		
--  Syslog 
-- 
		-- *******.4.1.6296.*******.19
		dsSyslogInfo OBJECT IDENTIFIER ::= { dsSwitchModules 19 }

		
		-- *******.4.1.6296.*******.19.1
		dsSyslogBindAddressType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogInfo 1 }

		
		-- *******.4.1.6296.*******.19.2
		dsSyslogBindAddress OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogInfo 2 }

		
		-- *******.4.1.6296.*******.19.3
		dsSyslogLocalCode OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogInfo 3 }

		
		-- *******.4.1.6296.*******.19.10
		dsSyslogConfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsSyslogConfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsSyslogConfEntry entries."
			::= { dsSyslogInfo 10 }

		
		-- *******.4.1.6296.*******.19.10.1
		dsSyslogConfEntry OBJECT-TYPE
			SYNTAX DsSyslogConfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing Syslog Configuration information."
			INDEX { dsSyslogConfIndex }
			::= { dsSyslogConfTable 1 }

		
		DsSyslogConfEntry ::=
			SEQUENCE { 
				dsSyslogConfIndex
					INTEGER,
				dsSyslogConfFacility
					INTEGER,
				dsSyslogConfSeverity
					INTEGER,
				dsSyslogConfTarget
					INTEGER,
				dsSyslogConfStorage
					INTEGER,
				dsSyslogConfRemoteAddrType
					InetAddressType,
				dsSyslogConfRemoteAddress
					InetAddress,
				dsSyslogConfRowStatus
					INTEGER
			 }

		-- *******.4.1.6296.*******.*********
		dsSyslogConfIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				node. It is recommended that values are assinged 
				contiguously starting from 1."
			::= { dsSyslogConfEntry 1 }

		
		-- *******.4.1.6296.*******.*********
		dsSyslogConfFacility OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(0),
				user(1),
				mail(2),
				daemon(3),
				auth(4),
				syslog(5),
				lpr(6),
				news(7),
				uucp(8),
				cron(9),
				authPriv(10),
				ftp(11),
				ntp(12),
				security(13),
				console(14),
				local0(16),
				local1(17),
				local2(18),
				local3(19),
				local4(20),
				local5(21),
				local6(22),
				local7(23)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogConfEntry 2 }

		
		-- *******.4.1.6296.*******.*********
		dsSyslogConfSeverity OBJECT-TYPE
			SYNTAX INTEGER
				{
				emerg(0),
				alert(1),
				crit(2),
				err(3),
				warning(4),
				notice(5),
				info(6),
				debug(7),
				all(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogConfEntry 3 }

		
		-- *******.4.1.6296.*******.*********
		dsSyslogConfTarget OBJECT-TYPE
			SYNTAX INTEGER
				{
				local(0),
				console(1),
				remote(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogConfEntry 4 }

		
		-- *******.4.1.6296.*******.*********
		dsSyslogConfStorage OBJECT-TYPE
			SYNTAX INTEGER
				{
				volatile(0),
				non-volatile(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogConfEntry 5 }

		
		-- *******.4.1.6296.*******.*********
		dsSyslogConfRemoteAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogConfEntry 6 }

		
		-- *******.4.1.6296.*******.*********
		dsSyslogConfRemoteAddress OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogConfEntry 7 }

		
		-- *******.4.1.6296.*******.*********
		dsSyslogConfRowStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				active(1),
				create(2),
				destroy(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsSyslogConfEntry 8 }

		
--  
-- TC table
-- 
		-- *******.4.1.6296.*******.20
		dsTcTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsTcEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of dsTcEntrry entries."
			::= { dsSwitchModules 20 }

		
		-- *******.4.1.6296.*******.20.1
		dsTcEntry OBJECT-TYPE
			SYNTAX DsTcEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry containing dsTc information."
			INDEX { dsTcNodeIndex, dsTcIndex }
			::= { dsTcTable 1 }

		
		DsTcEntry ::=
			SEQUENCE { 
				dsTcNodeIndex
					INTEGER,
				dsTcIndex
					INTEGER,
				dsTcGuaranteedBandwidth
					INTEGER,
				dsTcMaxBandwidth
					INTEGER,
				dsTcWeight
					INTEGER,
				dsTcReset
					INTEGER,
				dsTcStatsCount
					Counter64,
				dsTcStatsCount5Sec
					INTEGER,
				dsTcStatsCount1Min
					INTEGER,
				dsTcStatsCount10Min
					INTEGER,
				dsTcStatsCountReset
					INTEGER
			 }

		-- *******.4.1.6296.*******.20.1.1
		dsTcNodeIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				node. It is recommended that values are assinged 
				contiguously starting from 1."
			::= { dsTcEntry 1 }

		
		-- *******.4.1.6296.*******.20.1.2
		dsTcIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A unique value, greater than zero, for each
				dsTc. It is recommended that values are assinged 
				contiguously starting from 1."
			::= { dsTcEntry 2 }

		
		-- *******.4.1.6296.*******.20.1.3
		dsTcGuaranteedBandwidth OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"It means reserved bandwidth of route applied to
				processing protocol. Bandwidth can be from 1 to 16Gbps."
			::= { dsTcEntry 3 }

		
		-- *******.4.1.6296.*******.20.1.4
		dsTcMaxBandwidth OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"It means maximum bandwidth of route applied to 
				processing protocol. Bandwidth can be from 1 to 16Gbps."
			::= { dsTcEntry 4 }

		
		-- *******.4.1.6296.*******.20.1.5
		dsTcWeight OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"It means weight limit of route applied to processing 
				protocol. Weight limit can be from 1 to 1024."
			::= { dsTcEntry 5 }

		
		-- *******.4.1.6296.*******.20.1.6
		dsTcReset OBJECT-TYPE
			SYNTAX INTEGER { reset(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Reset configuration about route applied to processing protocol"
			::= { dsTcEntry 6 }

		
		-- *******.4.1.6296.*******.20.1.7
		dsTcStatsCount OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total bits number of packets matching with a tc configuration, 
				the counter value is a cumulative sum"
			::= { dsTcEntry 7 }

		
		-- *******.4.1.6296.*******.20.1.8
		dsTcStatsCount5Sec OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The average bps of packets matching with 
				dsTc configuration during 5 sec."
			::= { dsTcEntry 8 }

		
		-- *******.4.1.6296.*******.20.1.9
		dsTcStatsCount1Min OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The average bps of packets matching with 
				dsTc configuration during 1 minutes."
			::= { dsTcEntry 9 }

		
		-- *******.4.1.6296.*******.20.1.10
		dsTcStatsCount10Min OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The average bps of packets matching with 
				dsTc configuration during 10 minutes."
			::= { dsTcEntry 10 }

		
		-- *******.4.1.6296.*******.20.1.11
		dsTcStatsCountReset OBJECT-TYPE
			SYNTAX INTEGER { reset(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Reset Tc Statistics Counter Registers including tcStatsCount, 
				tcStatsCount5Sec, tcStatsCount1Min and tcStatsCount10Min"
			::= { dsTcEntry 11 }

		
--  dasan vdslLineConfProfile table
-- 
-- 
		-- *******.4.1.6296.*******.25
		dsVdslLineConfProfileTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslLineConfProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table contains information on the VDSL line
				configuration.  One entry in this table reflects a
				profile defined by a manager which can be used to
				configure the VDSL line.
				
				Entries in this table MUST be maintained in a 
				persistent manner."
			::= { dsSwitchModules 25 }

		
		-- *******.4.1.6296.*******.25.1
		dsVdslLineConfProfileEntry OBJECT-TYPE
			SYNTAX DsVdslLineConfProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Each entry consists of a list of parameters that
				represents the configuration of a VDSL line.   
				
				A default profile with an index of 'DEFVAL', will 
				always exist and its parameters will be set to vendor 
				specific values, unless otherwise specified in this 
				document."
			INDEX { dsVdslLineConfProfileName }
			::= { dsVdslLineConfProfileTable 1 }

		
		DsVdslLineConfProfileEntry ::=
			SEQUENCE { 
				dsVdslLineConfProfileName
					DisplayString,
				dsVdslLineConfDownMaxSnrMgn
					Unsigned32,
				dsVdslLineConfDownMinSnrMgn
					Unsigned32,
				dsVdslLineConfDownTargetSnrMgn
					Unsigned32,
				dsVdslLineConfUpMaxSnrMgn
					Unsigned32,
				dsVdslLineConfUpMinSnrMgn
					Unsigned32,
				dsVdslLineConfUpTargetSnrMgn
					Unsigned32,
				dsVdslLineConfDownMaxDataRate
					Unsigned32,
				dsVdslLineConfDownMinDataRate
					Unsigned32,
				dsVdslLineConfUpMaxDataRate
					Unsigned32,
				dsVdslLineConfUpMinDataRate
					Unsigned32,
				dsVdslLineConfDownMaxInterDelay
					INTEGER,
				dsVdslLineConfUpMaxInterDelay
					INTEGER,
				dsVdslLineConfHamband
					Counter64,
				dsVdslLineConfDownINP
					INTEGER,
				dsVdslLineConfUpINP
					INTEGER,
				dsVdslLineConfPBOLength
					INTEGER,
				dsVdslLineConfPSDMaskLevel
					INTEGER,
				dsVdslLineConfTCMAdmin
					INTEGER,
				dsVdslLineConfUpboEnable
					INTEGER,
				dsVdslLineConfChannel
					INTEGER,
				dsVdslLineConfProfRowStatus
					RowStatus
			 }

		-- *******.4.1.6296.*******.25.1.1
		dsVdslLineConfProfileName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..32))
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object identifies a row in this table.  
				
				A default profile with an index of 'DEFVAL', will 
				always exist and its parameters will be set to vendor 
				specific values, unless otherwise specified in this 
				document."
			::= { dsVdslLineConfProfileEntry 1 }

		
--     dsVdslLineConfDownMaxPwr OBJECT-TYPE
-- SYNTAX       Unsigned32 (0..58)
-- UNITS        "0.25dBm"
-- MAX-ACCESS   read-create
-- STATUS       current
-- DESCRIPTION
--     "Specifies the maximum aggregate downstream power
--     level in the range 0 to 14.5 dBm."
-- REFERENCE    "T1E1.4/2000-009R3, Part 1, common spec"
-- DEFVAL       { 0 }
-- ::= { dsVdslLineConfProfileEntry 2 }
-- dsVdslLineConfUpMaxPwr OBJECT-TYPE
-- SYNTAX       Unsigned32 (0..58)
-- UNITS        "0.25dBm"
-- MAX-ACCESS   read-create
-- STATUS       current
-- DESCRIPTION
--     "Specifies the maximum aggregate upstream power
--     level in the range 0 to 14.5 dBm."
-- REFERENCE    "T1E1.4/2000-009R3, Part 1, common spec"
-- DEFVAL       { 0 }
-- ::= { dsVdslLineConfProfileEntry 3 }
		-- *******.4.1.6296.*******.25.1.4
		dsVdslLineConfDownMaxSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum downstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 4 }

		
		-- *******.4.1.6296.*******.25.1.5
		dsVdslLineConfDownMinSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum downstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 5 }

		
		-- *******.4.1.6296.*******.25.1.6
		dsVdslLineConfDownTargetSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the target downstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB.
				This is the Noise Margin the transceivers must achieve 
				with a BER of 10^-7 or better to successfully complete 
				initialization."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 6 }

		
		-- *******.4.1.6296.*******.25.1.7
		dsVdslLineConfUpMaxSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum upstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 7 }

		
		-- *******.4.1.6296.*******.25.1.8
		dsVdslLineConfUpMinSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum upstream Signal/Noise Margin
				in units of 0.25 dB, for a range of 0 to 31.75 dB."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 8 }

		
		-- *******.4.1.6296.*******.25.1.9
		dsVdslLineConfUpTargetSnrMgn OBJECT-TYPE
			SYNTAX Unsigned32 (0..127)
			UNITS "0.25dBm"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the target upstream Signal/Noise Margin in 
				units of 0.25 dB, for a range of 0 to 31.75 dB.  This 
				is the Noise Margin the transceivers must achieve with 
				a BER of 10^-7 or better to successfully complete 
				initialization."
			REFERENCE
				"T1E1.4/2000-009R3, Part 1, common spec"
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 9 }

		
		-- *******.4.1.6296.*******.25.1.10
		dsVdslLineConfDownMaxDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum downstream slow channel
				data rate in steps of 1000 bits/second.
				
				The maximum aggregate downstream transmit speed
				of the line can be derived from the sum of maximum
				downstream fast and slow channel data rates."
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 10 }

		
		-- *******.4.1.6296.*******.25.1.11
		dsVdslLineConfDownMinDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum downstream slow channel
				data rate in steps of 1000 bits/second.
				
				The minimum aggregate downstream transmit speed
				of the line can be derived from the sum of minimum
				downstream fast and slow channel data rates."
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 11 }

		
		-- *******.4.1.6296.*******.25.1.12
		dsVdslLineConfUpMaxDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum upstream slow channel
				data rate in steps of 1000 bits/second."
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 12 }

		
		-- *******.4.1.6296.*******.25.1.13
		dsVdslLineConfUpMinDataRate OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the minimum upstream slow channel
				data rate in steps of 1000 bits/second."
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 13 }

		
		-- *******.4.1.6296.*******.25.1.16
		dsVdslLineConfDownMaxInterDelay OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			UNITS "milliseconds"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum interleave delay for the
				downstream slow channel."
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 16 }

		
		-- *******.4.1.6296.*******.25.1.17
		dsVdslLineConfUpMaxInterDelay OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			UNITS "milliseconds"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Specifies the maximum interleave delay for the
				upstream slow channel."
			DEFVAL { 0 }
			::= { dsVdslLineConfProfileEntry 17 }

		
		-- *******.4.1.6296.*******.25.1.20
		dsVdslLineConfHamband OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"dsVdsl Amature Radio(HAM) band ID.
				The value of Ham band is combination of the following value.
				If you want to set band1 and band10, you should set value 257(band1 + band10)
				to the Ham band.   
				band1   [  1.800  -  1.810  ] MHz : RFI Notch                (1)
				band2   [  1.800  -  1.825  ] MHz : KOREA HAM-BAND           (2)
				band3   [  1.810  -  1.825  ] MHz : ANNEX F                  (4)
				band4   [  1.810  -  2.000  ] MHz : ETSI, T1E1               (8)
				band5   [  1.9075 -  1.9125 ] MHz : ANNEX F                  (16)
				band6   [  2.173  -  2.191  ] MHz : DT GMDSS                 (2097152)					0x0000 0020 0000
				band7   [  3.500  -  3.550  ] MHz : KOREA HAM-BAND           (32)
				band8   [  3.500  -  3.575  ] MHz : ANNEX F                  (64) 
				band9   [  3.500  -  3.800  ] MHz : ETSI                     (128)
				band10  [  3.500  -  4.000  ] MHz : T1E1                     (256)
				band11  [  3.747  -  3.754  ] MHz : ANNEX F                  (512)
				band12  [  3.790  -  3.800  ] MHz : KOREA HAM-BAND           (1024)             
				band13  [  3.791  -  3.805  ] MHz : ANNEX F                  (2048)
				band14  [  4.200  -  4.215  ] MHz : DT GMDSS                 (4194304)					0x0000 0040 0000
				band15  [  5.900  -  6.200  ] MHz : DT DRM radio (Broadcasting)           (8388608)		0x0000 0080 0000
				band16  [  6.300  -  6.320  ] MHz : DT GMDSS                              (16777216)		0x0000 0100 0000
				band17  [  7.000  -  7.100  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI         (4096)
				band18  [  7.000  -  7.300  ] MHz : T1E1                                  (8192)
				band19  [  7.100  -  7.350  ] MHz : DT DRM radio (Broadcasting)           (33554432)		0x0000 0200 0000
				band20  [  8.405  -  8.420  ] MHz : DT GMDSS                              (67108864)		0x0000 0400 0000
				band21  [  9.400  -  9.900  ] MHz : DT DRM radio (Broadcasting)           (134217728)	0x0000 0800 0000   
				band22  [ 10.100  - 10.150  ] MHz : KOREA HAM-BAND, ANNEX F, ETSI, T1E1   (16384)
				band23  [ 11.600  - 12.100  ] MHz : DT DRM radio (Broadcasting)           (268435456)	0x0000 1000 0000
				band24  [ 12.570  - 12.585  ] MHz : DT GMDSS                              (536870912)	0x0000 2000 0000
				band25  [ 13.570  - 13.870  ] MHz : DT DRM radio (Broadcasting)           (1073741824)	0x0000 4000 0000
				band26  [ 14.000  - 14.350  ] MHz : ANNEX F, ETSI, T1E1                   (32768)
				band27  [ 15.100  - 15.800  ] MHz : DT DRM radio (Broadcasting)           (2147483648)	0x0000 8000 0000
				band28  [ 16.795  - 16.810  ] MHz : DT GMDSS                              (4294967296)	0x0001 0000 0000
				band29  [ 17.480  - 17.900  ] MHz : DT DRM radio (Broadcasting)           (8589934592)	0x0002 0000 0000
				band30  [ 18.068  - 18.168  ] MHz : ANNEX F, ETSI, T1E1                   (65536)
				band31  [ 21.000  - 21.450  ] MHz : ANNEX F, ETSI, T1E1                   (131072)		0x0000 0002 0000
				band32  [ 24.890  - 24.990  ] MHz : ANNEX F, ETST, T1E1                   (262144)		0x0000 0004 0000
				band33  [ 28.000  - 29.100  ] MHz : ETSI                                  (524288)		0x0000 0008 0000
				band34  [ 28.000  - 29.700  ] MHz : ANNEX F, ETSI, T1E1                   (1048576)		0x0000 0010 0000
				"
			::= { dsVdslLineConfProfileEntry 20 }

		
		-- *******.4.1.6296.*******.25.1.21
		dsVdslLineConfDownINP OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			UNITS "unit of 125usec"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Vdsl downstream INP ."
			::= { dsVdslLineConfProfileEntry 21 }

		
		-- *******.4.1.6296.*******.25.1.22
		dsVdslLineConfUpINP OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			UNITS "unit of 125usec"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Vdsl upstream INP."
			::= { dsVdslLineConfProfileEntry 22 }

		
		-- *******.4.1.6296.*******.25.1.23
		dsVdslLineConfPBOLength OBJECT-TYPE
			SYNTAX INTEGER
				{
				pbo100m(1),
				pbo200m(2),
				pbo300m(3),
				pbo400m(4),
				pbo500m(5),
				pbo600m(6),
				pbo700m(7),
				pbo800m(8),
				pbo900m(9),
				pbo1000m(10)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl Power Back-Off Length.
				100m(1)
				200m(2)
				300m(3)
				400m(4)
				500m(5)
				600m(6)
				700m(7)
				800m(8)
				900m(9)
				1000m(10)                       
				"
			::= { dsVdslLineConfProfileEntry 23 }

		
		-- *******.4.1.6296.*******.25.1.24
		dsVdslLineConfPSDMaskLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				notavailable(-1),
				old-gains(0),
				aNSI-M1(1),
				aNSI-M2(2),
				eTSI-M1(3),
				eTSI-M2(4),
				aNNEX-F-8-5(5),
				aNSI-M1-EX(6),
				aNSI-M2-EX(7),
				eTSI-M1-EX(8),
				eTSI-M2-EX(9),
				aNNEX-F-11-5(10),
				pSD-K(11)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"dsVdsl PSD Mask Level.
				notavailable(-1)
				old gains(0)
				ANSI M1(1)
				ANSI M2(2) 
				ETSI M1(3)
				ETSI M2(4)
				ANNEX F 8.5(5) 
				ANSI M1 EX(6)
				ANSI M2 EX(7)
				ETSI M1 EX(8)
				ETSI M2 EX(9)
				ANNEX F 11.5(10)                                      
				PSD-K(11)
				"
			::= { dsVdslLineConfProfileEntry 24 }

		
		-- *******.4.1.6296.*******.25.1.25
		dsVdslLineConfTCMAdmin OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(0),
				disable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslLineConfProfileEntry 25 }

		
		-- *******.4.1.6296.*******.25.1.26
		dsVdslLineConfUpboEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslLineConfProfileEntry 26 }

		
		-- *******.4.1.6296.*******.25.1.27
		dsVdslLineConfChannel OBJECT-TYPE
			SYNTAX INTEGER
				{
				fast(0),
				slow(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslLineConfProfileEntry 27 }

		
		-- *******.4.1.6296.*******.25.1.28
		dsVdslLineConfProfRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to create a new row or modify or
				delete an existing row in this table.
				
				A profile activated by setting this object to 'active'.  
				When 'active' is set, the system will validate the profile.
				Before a profile can be deleted or taken out of service, 
				(by setting this object to 'destroy' or 'outOfService') 
				it must be first unreferenced from all associated lines."
			::= { dsVdslLineConfProfileEntry 28 }

		
--  dasan vdslCpeCount table
-- 
-- 
		-- *******.4.1.6296.*******.26
		dsVdslCpeCountTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslCpeCountEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			::= { dsSwitchModules 26 }

		
		-- *******.4.1.6296.*******.26.1
		dsVdslCpeCountEntry OBJECT-TYPE
			SYNTAX DsVdslCpeCountEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			INDEX { dsVdslCpeCountFeFECB0 }
			::= { dsVdslCpeCountTable 1 }

		
		DsVdslCpeCountEntry ::=
			SEQUENCE { 
				dsVdslCpeCountFeFECB0
					INTEGER,
				dsVdslCpeCountFeFECB1
					INTEGER,
				dsVdslCpeCountFeCRCB0
					INTEGER,
				dsVdslCpeCountFeCRCB1
					INTEGER,
				dsVdslCpeCountFeFLOS
					INTEGER,
				dsVdslCpeCountFeSEF
					INTEGER,
				dsVdslCpeCountFeFECUnCrrB0
					INTEGER,
				dsVdslCpeCountFeFECUnCrrB1
					INTEGER,
				dsVdslCpeCountTxFrameCnt
					INTEGER,
				dsVdslCpeCountRxFrameCnt
					INTEGER,
				dsVdslCpeCountTxCrcCnt
					INTEGER,
				dsVdslCpeCountRxCrcCnt
					INTEGER,
				dsVdslCpeCountDropFrameCnt
					INTEGER
			 }

		-- *******.4.1.6296.*******.26.1.1
		dsVdslCpeCountFeFECB0 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 1 }

		
		-- *******.4.1.6296.*******.26.1.2
		dsVdslCpeCountFeFECB1 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 2 }

		
		-- *******.4.1.6296.*******.26.1.3
		dsVdslCpeCountFeCRCB0 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 3 }

		
		-- *******.4.1.6296.*******.26.1.4
		dsVdslCpeCountFeCRCB1 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 4 }

		
		-- *******.4.1.6296.*******.26.1.5
		dsVdslCpeCountFeFLOS OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 5 }

		
		-- *******.4.1.6296.*******.26.1.6
		dsVdslCpeCountFeSEF OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 6 }

		
		-- *******.4.1.6296.*******.26.1.7
		dsVdslCpeCountFeFECUnCrrB0 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 7 }

		
		-- *******.4.1.6296.*******.26.1.8
		dsVdslCpeCountFeFECUnCrrB1 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 8 }

		
		-- *******.4.1.6296.*******.26.1.9
		dsVdslCpeCountTxFrameCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 9 }

		
		-- *******.4.1.6296.*******.26.1.10
		dsVdslCpeCountRxFrameCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 10 }

		
		-- *******.4.1.6296.*******.26.1.11
		dsVdslCpeCountTxCrcCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 11 }

		
		-- *******.4.1.6296.*******.26.1.12
		dsVdslCpeCountRxCrcCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 12 }

		
		-- *******.4.1.6296.*******.26.1.13
		dsVdslCpeCountDropFrameCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCpeCountEntry 13 }

		
--  dasan vdslCoCount table
-- 
-- 
		-- *******.4.1.6296.*******.27
		dsVdslCoCountTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslCoCountEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			::= { dsSwitchModules 27 }

		
		-- *******.4.1.6296.*******.27.1
		dsVdslCoCountEntry OBJECT-TYPE
			SYNTAX DsVdslCoCountEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"."
			INDEX { dsVdslCoCountRxFrame }
			::= { dsVdslCoCountTable 1 }

		
		DsVdslCoCountEntry ::=
			SEQUENCE { 
				dsVdslCoCountRxFrame
					INTEGER,
				dsVdslCoCountRxCrcErr
					INTEGER,
				dsVdslCoCountRxDrop
					INTEGER,
				dsVdslCoCountTxFrame
					INTEGER,
				dsVdslCoCountTxDrop
					INTEGER,
				dsVdslCoCountEnetCrcErrCnt
					INTEGER
			 }

		-- *******.4.1.6296.*******.27.1.1
		dsVdslCoCountRxFrame OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCoCountEntry 1 }

		
		-- *******.4.1.6296.*******.27.1.2
		dsVdslCoCountRxCrcErr OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCoCountEntry 2 }

		
		-- *******.4.1.6296.*******.27.1.3
		dsVdslCoCountRxDrop OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCoCountEntry 3 }

		
		-- *******.4.1.6296.*******.27.1.4
		dsVdslCoCountTxFrame OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCoCountEntry 4 }

		
		-- *******.4.1.6296.*******.27.1.5
		dsVdslCoCountTxDrop OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCoCountEntry 5 }

		
		-- *******.4.1.6296.*******.27.1.6
		dsVdslCoCountEnetCrcErrCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { dsVdslCoCountEntry 6 }

		
--  
-- 
-- *******.4.1.6296.*******.28
		-- *******.4.1.6296.*******.28
		dsVdslCpeInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DsVdslCpeInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table contains information of the CPEs."
			::= { dsSwitchModules 28 }

		
--  
-- 
-- *******.4.1.6296.*******.28.1
		-- *******.4.1.6296.*******.28.1
		dsVdslCpeInfoEntry OBJECT-TYPE
			SYNTAX DsVdslCpeInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Each entry consists of a list of parameters that
				represents the information of a CPE."
			INDEX { ifIndex }
			::= { dsVdslCpeInfoTable 1 }

		
		DsVdslCpeInfoEntry ::=
			SEQUENCE { 
				dsVdslCpeVersion
					DisplayString,
				dsVdslCpeVendorId
					DisplayString,
				dsVdslCpeVendorName
					DisplayString,
				dsVdslCpeModelName
					DisplayString,
				dsVdslCpeSerialNumber
					DisplayString
			 }

--  
-- 
-- *******.4.1.6296.*******.28.1.1
		-- *******.4.1.6296.*******.28.1.1
		dsVdslCpeVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"CPE NOS version"
			::= { dsVdslCpeInfoEntry 1 }

		
--  
-- 
-- *******.4.1.6296.*******.28.1.2
		-- *******.4.1.6296.*******.28.1.2
		dsVdslCpeVendorId OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"CPE vendor ID"
			::= { dsVdslCpeInfoEntry 2 }

		
--  
-- 
-- *******.4.1.6296.*******.28.1.3
		-- *******.4.1.6296.*******.28.1.3
		dsVdslCpeVendorName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"CPE vendor name"
			::= { dsVdslCpeInfoEntry 3 }

		
--  
-- 
-- *******.4.1.6296.*******.28.1.4
		-- *******.4.1.6296.*******.28.1.4
		dsVdslCpeModelName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"CPE model name"
			::= { dsVdslCpeInfoEntry 4 }

		
--  
-- 
-- *******.4.1.6296.*******.28.1.5
		-- *******.4.1.6296.*******.28.1.5
		dsVdslCpeSerialNumber OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"CPE serial number"
			::= { dsVdslCpeInfoEntry 5 }

		
	
	END

--
-- dasan-switch-mib.mib
--
