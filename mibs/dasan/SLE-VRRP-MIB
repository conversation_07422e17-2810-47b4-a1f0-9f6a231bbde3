--
-- sle-vrrp-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, April 28, 2016 at 14:29:12
--

	SLE-VRRP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Integer32, <PERSON><PERSON>ge32, Counter32, OBJECT-TYPE, 
			MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleVRRP MODULE-IDENTITY 
			LAST-UPDATED "201603161455Z"		-- March 16, 2016 at 14:55 GMT
			ORGANIZATION 
				"DASAN Networks"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains information about RIP version 2."
			REVISION ""		-- 
			DESCRIPTION 
				"OSFPv2"
			::= { sleMgmt 57 }

		
	
	
--
-- Node definitions
--
	
		sleVrrpBase OBJECT IDENTIFIER ::= { sleVRRP 1 }

		
		sleVrrpBaseInfo OBJECT IDENTIFIER ::= { sleVrrpBase 1 }

		
		sleVrrpBaseInfoCompatibleV2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Backward-compatibility feature"
			::= { sleVrrpBaseInfo 1 }

		
		sleVrrpBaseInfoVMac OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Virtual-MAC feature"
			::= { sleVrrpBaseInfo 2 }

		
		sleVrrpBaseInfoDelegate OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Specify Delegation mode"
			::= { sleVrrpBaseInfo 3 }

		
		sleVrrpBaseControl OBJECT IDENTIFIER ::= { sleVrrpBase 2 }

		
		sleVrrpBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setVrrpBaseCompatibleV2(1),
				setVrrpBaseVmacStatus(2),
				setVrrpBaseDelegate(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrpBaseControl 1 }

		
		sleVrrpBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrpBaseControl 2 }

		
		sleVrrpBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrpBaseControl 3 }

		
		sleVrrpBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrpBaseControl 4 }

		
		sleVrrpBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrpBaseControl 5 }

		
		sleVrrpBaseControlCompatibleV2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Backward-compatibility feature"
			::= { sleVrrpBaseControl 6 }

		
		sleVrrpBaseControlVMac OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Virtual-MAC feature"
			::= { sleVrrpBaseControl 7 }

		
		sleVrrpBaseControlDelegate OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Specify Delegation mode"
			::= { sleVrrpBaseControl 8 }

		
		sleVrrpSess OBJECT IDENTIFIER ::= { sleVRRP 2 }

		
		sleVrrp4Session OBJECT IDENTIFIER ::= { sleVrrpSess 1 }

		
		sleVrrp4SessionTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVrrp4SessionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4Session 1 }

		
		sleVrrp4SessionEntry OBJECT-TYPE
			SYNTAX SleVrrp4SessionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVrrp4SessionVrId, sleVrrp4SessionIfIndex }
			::= { sleVrrp4SessionTable 1 }

		
		SleVrrp4SessionEntry ::=
			SEQUENCE { 
				sleVrrp4SessionVrId
					Integer32,
				sleVrrp4SessionIfIndex
					Integer32,
				sleVrrp4SessionStatus
					INTEGER,
				sleVrrp4SessionAcceptMode
					INTEGER,
				sleVrrp4SessionAdvertisementInterval
					Integer32,
				sleVrrp4SessionCircuitFailOverIfIndex
					Integer32,
				sleVrrp4SessionCircuitFailOverPriorityDelta
					Integer32,
				sleVrrp4SessionPreemptMode
					INTEGER,
				sleVrrp4SessionPriority
					Integer32,
				sleVrrp4SessionSwitchBackDelay
					Integer32,
				sleVrrp4SessionVirtualIpVal
					OCTET STRING,
				sleVrrp4SessionVirtualIpOwner
					INTEGER,
				sleVrrp4SessionCompatibleV2
					INTEGER
			 }

		sleVrrp4SessionVrId OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp4SessionEntry 1 }

		
		sleVrrp4SessionIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface index."
			::= { sleVrrp4SessionEntry 2 }

		
		sleVrrp4SessionStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VRRP session"
			::= { sleVrrp4SessionEntry 3 }

		
		sleVrrp4SessionAcceptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				false(0),
				true(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accept mode for the session
				1:true is default"
			::= { sleVrrp4SessionEntry 4 }

		
		sleVrrp4SessionAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..10000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The advertisement interval"
			::= { sleVrrp4SessionEntry 5 }

		
		sleVrrp4SessionCircuitFailOverIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp4SessionEntry 6 }

		
		sleVrrp4SessionCircuitFailOverPriorityDelta OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..253)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover priority delta for this VRRP session"
			::= { sleVrrp4SessionEntry 7 }

		
		sleVrrp4SessionPreemptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The preempt mode for the session"
			::= { sleVrrp4SessionEntry 8 }

		
		sleVrrp4SessionPriority OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The router priority within virtual router
				Must be 255 if own virtual IP address)"
			::= { sleVrrp4SessionEntry 9 }

		
		sleVrrp4SessionSwitchBackDelay OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..500000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The router switch-back-delay interval
				switch-back-delay must be between 1 to 500000 msecs"
			::= { sleVrrp4SessionEntry 10 }

		
		sleVrrp4SessionVirtualIpVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The virtual IPv4 address"
			::= { sleVrrp4SessionEntry 11 }

		
		sleVrrp4SessionVirtualIpOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				unSet(0),
				set(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The virtual IPv4 address owner"
			::= { sleVrrp4SessionEntry 12 }

		
		sleVrrp4SessionCompatibleV2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Backward-compatibility feature"
			::= { sleVrrp4SessionEntry 13 }

		
		sleVrrp4SessionControl OBJECT IDENTIFIER ::= { sleVrrp4Session 2 }

		
		sleVrrp4SessionControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createVrrp4Sess(1),
				delVrrp4Sess(2),
				setVrrp4SessStatus(3),
				setVrrp4SessAcceptMode(4),
				setVrrp4SessAdvInterval(5),
				delVrrp4SessAdvInterval(6),
				setVrrp4SessCircuitFailOver(7),
				delVrrp4SessCircuitFailOver(8),
				setVrrp4SessPreemptMode(9),
				setVrrp4SessPriority(10),
				delVrrp4SessPriority(11),
				setVrrp4SessSwitchBackDelay(12),
				delVrrp4SessSwitchBackDelay(13),
				setVrrp4SessVirtualIp(14),
				delVrrp4SessVirtualIp(15),
				setVrrp4SessCompatibleV2(16)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4SessionControl 1 }

		
		sleVrrp4SessionControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4SessionControl 2 }

		
		sleVrrp4SessionControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4SessionControl 3 }

		
		sleVrrp4SessionControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4SessionControl 4 }

		
		sleVrrp4SessionControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4SessionControl 5 }

		
		sleVrrp4SessionControlVrId OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp4SessionControl 6 }

		
		sleVrrp4SessionControlIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The interface index"
			::= { sleVrrp4SessionControl 7 }

		
		sleVrrp4SessionControlSessionStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VRRP session"
			::= { sleVrrp4SessionControl 8 }

		
		sleVrrp4SessionControlAcceptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				false(0),
				true(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Set accept mode for the session
				1:true is default"
			::= { sleVrrp4SessionControl 9 }

		
		sleVrrp4SessionControlAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (1..10000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set advertisement interval"
			::= { sleVrrp4SessionControl 10 }

		
		sleVrrp4SessionControlCircuitFailOverIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp4SessionControl 11 }

		
		sleVrrp4SessionControlCircuitFailOverPriorityDelta OBJECT-TYPE
			SYNTAX Integer32 (1..253)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp4SessionControl 12 }

		
		sleVrrp4SessionControlPreemptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set preempt mode for the session"
			::= { sleVrrp4SessionControl 13 }

		
		sleVrrp4SessionControlPriority OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set router priority within virtual router
				Must be 255 if own virtual IP address)"
			::= { sleVrrp4SessionControl 14 }

		
		sleVrrp4SessionControlSwitchBackDelay OBJECT-TYPE
			SYNTAX Integer32 (1..500000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set router switch-back-delay interval
				switch-back-delay must be between 1 to 500000 msecs"
			::= { sleVrrp4SessionControl 15 }

		
		sleVrrp4SessionControlVirtualIpVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set virtual IPv4/6 address"
			::= { sleVrrp4SessionControl 16 }

		
		sleVrrp4SessionControlVirtualIpOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				unset(0),
				set(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set virtual IPv4 address"
			::= { sleVrrp4SessionControl 17 }

		
		sleVrrp4SessionControlCompatibleV2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable Backward-compatibility feature"
			::= { sleVrrp4SessionControl 18 }

		
		sleVrrp4SessionNotification OBJECT IDENTIFIER ::= { sleVrrp4Session 3 }

		
		sleVrrp4SessInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVrrp4SessInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4Session 4 }

		
		sleVrrp4SessInfoEntry OBJECT-TYPE
			SYNTAX SleVrrp4SessInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVrrp4SessInfoVrId, sleVrrp4SessInfoIfIndex }
			::= { sleVrrp4SessInfoTable 1 }

		
		SleVrrp4SessInfoEntry ::=
			SEQUENCE { 
				sleVrrp4SessInfoVrId
					Integer32,
				sleVrrp4SessInfoIfIndex
					Integer32,
				sleVrrp4SessInfoVrfName
					OCTET STRING,
				sleVrrp4SessInfoVrfFibId
					Integer32,
				sleVrrp4SessInfoSessionAdminState
					INTEGER,
				sleVrrp4SessInfoSessionState
					INTEGER,
				sleVrrp4SessInfoSessionStateInitMsg
					INTEGER,
				sleVrrp4SessInfoVirtualIp
					OCTET STRING,
				sleVrrp4SessInfoVirtualIpOwner
					INTEGER,
				sleVrrp4SessInfoConfiguredPriority
					Integer32,
				sleVrrp4SessInfoCurrentPriority
					Integer32,
				sleVrrp4SessInfoSwitchBackDelay
					Integer32,
				sleVrrp4SessInfoAdvertisementInterval
					Integer32,
				sleVrrp4SessInfoMasterAdvertisementInterval
					Integer32,
				sleVrrp4SessInfoSkewTime
					Integer32,
				sleVrrp4SessInfoAcceptMode
					INTEGER,
				sleVrrp4SessInfoPreemptMode
					INTEGER,
				sleVrrp4SessInfoMonitorCircuitIfIndex
					Integer32,
				sleVrrp4SessInfoMonitorCircuitPrioDelta
					Integer32,
				sleVrrp4SessInfoMonitorCircuitState
					INTEGER,
				sleVrrp4SessInfoMulticastMembershipOn
					INTEGER,
				sleVrrp4SessInfoCompatibleV2
					INTEGER
			 }

		sleVrrp4SessInfoVrId OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp4SessInfoEntry 1 }

		
		sleVrrp4SessInfoIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface index."
			::= { sleVrrp4SessInfoEntry 2 }

		
		sleVrrp4SessInfoVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The VRF name"
			::= { sleVrrp4SessInfoEntry 3 }

		
		sleVrrp4SessInfoVrfFibId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The FIB index."
			::= { sleVrrp4SessInfoEntry 4 }

		
		sleVrrp4SessInfoSessionAdminState OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The VRRP session admin status."
			::= { sleVrrp4SessInfoEntry 5 }

		
		sleVrrp4SessInfoSessionState OBJECT-TYPE
			SYNTAX INTEGER
				{
				init(1),
				backup(2),
				master(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The session state."
			::= { sleVrrp4SessInfoEntry 6 }

		
		sleVrrp4SessInfoSessionStateInitMsg OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(-1),
				adminDown(0),
				interfaceNotRunning(1),
				circuitDown(2),
				noSubnet(3),
				virtualIpUnset(4),
				inSwitchBackDelay(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The message code in init state of session"
			::= { sleVrrp4SessInfoEntry 7 }

		
		sleVrrp4SessInfoVirtualIp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The virtual IPv4 address"
			::= { sleVrrp4SessInfoEntry 8 }

		
		sleVrrp4SessInfoVirtualIpOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				unset(0),
				set(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The virtual IPv4 address owner"
			::= { sleVrrp4SessInfoEntry 9 }

		
		sleVrrp4SessInfoConfiguredPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The configured router priority within virtual router"
			::= { sleVrrp4SessInfoEntry 10 }

		
		sleVrrp4SessInfoCurrentPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current router priority within virtual router"
			::= { sleVrrp4SessInfoEntry 11 }

		
		sleVrrp4SessInfoSwitchBackDelay OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The router switch-back-delay interval"
			::= { sleVrrp4SessInfoEntry 12 }

		
		sleVrrp4SessInfoAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (0..10000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The advertisement interval"
			::= { sleVrrp4SessInfoEntry 13 }

		
		sleVrrp4SessInfoMasterAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (0..10000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The master advertisement interval for VRRP version 3"
			::= { sleVrrp4SessInfoEntry 14 }

		
		sleVrrp4SessInfoSkewTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The skew time for VRRP version3"
			::= { sleVrrp4SessInfoEntry 15 }

		
		sleVrrp4SessInfoAcceptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accept mode for VRRP version3"
			::= { sleVrrp4SessInfoEntry 16 }

		
		sleVrrp4SessInfoPreemptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The preempt mode for the session"
			::= { sleVrrp4SessInfoEntry 17 }

		
		sleVrrp4SessInfoMonitorCircuitIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp4SessInfoEntry 18 }

		
		sleVrrp4SessInfoMonitorCircuitPrioDelta OBJECT-TYPE
			SYNTAX Integer32 (1..253)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover priority delta for this VRRP session"
			::= { sleVrrp4SessInfoEntry 19 }

		
		sleVrrp4SessInfoMonitorCircuitState OBJECT-TYPE
			SYNTAX INTEGER
				{
				down(0),
				up(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The monitor circuit state"
			::= { sleVrrp4SessInfoEntry 20 }

		
		sleVrrp4SessInfoMulticastMembershipOn OBJECT-TYPE
			SYNTAX INTEGER
				{
				joined(1),
				left(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The multicast membership on (joined/left)"
			::= { sleVrrp4SessInfoEntry 21 }

		
		sleVrrp4SessInfoCompatibleV2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Backward-compatibility feature for VRRP version 3"
			::= { sleVrrp4SessInfoEntry 22 }

		
		sleVrrp4SessStatTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVrrp4SessStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp4Session 5 }

		
		sleVrrp4SessStatEntry OBJECT-TYPE
			SYNTAX SleVrrp4SessStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVrrp4SessStatVrid, sleVrrp4SessStatIfIndex }
			::= { sleVrrp4SessStatTable 1 }

		
		SleVrrp4SessStatEntry ::=
			SEQUENCE { 
				sleVrrp4SessStatVrid
					Integer32,
				sleVrrp4SessStatIfIndex
					Integer32,
				sleVrrp4SessStatChecksumErrors
					Counter32,
				sleVrrp4SessStatVersionErrors
					Counter32,
				sleVrrp4SessStatVridErrors
					Counter32,
				sleVrrp4SessStatMasterTransitions
					Counter32,
				sleVrrp4SessStatAdvertisementsRcvd
					Counter32,
				sleVrrp4SessStatPktsRcvdAdvertisementIntervalErrors
					Counter32,
				sleVrrp4SessStatPktsRcvdIpTtlErros
					Counter32,
				sleVrrp4SessStatPktsRcvdZeroPriority
					Counter32,
				sleVrrp4SessStatPktsSentZeroPriority
					Counter32,
				sleVrrp4SessStatPktsRcvdInvalidType
					Counter32,
				sleVrrp4SessStatPktsRcvdAddressListErrors
					Counter32,
				sleVrrp4SessStatPktsRcvdPacketLengthErrors
					Counter32,
				sleVrrp4SessStatPktsRcvdUnknownAuthenticationType
					Counter32,
				sleVrrp4SessStatPktsRcvdIpCountMismatch
					Counter32,
				sleVrrp4SessStatDiscontinuityTime
					OCTET STRING,
				sleVrrp4SessStatRefreshRate
					Integer32
			 }

		sleVrrp4SessStatVrid OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp4SessStatEntry 1 }

		
		sleVrrp4SessStatIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface index."
			::= { sleVrrp4SessStatEntry 2 }

		
		sleVrrp4SessStatChecksumErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Checksum Errors"
			::= { sleVrrp4SessStatEntry 3 }

		
		sleVrrp4SessStatVersionErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version Errors"
			::= { sleVrrp4SessStatEntry 4 }

		
		sleVrrp4SessStatVridErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VRid Errors"
			::= { sleVrrp4SessStatEntry 5 }

		
		sleVrrp4SessStatMasterTransitions OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Master Transitions"
			::= { sleVrrp4SessStatEntry 6 }

		
		sleVrrp4SessStatAdvertisementsRcvd OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Advertisements Rcvd"
			::= { sleVrrp4SessStatEntry 7 }

		
		sleVrrp4SessStatPktsRcvdAdvertisementIntervalErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Advertisement Interval Errors"
			::= { sleVrrp4SessStatEntry 8 }

		
		sleVrrp4SessStatPktsRcvdIpTtlErros OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with IP TTL Errors"
			::= { sleVrrp4SessStatEntry 9 }

		
		sleVrrp4SessStatPktsRcvdZeroPriority OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Zero Priority"
			::= { sleVrrp4SessStatEntry 10 }

		
		sleVrrp4SessStatPktsSentZeroPriority OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets sent with Zero Priority"
			::= { sleVrrp4SessStatEntry 11 }

		
		sleVrrp4SessStatPktsRcvdInvalidType OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Invalid type."
			::= { sleVrrp4SessStatEntry 12 }

		
		sleVrrp4SessStatPktsRcvdAddressListErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Address List Errors"
			::= { sleVrrp4SessStatEntry 13 }

		
		sleVrrp4SessStatPktsRcvdPacketLengthErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Packet Length Errors"
			::= { sleVrrp4SessStatEntry 14 }

		
		sleVrrp4SessStatPktsRcvdUnknownAuthenticationType OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Unknown Authentication Type"
			::= { sleVrrp4SessStatEntry 15 }

		
		sleVrrp4SessStatPktsRcvdIpCountMismatch OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with IP Count Mismatch"
			::= { sleVrrp4SessStatEntry 16 }

		
		sleVrrp4SessStatDiscontinuityTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Discontuinity Time.
				(Discontuinity Time) days:hours:minutes:seconds:centiseconds"
			::= { sleVrrp4SessStatEntry 17 }

		
		sleVrrp4SessStatRefreshRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Refresh Rate"
			::= { sleVrrp4SessStatEntry 18 }

		
		sleVrrp6Session OBJECT IDENTIFIER ::= { sleVrrpSess 2 }

		
		sleVrrp6SessionTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVrrp6SessionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6Session 1 }

		
		sleVrrp6SessionEntry OBJECT-TYPE
			SYNTAX SleVrrp6SessionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVrrp6SessionVrId, sleVrrp6SessionIfIndex }
			::= { sleVrrp6SessionTable 1 }

		
		SleVrrp6SessionEntry ::=
			SEQUENCE { 
				sleVrrp6SessionVrId
					Integer32,
				sleVrrp6SessionIfIndex
					Integer32,
				sleVrrp6SessionStatus
					INTEGER,
				sleVrrp6SessionAcceptMode
					INTEGER,
				sleVrrp6SessionAdvertisementInterval
					Integer32,
				sleVrrp6SessionCircuitFailOverIfIndex
					Integer32,
				sleVrrp6SessionCircuitFailOverPriorityDelta
					Integer32,
				sleVrrp6SessionPreemptMode
					INTEGER,
				sleVrrp6SessionPriority
					Integer32,
				sleVrrp6SessionSwitchBackDelay
					Integer32,
				sleVrrp6SessionVirtualIpVal
					OCTET STRING,
				sleVrrp6SessionVirtualIpOwner
					INTEGER
			 }

		sleVrrp6SessionVrId OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp6SessionEntry 1 }

		
		sleVrrp6SessionIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface index."
			::= { sleVrrp6SessionEntry 2 }

		
		sleVrrp6SessionStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VRRP session"
			::= { sleVrrp6SessionEntry 3 }

		
		sleVrrp6SessionAcceptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				false(0),
				true(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Set accept mode for the session
				1:true is default"
			::= { sleVrrp6SessionEntry 4 }

		
		sleVrrp6SessionAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..10000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set advertisement interval"
			::= { sleVrrp6SessionEntry 5 }

		
		sleVrrp6SessionCircuitFailOverIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp6SessionEntry 6 }

		
		sleVrrp6SessionCircuitFailOverPriorityDelta OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..253)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp6SessionEntry 7 }

		
		sleVrrp6SessionPreemptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set preempt mode for the session"
			::= { sleVrrp6SessionEntry 8 }

		
		sleVrrp6SessionPriority OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set router priority within virtual router
				Must be 255 if own virtual IP address)"
			::= { sleVrrp6SessionEntry 9 }

		
		sleVrrp6SessionSwitchBackDelay OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..500000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set router switch-back-delay interval
				switch-back-delay must be between 1 to 500000 msecs"
			::= { sleVrrp6SessionEntry 10 }

		
		sleVrrp6SessionVirtualIpVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set virtual IPv6 address"
			::= { sleVrrp6SessionEntry 11 }

		
		sleVrrp6SessionVirtualIpOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				unSet(0),
				set(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set virtual IPv4 address"
			::= { sleVrrp6SessionEntry 12 }

		
		sleVrrp6SessionControl OBJECT IDENTIFIER ::= { sleVrrp6Session 2 }

		
		sleVrrp6SessionControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createVrrp6Sess(1),
				delVrrp6Sess(2),
				setVrrp6SessStatus(3),
				setVrrp6SessAcceptMode(4),
				setVrrp6SessAdvInterval(5),
				delVrrp6SessAdvInterval(6),
				setVrrp6SessCircuitFailOver(7),
				delVrrp6SessCircuitFailOver(8),
				setVrrp6SessPreemptMode(9),
				setVrrp6SessPriority(10),
				delVrrp6SessPriority(11),
				setVrrp6SessSwitchBackDelay(12),
				delVrrp6SessSwitchBackDelay(13),
				setVrrp6SessVirtualIp(14),
				delVrrp6SessVirtualIp(15)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6SessionControl 1 }

		
		sleVrrp6SessionControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6SessionControl 2 }

		
		sleVrrp6SessionControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6SessionControl 3 }

		
		sleVrrp6SessionControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6SessionControl 4 }

		
		sleVrrp6SessionControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6SessionControl 5 }

		
		sleVrrp6SessionControlVrId OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp6SessionControl 6 }

		
		sleVrrp6SessionControlIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The interface index"
			::= { sleVrrp6SessionControl 7 }

		
		sleVrrp6SessionControlSessionStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VRRP session"
			::= { sleVrrp6SessionControl 8 }

		
		sleVrrp6SessionControlAcceptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				false(0),
				true(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Set accept mode for the session
				1:true is default"
			::= { sleVrrp6SessionControl 9 }

		
		sleVrrp6SessionControlAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (1..10000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set advertisement interval"
			::= { sleVrrp6SessionControl 10 }

		
		sleVrrp6SessionControlCircuitFailOverIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp6SessionControl 11 }

		
		sleVrrp6SessionControlCircuitFailOverPriorityDelta OBJECT-TYPE
			SYNTAX Integer32 (1..253)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp6SessionControl 12 }

		
		sleVrrp6SessionControlPreemptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set preempt mode for the session"
			::= { sleVrrp6SessionControl 13 }

		
		sleVrrp6SessionControlPriority OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set router priority within virtual router
				Must be 255 if own virtual IP address)"
			::= { sleVrrp6SessionControl 14 }

		
		sleVrrp6SessionControlSwitchBackDelay OBJECT-TYPE
			SYNTAX Integer32 (1..500000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set router switch-back-delay interval
				switch-back-delay must be between 1 to 500000 msecs"
			::= { sleVrrp6SessionControl 15 }

		
		sleVrrp6SessionControlVirtualIpVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set virtual IPv4/6 address"
			::= { sleVrrp6SessionControl 16 }

		
		sleVrrp6SessionControlVirtualIpOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				unset(0),
				set(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set virtual IPv4 address"
			::= { sleVrrp6SessionControl 17 }

		
		sleVrrp6SessionNotification OBJECT IDENTIFIER ::= { sleVrrp6Session 3 }

		
		sleVrrp6SessInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVrrp6SessInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6Session 4 }

		
		sleVrrp6SessInfoEntry OBJECT-TYPE
			SYNTAX SleVrrp6SessInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVrrp6SessInfoVrId, sleVrrp6SessInfoIfIndex }
			::= { sleVrrp6SessInfoTable 1 }

		
		SleVrrp6SessInfoEntry ::=
			SEQUENCE { 
				sleVrrp6SessInfoVrId
					Integer32,
				sleVrrp6SessInfoIfIndex
					Integer32,
				sleVrrp6SessInfoVrfName
					OCTET STRING,
				sleVrrp6SessInfoVrfFibId
					Integer32,
				sleVrrp6SessInfoSessionAdminState
					INTEGER,
				sleVrrp6SessInfoSessionState
					INTEGER,
				sleVrrp6SessInfoSessionStateInitMsg
					INTEGER,
				sleVrrp6SessInfoVirtualIp
					OCTET STRING,
				sleVrrp6SessInfoVirtualIpOwner
					INTEGER,
				sleVrrp6SessInfoConfiguredPriority
					Integer32,
				sleVrrp6SessInfoCurrentPriority
					Integer32,
				sleVrrp6SessInfoSwitchBackDelay
					Integer32,
				sleVrrp6SessInfoAdvertisementInterval
					Integer32,
				sleVrrp6SessInfoMasterAdvertisementInterval
					Integer32,
				sleVrrp6SessInfoSkewTime
					Integer32,
				sleVrrp6SessInfoAcceptMode
					INTEGER,
				sleVrrp6SessInfoPreemptMode
					INTEGER,
				sleVrrp6SessInfoMonitorCircuitIfIndex
					Integer32,
				sleVrrp6SessInfoMonitorCircuitPrioDelta
					Integer32,
				sleVrrp6SessInfoMonitorCircuitState
					INTEGER,
				sleVrrp6SessInfoMulticastMembershipOn
					INTEGER,
				sleVrrp6SessInfoCompatibleV2
					INTEGER
			 }

		sleVrrp6SessInfoVrId OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp6SessInfoEntry 1 }

		
		sleVrrp6SessInfoIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface index."
			::= { sleVrrp6SessInfoEntry 2 }

		
		sleVrrp6SessInfoVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The VRF name"
			::= { sleVrrp6SessInfoEntry 3 }

		
		sleVrrp6SessInfoVrfFibId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The FIB index."
			::= { sleVrrp6SessInfoEntry 4 }

		
		sleVrrp6SessInfoSessionAdminState OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The VRRP session admin status."
			::= { sleVrrp6SessInfoEntry 5 }

		
		sleVrrp6SessInfoSessionState OBJECT-TYPE
			SYNTAX INTEGER
				{
				init(1),
				backup(2),
				master(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The session state."
			::= { sleVrrp6SessInfoEntry 6 }

		
		sleVrrp6SessInfoSessionStateInitMsg OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(-1),
				adminDown(0),
				interfaceNotRunning(1),
				circuitDown(2),
				noSubnet(3),
				virtualIpUnset(4),
				inSwitchBackDelay(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The message code in init state of session"
			::= { sleVrrp6SessInfoEntry 7 }

		
		sleVrrp6SessInfoVirtualIp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The virtual IPv6 address"
			::= { sleVrrp6SessInfoEntry 8 }

		
		sleVrrp6SessInfoVirtualIpOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				unset(0),
				set(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The virtual IPv4 address owner"
			::= { sleVrrp6SessInfoEntry 9 }

		
		sleVrrp6SessInfoConfiguredPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The configured router priority within virtual router"
			::= { sleVrrp6SessInfoEntry 10 }

		
		sleVrrp6SessInfoCurrentPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current router priority within virtual router"
			::= { sleVrrp6SessInfoEntry 11 }

		
		sleVrrp6SessInfoSwitchBackDelay OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The router switch-back-delay interval"
			::= { sleVrrp6SessInfoEntry 12 }

		
		sleVrrp6SessInfoAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (0..10000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The advertisement interval"
			::= { sleVrrp6SessInfoEntry 13 }

		
		sleVrrp6SessInfoMasterAdvertisementInterval OBJECT-TYPE
			SYNTAX Integer32 (0..10000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The master advertisement interval for VRRP version 3"
			::= { sleVrrp6SessInfoEntry 14 }

		
		sleVrrp6SessInfoSkewTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The skew time for VRRP version3"
			::= { sleVrrp6SessInfoEntry 15 }

		
		sleVrrp6SessInfoAcceptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The accept mode for VRRP version3"
			::= { sleVrrp6SessInfoEntry 16 }

		
		sleVrrp6SessInfoPreemptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The preempt mode for the session"
			::= { sleVrrp6SessInfoEntry 17 }

		
		sleVrrp6SessInfoMonitorCircuitIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover for this VRRP session"
			::= { sleVrrp6SessInfoEntry 18 }

		
		sleVrrp6SessInfoMonitorCircuitPrioDelta OBJECT-TYPE
			SYNTAX Integer32 (1..253)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Circuit failover priority delta for this VRRP session"
			::= { sleVrrp6SessInfoEntry 19 }

		
		sleVrrp6SessInfoMonitorCircuitState OBJECT-TYPE
			SYNTAX INTEGER
				{
				down(0),
				up(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The monitor circuit state"
			::= { sleVrrp6SessInfoEntry 20 }

		
		sleVrrp6SessInfoMulticastMembershipOn OBJECT-TYPE
			SYNTAX INTEGER
				{
				joined(1),
				left(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The multicast membership on (joined/left)"
			::= { sleVrrp6SessInfoEntry 21 }

		
		sleVrrp6SessInfoCompatibleV2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Backward-compatibility feature for VRRP version 3"
			::= { sleVrrp6SessInfoEntry 22 }

		
		sleVrrp6SessStatTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleVrrp6SessStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleVrrp6Session 5 }

		
		sleVrrp6SessStatEntry OBJECT-TYPE
			SYNTAX SleVrrp6SessStatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleVrrp6SessStatVrid, sleVrrp6SessStatIfIndex }
			::= { sleVrrp6SessStatTable 1 }

		
		SleVrrp6SessStatEntry ::=
			SEQUENCE { 
				sleVrrp6SessStatVrid
					Integer32,
				sleVrrp6SessStatIfIndex
					Integer32,
				sleVrrp6SessStatChecksumErrors
					Counter32,
				sleVrrp6SessStatVersionErrors
					Counter32,
				sleVrrp6SessStatVridErrors
					Counter32,
				sleVrrp6SessStatMasterTransitions
					Counter32,
				sleVrrp6SessStatAdvertisementsRcvd
					Counter32,
				sleVrrp6SessStatPktsRcvdAdvertisementIntervalErrors
					Counter32,
				sleVrrp6SessStatPktsRcvdIpTtlErros
					Counter32,
				sleVrrp6SessStatPktsRcvdZeroPriority
					Counter32,
				sleVrrp6SessStatPktsSentZeroPriority
					Counter32,
				sleVrrp6SessStatPktsRcvdInvalidType
					Counter32,
				sleVrrp6SessStatPktsRcvdAddressListErrors
					Counter32,
				sleVrrp6SessStatPktsRcvdPacketLengthErrors
					Counter32,
				sleVrrp6SessStatPktsRcvdUnknownAuthenticationType
					Counter32,
				sleVrrp6SessStatPktsRcvdIpCountMismatch
					Counter32,
				sleVrrp6SessStatDiscontinuityTime
					OCTET STRING,
				sleVrrp6SessStatRefreshRate
					Integer32
			 }

		sleVrrp6SessStatVrid OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vrrp index."
			::= { sleVrrp6SessStatEntry 1 }

		
		sleVrrp6SessStatIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface index."
			::= { sleVrrp6SessStatEntry 2 }

		
		sleVrrp6SessStatChecksumErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Checksum Errors"
			::= { sleVrrp6SessStatEntry 3 }

		
		sleVrrp6SessStatVersionErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version Errors"
			::= { sleVrrp6SessStatEntry 4 }

		
		sleVrrp6SessStatVridErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VRid Errors"
			::= { sleVrrp6SessStatEntry 5 }

		
		sleVrrp6SessStatMasterTransitions OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Master Transitions"
			::= { sleVrrp6SessStatEntry 6 }

		
		sleVrrp6SessStatAdvertisementsRcvd OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Advertisements Rcvd"
			::= { sleVrrp6SessStatEntry 7 }

		
		sleVrrp6SessStatPktsRcvdAdvertisementIntervalErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Advertisement Interval Errors"
			::= { sleVrrp6SessStatEntry 8 }

		
		sleVrrp6SessStatPktsRcvdIpTtlErros OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with IP TTL Errors"
			::= { sleVrrp6SessStatEntry 9 }

		
		sleVrrp6SessStatPktsRcvdZeroPriority OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Zero Priority"
			::= { sleVrrp6SessStatEntry 10 }

		
		sleVrrp6SessStatPktsSentZeroPriority OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets sent with Zero Priority"
			::= { sleVrrp6SessStatEntry 11 }

		
		sleVrrp6SessStatPktsRcvdInvalidType OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Invalid type."
			::= { sleVrrp6SessStatEntry 12 }

		
		sleVrrp6SessStatPktsRcvdAddressListErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Address List Errors"
			::= { sleVrrp6SessStatEntry 13 }

		
		sleVrrp6SessStatPktsRcvdPacketLengthErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Packet Length Errors"
			::= { sleVrrp6SessStatEntry 14 }

		
		sleVrrp6SessStatPktsRcvdUnknownAuthenticationType OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with Unknown Authentication Type"
			::= { sleVrrp6SessStatEntry 15 }

		
		sleVrrp6SessStatPktsRcvdIpCountMismatch OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Packets recieved with IP Count Mismatch"
			::= { sleVrrp6SessStatEntry 16 }

		
		sleVrrp6SessStatDiscontinuityTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Discontuinity Time.
				(Discontuinity Time) days:hours:minutes:seconds:centiseconds"
			::= { sleVrrp6SessStatEntry 17 }

		
		sleVrrp6SessStatRefreshRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Refresh Rate"
			::= { sleVrrp6SessStatEntry 18 }

		
		sleISISGroup OBJECT-GROUP
			OBJECTS { sleVrrpBaseInfoCompatibleV2, sleVrrpBaseInfoVMac, sleVrrpBaseInfoDelegate, sleVrrpBaseControlRequest, sleVrrpBaseControlStatus, 
				sleVrrpBaseControlTimer, sleVrrpBaseControlTimeStamp, sleVrrpBaseControlReqResult, sleVrrpBaseControlCompatibleV2, sleVrrpBaseControlVMac, 
				sleVrrpBaseControlDelegate, sleVrrp4SessionVrId, sleVrrp4SessionIfIndex, sleVrrp4SessionStatus, sleVrrp4SessionAcceptMode, 
				sleVrrp4SessionAdvertisementInterval, sleVrrp4SessionCircuitFailOverIfIndex, sleVrrp4SessionCircuitFailOverPriorityDelta, sleVrrp4SessionPreemptMode, sleVrrp4SessionPriority, 
				sleVrrp4SessionSwitchBackDelay, sleVrrp4SessionVirtualIpVal, sleVrrp4SessionVirtualIpOwner, sleVrrp4SessionCompatibleV2, sleVrrp4SessionControlRequest, 
				sleVrrp4SessionControlStatus, sleVrrp4SessionControlTimer, sleVrrp4SessionControlTimeStamp, sleVrrp4SessionControlReqResult, sleVrrp4SessionControlVrId, 
				sleVrrp4SessionControlIfIndex, sleVrrp4SessionControlSessionStatus, sleVrrp4SessionControlAcceptMode, sleVrrp4SessionControlAdvertisementInterval, sleVrrp4SessionControlCircuitFailOverIfIndex, 
				sleVrrp4SessionControlCircuitFailOverPriorityDelta, sleVrrp4SessionControlPreemptMode, sleVrrp4SessionControlPriority, sleVrrp4SessionControlSwitchBackDelay, sleVrrp4SessionControlVirtualIpVal, 
				sleVrrp4SessionControlVirtualIpOwner, sleVrrp4SessionControlCompatibleV2, sleVrrp6SessionVrId, sleVrrp6SessionIfIndex, sleVrrp6SessionStatus, 
				sleVrrp6SessionAcceptMode, sleVrrp6SessionAdvertisementInterval, sleVrrp6SessionCircuitFailOverIfIndex, sleVrrp6SessionCircuitFailOverPriorityDelta, sleVrrp6SessionPreemptMode, 
				sleVrrp6SessionPriority, sleVrrp6SessionSwitchBackDelay, sleVrrp6SessionVirtualIpVal, sleVrrp6SessionVirtualIpOwner, sleVrrp6SessionControlRequest, 
				sleVrrp6SessionControlStatus, sleVrrp6SessionControlTimer, sleVrrp6SessionControlTimeStamp, sleVrrp6SessionControlReqResult, sleVrrp6SessionControlVrId, 
				sleVrrp6SessionControlIfIndex, sleVrrp6SessionControlSessionStatus, sleVrrp6SessionControlAcceptMode, sleVrrp6SessionControlAdvertisementInterval, sleVrrp6SessionControlCircuitFailOverIfIndex, 
				sleVrrp6SessionControlCircuitFailOverPriorityDelta, sleVrrp6SessionControlPreemptMode, sleVrrp6SessionControlPriority, sleVrrp6SessionControlSwitchBackDelay, sleVrrp6SessionControlVirtualIpVal, 
				sleVrrp6SessionControlVirtualIpOwner, sleVrrp4SessInfoVrId, sleVrrp4SessInfoIfIndex, sleVrrp4SessInfoVrfName, sleVrrp4SessInfoVrfFibId, 
				sleVrrp4SessInfoSessionAdminState, sleVrrp4SessInfoSessionState, sleVrrp4SessInfoVirtualIp, sleVrrp4SessInfoVirtualIpOwner, sleVrrp4SessInfoConfiguredPriority, 
				sleVrrp4SessInfoCurrentPriority, sleVrrp4SessInfoSwitchBackDelay, sleVrrp4SessInfoAdvertisementInterval, sleVrrp4SessInfoMasterAdvertisementInterval, sleVrrp4SessInfoSkewTime, 
				sleVrrp4SessInfoAcceptMode, sleVrrp4SessInfoPreemptMode, sleVrrp4SessInfoMonitorCircuitIfIndex, sleVrrp4SessInfoMonitorCircuitPrioDelta, sleVrrp4SessInfoMonitorCircuitState, 
				sleVrrp4SessInfoMulticastMembershipOn, sleVrrp4SessStatVrid, sleVrrp4SessStatIfIndex, sleVrrp4SessStatChecksumErrors, sleVrrp4SessStatVersionErrors, 
				sleVrrp4SessStatVridErrors, sleVrrp4SessStatMasterTransitions, sleVrrp4SessStatAdvertisementsRcvd, sleVrrp4SessStatPktsRcvdAdvertisementIntervalErrors, sleVrrp4SessStatPktsRcvdIpTtlErros, 
				sleVrrp4SessStatPktsRcvdZeroPriority, sleVrrp4SessStatPktsSentZeroPriority, sleVrrp4SessStatPktsRcvdInvalidType, sleVrrp4SessStatPktsRcvdAddressListErrors, sleVrrp4SessStatPktsRcvdPacketLengthErrors, 
				sleVrrp4SessStatPktsRcvdUnknownAuthenticationType, sleVrrp4SessStatPktsRcvdIpCountMismatch, sleVrrp4SessStatDiscontinuityTime, sleVrrp4SessStatRefreshRate, sleVrrp6SessStatVrid, 
				sleVrrp6SessStatIfIndex, sleVrrp6SessStatChecksumErrors, sleVrrp6SessStatVersionErrors, sleVrrp6SessStatVridErrors, sleVrrp6SessStatMasterTransitions, 
				sleVrrp6SessStatAdvertisementsRcvd, sleVrrp6SessStatPktsRcvdAdvertisementIntervalErrors, sleVrrp6SessStatPktsRcvdIpTtlErros, sleVrrp6SessStatPktsRcvdZeroPriority, sleVrrp6SessStatPktsSentZeroPriority, 
				sleVrrp6SessStatPktsRcvdInvalidType, sleVrrp6SessStatPktsRcvdAddressListErrors, sleVrrp6SessStatPktsRcvdPacketLengthErrors, sleVrrp6SessStatPktsRcvdUnknownAuthenticationType, sleVrrp6SessStatPktsRcvdIpCountMismatch, 
				sleVrrp6SessStatDiscontinuityTime, sleVrrp6SessStatRefreshRate, sleVrrp4SessInfoCompatibleV2, sleVrrp4SessInfoSessionStateInitMsg, sleVrrp6SessInfoVrId, 
				sleVrrp6SessInfoIfIndex, sleVrrp6SessInfoVrfName, sleVrrp6SessInfoVrfFibId, sleVrrp6SessInfoSessionAdminState, sleVrrp6SessInfoSessionState, 
				sleVrrp6SessInfoSessionStateInitMsg, sleVrrp6SessInfoVirtualIp, sleVrrp6SessInfoVirtualIpOwner, sleVrrp6SessInfoConfiguredPriority, sleVrrp6SessInfoCurrentPriority, 
				sleVrrp6SessInfoSwitchBackDelay, sleVrrp6SessInfoAdvertisementInterval, sleVrrp6SessInfoMasterAdvertisementInterval, sleVrrp6SessInfoSkewTime, sleVrrp6SessInfoAcceptMode, 
				sleVrrp6SessInfoPreemptMode, sleVrrp6SessInfoMonitorCircuitIfIndex, sleVrrp6SessInfoMonitorCircuitPrioDelta, sleVrrp6SessInfoMonitorCircuitState, sleVrrp6SessInfoMulticastMembershipOn, 
				sleVrrp6SessInfoCompatibleV2 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleVRRP 5 }

		
	
	END

--
-- sle-vrrp-mib.mib
--
