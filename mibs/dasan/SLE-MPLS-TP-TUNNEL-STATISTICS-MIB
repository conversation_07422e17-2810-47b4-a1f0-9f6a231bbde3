--
-- sle-mpls-tp-tunnel-statistics-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, January 21, 2016 at 14:30:41
--

	SLE-MPLS-TP-TUNNEL-STATISTICS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			mplsStdMIB			
				FROM MPLS-TC-STD-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			zeroDotZero, TimeTicks, Unsigned32, Gauge32, Counter64, 
			OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpTunnelStats MODULE-IDENTITY 
			LAST-UPDATED "201501280000Z"		-- January 28, 2015 at 00:00 GMT
			ORGANIZATION 
				"Multiprotocol Label Switching (MPLS) Working Group"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				<PERSON><PERSON><PERSON> Shin (<PERSON>)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"This mib contains the managed objects for MPLS-TP lsp statistics."
			REVISION "201601180000Z"		-- January 18, 2016 at 00:00 GMT
			DESCRIPTION 
				"Initial version"
			::= { sleMpls 20 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT IDENTIFIER ::= { sleMgmt 16 }

		
		sleMplsTpTunnelStatsTable OBJECT IDENTIFIER ::= { sleMplsTpTunnelStats 1 }

		
		sleMplsTpTunnelStatsInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpTunnelStatsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table contains the information about the statistics
				of LSP which exist on the LSR or LER."
			::= { sleMplsTpTunnelStatsTable 1 }

		
		sleMplsTpTunnelStatsInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpTunnelStatsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in this table represents the statistics of LSP.
				The LSP can be creates by network administator or SNMP agent
				as instruct by the MPLS-TP"
			INDEX { sleMplsTpTunnelStatsInfoIndex }
			::= { sleMplsTpTunnelStatsInfoTable 1 }

		
		SleMplsTpTunnelStatsInfoEntry ::=
			SEQUENCE { 
				sleMplsTpTunnelStatsInfoIndex
					Unsigned32,
				sleMplsTpTunnelStatsInfoTunnelName
					OCTET STRING,
				sleMplsTpTunnelStatsInfoRole
					INTEGER,
				sleMplsTpTunnelStatsInfoFwdTxPkts
					Counter64,
				sleMplsTpTunnelStatsInfoFwdTxBytes
					Counter64,
				sleMplsTpTunnelStatsInfoFwdRxPkts
					Counter64,
				sleMplsTpTunnelStatsInfoFwdRxBytes
					Counter64,
				sleMplsTpTunnelStatsInfoRevTxPkts
					Counter64,
				sleMplsTpTunnelStatsInfoRevTxBytes
					Counter64,
				sleMplsTpTunnelStatsInfoRevRxPkts
					Counter64,
				sleMplsTpTunnelStatsInfoRevRxBytes
					Counter64
			 }

		sleMplsTpTunnelStatsInfoIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Uniquely identifies a set of tunnel instances
				between a pair of ingress and egress LSRs.
				Managers should obtain new values for row
				creation in this table by reading
				mplsTunnelIndexN. When
				the MPLS signalling protocol is rsvp(2) this value
				SHOULD be equal to the value signaled in the
				Tunnel Id of the Session object. When the MPLS
				signalling protocol is crldp(3) this value
				SHOULD be equal to the value signaled in the
				LSP ID."
			::= { sleMplsTpTunnelStatsInfoEntry 1 }

		
		sleMplsTpTunnelStatsInfoTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The tunnel name of the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 2 }

		
		sleMplsTpTunnelStatsInfoRole OBJECT-TYPE
			SYNTAX INTEGER
				{
				source(0),
				transit(1),
				destination(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This value signifies the role that this tunnel
				entry/instance represents. This value MUST be set
				to head(1) at the originating point of the tunnel.
				This value MUST be set to transit(2) at transit
				points along the tunnel, if transit points are
				supported. This value MUST be set to tail(3) at the
				terminating point of the tunnel if tunnel tails are
				supported.
				
				The value headTail(4) is provided for tunnels that
				begin and end on the same LSR."
			::= { sleMplsTpTunnelStatsInfoEntry 3 }

		
		sleMplsTpTunnelStatsInfoFwdTxPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of packet sent through
				the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 4 }

		
		sleMplsTpTunnelStatsInfoFwdTxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total bytes sent through
				the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 5 }

		
		sleMplsTpTunnelStatsInfoFwdRxPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of packet recieved through
				the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 6 }

		
		sleMplsTpTunnelStatsInfoFwdRxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total bytes recieved through the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 7 }

		
		sleMplsTpTunnelStatsInfoRevTxPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of packet sent through the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 8 }

		
		sleMplsTpTunnelStatsInfoRevTxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total bytes sent through
				the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 9 }

		
		sleMplsTpTunnelStatsInfoRevRxPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of packet recieved through
				the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 10 }

		
		sleMplsTpTunnelStatsInfoRevRxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total bytes recieved through
				the LSP."
			::= { sleMplsTpTunnelStatsInfoEntry 11 }

		
		sleMplsTpTunnelStatsControl OBJECT IDENTIFIER ::= { sleMplsTpTunnelStatsTable 2 }

		
		sleMplsTpTunnelStatsControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setToClearTunnelStats(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be 
				modified in the Vrf table. For each read-write column of Vrf 
				table, a Set Operation control value is added in this object."
			::= { sleMplsTpTunnelStatsControl 1 }

		
		sleMplsTpTunnelStatsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpTunnelStatsControl 2 }

		
		sleMplsTpTunnelStatsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured 
				for every control table."
			::= { sleMplsTpTunnelStatsControl 3 }

		
		sleMplsTpTunnelStatsControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpTunnelStatsControl 4 }

		
		sleMplsTpTunnelStatsReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpTunnelStatsControl 5 }

		
		sleMplsTpTunnelStatsControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The tunnel name of the LSP.
				
				To clear all the tunnel statistics, give the name as 'all'.
				
				To clear the specific tunnel, give the specific tunnel name.
				"
			::= { sleMplsTpTunnelStatsControl 6 }

		
	
	END

--
-- sle-mpls-tp-tunnel-statistics-mib.mib
--
