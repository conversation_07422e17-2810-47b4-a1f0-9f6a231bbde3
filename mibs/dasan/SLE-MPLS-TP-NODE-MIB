--
-- sle-mpls-tp-node-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, January 12, 2016 at 11:22:02
--

	SLE-MPLS-TP-NODE-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			MplsCcId, MplsIccId			
				FROM MPLS-TC-EXT-STD-MIB			
			mplsTunnelIndex, mplsTunnelInstance, mplsTunnelIngressLSRId, mplsTunnelEgressLSRId			
				FROM MPLS-TE-STD-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			zeroDot<PERSON>ero, TimeTicks, IpAddress, Unsigned32, Gauge32, 
			OBJECT-TYPE, MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpNode MODULE-IDENTITY 
			LAST-UPDATED "201510070000Z"		-- October 07, 2015 at 00:00 GMT
			ORGANIZATION 
				"Multiprotocol Label Switching (MPLS) Working Group"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				DongChel Shin (Chris)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"Copyright (c) 2012 IETF Trust and the persons identified
				as the document authors.  All rights reserved.
				
				This MIB module contains generic object definitions for
				MPLS Traffic Engineering in transport networks."
			REVISION "201207150000Z"		-- July 15, 2012 at 00:00 GMT
			DESCRIPTION 
				"MPLS_TP node configuration table"
			::= { sleMpls 13 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MPLS."
			::= { sleMgmt 16 }

		
		sleMplsTpNodeCfg OBJECT IDENTIFIER ::= { sleMplsTpNode 1 }

		
		sleMplsTpNodeCfgInfo OBJECT IDENTIFIER ::= { sleMplsTpNodeCfg 1 }

		
		sleMplsTpNodeCfgInfoGlobalId OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the Global Operator Identifier.
				this object value should be zero when
				sleMplsTpNodeConfigInfoCcId and  sleMplsTpNodeConfigInfoICcId
				is configured with non-null value."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgInfo 1 }

		
		sleMplsTpNodeCfgInfoItutCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object allows the operator or service provider to
				configure a unique MPLS-TP ITU-T Carrier Code (ICC)
				either for Ingress ID or Egress ID.
				
				This object value should be zero when 
				sleMplsTpNodeConfigInfoGlobalId are assigned with non-zero value."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgInfo 2 }

		
		sleMplsTpNodeCfgInfoItutIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object allows the operator or service provider to
				configure a unique MPLS-TP ITU-T Carrier Code (ICC)
				either for Ingress ID or Egress ID.
				
				This object value should be zero when
				sleMplsTpNodeConfigInfoGlobalId are assigned with non-zero value."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgInfo 3 }

		
		sleMplsTpNodeCfgInfoNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the Node_ID within the operator."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgInfo 4 }

		
		sleMplsTpNodeCfgInfoNodeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object value should be zero when 
				sleMplsTpNodeConfigInfoGlobalId are assigned with non-zero value ."
			::= { sleMplsTpNodeCfgInfo 5 }

		
		sleMplsTpNodeCfgControl OBJECT IDENTIFIER ::= { sleMplsTpNodeCfg 2 }

		
		sleMplsTpNodeCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setMplsTpNode(1),
				unsetMplsTpNode(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be 
				modified in the TunnelExtNodeConfigTable table. For each read-write 
				column of TunnelExtNodeConfigTable table, a Set Operation control 
				value is added in this object."
			::= { sleMplsTpNodeCfgControl 1 }

		
		sleMplsTpNodeCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpNodeCfgControl 2 }

		
		sleMplsTpNodeCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured
				for every control table."
			::= { sleMplsTpNodeCfgControl 3 }

		
		sleMplsTpNodeCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpNodeCfgControl 4 }

		
		sleMplsTpNodeCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpNodeCfgControl 5 }

		
		sleMplsTpNodeCfgControlGlobalId OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object indicates the Global Operator Identifier.
				This object value should be zero when 
				sleMplsTpNodeConfigControlIccId  and sleMplsTpNodeConfigControlccId
				is configured with non-null value."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgControl 6 }

		
		sleMplsTpNodeCfgControlItutCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object allows the operator or service provider to
				configure a unique MPLS-TP ITU-T Carrier Code (ICC)
				either for Ingress ID or Egress ID.
				
				This object value should be zero when 
				sleMplsTpNodeConfigControlGlobalId are assigned with 
				non-zero value."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgControl 7 }

		
		sleMplsTpNodeCfgControlItutIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object allows the operator or service provider to configure a 
				unique MPLS-TP ITU-T Carrier Code (ICC) either for Ingress ID 
				or Egress ID.
				
				This object value should be zero when
				sleMplsTpNodeConfigControlGlobalId are assigned with non-zero
				value."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgControl 8 }

		
		sleMplsTpNodeCfgControlNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object indicates the Node_ID within the operator."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgControl 9 }

		
		sleMplsTpNodeCfgControlNodeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object allows the operator or service provider to
				configure a unique MPLS-TP ITU-T Carrier Code (ICC)
				either for Ingress ID or Egress ID.
				
				This object value should be zero when
				msleMplsTpNodeConfigControlGlobalId are assigned with non-zero
				value."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpNodeCfgControl 10 }

		
	
	END

--
-- sle-mpls-tp-node-mib.mib
--
