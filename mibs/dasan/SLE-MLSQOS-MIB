--
-- sle-mlsqos-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Monday, May 16, 2016 at 14:59:46
--

	SLE-MLSQOS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			SnmpAdminString			
				FROM SNMP-FRAMEWORK-MIB			
			OBJECT-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Integer32, Unsigned32, Gauge32, Counter64, 
			OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI			
			TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		sleMlsQos MODULE-IDENTITY 
			LAST-UPDATED "201310040903Z"		-- October 04, 2013 at 09:03 GMT
			ORGANIZATION 
				" "
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains all informations about ML QOS supported features."
			REVISION "201308040903Z"		-- August 04, 2013 at 09:03 GMT
			DESCRIPTION 
				" "
			::= { sleMgmt 28 }

		
	
--
-- Textual conventions
--
	
		MlsQosStatusType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Enable(1) : enabled in the system.
				Disable(0) : Disable in system."
			SYNTAX INTEGER
				{
				enable(1),
				disable(0)
				}

		MlsQosMappingType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"CosToCos(1) :
				CosToQueue(2) : 
				DscpToDscp(3) : 
				DscpToQueue(4) : 
				ExpToExp(5) : 
				ExpToQueue(6) : "
			SYNTAX INTEGER
				{
				cosToCos(1),
				cosToQueue(2),
				dscpToDscp(3),
				dscpToQueue(4),
				expToExp(5),
				expToQueue(6),
				expToClass(7)
				}

		ACLMatchType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				" ACL Match Types
				mac (1),
				EthType (2)
				L3Proto(3)"
			SYNTAX INTEGER
				{
				mac(1),
				ethType(2),
				l3Proto(3)
				}

		ACLMatchActionType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"deny(0) 	deny the packets
				permit(1)    	permit.
				remark(2)	mark the packets "
			SYNTAX INTEGER
				{
				deny(0),
				permit(1),
				remark(2)
				}

		ACLEtherType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				" WORD  Ethertype value - (0x600-0xffff)
				ip4   IPv4 Ethertype - 0x0800
				ip6   IPv6 Ethertype - 0x86dd
				mpls  MPLS Ethertype - 0x8847"
			SYNTAX OCTET STRING

		AclTcpUdpPortActionType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"
				eq(1) Equals to 
				neq(2) Not equals to 
				lt(3) Lower Than
				gt(4) Greather Than"
			SYNTAX INTEGER
				{
				eq(1),
				neq(2),
				lt(3),
				gt(4)
				}

		ClassMapMatchType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"cos (1) ,
				innerCos(2),
				ipDscp (3),
				ipPrecedence (4),
				ip6Dscp (5),
				ip6Precedence (6),
				tcpSrcPort (7),
				tcpDstPort (8),
				udpSrcPort (9),
				udpDstPort (10),
				VlanId (11),
				innerVlanId (12),
				layer4SrcPort (13) 
				layer4DstPort (14)"
			SYNTAX INTEGER
				{
				cos(1),
				innerCos(2),
				ipDscp(3),
				ipPrecedence(4),
				ip6Dscp(5),
				ip6Precedence(6),
				tcpSrcPort(7),
				tcpDstPort(8),
				udpSrcPort(9),
				udpDstPort(10),
				vlanId(11),
				innerVlanId(12),
				layer4SrcPort(13),
				layer4DstPort(14)
				}

		ClassMapMatchRangeType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"tcpSrcPort (1),
				tcpDstPort (2),
				udpSrcPort (3),
				udpDstPort (4),
				VlanId (5), 
				layer4SrcPort (6),
				layer4DstPort (7)"
			SYNTAX INTEGER
				{
				tcpSrcPort(1),
				tcpDstPort(2),
				udpSrcPort(3),
				udpDstPort(4),
				vlanId(5),
				layer4SrcPort(6),
				layer4DstPort(7)
				}

		PmapPriorityType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"	low(0) 
				medium(1)
				high(2)
				highest(3)"
			SYNTAX INTEGER
				{
				low(0),
				medium(1),
				high(2),
				highest(3)
				}

		PmapPoliceType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"	srtcm(1) 
				trtcm(2)"
			SYNTAX INTEGER
				{
				srtcm(1),
				trtcm(2)
				}

		PmapExceedActionType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"	none (0)
				drop (1)
				set-dscp-transmit (2)
				set-tos-transmit (3)
				set-cos-transmit (4)
				transmit (7)"
			SYNTAX INTEGER
				{
				none(0),
				drop(1),
				setDscpTransmit(2),
				setTosTransmit(3),
				setCosTransmit(4),
				transmit(7)
				}

		PmapViolateActionType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"	none(0)
				drop(1) 
				set-dscp-transmit(2)
				set-cos-transmit(3)
				set-tos-transmit(4)
				transmit (5)"
			SYNTAX INTEGER
				{
				none(0),
				drop(1),
				dscpTx(2),
				cosTx(3),
				tosTx(4),
				transmit(5)
				}

		PmapSetActionType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"	cos (1),
				cpu-cos (2),
				ip-dscp (3),
				ip-precedence (4),
				redirect-to-port (5),
				mirror-to-port (6),
				vlan (7),
				ip6-dscp (8), 
				ip6-precedence (9),
				cpu-copy (10),
				deny (11),
				qos-group(12),
				none(13),
				queue(14),
				vlan-cos(15)"
			SYNTAX INTEGER
				{
				cos(1),
				cpuCos(2),
				ipDscp(3),
				ipPrecedence(4),
				redirectToPort(5),
				mirrorToPort(6),
				vlan(7),
				ip6Dscp(8),
				ip6Precedence(9),
				cpuCopy(10),
				deny(11),
				qosGroup(12),
				none(13),
				queue(14),
				vlanCos(15)
				}

		MlsQosIntfTrustState ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"cos  (1)
				dscp (2)"
			SYNTAX INTEGER
				{
				cos(1),
				dscp(2)
				}

		MlsQosInterfaceMapingType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"
				cos (1)          Configure interface default CoS values
				cos-cos (2)      Specify cos-cos
				cos-queue (3)    Specify cos-queue map
				dscp (4)         Set the default DSCP vlaue
				dscp-dscp(5)     Specify dscp-dscp
				dscp-queue (6)   DSCP-to-QUEUE
				exp-exp(7)  	   Specify exp-exp
				trust  (8)   	   Configure port trust state
				trust-passthrough (9)     COnfigure port trust pass-through state
				cosToClass (10)			configure cos to class
				dscpToClass (11)			configure dscp to class"
			SYNTAX INTEGER
				{
				cos(1),
				cosToCos(2),
				cosToQueue(3),
				dscp(4),
				dscpToDscp(5),
				dscpToQueue(6),
				expToExp(7),
				trust(8),
				trustPassThrough(9),
				cosToClass(10),
				dscpToClass(11)
				}

		MlsQosIntfQueProfilingType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"shaping (1),
				wrrQWt (2),
				wrrRandomDetect (3),
				tailDrop (4),
				strictQ (5)"
			SYNTAX INTEGER
				{
				shaping(1),
				wrrQWt(2),
				wrrRandomDetect(3),
				tailDrop(4),
				strictQ(5),
				reservedBandwidth(6)
				}

		ACLMacType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"
				src(1) 			Src Mac
				dst(2) 			Dst Mac
				srcWildcard(3) 	Src Wildcard bits
				dstWildcard(4)	Dst Wildcard Bits "
			SYNTAX INTEGER
				{
				src(1),
				dst(2),
				srcWildcard(3),
				dstWildcard(4)
				}

		ACLIpType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"
				src(1) 			Src Mac
				dst(2) 			Dst Mac
				srcWildcard(3) 	Src Wildcard bits
				dstWildcard(4)	Dst Wildcard Bits 
				host(5)
				any(6)"
			SYNTAX INTEGER
				{
				src(1),
				dst(2),
				srcWildcard(3),
				dstWildcard(4)
				}

		PolMapClassMatchPrioType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"high(1) :
				highest(2) : 
				low(3) : 
				medium(4) : "
			SYNTAX INTEGER
				{
				high(1),
				highest(2),
				low(3),
				medium(4)
				}

		PoliceExceedActionType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"drop(1) :
				set-cos-transmit(2) : 
				set-dscp-transmit(3) : 
				set-tos-transmit(4) : 
				transmit (5)"
			SYNTAX INTEGER
				{
				drop(1),
				setCosTransmit(2),
				setDscpTransmit(3),
				setTosTransmit(4),
				transmit(5)
				}

	
--
-- Node definitions
--
	
		sleMlsQosGlobal OBJECT IDENTIFIER ::= { sleMlsQos 1 }

		
		sleMlsQosGlobalInfo OBJECT IDENTIFIER ::= { sleMlsQosGlobal 1 }

		
		sleMlsQosStatus OBJECT-TYPE
			SYNTAX MlsQosStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Enable / Disable MlQos
				enable (1) 
				disable (0) "
			::= { sleMlsQosGlobalInfo 1 }

		
		sleMlsQosMapCosToCos OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Global incoming Cos to outgoing Cos mapping."
			::= { sleMlsQosGlobalInfo 2 }

		
		sleMlsQosMapCosToQueue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Global incoming Cos to outgoing queue mapping."
			::= { sleMlsQosGlobalInfo 3 }

		
		sleMlsQosMapDscpToDscp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Global incoming dscp to outgoing dscp mapping."
			::= { sleMlsQosGlobalInfo 4 }

		
		sleMlsQosMapDscpToQueue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Global incoming dscp to outgoing queue mapping."
			::= { sleMlsQosGlobalInfo 5 }

		
		sleMlsQosMapExpToExp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Global incoming exp to outgoing exp mapping."
			::= { sleMlsQosGlobalInfo 6 }

		
		sleMlsQosMapExpToQueue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Global incoming exp to outgoing queue mapping."
			::= { sleMlsQosGlobalInfo 7 }

		
		sleMlsQosMapStrictQueueId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Queues with Strict scheduling"
			::= { sleMlsQosGlobalInfo 8 }

		
		sleMlsQosMapWrrQueueWeight OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Queue weight for Weighted round robin scheduling.
				If the queue wt is given 0, then the queue becomes strict queue"
			::= { sleMlsQosGlobalInfo 9 }

		
		sleMlsQosMapCpuMaxPpsRate OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Rate limiting for CPU. Defines Max packets per second to CPU"
			::= { sleMlsQosGlobalInfo 10 }

		
		sleMlsQosMapCpuQueueWeight OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"weight for Cpu Queues"
			::= { sleMlsQosGlobalInfo 11 }

		
		sleMlsQosMapNodeCpuMaxPpsRate OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"node Rate limiting for CPU. Defines Max packets per second to CPU"
			::= { sleMlsQosGlobalInfo 12 }

		
		sleMlsQosMapNodeCpuQueueWeight OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"node weight for Cpu Queues"
			::= { sleMlsQosGlobalInfo 13 }

		
		sleMlsQosMapExpToClass OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Exp - to - class mapping"
			::= { sleMlsQosGlobalInfo 14 }

		
		sleHQosStatistics OBJECT-TYPE
			SYNTAX MlsQosStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Hqos has feature to disable or enable
				statistics so this object is only applicable
				when hqos is enabled"
			::= { sleMlsQosGlobalInfo 15 }

		
		sleQosPhbPriorityColor OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"qos phb-priority-color
				Ex:  PHB Name | Priority Color
						-------------------------------------------
						 be       | 0        red
						 cs0      | 0        green
						 cs1      | 1        green
						 af11     | 1        yellow
						 af12     | 1        red
						 af13     | 0        green
						 cs2      | 2        green
						 af21     | 2        green
						 af22     | 2        yellow
						 af23     | 2        red
						 cs3      | 3        green
						 af31     | 4        red
						 af32     | 3        green
						 af33     | 3        yellow
						 cs4      | 4        green
						 af41     | 5        yellow
						 af42     | 5        red
						 af43     | 4        green
						 cs5      | 5        green
						 ef       | 6        red
						 cs6      | 6        yellow
						 cs7      | 7        green"
			::= { sleMlsQosGlobalInfo 16 }

		
		sleHQosDefaultClassToDscp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Default Class to DSCP
				Ex:  QoS PHB map to DSCP:
						 PHB Name | dscp
						-------------------------------------------
						 be       | 0
						 be       | 0
						 cs1      | 9
						 af11     | 10
						 af12     | 12
						 af13     | 14
						 cs2      | 16
						 af21     | 18
						 af22     | 20
						 af23     | 22
						 cs3      | 24
						 af31     | 26
						 af32     | 28
						 af33     | 30
						 cs4      | 32
						 af41     | 34
						 af42     | 35
						 af43     | 38
						 cs5      | 40
						 ef       | 46
						 cs6      | 48
						 cs7      | 56"
			::= { sleMlsQosGlobalInfo 17 }

		
		sleQosDefaultCosToClassTrust OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"qos default cos to class trust
				Ex:QoS Cos+Cfi/DEI to CLASS:
				 COS       CFI/DEI  | CLASS
				-------------------------------------------
				 0         0        | cs0
				 0         1        | cs0
				 1         0        | cs1
				 1         1        | cs1
				 2         0        | cs2
				 2         1        | cs2
				 3         0        | cs3
				 3         1        | cs3
				 4         0        | cs4
				 4         1        | cs4
				 5         0        | cs5
				 5         1        | cs5
				 6         0        | cs6
				 6         1        | cs6
				 7         0        | cs7
				 7         1        | cs7"
			::= { sleMlsQosGlobalInfo 18 }

		
		sleQosDefaultCosToClassNoTrust OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"qos Default cos to class no trust
				Ex:
				 QoS Cos+Cfi/DEI to CLASS:
				 COS       CFI/DEI  | CLASS
				-------------------------------------------
				 0         0        | be
				 0         1        | be
				 1         0        | be
				 1         1        | be
				 2         0        | be
				 2         1        | be
				 3         0        | be
				 3         1        | be
				 4         0        | be
				 4         1        | be
				 5         0        | be
				 5         1        | be
				 6         0        | be
				 6         1        | be
				 7         0        | be
				 7         1        | be"
			::= { sleMlsQosGlobalInfo 19 }

		
		sleQosDefaultDscpToClassTrust OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"qos default dscp to class trust
				Ex: QoS Dscp to CLASS:
				DSCP     | CLASS
				-------------------------------------------
				0        |     be
				1        |     be
				2        |     be
				3        |     be
				4        |     be
				5        |     be
				6        |     be
				7        |     be
				8        |     cs1
				9        |     be
				10       |     af11
				11       |     be
				12       |     af12
				13       |     be
				14       |     af13
				15       |     be
				16       |     cs2
				17       |     be
				18       |     af21
				19       |     be
				20       |     af22
				21       |     be
				22       |     af23
				23       |     be
				24       |     cs3
				25       |     be
				26       |     af31
				27       |     be
				28       |     af32
				29       |     be
				30       |     af33
				31       |     be
				32       |     cs4
				33       |     be
				34       |     af41
				35       |     be
				36       |     af42
				37       |     be
				38       |     af43
				39       |     be
				40       |     cs5
				41       |     be
				42       |     be
				43       |     be
				44       |     be
				45       |     be
				46       |     ef
				47       |     be
				48       |     cs6
				49       |     be
				50       |     be
				51       |     be
				52       |     be
				53       |     be
				54       |     be
				55       |     be
				56       |     cs7
				57       |     be
				58       |     be
				59       |     be
				60       |     be
				61       |     be
				62       |     be
				63       |     be
				"
			::= { sleMlsQosGlobalInfo 20 }

		
		sleQosDefaultDscpToClassNoTrust OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"qos defualt dscp to class no trust
				Ex:
				 QoS Dscp to CLASS:
				DSCP     | CLASS
				-------------------------------------------
				0        |     be
				1        |     be
				2        |     be
				3        |     be
				4        |     be
				5        |     be
				6        |     be
				7        |     be
				8        |     be
				9        |     be
				10       |     be
				11       |     be
				12       |     be
				13       |     be
				14       |     be
				15       |     be
				16       |     be
				17       |     be
				18       |     be
				19       |     be
				20       |     be
				21       |     be
				22       |     be
				23       |     be
				24       |     be
				25       |     be
				26       |     be
				27       |     be
				28       |     be
				29       |     be
				30       |     be
				31       |     be
				32       |     be
				33       |     be
				34       |     be
				35       |     be
				36       |     be
				37       |     be
				38       |     be
				39       |     be
				40       |     be
				41       |     be
				42       |     be
				43       |     be
				44       |     be
				45       |     be
				46       |     be
				47       |     be
				48       |     be
				49       |     be
				50       |     be
				51       |     be
				52       |     be
				53       |     be
				54       |     be
				55       |     be
				56       |     be
				57       |     be
				58       |     be
				59       |     be
				60       |     be
				61       |     be
				62       |     be
				63       |     be"
			::= { sleMlsQosGlobalInfo 21 }

		
		sleMlsQosGlobalControl OBJECT IDENTIFIER ::= { sleMlsQosGlobal 2 }

		
		sleMlsQosGlobalControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setMlQosStatus(1),
				setMlsQosMapping(2),
				unSetMlsQosMapping(3),
				setMlsQosStrictQueue(4),
				unSetMlsQosStrictQueue(5),
				setMlsQosWrr(6),
				unSetMlsQosWrr(7),
				setMlsQosCpuRate(8),
				setMlsQosCpuQueueWt(9),
				setMlsQosNodeCpuRate(10),
				setMlsQosNodeCpuQueueWt(11),
				setHQosStatistics(12),
				unsetHQosStatistics(13)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { sleMlsQosGlobalControl 1 }

		
		sleMlsQosGlobalControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosGlobalControl 2 }

		
		sleMlsQosGlobalCtrlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosGlobalControl 3 }

		
		sleMlsQosGlobalControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosGlobalControl 4 }

		
		sleMlsQosGlobalControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosGlobalControl 5 }

		
		sleMlsQosCtrlGlobalStatus OBJECT-TYPE
			SYNTAX MlsQosStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Enable / Disable MlQos
				enable (1) 
				disable (0) "
			::= { sleMlsQosGlobalControl 6 }

		
		sleMlsQosGlobalControlMappingType OBJECT-TYPE
			SYNTAX MlsQosMappingType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Below Mapping Types can be configured
				cosToCos(1),
				cosToQueue(2),
				dscpToDscp(3),
				dscpToQueue(4),
				expToExp(5),
				expToQueue(6)
				expToClass(7)"
			::= { sleMlsQosGlobalControl 7 }

		
		sleMlsQosGlobalControlMappingIngValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ingress or incoming mapping values. 
				Based on sleMlsQosGlobalControlMappingType, this value has to be set.
				Ex.
					If sleMlsQosGlobalControlMappingType = cosToCos, then
					sleMlsQosGlobalControlMappingIngValue =  Incoming or ingress COS value"
			::= { sleMlsQosGlobalControl 8 }

		
		sleMlsQosGlobalControlMappingEgrValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Egress or outgoing mapping values. 
				Based on sleMlsQosGlobalControlMappingType, this value has to be set.
				Ex.
					If sleMlsQosGlobalControlMappingType = cosToCos, then
					sleMlsQosGlobalControlMappingEgrValue =  outgoing or egress COS value"
			::= { sleMlsQosGlobalControl 9 }

		
		sleMlsQosGlobalControlQueueId OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Queue Id"
			::= { sleMlsQosGlobalControl 10 }

		
		sleMlsQosGlobalControlWrrQueueWeight OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Queue weight for weighted round robin scheduling. 
				This value is set for the Queue Id configured via sleMlsQosGlobalControlQueueId"
			::= { sleMlsQosGlobalControl 11 }

		
		sleMlsQosGlobalControlCpuMaxPpsRate OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Rate limiting for CPU. Defines Max packets per second to CPU
				This value is set for the Queue Id configured via sleMlsQosGlobalControlQueueId"
			::= { sleMlsQosGlobalControl 12 }

		
		sleMlsQosGlobalControlCpuQueueWt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Weight for CPU Queue.
				This value is set for the Queue Id configured via sleMlsQosGlobalControlQueueId"
			::= { sleMlsQosGlobalControl 13 }

		
		sleMlsQosGlobalControlNodeId OBJECT-TYPE
			SYNTAX INTEGER (0..4)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Node Id"
			::= { sleMlsQosGlobalControl 14 }

		
		sleMlsQosGlobalControlMappingEgrClassValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Egress or outgoing mapping value is only for exp to class
				Diffserv class alias, eg: be, ef, af11 etc. 
				If sleMlsQosGlobalControlMappingType is exp-to-class, this value has to be set.
				Ex.
					If sleMlsQosGlobalControlMappingType = exp-to-class, then
					sleMlsQosGlobalControlMappingEgrValue =  outgoing or egress class value"
			::= { sleMlsQosGlobalControl 15 }

		
		sleMlsQosAggPolice OBJECT IDENTIFIER ::= { sleMlsQos 2 }

		
		sleMlsQosAggPoliceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMlsQosAggPoliceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Aggrgegate Policy Table"
			::= { sleMlsQosAggPolice 1 }

		
		sleMlsQosAggPoliceEntry OBJECT-TYPE
			SYNTAX SleMlsQosAggPoliceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleMlsQosAggPoliceIndex }
			::= { sleMlsQosAggPoliceTable 1 }

		
		SleMlsQosAggPoliceEntry ::=
			SEQUENCE { 
				sleMlsQosAggPoliceIndex
					Integer32,
				sleMlsQosAggPoliceName
					OCTET STRING,
				sleMlsQosAggPoliceTrafficRate
					Integer32,
				sleMlsQosAggPoliceBurstSize
					Integer32
			 }

		sleMlsQosAggPoliceIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Police index"
			::= { sleMlsQosAggPoliceEntry 1 }

		
		sleMlsQosAggPoliceName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Police Name"
			::= { sleMlsQosAggPoliceEntry 2 }

		
		sleMlsQosAggPoliceTrafficRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Traffic Rate configured for the Aggregate police profile"
			::= { sleMlsQosAggPoliceEntry 3 }

		
		sleMlsQosAggPoliceBurstSize OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Burst Size configured for the Aggregate police profile"
			::= { sleMlsQosAggPoliceEntry 4 }

		
		sleMlsQosAggPoliceControl OBJECT IDENTIFIER ::= { sleMlsQosAggPolice 2 }

		
		sleMlsQosAggPoliceCtrlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createAggrPolice(1),
				deleteAggrPolice(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMlsQosAggPoliceControl 1 }

		
		sleMlsQosAggPoliceCtrlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosAggPoliceControl 2 }

		
		sleMlsQosAggPoliceConfigCtrlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosAggPoliceControl 3 }

		
		sleMlsQosAggPoliceCtrlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosAggPoliceControl 4 }

		
		sleMlsQosAggPoliceCtrlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosAggPoliceControl 5 }

		
		sleMlsQosAggPoliceCtrlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Aggregator Police Name"
			::= { sleMlsQosAggPoliceControl 6 }

		
		sleMlsQosAggPoliceCtrlTrafficRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Traffic Rate configured for the Aggrgegate Police profile"
			::= { sleMlsQosAggPoliceControl 7 }

		
		sleMlsQosAggPoliceCtrlBurstSize OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Traffic Rate configured for the Aggrgegate Police profile"
			::= { sleMlsQosAggPoliceControl 8 }

		
		sleMlsQosACL OBJECT IDENTIFIER ::= { sleMlsQos 3 }

		
		sleMlsQosACLTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMlsQosACLEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosACL 1 }

		
		sleMlsQosACLEntry OBJECT-TYPE
			SYNTAX SleMlsQosACLEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleMlsQosACLIndex, sleMlsQosACLFilterIndex }
			::= { sleMlsQosACLTable 1 }

		
		SleMlsQosACLEntry ::=
			SEQUENCE { 
				sleMlsQosACLIndex
					Integer32,
				sleMlsQosACLFilterIndex
					Integer32,
				sleMlsQosACLName
					OCTET STRING,
				sleMlsQosACLMatchType
					ACLMatchType,
				sleMlsQosACLMatchAction
					ACLMatchActionType,
				sleMlsQosACLEtherType
					ACLEtherType,
				sleMlsQosACLL3Protocol
					Integer32,
				sleMlsQosACLSrcIpAddress
					OCTET STRING,
				sleMlsQosACLDstIpAddress
					OCTET STRING,
				sleMlsQosACLSrcIpAddrMask
					OCTET STRING,
				sleMlsQosACLDstIpAddrMask
					OCTET STRING,
				sleMlsQosACLSrcMacAddress
					OCTET STRING,
				sleMlsQosACLDstMacAddress
					OCTET STRING,
				sleMlsQosACLSrcMacAddrMask
					OCTET STRING,
				sleMlsQosACLDstMacAddrMask
					OCTET STRING,
				sleMlsQosACLTcpUdpSrcPortAction
					AclTcpUdpPortActionType,
				sleMlsQosACLTcpUdpDstPortAction
					AclTcpUdpPortActionType,
				sleMlsQosACLTcpUdpSrcPort
					Integer32,
				sleMlsQosACLTcpUdpDstPort
					Integer32,
				sleMlsQosACLNameSrcIpExactMatch
					Integer32,
				sleMlsQosACLActionRemarkDesc
					OCTET STRING,
				sleMlsQosACLIcmpType
					Integer32,
				sleMlsQosACLIcmpCode
					Integer32,
				sleMlsQosACLTcpUdpSrcPortEnd
					Integer32,
				sleMlsQosACLTcpUdpDstPortEnd
					Integer32
			 }

		sleMlsQosACLIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Index for the table. 
				This is a running index number, which will be generated internally"
			::= { sleMlsQosACLEntry 1 }

		
		sleMlsQosACLFilterIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Filter Index for the table. 
				This is a running index number, which will be generated internally"
			::= { sleMlsQosACLEntry 2 }

		
		sleMlsQosACLName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Access Control List Num.
				<1-99>       standard access list
				<100-199>    extended access list
				<1300-1999>  standard access list (expanded range)
				<2000-2699>  extended access list (expanded range)
				<WORD> Name of the access list"
			::= { sleMlsQosACLEntry 3 }

		
		sleMlsQosACLMatchType OBJECT-TYPE
			SYNTAX ACLMatchType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" ACL MATCH Type
				mac (1),
				EthType(2),
				l3Proto (3)"
			::= { sleMlsQosACLEntry 4 }

		
		sleMlsQosACLMatchAction OBJECT-TYPE
			SYNTAX ACLMatchActionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" ACL Actions
				deny(0) 	deny the packets
				permit(1)   permit.
				remark(2)	mark the packets"
			::= { sleMlsQosACLEntry 5 }

		
		sleMlsQosACLEtherType OBJECT-TYPE
			SYNTAX ACLEtherType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" ACL ethertype to be matched.
				ip4(0) 		ipv4 packets
				ip6(1)    	ipv6 packets.
				mpls(3)		mpls packets "
			::= { sleMlsQosACLEntry 6 }

		
		sleMlsQosACLL3Protocol OBJECT-TYPE
			SYNTAX Integer32 (0..256)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" ACL protocols to be matched.
				0-255 : IANA protocol numbers
				256 - any
				Some important protocol Numbers 
				GRE - 47
				IGMP -2
				IPV4 - 4
				IPComp - 108
				OSPF - 89
				RSVP - 46
				PIM  - 103
				VRRP - 112"
			::= { sleMlsQosACLEntry 7 }

		
		sleMlsQosACLSrcIpAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Src Ip Address "
			::= { sleMlsQosACLEntry 8 }

		
		sleMlsQosACLDstIpAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" DST  Address"
			::= { sleMlsQosACLEntry 9 }

		
		sleMlsQosACLSrcIpAddrMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" SRC Address Mask"
			::= { sleMlsQosACLEntry 10 }

		
		sleMlsQosACLDstIpAddrMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" DST Address Mask"
			::= { sleMlsQosACLEntry 11 }

		
		sleMlsQosACLSrcMacAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Src MAC Address "
			::= { sleMlsQosACLEntry 12 }

		
		sleMlsQosACLDstMacAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" DST MAC Address"
			::= { sleMlsQosACLEntry 13 }

		
		sleMlsQosACLSrcMacAddrMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" SRC Address Mask"
			::= { sleMlsQosACLEntry 14 }

		
		sleMlsQosACLDstMacAddrMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" DST Address Mask"
			::= { sleMlsQosACLEntry 15 }

		
		sleMlsQosACLTcpUdpSrcPortAction OBJECT-TYPE
			SYNTAX AclTcpUdpPortActionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" TCP/UDP Src port action
				0 - no-action
				1 - equal
				2 - not-equal
				3 - less-than
				4 - Greater-than
				5 - Range"
			::= { sleMlsQosACLEntry 16 }

		
		sleMlsQosACLTcpUdpDstPortAction OBJECT-TYPE
			SYNTAX AclTcpUdpPortActionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" TCP/UDP Dst port action
				0 - no-action
				1 - equal
				2 - not-equal
				3 - less-than
				4 - Greater-than
				5 - Range"
			::= { sleMlsQosACLEntry 17 }

		
		sleMlsQosACLTcpUdpSrcPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" TCP/UDP Src port ."
			::= { sleMlsQosACLEntry 18 }

		
		sleMlsQosACLTcpUdpDstPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" TCP/UDP Dst port ."
			::= { sleMlsQosACLEntry 19 }

		
		sleMlsQosACLNameSrcIpExactMatch OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Exact Match"
			::= { sleMlsQosACLEntry 20 }

		
		sleMlsQosACLActionRemarkDesc OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Remark Description"
			::= { sleMlsQosACLEntry 21 }

		
		sleMlsQosACLIcmpType OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" The object is used to display the ICMP type of control message 
				
				Description                                 Type               Code
				                  echo-reply                                   0                 0
				net-unreachable        						 3                 0
				     					host-unreachable                         	 3                 1
				       protocol-unreachable            			 3                 2
				         				port-unreachable                			 3                 3
					packet-too-big							     3                 4
					source-route-failed                          3                 5
					network-unknown                              3                 6
					host-unknown			                     3                 7
				host-isolated                                3 				   8		     
									    dod-net-prohibited		      		         3                 9
				       		dod-host-prohibited                          3                 10
						net-tos-unreachable     			         3                 11    
					    host-tos-unreachable	                     3                 12 
						administratively-prohibited                  3                 13   
						host-precedence-unreachable                  3                 14
				    precedence-unreachable		                 3                 15
				       source-quench        					     4                 0
				    net-redirect					             5                 0
					host-redirect					             5                 1
					net-tos-redirect    					     5                 2
				   host-tos-redirect					         5                 3
				   alternate-address                            6  			  -1
				   echo                                         8 				   0
				   router-advertisement                         9                 0
				                        router-solicitation					         10				   0
				                        time-exceeded						         11				  -1
				                        ttl-exceeded                                 11                0
				                        reassembly-timeout                           11                1	
				                        parameter-problem		                     12		          -1    
				                        no-room-for-option                           12                0
				                        option-missing 				                 12				   1
				                        timestamp-request                            13				   0
				                        timestamp-reply                              14				   0
				                        information-request                          15				   0
				                        information-reply                            16				   0
				                        mask-request                                 17				   0
				                        mask-reply                                   18 			   0 
				                        traceroute                                   30               -1
				                        conversion-error                             31               -1
				                        mobile-redirect                              32               -1
				                                                         "
			::= { sleMlsQosACLEntry 22 }

		
		sleMlsQosACLIcmpCode OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" The object is used to display the ICMP code of control message
				
				Description                                 Type               Code
				echo-reply                                   0                 0
				net-unreachable        						 3                 0
				host-unreachable                         	 3                 1
				protocol-unreachable            			 3                 2
				port-unreachable                			 3                 3
				packet-too-big							     3                 4
				source-route-failed                          3                 5
				network-unknown                              3                 6
				host-unknown			                     3                 7
				host-isolated                                3 				   8		     
				dod-net-prohibited		      		         3                 9
				dod-host-prohibited                          3                 10
				net-tos-unreachable     			         3                 11    
				host-tos-unreachable	                     3                 12 
				administratively-prohibited                  3                 13   
				host-precedence-unreachable                  3                 14
				precedence-unreachable		                 3                 15
				source-quench        					     4                 0
				net-redirect					             5                 0
				host-redirect					             5                 1
				net-tos-redirect    					     5                 2
				host-tos-redirect					         5                 3
				alternate-address                            6  			  -1
				echo                                         8 				   0
				router-advertisement                         9                 0
				      router-solicitation					         10				   0
				      time-exceeded						         11				  -1
				      ttl-exceeded                                 11                0
				      reassembly-timeout                           11                1	
				      parameter-problem		                     12		          -1    
				      no-room-for-option                           12                0
				      option-missing 				                 12				   1
				      timestamp-request                            13				   0
				      timestamp-reply                              14				   0
				      information-request                          15				   0
				      information-reply                            16				   0
				      mask-request                                 17				   0
				      mask-reply                                   18 			   0 
				      traceroute                                   30               -1
				      conversion-error                             31               -1
				      mobile-redirect                              32               -1
				"
			::= { sleMlsQosACLEntry 23 }

		
		sleMlsQosACLTcpUdpSrcPortEnd OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" End port for TCP/UDP source port range."
			::= { sleMlsQosACLEntry 24 }

		
		sleMlsQosACLTcpUdpDstPortEnd OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" End port for TCP/UDP destination port range."
			::= { sleMlsQosACLEntry 25 }

		
		sleMlsQosACLControl OBJECT IDENTIFIER ::= { sleMlsQosACL 2 }

		
		sleMlsQosACLControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setStandAclMatchSrcIp(1),
				unSetStandAclMatchSrcIp(2),
				setExtenAclMatchMac(3),
				unSetExtenAclMatchMac(4),
				setExtenAclMatchEthType(5),
				unSetExtenAclMatchEthType(6),
				setExtenAclMatchL3Proto(7),
				unSetExtenAclMatchL3Proto(8),
				setExtenAclMatchTcpUdp(9),
				unSetExtenAclMatchTcpUdp(10),
				setAclNameSrcIp(11),
				unSetAclNameSrcIp(12),
				setAclWithActionRemark(13),
				unSetAclWithActionRemark(14)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMlsQosACLControl 1 }

		
		sleMlsQosACLControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosACLControl 2 }

		
		sleMlsQosACLConfigControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosACLControl 3 }

		
		sleMlsQosACLControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosACLControl 4 }

		
		sleMlsQosACLControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosACLControl 5 }

		
		sleMlsQosACLCtrlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Access Control List Name.
				<1-99>       standard access list
				<100-199>    extended access list
				<1300-1999>  standard access list (expanded range)
				<2000-2699>  extended access list (expanded range)
				<word> Name for the access list"
			::= { sleMlsQosACLControl 6 }

		
		sleMlsQosACLCtrlMatchType OBJECT-TYPE
			SYNTAX ACLMatchType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL Match Types
				mac (1),
				EthType(2),
				l3Proto (3)"
			::= { sleMlsQosACLControl 7 }

		
		sleMlsQosACLCtrlMatchAction OBJECT-TYPE
			SYNTAX ACLMatchActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL Actions
				deny(0) 	deny the packets
				permit(1)    	Permit packets
				remark(2)	mark the packets"
			::= { sleMlsQosACLControl 8 }

		
		sleMlsQosACLCtrlEtherType OBJECT-TYPE
			SYNTAX ACLEtherType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL MAC ethertype to be matched.
				WORD  Ethertype value - (0x600-0xffff)
				ip4   IPv4 Ethertype - 0x0800
				ip6   IPv6 Ethertype - 0x86dd
				mpls  MPLS Ethertype - 0x8847"
			::= { sleMlsQosACLControl 9 }

		
		sleMlsQosACLCtrlL3Protocol OBJECT-TYPE
			SYNTAX Integer32 (0..256)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL protocols to be matched.
				0-255 : IANA protocol numbers
				256 - any
				Some important protocol Numbers 
				GRE - 47
				IGMP -2
				IPV4 - 4
				IPComp - 108
				OSPF - 89
				RSVP - 46
				PIM  - 103
				VRRP - 112"
			::= { sleMlsQosACLControl 10 }

		
		sleMlsQosACLCtrlSrcAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL SrcAddress.
				1. MAC address
				2. IP Address
				3. Any"
			::= { sleMlsQosACLControl 11 }

		
		sleMlsQosACLCtrlDstAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL SrcAddress.
				1. MAC address
				2. IP Address
				3. Any"
			::= { sleMlsQosACLControl 12 }

		
		sleMlsQosACLCtrlSrcAddrMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL Src Addr Mask.
				1. MAC address
				2. IP Address"
			::= { sleMlsQosACLControl 13 }

		
		sleMlsQosACLCtrlDstAddrMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" ACL dst Addr Mask.
				1. MAC address
				2. IP Address"
			::= { sleMlsQosACLControl 14 }

		
		sleMlsQosACLCtrlTcpUdpSrcPortAction OBJECT-TYPE
			SYNTAX AclTcpUdpPortActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" TCP/UDP Port Action to be matched.
				1 - equal
				2 - not-equal
				3 - less-than
				4 - Greater-than
				5 - Range"
			::= { sleMlsQosACLControl 15 }

		
		sleMlsQosACLCtrlTcpUdpDstPortAction OBJECT-TYPE
			SYNTAX AclTcpUdpPortActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" TCP/UDP Port Action to be matched.
				1 - equal
				2 - not-equal
				3 - less-than
				4 - Greater-than
				5 - Range"
			::= { sleMlsQosACLControl 16 }

		
		sleMlsQosACLCtrlTcpUdpSrcPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" TCP/UDP Src port to be matched."
			::= { sleMlsQosACLControl 17 }

		
		sleMlsQosACLCtrlTcpUdpDstPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" TCP/UDP Dst port to be matched."
			::= { sleMlsQosACLControl 18 }

		
		sleMlsQosACLCtrlAclNameExactMatch OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Configure Exact Match for Name"
			::= { sleMlsQosACLControl 19 }

		
		sleMlsQosACLCtrlActionRemarkDesc OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Configure remark description"
			::= { sleMlsQosACLControl 20 }

		
		sleMlsQosACLCtrlIcmpType OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The object is used to set the ICMP type of control message   
				
				Description                                 Type               Code   
				echo-reply                                   0                 0
				net-unreachable        						 3                 0
				host-unreachable                         	 3                 1
				protocol-unreachable            			 3                 2
				port-unreachable                			 3                 3
				packet-too-big							     3                 4
				source-route-failed                          3                 5
				network-unknown                              3                 6
				host-unknown			                     3                 7
				host-isolated                                3 				   8		     
				dod-net-prohibited		      		         3                 9
				dod-host-prohibited                          3                 10
				net-tos-unreachable     			         3                 11    
				host-tos-unreachable	                     3                 12 
				administratively-prohibited                  3                 13   
				host-precedence-unreachable                  3                 14
				precedence-unreachable		                 3                 15
				source-quench        					     4                 0
				net-redirect					             5                 0
				host-redirect					             5                 1
				net-tos-redirect    					     5                 2
				host-tos-redirect					         5                 3
				alternate-address                            6  			  -1
				echo                                         8 				   0
				router-advertisement                         9                 0
				      router-solicitation					         10				   0
				      time-exceeded						         11				  -1
				      ttl-exceeded                                 11                0
				      reassembly-timeout                           11                1	
				      parameter-problem		                     12		          -1    
				      no-room-for-option                           12                0
				      option-missing 				                 12				   1
				      timestamp-request                            13				   0
				      timestamp-reply                              14				   0
				      information-request                          15				   0
				      information-reply                            16				   0
				      mask-request                                 17				   0
				      mask-reply                                   18 			   0 
				      traceroute                                   30               -1
				      conversion-error                             31               -1
				      mobile-redirect                              32               -1
				                          "
			::= { sleMlsQosACLControl 21 }

		
		sleMlsQosACLCtrlIcmpCode OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The object is used to set the ICMP code of control message 
				
				Description                                 Type               Code
				echo-reply                                   0                 0
				net-unreachable        						 3                 0
				host-unreachable                         	 3                 1
				protocol-unreachable            			 3                 2
				port-unreachable                			 3                 3
				packet-too-big							     3                 4
				source-route-failed                          3                 5
				network-unknown                              3                 6
				host-unknown			                     3                 7
				host-isolated                                3 				   8		     
				dod-net-prohibited		      		         3                 9
				dod-host-prohibited                          3                 10
				net-tos-unreachable     			         3                 11    
				host-tos-unreachable	                     3                 12 
				administratively-prohibited                  3                 13   
				host-precedence-unreachable                  3                 14
				precedence-unreachable		                 3                 15
				source-quench        					     4                 0
				net-redirect					             5                 0
				host-redirect					             5                 1
				net-tos-redirect    					     5                 2
				host-tos-redirect					         5                 3
				alternate-address                            6  			  -1
				echo                                         8 				   0
				router-advertisement                         9                 0
				      router-solicitation					         10				   0
				      time-exceeded						         11				  -1
				      ttl-exceeded                                 11                0
				      reassembly-timeout                           11                1	
				      parameter-problem		                     12		          -1    
				      no-room-for-option                           12                0
				      option-missing 				                 12				   1
				      timestamp-request                            13				   0
				      timestamp-reply                              14				   0
				      information-request                          15				   0
				      information-reply                            16				   0
				      mask-request                                 17				   0
				      mask-reply                                   18 			   0 
				      traceroute                                   30               -1
				      conversion-error                             31               -1
				      mobile-redirect                              32               -1
				          "
			::= { sleMlsQosACLControl 22 }

		
		sleMlsQosACLCtrlTcpUdpSrcPortEnd OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" End port for TCP/UDP Src port range to be matched."
			::= { sleMlsQosACLControl 23 }

		
		sleMlsQosACLCtrlTcpUdpDstPortEnd OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" End port for TCP/UDP Dst port range to be matched."
			::= { sleMlsQosACLControl 24 }

		
		sleMlsQosACLNotification OBJECT IDENTIFIER ::= { sleMlsQosACL 3 }

		
		sleMlsQosClassMap OBJECT IDENTIFIER ::= { sleMlsQos 4 }

		
		sleMlsQosClassMapTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMlsQosClassMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosClassMap 1 }

		
		sleMlsQosClassMapEntry OBJECT-TYPE
			SYNTAX SleMlsQosClassMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleMlsQosClassMapName }
			::= { sleMlsQosClassMapTable 1 }

		
		SleMlsQosClassMapEntry ::=
			SEQUENCE { 
				sleMlsQosClassMapName
					OCTET STRING,
				sleMlsQosClassMapMatchCosValue
					Integer32,
				sleMlsQosClassMapMatchInnerCosValue
					Integer32,
				sleMlsQosClassMapMatchEgressInterface
					OCTET STRING,
				sleMlsQosClassMapMatchEtherType
					OCTET STRING,
				sleMlsQosClassMapMatchSrcIpAddr
					OCTET STRING,
				sleMlsQosClassMapMatchDstIpAddr
					OCTET STRING,
				sleMlsQosClassMapMatchSrcIpMaskLen
					Integer32,
				sleMlsQosClassMapMatchDstIpMaskLen
					Integer32,
				sleMlsQosClassMapMatchSrcIpV6Addr
					OCTET STRING,
				sleMlsQosClassMapMatchDstIpV6Addr
					OCTET STRING,
				sleMlsQosClassMapMatchSrcIpV6MaskLen
					Integer32,
				sleMlsQosClassMapMatchDstIpV6MaskLen
					Integer32,
				sleMlsQosClassMapMatchIpDscp
					Integer32,
				sleMlsQosClassMapMatchIpPrecedence
					Integer32,
				sleMlsQosClassMapMatchIp6Dscp
					Integer32,
				sleMlsQosClassMapMatchIp6Precedence
					Integer32,
				sleMlsQosClassMapMatchTcpSrcPort
					OCTET STRING,
				sleMlsQosClassMapMatchTcpDstPort
					OCTET STRING,
				sleMlsQosClassMapMatchTcpSrcPortRange
					OCTET STRING,
				sleMlsQosClassMapMatchTcpDstPortRange
					OCTET STRING,
				sleMlsQosClassMapMatchUdpSrcPort
					OCTET STRING,
				sleMlsQosClassMapMatchUdpDstPort
					OCTET STRING,
				sleMlsQosClassMapMatchUdpSrcPortRange
					OCTET STRING,
				sleMlsQosClassMapMatchUdpDstPortRange
					OCTET STRING,
				sleMlsQosClassMapMatchSrcMacAddr
					OCTET STRING,
				sleMlsQosClassMapMatchSrcMacMask
					OCTET STRING,
				sleMlsQosClassMapMatchDstMacAddr
					OCTET STRING,
				sleMlsQosClassMapMatchDstMacMask
					OCTET STRING,
				sleMlsQosClassMapMatchVlanId
					OCTET STRING,
				sleMlsQosClassMapMatchVlanIdRange
					OCTET STRING,
				sleMlsQosClassMapMatchInnerVlanId
					Integer32,
				sleMlsQosClassMapMatchVlanTpid
					OCTET STRING,
				sleMlsQosClassMapMatchAccessGroup
					OCTET STRING,
				sleMlsQosClassMapMatchLayer4SrcPort
					OCTET STRING,
				sleMlsQosClassMapMatchLayer4DstPort
					OCTET STRING,
				sleMlsQosClassMapMatchLayer4SrcPortRange
					OCTET STRING,
				sleMlsQosClassMapMatchLayer4DstPortRange
					OCTET STRING,
				sleMplsQosClassMapMatchCriteria
					INTEGER
			 }

		sleMlsQosClassMapName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Index for the table. Class Map name"
			::= { sleMlsQosClassMapEntry 1 }

		
		sleMlsQosClassMapMatchCosValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Cos value"
			::= { sleMlsQosClassMapEntry 2 }

		
		sleMlsQosClassMapMatchInnerCosValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Inner Cos value"
			::= { sleMlsQosClassMapEntry 3 }

		
		sleMlsQosClassMapMatchEgressInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Engress Interface"
			::= { sleMlsQosClassMapEntry 4 }

		
		sleMlsQosClassMapMatchEtherType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Eth Type"
			::= { sleMlsQosClassMapEntry 5 }

		
		sleMlsQosClassMapMatchSrcIpAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src Ip Address"
			::= { sleMlsQosClassMapEntry 6 }

		
		sleMlsQosClassMapMatchDstIpAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : dst Ip Address"
			::= { sleMlsQosClassMapEntry 7 }

		
		sleMlsQosClassMapMatchSrcIpMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src Ip Mask Length"
			::= { sleMlsQosClassMapEntry 8 }

		
		sleMlsQosClassMapMatchDstIpMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Dst Ip Mask Length"
			::= { sleMlsQosClassMapEntry 9 }

		
		sleMlsQosClassMapMatchSrcIpV6Addr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src Ip Address"
			::= { sleMlsQosClassMapEntry 10 }

		
		sleMlsQosClassMapMatchDstIpV6Addr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : dst Ip Address"
			::= { sleMlsQosClassMapEntry 11 }

		
		sleMlsQosClassMapMatchSrcIpV6MaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src Ip Mask Length"
			::= { sleMlsQosClassMapEntry 12 }

		
		sleMlsQosClassMapMatchDstIpV6MaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Dst Ip Mask Length"
			::= { sleMlsQosClassMapEntry 13 }

		
		sleMlsQosClassMapMatchIpDscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : DSCP value"
			::= { sleMlsQosClassMapEntry 14 }

		
		sleMlsQosClassMapMatchIpPrecedence OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Ip Precedence"
			::= { sleMlsQosClassMapEntry 15 }

		
		sleMlsQosClassMapMatchIp6Dscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Ip6 DSCP value"
			::= { sleMlsQosClassMapEntry 16 }

		
		sleMlsQosClassMapMatchIp6Precedence OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : ip6 Precedence value"
			::= { sleMlsQosClassMapEntry 17 }

		
		sleMlsQosClassMapMatchTcpSrcPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : TCP src Port"
			::= { sleMlsQosClassMapEntry 18 }

		
		sleMlsQosClassMapMatchTcpDstPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : TCP Dst Port"
			::= { sleMlsQosClassMapEntry 19 }

		
		sleMlsQosClassMapMatchTcpSrcPortRange OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : TCP src Port Range"
			::= { sleMlsQosClassMapEntry 20 }

		
		sleMlsQosClassMapMatchTcpDstPortRange OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : TCP Dst Port Range"
			::= { sleMlsQosClassMapEntry 21 }

		
		sleMlsQosClassMapMatchUdpSrcPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : UDP src Port"
			::= { sleMlsQosClassMapEntry 22 }

		
		sleMlsQosClassMapMatchUdpDstPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : UDP Dst Port"
			::= { sleMlsQosClassMapEntry 23 }

		
		sleMlsQosClassMapMatchUdpSrcPortRange OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : UDP src Port"
			::= { sleMlsQosClassMapEntry 24 }

		
		sleMlsQosClassMapMatchUdpDstPortRange OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : UDP Dst Port"
			::= { sleMlsQosClassMapEntry 25 }

		
		sleMlsQosClassMapMatchSrcMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src MAC Addr "
			::= { sleMlsQosClassMapEntry 26 }

		
		sleMlsQosClassMapMatchSrcMacMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src MAC Addr Mask"
			::= { sleMlsQosClassMapEntry 27 }

		
		sleMlsQosClassMapMatchDstMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Dst MAC Addr "
			::= { sleMlsQosClassMapEntry 28 }

		
		sleMlsQosClassMapMatchDstMacMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Dst MAC Addr Mask"
			::= { sleMlsQosClassMapEntry 29 }

		
		sleMlsQosClassMapMatchVlanId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Vlan Id"
			::= { sleMlsQosClassMapEntry 30 }

		
		sleMlsQosClassMapMatchVlanIdRange OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Vlan Id Range"
			::= { sleMlsQosClassMapEntry 31 }

		
		sleMlsQosClassMapMatchInnerVlanId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Inner Vlan Id "
			::= { sleMlsQosClassMapEntry 32 }

		
		sleMlsQosClassMapMatchVlanTpid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Vlan TPID"
			::= { sleMlsQosClassMapEntry 33 }

		
		sleMlsQosClassMapMatchAccessGroup OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Acl Name"
			::= { sleMlsQosClassMapEntry 34 }

		
		sleMlsQosClassMapMatchLayer4SrcPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Layer4 Any Src Port"
			::= { sleMlsQosClassMapEntry 35 }

		
		sleMlsQosClassMapMatchLayer4DstPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Layer4 Any Dst Port"
			::= { sleMlsQosClassMapEntry 36 }

		
		sleMlsQosClassMapMatchLayer4SrcPortRange OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Layer4 Any src Port range"
			::= { sleMlsQosClassMapEntry 37 }

		
		sleMlsQosClassMapMatchLayer4DstPortRange OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Layer4 Any Dst Port range"
			::= { sleMlsQosClassMapEntry 38 }

		
		sleMplsQosClassMapMatchCriteria OBJECT-TYPE
			SYNTAX INTEGER
				{
				noMatch(0),
				matchAll(1),
				matchAny(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the 
				Class map match criteria,  to map the matchAll or MatchAny 
				for HQS."
			::= { sleMlsQosClassMapEntry 39 }

		
		sleMlsQosClassMapControl OBJECT IDENTIFIER ::= { sleMlsQosClassMap 2 }

		
		sleMlsQosClassMapControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createClassMap(1),
				deleteClassMap(2),
				setClassMapMatch(3),
				unSetClassMapMatch(4),
				setClassMapMatchRange(5),
				unSetClassMapMatchRange(6),
				setClassMapMatchEtherType(7),
				unSetClassMapMatchEtherType(8),
				setClassMapMatchIpAddr(9),
				unSetClassMapMatchIpAddr(10),
				setClassMapMatchMac(11),
				unSetClassMapMatchMac(12),
				setClassMapMatchVlanTpid(13),
				unSetClassMapMatchVlanTpid(14),
				setClassMapMatchAccessGroup(15),
				unSetClassMapMatchAccessGroup(16),
				setClassMapMatchEgressIntf(17),
				unSetClassMapMatchEgressIntf(18)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMlsQosClassMapControl 1 }

		
		sleMlsQosClassMapControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosClassMapControl 2 }

		
		sleMlsQosClassMapControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosClassMapControl 3 }

		
		sleMlsQosClassMapontrolTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosClassMapControl 4 }

		
		sleMlsQosClassMapControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosClassMapControl 5 }

		
		sleMlsQosClassMapCtrlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class Map name"
			::= { sleMlsQosClassMapControl 6 }

		
		sleMlsQosClassMapCtrlMatchType OBJECT-TYPE
			SYNTAX ClassMapMatchType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class Map Type
				cos (1) ,
				innerCos(2),
				ipDscp (3),
				ipPrecedence (4),
				ip6Dscp (5),
				ip6Precedence (6),
				tcpSrcPort (7),
				tcpDstPort (8),
				udpSrcPort (9),
				udpDstPort (10),
				VlanId (11),
				innerVlanId (12),
				layer4SrcPort (13),
				layer4DstPort (14)"
			::= { sleMlsQosClassMapControl 7 }

		
		sleMlsQosClassMapCtrlMatchVal OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class Map match Value"
			::= { sleMlsQosClassMapControl 8 }

		
		sleMlsQosClassMapCtrlMatchRangeType OBJECT-TYPE
			SYNTAX ClassMapMatchRangeType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class Map Type
				tcpSrcPort (1),
				tcpDstPort (2),
				udpSrcPort (3),
				udpDstPort (4),
				VlanId (5),
				layer4SrcPort (6),
				layer4DstPort (7)"
			::= { sleMlsQosClassMapControl 9 }

		
		sleMlsQosClassMapCtrlMatchRangeLow OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class Map match Value: Low"
			::= { sleMlsQosClassMapControl 10 }

		
		sleMlsQosClassMapCtrlMatchRangeHigh OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class Map match Value : High"
			::= { sleMlsQosClassMapControl 11 }

		
		sleMlsQosClassMapCtrlMatchEtherType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Eth Type"
			::= { sleMlsQosClassMapControl 12 }

		
		sleMlsQosClassMapCtrlMatchSrcType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipv4(1),
				ipv6(2),
				mac(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class Map Src Type 
				1. IPv4
				2. IPv6
				3. Mac"
			::= { sleMlsQosClassMapControl 13 }

		
		sleMlsQosClassMapCtrlMatchSrcAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src Address.
				It can be IPV4 / IPV6 / MAC"
			::= { sleMlsQosClassMapControl 14 }

		
		sleMlsQosClassMapCtrlMatchDstAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : dst Ip Address
				it can be IPV4 / IPV6 / MAC"
			::= { sleMlsQosClassMapControl 15 }

		
		sleMlsQosClassMapCtrlMatchSrcIpMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src Ip Mask Length"
			::= { sleMlsQosClassMapControl 16 }

		
		sleMlsQosClassMapCtrlMatchDstIpMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Dst Ip Mask Length"
			::= { sleMlsQosClassMapControl 17 }

		
		sleMlsQosClassMapCtrlMatchSrcMacMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Src MAC Addr Mask"
			::= { sleMlsQosClassMapControl 18 }

		
		sleMlsQosClassMapCtrlMatchDstMacMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : Dst MAC Addr Mask"
			::= { sleMlsQosClassMapControl 19 }

		
		sleMlsQosClassMapCtrlMatchAcessGroup OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : ACL Group List"
			::= { sleMlsQosClassMapControl 20 }

		
		sleMlsQosClassMapCtrlMatchVlanTpid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : vlan TDID"
			::= { sleMlsQosClassMapControl 21 }

		
		sleMlsQosClassMapCtrlMatchEgressInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Match criteria : egress interface.
				This can be CPU, interface Name, Vlan Interface"
			::= { sleMlsQosClassMapControl 22 }

		
		sleMplsQosClassMapCtrlMatchCriteria OBJECT-TYPE
			SYNTAX INTEGER
				{
				matchAll(1),
				matchAny(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the 
				Class map match criteria,  to map the matchAll or MatchAny 
				for HQS."
			::= { sleMlsQosClassMapControl 23 }

		
		sleMlsQosClassMapNotification OBJECT IDENTIFIER ::= { sleMlsQosClassMap 3 }

		
		sleMlsQosPolicyMap OBJECT IDENTIFIER ::= { sleMlsQos 5 }

		
		sleMlsQosPolicyMapTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMlsQosPolicyMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosPolicyMap 1 }

		
		sleMlsQosPolicyMapEntry OBJECT-TYPE
			SYNTAX SleMlsQosPolicyMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleMlsQosPmapName, sleMlsQosPmapClassName }
			::= { sleMlsQosPolicyMapTable 1 }

		
		SleMlsQosPolicyMapEntry ::=
			SEQUENCE { 
				sleMlsQosPmapName
					OCTET STRING,
				sleMlsQosPmapClassName
					OCTET STRING,
				sleMlsQosPmapClassMatchPriority
					PmapPriorityType,
				sleMlsQosPmapClassOperMode
					INTEGER,
				sleMlsQosPmapClassPoliceType
					PmapPoliceType,
				sleMlsQosPmapClassPoliceCIR
					Integer32,
				sleMlsQosPmapClassPolicePIR
					Integer32,
				sleMlsQosPmapClassPoliceCBS
					Integer32,
				sleMlsQosPmapClassPoliceEBS
					Integer32,
				sleMlsQosPmapClassPoliceExdAction
					PmapExceedActionType,
				sleMlsQosPmapClassPoliceExdActionCos
					Integer32,
				sleMlsQosPmapClassPoliceExdActionDscp
					Integer32,
				sleMlsQosPmapClassPoliceExdActionTos
					Integer32,
				sleMlsQosPmapClassPoliceExdActionViolateAction
					PmapViolateActionType,
				sleMlsQosPmapClassPoliceExdActionViolateValue
					Integer32,
				sleMlsQosPmapClassPoliceAggregateName
					OCTET STRING,
				sleMlsQosPmapClassSetActionDeny
					Integer32,
				sleMlsQosPmapClassSetActionCos
					Integer32,
				sleMlsQosPmapClassSetActionCpuCos
					Integer32,
				sleMlsQosPmapClassSetActionIpDscp
					Integer32,
				sleMlsQosPmapClassSetActionIp6Dscp
					Integer32,
				sleMlsQosPmapClassSetActionIpPrecedence
					Integer32,
				sleMlsQosPmapClassSetActionIp6Precedence
					Integer32,
				sleMlsQosPmapClassSetActionMirrorToPortVal
					OCTET STRING,
				sleMlsQosPmapClassSetActionRedirectToPortVal
					OCTET STRING,
				sleMlsQosPmapClassSetActionVlanId
					Integer32,
				sleMlsQosPmapClassSetActionVlanCos
					Integer32,
				sleMlsQosPmapClassSetActionQosGroup
					INTEGER,
				sleMplsQosPmapClassSetActionQueue
					INTEGER,
				sleMplsQosPmapClassSetActionCopyCpu
					INTEGER
			 }

		sleMlsQosPmapName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Policy Map name"
			::= { sleMlsQosPolicyMapEntry 1 }

		
		sleMlsQosPmapClassName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class map Name"
			::= { sleMlsQosPolicyMapEntry 2 }

		
		sleMlsQosPmapClassMatchPriority OBJECT-TYPE
			SYNTAX PmapPriorityType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Priority
				low(0) 
				medium(1)
				high(2)
				highest(3)"
			::= { sleMlsQosPolicyMapEntry 3 }

		
		sleMlsQosPmapClassOperMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				allow(1),
				deny(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Operation Mode"
			::= { sleMlsQosPolicyMapEntry 4 }

		
		sleMlsQosPmapClassPoliceType OBJECT-TYPE
			SYNTAX PmapPoliceType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Police Type
				srtcm(1) 
				trtcm(2)"
			::= { sleMlsQosPolicyMapEntry 5 }

		
		sleMlsQosPmapClassPoliceCIR OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Police CIR (committed ingress rate) value"
			::= { sleMlsQosPolicyMapEntry 6 }

		
		sleMlsQosPmapClassPolicePIR OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Police PIR (peak ingress rate) value"
			::= { sleMlsQosPolicyMapEntry 7 }

		
		sleMlsQosPmapClassPoliceCBS OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Police CBS (committed burst size) value"
			::= { sleMlsQosPolicyMapEntry 8 }

		
		sleMlsQosPmapClassPoliceEBS OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Police EBS (Excess burst size) value"
			::= { sleMlsQosPolicyMapEntry 9 }

		
		sleMlsQosPmapClassPoliceExdAction OBJECT-TYPE
			SYNTAX PmapExceedActionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Exceed Action Type
				none (0)
				drop (1)
				set-dscp-transmit (2)
				set-tos-transmit (3)
				set-cos-transmit (4)
				transmit (7)"
			::= { sleMlsQosPolicyMapEntry 10 }

		
		sleMlsQosPmapClassPoliceExdActionCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Exceed Action COS"
			::= { sleMlsQosPolicyMapEntry 11 }

		
		sleMlsQosPmapClassPoliceExdActionDscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Exceed Action DSCP"
			::= { sleMlsQosPolicyMapEntry 12 }

		
		sleMlsQosPmapClassPoliceExdActionTos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Exceed Action TOS"
			::= { sleMlsQosPolicyMapEntry 13 }

		
		sleMlsQosPmapClassPoliceExdActionViolateAction OBJECT-TYPE
			SYNTAX PmapViolateActionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Violate Action Type
				none(0)
				drop(1) 
				set-dscp-transmit(2)
				set-cos-transmit(3)
				set-tos-transmit(4)
				transmit(5)"
			::= { sleMlsQosPolicyMapEntry 14 }

		
		sleMlsQosPmapClassPoliceExdActionViolateValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Violate Action Value"
			::= { sleMlsQosPolicyMapEntry 15 }

		
		sleMlsQosPmapClassPoliceAggregateName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Agrregate Police name: "
			::= { sleMlsQosPolicyMapEntry 16 }

		
		sleMlsQosPmapClassSetActionDeny OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Action Type: deny"
			::= { sleMlsQosPolicyMapEntry 17 }

		
		sleMlsQosPmapClassSetActionCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Cos value "
			::= { sleMlsQosPolicyMapEntry 18 }

		
		sleMlsQosPmapClassSetActionCpuCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" cpy Cos value. "
			::= { sleMlsQosPolicyMapEntry 19 }

		
		sleMlsQosPmapClassSetActionIpDscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Ip DSCP value ."
			::= { sleMlsQosPolicyMapEntry 20 }

		
		sleMlsQosPmapClassSetActionIp6Dscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Ip6 DSCP value to be set."
			::= { sleMlsQosPolicyMapEntry 21 }

		
		sleMlsQosPmapClassSetActionIpPrecedence OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Ip precedence value to be set."
			::= { sleMlsQosPolicyMapEntry 22 }

		
		sleMlsQosPmapClassSetActionIp6Precedence OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Ip6 precedence value to be set."
			::= { sleMlsQosPolicyMapEntry 23 }

		
		sleMlsQosPmapClassSetActionMirrorToPortVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Mirror to Port value."
			::= { sleMlsQosPolicyMapEntry 24 }

		
		sleMlsQosPmapClassSetActionRedirectToPortVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" redirect to Port value."
			::= { sleMlsQosPolicyMapEntry 25 }

		
		sleMlsQosPmapClassSetActionVlanId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Vlan Id value."
			::= { sleMlsQosPolicyMapEntry 26 }

		
		sleMlsQosPmapClassSetActionVlanCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Vlan COS value"
			::= { sleMlsQosPolicyMapEntry 27 }

		
		sleMlsQosPmapClassSetActionQosGroup OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Heirarchical qos policy-map qos-group.
				Max range is 1 to 2000."
			::= { sleMlsQosPolicyMapEntry 28 }

		
		sleMplsQosPmapClassSetActionQueue OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Internal Queue Priority."
			::= { sleMlsQosPolicyMapEntry 29 }

		
		sleMplsQosPmapClassSetActionCopyCpu OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Copy to CPU."
			::= { sleMlsQosPolicyMapEntry 30 }

		
		sleMlsQosPolicyMapControl OBJECT IDENTIFIER ::= { sleMlsQosPolicyMap 2 }

		
		sleMlsQosPolicyMapControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createPolicyMapClass(1),
				deletePolicyMapClass(2),
				setPolicyMapClassMatchPriority(3),
				setPolicyMapClassOperMode(4),
				unSetPolicyMapClassOperMode(5),
				setPolicyMapClassPolicer(6),
				unSetPolicyMapClassPolicer(7),
				setPolicyMapClassPolicerAggregate(8),
				unSetPolicyMapClassPolicerAggregate(9),
				setPolicyMapClassSet(10),
				unSetPolicyMapClassSet(11),
				deletePolicyMap(12)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMlsQosPolicyMapControl 1 }

		
		sleMlsQosPolicyMapControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosPolicyMapControl 2 }

		
		sleMlsQosPolicyMapControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosPolicyMapControl 3 }

		
		sleMlsQosPolicyMapontrolTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosPolicyMapControl 4 }

		
		sleMlsQosPolicyMapControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosPolicyMapControl 5 }

		
		sleMlsQosPolicyMapCtrlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index for the table. Class Map name"
			::= { sleMlsQosPolicyMapControl 6 }

		
		sleMlsQosPolicyMapCtrlClassName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Name"
			::= { sleMlsQosPolicyMapControl 7 }

		
		sleMlsQosPolicyMapCtrlClassMatchPriority OBJECT-TYPE
			SYNTAX PmapPriorityType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Priority
				low(0) 
				medium(1)
				high(2)
				highest(3)"
			::= { sleMlsQosPolicyMapControl 8 }

		
		sleMlsQosPolicyMapCtrlClassOperMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				allow(1),
				deny(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Operation Mode"
			::= { sleMlsQosPolicyMapControl 9 }

		
		sleMlsQosPolicyMapCtrlClassPoliceType OBJECT-TYPE
			SYNTAX PmapPoliceType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Police Type
				srtcm(1) 
				trtcm(2)"
			::= { sleMlsQosPolicyMapControl 10 }

		
		sleMlsQosPolicyMapCtrlClassPoliceCIR OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Police CIR (committed ingress rate) value"
			::= { sleMlsQosPolicyMapControl 11 }

		
		sleMlsQosPolicyMapCtrlClassPolicePIR OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Police PIR (Peak ingress rate) value"
			::= { sleMlsQosPolicyMapControl 12 }

		
		sleMlsQosPolicyMapCtrlClassPoliceCBS OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Police CBS (Committed Burst size) value"
			::= { sleMlsQosPolicyMapControl 13 }

		
		sleMlsQosPolicyMapCtrlClassPoliceEBS OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Police EBS (Excess Burst size) value"
			::= { sleMlsQosPolicyMapControl 14 }

		
		sleMlsQosPolicyMapCtrlClassPoliceExdAction OBJECT-TYPE
			SYNTAX PmapExceedActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Exceed Action Type
				none (0)
				drop (1)
				set-dscp-transmit (2)
				set-tos-transmit (3)
				set-cos-transmit (4)
				transmit (7)"
			::= { sleMlsQosPolicyMapControl 15 }

		
		sleMlsQosPolicyMapCtrlClassPoliceExdActionCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Exceed Action COS"
			::= { sleMlsQosPolicyMapControl 16 }

		
		sleMlsQosPolicyMapCtrlClassPoliceExdActionDscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Exceed Action DSCP"
			::= { sleMlsQosPolicyMapControl 17 }

		
		sleMlsQosPolicyMapCtrlClassPoliceExdActionTos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Exceed Action TOS"
			::= { sleMlsQosPolicyMapControl 18 }

		
		sleMlsQosPolicyMapCtrlClassPoliceExdActionViolateAction OBJECT-TYPE
			SYNTAX PmapViolateActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Exceed Action Violate 
				none(0)
				drop(1) 
				set-dscp-transmit(2)
				set-cos-transmit(3)
				set-tos-transmit(4)
				transmit(5)"
			::= { sleMlsQosPolicyMapControl 19 }

		
		sleMlsQosPolicyMapCtrlClassPoliceExdActionViolateValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Exceed Action Violate Value"
			::= { sleMlsQosPolicyMapControl 20 }

		
		sleMlsQosPolicyMapCtrlClassPoliceAggregateName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Aggregate Policy name: this is configured via sleMlsQosAggPoliceTable"
			::= { sleMlsQosPolicyMapControl 21 }

		
		sleMlsQosPolicyMapCtrlClassSetAction OBJECT-TYPE
			SYNTAX PmapSetActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Action Types:
				cos (1),
				cpu-cos (2),
				ip-dscp (3),
				ip-precedence (4),
				redirect-to-port (5),
				mirror-to-port (6),
				vlan (7),
				ip6-dscp (8), 
				ip6-precedence (9),
				cpu-copy (10),
				deny (11),
				qos-group(12),
				none(13),
				queue(14),
				vlan-cos(15)"
			::= { sleMlsQosPolicyMapControl 22 }

		
		sleMlsQosPolicyMapCtrlClassSetActionCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Cos value to be set. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To set this value, sleMlsQosPolicyMapCtrlClassSetAction should be set as cos"
			::= { sleMlsQosPolicyMapControl 23 }

		
		sleMlsQosPolicyMapCtrlClassSetActionCpuCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" cpy Cos value to be set. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To set this value, sleMlsQosPolicyMapCtrlClassSetAction should be set as cpu-cos"
			::= { sleMlsQosPolicyMapControl 24 }

		
		sleMlsQosPolicyMapCtrlClassSetActionIpDscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Ip DSCP value to be set. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To set this value, sleMlsQosPolicyMapCtrlClassSetAction should be set as ip-dscp"
			::= { sleMlsQosPolicyMapControl 25 }

		
		sleMlsQosPolicyMapCtrlClassSetActionIp6Dscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Ip6 DSCP value to be set. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To set this value, sleMlsQosPolicyMapCtrlClassSetAction should be set as ip6-dscp"
			::= { sleMlsQosPolicyMapControl 26 }

		
		sleMlsQosPolicyMapCtrlClassSetActionIpPrecedence OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Ip precedence value to be set. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To set this value, sleMlsQosPolicyMapCtrlClassSetAction should be set as ip-precedence"
			::= { sleMlsQosPolicyMapControl 27 }

		
		sleMlsQosPolicyMapCtrlClassSetActionIp6Precedence OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Ip6 precedence value to be set. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To set this value, sleMlsQosPolicyMapCtrlClassSetAction should be set as ip6-precedence"
			::= { sleMlsQosPolicyMapControl 28 }

		
		sleMlsQosPolicyMapCtrlClassSetActionMirrorToPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Mirror to Port value. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To setthis value, sleMlsQosPolicyMapCtrlClassSetAction should be set as mirror-to-port"
			::= { sleMlsQosPolicyMapControl 29 }

		
		sleMlsQosPolicyMapCtrlClassSetActionRedirectToPort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" redirect to Port value. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To setthis value, sleMlsQosPolicyMapCtrlClassSetAction should be set as redirect-to-port"
			::= { sleMlsQosPolicyMapControl 30 }

		
		sleMlsQosPolicyMapCtrlClassSetActionVlanId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Vlan Id value. 
				This object is dependent on sleMlsQosPolicyMapCtrlClassSetAction.
				To set this value, sleMlsQosPolicyMapCtrlClassSetAction should be set as vlan"
			::= { sleMlsQosPolicyMapControl 31 }

		
		sleMlsQosPolicyMapCtrlClassSetActionVlanCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Vlan COS value"
			::= { sleMlsQosPolicyMapControl 32 }

		
		sleMlsQosPolicyMapCtrlClassSetActionQosGroup OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Heirarchical qos policy-map qos-group.
				Max range is 1 to 2000."
			::= { sleMlsQosPolicyMapControl 33 }

		
		sleMlsQosPolicyMapCtrlClassSetActionQueue OBJECT-TYPE
			SYNTAX INTEGER (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Internal Queue Priority."
			::= { sleMlsQosPolicyMapControl 34 }

		
		sleMlsQosInterface OBJECT IDENTIFIER ::= { sleMlsQos 6 }

		
		sleMlsQosInterfaceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMlsQosInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Interface table"
			::= { sleMlsQosInterface 1 }

		
		sleMlsQosInterfaceEntry OBJECT-TYPE
			SYNTAX SleMlsQosInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleMlsQosInterfaceIndex }
			::= { sleMlsQosInterfaceTable 1 }

		
		SleMlsQosInterfaceEntry ::=
			SEQUENCE { 
				sleMlsQosInterfaceIndex
					Integer32,
				sleMlsQosInterfaceName
					OCTET STRING,
				sleMlsQosInterfaceTrustState
					MlsQosIntfTrustState,
				sleMlsQosInterfaceCos
					Integer32,
				sleMlsQosInterfaceCosOverride
					Integer32,
				sleMlsQosInterfaceCosToCos
					OCTET STRING,
				sleMlsQosInterfaceCosToQueue
					OCTET STRING,
				sleMlsQosInterfaceDscp
					Integer32,
				sleMlsQosInterfaceDscpToDscp
					OCTET STRING,
				sleMlsQosInterfaceDscpToQueue
					OCTET STRING,
				sleMlsQosInterfaceExpToExp
					OCTET STRING,
				sleMlsQosInterfaceTrafficShapeRate
					Integer32,
				sleMlsQosInterfaceTrafficShapeBurst
					Integer32,
				sleMlsQosInterfaceInputPolicyMap
					OCTET STRING,
				sleMlsQosInterfaceOutputPolicyMap
					OCTET STRING,
				sleMlsQosInterfaceTrustPassthroughCos
					Integer32,
				sleMlsQosInterfaceTrustPassthroughDscp
					Integer32,
				sleHQosInterfaceCosToClass
					OCTET STRING,
				sleHQosInterfaceDscpToClass
					OCTET STRING,
				sleHQosInterfaceReplace
					INTEGER,
				sleMlsQosInterfaceTrafficIfgExclude
					Integer32,
				sleMlsQosInterfaceTrafficPolicingRate
					Integer32,
				sleMlsQosInterfaceTrafficPolicingBurst
					Integer32
			 }

		sleMlsQosInterfaceIndex OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This Index is based on Max entries i.e. 64 (dscp)"
			::= { sleMlsQosInterfaceEntry 1 }

		
		sleMlsQosInterfaceName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index for the table. Interface Name"
			::= { sleMlsQosInterfaceEntry 2 }

		
		sleMlsQosInterfaceTrustState OBJECT-TYPE
			SYNTAX MlsQosIntfTrustState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Interface trust state
				none (0)
				cos  (1)
				dscp (2)"
			::= { sleMlsQosInterfaceEntry 3 }

		
		sleMlsQosInterfaceCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Cos value"
			::= { sleMlsQosInterfaceEntry 4 }

		
		sleMlsQosInterfaceCosOverride OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Cos override value"
			::= { sleMlsQosInterfaceEntry 5 }

		
		sleMlsQosInterfaceCosToCos OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Cos to cos mapping"
			::= { sleMlsQosInterfaceEntry 6 }

		
		sleMlsQosInterfaceCosToQueue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Cos to queue mapping"
			::= { sleMlsQosInterfaceEntry 7 }

		
		sleMlsQosInterfaceDscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface DSCP values"
			::= { sleMlsQosInterfaceEntry 8 }

		
		sleMlsQosInterfaceDscpToDscp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface dscp to dscp mapping"
			::= { sleMlsQosInterfaceEntry 9 }

		
		sleMlsQosInterfaceDscpToQueue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface dscp to queue mapping"
			::= { sleMlsQosInterfaceEntry 10 }

		
		sleMlsQosInterfaceExpToExp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface exp to exp mapping"
			::= { sleMlsQosInterfaceEntry 11 }

		
		sleMlsQosInterfaceTrafficShapeRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Traffic Shaping rate"
			::= { sleMlsQosInterfaceEntry 12 }

		
		sleMlsQosInterfaceTrafficShapeBurst OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface traffic shaping burst"
			::= { sleMlsQosInterfaceEntry 13 }

		
		sleMlsQosInterfaceInputPolicyMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface policy map mapping"
			::= { sleMlsQosInterfaceEntry 14 }

		
		sleMlsQosInterfaceOutputPolicyMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosInterfaceEntry 15 }

		
		sleMlsQosInterfaceTrustPassthroughCos OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface trust pass-through COS enabled"
			::= { sleMlsQosInterfaceEntry 16 }

		
		sleMlsQosInterfaceTrustPassthroughDscp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface trust pass-through DSCP enabled"
			::= { sleMlsQosInterfaceEntry 17 }

		
		sleHQosInterfaceCosToClass OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface cos to class mapping in no truct mode
				It represnet COS <VALUE> - CNG <VALUE> - CLASS <VALUE>"
			::= { sleMlsQosInterfaceEntry 18 }

		
		sleHQosInterfaceDscpToClass OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface dscp to class mapping in no truct mode
				It represnet DSCP <VALUE> - CLASS <VALUE>"
			::= { sleMlsQosInterfaceEntry 19 }

		
		sleHQosInterfaceReplace OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				cos(1),
				dscp(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"To rewite the dscp/cos at egress in uniform mode."
			::= { sleMlsQosInterfaceEntry 20 }

		
		sleMlsQosInterfaceTrafficIfgExclude OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface traffic IFG exclude
				0 - No
				1 - Yes"
			::= { sleMlsQosInterfaceEntry 21 }

		
		sleMlsQosInterfaceTrafficPolicingRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface traffic policing rate
				<8-1000000> in kbps "
			::= { sleMlsQosInterfaceEntry 22 }

		
		sleMlsQosInterfaceTrafficPolicingBurst OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface traffic policing burst
				<2-2000> "
			::= { sleMlsQosInterfaceEntry 23 }

		
		sleMlsQosInterfaceControl OBJECT IDENTIFIER ::= { sleMlsQosInterface 2 }

		
		sleMlsQosInterfaceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setInterfaceQosMapping(1),
				unSetInterfaceQosMapping(2),
				setInterfaceTrafficShaping(3),
				unSetInterfaceTrafficShaping(4),
				setInterfaceInputPolicyMap(5),
				unSetInterfaceInputPolicyMap(6),
				setInterfaceOutputPolicyMap(7),
				unSetInterfaceOutputPolicyMap(8),
				setInterfaceOveride(9),
				unsetInterfaceOveride(10),
				setInterfaceReplace(11),
				unsetInterfaceReplace(12),
				setInterfaceTrafficIfgExclude(13),
				unsetInterfaceTrafficIfgExclude(14),
				setInterfaceTrafficPolicingDot3x(15),
				unsetInterfaceTrafficPolicingDot3x(16)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMlsQosInterfaceControl 1 }

		
		sleMlsQosInterfaceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosInterfaceControl 2 }

		
		sleMlsQosInterfaceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosInterfaceControl 3 }

		
		sleMlsQosInterfaceontrolTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosInterfaceControl 4 }

		
		sleMlsQosInterfaceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosInterfaceControl 5 }

		
		sleMlsQosInterfaceCtrlIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Name"
			::= { sleMlsQosInterfaceControl 6 }

		
		sleMlsQosInterfaceCtrlMapingType OBJECT-TYPE
			SYNTAX MlsQosInterfaceMapingType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Mapping Type:
				cos (1)          Configure interface default CoS values
				cos-cos (2)      Specify cos-cos
				cos-queue (3)    Specify cos-queue map
				dscp (4)         Set the default DSCP vlaue
				dscp-dscp(5)     Specify dscp-dscp
				dscp-queue (6)   DSCP-to-QUEUE
				exp-exp(7)  	   Specify exp-exp
				trust  (8)   	   Configure port trust state
				trust-passthrough (9)     COnfigure port trust pass-through state
				 cosToClass (10)			configure cos to class
				dscpToClass (11)			configure dscp to class
				"
			::= { sleMlsQosInterfaceControl 7 }

		
		sleMlsQosInterfaceCtrlMapingIngVal OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ingress or incoming mapping values. 
				Based on sleMlsQosInterfaceCtrlMapingType, this value has to be set.
				Ex.
					If sleMlsQosInterfaceCtrlMapingType = cosToCos, then
					sleMlsQosInterfaceCtrlMapingIngVal =  Incoming or ingress COS value"
			::= { sleMlsQosInterfaceControl 8 }

		
		sleMlsQosInterfaceCtrlMapingEgrVal OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Egress or outgoing mapping values. 
				Based on sleMlsQosInterfaceCtrlMapingType, this value has to be set.
				Ex.
					If sleMlsQosInterfaceCtrlMapingType = cosToCos, then
					sleMlsQosInterfaceCtrlMapingIngVal =  Egress COS value"
			::= { sleMlsQosInterfaceControl 9 }

		
		sleMlsQosInterfaceCtrlMapingCosOverride OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface COS override value"
			::= { sleMlsQosInterfaceControl 10 }

		
		sleMlsQosInterfaceCtrlMapingTrustState OBJECT-TYPE
			SYNTAX MlsQosIntfTrustState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Interface trust state
				Trust:
				cos  (1)
				dscp (2)
				Trust pass-through:
				pass-through cos (1)
				pass-through dscp (2)"
			::= { sleMlsQosInterfaceControl 11 }

		
		sleMlsQosInterfaceCtrlTrafficShapeRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Traffic shape rate"
			::= { sleMlsQosInterfaceControl 12 }

		
		sleMlsQosInterfaceCtrlTrafficShapeBurst OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Traffic shape burst"
			::= { sleMlsQosInterfaceControl 13 }

		
		sleMlsQosInterfaceCtrlInputPolicyMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Policy Map mapping"
			::= { sleMlsQosInterfaceControl 14 }

		
		sleMlsQosInterfaceCtrlOutputPolicyMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosInterfaceControl 15 }

		
		sleMlsQosInterfaceCtrlMapingCNGValue OBJECT-TYPE
			SYNTAX Integer32 (0..1)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Specify CNG value <0-1>"
			::= { sleMlsQosInterfaceControl 16 }

		
		sleMlsQosInterfaceCtrlMapingEgrClassVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosInterfaceControl 17 }

		
		sleMlsQosInterfaceCtrlReplace OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				cos(1),
				dscp(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"rewite the dscp/cos at egress in uniform mode."
			::= { sleMlsQosInterfaceControl 18 }

		
		sleMlsQosIntfQue OBJECT IDENTIFIER ::= { sleMlsQos 7 }

		
		sleMlsQosIntfQueTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMlsQosIntfQueEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosIntfQue 1 }

		
		sleMlsQosIntfQueEntry OBJECT-TYPE
			SYNTAX SleMlsQosIntfQueEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleMlsQosIntfQueIntfIndex, sleMlsQosIntfQueId }
			::= { sleMlsQosIntfQueTable 1 }

		
		SleMlsQosIntfQueEntry ::=
			SEQUENCE { 
				sleMlsQosIntfQueIntfIndex
					Integer32,
				sleMlsQosIntfQueId
					Integer32,
				sleMlsQosIntfQueShapeQueueRate
					Integer32,
				sleMlsQosIntfQueWrrQueueWeight
					Integer32,
				sleMlsQosIntfQueWrrRandomDetectMinThr
					Integer32,
				sleMlsQosIntfQueWrrRandomDetectMaxThr
					Integer32,
				sleMlsQosIntfQueWrrRandomDetectExpWt
					Integer32,
				sleMlsQosIntfQueTailDropThr
					Integer32,
				sleMlsQosIntfQueStrictQueue
					Integer32,
				sleMlsQosIntfQueRandomDetectDropStart
					Integer32,
				sleMlsQosIntfQueRandomDetectDropSlope
					Integer32,
				sleMlsQosIntfQueRandomDetectColor
					INTEGER,
				sleMlsQosIntfQueReservedBandwidth
					Integer32
			 }

		sleMlsQosIntfQueIntfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This Index is based on no of Queues"
			::= { sleMlsQosIntfQueEntry 1 }

		
		sleMlsQosIntfQueId OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This Index is based on no of Queues"
			::= { sleMlsQosIntfQueEntry 2 }

		
		sleMlsQosIntfQueShapeQueueRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Queue Shape rate"
			::= { sleMlsQosIntfQueEntry 3 }

		
		sleMlsQosIntfQueWrrQueueWeight OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Queue WRR queue weight"
			::= { sleMlsQosIntfQueEntry 4 }

		
		sleMlsQosIntfQueWrrRandomDetectMinThr OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Queue WRR random detect min threshold"
			::= { sleMlsQosIntfQueEntry 5 }

		
		sleMlsQosIntfQueWrrRandomDetectMaxThr OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Queue WRR random detect max threshold"
			::= { sleMlsQosIntfQueEntry 6 }

		
		sleMlsQosIntfQueWrrRandomDetectExpWt OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Queue WRR random detect exp weight"
			::= { sleMlsQosIntfQueEntry 7 }

		
		sleMlsQosIntfQueTailDropThr OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Queue TailDrop threshold"
			::= { sleMlsQosIntfQueEntry 8 }

		
		sleMlsQosIntfQueStrictQueue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Strict Queue Config"
			::= { sleMlsQosIntfQueEntry 9 }

		
		sleMlsQosIntfQueRandomDetectDropStart OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"WRR Random Detect Drop Start"
			::= { sleMlsQosIntfQueEntry 10 }

		
		sleMlsQosIntfQueRandomDetectDropSlope OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"WRR Random Detect Drop Slope"
			::= { sleMlsQosIntfQueEntry 11 }

		
		sleMlsQosIntfQueRandomDetectColor OBJECT-TYPE
			SYNTAX INTEGER
				{
				yellow(1),
				red(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"WRR Random Detect Color"
			::= { sleMlsQosIntfQueEntry 12 }

		
		sleMlsQosIntfQueReservedBandwidth OBJECT-TYPE
			SYNTAX Integer32 (8..1000000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Queue Shape rate (minimum)"
			::= { sleMlsQosIntfQueEntry 13 }

		
		sleMlsQosIntfQueControl OBJECT IDENTIFIER ::= { sleMlsQosIntfQue 2 }

		
		sleMlsQosIntfQueControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setQueueProfile(1),
				unSetQueueProfile(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMlsQosIntfQueControl 1 }

		
		sleMlsQosIntfQueControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosIntfQueControl 2 }

		
		sleMlsQosIntfQueControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosIntfQueControl 3 }

		
		sleMlsQosIntfQueontrolTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosIntfQueControl 4 }

		
		sleMlsQosIntfQueControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosIntfQueControl 5 }

		
		sleMlsQosIntfQueCtrlInterfaceIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Queue Id"
			::= { sleMlsQosIntfQueControl 6 }

		
		sleMlsQosIntfQueCtrlQueueId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Queue Id"
			::= { sleMlsQosIntfQueControl 7 }

		
		sleMlsQosIntfQueCtrlProfileType OBJECT-TYPE
			SYNTAX MlsQosIntfQueProfilingType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosIntfQueControl 8 }

		
		sleMlsQosIntfQueCtrlShapeQueueRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue Rate"
			::= { sleMlsQosIntfQueControl 9 }

		
		sleMlsQosIntfQueCtrlWrrQueueWeight OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue WRR Queue weight"
			::= { sleMlsQosIntfQueControl 10 }

		
		sleMlsQosIntfQueCtrlWrrRandomDetectMinThr OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue WRR random detect Min Threshold"
			::= { sleMlsQosIntfQueControl 11 }

		
		sleMlsQosIntfQueCtrlWrrRandomDetectMaxThr OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue WRR random detect Max Threshold"
			::= { sleMlsQosIntfQueControl 12 }

		
		sleMlsQosIntfQueCtrlWrrRandomDetectExpWt OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue WRR random detect exp weight"
			::= { sleMlsQosIntfQueControl 13 }

		
		sleMlsQosIntfQueCtrlTailDropThr OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue Taildrop Threshold"
			::= { sleMlsQosIntfQueControl 14 }

		
		sleMlsQosIntfQueCtrlRandomDetectDropStart OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue WRR Random Detect Drop Start"
			::= { sleMlsQosIntfQueControl 15 }

		
		sleMlsQosIntfQueCtrlRandomDetectDropSlope OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue WRR Random Detect Drop Slope"
			::= { sleMlsQosIntfQueControl 16 }

		
		sleMlsQosIntfQueCtrlRandomDetectColor OBJECT-TYPE
			SYNTAX INTEGER
				{
				yellow(1),
				red(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue WRR Random Detect Color"
			::= { sleMlsQosIntfQueControl 17 }

		
		sleMlsQosIntfQueCtrlReservedBandwidth OBJECT-TYPE
			SYNTAX Integer32 (8..1000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Queue Rate(minimum)"
			::= { sleMlsQosIntfQueControl 18 }

		
		sleMlsQosQStats OBJECT IDENTIFIER ::= { sleMlsQos 8 }

		
		sleMlsQosQStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMlsQosQStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { sleMlsQosQStats 1 }

		
		sleMlsQosQStatsEntry OBJECT-TYPE
			SYNTAX SleMlsQosQStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleMlsQosQstatsIfIndex, sleMlsQosQId }
			::= { sleMlsQosQStatsTable 1 }

		
		SleMlsQosQStatsEntry ::=
			SEQUENCE { 
				sleMlsQosQstatsIfIndex
					Integer32,
				sleMlsQosQId
					Integer32,
				sleMlsQosWredGreenDropPkts
					Counter64,
				sleMlsQosWredYellowDropPkts
					Counter64,
				sleMlsQosWredRedDropPkts
					Counter64,
				sleMlsQosTailDropPkts
					Counter64,
				sleMlsQosTailDropBytes
					Counter64,
				sleMlsQosQStatsOutPkts
					Counter64,
				sleMlsQosQStatsOutBytes
					Counter64,
				sleMlsQosQStatsMcastOutPkts
					Counter64,
				sleMlsQosQStatsMcastOutBytes
					Counter64,
				sleMlsQosQStatsDropPkts
					Counter64,
				sleMlsQosQStatsDropBytes
					Counter64,
				sleMlsQosQStatsMcastDropPkts
					Counter64,
				sleMlsQosQStatsMcastDropBytes
					Counter64,
				sleMlsQosQStatsEnqueuedPkts
					Counter64,
				sleMlsQosQStatsEnqueuedBytes
					Counter64
			 }

		sleMlsQosQstatsIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Index for the table. QStats Name"
			::= { sleMlsQosQStatsEntry 1 }

		
		sleMlsQosQId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This Index is based on Max entries i.e. 64 (dscp)"
			::= { sleMlsQosQStatsEntry 2 }

		
		sleMlsQosWredGreenDropPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Wred Green drop packet counter"
			::= { sleMlsQosQStatsEntry 3 }

		
		sleMlsQosWredYellowDropPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Wred yellow drop packet counter"
			::= { sleMlsQosQStatsEntry 4 }

		
		sleMlsQosWredRedDropPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Wred red drop packet counter"
			::= { sleMlsQosQStatsEntry 5 }

		
		sleMlsQosTailDropPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Taildrop drop packet counter"
			::= { sleMlsQosQStatsEntry 6 }

		
		sleMlsQosTailDropBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Taildrop drop bytes counter"
			::= { sleMlsQosQStatsEntry 7 }

		
		sleMlsQosQStatsOutPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total out packets in the Queue"
			::= { sleMlsQosQStatsEntry 8 }

		
		sleMlsQosQStatsOutBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total out bytes in the Queue"
			::= { sleMlsQosQStatsEntry 9 }

		
		sleMlsQosQStatsMcastOutPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total mcast out packets in the Queue"
			::= { sleMlsQosQStatsEntry 10 }

		
		sleMlsQosQStatsMcastOutBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total mcast out bytes in the Queue"
			::= { sleMlsQosQStatsEntry 11 }

		
		sleMlsQosQStatsDropPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total drop packets in the Queue"
			::= { sleMlsQosQStatsEntry 12 }

		
		sleMlsQosQStatsDropBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total drop bytes in the Queue"
			::= { sleMlsQosQStatsEntry 13 }

		
		sleMlsQosQStatsMcastDropPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total mcast drop packets in the Queue"
			::= { sleMlsQosQStatsEntry 14 }

		
		sleMlsQosQStatsMcastDropBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total mcast drop bytes in the Queue"
			::= { sleMlsQosQStatsEntry 15 }

		
		sleMlsQosQStatsEnqueuedPkts OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total Enqueued packets in the Queue"
			::= { sleMlsQosQStatsEntry 16 }

		
		sleMlsQosQStatsEnqueuedBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Total Enqueued bytes in the Queue"
			::= { sleMlsQosQStatsEntry 17 }

		
		sleMlsQosQStatsControl OBJECT IDENTIFIER ::= { sleMlsQosQStats 2 }

		
		sleMlsQosQStatsControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				clearWredStats(1),
				clearTailDropStats(2),
				clearQueueStats(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting this entry as proper value."
			::= { sleMlsQosQStatsControl 1 }

		
		sleMlsQosQStatsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosQStatsControl 2 }

		
		sleMlsQosQStatsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosQStatsControl 3 }

		
		sleMlsQosQStatsontrolTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosQStatsControl 4 }

		
		sleMlsQosQStatsControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosQStatsControl 5 }

		
		sleMlsQosQstatsCtrlIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index for the table. Class Map name"
			::= { sleMlsQosQStatsControl 6 }

		
		sleMlsQosQstatsCtrlQId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Class map Name"
			::= { sleMlsQosQStatsControl 7 }

		
		sleHqosClassMapQueue OBJECT IDENTIFIER ::= { sleMlsQos 9 }

		
		sleHqosClassMapQueueInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleHqosClassMapQueueInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Table is used to show the information for Class Map queueing Table"
			::= { sleHqosClassMapQueue 1 }

		
		sleHqosClassMapQueueInfoEntry OBJECT-TYPE
			SYNTAX SleHqosClassMapQueueInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Table is used to show the information for Class Map queueing Table"
			INDEX { sleHqosClassMapQueueInfoName }
			::= { sleHqosClassMapQueueInfoTable 1 }

		
		SleHqosClassMapQueueInfoEntry ::=
			SEQUENCE { 
				sleHqosClassMapQueueInfoName
					OCTET STRING,
				sleHqosClassMapQueueInfoMatchGroup
					INTEGER
			 }

		sleHqosClassMapQueueInfoName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Class Map queue name.
				
				An unique object identify an entry of this table."
			::= { sleHqosClassMapQueueInfoEntry 1 }

		
		sleHqosClassMapQueueInfoMatchGroup OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the match group of the class map 
				queueing."
			::= { sleHqosClassMapQueueInfoEntry 2 }

		
		sleHqosClassMapQueueControl OBJECT IDENTIFIER ::= { sleHqosClassMapQueue 2 }

		
		sleHqosClassMapQueueControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSleHqosClassMapQueueControlEntry(1),
				deleteSleHqosClassMapQueueControlEntry(2),
				setSleHqosClassMapQueueControlMatchGroup(3),
				unsetSleHqosClassMapQueueControlMatchGroup(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { sleHqosClassMapQueueControl 1 }

		
		sleHqosClassMapQueueControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as 
				.busy. or .idle. before do setRequest."
			::= { sleHqosClassMapQueueControl 2 }

		
		sleHqosClassMapQueueControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. 
				In case of short-time command, this value is 0"
			::= { sleHqosClassMapQueueControl 3 }

		
		sleHqosClassMapQueueControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleHqosClassMapQueueControl 4 }

		
		sleHqosClassMapQueueControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleHqosClassMapQueueControl 5 }

		
		sleHqosClassMapQueueControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This Object is used to the set the control name for the 
				Class map queue Table"
			::= { sleHqosClassMapQueueControl 6 }

		
		sleHqosClassMapQueueControlMatchGroup OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the match group of the class map queueing"
			::= { sleHqosClassMapQueueControl 7 }

		
		sleHqosPolicyMapQueue OBJECT IDENTIFIER ::= { sleMlsQos 10 }

		
		sleHqosPolicyMapQueueInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleHqosPolicyMapQueueInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This table is used to show the Policy mapping queue information 
				of the policy map queueing"
			::= { sleHqosPolicyMapQueue 1 }

		
		sleHqosPolicyMapQueueInfoEntry OBJECT-TYPE
			SYNTAX SleHqosPolicyMapQueueInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This table is used to show the Policy mapping queue information of 
				the policy map queueing"
			INDEX { sleHqosPolicyMapQueueInfoName, sleHqosClassMapQueueInfoName }
			::= { sleHqosPolicyMapQueueInfoTable 1 }

		
		SleHqosPolicyMapQueueInfoEntry ::=
			SEQUENCE { 
				sleHqosPolicyMapQueueInfoName
					OCTET STRING,
				sleHqosPolicyMapQueueInfoShape
					SnmpAdminString,
				sleHqosPolicyMapQueueInfoBandwidth
					SnmpAdminString,
				sleHqosPolicyMapQueueInfoBandwidthRemainingPercent
					INTEGER,
				sleHqosPolicyMapQueueInfoQueueLimit
					SnmpAdminString,
				sleHqosPolicyMapQueueInfoServicePolicyName
					SnmpAdminString,
				sleHqosPolicyMapQueueInfoPriority
					INTEGER,
				sleHqosPolicyMapQueueInfoRDMinThreshold
					SnmpAdminString,
				sleHqosPolicyMapQueueInfoRDMaxThreshold
					SnmpAdminString
			 }

		sleHqosPolicyMapQueueInfoName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object identify an entry in this table."
			::= { sleHqosPolicyMapQueueInfoEntry 1 }

		
		sleHqosPolicyMapQueueInfoShape OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" 
				This object is used to show the shape for the policy map queue
				Types:
					1)average 	1-1000000000(bps|kbps|mbps|gbps)
				2)percent		1-100
				
				"
			::= { sleHqosPolicyMapQueueInfoEntry 2 }

		
		sleHqosPolicyMapQueueInfoBandwidth OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the bandwidth for the policy map queue
				average 	1-1000000000(bps|kbps|mbps|gbps)
				percent		1-100
				
				"
			::= { sleHqosPolicyMapQueueInfoEntry 3 }

		
		sleHqosPolicyMapQueueInfoBandwidthRemainingPercent OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the remained bandwidth for the policy map queue"
			::= { sleHqosPolicyMapQueueInfoEntry 4 }

		
		sleHqosPolicyMapQueueInfoQueueLimit OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the queue limit for the policy map queue"
			::= { sleHqosPolicyMapQueueInfoEntry 5 }

		
		sleHqosPolicyMapQueueInfoServicePolicyName OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the service name for the policy map queue"
			::= { sleHqosPolicyMapQueueInfoEntry 6 }

		
		sleHqosPolicyMapQueueInfoPriority OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the priority for the policy map queue
				Always value is 1"
			::= { sleHqosPolicyMapQueueInfoEntry 7 }

		
		sleHqosPolicyMapQueueInfoRDMinThreshold OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the random detuct minimum threshold for the 
				policy map queue"
			::= { sleHqosPolicyMapQueueInfoEntry 8 }

		
		sleHqosPolicyMapQueueInfoRDMaxThreshold OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to show the random detuct maximum threshold for the 
				policy map queue"
			::= { sleHqosPolicyMapQueueInfoEntry 9 }

		
		sleHqosPolicyMapQueueControl OBJECT IDENTIFIER ::= { sleHqosPolicyMapQueue 2 }

		
		sleHqosPolicyMapQueueControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createPolicyMapQueueEntry(1),
				deletePolicyMapQueueEntry(2),
				setPolicyMapQueueParameters(3),
				unsetPolicyMapQueueParameters(4),
				setPolicyMapQueueServicePolicy(5),
				unsetPolicyMapQueueServicePolicy(6),
				setPolicyMapQueueRandomDetect(7),
				unsetPolicyMapQueueRandomDetect(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { sleHqosPolicyMapQueueControl 1 }

		
		sleHqosPolicyMapQueueControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. 
				before do setRequest."
			::= { sleHqosPolicyMapQueueControl 2 }

		
		sleHqosPolicyMapQueueControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, 
				this value is 0"
			::= { sleHqosPolicyMapQueueControl 3 }

		
		sleHqosPolicyMapQueueControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleHqosPolicyMapQueueControl 4 }

		
		sleHqosPolicyMapQueueControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleHqosPolicyMapQueueControl 5 }

		
		sleHqosPolicyMapQueueControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the name for the policy map queue"
			::= { sleHqosPolicyMapQueueControl 6 }

		
		sleHqosPolicyMapQueueControlClassMapQueueName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the name for the policy map queue"
			::= { sleHqosPolicyMapQueueControl 7 }

		
		sleHqosPolicyMapQueueControlParams OBJECT-TYPE
			SYNTAX INTEGER
				{
				shape(1),
				queueLimit(2),
				bandwidth(3),
				bandwidthRemain(4),
				priority(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" This object is used to set 
				1)shape: the shape for the policy map queue is to configure shaping on an egress 
							queue to impose a maximum rate on it
				2)queue-limit: the Queue Limit is to configure tail drop by setting queue limits 
							on both ingress and egress queues
				3)bandwidth: To allocate a minimum percentage of the interface bandwidth to a queue
				4)bandwidth-Remain: To configures the bandwidth remaining on the interface in a queue
				5)Priority:  To configure a single output queuing class as the priority queue 
				 
					average 	1-1000000000(bps|kbps|mbps|gbps)
					limit		1-131072 (packets | bytes | kbytes)
				percent		1-100
				"
			::= { sleHqosPolicyMapQueueControl 8 }

		
		sleHqosPolicyMapQueueControlParamsValue OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" This object is used to set 
				1)shape: the shape for the policy map queue is to configure shaping on an egress 
							queue to impose a maximum rate on it
				2)queue-limit: the Queue Limit is to configure tail drop by setting queue limits 
							on both ingress and egress queues
				3)bandwidth: To allocate a minimum percentage of the interface bandwidth to a queue
				4)bandwidth-Remain: To configures the bandwidth remaining on the interface in a queue
				5)Priority:  To configure a single output queuing class as the priority queue 
				 
					average 	1-1000000000(bps|kbps|mbps|gbps)
					limit		1-131072 (packets | bytes | kbytes)
				percent		1-100
				"
			::= { sleHqosPolicyMapQueueControl 9 }

		
		sleHqosPolicyMapQueueControlUnits OBJECT-TYPE
			SYNTAX INTEGER
				{
				bps(1),
				kbps(2),
				mbps(3),
				gbps(4),
				packets(5),
				bytes(6),
				kbytes(7),
				average(8),
				percent(9)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the units for the specified objects "
			::= { sleHqosPolicyMapQueueControl 10 }

		
		sleHqosPolicyMapQueueControlRDMiniThreshold OBJECT-TYPE
			SYNTAX INTEGER (1..131072)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"his object is used to set the Minimum random threashold"
			::= { sleHqosPolicyMapQueueControl 11 }

		
		sleHqosPolicyMapQueueControlRDMaxThreshold OBJECT-TYPE
			SYNTAX INTEGER (1..131072)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Maximum random threashold "
			::= { sleHqosPolicyMapQueueControl 12 }

		
		sleHqosPolicyMapQueueControlRDMinimumUnits OBJECT-TYPE
			SYNTAX INTEGER
				{
				bytes(1),
				kbytes(2),
				packets(3),
				percent(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the units for Random Minimum "
			::= { sleHqosPolicyMapQueueControl 13 }

		
		sleHqosPolicyMapQueueControlRDMaximumUnits OBJECT-TYPE
			SYNTAX INTEGER
				{
				bytes(1),
				kbytes(2),
				packets(3),
				percent(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the units for Random Maximum"
			::= { sleHqosPolicyMapQueueControl 14 }

		
		sleHqosPolicyMapQueueControlServicePolicyName OBJECT-TYPE
			SYNTAX SnmpAdminString (SIZE (1..48))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the service name,
				to attach a child policy onto a parent policy
				"
			::= { sleHqosPolicyMapQueueControl 15 }

		
		sleMlsQosAclGlobal OBJECT IDENTIFIER ::= { sleMlsQos 11 }

		
		sleMlsQosAclGlobalInfo OBJECT IDENTIFIER ::= { sleMlsQosAclGlobal 1 }

		
		sleMlsQosAclGlobalInfoMaxAccessList OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Max accessList suported"
			::= { sleMlsQosAclGlobalInfo 1 }

		
		sleMlsQosAclGlobalControl OBJECT IDENTIFIER ::= { sleMlsQosAclGlobal 2 }

		
		sleMlsQosAclGlobalControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setMaxAccessList(1),
				unsetMaxAccessList(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { sleMlsQosAclGlobalControl 1 }

		
		sleMlsQosAclGlobalControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as .busy. or .idle. before do setRequest."
			::= { sleMlsQosAclGlobalControl 2 }

		
		sleMlsQosAclGlobalControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMlsQosAclGlobalControl 3 }

		
		sleMlsQosAclGlobalControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMlsQosAclGlobalControl 4 }

		
		sleMlsQosAclGlobalControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMlsQosAclGlobalControl 5 }

		
		sleMlsQosAclGlobalControlMaxAccessList OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Max accessList suported"
			::= { sleMlsQosAclGlobalControl 6 }

		
		sleMlsQosGroup OBJECT-GROUP
			OBJECTS { sleMlsQosStatus, sleMlsQosMapCosToCos, sleMlsQosMapCosToQueue, sleMlsQosMapDscpToDscp, sleMlsQosMapDscpToQueue, 
				sleMlsQosMapExpToExp, sleMlsQosMapExpToQueue, sleMlsQosMapStrictQueueId, sleMlsQosMapWrrQueueWeight, sleMlsQosMapCpuMaxPpsRate, 
				sleMlsQosMapCpuQueueWeight, sleMlsQosMapNodeCpuMaxPpsRate, sleMlsQosMapNodeCpuQueueWeight, sleMlsQosMapExpToClass, sleHQosStatistics, 
				sleQosPhbPriorityColor, sleHQosDefaultClassToDscp, sleQosDefaultCosToClassTrust, sleQosDefaultCosToClassNoTrust, sleQosDefaultDscpToClassTrust, 
				sleQosDefaultDscpToClassNoTrust, sleMlsQosGlobalControlRequest, sleMlsQosGlobalControlStatus, sleMlsQosGlobalCtrlTimer, sleMlsQosGlobalControlTimeStamp, 
				sleMlsQosGlobalControlReqResult, sleMlsQosCtrlGlobalStatus, sleMlsQosGlobalControlMappingType, sleMlsQosGlobalControlMappingIngValue, sleMlsQosGlobalControlMappingEgrValue, 
				sleMlsQosGlobalControlQueueId, sleMlsQosGlobalControlWrrQueueWeight, sleMlsQosGlobalControlCpuMaxPpsRate, sleMlsQosGlobalControlCpuQueueWt, sleMlsQosGlobalControlNodeId, 
				sleMlsQosGlobalControlMappingEgrClassValue, sleMlsQosAggPoliceIndex, sleMlsQosAggPoliceName, sleMlsQosAggPoliceTrafficRate, sleMlsQosAggPoliceBurstSize, 
				sleMlsQosAggPoliceCtrlRequest, sleMlsQosAggPoliceCtrlStatus, sleMlsQosAggPoliceConfigCtrlTimer, sleMlsQosAggPoliceCtrlTimeStamp, sleMlsQosAggPoliceCtrlReqResult, 
				sleMlsQosAggPoliceCtrlName, sleMlsQosAggPoliceCtrlTrafficRate, sleMlsQosAggPoliceCtrlBurstSize, sleMlsQosACLIndex, sleMlsQosACLFilterIndex, 
				sleMlsQosACLName, sleMlsQosACLMatchType, sleMlsQosACLMatchAction, sleMlsQosACLEtherType, sleMlsQosACLL3Protocol, 
				sleMlsQosACLSrcIpAddress, sleMlsQosACLDstIpAddress, sleMlsQosACLSrcIpAddrMask, sleMlsQosACLDstIpAddrMask, sleMlsQosACLSrcMacAddress, 
				sleMlsQosACLDstMacAddress, sleMlsQosACLSrcMacAddrMask, sleMlsQosACLDstMacAddrMask, sleMlsQosACLTcpUdpSrcPortAction, sleMlsQosACLTcpUdpDstPortAction, 
				sleMlsQosACLTcpUdpSrcPort, sleMlsQosACLTcpUdpDstPort, sleMlsQosACLNameSrcIpExactMatch, sleMlsQosACLActionRemarkDesc, sleMlsQosACLIcmpType, 
				sleMlsQosACLIcmpCode, sleMlsQosACLTcpUdpSrcPortEnd, sleMlsQosACLTcpUdpDstPortEnd, sleMlsQosACLControlRequest, sleMlsQosACLControlStatus, 
				sleMlsQosACLConfigControlTimer, sleMlsQosACLControlTimeStamp, sleMlsQosACLControlReqResult, sleMlsQosACLCtrlName, sleMlsQosACLCtrlMatchType, 
				sleMlsQosACLCtrlMatchAction, sleMlsQosACLCtrlEtherType, sleMlsQosACLCtrlL3Protocol, sleMlsQosACLCtrlSrcAddress, sleMlsQosACLCtrlDstAddress, 
				sleMlsQosACLCtrlSrcAddrMask, sleMlsQosACLCtrlDstAddrMask, sleMlsQosACLCtrlTcpUdpSrcPortAction, sleMlsQosACLCtrlTcpUdpDstPortAction, sleMlsQosACLCtrlTcpUdpSrcPort, 
				sleMlsQosACLCtrlTcpUdpDstPort, sleMlsQosACLCtrlAclNameExactMatch, sleMlsQosACLCtrlActionRemarkDesc, sleMlsQosACLCtrlIcmpType, sleMlsQosACLCtrlIcmpCode, 
				sleMlsQosACLCtrlTcpUdpSrcPortEnd, sleMlsQosACLCtrlTcpUdpDstPortEnd, sleMlsQosClassMapName, sleMlsQosClassMapMatchCosValue, sleMlsQosClassMapMatchInnerCosValue, 
				sleMlsQosClassMapMatchEgressInterface, sleMlsQosClassMapMatchEtherType, sleMlsQosClassMapMatchSrcIpAddr, sleMlsQosClassMapMatchDstIpAddr, sleMlsQosClassMapMatchSrcIpMaskLen, 
				sleMlsQosClassMapMatchDstIpMaskLen, sleMlsQosClassMapMatchSrcIpV6Addr, sleMlsQosClassMapMatchDstIpV6Addr, sleMlsQosClassMapMatchSrcIpV6MaskLen, sleMlsQosClassMapMatchDstIpV6MaskLen, 
				sleMlsQosClassMapMatchIpDscp, sleMlsQosClassMapMatchIpPrecedence, sleMlsQosClassMapMatchIp6Dscp, sleMlsQosClassMapMatchIp6Precedence, sleMlsQosClassMapMatchTcpSrcPort, 
				sleMlsQosClassMapMatchTcpDstPort, sleMlsQosClassMapMatchTcpSrcPortRange, sleMlsQosClassMapMatchTcpDstPortRange, sleMlsQosClassMapMatchUdpSrcPort, sleMlsQosClassMapMatchUdpDstPort, 
				sleMlsQosClassMapMatchUdpSrcPortRange, sleMlsQosClassMapMatchUdpDstPortRange, sleMlsQosClassMapMatchSrcMacAddr, sleMlsQosClassMapMatchSrcMacMask, sleMlsQosClassMapMatchDstMacAddr, 
				sleMlsQosClassMapMatchDstMacMask, sleMlsQosClassMapMatchVlanId, sleMlsQosClassMapMatchVlanIdRange, sleMlsQosClassMapMatchInnerVlanId, sleMlsQosClassMapMatchVlanTpid, 
				sleMlsQosClassMapMatchAccessGroup, sleMlsQosClassMapMatchLayer4SrcPort, sleMlsQosClassMapMatchLayer4DstPort, sleMlsQosClassMapMatchLayer4SrcPortRange, sleMlsQosClassMapMatchLayer4DstPortRange, 
				sleMplsQosClassMapMatchCriteria, sleMlsQosClassMapControlRequest, sleMlsQosClassMapControlStatus, sleMlsQosClassMapControlTimer, sleMlsQosClassMapontrolTimeStamp, 
				sleMlsQosClassMapControlReqResult, sleMlsQosClassMapCtrlName, sleMlsQosClassMapCtrlMatchType, sleMlsQosClassMapCtrlMatchVal, sleMlsQosClassMapCtrlMatchRangeType, 
				sleMlsQosClassMapCtrlMatchRangeLow, sleMlsQosClassMapCtrlMatchRangeHigh, sleMlsQosClassMapCtrlMatchEtherType, sleMlsQosClassMapCtrlMatchSrcType, sleMlsQosClassMapCtrlMatchSrcAddr, 
				sleMlsQosClassMapCtrlMatchDstAddr, sleMlsQosClassMapCtrlMatchSrcIpMaskLen, sleMlsQosClassMapCtrlMatchDstIpMaskLen, sleMlsQosClassMapCtrlMatchSrcMacMask, sleMlsQosClassMapCtrlMatchDstMacMask, 
				sleMlsQosClassMapCtrlMatchAcessGroup, sleMlsQosClassMapCtrlMatchVlanTpid, sleMlsQosClassMapCtrlMatchEgressInterface, sleMplsQosClassMapCtrlMatchCriteria, sleMlsQosPmapName, 
				sleMlsQosPmapClassName, sleMlsQosPmapClassMatchPriority, sleMlsQosPmapClassOperMode, sleMlsQosPmapClassPoliceType, sleMlsQosPmapClassPoliceCIR, 
				sleMlsQosPmapClassPolicePIR, sleMlsQosPmapClassPoliceCBS, sleMlsQosPmapClassPoliceEBS, sleMlsQosPmapClassPoliceExdAction, sleMlsQosPmapClassPoliceExdActionCos, 
				sleMlsQosPmapClassPoliceExdActionDscp, sleMlsQosPmapClassPoliceExdActionTos, sleMlsQosPmapClassPoliceExdActionViolateAction, sleMlsQosPmapClassPoliceExdActionViolateValue, sleMlsQosPmapClassPoliceAggregateName, 
				sleMlsQosPmapClassSetActionDeny, sleMlsQosPmapClassSetActionCos, sleMlsQosPmapClassSetActionCpuCos, sleMlsQosPmapClassSetActionIpDscp, sleMlsQosPmapClassSetActionIp6Dscp, 
				sleMlsQosPmapClassSetActionIpPrecedence, sleMlsQosPmapClassSetActionIp6Precedence, sleMlsQosPmapClassSetActionMirrorToPortVal, sleMlsQosPmapClassSetActionRedirectToPortVal, sleMlsQosPmapClassSetActionVlanId, 
				sleMlsQosPmapClassSetActionVlanCos, sleMlsQosPmapClassSetActionQosGroup, sleMplsQosPmapClassSetActionQueue, sleMplsQosPmapClassSetActionCopyCpu, sleMlsQosPolicyMapControlRequest, 
				sleMlsQosPolicyMapControlStatus, sleMlsQosPolicyMapControlTimer, sleMlsQosPolicyMapontrolTimeStamp, sleMlsQosPolicyMapControlReqResult, sleMlsQosPolicyMapCtrlName, 
				sleMlsQosPolicyMapCtrlClassName, sleMlsQosPolicyMapCtrlClassMatchPriority, sleMlsQosPolicyMapCtrlClassOperMode, sleMlsQosPolicyMapCtrlClassPoliceType, sleMlsQosPolicyMapCtrlClassPoliceCIR, 
				sleMlsQosPolicyMapCtrlClassPolicePIR, sleMlsQosPolicyMapCtrlClassPoliceCBS, sleMlsQosPolicyMapCtrlClassPoliceEBS, sleMlsQosPolicyMapCtrlClassPoliceExdAction, sleMlsQosPolicyMapCtrlClassPoliceExdActionCos, 
				sleMlsQosPolicyMapCtrlClassPoliceExdActionDscp, sleMlsQosPolicyMapCtrlClassPoliceExdActionTos, sleMlsQosPolicyMapCtrlClassPoliceExdActionViolateAction, sleMlsQosPolicyMapCtrlClassPoliceExdActionViolateValue, sleMlsQosPolicyMapCtrlClassPoliceAggregateName, 
				sleMlsQosPolicyMapCtrlClassSetAction, sleMlsQosPolicyMapCtrlClassSetActionCos, sleMlsQosPolicyMapCtrlClassSetActionCpuCos, sleMlsQosPolicyMapCtrlClassSetActionIpDscp, sleMlsQosPolicyMapCtrlClassSetActionIp6Dscp, 
				sleMlsQosPolicyMapCtrlClassSetActionIpPrecedence, sleMlsQosPolicyMapCtrlClassSetActionIp6Precedence, sleMlsQosPolicyMapCtrlClassSetActionMirrorToPort, sleMlsQosPolicyMapCtrlClassSetActionRedirectToPort, sleMlsQosPolicyMapCtrlClassSetActionVlanId, 
				sleMlsQosPolicyMapCtrlClassSetActionVlanCos, sleMlsQosPolicyMapCtrlClassSetActionQosGroup, sleMlsQosPolicyMapCtrlClassSetActionQueue, sleMlsQosInterfaceIndex, sleMlsQosInterfaceName, 
				sleMlsQosInterfaceTrustState, sleMlsQosInterfaceCos, sleMlsQosInterfaceCosOverride, sleMlsQosInterfaceCosToCos, sleMlsQosInterfaceCosToQueue, 
				sleMlsQosInterfaceDscp, sleMlsQosInterfaceDscpToDscp, sleMlsQosInterfaceDscpToQueue, sleMlsQosInterfaceExpToExp, sleMlsQosInterfaceTrafficShapeRate, 
				sleMlsQosInterfaceTrafficShapeBurst, sleMlsQosInterfaceInputPolicyMap, sleMlsQosInterfaceOutputPolicyMap, sleMlsQosInterfaceTrustPassthroughCos, sleMlsQosInterfaceTrustPassthroughDscp, 
				sleHQosInterfaceCosToClass, sleHQosInterfaceDscpToClass, sleHQosInterfaceReplace, sleMlsQosInterfaceTrafficIfgExclude, sleMlsQosInterfaceTrafficPolicingRate, 
				sleMlsQosInterfaceTrafficPolicingBurst, sleMlsQosInterfaceControlRequest, sleMlsQosInterfaceControlStatus, sleMlsQosInterfaceControlTimer, sleMlsQosInterfaceontrolTimeStamp, 
				sleMlsQosInterfaceControlReqResult, sleMlsQosInterfaceCtrlIndex, sleMlsQosInterfaceCtrlMapingType, sleMlsQosInterfaceCtrlMapingIngVal, sleMlsQosInterfaceCtrlMapingEgrVal, 
				sleMlsQosInterfaceCtrlMapingCosOverride, sleMlsQosInterfaceCtrlMapingTrustState, sleMlsQosInterfaceCtrlTrafficShapeRate, sleMlsQosInterfaceCtrlTrafficShapeBurst, sleMlsQosInterfaceCtrlInputPolicyMap, 
				sleMlsQosInterfaceCtrlOutputPolicyMap, sleMlsQosInterfaceCtrlMapingCNGValue, sleMlsQosInterfaceCtrlMapingEgrClassVal, sleMlsQosInterfaceCtrlReplace, sleMlsQosIntfQueIntfIndex, 
				sleMlsQosIntfQueId, sleMlsQosIntfQueShapeQueueRate, sleMlsQosIntfQueWrrQueueWeight, sleMlsQosIntfQueWrrRandomDetectMinThr, sleMlsQosIntfQueWrrRandomDetectMaxThr, 
				sleMlsQosIntfQueWrrRandomDetectExpWt, sleMlsQosIntfQueTailDropThr, sleMlsQosIntfQueStrictQueue, sleMlsQosIntfQueRandomDetectDropStart, sleMlsQosIntfQueRandomDetectDropSlope, 
				sleMlsQosIntfQueRandomDetectColor, sleMlsQosIntfQueReservedBandwidth, sleMlsQosIntfQueControlRequest, sleMlsQosIntfQueControlStatus, sleMlsQosIntfQueControlTimer, 
				sleMlsQosIntfQueontrolTimeStamp, sleMlsQosIntfQueControlReqResult, sleMlsQosIntfQueCtrlInterfaceIndex, sleMlsQosIntfQueCtrlQueueId, sleMlsQosIntfQueCtrlProfileType, 
				sleMlsQosIntfQueCtrlShapeQueueRate, sleMlsQosIntfQueCtrlWrrQueueWeight, sleMlsQosIntfQueCtrlWrrRandomDetectMinThr, sleMlsQosIntfQueCtrlWrrRandomDetectMaxThr, sleMlsQosIntfQueCtrlWrrRandomDetectExpWt, 
				sleMlsQosIntfQueCtrlTailDropThr, sleMlsQosIntfQueCtrlRandomDetectDropStart, sleMlsQosIntfQueCtrlRandomDetectDropSlope, sleMlsQosIntfQueCtrlRandomDetectColor, sleMlsQosIntfQueCtrlReservedBandwidth, 
				sleMlsQosQstatsIfIndex, sleMlsQosQId, sleMlsQosWredGreenDropPkts, sleMlsQosWredYellowDropPkts, sleMlsQosWredRedDropPkts, 
				sleMlsQosTailDropPkts, sleMlsQosTailDropBytes, sleMlsQosQStatsOutPkts, sleMlsQosQStatsOutBytes, sleMlsQosQStatsMcastOutPkts, 
				sleMlsQosQStatsMcastOutBytes, sleMlsQosQStatsDropPkts, sleMlsQosQStatsDropBytes, sleMlsQosQStatsMcastDropPkts, sleMlsQosQStatsMcastDropBytes, 
				sleMlsQosQStatsEnqueuedPkts, sleMlsQosQStatsEnqueuedBytes, sleMlsQosQStatsControlRequest, sleMlsQosQStatsControlStatus, sleMlsQosQStatsControlTimer, 
				sleMlsQosQStatsontrolTimeStamp, sleMlsQosQStatsControlReqResult, sleMlsQosQstatsCtrlIfIndex, sleMlsQosQstatsCtrlQId, sleHqosClassMapQueueInfoName, 
				sleHqosClassMapQueueInfoMatchGroup, sleHqosClassMapQueueControlRequest, sleHqosClassMapQueueControlStatus, sleHqosClassMapQueueControlTimer, sleHqosClassMapQueueControlTimeStamp, 
				sleHqosClassMapQueueControlReqResult, sleHqosClassMapQueueControlName, sleHqosClassMapQueueControlMatchGroup, sleHqosPolicyMapQueueInfoName, sleHqosPolicyMapQueueInfoShape, 
				sleHqosPolicyMapQueueInfoBandwidth, sleHqosPolicyMapQueueInfoBandwidthRemainingPercent, sleHqosPolicyMapQueueInfoQueueLimit, sleHqosPolicyMapQueueInfoServicePolicyName, sleHqosPolicyMapQueueInfoPriority, 
				sleHqosPolicyMapQueueInfoRDMinThreshold, sleHqosPolicyMapQueueInfoRDMaxThreshold, sleHqosPolicyMapQueueControlRequest, sleHqosPolicyMapQueueControlStatus, sleHqosPolicyMapQueueControlTimer, 
				sleHqosPolicyMapQueueControlTimeStamp, sleHqosPolicyMapQueueControlReqResult, sleHqosPolicyMapQueueControlName, sleHqosPolicyMapQueueControlClassMapQueueName, sleHqosPolicyMapQueueControlParams, 
				sleHqosPolicyMapQueueControlParamsValue, sleHqosPolicyMapQueueControlUnits, sleHqosPolicyMapQueueControlRDMiniThreshold, sleHqosPolicyMapQueueControlRDMaxThreshold, sleHqosPolicyMapQueueControlRDMinimumUnits, 
				sleHqosPolicyMapQueueControlRDMaximumUnits, sleHqosPolicyMapQueueControlServicePolicyName, sleMlsQosAclGlobalInfoMaxAccessList, sleMlsQosAclGlobalControlRequest, sleMlsQosAclGlobalControlStatus, 
				sleMlsQosAclGlobalControlTimer, sleMlsQosAclGlobalControlTimeStamp, sleMlsQosAclGlobalControlReqResult, sleMlsQosAclGlobalControlMaxAccessList }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMlsQos 12 }

		
	
	END

--
-- sle-mlsqos-mib.mib
--
