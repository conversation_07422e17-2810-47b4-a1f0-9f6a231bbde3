--
-- SLE-DHCP-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 3.0 Build 285
-- Wednesday, February 14, 2007 at 11:27:52
--

	SLE-DHCP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			InterfaceIndex			
				FROM IF-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GRO<PERSON>, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Ip<PERSON><PERSON><PERSON>, Integer32, Unsigned32, Gauge32, 
			BITS, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-<PERSON><PERSON>			
			MacAddress			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.6296.101.6
		sleDhcp MODULE-IDENTITY 
			LAST-UPDATED "200412291441Z"		-- December 29, 2004 at 14:41 GMT
			ORGANIZATION 
				"HANASOFT"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains all needed informations about DHCP and
				all supported sle DHCP features."
			REVISION "200412101632Z"		-- December 10, 2004 at 16:32 GMT
			DESCRIPTION 
				"Description"
			::= { sleMgmt 6 }

		
	
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.6.1
		sleDhcpBase OBJECT IDENTIFIER::= { sleDhcp 1 }

		
		-- *******.4.1.6296.*********
		sleDhcpBaseInfo OBJECT IDENTIFIER::= { sleDhcpBase 1 }

		
		-- *******.4.1.6296.*********.1
		sleDhcpDefaultLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 120..2147483637)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The default time assigned to Lease in Dhcp server. 
				The unit is second.
				If 0, the default time was not set."
			::= { sleDhcpBaseInfo 1 }

		
		-- *******.4.1.6296.*********.2
		sleDhcpMaxLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 120..2147483637)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The max time assigned to Lease in Dhcp server. 
				The unit is second.
				If 0, the max time was not set."
			::= { sleDhcpBaseInfo 2 }

		
		-- *******.4.1.6296.*********.3
		sleDhcpDnsIp1 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The default Dns IP of the Dhcp daemon"
			::= { sleDhcpBaseInfo 3 }

		
		-- *******.4.1.6296.*********.4
		sleDhcpDnsIp2 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The default Dns IP of the Dhcp daemon"
			::= { sleDhcpBaseInfo 4 }

		
		-- *******.4.1.6296.*********.5
		sleDhcpDnsIp3 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The default Dns IP of the Dhcp daemon"
			::= { sleDhcpBaseInfo 5 }

		
		-- *******.4.1.6296.*********.6
		sleDhcpMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				noOperation(0),
				serverMode(1),
				relayMode(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Dhcp operation mode."
			::= { sleDhcpBaseInfo 6 }

		
		-- *******.4.1.6296.*********.7
		sleDhcpLeasedbBackupIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Ip address of LeaseDB backup server.
				If 0.0.0.0, it is not set."
			::= { sleDhcpBaseInfo 7 }

		
		-- *******.4.1.6296.*********.8
		sleDhcpLeasedbBackupInterval OBJECT-TYPE
			SYNTAX INTEGER (0..2147483637)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interval time assigned to LeaseDB backup.
				The unit is second.
				If 0, it is not set."
			::= { sleDhcpBaseInfo 8 }

		
		-- *******.4.1.6296.*********.9
		sleDhcpDatabaseKey OBJECT-TYPE
			SYNTAX INTEGER
				{
				clientid(0),
				hwaddress(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Database-key Setting for dhcp daemon.
				"
			::= { sleDhcpBaseInfo 9 }

		
		-- *******.4.1.6296.*********.10
		sleDhcpDebugStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugFilter(0),
				debugLease(1),
				debugPacket(2),
				debugServices(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The  debug option setting of dhcp daemon.
				debugNo(0)
				debugFilter(1)
				debugLease(2)
				debugPacket(4)
				debugService(8)
				The above value is denotes by 'or'.
				debugAll(16)
				
				"
			::= { sleDhcpBaseInfo 10 }

		
		-- *******.4.1.6296.*********.11
		sleDhcpOption82 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Option82 is set or not.
				0 : not set
				1 : set"
			::= { sleDhcpBaseInfo 11 }

		
		-- *******.4.1.6296.*********.12
		sleDhcpOption82Policy OBJECT-TYPE
			SYNTAX INTEGER
				{
				keep(0),
				replace(1),
				drop(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Option82 policy
				1 - keep
				2 - replace
				3 - drop"
			::= { sleDhcpBaseInfo 12 }

		
		-- *******.4.1.6296.*********.13
		sleDhcpOption82SystemRtype OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				ipaddress(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Value type of  sleDhcpOption82SystemRid
				invalid : The value of sleDhcpOption82SystemRid is invalid.
				ipaddress : denotes such as 'A.B.C.D'.
				binary : denotes such as '89:AB:CD:EF:01:02'.
				text : denotes such as 'TextString'.
				
				"
			::= { sleDhcpBaseInfo 13 }

		
		-- *******.4.1.6296.*********.14
		sleDhcpOption82SystemRid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..63))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This value is the system remote index 
				for option82 function."
			::= { sleDhcpBaseInfo 14 }

		
		-- *******.4.1.6296.*********.15
		sleDhcpAuthorizedArp OBJECT-TYPE
			SYNTAX INTEGER
				{
				noSet(0),
				defaultLeaseTime(1),
				halfLeaseTime(2),
				maxLeaseTime(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The set of starting time for secure ARP function.
				0:no set
				1:default-lease-time
				2:half-lease-time
				3:max-lease-time"
			::= { sleDhcpBaseInfo 15 }

		
		-- *******.4.1.6296.*********.16
		sleDhcpAuthArpStarted OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Time when authorized-arp function has started.
				It denotes such as '2004/12/20 10:20:30'."
			::= { sleDhcpBaseInfo 16 }

		
		-- *******.4.1.6296.*********.17
		sleDhcpAuthArpLeft OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The left time until authorized-arp function will be start.
				It denotes such as '01:20:31'"
			::= { sleDhcpBaseInfo 17 }

		
		-- *******.4.1.6296.*********.18
		sleDhcpStatisticReceived OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP packets."
			::= { sleDhcpBaseInfo 18 }

		
		-- *******.4.1.6296.*********.19
		sleDhcpStatisticSent OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of sent DHCP packets."
			::= { sleDhcpBaseInfo 19 }

		
		-- *******.4.1.6296.*********.20
		sleDhcpStatisticReceivedErrors OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP Error packets."
			::= { sleDhcpBaseInfo 20 }

		
		-- *******.4.1.6296.*********.21
		sleDhcpStatisticSentErrors OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of sent DHCP Error packets."
			::= { sleDhcpBaseInfo 21 }

		
		-- *******.4.1.6296.*********.22
		sleDhcpStatisticBootpReceivedRequests OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP Bootp Request packets."
			::= { sleDhcpBaseInfo 22 }

		
		-- *******.4.1.6296.*********.23
		sleDhcpStatisticBootpReceivedReplies OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP Bootp Reply packets."
			::= { sleDhcpBaseInfo 23 }

		
		-- *******.4.1.6296.*********.24
		sleDhcpStatisticBootpSentRequests OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of sent DHCP Bootp Request packets."
			::= { sleDhcpBaseInfo 24 }

		
		-- *******.4.1.6296.*********.25
		sleDhcpStatisticBootpSentReplies OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of sent DHCP Bootp Reply packets."
			::= { sleDhcpBaseInfo 25 }

		
		-- *******.4.1.6296.*********.26
		sleDhcpStatisticReceivedDiscover OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received Discover packets."
			::= { sleDhcpBaseInfo 26 }

		
		-- *******.4.1.6296.*********.27
		sleDhcpStatisticReceivedRequest OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP Request packets."
			::= { sleDhcpBaseInfo 27 }

		
		-- *******.4.1.6296.*********.28
		sleDhcpStatisticReceivedRelease OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP Release packets."
			::= { sleDhcpBaseInfo 28 }

		
		-- *******.4.1.6296.*********.29
		sleDhcpStatisticReceivedInform OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP Inform packets."
			::= { sleDhcpBaseInfo 29 }

		
		-- *******.4.1.6296.*********.30
		sleDhcpStatisticReceivedDecline OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of received DHCP Decline packets."
			::= { sleDhcpBaseInfo 30 }

		
		-- *******.4.1.6296.*********.31
		sleDhcpStatisticSentOffer OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of sent DHCP Offer packets."
			::= { sleDhcpBaseInfo 31 }

		
		-- *******.4.1.6296.*********.32
		sleDhcpStatisticSentAck OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of sent DHCP Ack packets."
			::= { sleDhcpBaseInfo 32 }

		
		-- *******.4.1.6296.*********.33
		sleDhcpStatisticSentNak OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of sent DHCP Nak packets."
			::= { sleDhcpBaseInfo 33 }

		
		-- *******.4.1.6296.*********.34
		sleDhcpSummaryPoolCnt OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Pools.to be set."
			::= { sleDhcpBaseInfo 34 }

		
		-- *******.4.1.6296.*********.35
		sleDhcpSummaryTotal OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Total leases to be set."
			::= { sleDhcpBaseInfo 35 }

		
		-- *******.4.1.6296.*********.36
		sleDhcpSummaryAvailable OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Available leases to be set."
			::= { sleDhcpBaseInfo 36 }

		
		-- *******.4.1.6296.*********.37
		sleDhcpSummaryAbandon OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Abandon leases to be set."
			::= { sleDhcpBaseInfo 37 }

		
		-- *******.4.1.6296.*********.38
		sleDhcpSummaryBound OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Bound leases to be set."
			::= { sleDhcpBaseInfo 38 }

		
		-- *******.4.1.6296.*********.39
		sleDhcpSummaryOffered OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Offered leases to be set."
			::= { sleDhcpBaseInfo 39 }

		
		-- *******.4.1.6296.*********.40
		sleDhcpSummaryFixed OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Fixed leases to be set."
			::= { sleDhcpBaseInfo 40 }

		
		-- *******.4.1.6296.*********.41
		sleDhcpClientHardwareAddress OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseInfo 41 }

		
		-- *******.4.1.6296.*********.42
		sleDhcpSimplifiedOption82 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseInfo 42 }

		
		-- *******.4.1.6296.*********.43
		sleDhcpSummaryAllocated OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseInfo 43 }

		
		-- *******.4.1.6296.*********
		sleDhcpBaseControl OBJECT IDENTIFIER::= { sleDhcpBase 2 }

		
		-- *******.4.1.6296.*********.1
		sleDhcpControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setDhcpLeaseTimeProfile(1),
				setDhcpDnsIpProfile(2),
				setDhcpServerMode(3),
				setDhcpLeasedbBackupProfile(4),
				setDhcpDatabaseKey(5),
				setDhcpDebugStatus(6),
				setDhcpOption82SystemProfile(7),
				setDhcpAuthorizedArp(8),
				clearDhcpStatistics(9),
				setDhcpClientHardwareAddress(10),
				setSimplifiedOption82(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleDhcpBaseControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleDhcpControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleDhcpBaseControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleDhcpControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleDhcpBaseControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleDhcpControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleDhcpBaseControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleDhcpControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleDhcpBaseControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleDhcpControlDefaultLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 120..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleDhcpControlMaxLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 120..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleDhcpControlDnsIp1 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleDhcpControlDnsIp2 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleDhcpControlDnsIp3 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 10 }

		
		-- *******.4.1.6296.*********.11
		sleDhcpControlServerMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				clear(0),
				set(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 11 }

		
		-- *******.4.1.6296.*********.12
		sleDhcpControlLeasedbBackupIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 12 }

		
		-- *******.4.1.6296.*********.13
		sleDhcpControlLeasedbBackupInterval OBJECT-TYPE
			SYNTAX INTEGER (0..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 13 }

		
		-- *******.4.1.6296.*********.14
		sleDhcpControlDatabaseKey OBJECT-TYPE
			SYNTAX INTEGER
				{
				clientid(0),
				hwaddress(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 14 }

		
		-- *******.4.1.6296.*********.15
		sleDhcpControlDebugStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugFilter(0),
				debugLease(1),
				debugPacket(2),
				debugServices(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The  debug option setting of dhcp daemon.
				The  debug option setting of dhcp daemon.
				debugNo(0)
				debugFilter(1)
				debugLease(2)
				debugPacket(4)
				debugService(8)
				The above value is denotes by 'or'.
				debugAll(16)
				
				"
			::= { sleDhcpBaseControl 15 }

		
		-- *******.4.1.6296.*********.16
		sleDhcpControlOption82 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 16 }

		
		-- *******.4.1.6296.*********.17
		sleDhcpControlOption82Policy OBJECT-TYPE
			SYNTAX INTEGER
				{
				keep(0),
				replace(1),
				drop(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 17 }

		
		-- *******.4.1.6296.*********.18
		sleDhcpControlOption82SystemRtype OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				ipaddress(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Value type of  sleDhcpOption82SystemRid
				invalid : it is cleared.
				ipaddress : denotes such as 'A.B.C.D'.
				binary : denotes such as '89:AB:CD:EF:01:02'.
				text : denotes such as 'TextString'.
				"
			::= { sleDhcpBaseControl 18 }

		
		-- *******.4.1.6296.*********.19
		sleDhcpControlOption82SystemRid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..63))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This value is the system remote index 
				for option82 function.
				If the size is 0, it is cleared."
			::= { sleDhcpBaseControl 19 }

		
		-- *******.4.1.6296.*********.20
		sleDhcpControlAuthorizedArp OBJECT-TYPE
			SYNTAX INTEGER
				{
				noSet(0),
				defaultLeaseTime(1),
				halfLeaseTime(2),
				maxLeaseTime(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 20 }

		
		-- *******.4.1.6296.*********.21
		sleDhcpControlClientHardwareAddress OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 21 }

		
		-- *******.4.1.6296.*********.22
		sleDhcpControlSimplifiedOption82 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcpBaseControl 22 }

		
		-- *******.4.1.6296.*********
		sleDhcpBaseNotification OBJECT IDENTIFIER::= { sleDhcpBase 3 }

		
		-- *******.4.1.6296.*********.1
		sleDhcpLeaseTimeProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpDefaultLeaseTime, sleDhcpMaxLeaseTime, sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleDhcpDnsIpProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpDnsIp1, sleDhcpDnsIp2, sleDhcpDnsIp3, sleDhcpControlRequest, sleDhcpControlTimeStamp, 
				sleDhcpControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleDhcpServerModeChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpMode, sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 3 }

		
		-- *******.4.1.6296.*********.4
		sleDhcpLeasedbBackupProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpLeasedbBackupIp, sleDhcpLeasedbBackupInterval, sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 4 }

		
		-- *******.4.1.6296.*********.5
		sleDhcpDatabaseKeyChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpDatabaseKey, sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 5 }

		
		-- *******.4.1.6296.*********.6
		sleDhcpDebugStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpDebugStatus, sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 6 }

		
		-- *******.4.1.6296.*********.7
		sleDhcpOption82SystemProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult, sleDhcpOption82, sleDhcpOption82Policy, 
				sleDhcpOption82SystemRtype, sleDhcpOption82SystemRid }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 7 }

		
		-- *******.4.1.6296.*********.8
		sleDhcpAuthorizedArpChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpAuthorizedArp, sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 8 }

		
		-- *******.4.1.6296.*********.9
		sleDhcpStatisticsCleared NOTIFICATION-TYPE
			OBJECTS { sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 9 }

		
		-- *******.4.1.6296.*********.10
		sleDhcpClientHardwareAddressChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult, sleDhcpControlClientHardwareAddress }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 10 }

		
		-- *******.4.1.6296.*********.11
		sleDhcpSimplifiedOption82Changed NOTIFICATION-TYPE
			OBJECTS { sleDhcpControlRequest, sleDhcpControlTimeStamp, sleDhcpControlReqResult, sleDhcpControlSimplifiedOption82 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcpBaseNotification 11 }

		
		-- *******.4.1.6296.101.6.2
		sleFilterPort OBJECT IDENTIFIER::= { sleDhcp 2 }

		
		-- *******.4.1.6296.*********
		sleFilterPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleFilterPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFilterPort 1 }

		
		-- *******.4.1.6296.*********.1
		sleFilterPortEntry OBJECT-TYPE
			SYNTAX SleFilterPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleFilterPortIndex }
			::= { sleFilterPortTable 1 }

		
		SleFilterPortEntry ::=
			SEQUENCE { 
				sleFilterPortIndex
					InterfaceIndex,
				sleFilterPortMode
					INTEGER
			 }

		-- *******.4.1.6296.*********.1.1
		sleFilterPortIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFilterPortEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleFilterPortMode OBJECT-TYPE
			SYNTAX INTEGER (0 | 1)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFilterPortEntry 2 }

		
		-- *******.4.1.6296.*********
		sleFilterPortControl OBJECT IDENTIFIER::= { sleFilterPort 2 }

		
		-- *******.4.1.6296.*********.1
		sleFilterPortControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setFilterPortMode(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleFilterPortControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleFilterPortControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleFilterPortControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleFilterPortControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleFilterPortControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleFilterPortControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleFilterPortControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleFilterPortControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleFilterPortControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleFilterPortControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFilterPortControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleFilterPortControlMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				clear(0),
				set(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFilterPortControl 7 }

		
		-- *******.4.1.6296.*********
		sleFilterPortNotification OBJECT IDENTIFIER::= { sleFilterPort 3 }

		
		-- *******.4.1.6296.*********.1
		sleFilterPortChanged NOTIFICATION-TYPE
			OBJECTS { sleFilterPortMode, sleFilterPortControlRequest, sleFilterPortControlTImeStamp, sleFilterPortControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleFilterPortNotification 1 }

		
		-- *******.4.1.6296.101.6.3
		sleFilterAddress OBJECT IDENTIFIER::= { sleDhcp 3 }

		
		-- *******.4.1.6296.*********
		sleFilterAddressTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleFilterAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFilterAddress 1 }

		
		-- *******.4.1.6296.*********.1
		sleFilterAddressEntry OBJECT-TYPE
			SYNTAX SleFilterAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleFilterAddressMac }
			::= { sleFilterAddressTable 1 }

		
		SleFilterAddressEntry ::=
			SEQUENCE { 
				sleFilterAddressMac
					MacAddress
			 }

		-- *******.4.1.6296.*********.1.1
		sleFilterAddressMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFilterAddressEntry 1 }

		
		-- *******.4.1.6296.*********
		sleFilterAddressControl OBJECT IDENTIFIER::= { sleFilterAddress 2 }

		
		-- *******.4.1.6296.*********.1
		sleFilterAddressControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createFilterAddress(1),
				destroyFilterAddress(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleFilterAddressControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleFilterAddressControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleFilterAddressControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleFilterAddressControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleFilterAddressControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleFilterAddressControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleFilterAddressControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleFilterAddressControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleFilterAddressControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleFilterAddressControlMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The MAC Address for the Deny Host."
			::= { sleFilterAddressControl 6 }

		
		-- *******.4.1.6296.*********
		sleFilterAddressNotification OBJECT IDENTIFIER::= { sleFilterAddress 3 }

		
		-- *******.4.1.6296.*********.1
		sleFilterAddressCreated NOTIFICATION-TYPE
			OBJECTS { sleFilterAddressMac, sleFilterAddressControlRequest, sleFilterAddressControlTImeStamp, sleFilterAddressControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleFilterAddressNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleFilterAddressDestroyed NOTIFICATION-TYPE
			OBJECTS { sleFilterAddressMac, sleFilterAddressControlRequest, sleFilterAddressControlTImeStamp, sleFilterAddressControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleFilterAddressNotification 2 }

		
		-- *******.4.1.6296.101.6.4
		sleRelayServer OBJECT IDENTIFIER::= { sleDhcp 4 }

		
		-- *******.4.1.6296.*********
		sleRelayServerTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRelayServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServer 1 }

		
		-- *******.4.1.6296.*********.1
		sleRelayServerEntry OBJECT-TYPE
			SYNTAX SleRelayServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRelayServerIndex }
			::= { sleRelayServerTable 1 }

		
		SleRelayServerEntry ::=
			SEQUENCE { 
				sleRelayServerIndex
					Integer32,
				sleRelayServerIp
					IpAddress,
				sleRelayServerVid
					INTEGER,
				sleRelayServerOUI
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleRelayServerIndex OBJECT-TYPE
			SYNTAX Integer32 (1..3)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServerEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleRelayServerIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The DHCP Server IP creating in the Relay Daemon."
			::= { sleRelayServerEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleRelayServerVid OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServerEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleRelayServerOUI OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..3))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServerEntry 4 }

		
		-- *******.4.1.6296.*********
		sleRelayServerControl OBJECT IDENTIFIER::= { sleRelayServer 2 }

		
		-- *******.4.1.6296.*********.1
		sleRelayServerControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRelayServer(1),
				destroyRelayServer(2),
				createRelayServerOUI(3),
				destroyRelayServerOUI(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command.
				1 - createRelayServer
				2 - destroyRelayServer"
			::= { sleRelayServerControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleRelayServerControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleRelayServerControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleRelayServerControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleRelayServerControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleRelayServerControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleRelayServerControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleRelayServerControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleRelayServerControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleRelayServerControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..3)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServerControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleRelayServerControlIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServerControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleRelayServerControlVid OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServerControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleRelayServerControlOUI OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..3))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRelayServerControl 9 }

		
		-- *******.4.1.6296.*********
		sleRelayServerNotification OBJECT IDENTIFIER::= { sleRelayServer 3 }

		
		-- *******.4.1.6296.*********.1
		sleRelayServerCreated NOTIFICATION-TYPE
			OBJECTS { sleRelayServerControlRequest, sleRelayServerControlTImeStamp, sleRelayServerControlReqResult, sleRelayServerIp, sleRelayServerVid
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRelayServerNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleRelayServerDestroyed NOTIFICATION-TYPE
			OBJECTS { sleRelayServerControlRequest, sleRelayServerControlTImeStamp, sleRelayServerControlReqResult, sleRelayServerIp, sleRelayServerVid
				 }
			STATUS current
			DESCRIPTION 
				"The sleRelayServerIp is 0.0.0.0 for CLI 
				'no ip dhcp mode relay all'"
			::= { sleRelayServerNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleRelayServerOUICreated NOTIFICATION-TYPE
			OBJECTS { sleRelayServerControlRequest, sleRelayServerControlTImeStamp, sleRelayServerControlReqResult, sleRelayServerIp, sleRelayServerVid, 
				sleRelayServerOUI }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRelayServerNotification 3 }

		
		-- *******.4.1.6296.*********.4
		sleRelayServerOUIDestroyed NOTIFICATION-TYPE
			OBJECTS { sleRelayServerControlRequest, sleRelayServerControlTImeStamp, sleRelayServerControlReqResult, sleRelayServerIp, sleRelayServerVid, 
				sleRelayServerOUI }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRelayServerNotification 4 }

		
		-- *******.4.1.6296.101.6.5
		slePool OBJECT IDENTIFIER::= { sleDhcp 5 }

		
		-- *******.4.1.6296.*********
		slePoolTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePoolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePool 1 }

		
		-- *******.4.1.6296.*********.1
		slePoolEntry OBJECT-TYPE
			SYNTAX SlePoolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePoolIndex }
			::= { slePoolTable 1 }

		
		SlePoolEntry ::=
			SEQUENCE { 
				slePoolIndex
					INTEGER,
				slePoolName
					OCTET STRING,
				slePoolDefaultLeaseTime
					INTEGER,
				slePoolMaxLeaseTime
					INTEGER,
				slePoolSummaryTotal
					Unsigned32,
				slePoolSummaryAllocated
					Unsigned32,
				slePoolSummaryBound
					Unsigned32,
				slePoolSummaryOffered
					Unsigned32,
				slePoolSummaryFixed
					Unsigned32,
				slePoolSummaryAbandon
					Unsigned32,
				slePoolSummaryAvailable
					Unsigned32,
				slePoolSummarySubnet
					Unsigned32,
				slePoolDomainName
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		slePoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Pool index"
			::= { slePoolEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		slePoolName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Pool name."
			::= { slePoolEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		slePoolDefaultLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (120..2147483637)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The default time assigned to Lease in Pool.
				The value of 0 denotes that the default time was not set."
			::= { slePoolEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		slePoolMaxLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (120..2147483637)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The max time assigned to Lease in Pool.
				The value of 0 denotes that the max time was not set"
			::= { slePoolEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		slePoolSummaryTotal OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Total Leases in Pool."
			::= { slePoolEntry 5 }

		
		-- *******.4.1.6296.*********.1.6
		slePoolSummaryAllocated OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Allocated Leases in Pool."
			::= { slePoolEntry 6 }

		
		-- *******.4.1.6296.*********.1.7
		slePoolSummaryBound OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Bound Leases in Pool."
			::= { slePoolEntry 7 }

		
		-- *******.4.1.6296.*********.1.8
		slePoolSummaryOffered OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Offered Leases in Pool."
			::= { slePoolEntry 8 }

		
		-- *******.4.1.6296.*********.1.9
		slePoolSummaryFixed OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Fixed Leases in Pool."
			::= { slePoolEntry 9 }

		
		-- *******.4.1.6296.*********.1.10
		slePoolSummaryAbandon OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Abandon Leases in Pool."
			::= { slePoolEntry 10 }

		
		-- *******.4.1.6296.*********.1.11
		slePoolSummaryAvailable OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Available Leases in Pool."
			::= { slePoolEntry 11 }

		
		-- *******.4.1.6296.*********.1.12
		slePoolSummarySubnet OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePoolEntry 12 }

		
		-- *******.4.1.6296.*********.1.13
		slePoolDomainName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0 | 1..64))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePoolEntry 13 }

		
		-- *******.4.1.6296.*********
		slePoolControl OBJECT IDENTIFIER::= { slePool 2 }

		
		-- *******.4.1.6296.*********.1
		slePoolControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createPool(1),
				destroyPool(2),
				setPoolLeaseTimeProfile(3),
				setPoolDomainName(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { slePoolControl 1 }

		
		-- *******.4.1.6296.*********.2
		slePoolControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { slePoolControl 2 }

		
		-- *******.4.1.6296.*********.3
		slePoolControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { slePoolControl 3 }

		
		-- *******.4.1.6296.*********.4
		slePoolControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { slePoolControl 4 }

		
		-- *******.4.1.6296.*********.5
		slePoolControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { slePoolControl 5 }

		
		-- *******.4.1.6296.*********.6
		slePoolControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePoolControl 6 }

		
		-- *******.4.1.6296.*********.7
		slePoolControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Pool name."
			::= { slePoolControl 7 }

		
		-- *******.4.1.6296.*********.8
		slePoolControlDefaultLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 120..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The default time assigned to Lease in Pool.
				If 0, the default time is clear"
			::= { slePoolControl 8 }

		
		-- *******.4.1.6296.*********.9
		slePoolControlMaxLeaseTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 120..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The max time assigned to Lease in Pool.
				If 0, the max time is clear."
			::= { slePoolControl 9 }

		
		-- *******.4.1.6296.*********.10
		slePoolControlDomainName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0 | 1..64))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePoolControl 10 }

		
		-- *******.4.1.6296.*********
		slePoolNotification OBJECT IDENTIFIER::= { slePool 3 }

		
		-- *******.4.1.6296.*********.1
		slePoolCreated NOTIFICATION-TYPE
			OBJECTS { slePoolName, slePoolDefaultLeaseTime, slePoolMaxLeaseTime, slePoolControlRequest, slePoolControlTImeStamp, 
				slePoolControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePoolNotification 1 }

		
		-- *******.4.1.6296.*********.2
		slePoolDestroyed NOTIFICATION-TYPE
			OBJECTS { slePoolIndex, slePoolControlRequest, slePoolControlTImeStamp, slePoolControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePoolNotification 2 }

		
		-- *******.4.1.6296.*********.3
		slePoolLeaseTimeProfileChanged NOTIFICATION-TYPE
			OBJECTS { slePoolDefaultLeaseTime, slePoolMaxLeaseTime, slePoolControlRequest, slePoolControlTImeStamp, slePoolControlReqResult
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePoolNotification 3 }

		
		-- *******.4.1.6296.*********.4
		slePoolDomainNameChanged NOTIFICATION-TYPE
			OBJECTS { slePoolControlRequest, slePoolControlTImeStamp, slePoolControlReqResult, slePoolDomainName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePoolNotification 4 }

		
		-- *******.4.1.6296.101.6.6
		sleDns OBJECT IDENTIFIER::= { sleDhcp 6 }

		
		-- *******.4.1.6296.*********
		sleDnsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDnsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDns 1 }

		
		-- *******.4.1.6296.*********.1
		sleDnsEntry OBJECT-TYPE
			SYNTAX SleDnsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePoolIndex, sleDnsIndex }
			::= { sleDnsTable 1 }

		
		SleDnsEntry ::=
			SEQUENCE { 
				sleDnsIndex
					Integer32,
				sleDnsIp
					IpAddress
			 }

		-- *******.4.1.6296.*********.1.1
		sleDnsIndex OBJECT-TYPE
			SYNTAX Integer32 (1..3)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDnsEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleDnsIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Ip address of Dns setting in Dhcp server daemon.
				"
			::= { sleDnsEntry 2 }

		
		-- *******.4.1.6296.*********
		sleDnsControl OBJECT IDENTIFIER::= { sleDns 2 }

		
		-- *******.4.1.6296.*********.1
		sleDnsControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDns(1),
				destroyDns(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleDnsControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleDnsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleDnsControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleDnsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleDnsControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleDnsControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleDnsControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleDnsControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleDnsControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleDnsControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDnsControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleDnsControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..3)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDnsControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleDnsControlIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDnsControl 8 }

		
		-- *******.4.1.6296.*********
		sleDnsNotification OBJECT IDENTIFIER::= { sleDns 3 }

		
		-- *******.4.1.6296.*********.1
		sleDnsCreated NOTIFICATION-TYPE
			OBJECTS { sleDnsIp, sleDnsControlRequest, sleDnsControlTImeStamp, sleDnsControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDnsNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleDnsDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDnsIndex, sleDnsControlRequest, sleDnsControlTImeStamp, sleDnsControlReqResult }
			STATUS current
			DESCRIPTION 
				"The sleDnsIp is 0.0.0.0 for CLI 
				'no ip dhcp default-config dns-server'"
			::= { sleDnsNotification 2 }

		
		-- *******.4.1.6296.101.6.7
		sleSubnet OBJECT IDENTIFIER::= { sleDhcp 7 }

		
		-- *******.4.1.6296.*********
		sleSubnetTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSubnetEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSubnet 1 }

		
		-- *******.4.1.6296.*********.1
		sleSubnetEntry OBJECT-TYPE
			SYNTAX SleSubnetEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePoolIndex, sleSubnetIp, sleSubnetMask }
			::= { sleSubnetTable 1 }

		
		SleSubnetEntry ::=
			SEQUENCE { 
				sleSubnetIp
					IpAddress,
				sleSubnetMask
					INTEGER,
				sleSubnetDefaultGateway
					IpAddress
			 }

		-- *******.4.1.6296.*********.1.1
		sleSubnetIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Subnet IP Address."
			::= { sleSubnetEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleSubnetMask OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Subnet Mask prefix length."
			::= { sleSubnetEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleSubnetDefaultGateway OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Gateway IP assigned to SubnetMask."
			::= { sleSubnetEntry 3 }

		
		-- *******.4.1.6296.*********
		sleSubnetControl OBJECT IDENTIFIER::= { sleSubnet 2 }

		
		-- *******.4.1.6296.*********.1
		sleSubnetControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSubnet(1),
				destroySubnet(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleSubnetControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleSubnetControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleSubnetControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleSubnetControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleSubnetControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleSubnetControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleSubnetControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleSubnetControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleSubnetControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleSubnetControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSubnetControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleSubnetControlIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSubnetControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleSubnetControlMask OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSubnetControl 8 }

		
		-- *******.4.1.6296.*********
		sleSubnetNotification OBJECT IDENTIFIER::= { sleSubnet 3 }

		
		-- *******.4.1.6296.*********.1
		sleSubnetCreated NOTIFICATION-TYPE
			OBJECTS { sleSubnetMask, sleSubnetControlRequest, sleSubnetControlTImeStamp, sleSubnetControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSubnetNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleSubnetDestroyed NOTIFICATION-TYPE
			OBJECTS { sleSubnetMask, sleSubnetControlRequest, sleSubnetControlTImeStamp, sleSubnetControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSubnetNotification 2 }

		
		-- *******.4.1.6296.101.6.8
		sleDefaultGateway OBJECT IDENTIFIER::= { sleDhcp 8 }

		
		-- *******.4.1.6296.*********
		sleDefaultGatewayTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDefaultGatewayEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDefaultGateway 1 }

		
		-- *******.4.1.6296.*********.1
		sleDefaultGatewayEntry OBJECT-TYPE
			SYNTAX SleDefaultGatewayEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePoolIndex, sleDefaultGatewayIndex }
			::= { sleDefaultGatewayTable 1 }

		
		SleDefaultGatewayEntry ::=
			SEQUENCE { 
				sleDefaultGatewayIndex
					Integer32,
				sleDefaultGatewayIp
					IpAddress
			 }

		-- *******.4.1.6296.*********.1.1
		sleDefaultGatewayIndex OBJECT-TYPE
			SYNTAX Integer32 (1..48)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDefaultGatewayEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleDefaultGatewayIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Gateway IP assigned to leases of the Subnet."
			::= { sleDefaultGatewayEntry 2 }

		
		-- *******.4.1.6296.*********
		sleDefaultGatewayControl OBJECT IDENTIFIER::= { sleDefaultGateway 2 }

		
		-- *******.4.1.6296.*********.1
		sleDefaultGatewayControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDefaultGateway(1),
				destroyDefaultGateway(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleDefaultGatewayControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleDefaultGatewayControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleDefaultGatewayControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleDefaultGatewayControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleDefaultGatewayControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleDefaultGatewayControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleDefaultGatewayControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleDefaultGatewayControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleDefaultGatewayControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleDefaultGatewayControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Pool name."
			::= { sleDefaultGatewayControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleDefaultGatewayControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..48)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDefaultGatewayControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleDefaultGatewayControlIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Gateway IP assigned to Subnet."
			::= { sleDefaultGatewayControl 8 }

		
		-- *******.4.1.6296.*********
		sleDefaultGatewayNotification OBJECT IDENTIFIER::= { sleDefaultGateway 3 }

		
		-- *******.4.1.6296.*********.1
		sleDefaultGatewayCreated NOTIFICATION-TYPE
			OBJECTS { sleDefaultGatewayIp, sleDefaultGatewayControlRequest, sleDefaultGatewayControlTImeStamp, sleDefaultGatewayControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDefaultGatewayNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleDefaultGatewayDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDefaultGatewayIndex, sleDefaultGatewayControlRequest, sleDefaultGatewayControlTImeStamp, sleDefaultGatewayControlReqResult }
			STATUS current
			DESCRIPTION 
				"The sleDefaultGatewayIp is 0.0.0.0 for CLI 
				'no default-gateway all'"
			::= { sleDefaultGatewayNotification 2 }

		
		-- *******.4.1.6296.101.6.9
		sleRange OBJECT IDENTIFIER::= { sleDhcp 9 }

		
		-- *******.4.1.6296.*********
		sleRangeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRange 1 }

		
		-- *******.4.1.6296.*********.1
		sleRangeEntry OBJECT-TYPE
			SYNTAX SleRangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePoolIndex, sleRangeStart, sleRangeEnd }
			::= { sleRangeTable 1 }

		
		SleRangeEntry ::=
			SEQUENCE { 
				sleRangeStart
					IpAddress,
				sleRangeEnd
					IpAddress,
				sleRangeSubnetIP
					IpAddress,
				sleRangeSubnetMask
					INTEGER
			 }

		-- *******.4.1.6296.*********.1.1
		sleRangeStart OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The start IP of subnet's range."
			::= { sleRangeEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleRangeEnd OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The end IP of subnet's range."
			::= { sleRangeEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleRangeSubnetIP OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The IP Address of the parent Subnet that the static Lease belongs to."
			::= { sleRangeEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleRangeSubnetMask OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Mask prefix length of the parent Subnet that the range belongs to."
			::= { sleRangeEntry 4 }

		
		-- *******.4.1.6296.*********
		sleRangeControl OBJECT IDENTIFIER::= { sleRange 2 }

		
		-- *******.4.1.6296.*********.1
		sleRangeControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRange(1),
				destroyRange(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleRangeControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleRangeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleRangeControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleRangeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleRangeControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleRangeControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleRangeControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleRangeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleRangeControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleRangeControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRangeControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleRangeControlStart OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRangeControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleRangeControlEnd OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRangeControl 8 }

		
		-- *******.4.1.6296.*********
		sleRangeNotification OBJECT IDENTIFIER::= { sleRange 3 }

		
		-- *******.4.1.6296.*********.1
		sleRangeCreated NOTIFICATION-TYPE
			OBJECTS { sleRangeSubnetIP, sleRangeSubnetMask, sleRangeControlRequest, sleRangeControlTImeStamp, sleRangeControlReqResult
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRangeNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleRangeDestroyed NOTIFICATION-TYPE
			OBJECTS { sleRangeEnd, sleRangeControlRequest, sleRangeControlTImeStamp, sleRangeControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRangeNotification 2 }

		
		-- *******.4.1.6296.101.6.10
		sleFixed OBJECT IDENTIFIER::= { sleDhcp 10 }

		
		-- *******.4.1.6296.**********
		sleFixedTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleFixedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFixed 1 }

		
		-- *******.4.1.6296.**********.1
		sleFixedEntry OBJECT-TYPE
			SYNTAX SleFixedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePoolIndex, sleFixedIp, sleFixedMac }
			::= { sleFixedTable 1 }

		
		SleFixedEntry ::=
			SEQUENCE { 
				sleFixedIp
					IpAddress,
				sleFixedMac
					MacAddress
			 }

		-- *******.4.1.6296.**********.1.1
		sleFixedIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The IP address for the fixed Lease."
			::= { sleFixedEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleFixedMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The MAC address for the fixed Lease."
			::= { sleFixedEntry 2 }

		
		-- *******.4.1.6296.**********
		sleFixedControl OBJECT IDENTIFIER::= { sleFixed 2 }

		
		-- *******.4.1.6296.**********.1
		sleFixedControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createFixed(1),
				destroyFixed(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleFixedControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleFixedControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleFixedControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleFixedControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleFixedControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleFixedControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleFixedControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleFixedControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleFixedControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleFixedControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFixedControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleFixedControlIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFixedControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleFixedControlMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleFixedControl 8 }

		
		-- *******.4.1.6296.**********
		sleFixedNotification OBJECT IDENTIFIER::= { sleFixed 3 }

		
		-- *******.4.1.6296.**********.1
		sleFixedCreated NOTIFICATION-TYPE
			OBJECTS { sleFixedMac, sleFixedControlRequest, sleFixedControlTImeStamp, sleFixedControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleFixedNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleFixedDestroyed NOTIFICATION-TYPE
			OBJECTS { sleFixedMac, sleFixedControlRequest, sleFixedControlTImeStamp, sleFixedControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleFixedNotification 2 }

		
		-- *******.4.1.6296.101.6.11
		sleOption82 OBJECT IDENTIFIER::= { sleDhcp 11 }

		
		-- *******.4.1.6296.**********
		sleOption82Table OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOption82Entry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOption82 1 }

		
		-- *******.4.1.6296.**********.1
		sleOption82Entry OBJECT-TYPE
			SYNTAX SleOption82Entry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOption82RemoteType, sleOption82RemoteId, sleOption82CircuitType, sleOption82CircuitId }
			::= { sleOption82Table 1 }

		
		SleOption82Entry ::=
			SEQUENCE { 
				sleOption82RemoteType
					INTEGER,
				sleOption82RemoteId
					OCTET STRING,
				sleOption82CircuitType
					INTEGER,
				sleOption82CircuitId
					OCTET STRING,
				sleOption82Limit
					INTEGER,
				sleOption82PoolName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleOption82RemoteType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipaddress(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Value type of sleOption82RemoteId
				ipaddress : denotes such as 'A.B.C.D'.
				binary : denotes such as '89:AB:CD:EF:01:02'.
				text : denotes such as 'TextString'."
			::= { sleOption82Entry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleOption82RemoteId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The remote id for the Option82 Lease limit."
			::= { sleOption82Entry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleOption82CircuitType OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				index(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Value type of sleOption82CircuitId
				invalid : The sleOption82CircuitId is invalid.
				index : This string denotes value in the range (0..65535).
				binary : denotes such as '89:AB:CD:EF:01:02'.
				text : denotes such as 'TextString'.
				"
			::= { sleOption82Entry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleOption82CircuitId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..48))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The circuit id for the Option82 Lease limit."
			::= { sleOption82Entry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleOption82Limit OBJECT-TYPE
			SYNTAX INTEGER (-1..2147483637)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Option82 Lease limit count for the remote system and the corresponding port.
				-1 : default value(un set value)
				0-2147483637 : set value"
			::= { sleOption82Entry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleOption82PoolName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Pool Name.
				NULL: unset value"
			::= { sleOption82Entry 6 }

		
		-- *******.4.1.6296.**********
		sleOption82Control OBJECT IDENTIFIER::= { sleOption82 2 }

		
		-- *******.4.1.6296.**********.1
		sleOption82ControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOption82(1),
				destroyOption82(2),
				setOption82Profile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleOption82Control 1 }

		
		-- *******.4.1.6296.**********.2
		sleOption82ControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleOption82Control 2 }

		
		-- *******.4.1.6296.**********.3
		sleOption82ControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleOption82Control 3 }

		
		-- *******.4.1.6296.**********.4
		sleOption82ControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleOption82Control 4 }

		
		-- *******.4.1.6296.**********.5
		sleOption82ControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleOption82Control 5 }

		
		-- *******.4.1.6296.**********.6
		sleOption82ControlRemoteType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipaddress(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Value Type of sleOption82ControlRemoteId
				ipaddress : denotes such as 'A.B.C.D'.
				binary : denotes such as '89:AB:CD:EF:01:02'.
				text : denotes such as 'TextString'."
			::= { sleOption82Control 6 }

		
		-- *******.4.1.6296.**********.7
		sleOption82ControlRemoteId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..48))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOption82Control 7 }

		
		-- *******.4.1.6296.**********.8
		sleOption82ControlCircuitType OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				index(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Value Type of sleOption82ControlCircuitId
				invalid : The sleOption82ControlCircuitId is invalid.
				index : This string denotes value in the range (0..65535).
				binary : it denotes such as '89:AB:CD:EF:01:02'.
				text : it denotes such as 'TextString'.
				"
			::= { sleOption82Control 8 }

		
		-- *******.4.1.6296.**********.9
		sleOption82ControlCircuitId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..48))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOption82Control 9 }

		
		-- *******.4.1.6296.**********.10
		sleOption82ControlLimit OBJECT-TYPE
			SYNTAX INTEGER (-2..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"-2 : invalid value.
				-1 : default value(clear value).
				0-2147483637 : set value"
			::= { sleOption82Control 10 }

		
		-- *******.4.1.6296.**********.11
		sleOption82ControlPoolName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Pool Name.
				NULL: default default value (clear)
				{0xff, 0x00}: default value (clear)
				{0xfe, 0x00}: not avaliable
				"
			::= { sleOption82Control 11 }

		
		-- *******.4.1.6296.**********
		sleOption82Notification OBJECT IDENTIFIER::= { sleOption82 3 }

		
		-- *******.4.1.6296.**********.1
		sleOption82Created NOTIFICATION-TYPE
			OBJECTS { sleOption82ControlRequest, sleOption82ControlTImeStamp, sleOption82ControlReqResult, sleOption82ControlLimit, sleOption82ControlPoolName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOption82Notification 1 }

		
		-- *******.4.1.6296.**********.2
		sleOption82Destroyed NOTIFICATION-TYPE
			OBJECTS { sleOption82ControlRequest, sleOption82ControlTImeStamp, sleOption82ControlReqResult, sleOption82ControlCircuitId }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOption82Notification 2 }

		
		-- *******.4.1.6296.**********.3
		sleOption82ProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleOption82ControlRequest, sleOption82ControlTImeStamp, sleOption82ControlReqResult, sleOption82ControlLimit, sleOption82ControlPoolName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOption82Notification 3 }

		
		-- *******.4.1.6296.101.6.12
		sleOption82System OBJECT IDENTIFIER::= { sleDhcp 12 }

		
		-- *******.4.1.6296.**********
		sleOption82SystemTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOption82SystemEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOption82System 1 }

		
		-- *******.4.1.6296.**********.1
		sleOption82SystemEntry OBJECT-TYPE
			SYNTAX SleOption82SystemEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOption82SystemIndex }
			::= { sleOption82SystemTable 1 }

		
		SleOption82SystemEntry ::=
			SEQUENCE { 
				sleOption82SystemIndex
					InterfaceIndex,
				sleOption82SystemCtype
					INTEGER,
				sleOption82SystemCid
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleOption82SystemIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PortIndex"
			::= { sleOption82SystemEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleOption82SystemCtype OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				index(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Value type of sleOption82SystemCid.
				invalid : The sleOption82SystemCid is invalid.
				index : This string denotes value in the range (0..65535).
				binary : it denotes such as '89:AB:CD:EF:01:02'.
				text : it denotes such as 'TextString'.
				"
			::= { sleOption82SystemEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleOption82SystemCid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..63))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The circuit id for the Option82 Lease limit.
				If Null, it is not set."
			::= { sleOption82SystemEntry 3 }

		
		-- *******.4.1.6296.**********
		sleOption82SystemControl OBJECT IDENTIFIER::= { sleOption82System 2 }

		
		-- *******.4.1.6296.**********.1
		sleOption82SystemControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setOption82SystemCircuit(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleOption82SystemControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleOption82SystemControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleOption82SystemControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleOption82SystemControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleOption82SystemControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleOption82SystemControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleOption82SystemControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleOption82SystemControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleOption82SystemControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleOption82SystemControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"InterfaceIndex"
			::= { sleOption82SystemControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleOption82SystemControlCtype OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				index(1),
				binary(2),
				text(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Value type of sleOption82SystemControlCid.
				invalid : The sleOption82SystemControlCid is invalid.
				          In this case, it is cleared.
				index : This string denotes value in the range (0..65535).
				binary : it denotes such as '89:AB:CD:EF:01:02'.
				text : it denotes such as 'TextString'.
				"
			::= { sleOption82SystemControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleOption82SystemControlCid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..63))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The circuit id of the port for the Option82.
				"
			::= { sleOption82SystemControl 8 }

		
		-- *******.4.1.6296.**********
		sleOption82CircuitNotification OBJECT IDENTIFIER::= { sleOption82System 3 }

		
		-- *******.4.1.6296.**********.1
		setOption82SystemCircuit NOTIFICATION-TYPE
			OBJECTS { sleOption82SystemControlRequest, sleOption82SystemControlTImeStamp, sleOption82SystemControlReqResult, sleOption82SystemControlCtype, sleOption82SystemControlCid
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOption82CircuitNotification 1 }

		
		-- *******.4.1.6296.101.6.13
		sleIllegal OBJECT IDENTIFIER::= { sleDhcp 13 }

		
		-- *******.4.1.6296.**********
		sleIllegalTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleIllegalEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleIllegal 1 }

		
		-- *******.4.1.6296.**********.1
		sleIllegalEntry OBJECT-TYPE
			SYNTAX SleIllegalEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleIllegalIp, sleIllegalMac }
			::= { sleIllegalTable 1 }

		
		SleIllegalEntry ::=
			SEQUENCE { 
				sleIllegalIp
					IpAddress,
				sleIllegalMac
					MacAddress,
				sleIllegalTime
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleIllegalIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The illegal IP address."
			::= { sleIllegalEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleIllegalMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The MAC Address of this illegal address."
			::= { sleIllegalEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleIllegalTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Time that the illegal Ip was found.
				It denotes such as '2004/12/20 10:20:30'"
			::= { sleIllegalEntry 3 }

		
		-- *******.4.1.6296.**********
		sleIllegalControl OBJECT IDENTIFIER::= { sleIllegal 2 }

		
		-- *******.4.1.6296.**********.1
		sleIllegalControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearIllegal(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleIllegalControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleIllegalControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleIllegalControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleIllegalControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleIllegalControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleIllegalControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleIllegalControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleIllegalControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleIllegalControl 5 }

		
		-- *******.4.1.6296.**********
		sleIllegalNotification OBJECT IDENTIFIER::= { sleIllegal 3 }

		
		-- *******.4.1.6296.**********.1
		sleIllegalCleared NOTIFICATION-TYPE
			OBJECTS { sleIllegalControlRequest, sleIllegalControlTImeStamp, sleIllegalControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleIllegalNotification 1 }

		
		-- *******.4.1.6296.101.6.14
		sleLeased OBJECT IDENTIFIER::= { sleDhcp 14 }

		
		-- *******.4.1.6296.**********
		sleLeasedTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleLeasedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleLeased 1 }

		
		-- *******.4.1.6296.**********.1
		sleLeasedEntry OBJECT-TYPE
			SYNTAX SleLeasedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleLeasedIp }
			::= { sleLeasedTable 1 }

		
		SleLeasedEntry ::=
			SEQUENCE { 
				sleLeasedIp
					IpAddress,
				sleLeasedPoolIndex
					INTEGER,
				sleLeasedPoolName
					OCTET STRING,
				sleLeasedTime
					OCTET STRING,
				sleLeasedHwaddr
					OCTET STRING,
				sleLeasedHostname
					OCTET STRING,
				sleLeasedUid
					OCTET STRING,
				sleLeasedRid
					OCTET STRING,
				sleLeasedCid
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleLeasedIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The leased IP address."
			::= { sleLeasedEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleLeasedPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Pool Index."
			::= { sleLeasedEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleLeasedPoolName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Pool name."
			::= { sleLeasedEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleLeasedTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Lease's TimeStamp.
				It denotes such as '2004/12/20 10:20:30 - 2004/12/22 10:24:32'."
			::= { sleLeasedEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleLeasedHwaddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Hardware Address of the allocated Lease."
			::= { sleLeasedEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleLeasedHostname OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Host name of the allocated Lease."
			::= { sleLeasedEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleLeasedUid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Client-ID of the allocated Lease."
			::= { sleLeasedEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleLeasedRid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Option82 Remote-ID of the allocated Lease."
			::= { sleLeasedEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleLeasedCid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Option82 Circuit-ID of the allocated Lease."
			::= { sleLeasedEntry 9 }

		
		-- *******.4.1.6296.**********
		sleLeasedControl OBJECT IDENTIFIER::= { sleLeased 2 }

		
		-- *******.4.1.6296.**********.1
		sleLeasedControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				flushLeasedIpMask(1),
				flushLeasedPool(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleLeasedControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleLeasedControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleLeasedControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleLeasedControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleLeasedControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleLeasedControlTImeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleLeasedControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleLeasedControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleLeasedControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleLeasedControlIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The lease IP address."
			::= { sleLeasedControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleLeasedControlMask OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The lease IP Mask"
			::= { sleLeasedControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleLeasedControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"sleLeasedPoolIndex"
			::= { sleLeasedControl 8 }

		
		-- *******.4.1.6296.**********
		sleLeasedNotification OBJECT IDENTIFIER::= { sleLeased 3 }

		
		-- *******.4.1.6296.**********.1
		sleLeasedIpMaskFlushed NOTIFICATION-TYPE
			OBJECTS { sleLeasedControlRequest, sleLeasedControlTImeStamp, sleLeasedControlReqResult, sleLeasedControlIp, sleLeasedControlMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleLeasedNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleLeasedPoolFlushed NOTIFICATION-TYPE
			OBJECTS { sleLeasedControlRequest, sleLeasedControlTImeStamp, sleLeasedControlReqResult, sleLeasedControlPoolIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleLeasedNotification 2 }

		
		-- *******.4.1.6296.101.6.15
		sleDhcpGroup OBJECT-GROUP
			OBJECTS { sleDhcpDefaultLeaseTime, sleDhcpMaxLeaseTime, sleDhcpDnsIp1, sleDhcpDnsIp2, sleDhcpDnsIp3, 
				sleDhcpMode, sleDhcpLeasedbBackupIp, sleDhcpLeasedbBackupInterval, sleDhcpDatabaseKey, sleDhcpDebugStatus, 
				sleDhcpOption82, sleDhcpOption82Policy, sleDhcpOption82SystemRid, sleDhcpAuthorizedArp, sleDhcpAuthArpStarted, 
				sleDhcpAuthArpLeft, sleDhcpStatisticReceived, sleDhcpStatisticSent, sleDhcpStatisticReceivedErrors, sleDhcpStatisticSentErrors, 
				sleDhcpStatisticBootpReceivedRequests, sleDhcpStatisticBootpReceivedReplies, sleDhcpStatisticBootpSentRequests, sleDhcpStatisticBootpSentReplies, sleDhcpStatisticReceivedDiscover, 
				sleDhcpStatisticReceivedRequest, sleDhcpStatisticReceivedRelease, sleDhcpStatisticReceivedInform, sleDhcpStatisticReceivedDecline, sleDhcpStatisticSentOffer, 
				sleDhcpStatisticSentAck, sleDhcpStatisticSentNak, sleDhcpSummaryPoolCnt, sleDhcpSummaryTotal, sleDhcpSummaryAvailable, 
				sleDhcpSummaryAbandon, sleDhcpSummaryBound, sleDhcpSummaryOffered, sleDhcpSummaryFixed, sleDhcpControlRequest, 
				sleDhcpControlStatus, sleDhcpControlTimer, sleDhcpControlTimeStamp, sleDhcpControlReqResult, sleDhcpControlDefaultLeaseTime, 
				sleDhcpControlMaxLeaseTime, sleDhcpControlDnsIp1, sleDhcpControlDnsIp2, sleDhcpControlDnsIp3, sleDhcpControlServerMode, 
				sleDhcpControlLeasedbBackupIp, sleDhcpControlLeasedbBackupInterval, sleDhcpControlDatabaseKey, sleDhcpControlDebugStatus, sleDhcpControlOption82, 
				sleDhcpControlOption82Policy, sleDhcpControlOption82SystemRid, sleDhcpControlAuthorizedArp, sleFilterPortIndex, sleFilterPortMode, 
				sleFilterPortControlRequest, sleFilterPortControlStatus, sleFilterPortControlTimer, sleFilterPortControlTImeStamp, sleFilterPortControlReqResult, 
				sleFilterPortControlIndex, sleFilterPortControlMode, sleFilterAddressMac, sleFilterAddressControlRequest, sleFilterAddressControlStatus, 
				sleFilterAddressControlTimer, sleFilterAddressControlTImeStamp, sleFilterAddressControlReqResult, sleFilterAddressControlMac, sleRelayServerIp, 
				sleRelayServerControlRequest, sleRelayServerControlStatus, sleRelayServerControlTimer, sleRelayServerControlTImeStamp, sleRelayServerControlReqResult, 
				sleRelayServerControlIp, slePoolIndex, slePoolName, slePoolDefaultLeaseTime, slePoolMaxLeaseTime, 
				slePoolSummaryTotal, slePoolSummaryAllocated, slePoolSummaryBound, slePoolSummaryOffered, slePoolSummaryFixed, 
				slePoolSummaryAbandon, slePoolSummaryAvailable, slePoolControlRequest, slePoolControlStatus, slePoolControlTimer, 
				slePoolControlTImeStamp, slePoolControlReqResult, slePoolControlIndex, slePoolControlName, slePoolControlDefaultLeaseTime, 
				slePoolControlMaxLeaseTime, sleDnsIp, sleDnsControlRequest, sleDnsControlStatus, sleDnsControlTimer, 
				sleDnsControlTImeStamp, sleDnsControlReqResult, sleDnsControlPoolIndex, sleDnsControlIp, sleSubnetIp, 
				sleSubnetMask, sleSubnetDefaultGateway, sleSubnetControlRequest, sleSubnetControlStatus, sleSubnetControlTimer, 
				sleSubnetControlTImeStamp, sleSubnetControlReqResult, sleSubnetControlPoolIndex, sleSubnetControlIp, sleSubnetControlMask, 
				sleDefaultGatewayIp, sleDefaultGatewayControlRequest, sleDefaultGatewayControlStatus, sleDefaultGatewayControlTimer, sleDefaultGatewayControlTImeStamp, 
				sleDefaultGatewayControlReqResult, sleDefaultGatewayControlPoolIndex, sleDefaultGatewayControlIp, sleRangeStart, sleRangeEnd, 
				sleRangeSubnetIP, sleRangeSubnetMask, sleRangeControlRequest, sleRangeControlStatus, sleRangeControlTimer, 
				sleRangeControlTImeStamp, sleRangeControlReqResult, sleRangeControlPoolIndex, sleRangeControlStart, sleRangeControlEnd, 
				sleFixedIp, sleFixedMac, sleFixedControlRequest, sleFixedControlStatus, sleFixedControlTimer, 
				sleFixedControlTImeStamp, sleFixedControlReqResult, sleFixedControlPoolIndex, sleFixedControlIp, sleFixedControlMac, 
				sleOption82RemoteType, sleIllegalIp, sleIllegalControlRequest, sleIllegalControlStatus, sleIllegalControlTimer, 
				sleIllegalControlTImeStamp, sleIllegalControlReqResult, sleLeasedControlRequest, sleLeasedControlStatus, sleLeasedControlTimer, 
				sleLeasedControlTImeStamp, sleLeasedControlReqResult, sleLeasedControlIp, sleLeasedControlMask, sleRelayServerIndex, 
				sleRelayServerControlIndex, sleDnsIndex, sleDnsControlIndex, sleDefaultGatewayIndex, sleDefaultGatewayControlIndex, 
				sleOption82CircuitType, sleDhcpOption82SystemRtype, sleDhcpControlOption82SystemRtype, sleOption82RemoteId, sleOption82CircuitId, 
				sleOption82Limit, sleOption82PoolName, sleOption82ControlRequest, sleOption82ControlStatus, sleOption82ControlTimer, 
				sleOption82ControlTImeStamp, sleOption82ControlReqResult, sleOption82ControlRemoteType, sleOption82ControlRemoteId, sleOption82ControlCircuitType, 
				sleOption82ControlCircuitId, sleOption82ControlLimit, sleOption82ControlPoolName, sleOption82SystemIndex, sleOption82SystemCtype, 
				sleOption82SystemCid, sleOption82SystemControlRequest, sleOption82SystemControlStatus, sleOption82SystemControlTimer, sleOption82SystemControlTImeStamp, 
				sleOption82SystemControlReqResult, sleOption82SystemControlIndex, sleOption82SystemControlCtype, sleOption82SystemControlCid, sleLeasedHostname, 
				sleLeasedUid, sleLeasedRid, sleLeasedCid, sleDhcpClientHardwareAddress, sleDhcpControlClientHardwareAddress, 
				sleDhcpSimplifiedOption82, sleDhcpControlSimplifiedOption82, sleRelayServerVid, sleRelayServerOUI, sleRelayServerControlVid, 
				sleRelayServerControlOUI, sleDhcpSummaryAllocated, slePoolSummarySubnet, slePoolDomainName, slePoolControlDomainName, 
				sleLeasedControlPoolIndex, sleIllegalMac, sleIllegalTime, sleLeasedIp, sleLeasedPoolIndex, 
				sleLeasedPoolName, sleLeasedTime, sleLeasedHwaddr }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp 15 }

		
		-- *******.4.1.6296.101.6.16
		sleDhcpNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleFilterPortChanged, sleFilterAddressCreated, sleFilterAddressDestroyed, sleRelayServerCreated, sleRelayServerDestroyed, 
				slePoolCreated, slePoolDestroyed, slePoolLeaseTimeProfileChanged, sleDnsCreated, sleDnsDestroyed, 
				sleSubnetCreated, sleSubnetDestroyed, sleDefaultGatewayCreated, sleDefaultGatewayDestroyed, sleRangeCreated, 
				sleRangeDestroyed, sleFixedCreated, sleFixedDestroyed, sleIllegalCleared, sleLeasedIpMaskFlushed, 
				sleLeasedPoolFlushed, sleDhcpLeaseTimeProfileChanged, sleDhcpDnsIpProfileChanged, sleDhcpServerModeChanged, sleDhcpLeasedbBackupProfileChanged, 
				sleDhcpDatabaseKeyChanged, sleOption82Created, sleOption82Destroyed, sleOption82ProfileChanged, setOption82SystemCircuit, 
				sleDhcpDebugStatusChanged, sleDhcpAuthorizedArpChanged, sleDhcpClientHardwareAddressChanged, sleDhcpSimplifiedOption82Changed, sleRelayServerOUICreated, 
				sleRelayServerOUIDestroyed, slePoolDomainNameChanged, sleDhcpStatisticsCleared, sleDhcpOption82SystemProfileChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp 16 }

		
	
	END

--
-- SLE-DHCP-MIB.my
--
