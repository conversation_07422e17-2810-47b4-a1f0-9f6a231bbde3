--
-- sle-ripng-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Monday, March 23, 2015 at 15:00:56
--

	SLE-RIPng-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			InetAddress			
				FROM INET-ADDRESS-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-G<PERSON><PERSON>, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.55
		sleRIPng MODULE-IDENTITY 
			LAST-UPDATED "200412291441Z"		-- December 29, 2004 at 14:41 GMT
			ORGANIZATION 
				"HANASOFT"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains information about RIPng version 2."
			REVISION "201003211954Z"		-- March 21, 2010 at 19:54 GMT
			DESCRIPTION 
				"OSFPv2"
			::= { sleMgmt 55 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.55.1
		sleRIPngBase OBJECT IDENTIFIER ::= { sleRIPng 1 }

		
		-- *******.4.1.6296.**********
		sleRIPngBaseInfo OBJECT IDENTIFIER ::= { sleRIPngBase 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngDefaultMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1 }
			::= { sleRIPngBaseInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngDefaultInformationOrg OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleRIPngBaseInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngDefaultDistance OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngBaseInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngRecvBufferSize OBJECT-TYPE
			SYNTAX INTEGER (8192..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 215040 }
			::= { sleRIPngBaseInfo 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngMaximumPaths OBJECT-TYPE
			SYNTAX INTEGER (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1 }
			::= { sleRIPngBaseInfo 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngMaximumPrefixRoute OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1 }
			::= { sleRIPngBaseInfo 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngMaximumPrefixRoutePercent OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 75 }
			::= { sleRIPngBaseInfo 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngMetricSumApply OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleRIPngBaseInfo 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPngBasicUpdateTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 30 }
			::= { sleRIPngBaseInfo 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPngBasicTimeoutTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 180 }
			::= { sleRIPngBaseInfo 10 }

		
		-- *******.4.1.6296.**********.11
		sleRIPngBasicGarbageTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 120 }
			::= { sleRIPngBaseInfo 11 }

		
		-- *******.4.1.6296.**********
		sleRIPngBaseControl OBJECT IDENTIFIER ::= { sleRIPngBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngMode(1),
				deleteRIPngMode(2),
				setRIPngDefaultMetric(3),
				setRIPngDefaultInformationOrg(4),
				setRIPngDefaultDistance(5),
				setRIPngRecvBufferSize(6),
				setRIPngMaximumPaths(7),
				setRIPngMaximumPrefixRouteProfile(8),
				setRIPngMetricSumApply(9),
				setRIPngBasicTimersProfile(10),
				clearRIPngAll(11),
				clearRIPngRoute(12),
				clearRIPngProtoType(13)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngBaseControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.2"
			::= { sleRIPngBaseControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.3"
			::= { sleRIPngBaseControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.4"
			::= { sleRIPngBaseControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.5"
			::= { sleRIPngBaseControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngControlDefaultMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.7"
			DEFVAL { 1 }
			::= { sleRIPngBaseControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngControlDefaultInformationOrg OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.8"
			DEFVAL { 0 }
			::= { sleRIPngBaseControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngControlDefaultDistance OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngBaseControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPngControlRecvBufferSize OBJECT-TYPE
			SYNTAX INTEGER (8192..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.9"
			DEFVAL { 215040 }
			::= { sleRIPngBaseControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPngControlMaximumPaths OBJECT-TYPE
			SYNTAX INTEGER (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.10"
			DEFVAL { 1 }
			::= { sleRIPngBaseControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleRIPngControlMaximumPrefixRoute OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.11"
			DEFVAL { 1 }
			::= { sleRIPngBaseControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleRIPngControlMaximumPrefixRoutePercent OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.12"
			DEFVAL { 75 }
			::= { sleRIPngBaseControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleRIPngControlMetricSumApply OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.13"
			DEFVAL { 0 }
			::= { sleRIPngBaseControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleRIPngControlBasicUpdateTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.14"
			DEFVAL { 30 }
			::= { sleRIPngBaseControl 14 }

		
		-- *******.4.1.6296.**********.15
		sleRIPngControlBasicTimeoutTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.15"
			DEFVAL { 180 }
			::= { sleRIPngBaseControl 15 }

		
		-- *******.4.1.6296.**********.16
		sleRIPngControlBasicGarbageTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.16"
			DEFVAL { 180 }
			::= { sleRIPngBaseControl 16 }

		
		-- *******.4.1.6296.**********.17
		sleRIPngControlClearRoutePrefix OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.13"
			::= { sleRIPngBaseControl 17 }

		
		-- *******.4.1.6296.**********.18
		sleRIPngControlClearRouteMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.13"
			::= { sleRIPngBaseControl 18 }

		
		-- *******.4.1.6296.**********.19
		sleRIPngControlClearProtoTpye OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				bgp(4),
				ospf(5),
				rip(6)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.13"
			DEFVAL { 0 }
			::= { sleRIPngBaseControl 19 }

		
		-- *******.4.1.6296.**********
		sleRIPngBaseNotification OBJECT IDENTIFIER ::= { sleRIPngBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngModeCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngModeDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngDefaultMetricChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngDefaultMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngDefaultInformationOrgChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngDefaultInformationOrg }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngDefaultDistanceChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngDefaultDistance }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngRecvBufferSizeChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngRecvBufferSize }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngMaximumPathsChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngMaximumPaths }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngMaximumPrefixProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngMaximumPrefixRoute, sleRIPngMaximumPrefixRoutePercent
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPngMetricSumApplyChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngMetricSumApply }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPngTimersChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngBasicUpdateTimer, sleRIPngBasicTimeoutTimer, 
				sleRIPngBasicGarbageTimer }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 10 }

		
		-- *******.4.1.6296.**********.11
		sleRIPngAllCleared NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 11 }

		
		-- *******.4.1.6296.**********.12
		sleRIPngRouteCleared NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngControlClearRoutePrefix, sleRIPngControlClearRouteMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 12 }

		
		-- *******.4.1.6296.**********.13
		sleRIPngProtoTypeCleared NOTIFICATION-TYPE
			OBJECTS { sleRIPngControlRequest, sleRIPngControlTimeStamp, sleRIPngControlReqResult, sleRIPngControlClearProtoTpye }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngBaseNotification 13 }

		
		-- *******.4.1.6296.101.55.2
		sleRIPngAggregate OBJECT IDENTIFIER ::= { sleRIPng 2 }

		
		-- *******.4.1.6296.**********
		sleRIPngAggregateTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPngAggregateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregate 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngAggregateEntry OBJECT-TYPE
			SYNTAX SleRIPngAggregateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngAggregateAddr, sleRIPngAggregateMask }
			::= { sleRIPngAggregateTable 1 }

		
		SleRIPngAggregateEntry ::=
			SEQUENCE { 
				sleRIPngAggregateAddr
					InetAddress,
				sleRIPngAggregateMask
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngAggregateAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngAggregateMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateEntry 2 }

		
		-- *******.4.1.6296.**********
		sleRIPngAggregateControl OBJECT IDENTIFIER ::= { sleRIPngAggregate 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngAggregateControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngAggregateAddr(1),
				deleteRIPngAggregateAddr(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngAggregateControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngAggregateControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngAggregateControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngAggregateControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngAggregateControlAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngAggregateControlMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAggregateControl 7 }

		
		-- *******.4.1.6296.**********
		sleRIPngAggregateNotification OBJECT IDENTIFIER ::= { sleRIPngAggregate 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngAggregateAddrCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngAggregateControlRequest, sleRIPngAggregateControlTimeStamp, sleRIPngAggregateControlReqResult, sleRIPngAggregateAddr, sleRIPngAggregateMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngAggregateNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngAggregateAddrDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngAggregateControlRequest, sleRIPngAggregateControlTimeStamp, sleRIPngAggregateControlReqResult, sleRIPngAggregateControlAddr, sleRIPngAggregateControlMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngAggregateNotification 2 }

		
		-- *******.4.1.6296.101.55.3
		sleRIPngNeighbor OBJECT IDENTIFIER ::= { sleRIPng 3 }

		
		-- *******.4.1.6296.**********
		sleRIPngNeighborTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPNeighborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighbor 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngNeighborEntry OBJECT-TYPE
			SYNTAX SleRIPNeighborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngNeighborAddr }
			::= { sleRIPngNeighborTable 1 }

		
		SleRIPNeighborEntry ::=
			SEQUENCE { 
				sleRIPngNeighborAddr
					InetAddress,
				sleRIPngNeighborIfName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngNeighborAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngNeighborIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborEntry 2 }

		
		-- *******.4.1.6296.**********
		sleRIPngNeighborControl OBJECT IDENTIFIER ::= { sleRIPngNeighbor 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngNeighborControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngNeighbor(1),
				deleteRIPngNeighbor(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngNeighborControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngNeighborControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngNeighborControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngNeighborControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngNeighborControlAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngNeighborControlIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngNeighborControl 7 }

		
		-- *******.4.1.6296.**********
		sleRIPngNeighborNotification OBJECT IDENTIFIER ::= { sleRIPngNeighbor 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngNeighborCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngNeighborControlRequest, sleRIPngNeighborControlTimeStamp, sleRIPngNeighborControlReqResult, sleRIPngNeighborAddr, sleRIPngNeighborIfName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngNeighborNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngNeighborDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngNeighborControlRequest, sleRIPngNeighborControlTimeStamp, sleRIPngNeighborControlReqResult, sleRIPngNeighborControlAddr, sleRIPngNeighborControlIfName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngNeighborNotification 2 }

		
		-- *******.4.1.6296.101.55.4
		sleRIPngStaticRoute OBJECT IDENTIFIER ::= { sleRIPng 4 }

		
		-- *******.4.1.6296.**********
		sleRIPngStaticRouteTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPStaticRouteEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRoute 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngStaticRouteEntry OBJECT-TYPE
			SYNTAX SleRIPStaticRouteEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngStaticRouteAddr, sleRIPngStaticRouteMask }
			::= { sleRIPngStaticRouteTable 1 }

		
		SleRIPStaticRouteEntry ::=
			SEQUENCE { 
				sleRIPngStaticRouteAddr
					InetAddress,
				sleRIPngStaticRouteMask
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngStaticRouteAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngStaticRouteMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteEntry 2 }

		
		-- *******.4.1.6296.**********
		sleRIPngStaticRouteControl OBJECT IDENTIFIER ::= { sleRIPngStaticRoute 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngStaticRouteControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngStaticRoute(1),
				deleteRIPngStaticRoute(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngStaticRouteControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngStaticRouteControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngStaticRouteControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngStaticRouteControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngStaticRouteControlAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngStaticRouteControlMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngStaticRouteControl 7 }

		
		-- *******.4.1.6296.**********
		sleRIPngStaticRouteNotification OBJECT IDENTIFIER ::= { sleRIPngStaticRoute 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngStaticRouteCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngStaticRouteControlRequest, sleRIPngStaticRouteControlTimeStamp, sleRIPngStaticRouteControlReqResult, sleRIPngStaticRouteAddr, sleRIPngStaticRouteMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngStaticRouteNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngStaticRouteDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngStaticRouteControlRequest, sleRIPngStaticRouteControlTimeStamp, sleRIPngStaticRouteControlReqResult, sleRIPngStaticRouteControlAddr, sleRIPngStaticRouteControlMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngStaticRouteNotification 2 }

		
		-- *******.4.1.6296.101.55.5
		sleRIPngAdminDistance OBJECT IDENTIFIER ::= { sleRIPng 5 }

		
		-- *******.4.1.6296.**********
		sleRIPngAdminDistanceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPAdminDistanceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistance 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngAdminDistanceEntry OBJECT-TYPE
			SYNTAX SleRIPAdminDistanceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngAdminDistanceValue, sleRIPngAdminDistanceAddr, sleRIPngAdminDistanceMask }
			::= { sleRIPngAdminDistanceTable 1 }

		
		SleRIPAdminDistanceEntry ::=
			SEQUENCE { 
				sleRIPngAdminDistanceValue
					INTEGER,
				sleRIPngAdminDistanceAddr
					InetAddress,
				sleRIPngAdminDistanceMask
					INTEGER,
				sleRIPngAdminDistanceAccessName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngAdminDistanceValue OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngAdminDistanceAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPngAdminDistanceMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleRIPngAdminDistanceAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceEntry 4 }

		
		-- *******.4.1.6296.**********
		sleRIPngAdminDistanceControl OBJECT IDENTIFIER ::= { sleRIPngAdminDistance 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngAdminDistanceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngAdminDistance(1),
				deleteRIPngAdminDistance(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngAdminDistanceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngAdminDistanceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngAdminDistanceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngAdminDistanceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngAdminDistanceControlValue OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngAdminDistanceControlAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngAdminDistanceControlMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPngAdminDistanceControlAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngAdminDistanceControl 9 }

		
		-- *******.4.1.6296.**********
		sleRIPngAdminDistanceNotification OBJECT IDENTIFIER ::= { sleRIPngAdminDistance 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngAdminDistanceCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngAdminDistanceControlRequest, sleRIPngAdminDistanceControlTimeStamp, sleRIPngAdminDistanceControlReqResult, sleRIPngAdminDistanceValue, sleRIPngAdminDistanceAddr, 
				sleRIPngAdminDistanceMask, sleRIPngAdminDistanceAccessName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngAdminDistanceNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngAdminDistanceDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngAdminDistanceControlRequest, sleRIPngAdminDistanceControlTimeStamp, sleRIPngAdminDistanceControlReqResult, sleRIPngAdminDistanceControlValue, sleRIPngAdminDistanceControlAddr, 
				sleRIPngAdminDistanceControlMask, sleRIPngAdminDistanceControlAccessName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngAdminDistanceNotification 2 }

		
		-- *******.4.1.6296.101.55.6
		sleRIPngDistribute OBJECT IDENTIFIER ::= { sleRIPng 6 }

		
		-- *******.4.1.6296.**********
		sleRIPngDistributeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPngDistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistribute 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngDistributeEntry OBJECT-TYPE
			SYNTAX SleRIPngDistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngDistributeIfName }
			::= { sleRIPngDistributeTable 1 }

		
		SleRIPngDistributeEntry ::=
			SEQUENCE { 
				sleRIPngDistributeIfName
					OCTET STRING,
				sleRIPngDistributeInAccessName
					OCTET STRING,
				sleRIPngDistributeOutAccessName
					OCTET STRING,
				sleRIPngDistributeInPrefixName
					OCTET STRING,
				sleRIPngDistributeOutPrefixName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngDistributeIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngDistributeInAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPngDistributeOutAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleRIPngDistributeInPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleRIPngDistributeOutPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeEntry 5 }

		
		-- *******.4.1.6296.**********
		sleRIPngDistributeControl OBJECT IDENTIFIER ::= { sleRIPngDistribute 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngDistributeControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngDistributeInAccess(1),
				deleteRIPngDistributeInAccess(2),
				createRIPngDistributeOutAccess(3),
				deleteRIPngDistributeOutAccess(4),
				createRIPngDistributeInPrefix(5),
				deleteRIPngDistributeInPrefix(6),
				createRIPngDistributeOutPrefix(7),
				deleteRIPngDistributeOutPrefix(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngDistributeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngDistributeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngDistributeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngDistributeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngDistributeControlIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngDistributeControlInAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngDistributeControlOutAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPngDistributeControlInPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPngDistributeControlOutPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngDistributeControl 10 }

		
		-- *******.4.1.6296.**********
		sleRIPngDistributeNotification OBJECT IDENTIFIER ::= { sleRIPngDistribute 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngDistributeInAccessCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeIfName, sleRIPngDistributeInAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngDistributeInAccessDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeControlIfName, sleRIPngDistributeControlInAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngDistributeOutAccessCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeIfName, sleRIPngDistributeOutAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngDistributeOutAccessDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeControlIfName, sleRIPngDistributeControlOutAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngDistributeInPrefixCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeIfName, sleRIPngDistributeInPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngDistributeInPrefixDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeControlIfName, sleRIPngDistributeControlInPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngDistributeOutPrefixCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeIfName, sleRIPngDistributeOutPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngDistributeOutPrefixDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngDistributeControlRequest, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, sleRIPngDistributeControlIfName, sleRIPngDistributeControlOutPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngDistributeNotification 8 }

		
		-- *******.4.1.6296.101.55.7
		sleRIPngOffsetList OBJECT IDENTIFIER ::= { sleRIPng 7 }

		
		-- *******.4.1.6296.**********
		sleRIPngOffsetListTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPOffsetListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetList 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngOffsetListEntry OBJECT-TYPE
			SYNTAX SleRIPOffsetListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngOffsetListIfname }
			::= { sleRIPngOffsetListTable 1 }

		
		SleRIPOffsetListEntry ::=
			SEQUENCE { 
				sleRIPngOffsetListIfname
					OCTET STRING,
				sleRIPngOffsetListInAccName
					OCTET STRING,
				sleRIPngOffsetListInMetric
					INTEGER,
				sleRIPngOffsetListOutAccName
					OCTET STRING,
				sleRIPngOffsetListOutMetric
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngOffsetListIfname OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngOffsetListInAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPngOffsetListInMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleRIPngOffsetListOutAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleRIPngOffsetListOutMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListEntry 5 }

		
		-- *******.4.1.6296.**********
		sleRIPngOffsetListControl OBJECT IDENTIFIER ::= { sleRIPngOffsetList 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngOffsetListControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngOffsetListIn(1),
				deleteRIPngOffsetListIn(2),
				createRIPngOffsetListOut(3),
				deleteRIPngOffsetListOut(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngOffsetListControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngOffsetListControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngOffsetListControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngOffsetListControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngOffsetListControlIfname OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngOffsetListControlInAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngOffsetListControlInMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPngOffsetListControlOutAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPngOffsetListControlOutMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngOffsetListControl 10 }

		
		-- *******.4.1.6296.**********
		sleRIPngOffsetListNotification OBJECT IDENTIFIER ::= { sleRIPngOffsetList 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngOffsetListInCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngOffsetListControlRequest, sleRIPngOffsetListControlTimeStamp, sleRIPngOffsetListControlReqResult, sleRIPngOffsetListIfname, sleRIPngOffsetListInAccName, 
				sleRIPngOffsetListInMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngOffsetListNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngOffsetListInDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngOffsetListControlRequest, sleRIPngOffsetListControlTimeStamp, sleRIPngOffsetListControlReqResult, sleRIPngOffsetListControlIfname, sleRIPngOffsetListControlInAccName, 
				sleRIPngOffsetListControlInMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngOffsetListNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngOffsetListOutCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngOffsetListControlRequest, sleRIPngOffsetListControlTimeStamp, sleRIPngOffsetListControlReqResult, sleRIPngOffsetListIfname, sleRIPngOffsetListOutAccName, 
				sleRIPngOffsetListOutMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngOffsetListNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngOffsetListOutDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngOffsetListControlRequest, sleRIPngOffsetListControlTimeStamp, sleRIPngOffsetListControlReqResult, sleRIPngOffsetListControlIfname, sleRIPngOffsetListControlOutAccName, 
				sleRIPngOffsetListControlOutMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngOffsetListNotification 4 }

		
		-- *******.4.1.6296.101.55.8
		sleRIPngRedistribute OBJECT IDENTIFIER ::= { sleRIPng 8 }

		
		-- *******.4.1.6296.**********
		sleRIPngRedistributeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPRedistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistribute 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngRedistributeEntry OBJECT-TYPE
			SYNTAX SleRIPRedistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngRedistType }
			::= { sleRIPngRedistributeTable 1 }

		
		SleRIPRedistributeEntry ::=
			SEQUENCE { 
				sleRIPngRedistType
					INTEGER,
				sleRIPngRedistMetric
					INTEGER,
				sleRIPngRedistRouteMapName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngRedistType OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				bgp(4),
				ospf(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngRedistMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16777214)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPngRedistRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeEntry 3 }

		
		-- *******.4.1.6296.**********
		sleRIPngRedistributeControl OBJECT IDENTIFIER ::= { sleRIPngRedistribute 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngRedistControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngRedistribute(1),
				deleteRIPngRedistribute(2),
				setRIPngRedistribute(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngRedistControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngRedistControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngRedistControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngRedistControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngRedistControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				bgp(4),
				ospf(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngRedistControlMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16777214)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngRedistControlRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRedistributeControl 8 }

		
		-- *******.4.1.6296.**********
		sleRIPngRedistributeNotification OBJECT IDENTIFIER ::= { sleRIPngRedistribute 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngRedistributeCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngRedistControlRequest, sleRIPngRedistControlTimeStamp, sleRIPngRedistControlReqResult, sleRIPngRedistType, sleRIPngRedistMetric, 
				sleRIPngRedistRouteMapName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngRedistributeNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngRedistributeDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngRedistControlRequest, sleRIPngRedistControlTimeStamp, sleRIPngRedistControlReqResult, sleRIPngRedistControlType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngRedistributeNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngRedistributeChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngRedistControlRequest, sleRIPngRedistControlTimeStamp, sleRIPngRedistControlReqResult, sleRIPngRedistType, sleRIPngRedistMetric, 
				sleRIPngRedistRouteMapName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngRedistributeNotification 3 }

		
		-- *******.4.1.6296.101.55.9
		sleRIPngRoutemap OBJECT IDENTIFIER ::= { sleRIPng 9 }

		
		-- *******.4.1.6296.**********
		sleRIPngRoutemapTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPngRoutemapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemap 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngRoutemapEntry OBJECT-TYPE
			SYNTAX SleRIPngRoutemapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngRoutemapName, sleRIPngRoutemapType, sleRIPngRoutemapIfname }
			::= { sleRIPngRoutemapTable 1 }

		
		SleRIPngRoutemapEntry ::=
			SEQUENCE { 
				sleRIPngRoutemapName
					OCTET STRING,
				sleRIPngRoutemapType
					INTEGER,
				sleRIPngRoutemapIfname
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPngRoutemapName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPngRoutemapType OBJECT-TYPE
			SYNTAX INTEGER
				{
				in(0),
				out(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPngRoutemapIfname OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapEntry 3 }

		
		-- *******.4.1.6296.**********
		sleRIPngRoutemapControl OBJECT IDENTIFIER ::= { sleRIPngRoutemap 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngRoutemapControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngRoutemap(1),
				deleteRIPngRoutemap(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngRoutemapControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPngRoutemapControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPngRoutemapControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPngRoutemapControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPngRoutemapControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPngRoutemapControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				in(0),
				out(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPngRoutemapControlIfname OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutemapControl 8 }

		
		-- *******.4.1.6296.**********
		sleRIPngRoutemapNotification OBJECT IDENTIFIER ::= { sleRIPngRoutemap 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPngRoutemapCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngRoutemapControlRequest, sleRIPngRoutemapControlTimeStamp, sleRIPngRoutemapControlReqResult, sleRIPngRoutemapName, sleRIPngRoutemapType, 
				sleRIPngRoutemapIfname }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngRoutemapNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPngRoutemapDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngRoutemapControlRequest, sleRIPngRoutemapControlTimeStamp, sleRIPngRoutemapControlReqResult, sleRIPngRoutemapControlName, sleRIPngRoutemapControlType, 
				sleRIPngRoutemapControlIfname }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngRoutemapNotification 2 }

		
		-- *******.4.1.6296.101.55.10
		sleRIPngPassInterface OBJECT IDENTIFIER ::= { sleRIPng 10 }

		
		-- *******.4.1.6296.***********
		sleRIPngPassInterfaceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPPassInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterface 1 }

		
		-- *******.4.1.6296.***********.1
		sleRIPngPassInterfaceEntry OBJECT-TYPE
			SYNTAX SleRIPPassInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngPassInterfaceName }
			::= { sleRIPngPassInterfaceTable 1 }

		
		SleRIPPassInterfaceEntry ::=
			SEQUENCE { 
				sleRIPngPassInterfaceName
					OCTET STRING
			 }

		-- *******.4.1.6296.***********.1.1
		sleRIPngPassInterfaceName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterfaceEntry 1 }

		
		-- *******.4.1.6296.***********
		sleRIPngPassInterfaceControl OBJECT IDENTIFIER ::= { sleRIPngPassInterface 2 }

		
		-- *******.4.1.6296.***********.1
		sleRIPngPassInterfaceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngPassInterface(1),
				deleteRIPngPassInterface(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterfaceControl 1 }

		
		-- *******.4.1.6296.***********.2
		sleRIPngPassInterfaceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterfaceControl 2 }

		
		-- *******.4.1.6296.***********.3
		sleRIPngPassInterfaceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterfaceControl 3 }

		
		-- *******.4.1.6296.***********.4
		sleRIPngPassInterfaceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterfaceControl 4 }

		
		-- *******.4.1.6296.***********.5
		sleRIPngPassInterfaceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterfaceControl 5 }

		
		-- *******.4.1.6296.***********.6
		sleRIPngPassInterfaceControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngPassInterfaceControl 6 }

		
		-- *******.4.1.6296.***********
		sleRIPngPassInterfaceNotification OBJECT IDENTIFIER ::= { sleRIPngPassInterface 3 }

		
		-- *******.4.1.6296.***********.1
		sleRIPngPassInterfaceCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngPassInterfaceControlRequest, sleRIPngPassInterfaceControlTimeStamp, sleRIPngPassInterfaceControlReqResult, sleRIPngPassInterfaceName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngPassInterfaceNotification 1 }

		
		-- *******.4.1.6296.***********.2
		sleRIPngPassInterfaceDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngPassInterfaceControlRequest, sleRIPngPassInterfaceControlTimeStamp, sleRIPngPassInterfaceControlReqResult, sleRIPngPassInterfaceControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngPassInterfaceNotification 2 }

		
		-- *******.4.1.6296.101.55.11
		sleRIPngInterface OBJECT IDENTIFIER ::= { sleRIPng 11 }

		
		-- *******.4.1.6296.***********
		sleRIPngInterfaceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterface 1 }

		
		-- *******.4.1.6296.***********.1
		sleRIPngInterfaceEntry OBJECT-TYPE
			SYNTAX SleRIPInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngInterfaceIndex }
			::= { sleRIPngInterfaceTable 1 }

		
		SleRIPInterfaceEntry ::=
			SEQUENCE { 
				sleRIPngInterfaceIndex
					INTEGER,
				sleRIPngInterfaceRouterMode
					INTEGER,
				sleRIPngInterfaceRecvPacket
					INTEGER,
				sleRIPngInterfaceSendPacket
					INTEGER,
				sleRIPngInterfaceSplitHorizonMode
					INTEGER
			 }

		-- *******.4.1.6296.***********.1.1
		sleRIPngInterfaceIndex OBJECT-TYPE
			SYNTAX INTEGER (0..4097)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleRIPngInterfaceRouterMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleRIPngInterfaceRecvPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceEntry 3 }

		
		-- *******.4.1.6296.***********.1.4
		sleRIPngInterfaceSendPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceEntry 4 }

		
		-- *******.4.1.6296.***********.1.5
		sleRIPngInterfaceSplitHorizonMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				withoutPoisonedReverse(1),
				withPoisonedReverse(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceEntry 5 }

		
		-- *******.4.1.6296.***********
		sleRIPngInterfaceControl OBJECT IDENTIFIER ::= { sleRIPngInterface 2 }

		
		-- *******.4.1.6296.***********.1
		sleRIPngInterfaceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPngInterfaceRouterMode(1),
				deleteRIPngInterfaceRouterMode(2),
				setRIPngInterfaceRecvPacketEnable(3),
				setRIPngInterfaceSendPacketEnable(4),
				setRIPngInterfaceSplitHorizonMode(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 1 }

		
		-- *******.4.1.6296.***********.2
		sleRIPngInterfaceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 2 }

		
		-- *******.4.1.6296.***********.3
		sleRIPngInterfaceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 3 }

		
		-- *******.4.1.6296.***********.4
		sleRIPngInterfaceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 4 }

		
		-- *******.4.1.6296.***********.5
		sleRIPngInterfaceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 5 }

		
		-- *******.4.1.6296.***********.6
		sleRIPngInterfaceControlIndex OBJECT-TYPE
			SYNTAX INTEGER (0..4097)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 6 }

		
		-- *******.4.1.6296.***********.7
		sleRIPngInterfaceControlRouterMode OBJECT-TYPE
			SYNTAX SleControlRequestResultType
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 7 }

		
		-- *******.4.1.6296.***********.8
		sleRIPngInterfaceControlRecvPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 8 }

		
		-- *******.4.1.6296.***********.9
		sleRIPngInterfaceControlSendPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 9 }

		
		-- *******.4.1.6296.***********.10
		sleRIPngInterfaceControlSplitHorizonMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				withoutPoisonedReverse(1),
				withPoisonedReverse(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngInterfaceControl 10 }

		
		-- *******.4.1.6296.***********
		sleRIPngInterfaceNotification OBJECT IDENTIFIER ::= { sleRIPngInterface 3 }

		
		-- *******.4.1.6296.***********.1
		sleRIPngInterfaceRouterModeCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPngInterfaceControlRequest, sleRIPngInterfaceControlTimeStamp, sleRIPngInterfaceControlReqResult, sleRIPngInterfaceIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngInterfaceNotification 1 }

		
		-- *******.4.1.6296.***********.2
		sleRIPngInterfaceRouterModeDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPngInterfaceControlRequest, sleRIPngInterfaceControlTimeStamp, sleRIPngInterfaceControlReqResult, sleRIPngInterfaceIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngInterfaceNotification 2 }

		
		-- *******.4.1.6296.***********.3
		sleRIPngInterfaceRecvPacketChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngInterfaceControlRequest, sleRIPngInterfaceControlTimeStamp, sleRIPngInterfaceControlReqResult, sleRIPngInterfaceIndex, sleRIPngInterfaceRecvPacket
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngInterfaceNotification 3 }

		
		-- *******.4.1.6296.***********.4
		sleRIPngInterfaceSendPacketChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngInterfaceControlRequest, sleRIPngInterfaceControlTimeStamp, sleRIPngInterfaceControlReqResult, sleRIPngInterfaceIndex, sleRIPngInterfaceSendPacket
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngInterfaceNotification 4 }

		
		-- *******.4.1.6296.***********.5
		sleRIPngInterfaceSplitHorizonModeChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPngInterfaceControlRequest, sleRIPngInterfaceControlTimeStamp, sleRIPngInterfaceControlReqResult, sleRIPngInterfaceIndex, sleRIPngInterfaceSplitHorizonMode
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPngInterfaceNotification 5 }

		
		-- *******.4.1.6296.101.55.12
		sleRIPngRoutes OBJECT IDENTIFIER ::= { sleRIPng 12 }

		
		-- *******.4.1.6296.***********
		sleRIPngRoutesTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPRoutesEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutes 1 }

		
		-- *******.4.1.6296.***********.1
		sleRIPngRoutesEntry OBJECT-TYPE
			SYNTAX SleRIPRoutesEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPngRoutesType, sleRIPngRoutesPrefix, sleRIPngRoutesMask, sleRIPngRoutesNextHop }
			::= { sleRIPngRoutesTable 1 }

		
		SleRIPRoutesEntry ::=
			SEQUENCE { 
				sleRIPngRoutesType
					INTEGER,
				sleRIPngRoutesPrefix
					IpAddress,
				sleRIPngRoutesMask
					INTEGER,
				sleRIPngRoutesNextHop
					IpAddress,
				sleRIPngRoutesSelected
					INTEGER,
				sleRIPngRoutesIfName
					OCTET STRING,
				sleRIPngRoutesMetric
					INTEGER,
				sleRIPngRoutesTag
					INTEGER,
				sleRIPngRoutesUpTime
					TimeTicks
			 }

		-- *******.4.1.6296.***********.1.1
		sleRIPngRoutesType OBJECT-TYPE
			SYNTAX INTEGER
				{
				rip(1),
				kernel(2),
				connected(3),
				static(4),
				ospf(5),
				isis(6),
				bgp(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleRIPngRoutesPrefix OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleRIPngRoutesMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 3 }

		
		-- *******.4.1.6296.***********.1.4
		sleRIPngRoutesNextHop OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 4 }

		
		-- *******.4.1.6296.***********.1.5
		sleRIPngRoutesSelected OBJECT-TYPE
			SYNTAX INTEGER
				{
				deselected(0),
				selected(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 5 }

		
		-- *******.4.1.6296.***********.1.6
		sleRIPngRoutesIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 6 }

		
		-- *******.4.1.6296.***********.1.7
		sleRIPngRoutesMetric OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 7 }

		
		-- *******.4.1.6296.***********.1.8
		sleRIPngRoutesTag OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 8 }

		
		-- *******.4.1.6296.***********.1.9
		sleRIPngRoutesUpTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPngRoutesEntry 9 }

		
		-- *******.4.1.6296.101.55.13
		sleRIPngGroup OBJECT-GROUP
			OBJECTS { sleRIPngDefaultMetric, sleRIPngDefaultInformationOrg, sleRIPngDefaultDistance, sleRIPngRecvBufferSize, sleRIPngMaximumPaths, 
				sleRIPngMaximumPrefixRoute, sleRIPngMaximumPrefixRoutePercent, sleRIPngMetricSumApply, sleRIPngBasicUpdateTimer, sleRIPngBasicTimeoutTimer, 
				sleRIPngBasicGarbageTimer, sleRIPngControlRequest, sleRIPngControlStatus, sleRIPngControlTimer, sleRIPngControlTimeStamp, 
				sleRIPngControlReqResult, sleRIPngControlDefaultMetric, sleRIPngControlDefaultInformationOrg, sleRIPngControlDefaultDistance, sleRIPngControlRecvBufferSize, 
				sleRIPngControlMaximumPaths, sleRIPngControlMaximumPrefixRoute, sleRIPngControlMaximumPrefixRoutePercent, sleRIPngControlMetricSumApply, sleRIPngControlBasicUpdateTimer, 
				sleRIPngControlBasicTimeoutTimer, sleRIPngControlBasicGarbageTimer, sleRIPngControlClearRoutePrefix, sleRIPngControlClearRouteMask, sleRIPngControlClearProtoTpye, 
				sleRIPngAggregateAddr, sleRIPngAggregateMask, sleRIPngAggregateControlRequest, sleRIPngAggregateControlStatus, sleRIPngAggregateControlTimer, 
				sleRIPngAggregateControlTimeStamp, sleRIPngAggregateControlReqResult, sleRIPngAggregateControlAddr, sleRIPngAggregateControlMask, sleRIPngNeighborAddr, 
				sleRIPngNeighborIfName, sleRIPngNeighborControlRequest, sleRIPngNeighborControlStatus, sleRIPngNeighborControlTimer, sleRIPngNeighborControlTimeStamp, 
				sleRIPngNeighborControlReqResult, sleRIPngNeighborControlAddr, sleRIPngNeighborControlIfName, sleRIPngStaticRouteAddr, sleRIPngStaticRouteMask, 
				sleRIPngStaticRouteControlRequest, sleRIPngStaticRouteControlStatus, sleRIPngStaticRouteControlTimer, sleRIPngStaticRouteControlTimeStamp, sleRIPngStaticRouteControlReqResult, 
				sleRIPngStaticRouteControlAddr, sleRIPngStaticRouteControlMask, sleRIPngAdminDistanceValue, sleRIPngAdminDistanceAddr, sleRIPngAdminDistanceMask, 
				sleRIPngAdminDistanceAccessName, sleRIPngAdminDistanceControlRequest, sleRIPngAdminDistanceControlStatus, sleRIPngAdminDistanceControlTimer, sleRIPngAdminDistanceControlTimeStamp, 
				sleRIPngAdminDistanceControlReqResult, sleRIPngAdminDistanceControlValue, sleRIPngAdminDistanceControlAddr, sleRIPngAdminDistanceControlMask, sleRIPngAdminDistanceControlAccessName, 
				sleRIPngDistributeIfName, sleRIPngDistributeInAccessName, sleRIPngDistributeOutAccessName, sleRIPngDistributeInPrefixName, sleRIPngDistributeOutPrefixName, 
				sleRIPngDistributeControlRequest, sleRIPngDistributeControlStatus, sleRIPngDistributeControlTimer, sleRIPngDistributeControlTimeStamp, sleRIPngDistributeControlReqResult, 
				sleRIPngDistributeControlIfName, sleRIPngDistributeControlInAccessName, sleRIPngDistributeControlOutAccessName, sleRIPngDistributeControlInPrefixName, sleRIPngDistributeControlOutPrefixName, 
				sleRIPngOffsetListIfname, sleRIPngOffsetListInAccName, sleRIPngOffsetListInMetric, sleRIPngOffsetListOutAccName, sleRIPngOffsetListOutMetric, 
				sleRIPngOffsetListControlRequest, sleRIPngOffsetListControlStatus, sleRIPngOffsetListControlTimer, sleRIPngOffsetListControlTimeStamp, sleRIPngOffsetListControlReqResult, 
				sleRIPngOffsetListControlIfname, sleRIPngOffsetListControlInAccName, sleRIPngOffsetListControlInMetric, sleRIPngOffsetListControlOutAccName, sleRIPngOffsetListControlOutMetric, 
				sleRIPngRedistType, sleRIPngRedistMetric, sleRIPngRedistRouteMapName, sleRIPngRedistControlRequest, sleRIPngRedistControlStatus, 
				sleRIPngRedistControlTimer, sleRIPngRedistControlTimeStamp, sleRIPngRedistControlReqResult, sleRIPngRedistControlType, sleRIPngRedistControlMetric, 
				sleRIPngRedistControlRouteMapName, sleRIPngRoutemapName, sleRIPngRoutemapType, sleRIPngRoutemapIfname, sleRIPngRoutemapControlRequest, 
				sleRIPngRoutemapControlStatus, sleRIPngRoutemapControlTimer, sleRIPngRoutemapControlTimeStamp, sleRIPngRoutemapControlReqResult, sleRIPngRoutemapControlName, 
				sleRIPngRoutemapControlType, sleRIPngRoutemapControlIfname, sleRIPngPassInterfaceName, sleRIPngPassInterfaceControlRequest, sleRIPngPassInterfaceControlStatus, 
				sleRIPngPassInterfaceControlTimer, sleRIPngPassInterfaceControlTimeStamp, sleRIPngPassInterfaceControlReqResult, sleRIPngPassInterfaceControlName, sleRIPngInterfaceIndex, 
				sleRIPngInterfaceRouterMode, sleRIPngInterfaceRecvPacket, sleRIPngInterfaceSendPacket, sleRIPngInterfaceSplitHorizonMode, sleRIPngInterfaceControlRequest, 
				sleRIPngInterfaceControlStatus, sleRIPngInterfaceControlTimer, sleRIPngInterfaceControlTimeStamp, sleRIPngInterfaceControlReqResult, sleRIPngInterfaceControlIndex, 
				sleRIPngInterfaceControlRouterMode, sleRIPngInterfaceControlRecvPacket, sleRIPngInterfaceControlSendPacket, sleRIPngInterfaceControlSplitHorizonMode, sleRIPngRoutesType, 
				sleRIPngRoutesPrefix, sleRIPngRoutesMask, sleRIPngRoutesNextHop, sleRIPngRoutesSelected, sleRIPngRoutesIfName, 
				sleRIPngRoutesMetric, sleRIPngRoutesTag, sleRIPngRoutesUpTime }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPng 13 }

		
		-- *******.4.1.6296.101.55.14
		sleRIPngNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleRIPngModeCreated, sleRIPngModeDeleted, sleRIPngDefaultMetricChanged, sleRIPngDefaultInformationOrgChanged, sleRIPngDefaultDistanceChanged, 
				sleRIPngRecvBufferSizeChanged, sleRIPngMaximumPathsChanged, sleRIPngMaximumPrefixProfileChanged, sleRIPngMetricSumApplyChanged, sleRIPngTimersChanged, 
				sleRIPngAllCleared, sleRIPngRouteCleared, sleRIPngProtoTypeCleared, sleRIPngAggregateAddrCreated, sleRIPngAggregateAddrDeleted, 
				sleRIPngNeighborCreated, sleRIPngNeighborDeleted, sleRIPngStaticRouteCreated, sleRIPngStaticRouteDeleted, sleRIPngAdminDistanceCreated, 
				sleRIPngAdminDistanceDeleted, sleRIPngDistributeInAccessCreated, sleRIPngDistributeInAccessDeleted, sleRIPngDistributeOutAccessCreated, sleRIPngDistributeOutAccessDeleted, 
				sleRIPngDistributeInPrefixCreated, sleRIPngDistributeInPrefixDeleted, sleRIPngDistributeOutPrefixCreated, sleRIPngDistributeOutPrefixDeleted, sleRIPngOffsetListInCreated, 
				sleRIPngOffsetListInDeleted, sleRIPngOffsetListOutCreated, sleRIPngOffsetListOutDeleted, sleRIPngRedistributeCreated, sleRIPngRedistributeDeleted, 
				sleRIPngRedistributeChanged, sleRIPngRoutemapCreated, sleRIPngRoutemapDeleted, sleRIPngPassInterfaceCreated, sleRIPngPassInterfaceDeleted, 
				sleRIPngInterfaceRouterModeCreated, sleRIPngInterfaceRouterModeDeleted, sleRIPngInterfaceRecvPacketChanged, sleRIPngInterfaceSendPacketChanged, sleRIPngInterfaceSplitHorizonModeChanged
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPng 14 }

		
	
	END

--
-- sle-ripng-mib.mib
--
