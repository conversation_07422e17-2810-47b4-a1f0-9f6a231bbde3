--
-- sle-mpls-tp-tunnel-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, January 15, 2016 at 13:52:12
--

	SLE-MPLS-TP-TUNNEL-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			ifGeneralInformationGroup, ifCounterDiscontinuityGroup, InterfaceIndexOrZero			
				FROM IF-MIB			
			MplsCcId, MplsIccId			
				FROM MPLS-TC-EXT-STD-MIB			
			mplsStdMIB, MplsLabel			
				FROM MPLS-TC-STD-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			zeroDotZero, TimeTicks, IpAddress, Unsigned32, Gauge32, 
			OBJECT-TYPE, MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-<PERSON><PERSON>			
			MacAddress			
				FROM SNMPv2-TC;
	
	
		sleMplsTpTunnel MODULE-IDENTITY 
			LAST-UPDATED "201510070000Z"		-- October 07, 2015 at 00:00 GMT
			ORGANIZATION 
				"Multiprotocol Label Switching (MPLS) Working Group"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				DongChel Shin (Chris)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"Copyright (C) The Internet Society (2004). The
				initial version of this MIB module was published
				in RFC 3812. For full legal notices see the RFC
				itself or see: http://www.ietf.org/copyrights/ianamib.html
				
				This MIB module contains managed object definitions
				 for MPLS Traffic Engineering (TE) as defined in:
				1. Extensions to RSVP for LSP Tunnels, Awduche et
				 al, RFC 3209, December 2001
				2. Constraint-Based LSP Setup using LDP, Jamoussi
				
				 (Editor), RFC 3212, January 2002
				3. Requirements for Traffic Engineering Over MPLS,
				 Awduche, D., Malcolm, J., Agogbua, J., O'Dell, M.,
				 and J. McManus, [RFC2702], September 1999"
			REVISION "200406030000Z"		-- June 03, 2004 at 00:00 GMT
			DESCRIPTION 
				"Initial version issued as part of RFC 3812."
			::= { sleMpls 14 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MPLS."
			::= { sleMgmt 16 }

		
		sleMplsTpTunnelCfg OBJECT IDENTIFIER ::= { sleMplsTpTunnel 1 }

		
		sleMplsTpTunnelCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpTunnelCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The mplsTunnelTable allows new MPLS tunnels to be
				created between an LSR and a remote endpoint, and
				existing tunnels to be reconfigured or removed.
				Note that only point-to-point tunnel segments are
				supported, although multipoint-to-point and point-
				to-multipoint connections are supported by an LSR
				acting as a cross-connect.  Each MPLS tunnel can
				thus have one out-segment originating at this LSR
				and/or one in-segment terminating at this LSR."
			::= { sleMplsTpTunnelCfg 1 }

		
		sleMplsTpTunnelCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpTunnelCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in this table represents an MPLS tunnel.
				An entry can be created by a network administrator
				or by an SNMP agent as instructed by an MPLS
				signalling protocol. Whenever a new entry is
				created with mplsTunnelIsIf set to true(1), then a
				corresponding entry is created in ifTable as well
				(see RFC 2863). The ifType of this entry is
				mplsTunnel(150).
				
				A tunnel entry needs to be uniquely identified across
				a MPLS network. Indices mplsTunnelIndex and
				mplsTunnelInstance uniquely identify a tunnel on
				the LSR originating the tunnel.  To uniquely
				identify a tunnel across an MPLS network requires
				
				index mplsTunnelIngressLSRId.  The last index
				mplsTunnelEgressLSRId is useful in identifying all
				instances of a tunnel that terminate on the same
				egress LSR."
			REFERENCE
				"1. RFC 2863 - The Interfaces Group MIB, McCloghrie,
				K., and F. Kastenholtz, June 2000 "
			INDEX { sleMplsTpTunnelCfgInfoIndex }
			::= { sleMplsTpTunnelCfgInfoTable 1 }

		
		SleMplsTpTunnelCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpTunnelCfgInfoIndex
					Unsigned32,
				sleMplsTpTunnelCfgInfoName
					OCTET STRING,
				sleMplsTpTunnelCfgInfoId
					Unsigned32,
				sleMplsTpTunnelCfgInfoSrcIdType
					INTEGER,
				sleMplsTpTunnelCfgInfoSrcGId
					Unsigned32,
				sleMplsTpTunnelCfgInfoSrcCc
					MplsCcId,
				sleMplsTpTunnelCfgInfoSrcIcc
					MplsIccId,
				sleMplsTpTunnelCfgInfoSrcNodeId
					IpAddress,
				sleMplsTpTunnelCfgInfoDestIdType
					INTEGER,
				sleMplsTpTunnelCfgInfoDestGId
					Unsigned32,
				sleMplsTpTunnelCfgInfoDestCc
					MplsCcId,
				sleMplsTpTunnelCfgInfoDestIcc
					MplsIccId,
				sleMplsTpTunnelCfgInfoDestNodeId
					IpAddress,
				sleMplsTpTunnelCfgInfoMode
					INTEGER,
				sleMplsTpTunnelCfgInfoFwdInLabel
					MplsLabel,
				sleMplsTpTunnelCfgInfoFwdInIfIndex
					InterfaceIndexOrZero,
				sleMplsTpTunnelCfgInfoFwdOperation
					INTEGER,
				sleMplsTpTunnelCfgInfoFwdOutLabel
					MplsLabel,
				sleMplsTpTunnelCfgInfoFwdOutIfIndex
					InterfaceIndexOrZero,
				sleMplsTpTunnelCfgInfoFwdOutMac
					MacAddress,
				sleMplsTpTunnelCfgInfoRevInLabel
					MplsLabel,
				sleMplsTpTunnelCfgInfoRevInIfIndex
					InterfaceIndexOrZero,
				sleMplsTpTunnelCfgInfoRevOperation
					INTEGER,
				sleMplsTpTunnelCfgInfoRevOutLabel
					MplsLabel,
				sleMplsTpTunnelCfgInfoRevOutIfIndex
					InterfaceIndexOrZero,
				sleMplsTpTunnelCfgInfoRevOutMac
					MacAddress,
				sleMplsTpTunnelCfgInfoState
					INTEGER,
				sleMplsTpTunnelCfgInfoRole
					INTEGER,
				sleMplsTpTunnelCfgInfoAssociateTnlName
					OCTET STRING,
				sleMplsTpTunnelCfgInfoDescription
					OCTET STRING,
				sleMplsTpTunnelCfgInfoHlspRole
					INTEGER,
				sleMplsTpTunnelCfgInfoHlspServerTunnelName
					OCTET STRING,
				sleMplsTpTunnelCfgInfoQosPolicyName
					OCTET STRING
			 }

		sleMplsTpTunnelCfgInfoIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A unique value which identify an entry in this table."
			::= { sleMplsTpTunnelCfgInfoEntry 1 }

		
		sleMplsTpTunnelCfgInfoName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..16))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The canonical name assigned to the tunnel. This name
				can be used to refer to the tunnel on the LSR's
				console port.  If mplsTunnelIsIf is set to true
				then the ifName of the interface corresponding to
				this tunnel should have a value equal to
				mplsTunnelName.  Also see the description of ifName
				in RFC 2863."
			REFERENCE
				"RFC 2863 - The Interfaces Group MIB, McCloghrie, K.,
				and F. Kastenholtz, June 2000."
			DEFVAL { "" }
			::= { sleMplsTpTunnelCfgInfoEntry 2 }

		
		sleMplsTpTunnelCfgInfoId OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Uniquely identifies a set of tunnel instances
				between a pair of ingress and egress LSRs.
				Managers should obtain new values for row
				creation in this table by reading
				mplsTunnelIndexNext. When
				the MPLS signalling protocol is rsvp(2) this value
				SHOULD be equal to the value signaled in the
				Tunnel Id of the Session object. When the MPLS
				signalling protocol is crldp(3) this value
				SHOULD be equal to the value signaled in the
				LSP ID."
			::= { sleMplsTpTunnelCfgInfoEntry 3 }

		
		sleMplsTpTunnelCfgInfoSrcIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the ietf or itut type."
			::= { sleMplsTpTunnelCfgInfoEntry 4 }

		
		sleMplsTpTunnelCfgInfoSrcGId OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to display the GlobalID of IETF"
			::= { sleMplsTpTunnelCfgInfoEntry 5 }

		
		sleMplsTpTunnelCfgInfoSrcCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to display the Source CC-ID of ITUT"
			::= { sleMplsTpTunnelCfgInfoEntry 6 }

		
		sleMplsTpTunnelCfgInfoSrcIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to set the Source ICC-ID of ITUT"
			::= { sleMplsTpTunnelCfgInfoEntry 7 }

		
		sleMplsTpTunnelCfgInfoSrcNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to display the Source Node ID of IETF and ITUT"
			::= { sleMplsTpTunnelCfgInfoEntry 8 }

		
		sleMplsTpTunnelCfgInfoDestIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the ietf or itut type."
			::= { sleMplsTpTunnelCfgInfoEntry 9 }

		
		sleMplsTpTunnelCfgInfoDestGId OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to set the Destination GlobalID of IETF"
			::= { sleMplsTpTunnelCfgInfoEntry 10 }

		
		sleMplsTpTunnelCfgInfoDestCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to display the Destination CC-ID of ITUT"
			::= { sleMplsTpTunnelCfgInfoEntry 11 }

		
		sleMplsTpTunnelCfgInfoDestIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to set the Destination ICC-ID of ITUT"
			::= { sleMplsTpTunnelCfgInfoEntry 12 }

		
		sleMplsTpTunnelCfgInfoDestNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This Object is used to display the Destination Node ID of IETF and ITUT"
			::= { sleMplsTpTunnelCfgInfoEntry 13 }

		
		sleMplsTpTunnelCfgInfoMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				unidirectional(1),
				bidirectional(2),
				corouted(3),
				associate(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Tunnel Mode"
			::= { sleMplsTpTunnelCfgInfoEntry 14 }

		
		sleMplsTpTunnelCfgInfoFwdInLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the Forward In Label.
				The lable range is 0 to 1048575. Here 0 to 15 are
				reserved labels. User allowed range is 16 to 1048575..
				
				The default value 1048576 is invalid label "
			DEFVAL { 1048576 }
			::= { sleMplsTpTunnelCfgInfoEntry 15 }

		
		sleMplsTpTunnelCfgInfoFwdInIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Forward IN Interface index "
			::= { sleMplsTpTunnelCfgInfoEntry 16 }

		
		sleMplsTpTunnelCfgInfoFwdOperation OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				push(1),
				pop(2),
				swap(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Forward Ilm OPeration ."
			::= { sleMplsTpTunnelCfgInfoEntry 17 }

		
		sleMplsTpTunnelCfgInfoFwdOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the Forward In Label.
				The lable range is 0 to 1048575. Here 0 to 15 are
				reserved labels. User allowed range is 16 to 1048575.
				
				The default value 1048576 is invalid label "
			DEFVAL { 1048576 }
			::= { sleMplsTpTunnelCfgInfoEntry 18 }

		
		sleMplsTpTunnelCfgInfoFwdOutIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Forward OUT Interface index "
			::= { sleMplsTpTunnelCfgInfoEntry 19 }

		
		sleMplsTpTunnelCfgInfoFwdOutMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the Forward OUT MAC ADDRESS "
			::= { sleMplsTpTunnelCfgInfoEntry 20 }

		
		sleMplsTpTunnelCfgInfoRevInLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the Forward In Label.
				The lable range is 0 to 1048575. Here 0 to 15 are
				reserved labels. User allowed range is 16 to 1048575.
				
				The default value 1048576 is invalid label "
			DEFVAL { 1048576 }
			::= { sleMplsTpTunnelCfgInfoEntry 21 }

		
		sleMplsTpTunnelCfgInfoRevInIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Reverse IN Interface index "
			::= { sleMplsTpTunnelCfgInfoEntry 22 }

		
		sleMplsTpTunnelCfgInfoRevOperation OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				push(1),
				pop(2),
				swap(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Reverse Ilm OPeration ."
			::= { sleMplsTpTunnelCfgInfoEntry 23 }

		
		sleMplsTpTunnelCfgInfoRevOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the Forward In Label.
				The lable range is 0 to 1048575. Here 0 to 15 are
				reserved labels. User allowed range is 16 to 1048575.
				
				The default value 1048576 is invalid label "
			DEFVAL { 1048576 }
			::= { sleMplsTpTunnelCfgInfoEntry 24 }

		
		sleMplsTpTunnelCfgInfoRevOutIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Reverse OUT Interface index "
			::= { sleMplsTpTunnelCfgInfoEntry 25 }

		
		sleMplsTpTunnelCfgInfoRevOutMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to display the Forward OUT MAC ADDRESS ."
			::= { sleMplsTpTunnelCfgInfoEntry 26 }

		
		sleMplsTpTunnelCfgInfoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is to display status of the tunnel."
			::= { sleMplsTpTunnelCfgInfoEntry 27 }

		
		sleMplsTpTunnelCfgInfoRole OBJECT-TYPE
			SYNTAX INTEGER
				{
				source(0),
				transist(1),
				destination(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is to display Role  of the tunnel."
			::= { sleMplsTpTunnelCfgInfoEntry 28 }

		
		sleMplsTpTunnelCfgInfoAssociateTnlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object to display associate of reverse tunnel."
			::= { sleMplsTpTunnelCfgInfoEntry 29 }

		
		sleMplsTpTunnelCfgInfoDescription OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMplsTpTunnelCfgInfoEntry 30 }

		
		sleMplsTpTunnelCfgInfoHlspRole OBJECT-TYPE
			SYNTAX INTEGER
				{
				server(1),
				client(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This  object is used to set the Hlsp Tunnel. whether the tunnel is Server or client.
				server (1) :- To set the  Server Tunnel
				client(2) :- To set the  Client Tunnel ."
			::= { sleMplsTpTunnelCfgInfoEntry 31 }

		
		sleMplsTpTunnelCfgInfoHlspServerTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the server TunnelName, 
				when sleMplsTpTunnelCfgControlHlspOption as a client ."
			::= { sleMplsTpTunnelCfgInfoEntry 32 }

		
		sleMplsTpTunnelCfgInfoQosPolicyName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object is used to set the Qos Policy name."
			::= { sleMplsTpTunnelCfgInfoEntry 33 }

		
		sleMplsTpTunnelCfgControl OBJECT IDENTIFIER ::= { sleMplsTpTunnelCfg 2 }

		
		sleMplsTpTunnelCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createMplsTpTunnelEntry(1),
				deleteMplsTpTunnelEntry(2),
				setMplsTpTunnelMode(3),
				setNhlfe(4),
				setIlmPop(5),
				setIlmSwap(6),
				setAssociateTunnel(7),
				unsetNhlfe(8),
				unsetIlmPop(9),
				unsetIlmSwap(10),
				unsetAssociateTunnel(11),
				setDescription(12),
				setHlspServerLsp(13),
				setHlspClientLsp(14),
				unsetHlspServerLsp(15),
				unsetHlspClientLsp(16),
				setTunnelQosPolicyName(17),
				unsetTunnelQosPolicyName(18)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be
				modified in the Tunnel table. For each read-write column of 
				Tunnel table, a Set Operation controlvalue is added in this object."
			::= { sleMplsTpTunnelCfgControl 1 }

		
		sleMplsTpTunnelCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpTunnelCfgControl 2 }

		
		sleMplsTpTunnelCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured 
				for every control table."
			::= { sleMplsTpTunnelCfgControl 3 }

		
		sleMplsTpTunnelCfgControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpTunnelCfgControl 4 }

		
		sleMplsTpTunnelCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpTunnelCfgControl 5 }

		
		sleMplsTpTunnelCfgControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The canonical name assigned to the tunnel. This name
				can be used to refer to the tunnel on the LSR's
				console port.  If mplsTunnelIsIf is set to true
				then the ifName of the interface corresponding to
				this tunnel should have a value equal to
				SlemplsTunnelName.  Also see the description of ifName
				in RFC 2863."
			REFERENCE
				"RFC 2863 - The Interfaces Group MIB, McCloghrie, K.,
				and F. Kastenholtz, June 2000."
			DEFVAL { "" }
			::= { sleMplsTpTunnelCfgControl 6 }

		
		sleMplsTpTunnelCfgControlId OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the tunnel Id ."
			::= { sleMplsTpTunnelCfgControl 7 }

		
		sleMplsTpTunnelCfgControlSrcIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the ietf or itut type."
			::= { sleMplsTpTunnelCfgControl 8 }

		
		sleMplsTpTunnelCfgControlSrcGId OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the source Global-Id for IETF ."
			::= { sleMplsTpTunnelCfgControl 9 }

		
		sleMplsTpTunnelCfgControlSrcCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the source CC-ID for ITUT."
			::= { sleMplsTpTunnelCfgControl 10 }

		
		sleMplsTpTunnelCfgControlSrcIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Source ICC-ID for ITUT."
			::= { sleMplsTpTunnelCfgControl 11 }

		
		sleMplsTpTunnelCfgControlSrcNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Source Node-Id for IETF and ITUT"
			::= { sleMplsTpTunnelCfgControl 12 }

		
		sleMplsTpTunnelCfgControlDestIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the ietf or itut type."
			::= { sleMplsTpTunnelCfgControl 13 }

		
		sleMplsTpTunnelCfgControlDestGId OBJECT-TYPE
			SYNTAX Unsigned32 (1)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Destination Global-Id for IETF ."
			::= { sleMplsTpTunnelCfgControl 14 }

		
		sleMplsTpTunnelCfgControlDestCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Destination CC-ID for ITUT."
			::= { sleMplsTpTunnelCfgControl 15 }

		
		sleMplsTpTunnelCfgControlDestIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Destination ICC-ID for ITUT."
			::= { sleMplsTpTunnelCfgControl 16 }

		
		sleMplsTpTunnelCfgControlDestNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This  object is used to set the Destination NodeID for IETF 
				and ITUT."
			::= { sleMplsTpTunnelCfgControl 17 }

		
		sleMplsTpTunnelCfgControlMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				unidirectional(1),
				bidirectional(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This  object is used to set the Tunnel mode ."
			::= { sleMplsTpTunnelCfgControl 18 }

		
		sleMplsTpTunnelCfgControlPath OBJECT-TYPE
			SYNTAX INTEGER
				{
				forwardPath(1),
				reversePath(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to Set the forward path and reverse path 
				tunnel."
			::= { sleMplsTpTunnelCfgControl 19 }

		
		sleMplsTpTunnelCfgControlInLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Inlabel. "
			::= { sleMplsTpTunnelCfgControl 20 }

		
		sleMplsTpTunnelCfgControlInInterface OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Ininterface index."
			::= { sleMplsTpTunnelCfgControl 21 }

		
		sleMplsTpTunnelCfgControlOperation OBJECT-TYPE
			SYNTAX INTEGER
				{
				push(1),
				pop(2),
				swap(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the operation of Tuunel "
			::= { sleMplsTpTunnelCfgControl 22 }

		
		sleMplsTpTunnelCfgControlOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the outlabel. "
			::= { sleMplsTpTunnelCfgControl 23 }

		
		sleMplsTpTunnelCfgControlOutInterface OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the outerinterface index."
			::= { sleMplsTpTunnelCfgControl 24 }

		
		sleMplsTpTunnelCfgControlOutMacAddress OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to Set the Out MacAddress ."
			::= { sleMplsTpTunnelCfgControl 25 }

		
		sleMplsTpTunnelCfgControlAssociateTnlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to Associated Forward tunnel and reverse tunnel
				with reverse tunnel name ."
			::= { sleMplsTpTunnelCfgControl 26 }

		
		sleMplsTpTunnelCfgControlDescription OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMplsTpTunnelCfgControl 27 }

		
		sleMplsTpTunnelCfgControlHlspSeverTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the server Tunnel name for associating server and client."
			::= { sleMplsTpTunnelCfgControl 28 }

		
		sleMplsTpTunnelCfgControlQosPolicyName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the Qos Policy name."
			::= { sleMplsTpTunnelCfgControl 29 }

		
	
	END

--
-- sle-mpls-tp-tunnel-mib.mib
--
