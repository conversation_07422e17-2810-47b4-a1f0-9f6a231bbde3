--
-- sle-mpls-tp-lps-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, January 12, 2016 at 11:21:30
--

	SLE-MPLS-TP-LPS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			TimeTicks, Unsigned32, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, 
			OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpLps MODULE-IDENTITY 
			LAST-UPDATED "201510070000Z"		-- October 07, 2015 at 00:00 GMT
			ORGANIZATION 
				"Multiprotocol Label Switching (MplsTp) Working Group"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				<PERSON><PERSON><PERSON> Shin (<PERSON>)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"This management information module supports the
				configuration and management of MplsTp TP linear
				protection groups. "
			REVISION "201207150000Z"		-- July 15, 2012 at 00:00 GMT
			DESCRIPTION 
				"MplsTp Protection Switching Group objects for LSP MEPs"
			::= { sleMpls 18 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MplsTp."
			::= { sleMgmt 16 }

		
		sleMplsTpLpsCfg OBJECT IDENTIFIER ::= { sleMplsTpLps 1 }

		
		sleMplsTpLpsCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpLpsCfgInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This table lists the sleMplsTp linear protection groups that
				have been configured on the system."
			::= { sleMplsTpLpsCfg 1 }

		
		sleMplsTpLpsCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpLpsCfgInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A conceptual row in the sleMplsTpLpsCfgInfoTable."
			INDEX { sleMplsTpLpsCfgInfoGroupIndex }
			::= { sleMplsTpLpsCfgInfoTable 1 }

		
		SleMplsTpLpsCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpLpsCfgInfoGroupIndex
					Unsigned32,
				sleMplsTpLpsCfgInfoGroupName
					OCTET STRING,
				sleMplsTpLpsCfgInfoMode
					INTEGER,
				sleMplsTpLpsCfgInfoProtectionScheme
					INTEGER,
				sleMplsTpLpsCfgInfoRevertive
					INTEGER,
				sleMplsTpLpsCfgInfoWtr
					Unsigned32,
				sleMplsTpLpsCfgInfoContinualTxInterval
					Unsigned32,
				sleMplsTpLpsCfgInfoRapidTxInterval
					Unsigned32,
				sleMplsTpLpsCfgInfoSwitchOver
					INTEGER,
				sleMplsTpLpsCfgInfoLockOut
					INTEGER,
				sleMplsTpLpsCfgInfoHoldOffTimer
					Unsigned32,
				sleMplsTpLpsCfgInfoActivePath
					INTEGER,
				sleMplsTpLpsCfgInfoOperationState
					INTEGER,
				sleMplsTpLpsCfgInfoEventStatus
					INTEGER
			 }

		sleMplsTpLpsCfgInfoGroupIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index for the conceptual row identifying a protection group."
			::= { sleMplsTpLpsCfgInfoEntry 1 }

		
		sleMplsTpLpsCfgInfoGroupName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..128))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Textual name represents the sleMplsTp tp protection group.
				Each Protection Group is identified by a unique
				protection group name. "
			::= { sleMplsTpLpsCfgInfoEntry 2 }

		
		sleMplsTpLpsCfgInfoMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				onePlusOne(1),
				oneColonOne(2),
				oneColonN(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The architectural mode of the Protection group. This can
				either be 1+1, 1:1, 1:n.
				
				1+1
				
				In the 1+1 protection scheme, a fully dedicated
				protection entity is allocated. Data traffic is copied
				and fed at the source to both the working and the
				protection entities.  The traffic on the working and the
				protection entities is transmitted simultaneously to
				the sink of the protection domain, where selection
				between the working and protection entities is performed
				
				1:1
				
				In the 1:1 scheme, a protection path is allocated to
				protect against a defect, failure, or a degradation in a
				working path. In normal conditions, data traffic is
				transmitted over the working entity, while the
				protection entity functions in the idle state. If there
				is a defect on the working entity or a specific
				administrative request, traffic is switched to the
				protection entity.
				
				1:n
				
				In case of 1:n linear protection, one protection entity
				is allocated to protect n working entities. The protection
				entity might not have sufficient resources to protect all the
				working entities that may be affected by fault conditions at a
				specific time. In this case, in order to guaranteed
				protection, the protection entity should support enough
				capacity and bandwidth to protect any of the n working
				entities."
			DEFVAL { oneColonOne }
			::= { sleMplsTpLpsCfgInfoEntry 3 }

		
		sleMplsTpLpsCfgInfoProtectionScheme OBJECT-TYPE
			SYNTAX INTEGER
				{
				bidirectional(1),
				unidirectional(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The object represents the operational scheme of
				protection switching group. The protection scheme may
				either be unidirectional or bidirectional.
				
				bidirectional
				In bidirectional protection scheme, both the directions
				will be switched simultaneously even if the fault
				applies to only one direction of the path.
				
				unidirectional
				In unidirectional protection scheme protection switching
				will be performed independently for each direction of a
				bidirectional transport path
				
				This object may not be modified if the associated
				sleMplsTpLpsCfgRowStatus object is equal to active(1). "
			DEFVAL { bidirectional }
			::= { sleMplsTpLpsCfgInfoEntry 4 }

		
		sleMplsTpLpsCfgInfoRevertive OBJECT-TYPE
			SYNTAX INTEGER
				{
				nonrevertive(1),
				revertive(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object represents the reversion mode of the Linear
				Protection Switching group. The reversion mode of
				protection mechanism may be either revertive or
				non-revertive.
				
				In non-revertive mode, after a service has been
				recovered, traffic will be forwarded on the recovery
				path revertive
				In revertive mode, after a service has been
				recovered, traffic will be redirected back onto the
				original working path."
			DEFVAL { nonrevertive }
			::= { sleMplsTpLpsCfgInfoEntry 5 }

		
		sleMplsTpLpsCfgInfoWtr OBJECT-TYPE
			SYNTAX Unsigned32 (0..720)
			UNITS "seconds"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object hold the Wait To Restore timer value in
				seconds.
				The WTR timer is used to delay reversion of PSC state
				to Normal state when recovering from a failure
				condition on the working path when the protection
				domain is configured for revertive behavior
				
				This object may not be modified if the associated
				sleMplsTpLpsCfgRowStatus object is equal to active(1)."
			DEFVAL { 300 }
			::= { sleMplsTpLpsCfgInfoEntry 6 }

		
		sleMplsTpLpsCfgInfoContinualTxInterval OBJECT-TYPE
			SYNTAX Unsigned32 (1..20)
			UNITS "seconds"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Continual Tx Time in Seconds. Represents the time 
				interval to send the continual LPS packet to the other 
				end based on the current state."
			DEFVAL { 5 }
			::= { sleMplsTpLpsCfgInfoEntry 7 }

		
		sleMplsTpLpsCfgInfoRapidTxInterval OBJECT-TYPE
			SYNTAX Unsigned32 (1000..20000)
			UNITS "micro-seconds"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Rapid Tx interval in micro-Seconds. Represents the time
				interval to send the LPS packet to the other end, when
				there is a change in state of Linear Protection domain due
				to local input. The default value is 3.3 milli-seconds
				which is 3300 micro-seconds"
			DEFVAL { 3300 }
			::= { sleMplsTpLpsCfgInfoEntry 8 }

		
		sleMplsTpLpsCfgInfoSwitchOver OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				forced(1),
				manual(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" this object used for switch the traffic to the back up path."
			::= { sleMplsTpLpsCfgInfoEntry 9 }

		
		sleMplsTpLpsCfgInfoLockOut OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"this is object is used for preventing the swith traffic by the protection path."
			::= { sleMplsTpLpsCfgInfoEntry 10 }

		
		sleMplsTpLpsCfgInfoHoldOffTimer OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" this object used for show the hold off timer."
			::= { sleMplsTpLpsCfgInfoEntry 11 }

		
		sleMplsTpLpsCfgInfoActivePath OBJECT-TYPE
			SYNTAX INTEGER
				{
				primary(1),
				backup(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object say which path is Active ."
			::= { sleMplsTpLpsCfgInfoEntry 12 }

		
		sleMplsTpLpsCfgInfoOperationState OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(0)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"this  object says Operational Status of LPS.    The LPS is between PE to PE."
			DEFVAL { 0 }
			::= { sleMplsTpLpsCfgInfoEntry 13 }

		
		sleMplsTpLpsCfgInfoEventStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				protection(2),
				localForce(3),
				localManual(4),
				localSgfProtection(5),
				localSgfWorking(6),
				localWtr(7),
				localLock(8),
				localClrSfProtection(9),
				localClrSfWorking(10),
				localClrEvent(11),
				remoteLock(12),
				remoteForce(13),
				remoteManual(14),
				remoteSfProtect(15),
				remoteSfWork(16),
				remoteWtr(17),
				remoteNoReq(18),
				remoteNotRevert(19),
				noRequest(20),
				remoteSdWork(21),
				remoteSdProtection(22),
				remoteExesWork(23),
				remoteExesProtect(24),
				remoteRrReqWork(25),
				remoteRrReqProtec(26),
				remoteNoReqWork(27),
				remoteNoReqProtection(28),
				localSdProtection(29),
				localClearSdProtection(30),
				localSdWorking(31),
				localClearSdWorking(32),
				localExercise(33)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This  is object says  EVENT Status of LPS protection ."
			DEFVAL { 0 }
			::= { sleMplsTpLpsCfgInfoEntry 14 }

		
		sleMplsTpLpsCfgControl OBJECT IDENTIFIER ::= { sleMplsTpLpsCfg 2 }

		
		sleMplsTpLpsCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSleMplsTpLpsCfgEntry(1),
				deleteSleMplsTpLpsCfgEntry(2),
				setSleMplsTpLpsCfgControProtectionScheme(3),
				unsetSleMplsTpLpsCfgControProtectionScheme(4),
				setSleMplsLpsTpCfgControlRevertive(5),
				setSleMplsLpsTpCfgControlWaitToRestoreset(6),
				unsetSleMplsTpLpsCfgControlWaitToRestoreset(7),
				setSleMplsLpsTpCfgControlContinualTxInterval(8),
				unsetSleMplsTpLpsCfgControlContinualTxInterval(9),
				setSleMplsLpsTpCfgControlRapidTxInterval(10),
				unsetSleMplsTpLpsCfgControlRapidTxInterval(11),
				setSleMplsLpsTpCfgControlSwitchover(12),
				unsetSleMplsTpLpsCfgControlSwitchover(13),
				setSleMplsTpLpsCfgControlLockOut(14),
				unsetSleMplsTpLpsTpCfgControlLockOut(15),
				setSleMplsTpLpsCfgControlHoldOffTimer(16),
				unSetSleMplsTpLpsCfgControlHoldOffTimer(17)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions 
				via setting this entry as proper value."
			::= { sleMplsTpLpsCfgControl 1 }

		
		sleMplsTpLpsCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as 
				.busy. or .idle. before do setRequest."
			::= { sleMplsTpLpsCfgControl 2 }

		
		sleMplsTpLpsCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMplsTpLpsCfgControl 3 }

		
		sleMplsTpLpsCfgControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMplsTpLpsCfgControl 4 }

		
		sleMplsTpLpsCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMplsTpLpsCfgControl 5 }

		
		sleMplsTpLpsCfgControlGroupName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Textual name represents the sleMplsTp tp protection group.
				Each Protection Group is identified by a unique
				protection group name. "
			::= { sleMplsTpLpsCfgControl 6 }

		
		sleMplsTpLpsCfgControlMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				onePlusOne(1),
				oneColonOne(2),
				oneColonN(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The architectural mode of the Protection group. This can
				either be 1+1, 1:1, 1:n.
				
				1+1
				
				In the 1+1 protection scheme, a fully dedicated
				protection entity is allocated. Data traffic is copied
				
				
				
				and fed at the source to both the working and the
				protection entities.  The traffic on the working and the
				protection entities is transmitted simultaneously to
				the sink of the protection domain, where selection
				between the working and protection entities is performed
				
				1:1
				
				In the 1:1 scheme, a protection path is allocated to
				protect against a defect, failure, or a degradation in a
				working path. In normal conditions, data traffic is
				transmitted over the working entity, while the
				protection entity functions in the idle state. If there
				is a defect on the working entity or a specific
				administrative request, traffic is switched to the
				protection entity.
				
				1:n
				
				In case of 1:n linear protection, one protection entity
				is allocated to protect n working entities. The protection
				entity might not have sufficient resources to protect all the
				working entities that may be affected by fault conditions at a
				specific time. In this case, in order to guaranteed
				protection, the protection entity should support enough
				capacity and bandwidth to protect any of the n working
				entities."
			DEFVAL { oneColonOne }
			::= { sleMplsTpLpsCfgControl 7 }

		
		sleMplsTpLpsCfgControlProtectionScheme OBJECT-TYPE
			SYNTAX INTEGER
				{
				bidirectional(1),
				unidirectional(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The object represents the operational scheme of
				protection switching group. The protection scheme may
				either be unidirectional or bidirectional.
				
				bidirectional
				In bidirectional protection scheme, both the directions
				will be switched simultaneously even if the fault
				applies to only one direction of the path.
				
				unidirectional
				In unidirectional protection scheme protection switching
				will be performed independently for each direction of a
				bidirectional transport path
				
				This object may not be modified if the associated
				sleMplsTpLpsCfgControlRowStatus object is equal to active(1). "
			DEFVAL { bidirectional }
			::= { sleMplsTpLpsCfgControl 8 }

		
		sleMplsTpLpsCfgControlRevertive OBJECT-TYPE
			SYNTAX INTEGER
				{
				nonrevertive(1),
				revertive(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object represents the reversion mode of the Linear
				Protection Switching group. The reversion mode of
				protection mechanism may be either revertive or
				non-revertive.
				
				nonrevertive
				In non-revertive mode, after a service has been
				recovered, traffic will be forwarded on the recovery
				path
				
				revertive
				In revertive mode, after a service has been
				recovered, traffic will be redirected back onto the
				original working path."
			DEFVAL { nonrevertive }
			::= { sleMplsTpLpsCfgControl 9 }

		
		sleMplsTpLpsCfgControlWtr OBJECT-TYPE
			SYNTAX Unsigned32 (0..720)
			UNITS "seconds"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object hold the Wait To Restore timer value in
				seconds.
				The WTR timer is used to delay reversion of PSC state
				to Normal state when recovering from a failure
				condition on the working path when the protection
				domain is configured for revertive behavior
				
				This object may not be modified if the associated
				sleMplsTpLpsCfgControlRowStatus object is equal to active(1)."
			DEFVAL { 300 }
			::= { sleMplsTpLpsCfgControl 10 }

		
		sleMplsTpLpsCfgControlContinualTxInterval OBJECT-TYPE
			SYNTAX Unsigned32 (1..20)
			UNITS "seconds"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Continual Tx Time in Seconds. Represents the time 
				interval to send the continual LPS packet to the other 
				end based on the current state."
			DEFVAL { 5 }
			::= { sleMplsTpLpsCfgControl 11 }

		
		sleMplsTpLpsCfgControlRapidTxInterval OBJECT-TYPE
			SYNTAX Unsigned32 (1000)
			UNITS "micro-seconds"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Rapid Tx interval in micro-Seconds. Represents the time
				interval to send the LPS packet to the other end, when
				there is a change in state of Linear Protection domain due
				to local input. The default value is 3.3 milli-seconds
				which is 3300 micro-seconds"
			DEFVAL { 3300 }
			::= { sleMplsTpLpsCfgControl 12 }

		
		sleMplsTpLpsCfgControlswitchOver OBJECT-TYPE
			SYNTAX INTEGER
				{
				force(1),
				manual(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" this object used for switch the traffic to the back up path."
			::= { sleMplsTpLpsCfgControl 13 }

		
		sleMplsTpLpsCfgControlLockOut OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(0)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"this is object is used for preventing the swith traffic by the protection path."
			::= { sleMplsTpLpsCfgControl 14 }

		
		sleMplsTpLpsCfgControlHoldOffTimer OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to set the holdofftimer "
			::= { sleMplsTpLpsCfgControl 15 }

		
		sleMplsTpLpsMeCfg OBJECT IDENTIFIER ::= { sleMplsTpLps 2 }

		
		sleMplsTpLpsMeCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpLpsMeCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table lists Maintenance Association that have been
				configured in Protection groups."
			::= { sleMplsTpLpsMeCfg 1 }

		
		sleMplsTpLpsMeCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpLpsMeCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A conceptual row in the sleMplsTpLpsMeCfgInfoTable."
			INDEX { sleMplsTpLpsMeCfgInfoMeIndex, sleMplsTpLpsMeCfgInfoMPId, sleMplsTpLspMeCfgInfoGroupIndex, sleMplsTpLpsMeCfgInfoMegIndex }
			::= { sleMplsTpLpsMeCfgInfoTable 1 }

		
		SleMplsTpLpsMeCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpLpsMeCfgInfoMegIndex
					Unsigned32,
				sleMplsTpLpsMeCfgInfoMeIndex
					Unsigned32,
				sleMplsTpLpsMeCfgInfoMPId
					Unsigned32,
				sleMplsTpLspMeCfgInfoGroupIndex
					Unsigned32,
				sleMplsTpLpsMeCfgInfoState
					INTEGER
			 }

		sleMplsTpLpsMeCfgInfoMegIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEG."
			::= { sleMplsTpLpsMeCfgInfoEntry 1 }

		
		sleMplsTpLpsMeCfgInfoMeIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object denotes the ME name, each
				Maintenance Entity has unique name within MEG."
			::= { sleMplsTpLpsMeCfgInfoEntry 2 }

		
		sleMplsTpLpsMeCfgInfoMPId OBJECT-TYPE
			SYNTAX Unsigned32 (1..8191)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Indicates the maintenance point index, used to create
				multiple MEPs in a node of single ME. The value of this
				object can be MEP index or MIP index. Managers should 
				obtain new values for row creation in this table by reading
				mplsOamIdMeMpIndexNext."
			::= { sleMplsTpLpsMeCfgInfoEntry 3 }

		
		sleMplsTpLspMeCfgInfoGroupIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Textual name represents the sleMplsTp tp protection group.
				Each Protection Group is identified by a unique
				protection group name. "
			::= { sleMplsTpLpsMeCfgInfoEntry 4 }

		
		sleMplsTpLpsMeCfgInfoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				primary(1),
				backup(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object represents the operational state of the ME
				as either primary or backup"
			::= { sleMplsTpLpsMeCfgInfoEntry 5 }

		
		sleMplsTpLpsMeCfgControl OBJECT IDENTIFIER ::= { sleMplsTpLpsMeCfg 2 }

		
		sleMplsTpLpsMeCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSleMplsTpLpsMeConfigEntry(1),
				deleteSleMplsTpLpsMeConfigEntry(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the configuration commands, and user can configure functions via setting 
				this entry as proper value."
			::= { sleMplsTpLpsMeCfgControl 1 }

		
		sleMplsTpLpsMeCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this value as 
				.busy. or .idle. before do setRequest."
			::= { sleMplsTpLpsMeCfgControl 2 }

		
		sleMplsTpLpsMeCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest end. In case of short-time command, this value is 0"
			::= { sleMplsTpLpsMeCfgControl 3 }

		
		sleMplsTpLpsMeCfgControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleMplsTpLpsMeCfgControl 4 }

		
		sleMplsTpLpsMeCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleMplsTpLpsMeCfgControl 5 }

		
		sleMplsTpLpsMeCfgControlMegName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Uniquely identifies a maintenance entity index within
				a MEG"
			::= { sleMplsTpLpsMeCfgControl 6 }

		
		sleMplsTpLpsMeCfgControlMeName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object denotes the ME name, each
				Maintenance Entity has unique name within MEG."
			::= { sleMplsTpLpsMeCfgControl 7 }

		
		sleMplsTpLpsMeCfgControlMpId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION			" "
			::= { sleMplsTpLpsMeCfgControl 8 }

		
		sleMplsTpLpsMeCfgControlGroupName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the Protection group index wherein
				this ME included in. If this ME is not part of a protection
				group this value is set to 0. "
			::= { sleMplsTpLpsMeCfgControl 9 }

		
		sleMplsTpLpsMeCfgControlState OBJECT-TYPE
			SYNTAX INTEGER
				{
				primary(1),
				backup(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object represents the operational state of the ME
				as either primary or backup"
			::= { sleMplsTpLpsMeCfgControl 10 }

		
	
	END

--
-- sle-mpls-tp-lps-mib.mib
--
