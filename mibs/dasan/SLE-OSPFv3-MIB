--
-- sle-ospfv3-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, August 04, 2015 at 13:23:52
--

	SLE-OSPFv3-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			InetAddressIPv4, InetAddressIPv6			
				FROM INET-ADDRESS-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>te<PERSON>32, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>32, 
			BITS, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.52
		sleOSPFv3 MODULE-IDENTITY 
			LAST-UPDATED "201411180836Z"		-- November 18, 2014 at 08:36 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleMgmt 52 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.52.1
		sleOspfv3Base OBJECT IDENTIFIER ::= { sleOSPFv3 1 }

		
		-- *******.4.1.6296.**********
		sleOspfv3BaseInfo OBJECT IDENTIFIER ::= { sleOspfv3Base 1 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3RouteDisplayMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				singleLine(0),
				multiLine(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3BaseInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleOspfv3RestartPeriod OBJECT-TYPE
			SYNTAX Integer32 (0..1800)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"OSPF Graceful-restart period"
			::= { sleOspfv3BaseInfo 2 }

		
		-- *******.4.1.6296.101.5*******
		sleOspfv3RestartHelperPolicy OBJECT-TYPE
			SYNTAX INTEGER
				{
				never(1),
				onlyReload(2),
				onlyUpgrade(3),
				reloadUpgrade(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"OSPF Graceful-restart local policy by helper mode"
			::= { sleOspfv3BaseInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleOspfv3RestartHelperPeriod OBJECT-TYPE
			SYNTAX Integer32 (0..1800)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"OSPF Graceful-restart local policy period by helper mode"
			::= { sleOspfv3BaseInfo 4 }

		
		-- *******.4.1.6296.**********.5
		sleOspfv3SnmpNotification OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp notification status of OSPF."
			::= { sleOspfv3BaseInfo 5 }

		
		-- *******.4.1.6296.**********
		sleOspfv3BaseControl OBJECT IDENTIFIER ::= { sleOspfv3Base 2 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3ControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setOspfv3RouteDisplayMode(1),
				setOspfv3RestartPeriod(2),
				setOspfv3RestartHelperProfile(3),
				restartOspfv3Graceful(4),
				setSnmpNotification(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleOspfv3BaseControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleOspfv3ControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3BaseControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleOspfv3ControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3BaseControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleOspfv3ControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3BaseControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleOspfv3ControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3BaseControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleOspfv3ControlRouteDisplayMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				singleLine(0),
				multiLine(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Display Mode of OSPFv3 Route."
			::= { sleOspfv3BaseControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleOspfv3ControlRestartPeriod OBJECT-TYPE
			SYNTAX Integer32 (0..1800)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"OSPFv3 Graceful-restart period."
			::= { sleOspfv3BaseControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleOspfv3ControlRestartHelperPolicy OBJECT-TYPE
			SYNTAX INTEGER
				{
				unspec(0),
				never(1),
				onlyReload(2),
				onlyUpgrade(3),
				reloadUpgrade(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Tag of OSPFv3 Process."
			::= { sleOspfv3BaseControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleOspfv3ControlRestartHelperPeriod OBJECT-TYPE
			SYNTAX Integer32 (0..1800)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"OSPF Graceful-restart local policy period by helper mode"
			::= { sleOspfv3BaseControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleOspfv3ControlSnmpNotification OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Snmp notification status of OSPF."
			::= { sleOspfv3BaseControl 10 }

		
		-- *******.4.1.6296.**********
		sleOspfv3BaseNotification OBJECT IDENTIFIER ::= { sleOspfv3Base 3 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3RouteDisplayModeChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ControlRequest, sleOspfv3ControlTimeStamp, sleOspfv3ControlRouteDisplayMode, sleOspfv3ControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3BaseNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleOspfv3RestartPeriodChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ControlRequest, sleOspfv3ControlTimeStamp, sleOspfv3ControlReqResult, sleOspfv3RestartPeriod }
			STATUS current
			DESCRIPTION 
				"setOspfv3RestartPeriod"
			::= { sleOspfv3BaseNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleOspfv3RestartHelperProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ControlRequest, sleOspfv3ControlTimeStamp, sleOspfv3ControlReqResult, sleOspfv3RestartHelperPolicy, sleOspfv3RestartHelperPeriod
				 }
			STATUS current
			DESCRIPTION 
				"setOspfv3RestartHelperProfile"
			::= { sleOspfv3BaseNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleOspfv3GracefulRestarted NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ControlRequest, sleOspfv3ControlTimeStamp, sleOspfv3ControlReqResult, sleOspfv3ControlRestartPeriod }
			STATUS current
			DESCRIPTION 
				"restartOspfv3Graceful"
			::= { sleOspfv3BaseNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleOspfv3SnmpNotificationiChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ControlRequest, sleOspfv3ControlTimeStamp, sleOspfv3ControlReqResult, sleOspfv3SnmpNotification }
			STATUS current
			DESCRIPTION 
				"setSnmpNotification"
			::= { sleOspfv3BaseNotification 5 }

		
		-- *******.4.1.629**********
		sleOspfv3Proc OBJECT IDENTIFIER ::= { sleOSPFv3 2 }

		
		-- *******.4.1.6296.**********
		sleOspfv3ProcInfo OBJECT IDENTIFIER ::= { sleOspfv3Proc 1 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3ProcInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3ProcInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfo 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleOspfv3ProcInfoEntry OBJECT-TYPE
			SYNTAX SleOspfv3ProcInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex }
			::= { sleOspfv3ProcInfoTable 1 }

		
		SleOspfv3ProcInfoEntry ::=
			SEQUENCE { 
				sleOspfv3ProcIndex
					INTEGER,
				sleOspfv3ProcTag
					OCTET STRING,
				sleOspfv3ProcRouterId
					InetAddressIPv4,
				sleOspfv3ProcSpfDelayTime
					INTEGER,
				sleOspfv3ProcSpfHoldTime
					INTEGER,
				sleOspfv3ProcAutoCost
					INTEGER,
				sleOspfv3ProcAbrType
					INTEGER,
				sleOspfv3ProcDefaultMetric
					INTEGER,
				sleOspfv3ProcMaxConcurrentDD
					INTEGER,
				sleOspfv3ProcDefaultOriginType
					INTEGER,
				sleOspfv3ProcDefaultOriginMetricType
					INTEGER,
				sleOspfv3ProcDefaultOriginMetric
					INTEGER,
				sleOspfv3ProcDefaultOriginRouteMap
					OCTET STRING,
				sleOspfv3ProcLogNeighborChange
					INTEGER,
				sleOspfv3ProcBfdAllIf
					INTEGER,
				sleOspfv3ProcEfmAllIf
					INTEGER,
				sleOspfv3ProcVRIndex
					INTEGER,
				sleOspfv3ProcVRFName
					OCTET STRING,
				sleOspfv3ProcSPFStartDelay
					INTEGER,
				sleOspfv3ProcSPFMinDelay
					INTEGER,
				sleOspfv3ProcSPFMaxDelay
					INTEGER,
				sleOspfv3ProcLSAStartDelay
					INTEGER,
				sleOspfv3ProcLSAMinDelay
					INTEGER,
				sleOspfv3ProcLSAMaxDelay
					INTEGER,
				sleOspfv3ProcLSAArrivalDelay
					INTEGER
			 }

		-- *******.4.1.629**********.*******
		sleOspfv3ProcIndex OBJECT-TYPE
			SYNTAX INTEGER (1..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3ProcInfoEntry 1 }

		
		-- *******.4.1.629**********.*******
		sleOspfv3ProcTag OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Tag name of OSPFv3 Process."
			::= { sleOspfv3ProcInfoEntry 2 }

		
		-- *******.4.1.629**********.*******
		sleOspfv3ProcRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Router-Id in IPv4 Address format of this OSPFv3 Process.
				The size of 0 means that this process has not router-id.
				
				"
			::= { sleOspfv3ProcInfoEntry 3 }

		
		-- *******.4.1.629**********.*******
		sleOspfv3ProcSpfDelayTime OBJECT-TYPE
			SYNTAX INTEGER (0..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"SPF Delay-Time of this OSPFv3 Process.
				The value of -1 means default delay-time.
				"
			::= { sleOspfv3ProcInfoEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleOspfv3ProcSpfHoldTime OBJECT-TYPE
			SYNTAX INTEGER (0..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"SPF Hold-Time of this OSPFv3 Process.
				The value of -1 means default hold-time.
				"
			::= { sleOspfv3ProcInfoEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleOspfv3ProcAutoCost OBJECT-TYPE
			SYNTAX INTEGER (1..4294967)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"OSPFv3 Interface cost calculated according to bandwidth.
				The value of 0 means default auto-cost.
				"
			::= { sleOspfv3ProcInfoEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleOspfv3ProcAbrType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				standard(1),
				cisco(2),
				ibm(3),
				shortcut(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ABR type of this OSPFv3 Process.
				Default value is 'cisco'.
				"
			::= { sleOspfv3ProcInfoEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleOspfv3ProcDefaultMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16777214)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfoEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleOspfv3ProcMaxConcurrentDD OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Maximum number allowed to process DD concurrently in this OSPFv3 Process.
				"
			::= { sleOspfv3ProcInfoEntry 9 }

		
		-- *******.4.1.629**********.*******0
		sleOspfv3ProcDefaultOriginType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				normal(1),
				always(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If or not Always advertise default route.
				
				"
			::= { sleOspfv3ProcInfoEntry 10 }

		
		-- *******.4.1.629**********.*******1
		sleOspfv3ProcDefaultOriginMetricType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				external1(1),
				external2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Metric type for default route in this OSPFv3 Process."
			::= { sleOspfv3ProcInfoEntry 11 }

		
		-- *******.4.1.629**********.*******2
		sleOspfv3ProcDefaultOriginMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16777215)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Metric value for default route in this OSPFv3 Process."
			::= { sleOspfv3ProcInfoEntry 12 }

		
		-- *******.4.1.629**********.*******3
		sleOspfv3ProcDefaultOriginRouteMap OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Route-Map referenced by default route in this OSPFv3 Process.
				The size of 0 means that any route-map is not referenced.
				"
			::= { sleOspfv3ProcInfoEntry 13 }

		
		-- *******.4.1.629**********.*******4
		sleOspfv3ProcLogNeighborChange OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfoEntry 14 }

		
		-- *******.4.1.629**********.*******5
		sleOspfv3ProcBfdAllIf OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If or not use bfd in all the interface refered by this process."
			::= { sleOspfv3ProcInfoEntry 15 }

		
		-- *******.4.1.629**********.*******6
		sleOspfv3ProcEfmAllIf OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If or not use efm-oam in all the interface refered by this process."
			::= { sleOspfv3ProcInfoEntry 16 }

		
		-- *******.4.1.629**********.*******7
		sleOspfv3ProcVRIndex OBJECT-TYPE
			SYNTAX INTEGER (1..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VR Index of OSPFv3 Process."
			::= { sleOspfv3ProcInfoEntry 17 }

		
		-- *******.4.1.629**********.*******8
		sleOspfv3ProcVRFName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VRF Name of OSPFv3 Process."
			::= { sleOspfv3ProcInfoEntry 18 }

		
		-- *******.4.1.629**********.*******9
		sleOspfv3ProcSPFStartDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"
				"
			::= { sleOspfv3ProcInfoEntry 19 }

		
		-- *******.4.1.629**********.*******0
		sleOspfv3ProcSPFMinDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"SPF Throttle Min Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoEntry 20 }

		
		-- *******.4.1.629**********.*******1
		sleOspfv3ProcSPFMaxDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"SPF Throttle Max Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoEntry 21 }

		
		-- *******.4.1.629**********.*******2
		sleOspfv3ProcLSAStartDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"LSA Throttle Start Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoEntry 22 }

		
		-- *******.4.1.629**********.*******3
		sleOspfv3ProcLSAMinDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"LSA Throttle Min Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoEntry 23 }

		
		-- *******.4.1.629**********.*******4
		sleOspfv3ProcLSAMaxDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"LSA Throttle Max Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoEntry 24 }

		
		-- *******.4.1.629**********.*******5
		sleOspfv3ProcLSAArrivalDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"LSA Arrival Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoEntry 25 }

		
		-- *******.4.1.6296.**********.2
		sleOspfv3ProcInfoControl OBJECT IDENTIFIER ::= { sleOspfv3ProcInfo 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleOspfv3ProcControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOspfv3Proc(1),
				setOspfv3Proc(2),
				destroyOspfv3Proc(3),
				clearOspfv3Proc(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfoControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleOspfv3ProcControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfoControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleOspfv3ProcControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfoControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleOspfv3ProcControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfoControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleOspfv3ProcControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcInfoControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleOspfv3ProcControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3ProcInfoControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleOspfv3ProcControlTag OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Tag name of OSPFv3 Process."
			::= { sleOspfv3ProcInfoControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleOspfv3ProcControlRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Router-Id in IPv4 Address format of this OSPFv3 Process.
				The size of 0 means that this process has not router-id."
			::= { sleOspfv3ProcInfoControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleOspfv3ProcControlSpfDelayTime OBJECT-TYPE
			SYNTAX INTEGER (0..2147483647)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SPF Delay-Time of this OSPFv3 Process.
				The value of -1 means default delay-time."
			::= { sleOspfv3ProcInfoControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleOspfv3ProcControlSpfHoldTime OBJECT-TYPE
			SYNTAX INTEGER (0..2147483647)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SPF Hold-Time of this OSPFv3 Process.
				The value of -1 means default hold-time."
			::= { sleOspfv3ProcInfoControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleOspfv3ProcControlAutoCost OBJECT-TYPE
			SYNTAX INTEGER (1..4294967)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"OSPFv3 Interface cost calculated according to bandwidth.
				The value of 0 means default auto-cost."
			::= { sleOspfv3ProcInfoControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleOspfv3ProcControlAbrType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				standard(1),
				cisco(2),
				ibm(3),
				shortcut(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ABR type of this OSPFv3 Process.
				Default value is 'cisco'."
			::= { sleOspfv3ProcInfoControl 12 }

		
		-- *******.4.1.6296.**********.2.13
		sleOspfv3ProcControlDefaultMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16777214)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Default Metric value of this OSPFv3 Process.
				The value of -1 means default metric value."
			::= { sleOspfv3ProcInfoControl 13 }

		
		-- *******.4.1.6296.**********.2.14
		sleOspfv3ProcControlMaxConcurrentDD OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Maximum number allowed to process DD concurrently in this OSPFv3 Process."
			::= { sleOspfv3ProcInfoControl 14 }

		
		-- *******.4.1.6296.**********.2.15
		sleOspfv3ProcControlDefaultOriginType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				normal(1),
				always(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If or not Always advertise default route."
			::= { sleOspfv3ProcInfoControl 15 }

		
		-- *******.4.1.6296.**********.2.16
		sleOspfv3ProcControlDefaultOriginMetricType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				external1(1),
				external2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Metric type for default route in this OSPFv3 Process.
				"
			::= { sleOspfv3ProcInfoControl 16 }

		
		-- *******.4.1.6296.**********.2.17
		sleOspfv3ProcControlDefaultOriginMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16777215)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Metric value for default route in this OSPFv3 Process."
			::= { sleOspfv3ProcInfoControl 17 }

		
		-- *******.4.1.6296.**********.2.18
		sleOspfv3ProcControlDefaultOriginRouteMap OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Route-Map referenced by default route in this OSPFv3 Process.
				The size of 0 means that any route-map is not referenced."
			::= { sleOspfv3ProcInfoControl 18 }

		
		-- *******.4.1.6296.**********.2.19
		sleOspfv3ProcControlLogNeighborChange OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If or not logging neigbour-state change and reset reason."
			::= { sleOspfv3ProcInfoControl 19 }

		
		-- *******.4.1.6296.**********.2.20
		sleOspfv3ProcControlBfdAllIf OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If or not use bfd in all the interface refered by this process."
			::= { sleOspfv3ProcInfoControl 20 }

		
		-- *******.4.1.6296.**********.2.21
		sleOspfv3ProcControlEfmAllIf OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If or not use efm-oam in all the interface refered by this process."
			::= { sleOspfv3ProcInfoControl 21 }

		
		-- *******.4.1.6296.**********.2.22
		sleOspfv3ProcControlVRIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VR Index of OSPFv3 Process."
			::= { sleOspfv3ProcInfoControl 22 }

		
		-- *******.4.1.6296.**********.2.23
		sleOspfv3ProcControlVRFName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VRF Name of OSPFv3 Process."
			::= { sleOspfv3ProcInfoControl 23 }

		
		-- *******.4.1.6296.**********.2.24
		sleOspfv3ProcControlSPFStartDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SPF Throttle Start Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoControl 24 }

		
		-- *******.4.1.6296.**********.2.25
		sleOspfv3ProcControlSPFMinDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SPF Throttle Min Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoControl 25 }

		
		-- *******.4.1.6296.**********.2.26
		sleOspfv3ProcControlSPFMaxDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SPF Throttle Max Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoControl 26 }

		
		-- *******.4.1.6296.**********.2.27
		sleOspfv3ProcControlLSAStartDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LSA Throttle Start Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoControl 27 }

		
		-- *******.4.1.6296.**********.2.28
		sleOspfv3ProcControlLSAMinDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LSA Throttle Min Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoControl 28 }

		
		-- *******.4.1.6296.**********.2.29
		sleOspfv3ProcControlLSAMaxDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LSA Throttle Max Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoControl 29 }

		
		-- *******.4.1.6296.**********.2.30
		sleOspfv3ProcControlLSAArrivalDelay OBJECT-TYPE
			SYNTAX INTEGER (0..600000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LSA Arrival Delay of OSPFv3 Timer"
			::= { sleOspfv3ProcInfoControl 30 }

		
		-- *******.4.1.6296.**********.3
		sleOspfv3ProcInfoNotification OBJECT IDENTIFIER ::= { sleOspfv3ProcInfo 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleOspfv3ProcCreated NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcControlRequest, sleOspfv3ProcControlTimeStamp, sleOspfv3ProcControlReqResult, sleOspfv3ProcTag, sleOspfv3ProcVRIndex, 
				sleOspfv3ProcVRFName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcInfoNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleOspfv3ProcProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcControlRequest, sleOspfv3ProcControlTimeStamp, sleOspfv3ProcControlReqResult, sleOspfv3ProcSPFStartDelay, sleOspfv3ProcSPFMinDelay, 
				sleOspfv3ProcSPFMaxDelay, sleOspfv3ProcLSAStartDelay, sleOspfv3ProcLSAMinDelay, sleOspfv3ProcLSAMaxDelay, sleOspfv3ProcLSAArrivalDelay, 
				sleOspfv3ProcRouterId, sleOspfv3ProcSpfDelayTime, sleOspfv3ProcSpfHoldTime, sleOspfv3ProcAutoCost, sleOspfv3ProcAbrType, 
				sleOspfv3ProcDefaultMetric, sleOspfv3ProcMaxConcurrentDD, sleOspfv3ProcDefaultOriginType, sleOspfv3ProcDefaultOriginMetricType, sleOspfv3ProcDefaultOriginMetric, 
				sleOspfv3ProcDefaultOriginRouteMap, sleOspfv3ProcLogNeighborChange, sleOspfv3ProcBfdAllIf, sleOspfv3ProcEfmAllIf }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcInfoNotification 2 }

		
		-- *******.4.1.6296.**********.3.3
		sleOspfv3ProcDestroyed NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcControlRequest, sleOspfv3ProcControlTimeStamp, sleOspfv3ProcControlReqResult, sleOspfv3ProcControlIndex, sleOspfv3ProcControlVRIndex
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcInfoNotification 3 }

		
		-- *******.4.1.6296.**********.3.4
		sleOspfv3ProcCleared NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcControlRequest, sleOspfv3ProcControlTimeStamp, sleOspfv3ProcControlReqResult, sleOspfv3ProcControlIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcInfoNotification 4 }

		
		-- *******.4.1.629**********.2
		sleOspfv3ProcSummary OBJECT IDENTIFIER ::= { sleOspfv3Proc 2 }

		
		-- *******.4.1.629**********.2.1
		sleOspfv3ProcSummaryTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3ProcSummaryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcSummary 1 }

		
		-- *******.4.1.629**********.2.1.1
		sleOspfv3ProcSummaryEntry OBJECT-TYPE
			SYNTAX SleOspfv3ProcSummaryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3ProcSummaryAddress, sleOspfv3ProcSummaryMask }
			::= { sleOspfv3ProcSummaryTable 1 }

		
		SleOspfv3ProcSummaryEntry ::=
			SEQUENCE { 
				sleOspfv3ProcSummaryAddress
					InetAddressIPv6,
				sleOspfv3ProcSummaryMask
					INTEGER,
				sleOspfv3ProcSummaryTag
					Gauge32,
				sleOspfv3ProcSummaryAdvertiseFlag
					INTEGER
			 }

		-- *******.4.1.629**********.*******
		sleOspfv3ProcSummaryAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IPv6 Address summaries."
			::= { sleOspfv3ProcSummaryEntry 1 }

		
		-- *******.4.1.629**********.*******
		sleOspfv3ProcSummaryMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IPv6 Address summaries'mask length."
			::= { sleOspfv3ProcSummaryEntry 2 }

		
		-- *******.4.1.629**********.*******
		sleOspfv3ProcSummaryTag OBJECT-TYPE
			SYNTAX Gauge32 (0..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IPv6 Address summaries'tag value."
			::= { sleOspfv3ProcSummaryEntry 3 }

		
		-- *******.4.1.629**********.2.1.1.4
		sleOspfv3ProcSummaryAdvertiseFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				notAdvertise(0),
				tag(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use suppress routes or tag."
			::= { sleOspfv3ProcSummaryEntry 4 }

		
		-- *******.4.1.629**********.2.2
		sleOspfv3rocSummaryControl OBJECT IDENTIFIER ::= { sleOspfv3ProcSummary 2 }

		
		-- *******.4.1.629**********.2.2.1
		sleOspfv3ProcSummaryControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOpsfv3ProcSummary(1),
				modifyOspfv3ProcSummary(2),
				delstroyOspfv3ProcSummary(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3rocSummaryControl 1 }

		
		-- *******.4.1.629**********.2.2.2
		sleOspfv3ProcSummaryControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3rocSummaryControl 2 }

		
		-- *******.4.1.629**********.2.2.3
		sleOspfv3ProcSummaryControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3rocSummaryControl 3 }

		
		-- *******.4.1.629**********.2.2.4
		sleOspfv3ProcSummaryControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3rocSummaryControl 4 }

		
		-- *******.4.1.629**********.2.2.5
		sleOspfv3ProcSummaryControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3rocSummaryControl 5 }

		
		-- *******.4.1.629**********.2.2.6
		sleOspfv3ProcSummaryControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3rocSummaryControl 6 }

		
		-- *******.4.1.629**********.2.2.7
		sleOspfv3ProcSummaryControlAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IPv6 Address summaries."
			::= { sleOspfv3rocSummaryControl 7 }

		
		-- *******.4.1.629**********.2.2.8
		sleOspfv3ProcSummaryControlMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IPv6 Address summaries'mask length."
			::= { sleOspfv3rocSummaryControl 8 }

		
		-- *******.4.1.629**********.2.2.9
		sleOspfv3ProcSummaryControlTag OBJECT-TYPE
			SYNTAX Gauge32 (0..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IPv6 Address summaries'tag value."
			::= { sleOspfv3rocSummaryControl 9 }

		
		-- *******.4.1.629**********.2.2.10
		sleOspfv3ProcSummaryControlAdvertiseFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				notAdvertise(0),
				tag(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use suppress routes or tag."
			::= { sleOspfv3rocSummaryControl 10 }

		
		-- *******.4.1.629**********.2.3
		sleOspfv3ProcSummaryNotification OBJECT IDENTIFIER ::= { sleOspfv3ProcSummary 3 }

		
		-- *******.4.1.629**********.2.3.1
		sleOspfv3ProcSummaryCreated NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcSummaryControlRequest, sleOspfv3ProcSummaryControlTimeStamp, sleOspfv3ProcSummaryControlReqResult, sleOspfv3ProcSummaryTag, sleOspfv3ProcSummaryAdvertiseFlag
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcSummaryNotification 1 }

		
		-- *******.4.1.629**********.2.3.2
		sleOspfv3ProcSummaryChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcSummaryControlRequest, sleOspfv3ProcSummaryControlTimeStamp, sleOspfv3ProcSummaryControlReqResult, sleOspfv3ProcSummaryTag, sleOspfv3ProcSummaryAdvertiseFlag
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcSummaryNotification 2 }

		
		-- *******.4.1.629**********.2.3.3
		sleOspfv3ProcSummaryDestroyed NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcSummaryControlRequest, sleOspfv3ProcSummaryControlTimeStamp, sleOspfv3ProcSummaryControlReqResult, sleOspfv3ProcSummaryControlIndex, sleOspfv3ProcSummaryControlAddress, 
				sleOspfv3ProcSummaryControlMask }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcSummaryNotification 3 }

		
		-- *******.4.1.629**********.3
		sleOspfv3ProcPassiveIf OBJECT IDENTIFIER ::= { sleOspfv3Proc 3 }

		
		-- *******.4.1.629**********.3.1
		sleOspfv3ProcPassiveIfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3ProcPassiveIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcPassiveIf 1 }

		
		-- *******.4.1.629**********.3.1.1
		sleOspfv3ProcPassiveIfEntry OBJECT-TYPE
			SYNTAX SleOspfv3ProcPassiveIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3ProcPassiveIfIndex }
			::= { sleOspfv3ProcPassiveIfTable 1 }

		
		SleOspfv3ProcPassiveIfEntry ::=
			SEQUENCE { 
				sleOspfv3ProcPassiveIfIndex
					INTEGER
			 }

		-- *******.4.1.629**********.*******
		sleOspfv3ProcPassiveIfIndex OBJECT-TYPE
			SYNTAX INTEGER (0..4097)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of Passive Interface in this OSPFv3 Process.
				"
			::= { sleOspfv3ProcPassiveIfEntry 1 }

		
		-- *******.4.1.629**********.3.2
		sleOspfv3ProcPassIfControl OBJECT IDENTIFIER ::= { sleOspfv3ProcPassiveIf 2 }

		
		-- *******.4.1.629**********.3.2.1
		sleOspfv3ProcPassiveIfControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				addOspfv3ProcPassiveIf(1),
				deleteOspfv3ProcPassiveIf(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcPassIfControl 1 }

		
		-- *******.4.1.629**********.3.2.2
		sleOspfv3ProcPassiveIfControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcPassIfControl 2 }

		
		-- *******.4.1.629**********.3.2.3
		sleOspfv3ProcPassiveIfControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcPassIfControl 3 }

		
		-- *******.4.1.629**********.3.2.4
		sleOspfv3ProcPassiveIfControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcPassIfControl 4 }

		
		-- *******.4.1.629**********.3.2.5
		sleOspfv3ProcPassiveIfControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcPassIfControl 5 }

		
		-- *******.4.1.629**********.3.2.6
		sleOspfv3ProcPassiveIfControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3ProcPassIfControl 6 }

		
		-- *******.4.1.629**********.3.2.7
		sleOspfv3ProcPassiveIfControlIfIndex OBJECT-TYPE
			SYNTAX INTEGER (0..4097)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index of Passive Interface in this OSPFv3 Process.
				"
			::= { sleOspfv3ProcPassIfControl 7 }

		
		-- *******.4.1.629**********.3.3
		sleOspfv3ProcPassIfNotification OBJECT IDENTIFIER ::= { sleOspfv3ProcPassiveIf 3 }

		
		-- *******.4.1.629**********.3.3.1
		sleOspfv3ProcPassiveIfAdded NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcPassiveIfControlRequest, sleOspfv3ProcPassiveIfControlTimeStamp, sleOspfv3ProcPassiveIfControlReqResult, sleOspfv3ProcPassiveIfIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcPassIfNotification 1 }

		
		-- *******.4.1.629**********.3.3.2
		sleOspfv3ProcPassiveIfDeleted NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcPassiveIfControlRequest, sleOspfv3ProcPassiveIfControlTimeStamp, sleOspfv3ProcPassiveIfControlReqResult, sleOspfv3ProcPassiveIfControlIndex, sleOspfv3ProcPassiveIfControlIfIndex
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcPassIfNotification 2 }

		
		-- *******.4.1.629**********.4
		sleOspfv3ProcRedist OBJECT IDENTIFIER ::= { sleOspfv3Proc 4 }

		
		-- *******.4.1.629**********.4.1
		sleOspfv3ProcRedistTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3ProcRedistEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcRedist 1 }

		
		-- *******.4.1.629**********.4.1.1
		sleOspfv3ProcRedistEntry OBJECT-TYPE
			SYNTAX SleOspfv3ProcRedistEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3ProcRedistType }
			::= { sleOspfv3ProcRedistTable 1 }

		
		SleOspfv3ProcRedistEntry ::=
			SEQUENCE { 
				sleOspfv3ProcRedistType
					INTEGER,
				sleOspfv3ProcRedistMetricType
					INTEGER,
				sleOspfv3ProcRedistMetric
					INTEGER,
				sleOSpfv3ProcRedistRouteMapName
					OCTET STRING
			 }

		-- *******.4.1.629**********.4.1.1.1
		sleOspfv3ProcRedistType OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				isis(3),
				static(4),
				rip(5),
				bgp(6)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Metric type for redistributed routes."
			::= { sleOspfv3ProcRedistEntry 1 }

		
		-- *******.4.1.629**********.4.1.1.2
		sleOspfv3ProcRedistMetricType OBJECT-TYPE
			SYNTAX INTEGER
				{
				external1(1),
				external2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Metric type for redistributed routes."
			::= { sleOspfv3ProcRedistEntry 2 }

		
		-- *******.4.1.629**********.4.1.1.3
		sleOspfv3ProcRedistMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16777215)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Default Metric value of redistributed routes."
			::= { sleOspfv3ProcRedistEntry 3 }

		
		-- *******.4.1.629**********.4.1.1.4
		sleOSpfv3ProcRedistRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Route-map name refered by this.
				The size of 0 means that it has not refered rout-map.
				"
			::= { sleOspfv3ProcRedistEntry 4 }

		
		-- *******.4.1.629**********.4.2
		sleOspfv3ProcRedistControl OBJECT IDENTIFIER ::= { sleOspfv3ProcRedist 2 }

		
		-- *******.4.1.629**********.4.2.1
		sleOspfv3ProcRedistControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOspfv3ProcRedist(1),
				modifyOspfv3ProcRedist(2),
				destroyOspfv3ProcRedist(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcRedistControl 1 }

		
		-- *******.4.1.629**********.4.2.2
		sleOspfv3ProcRedistControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcRedistControl 2 }

		
		-- *******.4.1.629**********.4.2.3
		sleOspfv3ProcRedistControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcRedistControl 3 }

		
		-- *******.4.1.629**********.4.2.4
		sleOspfv3ProcRedistControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcRedistControl 4 }

		
		-- *******.4.1.629**********.4.2.5
		sleOspfv3ProcRedistControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcRedistControl 5 }

		
		-- *******.4.1.629**********.4.2.6
		sleOspfv3ProcRedistControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3ProcRedistControl 6 }

		
		-- *******.4.1.629**********.4.2.7
		sleOspfv3ProcRedistControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				isis(3),
				static(4),
				rip(5),
				bgp(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Metric type for redistributed routes."
			::= { sleOspfv3ProcRedistControl 7 }

		
		-- *******.4.1.629**********.4.2.8
		sleOspfv3ProcRedistControlMetricType OBJECT-TYPE
			SYNTAX INTEGER
				{
				external1(1),
				external2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Metric type for redistributed routes."
			::= { sleOspfv3ProcRedistControl 8 }

		
		-- *******.4.1.629**********.4.2.9
		sleOspfv3ProcRedistControlMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16777215)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Default Metric value of redistributed routes."
			::= { sleOspfv3ProcRedistControl 9 }

		
		-- *******.4.1.629**********.4.2.10
		sleOSpfv3ProcRedistControlRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Route-map name refered by this.
				The size of 0 means that it has not refered rout-map.
				"
			::= { sleOspfv3ProcRedistControl 10 }

		
		-- *******.4.1.629**********.4.3
		sleOspfv3ProcRedistNotification OBJECT IDENTIFIER ::= { sleOspfv3ProcRedist 3 }

		
		-- *******.4.1.629**********.4.3.1
		sleOspfv3ProcRedistCreated NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcRedistControlRequest, sleOspfv3ProcRedistControlTimeStamp, sleOspfv3ProcRedistControlReqResult, sleOspfv3ProcRedistMetricType, sleOspfv3ProcRedistMetric, 
				sleOSpfv3ProcRedistRouteMapName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcRedistNotification 1 }

		
		-- *******.4.1.629**********.4.3.2
		sleOspfv3ProcRedistChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcRedistControlRequest, sleOspfv3ProcRedistControlTimeStamp, sleOspfv3ProcRedistControlReqResult, sleOspfv3ProcRedistMetricType, sleOspfv3ProcRedistMetric, 
				sleOSpfv3ProcRedistRouteMapName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcRedistNotification 2 }

		
		-- *******.4.1.629**********.4.3.3
		sleOspfv3ProcRedistDestroyed NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcRedistControlRequest, sleOspfv3ProcRedistControlTimeStamp, sleOspfv3ProcRedistControlReqResult, sleOspfv3ProcRedistControlIndex, sleOspfv3ProcRedistControlType
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcRedistNotification 3 }

		
		-- *******.4.1.6296.**********
		sleOspfv3ProcArea OBJECT IDENTIFIER ::= { sleOspfv3Proc 5 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3ProcAreaInfo OBJECT IDENTIFIER ::= { sleOspfv3ProcArea 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleOspfv3ProcAreaInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3ProcAreaInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaInfo 1 }

		
		-- *******.4.1.6296.**********.1.1.1
		sleOspfv3ProcAreaInfoEntry OBJECT-TYPE
			SYNTAX SleOspfv3ProcAreaInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3ProcAreaInfoIndex }
			::= { sleOspfv3ProcAreaInfoTable 1 }

		
		SleOspfv3ProcAreaInfoEntry ::=
			SEQUENCE { 
				sleOspfv3ProcAreaInfoIndex
					IpAddress,
				sleOspfv3ProcAreaInfoType
					INTEGER,
				sleOspfv3ProcAreaInfoDefaultCost
					INTEGER,
				sleOspfv3ProcAreaInfoSummary
					INTEGER
			 }

		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaInfoIndex OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Area ID in decimal."
			::= { sleOspfv3ProcAreaInfoEntry 1 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaInfoType OBJECT-TYPE
			SYNTAX INTEGER
				{
				general(1),
				stub(2),
				nssa(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Area Type."
			::= { sleOspfv3ProcAreaInfoEntry 2 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaInfoDefaultCost OBJECT-TYPE
			SYNTAX INTEGER (0..16777215)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Default cost of area. It is meaning only if area is a NSSA or stub.
				The value of -1 that this area has not significant default cost.
				"
			::= { sleOspfv3ProcAreaInfoEntry 3 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaInfoSummary OBJECT-TYPE
			SYNTAX INTEGER
				{
				noAreaSummary(1),
				sendAreaSummary(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The controls the  import of summary LSAs into stub areas. 
				It has no effect on other areas. 
				If it is noAreaSummary, the router will neither originate  
				nor propagate summary LSAs into the stub area.  
				It will rely entirely on its default route. 
				If it is sendAreaSummary, the router will both summarize and propagate summary LSAs.
				"
			::= { sleOspfv3ProcAreaInfoEntry 4 }

		
		-- *******.4.1.6296.**********.1.2
		sleOspfv3ProcAreaInfoControl OBJECT IDENTIFIER ::= { sleOspfv3ProcAreaInfo 2 }

		
		-- *******.4.1.6296.**********.1.2.1
		sleOspfv3ProcAreaInfoControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOspfv3ProcAreaInfo(1),
				modifyOspfv3ProcAreaInfo(2),
				destroyOspfv3ProcAreaInfo(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaInfoControl 1 }

		
		-- *******.4.1.6296.**********.1.2.2
		sleOspfv3ProcAreaInfoControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaInfoControl 2 }

		
		-- *******.4.1.6296.**********.1.2.3
		sleOspfv3ProcAreaInfoControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaInfoControl 3 }

		
		-- *******.4.1.6296.**********.1.2.4
		sleOspfv3ProcAreaInfoControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaInfoControl 4 }

		
		-- *******.4.1.6296.**********.1.2.5
		sleOspfv3ProcAreaInfoControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaInfoControl 5 }

		
		-- *******.4.1.6296.**********.1.2.6
		sleOspfv3ProcAreaInfoControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3ProcAreaInfoControl 6 }

		
		-- *******.4.1.6296.**********.1.2.7
		sleOspfv3ProcAreaInfoControlAreaIndex OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Area ID in decimal."
			::= { sleOspfv3ProcAreaInfoControl 7 }

		
		-- *******.4.1.6296.**********.1.2.8
		sleOspfv3ProcAreaInfoControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				general(1),
				stub(2),
				nssa(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Area Type."
			::= { sleOspfv3ProcAreaInfoControl 8 }

		
		-- *******.4.1.6296.**********.1.2.9
		sleOspfv3ProcAreaInfoControlDefaultCost OBJECT-TYPE
			SYNTAX INTEGER (0..16777215)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Area Type."
			::= { sleOspfv3ProcAreaInfoControl 9 }

		
		-- *******.4.1.6296.**********.1.2.10
		sleOspfv3ProcAreaInfoControlSummary OBJECT-TYPE
			SYNTAX INTEGER
				{
				noAreaSummary(1),
				sendAreaSummary(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The controls the  import of summary LSAs into stub areas. 
				It has no effect on other areas. 
				If it is noAreaSummary, the router will neither originate  
				nor propagate summary LSAs into the stub area.  
				It will rely entirely on its default route. 
				If it is sendAreaSummary, the router will both summarize and propagate summary LSAs.
				"
			::= { sleOspfv3ProcAreaInfoControl 10 }

		
		-- *******.4.1.6296.**********.1.3
		sleOspfv3ProcAreaInfoNotification OBJECT IDENTIFIER ::= { sleOspfv3ProcAreaInfo 3 }

		
		-- *******.4.1.6296.**********.1.3.1
		sleOspfv3ProcAreaInfoCreated NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaInfoControlRequest, sleOspfv3ProcAreaInfoControlTimeStamp, sleOspfv3ProcAreaInfoControlReqResult, sleOspfv3ProcAreaInfoType, sleOspfv3ProcAreaInfoDefaultCost, 
				sleOspfv3ProcAreaInfoSummary }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaInfoNotification 1 }

		
		-- *******.4.1.6296.**********.1.3.2
		sleOspfv3ProcAreaInfoChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaInfoControlRequest, sleOspfv3ProcAreaInfoControlTimeStamp, sleOspfv3ProcAreaInfoControlReqResult, sleOspfv3ProcAreaInfoType, sleOspfv3ProcAreaInfoDefaultCost, 
				sleOspfv3ProcAreaInfoSummary }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaInfoNotification 2 }

		
		-- *******.4.1.6296.**********.1.3.3
		sleOspfv3ProcAreaInfoDestroyed NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaInfoControlRequest, sleOspfv3ProcAreaInfoControlTimeStamp, sleOspfv3ProcAreaInfoControlReqResult, sleOspfv3ProcAreaInfoControlIndex, sleOspfv3ProcAreaInfoControlAreaIndex
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaInfoNotification 3 }

		
		-- *******.4.1.6296.**********.2
		sleOspfv3ProcAreaRange OBJECT IDENTIFIER ::= { sleOspfv3ProcArea 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleOspfv3ProcAreaRangeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3ProcAreaRangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRange 1 }

		
		-- *******.4.1.6296.**********.2.1.1
		sleOspfv3ProcAreaRangeEntry OBJECT-TYPE
			SYNTAX SleOspfv3ProcAreaRangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3ProcAreaInfoIndex, sleOspfv3ProcAreaRangeAddress, sleOspfv3ProcAreaRangeMask }
			::= { sleOspfv3ProcAreaRangeTable 1 }

		
		SleOspfv3ProcAreaRangeEntry ::=
			SEQUENCE { 
				sleOspfv3ProcAreaRangeAddress
					InetAddressIPv6,
				sleOspfv3ProcAreaRangeMask
					INTEGER,
				sleOspfv3ProcAreaRangeAdvertiseFlag
					INTEGER
			 }

		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaRangeAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IPv6 Address of range.
				
				"
			::= { sleOspfv3ProcAreaRangeEntry 1 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaRangeMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IPv6 Address of range.
				
				"
			::= { sleOspfv3ProcAreaRangeEntry 2 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaRangeAdvertiseFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				notAdvertise(0),
				advertise(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Advertise or not this range of area."
			::= { sleOspfv3ProcAreaRangeEntry 3 }

		
		-- *******.4.1.6296.**********.2.2
		sleOspfv3ProcAreaRangeControl OBJECT IDENTIFIER ::= { sleOspfv3ProcAreaRange 2 }

		
		-- *******.4.1.6296.**********.2.2.1
		sleOspfv3ProcAreaRangeControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOspfv3ProcAreaRange(1),
				modifyOspfv3ProcAreaRange(2),
				destroyOspfv3ProcAreaRange(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRangeControl 1 }

		
		-- *******.4.1.6296.**********.2.2.2
		sleOspfv3ProcAreaRangeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRangeControl 2 }

		
		-- *******.4.1.6296.**********.2.2.3
		sleOspfv3ProcAreaRangeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRangeControl 3 }

		
		-- *******.4.1.6296.**********.2.2.4
		sleOspfv3ProcAreaRangeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRangeControl 4 }

		
		-- *******.4.1.6296.**********.2.2.5
		sleOspfv3ProcAreaRangeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3ProcAreaRangeControl 5 }

		
		-- *******.4.1.6296.**********.2.2.6
		sleOspfv3ProcAreaRangeControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRangeControl 6 }

		
		-- *******.4.1.6296.**********.2.2.7
		sleOspfv3ProcAreaRangeControlAreaIndex OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Area ID in decimal."
			::= { sleOspfv3ProcAreaRangeControl 7 }

		
		-- *******.4.1.6296.**********.2.2.8
		sleOspfv3ProcAreaRangeControlAddr OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IPv6 Address of range"
			::= { sleOspfv3ProcAreaRangeControl 8 }

		
		-- *******.4.1.6296.**********.2.2.9
		sleOspfv3ProcAreaRangeControlMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRangeControl 9 }

		
		-- *******.4.1.6296.**********.2.2.10
		sleOspfv3ProcAreaRangeControlAdvertiseFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				notAdvertise(0),
				advertise(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaRangeControl 10 }

		
		-- *******.4.1.6296.**********.2.3
		sleOspfv3ProcAreaRangeNotification OBJECT IDENTIFIER ::= { sleOspfv3ProcAreaRange 3 }

		
		-- *******.4.1.6296.**********.2.3.1
		sleOspfv3ProcAreaRangeCreated NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaRangeControlRequest, sleOspfv3ProcAreaRangeControlTimeStamp, sleOspfv3ProcAreaRangeControlReqResult, sleOspfv3ProcAreaRangeAdvertiseFlag }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaRangeNotification 1 }

		
		-- *******.4.1.6296.**********.2.3.2
		sleOspfv3ProcAreaRangeChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaRangeControlRequest, sleOspfv3ProcAreaRangeControlTimeStamp, sleOspfv3ProcAreaRangeControlReqResult, sleOspfv3ProcAreaRangeAdvertiseFlag }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaRangeNotification 2 }

		
		-- *******.4.1.6296.**********.2.3.3
		sleOspfv3ProcAreaRangeDestroyed NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaRangeControlRequest, sleOspfv3ProcAreaRangeControlTimeStamp, sleOspfv3ProcAreaRangeControlReqResult, sleOspfv3ProcAreaRangeControlIndex, sleOspfv3ProcAreaRangeControlAreaIndex, 
				sleOspfv3ProcAreaRangeControlMask, sleOspfv3ProcAreaRangeControlAddr }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaRangeNotification 3 }

		
		-- *******.4.1.6296.**********.3
		sleOspfv3ProcAreaVlink OBJECT IDENTIFIER ::= { sleOspfv3ProcArea 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleOspfv3ProcAreaVlinkTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3ProcAreaVlinkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaVlink 1 }

		
		-- *******.4.1.6296.**********.3.1.1
		sleOspfv3ProcAreaVlinkEntry OBJECT-TYPE
			SYNTAX SleOspfv3ProcAreaVlinkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3ProcAreaInfoIndex, sleOspfv3ProcAreaVlinkRouterId }
			::= { sleOspfv3ProcAreaVlinkTable 1 }

		
		SleOspfv3ProcAreaVlinkEntry ::=
			SEQUENCE { 
				sleOspfv3ProcAreaVlinkRouterId
					InetAddressIPv4,
				sleOspfv3ProcAreaVlinkDeadInterval
					INTEGER,
				sleOspfv3ProcAreaVlinkHelloInterval
					INTEGER,
				sleOspfv3ProcAreaVlinkInstanceId
					INTEGER,
				sleOspfv3ProcAreaVlinkRetransInterval
					INTEGER,
				sleOspfv3ProcAreaVlinkTransDelay
					INTEGER
			 }

		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaVlinkRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Router ID associated with this virtual-link neighbor.
				
				
				"
			::= { sleOspfv3ProcAreaVlinkEntry 1 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaVlinkDeadInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Dead router detection time in unit of second.
				The value of 0 means that this virtual-link has not specific dead-interval.
				
				
				"
			::= { sleOspfv3ProcAreaVlinkEntry 2 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaVlinkHelloInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interval between hello packet in unit of second.
				The value of 0 means that this virtual-link has not specific hello-interval.
				
				
				"
			::= { sleOspfv3ProcAreaVlinkEntry 3 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaVlinkInstanceId OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"OSPFv3 instance ID of this virtual-link.
				The value of 0 means unspecific setting.
				
				
				"
			::= { sleOspfv3ProcAreaVlinkEntry 4 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaVlinkRetransInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"LSA retransmit interval in unit of second.
				
				"
			::= { sleOspfv3ProcAreaVlinkEntry 5 }

		
		-- *******.4.1.6296.**********.*******
		sleOspfv3ProcAreaVlinkTransDelay OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"LSA transmission delay in unit of second.
				The value of 0 means that this virtual-link has not specific transmission-delay.
				
				
				"
			::= { sleOspfv3ProcAreaVlinkEntry 6 }

		
		-- *******.4.1.6296.**********.3.2
		sleOspfv3ProcAreaVlinkControl OBJECT IDENTIFIER ::= { sleOspfv3ProcAreaVlink 2 }

		
		-- *******.4.1.6296.**********.3.2.1
		sleOspfv3ProcAreaVlinkControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOspfv3ProcAreaVlink(1),
				modifyOspfv3ProcAreaVlink(2),
				destroyOspfv3ProcAreaVlink(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaVlinkControl 1 }

		
		-- *******.4.1.6296.**********.3.2.2
		sleOspfv3ProcAreaVlinkControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaVlinkControl 2 }

		
		-- *******.4.1.6296.**********.3.2.3
		sleOspfv3ProcAreaVlinkControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaVlinkControl 3 }

		
		-- *******.4.1.6296.**********.3.2.4
		sleOspfv3ProcAreaVlinkControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaVlinkControl 4 }

		
		-- *******.4.1.6296.**********.3.2.5
		sleOspfv3ProcAreaVlinkControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of OSPFv3 Process."
			::= { sleOspfv3ProcAreaVlinkControl 5 }

		
		-- *******.4.1.6296.**********.3.2.6
		sleOspfv3ProcAreaVlinkControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcAreaVlinkControl 6 }

		
		-- *******.4.1.6296.**********.3.2.7
		sleOspfv3ProcAreaVlinkControlAreaIndex OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Area ID in decimal."
			::= { sleOspfv3ProcAreaVlinkControl 7 }

		
		-- *******.4.1.6296.**********.3.2.8
		sleOspfv3ProcAreaVlinkControlRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Router ID associated with this virtual-link neighbor."
			::= { sleOspfv3ProcAreaVlinkControl 8 }

		
		-- *******.4.1.6296.**********.3.2.9
		sleOspfv3ProcAreaVlinkControlDeadInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Dead router detection time in unit of second.
				The value of 0 means that this virtual-link has not specific dead-interval.
				
				
				"
			::= { sleOspfv3ProcAreaVlinkControl 9 }

		
		-- *******.4.1.6296.**********.3.2.10
		sleOspfv3ProcAreaVlinkControlHelloInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interval between hello packet in unit of second.
				The value of 0 means that this virtual-link has not specific hello-interval.
				"
			::= { sleOspfv3ProcAreaVlinkControl 10 }

		
		-- *******.4.1.6296.**********.3.2.11
		sleOspfv3ProcAreaVlinkControlInstanceId OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"OSPFv3 instance ID of this virtual-link.
				The value of 0 means unspecific setting.
				
				
				
				"
			::= { sleOspfv3ProcAreaVlinkControl 11 }

		
		-- *******.4.1.6296.**********.3.2.12
		sleOspfv3ProcAreaVlinkControlRetransInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LSA retransmit interval in unit of second.
				The value of 0 means that this virtual-link has not specific retransmit-interval.
				
				
				"
			::= { sleOspfv3ProcAreaVlinkControl 12 }

		
		-- *******.4.1.6296.**********.3.2.13
		sleOspfv3ProcAreaVlinkControlTransDelay OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LSA transmission delay in unit of second.
				The value of 0 means that this virtual-link has not specific transmission-delay.
				"
			::= { sleOspfv3ProcAreaVlinkControl 13 }

		
		-- *******.4.1.6296.**********.3.3
		sleOspfv3ProcAreaVlinkNotification OBJECT IDENTIFIER ::= { sleOspfv3ProcAreaVlink 3 }

		
		-- *******.4.1.6296.**********.3.3.1
		sleOspfv3ProcAreaVlinkCreated NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaVlinkControlRequest, sleOspfv3ProcAreaVlinkControlTimeStamp, sleOspfv3ProcAreaVlinkControlReqResult, sleOspfv3ProcAreaVlinkDeadInterval, sleOspfv3ProcAreaVlinkHelloInterval, 
				sleOspfv3ProcAreaVlinkInstanceId, sleOspfv3ProcAreaVlinkRetransInterval, sleOspfv3ProcAreaVlinkTransDelay }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaVlinkNotification 1 }

		
		-- *******.4.1.6296.**********.3.3.2
		sleOspfv3ProcAreaVlinkChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaVlinkControlRequest, sleOspfv3ProcAreaVlinkControlTimeStamp, sleOspfv3ProcAreaVlinkControlReqResult, sleOspfv3ProcAreaVlinkDeadInterval, sleOspfv3ProcAreaVlinkHelloInterval, 
				sleOspfv3ProcAreaVlinkInstanceId, sleOspfv3ProcAreaVlinkRetransInterval, sleOspfv3ProcAreaVlinkTransDelay }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaVlinkNotification 2 }

		
		-- *******.4.1.6296.**********.3.3.3
		sleOspfv3ProcAreaVlinkDestroyed NOTIFICATION-TYPE
			OBJECTS { sleOspfv3ProcAreaVlinkControlRequest, sleOspfv3ProcAreaVlinkControlTimeStamp, sleOspfv3ProcAreaVlinkControlReqResult, sleOspfv3ProcAreaVlinkControlIndex, sleOspfv3ProcAreaVlinkControlAreaIndex, 
				sleOspfv3ProcAreaVlinkControlRouterId }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3ProcAreaVlinkNotification 3 }

		
		-- *******.4.1.6296.101.52.3
		sleOspfv3Interface OBJECT IDENTIFIER ::= { sleOSPFv3 3 }

		
		-- *******.4.1.6296.**********
		sleOspfv3IfInstance OBJECT IDENTIFIER ::= { sleOspfv3Interface 1 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3IfInstanceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3IfInstanceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstance 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleOspfv3IfInstanceEntry OBJECT-TYPE
			SYNTAX SleOspfv3IfInstanceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3IfInstanceIfIndex, sleOspfv3IfInstanceInstanceId }
			::= { sleOspfv3IfInstanceTable 1 }

		
		SleOspfv3IfInstanceEntry ::=
			SEQUENCE { 
				sleOspfv3IfInstanceIfIndex
					Integer32,
				sleOspfv3IfInstanceInstanceId
					Integer32,
				sleOspfv3IfInstanceProcTag
					OCTET STRING,
				sleOspfv3IfInstanceAreaFlag
					INTEGER,
				sleOspfv3IfInstanceAreaIndex
					IpAddress,
				sleOspfv3IfInstanceDeadInterval
					Integer32,
				sleOspfv3IfInstanceHelloInterval
					Integer32,
				sleOspfv3IfInstanceNetworkType
					INTEGER,
				sleOspfv3IfInstanceCost
					Integer32,
				sleOspfv3IfInstanceTransDelay
					Integer32,
				sleOspfv3IfInstanceRetransInterval
					Integer32,
				sleOspfv3IfInstancePriority
					Integer32,
				sleOspfv3IfInstanceBfd
					INTEGER,
				sleOspfv3IfInstanceEfm
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleOspfv3IfInstanceIfIndex OBJECT-TYPE
			SYNTAX Integer32 (0..4097)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleOspfv3IfInstanceInstanceId OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleOspfv3IfInstanceProcTag OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleOspfv3IfInstanceAreaFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				noArea(0),
				useArea(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleOspfv3IfInstanceAreaIndex OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleOspfv3IfInstanceDeadInterval OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleOspfv3IfInstanceHelloInterval OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleOspfv3IfInstanceNetworkType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				p2p(1),
				broadcast(2),
				nbma(3),
				p2mp(4),
				p2mpNbma(5),
				virtualLink(6),
				loopback(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleOspfv3IfInstanceCost OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 9 }

		
		-- *******.4.1.6296.**********.1.1.10
		sleOspfv3IfInstanceTransDelay OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 10 }

		
		-- *******.4.1.6296.**********.1.1.11
		sleOspfv3IfInstanceRetransInterval OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 11 }

		
		-- *******.4.1.6296.**********.1.1.12
		sleOspfv3IfInstancePriority OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 12 }

		
		-- *******.4.1.6296.**********.1.1.13
		sleOspfv3IfInstanceBfd OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 13 }

		
		-- *******.4.1.6296.**********.1.1.14
		sleOspfv3IfInstanceEfm OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfInstanceEntry 14 }

		
		-- *******.4.1.6296.**********.2
		sleOspfv3ProcIfInstanceControl OBJECT IDENTIFIER ::= { sleOspfv3IfInstance 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleOspfv3IfInstanceControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setOspfv3IfInstance(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleOspfv3IfInstanceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleOspfv3IfInstanceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleOspfv3IfInstanceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleOspfv3IfInstanceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleOspfv3IfInstanceControlIfIndex OBJECT-TYPE
			SYNTAX Integer32 (0..4097)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleOspfv3IfInstanceControlIfInstanceId OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleOspfv3IfInstanceControlProcTag OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleOspfv3IfInstanceControlAreaFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				noArea(0),
				useArea(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleOspfv3IfInstanceControlAreaIndex OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleOspfv3IfInstanceControlDeadInterval OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleOspfv3IfInstanceControlHelloInterval OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 12 }

		
		-- *******.4.1.6296.**********.2.13
		sleOspfv3IfInstanceControlNetworkType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				p2p(1),
				broadcast(2),
				nbma(3),
				p2mp(4),
				p2mpNbma(5),
				virtualLink(6),
				loopback(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 13 }

		
		-- *******.4.1.6296.**********.2.14
		sleOspfv3IfInstanceControlCost OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 14 }

		
		-- *******.4.1.6296.**********.2.15
		sleOspfv3IfInstanceControlTransDelay OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 15 }

		
		-- *******.4.1.6296.**********.2.16
		sleOspfv3IfInstanceControlRetransInterval OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 16 }

		
		-- *******.4.1.6296.**********.2.17
		sleOspfv3IfInstanceControlPriority OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 17 }

		
		-- *******.4.1.6296.**********.2.18
		sleOspfv3IfInstanceControlBfd OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 18 }

		
		-- *******.4.1.6296.**********.2.19
		sleOspfv3IfInstanceControlEfm OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfInstanceControl 19 }

		
		-- *******.4.1.6296.**********.3
		sleOspfv3IfInstanceNotifications OBJECT IDENTIFIER ::= { sleOspfv3IfInstance 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleOspfv3IfInstanceChanged NOTIFICATION-TYPE
			OBJECTS { sleOspfv3IfInstanceControlRequest, sleOspfv3IfInstanceControlTimeStamp, sleOspfv3IfInstanceControlReqResult, sleOspfv3IfInstanceProcTag, sleOspfv3IfInstanceAreaFlag, 
				sleOspfv3IfInstanceAreaIndex, sleOspfv3IfInstanceDeadInterval, sleOspfv3IfInstanceHelloInterval, sleOspfv3IfInstanceNetworkType, sleOspfv3IfInstanceCost, 
				sleOspfv3IfInstanceTransDelay, sleOspfv3IfInstanceRetransInterval, sleOspfv3IfInstancePriority, sleOspfv3IfInstanceBfd, sleOspfv3IfInstanceEfm
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3IfInstanceNotifications 1 }

		
		-- *******.4.1.6296.**********
		sleOspfv3IfNeighbor OBJECT IDENTIFIER ::= { sleOspfv3Interface 2 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3IfNeighborTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3IfNeighborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfNeighbor 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleOspfv3IfNeighborEntry OBJECT-TYPE
			SYNTAX SleOspfv3IfNeighborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3IfInstanceIfIndex, sleOspfv3IfInstanceInstanceId, sleOspfv3IfNeighborAddress }
			::= { sleOspfv3IfNeighborTable 1 }

		
		SleOspfv3IfNeighborEntry ::=
			SEQUENCE { 
				sleOspfv3IfNeighborAddress
					InetAddressIPv6,
				sleOspfv3IfNeighborCost
					Integer32,
				sleOspfv3IfNeighborPollIntervalFlag
					INTEGER,
				sleOspfv3IfNeighborPollIntervalValue
					Unsigned32,
				sleOspfv3IfNeighborPriority
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleOspfv3IfNeighborAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfNeighborEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleOspfv3IfNeighborCost OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfNeighborEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleOspfv3IfNeighborPollIntervalFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				noUse(0),
				use(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfNeighborEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleOspfv3IfNeighborPollIntervalValue OBJECT-TYPE
			SYNTAX Unsigned32 (0..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfNeighborEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleOspfv3IfNeighborPriority OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfNeighborEntry 5 }

		
		-- *******.4.1.6296.**********.2
		sleOspfv3ProcIfNeighborControl OBJECT IDENTIFIER ::= { sleOspfv3IfNeighbor 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleOspfv3IfNeighborControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createOspfv3IfNeighbor(1),
				modifyOspfv3IfNeighbor(2),
				destroyOspfv3IfNeighbor(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleOspfv3IfNeighborControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleOspfv3IfNeighborControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleOspfv3IfNeighborControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleOspfv3IfNeighborControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleOspfv3IfNeighborControlIndex OBJECT-TYPE
			SYNTAX Integer32 (0..4097)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleOspfv3IfNeighborControlInstanceIndex OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleOspfv3IfNeighborControlAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleOspfv3IfNeighborControlCost OBJECT-TYPE
			SYNTAX Integer32 (-1 | 0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleOspfv3IfNeighborControlPollIntervalFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				noUse(0),
				use(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleOspfv3IfNeighborControlPollInterval OBJECT-TYPE
			SYNTAX Unsigned32 (0..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleOspfv3IfNeighborControlPriority OBJECT-TYPE
			SYNTAX Integer32 (-1 | 0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3ProcIfNeighborControl 12 }

		
		-- *******.4.1.6296.**********.3
		sleOspfv3IfNeighborNotifications OBJECT IDENTIFIER ::= { sleOspfv3IfNeighbor 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleOspfv3IfNeighborCreated NOTIFICATION-TYPE
			OBJECTS { sleOspfv3IfNeighborControlRequest, sleOspfv3IfNeighborControlTimeStamp, sleOspfv3IfNeighborControlReqResult, sleOspfv3IfNeighborCost, sleOspfv3IfNeighborPollIntervalFlag, 
				sleOspfv3IfNeighborPollIntervalValue, sleOspfv3IfNeighborPriority }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3IfNeighborNotifications 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleOspfv3IfNeighborModified NOTIFICATION-TYPE
			OBJECTS { sleOspfv3IfNeighborControlRequest, sleOspfv3IfNeighborControlTimeStamp, sleOspfv3IfNeighborControlReqResult, sleOspfv3IfNeighborCost, sleOspfv3IfNeighborPollIntervalFlag, 
				sleOspfv3IfNeighborPollIntervalValue, sleOspfv3IfNeighborPriority }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3IfNeighborNotifications 2 }

		
		-- *******.4.1.6296.**********.3.3
		sleOspfv3IfNeighborDestroyed NOTIFICATION-TYPE
			OBJECTS { sleOspfv3IfNeighborControlRequest, sleOspfv3IfNeighborControlTimeStamp, sleOspfv3IfNeighborControlReqResult, sleOspfv3IfNeighborControlIndex, sleOspfv3IfNeighborControlInstanceIndex, 
				sleOspfv3IfNeighborControlAddress }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOspfv3IfNeighborNotifications 3 }

		
		-- *******.4.1.6296.**********
		sleOspfv3IfStatus OBJECT IDENTIFIER ::= { sleOspfv3Interface 3 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3IfStatusTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3IfStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatus 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleOspfv3IfStatusEntry OBJECT-TYPE
			SYNTAX SleOspfv3IfStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3IfInstanceIfIndex, sleOspfv3IfInstanceInstanceId }
			::= { sleOspfv3IfStatusTable 1 }

		
		SleOspfv3IfStatusEntry ::=
			SEQUENCE { 
				sleOspfv3IfStatusProcTag
					OCTET STRING,
				sleOspfv3IfStatusAreaId
					InetAddressIPv4,
				sleOspfv3IfStatusNetworkType
					INTEGER,
				sleOspfv3IfStatusCost
					INTEGER,
				sleOspfv3IfStatusTransDelay
					INTEGER,
				sleOspfv3IfStatusState
					INTEGER,
				sleOspfv3IfStatusPriority
					INTEGER,
				sleOspfv3IfStatusDRRouterId
					InetAddressIPv4,
				sleOspfv3IfStatusDRAddress
					InetAddressIPv6,
				sleOspfv3IfStatusBackupRouterId
					InetAddressIPv4,
				sleOspfv3IfStatusBackupAddress
					InetAddressIPv6,
				sleOspfv3IfStatusHelloInterval
					INTEGER,
				sleOspfv3IfStatusDeadInterval
					INTEGER,
				sleOspfv3IfStatusWaitInterval
					INTEGER,
				sleOspfv3IfStatusRetransInterval
					INTEGER,
				sleOspfv3IfNeighborCount
					INTEGER,
				sleOspfv3IfAdjNeighborCount
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleOspfv3IfStatusProcTag OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleOspfv3IfStatusAreaId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleOspfv3IfStatusNetworkType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				p2p(1),
				broadcast(2),
				nbma(3),
				p2mp(4),
				p2mpNbma(5),
				virtualLink(6),
				loopback(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleOspfv3IfStatusCost OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleOspfv3IfStatusTransDelay OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleOspfv3IfStatusState OBJECT-TYPE
			SYNTAX INTEGER
				{
				dependUpon(0),
				down(1),
				loopback(2),
				waiting(3),
				p2p(4),
				drOther(5),
				backup(6),
				dr(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleOspfv3IfStatusPriority OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleOspfv3IfStatusDRRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleOspfv3IfStatusDRAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 9 }

		
		-- *******.4.1.6296.**********.1.1.10
		sleOspfv3IfStatusBackupRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 10 }

		
		-- *******.4.1.6296.**********.1.1.11
		sleOspfv3IfStatusBackupAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 11 }

		
		-- *******.4.1.6296.**********.1.1.12
		sleOspfv3IfStatusHelloInterval OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 12 }

		
		-- *******.4.1.6296.**********.1.1.13
		sleOspfv3IfStatusDeadInterval OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 13 }

		
		-- *******.4.1.6296.**********.1.1.14
		sleOspfv3IfStatusWaitInterval OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 14 }

		
		-- *******.4.1.6296.**********.1.1.15
		sleOspfv3IfStatusRetransInterval OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 15 }

		
		-- *******.4.1.6296.**********.1.1.16
		sleOspfv3IfNeighborCount OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 16 }

		
		-- *******.4.1.6296.**********.1.1.17
		sleOspfv3IfAdjNeighborCount OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3IfStatusEntry 17 }

		
		-- *******.4.1.6296.101.52.4
		sleOspfv3Lsdb OBJECT IDENTIFIER ::= { sleOSPFv3 4 }

		
		-- *******.4.1.6296.**********
		sleOspfv3LsdbTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3LsdbEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3Lsdb 1 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3LsdbEntry OBJECT-TYPE
			SYNTAX SleOspfv3LsdbEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3LsdbType, sleOspfv3LsdbLinkStateId, sleOspfv3LsdbAdvRouterId }
			::= { sleOspfv3LsdbTable 1 }

		
		SleOspfv3LsdbEntry ::=
			SEQUENCE { 
				sleOspfv3LsdbType
					INTEGER,
				sleOspfv3LsdbLinkStateId
					InetAddressIPv4,
				sleOspfv3LsdbAdvRouterId
					InetAddressIPv4,
				sleOspfv3LsdbAge
					Integer32,
				sleOspfv3LsdbSeqnum
					Unsigned32,
				sleOspfv3LsdbLength
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1
		sleOspfv3LsdbType OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(0),
				router(1),
				network(2),
				interPrefix(3),
				interRouter(4),
				asExternal(5),
				groupMembership(6),
				nssa(7),
				link(8),
				intraPrefix(9)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3LsdbEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleOspfv3LsdbLinkStateId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3LsdbEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleOspfv3LsdbAdvRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3LsdbEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleOspfv3LsdbAge OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3LsdbEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleOspfv3LsdbSeqnum OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3LsdbEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleOspfv3LsdbLength OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3LsdbEntry 6 }

		
		-- *******.4.1.6296.101.52.5
		sleOspfv3Neighbor OBJECT IDENTIFIER ::= { sleOSPFv3 5 }

		
		-- *******.4.1.6296.**********
		sleOspfv3NeigborTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3NeigborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3Neighbor 1 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3NeigborEntry OBJECT-TYPE
			SYNTAX SleOspfv3NeigborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3NeighborRouterId, sleOspfv3NeighborIfType, sleOspfv3NeighborAddress, sleOspfv3NeighborIfIndex
				 }
			::= { sleOspfv3NeigborTable 1 }

		
		SleOspfv3NeigborEntry ::=
			SEQUENCE { 
				sleOspfv3NeighborIfType
					INTEGER,
				sleOspfv3NeighborIfIndex
					Unsigned32,
				sleOspfv3NeighborRouterId
					InetAddressIPv4,
				sleOspfv3NeighborAddress
					InetAddressIPv6,
				sleOspfv3NeighborAreaId
					InetAddressIPv4,
				sleOspfv3NeighborIfName
					OCTET STRING,
				sleOspfv3NeighborPriority
					INTEGER,
				sleOspfv3NeighborState
					INTEGER,
				sleOspfv3NeighborInstanceId
					Integer32,
				sleOspfv3NeighborDRRouterId
					InetAddressIPv4,
				sleOspfv3NeighborBDRRouterId
					InetAddressIPv4,
				sleOspfv3NeighborOption
					BITS,
				sleOspfv3NeighborDeadTime
					TimeTicks,
				sleOspfv3NeighborLsdbCount
					Unsigned32,
				sleOspfv3NeighborLsreqCount
					Unsigned32,
				sleOspfv3NeighborLsrxmitCount
					Unsigned32
			 }

		-- *******.4.1.6296.**********.1.1
		sleOspfv3NeighborIfType OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				virtual(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleOspfv3NeighborIfIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..4095)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleOspfv3NeighborRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleOspfv3NeighborAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleOspfv3NeighborAreaId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleOspfv3NeighborIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..128))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleOspfv3NeighborPriority OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleOspfv3NeighborState OBJECT-TYPE
			SYNTAX INTEGER
				{
				dependUpon(0),
				down(1),
				attempt(2),
				init(3),
				twoWay(4),
				exStart(5),
				exchange(6),
				loading(7),
				full(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleOspfv3NeighborInstanceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 9 }

		
		-- *******.4.1.6296.**********.1.10
		sleOspfv3NeighborDRRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 10 }

		
		-- *******.4.1.6296.**********.1.11
		sleOspfv3NeighborBDRRouterId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 11 }

		
		-- *******.4.1.6296.**********.1.12
		sleOspfv3NeighborOption OBJECT-TYPE
			SYNTAX BITS
				{
				forwarding(0),
				externalRouting(1),
				mcastForwarding(2),
				nssaLsa(3),
				anyProtocol(4),
				demand(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 12 }

		
		-- *******.4.1.6296.**********.1.13
		sleOspfv3NeighborDeadTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 13 }

		
		-- *******.4.1.6296.**********.1.14
		sleOspfv3NeighborLsdbCount OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 14 }

		
		-- *******.4.1.6296.**********.1.15
		sleOspfv3NeighborLsreqCount OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 15 }

		
		-- *******.4.1.6296.**********.1.16
		sleOspfv3NeighborLsrxmitCount OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3NeigborEntry 16 }

		
		-- *******.4.1.6296.101.52.6
		sleOspfv3Route OBJECT IDENTIFIER ::= { sleOSPFv3 6 }

		
		-- *******.4.1.6296.**********
		sleOspfv3RouteTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleOspfv3RouteEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3Route 1 }

		
		-- *******.4.1.6296.**********.1
		sleOspfv3RouteEntry OBJECT-TYPE
			SYNTAX SleOspfv3RouteEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleOspfv3ProcIndex, sleOspfv3RouteNetAddr, sleOspfv3RouteNetMask, sleOspfv3RouteNexthop }
			::= { sleOspfv3RouteTable 1 }

		
		SleOspfv3RouteEntry ::=
			SEQUENCE { 
				sleOspfv3RouteNetAddr
					InetAddressIPv6,
				sleOspfv3RouteNetMask
					INTEGER,
				sleOspfv3RouteNexthop
					InetAddressIPv6,
				sleOspfv3RouteType
					INTEGER,
				sleOspfv3RouteMetric
					INTEGER,
				sleOspfv3RouteIfName
					OCTET STRING,
				sleOspfv3RouteAreaFlag
					INTEGER,
				sleOspfv3RouteAreaId
					InetAddressIPv4
			 }

		-- *******.4.1.6296.**********.1.1
		sleOspfv3RouteNetAddr OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleOspfv3RouteNetMask OBJECT-TYPE
			SYNTAX INTEGER (0..128)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleOspfv3RouteNexthop OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleOspfv3RouteType OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(0),
				connected(1),
				intra(2),
				inter(3),
				externE1(4),
				externE2(5),
				externN1(6),
				externN2(7),
				discard(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleOspfv3RouteMetric OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleOspfv3RouteIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..128))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleOspfv3RouteAreaFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				upon(1),
				transit(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleOspfv3RouteAreaId OBJECT-TYPE
			SYNTAX InetAddressIPv4
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleOspfv3RouteEntry 8 }

		
		-- *******.4.1.6296.101.52.7
		sleOspfv3Group OBJECT-GROUP
			OBJECTS { sleOspfv3RouteDisplayMode, sleOspfv3ControlRequest, sleOspfv3ControlStatus, sleOspfv3ControlTimer, sleOspfv3ControlTimeStamp, 
				sleOspfv3ControlReqResult, sleOspfv3ControlRouteDisplayMode, sleOspfv3ProcIndex, sleOspfv3ProcTag, sleOspfv3ProcRouterId, 
				sleOspfv3ProcSpfDelayTime, sleOspfv3ProcSpfHoldTime, sleOspfv3ProcAutoCost, sleOspfv3ProcAbrType, sleOspfv3ProcDefaultMetric, 
				sleOspfv3ProcMaxConcurrentDD, sleOspfv3ProcDefaultOriginType, sleOspfv3ProcDefaultOriginMetricType, sleOspfv3ProcDefaultOriginMetric, sleOspfv3ProcDefaultOriginRouteMap, 
				sleOspfv3ProcLogNeighborChange, sleOspfv3ProcBfdAllIf, sleOspfv3ProcEfmAllIf, sleOspfv3ProcVRIndex, sleOspfv3ProcVRFName, 
				sleOspfv3ProcControlRequest, sleOspfv3ProcControlStatus, sleOspfv3ProcControlTimer, sleOspfv3ProcControlTimeStamp, sleOspfv3ProcControlReqResult, 
				sleOspfv3ProcControlIndex, sleOspfv3ProcControlTag, sleOspfv3ProcControlRouterId, sleOspfv3ProcControlSpfDelayTime, sleOspfv3ProcControlSpfHoldTime, 
				sleOspfv3ProcControlAutoCost, sleOspfv3ProcControlAbrType, sleOspfv3ProcControlDefaultMetric, sleOspfv3ProcControlMaxConcurrentDD, sleOspfv3ProcControlDefaultOriginType, 
				sleOspfv3ProcControlDefaultOriginMetricType, sleOspfv3ProcControlDefaultOriginMetric, sleOspfv3ProcControlDefaultOriginRouteMap, sleOspfv3ProcControlLogNeighborChange, sleOspfv3ProcControlBfdAllIf, 
				sleOspfv3ProcControlEfmAllIf, sleOspfv3ProcControlVRIndex, sleOspfv3ProcControlVRFName, sleOspfv3ProcSummaryAddress, sleOspfv3ProcSummaryMask, 
				sleOspfv3ProcSummaryTag, sleOspfv3ProcSummaryAdvertiseFlag, sleOspfv3ProcSummaryControlRequest, sleOspfv3ProcSummaryControlStatus, sleOspfv3ProcSummaryControlTimer, 
				sleOspfv3ProcSummaryControlTimeStamp, sleOspfv3ProcSummaryControlReqResult, sleOspfv3ProcSummaryControlIndex, sleOspfv3ProcSummaryControlAddress, sleOspfv3ProcSummaryControlMask, 
				sleOspfv3ProcSummaryControlTag, sleOspfv3ProcSummaryControlAdvertiseFlag, sleOspfv3ProcPassiveIfIndex, sleOspfv3ProcPassiveIfControlRequest, sleOspfv3ProcPassiveIfControlStatus, 
				sleOspfv3ProcPassiveIfControlTimer, sleOspfv3ProcPassiveIfControlTimeStamp, sleOspfv3ProcPassiveIfControlReqResult, sleOspfv3ProcPassiveIfControlIndex, sleOspfv3ProcPassiveIfControlIfIndex, 
				sleOspfv3ProcRedistType, sleOspfv3ProcRedistMetricType, sleOspfv3ProcRedistMetric, sleOSpfv3ProcRedistRouteMapName, sleOspfv3ProcRedistControlRequest, 
				sleOspfv3ProcRedistControlStatus, sleOspfv3ProcRedistControlTimer, sleOspfv3ProcRedistControlTimeStamp, sleOspfv3ProcRedistControlReqResult, sleOspfv3ProcRedistControlIndex, 
				sleOspfv3ProcRedistControlType, sleOspfv3ProcRedistControlMetricType, sleOspfv3ProcRedistControlMetric, sleOSpfv3ProcRedistControlRouteMapName, sleOspfv3ProcAreaInfoIndex, 
				sleOspfv3ProcAreaInfoType, sleOspfv3ProcAreaInfoDefaultCost, sleOspfv3ProcAreaInfoSummary, sleOspfv3ProcAreaInfoControlRequest, sleOspfv3ProcAreaInfoControlStatus, 
				sleOspfv3ProcAreaInfoControlTimer, sleOspfv3ProcAreaInfoControlTimeStamp, sleOspfv3ProcAreaInfoControlReqResult, sleOspfv3ProcAreaInfoControlIndex, sleOspfv3ProcAreaInfoControlAreaIndex, 
				sleOspfv3ProcAreaInfoControlType, sleOspfv3ProcAreaInfoControlDefaultCost, sleOspfv3ProcAreaInfoControlSummary, sleOspfv3ProcAreaRangeAddress, sleOspfv3ProcAreaRangeMask, 
				sleOspfv3ProcAreaRangeAdvertiseFlag, sleOspfv3ProcAreaRangeControlRequest, sleOspfv3ProcAreaRangeControlStatus, sleOspfv3ProcAreaRangeControlTimer, sleOspfv3ProcAreaRangeControlTimeStamp, 
				sleOspfv3ProcAreaRangeControlReqResult, sleOspfv3ProcAreaRangeControlIndex, sleOspfv3ProcAreaRangeControlAreaIndex, sleOspfv3ProcAreaRangeControlAddr, sleOspfv3ProcAreaRangeControlMask, 
				sleOspfv3ProcAreaRangeControlAdvertiseFlag, sleOspfv3ProcAreaVlinkRouterId, sleOspfv3ProcAreaVlinkDeadInterval, sleOspfv3ProcAreaVlinkHelloInterval, sleOspfv3ProcAreaVlinkInstanceId, 
				sleOspfv3ProcAreaVlinkRetransInterval, sleOspfv3ProcAreaVlinkTransDelay, sleOspfv3ProcAreaVlinkControlRequest, sleOspfv3ProcAreaVlinkControlStatus, sleOspfv3ProcAreaVlinkControlTimer, 
				sleOspfv3ProcAreaVlinkControlTimeStamp, sleOspfv3ProcAreaVlinkControlReqResult, sleOspfv3ProcAreaVlinkControlIndex, sleOspfv3ProcAreaVlinkControlAreaIndex, sleOspfv3ProcAreaVlinkControlRouterId, 
				sleOspfv3ProcAreaVlinkControlDeadInterval, sleOspfv3ProcAreaVlinkControlHelloInterval, sleOspfv3ProcAreaVlinkControlInstanceId, sleOspfv3ProcAreaVlinkControlRetransInterval, sleOspfv3ProcAreaVlinkControlTransDelay, 
				sleOspfv3IfInstanceIfIndex, sleOspfv3IfInstanceInstanceId, sleOspfv3IfInstanceProcTag, sleOspfv3IfInstanceAreaFlag, sleOspfv3IfInstanceAreaIndex, 
				sleOspfv3IfInstanceDeadInterval, sleOspfv3IfInstanceHelloInterval, sleOspfv3IfInstanceNetworkType, sleOspfv3IfInstanceCost, sleOspfv3IfInstanceTransDelay, 
				sleOspfv3IfInstanceRetransInterval, sleOspfv3IfInstancePriority, sleOspfv3IfInstanceBfd, sleOspfv3IfInstanceEfm, sleOspfv3IfInstanceControlRequest, 
				sleOspfv3IfInstanceControlStatus, sleOspfv3IfInstanceControlTimer, sleOspfv3IfInstanceControlTimeStamp, sleOspfv3IfInstanceControlReqResult, sleOspfv3IfInstanceControlIfIndex, 
				sleOspfv3IfInstanceControlIfInstanceId, sleOspfv3IfInstanceControlProcTag, sleOspfv3IfInstanceControlAreaFlag, sleOspfv3IfInstanceControlAreaIndex, sleOspfv3IfInstanceControlDeadInterval, 
				sleOspfv3IfInstanceControlHelloInterval, sleOspfv3IfInstanceControlNetworkType, sleOspfv3IfInstanceControlCost, sleOspfv3IfInstanceControlTransDelay, sleOspfv3IfInstanceControlRetransInterval, 
				sleOspfv3IfInstanceControlPriority, sleOspfv3IfInstanceControlBfd, sleOspfv3IfInstanceControlEfm, sleOspfv3IfNeighborAddress, sleOspfv3IfNeighborCost, 
				sleOspfv3IfNeighborPollIntervalFlag, sleOspfv3IfNeighborPollIntervalValue, sleOspfv3IfNeighborPriority, sleOspfv3IfNeighborControlRequest, sleOspfv3IfNeighborControlStatus, 
				sleOspfv3IfNeighborControlTimer, sleOspfv3IfNeighborControlTimeStamp, sleOspfv3IfNeighborControlReqResult, sleOspfv3IfNeighborControlIndex, sleOspfv3IfNeighborControlInstanceIndex, 
				sleOspfv3IfNeighborControlAddress, sleOspfv3IfNeighborControlCost, sleOspfv3IfNeighborControlPollIntervalFlag, sleOspfv3IfNeighborControlPollInterval, sleOspfv3IfNeighborControlPriority, 
				sleOspfv3IfStatusProcTag, sleOspfv3IfStatusAreaId, sleOspfv3IfStatusNetworkType, sleOspfv3IfStatusCost, sleOspfv3IfStatusTransDelay, 
				sleOspfv3IfStatusState, sleOspfv3IfStatusPriority, sleOspfv3IfStatusDRRouterId, sleOspfv3IfStatusDRAddress, sleOspfv3IfStatusBackupRouterId, 
				sleOspfv3IfStatusBackupAddress, sleOspfv3IfStatusHelloInterval, sleOspfv3IfStatusDeadInterval, sleOspfv3IfStatusWaitInterval, sleOspfv3IfStatusRetransInterval, 
				sleOspfv3IfNeighborCount, sleOspfv3IfAdjNeighborCount, sleOspfv3LsdbType, sleOspfv3LsdbLinkStateId, sleOspfv3LsdbAdvRouterId, 
				sleOspfv3LsdbAge, sleOspfv3LsdbSeqnum, sleOspfv3LsdbLength, sleOspfv3NeighborIfType, sleOspfv3NeighborIfIndex, 
				sleOspfv3NeighborRouterId, sleOspfv3NeighborAddress, sleOspfv3NeighborAreaId, sleOspfv3NeighborIfName, sleOspfv3NeighborPriority, 
				sleOspfv3NeighborState, sleOspfv3NeighborInstanceId, sleOspfv3NeighborDRRouterId, sleOspfv3NeighborBDRRouterId, sleOspfv3NeighborOption, 
				sleOspfv3NeighborDeadTime, sleOspfv3NeighborLsdbCount, sleOspfv3NeighborLsreqCount, sleOspfv3NeighborLsrxmitCount, sleOspfv3RouteNetAddr, 
				sleOspfv3RouteNetMask, sleOspfv3RouteNexthop, sleOspfv3RouteType, sleOspfv3RouteMetric, sleOspfv3RouteIfName, 
				sleOspfv3RouteAreaFlag, sleOspfv3ProcSPFStartDelay, sleOspfv3ProcSPFMinDelay, sleOspfv3ProcSPFMaxDelay, sleOspfv3ProcLSAStartDelay, 
				sleOspfv3ProcLSAMinDelay, sleOspfv3ProcLSAMaxDelay, sleOspfv3ProcLSAArrivalDelay, sleOspfv3ProcControlSPFStartDelay, sleOspfv3ProcControlSPFMinDelay, 
				sleOspfv3ProcControlSPFMaxDelay, sleOspfv3ProcControlLSAStartDelay, sleOspfv3ProcControlLSAMinDelay, sleOspfv3ProcControlLSAMaxDelay, sleOspfv3ProcControlLSAArrivalDelay, 
				sleOspfv3RouteAreaId, sleOspfv3RestartPeriod, sleOspfv3RestartHelperPolicy, sleOspfv3RestartHelperPeriod, sleOspfv3SnmpNotification, 
				sleOspfv3ControlRestartPeriod, sleOspfv3ControlRestartHelperPolicy, sleOspfv3ControlRestartHelperPeriod, sleOspfv3ControlSnmpNotification }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOSPFv3 7 }

		
		-- *******.4.1.6296.101.52.8
		sleOspfv3NotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleOspfv3RouteDisplayModeChanged, sleOspfv3ProcCreated, sleOspfv3ProcProfileChanged, sleOspfv3ProcDestroyed, sleOspfv3ProcCleared, 
				sleOspfv3ProcSummaryCreated, sleOspfv3ProcSummaryChanged, sleOspfv3ProcSummaryDestroyed, sleOspfv3ProcPassiveIfAdded, sleOspfv3ProcPassiveIfDeleted, 
				sleOspfv3ProcRedistCreated, sleOspfv3ProcRedistChanged, sleOspfv3ProcRedistDestroyed, sleOspfv3ProcAreaInfoCreated, sleOspfv3ProcAreaInfoChanged, 
				sleOspfv3ProcAreaInfoDestroyed, sleOspfv3ProcAreaRangeCreated, sleOspfv3ProcAreaRangeChanged, sleOspfv3ProcAreaRangeDestroyed, sleOspfv3ProcAreaVlinkCreated, 
				sleOspfv3ProcAreaVlinkChanged, sleOspfv3ProcAreaVlinkDestroyed, sleOspfv3IfInstanceChanged, sleOspfv3IfNeighborCreated, sleOspfv3IfNeighborModified, 
				sleOspfv3IfNeighborDestroyed, sleOspfv3RestartPeriodChanged, sleOspfv3RestartHelperProfileChanged, sleOspfv3GracefulRestarted, sleOspfv3SnmpNotificationiChanged
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleOSPFv3 8 }

		
	
	END

--
-- sle-ospfv3-mib.mib
--
