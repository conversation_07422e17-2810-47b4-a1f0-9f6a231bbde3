--
-- sle-clock-mib.mib
-- MIB generated by MG-SO<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Wednesday, December 16, 2015 at 15:10:12
--

	SLE-CLOCK-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GRO<PERSON>, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.6296.101.92
		sleClock MODULE-IDENTITY 
			LAST-UPDATED "201508271441Z"		-- August 27, 2015 at 14:41 GMT
			ORGANIZATION 
				"dasan networks"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains information about Clock."
			::= { sleMgmt 92 }

		
	
--
-- Textual conventions
--
	
		ClockSourceAll ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"clock source all"
			SYNTAX INTEGER
				{
				bitsa(1),
				bitsb(2),
				synce(3),
				ieee1588(7),
				gps10m(11),
				int(12)
				}

		ClockQualityLevel ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"QualityLevel - none, option1(prc,ssua,ssub,sec,dnu) option2gen1(prc,stu,st2,st3,smc,prov,dus) option2gen2(prc,stu,st2,tnc,st3e,st3,smc,prov,dus)"
			SYNTAX INTEGER
				{
				none(0),
				prc(1),
				ssua(2),
				ssub(3),
				sec(4),
				dnu(5),
				stu(7),
				st2(8),
				tnc(9),
				st3e(10),
				st3(11),
				smc(12),
				prov(13),
				dus(14)
				}

		ClockSourceSystem ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"clock source for system clock"
			SYNTAX INTEGER
				{
				bitsa(1),
				bitsb(2),
				synce(3),
				ieee1588(7),
				gps10m(11),
				int(12)
				}

		ClockQualityLevelValid ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"QualityLevel - option1(prc,ssua,ssub,sec,dnu) option2gen1(prc,stu,st2,st3,smc,prov,dus) option2gen2(prc,stu,st2,tnc,st3e,st3,smc,prov,dus)"
			SYNTAX INTEGER
				{
				prc(1),
				ssua(2),
				ssub(3),
				sec(4),
				dnu(5),
				stu(7),
				st2(8),
				tnc(9),
				st3e(10),
				st3(11),
				smc(12),
				prov(13),
				dus(14)
				}

	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.92.1
		sleClockBaseMode OBJECT IDENTIFIER ::= { sleClock 1 }

		
		-- *******.4.1.6296.**********
		sleClockBaseModeInfo OBJECT IDENTIFIER ::= { sleClockBaseMode 1 }

		
		-- *******.4.1.6296.**********.1
		sleClockSyncOption OBJECT-TYPE
			SYNTAX INTEGER
				{
				option1(1),
				option2gen1(2),
				option2gen2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"synchronization ssm option "
			::= { sleClockBaseModeInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockSelectionMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				qlEnable(1),
				qlDisable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"best clock source select by qualityLevel."
			::= { sleClockBaseModeInfo 2 }

		
		-- *******.4.1.6296.**********
		sleClockBaseModeControl OBJECT IDENTIFIER ::= { sleClockBaseMode 2 }

		
		-- *******.4.1.6296.**********.1
		sleClockModeControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSyncOption(1),
				setSelectionMode(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBaseModeControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockModeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBaseModeControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockModeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBaseModeControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleClockModeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBaseModeControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleClockModeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBaseModeControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleClockModeControlSyncOption OBJECT-TYPE
			SYNTAX INTEGER
				{
				option1(1),
				option2gen1(2),
				option2gen2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"synchronization ssm option."
			::= { sleClockBaseModeControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleClockModeControlSelectionMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				qlEnable(1),
				qlDisable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"best clock source select by qualityLevel."
			::= { sleClockBaseModeControl 7 }

		
		-- *******.4.1.6296.**********
		sleClockBaseModeNotification OBJECT IDENTIFIER ::= { sleClockBaseMode 3 }

		
		-- *******.4.1.6296.**********.1
		sleClockSyncOptionChanged NOTIFICATION-TYPE
			OBJECTS { sleClockModeControlRequest, sleClockModeControlStatus, sleClockModeControlTimeStamp, sleClockModeControlReqResult, sleClockModeControlSyncOption
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockBaseModeNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockSelectionModeChanged NOTIFICATION-TYPE
			OBJECTS { sleClockModeControlRequest, sleClockModeControlStatus, sleClockModeControlTimeStamp, sleClockModeControlReqResult, sleClockModeControlSelectionMode
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockBaseModeNotification 2 }

		
		-- *******.4.1.6296.101.92.2
		sleClockBits OBJECT IDENTIFIER ::= { sleClock 2 }

		
		-- *******.4.1.6296.**********
		sleClockBitsInfo OBJECT IDENTIFIER ::= { sleClockBits 1 }

		
		-- *******.4.1.6296.**********.1
		sleClockBitsType OBJECT-TYPE
			SYNTAX INTEGER
				{
				t1(1),
				e1(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Bits interface type t1 or e1"
			::= { sleClockBitsInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockBitsFrameMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				sf(1),
				esf(2),
				g704(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Bits frame mode T1(sf, esf) E1(g704)."
			::= { sleClockBitsInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockBitsLineEncoding OBJECT-TYPE
			SYNTAX INTEGER
				{
				b8zs(1),
				ami(2),
				hdb3(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Bits Line Coding T1(b8zs, ami) E1(hdb3, ami)."
			::= { sleClockBitsInfo 3 }

		
		-- *******.4.1.6296.**********
		sleClockBitsControl OBJECT IDENTIFIER ::= { sleClockBits 2 }

		
		-- *******.4.1.6296.**********.1
		sleClockBitsControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setBitsMode(1),
				setBitsLineEncoding(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBitsControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockBitsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBitsControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockBitsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBitsControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleClockBitsControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBitsControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleClockBitsControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockBitsControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleClockBitsControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				t1(1),
				e1(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Bits interface type t1 or e1."
			::= { sleClockBitsControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleClockBitsControlFrameMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				sf(1),
				esf(2),
				g704(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Bits frame mode T1(sf, esf) E1(g704)."
			::= { sleClockBitsControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleClockBitsControlLineEncoding OBJECT-TYPE
			SYNTAX INTEGER
				{
				b8zs(1),
				ami(2),
				hdb3(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Bits Line Coding T1(b8zs, ami) E1(hdb3, ami)."
			::= { sleClockBitsControl 8 }

		
		-- *******.4.1.6296.**********
		sleClockBitsNotification OBJECT IDENTIFIER ::= { sleClockBits 3 }

		
		-- *******.4.1.6296.**********.1
		sleClockBitsTypeChanged NOTIFICATION-TYPE
			OBJECTS { sleClockBitsControlRequest, sleClockBitsControlStatus, sleClockBitsControlTimeStamp, sleClockBitsControlReqResult, sleClockBitsControlType, 
				sleClockBitsControlFrameMode, sleClockBitsControlLineEncoding }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockBitsNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockBitsLineEncodingChanged NOTIFICATION-TYPE
			OBJECTS { sleClockBitsControlRequest, sleClockBitsControlStatus, sleClockBitsControlTimeStamp, sleClockBitsControlReqResult, sleClockBitsControlLineEncoding
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockBitsNotification 2 }

		
		-- *******.4.1.6296.101.92.3
		sleClockQlSet OBJECT IDENTIFIER ::= { sleClock 3 }

		
		-- *******.4.1.6296.**********
		sleClockQlSetTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleClockQlSetEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockQlSet 1 }

		
		-- *******.4.1.6296.**********.1
		sleClockQlSetEntry OBJECT-TYPE
			SYNTAX SleClockQlSetEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleClockQlSetSource }
			::= { sleClockQlSetTable 1 }

		
		SleClockQlSetEntry ::=
			SEQUENCE { 
				sleClockQlSetSource
					ClockSourceAll,
				sleClockQlSetQulatyLevel
					ClockQualityLevel
			 }

		-- *******.4.1.6296.**********.1.1
		sleClockQlSetSource OBJECT-TYPE
			SYNTAX ClockSourceAll
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"clock source."
			::= { sleClockQlSetEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleClockQlSetQulatyLevel OBJECT-TYPE
			SYNTAX ClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"QualityLevel."
			::= { sleClockQlSetEntry 2 }

		
		-- *******.4.1.6296.**********
		sleClockQlSetControl OBJECT IDENTIFIER ::= { sleClockQlSet 2 }

		
		-- *******.4.1.6296.**********.1
		sleClockQlSetControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setQualityLevel(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockQlSetControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockQlSetControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockQlSetControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockQlSetControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockQlSetControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleClockQlSetControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockQlSetControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleClockQlSetControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockQlSetControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleClockQlSetControlSoruce OBJECT-TYPE
			SYNTAX ClockSourceAll
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Clock Source."
			::= { sleClockQlSetControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleClockQlSetControlQualityLevel OBJECT-TYPE
			SYNTAX ClockQualityLevel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"QualityLevel."
			::= { sleClockQlSetControl 7 }

		
		-- *******.4.1.6296.**********
		sleClockQlNotification OBJECT IDENTIFIER ::= { sleClockQlSet 3 }

		
		-- *******.4.1.6296.**********.1
		sleClockQlSetQualityLevelChanged NOTIFICATION-TYPE
			OBJECTS { sleClockQlSetControlRequest, sleClockQlSetControlStatus, sleClockQlSetControlTimeStamp, sleClockQlSetControlReqResult, sleClockQlSetControlSoruce, 
				sleClockQlSetControlQualityLevel }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockQlNotification 1 }

		
		-- *******.4.1.6296.101.92.4
		sleClockSelected OBJECT IDENTIFIER ::= { sleClock 4 }

		
		-- *******.4.1.6296.**********
		sleClockSelectedInfo OBJECT IDENTIFIER ::= { sleClockSelected 1 }

		
		-- *******.4.1.6296.**********.1
		sleClockSelectedSource OBJECT-TYPE
			SYNTAX ClockSourceSystem
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Selected Clock Source."
			::= { sleClockSelectedInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockSelectedState OBJECT-TYPE
			SYNTAX INTEGER
				{
				freerun(1),
				holdover(2),
				locked(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Clock operate status."
			::= { sleClockSelectedInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockSelectedQualityLevel OBJECT-TYPE
			SYNTAX ClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"current Selected QualityLevel."
			::= { sleClockSelectedInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleClockSelectedSwitchType OBJECT-TYPE
			SYNTAX INTEGER
				{
				clear(0),
				force(1),
				man(2),
				auto(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"switch type, ."
			::= { sleClockSelectedInfo 4 }

		
		-- *******.4.1.6296.**********
		sleClockSelectedControl OBJECT IDENTIFIER ::= { sleClockSelected 2 }

		
		-- *******.4.1.6296.**********.1
		sleClockSelectedControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSwitchClear(1),
				setSwitchMan(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockSelectedControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockSelectedControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockSelectedControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockSelectedControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockSelectedControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleClockSelectedControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockSelectedControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleClockSelectedControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockSelectedControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleClockSelectedControlDestSource OBJECT-TYPE
			SYNTAX ClockSourceSystem
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockSelectedControl 6 }

		
		-- *******.4.1.6296.**********
		sleClockSelectedNotification OBJECT IDENTIFIER ::= { sleClockSelected 3 }

		
		-- *******.4.1.6296.**********.1
		sleClockSwitchManChanged NOTIFICATION-TYPE
			OBJECTS { sleClockSelectedControlRequest, sleClockSelectedControlStatus, sleClockSelectedControlTimeStamp, sleClockSelectedControlReqResult, sleClockSelectedControlDestSource
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockSelectedNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockSwitchClearChanged NOTIFICATION-TYPE
			OBJECTS { sleClockSelectedControlRequest, sleClockSelectedControlStatus, sleClockSelectedControlTimeStamp, sleClockSelectedControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockSelectedNotification 2 }

		
		-- *******.4.1.6296.101.92.5
		sleClockInputSource OBJECT IDENTIFIER ::= { sleClock 5 }

		
		-- *******.4.1.6296.**********
		sleClockInputSourceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleClockInputSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSource 1 }

		
		-- *******.4.1.6296.**********.1
		sleClockInputSourceEntry OBJECT-TYPE
			SYNTAX SleClockInputSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleClockInputSourceSource }
			::= { sleClockInputSourceTable 1 }

		
		SleClockInputSourceEntry ::=
			SEQUENCE { 
				sleClockInputSourceSource
					ClockSourceSystem,
				sleClockInputSourceUse
					INTEGER,
				sleClockInputSourcePriority
					INTEGER,
				sleClockInputSourceIfName
					OCTET STRING,
				sleClockInputSourceConfQulatyLevel
					ClockQualityLevel,
				sleClockInputSourceRecvQulatyLevel
					ClockQualityLevel,
				sleClockInputSourceValid
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleClockInputSourceSource OBJECT-TYPE
			SYNTAX ClockSourceSystem
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleClockInputSourceUse OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleClockInputSourcePriority OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1 is the highest priority, 255 is the lowest priority"
			::= { sleClockInputSourceEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleClockInputSourceIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"only valid clock source synce1~synce4, sdh1 ~ sdh3."
			::= { sleClockInputSourceEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleClockInputSourceConfQulatyLevel OBJECT-TYPE
			SYNTAX ClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleClockInputSourceRecvQulatyLevel OBJECT-TYPE
			SYNTAX ClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleClockInputSourceValid OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				valid(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceEntry 7 }

		
		-- *******.4.1.6296.**********
		sleClockInputSourceControl OBJECT IDENTIFIER ::= { sleClockInputSource 2 }

		
		-- *******.4.1.6296.**********.1
		sleClockInputSourceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setUseOn(1),
				setUseOff(2),
				setPriority(3),
				setIfName(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockInputSourceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockInputSourceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleClockInputSourceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleClockInputSourceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleClockInputSourceControlSource OBJECT-TYPE
			SYNTAX ClockSourceSystem
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleClockInputSourceControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleClockInputSourceControlPriority OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"1 is the highest priority, 255 is the lowest priority."
			::= { sleClockInputSourceControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleClockInputSourceControlIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"only valid clock source synce1~synce4, sdh1 ~ sdh3."
			::= { sleClockInputSourceControl 8 }

		
		-- *******.4.1.6296.**********
		sleClockInputSourceNotification OBJECT IDENTIFIER ::= { sleClockInputSource 3 }

		
		-- *******.4.1.6296.**********.1
		sleClockInputSourceUseOnChanged NOTIFICATION-TYPE
			OBJECTS { sleClockInputSourceControlRequest, sleClockInputSourceControlStatus, sleClockInputSourceControlTimeStamp, sleClockInputSourceControlReqResult, sleClockInputSourceControlSource, 
				sleClockInputSourceControlPriority }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockInputSourceNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleClockInputSourceUseOffChanged NOTIFICATION-TYPE
			OBJECTS { sleClockInputSourceControlRequest, sleClockInputSourceControlStatus, sleClockInputSourceControlTimeStamp, sleClockInputSourceControlReqResult, sleClockInputSourceControlSource
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockInputSourceNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleClockInputSourcePriorityChanged NOTIFICATION-TYPE
			OBJECTS { sleClockInputSourceControlRequest, sleClockInputSourceControlStatus, sleClockInputSourceControlTimeStamp, sleClockInputSourceControlReqResult, sleClockInputSourceControlSource, 
				sleClockInputSourceControlPriority }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockInputSourceNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleClockInputSourceIfNameChanged NOTIFICATION-TYPE
			OBJECTS { sleClockInputSourceControlRequest, sleClockInputSourceControlStatus, sleClockInputSourceControlTimeStamp, sleClockInputSourceControlReqResult, sleClockInputSourceControlSource, 
				sleClockInputSourceControlIfName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClockInputSourceNotification 4 }

		
		-- *******.4.1.6296.101.92.29
		sleClockObjectGroup OBJECT-GROUP
			OBJECTS { sleClockSyncOption, sleClockSelectionMode, sleClockModeControlRequest, sleClockModeControlStatus, sleClockModeControlTimer, 
				sleClockModeControlTimeStamp, sleClockModeControlReqResult, sleClockModeControlSyncOption, sleClockModeControlSelectionMode, sleClockBitsType, 
				sleClockBitsFrameMode, sleClockBitsLineEncoding, sleClockBitsControlRequest, sleClockBitsControlStatus, sleClockBitsControlTimer, 
				sleClockBitsControlTimeStamp, sleClockBitsControlReqResult, sleClockBitsControlType, sleClockBitsControlFrameMode, sleClockBitsControlLineEncoding, 
				sleClockQlSetSource, sleClockQlSetQulatyLevel, sleClockQlSetControlRequest, sleClockQlSetControlStatus, sleClockQlSetControlTimer, 
				sleClockQlSetControlTimeStamp, sleClockQlSetControlReqResult, sleClockQlSetControlSoruce, sleClockQlSetControlQualityLevel, sleClockSelectedSource, 
				sleClockSelectedState, sleClockSelectedControlRequest, sleClockSelectedControlStatus, sleClockSelectedControlTimer, sleClockSelectedControlTimeStamp, 
				sleClockSelectedControlReqResult, sleClockSelectedControlDestSource, sleClockInputSourceSource, sleClockInputSourceUse, sleClockInputSourcePriority, 
				sleClockInputSourceIfName, sleClockInputSourceConfQulatyLevel, sleClockInputSourceRecvQulatyLevel, sleClockInputSourceValid, sleClockInputSourceControlRequest, 
				sleClockInputSourceControlStatus, sleClockInputSourceControlTimer, sleClockInputSourceControlTimeStamp, sleClockInputSourceControlReqResult, sleClockInputSourceControlSource, 
				sleClockInputSourceControlPriority, sleClockInputSourceControlIfName, sleClockSelectedQualityLevel, sleClockSelectedSwitchType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClock 29 }

		
		-- *******.4.1.6296.101.92.30
		sleClockNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleClockSyncOptionChanged, sleClockSelectionModeChanged, sleClockBitsTypeChanged, sleClockBitsLineEncodingChanged, sleClockQlSetQualityLevelChanged, 
				sleClockSwitchManChanged, sleClockInputSourcePriorityChanged, sleClockInputSourceIfNameChanged, sleClockInputSourceUseOnChanged, sleClockInputSourceUseOffChanged, 
				sleClockSwitchClearChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleClock 30 }

		
	
	END

--
-- sle-clock-mib.mib
--
