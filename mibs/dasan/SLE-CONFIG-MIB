--
-- SLE-CONFIG-MIB.my
-- MIB generated by MG-<PERSON><PERSON><PERSON> Visual MIB Builder Version 6.0  Build 88
-- Tuesday, November 13, 2007 at 10:42:54
--

--  SLE-MCAST-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 3.0 Build 285
-- Wednesday, October 19, 2005 at 13:53:03
-- 
--  SLE-MUTICASTROUTINGPROTOCOL-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 285
-- Monday, December 13, 2004 at 13:09:15
-- 
-- 

	SLE-CONFIG-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			dasan			
				FROM DASAN-SMI			
			OBJECT-GROUP			
				FROM SNMPv2-CONF			
			OBJECT-TYPE, MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.100.1
		sleMibConfig MODULE-IDENTITY 
			LAST-UPDATED "200510281731Z"		-- October 28, 2005 at 17:31 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleConfigMgmt 1 }

		
	
--
-- Type definitions
--
	
		SleVersionType ::= INTEGER
			{
			undef(0),
			one(1),
			two(2),
			three(3),
			four(4),
			five(5),
			six(6),
			seven(7)
			}

	
--
-- Node definitions
--
	
		-- *******.4.1.6296.100
		sleConfigMgmt OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"sleConfig"
			::= { dasan 100 }

		
		-- *******.4.1.6296.100.1.1
		sleMibConfInfo OBJECT IDENTIFIER ::= { sleMibConfig 1 }

		
		-- *******.4.1.6296.*********
		sleMibConfInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMibConfInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"SLE MIB Configuring Information Table"
			::= { sleMibConfInfo 1 }

		
		-- *******.4.1.6296.*********.1
		sleMibConfInfoEntry OBJECT-TYPE
			SYNTAX SleMibConfInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"SLE MIB Configuring Information Table Entries"
			INDEX { sleMibConfIndex }
			::= { sleMibConfInfoTable 1 }

		
		SleMibConfInfoEntry ::=
			SEQUENCE { 
				sleMibConfIndex
					INTEGER,
				sleMibConfVersion
					SleVersionType,
				sleMibConfDescription
					OCTET STRING,
				sleMibConfDirectory
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleMibConfIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				systemManagement(1),
				device(2),
				bridge(3),
				faultManagement(4),
				performanceManagement(5),
				dhcp(6),
				security(7),
				snmp(8),
				rmon(9),
				qos(10),
				network(11),
				multicast(12),
				dhcpSnooping(13),
				mvQos(14),
				epon(20),
				sFlow(21),
				red(22),
				debug(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of SLE MIBs"
			::= { sleMibConfInfoEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleMibConfVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of implemented specific-MIB"
			::= { sleMibConfInfoEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleMibConfDescription OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description of indicated MIB"
			::= { sleMibConfInfoEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleMibConfDirectory OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier of module information folder for specific-MIB"
			::= { sleMibConfInfoEntry 4 }

		
		-- *******.4.1.6296.100.1.2
		sleModuleInfo OBJECT IDENTIFIER ::= { sleMibConfig 2 }

		
		-- *******.4.1.6296.*********
		sleSystemModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSystemModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"System Maintenance Module's Table"
			::= { sleModuleInfo 1 }

		
		-- *******.4.1.6296.*********.1
		sleSystemModuleEntry OBJECT-TYPE
			SYNTAX SleSystemModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"System Maintenance Module's Table Entries"
			INDEX { sleSystemModuleIndex }
			::= { sleSystemModuleTable 1 }

		
		SleSystemModuleEntry ::=
			SEQUENCE { 
				sleSystemModuleIndex
					INTEGER,
				sleSystemModuleVersion
					SleVersionType,
				sleSystemModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleSystemModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				login(11),
				process(12),
				backup(21),
				syslogConf(31),
				syslogHistVol(32),
				syslogHistNVol(33),
				dnsServer(41),
				ntpServer(42),
				sshRemote(43),
				autoCLI(51),
				autoCliScript(52),
				autoCliSchedule(53),
				autoCliProfile(54),
				autoCliProfileServer(55),
				autoCliOutputMemory(56),
				autoResetPing(61),
				autoResetMemoryleak(62),
				coreDump(71),
				sysService(81),
				sysUser(82),
				parameter(91),
				watchDog(101)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of system maintenance's modules"
			::= { sleSystemModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleSystemModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of system maintenance"
			::= { sleSystemModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleSystemModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of system maintenance"
			::= { sleSystemModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		sleDeviceModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDeviceModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Device Module's Table"
			::= { sleModuleInfo 2 }

		
		-- *******.4.1.6296.*********.1
		sleDeviceModuleEntry OBJECT-TYPE
			SYNTAX SleDeviceModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Device Module's Table Entries"
			INDEX { sleDeviceModuleIndex }
			::= { sleDeviceModuleTable 1 }

		
		SleDeviceModuleEntry ::=
			SEQUENCE { 
				sleDeviceModuleIndex
					INTEGER,
				sleDeviceModuleVersion
					SleVersionType,
				sleDeviceModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleDeviceModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				ethernetPort(11),
				power(21),
				fanModule(22),
				fanUnit(23),
				temperature(24),
				battery(31),
				door(41),
				cpu(51)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of device's modules"
			::= { sleDeviceModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleDeviceModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of device"
			::= { sleDeviceModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleDeviceModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of device"
			::= { sleDeviceModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		sleBridgeModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleBridgeModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Bridge Module's Table"
			::= { sleModuleInfo 3 }

		
		-- *******.4.1.6296.*********.1
		sleBridgeModuleEntry OBJECT-TYPE
			SYNTAX SleBridgeModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Bridge Module's Table Entries"
			INDEX { sleBridgeModuleIndex }
			::= { sleBridgeModuleTable 1 }

		
		SleBridgeModuleEntry ::=
			SEQUENCE { 
				sleBridgeModuleIndex
					INTEGER,
				sleBridgeModuleVersion
					SleVersionType,
				sleBridgeModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleBridgeModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				bridgePort(11),
				vlan(21),
				vlanMap(22),
				subnetBasedVlan(23),
				protocolBasedVlan(24),
				fdb(31),
				mfdb(32),
				stackDevice(41),
				stp(51),
				stpInstance(52),
				stpInstancePort(53),
				lag(61),
				lagLacp(62),
				lagLacpPort(63),
				erpDomain(71),
				lldpPort(81),
				lldpRemote(82),
				lldpStatistics(83),
				igmpSnoopConf(91),
				igmpSnoopGroup(92),
				portSecurity(101),
				portSecurityFdb(102),
				floodGuard(111),
				vlanTranslation(121)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of bridge's modules"
			::= { sleBridgeModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleBridgeModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of bridge"
			::= { sleBridgeModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleBridgeModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of bridge"
			::= { sleBridgeModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		sleFaultMgmtModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleFaultMgmtModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Fault Management Module's Table"
			::= { sleModuleInfo 4 }

		
		-- *******.4.1.6296.*********.1
		sleFaultMgmtModuleEntry OBJECT-TYPE
			SYNTAX SleFaultMgmtModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Fault Management Module's Table Entries"
			INDEX { sleFaultMgmtModuleIndex }
			::= { sleFaultMgmtModuleTable 1 }

		
		SleFaultMgmtModuleEntry ::=
			SEQUENCE { 
				sleFaultMgmtModuleIndex
					INTEGER,
				sleFaultMgmtModuleVersion
					SleVersionType,
				sleFaultMgmtModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleFaultMgmtModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				alarmSeverity(11),
				alarmReport(12),
				alarmHistory(13),
				efmOam(21),
				advaSystem(31),
				advaGeneral(32),
				advaModule(33),
				advaOptical(34),
				advaOam(35)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of fault management's modules"
			::= { sleFaultMgmtModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleFaultMgmtModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of fault management"
			::= { sleFaultMgmtModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleFaultMgmtModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of fault management"
			::= { sleFaultMgmtModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		slePerfMgmtModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePerfMgmtModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Performance Management Module's Table"
			::= { sleModuleInfo 5 }

		
		-- *******.4.1.6296.*********.1
		slePerfMgmtModuleEntry OBJECT-TYPE
			SYNTAX SlePerfMgmtModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Performance Management Module's Table Entries"
			INDEX { slePerfMgmtModuleIndex }
			::= { slePerfMgmtModuleTable 1 }

		
		SlePerfMgmtModuleEntry ::=
			SEQUENCE { 
				slePerfMgmtModuleIndex
					INTEGER,
				slePerfMgmtModuleVersion
					SleVersionType,
				slePerfMgmtModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		slePerfMgmtModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				portThreshold(11),
				portRate(12),
				portStatistics(13),
				cpuStatistics(14)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of performance management's modules"
			::= { slePerfMgmtModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		slePerfMgmtModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of performance management"
			::= { slePerfMgmtModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		slePerfMgmtModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of performance management"
			::= { slePerfMgmtModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		sleDhcpModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcpModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DHCP Module's Table"
			::= { sleModuleInfo 6 }

		
		-- *******.4.1.6296.*********.1
		sleDhcpModuleEntry OBJECT-TYPE
			SYNTAX SleDhcpModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DHCP Module's Table Entries"
			INDEX { sleDhcpModuleIndex }
			::= { sleDhcpModuleTable 1 }

		
		SleDhcpModuleEntry ::=
			SEQUENCE { 
				sleDhcpModuleIndex
					INTEGER,
				sleDhcpModuleVersion
					SleVersionType,
				sleDhcpModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleDhcpModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				pool(11),
				network(12),
				range(13),
				fixedAddress(14),
				dnsOption(15),
				defaultGwOption(16),
				serverOptions(17),
				dhcp4Logs(18),
				dhcp4Ntp(19),
				dhcp4PE(20),
				option82Port(31),
				option82Remote(32),
				option82Circuit(33),
				option82Class(34),
				option82ClassMap(35),
				option82ClassRange(36),
				filterPort(41),
				filterAddress(42),
				illegal(43),
				packetStats(51),
				leased(52),
				packetStatsPort(53),
				snopping(61),
				snoopVlan(62),
				snoopPort(63),
				snoopBindings(64),
				relayInterface(71),
				relayHelper(72),
				clientInterface(81),
				clientOptions(82),
				dhcp4PortLease(91)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of DHCP's modules"
			::= { sleDhcpModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleDhcpModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of DHCP"
			::= { sleDhcpModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleDhcpModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of DHCP"
			::= { sleDhcpModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		sleSecurityModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSecurityModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Security Module's Table"
			::= { sleModuleInfo 7 }

		
		-- *******.4.1.6296.*********.1
		sleSecurityModuleEntry OBJECT-TYPE
			SYNTAX SleSecurityModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Security Module's Table Entries"
			INDEX { sleSecurityModuleIndex }
			::= { sleSecurityModuleTable 1 }

		
		SleSecurityModuleEntry ::=
			SEQUENCE { 
				sleSecurityModuleIndex
					INTEGER,
				sleSecurityModuleVersion
					SleVersionType,
				sleSecurityModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleSecurityModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				aaa(11),
				radiusServer(12),
				tacacsServer(13),
				acl(21),
				dot1xPort(31),
				dot1xStatistics(32),
				arpInspection(41)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of security's modules"
			::= { sleSecurityModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleSecurityModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of security"
			::= { sleSecurityModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleSecurityModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of security"
			::= { sleSecurityModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		sleSnmpModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSnmpModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"SNMP Module's Table"
			::= { sleModuleInfo 8 }

		
		-- *******.4.1.6296.*********.1
		sleSnmpModuleEntry OBJECT-TYPE
			SYNTAX SleSnmpModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"SNMP Module's Table Entries"
			INDEX { sleSnmpModuleIndex }
			::= { sleSnmpModuleTable 1 }

		
		SleSnmpModuleEntry ::=
			SEQUENCE { 
				sleSnmpModuleIndex
					INTEGER,
				sleSnmpModuleVersion
					SleVersionType,
				sleSnmpModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleSnmpModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				com2Sec(11),
				trapHost(12),
				community(13),
				viewTreeFamily(14),
				access(15),
				securityToGroup(16),
				user(17),
				agent(18),
				snmpSeesion(19),
				snmpTrap(20),
				snmpTrapSource(21)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of SNMP's modules"
			::= { sleSnmpModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleSnmpModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of SNMP"
			::= { sleSnmpModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleSnmpModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of SNMP"
			::= { sleSnmpModuleEntry 3 }

		
		-- *******.4.1.6296.*********
		sleRmonModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRmonModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"RMON Module's Table"
			::= { sleModuleInfo 9 }

		
		-- *******.4.1.6296.*********.1
		sleRmonModuleEntry OBJECT-TYPE
			SYNTAX SleRmonModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"RMON Module's Table Entries"
			INDEX { sleRmonModuleIndex }
			::= { sleRmonModuleTable 1 }

		
		SleRmonModuleEntry ::=
			SEQUENCE { 
				sleRmonModuleIndex
					INTEGER,
				sleRmonModuleVersion
					SleVersionType,
				sleRmonModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleRmonModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				event(11),
				alarm(12),
				history(13),
				etherStats(21)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of RMON's modules"
			::= { sleRmonModuleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleRmonModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of RMON"
			::= { sleRmonModuleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleRmonModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of RMON"
			::= { sleRmonModuleEntry 3 }

		
		-- *******.4.1.6296.*********0
		sleQosModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleQosModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"QoS Module's Table"
			::= { sleModuleInfo 10 }

		
		-- *******.4.1.6296.*********0.1
		sleQosModuleEntry OBJECT-TYPE
			SYNTAX SleQosModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"QoS Module's Table Entries"
			INDEX { sleQosModuleIndex }
			::= { sleQosModuleTable 1 }

		
		SleQosModuleEntry ::=
			SEQUENCE { 
				sleQosModuleIndex
					INTEGER,
				sleQosModuleVersion
					SleVersionType,
				sleQosModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********0.1.1
		sleQosModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				baseV4(2),
				baseV6(3),
				trafficeProfile(11),
				portSchedule(12),
				queue(13),
				counter(14),
				colorMarking(15),
				userflow(21),
				flow(31),
				flowClassMap(32),
				flowPolicyMap(33),
				class(41),
				classFlowMap(42),
				classPolicyMap(43),
				policer(51),
				policy(61),
				policyFCMap(62),
				remark(71),
				remarkL3(72),
				remarkL2(73),
				redProfile(81),
				portRed(82)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of QoS's modules"
			::= { sleQosModuleEntry 1 }

		
		-- *******.4.1.6296.*********0.1.2
		sleQosModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of QoS"
			::= { sleQosModuleEntry 2 }

		
		-- *******.4.1.6296.*********0.1.3
		sleQosModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of QoS"
			::= { sleQosModuleEntry 3 }

		
		-- *******.4.1.6296.*********1
		sleNetworkModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleNetworkModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Network Module's Table"
			::= { sleModuleInfo 11 }

		
		-- *******.4.1.6296.*********1.1
		sleNetworkModuleEntry OBJECT-TYPE
			SYNTAX SleNetworkModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Network Module's Table Entries"
			INDEX { sleNetworkModuleIndex }
			::= { sleNetworkModuleTable 1 }

		
		SleNetworkModuleEntry ::=
			SEQUENCE { 
				sleNetworkModuleIndex
					INTEGER,
				sleNetworkModuleVersion
					SleVersionType,
				sleNetworkModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********1.1.1
		sleNetworkModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				interface(11),
				ipAddress(12),
				arp(13),
				routing(21),
				dhcpClient(31)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of network's modules"
			::= { sleNetworkModuleEntry 1 }

		
		-- *******.4.1.6296.*********1.1.2
		sleNetworkModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of network"
			::= { sleNetworkModuleEntry 2 }

		
		-- *******.4.1.6296.*********1.1.3
		sleNetworkModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of network"
			::= { sleNetworkModuleEntry 3 }

		
		-- *******.4.1.6296.*********2
		sleMulticastModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMulticastModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Multicast Module's Table"
			::= { sleModuleInfo 12 }

		
		-- *******.4.1.6296.*********2.1
		sleMulticastModuleEntry OBJECT-TYPE
			SYNTAX SleMulticastModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Multicast Module's Table Entries"
			INDEX { sleMulticastModuleIndex }
			::= { sleMulticastModuleTable 1 }

		
		SleMulticastModuleEntry ::=
			SEQUENCE { 
				sleMulticastModuleIndex
					INTEGER,
				sleMulticastModuleVersion
					SleVersionType,
				sleMulticastModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********2.1.1
		sleMulticastModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				mrouteBase(11),
				mroute(12),
				mrouteNextHop(13),
				mrouteInterface(14),
				igmpBase(21),
				igmpSsmMapping(22),
				igmpHelperVlan(31),
				igmpHelperGroup(32),
				igmpInterface(41),
				igmpCache(42),
				igmpSource(43),
				igmpGroupState(44),
				igmpSnoopVlan(51),
				igmpSnoopGroup(52),
				igmpSnoopSource(53),
				igmpSnoopReport(54),
				igmpSnoopPort(55),
				igmpSnoopPortStats(56),
				igmpProfile(61),
				igmpProfileRange(62),
				igmpFilterPort(63),
				mvrPort(71),
				mvrGroup(72),
				pimBase(81),
				pimSnoopVlan(91),
				pimAgent(101),
				igmpProxyBase(111),
				igmpProxyInterface(121)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of multicast's modules"
			::= { sleMulticastModuleEntry 1 }

		
		-- *******.4.1.6296.*********2.1.2
		sleMulticastModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of multicast"
			::= { sleMulticastModuleEntry 2 }

		
		-- *******.4.1.6296.*********2.1.3
		sleMulticastModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of multicast"
			::= { sleMulticastModuleEntry 3 }

		
		-- *******.4.1.6296.*********3
		sleDhcpSnoopModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcpSnoopModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DHCP Snooping Module's Table (obsolated module)"
			::= { sleModuleInfo 13 }

		
		-- *******.4.1.6296.*********3.1
		sleDhcpSnoopModuleEntry OBJECT-TYPE
			SYNTAX SleDhcpSnoopModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Multicast Module's Table Entries"
			INDEX { sleDhcpSnoopModuleIndex }
			::= { sleDhcpSnoopModuleTable 1 }

		
		SleDhcpSnoopModuleEntry ::=
			SEQUENCE { 
				sleDhcpSnoopModuleIndex
					INTEGER,
				sleDhcpSnoopModuleVersion
					SleVersionType,
				sleDhcpSnoopModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********3.1.1
		sleDhcpSnoopModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				portSrcGuardConf(11),
				portSrcGuardAddress(12)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of DHCP snooping's modules"
			::= { sleDhcpSnoopModuleEntry 1 }

		
		-- *******.4.1.6296.*********3.1.2
		sleDhcpSnoopModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of DHCP snooping"
			::= { sleDhcpSnoopModuleEntry 2 }

		
		-- *******.4.1.6296.*********3.1.3
		sleDhcpSnoopModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of DHCP snooping"
			::= { sleDhcpSnoopModuleEntry 3 }

		
		-- *******.4.1.6296.*********4
		sleMvQosModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMvQosModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"MvQoS Module's Table"
			::= { sleModuleInfo 14 }

		
		-- *******.4.1.6296.*********4.1
		sleMvQosModuleEntry OBJECT-TYPE
			SYNTAX SleMvQosModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"MvQoS Module's Table Entries"
			INDEX { sleMvQosModuleIndex }
			::= { sleMvQosModuleTable 1 }

		
		SleMvQosModuleEntry ::=
			SEQUENCE { 
				sleMvQosModuleIndex
					INTEGER,
				sleMvQosModuleVersion
					SleVersionType,
				sleMvQosModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********4.1.1
		sleMvQosModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				baseV4(2),
				baseV6(3),
				bridgePort2Tc(11),
				bridgeProtocol2Tc(12),
				bridgeSubnet2Tc(13),
				bridgeMac2Tc(14),
				bridgeUp2TcEnable(15),
				bridgeUp2Tc(16),
				bridgeTc2Up(17),
				bridgeUp2Dp(18),
				inLifMark(21),
				routerMark(31)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of MvQoS's modules.(obsolated module)"
			::= { sleMvQosModuleEntry 1 }

		
		-- *******.4.1.6296.*********4.1.2
		sleMvQosModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of MvQoS."
			::= { sleMvQosModuleEntry 2 }

		
		-- *******.4.1.6296.*********4.1.3
		sleMvQosModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of MvQoS."
			::= { sleMvQosModuleEntry 3 }

		
		-- *******.4.1.6296.*********0
		sleEponModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleEponModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"EPON Module's Table"
			::= { sleModuleInfo 20 }

		
		-- *******.4.1.6296.*********0.1
		sleEponModuleEntry OBJECT-TYPE
			SYNTAX SleEponModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"EPON Module's Table Entries"
			INDEX { sleEponModuleIndex }
			::= { sleEponModuleTable 1 }

		
		SleEponModuleEntry ::=
			SEQUENCE { 
				sleEponModuleIndex
					INTEGER,
				sleEponModuleVersion
					SleVersionType,
				sleEponModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********0.1.1
		sleEponModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				olt(11),
				onu(21),
				vlan(31),
				profile(41),
				profileQueue(42),
				alarm(51),
				oltStatistics(61),
				onuStatistics(62)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of EPON's modules.(obsolated module)"
			::= { sleEponModuleEntry 1 }

		
		-- *******.4.1.6296.*********0.1.2
		sleEponModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of EPON."
			::= { sleEponModuleEntry 2 }

		
		-- *******.4.1.6296.*********0.1.3
		sleEponModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of EPON."
			::= { sleEponModuleEntry 3 }

		
		-- *******.4.1.6296.*********1
		sleSFlowModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSFlowModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"sFlow Module's Table"
			::= { sleModuleInfo 21 }

		
		-- *******.4.1.6296.*********1.1
		sleSFlowModuleEntry OBJECT-TYPE
			SYNTAX SleSFlowModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"sFlow Module's Table Entries"
			INDEX { sleSFlowModuleIndex }
			::= { sleSFlowModuleTable 1 }

		
		SleSFlowModuleEntry ::=
			SEQUENCE { 
				sleSFlowModuleIndex
					INTEGER,
				sleSFlowModuleVersion
					SleVersionType,
				sleSFlowModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********1.1.1
		sleSFlowModuleIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				base(1),
				receiver(11),
				flowSampler(12),
				counterPoller(13)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of sFlow's modules.(obsolated module)"
			::= { sleSFlowModuleEntry 1 }

		
		-- *******.4.1.6296.*********1.1.2
		sleSFlowModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of sFlow."
			::= { sleSFlowModuleEntry 2 }

		
		-- *******.4.1.6296.*********1.1.3
		sleSFlowModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of sFlow."
			::= { sleSFlowModuleEntry 3 }

		
		-- *******.4.1.6296.**********
		sleRedModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRedModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleModuleInfo 22 }

		
		-- *******.4.1.6296.**********.1
		sleRedModuleEntry OBJECT-TYPE
			SYNTAX SleRedModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRedModuleIndex }
			::= { sleRedModuleTable 1 }

		
		SleRedModuleEntry ::=
			SEQUENCE { 
				sleRedModuleIndex
					INTEGER,
				sleRedModuleVersion
					SleVersionType,
				sleRedModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.**********.1.1
		sleRedModuleIndex OBJECT-TYPE
			SYNTAX INTEGER { base(1) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRedModuleEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRedModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRedModuleEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRedModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRedModuleEntry 3 }

		
		-- *******.4.1.6296.**********
		sleDebugModuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDebugModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"sFlow Module's Table"
			::= { sleModuleInfo 99 }

		
		-- *******.4.1.6296.**********.1
		sleDebugModuleEntry OBJECT-TYPE
			SYNTAX SleDebugModuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"sFlow Module's Table Entries"
			INDEX { sleSFlowModuleIndex }
			::= { sleDebugModuleTable 1 }

		
		SleDebugModuleEntry ::=
			SEQUENCE { 
				sleDebugModuleIndex
					INTEGER,
				sleDebugModuleVersion
					SleVersionType,
				sleDebugModuleObjectId
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.**********.1.1
		sleDebugModuleIndex OBJECT-TYPE
			SYNTAX INTEGER { debugBase(1) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Index of sFlow's modules.(obsolated module)"
			::= { sleDebugModuleEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleDebugModuleVersion OBJECT-TYPE
			SYNTAX SleVersionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Version of specific-module of sFlow."
			::= { sleDebugModuleEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleDebugModuleObjectId OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Object identifier for specific-module table or group of sFlow."
			::= { sleDebugModuleEntry 3 }

		
		-- *******.4.1.6296.100.1.3
		sleMibConfGroup OBJECT-GROUP
			OBJECTS { sleMibConfIndex, sleMibConfVersion, sleMibConfDescription, sleMibConfDirectory, sleSystemModuleIndex, 
				sleSystemModuleVersion, sleSystemModuleObjectId, sleDeviceModuleIndex, sleDeviceModuleVersion, sleDeviceModuleObjectId, 
				sleBridgeModuleIndex, sleBridgeModuleVersion, sleBridgeModuleObjectId, sleFaultMgmtModuleIndex, sleFaultMgmtModuleVersion, 
				sleFaultMgmtModuleObjectId, slePerfMgmtModuleIndex, slePerfMgmtModuleVersion, slePerfMgmtModuleObjectId, sleDhcpModuleIndex, 
				sleDhcpModuleVersion, sleDhcpModuleObjectId, sleSecurityModuleIndex, sleSecurityModuleVersion, sleSecurityModuleObjectId, 
				sleSnmpModuleIndex, sleSnmpModuleVersion, sleSnmpModuleObjectId, sleRmonModuleIndex, sleRmonModuleVersion, 
				sleRmonModuleObjectId, sleQosModuleIndex, sleQosModuleVersion, sleQosModuleObjectId, sleNetworkModuleIndex, 
				sleNetworkModuleVersion, sleNetworkModuleObjectId, sleMulticastModuleIndex, sleMulticastModuleVersion, sleMulticastModuleObjectId, 
				sleDhcpSnoopModuleIndex, sleDhcpSnoopModuleVersion, sleDhcpSnoopModuleObjectId, sleSFlowModuleIndex, sleSFlowModuleVersion, 
				sleDebugModuleIndex, sleDebugModuleVersion, sleDebugModuleObjectId, sleRedModuleIndex, sleRedModuleVersion, 
				sleRedModuleObjectId, sleSFlowModuleObjectId, sleMvQosModuleIndex, sleMvQosModuleVersion, sleMvQosModuleObjectId, 
				sleEponModuleIndex, sleEponModuleVersion, sleEponModuleObjectId }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMibConfig 3 }

		
	
	END

--
-- SLE-CONFIG-MIB.my
--
