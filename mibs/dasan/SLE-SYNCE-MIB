--
-- sle-synce-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, December 16, 2015 at 15:12:34
--

	SLE-SYNCE-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Integer32, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.6296.101.93
		sleSynce MODULE-IDENTITY 
			LAST-UPDATED "201508271441Z"		-- August 27, 2015 at 14:41 GMT
			ORGANIZATION 
				"dasan networks"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains information about synce."
			::= { sleMgmt 93 }

		
	
--
-- Textual conventions
--
	
		SynceClockQualityLevel ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"QualityLevel - none, option1(prc,ssua,ssub,sec,dnu) option2gen1(prc,stu,st2,st3,smc,prov,dus) option2gen2(prc,stu,st2,tnc,st3e,st3,smc,prov,dus)"
			SYNTAX INTEGER
				{
				none(0),
				prc(1),
				ssua(2),
				ssub(3),
				sec(4),
				dnu(5),
				stu(7),
				st2(8),
				tnc(9),
				st3e(10),
				st3(11),
				smc(12),
				prov(13),
				dus(14)
				}

	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.93.1
		sleSynceBaseMode OBJECT IDENTIFIER ::= { sleSynce 1 }

		
		-- *******.4.1.6296.**********
		sleSynceBaseModeInfo OBJECT IDENTIFIER ::= { sleSynceBaseMode 1 }

		
		-- *******.4.1.6296.**********.1
		sleSynceSyncOption OBJECT-TYPE
			SYNTAX INTEGER
				{
				option1(1),
				option2gen1(2),
				option2gen2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"synchronization ssm option "
			::= { sleSynceBaseModeInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceSelectionMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				qlEnable(1),
				qlDisable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"best clock source select by qualityLevel."
			::= { sleSynceBaseModeInfo 2 }

		
		-- *******.4.1.6296.**********
		sleSynceBaseModeControl OBJECT IDENTIFIER ::= { sleSynceBaseMode 2 }

		
		-- *******.4.1.6296.**********.1
		sleSynceModeControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setSelectionMode(2) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceBaseModeControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceModeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceBaseModeControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSynceModeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceBaseModeControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSynceModeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceBaseModeControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSynceModeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceBaseModeControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSynceModeControlSyncOption OBJECT-TYPE
			SYNTAX INTEGER
				{
				option1(1),
				option2gen1(2),
				option2gen2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"synchronization ssm option."
			::= { sleSynceBaseModeControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSynceModeControlSelectionMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				qlEnable(1),
				qlDisable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"best clock source select by qualityLevel."
			::= { sleSynceBaseModeControl 7 }

		
		-- *******.4.1.6296.**********
		sleSynceBaseModeNotification OBJECT IDENTIFIER ::= { sleSynceBaseMode 3 }

		
		-- *******.4.1.6296.**********.1
		sleSynceSyncOptionChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceModeControlRequest, sleSynceModeControlStatus, sleSynceModeControlTimeStamp, sleSynceModeControlReqResult, sleSynceModeControlSyncOption
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceBaseModeNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceSelectionModeChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceModeControlRequest, sleSynceModeControlStatus, sleSynceModeControlTimeStamp, sleSynceModeControlReqResult, sleSynceModeControlSelectionMode
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceBaseModeNotification 2 }

		
		-- *******.4.1.6296.101.93.2
		sleSynceIfEnable OBJECT IDENTIFIER ::= { sleSynce 2 }

		
		-- *******.4.1.6296.**********
		sleSynceIfEnableTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSynceIfEnableEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnable 1 }

		
		-- *******.4.1.6296.**********.1
		sleSynceIfEnableEntry OBJECT-TYPE
			SYNTAX SleSynceIfEnableEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSynceIfEnableIfIndex }
			::= { sleSynceIfEnableTable 1 }

		
		SleSynceIfEnableEntry ::=
			SEQUENCE { 
				sleSynceIfEnableIfIndex
					Integer32,
				sleSynceIfEnableName
					OCTET STRING,
				sleSynceIfEnableState
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSynceIfEnableIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSynceIfEnableName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSynceIfEnableState OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableEntry 3 }

		
		-- *******.4.1.6296.**********
		sleSynceIfEnableControl OBJECT IDENTIFIER ::= { sleSynceIfEnable 2 }

		
		-- *******.4.1.6296.**********.1
		sleSynceIfEnableControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setSynceEnable(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceIfEnableControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSynceIfEnableControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSynceIfEnableControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSynceIfEnableControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSynceIfEnableControlIfIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSynceIfEnableControlState OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEnableControl 7 }

		
		-- *******.4.1.6296.**********
		sleSynceIfEnableNotification OBJECT IDENTIFIER ::= { sleSynceIfEnable 3 }

		
		-- *******.4.1.6296.**********.1
		sleSynceIfEnableStateChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfEnableControlRequest, sleSynceIfEnableControlStatus, sleSynceIfEnableControlTimeStamp, sleSynceIfEnableControlReqResult, sleSynceIfEnableControlIfIndex, 
				sleSynceIfEnableControlState }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfEnableNotification 1 }

		
		-- *******.4.1.6296.101.93.3
		sleSynceIf OBJECT IDENTIFIER ::= { sleSynce 3 }

		
		-- *******.4.1.6296.**********
		sleSynceIfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSynceIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIf 1 }

		
		-- *******.4.1.6296.**********.1
		sleSynceIfEntry OBJECT-TYPE
			SYNTAX SleSynceIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSynceIfIfIndex }
			::= { sleSynceIfTable 1 }

		
		SleSynceIfEntry ::=
			SEQUENCE { 
				sleSynceIfIfIndex
					Integer32,
				sleSynceIfName
					OCTET STRING,
				sleSynceIfInputSource
					INTEGER,
				sleSynceIfOoutputSource
					INTEGER,
				sleSynceIfPriority
					INTEGER,
				sleSynceIfRecvQl
					SynceClockQualityLevel,
				sleSynceIfSendQl
					SynceClockQualityLevel,
				sleSynceIfHoldOff
					INTEGER,
				sleSynceIfWaitToRestore
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSynceIfIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSynceIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSynceIfInputSource OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleSynceIfOoutputSource OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleSynceIfPriority OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleSynceIfRecvQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleSynceIfSendQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleSynceIfHoldOff OBJECT-TYPE
			SYNTAX INTEGER (300..1800)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleSynceIfWaitToRestore OBJECT-TYPE
			SYNTAX INTEGER (0..12)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfEntry 9 }

		
		-- *******.4.1.6296.**********
		sleSynceIfControl OBJECT IDENTIFIER ::= { sleSynceIf 2 }

		
		-- *******.4.1.6296.**********.1
		sleSynceIfControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSynceIfAddInputSource(1),
				setSynceIfDelInputSource(2),
				setSynceIfAddOutputSource(3),
				setSynceIfDelOutputSource(4),
				setSynceIfRecvQl(5),
				setSynceIfSendQl(6),
				setSynceIfHoldOff(7),
				setSynceIfWaitToRestore(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceIfControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSynceIfControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSynceIfControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSynceIfControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSynceIfControlIfIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSynceIfControlPriority OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleSynceIfControlConfigRecvQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleSynceIfControlConfigSendQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleSynceIfControlHoldOff OBJECT-TYPE
			SYNTAX INTEGER (300..1800)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleSynceIfControlWaitToRestore OBJECT-TYPE
			SYNTAX INTEGER (0..12)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceIfControl 11 }

		
		-- *******.4.1.6296.**********
		sleSynceIfNotification OBJECT IDENTIFIER ::= { sleSynceIf 3 }

		
		-- *******.4.1.6296.**********.1
		sleSynceIfAddInputSourceChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex, 
				sleSynceIfControlPriority }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceIfDelInputSourceChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleSynceIfAddOutputSourceChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleSynceIfDelOutputSourceChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleSynceIfRecvQlChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex, 
				sleSynceIfControlConfigRecvQl }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleSynceIfSendQlChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex, 
				sleSynceIfControlConfigSendQl }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 6 }

		
		-- *******.4.1.6296.**********.7
		sleSynceIfHoldOffChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex, 
				sleSynceIfControlHoldOff }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 7 }

		
		-- *******.4.1.6296.**********.8
		sleSynceIfWaitToRestoreChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceIfControlRequest, sleSynceIfControlStatus, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex, 
				sleSynceIfControlWaitToRestore }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceIfNotification 8 }

		
		-- *******.4.1.6296.101.93.4
		sleSynceInputSource OBJECT IDENTIFIER ::= { sleSynce 4 }

		
		-- *******.4.1.6296.**********
		sleSynceInputSourceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSynceInputSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSource 1 }

		
		-- *******.4.1.6296.**********.1
		sleSynceInputSourceEntry OBJECT-TYPE
			SYNTAX SleSynceInputSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSynceInputSourceIfIndex }
			::= { sleSynceInputSourceTable 1 }

		
		SleSynceInputSourceEntry ::=
			SEQUENCE { 
				sleSynceInputSourceIfIndex
					Integer32,
				sleSynceInputSourceName
					OCTET STRING,
				sleSynceInputSourcePriority
					INTEGER,
				sleSynceInputSourceEsmcState
					INTEGER,
				sleSynceInputSourceSelected
					INTEGER,
				sleSynceInputSourceConfigRecvQl
					SynceClockQualityLevel,
				sleSynceInputSourceRecvQl
					SynceClockQualityLevel,
				sleSynceInputSourceSignalFail
					INTEGER,
				sleSynceInputSourceCmd
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSynceInputSourceIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSynceInputSourceName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSynceInputSourcePriority OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleSynceInputSourceEsmcState OBJECT-TYPE
			SYNTAX INTEGER
				{
				idle(0),
				start(1),
				ok(2),
				failed(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleSynceInputSourceSelected OBJECT-TYPE
			SYNTAX INTEGER
				{
				no(0),
				yes(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleSynceInputSourceConfigRecvQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleSynceInputSourceRecvQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleSynceInputSourceSignalFail OBJECT-TYPE
			SYNTAX INTEGER
				{
				no(0),
				yes(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleSynceInputSourceCmd OBJECT-TYPE
			SYNTAX INTEGER
				{
				lockout(1),
				manual(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceEntry 9 }

		
		-- *******.4.1.6296.**********
		sleSynceInputSourceControl OBJECT IDENTIFIER ::= { sleSynceInputSource 2 }

		
		-- *******.4.1.6296.**********.1
		sleSynceInputSourceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSynceInputsourceSwitch(1),
				setSynceInputsourceLockout(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceInputSourceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSynceInputSourceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSynceInputSourceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSynceInputSourceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSynceInputSourceControlIfIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSynceInputSourceControlSwitchType OBJECT-TYPE
			SYNTAX INTEGER
				{
				clear(1),
				manual(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleSynceInputSourceControlLockoutState OBJECT-TYPE
			SYNTAX INTEGER
				{
				clear(0),
				set(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceInputSourceControl 8 }

		
		-- *******.4.1.6296.**********
		sleSynceInputSourceNotification OBJECT IDENTIFIER ::= { sleSynceInputSource 3 }

		
		-- *******.4.1.6296.**********.1
		sleSynceInputSourceSwitchChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceInputSourceControlRequest, sleSynceInputSourceControlStatus, sleSynceInputSourceControlTimeStamp, sleSynceInputSourceControlReqResult, sleSynceInputSourceControlIfIndex, 
				sleSynceInputSourceControlSwitchType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceInputSourceNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSynceInputSourceLockoutChanged NOTIFICATION-TYPE
			OBJECTS { sleSynceInputSourceControlRequest, sleSynceInputSourceControlStatus, sleSynceInputSourceControlTimeStamp, sleSynceInputSourceControlReqResult, sleSynceInputSourceControlIfIndex, 
				sleSynceInputSourceControlLockoutState }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynceInputSourceNotification 2 }

		
		-- *******.4.1.6296.101.93.5
		sleSynceOutputSource OBJECT IDENTIFIER ::= { sleSynce 5 }

		
		-- *******.4.1.6296.**********
		sleSynceOutputSourceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSynceOutputSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceOutputSource 1 }

		
		-- *******.4.1.6296.**********.1
		sleSynceOutputSourceEntry OBJECT-TYPE
			SYNTAX SleSynceOutputSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSynceOutputSourceIfIndex }
			::= { sleSynceOutputSourceTable 1 }

		
		SleSynceOutputSourceEntry ::=
			SEQUENCE { 
				sleSynceOutputSourceIfIndex
					Integer32,
				sleSynceOutputSourceName
					OCTET STRING,
				sleSynceOutputSourceConfigSendQl
					SynceClockQualityLevel,
				sleSynceOutputSourceSystemQl
					SynceClockQualityLevel,
				sleSynceOutputSourceSendQl
					SynceClockQualityLevel
			 }

		-- *******.4.1.6296.**********.1.1
		sleSynceOutputSourceIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceOutputSourceEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSynceOutputSourceName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceOutputSourceEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSynceOutputSourceConfigSendQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceOutputSourceEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleSynceOutputSourceSystemQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceOutputSourceEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleSynceOutputSourceSendQl OBJECT-TYPE
			SYNTAX SynceClockQualityLevel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSynceOutputSourceEntry 5 }

		
		-- *******.4.1.6296.101.93.29
		sleSynceObjectGroup OBJECT-GROUP
			OBJECTS { sleSynceIfEnableIfIndex, sleSynceIfEnableName, sleSynceIfEnableState, sleSynceIfEnableControlRequest, sleSynceIfEnableControlStatus, 
				sleSynceIfEnableControlTimer, sleSynceIfEnableControlTimeStamp, sleSynceIfEnableControlReqResult, sleSynceIfEnableControlIfIndex, sleSynceIfEnableControlState, 
				sleSynceIfIfIndex, sleSynceIfName, sleSynceIfInputSource, sleSynceIfOoutputSource, sleSynceIfPriority, 
				sleSynceIfRecvQl, sleSynceIfSendQl, sleSynceIfHoldOff, sleSynceIfWaitToRestore, sleSynceIfControlRequest, 
				sleSynceIfControlStatus, sleSynceIfControlTimer, sleSynceIfControlTimeStamp, sleSynceIfControlReqResult, sleSynceIfControlIfIndex, 
				sleSynceIfControlPriority, sleSynceIfControlConfigRecvQl, sleSynceIfControlConfigSendQl, sleSynceIfControlHoldOff, sleSynceIfControlWaitToRestore, 
				sleSynceInputSourceIfIndex, sleSynceInputSourceName, sleSynceInputSourceEsmcState, sleSynceInputSourceSelected, sleSynceInputSourceConfigRecvQl, 
				sleSynceInputSourceRecvQl, sleSynceInputSourceSignalFail, sleSynceInputSourceCmd, sleSynceInputSourceControlIfIndex, sleSynceInputSourceControlSwitchType, 
				sleSynceInputSourceControlLockoutState, sleSynceOutputSourceIfIndex, sleSynceOutputSourceName, sleSynceOutputSourceConfigSendQl, sleSynceOutputSourceSystemQl, 
				sleSynceOutputSourceSendQl, sleSynceSyncOption, sleSynceSelectionMode, sleSynceModeControlRequest, sleSynceModeControlStatus, 
				sleSynceModeControlTimer, sleSynceModeControlTimeStamp, sleSynceModeControlReqResult, sleSynceModeControlSyncOption, sleSynceModeControlSelectionMode, 
				sleSynceInputSourcePriority, sleSynceInputSourceControlRequest, sleSynceInputSourceControlStatus, sleSynceInputSourceControlTimer, sleSynceInputSourceControlTimeStamp, 
				sleSynceInputSourceControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynce 29 }

		
		-- *******.4.1.6296.101.93.30
		sleSynceNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleSynceIfEnableStateChanged, sleSynceIfAddInputSourceChanged, sleSynceIfDelInputSourceChanged, sleSynceIfAddOutputSourceChanged, sleSynceIfDelOutputSourceChanged, 
				sleSynceIfRecvQlChanged, sleSynceIfSendQlChanged, sleSynceIfHoldOffChanged, sleSynceIfWaitToRestoreChanged, sleSynceInputSourceSwitchChanged, 
				sleSynceInputSourceLockoutChanged, sleSynceSyncOptionChanged, sleSynceSelectionModeChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSynce 30 }

		
	
	END

--
-- sle-synce-mib.mib
--
