--
-- sle-rip-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Monday, March 23, 2015 at 14:56:23
--

	SLE-RIP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.54
		sleRIP MODULE-IDENTITY 
			LAST-UPDATED "200412291441Z"		-- December 29, 2004 at 14:41 GMT
			ORGANIZATION 
				"HANASOFT"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains information about RIP version 2."
			REVISION "201003211954Z"		-- March 21, 2010 at 19:54 GMT
			DESCRIPTION 
				"OSFPv2"
			::= { sleMgmt 54 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.54.1
		sleRIPBase OBJECT IDENTIFIER ::= { sleRIP 1 }

		
		-- *******.4.1.6296.**********
		sleRIPBaseInfo OBJECT IDENTIFIER ::= { sleRIPBase 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPVersion OBJECT-TYPE
			SYNTAX INTEGER (0..2)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPBaseInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPDefaultMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1 }
			::= { sleRIPBaseInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPDefaultInformationOrg OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleRIPBaseInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPDefaultDistance OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPBaseInfo 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPRecvBufferSize OBJECT-TYPE
			SYNTAX INTEGER (8192..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 215040 }
			::= { sleRIPBaseInfo 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPMaximumPaths OBJECT-TYPE
			SYNTAX INTEGER (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1 }
			::= { sleRIPBaseInfo 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPMaximumPrefixRoute OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1 }
			::= { sleRIPBaseInfo 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPMaximumPrefixRoutePercent OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 75 }
			::= { sleRIPBaseInfo 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPMetricSumApply OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleRIPBaseInfo 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPBasicUpdateTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 30 }
			::= { sleRIPBaseInfo 10 }

		
		-- *******.4.1.6296.**********.11
		sleRIPBasicTimeoutTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 180 }
			::= { sleRIPBaseInfo 11 }

		
		-- *******.4.1.6296.**********.12
		sleRIPBasicGarbageTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 120 }
			::= { sleRIPBaseInfo 12 }

		
		-- *******.4.1.6296.**********.13
		sleRIPRestartPeriod OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPBaseInfo 13 }

		
		-- *******.4.1.6296.**********
		sleRIPBaseControl OBJECT IDENTIFIER ::= { sleRIPBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPMode(1),
				deleteRIPMode(2),
				setRIPVersion(3),
				setRIPDefaultMetric(4),
				setRIPDefaultInformationOrg(5),
				setRIPDefaultDistance(6),
				setRIPRecvBufferSize(7),
				setRIPMaximumPaths(8),
				setRIPMaximumPrefixRouteProfile(9),
				setRIPMetricSumApply(10),
				setRIPBasicTimersProfile(11),
				setRIPRestartPeriod(12),
				unsetRIPRestartPeriod(13),
				clearRIPAll(14),
				clearRIPRoute(15),
				clearRIPProtoType(16)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPBaseControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.2"
			::= { sleRIPBaseControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.3"
			::= { sleRIPBaseControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.4"
			::= { sleRIPBaseControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.5"
			::= { sleRIPBaseControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPControlVersion OBJECT-TYPE
			SYNTAX INTEGER (0..2)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.6"
			::= { sleRIPBaseControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPControlDefaultMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.7"
			DEFVAL { 1 }
			::= { sleRIPBaseControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPControlDefaultInformationOrg OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.8"
			DEFVAL { 0 }
			::= { sleRIPBaseControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPControlDefaultDistance OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPBaseControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPControlRecvBufferSize OBJECT-TYPE
			SYNTAX INTEGER (8192..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.9"
			DEFVAL { 215040 }
			::= { sleRIPBaseControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleRIPControlMaximumPaths OBJECT-TYPE
			SYNTAX INTEGER (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.10"
			DEFVAL { 1 }
			::= { sleRIPBaseControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleRIPControlMaximumPrefixRoute OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.11"
			DEFVAL { 1 }
			::= { sleRIPBaseControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleRIPControlMaximumPrefixRoutePercent OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.12"
			DEFVAL { 75 }
			::= { sleRIPBaseControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleRIPControlMetricSumApply OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.13"
			DEFVAL { 0 }
			::= { sleRIPBaseControl 14 }

		
		-- *******.4.1.6296.**********.15
		sleRIPControlBasicUpdateTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.14"
			DEFVAL { 30 }
			::= { sleRIPBaseControl 15 }

		
		-- *******.4.1.6296.**********.16
		sleRIPControlBasicTimeoutTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.15"
			DEFVAL { 180 }
			::= { sleRIPBaseControl 16 }

		
		-- *******.4.1.6296.**********.17
		sleRIPControlBasicGarbageTimer OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.16"
			DEFVAL { 180 }
			::= { sleRIPBaseControl 17 }

		
		-- *******.4.1.6296.**********.18
		sleRIPControlRestartPeriod OBJECT-TYPE
			SYNTAX INTEGER (5..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.17"
			DEFVAL { 180 }
			::= { sleRIPBaseControl 18 }

		
		-- *******.4.1.6296.**********.19
		sleRIPControlClearRoutePrefix OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.17"
			::= { sleRIPBaseControl 19 }

		
		-- *******.4.1.6296.**********.20
		sleRIPControlClearRouteMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.17"
			DEFVAL { 180 }
			::= { sleRIPBaseControl 20 }

		
		-- *******.4.1.6296.**********.21
		sleRIPControlClearProtoTpye OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				bgp(4),
				ospf(5),
				rip(6)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"*******.4.1.6296.**********.17"
			::= { sleRIPBaseControl 21 }

		
		-- *******.4.1.6296.**********
		sleRIPBaseNotification OBJECT IDENTIFIER ::= { sleRIPBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPModeCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPModeDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPVersionChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPVersion }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPDefaultMetricChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPDefaultMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPDefaultInformationOrgChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPDefaultInformationOrg }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPDefaultDistanceChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPDefaultDistance }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPRecvBufferSizeChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPRecvBufferSize }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPMaximumPathsChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPMaximumPaths }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPMaximumPrefixProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPMaximumPrefixRoute, sleRIPMaximumPrefixRoutePercent
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPMetricSumApplyChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPMetricSumApply }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 10 }

		
		-- *******.4.1.6296.**********.11
		sleRIPTimersChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPBasicUpdateTimer, sleRIPBasicTimeoutTimer, 
				sleRIPBasicGarbageTimer }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 11 }

		
		-- *******.4.1.6296.**********.12
		sleRIPRestartPeriodChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPRestartPeriod }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 12 }

		
		-- *******.4.1.6296.**********.13
		sleRIPRestartRemoved NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPControlRestartPeriod }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 13 }

		
		-- *******.4.1.6296.**********.14
		sleRIPAllCleared NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 14 }

		
		-- *******.4.1.6296.**********.15
		sleRIPRouteCleared NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPControlClearRoutePrefix, sleRIPControlClearRouteMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 15 }

		
		-- *******.4.1.6296.**********.16
		sleRIPProtoTypeCleared NOTIFICATION-TYPE
			OBJECTS { sleRIPControlRequest, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPControlClearProtoTpye }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPBaseNotification 16 }

		
		-- *******.4.1.6296.101.54.2
		sleRIPNetwork OBJECT IDENTIFIER ::= { sleRIP 2 }

		
		-- *******.4.1.6296.**********
		sleRIPNetworkIP OBJECT IDENTIFIER ::= { sleRIPNetwork 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPNetworkIPTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPNetworkIPEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIP 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleRIPNetworkIPEntry OBJECT-TYPE
			SYNTAX SleRIPNetworkIPEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPNetworkIPAddr, sleRIPNetworkIPMask }
			::= { sleRIPNetworkIPTable 1 }

		
		SleRIPNetworkIPEntry ::=
			SEQUENCE { 
				sleRIPNetworkIPAddr
					IpAddress,
				sleRIPNetworkIPMask
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleRIPNetworkIPAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleRIPNetworkIPMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPEntry 2 }

		
		-- *******.4.1.6296.**********.2
		sleRIPNetworkIPControl OBJECT IDENTIFIER ::= { sleRIPNetworkIP 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleRIPNetworkIPControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPNetworkIP(1),
				deleteRIPNetworkIP(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleRIPNetworkIPControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleRIPNetworkIPControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleRIPNetworkIPControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleRIPNetworkIPControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleRIPNetworkIPControlIPAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleRIPNetworkIPControlIPMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkIPControl 7 }

		
		-- *******.4.1.6296.**********.3
		sleRIPNetworkIPNotification OBJECT IDENTIFIER ::= { sleRIPNetworkIP 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleRIPNetworkIPCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPNetworkIPControlRequest, sleRIPNetworkIPControlTimeStamp, sleRIPNetworkIPControlReqResult, sleRIPNetworkIPAddr, sleRIPNetworkIPMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPNetworkIPNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleRIPNetworkIPDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPNetworkIPControlRequest, sleRIPNetworkIPControlTimeStamp, sleRIPNetworkIPControlReqResult, sleRIPNetworkIPControlIPAddr, sleRIPNetworkIPControlIPMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPNetworkIPNotification 2 }

		
		-- *******.4.1.6296.**********
		sleRIPNetworkInterface OBJECT IDENTIFIER ::= { sleRIPNetwork 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPNetworkInterfaceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPNetworkInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterface 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleRIPNetworkInterfaceEntry OBJECT-TYPE
			SYNTAX SleRIPNetworkInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPNetworkInterfaceName }
			::= { sleRIPNetworkInterfaceTable 1 }

		
		SleRIPNetworkInterfaceEntry ::=
			SEQUENCE { 
				sleRIPNetworkInterfaceName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleRIPNetworkInterfaceName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterfaceEntry 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPNetworkInterfaceControl OBJECT IDENTIFIER ::= { sleRIPNetworkInterface 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleRIPNetworkInterfaceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPNetworkIfname(1),
				deleteRIPNetworkIfname(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterfaceControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleRIPNetworkInterfaceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterfaceControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleRIPNetworkInterfaceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterfaceControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleRIPNetworkInterfaceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterfaceControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleRIPNetworkInterfaceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterfaceControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleRIPNetworkInterfaceControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNetworkInterfaceControl 6 }

		
		-- *******.4.1.6296.**********.3
		sleRIPNetworkInterfaceNotification OBJECT IDENTIFIER ::= { sleRIPNetworkInterface 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleRIPNetworkInterfaceCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPNetworkInterfaceControlRequest, sleRIPNetworkInterfaceControlTimeStamp, sleRIPNetworkInterfaceControlReqResult, sleRIPNetworkInterfaceName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPNetworkInterfaceNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleRIPNetworkInterfaceDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPNetworkInterfaceControlRequest, sleRIPNetworkInterfaceControlTimeStamp, sleRIPNetworkInterfaceControlReqResult, sleRIPNetworkInterfaceControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPNetworkInterfaceNotification 2 }

		
		-- *******.4.1.6296.101.54.3
		sleRIPNeighbor OBJECT IDENTIFIER ::= { sleRIP 3 }

		
		-- *******.4.1.6296.**********
		sleRIPNeighborTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPNeighborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighbor 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPNeighborEntry OBJECT-TYPE
			SYNTAX SleRIPNeighborEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPNeighborIPAddr }
			::= { sleRIPNeighborTable 1 }

		
		SleRIPNeighborEntry ::=
			SEQUENCE { 
				sleRIPNeighborIPAddr
					IpAddress
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPNeighborIPAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighborEntry 1 }

		
		-- *******.4.1.6296.**********
		sleRIPNeighborControl OBJECT IDENTIFIER ::= { sleRIPNeighbor 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPNeighborControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPNeighbor(1),
				deleteRIPNeighbor(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighborControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPNeighborControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighborControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPNeighborControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighborControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPNeighborControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighborControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPNeighborControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighborControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPNeighborControlIPAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPNeighborControl 6 }

		
		-- *******.4.1.6296.**********
		sleRIPNeighborNotification OBJECT IDENTIFIER ::= { sleRIPNeighbor 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPNeighborCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPNeighborControlRequest, sleRIPNeighborControlTimeStamp, sleRIPNeighborControlReqResult, sleRIPNeighborIPAddr }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPNeighborNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPNeighborDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPNeighborControlRequest, sleRIPNeighborControlTimeStamp, sleRIPNeighborControlReqResult, sleRIPNeighborControlIPAddr }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPNeighborNotification 2 }

		
		-- *******.4.1.6296.101.54.4
		sleRIPStaticRoute OBJECT IDENTIFIER ::= { sleRIP 4 }

		
		-- *******.4.1.6296.**********
		sleRIPStaticRouteTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPStaticRouteEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRoute 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPStaticRouteEntry OBJECT-TYPE
			SYNTAX SleRIPStaticRouteEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPStaticRouteIPAddr, sleRIPStaticRouteIPMask }
			::= { sleRIPStaticRouteTable 1 }

		
		SleRIPStaticRouteEntry ::=
			SEQUENCE { 
				sleRIPStaticRouteIPAddr
					IpAddress,
				sleRIPStaticRouteIPMask
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPStaticRouteIPAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPStaticRouteIPMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteEntry 2 }

		
		-- *******.4.1.6296.**********
		sleRIPStaticRouteControl OBJECT IDENTIFIER ::= { sleRIPStaticRoute 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPStaticRouteControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPStaticRoute(1),
				deleteRIPStaticRoute(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPStaticRouteControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPStaticRouteControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPStaticRouteControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPStaticRouteControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPStaticRouteControlIPAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPStaticRouteControlIPMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPStaticRouteControl 7 }

		
		-- *******.4.1.6296.**********
		sleRIPStaticRouteNotification OBJECT IDENTIFIER ::= { sleRIPStaticRoute 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPStaticRouteCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPStaticRouteControlRequest, sleRIPStaticRouteControlTimeStamp, sleRIPStaticRouteControlReqResult, sleRIPStaticRouteIPAddr, sleRIPStaticRouteIPMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPStaticRouteNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPStaticRouteDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPStaticRouteControlRequest, sleRIPStaticRouteControlTimeStamp, sleRIPStaticRouteControlReqResult, sleRIPStaticRouteControlIPAddr, sleRIPStaticRouteControlIPMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPStaticRouteNotification 2 }

		
		-- *******.4.1.6296.101.54.5
		sleRIPAdminDistance OBJECT IDENTIFIER ::= { sleRIP 5 }

		
		-- *******.4.1.6296.**********
		sleRIPAdminDistanceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPAdminDistanceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistance 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPAdminDistanceEntry OBJECT-TYPE
			SYNTAX SleRIPAdminDistanceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPAdminDistanceValue, sleRIPAdminDistanceAddr, sleRIPAdminDistanceMask }
			::= { sleRIPAdminDistanceTable 1 }

		
		SleRIPAdminDistanceEntry ::=
			SEQUENCE { 
				sleRIPAdminDistanceValue
					INTEGER,
				sleRIPAdminDistanceAddr
					IpAddress,
				sleRIPAdminDistanceMask
					INTEGER,
				sleRIPAdminDistanceAccessName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPAdminDistanceValue OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPAdminDistanceAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPAdminDistanceMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleRIPAdminDistanceAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceEntry 4 }

		
		-- *******.4.1.6296.**********
		sleRIPAdminDistanceControl OBJECT IDENTIFIER ::= { sleRIPAdminDistance 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPAdminDistanceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPAdminDistance(1),
				deleteRIPAdminDistance(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPAdminDistanceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPAdminDistanceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPAdminDistanceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPAdminDistanceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPAdminDistanceControlValue OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPAdminDistanceControlAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPAdminDistanceControlMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPAdminDistanceControlAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPAdminDistanceControl 9 }

		
		-- *******.4.1.6296.**********
		sleRIPAdminDistanceNotification OBJECT IDENTIFIER ::= { sleRIPAdminDistance 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPAdminDistanceCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPAdminDistanceControlRequest, sleRIPAdminDistanceControlTimeStamp, sleRIPAdminDistanceControlReqResult, sleRIPAdminDistanceValue, sleRIPAdminDistanceAddr, 
				sleRIPAdminDistanceMask, sleRIPAdminDistanceAccessName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPAdminDistanceNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPAdminDistanceDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPAdminDistanceControlRequest, sleRIPAdminDistanceControlTimeStamp, sleRIPAdminDistanceControlReqResult, sleRIPAdminDistanceControlValue, sleRIPAdminDistanceControlAddr, 
				sleRIPAdminDistanceControlMask, sleRIPAdminDistanceControlAccessName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPAdminDistanceNotification 2 }

		
		-- *******.4.1.6296.101.54.6
		sleRIPDistribute OBJECT IDENTIFIER ::= { sleRIP 6 }

		
		-- *******.4.1.6296.**********
		sleRIPDistributeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPDistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistribute 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPDistributeEntry OBJECT-TYPE
			SYNTAX SleRIPDistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPDistributeIfName }
			::= { sleRIPDistributeTable 1 }

		
		SleRIPDistributeEntry ::=
			SEQUENCE { 
				sleRIPDistributeIfName
					OCTET STRING,
				sleRIPDistributeInAccessName
					OCTET STRING,
				sleRIPDistributeOutAccessName
					OCTET STRING,
				sleRIPDistributeInPrefixName
					OCTET STRING,
				sleRIPDistributeOutPrefixName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPDistributeIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPDistributeInAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPDistributeOutAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleRIPDistributeInPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleRIPDistributeOutPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeEntry 5 }

		
		-- *******.4.1.6296.**********
		sleRIPDistributeControl OBJECT IDENTIFIER ::= { sleRIPDistribute 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPDistributeControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPDistributeInAccess(1),
				deleteRIPDistributeInAccess(2),
				createRIPDistributeOutAccess(3),
				deleteRIPDistributeOutAccess(4),
				createRIPDistributeInPrefix(5),
				deleteRIPDistributeInPrefix(6),
				createRIPDistributeOutPrefix(7),
				deleteRIPDistributeOutPrefix(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPDistributeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPDistributeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPDistributeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPDistributeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPDistributeControlIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPDistributeControlInAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPDistributeControlOutAccessName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPDistributeControlInPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPDistributeControlOutPrefixName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPDistributeControl 10 }

		
		-- *******.4.1.6296.**********
		sleRIPDistributeNotification OBJECT IDENTIFIER ::= { sleRIPDistribute 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPDistributeInAccessCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeIfName, sleRIPDistributeInAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPDistributeInAccessDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeControlIfName, sleRIPDistributeControlInAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPDistributeOutAccessCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeIfName, sleRIPDistributeOutAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPDistributeOutAccessDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeControlIfName, sleRIPDistributeControlOutAccessName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPDistributeInPrefixCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeIfName, sleRIPDistributeInPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPDistributeInPrefixDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeControlIfName, sleRIPDistributeControlInPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPDistributeOutPrefixCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeIfName, sleRIPDistributeOutPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPDistributeOutPrefixDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPDistributeControlRequest, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeControlIfName, sleRIPDistributeControlOutPrefixName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPDistributeNotification 8 }

		
		-- *******.4.1.6296.101.54.7
		sleRIPOffsetList OBJECT IDENTIFIER ::= { sleRIP 7 }

		
		-- *******.4.1.6296.**********
		sleRIPOffsetListTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPOffsetListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetList 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPOffsetListEntry OBJECT-TYPE
			SYNTAX SleRIPOffsetListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPOffsetListIfname }
			::= { sleRIPOffsetListTable 1 }

		
		SleRIPOffsetListEntry ::=
			SEQUENCE { 
				sleRIPOffsetListIfname
					OCTET STRING,
				sleRIPOffsetListInAccName
					OCTET STRING,
				sleRIPOffsetListInMetric
					INTEGER,
				sleRIPOffsetListOutAccName
					OCTET STRING,
				sleRIPOffsetListOutMetric
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPOffsetListIfname OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPOffsetListInAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPOffsetListInMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleRIPOffsetListOutAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleRIPOffsetListOutMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListEntry 5 }

		
		-- *******.4.1.6296.**********
		sleRIPOffsetListControl OBJECT IDENTIFIER ::= { sleRIPOffsetList 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPOffsetListControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPOffsetListIn(1),
				deleteRIPOffsetListIn(2),
				createRIPOffsetListOut(3),
				deleteRIPOffsetListOut(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPOffsetListControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPOffsetListControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPOffsetListControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPOffsetListControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPOffsetListControlIfname OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPOffsetListControlInAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPOffsetListControlInMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRIPOffsetListControlOutAccName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleRIPOffsetListControlOutMetric OBJECT-TYPE
			SYNTAX INTEGER (0..16)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPOffsetListControl 10 }

		
		-- *******.4.1.6296.**********
		sleRIPOffsetListNotification OBJECT IDENTIFIER ::= { sleRIPOffsetList 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPOffsetListCreatedIn NOTIFICATION-TYPE
			OBJECTS { sleRIPOffsetListControlRequest, sleRIPOffsetListControlTimeStamp, sleRIPOffsetListControlReqResult, sleRIPOffsetListIfname, sleRIPOffsetListInAccName, 
				sleRIPOffsetListInMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPOffsetListNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPOffsetListDeletedIn NOTIFICATION-TYPE
			OBJECTS { sleRIPOffsetListControlRequest, sleRIPOffsetListControlTimeStamp, sleRIPOffsetListControlReqResult, sleRIPOffsetListControlIfname, sleRIPOffsetListControlInAccName, 
				sleRIPOffsetListControlInMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPOffsetListNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPOffsetListCreatedOut NOTIFICATION-TYPE
			OBJECTS { sleRIPOffsetListControlRequest, sleRIPOffsetListControlTimeStamp, sleRIPOffsetListControlReqResult, sleRIPOffsetListIfname, sleRIPDistributeOutAccessName, 
				sleRIPDistributeOutPrefixName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPOffsetListNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPOffsetListDeletedOut NOTIFICATION-TYPE
			OBJECTS { sleRIPOffsetListControlRequest, sleRIPOffsetListControlTimeStamp, sleRIPOffsetListControlReqResult, sleRIPOffsetListControlIfname, sleRIPOffsetListControlOutAccName, 
				sleRIPOffsetListControlOutMetric }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPOffsetListNotification 4 }

		
		-- *******.4.1.6296.101.54.8
		sleRIPRedistribute OBJECT IDENTIFIER ::= { sleRIP 8 }

		
		-- *******.4.1.6296.**********
		sleRIPRedistributeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPRedistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistribute 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPRedistributeEntry OBJECT-TYPE
			SYNTAX SleRIPRedistributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPRedistType }
			::= { sleRIPRedistributeTable 1 }

		
		SleRIPRedistributeEntry ::=
			SEQUENCE { 
				sleRIPRedistType
					INTEGER,
				sleRIPRedistMetric
					INTEGER,
				sleRIPRedistRouteMapName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPRedistType OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				bgp(4),
				ospf(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleRIPRedistMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16777214)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleRIPRedistRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeEntry 3 }

		
		-- *******.4.1.6296.**********
		sleRIPRedistributeControl OBJECT IDENTIFIER ::= { sleRIPRedistribute 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPRedistControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPRedistribute(1),
				deleteRIPRedistribute(2),
				setRIPRedistribute(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPRedistControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPRedistControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPRedistControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPRedistControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPRedistControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				bgp(4),
				ospf(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRIPRedistControlMetric OBJECT-TYPE
			SYNTAX INTEGER (1..16777214)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRIPRedistControlRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRedistributeControl 8 }

		
		-- *******.4.1.6296.**********
		sleRIPRedistributeNotification OBJECT IDENTIFIER ::= { sleRIPRedistribute 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPRedistributeCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPRedistControlRequest, sleRIPRedistControlTimeStamp, sleRIPRedistControlReqResult, sleRIPRedistType, sleRIPRedistMetric, 
				sleRIPRedistRouteMapName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPRedistributeNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPRedistributeDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPRedistControlRequest, sleRIPRedistControlTimeStamp, sleRIPRedistControlReqResult, sleRIPRedistControlType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPRedistributeNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPRedistributeChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPRedistControlRequest, sleRIPRedistControlTimeStamp, sleRIPRedistControlReqResult, sleRIPRedistType, sleRIPRedistMetric, 
				sleRIPRedistRouteMapName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPRedistributeNotification 3 }

		
		-- *******.4.1.6296.101.54.9
		sleRIPPassInterface OBJECT IDENTIFIER ::= { sleRIP 9 }

		
		-- *******.4.1.6296.**********
		sleRIPPassInterfaceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPPassInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterface 1 }

		
		-- *******.4.1.6296.**********.1
		sleRIPPassInterfaceEntry OBJECT-TYPE
			SYNTAX SleRIPPassInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPPassInterfaceName }
			::= { sleRIPPassInterfaceTable 1 }

		
		SleRIPPassInterfaceEntry ::=
			SEQUENCE { 
				sleRIPPassInterfaceName
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleRIPPassInterfaceName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterfaceEntry 1 }

		
		-- *******.4.1.6296.**********
		sleRIPPassInterfaceControl OBJECT IDENTIFIER ::= { sleRIPPassInterface 2 }

		
		-- *******.4.1.6296.**********.1
		sleRIPPassInterfaceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPPassInterface(1),
				deleteRIPPassInterface(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterfaceControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPPassInterfaceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterfaceControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRIPPassInterfaceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterfaceControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRIPPassInterfaceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterfaceControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRIPPassInterfaceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterfaceControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRIPPassInterfaceControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPPassInterfaceControl 6 }

		
		-- *******.4.1.6296.**********
		sleRIPPassInterfaceNotification OBJECT IDENTIFIER ::= { sleRIPPassInterface 3 }

		
		-- *******.4.1.6296.**********.1
		sleRIPPassInterfaceCreated NOTIFICATION-TYPE
			OBJECTS { sleRIPPassInterfaceControlRequest, sleRIPPassInterfaceControlTimeStamp, sleRIPPassInterfaceControlReqResult, sleRIPPassInterfaceName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPPassInterfaceNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRIPPassInterfaceDeleted NOTIFICATION-TYPE
			OBJECTS { sleRIPPassInterfaceControlRequest, sleRIPPassInterfaceControlTimeStamp, sleRIPPassInterfaceControlReqResult, sleRIPPassInterfaceControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPPassInterfaceNotification 2 }

		
		-- *******.4.1.6296.101.54.10
		sleRIPInterface OBJECT IDENTIFIER ::= { sleRIP 10 }

		
		-- *******.4.1.6296.***********
		sleRIPInterfaceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterface 1 }

		
		-- *******.4.1.6296.***********.1
		sleRIPInterfaceEntry OBJECT-TYPE
			SYNTAX SleRIPInterfaceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPInterfaceIndex }
			::= { sleRIPInterfaceTable 1 }

		
		SleRIPInterfaceEntry ::=
			SEQUENCE { 
				sleRIPInterfaceIndex
					INTEGER,
				sleRIPInterfaceRecvVer
					INTEGER,
				sleRIPInterfaceRecvPacket
					INTEGER,
				sleRIPInterfaceSendVer
					INTEGER,
				sleRIPInterfaceSendPacket
					INTEGER,
				sleRIPInterfaceSplitHorizonMode
					INTEGER,
				sleRIPInterfaceAuthMode
					INTEGER,
				sleRIPInterfaceAuthString
					OCTET STRING,
				sleRIPInterfaceAuthKeyChain
					OCTET STRING
			 }

		-- *******.4.1.6296.***********.1.1
		sleRIPInterfaceIndex OBJECT-TYPE
			SYNTAX INTEGER (0..4097)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleRIPInterfaceRecvVer OBJECT-TYPE
			SYNTAX INTEGER
				{
				default(0),
				ripv1(1),
				ripv2(2),
				ripv1-ripv2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleRIPInterfaceRecvPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 3 }

		
		-- *******.4.1.6296.***********.1.4
		sleRIPInterfaceSendVer OBJECT-TYPE
			SYNTAX INTEGER
				{
				default(0),
				ripv1(1),
				ripv2(2),
				ripv1-ripv2(3),
				ripv1-compatible(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 4 }

		
		-- *******.4.1.6296.***********.1.5
		sleRIPInterfaceSendPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 5 }

		
		-- *******.4.1.6296.***********.1.6
		sleRIPInterfaceSplitHorizonMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				withoutPoisonedReverse(1),
				withPoisonedReverse(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 6 }

		
		-- *******.4.1.6296.***********.1.7
		sleRIPInterfaceAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				noauth(0),
				text(1),
				md5(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 7 }

		
		-- *******.4.1.6296.***********.1.8
		sleRIPInterfaceAuthString OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 8 }

		
		-- *******.4.1.6296.***********.1.9
		sleRIPInterfaceAuthKeyChain OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceEntry 9 }

		
		-- *******.4.1.6296.***********
		sleRIPInterfaceControl OBJECT IDENTIFIER ::= { sleRIPInterface 2 }

		
		-- *******.4.1.6296.***********.1
		sleRIPInterfaceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setRIPInterfaceRecvVer(1),
				setRIPInterfaceRecvPacketEnable(2),
				setRIPInterfaceSendVer(3),
				setRIPInterfaceSendPacketEnable(4),
				setRIPInterfaceSplitHorizonMode(5),
				setRIPInterfaceAuthMode(6),
				setRIPInterfaceAuthString(7),
				setRIPInterfaceAuthKeyChain(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 1 }

		
		-- *******.4.1.6296.***********.2
		sleRIPInterfaceControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 2 }

		
		-- *******.4.1.6296.***********.3
		sleRIPInterfaceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 3 }

		
		-- *******.4.1.6296.***********.4
		sleRIPInterfaceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 4 }

		
		-- *******.4.1.6296.***********.5
		sleRIPInterfaceControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 5 }

		
		-- *******.4.1.6296.***********.6
		sleRIPInterfaceControlIndex OBJECT-TYPE
			SYNTAX INTEGER (0..4097)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 6 }

		
		-- *******.4.1.6296.***********.7
		sleRIPInterfaceControlRecvVer OBJECT-TYPE
			SYNTAX INTEGER
				{
				default(0),
				ripv1(1),
				ripv2(2),
				ripv1-ripv2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 7 }

		
		-- *******.4.1.6296.***********.8
		sleRIPInterfaceControlRecvPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 8 }

		
		-- *******.4.1.6296.***********.9
		sleRIPInterfaceControlSendVer OBJECT-TYPE
			SYNTAX INTEGER
				{
				default(0),
				ripv1(1),
				ripv2(2),
				ripv1-ripv2(3),
				ripv1-compatible(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 9 }

		
		-- *******.4.1.6296.***********.10
		sleRIPInterfaceControlSendPacket OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 10 }

		
		-- *******.4.1.6296.***********.11
		sleRIPInterfaceControlSplitHorizonMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				withoutPoisonedReverse(1),
				withPoisonedReverse(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 11 }

		
		-- *******.4.1.6296.***********.12
		sleRIPInterfaceControlAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				noauth(0),
				text(1),
				md5(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 12 }

		
		-- *******.4.1.6296.***********.13
		sleRIPInterfaceControlAuthString OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 13 }

		
		-- *******.4.1.6296.***********.14
		sleRIPInterfaceControlAuthKeyChain OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPInterfaceControl 14 }

		
		-- *******.4.1.6296.***********
		sleRIPInterfaceNotification OBJECT IDENTIFIER ::= { sleRIPInterface 3 }

		
		-- *******.4.1.6296.***********.1
		sleRIPInterfaceRecvVerChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceRecvVer
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 1 }

		
		-- *******.4.1.6296.***********.2
		sleRIPInterfaceRecvPacketChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceRecvPacket
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 2 }

		
		-- *******.4.1.6296.***********.3
		sleRIPInterfaceSendVerChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceSendVer
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 3 }

		
		-- *******.4.1.6296.***********.4
		sleRIPInterfaceSendPacketChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceSendPacket
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 4 }

		
		-- *******.4.1.6296.***********.5
		sleRIPInterfaceSplitHorizonModeChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceSplitHorizonMode
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 5 }

		
		-- *******.4.1.6296.***********.6
		sleRIPInterfaceAuthModeChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceAuthMode
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 6 }

		
		-- *******.4.1.6296.***********.7
		sleRIPInterfaceAuthStringChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceAuthString
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 7 }

		
		-- *******.4.1.6296.***********.8
		sleRIPInterfaceAuthKeyChainChanged NOTIFICATION-TYPE
			OBJECTS { sleRIPInterfaceControlRequest, sleRIPInterfaceControlTimeStamp, sleRIPInterfaceControlReqResult, sleRIPInterfaceIndex, sleRIPInterfaceAuthKeyChain
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIPInterfaceNotification 8 }

		
		-- *******.4.1.6296.101.54.11
		sleRIPRoutes OBJECT IDENTIFIER ::= { sleRIP 11 }

		
		-- *******.4.1.6296.***********
		sleRIPRoutesTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRIPRoutesEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutes 1 }

		
		-- *******.4.1.6296.***********.1
		sleRIPRoutesEntry OBJECT-TYPE
			SYNTAX SleRIPRoutesEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRIPRoutesType, sleRIPRoutesPrefix, sleRIPRoutesMask, sleRIPRoutesNextHop, sleRIPRoutesMetric
				 }
			::= { sleRIPRoutesTable 1 }

		
		SleRIPRoutesEntry ::=
			SEQUENCE { 
				sleRIPRoutesType
					INTEGER,
				sleRIPRoutesPrefix
					IpAddress,
				sleRIPRoutesMask
					INTEGER,
				sleRIPRoutesNextHop
					IpAddress,
				sleRIPRoutesMetric
					INTEGER,
				sleRIPRoutesNetworkFrom
					IpAddress,
				sleRIPRoutesIfName
					OCTET STRING,
				sleRIPRoutesUpTime
					TimeTicks
			 }

		-- *******.4.1.6296.***********.1.1
		sleRIPRoutesType OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				rip(4),
				ripng(5),
				ospf(6),
				ospfv3(7),
				bgp(8),
				isis(9)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleRIPRoutesPrefix OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleRIPRoutesMask OBJECT-TYPE
			SYNTAX INTEGER (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 3 }

		
		-- *******.4.1.6296.***********.1.4
		sleRIPRoutesNextHop OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 4 }

		
		-- *******.4.1.6296.***********.1.6
		sleRIPRoutesMetric OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 6 }

		
		-- *******.4.1.6296.***********.1.7
		sleRIPRoutesNetworkFrom OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 7 }

		
		-- *******.4.1.6296.***********.1.8
		sleRIPRoutesIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 8 }

		
		-- *******.4.1.6296.***********.1.9
		sleRIPRoutesUpTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRIPRoutesEntry 9 }

		
		-- *******.4.1.6296.101.54.12
		sleRIPGroup OBJECT-GROUP
			OBJECTS { sleRIPVersion, sleRIPDefaultMetric, sleRIPDefaultInformationOrg, sleRIPDefaultDistance, sleRIPRecvBufferSize, 
				sleRIPMaximumPaths, sleRIPMaximumPrefixRoute, sleRIPMaximumPrefixRoutePercent, sleRIPMetricSumApply, sleRIPBasicUpdateTimer, 
				sleRIPBasicTimeoutTimer, sleRIPBasicGarbageTimer, sleRIPRestartPeriod, sleRIPControlRequest, sleRIPControlStatus, 
				sleRIPControlTimer, sleRIPControlTimeStamp, sleRIPControlReqResult, sleRIPControlVersion, sleRIPControlDefaultMetric, 
				sleRIPControlDefaultInformationOrg, sleRIPControlDefaultDistance, sleRIPControlRecvBufferSize, sleRIPControlMaximumPaths, sleRIPControlMaximumPrefixRoute, 
				sleRIPControlMaximumPrefixRoutePercent, sleRIPControlMetricSumApply, sleRIPControlBasicUpdateTimer, sleRIPControlBasicTimeoutTimer, sleRIPControlBasicGarbageTimer, 
				sleRIPControlRestartPeriod, sleRIPControlClearRoutePrefix, sleRIPControlClearRouteMask, sleRIPControlClearProtoTpye, sleRIPNetworkIPAddr, 
				sleRIPNetworkIPMask, sleRIPNetworkIPControlRequest, sleRIPNetworkIPControlStatus, sleRIPNetworkIPControlTimer, sleRIPNetworkIPControlTimeStamp, 
				sleRIPNetworkIPControlReqResult, sleRIPNetworkIPControlIPAddr, sleRIPNetworkIPControlIPMask, sleRIPNetworkInterfaceName, sleRIPNetworkInterfaceControlRequest, 
				sleRIPNetworkInterfaceControlStatus, sleRIPNetworkInterfaceControlTimer, sleRIPNetworkInterfaceControlTimeStamp, sleRIPNetworkInterfaceControlReqResult, sleRIPNetworkInterfaceControlName, 
				sleRIPNeighborIPAddr, sleRIPNeighborControlRequest, sleRIPNeighborControlStatus, sleRIPNeighborControlTimer, sleRIPNeighborControlTimeStamp, 
				sleRIPNeighborControlReqResult, sleRIPNeighborControlIPAddr, sleRIPStaticRouteIPAddr, sleRIPStaticRouteIPMask, sleRIPStaticRouteControlRequest, 
				sleRIPStaticRouteControlStatus, sleRIPStaticRouteControlTimer, sleRIPStaticRouteControlTimeStamp, sleRIPStaticRouteControlReqResult, sleRIPStaticRouteControlIPAddr, 
				sleRIPStaticRouteControlIPMask, sleRIPAdminDistanceValue, sleRIPAdminDistanceAddr, sleRIPAdminDistanceMask, sleRIPAdminDistanceAccessName, 
				sleRIPAdminDistanceControlRequest, sleRIPAdminDistanceControlStatus, sleRIPAdminDistanceControlTimer, sleRIPAdminDistanceControlTimeStamp, sleRIPAdminDistanceControlReqResult, 
				sleRIPAdminDistanceControlValue, sleRIPAdminDistanceControlAddr, sleRIPAdminDistanceControlMask, sleRIPAdminDistanceControlAccessName, sleRIPDistributeIfName, 
				sleRIPDistributeInAccessName, sleRIPDistributeOutAccessName, sleRIPDistributeInPrefixName, sleRIPDistributeOutPrefixName, sleRIPDistributeControlRequest, 
				sleRIPDistributeControlStatus, sleRIPDistributeControlTimer, sleRIPDistributeControlTimeStamp, sleRIPDistributeControlReqResult, sleRIPDistributeControlIfName, 
				sleRIPDistributeControlInAccessName, sleRIPDistributeControlOutAccessName, sleRIPDistributeControlInPrefixName, sleRIPDistributeControlOutPrefixName, sleRIPOffsetListIfname, 
				sleRIPOffsetListInAccName, sleRIPOffsetListInMetric, sleRIPOffsetListOutAccName, sleRIPOffsetListOutMetric, sleRIPOffsetListControlRequest, 
				sleRIPOffsetListControlStatus, sleRIPOffsetListControlTimer, sleRIPOffsetListControlTimeStamp, sleRIPOffsetListControlReqResult, sleRIPOffsetListControlIfname, 
				sleRIPOffsetListControlInAccName, sleRIPOffsetListControlInMetric, sleRIPOffsetListControlOutAccName, sleRIPOffsetListControlOutMetric, sleRIPRedistType, 
				sleRIPRedistMetric, sleRIPRedistRouteMapName, sleRIPRedistControlRequest, sleRIPRedistControlStatus, sleRIPRedistControlTimer, 
				sleRIPRedistControlTimeStamp, sleRIPRedistControlReqResult, sleRIPRedistControlType, sleRIPRedistControlMetric, sleRIPRedistControlRouteMapName, 
				sleRIPPassInterfaceName, sleRIPPassInterfaceControlRequest, sleRIPPassInterfaceControlStatus, sleRIPPassInterfaceControlTimer, sleRIPPassInterfaceControlTimeStamp, 
				sleRIPPassInterfaceControlReqResult, sleRIPPassInterfaceControlName, sleRIPInterfaceIndex, sleRIPInterfaceRecvVer, sleRIPInterfaceRecvPacket, 
				sleRIPInterfaceSendVer, sleRIPInterfaceSendPacket, sleRIPInterfaceSplitHorizonMode, sleRIPInterfaceAuthMode, sleRIPInterfaceAuthString, 
				sleRIPInterfaceAuthKeyChain, sleRIPInterfaceControlRequest, sleRIPInterfaceControlStatus, sleRIPInterfaceControlTimer, sleRIPInterfaceControlTimeStamp, 
				sleRIPInterfaceControlReqResult, sleRIPInterfaceControlIndex, sleRIPInterfaceControlRecvVer, sleRIPInterfaceControlRecvPacket, sleRIPInterfaceControlSendVer, 
				sleRIPInterfaceControlSendPacket, sleRIPInterfaceControlSplitHorizonMode, sleRIPInterfaceControlAuthMode, sleRIPInterfaceControlAuthString, sleRIPInterfaceControlAuthKeyChain, 
				sleRIPRoutesType, sleRIPRoutesPrefix, sleRIPRoutesMask, sleRIPRoutesNextHop, sleRIPRoutesMetric, 
				sleRIPRoutesNetworkFrom, sleRIPRoutesIfName, sleRIPRoutesUpTime }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIP 12 }

		
		-- *******.4.1.6296.101.54.13
		sleRIPNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleRIPModeCreated, sleRIPModeDeleted, sleRIPVersionChanged, sleRIPDefaultMetricChanged, sleRIPDefaultInformationOrgChanged, 
				sleRIPDefaultDistanceChanged, sleRIPRecvBufferSizeChanged, sleRIPMaximumPathsChanged, sleRIPMaximumPrefixProfileChanged, sleRIPMetricSumApplyChanged, 
				sleRIPTimersChanged, sleRIPRestartPeriodChanged, sleRIPRestartRemoved, sleRIPAllCleared, sleRIPRouteCleared, 
				sleRIPProtoTypeCleared, sleRIPNetworkIPCreated, sleRIPNetworkIPDeleted, sleRIPNetworkInterfaceCreated, sleRIPNetworkInterfaceDeleted, 
				sleRIPNeighborCreated, sleRIPNeighborDeleted, sleRIPStaticRouteCreated, sleRIPStaticRouteDeleted, sleRIPAdminDistanceCreated, 
				sleRIPAdminDistanceDeleted, sleRIPDistributeInAccessCreated, sleRIPDistributeInAccessDeleted, sleRIPDistributeOutAccessCreated, sleRIPDistributeOutAccessDeleted, 
				sleRIPDistributeInPrefixCreated, sleRIPDistributeInPrefixDeleted, sleRIPDistributeOutPrefixCreated, sleRIPDistributeOutPrefixDeleted, sleRIPOffsetListCreatedIn, 
				sleRIPOffsetListDeletedIn, sleRIPOffsetListCreatedOut, sleRIPOffsetListDeletedOut, sleRIPRedistributeCreated, sleRIPRedistributeDeleted, 
				sleRIPRedistributeChanged, sleRIPPassInterfaceCreated, sleRIPPassInterfaceDeleted, sleRIPInterfaceRecvVerChanged, sleRIPInterfaceRecvPacketChanged, 
				sleRIPInterfaceSendVerChanged, sleRIPInterfaceSendPacketChanged, sleRIPInterfaceSplitHorizonModeChanged, sleRIPInterfaceAuthModeChanged, sleRIPInterfaceAuthStringChanged, 
				sleRIPInterfaceAuthKeyChainChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRIP 13 }

		
	
	END

--
-- sle-rip-mib.mib
--
