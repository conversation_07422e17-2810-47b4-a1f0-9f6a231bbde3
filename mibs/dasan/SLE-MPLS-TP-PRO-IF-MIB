--
-- sle-mpls-tp-pro-if-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, January 12, 2016 at 14:08:09
--

	SLE-MPLS-TP-PRO-IF-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			mplsTunnelIndex, mplsTunnelInstance, mplsTunnelIngressLSRId, mplsTunnelEgressLSRId			
				FROM MPLS-TE-STD-MIB			
			Sle<PERSON>ontrolStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			zeroDotZero, TimeTicks, Ip<PERSON>ddress, Unsigned32, G<PERSON><PERSON>32, 
			OBJECT-TYPE, MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpNode MODULE-IDENTITY 
			LAST-UPDATED "201510070000Z"		-- October 07, 2015 at 00:00 GMT
			ORGANIZATION 
				"Multiprotocol Label Switching (MPLS) Working Group"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				DongChel Shin (Chris)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"Copyright (c) 2012 IETF Trust and the persons identified
				as the document authors.  All rights reserved.
				
				This MIB module contains generic object definitions for
				MPLS Traffic Engineering in transport networks."
			REVISION "201207150000Z"		-- July 15, 2012 at 00:00 GMT
			DESCRIPTION 
				"MPLS_TP node configuration table"
			::= { sleMpls 13 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MPLS."
			::= { sleMgmt 16 }

		
		sleMplsTpProIf OBJECT IDENTIFIER ::= { sleMplsTpNode 2 }

		
		sleMplsTpProIfInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpProIfInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION			" "
			::= { sleMplsTpProIf 1 }

		
		sleMplsTpProIfInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpProIfInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION			" "
			INDEX { sleMplsTPProIfInfoIfIndex }
			::= { sleMplsTpProIfInfoTable 1 }

		
		SleMplsTpProIfInfoEntry ::=
			SEQUENCE { 
				sleMplsTPProIfInfoIfIndex
					Unsigned32,
				sleMplsTPProIfInfoIpAddr
					IpAddress
			 }

		sleMplsTPProIfInfoIfIndex OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object is used in accommodating the bigger
				size Global_Node_ID and/or ICC with lower size LSR
				identifier in order to index the mplsTunnelTable.
				
				The Local Identifier is configured between 1 and 16777215,
				as valid IP address range starts from 16777216(***********).
				This range is chosen to identify the mplsTunnelTable's
				Ingress/Egress LSR-id is IP address or Local identifier,
				if the configured range is not IP address, administrator is
				expected to retrieve the complete information 
				(Global_Node_ID or ICC) from mplsTunnelExtNodeConfigTable. 
				This way, existing mplsTunnelTable is reused for 
				bidirectional tunnel extensions for MPLS based transport networks.
				
				This Local Identifier allows the administrator to assign
				a unique identifier to map Global_Node_ID and/or ICC."
			::= { sleMplsTpProIfInfoEntry 1 }

		
		sleMplsTPProIfInfoIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION			" "
			::= { sleMplsTpProIfInfoEntry 2 }

		
		sleMplsTpProIfControl OBJECT IDENTIFIER ::= { sleMplsTpProIf 2 }

		
		sleMplsTpProIfControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setMplsTpProIfIpAddr(1),
				unSetMplsTpProIfIpAddr(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that
				can be modified in the TunnelExtNodeConfigTable table. 
				For each read-write column of TunnelExtNodeConfigTable table, 
				a Set Operation control value is added in this object."
			::= { sleMplsTpProIfControl 1 }

		
		sleMplsTpProIfControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpProIfControl 2 }

		
		sleMplsTpProIfControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpProIfControl 3 }

		
		sleMplsTpProIfControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpProIfControl 4 }

		
		sleMplsTpProIfControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpProIfControl 5 }

		
		sleMplsTpProIfControlIfIndex OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION			" "
			::= { sleMplsTpProIfControl 6 }

		
		sleMplsTpProIfControlIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object indicates the Global Operator Identifier."
			REFERENCE
				"MPLS-TP Identifiers [RFC6370]."
			::= { sleMplsTpProIfControl 7 }

		
	
	END

--
-- sle-mpls-tp-pro-if-mib.mib
--
