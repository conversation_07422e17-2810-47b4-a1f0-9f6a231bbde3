--
-- SLE-RED-MIB.my
-- MIB generated by MG-<PERSON>O<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Monday, December 21, 2009 at 16:16:11
--

	SLE-RED-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
--  October 19, 2007 at 22:00 GMT
		-- *******.4.1.6296.101.22
		sleRed MODULE-IDENTITY 
			LAST-UPDATED "200710192200Z"		-- October 19, 2007 at 22:00 GMT
			ORGANIZATION 
				"DASAN Networks."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"This MIB contains information to support
				active/standby control redundancy."
			::= { sleMgmt 22 }

		
	
--
-- Type definitions
--
	
		SleRedBoardIdType ::= INTEGER
			{
			sfuA(1),
			sfuB(2)
			}

		SleRedModeType ::= INTEGER
			{
			redundant(1),
			standalone(2)
			}

		SleRedFaultActionType ::= INTEGER
			{
			switchover(1),
			log(2),
			disable(3)
			}

		SleRedReloadOSType ::= INTEGER
			{
			os1(1),
			os2(2),
			default(3)
			}

	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.22.1
		sleRedBase OBJECT IDENTIFIER ::= { sleRed 1 }

		
		-- *******.4.1.6296.**********
		sleRedInfo OBJECT IDENTIFIER ::= { sleRedBase 1 }

		
		-- *******.4.1.6296.**********.1
		sleRedActiveBoard OBJECT-TYPE
			SYNTAX SleRedBoardIdType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indication of the actual service board identifier. 
				Regardless of redundancy mode, it has actual service board id.
				"
			::= { sleRedInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleRedMode OBJECT-TYPE
			SYNTAX SleRedModeType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If two SFUs are installed in the system,
				it works as redundant mode. Otherwise, it works as standalone mode.
				"
			::= { sleRedInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleRedFaultCrashAction OBJECT-TYPE
			SYNTAX SleRedFaultActionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If any software module is crashed, the given action is taken.
				For 'switchover' action, if the board is active and a standby board is available,
				'switchover' is carried out. 
				
				If a standby board is not available, just warning message is left.
				If it is a standby board, 'reset' is carried out.
				"
			::= { sleRedInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleRedFaultTimeoutAction OBJECT-TYPE
			SYNTAX SleRedFaultActionType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If any software module doesn't respond for healthcheck messages for the certain time,
				the given action is taken.
				For 'switchover' action, if the board is active and a standby board is available,
				'switchover' is carried out.
				
				If a standby board is not available, just warning message is left.
				If it is a standby board, 'reset' is carried out.
				"
			::= { sleRedInfo 4 }

		
		-- *******.4.1.6296.**********.5
		sleRedFaultTimeout OBJECT-TYPE
			SYNTAX INTEGER (5..720000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Healthcheck timeout. If a software module doesn't respond for the given time,
				it is regarded as a timed-out module.
				
				(Unit: second, default is '5')
				"
			::= { sleRedInfo 5 }

		
		-- *******.4.1.6296.**********.6
		sleRedActivePrevState OBJECT-TYPE
			SYNTAX INTEGER
				{
				activeInit(0),
				singleActiveReady(1),
				versionReport(2),
				softwareXfer(3),
				softwareXferDone(4),
				configXfer(5),
				configXferDone(6),
				stateXfer(7),
				activeReady(8),
				disconnectStandby(9),
				standbyWait(10),
				versionCheck(11),
				updateMac(12),
				softwareSync(13),
				softwareSyncDone(14),
				configSync(15),
				configSyncDone(16),
				startupSync(17),
				standbyReady(18),
				standbyReset(19)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Previous state of active board."
			::= { sleRedInfo 6 }

		
		-- *******.4.1.6296.**********.7
		sleRedActiveCurrState OBJECT-TYPE
			SYNTAX INTEGER
				{
				activeInit(0),
				singleActiveReady(1),
				versionReport(2),
				softwareXfer(3),
				softwareXferDone(4),
				configXfer(5),
				configXferDone(6),
				stateXfer(7),
				activeReady(8),
				disconnectStandby(9),
				standbyWait(10),
				versionCheck(11),
				updateMac(12),
				softwareSync(13),
				softwareSyncDone(14),
				configSync(15),
				configSyncDone(16),
				startupSync(17),
				standbyReady(18),
				standbyReset(19)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current state of active board."
			::= { sleRedInfo 7 }

		
		-- *******.4.1.6296.**********
		sleRedControl OBJECT IDENTIFIER ::= { sleRedBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleRedControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				reloadStandby(1),
				switchover(2),
				setFaultMonitor(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"System commands related to control redundancy function.
				"
			::= { sleRedControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleRedControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleRedControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleRedControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleRedControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleRedControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleRedControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleRedControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleRedControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleRedControlReloadOS OBJECT-TYPE
			SYNTAX SleRedReloadOSType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A mate board is reloaded with the given os.
				If 'default' is given, the board is reloaded with 'default' OS.
				"
			::= { sleRedControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleRedControlFaultCrashAction OBJECT-TYPE
			SYNTAX SleRedFaultActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If any software module is crashed, the given action is taken.
				For 'switchover' action, if the board is active and a standby board is available,
				'switchover' is carried out. 
				
				If a standby board is not available, just warning message is left.
				If it is a standby board, 'reset' is carried out.
				"
			::= { sleRedControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleRedControlFaultTimeoutAction OBJECT-TYPE
			SYNTAX SleRedFaultActionType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If any software module doesn't respond for healthcheck messages for the certain time,
				the given action is taken.
				For 'switchover' action, if the board is active and a standby board is available,
				'switchover' is carried out.
				
				If a standby board is not available, just warning message is left.
				If it is a standby board, 'reset' is carried out.
				"
			::= { sleRedControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleRedControlFaultTimeout OBJECT-TYPE
			SYNTAX INTEGER (5..720000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Healthcheck timeout. If a software module doesn't respond for the given time,
				it is regarded as a timed-out module.
				
				(Unit: second, default is '5')
				"
			::= { sleRedControl 9 }

		
		-- *******.4.1.6296.**********
		sleRedNotification OBJECT IDENTIFIER ::= { sleRedBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleRedMateReloadRequested NOTIFICATION-TYPE
			OBJECTS { sleRedControlRequest, sleRedControlTimeStamp, sleRedControlReqResult, sleRedActiveBoard }
			STATUS current
			DESCRIPTION 
				"A new active SFU indication"
			::= { sleRedNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleRedSwitchoverRequested NOTIFICATION-TYPE
			OBJECTS { sleRedControlRequest, sleRedControlTimeStamp, sleRedControlReqResult }
			STATUS current
			DESCRIPTION 
				"The system redundancy mode has been changed. A standby board is pluged out or in."
			::= { sleRedNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleRedFaultMonitorChanged NOTIFICATION-TYPE
			OBJECTS { sleRedControlRequest, sleRedControlTimeStamp, sleRedControlReqResult, sleRedControlFaultCrashAction, sleRedControlFaultTimeoutAction, 
				sleRedControlFaultTimeout }
			STATUS current
			DESCRIPTION 
				"Fault monitor configuration has been changed."
			::= { sleRedNotification 3 }

		
		-- *******.4.1.6296.101.22.2
		sleRedGroup OBJECT-GROUP
			OBJECTS { sleRedActiveBoard, sleRedMode, sleRedFaultCrashAction, sleRedFaultTimeoutAction, sleRedFaultTimeout, 
				sleRedControlStatus, sleRedControlTimer, sleRedControlTimeStamp, sleRedControlReqResult, sleRedControlReloadOS, 
				sleRedControlFaultCrashAction, sleRedControlFaultTimeoutAction, sleRedActivePrevState, sleRedActiveCurrState, sleRedControlFaultTimeout, 
				sleRedControlRequest }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRed 2 }

		
		-- *******.4.1.6296.101.22.3
		sleRedNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleRedMateReloadRequested, sleRedSwitchoverRequested, sleRedFaultMonitorChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRed 3 }

		
	
	END

--
-- SLE-RED-MIB.my
--
