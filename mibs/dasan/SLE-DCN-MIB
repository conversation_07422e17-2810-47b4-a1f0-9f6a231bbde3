--
-- sle-dcn-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, December 17, 2015 at 17:46:36
--

	SLE-DCN-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OBJECT-TYPE, 
			MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.91
		sleDCN MODULE-IDENTITY 
			LAST-UPDATED "201508271441Z"		-- August 27, 2015 at 14:41 GMT
			ORGANIZATION 
				"dasan networks"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains information about DCN."
			::= { sleMgmt 91 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.91.1
		sleDcnNodeInfo OBJECT IDENTIFIER ::= { sleDCN 1 }

		
		-- *******.4.1.6296.**********
		sleDcnNodeBaseInfo OBJECT IDENTIFIER ::= { sleDcnNodeInfo 1 }

		
		-- *******.4.1.6296.**********.1
		sleDcnNodeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ne(0),
				gne(1),
				l2ne(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleDcnMacAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleDcnLoIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleDcnLoIpRegisterType OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(0),
				manual(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 4 }

		
		-- *******.4.1.6296.**********.5
		sleDcnIfNetwork OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 5 }

		
		-- *******.4.1.6296.**********.6
		sleDcnIfNetworkRegisterType OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(0),
				manual(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 6 }

		
		-- *******.4.1.6296.**********.7
		sleNeDcnOspfArea OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 7 }

		
		-- *******.4.1.6296.**********.8
		sleDcnGneState OBJECT-TYPE
			SYNTAX INTEGER
				{
				noneGne(0),
				noCandidate(1),
				waiting(2),
				slave(3),
				master(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 8 }

		
		-- *******.4.1.6296.**********.9
		sleDcnMgneCandidate OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 9 }

		
		-- *******.4.1.6296.**********.10
		sleDcnMgnePriority OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 10 }

		
		-- *******.4.1.6296.**********.11
		sleDcnLoIpBase OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 11 }

		
		-- *******.4.1.6296.**********.12
		sleDcnLoIpBasePrefixlen OBJECT-TYPE
			SYNTAX Integer32 (0..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 12 }

		
		-- *******.4.1.6296.**********.13
		sleDcnIfIpBase OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 13 }

		
		-- *******.4.1.6296.**********.14
		sleDcnIfIpBasePrefixlen OBJECT-TYPE
			SYNTAX Integer32 (0..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 14 }

		
		-- *******.4.1.6296.**********.15
		sleDcnL2gwState OBJECT-TYPE
			SYNTAX INTEGER
				{
				noneL2gw(0),
				noCandidate(1),
				waiting(2),
				slave(3),
				master(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 15 }

		
		-- *******.4.1.6296.**********.16
		sleDcnMl2gwCandidate OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 16 }

		
		-- *******.4.1.6296.**********.17
		sleDcnMl2gwPriority OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 17 }

		
		-- *******.4.1.6296.**********.18
		sleDcnVlanIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 18 }

		
		-- *******.4.1.6296.**********.19
		sleDcnVlanIpPrefixlen OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 19 }

		
		-- *******.4.1.6296.**********.20
		sleDcnVlanIpRegisterType OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(0),
				manual(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 20 }

		
		-- *******.4.1.6296.**********.21
		sleDcnL2GatewayIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 21 }

		
		-- *******.4.1.6296.**********.22
		sleDcnMgmtIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 22 }

		
		-- *******.4.1.6296.**********.23
		sleDcnMgmtIpPrefixlen OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseInfo 23 }

		
		-- *******.4.1.6296.**********
		sleDcnNodeBaseControl OBJECT IDENTIFIER ::= { sleDcnNodeInfo 2 }

		
		-- *******.4.1.6296.**********.1
		sleDcnControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setNodeType(1),
				dcnLoip(2),
				dcnLoIpBase(3),
				dcnIfNetwork(4),
				dcnIfIpBase(5),
				mgneSwitchOver(6),
				mgneCandidate(7),
				mgnePriority(8),
				l2GatewayEnable(9),
				dcnVlanIp(10),
				ml2gwSwitchOver(11),
				ml2gwCandidate(12),
				ml2gwPriority(13),
				remoteLoip(14),
				remoteIfNetwork(15),
				mgmtIp(16)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleDcnControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleDcnControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleDcnControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleDcnControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleDcnControlNodeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ne(0),
				gne(1),
				l2ne(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleDcnControlDcnLoIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleDcnControlDcnLoIpBase OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleDcnControlDcnLoIpBasePrefixlen OBJECT-TYPE
			SYNTAX Integer32 (4..24)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleDcnControlDcnIfIpBase OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleDcnControlDcnIfIpBasePrefixlen OBJECT-TYPE
			SYNTAX Integer32 (4..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleDcnControlDcnMgneCandidate OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleDcnControlDcnMgnePriority OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleDcnControlL2GatewayEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 14 }

		
		-- *******.4.1.6296.**********.15
		sleDcnControlDcnVlanIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 15 }

		
		-- *******.4.1.6296.**********.16
		sleDcnControlDcnVlanIpPrefixlen OBJECT-TYPE
			SYNTAX Integer32 (4..24)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 16 }

		
		-- *******.4.1.6296.**********.17
		sleDcnControlDcnMl2gwCandidate OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 17 }

		
		-- *******.4.1.6296.**********.18
		sleDcnControlDcnMl2gwPriority OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 18 }

		
		-- *******.4.1.6296.**********.19
		sleDcnControlDcnMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 19 }

		
		-- *******.4.1.6296.**********.20
		sleDcnControlDcnIfNetwork OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 20 }

		
		-- *******.4.1.6296.**********.21
		sleDcnControlDcnMgmtIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 21 }

		
		-- *******.4.1.6296.**********.22
		sleDcnControlDcnMgmtIpPrefixlen OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNodeBaseControl 22 }

		
		-- *******.4.1.6296.**********
		sleDcnNodeBaseNotification OBJECT IDENTIFIER ::= { sleDcnNodeInfo 3 }

		
		-- *******.4.1.6296.**********.1
		sleDcnNodeTypeChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlNodeType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleDcnLoIpChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnLoIp }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleDcnLoIpBaseChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnLoIpBase, sleDcnControlDcnLoIpBasePrefixlen
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleDcnIfNetworkChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnIfNetwork }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleDcnIfIpBaseChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnIfIpBase, sleDcnControlDcnIfIpBasePrefixlen
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleDcnMgneSwitchOverChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 6 }

		
		-- *******.4.1.6296.**********.7
		sleDcnMgneCandidateChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnMgneCandidate }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 7 }

		
		-- *******.4.1.6296.**********.8
		sleDcnMgnePriorityChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnMgnePriority }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 8 }

		
		-- *******.4.1.6296.**********.9
		sleDcnL2GatewayEnableChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlL2GatewayEnable }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 9 }

		
		-- *******.4.1.6296.**********.10
		sleDcnVlanIpChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnVlanIp, sleDcnControlDcnVlanIpPrefixlen
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 10 }

		
		-- *******.4.1.6296.**********.11
		sleDcnMl2gwSwitchOverChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 11 }

		
		-- *******.4.1.6296.**********.12
		sleDcnMl2gwCandidateChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnMl2gwCandidate }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 12 }

		
		-- *******.4.1.6296.**********.13
		sleDcnMl2gwPriorityChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnMl2gwPriority }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 13 }

		
		-- *******.4.1.6296.**********.14
		sleDcnRemoteLoIpChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnMacAddr, sleDcnControlDcnLoIp
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 14 }

		
		-- *******.4.1.6296.**********.15
		sleDcnRemoteIfNetworkChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnMacAddr, sleDcnControlDcnIfNetwork
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 15 }

		
		-- *******.4.1.6296.**********.16
		sleDcnMgmtIpChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnControlRequest, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlDcnMgmtIp, sleDcnControlDcnMgmtIpPrefixlen
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnNodeBaseNotification 16 }

		
		-- *******.4.1.6296.101.91.2
		sleDcnGneInfo OBJECT IDENTIFIER ::= { sleDCN 2 }

		
		-- *******.4.1.6296.**********
		sleDcnNe OBJECT IDENTIFIER ::= { sleDcnGneInfo 1 }

		
		-- *******.4.1.6296.**********.1
		sleDcnNeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDcnNeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNe 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDcnNeEntry OBJECT-TYPE
			SYNTAX SleDcnNeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDcnNeMacAddress }
			::= { sleDcnNeTable 1 }

		
		SleDcnNeEntry ::=
			SEQUENCE { 
				sleDcnNeMacAddress
					OCTET STRING,
				sleDcnNeLoIpAddr
					IpAddress,
				sleDcnNeLoipRegsterType
					INTEGER,
				sleDcnNeType
					INTEGER,
				sleDcnNeNodeRegisterType
					INTEGER,
				sleDcnNodeFail
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDcnNeMacAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDcnNeLoIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDcnNeLoipRegsterType OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(0),
				manual(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDcnNeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ne(0),
				gne(1),
				l2ne(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDcnNeNodeRegisterType OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(0),
				manual(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDcnNodeFail OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				fail(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeEntry 6 }

		
		-- *******.4.1.6296.**********.2
		sleDcnNeSetControl OBJECT IDENTIFIER ::= { sleDcnNe 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDcnNeControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				register(1),
				unregister(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeSetControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDcnNeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeSetControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDcnNeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeSetControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDcnNeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeSetControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDcnNeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeSetControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDcnNeControlNeMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnNeSetControl 6 }

		
		-- *******.4.1.6296.**********.3
		sleDcnProxySetNotification OBJECT IDENTIFIER ::= { sleDcnNe 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDcnNeRegisterChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnNeControlRequest, sleDcnNeControlTimeStamp, sleDcnNeControlReqResult, sleDcnNeControlNeMacAddr }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnProxySetNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDcnNeUnegisterChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnNeControlRequest, sleDcnNeControlTimeStamp, sleDcnNeControlReqResult, sleDcnNeControlNeMacAddr }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnProxySetNotification 2 }

		
		-- *******.4.1.6296.101.91.3
		sleDcnNeInfo OBJECT IDENTIFIER ::= { sleDCN 3 }

		
		-- *******.4.1.6296.**********
		sleDcnGNE OBJECT IDENTIFIER ::= { sleDcnNeInfo 1 }

		
		-- *******.4.1.6296.**********.1
		sleDcnGneTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDcnGneEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnGNE 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDcnGneEntry OBJECT-TYPE
			SYNTAX SleDcnGneEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDcnGneMasterType, sleDcnGneMacAddr }
			::= { sleDcnGneTable 1 }

		
		SleDcnGneEntry ::=
			SEQUENCE { 
				sleDcnGneMasterType
					INTEGER,
				sleDcnGneMacAddr
					OCTET STRING,
				sleDcnGneLoIpAddr
					IpAddress,
				sleDcnGneNodeType
					INTEGER,
				sleDcnGNeGatewayState
					INTEGER,
				sleDcnGneNodeFail
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDcnGneMasterType OBJECT-TYPE
			SYNTAX INTEGER
				{
				gne(1),
				l2gw(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnGneEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDcnGneMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnGneEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDcnGneLoIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnGneEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDcnGneNodeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ne(0),
				gne(1),
				l2ne(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnGneEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDcnGNeGatewayState OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				noCandidate(1),
				waiting(2),
				slave(3),
				master(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnGneEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDcnGneNodeFail OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				fail(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnGneEntry 6 }

		
		-- *******.4.1.6296.101.91.4
		sleDcnInterface OBJECT IDENTIFIER ::= { sleDCN 4 }

		
		-- *******.4.1.6296.**********
		sleDcnIf OBJECT IDENTIFIER ::= { sleDcnInterface 1 }

		
		-- *******.4.1.6296.**********.1
		sleDcnIfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDcnIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIf 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDcnIfEntry OBJECT-TYPE
			SYNTAX SleDcnIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDcnIfIndex }
			::= { sleDcnIfTable 1 }

		
		SleDcnIfEntry ::=
			SEQUENCE { 
				sleDcnIfIndex
					Integer32,
				sleDcnIfName
					OCTET STRING,
				sleDcnIfDcnMode
					INTEGER,
				sleDcnIfAllocIpAddress
					IpAddress,
				sleDcnIfCurrentIpAddress
					IpAddress,
				sleDcnIfGneOspfArea
					IpAddress,
				sleDcnNearMacAddress
					OCTET STRING,
				sleDcnNearNodeLoIpAddr
					IpAddress,
				sleDcnNearNodeIfName
					OCTET STRING,
				sleDcnNearIfFail
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDcnIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDcnIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDcnIfDcnMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				noDcnIf(0),
				auto(1),
				passive(2),
				l2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDcnIfAllocIpAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDcnIfCurrentIpAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDcnIfGneOspfArea OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleDcnNearMacAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleDcnNearNodeLoIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleDcnNearNodeIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 9 }

		
		-- *******.4.1.6296.**********.1.1.10
		sleDcnNearIfFail OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				fail(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfEntry 10 }

		
		-- *******.4.1.6296.**********.2
		sleDcnIfControl OBJECT IDENTIFIER ::= { sleDcnIf 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDcnIfControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				dcnifDcnMode(1),
				gneIfOspfArea(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDcnIfControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDcnIfControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDcnIfControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDcnIfControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDcnIfControlIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDcnIfControlDcnMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				noDcnIf(0),
				auto(1),
				passive(2),
				l2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDcnIfControlGneOspfArea OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDcnIfControl 8 }

		
		-- *******.4.1.6296.**********.3
		sleDcnIfNotification OBJECT IDENTIFIER ::= { sleDcnIf 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDcnIfModeChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnIfControlRequest, sleDcnIfControlTimeStamp, sleDcnIfControlReqResult, sleDcnIfControlIfIndex, sleDcnIfControlDcnMode
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnIfNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDcnIfGneOspfAreaChanged NOTIFICATION-TYPE
			OBJECTS { sleDcnIfControlRequest, sleDcnIfControlTimeStamp, sleDcnIfControlReqResult, sleDcnIfControlIfIndex, sleDcnIfControlGneOspfArea
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDcnIfNotification 2 }

		
		-- *******.4.1.6296.101.91.13
		sleDcnObjectGroup OBJECT-GROUP
			OBJECTS { sleDcnNodeType, sleDcnMacAddress, sleDcnMgneCandidate, sleDcnMgnePriority, sleDcnIfIpBase, 
				sleDcnIfIpBasePrefixlen, sleDcnL2gwState, sleDcnMl2gwCandidate, sleDcnMl2gwPriority, sleDcnControlDcnIfIpBase, 
				sleDcnControlDcnIfIpBasePrefixlen, sleDcnControlDcnMgneCandidate, sleDcnControlDcnMgnePriority, sleDcnControlDcnMl2gwCandidate, sleDcnControlDcnMl2gwPriority, 
				sleDcnNeMacAddress, sleDcnNeLoIpAddr, sleDcnNeLoipRegsterType, sleDcnNeNodeRegisterType, sleDcnNeControlRequest, 
				sleDcnNeControlStatus, sleDcnNeControlTimer, sleDcnNeControlTimeStamp, sleDcnNeControlReqResult, sleDcnNeControlNeMacAddr, 
				sleDcnGneMacAddr, sleDcnGneNodeType, sleDcnGNeGatewayState, sleDcnIfName, sleDcnIfDcnMode, 
				sleDcnIfAllocIpAddress, sleDcnIfCurrentIpAddress, sleDcnIfControlDcnMode, sleDcnControlRequest, sleDcnControlStatus, 
				sleDcnControlTimer, sleDcnControlTimeStamp, sleDcnControlReqResult, sleDcnControlNodeType, sleDcnNodeFail, 
				sleDcnGneLoIpAddr, sleDcnGneNodeFail, sleDcnIfIndex, sleDcnNearNodeLoIpAddr, sleDcnNearNodeIfName, 
				sleDcnIfControlRequest, sleDcnIfControlStatus, sleDcnIfControlTimer, sleDcnIfControlTimeStamp, sleDcnIfControlReqResult, 
				sleDcnIfControlIfIndex, sleDcnControlDcnMgmtIp, sleDcnMgmtIp, sleDcnControlDcnMgmtIpPrefixlen, sleDcnMgmtIpPrefixlen, 
				sleDcnControlDcnVlanIpPrefixlen, sleDcnVlanIpPrefixlen, sleDcnNearIfFail, sleDcnGneState, sleDcnNeType, 
				sleDcnLoIpBase, sleDcnLoIpBasePrefixlen, sleDcnLoIp, sleDcnVlanIp, sleNeDcnOspfArea, 
				sleDcnControlL2GatewayEnable, sleDcnControlDcnLoIpBase, sleDcnControlDcnLoIpBasePrefixlen, sleDcnControlDcnLoIp, sleDcnControlDcnVlanIp, 
				sleDcnIfGneOspfArea, sleDcnL2GatewayIp, sleDcnIfControlGneOspfArea, sleDcnControlDcnMacAddr, sleDcnControlDcnIfNetwork, 
				sleDcnNearMacAddress, sleDcnGneMasterType, sleDcnLoIpRegisterType, sleDcnIfNetwork, sleDcnIfNetworkRegisterType, 
				sleDcnVlanIpRegisterType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDCN 13 }

		
		-- *******.4.1.6296.101.91.14
		sleDcnNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleDcnIfIpBaseChanged, sleDcnMgneSwitchOverChanged, sleDcnMgneCandidateChanged, sleDcnMgnePriorityChanged, sleDcnMl2gwSwitchOverChanged, 
				sleDcnMl2gwCandidateChanged, sleDcnMl2gwPriorityChanged, sleDcnNeRegisterChanged, sleDcnNeUnegisterChanged, sleDcnIfModeChanged, 
				sleDcnNodeTypeChanged, sleDcnMgmtIpChanged, sleDcnL2GatewayEnableChanged, sleDcnIfNetworkChanged, sleDcnRemoteLoIpChanged, 
				sleDcnRemoteIfNetworkChanged, sleDcnLoIpBaseChanged, sleDcnLoIpChanged, sleDcnVlanIpChanged, sleDcnIfGneOspfAreaChanged
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDCN 14 }

		
	
	END

--
-- sle-dcn-mib.mib
--
