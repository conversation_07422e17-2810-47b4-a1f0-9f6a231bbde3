--
-- DASAN-AUTORESET-MIB.my
-- MIB generated by MG-<PERSON>O<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Thursday, November 11, 2010 at 16:59:25
--

	DASAN-AUTORESET-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			dsSwitchSystem			
				FROM DASAN-SWITCH-MIB			
			InterfaceIndex			
				FROM IF-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			TimeTicks, <PERSON>p<PERSON><PERSON><PERSON>, Gauge32, OBJECT-TYPE, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TruthValue, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
--
-- Textual conventions
--
	
--  Textual Conventions
		DsIgmpMode ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"A value that represents a working mode of IGMP protocol in a
				device.
				
				auto(1)    : The system decides the working mode by itself.
				
				igmpOnly(2): Working on IGMP mode only.
				
				igmpCgmp(3): Working on both IGMP and CGMP modes."
			SYNTAX INTEGER
				{
				auto(1),
				igmpOnly(2),
				igmpCgmp(3)
				}

		DsIgmpVersion ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"A value that represents the version of IGMP:
				
				version1(1)     : Version 1 of IGMP
				version2(2)     : Version 2 of IGMP
				version3(3)     : Version 3 of IGMP."
			SYNTAX INTEGER
				{
				version1(1),
				version2(2),
				version3(3)
				}

	
--
-- Node definitions
--
	
		-- *******.4.1.6296.*******.31
		dsAutoReset OBJECT IDENTIFIER ::= { dsSwitchSystem 31 }

		
		-- *******.4.1.6296.*******.31.1
		dsAutoResetBase OBJECT IDENTIFIER ::= { dsAutoReset 1 }

		
		-- *******.4.1.6296.*******.31.1.1
		dsAutoResetBaseInfo OBJECT IDENTIFIER ::= { dsAutoResetBase 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether AutoReset globally is enabled or not."
			::= { dsAutoResetBaseInfo 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetForceReboot OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time when force-reboot will occur, ex) '17:25 15 Mar 2010'"
			::= { dsAutoResetBaseInfo 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetResetCount OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Reboot count by AutoReset"
			::= { dsAutoResetBaseInfo 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetResetThreshold OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Reboot threshold for AutoReset"
			::= { dsAutoResetBaseInfo 4 }

		
		-- *******.4.1.6296.*******.31.1.2
		dsAutoResetBaseControl OBJECT IDENTIFIER ::= { dsAutoResetBase 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setAutoResetEnable(1),
				setAutoResetForceReboot(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetBaseControl 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetBaseControl 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetBaseControl 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetBaseControl 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetBaseControl 5 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether AutoReset globally is enabled or not."
			::= { dsAutoResetBaseControl 6 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetControlForceReboot OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time when force-reboot will occur, ex) '17:25 15 Mar 2010'"
			::= { dsAutoResetBaseControl 7 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetControlResetCount OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Reboot count by AutoReset"
			::= { dsAutoResetBaseControl 8 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetControlResetThreshold OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Reboot threshold for AutoReset"
			::= { dsAutoResetBaseControl 9 }

		
		-- *******.4.1.6296.*******.31.1.3
		dsAutoResetBaseNotification OBJECT IDENTIFIER ::= { dsAutoResetBase 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetBaseControlRequest, dsAutoResetBaseControlStatus, dsAutoResetBaseControlTimer, dsAutoResetBaseControlTimeStamp, dsAutoResetBaseControlReqResult, 
				dsAutoResetControlEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetEnable"
			::= { dsAutoResetBaseNotification 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetForceRebootChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetBaseControlRequest, dsAutoResetBaseControlStatus, dsAutoResetBaseControlTimer, dsAutoResetBaseControlTimeStamp, dsAutoResetBaseControlReqResult, 
				dsAutoResetControlForceReboot }
			STATUS current
			DESCRIPTION 
				"setAutoResetForceReboot"
			::= { dsAutoResetBaseNotification 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetResetThresholdChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetBaseControlRequest, dsAutoResetBaseControlStatus, dsAutoResetBaseControlTimer, dsAutoResetBaseControlTimeStamp, dsAutoResetBaseControlReqResult, 
				dsAutoResetControlResetThreshold }
			STATUS current
			DESCRIPTION 
				"setAutoResetResetThreshold"
			::= { dsAutoResetBaseNotification 3 }

		
		-- *******.4.1.6296.*******.31.2
		dsAutoResetCpu OBJECT IDENTIFIER ::= { dsAutoReset 2 }

		
		-- *******.4.1.6296.*******.31.2.1
		dsAutoResetCpuInfo OBJECT IDENTIFIER ::= { dsAutoResetCpu 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether CPU-based AutoReset is enabled or not."
			::= { dsAutoResetCpuInfo 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuThresholdHigh OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"High threshold for the CPU load."
			::= { dsAutoResetCpuInfo 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuThresholdLow OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Low threshold for the CPU load."
			::= { dsAutoResetCpuInfo 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuContinueTime OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time of duration.
				AutoReset condition is met if CPU load continuously exceeds the threshold during the given time."
			::= { dsAutoResetCpuInfo 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The reboot time due to CPU-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetCpuInfo 5 }

		
		-- *******.4.1.6296.*******.31.2.2
		dsAutoResetCpuControl OBJECT IDENTIFIER ::= { dsAutoResetCpu 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setAutoResetCpuEnable(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuControl 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuControl 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuControl 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuControl 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuControl 5 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether CPU-based AutoReset is enabled or not."
			::= { dsAutoResetCpuControl 6 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuControlThresholdHigh OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"High threshold for the CPU load."
			::= { dsAutoResetCpuControl 7 }

		
		-- *******.4.1.6296.*******.31.2.2.8
		dsAutoResetCpuControlThresholdLow OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Low threshold for the CPU load."
			::= { dsAutoResetCpuControl 8 }

		
		-- *******.4.1.6296.*******.31.2.2.9
		dsAutoResetCpuControlContinueTime OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time of duration.
				AutoReset condition is met if CPU load continuously exceeds the threshold during the given time."
			::= { dsAutoResetCpuControl 9 }

		
		-- *******.4.1.6296.*******.*********
		dsAutoResetCpuControlResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The reboot time due to CPU-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetCpuControl 10 }

		
		-- *******.4.1.6296.*******.31.2.3
		dsAutoResetCpuNotification OBJECT IDENTIFIER ::= { dsAutoResetCpu 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetCpuControlRequest, dsAutoResetCpuControlStatus, dsAutoResetCpuControlTimer, dsAutoResetCpuControlTimeStamp, dsAutoResetCpuControlReqResult, 
				dsAutoResetCpuControlEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetCpuEnable"
			::= { dsAutoResetCpuNotification 1 }

		
		-- *******.4.1.6296.*******.31.3
		dsAutoResetMemory OBJECT IDENTIFIER ::= { dsAutoReset 3 }

		
		-- *******.4.1.6296.*******.31.3.1
		dsAutoResetMemoryInfo OBJECT IDENTIFIER ::= { dsAutoResetMemory 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether Memory-based AutoReset is enabled or not."
			::= { dsAutoResetMemoryInfo 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryThresholdHigh OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"High threshold for the Memory usage."
			::= { dsAutoResetMemoryInfo 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryThresholdLow OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Low threshold for the Memory usage."
			::= { dsAutoResetMemoryInfo 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryContinueTime OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time of duration.
				AutoReset condition is met if Memory usage continuously exceeds the threshold during the given time."
			::= { dsAutoResetMemoryInfo 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The reboot time due to Memory-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetMemoryInfo 5 }

		
		-- *******.4.1.6296.*******.31.3.2
		dsAutoResetMemoryControl OBJECT IDENTIFIER ::= { dsAutoResetMemory 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setY1731Enable(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetMemoryControl 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetMemoryControl 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetMemoryControl 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetMemoryControl 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetMemoryControl 5 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether Memory-based AutoReset is enabled or not."
			::= { dsAutoResetMemoryControl 6 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlThresholdHigh OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"High threshold for the Memory usage."
			::= { dsAutoResetMemoryControl 7 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlThresholdLow OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Low threshold for the Memory usage."
			::= { dsAutoResetMemoryControl 8 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryControlContinueTime OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time of duration.
				AutoReset condition is met if Memory usage continuously exceeds the threshold during the given time."
			::= { dsAutoResetMemoryControl 9 }

		
		-- *******.4.1.6296.*******.*********
		dsAutoResetMemoryControlResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The reboot time due to Memory-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetMemoryControl 10 }

		
		-- *******.4.1.6296.*******.31.3.3
		dsAutoResetMemoryNotification OBJECT IDENTIFIER ::= { dsAutoResetMemory 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetMemoryEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetMemoryControlRequest, dsAutoResetMemoryControlStatus, dsAutoResetMemoryControlTimer, dsAutoResetMemoryControlTimeStamp, dsAutoResetMemoryControlReqResult, 
				dsAutoResetMemoryControlEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetMemoryEnable"
			::= { dsAutoResetMemoryNotification 1 }

		
		-- *******.4.1.6296.*******.31.4
		dsAutoResetNetwork OBJECT IDENTIFIER ::= { dsAutoReset 4 }

		
		-- *******.4.1.6296.*******.31.4.1
		dsAutoResetNetworkInfo OBJECT IDENTIFIER ::= { dsAutoResetNetwork 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether Network-based AutoReset is enabled or not."
			::= { dsAutoResetNetworkInfo 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkInterval OBJECT-TYPE
			SYNTAX INTEGER (1..60)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interval between ping request."
			::= { dsAutoResetNetworkInfo 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkCount OBJECT-TYPE
			SYNTAX INTEGER (1..60)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The count of ping request
				AutoReset condition is met if there was no ping reply for the count of continuous ping request."
			::= { dsAutoResetNetworkInfo 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkTargetIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ping destination IP address"
			::= { dsAutoResetNetworkInfo 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The reboot time due to Memory-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetNetworkInfo 5 }

		
		-- *******.4.1.6296.*******.31.4.2
		dsAutoResetNetworkControl OBJECT IDENTIFIER ::= { dsAutoResetNetwork 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setAutoResetNetworkEnable(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetNetworkControl 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetNetworkControl 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetNetworkControl 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetNetworkControl 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetNetworkControl 5 }

		
		-- *******.4.1.6296.*******.31.4.2.6
		dsAutoResetNetworkControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether Network-based AutoReset is enabled or not."
			::= { dsAutoResetNetworkControl 6 }

		
		-- *******.4.1.6296.*******.31.4.2.7
		dsAutoResetNetworkControlInterval OBJECT-TYPE
			SYNTAX INTEGER (1..60)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The interval between ping request."
			::= { dsAutoResetNetworkControl 7 }

		
		-- *******.4.1.6296.*******.31.4.2.8
		dsAutoResetNetworkControlCount OBJECT-TYPE
			SYNTAX INTEGER (1..60)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The count of ping request
				AutoReset condition is met if there was no ping reply for the count of continuous ping request."
			::= { dsAutoResetNetworkControl 8 }

		
		-- *******.4.1.6296.*******.31.4.2.9
		dsAutoResetNetworkControlTargetIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ping destination IP address"
			::= { dsAutoResetNetworkControl 9 }

		
		-- *******.4.1.6296.*******.********0
		dsAutoResetNetworkControlResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The reboot time due to Network-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetNetworkControl 10 }

		
		-- *******.4.1.6296.*******.31.4.3
		dsAutoResetNetworkNotification OBJECT IDENTIFIER ::= { dsAutoResetNetwork 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetNetworkEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetMemoryControlRequest, dsAutoResetMemoryControlStatus, dsAutoResetMemoryControlTimer, dsAutoResetMemoryControlTimeStamp, dsAutoResetMemoryControlReqResult, 
				dsAutoResetMemoryControlEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetNetworkEnable"
			::= { dsAutoResetNetworkNotification 1 }

		
		-- *******.4.1.6296.*******.31.5
		dsAutoResetProcess OBJECT IDENTIFIER ::= { dsAutoReset 5 }

		
		-- *******.4.1.6296.*******.31.5.1
		dsAutoResetProcessInfo OBJECT IDENTIFIER ::= { dsAutoResetProcess 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether Process-based AutoReset is enabled or not."
			::= { dsAutoResetProcessInfo 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The reboot time due to Process-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetProcessInfo 2 }

		
		-- *******.4.1.6296.*******.31.5.2
		dsAutoResetProcessControl OBJECT IDENTIFIER ::= { dsAutoResetProcess 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setAutoResetProcessEnable(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetProcessControl 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetProcessControl 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetProcessControl 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetProcessControl 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetProcessControl 5 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether Process-based AutoReset is enabled or not."
			::= { dsAutoResetProcessControl 6 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessControlResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enables/Disables Y.1731 OAM on a trunk port"
			::= { dsAutoResetProcessControl 7 }

		
		-- *******.4.1.6296.*******.31.5.3
		dsAutoResetProcessNotification OBJECT IDENTIFIER ::= { dsAutoResetProcess 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetProcessEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetProcessControlRequest, dsAutoResetProcessControlStatus, dsAutoResetProcessControlTimer, dsAutoResetProcessControlTimeStamp, dsAutoResetProcessControlReqResult, 
				dsAutoResetProcessControlEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetProcessEnable"
			::= { dsAutoResetProcessNotification 1 }

		
		-- *******.4.1.6296.*******.31.6
		dsAutoResetCpuNetwork OBJECT IDENTIFIER ::= { dsAutoReset 6 }

		
		-- *******.4.1.6296.*******.31.6.1
		dsAutoResetCpuNetworkInfo OBJECT IDENTIFIER ::= { dsAutoResetCpuNetwork 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether CPU&Network-based AutoReset is enabled or not."
			::= { dsAutoResetCpuNetworkInfo 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The reboot time due to CPU&Network-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetCpuNetworkInfo 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkCpuEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether CPU condition of the CPU&Network-based AutoReset is enabled or not."
			::= { dsAutoResetCpuNetworkInfo 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkCpuThresholdHigh OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"High threshold for the CPU load."
			::= { dsAutoResetCpuNetworkInfo 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkCpuThresholdLow OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Low threshold for the CPU load."
			::= { dsAutoResetCpuNetworkInfo 5 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkCpuContinueTime OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time of duration.
				AutoReset condition is met if CPU load continuously exceeds the threshold during the given time."
			::= { dsAutoResetCpuNetworkInfo 6 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkNetworkEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether Network condition of the CPU&Network-based AutoReset is enabled or not."
			::= { dsAutoResetCpuNetworkInfo 7 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkNetworkInterval OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interval between ping request."
			::= { dsAutoResetCpuNetworkInfo 8 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkNetworkCount OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The count of ping request
				AutoReset condition is met if there was no ping reply for the count of continuous ping request."
			::= { dsAutoResetCpuNetworkInfo 9 }

		
		-- *******.4.1.6296.*******.********0
		dsAutoResetCpuNetworkNetworkTargetIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ping destination IP address"
			::= { dsAutoResetCpuNetworkInfo 10 }

		
		-- *******.4.1.6296.*******.31.6.2
		dsAutoResetCpuNetworkControl OBJECT IDENTIFIER ::= { dsAutoResetCpuNetwork 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setAutoResetCpuNetworkEnable(1),
				setAutoResetCpuNetworkCpuEnable(2),
				setAutoResetCpuNetworkNetworkEnable(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuNetworkControl 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuNetworkControl 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuNetworkControl 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuNetworkControl 4 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { dsAutoResetCpuNetworkControl 5 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether CPU&Network-based AutoReset is enabled or not."
			::= { dsAutoResetCpuNetworkControl 6 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkControlResetTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The reboot time due to CPU&Network-based AutoReset.
				Format : hh:mm"
			::= { dsAutoResetCpuNetworkControl 7 }

		
		-- *******.4.1.6296.*******.31.6.2.8
		dsAutoResetCpuNetworkControlCpuEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether CPU condition of the CPU&Network-based AutoReset is enabled or not."
			::= { dsAutoResetCpuNetworkControl 8 }

		
		-- *******.4.1.6296.*******.31.6.2.9
		dsAutoResetCpuNetworkControlCpuThresholdHigh OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"High threshold for the CPU load."
			::= { dsAutoResetCpuNetworkControl 9 }

		
		-- *******.4.1.6296.*******.********0
		dsAutoResetCpuNetworkControlCpuThresholdLow OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Low threshold for the CPU load."
			::= { dsAutoResetCpuNetworkControl 10 }

		
		-- *******.4.1.6296.*******.********1
		dsAutoResetCpuNetworkControlCpuContinueTime OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time of duration.
				AutoReset condition is met if CPU load continuously exceeds the threshold during the given time."
			::= { dsAutoResetCpuNetworkControl 11 }

		
		-- *******.4.1.6296.*******.********2
		dsAutoResetCpuNetworkControlNetworkEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates whether Network condition of the CPU&Network-based AutoReset is enabled or not."
			::= { dsAutoResetCpuNetworkControl 12 }

		
		-- *******.4.1.6296.*******.*********
		dsAutoResetCpuNetworkControlNetworkInterval OBJECT-TYPE
			SYNTAX INTEGER (1..60)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The interval between ping request."
			::= { dsAutoResetCpuNetworkControl 13 }

		
		-- *******.4.1.6296.*******.*********
		dsAutoResetCpuNetworkControlNetworkCount OBJECT-TYPE
			SYNTAX INTEGER (1..60)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The count of ping request
				AutoReset condition is met if there was no ping reply for the count of continuous ping request."
			::= { dsAutoResetCpuNetworkControl 14 }

		
		-- *******.4.1.6296.*******.*********
		dsAutoResetCpuNetworkControlNetworkTargetIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ping destination IP address"
			::= { dsAutoResetCpuNetworkControl 15 }

		
		-- *******.4.1.6296.*******.31.6.3
		dsAutoResetCpuNetworkNotification OBJECT IDENTIFIER ::= { dsAutoResetCpuNetwork 3 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetCpuNetworkControlRequest, dsAutoResetCpuNetworkControlStatus, dsAutoResetCpuNetworkControlTimer, dsAutoResetCpuNetworkControlTimeStamp, dsAutoResetCpuNetworkControlReqResult, 
				dsAutoResetCpuNetworkControlEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetCpuNetworkEnable"
			::= { dsAutoResetCpuNetworkNotification 1 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkCpuEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetCpuNetworkControlRequest, dsAutoResetCpuNetworkControlStatus, dsAutoResetCpuNetworkControlTimer, dsAutoResetCpuNetworkControlTimeStamp, dsAutoResetCpuNetworkControlReqResult, 
				dsAutoResetCpuNetworkControlCpuEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetCpuNetworkCpuEnable"
			::= { dsAutoResetCpuNetworkNotification 2 }

		
		-- *******.4.1.6296.*******.********
		dsAutoResetCpuNetworkNetworkEnableChanged NOTIFICATION-TYPE
			OBJECTS { dsAutoResetCpuNetworkControlRequest, dsAutoResetCpuNetworkControlStatus, dsAutoResetCpuNetworkControlTimer, dsAutoResetCpuNetworkControlTimeStamp, dsAutoResetCpuNetworkControlReqResult, 
				dsAutoResetCpuNetworkControlNetworkEnable }
			STATUS current
			DESCRIPTION 
				"setAutoResetCpuNetworkNetworkEnable"
			::= { dsAutoResetCpuNetworkNotification 3 }

		
	
	END

--
-- DASAN-AUTORESET-MIB.my
--
