--
-- dasan-notification.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, December 09, 2010 at 18:13:13
--

--  dasan-notification.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, December 09, 2010 at 17:43:42
-- 
--  dasan-notification.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, December 09, 2010 at 17:25:10
-- 
--  *****************************************************************
-- DASAN-NOTIFICATION.mib:  Dasan Enterprise Notifications
-- 
-- Copyright (c) 2001 by Dasan Co., Ltd.
-- All rights reserved.
-- 
-- *****************************************************************
-- 

DASAN-NOTIFICATION DEFINITIONS ::= BEGIN

	IMPORTS
		dsSubnetConfName			
			FROM DASAN-DHCP-MIB			
		dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonPortIndex, 
		dsPonDirection, dsPonState, dsPonCount, dsPonQueueIndex			
			FROM DASAN-GEPON-MIB			
		dasanEvents			
			FROM DASAN-SMI			
		dsFreeMem, dsSystemUpgradePath, dsSystemUpgradeStorage, dsPortModuleIndex, dsPortPortIndex, 
		dsPortInstallStatus, dsTemperatureStatus, dsPowerIndex, dsPowerstatus, dsFanIndex, 
		dsFanstatus, dsUserMacAddress1, dsUserMacAddress2, dsUserMacAddress3, dsUserMacAddress4, 
		dsSwitchAtNetAddress, dsSwitchAtMacAddress, dsSwitchAtVID			
			FROM DASAN-SWITCH-MIB			
		dsUserLoginName, dsUserLoginIpAddress			
			FROM DASAN-USER-MANAGEMENT-MIB			
		sleY1731MegIndex, sleY1731MEgRaspWestPortStatus, sleY1731MEgRaspEastPortStatus
			FROM SLE-FAULTMGMT-MIB
		ifIndex			
			FROM IF-MIB			
		sysDescr, sysUpTime			
			FROM SNMPv2-MIB			
		NOTIFICATION-TYPE			
			FROM SNMPv2-SMI;



--
-- Node definitions
--

--  Node definitions
-- 
-- Node definitions
-- 
-- Dasan Enterprise-Specific TRAP define 
-- <NAME_EMAIL> 2003/03/07    
-- moved from DASAN-SWITCH-MIB.my  
-- <NAME_EMAIL>   2004/06/10
-- 
	-- *******.4.1.6296.0.1
	portInstalled NOTIFICATION-TYPE
		OBJECTS { ifIndex, dsPortInstallStatus }
		STATUS current
		DESCRIPTION 
			"portI0nstalled trap send when portInstallStatus is enabled."
		::= { dasanEvents 1 }

	
	-- *******.4.1.6296.0.2
	portRemoved NOTIFICATION-TYPE
		OBJECTS { ifIndex, dsPortInstallStatus }
		STATUS current
		DESCRIPTION 
			"portInstalled trap send when portInstallStatus is disabled."
		::= { dasanEvents 2 }

	
	-- *******.4.1.6296.0.3
	powerOk NOTIFICATION-TYPE
		OBJECTS { dsPowerIndex, dsPowerstatus }
		STATUS current
		DESCRIPTION 
			"Power status OK trap when powerStatus change from disable to enable."
		::= { dasanEvents 3 }

	
	-- *******.4.1.6296.0.4
	powerFail NOTIFICATION-TYPE
		OBJECTS { dsPowerIndex, dsPowerstatus }
		STATUS current
		DESCRIPTION 
			"Power status fail trap when powerStatus change from enable to disable."
		::= { dasanEvents 4 }

	
	-- *******.4.1.6296.0.5
	fanOk NOTIFICATION-TYPE
		OBJECTS { dsFanIndex, dsFanstatus }
		STATUS current
		DESCRIPTION 
			"Fan status OK trap when fanStatus change from disable to enable."
		::= { dasanEvents 5 }

	
	-- *******.4.1.6296.0.6
	fanFail NOTIFICATION-TYPE
		OBJECTS { dsFanIndex, dsFanstatus }
		STATUS current
		DESCRIPTION 
			"Fan status fail trap when fanStatus change from enable to disable."
		::= { dasanEvents 6 }

	
	-- *******.4.1.6296.0.7
	powerInstalled NOTIFICATION-TYPE
		OBJECTS { dsPowerIndex }
		STATUS current
		DESCRIPTION 
			"Power Installed trap when power was installed."
		::= { dasanEvents 7 }

	
	-- *******.4.1.6296.0.8
	powerRemoved NOTIFICATION-TYPE
		OBJECTS { dsPowerIndex }
		STATUS current
		DESCRIPTION 
			"Power removed trap when power was removed."
		::= { dasanEvents 8 }

	
	-- *******.4.1.6296.0.9
	fanInstalled NOTIFICATION-TYPE
		OBJECTS { dsFanIndex }
		STATUS current
		DESCRIPTION 
			"Fan Installed trap when fan was installed."
		::= { dasanEvents 9 }

	
	-- *******.4.1.6296.0.10
	fanRemoved NOTIFICATION-TYPE
		OBJECTS { dsFanIndex }
		STATUS current
		DESCRIPTION 
			"Fan removed trap when fan was removed."
		::= { dasanEvents 10 }

	
	-- *******.4.1.6296.0.11
	bcastOver NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"Broadcast packet over trap when Rx broadcast packet is over the given limit."
		::= { dasanEvents 11 }

	
	-- *******.4.1.6296.0.12
	cpuLoadOverThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"The cpuLoadOverThreshold trap is sent when cpuLoad is over the given limit."
		::= { dasanEvents 12 }

	
	-- *******.4.1.6296.0.13
	cpuLoadFallThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"The cpuLoadfallThreshold trap is sent when cpuLoad fall under the given limit."
		::= { dasanEvents 13 }

	
	-- *******.4.1.6296.0.14
	dhcpLeaseShortage NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSubnetConfName }
		STATUS current
		DESCRIPTION 
			"DHCP User IP pool shortage when User's DHCPDISCOVER message arrive."
		::= { dasanEvents 14 }

	
	-- *******.4.1.6296.0.15
	portOverThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Port traffic over HighThreshold trap when port traffic is over the given limit."
		::= { dasanEvents 15 }

	
	-- *******.4.1.6296.0.16
	portFallThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Port traffic fall under HighThreshold trap when port traffic falls below the given limit."
		::= { dasanEvents 16 }

	
	-- *******.4.1.6296.0.17
	temperatureOverThreshold NOTIFICATION-TYPE
		OBJECTS { dsTemperatureStatus}
		STATUS current
		DESCRIPTION 
			"TemperatureOverThreshold Trap is sent when temperature is over the certain threshold."
		::= { dasanEvents 17 }

	
	-- *******.4.1.6296.0.18
	systemRestart NOTIFICATION-TYPE
		OBJECTS { sysDescr, sleAutoResetBootReason }
		STATUS current
		DESCRIPTION 
			"SystemRestart Trap is sent when system is restarted by
			user. sleAutoResetBootReason is only used for auto-reset"
		::= { dasanEvents 18 }

	
	-- *******.4.1.6296.0.19
	mfgdBlocked NOTIFICATION-TYPE
		OBJECTS { dsSwitchAtMacAddress, dsSwitchAtNetAddress, dsSwitchAtVID }
		STATUS current
		DESCRIPTION 
			"mfgdblocked Trap is sent when a certain mac address is denied by mac-flood-guard mechanism."
		::= { dasanEvents 19 }

	
	-- *******.4.1.6296.0.20
	mfgdReleased NOTIFICATION-TYPE
		OBJECTS { dsSwitchAtMacAddress, dsSwitchAtNetAddress, dsSwitchAtVID }
		STATUS current
		DESCRIPTION 
			"mfgdReleased Trap is sent when a certain mac address is permitted again by mac-flood-guard mechanism."
		::= { dasanEvents 20 }

	
	-- *******.4.1.6296.0.21
	ipConflict NOTIFICATION-TYPE
		OBJECTS { dsSwitchAtNetAddress, dsSwitchAtMacAddress }
		STATUS current
		DESCRIPTION 
			"ipConflict Trap is sent when a certain IP address is shared among different users."
		::= { dasanEvents 21 }

	
	-- *******.4.1.6296.0.22
	dhcpIllegalEntry NOTIFICATION-TYPE
		OBJECTS { dsSwitchAtNetAddress, dsSwitchAtMacAddress, ifIndex }
		STATUS current
		DESCRIPTION 
			"dhcpIllegalEntry trap is sent when illegal dhcp user is found."
		::= { dasanEvents 22 }

	
	-- *******.4.1.6296.0.23
	optionModuleInstalled NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"optionModuleInstalled Trap is sent when option Module was installed."
		::= { dasanEvents 23 }

	
	-- *******.4.1.6296.0.24
	optionModuleRemoved NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"optionModuleRemoved Trap is sent when option Module was removed."
		::= { dasanEvents 24 }

	
	-- *******.4.1.6296.0.25
	memoryOverThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsFreeMem, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"MemoryOverThreshold trap is sent when used memory size is over the given limit 
			and free memory size falls below the given limit."
		::= { dasanEvents 25 }

	
	-- *******.4.1.6296.0.26
	memoryFallThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsFreeMem, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"MemoryFallThreshold trap is sent when free memory size is over the given limit."
		::= { dasanEvents 26 }

	
	-- *******.4.1.6296.0.27
	slotInstalled NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"slotInstalledTrap Trap is sent when Board was installed."
		::= { dasanEvents 27 }

	
	-- *******.4.1.6296.0.28
	slotRemoved NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"slotRemovedTrap Trap is sent when Board was removed."
		::= { dasanEvents 28 }

	
	-- *******.4.1.6296.0.29
	slotStateChange NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"slotStateChangeTrap Trap is sent when Board State change."
		::= { dasanEvents 29 }

	
	-- *******.4.1.6296.0.30
	portCRCOverThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"portCRCOverThreshold Trap is sent when the CRC count of
			port over threshold."
		::= { dasanEvents 30 }

	
	-- *******.4.1.6296.0.31
	portCRCFallThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"portCRCOverThreshold Trap is sent when the CRC count of
			port fall threshold."
		::= { dasanEvents 31 }

	
--     Newly Add traps related to temperature.
-- 
	-- *******.4.1.6296.0.32
	temperatureHighOverThreshold NOTIFICATION-TYPE
		OBJECTS { dsTemperatureStatus, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"The temperatureHighOverThreshold Trap is sent when temperature is over the high-threshold."
		::= { dasanEvents 32 }

	
	-- *******.4.1.6296.0.33
	temperatureHighFallThreshold NOTIFICATION-TYPE
		OBJECTS { dsTemperatureStatus, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"The temperatureHighFallThreshold Trap is sent when temperature fall the high-threshold."
		::= { dasanEvents 33 }

	
	-- *******.4.1.6296.0.34
	temperatureLowOverThreshold NOTIFICATION-TYPE
		OBJECTS { dsTemperatureStatus, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"The temperatureLowOverThreshold Trap is sent when 1st temperature is over the low-threshold."
		::= { dasanEvents 34 }

	
	-- *******.4.1.6296.0.35
	temperatureLowFallThreshold NOTIFICATION-TYPE
		OBJECTS { dsTemperatureStatus, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"The temperatureLowFallThreshold Trap is sent when temperature fall under the low-threshold."
		::= { dasanEvents 35 }

	
	-- *******.4.1.6296.0.36
	userLogIn NOTIFICATION-TYPE
		OBJECTS { dsUserLoginName, dsUserLoginIpAddress }
		STATUS current
		DESCRIPTION 
			"The user-login trap is sent when user login."
		::= { dasanEvents 36 }

	
	-- *******.4.1.6296.0.37
	userLogOut NOTIFICATION-TYPE
		OBJECTS { dsUserLoginName, dsUserLoginIpAddress }
		STATUS current
		DESCRIPTION 
			"The user-logout trap is sent when user logout."
		::= { dasanEvents 37 }

	
	-- *******.4.1.6296.0.38
	restoreFactory NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"The restoreFactory trap is sent when user initialize as factory defaults."
		::= { dasanEvents 38 }

	
	-- *******.4.1.6296.0.39
	cpuLoadHighOverThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr, sleSlotSystemIndex, sleSlotSystemCPULoadCurrent, sleSlotSystemCPULoadHighThreshold}
		STATUS current
		DESCRIPTION 
			"The cpuloadHighOverThreshold Trap is sent when temperature is over the high-threshold."
		::= { dasanEvents 39 }

	
	-- *******.4.1.6296.0.40
	cpuLoadHighFallThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr, sleSlotSystemIndex, sleSlotSystemCPULoadCurrent, sleSlotSystemCPULoadHighThreshold}
		STATUS current
		DESCRIPTION 
			"The cpuloadHighFallThreshold Trap is sent when temperature fall the high-threshold."
		::= { dasanEvents 40 }

	
	-- *******.4.1.6296.0.41
	cpuLoadLowOverThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr, sleSlotSystemIndex, sleSlotSystemCPULoadCurrent, sleSlotSystemCPULoadLowThreshold}
		STATUS current
		DESCRIPTION 
			"The cpuloadLowOverThreshold Trap is sent when 1st temperature is over the low-threshold."
		::= { dasanEvents 41 }

	
	-- *******.4.1.6296.0.42
	cpuLoadLowFallThreshold NOTIFICATION-TYPE
		OBJECTS { sysDescr, sleSlotSystemIndex, sleSlotSystemCPULoadCurrent, sleSlotSystemCPULoadLowThreshold}
		STATUS current
		DESCRIPTION 
			"The cpuloadLowFallThreshold Trap is sent when temperature fall under the low-threshold."
		::= { dasanEvents 42 }

	
--     skip 43 for dsIgmpSnooping Notification	
-- igmpSnoopingChanged NOTIFICATION-TYPE
--    ::= {dasanEvents 43 }
	-- *******.4.1.6296.0.44
	dsSystemUpgradeChanged NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"System NOS Upgrade status"
		::= { dasanEvents 44 }

	-- *******.4.1.6296.0.45
	interfaceOperDown NOTIFICATION-TYPE
		OBJECTS { ifIndex, ifOperStatus, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the interface oper down."
		::= { dasanEvents 45 }

	
	-- *******.4.1.6296.0.46
	interfaceOperUp NOTIFICATION-TYPE
		OBJECTS { ifIndex, ifOperStatus, sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the interface poer up."
		::= { dasanEvents 46 }

	-- *******.4.1.6296.0.47
	macLearnOverMax NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"This trap indicates the count of L2 mac is over the max."
		::= { dasanEvents 47 }

	-- *******.4.1.6296.0.48
	macLearnOverMaxCleared NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"This trap indicates the count of L2 mac is over the max is cleared"
		::= { dasanEvents 48 }

	-- *******.4.1.6296.0.49
	interfaceMacLearnOverMax NOTIFICATION-TYPE
		OBJECTS { sysDescr, ifindex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the count of interface's L2 mac is over the max."
		::= { dasanEvents 49 }

	-- *******.4.1.6296.0.50
	interfaceMacLearnOverMaxCleared NOTIFICATION-TYPE
		OBJECTS { sysDescr, ifindex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the count of interface's L2 mac is over the max is cleared"
		::= { dasanEvents 50 }
	
	
	-- *******.4.1.6296.0.51
	adslAtucPerfLofsThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Loss of Framing threshold reached(ATUC)."
		::= { dasanEvents 51 }

	
	-- *******.4.1.6296.0.52
	adslAtucPerfLossThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Loss of signal threshold reached(ATUC)."
		::= { dasanEvents 52 }

	
	-- *******.4.1.6296.0.53
	adslAtucPerfLprsThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Loss of power threshold reached (ATUC)."
		::= { dasanEvents 53 }

	
	-- *******.4.1.6296.0.54
	adslAtucPerfESsThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Errored Seconds threshold reached (ATUC)."
		::= { dasanEvents 54 }

	
	-- *******.4.1.6296.0.55
	adslAtucRateChange NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Changed atuc rate."
		::= { dasanEvents 55 }

	
	-- *******.4.1.6296.0.56
	adslAtucPerfLolsThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Loss of Link threshold reached (ATUC)."
		::= { dasanEvents 56 }

	
	-- *******.4.1.6296.0.61
	adslAturPerfLofsThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Loss of Framing threshold reached(ATUR)."
		::= { dasanEvents 61 }

	
	-- *******.4.1.6296.0.62
	adslAturPerfLossThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Loss of signal threshold reached(ATUR)."
		::= { dasanEvents 62 }

	
	-- *******.4.1.6296.0.63
	adslAturPerfLprsThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Loss of power threshold reached (ATUR)."
		::= { dasanEvents 63 }

	
	-- *******.4.1.6296.0.64
	adslAturPerfESsThresh NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Errored Seconds threshold reached (ATUR)."
		::= { dasanEvents 64 }

	
	-- *******.4.1.6296.0.65
	vdslCPEChangeInfoTrap NOTIFICATION-TYPE
		OBJECTS { sysDescr, ipAdEntAddr, dsVdslPortIndex, vdslPhysInvSerialNumber, dsUserMacAddress1, 
			dsUserMacAddress2, dsUserMacAddress3, dsUserMacAddress4 }
		STATUS current
		DESCRIPTION 
			"Trap is sent with CPE Serial No., Mac Address of CPE, after CPE serial change.  "
		::= { dasanEvents 65 }

	
	-- *******.4.1.6296.0.66
	vdslArulePacketDropTrap NOTIFICATION-TYPE
		OBJECTS { sysUpTime }
		STATUS current
		DESCRIPTION 
			"Packet Dropped by arule .  "
		::= { dasanEvents 66 }

        -- *******.4.1.6296.0.69
	portMododuleInstall NOTIFICATION-TYPE
		OBJECTS { ifindex }
		STATUS current
		DESCRIPTION 
			"port module install"
		::= { dasanEvents 69 }

  	-- *******.4.1.6296.0.70
	portMododuleRemove NOTIFICATION-TYPE
		OBJECTS { ifindex }
		STATUS current
		DESCRIPTION 
			"port module remove"
		::= { dasanEvents 70 }

--    flood-guard (CPU|SYSTEM) 
-- 
	-- *******.4.1.6296.0.71
	cfgdBlocked NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Cfgdblocked Trap is sent when a certain port is blocked by cpu-flood-guard mechanism."
		::= { dasanEvents 71 }

	
	-- *******.4.1.6296.0.72
	cfgdReleased NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"CfgdReleased Trap is sent when a certain port is non-blocked by cpu-flood-guard mechanism."
		::= { dasanEvents 72 }

	
	-- *******.4.1.6296.0.73
	cfgdOverThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"CfgdReleased Trap is sent when a certain port is over threshold by cpu-flood-guard mechanism."
		::= { dasanEvents 73 }

	
	-- *******.4.1.6296.0.74
	cfgdUnderThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"CfgdReleased Trap is sent when a certain port is under	threshold by cpu-flood-guard mechanism."
		::= { dasanEvents 74 }

	
	-- *******.4.1.6296.0.75
	sfgdBlocked NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Sfgdblocked Trap is sent when a certain port is blocked by system-flood-guard mechanism."
		::= { dasanEvents 75 }

	
	-- *******.4.1.6296.0.76
	sfgdReleased NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"SfgdReleased Trap is sent when a certain port is non-blocked by system-flood-guard mechanism."
		::= { dasanEvents 76 }

	
	-- *******.4.1.6296.0.77
	sfgdOverThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"SfgdReleased Trap is sent when a certain port is over threshold by system-flood-guard mechanism."
		::= { dasanEvents 77 }

	
	-- *******.4.1.6296.0.78
	sfgdUnderThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"SfgdReleased Trap is sent when a certain port is under threshold by system-flood-guard mechanism."
		::= { dasanEvents 78 }

	
	-- *******.4.1.6296.0.79
	ppsControlBlocked NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"PpsControlblocked Trap is sent when a certain port is blocked by PPS-Control mechanism."
		::= { dasanEvents 79 }

	
	-- *******.4.1.6296.0.80
	ppsControlReleased NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"PpsControlReleased Trap is sent when a certain port is non-blocked by PPS-Control mechanism."
		::= { dasanEvents 80 }

	
	-- *******.4.1.6296.0.81
	ppsControlOverThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION
			"PpsControlReleased Trap is sent when a certain port is over threshold by PPS-Control mechanism."
		::= { dasanEvents 81 }

	
	-- *******.4.1.6296.0.82
	ppsControlUnderThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION
			"PpsControlReleased Trap is sent when a certain port is	under threshold by PPS-Control mechanism."
		::= { dasanEvents 82 }

	
--    remote upgrade
-- 
	-- *******.4.1.6296.0.83
	nosRemoteUpgradeRequest NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSystemUpgradePath, dsSystemUpgradeStorage }
		STATUS current
		DESCRIPTION 
			"This trap indicates that the system received nos-remote-upgrade command."
		::= { dasanEvents 83 }

	
	-- *******.4.1.6296.0.84
	nosRemoteUpgradeSuccess NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSystemUpgradePath, dsSystemUpgradeStorage }
		STATUS current
		DESCRIPTION 
			"This trap indicates that the NOS succeeded to upgrade NOS."
		::= { dasanEvents 84 }

	
	-- *******.4.1.6296.0.85
	nosRemoteUpgradeFailAbort NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSystemUpgradePath, dsSystemUpgradeStorage }
		STATUS current
		DESCRIPTION 
			"This trap indicates that the system failed to upgrade NOS by user interrupt"
		::= { dasanEvents 85 }

	
	-- *******.4.1.6296.0.86
	nosRemoteUpgradeFailHeaderError NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSystemUpgradePath, dsSystemUpgradeStorage }
		STATUS current
		DESCRIPTION 
			"This trap indicates that the system failed to upgrade NOS, for NOS header-error."
		::= { dasanEvents 86 }

	
	-- *******.4.1.6296.0.87
	nosRemoteUpgradeFailDownloadError NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSystemUpgradePath, dsSystemUpgradeStorage }
		STATUS current
		DESCRIPTION 
			"This trap indicates that the system failed to upgrade NOS, for NOS checksum-error"
		::= { dasanEvents 87 }

	
	-- *******.4.1.6296.0.88
	nosRemoteUpgradeFailFlashError NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSystemUpgradePath, dsSystemUpgradeStorage }
		STATUS current
		DESCRIPTION 
			"This trap indicates that the system failed to upgrade NOS, for flash operation"
		::= { dasanEvents 88 }

	
	-- *******.4.1.6296.0.89
	nosRemoteUpgradeStart NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsSystemUpgradePath, dsSystemUpgradeStorage }
		STATUS current
		DESCRIPTION 
			"This trap indicates that the upgrade NOS process was started."
		::= { dasanEvents 89 }

	
--    Dying gasp for power fault 
	-- *******.4.1.6296.0.90
	systemDyingGasp NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"This trap indicates that Dying-gasp for power fault."
		::= { dasanEvents 90 }

	
--    Attack-Guard 
	-- *******.4.1.6296.0.91
	attackGuardBlock NOTIFICATION-TYPE
		OBJECTS { dsPortModuleIndex, dsPortPortIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates that some port is blocked by attack-guard."
		::= { dasanEvents 91 }

	
	-- *******.4.1.6296.0.92
	attackGuardUnBlock NOTIFICATION-TYPE
		OBJECTS { dsPortModuleIndex, dsPortPortIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates that some port is unblocked by attack-guard."
		::= { dasanEvents 92 }

	
	-- *******.4.1.6296.0.93
	fanPeriodicFault NOTIFICATION-TYPE
		OBJECTS { dsFanIndex, dsFanstatus }
		STATUS current
		DESCRIPTION 
			"This trap indicates the fan status is fault in some period."
		::= { dasanEvents 93 }

	
	-- *******.4.1.6296.0.94
	remoteOamDyingGasp NOTIFICATION-TYPE
		OBJECTS { ifindex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the remote oam entity is in dying-gasp ."
		::= { dasanEvents 94 }

	
	-- *******.4.1.6296.0.95
	remoteOamDyingGaspRestoration NOTIFICATION-TYPE
		OBJECTS { ifindex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the remote oam entity is restored."
		::= { dasanEvents 95 }

	
	-- *******.4.1.6296.0.96
	arpInspectInvalid NOTIFICATION-TYPE
		OBJECTS { sleArpInspectInvalidLogReason, sleArpInspectInvalidLogSamePktCnt, sleArpInspectInvalidLogOpcode, sleArpInspectInvalidLogPortNumber, sleArpInspectInvalidLogVlanId, 
			sleArpInspectInvalidLogSrcMacAddr, sleArpInspectInvalidLogSrcIpAddr, sleArpInspectInvalidLogDstMacAddr, sleArpInspectInvalidLogDstIpAddr, sleArpInspectInvalidLogLastRecvTime
			 }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ARP inspection is invalided."
		::= { dasanEvents 96 }

	
	-- *******.4.1.6296.0.97
	systemSwitchover NOTIFICATION-TYPE
		OBJECTS { sysDescr }
		STATUS current
		DESCRIPTION 
			"This trap indicates the system is switchover."
		::= { dasanEvents 97 }

	
	-- *******.4.1.6296.0.98
	boardChangeState NOTIFICATION-TYPE
		OBJECTS { sleRedActiveBoard, sleRedActivePrevState, sleRedActiveCurrState }
		STATUS current
		DESCRIPTION 
			"This trap indicates the board state is changed."
		::= { dasanEvents 98 }

	-- *******.4.1.6296.0.99
	redConfigurationMisMatch NOTIFICATION-TYPE
		OBJECTS {sysDescr}
		STATUS  current
		DESCRIPTION
		"Configurations of Active and Standby SFU are different."
		::= { dasanEvents 99}


--    100, 101 reserved as SLE
-- 
	-- *******.4.1.6296.0.105
	clockModuleInstalled NOTIFICATION-TYPE
		OBJECTS { sleClockModuleIndex, sleClockModuleBoardId, sleClockModuleInstallStatus }
		STATUS current
		DESCRIPTION 
			"clockModuleInstalled trap send when sleClockModuleInstallStatus is installed."
		::= { dasanEvents 105 }

	
	-- *******.4.1.6296.0.106
	clockModuleRemoved NOTIFICATION-TYPE
		OBJECTS { sleClockModuleIndex, sleClockModuleBoardId, sleClockModuleInstallStatus }
		STATUS current
		DESCRIPTION 
			"clockModuleRemoved trap send when sleClockModuleInstallStatus is removed."
		::= { dasanEvents 106 }

	
	-- *******.4.1.6296.0.107
	clockModuleInitOk NOTIFICATION-TYPE
		OBJECTS { sleClockModuleIndex, sleClockModuleBoardId, sleClockModuleInitStatus }
		STATUS current
		DESCRIPTION 
			"clockModuleInitOk trap send when sleClockModuleInitStatus is ok."
		::= { dasanEvents 107 }

	
	-- *******.4.1.6296.0.108
	clockModuleInitFail NOTIFICATION-TYPE
		OBJECTS { sleClockModuleIndex, sleClockModuleBoardId, sleClockModuleInitStatus }
		STATUS current
		DESCRIPTION 
			"clockModuleInitFail trap send when sleClockModuleInitStatus is failure."
		::= { dasanEvents 108 }

	
	-- *******.4.1.6296.0.109
	interfaceAdminUp NOTIFICATION-TYPE
		OBJECTS { ifIndex, ifDescr, ifAdminStatus }
		STATUS current
		DESCRIPTION 
			"This trap indicates the interface admin up."
		::= { dasanEvents 109 }

	
	-- *******.4.1.6296.0.110
	interfaceAdminDown NOTIFICATION-TYPE
		OBJECTS { ifIndex, ifDescr, ifAdminStatus }
		STATUS current
		DESCRIPTION 
			"This trap indicates the interface admin down."
		::= { dasanEvents 110 }

	
	-- *******.4.1.6296.0.111
	powerMonEventDetected NOTIFICATION-TYPE
		OBJECTS { sleSlotPowerMonIndex, sleSlotPowerMonStatus }
		STATUS current
		DESCRIPTION 
			"This trap indicates the power monitoring detects voltage change of slot."
		::= { dasanEvents 111 }

	
	-- *******.4.1.6296.0.112
	ntpConnectionFail NOTIFICATION-TYPE
		OBJECTS { sleNTPServerName }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ntp connection is lost."
		::= { dasanEvents 112 }

	
	-- *******.4.1.6296.0.113
	fanSpeedFallUnderThreshold NOTIFICATION-TYPE
		OBJECTS { sleFanUnitIndex, sleFanUnitSpeed }
		STATUS current
		DESCRIPTION 
			"This trap indicates the fan speed is under the threshold."
		::= { dasanEvents 113 }

	
	-- *******.4.1.6296.0.114
	fanSpeedRisingOverThreshold NOTIFICATION-TYPE
		OBJECTS { sleFanUnitIndex, sleFanUnitSpeed }
		STATUS current
		DESCRIPTION 
			"This trap indicates the fan speed is over the threshold."
		::= { dasanEvents 114 }

	
	-- *******.4.1.6296.0.115
	userLoginFail NOTIFICATION-TYPE
		OBJECTS { dsUserLoginName, dsUserLoginIpAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the user fail to login."
		::= { dasanEvents 115 }

	
	-- *******.4.1.6296.0.116
	bfdSessionStateChanged NOTIFICATION-TYPE
		OBJECTS { ifindex, sleBFDSessionNeighAddrType, sleBFDSessionNeighAddrValue, sleBFDSessionStatus }
		STATUS current
		DESCRIPTION 
			"This trap indicates that BFD session state is changed."
		::= { dasanEvents 116 }


	clockModuleOPModeChanged NOTIFICATION-TYPE
	    OBJECTS {
		sleClockModuleIndex, sleClockModuleOPMode, sleSlotSystemIndex
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that slot Operation mode is changed."
	    ::= { dasanEvents 117 }

	clockModuleRefrencesChanged NOTIFICATION-TYPE
	    OBJECTS {
		sleClockModuleIndex, sleSlotSystemIndex
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that slot reference ntr is changed."
	    ::= { dasanEvents 118 }

	-- *******.4.1.6296.0.119
	portOperUp NOTIFICATION-TYPE
	    OBJECTS {
		sleEthernetPortSlotIndex,sleEthernetPortSlotPortIndex 
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that port operation
		     is up."
	    ::= { dasanEvents 119 }

	-- *******.4.1.6296.0.120
	portOperDown NOTIFICATION-TYPE
	    OBJECTS {
		sleEthernetPortSlotIndex,sleEthernetPortSlotPortIndex 
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that port operation
		     is down"
	    ::= { dasanEvents 120 }


	-- *******.4.1.6296.0.121
	portRxOverThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Port Rx traffic over HighThreshold trap when port traffic is over the given limit."
		::= { dasanEvents 121 }


	-- *******.4.1.6296.0.122
	portRxFallThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Port Rx traffic fall under HighThreshold trap when port traffic falls below the given limit."
		::= { dasanEvents 122 }


	-- *******.4.1.6296.0.123
	portTxOverThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Port Tx traffic over HighThreshold trap when port traffic is over the given limit."
		::= { dasanEvents 123 }


	-- *******.4.1.6296.0.124
	portTxFallThreshold NOTIFICATION-TYPE
		OBJECTS { ifIndex }
		STATUS current
		DESCRIPTION 
			"Port Tx traffic fall under HighThreshold trap when port traffic falls below the given limit."
		::= { dasanEvents 124 }


	clockModuleInSrcStatusChanged NOTIFICATION-TYPE
	    OBJECTS {
		sleClockModuleIndex, sleClockInfoInSrcType, sleClockInfoInSrcStatus
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that module status is changed."
	    ::= { dasanEvents 125 }

	clockModuleInSrcAISStatusChanged NOTIFICATION-TYPE
	    OBJECTS {
		sleClockModuleIndex, sleClockInfoInSrcType, sleClockInfoInSrcAISStatus
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that AIS status is changed."
	    ::= { dasanEvents 126 }

	clockModuleInSrcLoSStatusChaged NOTIFICATION-TYPE
	    OBJECTS {
		sleClockModuleIndex, sleClockInfoInSrcType, sleClockInfoInSrcLoSStatus
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that LOS status is changed."
	    ::= { dasanEvents 127 }

	clockModuleInSrcLDSCStatus NOTIFICATION-TYPE
	    OBJECTS {
		sleClockModuleIndex, sleClockInfoInSrcType, sleClockInfoInSrcLoSStatus
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that LDSC status is changed."
	    ::= { dasanEvents 128 }

	clockModuleInSrcNtrClockChanged NOTIFICATION-TYPE
	    OBJECTS {
		sleClockModuleIndex, sleClockInfoInSrcType, sleClockInfoInSrcNtrClockType
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that NTR is changed."
	    ::= { dasanEvents 129 }


	reloadMateSFU NOTIFICATION-TYPE
	    OBJECTS {
		sleSlotSystemIndex
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that reload mate SFU."
	    ::= { dasanEvents 130 }

	processDown NOTIFICATION-TYPE
	    OBJECTS {
		sleSlotSystemIndex, sleProcessID
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that certain process is crashed and restarted."
	    ::= { dasanEvents 131 }

	processTimeout NOTIFICATION-TYPE
	    OBJECTS {
		sleSlotSystemIndex, sleProcessID
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that certain process is timeout."
	    ::= { dasanEvents 132 }

	sdcardInstalled NOTIFICATION-TYPE
	    OBJECTS {
		sleSlotSystemIndex
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that SD card is installed."
	    ::= { dasanEvents 133 }

	sdcardRemoved NOTIFICATION-TYPE
	    OBJECTS {
		sleSlotSystemIndex
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that SD card is removed."
	    ::= { dasanEvents 134 }

	bootedNonDefaultOS NOTIFICATION-TYPE
	    OBJECTS {
		sleSystemDefaultOS, sleSystemRunningOS, sleSlotSystemIndex
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that the system started with non-default OS."
	    ::= { dasanEvents 141 } 
	    
       slotBootedNonDefaultOS NOTIFICATION-TYPE
	    OBJECTS {
		sleSlotSystemIndex
	    }
	    STATUS             current
	    DESCRIPTION
		     "This trap indicate that the IU card started with non-default OS."
	    ::= { dasanEvents 142 }

		
	-- *******.4.1.6296.0.145
	cpuPpsOverThreshold NOTIFICATION-TYPE
		OBJECTS { slePerformanceBaseCpuPpsStatus, slePerformanceBaseCpuPpsThreshold, slePerformanceBaseCpuPpsAvg}
		STATUS  current
		DESCRIPTION
		"The PPS of cpu is over the threshold."
		::= { dasanEvents 145}

	-- *******.4.1.6296.0.146
	cpuPpsUnderThreshold NOTIFICATION-TYPE
		OBJECTS { slePerformanceBaseCpuPpsStatus, slePerformanceBaseCpuPpsThreshold, slePerformanceBaseCpuPpsAvg}
		STATUS  current
		DESCRIPTION
		"The PPS of cpu is under the threshold."
		::= { dasanEvents 146}

	redundantPacketDetected NOTIFICATION-TYPE
		OBJECTS { }
		STATUS  current
		DESCRIPTION
		"Redundant packets is detected."
		::= { dasanEvents 147}
		
	redundantPacketDetectedCleared NOTIFICATION-TYPE
		OBJECTS { }
		STATUS  current
		DESCRIPTION
		"Redundant packets detection is cleared."
		::= { dasanEvents 148}

--   GE-PON
	-- *******.4.1.6296.0.151
	ponLinkFault NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"ONU reports a Link Fault via the flag in the 802.3ah OAM header.No further information about the fault is available.."
		::= { dasanEvents 151 }

	
	-- *******.4.1.6296.0.152
	ponLos NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Loss of Signal is detected by the PHY device for OLT Network  interface, and PON interfaces on  the OLT and ONU."
		::= { dasanEvents 152 }

	
	-- *******.4.1.6296.0.153
	ponTransmitFail NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Transmitted output does not match input, as reported by EPON optics."
		::= { dasanEvents 153 }

	
	-- *******.4.1.6296.0.154
	ponTransmitDegrade NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Transmitted output has a high error level, but not so high as to cause - Failure. Optionallyreported by EPON optical module."
		::= { dasanEvents 154 }

	
	-- *******.4.1.6296.0.155
	ponQueueOverflow NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"One of the queues for the link has dropped frames due to becoming full."
		::= { dasanEvents 155 }

	
	-- *******.4.1.6296.0.156
	ponMacLearningTableOverflow NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"MAC address learning table is full, not in overwrite mode, and a new MAC address wasencountered."
		::= { dasanEvents 156 }

	
	-- *******.4.1.6296.0.157
	ponDuplicateMacRegistration NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"MPCP register request received for MAC that is registered and in-service."
		::= { dasanEvents 157 }

	
	-- *******.4.1.6296.0.158
	ponReportTimeout NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"ONU does not respond to grants from the OLT."
		::= { dasanEvents 158 }

	
	-- *******.4.1.6296.0.159
	ponGateTimeout NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Excessive interval between MPCP gates."
		::= { dasanEvents 159 }

	
	-- *******.4.1.6296.0.160
	ponOAMKeepaliveTimeout NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"OAM KeepaliveTimeout - OAM layer does not receive periodic messages from its peer."
		::= { dasanEvents 160 }

	
	-- *******.4.1.6296.0.161
	ponKeyExchangeFailure NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"The link peer has failed to generate a new key message, or to switch to the new key, in the timeallotted by the key exchange timer for this link. Encryption remains in force using the old key, and the key exchange process continues.The alarm will clear when a new key is established."
		::= { dasanEvents 161 }

	
	-- *******.4.1.6296.0.162
	ponAutoNegotiation NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"OLT Network port fails to auto-negotiate with the peer (if auto-negotiation feature is enabled)."
		::= { dasanEvents 162 }

	
	-- *******.4.1.6296.0.163
	ponLinkLoopback NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"The link is currently in the loopback state."
		::= { dasanEvents 163 }

	
	-- *******.4.1.6296.0.164
	ponStandardDyingGasp NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"ONU reports Dying Gasp via that bit in 802.3ah OAM header."
		::= { dasanEvents 164 }

	
	-- *******.4.1.6296.0.165
	ponPowerFail NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Power supply failure."
		::= { dasanEvents 165 }

	
	-- *******.4.1.6296.0.166
	ponTemperature NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Operating temperature range exceeded."
		::= { dasanEvents 166 }

	
	-- *******.4.1.6296.0.167
	ponAuthenticationInformationUnavailable NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"ONU is configured to use external authentication device, and that device is not present."
		::= { dasanEvents 167 }

	
	-- *******.4.1.6296.0.171
	ponOctetsTransferred NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Octets Transferred."
		::= { dasanEvents 171 }

	
	-- *******.4.1.6296.0.172
	ponTotalFramesTransferred NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Total frames transferred."
		::= { dasanEvents 172 }

	
	-- *******.4.1.6296.0.173
	ponUnicastFramesTransferred NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Unicast frames transferred."
		::= { dasanEvents 173 }

	
	-- *******.4.1.6296.0.174
	ponBroadcastFramesTransferred NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Broadcast frames transferred."
		::= { dasanEvents 174 }

	
	-- *******.4.1.6296.0.175
	ponMulticastFramesTransferred NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Multicast frames transferred."
		::= { dasanEvents 175 }

	
	-- *******.4.1.6296.0.176
	ponCRC32Errors NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"CRC-32 Errors."
		::= { dasanEvents 176 }

	
	-- *******.4.1.6296.0.177
	ponUndersizeFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Undersize Frames."
		::= { dasanEvents 177 }

	
	-- *******.4.1.6296.0.178
	ponOversizeFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Oversize Frames."
		::= { dasanEvents 178 }

	
	-- *******.4.1.6296.0.179
	ponCollisions NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Collisions."
		::= { dasanEvents 179 }

	
	-- *******.4.1.6296.0.180
	pon64OctetFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"64 Octet Frames."
		::= { dasanEvents 180 }

	
	-- *******.4.1.6296.0.181
	pon127OctetFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"65-127 Octet Frames."
		::= { dasanEvents 181 }

	
	-- *******.4.1.6296.0.182
	pon255OctetFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"128-255 Octet Frames."
		::= { dasanEvents 182 }

	
	-- *******.4.1.6296.0.183
	pon511OctetFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"256-511 Octet Frames."
		::= { dasanEvents 183 }

	
	-- *******.4.1.6296.0.184
	pon1023OctetFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"512-1023 Octet Frames."
		::= { dasanEvents 184 }

	
	-- *******.4.1.6296.0.185
	pon1518OctetFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"1024-1518 Octet Frames."
		::= { dasanEvents 185 }

	
	-- *******.4.1.6296.0.186
	pon1519OverOctetFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"1519+ Octet Frames."
		::= { dasanEvents 186 }

	
	-- *******.4.1.6296.0.187
	ponFramesDropped NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Frames dropped."
		::= { dasanEvents 187 }

	
	-- *******.4.1.6296.0.188
	ponOctetsDropped NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Octets dropped."
		::= { dasanEvents 188 }

	
	-- *******.4.1.6296.0.189
	ponOctetsDelayed NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Octets delayed."
		::= { dasanEvents 189 }

	
	-- *******.4.1.6296.0.190
	ponOctetsGranted NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Octets granted."
		::= { dasanEvents 190 }

	
	-- *******.4.1.6296.0.191
	ponOctetsGrantedButUnused NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Octets granted but unused."
		::= { dasanEvents 191 }

	
	-- *******.4.1.6296.0.192
	ponMaximumDelay NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"Maximum Delay (units of 100usec)."
		::= { dasanEvents 192 }

	
	-- *******.4.1.6296.0.193
	ponCRC8Errors NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonQueueIndex, 
			dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			"CRC-8 (preamble) Errors."
		::= { dasanEvents 193 }

	
	-- *******.4.1.6296.0.194
	ponLineCodeErrors NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonPortIndex, 
			dsPonQueueIndex, dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			" Line code errors "
		::= { dasanEvents 194 }

	
	-- *******.4.1.6296.0.195
	ponPauseFrames NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonPortIndex, 
			dsPonQueueIndex, dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			" Pause Frames "
		::= { dasanEvents 195 }

	
	-- *******.4.1.6296.0.196
	ponErroredFrameSeconds NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonPortIndex, 
			dsPonQueueIndex, dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			" Errored Frame Seconds "
		::= { dasanEvents 196 }

	
	-- *******.4.1.6296.0.197
	ponErroredFramePeriod NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonPortIndex, 
			dsPonQueueIndex, dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			" Errored Frame Period "
		::= { dasanEvents 197 }

	
	-- *******.4.1.6296.0.198
	ponErroredFrameSecondsSummary NOTIFICATION-TYPE
		OBJECTS { dsPonOltIndex, dsPonOnuIndex, dsPonOnuMacAddress, dsPonTaggedLabel, dsPonPortIndex, 
			dsPonQueueIndex, dsPonDirection, dsPonState, dsPonCount }
		STATUS current
		DESCRIPTION 
			" Errored Frame Seconds Summary "
		::= { dasanEvents 198 }

	
	-- *******.4.1.6296.0.200
	gponOnuRegistered NOTIFICATION-TYPE
		OBJECTS { sysDescr, sleAgentAddress, sleGponOltId, sleGponOnuId, sleGponOnuSerial, 
			sleGponOnuIsFirst }
		STATUS current
		DESCRIPTION 
			" Registered Onu Informations "
		::= { dasanEvents 200 }

	
	-- *******.4.1.6296.0.201
	gponOnuDeregistered NOTIFICATION-TYPE
		OBJECTS { sysDescr, sleAgentAddress, sleGponOltId, sleGponOnuId, sleGponOnuSerial
			 }
		STATUS current
		DESCRIPTION 
			" Deregistered Onu Informations "
		::= { dasanEvents 201 }

	
	-- *******.4.1.6296.0.202
	swWatchdogDetectError NOTIFICATION-TYPE
		OBJECTS { sleSwWatchdogType }
		STATUS current
		DESCRIPTION 
			" S/W Watchdog Detect Error "
		::= { dasanEvents 202 }

	
	-- *******.4.1.6296.0.203
	swWatchdogClearError NOTIFICATION-TYPE
		OBJECTS { sleSwWatchdogType }
		STATUS current
		DESCRIPTION 
			" S/W Watchdog Clear Error "
		::= { dasanEvents 203 }

	
	-- *******.4.1.6296.0.204
	swWatchdogNormalState NOTIFICATION-TYPE
		OBJECTS { sleSwWatchdogType }
		STATUS current
		DESCRIPTION 
			" S/W Watchdog Normal State "
		::= { dasanEvents 204 }

	
	-- *******.4.1.6296.0.205
	swWatchdogAbnomalState NOTIFICATION-TYPE
		OBJECTS { sleSwWatchdogType }
		STATUS current
		DESCRIPTION 
			" S/W Watchdog Abnormal State "
		::= { dasanEvents 205 }

	
	-- *******.4.1.6296.0.206
	gponOnuUpgradeCompleted NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial, sleGponOnuNosUpgradeStatus }
		STATUS current
		DESCRIPTION 
			" Upgrade Completed Onu Informations "
		::= { dasanEvents 206 }

	
	-- *******.4.1.6296.0.207
	gponOltCableDown NOTIFICATION-TYPE
		OBJECTS { sleGponOltId }
		STATUS current
		DESCRIPTION 
			" GPON Cable down event is detected.(This is not used for
			any product)"
		::= { dasanEvents 207 }

	
	-- *******.4.1.6296.0.208
	gponOnuAutoToManual NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial }
		STATUS current
		DESCRIPTION 
			" GPON Onu is auto-to-manual "
		::= { dasanEvents 208 }

	
	-- *******.4.1.6296.0.209
	slotActionEvent NOTIFICATION-TYPE
		OBJECTS { sleSlotSystemIndex, sleSlotStatusCurrentInstallCardType, sleSlotStatusActionEvent }
		STATUS current
		DESCRIPTION 
			" This trap indicates the slot action event "
		::= { dasanEvents 209 }

	
	-- *******.4.1.6296.0.210
	slotStateChanged NOTIFICATION-TYPE
		OBJECTS { sleSlotSystemIndex, sleSlotStatusCurrentInstallCardType, sleSlotStatusState }
		STATUS current
		DESCRIPTION 
			" This trap indicates the slot state is changed "
		::= { dasanEvents 210 }

	
	-- *******.4.1.6296.0.211
	gponNoAuthONU NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuAthenticationStatus }
		STATUS current
		DESCRIPTION 
			" No autheticated ONU "
		::= { dasanEvents 211 }

	
	-- *******.4.1.6296.0.212
	gponDuplicateONU NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial }
		STATUS current
		DESCRIPTION 
			" Duplicated ONU "
		::= { dasanEvents 212 }

	
	-- *******.4.1.6296.0.213
	gponDyingGasp NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial }
		STATUS current
		DESCRIPTION 
			"Dying Gasp of GPON ONU "
		::= { dasanEvents 213 }

	
	-- *******.4.1.6296.0.214
	gponRogueOnu NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial }
		STATUS current
		DESCRIPTION 
			"Found Rogue ONU. "
		::= { dasanEvents 214 }

	
	-- *******.4.1.6296.0.215
	gponOnuBatteryEvent NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuBatteryStatus }
		STATUS current
		DESCRIPTION 
			"Battery Event. "
		::= { dasanEvents 215 }

	
	-- *******.4.1.6296.0.216
	gponOnuBatteryEventCleared NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuBatteryStatus }
		STATUS current
		DESCRIPTION 
			"Battery Event is cleared. "
		::= { dasanEvents 216 }

	
	-- *******.4.1.6296.0.217
	gponOnuPortOperStatus NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial,
		    sleGponOnuPortId, sleGponOnuDescription, sleGponOnuPortControlSlotId,
		    sleGponOnuPortControlUniDescription, sleGponOnuPortOperStatus
			 }
		STATUS current
		DESCRIPTION 
			" This trap indicates state of ONU Port Operation. "
		::= { dasanEvents 217 }

	
	-- *******.4.1.6296.0.218
	gponTransceiverChanged NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOltTransceiverPort }
		STATUS current
		DESCRIPTION 
			" This trap indicates GPON transceiver changed. "
		::= { dasanEvents 218 }

	
	-- *******.4.1.6296.0.219
	gponOltLosOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId }
		STATUS current
		DESCRIPTION 
			" This trap indicates state of OLT Loss Of Signal (on). "
		::= { dasanEvents 219 }

	
	-- *******.4.1.6296.0.220
	gponOltLosOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId }
		STATUS current
		DESCRIPTION 
			" This trap indicates state of OLT Loss Of Signal (off). "
		::= { dasanEvents 220 }

	
	-- *******.4.1.6296.0.221
	gponOltLosiOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			" This trap indicates state of OLT Loss Of Signal for ONUi (on). "
		::= { dasanEvents 221 }

	
	-- *******.4.1.6296.0.222
	gponOltLosiOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			" This trap indicates state of OLT Loss Of Signal for ONUi (off). "
		::= { dasanEvents 222 }

	
	-- *******.4.1.6296.0.223
	gponDyingGaspOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial }
		STATUS current
		DESCRIPTION 
			"This trap indicates Dying Gasp of GPON ONU (on). "
		::= { dasanEvents 223 }

	
	-- *******.4.1.6296.0.224
	gponDyingGaspOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuSerial }
		STATUS current
		DESCRIPTION 
			"This trap indicates Dying Gasp of GPON ONU (off). "
		::= { dasanEvents 224 }

	
	-- *******.4.1.6296.0.225
	slotErrorLEDChanged NOTIFICATION-TYPE
		OBJECTS { sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates slot error LED changed. "
		::= { dasanEvents 225 }

	
	-- *******.4.1.6296.0.226
	nmsConnectionTypeChanged NOTIFICATION-TYPE
		OBJECTS { sleSnmpConnectionType }
		STATUS current
		DESCRIPTION 
			"This trap indicates NMS connection type changed. "
		::= { dasanEvents 226 }

	
	-- *******.4.1.6296.0.227
	gponOntOSUpgradeStatus NOTIFICATION-TYPE
		OBJECTS { sleGponOntFirmwareOltId, sleGponOntFirmwareOntId, sleGponOntFirmwareOntOsStatus }
		STATUS current
		DESCRIPTION 
			"This trap indicates ONT OS upgrade status changes. "
		::= { dasanEvents 227 }

	
	-- *******.4.1.6296.0.228
	gponOntBLUpgradeStatus NOTIFICATION-TYPE
		OBJECTS { sleGponOntFirmwareOltId, sleGponOntFirmwareOntId, sleGponOntFirmwareOntOsStatus }
		STATUS current
		DESCRIPTION 
			"This trap indicates ONT BL upgrade status changes. "
		::= { dasanEvents 228 }

	
	-- *******.4.1.6296.0.229
	gponOntRxPowerLoss NOTIFICATION-TYPE
		OBJECTS { sleGponOltId }
		STATUS current
		DESCRIPTION 
			"This trap indicates ONT port failed to detect Rx Power. "
		::= { dasanEvents 229 }

	
	-- *******.4.1.6296.0.230
	gponOntRxPowerDetected NOTIFICATION-TYPE
		OBJECTS { sleGponOltId }
		STATUS current
		DESCRIPTION 
			"This trap indicates ONT port detected Rx Power. "
		::= { dasanEvents 230 }

	
	-- *******.4.1.6296.0.231
	gponOnuTemperatureHighOverThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the temperature of ONU is over high threshold. "
		::= { dasanEvents 231 }

	
	-- *******.4.1.6296.0.232
	gponOnuTemperatureHighFallThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the temperature of ONU falls under high threshold. "
		::= { dasanEvents 232 }

	
	-- *******.4.1.6296.0.233
	gponOnuTemperatureLowOverThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the temperature of ONU is over low threshold. "
		::= { dasanEvents 233 }

	
	-- *******.4.1.6296.0.234
	gponOnuTemperatureLowFallThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the temperature of ONU falls under low threshold. "
		::= { dasanEvents 234 }

	
	-- *******.4.1.6296.0.235
	gponOnuCpuOverThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the CPU-load of ONU is over threshold. "
		::= { dasanEvents 235 }

	
	-- *******.4.1.6296.0.236
	gponOnuCpuFallThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the CPU-load of ONU falls under threshold. "
		::= { dasanEvents 236 }

	
	-- *******.4.1.6296.0.237
	gponOnuMemoryOverThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the memory-utilization of ONU is over threshold. "
		::= { dasanEvents 237 }

	
	-- *******.4.1.6296.0.238
	gponOnuMemoryFallThreshold NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates the memory-utilization of ONU falls under threshold. "
		::= { dasanEvents 238 }

	
	-- *******.4.1.6296.0.239
	gponOnuI2cFail NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"This trap indicates that detect ONU's i2c fail."
		::= { dasanEvents 239 }

	-- *******.4.1.6296.0.240
	gponOnuRxOpticPowerLowThreshAlarmOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"RX optic low power alarm is detected"
		::= { dasanEvents 240 }

	
	-- *******.4.1.6296.0.241
	gponOnuRxOpticPowerLowThreshAlarmOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"RX optic low power alarm is cleared"
		::= { dasanEvents 241 }

	
	-- *******.4.1.6296.0.242
	gponOnuRxOpticPowerHighThreshAlarmOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"RX Optic over power alarm is detected"
		::= { dasanEvents 242 }

	
	-- *******.4.1.6296.0.243
	gponOnuRxOpticPowerHighThreshAlarmOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"RX Optic over power alarm is cleared."
		::= { dasanEvents 243 }

	
	-- *******.4.1.6296.0.244
	gponOltLOFIOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LOFI alarm is detected"
		::= { dasanEvents 244 }

	
	-- *******.4.1.6296.0.245
	gponOltLOFIOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LOFI alarm is cleared"
		::= { dasanEvents 245 }

	
	-- *******.4.1.6296.0.246
	gponOltDOWOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT DOW alarm is detected"
		::= { dasanEvents 246 }

	
	-- *******.4.1.6296.0.247
	gponOltDOWOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT DOW alarm is cleared"
		::= { dasanEvents 247 }

	
	-- *******.4.1.6296.0.248
	gponOltSFOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT SF alarm is detected"
		::= { dasanEvents 248 }

	
	-- *******.4.1.6296.0.249
	gponOltSFOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT SF alarm is cleared"
		::= { dasanEvents 249 }

	
	-- *******.4.1.6296.0.250
	gponOltSDOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT SD alarm is detected"
		::= { dasanEvents 250 }

	
	-- *******.4.1.6296.0.251
	gponOltSDOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT SD alarm is cleared"
		::= { dasanEvents 251 }

	
	-- *******.4.1.6296.0.252
	gponOltLCDGIOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LCDGI alarm is detected"
		::= { dasanEvents 252 }

	
	-- *******.4.1.6296.0.253
	gponOltLCDGIOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LCDGI alarm is cleared"
		::= { dasanEvents 253 }

	
	-- *******.4.1.6296.0.254
	gponOltRDOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT RD alarm is detected"
		::= { dasanEvents 254 }

	
	-- *******.4.1.6296.0.255
	gponOltRDOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT RD alarm is cleared"
		::= { dasanEvents 255 }

	
	-- *******.4.1.6296.0.256
	gponOltSUFOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT SUF alarm is detected"
		::= { dasanEvents 256 }

	
	-- *******.4.1.6296.0.257
	gponOltSUFOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT SUF alarm is cleared"
		::= { dasanEvents 257 }

	
	-- *******.4.1.6296.0.258
	gponOltLOAOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LOA alarm is detected"
		::= { dasanEvents 258 }

	
	-- *******.4.1.6296.0.259
	gponOltLOAOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LOA alarm is cleared"
		::= { dasanEvents 259 }

	
	-- *******.4.1.6296.0.260
	gponOltLOAMIOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LOAMI alarm is detected"
		::= { dasanEvents 260 }

	
	-- *******.4.1.6296.0.261
	gponOltLOAMIOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT LOAMI alarm is cleared"
		::= { dasanEvents 261 }

	
	-- *******.4.1.6296.0.262
	gponOltMEMOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT MEM alarm is detected"
		::= { dasanEvents 262 }

	
	-- *******.4.1.6296.0.263
	gponOltMEMOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT MEM alarm is cleared"
		::= { dasanEvents 263 }

	
	-- *******.4.1.6296.0.264
	gponOltPEEOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT PEE alarm is detected"
		::= { dasanEvents 264 }

	
	-- *******.4.1.6296.0.265
	gponOltPEEOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT PEE alarm is cleared"
		::= { dasanEvents 265 }

	
	-- *******.4.1.6296.0.266
	gponOltPSTOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT PST alarm is detected"
		::= { dasanEvents 266 }

	
	-- *******.4.1.6296.0.267
	gponOltPSTOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT PST alarm is cleared"
		::= { dasanEvents 267 }

	
	-- *******.4.1.6296.0.268
	gponOltERROn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT alarm error is detected"
		::= { dasanEvents 268 }

	
	-- *******.4.1.6296.0.269
	gponOltERROff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT alarm error is cleared"
		::= { dasanEvents 269 }

	
	-- *******.4.1.6296.0.270
	gponOltREIOn NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT REI alarm is detected"
		::= { dasanEvents 270 }

	
	-- *******.4.1.6296.0.271
	gponOltREIOff NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId }
		STATUS current
		DESCRIPTION 
			"OLT REI alarm is cleared."
		::= { dasanEvents 271 }


	-- *******.4.1.6296.0.272
	gponAntiSpoofingDupPortOn NOTIFICATION-TYPE
		OBJECTS { sleGponAntiSpoofingOltId,
		    sleGponAntiSpoofingOnuId, sleGponAntiSpoofingVlanId,
		    sleGponAntiSpoofingMac } 
		STATUS current
		DESCRIPTION 
			"Anti spoofing duplication port alarm is detected"
		::= { dasanEvents 272 }

	-- *******.4.1.6296.0.273
	gponAntiSpoofingDupPortOff NOTIFICATION-TYPE
		OBJECTS { sleGponAntiSpoofingOltId,
		    sleGponAntiSpoofingOnuId, sleGponAntiSpoofingVlanId,
		    sleGponAntiSpoofingMac } 
		STATUS current
		DESCRIPTION 
			"Anti spoofing duplication port alarm is cleared"
		::= { dasanEvents 273 }

	-- *******.4.1.6296.0.274
	gponRedundancySwitchOver NOTIFICATION-TYPE
	        OBJECTS {sleGponRedGroupIndex,
		    sleGponRedGroupOltAIndex,
		    sleGponRedGroupOltAStatus,
		    sleGponRedGroupOltBIndex,
		    sleGponRedGroupOltBStatus,
		    sleGponRedLastReason }
		STATUS current
		DESCRIPTION 
			"Redundancy switch over"
		::= { dasanEvents 274 }

	-- *******.4.1.6296.0.275
	gponDuplicatedRangingResponse NOTIFICATION-TYPE
	        OBJECTS {sleGponOltId,
		    sleGponOnuId}
		STATUS current
		DESCRIPTION 
			"Duplicated ranging response detection."
		::= { dasanEvents 275 }
  

	-- *******.4.1.6296.0.276
	gponOnuPortOpticModuleInstall NOTIFICATION-TYPE
	        OBJECTS {sleGponOltId, sleGponOnuId,
		    sleGponOnuPortSlotId, sleGponOnuPortId }
		STATUS current
		DESCRIPTION 
			"ONU port optic module install detection."
		::= { dasanEvents 276 }

	-- *******.4.1.6296.0.277
	gponOnuPortOpticModuleRemove NOTIFICATION-TYPE
	        OBJECTS {sleGponOltId, sleGponOnuId,
		    sleGponOnuPortSlotId, sleGponOnuPortId }
		STATUS current
		DESCRIPTION 
			"ONU port optic module removal detection."
		::= { dasanEvents 277 }

       -- *******.4.1.6296.0.278
        gponOnuOMCCProblemDetected NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId }
                STATUS current
                DESCRIPTION 
                        "ONU OMCC Problem is detected."
                ::= { dasanEvents 278 }

       -- *******.4.1.6296.0.279
        gponOnuOMCCProblemCleared NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId }
                STATUS current
                DESCRIPTION 
                        "ONU OMCC Problem is cleared."
                ::= { dasanEvents 279 }

       -- *******.4.1.6296.0.280
        gponOltFixedTcontAlarmOn NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOltFixedTcontCount}
                STATUS current
                DESCRIPTION 
                        "OLT fixed tcont alarm is detected"
                ::= { dasanEvents 280 }

       -- *******.4.1.6296.0.281
        gponOltFixedTcontAlarmOff NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOltFixedTcontCount}
                STATUS current
                DESCRIPTION 
                        "OLT fixed tcont alarm is cleared"
                ::= { dasanEvents 281 }

       -- *******.4.1.6296.0.282
        gponOltDynamicTcontAlarmOn NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOltDynamicTcontCount}
                STATUS current
                DESCRIPTION 
                        "OLT dynamic tcont alarm is detected."
                ::= { dasanEvents 282 }

	-- *******.4.1.6296.0.283
        gponOltDynamicTcontAlarmOff NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOltDynamicTcontCount}
                STATUS current
                DESCRIPTION 
                        "OLT dynamic tcont alarm is cleared."
                ::= { dasanEvents 283 }

	-- *******.4.1.6296.0.284
        gponOltPortCountAlarmOn NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOltPortCount}
                STATUS current
                DESCRIPTION 
                        "OLT port count alarm is detected."
                ::= { dasanEvents 284 }

	-- *******.4.1.6296.0.285
        gponOltPortCountAlarmOff NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOltPortCount}
                STATUS current
                DESCRIPTION 
                        "OLT port count alarm is cleared."
                ::= { dasanEvents 285 }

	-- *******.4.1.6296.0.286
        gponOltCableDownAlarmOn NOTIFICATION-TYPE
                OBJECTS {sleGponOltId}
                STATUS current
                DESCRIPTION 
                        "GPON Cable down event is detected"
                ::= { dasanEvents 286 }

	-- *******.4.1.6296.0.287
        gponOltCableDownAlarmOff NOTIFICATION-TYPE
                OBJECTS {sleGponOltId}
                STATUS current
                DESCRIPTION 
                        "GPON Cable down event is cleared"
                ::= { dasanEvents 287 }

	-- *******.4.1.6296.0.288
        gponOltDeactiveMonitorAlarmOn NOTIFICATION-TYPE
                OBJECTS {sleGponOltId}
                STATUS current
                DESCRIPTION 
                        "GPON deactive monitor alarm is on"
                ::= { dasanEvents 288 }

	-- *******.4.1.6296.0.289
        gponOltDeactiveMonitorAlarmOff NOTIFICATION-TYPE
                OBJECTS {sleGponOltId}
                STATUS current
                DESCRIPTION 
                        "GPON deactive monitor alarm is off"
                ::= { dasanEvents 289 }

	-- *******.4.1.6296.0.290
        gponOltDFION NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId}
                STATUS current
                DESCRIPTION 
                        "GPON OLT DFI is on"
                ::= { dasanEvents 290 }

	-- *******.4.1.6296.0.291
        gponOltDFIOFF NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId}
                STATUS current
                DESCRIPTION 
                        "GPON OLT DFI is off"
                ::= { dasanEvents 291 }

	-- *******.4.1.6296.0.294
        gponOltTransceiverPortChanged NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOltTransceiverPort}
                STATUS current
                DESCRIPTION 
                        "GPON OLT Transceiver alarm is changed"
                ::= { dasanEvents 294 }

	-- *******.4.1.6296.0.295
        gponOltRxOpticPowerHighThresOver NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId}
                STATUS current
                DESCRIPTION 
                        "GPON OLT power high threshold is over"
                ::= { dasanEvents 295 }

	-- *******.4.1.6296.0.296
        gponOltRxOpticPowerHighThresOverCleared NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId}
                STATUS current
                DESCRIPTION 
                        "GPON OLT power high threshold over is cleared "
                ::= { dasanEvents 296 }

	-- *******.4.1.6296.0.297
        gponOltRxOpticPowerLowThresUnder NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId}
                STATUS current
                DESCRIPTION 
                        "GPON OLT power low threshold is under"
                ::= { dasanEvents 297 }

	-- *******.4.1.6296.0.298
        gponOltRxOpticPowerLowThresUnderCleared NOTIFICATION-TYPE
                OBJECTS {sleGponOltId, sleGponOnuId}
                STATUS current
                DESCRIPTION 
                        "GPON OLT power low threshold under is cleared"
                ::= { dasanEvents 298 }

	-- *******.4.1.6296.0.301
	oamRapsEastPortStatusChanged NOTIFICATION-TYPE
		OBJECTS {sleY1731MegIndex,
		    sleY1731MegRapsEastPortStatus}
		    STATUS             current
		    DESCRIPTION
		    "The east port status changed"
		    ::= { dasanEvents 301 }

	-- *******.4.1.6296.0.302
	oamRapsWestPortStatusChanged NOTIFICATION-TYPE
		OBJECTS {sleY1731MegIndex,
		    sleY1731MegRapWestPortStatus}
		    STATUS             current
		    DESCRIPTION
		    "The west port status changed"
		    ::= { dasanEvents 302 }

	-- *******.4.1.6296.0.303
	oamURNodePortStatusChanged NOTIFICATION-TYPE
		OBJECTS {sleY1731URMegIndex,
		    sleY1731URNodeConfPortNum,
		    sleY1731URNodeConfPortStatus}
		    STATUS             current
		    DESCRIPTION
		    "OAM uplink redundancy node's port status changed"
		    ::= { dasanEvents 303 }

	-- *******.4.1.6296.0.320
	loopDetectBlocked NOTIFICATION-TYPE
		OBJECTS {sleBridgePortIndex}
		STATUS  current
		DESCRIPTION
		"This trap indicate the Port is blocked by loop-detect."
		::= { dasanEvents 320}

	-- *******.4.1.6296.0.321
	loopDetectUnBlockedbyExpireTimer NOTIFICATION-TYPE
		OBJECTS {sleBridgePortIndex}
		STATUS  current
		DESCRIPTION
		"This trap indicate the Port is unblocked by loop-detect's expire timer."
		::= { dasanEvents 321}

	-- *******.4.1.6296.0.322
	loopDetectUnBlockedbyLinkDown NOTIFICATION-TYPE
		OBJECTS {sleBridgePortIndex}
		STATUS  current
		DESCRIPTION
		"This trap indicate the Port is unblocked by loop-detect's link	down."
		::= { dasanEvents 322}

	writeMemoryFailSeveralTimes NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"This trap indicates the writing memory is failed 3 times"
		::= { dasanEvents 341 }

	writeMemoryFailSeveralTimesCleared NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"This trap indicates the writing memory is ok"
		::= { dasanEvents 342 }

--   EM Log
	emLogDefectCPUHighOver NOTIFICATION-TYPE
	    OBJECTS {sleEMLogDefectCPUHigh}
	    STATUS             current
	    DESCRIPTION
		     "EM log about CPU load high over."
	    ::= { dasanEvents 351 }

	emLogDefectCPUHighUnder NOTIFICATION-TYPE
	    OBJECTS {sleEMLogDefectCPUHigh}
	    STATUS             current
	    DESCRIPTION
		     "EM log about CPU load high under."
	    ::= { dasanEvents 352 }


	emLogTemperHighOver NOTIFICATION-TYPE
	    OBJECTS {sleEMLogTemperHigh}
	    STATUS             current
	    DESCRIPTION
		     "EM log about temperature high over."
	    ::= { dasanEvents 353 }

	emLogTemperHighUnder NOTIFICATION-TYPE
	    OBJECTS {sleEMLogTemperHigh}
	    STATUS             current
	    DESCRIPTION
		     "EM log about temperature high under."
	    ::= { dasanEvents 354 }
	    
	emLogTemperLowUnder NOTIFICATION-TYPE
	    OBJECTS {sleEMLogTemperLow}
	    STATUS             current
	    DESCRIPTION
		     "EM log about temperature low under (occured)."
	    ::= { dasanEvents 355 }

	emLogTemperLowOver NOTIFICATION-TYPE
	    OBJECTS {sleEMLogTemperLow}
	    STATUS             current
	    DESCRIPTION
		     "EM log about temperature low under (cleared)."
	    ::= { dasanEvents 356 }


	emLogMemLowUnder NOTIFICATION-TYPE
	    OBJECTS {sleEMLogMemLow}
	    STATUS             current
	    DESCRIPTION
		     "EM log about memory low over (occured)."
	    ::= { dasanEvents 358 }

	emLogMemLowOver NOTIFICATION-TYPE
	    OBJECTS {sleEMLogMemLow}
	    STATUS             current
	    DESCRIPTION
		     "EM log about memory low over (cleared)."
	    ::= { dasanEvents 357 }


	emLogFanFail NOTIFICATION-TYPE
	    OBJECTS {sleEMLogFanFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about fan fail."
	    ::= { dasanEvents 359 }

	emLogFanFailCleared NOTIFICATION-TYPE
	    OBJECTS {sleEMLogFanFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about fan fail cleared."
	    ::= { dasanEvents 360 }


	emLogPhyCRCFail NOTIFICATION-TYPE
	    OBJECTS {sleEMLogPhyCRCFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about PHY CRC fail."
	    ::= { dasanEvents 361 }

	emLogPhyCRCFailCleared NOTIFICATION-TYPE
	    OBJECTS {sleEMLogPhyCRCFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about PHY CRC fail cleared."
	    ::= { dasanEvents 362 }

	emLogPhyDetectFail NOTIFICATION-TYPE
	    OBJECTS {sleEMLogPhyDetectFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about PHY detect fail."
	    ::= { dasanEvents 363 }

	emLogPhyDetectFailCleared NOTIFICATION-TYPE
	    OBJECTS {sleEMLogPhyDetectFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about PHY detect fail cleared."
	    ::= { dasanEvents 364 }


	emLogSFUMateInitFail NOTIFICATION-TYPE
	    OBJECTS {sleEMLogSFUMateInitFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about SFU mate init fail"
	    ::= { dasanEvents 365 }

	emLogSFUMateInitFailCleared NOTIFICATION-TYPE
	    OBJECTS {sleEMLogSFUMateInitFail}
	    STATUS             current
	    DESCRIPTION
		     "EM log about SFU mate init fail cleared"
	    ::= { dasanEvents 366 }

	emLogDaemonHealthBad NOTIFICATION-TYPE
	    OBJECTS {sleEMLogDaemonHealthBad}
	    STATUS             current
	    DESCRIPTION
		     "EM log about daemon health bad"
	    ::= { dasanEvents 367 }

	emLogDaemonHealthBadCleared NOTIFICATION-TYPE
	    OBJECTS {sleEMLogDaemonHealthBad}
	    STATUS             current
	    DESCRIPTION
		     "EM log about daemon health bad cleared"
	    ::= { dasanEvents 368 }


	emLogGPIURavenLinkDown NOTIFICATION-TYPE
	    OBJECTS {sleEMLogGPIURavenLinkDown}
	    STATUS             current
	    DESCRIPTION
		     "EM log about GPIU raven link down"
	    ::= { dasanEvents 369 }

	emLogGPIURavenLinkDownCleared NOTIFICATION-TYPE
	    OBJECTS {sleEMLogGPIURavenLinkDown}
	    STATUS             current
	    DESCRIPTION
		     "EM log about GPIU raven link down cleared"
	    ::= { dasanEvents 370 }

	bootSequenceConfigFile NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Boot Sequence Config File failed"
		::= { dasanEvents 371 }
		
	topologyChanged NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Topolgy changed"
		::= { dasanEvents 372 }

	configLoadFail NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Config load failed"
		::= { dasanEvents 373 }

	emlogFanRPMLow NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"FAN RPM Low detected in EMLOG"
		::= { dasanEvents 375 }

	emlogFanRPMLowCleared NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"FAN RPM Low cleared in EMLOG"
		::= { dasanEvents 376 }


	emlogNAKError NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"NAK Error detected in EMLOG"
		::= { dasanEvents 377 }

	emlogNAKErrorCleared NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"NAK Error cleared in EMLOG"
		::= { dasanEvents 378 }



	gponOnuAniVoltageLowThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Voltage low threshold alarm on"
		::= { dasanEvents 381 }

	gponOnuAniVoltageLowThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Voltage low threshold alarm off"
		::= { dasanEvents 382 }

	 gponOnuAniVoltageHighThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Voltage high threshold alarm on"
		::= { dasanEvents 383 }

	 gponOnuAniVoltageHighThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Voltage high threshold alarm off"
		::= { dasanEvents 384 }

	 gponOnuAniTemperatureLowThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Temperature low threshold alarm on"
		::= { dasanEvents 385 }

	 gponOnuAniTemperatureLowThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Temperature high threshold alarm off"
		::= { dasanEvents 386 }

	 gponOnuAniTemperatureHighThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Temperature high threshold alarm on"
		::= { dasanEvents 387 }

	 gponOnuAniTemperatureHighThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"Temperature high threshold alarm off"
		::= { dasanEvents 388 }


	 gponOnuAniTxBiasLowThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-bias low threshold alarm on"
		::= { dasanEvents 389 }

	 gponOnuAniTxBiasLowThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-bias low threshold alarm off"
		::= { dasanEvents 390 }


	 gponOnuAniTxBiasHighThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-bias high threshold alarm on"
		::= { dasanEvents 391 }

	 gponOnuAniTxBiasHighThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-bias high threshold alarm off"
		::= { dasanEvents 392 }


	 gponOnuAniTxpowerLowThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-power low threshold alarm on"
		::= { dasanEvents 393 }

	 gponOnuAniTxpowerLowThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-power low threshold alarm off"
		::= { dasanEvents 394 }

	 gponOnuAniTxpowerHighThreshAlarmOn NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-power high threshold alarm on"
		::= { dasanEvents 395 }

	 gponOnuAniTxpowerHighThreshAlarmOff NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"TX-power high threshold alarm off"
		::= { dasanEvents 396 }

	slotHealthStatusTimeout NOTIFICATION-TYPE
		OBJECTS { sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			" This trap indicates the slot health status time-out is occurred"
		::= { dasanEvents 397 }

	slotHealthStatusTimeoutCleared NOTIFICATION-TYPE
		OBJECTS { sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			" This trap indicates the slot health status time-out trap is cleared "
		::= { dasanEvents 398 }

	sfpDMIDiagTemperOverHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is over the high threshold.(Alarm) "
		::= { dasanEvents 401 }

	sfpDMIDiagTemperUnderHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is under the high threshold.(Alarm)"
		::= { dasanEvents 402 }

	sfpDMIDiagTemperUnderLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is under the low threshold.(Alarm)"
		::= { dasanEvents 403 }

	sfpDMIDiagTemperOverLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is over the low threshold.(Alarm)"
		::= { dasanEvents 404 }


	sfpDMIDiagTemperOverHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is over the high threshold.(Warn)"
		::= { dasanEvents 405 }

	sfpDMIDiagTemperUnderHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is under the high threshold.(Warn)"
		::= { dasanEvents 406 }

	sfpDMIDiagTemperUnderLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is under the low threshold.(Warn)"
		::= { dasanEvents 407 }

	sfpDMIDiagTemperOverLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITemper }
		STATUS current
		DESCRIPTION 
			" This trap indicates the temperature of sfp is over the low threshold.(Warn)"
		::= { dasanEvents 408 }


	sfpDMIDiagVoltageOverHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is over the high threshold.(Alarm) "
		::= { dasanEvents 409 }

	sfpDMIDiagVoltageUnderHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is under the high threshold.(Alarm)"
		::= { dasanEvents 410 }

	sfpDMIDiagVoltageUnderLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is under the low threshold.(Alarm)"
		::= { dasanEvents 411 }

	sfpDMIDiagVoltageOverLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is over the low threshold.(Alarm)"
		::= { dasanEvents 412 }


	sfpDMIDiagVoltageOverHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is over the high threshold.(Warn)"
		::= { dasanEvents 413 }

	sfpDMIDiagVoltageUnderHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is under the high threshold.(Warn)"
		::= { dasanEvents 414 }

	sfpDMIDiagVoltageUnderLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is under the low threshold.(Warn)"
		::= { dasanEvents 415 }

	sfpDMIDiagVoltageOverLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIVoltage }
		STATUS current
		DESCRIPTION 
			" This trap indicates the voltage of sfp is over the low threshold.(Warn)"
		::= { dasanEvents 416 }

	sfpDMIDiagTxBiasOverHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is over the high threshold.(Alarm) "
		::= { dasanEvents 417 }

	sfpDMIDiagTxBiasUnderHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is under the high threshold.(Alarm)"
		::= { dasanEvents 418 }

	sfpDMIDiagTxBiasUnderLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is under the low threshold.(Alarm)"
		::= { dasanEvents 419 }

	sfpDMIDiagTxBiasOverLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is over the low threshold.(Alarm)"
		::= { dasanEvents 420 }


	sfpDMIDiagTxBiasOverHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is over the high threshold.(Warn)"
		::= { dasanEvents 421 }

	sfpDMIDiagTxBiasUnderHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is under the high threshold.(Warn)"
		::= { dasanEvents 422 }

	sfpDMIDiagTxBiasUnderLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is under the low threshold.(Warn)"
		::= { dasanEvents 423 }

	sfpDMIDiagTxBiasOverLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxBias }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxBias of sfp is over the low threshold.(Warn)"
		::= { dasanEvents 424 }

	sfpDMIDiagTxPowerOverHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is over the high threshold.(Alarm) "
		::= { dasanEvents 425 }

	sfpDMIDiagTxPowerUnderHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is under the high threshold.(Alarm)"
		::= { dasanEvents 426 }

	sfpDMIDiagTxPowerUnderLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is under the low threshold.(Alarm)"
		::= { dasanEvents 427 }

	sfpDMIDiagTxPowerOverLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is over the low threshold.(Alarm)"
		::= { dasanEvents 428 }


	sfpDMIDiagTxPowerOverHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is over the high threshold.(Warn)"
		::= { dasanEvents 429 }

	sfpDMIDiagTxPowerUnderHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is under the high threshold.(Warn)"
		::= { dasanEvents 430 }

	sfpDMIDiagTxPowerUnderLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is under the low threshold.(Warn)"
		::= { dasanEvents 431 }

	sfpDMIDiagTxPowerOverLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMITxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the TxPower of sfp is over the low threshold.(Warn)"
		::= { dasanEvents 432 }

	sfpDMIDiagRxPowerOverHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is over the high threshold.(Alarm) "
		::= { dasanEvents 433 }

	sfpDMIDiagRxPowerUnderHighAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is under the high threshold.(Alarm)"
		::= { dasanEvents 434 }

	sfpDMIDiagRxPowerUnderLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is under the low threshold.(Alarm)"
		::= { dasanEvents 435 }

	sfpDMIDiagRxPowerOverLowAlarm NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is over the low threshold.(Alarm)"
		::= { dasanEvents 436 }


	sfpDMIDiagRxPowerOverHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is over the high threshold.(Warn)"
		::= { dasanEvents 437 }

	sfpDMIDiagRxPowerUnderHighWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is under the high threshold.(Warn)"
		::= { dasanEvents 438 }

	sfpDMIDiagRxPowerUnderLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is under the low threshold.(Warn)"
		::= { dasanEvents 439 }

	sfpDMIDiagRxPowerOverLowWarn NOTIFICATION-TYPE
		OBJECTS { sleEthernetPortDMIIndex, sleEthernetPortDMIRxPower }
		STATUS current
		DESCRIPTION 
			" This trap indicates the RxPower of sfp is over the low threshold.(Warn)"
		::= { dasanEvents 440 }

	 gponOnuOnuServiceOpModeAlarm  NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"GPON ONU service operational mode alarm."
		::= { dasanEvents 441 }

	 gponOnuOnuServiceOpModeAlarmOff  NOTIFICATION-TYPE
		STATUS current
		DESCRIPTION 
			"GPON ONU service operational mode cleared alarm."
		::= { dasanEvents 442 }

	systemTemperPowerCutOff NOTIFICATION-TYPE
		OBJECTS {  }
		STATUS current
		DESCRIPTION 
			"This trap indicates the system temperature power cut off"
		::= { dasanEvents 443 }

	systemTemperPowerCutoffCleared NOTIFICATION-TYPE
		OBJECTS {  }
		STATUS current
		DESCRIPTION 
			"This trap indicates the system temperature power cut off trap is cleared"
		::= { dasanEvents 444 }

	slotTemperPowerCutOff NOTIFICATION-TYPE
		OBJECTS { sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the slot temperature power cut off"
		::= { dasanEvents 445 }

	slotTemperPowerCutOffCleared NOTIFICATION-TYPE
		OBJECTS { sleSlotSystemIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the slot temperature power cut off is cleared"
		::= { dasanEvents 446 }


	gponOnuAuthErrorReasonUpdated NOTIFICATION-TYPE
		OBJECTS { sleGponOltId, sleGponOnuId, sleGponOnuAuthOnuErrorReason }
		STATUS current
		DESCRIPTION 
			"This trap indicates GPON ONU authentication error reason is updated"
		::= { dasanEvents 461 }

	ponRegErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon registration is not ok"
		::= { dasanEvents 501 }

	ponBadEncryptionKey NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon encryption key is bad"
		::= { dasanEvents 502 }

	ponLloidMismatch NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon LLOID is mismatched"
		::= { dasanEvents 503 }

	ponTooManyOnu NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon has too many of ONU"
		::= { dasanEvents 504 }

	ponDyingGasp NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon dying GASP"
		::= { dasanEvents 505 }

	ponRegister NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon is registered"
		::= { dasanEvents 506 }

	ponDeregister NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon is deregistered"
		::= { dasanEvents 507 }

	ponLastOnuDeregister NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the last ONU is deregistered"
		::= { dasanEvents 508 }

	ponCableDown NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon cable is down"
		::= { dasanEvents 509 }

	ponCableDownRecover NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon cable down is recovered"
		::= { dasanEvents 510 }

	ponFirstRegistration NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the first pon registration"
		::= { dasanEvents 511 }

	ponOntLinkUp NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ont link up"
		::= { dasanEvents 512 }

	ponOltOnuDown NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Onu down"
		::= { dasanEvents 513 }

	ponHealthCheckErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon health check error"
		::= { dasanEvents 514 }


	ponRegisterModeChange NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon register mode is changed"
		::= { dasanEvents 515 }

	ponOpticSignalErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon optic signal error"
		::= { dasanEvents 516 }

	ponOpticSignalErrRecover NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon optic signal error is recovered"
		::= { dasanEvents 517 }

	ponOverVolSigErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon over volatage signal error"
		::= { dasanEvents 518 }

	ponOverVolSigErrRecover NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon over volatage signal error is recovered"
		::= { dasanEvents 519 }


	ponOnuRssiInfoErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Onu RSSI information error"
		::= { dasanEvents 520 }

	ponOnuRssiInfoErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Onu RSSI information error is cleard"
		::= { dasanEvents 521 }

	ponOnuCrcUpAlarmErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ONU CRC up alarm error"
		::= { dasanEvents 522 }

	ponOnuCrcUpAlarmErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ONU CRC up alarm error is cleared"
		::= { dasanEvents 523 }

	ponOnuCrcDownAlarmErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ONU CRC down alarm error"
		::= { dasanEvents 524 }

	ponOnuCrcDownAlarmErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ONU CRC down alarm error is cleared"
		::= { dasanEvents 525 }

	ponOnuTooFrequentlyRegErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ONU is registered too frequently."
		::= { dasanEvents 526 }

	ponOnuTooFrequentlyRegErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the ONU registration frequently error is cleared."
		::= { dasanEvents 527 }


	ponOnuOpticPowerAlarmErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon ONU optic power alarm error"
		::= { dasanEvents 528 }

	ponOnuOpticPowerAlarmErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon ONU optic power alarm error is cleared"
		::= { dasanEvents 529 }

	ponOnuDcVoltageAlarmErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon ONU DC voltage alarm error"
		::= { dasanEvents 530 }

	ponOnuDcVoltageAlarmErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon ONU DC voltage alarm error is cleared"
		::= { dasanEvents 531 }

	ponOnuLoopingAlarmErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon ONU looping alarm error"
		::= { dasanEvents 532 }

	ponOnuLoopingAlarmErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the pon ONU looping alarm error is cleared"
		::= { dasanEvents 533 }

	ponDetectRogueOnu NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Rogue ONU detection."
		::= { dasanEvents 534 }

	ponDetectRogueOnuCleared NOTIFICATION-TYPE
		OBJECTS { sysDescr, dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Rogue ONU detection is cleared."
		::= { dasanEvents 535 }


	ponDetectRogueOnuPowerOff NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Rogue ONU power off is detected."
		::= { dasanEvents 536 }

	ponDetectRogueOnuDeregisterSuccess NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Rogue ONU deregister success is detected"
		::= { dasanEvents 537 }

	ponDectectRogueOnuFoundFailed NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Rogue ONU found fail is detected."
		::= { dasanEvents 538 }

	ponDectectRogueOnuTurn NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Rogue ONU turn is detected."
		::= { dasanEvents 539 }

	ponOltStandbyRxpowerOverHigh NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Olt standby RX power is over high threshold"
		::= { dasanEvents 540 }

	ponOltStandbyRxpowerUnderLow NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Olt standby RX power is under low threshold"
		::= { dasanEvents 541}

	ponOltRedundancyPortDown NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Olt redundancy port is down"
		::= { dasanEvents 542}

	ponOltRedundancyRxpower NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Olt redundancy RXpower"
		::= { dasanEvents 543}

	ponOnuBcastStormLlidUpErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the broadcast storm llid up error"
		::= { dasanEvents 544}

	ponOnuBcastStormLlidUpErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the broadcast storm llid up error is cleared"
		::= { dasanEvents 545}

	ponOnuBcastStormLlidDownErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the broadcast storm llid down error"
		::= { dasanEvents 546}

	ponOnuBcastStormLlidDownErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the broadcast storm llid down error is cleared"
		::= { dasanEvents 547}

	ponOnuBcastStormUniUpErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the broadcast storm UNI up error"
		::= { dasanEvents 548}

	ponOnuBcastStormUniUpErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the broadcast storm Uni up error is cleared"
		::= { dasanEvents 549}

	ponOnuReset NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates Pon ONU reset"
		::= { dasanEvents 550}


	ponOnuLifLcErrAlarmErr NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates Pon ONU LIF Error"
		::= { dasanEvents 551}

	ponOnuLifLcErrAlarmErrCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates Pon ONU LIF Error is cleared"
		::= { dasanEvents 552}

	ponOltResetPonMac NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates Pon OLT MAC reset"
		::= { dasanEvents 561}

	ponOltRedSwitchover NOTIFICATION-TYPE
		OBJECTS { sleEponOltRedundancyGroupID, sleEponOltRedundancyMaster, sleEponOltRedundancyMasteStatus, sleEponOltRedundancySlave, sleEponOltRedundancySlaveStatus, sleEponOltRedundancyLastReason }
		STATUS current
		DESCRIPTION 
			"This trap indicates the OLT redundancy switchover"
		::= { dasanEvents 581}


	ponOltRedGroupCreated NOTIFICATION-TYPE
		OBJECTS { sleEponOltRedundancyGroupID, sleEponOltRedundancyMaster, sleEponOltRedundancySlave }
		STATUS current
		DESCRIPTION 
		"This trap indicates the OLT redundancy group is created"
		::= { dasanEvents 582}

	ponOltRedGroupDeleted NOTIFICATION-TYPE
		OBJECTS { sleEponOltRedundancyGroupID, sleEponOltRedundancyMaster, sleEponOltRedundancySlave }
		STATUS current
		DESCRIPTION 
		"This trap indicates the OLT redundancy group is deleted"
		::= { dasanEvents 583}


	ponOltRedTriggerChanged NOTIFICATION-TYPE
		OBJECTS { sleEponOltRedundancyGroupID, sleEponOltRedundancyMaster, sleEponOltRedundancySlave, sleEponOltRedundancyTriggerLos, sleEponOltRedundancyTriggerManual, sleEponOltRedundancyTriggerSlot, sleEponOltRedundancyTriggerEponMac }
		STATUS current
		DESCRIPTION 
		"This trap indicates the OLT redundancy trigger is changed"
		::= { dasanEvents 584}


	 ponOltRedDbResyncChanged NOTIFICATION-TYPE
		OBJECTS { sleEponOltRedundancyGroupID, sleEponOltRedundancyMaster, sleEponOltRedundancyMasteStatus, sleEponOltRedundancySlave, sleEponOltRedundancySlaveStatus }
		STATUS current
		DESCRIPTION 
		"This trap indicates the OLT redundancy DB resync is changed"
		::= { dasanEvents 585}


	 ponOltRedDbSyncChecked NOTIFICATION-TYPE
		OBJECTS { sleEponOltRedundancyGroupID, sleEponOltRedundancyMaster, sleEponOltRedundancyMasteStatus, sleEponOltRedundancySlave, sleEponOltRedundancySlaveStatus }
		STATUS current
		DESCRIPTION 
		"This trap indicates the OLT redundancy DB sync is checked"
		::= { dasanEvents 586}


	ponOnuRssiWarn NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RSSI warning"
		::= { dasanEvents 601}

	ponOnuRssiWarnCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RSSI warning is cleared"
		::= { dasanEvents 602}

	ponOnuRssiAlarm NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RSSI alarm"
		::= { dasanEvents 603}

	ponOnuRssiAlarmCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RSSI alarm is cleared"
		::= { dasanEvents 604}

	ponOnuRxpowerWarn NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power warning"
		::= { dasanEvents 605}

	ponOnuRxpowerWarnCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power warning is cleared"
		::= { dasanEvents 606}

	ponOnuRxpowerAlarm NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power alarm"
		::= { dasanEvents 607}

	ponOnuRxpowerAlarmCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power alarm is cleared"
		::= { dasanEvents 608}

	ponMacTableOverFlow NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the Mac table is over Max entry."
		::= { dasanEvents 609 }

	ponMacTableOverFlowCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the Mac over max entry is cleared."
		::= { dasanEvents 610 }

	fiberLengthChangedWarningAlarm NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the fiber length is changed"
		::= { dasanEvents 611}

	fiberLengthChangedWarningAlarmCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the fiber length trap is cleared"
		::= { dasanEvents 612}
		
	ponOnuFrameReplicationAlarm NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the frame is replicated."
		::= { dasanEvents 613}
		
	ponOnuFrameReplicationAlarmCleared NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelNodeIndex, dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, dsEPonSystemChannelMacAddress }
		STATUS current
		DESCRIPTION 
			"This trap indicates the frame is replicated."
		::= { dasanEvents 614}  
		
	ponOnuRxpowerWarnOverHigh NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power high over warning"
		::= { dasanEvents 615}

    ponOnuRxpowerWarnOverHighCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power high over warning is cleared"
		::= { dasanEvents 616}

	ponOnuRxpowerWarnUnderLow NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power low under warning"
		::= { dasanEvents 617}

    ponOnuRxpowerWarnUnderLowCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power low under warning is cleared"
		::= { dasanEvents 618}
                                 
	ponOnuRxpowerAlarmOverHigh NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power high over alarm"
		::= { dasanEvents 619}

    ponOnuRxpowerAlarmOverHighCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power high over alarm is cleared"
		::= { dasanEvents 620}
                                 
	ponOnuRxpowerAlarmUnderLow NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power low under alarm"
		::= { dasanEvents 621}

    ponOnuRxpowerAlarmUnderLowCleared NOTIFICATION-TYPE
		OBJECTS { sleEponOltIndex, sleEponOnuIndex }
		STATUS current
		DESCRIPTION 
		"This trap indicates the ONU RX-power low under alarm is cleared"
		::= { dasanEvents 622}

	ponOnuUniLinkDown NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, sleEponOnuPortIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Onu Uni link is down."
		::= { dasanEvents 623}        
		
	ponOnuUniLinkUp NOTIFICATION-TYPE
		OBJECTS { dsEPonSystemChannelPortIndex, dsEPonSystemChannelId, sleEponOnuPortIndex }
		STATUS current
		DESCRIPTION 
			"This trap indicates the Onu Uni link is up."
		::= { dasanEvents 624} 		                           
END

--
-- dasan-notification.mib
--
