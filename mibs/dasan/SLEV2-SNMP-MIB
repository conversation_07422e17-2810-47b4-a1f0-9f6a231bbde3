--
-- slev2-snmp-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, August 04, 2015 at 13:35:14
--

	SLEV2-SNMP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleV2Mgmt			
				FROM DASAN-SMI			
			InetAddressType, Inet<PERSON><PERSON>ress, InetAddressPrefixLength			
				FROM INET-ADDRESS-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Integer32, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.102.8
		sleV2Snmp MODULE-IDENTITY 
			LAST-UPDATED "201501221548Z"		-- January 22, 2015 at 15:48 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleV2Mgmt 8 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.102.8.1
		sleV2SnmpBase OBJECT IDENTIFIER ::= { sleV2Snmp 1 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpBaseInfo OBJECT IDENTIFIER ::= { sleV2SnmpBase 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpBaseAgentAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpBaseAgentAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpBaseContacts OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpBaseEngineIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				hex(1),
				text(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpBaseEngineIdValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpBaseLocation OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpBaseLogStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				volatile(0),
				nonvolatile(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpBaseTrapLogStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				volatile(0),
				nonvolatile(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpBaseTrapLogThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpBaseTrapMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				event(0),
				alarmReport(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseInfo 10 }

		
		-- *******.4.1.6296.*********.11
		sleV2SnmpBaseVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The vrf name for SNMP"
			::= { sleV2SnmpBaseInfo 11 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpBaseControl OBJECT IDENTIFIER ::= { sleV2SnmpBase 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				resetSnmp(1),
				clearSnmpAlarmHistory(2),
				clearSnmpAlarmReport(3),
				clearSnmpLog(4),
				clearSnmpTrapLog(5),
				setSnmpAgentAddr(6),
				unsetSnmpAgentAddr(7),
				setSnmpContacts(8),
				unsetSnmpContacts(9),
				setSnmpLocation(10),
				unsetSnmpLocation(11),
				setSnmpTrapLogStatus(12),
				setSnmpTrapMode(13),
				setSnmpEngineId(14),
				unsetSnmpEngineId(15),
				setSnmpLogStatus(16),
				setSnmpVrf(17),
				unsetSnmpVrf(18)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpBaseControlAgentAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpBaseControlAgentAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpBaseControlContacts OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpBaseControlEngineIdType OBJECT-TYPE
			SYNTAX INTEGER
				{
				hex(1),
				text(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpBaseControlEngineIdValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 10 }

		
		-- *******.4.1.6296.*********.11
		sleV2SnmpBaseControlLocation OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 11 }

		
		-- *******.4.1.6296.*********.12
		sleV2SnmpBaseControlLogStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				volatile(0),
				nonvolatile(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 12 }

		
		-- *******.4.1.6296.*********.13
		sleV2SnmpBaseControlTrapLogStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				volatile(0),
				nonvolatile(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 13 }

		
		-- *******.4.1.6296.*********.14
		sleV2SnmpBaseControlTrapLogThreshold OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 14 }

		
		-- *******.4.1.6296.*********.15
		sleV2SnmpBaseControlTrapMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				event(0),
				alarmReport(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpBaseControl 15 }

		
		-- *******.4.1.6296.*********.16
		sleV2SnmpBaseControlVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vrf name for SNMP"
			::= { sleV2SnmpBaseControl 16 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpBaseNotification OBJECT IDENTIFIER ::= { sleV2SnmpBase 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpCleared NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpAlarmHistoryCleared NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpAlarmReportCleared NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpLogCleared NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpTrapLogCleared NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpAgentAddrCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseAgentAddrType, sleV2SnmpBaseAgentAddrValue
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpAgentAddrDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpContactsCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseContacts }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpContactsDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpLocationCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseLocation }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 10 }

		
		-- *******.4.1.6296.*********.11
		sleV2SnmpLocationDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 11 }

		
		-- *******.4.1.6296.*********.12
		sleV2SnmpTrapLogStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseTrapLogStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 12 }

		
		-- *******.4.1.6296.*********.13
		sleV2SnmpTrapModeChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseTrapMode }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 13 }

		
		-- *******.4.1.6296.*********.14
		sleV2SnmpEngineIdCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseEngineIdType, sleV2SnmpBaseEngineIdValue
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 14 }

		
		-- *******.4.1.6296.*********.15
		sleV2SnmpEngineIdDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 15 }

		
		-- *******.4.1.6296.*********.16
		sleV2SnmpLogStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseLogStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpBaseNotification 16 }

		
		-- *******.4.1.6296.*********.17
		sleV2SnmpVrfSet NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, sleV2SnmpBaseVrfName }
			STATUS current
			DESCRIPTION 
				"setSnmpVrf"
			::= { sleV2SnmpBaseNotification 17 }

		
		-- *******.4.1.6296.*********.18
		sleV2SnmpVrfUnset NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"unsetSnmpVrf"
			::= { sleV2SnmpBaseNotification 18 }

		
		-- *******.4.1.6296.102.8.2
		sleV2SnmpAccess OBJECT IDENTIFIER ::= { sleV2Snmp 2 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpAccessTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpAccessEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccess 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpAccessEntry OBJECT-TYPE
			SYNTAX SleV2SnmpAccessEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpAccessGroupName }
			::= { sleV2SnmpAccessTable 1 }

		
		SleV2SnmpAccessEntry ::=
			SEQUENCE { 
				sleV2SnmpAccessGroupName
					OCTET STRING,
				sleV2SnmpAccessSecurityModel
					INTEGER,
				sleV2SnmpAccessSecurityLevel
					INTEGER,
				sleV2SnmpAccessReadViewName
					OCTET STRING,
				sleV2SnmpAccessWriteViewName
					OCTET STRING,
				sleV2SnmpAccessNotifyViewName
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpAccessGroupName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpAccessSecurityModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				v1(1),
				v2c(2),
				v3(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpAccessSecurityLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				noauth(1),
				auth(2),
				priv(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleV2SnmpAccessReadViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleV2SnmpAccessWriteViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessEntry 5 }

		
		-- *******.4.1.6296.*********.1.6
		sleV2SnmpAccessNotifyViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessEntry 6 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpAccessControl OBJECT IDENTIFIER ::= { sleV2SnmpAccess 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpAccessControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createAccess(1),
				deleteAccess(2),
				setAccessProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpAccessControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpAccessControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpAccessControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpAccessControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpAccessControlGroupName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpAccessControlSecurityModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				v1(1),
				v2c(2),
				v3(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpAccessControlSecurityLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				noauth(1),
				auth(2),
				priv(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpAccessControlReadViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpAccessControlWriteViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 10 }

		
		-- *******.4.1.6296.*********.11
		sleV2SnmpAccessControlNotifyViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpAccessControl 11 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpAccessNotification OBJECT IDENTIFIER ::= { sleV2SnmpAccess 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpAccessCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpAccessControlRequest, sleV2SnmpAccessControlTimeStamp, sleV2SnmpAccessControlReqResult, sleV2SnmpAccessGroupName, sleV2SnmpAccessSecurityModel, 
				sleV2SnmpAccessSecurityLevel, sleV2SnmpAccessReadViewName, sleV2SnmpAccessWriteViewName, sleV2SnmpAccessNotifyViewName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpAccessNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpAccessDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpAccessControlRequest, sleV2SnmpAccessControlTimeStamp, sleV2SnmpAccessControlReqResult, sleV2SnmpAccessControlGroupName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpAccessNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpAccessProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpAccessControlRequest, sleV2SnmpAccessControlTimeStamp, sleV2SnmpAccessControlReqResult, sleV2SnmpAccessGroupName, sleV2SnmpAccessSecurityModel, 
				sleV2SnmpAccessSecurityLevel, sleV2SnmpAccessReadViewName, sleV2SnmpAccessWriteViewName, sleV2SnmpAccessNotifyViewName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpAccessNotification 3 }

		
		-- *******.4.1.6296.102.8.3
		sleV2SnmpCom2sec OBJECT IDENTIFIER ::= { sleV2Snmp 3 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpCom2secTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpCom2secEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2sec 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpCom2secEntry OBJECT-TYPE
			SYNTAX SleV2SnmpCom2secEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpCom2secName, sleV2SnmpCom2secAddrType }
			::= { sleV2SnmpCom2secTable 1 }

		
		SleV2SnmpCom2secEntry ::=
			SEQUENCE { 
				sleV2SnmpCom2secName
					OCTET STRING,
				sleV2SnmpCom2secAddrType
					InetAddressType,
				sleV2SnmpCom2secAddrValue
					InetAddress,
				sleV2SnmpCom2secPrefixLen
					InetAddressPrefixLength,
				sleV2SnmpCom2secCommunity
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpCom2secName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpCom2secAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpCom2secAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleV2SnmpCom2secPrefixLen OBJECT-TYPE
			SYNTAX InetAddressPrefixLength
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleV2SnmpCom2secCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secEntry 5 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpCom2secControl OBJECT IDENTIFIER ::= { sleV2SnmpCom2sec 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpCom2secControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createCom2sec(1),
				deleteCom2sec(2),
				changeCom2sec(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpCom2secControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpCom2secControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpCom2secControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpCom2secControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpCom2secControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpCom2secControlAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpCom2secControlAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpCom2secControlPrefixLen OBJECT-TYPE
			SYNTAX InetAddressPrefixLength
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpCom2secControlCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCom2secControl 10 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpCom2secNotification OBJECT IDENTIFIER ::= { sleV2SnmpCom2sec 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpCom2secCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpCom2secControlRequest, sleV2SnmpCom2secControlTimeStamp, sleV2SnmpCom2secControlReqResult, sleV2SnmpCom2secName, sleV2SnmpCom2secAddrType, 
				sleV2SnmpCom2secAddrValue, sleV2SnmpCom2secPrefixLen, sleV2SnmpCom2secCommunity }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpCom2secNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpCom2secDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpCom2secControlRequest, sleV2SnmpCom2secControlTimeStamp, sleV2SnmpCom2secControlReqResult, sleV2SnmpCom2secControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpCom2secNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpCom2secChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpCom2secControlRequest, sleV2SnmpCom2secControlTimeStamp, sleV2SnmpCom2secControlReqResult, sleV2SnmpCom2secName, sleV2SnmpCom2secAddrType, 
				sleV2SnmpCom2secAddrValue, sleV2SnmpCom2secPrefixLen, sleV2SnmpCom2secCommunity }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpCom2secNotification 3 }

		
		-- *******.4.1.6296.102.8.4
		sleV2SnmpCommunity OBJECT IDENTIFIER ::= { sleV2Snmp 4 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpCommunityTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpCommunityEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunity 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpCommunityEntry OBJECT-TYPE
			SYNTAX SleV2SnmpCommunityEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpCommunityValue }
			::= { sleV2SnmpCommunityTable 1 }

		
		SleV2SnmpCommunityEntry ::=
			SEQUENCE { 
				sleV2SnmpCommunityValue
					OCTET STRING,
				sleV2SnmpCommunityType
					INTEGER,
				sleV2SnmpCommunityAddrType
					InetAddressType,
				sleV2SnmpCommunityAddrValue
					InetAddress,
				sleV2SnmpCommunityOID
					OBJECT IDENTIFIER
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpCommunityValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpCommunityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				readonly(1),
				readwrite(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpCommunityAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleV2SnmpCommunityAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleV2SnmpCommunityOID OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityEntry 5 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpCommunityControl OBJECT IDENTIFIER ::= { sleV2SnmpCommunity 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpCommunityControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createCommunity(1),
				deleteCommunity(2),
				changeCommunity(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpCommunityControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpCommunityControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpCommunityControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpCommunityControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpCommunityControlValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpCommunityControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				readonly(1),
				readwrite(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpCommunityControlAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpCommunityControlAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpCommunityControlOID OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpCommunityControl 10 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpCommunityNotification OBJECT IDENTIFIER ::= { sleV2SnmpCommunity 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpCommunityCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpCommunityControlRequest, sleV2SnmpCommunityControlTimeStamp, sleV2SnmpCommunityControlReqResult, sleV2SnmpCommunityValue, sleV2SnmpCommunityType, 
				sleV2SnmpCommunityAddrType, sleV2SnmpCommunityAddrValue, sleV2SnmpCommunityOID }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpCommunityNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpCommunityDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpCommunityControlRequest, sleV2SnmpCommunityControlTimeStamp, sleV2SnmpCommunityControlReqResult, sleV2SnmpCommunityControlValue }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpCommunityNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpCommunityChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpCommunityControlRequest, sleV2SnmpCommunityControlTimeStamp, sleV2SnmpCommunityControlReqResult, sleV2SnmpCommunityValue, sleV2SnmpCommunityType, 
				sleV2SnmpCommunityAddrType, sleV2SnmpCommunityAddrValue, sleV2SnmpCommunityOID }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpCommunityNotification 3 }

		
		-- *******.4.1.6296.102.8.5
		sleV2SnmpGroup OBJECT IDENTIFIER ::= { sleV2Snmp 5 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpGroupTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroup 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpGroupEntry OBJECT-TYPE
			SYNTAX SleV2SnmpGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpGroupName, sleV2SnmpGroupSecModel, sleV2SnmpGroupSecName }
			::= { sleV2SnmpGroupTable 1 }

		
		SleV2SnmpGroupEntry ::=
			SEQUENCE { 
				sleV2SnmpGroupName
					OCTET STRING,
				sleV2SnmpGroupSecModel
					INTEGER,
				sleV2SnmpGroupSecName
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpGroupName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpGroupSecModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				v1(1),
				v2c(2),
				v3(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpGroupSecName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupEntry 3 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpGroupControl OBJECT IDENTIFIER ::= { sleV2SnmpGroup 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpGroupControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createGroup(1),
				deleteGroup(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpGroupControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpGroupControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpGroupControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpGroupControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpGroupControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpGroupControlSecModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				v1(1),
				v2c(2),
				v3(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpGroupControlSecName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpGroupControl 8 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpGroupNotification OBJECT IDENTIFIER ::= { sleV2SnmpGroup 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpGroupCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpGroupControlRequest, sleV2SnmpGroupControlTimeStamp, sleV2SnmpGroupControlReqResult, sleV2SnmpGroupName, sleV2SnmpGroupSecModel, 
				sleV2SnmpGroupSecName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpGroupNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpGroupDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpGroupControlRequest, sleV2SnmpGroupControlTimeStamp, sleV2SnmpGroupControlReqResult, sleV2SnmpGroupControlName, sleV2SnmpGroupControlSecModel, 
				sleV2SnmpGroupControlSecName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpGroupNotification 2 }

		
		-- *******.4.1.6296.102.8.6
		sleV2SnmpNotify OBJECT IDENTIFIER ::= { sleV2Snmp 6 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpNotifyTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpNotifyEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotify 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpNotifyEntry OBJECT-TYPE
			SYNTAX SleV2SnmpNotifyEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpNotifyName }
			::= { sleV2SnmpNotifyTable 1 }

		
		SleV2SnmpNotifyEntry ::=
			SEQUENCE { 
				sleV2SnmpNotifyName
					OCTET STRING,
				sleV2SnmpNotifyTag
					OCTET STRING,
				sleV2SnmpNotifyType
					INTEGER
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpNotifyName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpNotifyTag OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpNotifyType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				inform(1),
				trap(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyEntry 3 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpNotifyControl OBJECT IDENTIFIER ::= { sleV2SnmpNotify 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpNotifyControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createNotify(1),
				deleteNotify(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpNotifyControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpNotifyControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpNotifyControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpNotifyControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpNotifyControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpNotifyControlTag OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpNotifyControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				inform(1),
				trap(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpNotifyControl 8 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpNotifyNotification OBJECT IDENTIFIER ::= { sleV2SnmpNotify 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpNotifyCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpNotifyControlRequest, sleV2SnmpNotifyControlTimeStamp, sleV2SnmpNotifyControlReqResult, sleV2SnmpNotifyName, sleV2SnmpNotifyTag, 
				sleV2SnmpNotifyType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpNotifyNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpNotifyDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpNotifyControlRequest, sleV2SnmpNotifyControlTimeStamp, sleV2SnmpNotifyControlReqResult, sleV2SnmpNotifyControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpNotifyNotification 2 }

		
		-- *******.4.1.6296.102.8.7
		sleV2SnmpTargetAddr OBJECT IDENTIFIER ::= { sleV2Snmp 7 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTargetAddrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpTargetAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddr 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTargetAddrEntry OBJECT-TYPE
			SYNTAX SleV2SnmpTargetAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpTargetAddrName }
			::= { sleV2SnmpTargetAddrTable 1 }

		
		SleV2SnmpTargetAddrEntry ::=
			SEQUENCE { 
				sleV2SnmpTargetAddrName
					OCTET STRING,
				sleV2SnmpTargetAddrParams
					OCTET STRING,
				sleV2SnmpTargetAddrHostType
					InetAddressType,
				sleV2SnmpTargetAddrHostAddr
					InetAddress,
				sleV2SnmpTargetAddrPort
					INTEGER,
				sleV2SnmpTargetAddrTimeout
					INTEGER,
				sleV2SnmpTargetAddrRetryCnt
					INTEGER,
				sleV2SnmpTargetAddrTagList
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpTargetAddrName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpTargetAddrParams OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpTargetAddrHostType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleV2SnmpTargetAddrHostAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleV2SnmpTargetAddrPort OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 5 }

		
		-- *******.4.1.6296.*********.1.6
		sleV2SnmpTargetAddrTimeout OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 6 }

		
		-- *******.4.1.6296.*********.1.7
		sleV2SnmpTargetAddrRetryCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 7 }

		
		-- *******.4.1.6296.*********.1.8
		sleV2SnmpTargetAddrTagList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrEntry 8 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTargetAddrControl OBJECT IDENTIFIER ::= { sleV2SnmpTargetAddr 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTargetAddrControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createTargetAddr(1),
				deleteTargetAddr(2),
				setTartgetAddrProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpTargetAddrControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpTargetAddrControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpTargetAddrControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpTargetAddrControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpTargetAddrControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpTargetAddrControlParams OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpTargetAddrControlHostType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpTargetAddrControlHostAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpTargetAddrControlPort OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 10 }

		
		-- *******.4.1.6296.*********.11
		sleV2SnmpTargetAddrControlTimeout OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 11 }

		
		-- *******.4.1.6296.*********.12
		sleV2SnmpTargetAddrControlRetryCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 12 }

		
		-- *******.4.1.6296.*********.13
		sleV2SnmpTargetAddrControlTagList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetAddrControl 13 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTargetAddrNotification OBJECT IDENTIFIER ::= { sleV2SnmpTargetAddr 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTargetAddrCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTargetAddrControlRequest, sleV2SnmpTargetAddrControlTimeStamp, sleV2SnmpTargetAddrControlReqResult, sleV2SnmpTargetAddrName, sleV2SnmpTargetAddrParams, 
				sleV2SnmpTargetAddrHostType, sleV2SnmpTargetAddrHostAddr, sleV2SnmpTargetAddrPort, sleV2SnmpTargetAddrTimeout, sleV2SnmpTargetAddrRetryCnt, 
				sleV2SnmpTargetAddrTagList }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTargetAddrNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpTargetAddrDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTargetAddrControlRequest, sleV2SnmpTargetAddrControlTimeStamp, sleV2SnmpTargetAddrControlReqResult, sleV2SnmpTargetAddrControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTargetAddrNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpTargetAddrChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTargetAddrControlRequest, sleV2SnmpTargetAddrControlTimeStamp, sleV2SnmpTargetAddrControlReqResult, sleV2SnmpTargetAddrName, sleV2SnmpTargetAddrParams, 
				sleV2SnmpTargetAddrHostType, sleV2SnmpTargetAddrHostAddr, sleV2SnmpTargetAddrPort, sleV2SnmpTargetAddrTimeout, sleV2SnmpTargetAddrRetryCnt, 
				sleV2SnmpTargetAddrTagList }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTargetAddrNotification 3 }

		
		-- *******.4.1.6296.102.8.8
		sleV2SnmpTargetParam OBJECT IDENTIFIER ::= { sleV2Snmp 8 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTargetParamTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpTargetParamEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParam 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTargetParamEntry OBJECT-TYPE
			SYNTAX SleV2SnmpTargetParamEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpTargetParamName }
			::= { sleV2SnmpTargetParamTable 1 }

		
		SleV2SnmpTargetParamEntry ::=
			SEQUENCE { 
				sleV2SnmpTargetParamName
					OCTET STRING,
				sleV2SnmpTargetParamSecModel
					INTEGER,
				sleV2SnmpTargetParamSecName
					OCTET STRING,
				sleV2SnmpTargetParamSecLevel
					INTEGER
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpTargetParamName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpTargetParamSecModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				v1(1),
				v2c(2),
				v3(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpTargetParamSecName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleV2SnmpTargetParamSecLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				noauth(1),
				auth(2),
				priv(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamEntry 4 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTargetParamControl OBJECT IDENTIFIER ::= { sleV2SnmpTargetParam 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTargetParamControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createTargetParam(1),
				deleteTargetParam(2),
				setTargetParamProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpTargetParamControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpTargetParamControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpTargetParamControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpTargetParamControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpTargetParamControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpTargetParamControlSecModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				v1(1),
				v2c(2),
				v3(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpTargetParamControlSecName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpTargetParamControlSecLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				noauth(1),
				auth(2),
				priv(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTargetParamControl 9 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTargetParamNotification OBJECT IDENTIFIER ::= { sleV2SnmpTargetParam 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTargetParamCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTargetParamControlRequest, sleV2SnmpTargetParamControlTimeStamp, sleV2SnmpTargetParamControlReqResult, sleV2SnmpTargetParamName, sleV2SnmpTargetParamSecModel, 
				sleV2SnmpTargetParamSecName, sleV2SnmpTargetParamSecLevel }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTargetParamNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpTargetParamDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTargetParamControlRequest, sleV2SnmpTargetParamControlTimeStamp, sleV2SnmpTargetParamControlReqResult, sleV2SnmpTargetParamControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTargetParamNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpTargetParamChanged NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTargetParamControlRequest, sleV2SnmpTargetParamControlTimeStamp, sleV2SnmpTargetParamControlReqResult, sleV2SnmpTargetParamName, sleV2SnmpTargetParamSecModel, 
				sleV2SnmpTargetParamSecName, sleV2SnmpTargetParamSecLevel }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTargetParamNotification 3 }

		
		-- *******.4.1.6296.102.8.9
		sleV2SnmpTraphost OBJECT IDENTIFIER ::= { sleV2Snmp 9 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTraphostTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpTraphostEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphost 1 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTraphostEntry OBJECT-TYPE
			SYNTAX SleV2SnmpTraphostEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpTraphostType, sleV2SnmpTraphostAddrType, sleV2SnmpTraphostAddrValue }
			::= { sleV2SnmpTraphostTable 1 }

		
		SleV2SnmpTraphostEntry ::=
			SEQUENCE { 
				sleV2SnmpTraphostType
					INTEGER,
				sleV2SnmpTraphostAddrType
					InetAddressType,
				sleV2SnmpTraphostAddrValue
					InetAddress,
				sleV2SnmpTraphostCommunity
					OCTET STRING,
				sleV2SnmpTraphostVrfName
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleV2SnmpTraphostType OBJECT-TYPE
			SYNTAX INTEGER
				{
				trapHost(1),
				trap2Host(2),
				informTrapHost(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleV2SnmpTraphostAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleV2SnmpTraphostAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleV2SnmpTraphostCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleV2SnmpTraphostVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostEntry 5 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTraphostControl OBJECT IDENTIFIER ::= { sleV2SnmpTraphost 2 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTraphostControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createTraphost(1),
				deleteTraphost(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpTraphostControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleV2SnmpTraphostControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleV2SnmpTraphostControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleV2SnmpTraphostControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleV2SnmpTraphostControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				trapHost(1),
				trap2Host(2),
				informTraphost(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleV2SnmpTraphostControlAddrType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleV2SnmpTraphostControlAddrValue OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleV2SnmpTraphostControlCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleV2SnmpTraphostControlVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpTraphostControl 10 }

		
		-- *******.4.1.6296.*********
		sleV2SnmpTraphostNotification OBJECT IDENTIFIER ::= { sleV2SnmpTraphost 3 }

		
		-- *******.4.1.6296.*********.1
		sleV2SnmpTraphostCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTraphostControlRequest, sleV2SnmpTraphostControlTimeStamp, sleV2SnmpTraphostControlReqResult, sleV2SnmpTraphostType, sleV2SnmpTraphostAddrType, 
				sleV2SnmpTraphostAddrValue, sleV2SnmpTraphostCommunity, sleV2SnmpTraphostVrfName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTraphostNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleV2SnmpTraphostDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpTraphostControlRequest, sleV2SnmpTraphostControlTimeStamp, sleV2SnmpTraphostControlReqResult, sleV2SnmpTraphostControlType, sleV2SnmpTraphostControlAddrType, 
				sleV2SnmpTraphostControlAddrValue }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpTraphostNotification 2 }

		
		-- *******.4.1.6296.102.8.10
		sleV2SnmpUser OBJECT IDENTIFIER ::= { sleV2Snmp 10 }

		
		-- *******.4.1.6296.**********
		sleV2SnmpUserTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUser 1 }

		
		-- *******.4.1.6296.**********.1
		sleV2SnmpUserEntry OBJECT-TYPE
			SYNTAX SleV2SnmpUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpUserName }
			::= { sleV2SnmpUserTable 1 }

		
		SleV2SnmpUserEntry ::=
			SEQUENCE { 
				sleV2SnmpUserName
					OCTET STRING,
				sleV2SnmpUserAuthType
					INTEGER,
				sleV2SnmpUserAuthKey
					OCTET STRING,
				sleV2SnmpUserPrivType
					INTEGER,
				sleV2SnmpUserPrivKey
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleV2SnmpUserName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleV2SnmpUserAuthType OBJECT-TYPE
			SYNTAX INTEGER
				{
				md5(1),
				sha(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleV2SnmpUserAuthKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleV2SnmpUserPrivType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				des(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleV2SnmpUserPrivKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserEntry 5 }

		
		-- *******.4.1.6296.**********
		sleV2SnmpUserControl OBJECT IDENTIFIER ::= { sleV2SnmpUser 2 }

		
		-- *******.4.1.6296.**********.1
		sleV2SnmpUserControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createUser(1),
				deleteUser(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2SnmpUserControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleV2SnmpUserControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleV2SnmpUserControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleV2SnmpUserControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleV2SnmpUserControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleV2SnmpUserControlAuthType OBJECT-TYPE
			SYNTAX INTEGER
				{
				md5(1),
				sha(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleV2SnmpUserControlAuthKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleV2SnmpUserControlPrivType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				des(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleV2SnmpUserControlPrivKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpUserControl 10 }

		
		-- *******.4.1.6296.**********
		sleV2SnmpUserNotification OBJECT IDENTIFIER ::= { sleV2SnmpUser 3 }

		
		-- *******.4.1.6296.**********.1
		sleV2SnmpUserCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpUserControlRequest, sleV2SnmpUserControlTimeStamp, sleV2SnmpUserControlReqResult, sleV2SnmpUserName, sleV2SnmpUserAuthType, 
				sleV2SnmpUserAuthKey, sleV2SnmpUserPrivType, sleV2SnmpUserPrivKey }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpUserNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2SnmpUserDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpUserControlRequest, sleV2SnmpUserControlTimeStamp, sleV2SnmpUserControlReqResult, sleV2SnmpUserControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpUserNotification 2 }

		
		-- *******.4.1.6296.102.8.11
		sleV2SnmpView OBJECT IDENTIFIER ::= { sleV2Snmp 11 }

		
		-- *******.4.1.6296.**********
		sleV2SnmpViewTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2SnmpViewEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpView 1 }

		
		-- *******.4.1.6296.**********.1
		sleV2SnmpViewEntry OBJECT-TYPE
			SYNTAX SleV2SnmpViewEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2SnmpViewName, sleV2SnmpViewOid }
			::= { sleV2SnmpViewTable 1 }

		
		SleV2SnmpViewEntry ::=
			SEQUENCE { 
				sleV2SnmpViewName
					OCTET STRING,
				sleV2SnmpViewOid
					OBJECT IDENTIFIER,
				sleV2SnmpViewMask
					OCTET STRING,
				sleV2SnmpViewType
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleV2SnmpViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleV2SnmpViewOid OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleV2SnmpViewMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleV2SnmpViewType OBJECT-TYPE
			SYNTAX INTEGER
				{
				include(1),
				exclude(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewEntry 4 }

		
		-- *******.4.1.6296.**********
		sleV2SnmpViewControl OBJECT IDENTIFIER ::= { sleV2SnmpView 2 }

		
		-- *******.4.1.6296.**********.1
		sleV2SnmpViewControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createView(1),
				deleteView(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2SnmpViewControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleV2SnmpViewControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleV2SnmpViewControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleV2SnmpViewControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleV2SnmpViewControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleV2SnmpViewControlOid OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleV2SnmpViewControlMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleV2SnmpViewControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				include(1),
				exclude(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2SnmpViewControl 9 }

		
		-- *******.4.1.6296.**********
		sleV2SnmpViewNotification OBJECT IDENTIFIER ::= { sleV2SnmpView 3 }

		
		-- *******.4.1.6296.**********.1
		sleV2SnmpViewCreated NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpViewControlRequest, sleV2SnmpViewControlTimeStamp, sleV2SnmpViewControlReqResult, sleV2SnmpViewName, sleV2SnmpViewOid, 
				sleV2SnmpViewMask, sleV2SnmpViewType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpViewNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2SnmpViewDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2SnmpViewControlRequest, sleV2SnmpViewControlTimeStamp, sleV2SnmpViewControlReqResult, sleV2SnmpViewControlName, sleV2SnmpViewControlOid
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2SnmpViewNotification 2 }

		
		-- *******.4.1.6296.102.8.12
		sleV2SnmpObjectGroup OBJECT-GROUP
			OBJECTS { sleV2SnmpBaseAgentAddrType, sleV2SnmpBaseAgentAddrValue, sleV2SnmpBaseContacts, sleV2SnmpBaseEngineIdType, sleV2SnmpBaseEngineIdValue, 
				sleV2SnmpBaseLocation, sleV2SnmpBaseLogStatus, sleV2SnmpBaseTrapLogStatus, sleV2SnmpBaseTrapLogThreshold, sleV2SnmpBaseTrapMode, 
				sleV2SnmpBaseControlRequest, sleV2SnmpBaseControlStatus, sleV2SnmpBaseControlTimer, sleV2SnmpBaseControlTimeStamp, sleV2SnmpBaseControlReqResult, 
				sleV2SnmpBaseControlAgentAddrType, sleV2SnmpBaseControlAgentAddrValue, sleV2SnmpBaseControlContacts, sleV2SnmpBaseControlEngineIdType, sleV2SnmpBaseControlEngineIdValue, 
				sleV2SnmpBaseControlLocation, sleV2SnmpBaseControlLogStatus, sleV2SnmpBaseControlTrapLogStatus, sleV2SnmpBaseControlTrapLogThreshold, sleV2SnmpCom2secName, 
				sleV2SnmpCom2secAddrType, sleV2SnmpCom2secAddrValue, sleV2SnmpCom2secPrefixLen, sleV2SnmpCom2secCommunity, sleV2SnmpCom2secControlRequest, 
				sleV2SnmpCom2secControlStatus, sleV2SnmpCom2secControlTimer, sleV2SnmpCom2secControlTimeStamp, sleV2SnmpCom2secControlReqResult, sleV2SnmpCom2secControlName, 
				sleV2SnmpCom2secControlAddrType, sleV2SnmpCom2secControlAddrValue, sleV2SnmpCom2secControlPrefixLen, sleV2SnmpCom2secControlCommunity, sleV2SnmpCommunityValue, 
				sleV2SnmpCommunityType, sleV2SnmpCommunityAddrType, sleV2SnmpCommunityAddrValue, sleV2SnmpCommunityOID, sleV2SnmpCommunityControlRequest, 
				sleV2SnmpCommunityControlStatus, sleV2SnmpCommunityControlTimer, sleV2SnmpCommunityControlTimeStamp, sleV2SnmpCommunityControlReqResult, sleV2SnmpCommunityControlValue, 
				sleV2SnmpCommunityControlType, sleV2SnmpCommunityControlAddrType, sleV2SnmpCommunityControlAddrValue, sleV2SnmpCommunityControlOID, sleV2SnmpGroupName, 
				sleV2SnmpGroupSecModel, sleV2SnmpGroupSecName, sleV2SnmpGroupControlRequest, sleV2SnmpGroupControlStatus, sleV2SnmpGroupControlTimer, 
				sleV2SnmpGroupControlTimeStamp, sleV2SnmpGroupControlReqResult, sleV2SnmpGroupControlName, sleV2SnmpGroupControlSecModel, sleV2SnmpGroupControlSecName, 
				sleV2SnmpNotifyName, sleV2SnmpNotifyTag, sleV2SnmpNotifyType, sleV2SnmpNotifyControlRequest, sleV2SnmpNotifyControlStatus, 
				sleV2SnmpNotifyControlTimer, sleV2SnmpNotifyControlTimeStamp, sleV2SnmpNotifyControlReqResult, sleV2SnmpNotifyControlName, sleV2SnmpNotifyControlTag, 
				sleV2SnmpNotifyControlType, sleV2SnmpTargetAddrName, sleV2SnmpTargetAddrParams, sleV2SnmpTargetAddrHostType, sleV2SnmpTargetAddrHostAddr, 
				sleV2SnmpTargetAddrPort, sleV2SnmpTargetAddrTimeout, sleV2SnmpTargetAddrRetryCnt, sleV2SnmpTargetAddrTagList, sleV2SnmpTargetAddrControlRequest, 
				sleV2SnmpTargetAddrControlStatus, sleV2SnmpTargetAddrControlTimer, sleV2SnmpTargetAddrControlTimeStamp, sleV2SnmpTargetAddrControlReqResult, sleV2SnmpTargetAddrControlName, 
				sleV2SnmpTargetAddrControlParams, sleV2SnmpTargetAddrControlHostType, sleV2SnmpTargetAddrControlHostAddr, sleV2SnmpTargetAddrControlPort, sleV2SnmpTargetAddrControlTimeout, 
				sleV2SnmpTargetAddrControlRetryCnt, sleV2SnmpTargetAddrControlTagList, sleV2SnmpTargetParamName, sleV2SnmpTargetParamSecModel, sleV2SnmpTargetParamSecName, 
				sleV2SnmpTargetParamSecLevel, sleV2SnmpTargetParamControlRequest, sleV2SnmpTargetParamControlStatus, sleV2SnmpTargetParamControlTimer, sleV2SnmpTargetParamControlTimeStamp, 
				sleV2SnmpTargetParamControlReqResult, sleV2SnmpTargetParamControlName, sleV2SnmpTargetParamControlSecModel, sleV2SnmpTargetParamControlSecName, sleV2SnmpTargetParamControlSecLevel, 
				sleV2SnmpTraphostType, sleV2SnmpTraphostAddrType, sleV2SnmpTraphostAddrValue, sleV2SnmpTraphostCommunity, sleV2SnmpTraphostVrfName, 
				sleV2SnmpTraphostControlRequest, sleV2SnmpTraphostControlStatus, sleV2SnmpTraphostControlTimer, sleV2SnmpTraphostControlTimeStamp, sleV2SnmpTraphostControlReqResult, 
				sleV2SnmpTraphostControlType, sleV2SnmpTraphostControlAddrType, sleV2SnmpTraphostControlAddrValue, sleV2SnmpTraphostControlCommunity, sleV2SnmpTraphostControlVrfName, 
				sleV2SnmpUserName, sleV2SnmpUserAuthType, sleV2SnmpUserAuthKey, sleV2SnmpUserPrivType, sleV2SnmpUserPrivKey, 
				sleV2SnmpUserControlRequest, sleV2SnmpUserControlStatus, sleV2SnmpUserControlTimer, sleV2SnmpUserControlTimeStamp, sleV2SnmpUserControlReqResult, 
				sleV2SnmpUserControlName, sleV2SnmpUserControlAuthType, sleV2SnmpUserControlAuthKey, sleV2SnmpUserControlPrivType, sleV2SnmpUserControlPrivKey, 
				sleV2SnmpViewName, sleV2SnmpViewOid, sleV2SnmpViewMask, sleV2SnmpViewType, sleV2SnmpViewControlRequest, 
				sleV2SnmpViewControlStatus, sleV2SnmpViewControlTimer, sleV2SnmpViewControlTimeStamp, sleV2SnmpViewControlReqResult, sleV2SnmpViewControlName, 
				sleV2SnmpViewControlOid, sleV2SnmpViewControlMask, sleV2SnmpViewControlType, sleV2SnmpBaseControlTrapMode, sleV2SnmpAccessGroupName, 
				sleV2SnmpAccessSecurityModel, sleV2SnmpAccessSecurityLevel, sleV2SnmpAccessReadViewName, sleV2SnmpAccessWriteViewName, sleV2SnmpAccessNotifyViewName, 
				sleV2SnmpAccessControlRequest, sleV2SnmpAccessControlStatus, sleV2SnmpAccessControlTimer, sleV2SnmpAccessControlTimeStamp, sleV2SnmpAccessControlReqResult, 
				sleV2SnmpAccessControlGroupName, sleV2SnmpAccessControlSecurityModel, sleV2SnmpAccessControlSecurityLevel, sleV2SnmpAccessControlReadViewName, sleV2SnmpAccessControlWriteViewName, 
				sleV2SnmpAccessControlNotifyViewName, sleV2SnmpBaseVrfName, sleV2SnmpBaseControlVrfName, sleV2SnmpBaseNotifyStatus, sleV2SnmpLogIndex, 
				sleV2SnmpLogText, sleV2SnmpTrapLogIndex, sleV2SnmpTrapLogText, sleV2SnmpBaseControlNotifyStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2Snmp 12 }

		
		-- *******.4.1.6296.102.8.13
		sleV2SnmpNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleV2SnmpCleared, sleV2SnmpAlarmHistoryCleared, sleV2SnmpAlarmReportCleared, sleV2SnmpLogCleared, sleV2SnmpTrapLogCleared, 
				sleV2SnmpAgentAddrCreated, sleV2SnmpAgentAddrDeleted, sleV2SnmpContactsCreated, sleV2SnmpContactsDeleted, sleV2SnmpLocationCreated, 
				sleV2SnmpLocationDeleted, sleV2SnmpTrapLogStatusChanged, sleV2SnmpTrapModeChanged, sleV2SnmpEngineIdCreated, sleV2SnmpEngineIdDeleted, 
				sleV2SnmpLogStatusChanged, sleV2SnmpAccessCreated, sleV2SnmpAccessDeleted, sleV2SnmpAccessProfileChanged, sleV2SnmpCom2secCreated, 
				sleV2SnmpCom2secDeleted, sleV2SnmpCom2secChanged, sleV2SnmpCommunityCreated, sleV2SnmpCommunityDeleted, sleV2SnmpCommunityChanged, 
				sleV2SnmpGroupCreated, sleV2SnmpGroupDeleted, sleV2SnmpNotifyCreated, sleV2SnmpNotifyDeleted, sleV2SnmpTargetAddrCreated, 
				sleV2SnmpTargetAddrDeleted, sleV2SnmpTargetAddrChanged, sleV2SnmpTargetParamCreated, sleV2SnmpTargetParamDeleted, sleV2SnmpTargetParamChanged, 
				sleV2SnmpTraphostCreated, sleV2SnmpTraphostDeleted, sleV2SnmpUserCreated, sleV2SnmpUserDeleted, sleV2SnmpViewCreated, 
				sleV2SnmpNotifyActivityStatusChanged, sleV2SnmpViewDeleted, sleV2SnmpVrfSet, sleV2SnmpVrfUnset, sleV2SnmpTrapLogThresholdChanged
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2Snmp 13 }

		
	
	END

--
-- slev2-snmp-mib.mib
--
