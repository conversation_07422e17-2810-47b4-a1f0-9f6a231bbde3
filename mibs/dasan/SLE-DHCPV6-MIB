--
-- sle-dhcpv6-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, August 04, 2015 at 13:17:36
--

	SLE-DHCPV6-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			InetAddressIPv6, InetAddressType, InetAddress			
				FROM INET-ADDRESS-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>teger32, <PERSON><PERSON><PERSON>32, OBJECT-TYP<PERSON>, 
			MODULE-IDENTI<PERSON>, NOTIFICATION-TYPE			
				FROM SNMPv2-<PERSON><PERSON>			
			MacAddress			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.6296.101.27
		sleDhcp6 MODULE-IDENTITY 
			LAST-UPDATED "201411111530Z"		-- November 11, 2014 at 15:30 GMT
			ORGANIZATION 
				"Hana"
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleMgmt 27 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.27.1
		sleDhcp6Base OBJECT IDENTIFIER ::= { sleDhcp6 1 }

		
		-- *******.4.1.6296.**********
		sleDhcp6BaseInfo OBJECT IDENTIFIER ::= { sleDhcp6Base 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6Duid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6DatabaseAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6DatabaseInterval OBJECT-TYPE
			SYNTAX INTEGER (120..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseInfo 3 }

		
		-- *******.4.1.6296.**********
		sleDhcp6BaseControl OBJECT IDENTIFIER ::= { sleDhcp6Base 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6BaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6Database(1),
				destroyDhcp6Database(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6BaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6BaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleDhcp6BaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleDhcp6BaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleDhcp6BaseControlDatabaseAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleDhcp6BaseControlDatabaseInterval OBJECT-TYPE
			SYNTAX INTEGER (120..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BaseControl 7 }

		
		-- *******.4.1.6296.**********
		sleDhcp6BaseNotification OBJECT IDENTIFIER ::= { sleDhcp6Base 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6DatabaseCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6BaseControlRequest, sleDhcp6BaseControlTimeStamp, sleDhcp6BaseControlReqResult, sleDhcp6BaseControlDatabaseAddr, sleDhcp6BaseControlDatabaseInterval
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6BaseNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6DatabaseDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6BaseControlRequest, sleDhcp6BaseControlTimeStamp, sleDhcp6BaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6BaseNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6DatabaseChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6BaseControlRequest, sleDhcp6BaseControlTimeStamp, sleDhcp6BaseControlReqResult, sleDhcp6BaseControlDatabaseAddr, sleDhcp6BaseControlDatabaseInterval
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6BaseNotification 3 }

		
		-- *******.4.1.6296.101.27.2
		sleDhcp6Pool OBJECT IDENTIFIER ::= { sleDhcp6 2 }

		
		-- *******.4.1.6296.**********
		sleDhcp6PoolBase OBJECT IDENTIFIER ::= { sleDhcp6Pool 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6PoolTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6PoolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBase 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6PoolEntry OBJECT-TYPE
			SYNTAX SleDhcp6PoolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6PoolIndex }
			::= { sleDhcp6PoolTable 1 }

		
		SleDhcp6PoolEntry ::=
			SEQUENCE { 
				sleDhcp6PoolIndex
					INTEGER,
				sleDhcp6PoolName
					OCTET STRING,
				sleDhcp6PoolStaticEntry
					INTEGER,
				sleDhcp6PoolDynamicEntry
					INTEGER,
				sleDhcp6PoolImportDns
					INTEGER,
				sleDhcp6PoolImportDomain
					INTEGER,
				sleDhcp6PoolImportInfoRef
					INTEGER,
				sleDhcp6PoolImportNisAdd
					INTEGER,
				sleDhcp6PoolImportNisDom
					INTEGER,
				sleDhcp6PoolImportNispAdd
					INTEGER,
				sleDhcp6PoolImportNispDom
					INTEGER,
				sleDhcp6PoolImportSipAdd
					INTEGER,
				sleDhcp6PoolImportSipDom
					INTEGER,
				sleDhcp6PoolImportSntpAdd
					INTEGER,
				sleDhcp6PoolInfoRefTime
					INTEGER,
				sleDhcp6PoolAaaValTime
					INTEGER,
				sleDhcp6PoolAaaPreTime
					INTEGER,
				sleDhcp6PoolPdName
					OCTET STRING,
				sleDhcp6PoolPdValTime
					INTEGER,
				sleDhcp6PoolPdPreTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6PoolIndex OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6PoolName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6PoolStaticEntry OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6PoolDynamicEntry OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6PoolImportDns OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDhcp6PoolImportDomain OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleDhcp6PoolImportInfoRef OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleDhcp6PoolImportNisAdd OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleDhcp6PoolImportNisDom OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 9 }

		
		-- *******.4.1.6296.**********.1.1.10
		sleDhcp6PoolImportNispAdd OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 10 }

		
		-- *******.4.1.6296.**********.1.1.11
		sleDhcp6PoolImportNispDom OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 11 }

		
		-- *******.4.1.6296.**********.1.1.12
		sleDhcp6PoolImportSipAdd OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 12 }

		
		-- *******.4.1.6296.**********.1.1.13
		sleDhcp6PoolImportSipDom OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 13 }

		
		-- *******.4.1.6296.**********.1.1.14
		sleDhcp6PoolImportSntpAdd OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 14 }

		
		-- *******.4.1.6296.**********.1.1.15
		sleDhcp6PoolInfoRefTime OBJECT-TYPE
			SYNTAX INTEGER (600..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 15 }

		
		-- *******.4.1.6296.**********.1.1.16
		sleDhcp6PoolAaaValTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 16 }

		
		-- *******.4.1.6296.**********.1.1.17
		sleDhcp6PoolAaaPreTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 17 }

		
		-- *******.4.1.6296.**********.1.1.18
		sleDhcp6PoolPdName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 18 }

		
		-- *******.4.1.6296.**********.1.1.19
		sleDhcp6PoolPdValTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 19 }

		
		-- *******.4.1.6296.**********.1.1.20
		sleDhcp6PoolPdPreTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolEntry 20 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6PoolBaseControl OBJECT IDENTIFIER ::= { sleDhcp6PoolBase 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6PoolBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6Pool(1),
				destroyDhcp6Pool(2),
				setPoolImportDns(3),
				setPoolImportDomain(4),
				setPoolImportInfoRef(5),
				setPoolImportNisAdd(6),
				setPoolImportNisDom(7),
				setPoolImportNispAdd(8),
				setPoolImportNispDom(9),
				setPoolImportSipAdd(10),
				setPoolImportSipDom(11),
				setPoolImportSntpAdd(12),
				setPoolInfoRefTime(13),
				createPoolAaaTime(14),
				destroyPoolAaaTime(15),
				createPdPool(16),
				destroyPdPool(17)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6PoolBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6PoolBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6PoolBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6PoolBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6PoolBaseControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6PoolBaseControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6PoolBaseControlImportDns OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6PoolBaseControlImportDomain OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6PoolBaseControlImportInfoRef OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleDhcp6PoolBaseControlImportNisAdd OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleDhcp6PoolBaseControlImportNisDom OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 12 }

		
		-- *******.4.1.6296.**********.2.13
		sleDhcp6PoolBaseControlImportNispAdd OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 13 }

		
		-- *******.4.1.6296.**********.2.14
		sleDhcp6PoolBaseControlImportNispDom OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 14 }

		
		-- *******.4.1.6296.**********.2.15
		sleDhcp6PoolBaseControlImportSipAdd OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 15 }

		
		-- *******.4.1.6296.**********.2.16
		sleDhcp6PoolBaseControlImportSipDom OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 16 }

		
		-- *******.4.1.6296.**********.2.17
		sleDhcp6PoolBaseControlImportSntpAddr OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 17 }

		
		-- *******.4.1.6296.**********.2.18
		sleDhcp6PoolBaseControlInfoRefTime OBJECT-TYPE
			SYNTAX INTEGER (600..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 18 }

		
		-- *******.4.1.6296.**********.2.19
		sleDhcp6PoolBaseControlAaaValTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 19 }

		
		-- *******.4.1.6296.**********.2.20
		sleDhcp6PoolBaseControlAaaPreTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 20 }

		
		-- *******.4.1.6296.**********.2.21
		sleDhcp6PoolBaseControlPdName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 21 }

		
		-- *******.4.1.6296.**********.2.22
		sleDhcp6PoolBaseControlPdValTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 22 }

		
		-- *******.4.1.6296.**********.2.23
		sleDhcp6PoolBaseControlPdPreTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6PoolBaseControl 23 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6PoolBaseNotification OBJECT IDENTIFIER ::= { sleDhcp6PoolBase 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6PoolCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6PoolBaseControlRequest, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult, sleDhcp6PoolBaseControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6PoolBaseNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6PoolDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6PoolBaseControlRequest, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult, sleDhcp6PoolBaseControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6PoolBaseNotification 2 }

		
		-- *******.4.1.6296.**********.3.3
		sleDhcp6ImportSet NOTIFICATION-TYPE
			OBJECTS { sleDhcp6PoolBaseControlRequest, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult, sleDhcp6PoolBaseControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6PoolBaseNotification 3 }

		
		-- *******.4.1.6296.**********.3.4
		sleDhcp6PoolAaaTimeCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6PoolBaseControlRequest, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult, sleDhcp6PoolBaseControlAaaValTime, sleDhcp6PoolBaseControlAaaPreTime
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6PoolBaseNotification 4 }

		
		-- *******.4.1.6296.**********.3.5
		sleDhcp6PoolAaaTimeDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6PoolBaseControlRequest, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6PoolBaseNotification 5 }

		
		-- *******.4.1.6296.**********.3.6
		sleDhcp6PdPoolCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6PoolBaseControlRequest, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult, sleDhcp6PoolBaseControlPdName, sleDhcp6PoolBaseControlPdValTime, 
				sleDhcp6PoolBaseControlPdPreTime }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6PoolBaseNotification 6 }

		
		-- *******.4.1.6296.**********.3.7
		sleDhcp6PdPoolDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6PoolBaseControlRequest, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult, sleDhcp6PoolBaseControlPdName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6PoolBaseNotification 7 }

		
		-- *******.4.1.6296.**********
		sleDhcp6FixedAddr OBJECT IDENTIFIER ::= { sleDhcp6Pool 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6FixedAddrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6FixedAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddr 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6FixedAddrEntry OBJECT-TYPE
			SYNTAX SleDhcp6FixedAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6PoolIndex, sleDhcp6FixedAddrIndex }
			::= { sleDhcp6FixedAddrTable 1 }

		
		SleDhcp6FixedAddrEntry ::=
			SEQUENCE { 
				sleDhcp6FixedAddrIndex
					Integer32,
				sleDhcp6FixedAddrAddress
					OCTET STRING,
				sleDhcp6FixedAddrDuid
					OCTET STRING,
				sleDhcp6FixedAddrValTime
					INTEGER,
				sleDhcp6FixedAddrPreTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6FixedAddrIndex OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6FixedAddrAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6FixedAddrDuid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6FixedAddrValTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6FixedAddrPreTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrEntry 5 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6FixedAddrControl OBJECT IDENTIFIER ::= { sleDhcp6FixedAddr 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6FixedAddrControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6FixedAddr(1),
				destroyDhcp6FixedAddr(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6FixedAddrControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6FixedAddrControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6FixedAddrControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6FixedAddrControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6FixedAddrControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6FixedAddrControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6FixedAddrControlAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6FixedAddrControlDuid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6FixedAddrControlValTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleDhcp6FixedAddrControlPreTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedAddrControl 11 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6FixedAddrNotification OBJECT IDENTIFIER ::= { sleDhcp6FixedAddr 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6FixedAddrCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6FixedAddrControlRequest, sleDhcp6FixedAddrControlTimeStamp, sleDhcp6FixedAddrControlReqResult, sleDhcp6FixedAddrControlAddress, sleDhcp6FixedAddrControlDuid, 
				sleDhcp6FixedAddrControlValTime, sleDhcp6FixedAddrControlPreTime }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6FixedAddrNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6FixedAddrDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6FixedAddrControlRequest, sleDhcp6FixedAddrControlTimeStamp, sleDhcp6FixedAddrControlReqResult, sleDhcp6FixedAddrControlAddress, sleDhcp6FixedAddrControlDuid
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6FixedAddrNotification 2 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ServerOption OBJECT IDENTIFIER ::= { sleDhcp6Pool 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ServerOptionTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6ServerOptionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOption 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6ServerOptionEntry OBJECT-TYPE
			SYNTAX SleDhcp6ServerOptionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6ServerOptionIndex, sleDhcp6ServerOptionCode, sleDhcp6PoolIndex }
			::= { sleDhcp6ServerOptionTable 1 }

		
		SleDhcp6ServerOptionEntry ::=
			SEQUENCE { 
				sleDhcp6ServerOptionCode
					INTEGER,
				sleDhcp6ServerOptionIndex
					INTEGER,
				sleDhcp6ServerOptionType
					INTEGER,
				sleDhcp6ServerOptionValue
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6ServerOptionCode OBJECT-TYPE
			SYNTAX INTEGER
				{
				nisAddress(1),
				nisDomain(2),
				nispAddress(3),
				nispDomain(4),
				sipAddress(5),
				sipDomain(6),
				sntpAddress(7),
				domainName(8),
				dnsServer(9)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6ServerOptionIndex OBJECT-TYPE
			SYNTAX INTEGER (0..8)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6ServerOptionType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipv6address(1),
				text(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6ServerOptionValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionEntry 4 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6ServerOptionControl OBJECT IDENTIFIER ::= { sleDhcp6ServerOption 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6ServerOptionControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6ServerOption(1),
				destroyDhcp6ServerOption(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6ServerOptionControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6ServerOptionControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6ServerOptionControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6ServerOptionControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6ServerOptionControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6ServerOptionControlCode OBJECT-TYPE
			SYNTAX INTEGER
				{
				nisAddress(1),
				nisDomain(2),
				nispAddress(3),
				nispDomain(4),
				sipAddress(5),
				sipDomain(6),
				sntpAddress(7),
				domainName(8),
				dnsServer(9)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6ServerOptionControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6ServerOptionControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipv6address(1),
				text(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6ServerOptionControlValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerOptionControl 10 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6ServerOptionNotification OBJECT IDENTIFIER ::= { sleDhcp6ServerOption 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6ServerOptionCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ServerOptionControlRequest, sleDhcp6ServerOptionControlTimeStamp, sleDhcp6ServerOptionControlReqResult, sleDhcp6ServerOptionControlCode, sleDhcp6ServerOptionControlValue, 
				sleDhcp6ServerOptionControlType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ServerOptionNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6ServerOptionDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ServerOptionControlRequest, sleDhcp6ServerOptionControlTimeStamp, sleDhcp6ServerOptionControlReqResult, sleDhcp6ServerOptionControlCode, sleDhcp6ServerOptionControlType, 
				sleDhcp6ServerOptionControlValue }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ServerOptionNotification 2 }

		
		-- *******.4.1.6296.**********
		sleDhcp6Range OBJECT IDENTIFIER ::= { sleDhcp6Pool 4 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6RangeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6RangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6Range 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6RangeEntry OBJECT-TYPE
			SYNTAX SleDhcp6RangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6PoolIndex, sleDhcp6RangeIndex }
			::= { sleDhcp6RangeTable 1 }

		
		SleDhcp6RangeEntry ::=
			SEQUENCE { 
				sleDhcp6RangeIndex
					INTEGER,
				sleDhcp6RangeStart
					OCTET STRING,
				sleDhcp6RangeEnd
					OCTET STRING,
				sleDhcp6RangeValTime
					INTEGER,
				sleDhcp6RangePreTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6RangeIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6RangeStart OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6RangeEnd OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6RangeValTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6RangePreTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeEntry 5 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6RangeControl OBJECT IDENTIFIER ::= { sleDhcp6Range 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6RangeControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6Range(1),
				destroyDhcp6Range(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6RangeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6RangeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6RangeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6RangeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6RangeControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6RangeControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6RangeControlStart OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6RangeControlEnd OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6RangeControlValTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleDhcp6RangeControlPreTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RangeControl 11 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6RangeNotification OBJECT IDENTIFIER ::= { sleDhcp6Range 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6RangeCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6RangeControlRequest, sleDhcp6RangeControlTimeStamp, sleDhcp6RangeControlReqResult, sleDhcp6RangeControlStart, sleDhcp6RangeControlEnd, 
				sleDhcp6RangeControlValTime, sleDhcp6RangeControlPreTime }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6RangeNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6RangeDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6RangeControlRequest, sleDhcp6RangeControlTimeStamp, sleDhcp6RangeControlReqResult, sleDhcp6RangeControlStart, sleDhcp6RangeControlEnd
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6RangeNotification 2 }

		
		-- *******.4.1.6296.**********
		sleDhcp6FixedPrefix OBJECT IDENTIFIER ::= { sleDhcp6Pool 5 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6FixedPrefixTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6FixedPrefixEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefix 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6FixedPrefixEntry OBJECT-TYPE
			SYNTAX SleDhcp6FixedPrefixEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6PoolIndex, sleDhcp6FixedPrefixIndex }
			::= { sleDhcp6FixedPrefixTable 1 }

		
		SleDhcp6FixedPrefixEntry ::=
			SEQUENCE { 
				sleDhcp6FixedPrefixIndex
					INTEGER,
				sleDhcp6FixedPrefixValue
					OCTET STRING,
				sleDhcp6FixedPrefixLen
					INTEGER,
				sleDhcp6FixedPrefixDuid
					OCTET STRING,
				sleDhcp6FixedPrefixValTime
					INTEGER,
				sleDhcp6FixedPrefixPreTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6FixedPrefixIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6FixedPrefixValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6FixedPrefixLen OBJECT-TYPE
			SYNTAX INTEGER (1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6FixedPrefixDuid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6FixedPrefixValTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDhcp6FixedPrefixPreTime OBJECT-TYPE
			SYNTAX INTEGER (0 | 60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixEntry 6 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6FixedPrefixControl OBJECT IDENTIFIER ::= { sleDhcp6FixedPrefix 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6FixedPrefixControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6FixedPrefix(1),
				destroyDhcp6FixedPrefix(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6FixedPrefixControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6FixedPrefixControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6FixedPrefixControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6FixedPrefixControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6FixedPrefixControlPoolIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6FixedPrefixControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6FixedPrefixControlValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6FixedPrefixControlLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6FixedPrefixControlDuid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleDhcp6FixedPrefixControlValTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleDhcp6FixedPrefixControlPreTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6FixedPrefixControl 12 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6FixedPrefixNotification OBJECT IDENTIFIER ::= { sleDhcp6FixedPrefix 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6FixedPrefixCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6FixedPrefixControlRequest, sleDhcp6FixedPrefixControlTimeStamp, sleDhcp6FixedPrefixControlReqResult, sleDhcp6FixedPrefixControlValue, sleDhcp6FixedPrefixControlLen, 
				sleDhcp6FixedPrefixControlDuid }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6FixedPrefixNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6FixedPrefixDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6FixedPrefixControlRequest, sleDhcp6FixedPrefixControlTimeStamp, sleDhcp6FixedPrefixControlReqResult, sleDhcp6FixedPrefixControlValue, sleDhcp6FixedPrefixControlLen, 
				sleDhcp6FixedPrefixControlDuid }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6FixedPrefixNotification 2 }

		
		-- *******.4.1.6296.101.27.3
		sleDhcp6Binding OBJECT IDENTIFIER ::= { sleDhcp6 3 }

		
		-- *******.4.1.6296.**********
		sleDhcp6BindBase OBJECT IDENTIFIER ::= { sleDhcp6Binding 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6BindBaseTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6BindBaseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBase 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6BindBaseEntry OBJECT-TYPE
			SYNTAX SleDhcp6BindBaseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6BindBaseIndex }
			::= { sleDhcp6BindBaseTable 1 }

		
		SleDhcp6BindBaseEntry ::=
			SEQUENCE { 
				sleDhcp6BindBaseIndex
					INTEGER,
				sleDhcp6BindBaseLinkLocal
					OCTET STRING,
				sleDhcp6BindBaseDuid
					OCTET STRING,
				sleDhcp6BindBaseIaType
					INTEGER,
				sleDhcp6BindBaseIaid
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6BindBaseIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6BindBaseLinkLocal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6BindBaseDuid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6BindBaseIaType OBJECT-TYPE
			SYNTAX INTEGER
				{
				iana(1),
				iapd(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6BindBaseIaid OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseEntry 5 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6BindBaseControl OBJECT IDENTIFIER ::= { sleDhcp6BindBase 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6BindBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearDhcp6Binding(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6BindBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6BindBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6BindBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6BindBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindBaseControl 5 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6BindBaseNotification OBJECT IDENTIFIER ::= { sleDhcp6BindBase 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6BindingCleared NOTIFICATION-TYPE
			OBJECTS { sleDhcp6BindBaseControlRequest, sleDhcp6BindBaseControlTimeStamp, sleDhcp6BindBaseControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6BindBaseNotification 1 }

		
		-- *******.4.1.6296.**********
		sleDhcp6BindPd OBJECT IDENTIFIER ::= { sleDhcp6Binding 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6BindPdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6BindPdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindPd 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6BindPdEntry OBJECT-TYPE
			SYNTAX SleDhcp6BindPdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6BindBaseIndex, sleDhcp6BindPdIndex }
			::= { sleDhcp6BindPdTable 1 }

		
		SleDhcp6BindPdEntry ::=
			SEQUENCE { 
				sleDhcp6BindPdIndex
					INTEGER,
				sleDhcp6BindPdPrefix
					OCTET STRING,
				sleDhcp6BindPdPrefixLen
					INTEGER,
				sleDhcp6BindPdExpireTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6BindPdIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindPdEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6BindPdPrefix OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindPdEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6BindPdPrefixLen OBJECT-TYPE
			SYNTAX INTEGER (1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindPdEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6BindPdExpireTime OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindPdEntry 4 }

		
		-- *******.4.1.6296.**********
		sleDhcp6BindNa OBJECT IDENTIFIER ::= { sleDhcp6Binding 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6BindNaTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6BindNaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindNa 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6BindNaEntry OBJECT-TYPE
			SYNTAX SleDhcp6BindNaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6BindBaseIndex, sleDhcp6BindNaIndex }
			::= { sleDhcp6BindNaTable 1 }

		
		SleDhcp6BindNaEntry ::=
			SEQUENCE { 
				sleDhcp6BindNaIndex
					INTEGER,
				sleDhcp6BindNaAddress
					OCTET STRING,
				sleDhcp6BindNaExpireTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6BindNaIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindNaEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6BindNaAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindNaEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6BindNaExpireTime OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6BindNaEntry 3 }

		
		-- *******.4.1.6296.101.27.4
		sleDhcp6LocalPool OBJECT IDENTIFIER ::= { sleDhcp6 4 }

		
		-- *******.4.1.6296.**********
		sleDhcp6LocalPoolTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6LocalPoolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPool 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6LocalPoolEntry OBJECT-TYPE
			SYNTAX SleDhcp6LocalPoolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6LocalPoolIndex }
			::= { sleDhcp6LocalPoolTable 1 }

		
		SleDhcp6LocalPoolEntry ::=
			SEQUENCE { 
				sleDhcp6LocalPoolIndex
					INTEGER,
				sleDhcp6LocalPoolName
					OCTET STRING,
				sleDhcp6LocalPoolPrefix
					OCTET STRING,
				sleDhcp6LocalPoolPrefixLen
					INTEGER,
				sleDhcp6LocalPoolAssignLen
					INTEGER,
				sleDhcp6LocalPoolUsedCnt
					INTEGER,
				sleDhcp6LocalPoolAvailCnt
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleDhcp6LocalPoolIndex OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleDhcp6LocalPoolName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleDhcp6LocalPoolPrefix OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleDhcp6LocalPoolPrefixLen OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleDhcp6LocalPoolAssignLen OBJECT-TYPE
			SYNTAX INTEGER (2..64)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleDhcp6LocalPoolUsedCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleDhcp6LocalPoolAvailCnt OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolEntry 7 }

		
		-- *******.4.1.6296.**********
		sleDhcp6LocalPoolControl OBJECT IDENTIFIER ::= { sleDhcp6LocalPool 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6LocalPoolControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6LocalPool(1),
				destroyDhcp6LocalPool(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6LocalPoolControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6LocalPoolControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleDhcp6LocalPoolControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleDhcp6LocalPoolControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleDhcp6LocalPoolControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleDhcp6LocalPoolControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleDhcp6LocalPoolControlPrefix OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleDhcp6LocalPoolControlPrefixLen OBJECT-TYPE
			SYNTAX INTEGER (1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleDhcp6LocalPoolControlAssignLen OBJECT-TYPE
			SYNTAX INTEGER (2..64)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6LocalPoolControl 10 }

		
		-- *******.4.1.6296.**********
		sleDhcp6LocalPoolNotification OBJECT IDENTIFIER ::= { sleDhcp6LocalPool 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6LocalPoolCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6LocalPoolControlRequest, sleDhcp6LocalPoolControlTimeStamp, sleDhcp6LocalPoolControlReqResult, sleDhcp6LocalPoolControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6LocalPoolNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6LocalPoolDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6LocalPoolControlRequest, sleDhcp6LocalPoolControlTimeStamp, sleDhcp6LocalPoolControlReqResult, sleDhcp6LocalPoolControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6LocalPoolNotification 2 }

		
		-- *******.4.1.6296.101.27.5
		sleDhcp6Server OBJECT IDENTIFIER ::= { sleDhcp6 5 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ServerTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6ServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6Server 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ServerEntry OBJECT-TYPE
			SYNTAX SleDhcp6ServerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6ServerIndex }
			::= { sleDhcp6ServerTable 1 }

		
		SleDhcp6ServerEntry ::=
			SEQUENCE { 
				sleDhcp6ServerIndex
					INTEGER,
				sleDhcp6ServerIfName
					OCTET STRING,
				sleDhcp6ServerPoolName
					OCTET STRING,
				sleDhcp6ServerPreference
					INTEGER,
				sleDhcp6ServerRapidCommit
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleDhcp6ServerIndex OBJECT-TYPE
			SYNTAX INTEGER (0..1024)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleDhcp6ServerIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleDhcp6ServerPoolName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleDhcp6ServerPreference OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleDhcp6ServerRapidCommit OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerEntry 5 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ServerControl OBJECT IDENTIFIER ::= { sleDhcp6Server 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ServerControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6Server(1),
				destroyDhcp6Server(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6ServerControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6ServerControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleDhcp6ServerControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleDhcp6ServerControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleDhcp6ServerControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleDhcp6ServerControlIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleDhcp6ServerControlPoolName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleDhcp6ServerControlPreference OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleDhcp6ServerControlRapidCommit OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ServerControl 10 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ServerNotification OBJECT IDENTIFIER ::= { sleDhcp6Server 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ServerCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ServerControlRequest, sleDhcp6ServerControlTimeStamp, sleDhcp6ServerControlReqResult, sleDhcp6ServerControlIfName, sleDhcp6ServerControlPoolName, 
				sleDhcp6ServerControlPreference, sleDhcp6ServerControlRapidCommit }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ServerNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6ServerDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ServerControlRequest, sleDhcp6ServerControlTimeStamp, sleDhcp6ServerControlReqResult, sleDhcp6ServerControlIfName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ServerNotification 2 }

		
		-- *******.4.1.6296.101.27.6
		sleDhcp6Relay OBJECT IDENTIFIER ::= { sleDhcp6 6 }

		
		-- *******.4.1.6296.**********
		sleDhcp6RelayTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6RelayEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6Relay 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6RelayEntry OBJECT-TYPE
			SYNTAX SleDhcp6RelayEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6RelayIndex }
			::= { sleDhcp6RelayTable 1 }

		
		SleDhcp6RelayEntry ::=
			SEQUENCE { 
				sleDhcp6RelayIndex
					INTEGER,
				sleDhcp6RelayIfName
					OCTET STRING,
				sleDhcp6RelayDestAddr
					OCTET STRING,
				sleDhcp6RelayOutputIfname
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleDhcp6RelayIndex OBJECT-TYPE
			SYNTAX INTEGER (0..1024)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleDhcp6RelayIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleDhcp6RelayDestAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleDhcp6RelayOutputIfname OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayEntry 4 }

		
		-- *******.4.1.6296.**********
		sleDhcp6RelayControl OBJECT IDENTIFIER ::= { sleDhcp6Relay 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6RelayControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6Relay(1),
				destroyDhcp6Relay(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6RelayControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6RelayControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleDhcp6RelayControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleDhcp6RelayControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleDhcp6RelayControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleDhcp6RelayControlIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleDhcp6RelayControlDestAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleDhcp6RelayControlOutputIfname OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6RelayControl 9 }

		
		-- *******.4.1.6296.**********
		sleDhcp6RelayNotification OBJECT IDENTIFIER ::= { sleDhcp6Relay 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6RelayCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6RelayControlRequest, sleDhcp6RelayControlTimeStamp, sleDhcp6RelayControlReqResult, sleDhcp6RelayControlIfName, sleDhcp6RelayControlDestAddr, 
				sleDhcp6RelayControlOutputIfname }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6RelayNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6RelayDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6RelayControlRequest, sleDhcp6RelayControlTimeStamp, sleDhcp6RelayControlReqResult, sleDhcp6RelayControlIfName, sleDhcp6RelayControlDestAddr
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6RelayNotification 2 }

		
		-- *******.4.1.6296.101.27.7
		sleDhcp6Client OBJECT IDENTIFIER ::= { sleDhcp6 7 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ClientBase OBJECT IDENTIFIER ::= { sleDhcp6Client 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ClientTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6ClientEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBase 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6ClientEntry OBJECT-TYPE
			SYNTAX SleDhcp6ClientEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6ClientIndex }
			::= { sleDhcp6ClientTable 1 }

		
		SleDhcp6ClientEntry ::=
			SEQUENCE { 
				sleDhcp6ClientIndex
					INTEGER,
				sleDhcp6ClientIfName
					OCTET STRING,
				sleDhcp6ClientState
					INTEGER,
				sleDhcp6ClientServerAddr
					OCTET STRING,
				sleDhcp6ClientServerDuid
					OCTET STRING,
				sleDhcp6ClientServerPref
					INTEGER,
				sleDhcp6ClientRapidCommit
					INTEGER,
				sleDhcp6ClientPdname
					OCTET STRING,
				sleDhcp6ClientInfoRefTime
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6ClientIndex OBJECT-TYPE
			SYNTAX INTEGER (0..1024)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6ClientIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6ClientState OBJECT-TYPE
			SYNTAX INTEGER
				{
				inactive(1),
				active(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6ClientServerAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6ClientServerDuid OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDhcp6ClientServerPref OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleDhcp6ClientRapidCommit OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleDhcp6ClientPdname OBJECT-TYPE
			SYNTAX OCTET STRING
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleDhcp6ClientInfoRefTime OBJECT-TYPE
			SYNTAX Integer32 (600..*********)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Information Refresh Time"
			::= { sleDhcp6ClientEntry 9 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6ClientBaseControl OBJECT IDENTIFIER ::= { sleDhcp6ClientBase 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6ClientBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setDhcp6ClientStatelessConfig(1),
				setDhcp6ClientStatefulNa(2),
				setDhcp6ClientStatefulPd(3),
				setDhcp6ClientRefreshMin(4),
				unsetDhcp6ClientRefreshMin(5),
				setDhcp6ClientStatefulPdwithHint(6),
				clearDhcp6Client(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6ClientBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6ClientBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6ClientBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6ClientBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6ClientBaseControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6ClientBaseControlIfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6ClientBaseControlStatelessFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6ClientBaseControlNaFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6ClientBaseControlPdFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2),
				pdhint(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleDhcp6ClientBaseControlPdHint OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleDhcp6ClientBaseControlIanaRapidCommit OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 12 }

		
		-- *******.4.1.6296.**********.2.13
		sleDhcp6ClientBaseControlIapdRapidCommit OBJECT-TYPE
			SYNTAX INTEGER
				{
				set(1),
				unset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 13 }

		
		-- *******.4.1.6296.**********.2.14
		sleDhcp6ClientBaseControlRefreshMinimumVal OBJECT-TYPE
			SYNTAX INTEGER (600..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 14 }

		
		-- *******.4.1.6296.**********.2.15
		sleDhcp6ClientBaseControlPdname OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientBaseControl 15 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6ClientBaseNotification OBJECT IDENTIFIER ::= { sleDhcp6ClientBase 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6ClientStatelessSet NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ClientBaseControlRequest, sleDhcp6ClientBaseControlTimeStamp, sleDhcp6ClientBaseControlReqResult, sleDhcp6ClientBaseControlIfName, sleDhcp6ClientBaseControlStatelessFlag
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ClientBaseNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6ClientNaSet NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ClientBaseControlRequest, sleDhcp6ClientBaseControlTimeStamp, sleDhcp6ClientBaseControlReqResult, sleDhcp6ClientBaseControlIfName, sleDhcp6ClientBaseControlNaFlag
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ClientBaseNotification 2 }

		
		-- *******.4.1.6296.**********.3.3
		sleDhcp6ClientPdSet NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ClientBaseControlRequest, sleDhcp6ClientBaseControlTimeStamp, sleDhcp6ClientBaseControlReqResult, sleDhcp6ClientBaseControlIfName, sleDhcp6ClientBaseControlPdFlag
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ClientBaseNotification 3 }

		
		-- *******.4.1.6296.**********.3.4
		sleDhcp6ClientCleared NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ClientBaseControlRequest, sleDhcp6ClientBaseControlTimeStamp, sleDhcp6ClientBaseControlReqResult, sleDhcp6ClientBaseControlIfName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ClientBaseNotification 4 }

		
		-- *******.4.1.6296.**********.3.5
		sleDhcp6ClientRefreshMinChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6ClientBaseControlRequest, sleDhcp6ClientBaseControlTimeStamp, sleDhcp6ClientBaseControlReqResult, sleDhcp6ClientBaseControlIfName, sleDhcp6ClientBaseControlRefreshMinimumVal
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6ClientBaseNotification 5 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ClientOption OBJECT IDENTIFIER ::= { sleDhcp6Client 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ClientOptionTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6ClientOptionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientOption 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6ClientOptionEntry OBJECT-TYPE
			SYNTAX SleDhcp6ClientOptionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6ClientIndex, sleDhcp6ClientOptionCode }
			::= { sleDhcp6ClientOptionTable 1 }

		
		SleDhcp6ClientOptionEntry ::=
			SEQUENCE { 
				sleDhcp6ClientOptionCode
					INTEGER,
				sleDhcp6ClientOptionType
					INTEGER,
				sleDhcp6ClientOptionValue
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6ClientOptionCode OBJECT-TYPE
			SYNTAX INTEGER
				{
				nisAddress(1),
				nisDomain(2),
				nispAddress(3),
				nispDomain(4),
				sipAddress(5),
				sipDomain(6),
				sntpAddress(7),
				domainName(8),
				dnsServer(9),
				refreshTime(10)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientOptionEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6ClientOptionType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipv6address(1),
				text(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientOptionEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6ClientOptionValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientOptionEntry 3 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ClientIapd OBJECT IDENTIFIER ::= { sleDhcp6Client 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ClientIapdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6ClientIapdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapd 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6ClientIapdEntry OBJECT-TYPE
			SYNTAX SleDhcp6ClientIapdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6ClientIndex, sleDhcp6ClientIapdIndex }
			::= { sleDhcp6ClientIapdTable 1 }

		
		SleDhcp6ClientIapdEntry ::=
			SEQUENCE { 
				sleDhcp6ClientIapdIndex
					INTEGER,
				sleDhcp6ClientIapdIaid
					INTEGER,
				sleDhcp6ClientIapdT1
					INTEGER,
				sleDhcp6ClientIapdT2
					INTEGER,
				sleDhcp6ClientIapdPrefix
					OCTET STRING,
				sleDhcp6ClientIapdPrefixLen
					INTEGER,
				sleDhcp6ClientIapdLifeTime
					INTEGER,
				sleDhcp6ClientIapdValidTime
					INTEGER,
				sleDhcp6ClientIapdExpireTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6ClientIapdIndex OBJECT-TYPE
			SYNTAX INTEGER (0..1024)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6ClientIapdIaid OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6ClientIapdT1 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6ClientIapdT2 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6ClientIapdPrefix OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDhcp6ClientIapdPrefixLen OBJECT-TYPE
			SYNTAX INTEGER (1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleDhcp6ClientIapdLifeTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleDhcp6ClientIapdValidTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleDhcp6ClientIapdExpireTime OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIapdEntry 9 }

		
		-- *******.4.1.6296.**********
		sleDhcp6ClientIana OBJECT IDENTIFIER ::= { sleDhcp6Client 4 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6ClientIanaTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6ClientIanaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIana 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6ClientIanaEntry OBJECT-TYPE
			SYNTAX SleDhcp6ClientIanaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6ClientIndex, sleDhcp6ClientIanaIndex }
			::= { sleDhcp6ClientIanaTable 1 }

		
		SleDhcp6ClientIanaEntry ::=
			SEQUENCE { 
				sleDhcp6ClientIanaIndex
					INTEGER,
				sleDhcp6ClientIanaIaid
					INTEGER,
				sleDhcp6ClientIanaT1
					INTEGER,
				sleDhcp6ClientIanaT2
					INTEGER,
				sleDhcp6ClientIanaAddress
					OCTET STRING,
				sleDhcp6ClientIanaLifeTime
					INTEGER,
				sleDhcp6ClientIanaValidTime
					INTEGER,
				sleDhcp6ClientIanaExpireTime
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6ClientIanaIndex OBJECT-TYPE
			SYNTAX INTEGER (0..1024)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6ClientIanaIaid OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6ClientIanaT1 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6ClientIanaT2 OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6ClientIanaAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDhcp6ClientIanaLifeTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleDhcp6ClientIanaValidTime OBJECT-TYPE
			SYNTAX INTEGER (60..*********)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleDhcp6ClientIanaExpireTime OBJECT-TYPE
			SYNTAX INTEGER
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6ClientIanaEntry 8 }

		
		-- *******.4.1.6296.101.27.8
		sleDhcp6Option OBJECT IDENTIFIER ::= { sleDhcp6 8 }

		
		-- *******.4.1.6296.**********
		sleDhcp6OptionBase OBJECT IDENTIFIER ::= { sleDhcp6Option 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6OptionBaseTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6OptionBaseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBase 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6OptionBaseEntry OBJECT-TYPE
			SYNTAX SleDhcp6OptionBaseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6OptionBaseIndex }
			::= { sleDhcp6OptionBaseTable 1 }

		
		SleDhcp6OptionBaseEntry ::=
			SEQUENCE { 
				sleDhcp6OptionBaseIndex
					INTEGER,
				sleDhcp6OptionBaseName
					OCTET STRING,
				sleDhcp6OptionBaseRawDataLen
					INTEGER,
				sleDhcp6OptionBaseRawData
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6OptionBaseIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6OptionBaseName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6OptionBaseRawDataLen OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6OptionBaseRawData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseEntry 4 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6OptionBaseControl OBJECT IDENTIFIER ::= { sleDhcp6OptionBase 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6OptionBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6Option(1),
				destroyDhcp6Option(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6OptionBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6OptionBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6OptionBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6OptionBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6OptionBaseControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6OptionBaseControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionBaseControl 7 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6OptionBaseNotification OBJECT IDENTIFIER ::= { sleDhcp6OptionBase 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6OptionCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6OptionBaseControlRequest, sleDhcp6OptionBaseControlTimeStamp, sleDhcp6OptionBaseControlReqResult, sleDhcp6OptionBaseControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6OptionBaseNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6OptionDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6OptionBaseControlRequest, sleDhcp6OptionBaseControlTimeStamp, sleDhcp6OptionBaseControlReqResult, sleDhcp6OptionBaseControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6OptionBaseNotification 2 }

		
		-- *******.4.1.6296.**********
		sleDhcp6OptionAttr OBJECT IDENTIFIER ::= { sleDhcp6Option 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6OptionAttrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6OptionAttrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttr 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6OptionAttrEntry OBJECT-TYPE
			SYNTAX SleDhcp6OptionAttrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6OptionBaseIndex, sleDhcp6OptionAttrId }
			::= { sleDhcp6OptionAttrTable 1 }

		
		SleDhcp6OptionAttrEntry ::=
			SEQUENCE { 
				sleDhcp6OptionAttrId
					INTEGER,
				sleDhcp6OptionAttrType
					INTEGER,
				sleDhcp6OptionAttrLengthHidden
					INTEGER,
				sleDhcp6OptionAttrLengthType
					INTEGER,
				sleDhcp6OptionAttrLengthValue
					OCTET STRING,
				sleDhcp6OptionAttrValueType
					INTEGER,
				sleDhcp6OptionAttrValue
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6OptionAttrId OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6OptionAttrType OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6OptionAttrLengthHidden OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6OptionAttrLengthType OBJECT-TYPE
			SYNTAX INTEGER
				{
				fixed(1),
				variable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6OptionAttrLengthValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDhcp6OptionAttrValueType OBJECT-TYPE
			SYNTAX INTEGER
				{
				hex(1),
				ifipv6(2),
				index(3),
				ipv6(4),
				string(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleDhcp6OptionAttrValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrEntry 7 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6OptionAttrControl OBJECT IDENTIFIER ::= { sleDhcp6OptionAttr 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6OptionAttrControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6OptionAttr(1),
				destroyDhcp6OptionAttr(2),
				changeDhcp6OptionAttr(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6OptionAttrControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6OptionAttrControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6OptionAttrControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6OptionAttrControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6OptionAttrControlOptionIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6OptionAttrControlId OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6OptionAttrControlType OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6OptionAttrControlLengthHidden OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6OptionAttrControlLengthType OBJECT-TYPE
			SYNTAX INTEGER
				{
				fixed(1),
				variable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleDhcp6OptionAttrControlLengthValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleDhcp6OptionAttrControlValueType OBJECT-TYPE
			SYNTAX INTEGER
				{
				hex(1),
				ifipv6(2),
				index(3),
				ipv6(4),
				string(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 12 }

		
		-- *******.4.1.6296.**********.2.13
		sleDhcp6OptionAttrControlValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6OptionAttrControl 13 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6OptionAttrNotification OBJECT IDENTIFIER ::= { sleDhcp6OptionAttr 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6OptionAttrCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6OptionAttrControlRequest, sleDhcp6OptionAttrControlTimeStamp, sleDhcp6OptionAttrControlReqResult, sleDhcp6OptionAttrControlOptionName, sleDhcp6OptionAttrControlId
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6OptionAttrNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6OptionAttrDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6OptionAttrControlRequest, sleDhcp6OptionAttrControlTimeStamp, sleDhcp6OptionAttrControlReqResult, sleDhcp6OptionAttrControlOptionName, sleDhcp6OptionAttrControlId
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6OptionAttrNotification 2 }

		
		-- *******.4.1.6296.**********.3.3
		sleDhcp6OptionAttrChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6OptionAttrControlRequest, sleDhcp6OptionAttrControlTimeStamp, sleDhcp6OptionAttrControlReqResult, sleDhcp6OptionAttrControlOptionName, sleDhcp6OptionAttrControlId
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6OptionAttrNotification 3 }

		
		-- *******.4.1.6296.101.27.9
		sleDhcp6Snooping OBJECT IDENTIFIER ::= { sleDhcp6 9 }

		
		-- *******.4.1.6296.**********
		sleDhcp6SnoopBase OBJECT IDENTIFIER ::= { sleDhcp6Snooping 1 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6SnoopBaseInfo OBJECT IDENTIFIER ::= { sleDhcp6SnoopBase 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6SnoopBaseState OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseInfo 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleDhcp6SnoopBaseNdInspTime OBJECT-TYPE
			SYNTAX INTEGER (1..2147483637)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseInfo 2 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6SnoopBaseControl OBJECT IDENTIFIER ::= { sleDhcp6SnoopBase 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6SnoopBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setDhcp6SnoopState(1),
				setDhcp6SnoopNdInspTime(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6SnoopBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6SnoopBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6SnoopBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6SnoopBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6SnoopBaseControlState OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6SnoopBaseControlNdInspTime OBJECT-TYPE
			SYNTAX INTEGER (1..2147483637)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBaseControl 7 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6SnoopBaseNotification OBJECT IDENTIFIER ::= { sleDhcp6SnoopBase 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6SnoopBaseStateChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopBaseControlRequest, sleDhcp6SnoopBaseControlTimeStamp, sleDhcp6SnoopBaseControlReqResult, sleDhcp6SnoopBaseControlState }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopBaseNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6SnoopBaseNdInspTimeChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopBaseControlRequest, sleDhcp6SnoopBaseControlTimeStamp, sleDhcp6SnoopBaseControlReqResult, sleDhcp6SnoopBaseControlNdInspTime }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopBaseNotification 2 }

		
		-- *******.4.1.6296.**********
		sleDhcp6SnoopVlan OBJECT IDENTIFIER ::= { sleDhcp6Snooping 2 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6SnoopVlanTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6SnoopVlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlan 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6SnoopVlanEntry OBJECT-TYPE
			SYNTAX SleDhcp6SnoopVlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6SnoopVlanIndex }
			::= { sleDhcp6SnoopVlanTable 1 }

		
		SleDhcp6SnoopVlanEntry ::=
			SEQUENCE { 
				sleDhcp6SnoopVlanIndex
					INTEGER,
				sleDhcp6SnoopVlanState
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6SnoopVlanIndex OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6SnoopVlanState OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanEntry 2 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6SnoopVlanControl OBJECT IDENTIFIER ::= { sleDhcp6SnoopVlan 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6SnoopVlanControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setDhcp6SnoopVlanState(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6SnoopVlanControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6SnoopVlanControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6SnoopVlanControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6SnoopVlanControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6SnoopVlanControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6SnoopVlanControlState OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopVlanControl 7 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6SnoopVlanNotification OBJECT IDENTIFIER ::= { sleDhcp6SnoopVlan 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6SnoopVlanStateChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopVlanControlRequest, sleDhcp6SnoopVlanControlTimeStamp, sleDhcp6SnoopVlanControlReqResult, sleDhcp6SnoopVlanControlIndex, sleDhcp6SnoopVlanControlState
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopVlanNotification 1 }

		
		-- *******.4.1.6296.**********
		sleDhcp6SnoopPort OBJECT IDENTIFIER ::= { sleDhcp6Snooping 3 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6SnoopPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6SnoopPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPort 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6SnoopPortEntry OBJECT-TYPE
			SYNTAX SleDhcp6SnoopPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6SnoopPortIndex }
			::= { sleDhcp6SnoopPortTable 1 }

		
		SleDhcp6SnoopPortEntry ::=
			SEQUENCE { 
				sleDhcp6SnoopPortIndex
					INTEGER,
				sleDhcp6SnoopPortTrusted
					INTEGER,
				sleDhcp6SnoopPortLimitRate
					INTEGER,
				sleDhcp6SnoopPortLimitLease
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6SnoopPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6SnoopPortTrusted OBJECT-TYPE
			SYNTAX INTEGER
				{
				trust(1),
				untrust(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6SnoopPortLimitRate OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6SnoopPortLimitLease OBJECT-TYPE
			SYNTAX INTEGER (1..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortEntry 4 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6SnoopPortControl OBJECT IDENTIFIER ::= { sleDhcp6SnoopPort 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6SnoopPortControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setDhcp6SnoopPortTrust(1),
				setDhcp6SnoopPortLimitRate(2),
				setDhcp6SnoopPortLimitLease(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6SnoopPortControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6SnoopPortControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6SnoopPortControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6SnoopPortControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6SnoopPortControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6SnoopPortControlTrusted OBJECT-TYPE
			SYNTAX INTEGER
				{
				trust(1),
				untrust(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6SnoopPortControlLimitRate OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6SnoopPortControlLimitLease OBJECT-TYPE
			SYNTAX INTEGER (1..2147483637)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopPortControl 9 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6SnoopPortNotification OBJECT IDENTIFIER ::= { sleDhcp6SnoopPort 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6SnoopPortTrustChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopPortControlRequest, sleDhcp6SnoopPortControlTimeStamp, sleDhcp6SnoopPortControlReqResult, sleDhcp6SnoopPortControlIndex, sleDhcp6SnoopPortControlTrusted
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopPortNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6SnoopPortLimitRateChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopPortControlRequest, sleDhcp6SnoopPortControlTimeStamp, sleDhcp6SnoopPortControlReqResult, sleDhcp6SnoopPortControlIndex, sleDhcp6SnoopPortControlLimitRate
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopPortNotification 2 }

		
		-- *******.4.1.6296.**********.3.3
		sleDhcp6SnoopPortLimitLeaseChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopPortControlRequest, sleDhcp6SnoopPortControlTimeStamp, sleDhcp6SnoopPortControlReqResult, sleDhcp6SnoopPortControlIndex, sleDhcp6SnoopPortControlLimitLease
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopPortNotification 3 }

		
		-- *******.4.1.6296.**********
		sleDhcp6SnoopBind OBJECT IDENTIFIER ::= { sleDhcp6Snooping 4 }

		
		-- *******.4.1.6296.**********.1
		sleDhcp6SnoopBindTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6SnoopBindEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBind 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleDhcp6SnoopBindEntry OBJECT-TYPE
			SYNTAX SleDhcp6SnoopBindEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6SnoopBindIndex }
			::= { sleDhcp6SnoopBindTable 1 }

		
		SleDhcp6SnoopBindEntry ::=
			SEQUENCE { 
				sleDhcp6SnoopBindIndex
					INTEGER,
				sleDhcp6SnoopBindPortIndex
					INTEGER,
				sleDhcp6SnoopBindAddress
					OCTET STRING,
				sleDhcp6SnoopBindVlan
					INTEGER,
				sleDhcp6SnoopBindMac
					MacAddress,
				sleDhcp6SnoopBindValTime
					INTEGER,
				sleDhcp6SnoopBindState
					INTEGER,
				sleDhcp6SnoopBindType
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		sleDhcp6SnoopBindIndex OBJECT-TYPE
			SYNTAX INTEGER (0..1024)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		sleDhcp6SnoopBindPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		sleDhcp6SnoopBindAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		sleDhcp6SnoopBindVlan OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 4 }

		
		-- *******.4.1.6296.**********.1.1.5
		sleDhcp6SnoopBindMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 5 }

		
		-- *******.4.1.6296.**********.1.1.6
		sleDhcp6SnoopBindValTime OBJECT-TYPE
			SYNTAX INTEGER (120..2147483637)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 6 }

		
		-- *******.4.1.6296.**********.1.1.7
		sleDhcp6SnoopBindState OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleDhcp6SnoopBindType OBJECT-TYPE
			SYNTAX INTEGER
				{
				dynamic(1),
				static(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindEntry 8 }

		
		-- *******.4.1.6296.**********.2
		sleDhcp6SnoopBindControl OBJECT IDENTIFIER ::= { sleDhcp6SnoopBind 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleDhcp6SnoopBindControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6SnoopBind(1),
				destroyDhcp6SnoopBind(2),
				clearDhcp6SnoopBind(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		sleDhcp6SnoopBindControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		sleDhcp6SnoopBindControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		sleDhcp6SnoopBindControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		sleDhcp6SnoopBindControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		sleDhcp6SnoopBindControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		sleDhcp6SnoopBindControlPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		sleDhcp6SnoopBindControlAddress OBJECT-TYPE
			SYNTAX InetAddressIPv6
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 8 }

		
		-- *******.4.1.6296.**********.2.9
		sleDhcp6SnoopBindControlVlan OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 9 }

		
		-- *******.4.1.6296.**********.2.10
		sleDhcp6SnoopBindControlMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleDhcp6SnoopBindControlValTime OBJECT-TYPE
			SYNTAX INTEGER (120..2147483637)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleDhcp6SnoopBindControlState OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 12 }

		
		-- *******.4.1.6296.**********.2.13
		sleDhcp6SnoopBindControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				dynamic(1),
				static(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6SnoopBindControl 13 }

		
		-- *******.4.1.6296.**********.3
		sleDhcp6SnoopBindNotification OBJECT IDENTIFIER ::= { sleDhcp6SnoopBind 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleDhcp6SnoopBindCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopBindControlRequest, sleDhcp6SnoopBindControlTimeStamp, sleDhcp6SnoopBindControlReqResult, sleDhcp6SnoopBindControlPortIndex, sleDhcp6SnoopBindControlAddress, 
				sleDhcp6SnoopBindControlVlan, sleDhcp6SnoopBindControlMac, sleDhcp6SnoopBindControlValTime }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopBindNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		sleDhcp6SnoopBindDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopBindControlRequest, sleDhcp6SnoopBindControlTimeStamp, sleDhcp6SnoopBindControlReqResult, sleDhcp6SnoopBindControlPortIndex, sleDhcp6SnoopBindControlAddress
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopBindNotification 2 }

		
		-- *******.4.1.6296.**********.3.3
		sleDhcp6SnoopBindCleared NOTIFICATION-TYPE
			OBJECTS { sleDhcp6SnoopBindControlRequest, sleDhcp6SnoopBindControlTimeStamp, sleDhcp6SnoopBindControlReqResult, sleDhcp6SnoopBindControlPortIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6SnoopBindNotification 3 }

		
		-- *******.4.1.6296.101.27.10
		sleDhcp6Aaa OBJECT IDENTIFIER ::= { sleDhcp6 10 }

		
		-- *******.4.1.6296.***********
		sleDhcp6AaaTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleDhcp6AaaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6Aaa 1 }

		
		-- *******.4.1.6296.***********.1
		sleDhcp6AaaEntry OBJECT-TYPE
			SYNTAX SleDhcp6AaaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleDhcp6AaaIndex }
			::= { sleDhcp6AaaTable 1 }

		
		SleDhcp6AaaEntry ::=
			SEQUENCE { 
				sleDhcp6AaaIndex
					INTEGER,
				sleDhcp6AaaModelName
					OCTET STRING,
				sleDhcp6AaaServerType
					InetAddressType,
				sleDhcp6AaaServerAddr
					InetAddress,
				sleDhcp6AaaRadiusKey
					OCTET STRING,
				sleDhcp6AaaRadiusUser
					OCTET STRING,
				sleDhcp6AaaRadiusPasswd
					OCTET STRING,
				sleDhcp6AaaRadiusRetry
					INTEGER,
				sleDhcp6AaaRadiusTimeout
					INTEGER,
				sleDhcp6AaaRadiusPort
					INTEGER,
				sleDhcp6AaaRadiusInterface
					OCTET STRING
			 }

		-- *******.4.1.6296.***********.1.1
		sleDhcp6AaaIndex OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Index of AAA model"
			::= { sleDhcp6AaaEntry 1 }

		
		-- *******.4.1.6296.***********.1.2
		sleDhcp6AaaModelName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The aaa model name"
			::= { sleDhcp6AaaEntry 2 }

		
		-- *******.4.1.6296.***********.1.3
		sleDhcp6AaaServerType OBJECT-TYPE
			SYNTAX InetAddressType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The inet address type of aaa server"
			::= { sleDhcp6AaaEntry 3 }

		
		-- *******.4.1.6296.***********.1.4
		sleDhcp6AaaServerAddr OBJECT-TYPE
			SYNTAX InetAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The address of aaa server"
			::= { sleDhcp6AaaEntry 4 }

		
		-- *******.4.1.6296.***********.1.5
		sleDhcp6AaaRadiusKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Radius key value"
			::= { sleDhcp6AaaEntry 5 }

		
		-- *******.4.1.6296.***********.1.6
		sleDhcp6AaaRadiusUser OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Radius user name"
			::= { sleDhcp6AaaEntry 6 }

		
		-- *******.4.1.6296.***********.1.7
		sleDhcp6AaaRadiusPasswd OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Radius user password"
			::= { sleDhcp6AaaEntry 7 }

		
		-- *******.4.1.6296.***********.1.8
		sleDhcp6AaaRadiusRetry OBJECT-TYPE
			SYNTAX INTEGER (1..10)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Retry count"
			::= { sleDhcp6AaaEntry 8 }

		
		-- *******.4.1.6296.***********.1.9
		sleDhcp6AaaRadiusTimeout OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Timeout value"
			::= { sleDhcp6AaaEntry 9 }

		
		-- *******.4.1.6296.***********.1.10
		sleDhcp6AaaRadiusPort OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Radius port number"
			::= { sleDhcp6AaaEntry 10 }

		
		-- *******.4.1.6296.***********.1.11
		sleDhcp6AaaRadiusInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface name for aaa service"
			::= { sleDhcp6AaaEntry 11 }

		
		-- *******.4.1.6296.***********
		sleDhcp6AaaControl OBJECT IDENTIFIER ::= { sleDhcp6Aaa 2 }

		
		-- *******.4.1.6296.***********.1
		sleDhcp6AaaControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createDhcp6Aaa(1),
				deleteDhcp6Aaa(2),
				changeDhcp6AaaProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleDhcp6AaaControl 1 }

		
		-- *******.4.1.6296.***********.2
		sleDhcp6AaaControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleDhcp6AaaControl 2 }

		
		-- *******.4.1.6296.***********.3
		sleDhcp6AaaControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleDhcp6AaaControl 3 }

		
		-- *******.4.1.6296.***********.4
		sleDhcp6AaaControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time stamp of the last command"
			::= { sleDhcp6AaaControl 4 }

		
		-- *******.4.1.6296.***********.5
		sleDhcp6AaaControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleDhcp6AaaControl 5 }

		
		-- *******.4.1.6296.***********.6
		sleDhcp6AaaControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 6 }

		
		-- *******.4.1.6296.***********.7
		sleDhcp6AaaControlModelName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 7 }

		
		-- *******.4.1.6296.***********.8
		sleDhcp6AaaControlServerAddr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 8 }

		
		-- *******.4.1.6296.***********.9
		sleDhcp6AaaControlRadiusKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 9 }

		
		-- *******.4.1.6296.***********.10
		sleDhcp6AaaControlRadiusUser OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 10 }

		
		-- *******.4.1.6296.***********.11
		sleDhcp6AaaControlRadiusPasswd OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 11 }

		
		-- *******.4.1.6296.***********.12
		sleDhcp6AaaControlRadiusRetry OBJECT-TYPE
			SYNTAX INTEGER (1..10)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 12 }

		
		-- *******.4.1.6296.***********.13
		sleDhcp6AaaControlRadiusTimeout OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 13 }

		
		-- *******.4.1.6296.***********.14
		sleDhcp6AaaControlRadiusPort OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 14 }

		
		-- *******.4.1.6296.***********.15
		sleDhcp6AaaControlRadiusInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDhcp6AaaControl 15 }

		
		-- *******.4.1.6296.***********
		sleDhcp6AaaNotification OBJECT IDENTIFIER ::= { sleDhcp6Aaa 3 }

		
		-- *******.4.1.6296.***********.1
		sleDhcp6AaaCreated NOTIFICATION-TYPE
			OBJECTS { sleDhcp6AaaControlRequest, sleDhcp6AaaControlTimeStamp, sleDhcp6AaaControlReqResult, sleDhcp6AaaModelName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6AaaNotification 1 }

		
		-- *******.4.1.6296.***********.2
		sleDhcp6AaaDestroyed NOTIFICATION-TYPE
			OBJECTS { sleDhcp6AaaControlRequest, sleDhcp6AaaControlTimeStamp, sleDhcp6AaaControlReqResult, sleDhcp6AaaControlModelName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6AaaNotification 2 }

		
		-- *******.4.1.6296.***********.3
		sleDhcp6AaaProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleDhcp6AaaControlRequest, sleDhcp6AaaControlTimeStamp, sleDhcp6AaaControlReqResult, sleDhcp6AaaModelName, sleDhcp6AaaServerType, 
				sleDhcp6AaaServerAddr, sleDhcp6AaaRadiusKey, sleDhcp6AaaRadiusUser, sleDhcp6AaaRadiusPasswd, sleDhcp6AaaRadiusRetry, 
				sleDhcp6AaaRadiusTimeout, sleDhcp6AaaRadiusPort, sleDhcp6AaaRadiusInterface }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6AaaNotification 3 }

		
		-- *******.4.1.6296.101.27.11
		sleDhcp6Group OBJECT-GROUP
			OBJECTS { sleDhcp6Duid, sleDhcp6DatabaseAddr, sleDhcp6DatabaseInterval, sleDhcp6BaseControlRequest, sleDhcp6BaseControlStatus, 
				sleDhcp6BaseControlTimer, sleDhcp6BaseControlTimeStamp, sleDhcp6BaseControlReqResult, sleDhcp6BaseControlDatabaseAddr, sleDhcp6BaseControlDatabaseInterval, 
				sleDhcp6PoolName, sleDhcp6PoolStaticEntry, sleDhcp6PoolDynamicEntry, sleDhcp6PoolImportDns, sleDhcp6PoolImportDomain, 
				sleDhcp6PoolImportInfoRef, sleDhcp6PoolImportNisAdd, sleDhcp6PoolImportNisDom, sleDhcp6PoolImportNispAdd, sleDhcp6PoolImportNispDom, 
				sleDhcp6PoolImportSipAdd, sleDhcp6PoolImportSipDom, sleDhcp6PoolImportSntpAdd, sleDhcp6PoolInfoRefTime, sleDhcp6PoolAaaValTime, 
				sleDhcp6PoolAaaPreTime, sleDhcp6PoolPdName, sleDhcp6PoolPdValTime, sleDhcp6PoolPdPreTime, sleDhcp6PoolBaseControlRequest, 
				sleDhcp6PoolBaseControlStatus, sleDhcp6PoolBaseControlTimer, sleDhcp6PoolBaseControlTimeStamp, sleDhcp6PoolBaseControlReqResult, sleDhcp6PoolBaseControlName, 
				sleDhcp6PoolBaseControlImportDns, sleDhcp6PoolBaseControlImportDomain, sleDhcp6PoolBaseControlImportInfoRef, sleDhcp6PoolBaseControlImportNisAdd, sleDhcp6PoolBaseControlImportNisDom, 
				sleDhcp6PoolBaseControlImportNispAdd, sleDhcp6PoolBaseControlImportNispDom, sleDhcp6PoolBaseControlImportSipAdd, sleDhcp6PoolBaseControlImportSipDom, sleDhcp6PoolBaseControlImportSntpAddr, 
				sleDhcp6PoolBaseControlInfoRefTime, sleDhcp6PoolBaseControlAaaValTime, sleDhcp6PoolBaseControlAaaPreTime, sleDhcp6PoolBaseControlPdName, sleDhcp6PoolBaseControlPdValTime, 
				sleDhcp6PoolBaseControlPdPreTime, sleDhcp6FixedAddrAddress, sleDhcp6FixedAddrDuid, sleDhcp6FixedAddrValTime, sleDhcp6FixedAddrPreTime, 
				sleDhcp6FixedAddrControlRequest, sleDhcp6FixedAddrControlStatus, sleDhcp6FixedAddrControlTimer, sleDhcp6FixedAddrControlTimeStamp, sleDhcp6FixedAddrControlReqResult, 
				sleDhcp6FixedAddrControlAddress, sleDhcp6FixedAddrControlDuid, sleDhcp6FixedAddrControlValTime, sleDhcp6FixedAddrControlPreTime, sleDhcp6ServerOptionCode, 
				sleDhcp6ServerOptionType, sleDhcp6ServerOptionValue, sleDhcp6ServerOptionControlRequest, sleDhcp6ServerOptionControlStatus, sleDhcp6ServerOptionControlTimer, 
				sleDhcp6ServerOptionControlTimeStamp, sleDhcp6ServerOptionControlReqResult, sleDhcp6ServerOptionControlCode, sleDhcp6ServerOptionControlType, sleDhcp6ServerOptionControlValue, 
				sleDhcp6RangeStart, sleDhcp6RangeEnd, sleDhcp6RangeValTime, sleDhcp6RangePreTime, sleDhcp6RangeControlRequest, 
				sleDhcp6RangeControlStatus, sleDhcp6RangeControlTimer, sleDhcp6RangeControlTimeStamp, sleDhcp6RangeControlReqResult, sleDhcp6RangeControlStart, 
				sleDhcp6RangeControlEnd, sleDhcp6RangeControlValTime, sleDhcp6RangeControlPreTime, sleDhcp6FixedPrefixValue, sleDhcp6FixedPrefixLen, 
				sleDhcp6FixedPrefixDuid, sleDhcp6FixedPrefixValTime, sleDhcp6FixedPrefixPreTime, sleDhcp6FixedPrefixControlRequest, sleDhcp6FixedPrefixControlStatus, 
				sleDhcp6FixedPrefixControlTimer, sleDhcp6FixedPrefixControlTimeStamp, sleDhcp6FixedPrefixControlReqResult, sleDhcp6FixedPrefixControlValue, sleDhcp6FixedPrefixControlLen, 
				sleDhcp6FixedPrefixControlDuid, sleDhcp6FixedPrefixControlValTime, sleDhcp6FixedPrefixControlPreTime, sleDhcp6BindBaseLinkLocal, sleDhcp6BindBaseDuid, 
				sleDhcp6BindBaseIaType, sleDhcp6BindBaseControlRequest, sleDhcp6BindBaseControlStatus, sleDhcp6BindBaseControlTimer, sleDhcp6BindBaseControlTimeStamp, 
				sleDhcp6BindBaseControlReqResult, sleDhcp6BindPdPrefix, sleDhcp6BindPdPrefixLen, sleDhcp6BindPdExpireTime, sleDhcp6BindNaAddress, 
				sleDhcp6BindNaExpireTime, sleDhcp6LocalPoolName, sleDhcp6LocalPoolPrefix, sleDhcp6LocalPoolPrefixLen, sleDhcp6LocalPoolAssignLen, 
				sleDhcp6LocalPoolUsedCnt, sleDhcp6LocalPoolAvailCnt, sleDhcp6LocalPoolControlRequest, sleDhcp6LocalPoolControlStatus, sleDhcp6LocalPoolControlTimer, 
				sleDhcp6LocalPoolControlTimeStamp, sleDhcp6LocalPoolControlReqResult, sleDhcp6LocalPoolControlName, sleDhcp6LocalPoolControlPrefix, sleDhcp6LocalPoolControlPrefixLen, 
				sleDhcp6LocalPoolControlAssignLen, sleDhcp6ServerIfName, sleDhcp6ServerPoolName, sleDhcp6ServerPreference, sleDhcp6ServerRapidCommit, 
				sleDhcp6ServerControlRequest, sleDhcp6ServerControlStatus, sleDhcp6ServerControlTimer, sleDhcp6ServerControlTimeStamp, sleDhcp6ServerControlReqResult, 
				sleDhcp6ServerControlIfName, sleDhcp6ServerControlPoolName, sleDhcp6ServerControlPreference, sleDhcp6ServerControlRapidCommit, sleDhcp6RelayIfName, 
				sleDhcp6RelayDestAddr, sleDhcp6RelayOutputIfname, sleDhcp6RelayControlRequest, sleDhcp6RelayControlStatus, sleDhcp6RelayControlTimer, 
				sleDhcp6RelayControlTimeStamp, sleDhcp6RelayControlReqResult, sleDhcp6RelayControlIfName, sleDhcp6RelayControlDestAddr, sleDhcp6RelayControlOutputIfname, 
				sleDhcp6ClientIfName, sleDhcp6ClientState, sleDhcp6ClientIanaIaid, sleDhcp6ClientIanaT1, sleDhcp6ClientIanaT2, 
				sleDhcp6ClientIanaAddress, sleDhcp6ClientIanaLifeTime, sleDhcp6ClientIanaValidTime, sleDhcp6ClientIanaExpireTime, sleDhcp6ClientBaseControlRequest, 
				sleDhcp6ClientBaseControlStatus, sleDhcp6ClientBaseControlTimer, sleDhcp6ClientBaseControlTimeStamp, sleDhcp6ClientBaseControlReqResult, sleDhcp6ClientBaseControlIfName, 
				sleDhcp6ClientBaseControlStatelessFlag, sleDhcp6ClientBaseControlNaFlag, sleDhcp6ClientBaseControlPdFlag, sleDhcp6ClientBaseControlPdHint, sleDhcp6ClientBaseControlIanaRapidCommit, 
				sleDhcp6ClientBaseControlIapdRapidCommit, sleDhcp6ClientBaseControlRefreshMinimumVal, sleDhcp6ClientOptionCode, sleDhcp6ClientOptionType, sleDhcp6ClientOptionValue, 
				sleDhcp6ClientIapdIaid, sleDhcp6ClientIapdT1, sleDhcp6ClientIapdT2, sleDhcp6ClientIapdPrefix, sleDhcp6ClientIapdPrefixLen, 
				sleDhcp6ClientIapdLifeTime, sleDhcp6ClientIapdValidTime, sleDhcp6ClientIapdExpireTime, sleDhcp6OptionBaseName, sleDhcp6OptionBaseControlRequest, 
				sleDhcp6OptionBaseControlStatus, sleDhcp6OptionBaseControlTimer, sleDhcp6OptionBaseControlTimeStamp, sleDhcp6OptionBaseControlReqResult, sleDhcp6OptionBaseControlName, 
				sleDhcp6OptionAttrId, sleDhcp6OptionAttrType, sleDhcp6OptionAttrLengthHidden, sleDhcp6OptionAttrLengthType, sleDhcp6OptionAttrLengthValue, 
				sleDhcp6OptionAttrValueType, sleDhcp6OptionAttrValue, sleDhcp6OptionAttrControlRequest, sleDhcp6OptionAttrControlStatus, sleDhcp6OptionAttrControlTimer, 
				sleDhcp6OptionAttrControlTimeStamp, sleDhcp6OptionAttrControlReqResult, sleDhcp6OptionAttrControlId, sleDhcp6OptionAttrControlType, sleDhcp6OptionAttrControlLengthHidden, 
				sleDhcp6OptionAttrControlLengthType, sleDhcp6OptionAttrControlLengthValue, sleDhcp6OptionAttrControlValueType, sleDhcp6OptionAttrControlValue, sleDhcp6SnoopBaseState, 
				sleDhcp6SnoopBaseNdInspTime, sleDhcp6SnoopBaseControlRequest, sleDhcp6SnoopBaseControlStatus, sleDhcp6SnoopBaseControlTimer, sleDhcp6SnoopBaseControlTimeStamp, 
				sleDhcp6SnoopBaseControlReqResult, sleDhcp6SnoopBaseControlState, sleDhcp6SnoopBaseControlNdInspTime, sleDhcp6SnoopVlanIndex, sleDhcp6SnoopVlanState, 
				sleDhcp6SnoopVlanControlRequest, sleDhcp6SnoopVlanControlStatus, sleDhcp6SnoopVlanControlTimer, sleDhcp6SnoopVlanControlTimeStamp, sleDhcp6SnoopVlanControlReqResult, 
				sleDhcp6SnoopVlanControlIndex, sleDhcp6SnoopVlanControlState, sleDhcp6SnoopPortIndex, sleDhcp6SnoopPortTrusted, sleDhcp6SnoopPortLimitRate, 
				sleDhcp6SnoopPortLimitLease, sleDhcp6SnoopPortControlRequest, sleDhcp6SnoopPortControlStatus, sleDhcp6SnoopPortControlTimer, sleDhcp6SnoopPortControlTimeStamp, 
				sleDhcp6SnoopPortControlReqResult, sleDhcp6SnoopPortControlIndex, sleDhcp6SnoopPortControlTrusted, sleDhcp6SnoopPortControlLimitRate, sleDhcp6SnoopPortControlLimitLease, 
				sleDhcp6SnoopBindPortIndex, sleDhcp6SnoopBindAddress, sleDhcp6SnoopBindVlan, sleDhcp6SnoopBindMac, sleDhcp6SnoopBindValTime, 
				sleDhcp6SnoopBindState, sleDhcp6SnoopBindType, sleDhcp6SnoopBindControlRequest, sleDhcp6SnoopBindControlStatus, sleDhcp6SnoopBindControlTimer, 
				sleDhcp6SnoopBindControlTimeStamp, sleDhcp6SnoopBindControlReqResult, sleDhcp6SnoopBindControlPortIndex, sleDhcp6SnoopBindControlAddress, sleDhcp6SnoopBindControlVlan, 
				sleDhcp6SnoopBindControlMac, sleDhcp6SnoopBindControlValTime, sleDhcp6SnoopBindControlState, sleDhcp6SnoopBindControlType, sleDhcp6ClientIapdIndex, 
				sleDhcp6SnoopBindControlIndex, sleDhcp6OptionBaseIndex, sleDhcp6OptionBaseControlIndex, sleDhcp6OptionAttrControlOptionIndex, sleDhcp6SnoopBindIndex, 
				sleDhcp6ServerControlIndex, sleDhcp6RelayIndex, sleDhcp6RelayControlIndex, sleDhcp6ClientIndex, sleDhcp6ClientBaseControlIndex, 
				sleDhcp6BindBaseIndex, sleDhcp6BindPdIndex, sleDhcp6BindNaIndex, sleDhcp6LocalPoolIndex, sleDhcp6LocalPoolControlIndex, 
				sleDhcp6RangeControlIndex, sleDhcp6FixedPrefixIndex, sleDhcp6FixedPrefixControlPoolIndex, sleDhcp6FixedPrefixControlIndex, sleDhcp6FixedAddrControlIndex, 
				sleDhcp6ServerOptionControlPoolIndex, sleDhcp6RangeIndex, sleDhcp6ServerOptionIndex, sleDhcp6ServerOptionControlIndex, sleDhcp6BindBaseIaid, 
				sleDhcp6OptionBaseRawDataLen, sleDhcp6OptionBaseRawData, sleDhcp6RangeControlPoolIndex, sleDhcp6PoolIndex, sleDhcp6PoolBaseControlIndex, 
				sleDhcp6FixedAddrIndex, sleDhcp6FixedAddrControlPoolIndex, sleDhcp6ServerIndex, sleDhcp6ClientServerAddr, sleDhcp6ClientServerDuid, 
				sleDhcp6ClientServerPref, sleDhcp6ClientRapidCommit, sleDhcp6ClientInfoRefTime, sleDhcp6AaaIndex, sleDhcp6AaaModelName, 
				sleDhcp6AaaServerType, sleDhcp6AaaServerAddr, sleDhcp6AaaRadiusKey, sleDhcp6AaaRadiusUser, sleDhcp6AaaRadiusPasswd, 
				sleDhcp6AaaRadiusRetry, sleDhcp6AaaRadiusTimeout, sleDhcp6AaaRadiusPort, sleDhcp6AaaRadiusInterface, sleDhcp6AaaControlRequest, 
				sleDhcp6AaaControlStatus, sleDhcp6AaaControlTimer, sleDhcp6AaaControlTimeStamp, sleDhcp6AaaControlReqResult, sleDhcp6AaaControlIndex, 
				sleDhcp6AaaControlModelName, sleDhcp6AaaControlServerAddr, sleDhcp6AaaControlRadiusKey, sleDhcp6AaaControlRadiusUser, sleDhcp6AaaControlRadiusPasswd, 
				sleDhcp6AaaControlRadiusRetry, sleDhcp6AaaControlRadiusTimeout, sleDhcp6AaaControlRadiusPort, sleDhcp6AaaControlRadiusInterface, sleDhcp6ClientIanaIndex, 
				sleDhcp6ClientPdname, sleDhcp6ClientBaseControlPdname }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6 11 }

		
		-- *******.4.1.6296.101.27.12
		sleDhcp6NotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleDhcp6DatabaseCreated, sleDhcp6DatabaseDestroyed, sleDhcp6DatabaseChanged, sleDhcp6PoolCreated, sleDhcp6PoolDestroyed, 
				sleDhcp6PoolAaaTimeCreated, sleDhcp6PoolAaaTimeDestroyed, sleDhcp6PdPoolCreated, sleDhcp6PdPoolDestroyed, sleDhcp6FixedAddrCreated, 
				sleDhcp6FixedAddrDestroyed, sleDhcp6ServerOptionCreated, sleDhcp6ServerOptionDestroyed, sleDhcp6RangeCreated, sleDhcp6RangeDestroyed, 
				sleDhcp6FixedPrefixCreated, sleDhcp6FixedPrefixDestroyed, sleDhcp6BindingCleared, sleDhcp6LocalPoolCreated, sleDhcp6LocalPoolDestroyed, 
				sleDhcp6ServerCreated, sleDhcp6ServerDestroyed, sleDhcp6RelayCreated, sleDhcp6RelayDestroyed, sleDhcp6ClientStatelessSet, 
				sleDhcp6ClientNaSet, sleDhcp6ClientPdSet, sleDhcp6ClientCleared, sleDhcp6ClientRefreshMinChanged, sleDhcp6OptionCreated, 
				sleDhcp6OptionDestroyed, sleDhcp6OptionAttrCreated, sleDhcp6OptionAttrDestroyed, sleDhcp6OptionAttrChanged, sleDhcp6SnoopBaseStateChanged, 
				sleDhcp6SnoopBaseNdInspTimeChanged, sleDhcp6SnoopVlanStateChanged, sleDhcp6SnoopPortTrustChanged, sleDhcp6SnoopPortLimitRateChanged, sleDhcp6SnoopPortLimitLeaseChanged, 
				sleDhcp6SnoopBindCreated, sleDhcp6SnoopBindDestroyed, sleDhcp6AaaCreated, sleDhcp6AaaDestroyed, sleDhcp6AaaProfileChanged, 
				sleDhcp6SnoopBindCleared, sleDhcp6ImportSet }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDhcp6 12 }

		
	
	END

--
-- sle-dhcpv6-mib.mib
--
