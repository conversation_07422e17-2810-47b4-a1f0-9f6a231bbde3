DASAN-USER-MANAGEMENT-MIB DEFINITIONS ::= BEGIN

IMPORTS
	MODULE-IDENTITY, OBJECT-TYPE, Counter32, <PERSON><PERSON><PERSON>32, Counter64, Integer32, TimeTicks, mib-2, NOTIFICATION-TYPE FROM SNMPv2-SMI
	TEXTUAL-<PERSON>N<PERSON><PERSON><PERSON>, DisplayString, PhysAddress, TruthValue, RowStatus, TimeStamp, AutonomousType, TestAndIncr FROM SNMPv2-TC
	MODULE-COMPLIANCE, OBJECT-GROUP        FROM SNMPv2-CONF 
	--NetworkAddress, IpAddress  FROM RFC1155-SMI 
	dasanMgmt       FROM DASAN-SMI
	dsSwitchModules FROM DASAN-SWITCH-MIB;


-- Definition Grammer

dsUserManagementMIB MODULE-IDENTITY
    	LAST-UPDATED	"200601250000Z"
    	ORGANIZATION	"DASAN Co., Ltd."
    	CONTACT-INFO	"DASAN Co., Ltd."
    	DESCRIPTION     "."
    ::= { dsSwitchModules 14 }     
    

--
-- Info Tables
--
 
dsUserLoginInfo OBJECT IDENTIFIER ::= { dsUserManagementMIB 1 }

--
-- Base ID
--
dsUserLoginTable OBJECT-TYPE
		SYNTAX		SEQUENCE OF DsUserLoginEntry
		MAX-ACCESS	not-accessible
		STATUS		current
		DESCRIPTION	"."
		::= { dsUserLoginInfo 1 }

dsUserLoginEntry	OBJECT-TYPE
		SYNTAX		DsUserLoginEntry
		MAX-ACCESS	not-accessible
		STATUS		current
		DESCRIPTION	"."
		INDEX		{dsUserLoginIndex}
		::= { dsUserLoginTable 1 }        
		


DsUserLoginEntry ::= SEQUENCE {
		dsUserLoginIndex     		  INTEGER,
		dsUserLoginName				  OCTET STRING,
		dsUserLoginTty				  OCTET STRING,
		dsUserLoginIpAddress	      IpAddress,
		dsUserLoginType				  INTEGER
	}

dsUserLoginIndex OBJECT-TYPE
       SYNTAX	   INTEGER
       MAX-ACCESS  read-only  
       STATUS      current
       DESCRIPTION
               "The index of user-login-table."
       ::= { dsUserLoginEntry 1 }

dsUserLoginName OBJECT-TYPE
       SYNTAX	   OCTET STRING
       MAX-ACCESS  read-only  
       STATUS      current
       DESCRIPTION
               "The user name."
       ::= { dsUserLoginEntry 2 }

dsUserLoginTty OBJECT-TYPE
       SYNTAX	   OCTET STRING
       MAX-ACCESS  read-only  
       STATUS      current
       DESCRIPTION
               "The tty name."
       ::= { dsUserLoginEntry 3 }

dsUserLoginIpAddress OBJECT-TYPE
       SYNTAX	   IpAddress
       MAX-ACCESS  read-only  
       STATUS      current
       DESCRIPTION
               "The ip address of user."
       ::= { dsUserLoginEntry 4 }                          
 
dsUserLoginType OBJECT-TYPE
       SYNTAX	   INTEGER {
                      local(0),
                      telnet(1),
                      ssh(2)
                   }
       MAX-ACCESS  read-only  
       STATUS      current
       DESCRIPTION
               "The type of connection."
       ::= { dsUserLoginEntry 5 }  
 
END
