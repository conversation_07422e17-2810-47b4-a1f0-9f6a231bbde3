--
-- sle-mpls-tp-bfd-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, January 12, 2016 at 17:14:36
--

	SLE-MPLS-TP-BFD-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			IANAbfdDiagTC			
				FROM IANA-BFD-TC-STD-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			mib-2, TimeTicks, Unsigned32, Gauge32, OBJECT-TYPE, 
			MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpBfd MODULE-IDENTITY 
			LAST-UPDATED "201510070000Z"		-- October 07, 2015 at 00:00 GMT
			ORGANIZATION 
				"Dasan Networks"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				<PERSON><PERSON><PERSON> (<PERSON>)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"Bidirectional Forwarding Management Information Base."
			REVISION "200406030000Z"		-- June 03, 2004 at 00:00 GMT
			DESCRIPTION 
				"Initial version issued as part of RFC 3812."
			::= { sleMpls 19 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MPLS."
			::= { sleMgmt 16 }

		
		sleMplsTpBfdCfg OBJECT IDENTIFIER ::= { sleMplsTpBfd 1 }

		
		sleMplsTpBfdCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleBfdCfgInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The BFD Cfg Table describes the configuration details."
			::= { sleMplsTpBfdCfg 1 }

		
		sleMplsTpBfdCfgInfoEntry OBJECT-TYPE
			SYNTAX SleBfdCfgInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The BFD Session Entry describes BFD session."
			INDEX { sleMplsTpBfdCfgInfoMegIndex, sleMplsTpBfdCfgInfoMeIndex }
			::= { sleMplsTpBfdCfgInfoTable 1 }

		
		SleBfdCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpBfdCfgInfoMegIndex
					Unsigned32,
				sleMplsTpBfdCfgInfoMeIndex
					Unsigned32,
				sleMplsTpBfdCfgInfoMegName
					OCTET STRING,
				sleMplsTpBfdCfgInfoMeName
					OCTET STRING,
				sleMplsTpBfdCfgInfoTxInterval
					OCTET STRING,
				sleMplsTpBfdCfgInfoRXInterval
					OCTET STRING
			 }

		sleMplsTpBfdCfgInfoMegIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"MEG Index."
			::= { sleMplsTpBfdCfgInfoEntry 1 }

		
		sleMplsTpBfdCfgInfoMeIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Maintenance Entity Index."
			::= { sleMplsTpBfdCfgInfoEntry 2 }

		
		sleMplsTpBfdCfgInfoMegName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"MEG Index."
			::= { sleMplsTpBfdCfgInfoEntry 3 }

		
		sleMplsTpBfdCfgInfoMeName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Maintenance Entity Index."
			::= { sleMplsTpBfdCfgInfoEntry 4 }

		
		sleMplsTpBfdCfgInfoTxInterval OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"TX Packet Interval of BFD to neighbor."
			::= { sleMplsTpBfdCfgInfoEntry 5 }

		
		sleMplsTpBfdCfgInfoRXInterval OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"RX Packet Interval of BFD from neighbor."
			::= { sleMplsTpBfdCfgInfoEntry 6 }

		
		sleMplsTpBfdCfgControl OBJECT IDENTIFIER ::= { sleMplsTpBfdCfg 2 }

		
		sleMplsTpBfdCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createBfdcfgEntry(1),
				deleteBfdCfgEntry(2),
				setIntervals(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be 
				modified in the BFD Config table. For each read-write column of 
				BFD config table, a Set Operation control value is added in this 
				object."
			::= { sleMplsTpBfdCfgControl 1 }

		
		sleMplsTpBfdCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpBfdCfgControl 2 }

		
		sleMplsTpBfdCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is 
				configured for every control table."
			::= { sleMplsTpBfdCfgControl 3 }

		
		sleMplsTpBfdCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpBfdCfgControl 4 }

		
		sleMplsTpBfdCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpBfdCfgControl 5 }

		
		sleMplsTpBfdCfgControlMegName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"MEG Name."
			::= { sleMplsTpBfdCfgControl 6 }

		
		sleMplsTpBfdCfgControlMeName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Maintenance Entity Name."
			::= { sleMplsTpBfdCfgControl 7 }

		
		sleMplsTpBfdCfgControlTxInterval OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"TX Packet Interval of BFD to neighbor."
			::= { sleMplsTpBfdCfgControl 8 }

		
		sleMplsTpBfdCfgControlRXInterval OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"RX Packet Interval of BFD from neighbor."
			::= { sleMplsTpBfdCfgControl 9 }

		
		sleMplsTpBfdSession OBJECT IDENTIFIER ::= { sleMplsTpBfd 2 }

		
		sleMplsTpBfdSessionInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleBfdSessionInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The BFD Session Table describes the BFD sessions."
			REFERENCE
				"Katz, D. and D. Ward, Bidirectional Forwarding
				Detection (BFD), RFC 5880, June 2012."
			::= { sleMplsTpBfdSession 1 }

		
		sleMplsTpBfdSessionInfoEntry OBJECT-TYPE
			SYNTAX SleBfdSessionInfoEntry
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The BFD Session Entry describes BFD session."
			INDEX { sleMplsTpBfdSessionInfoSessIndex }
			::= { sleMplsTpBfdSessionInfoTable 1 }

		
		SleBfdSessionInfoEntry ::=
			SEQUENCE { 
				sleMplsTpBfdSessionInfoSessIndex
					Unsigned32,
				sleMplsTpBfdSessionInfoMegName
					OCTET STRING,
				sleMplsTpBfdSessionInfoMeName
					OCTET STRING,
				sleMplsTpBfdSessionInfoVersionNumber
					Unsigned32,
				sleMplsTpBfdSessionInfoDiscriminator
					Unsigned32,
				sleMplsTpBfdSessionInfoRemoteDiscriminator
					Unsigned32,
				sleMplsTpBfdSessionInfoState
					INTEGER,
				sleMplsTpBfdSessionInfoDiag
					IANAbfdDiagTC,
				sleMplsTpBfdSessionInfoDesiredMinTxInterval
					OCTET STRING,
				sleMplsTpBfdSessionInfoReqMinRxInterval
					OCTET STRING,
				sleMplsTpBfdSessionInfoDetectMult
					Unsigned32
			 }

		sleMplsTpBfdSessionInfoSessIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object contains an index used to represent a
				unique BFD session on this device."
			::= { sleMplsTpBfdSessionInfoEntry 1 }

		
		sleMplsTpBfdSessionInfoMegName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"MEG Name of the BFD Session."
			::= { sleMplsTpBfdSessionInfoEntry 2 }

		
		sleMplsTpBfdSessionInfoMeName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"MEG Name of the BFD Session."
			::= { sleMplsTpBfdSessionInfoEntry 3 }

		
		sleMplsTpBfdSessionInfoVersionNumber OBJECT-TYPE
			SYNTAX Unsigned32 (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The version number of the BFD protocol that this session
				is running in. Write access is available for this object
				to provide ability to set desired version for this
				BFD session."
			REFERENCE
				"Katz, D. and D. Ward, Bidirectional Forwarding
				Detection (BFD), RFC 5880, June 2012."
			DEFVAL { 1 }
			::= { sleMplsTpBfdSessionInfoEntry 4 }

		
		sleMplsTpBfdSessionInfoDiscriminator OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the local discriminator for this BFD
				session, used to uniquely identify it."
			::= { sleMplsTpBfdSessionInfoEntry 5 }

		
		sleMplsTpBfdSessionInfoRemoteDiscriminator OBJECT-TYPE
			SYNTAX Unsigned32 (0 | 1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the session discriminator chosen
				by the remote system for this BFD session.  The value may
				be zero(0) if the remote discriminator is not yet known
				or if the session is in the down or adminDown(1) state."
			REFERENCE
				"Section 6.8.6, from Katz, D. and D. Ward, Bidirectional 
				Forwarding Detection (BFD), RFC 5880, June 2012."
			::= { sleMplsTpBfdSessionInfoEntry 6 }

		
		sleMplsTpBfdSessionInfoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				adminDown(0),
				stateDown(1),
				stateInit(2),
				stateUp(3),
				unknown(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"BFD session state."
			::= { sleMplsTpBfdSessionInfoEntry 7 }

		
		sleMplsTpBfdSessionInfoDiag OBJECT-TYPE
			SYNTAX IANAbfdDiagTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A diagnostic code specifying the local system's reason
				for the last transition of the session from up(4)
				to some other state."
			::= { sleMplsTpBfdSessionInfoEntry 8 }

		
		sleMplsTpBfdSessionInfoDesiredMinTxInterval OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the minimum interval, in
				microseconds, that the local system would like to use
				when transmitting BFD Control packets. The value of
				zero(0) is reserved, and should not be used."
			REFERENCE
				"Section 4.1 from Katz, D. and D. Ward, Bidirectional 
				Forwarding Detection (BFD), RFC 5880, June 2012."
			::= { sleMplsTpBfdSessionInfoEntry 9 }

		
		sleMplsTpBfdSessionInfoReqMinRxInterval OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the minimum interval, in
				microseconds, between received  BFD Control packets the
				local system is capable of supporting. The value of
				zero(0) can be specified when the transmitting system
				does not want the remote system to send any periodic BFD
				control packets."
			REFERENCE
				"Section 4.1 from Katz, D. and D. Ward, Bidirectional 
				Forwarding Detection (BFD), RFC 5880, June 2012."
			::= { sleMplsTpBfdSessionInfoEntry 10 }

		
		sleMplsTpBfdSessionInfoDetectMult OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION			" "
			::= { sleMplsTpBfdSessionInfoEntry 11 }

		
	
	END

--
-- sle-mpls-tp-bfd-mib.mib
--
