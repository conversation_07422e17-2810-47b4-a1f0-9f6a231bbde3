--
-- SLE-TC-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 4.0 Build 349
-- Thursday, October 07, 2004 at 16:34:15
--

	SLE-TC-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			MODULE-IDENTITY			
				FROM SNMPv2-SMI			
			TEXTUAL-CONVENTION, DisplayString			
				FROM SNMPv2-TC;
	
		sleTcMib MODULE-IDENTITY 
			LAST-UPDATED "200410071042Z"		-- October 07, 2004 at 10:42 GMT
			ORGANIZATION 
				"Siemens AG, Com FN AS E"
			CONTACT-INFO 
				"<PERSON>erd Barchmann
				Siemens AG, Com FN AS E
				
				+493834 555654"
			DESCRIPTION 
				"Description."
			::= { sleMgmt 100 }

		
	
--
-- Textual conventions
--
	
		SleControlStatusType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"The control status of an entity."
			SYNTAX INTEGER
				{
				requestIdle(1),
				requestBusy(2),
				requestPassed(3),
				requestFailed(4)
				}

		SleControlRequestResultType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"The request result."
			SYNTAX INTEGER

		SlePermissionType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				deny(0),
				permit(1)
				}

		SleTrafficType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				broadcast(1),
				multicast(2),
				dlf(3)
				}

		SleScopeType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				port(1),
				vlan(2)
				}

		SleOperStateType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				enabled(1),
				disabled(2)
				}

		SleAdminStateType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				unlocked(1),
				locked(2)
				}

		SleCardType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Type for card type."
			SYNTAX 	DisplayString (SIZE (0..20))
			
        
        SleSystemFileIndexType ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				" Sytem file index type."
			SYNTAX 	INTEGER (1..8)

--
-- Node definitions
--
	
	
	END

--
-- SLE-TC-MIB.my
--
