--
-- sle-qos-mib.mib
-- MIB generated by MG-<PERSON>O<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Monday, April 18, 2011 at 12:24:04
--

	SLE-QOS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlRequestResultType, SleControlStatusType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Ip<PERSON>ddress, Integer32, Unsigned32, <PERSON><PERSON><PERSON>32, 
			Counter32, BITS, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
--  2
-- December 09, 2004 at 17:33 GMT
		-- *******.4.1.6296.101.10
		sleQoS MODULE-IDENTITY 
			LAST-UPDATED "200412091733Z"		-- December 09, 2004 at 17:33 GMT
			ORGANIZATION 
				"Hanasoft."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleMgmt 10 }

		
	
--
-- Type definitions
--
	
--  Type definitions
-- 
		SleTrafficProfileControlRequestType ::= INTEGER { setTrafficProfile(1) }

		SleUserFlowMatchType ::= BITS
			{
			ufIngressPort(0),
			ufEgressPort(1),
			ufEthernetType(2),
			ufSrcMacAddress(3),
			ufDstMacAddress(4),
			ufVlanId(5),
			uf8021p(6),
			ufSrcIpAddress(7),
			ufSrcPrefixLength(8),
			ufDstIpAddress(9),
			ufDstPrefixLength(10),
			ufDscp(11),
			ufProtocolType(12),
			ufTcpFlag(13),
			ufSrcL4Port(14),
			ufDstL4Port(15),
			ufPacketLength(16)
			}

		SleUserFlowActionType ::= BITS
			{
			ufActPermit(0),
			ufActCopyCpu(1),
			ufActSameAsTos(2),
			ufActSameAsCos(3),
			ufActMirror(4)
			}

		SleBooleanType ::= INTEGER
			{
			disable(0),
			enable(1)
			}

		SlePortScheduleControlRequestType ::= INTEGER { setPortScheduleMode(1) }

		SleQueueControlRequestType ::= INTEGER { setQueueProfile(1) }

		SleUserFlowControlRequestType ::= INTEGER
			{
			createUserFlow(1),
			destroyUserFlow(2),
			setUserFlowClassifierProfile(3),
			setUserFlowMatchActionProfile(4),
			setUserFlowNomatchActionProfile(5)
			}

	
	
--
-- Node definitions
--
	
--  Node definitions
-- 
-- 2.2
		-- *******.4.1.6296.101.10.1
		sleQoSBase OBJECT IDENTIFIER ::= { sleQoS 1 }

		
		-- *******.4.1.6296.**********
		sleQoSBaseInfo OBJECT IDENTIFIER ::= { sleQoSBase 1 }

		
--  2.2.1
		-- *******.4.1.6296.**********.1
		sleQosUserFlowNum OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseInfo 1 }

		
--  2.2.2
		-- *******.4.1.6296.**********.2
		sleQosTrafficProfileNum OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleQosCounterNum OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseInfo 3 }

		
--  2.2.4
		-- *******.4.1.6296.**********.4
		sleQosQueueNum OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseInfo 4 }

		
		-- *******.4.1.6296.**********.5
		sleQosColorMarkingProfileNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseInfo 5 }

		
		-- *******.4.1.6296.**********.6
		sleQosRuleMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				default(0),
				extension(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseInfo 6 }

		
		-- *******.4.1.6296.**********
		sleQoSBaseControl OBJECT IDENTIFIER ::= { sleQoSBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleQoSBaseControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setQoSBaseRuleMode(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleQoSBaseControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleQoSBaseControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleQoSBaseControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleQoSBaseControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleQoSBaseControlRuleMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				default(0),
				extension(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQoSBaseControl 6 }

		
		-- *******.4.1.6296.**********
		sleQoSBaseNotification OBJECT IDENTIFIER ::= { sleQoSBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleQoSBaseRuleModeChanged NOTIFICATION-TYPE
			OBJECTS { sleQoSBaseControlRequest, sleQoSBaseControlTimeStamp, sleQoSBaseControlReqResult, sleQosRuleMode }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleQoSBaseNotification 1 }

		
--  2.4
		-- *******.4.1.6296.101.10.2
		sleTrafficProfile OBJECT IDENTIFIER ::= { sleQoS 2 }

		
--  2.4.1
		-- *******.4.1.6296.**********
		sleTrafficProfileTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleTrafficProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrafficProfile 1 }

		
--  *******
		-- *******.4.1.6296.**********.1
		sleTrafficProfileEntry OBJECT-TYPE
			SYNTAX SleTrafficProfileEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleTrafficProfileIndex }
			::= { sleTrafficProfileTable 1 }

		
		SleTrafficProfileEntry ::=
			SEQUENCE { 
				sleTrafficProfileIndex
					Unsigned32,
				sleTrafficProfileMinBandwidth
					Integer32,
				sleTrafficProfileMaxBandwidth
					Integer32,
				sleTrafficProfileMaxBurstSize
					Integer32,
				sleTrafficProfileUseCount
					Integer32
			 }

--  *******.1
		-- *******.4.1.6296.**********.1.1
		sleTrafficProfileIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"meter Index"
			::= { sleTrafficProfileEntry 1 }

		
--  *******.2
		-- *******.4.1.6296.**********.1.2
		sleTrafficProfileMinBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value is in unit for 64 kbps"
			::= { sleTrafficProfileEntry 2 }

		
--  *******.3
		-- *******.4.1.6296.**********.1.3
		sleTrafficProfileMaxBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value is in unit for 64 kbps"
			::= { sleTrafficProfileEntry 3 }

		
--  *******.4
		-- *******.4.1.6296.**********.1.4
		sleTrafficProfileMaxBurstSize OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The size of the bucket used for metering"
			::= { sleTrafficProfileEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleTrafficProfileUseCount OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrafficProfileEntry 5 }

		
--  2.4.2
		-- *******.4.1.6296.**********
		sleTrafficProfileControl OBJECT IDENTIFIER ::= { sleTrafficProfile 2 }

		
--  *******
		-- *******.4.1.6296.**********.1
		sleTrafficProfileControlRequest OBJECT-TYPE
			SYNTAX SleTrafficProfileControlRequestType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrafficProfileControl 1 }

		
--  *******
		-- *******.4.1.6296.**********.2
		sleTrafficProfileControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrafficProfileControl 2 }

		
--  *******
		-- *******.4.1.6296.**********.3
		sleTrafficProfileControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrafficProfileControl 3 }

		
--  *******
		-- *******.4.1.6296.**********.4
		sleTrafficProfileControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrafficProfileControl 4 }

		
--  *******
		-- *******.4.1.6296.**********.5
		sleTrafficProfileControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of last command"
			::= { sleTrafficProfileControl 5 }

		
--  *******
		-- *******.4.1.6296.**********.6
		sleTrafficProfileControlIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"meter Index"
			::= { sleTrafficProfileControl 6 }

		
--  *******
		-- *******.4.1.6296.**********.7
		sleTrafficProfileControlMinBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value is in unit for 64 kbps"
			::= { sleTrafficProfileControl 7 }

		
--  *******
		-- *******.4.1.6296.**********.8
		sleTrafficProfileControlMaxBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value is in unit for 64 kbps"
			::= { sleTrafficProfileControl 8 }

		
--  *******
		-- *******.4.1.6296.**********.9
		sleTrafficProfileControlMaxBurstSize OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The size of the bucket used for metering"
			::= { sleTrafficProfileControl 9 }

		
--  2.4.3
		-- *******.4.1.6296.**********
		sleTrafficProfileNotification OBJECT IDENTIFIER ::= { sleTrafficProfile 3 }

		
--  *******
		-- *******.4.1.6296.**********.1
		sleTrafficProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleTrafficProfileMinBandwidth, sleTrafficProfileControlTimeStamp, sleTrafficProfileControlReqResult, sleTrafficProfileControlRequest, sleTrafficProfileMaxBurstSize, 
				sleTrafficProfileMaxBandwidth }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleTrafficProfileNotification 1 }

		
--  2.5
		-- *******.4.1.6296.101.10.3
		sleUserFlow OBJECT IDENTIFIER ::= { sleQoS 3 }

		
--  2.5.1
		-- *******.4.1.6296.**********
		sleUserFlowTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleUserFlowEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlow 1 }

		
--  *******
		-- *******.4.1.6296.**********.1
		sleUserFlowEntry OBJECT-TYPE
			SYNTAX SleUserFlowEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleUserFlowName }
			::= { sleUserFlowTable 1 }

		
		SleUserFlowEntry ::=
			SEQUENCE { 
				sleUserFlowName
					OCTET STRING,
				sleUserFlowIngressPorts
					OCTET STRING,
				sleUserFlowEgressPorts
					OCTET STRING,
				sleUserFlowEthernetType
					Integer32,
				sleUserFlowSrcMacAddr
					OCTET STRING,
				sleUserFlowDstMacAddr
					OCTET STRING,
				sleUserFlowVlan
					Integer32,
				sleUserFlow8021p
					Integer32,
				sleUserFlowSrcIpAddr
					IpAddress,
				sleUserFlowSrcPrefixLength
					Integer32,
				sleUserFlowDstIpAddr
					IpAddress,
				sleUserFlowDstPrefixLength
					Integer32,
				sleUserFlowIpPktPriorityType
					INTEGER,
				sleUserFlowIpPktPriority
					Integer32,
				sleUserFlowProtocolType
					Integer32,
				sleUserFlowTcpFlag
					BITS,
				sleUserFlowSrcL4Port
					Integer32,
				sleUserFlowDstL4Port
					Integer32,
				sleUserFlowPktLen
					Integer32,
				sleUserFlowValidFlags
					BITS,
				sleUserFlowMatchFlags
					BITS,
				sleUserFlowPriority
					INTEGER,
				sleUserFlowMatchCounterId
					Integer32,
				sleUserFlowMatchTrafficProfileId
					Integer32,
				sleUserFlowMatchQueue
					Integer32,
				sleUserFlowMatchIpPktPriorityType
					INTEGER,
				sleUserFlowMatchIpPktPriority
					Integer32,
				sleUserFlowMatchRedirPort
					Integer32,
				sleUserFlowMatchVid
					Integer32,
				sleUserFlowMatchDstMac
					OCTET STRING,
				sleUserFlowMatchPortMap
					OCTET STRING,
				sleUserFlowMatchEgressType
					INTEGER,
				sleUserFlowMatchAction
					BITS,
				sleUserFlowNomatchCounterId
					Integer32,
				sleUserFlowNomatchTrafficProfileId
					Integer32,
				sleUserFlowNomatchQueue
					Integer32,
				sleUserFlowNomatchIpPktPriorityType
					INTEGER,
				sleUserFlowNomatchIpPktPriority
					Integer32,
				sleUserFlowNomatchRedirPort
					Integer32,
				sleUserFlowNomatchVid
					Integer32,
				sleUserFlowNomatchAction
					BITS,
				sleUserFlowMatchColorMarkingId
					Integer32,
				sleUserFlowNomatchColorMarkingId
					Integer32,
				sleUserFlowInnerVlan
					Integer32,
				sleUserFlowInner8021p
					Integer32,
				sleUserFlowMatchRoutePrimary
					IpAddress,
				sleUserFlowMatchRouteSecondary
					IpAddress
			 }

--  *******.1
		-- *******.4.1.6296.**********.1.1
		sleUserFlowName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..63))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The name of this user flow."
			::= { sleUserFlowEntry 1 }

		
--  *******.2
		-- *******.4.1.6296.**********.1.2
		sleUserFlowIngressPorts OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ingress port number"
			::= { sleUserFlowEntry 2 }

		
--  *******.4
		-- *******.4.1.6296.**********.1.3
		sleUserFlowEgressPorts OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Egress Port Number"
			::= { sleUserFlowEntry 3 }

		
--  *******.6
		-- *******.4.1.6296.**********.1.4
		sleUserFlowEthernetType OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ethernet type value in incoming frames"
			::= { sleUserFlowEntry 4 }

		
--  *******.8
		-- *******.4.1.6296.**********.1.5
		sleUserFlowSrcMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (6))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Source MAC address"
			::= { sleUserFlowEntry 5 }

		
--  *******.10
		-- *******.4.1.6296.**********.1.6
		sleUserFlowDstMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (6))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Destination MAC address"
			::= { sleUserFlowEntry 6 }

		
--  *******.12
		-- *******.4.1.6296.**********.1.7
		sleUserFlowVlan OBJECT-TYPE
			SYNTAX Integer32 (0..4094)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VLAN ID"
			::= { sleUserFlowEntry 7 }

		
--  *******.14
		-- *******.4.1.6296.**********.1.8
		sleUserFlow8021p OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of the IEEE802.1p priority in the packet"
			::= { sleUserFlowEntry 8 }

		
--  *******.16
		-- *******.4.1.6296.**********.1.9
		sleUserFlowSrcIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The IP address to match against the packet's 
				source IP address."
			::= { sleUserFlowEntry 9 }

		
--  *******.18
		-- *******.4.1.6296.**********.1.10
		sleUserFlowSrcPrefixLength OBJECT-TYPE
			SYNTAX Integer32 (0..31)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Subnet mask. The length of 0 indicates a match of any address"
			::= { sleUserFlowEntry 10 }

		
--  *******.20
		-- *******.4.1.6296.**********.1.11
		sleUserFlowDstIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The IP address to match against the packet's 
				destination IP address."
			::= { sleUserFlowEntry 11 }

		
--  *******.22
		-- *******.4.1.6296.**********.1.12
		sleUserFlowDstPrefixLength OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Subnet mask. The length of 0 indicates a match of any address"
			::= { sleUserFlowEntry 12 }

		
		-- *******.4.1.6296.**********.1.13
		sleUserFlowIpPktPriorityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipPrecedence(1),
				diffServ(2),
				ipToS(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowEntry 13 }

		
--  *******.24
		-- *******.4.1.6296.**********.1.14
		sleUserFlowIpPktPriority OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Priority value in incoming IP packet.
				If sleUserFlowIpPktPriorityType is 1, 
				this value's range is 0-7.
				If sleUserFlowIpPktPriorityType is 2, 
				this value's range is 0-63.
				If sleUserFlowIpPktPriorityType is 3, 
				this value's range is 0-255.
				"
			::= { sleUserFlowEntry 14 }

		
--  *******.26
		-- *******.4.1.6296.**********.1.15
		sleUserFlowProtocolType OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The IP protocol type value"
			::= { sleUserFlowEntry 15 }

		
		-- *******.4.1.6296.**********.1.16
		sleUserFlowTcpFlag OBJECT-TYPE
			SYNTAX BITS
				{
				reserv0(0),
				reserv1(1),
				urg(2),
				ack(3),
				psh(4),
				rst(5),
				syn(6),
				fin(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"TCP Flags"
			::= { sleUserFlowEntry 16 }

		
--  *******.30
		-- *******.4.1.6296.**********.1.17
		sleUserFlowSrcL4Port OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The source L4 port number, only valid for 
				TCP or UDP packets."
			::= { sleUserFlowEntry 17 }

		
--  *******.32
		-- *******.4.1.6296.**********.1.18
		sleUserFlowDstL4Port OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The destination L4 port number, only valid for 
				TCP or UDP packets."
			::= { sleUserFlowEntry 18 }

		
--  *******.28
		-- *******.4.1.6296.**********.1.19
		sleUserFlowPktLen OBJECT-TYPE
			SYNTAX Integer32 (21..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The packet length of the incoming packet"
			::= { sleUserFlowEntry 19 }

		
		-- *******.4.1.6296.**********.1.20
		sleUserFlowValidFlags OBJECT-TYPE
			SYNTAX BITS
				{
				ufIngressPort(0),
				ufEgressPort(1),
				ufEthernetType(2),
				ufSrcMacAddress(3),
				ufDstMacAddress(4),
				ufVlanId(5),
				uf8021p(6),
				ufSrcIpAddress(7),
				ufSrcPrefixLength(8),
				ufDstIpAddress(9),
				ufDstPrefixLength(10),
				ufIpPktPriority(11),
				ufProtocolType(12),
				ufTcpFlag(13),
				ufSrcL4Port(14),
				ufDstL4Port(15),
				ufPacketLength(16),
				ufPriority(17),
				ufInnerVlanId(18),
				ufInner8021p(19)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"User Flow Valid Flags"
			::= { sleUserFlowEntry 20 }

		
		-- *******.4.1.6296.**********.1.21
		sleUserFlowMatchFlags OBJECT-TYPE
			SYNTAX BITS
				{
				ufIngressPort(0),
				ufEgressPort(1),
				ufEthernetType(2),
				ufSrcMacAddress(3),
				ufDstMacAddress(4),
				ufVlanId(5),
				uf8021p(6),
				ufSrcIpAddress(7),
				ufSrcPrefixLength(8),
				ufDstIpAddress(9),
				ufDstPrefixLength(10),
				ufIpPktPriority(11),
				ufProtocolType(12),
				ufTcpFlag(13),
				ufSrcL4Port(14),
				ufDstL4Port(15),
				ufPacketLength(16),
				ufPriority(17)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"User Flow Matching Flags"
			::= { sleUserFlowEntry 21 }

		
--  *******.38
		-- *******.4.1.6296.**********.1.22
		sleUserFlowPriority OBJECT-TYPE
			SYNTAX INTEGER
				{
				low(1),
				medium(2),
				high(3),
				highest(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The priority of this user flow"
			::= { sleUserFlowEntry 22 }

		
--  *******.39
		-- *******.4.1.6296.**********.1.23
		sleUserFlowMatchCounterId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The counter index to be apply for the matched packets."
			::= { sleUserFlowEntry 23 }

		
--  *******.40
		-- *******.4.1.6296.**********.1.24
		sleUserFlowMatchTrafficProfileId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The traffic profile index to be apply 
				for the matched packets."
			::= { sleUserFlowEntry 24 }

		
--  *******.45
		-- *******.4.1.6296.**********.1.25
		sleUserFlowMatchQueue OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"TThe CoS value to be assign for the nomatched packets"
			::= { sleUserFlowEntry 25 }

		
--  *******.43
		-- *******.4.1.6296.**********.1.26
		sleUserFlowMatchIpPktPriorityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipPrecedence(1),
				diffServ(2),
				ipToS(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Priority type to be applied to incoming IP packet."
			::= { sleUserFlowEntry 26 }

		
		-- *******.4.1.6296.**********.1.27
		sleUserFlowMatchIpPktPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Priority value in incoming IP packet.
				If sleUserFlowMatchIpPktPriorityType is 1, 
				then this value's range is 0-7.
				If sleUserFlowMatchIpPktPriorityType is 2, 
				then this value's range is 0-63.
				If sleUserFlowMatchIpPktPriorityType is 3, 
				then this value's range is 0-255.
				"
			DEFVAL { -1 }
			::= { sleUserFlowEntry 27 }

		
--  *******.46
		-- *******.4.1.6296.**********.1.28
		sleUserFlowMatchRedirPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The port to redirect to a matched packet."
			::= { sleUserFlowEntry 28 }

		
--  *******.50
		-- *******.4.1.6296.**********.1.29
		sleUserFlowMatchVid OBJECT-TYPE
			SYNTAX Integer32 (1..4096)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The vlan id to be assigned for the matched packets"
			::= { sleUserFlowEntry 29 }

		
		-- *******.4.1.6296.**********.1.30
		sleUserFlowMatchDstMac OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowEntry 30 }

		
		-- *******.4.1.6296.**********.1.31
		sleUserFlowMatchPortMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowEntry 31 }

		
		-- *******.4.1.6296.**********.1.32
		sleUserFlowMatchEgressType OBJECT-TYPE
			SYNTAX INTEGER
				{
				dmac(0),
				egressPorts(1),
				filterPorts(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowEntry 32 }

		
--  *******.41
		-- *******.4.1.6296.**********.1.33
		sleUserFlowMatchAction OBJECT-TYPE
			SYNTAX BITS
				{
				ufActDeny(0),
				ufActCopytoCpu(1),
				ufActSameAsTos(2),
				ufActSameAsCos(3),
				ufActMirror(4),
				ufActCounter(5),
				ufActTrafficeProfile(6),
				ufActQueue(7),
				ufActIpPktPriority(8),
				ufActRedirPort(9),
				ufAct8021p(10),
				ufActVid(11),
				ufActEgress(12),
				ufActColorMarking(13),
				ufActRoute(14),
				ufActRouteReachability(15)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The action to be apply for matched packets."
			::= { sleUserFlowEntry 33 }

		
--  *******.51
		-- *******.4.1.6296.**********.1.34
		sleUserFlowNomatchCounterId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The counter index to be apply for the nomatched packets."
			::= { sleUserFlowEntry 34 }

		
--  *******.40
		-- *******.4.1.6296.**********.1.35
		sleUserFlowNomatchTrafficProfileId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The traffic profile index to be apply 
				for the nomatched packets."
			::= { sleUserFlowEntry 35 }

		
--  *******.43
		-- *******.4.1.6296.**********.1.36
		sleUserFlowNomatchQueue OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The CoS value to be assign for the nomatched packets"
			::= { sleUserFlowEntry 36 }

		
--  *******.45
		-- *******.4.1.6296.**********.1.37
		sleUserFlowNomatchIpPktPriorityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipPrecedence(1),
				diffServ(2),
				ipToS(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Priority type to be applied to incoming IP packet, in case nomatched."
			::= { sleUserFlowEntry 37 }

		
		-- *******.4.1.6296.**********.1.38
		sleUserFlowNomatchIpPktPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Priority value in incoming IP packet.
				If sleUserFlowNomatchIpPktPriorityType is 1, 
				then this value's range is 0-7.
				If sleUserFlowNomatchIpPktPriorityType is 2, 
				then this value's range is 0-63.
				If sleUserFlowNomatchIpPktPriorityType is 3, 
				then this value's range is 0-255.
				"
			DEFVAL { -1 }
			::= { sleUserFlowEntry 38 }

		
--  *******.46
		-- *******.4.1.6296.**********.1.39
		sleUserFlowNomatchRedirPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The port to redirect to nomatched packet."
			::= { sleUserFlowEntry 39 }

		
--  *******.50
		-- *******.4.1.6296.**********.1.40
		sleUserFlowNomatchVid OBJECT-TYPE
			SYNTAX Integer32 (0..4096)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The vlan id to be assigned for the nomatched packets"
			::= { sleUserFlowEntry 40 }

		
--  *******.41
		-- *******.4.1.6296.**********.1.41
		sleUserFlowNomatchAction OBJECT-TYPE
			SYNTAX BITS
				{
				ufActDeny(0),
				ufActCopytoCpu(1),
				ufActSameAsTos(2),
				ufActSameAsCos(3),
				ufActMirror(4),
				ufActCounter(5),
				ufActTrafficeProfile(6),
				ufActQueue(7),
				ufActIpPktPriority(8),
				ufActRedirPort(9),
				ufAct8021p(10),
				ufActVid(11),
				ufActEgress(12)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The action to be apply for nomatched packets."
			::= { sleUserFlowEntry 41 }

		
		-- *******.4.1.6296.**********.1.42
		sleUserFlowMatchColorMarkingId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The colormarking profile index to be apply for the matched packet."
			::= { sleUserFlowEntry 42 }

		
		-- *******.4.1.6296.**********.1.43
		sleUserFlowNomatchColorMarkingId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The colormarking profile index to be apply for the unmatched packet. (not supported)"
			::= { sleUserFlowEntry 43 }

		
		-- *******.4.1.6296.**********.1.44
		sleUserFlowInnerVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Inner VLAN ID in QinQ"
			::= { sleUserFlowEntry 44 }

		
		-- *******.4.1.6296.**********.1.45
		sleUserFlowInner8021p OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of the inner IEEE802.1p priority in the packet"
			::= { sleUserFlowEntry 45 }

		
		-- *******.4.1.6296.**********.1.46
		sleUserFlowMatchRoutePrimary OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The policy-based-routing primary route"
			::= { sleUserFlowEntry 46 }

		
		-- *******.4.1.6296.**********.1.47
		sleUserFlowMatchRouteSecondary OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The policy-based-routing secondary route"
			::= { sleUserFlowEntry 47 }

		
--  2.5.2
		-- *******.4.1.6296.**********
		sleUserFlowControl OBJECT IDENTIFIER ::= { sleUserFlow 2 }

		
		-- *******.4.1.6296.**********.1
		sleUserFlowControlReqest OBJECT-TYPE
			SYNTAX SleUserFlowControlRequestType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleUserFlowControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleUserFlowControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleUserFlowControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleUserFlowControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleUserFlowControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..63))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleUserFlowControlIngressPorts OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ingress port"
			::= { sleUserFlowControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleUserFlowControlEgressPorts OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Egress port"
			::= { sleUserFlowControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleUserFlowControlEthernetType OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleUserFlowControlSrcMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (6))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Source MAC address"
			::= { sleUserFlowControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleUserFlowControlDstMacAddr OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (6))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Destination MAC address"
			::= { sleUserFlowControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleUserFlowControlVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4096)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Vlan ID"
			::= { sleUserFlowControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleUserFlowControl8021p OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value of the IEEE802.1p priority in the packet"
			::= { sleUserFlowControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleUserFlowControlSrcIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The IP address to match against the packet's source IP address."
			::= { sleUserFlowControl 14 }

		
		-- *******.4.1.6296.**********.15
		sleUserFlowControlSrcPrefixLength OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Subnet mask. The length of 0 indicates a match of any address"
			::= { sleUserFlowControl 15 }

		
		-- *******.4.1.6296.**********.16
		sleUserFlowControlDstIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The IP address to match against the packet's destination IP address."
			::= { sleUserFlowControl 16 }

		
		-- *******.4.1.6296.**********.17
		sleUserFlowControlDstPrefixLength OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Subnet mask. The length of 0 indicates a match of any address"
			::= { sleUserFlowControl 17 }

		
		-- *******.4.1.6296.**********.18
		sleUserFlowControlIpPktPriorityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipPrecedence(1),
				diffServ(2),
				ipToS(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 18 }

		
		-- *******.4.1.6296.**********.19
		sleUserFlowControlIpPktPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Priority value in incoming IP packet.
				If sleUserFlowControlIpPktPriorityType is 1, 
				this value's range is 0-7.
				If sleUserFlowControlIpPktPriorityType is 2, 
				this value's range is 0-63.
				If sleUserFlowControlIpPktPriorityType is 3, 
				this value's range is 0-255.
				"
			::= { sleUserFlowControl 19 }

		
		-- *******.4.1.6296.**********.20
		sleUserFlowControlProtocolType OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The IP protocol type value"
			::= { sleUserFlowControl 20 }

		
		-- *******.4.1.6296.**********.21
		sleUserFlowControlTcpFlag OBJECT-TYPE
			SYNTAX BITS
				{
				reserv0(0),
				reserv1(1),
				urg(2),
				ack(3),
				psh(4),
				rst(5),
				syn(6),
				fin(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 21 }

		
		-- *******.4.1.6296.**********.22
		sleUserFlowControlSrcL4Port OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The source L4 port number, only valid for TCP or UDP packets."
			::= { sleUserFlowControl 22 }

		
		-- *******.4.1.6296.**********.23
		sleUserFlowControlDstL4Port OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The destination L4 port number, only valid for TCP or UDP packets."
			::= { sleUserFlowControl 23 }

		
		-- *******.4.1.6296.**********.24
		sleUserFlowControlPktLen OBJECT-TYPE
			SYNTAX Integer32 (21..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The packet length of the incoming packet"
			::= { sleUserFlowControl 24 }

		
		-- *******.4.1.6296.**********.25
		sleUserFlowControlValidFlag OBJECT-TYPE
			SYNTAX BITS
				{
				ufIngressPort(0),
				ufEgressPort(1),
				ufEthernetType(2),
				ufSrcMacAddress(3),
				ufDstMacAddress(4),
				ufVlanId(5),
				uf8021p(6),
				ufSrcIpAddress(7),
				ufSrcPrefixLength(8),
				ufDstIpAddress(9),
				ufDstPrefixLength(10),
				ufIpPktPriority(11),
				ufProtocolType(12),
				ufTcpFlag(13),
				ufSrcL4Port(14),
				ufDstL4Port(15),
				ufPacketLength(16),
				ufPriority(17),
				ufInnerVlanId(18),
				ufInner8021p(19)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ICMP type, only valid for ICMP packets."
			::= { sleUserFlowControl 25 }

		
		-- *******.4.1.6296.**********.26
		sleUserFlowControlMatchFlag OBJECT-TYPE
			SYNTAX BITS
				{
				ufIngressPort(0),
				ufEgressPort(1),
				ufEthernetType(2),
				ufSrcMacAddress(3),
				ufDstMacAddress(4),
				ufVlanId(5),
				uf8021p(6),
				ufSrcIpAddress(7),
				ufSrcPrefixLength(8),
				ufDstIpAddress(9),
				ufDstPrefixLength(10),
				ufIpPktPriority(11),
				ufProtocolType(12),
				ufTcpFlag(13),
				ufSrcL4Port(14),
				ufDstL4Port(15),
				ufPacketLength(16),
				ufPriority(17)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ICMP code, only valid for ICMP packets."
			::= { sleUserFlowControl 26 }

		
		-- *******.4.1.6296.**********.27
		sleUserFlowControlPriority OBJECT-TYPE
			SYNTAX INTEGER
				{
				low(1),
				medium(2),
				high(3),
				highest(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The priority of this user flow"
			::= { sleUserFlowControl 27 }

		
		-- *******.4.1.6296.**********.28
		sleUserFlowControlMatchCounterId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The counter index to be apply for the matched packets."
			::= { sleUserFlowControl 28 }

		
		-- *******.4.1.6296.**********.29
		sleUserFlowControlMatchTrafficProfileId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The traffic profile index to be apply for the matched packets."
			::= { sleUserFlowControl 29 }

		
		-- *******.4.1.6296.**********.30
		sleUserFlowControlMatchQueue OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The CoS value to be assign for the matched packets"
			::= { sleUserFlowControl 30 }

		
		-- *******.4.1.6296.**********.31
		sleUserFlowControlMatchIpPktPriorityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipPrecedence(1),
				diffServ(2),
				ipToS(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 31 }

		
		-- *******.4.1.6296.**********.32
		sleUserFlowControlMatchIpPktPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Priority value in incoming IP packet.
				If type is 1, then this value's range is 0-7.
				If type is 2, then this value's range is 0-63.
				If type is 3, then this value's range is 0-255.
				"
			DEFVAL { -1 }
			::= { sleUserFlowControl 32 }

		
		-- *******.4.1.6296.**********.33
		sleUserFlowControlMatchRedirPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The port to redirect to a matched packet."
			::= { sleUserFlowControl 33 }

		
		-- *******.4.1.6296.**********.34
		sleUserFlowControlMatchVid OBJECT-TYPE
			SYNTAX Integer32 (1..4096)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vlan id to be assigned for the matched packets"
			::= { sleUserFlowControl 34 }

		
		-- *******.4.1.6296.**********.35
		sleUserFlowControlMatchDstMac OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 35 }

		
		-- *******.4.1.6296.**********.36
		sleUserFlowControlMatchPortMap OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 36 }

		
		-- *******.4.1.6296.**********.37
		sleUserFlowControlMatchEgressType OBJECT-TYPE
			SYNTAX INTEGER
				{
				dmac(0),
				egressPorts(1),
				filterPorts(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 37 }

		
		-- *******.4.1.6296.**********.38
		sleUserFlowControlMatchAction OBJECT-TYPE
			SYNTAX BITS
				{
				ufActDeny(0),
				ufActCopytoCpu(1),
				ufActSameAsTos(2),
				ufActSameAsCos(3),
				ufActMirror(4),
				ufActCounter(5),
				ufActTrafficeProfile(6),
				ufActQueue(7),
				ufActIpPktPriority(8),
				ufActRedirPort(9),
				ufAct8021p(10),
				ufActVid(11),
				ufActEgress(12),
				ufActColorMarking(13),
				ufActRoute(14),
				ufActRouteReachability(15)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The action to be apply for matched packets."
			::= { sleUserFlowControl 38 }

		
		-- *******.4.1.6296.**********.39
		sleUserFlowControlNomatchCounterId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The counter index to be apply for the unmatched packets."
			::= { sleUserFlowControl 39 }

		
		-- *******.4.1.6296.**********.40
		sleUserFlowControlNomatchTrafficProfileId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The traffic profile index to be apply for the unmatched packets."
			::= { sleUserFlowControl 40 }

		
		-- *******.4.1.6296.**********.41
		sleUserFlowControlNomatchQueue OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The CoS value to be assign for the unmatched packets"
			::= { sleUserFlowControl 41 }

		
		-- *******.4.1.6296.**********.42
		sleUserFlowControlNomatchIpPktPriorityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipPrecedence(1),
				diffServ(2),
				ipToS(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 42 }

		
		-- *******.4.1.6296.**********.43
		sleUserFlowControlNomatchIpPktPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Priority value in incoming IP packet.
				If type is 1, then this value's range is 0-7.
				If type is 2, then this value's range is 0-63.
				If type is 3, then this value's range is 0-255.
				"
			DEFVAL { -1 }
			::= { sleUserFlowControl 43 }

		
		-- *******.4.1.6296.**********.44
		sleUserFlowControlNomatchRedirPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The port to redirect a unmatched packet."
			::= { sleUserFlowControl 44 }

		
		-- *******.4.1.6296.**********.45
		sleUserFlowControlNomatchVid OBJECT-TYPE
			SYNTAX Integer32 (0..4096)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The vlan id to be assigned for the unmatched packets"
			::= { sleUserFlowControl 45 }

		
		-- *******.4.1.6296.**********.46
		sleUserFlowControlNomatchAction OBJECT-TYPE
			SYNTAX BITS
				{
				ufActDeny(0),
				ufActCopytoCpu(1),
				ufActSameAsTos(2),
				ufActSameAsCos(3),
				ufActMirror(4),
				ufActCounter(5),
				ufActTrafficeProfile(6),
				ufActQueue(7),
				ufActIpPktPriority(8),
				ufActRedirPort(9),
				ufAct8021p(10),
				ufActVid(11),
				ufActEgress(12)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The action to be apply for unmatched packets."
			::= { sleUserFlowControl 46 }

		
		-- *******.4.1.6296.**********.47
		sleUserFlowControlMatchColorMarkingId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The color marking profile index to be apply for the matched packet."
			::= { sleUserFlowControl 47 }

		
		-- *******.4.1.6296.**********.48
		sleUserFlowControlNomatchColorMarkingId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The color marking profile index to be apply for the nomatched packet."
			::= { sleUserFlowControl 48 }

		
		-- *******.4.1.6296.**********.49
		sleUserFlowControlInnerVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Inner VLAN ID"
			::= { sleUserFlowControl 49 }

		
		-- *******.4.1.6296.**********.50
		sleUserFlowControlInner8021p OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value of the Inner IEEE802.1p priority in the packet"
			::= { sleUserFlowControl 50 }

		
		-- *******.4.1.6296.**********.51
		sleUserFlowControlMatchRoutePrimary OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 51 }

		
		-- *******.4.1.6296.**********.52
		sleUserFlowControlMatchRouteSecondary OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserFlowControl 52 }

		
		-- *******.4.1.6296.**********
		sleUserFlowNotification OBJECT IDENTIFIER ::= { sleUserFlow 3 }

		
		-- *******.4.1.6296.**********.1
		sleUserFlowCreated NOTIFICATION-TYPE
			OBJECTS { sleUserFlowControlReqest, sleUserFlowControlTimeStamp, sleUserFlowControlReqResult, sleUserFlowControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleUserFlowNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleUserFlowDestroyed NOTIFICATION-TYPE
			OBJECTS { sleUserFlowControlReqest, sleUserFlowControlTimeStamp, sleUserFlowControlReqResult, sleUserFlowControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleUserFlowNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleUserFlowClassifierProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleUserFlowControlReqest, sleUserFlowControlTimeStamp, sleUserFlowControlReqResult, sleUserFlowControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleUserFlowNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleUserFlowMatchActionProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleUserFlowControlReqest, sleUserFlowControlTimeStamp, sleUserFlowControlReqResult, sleUserFlowControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleUserFlowNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleUserFlowNomatchActionProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleUserFlowControlReqest, sleUserFlowControlTimeStamp, sleUserFlowControlReqResult, sleUserFlowControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleUserFlowNotification 5 }

		
--  2.6
		-- *******.4.1.6296.101.10.4
		slePortSchedule OBJECT IDENTIFIER ::= { sleQoS 4 }

		
		-- *******.4.1.6296.**********
		slePortSchedultTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePortSchedultEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortSchedule 1 }

		
		-- *******.4.1.6296.**********.1
		slePortSchedultEntry OBJECT-TYPE
			SYNTAX SlePortSchedultEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePortSchedulInterfaceIndex }
			::= { slePortSchedultTable 1 }

		
		SlePortSchedultEntry ::=
			SEQUENCE { 
				slePortSchedulInterfaceIndex
					Integer32,
				slePortScheduleMode
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		slePortSchedulInterfaceIndex OBJECT-TYPE
			SYNTAX Integer32 (1..256)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface Index"
			::= { slePortSchedultEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		slePortScheduleMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				strictqueueing(1),
				wrr(2),
				wfq(3),
				rr(4),
				drr(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The scheduling mode of port"
			::= { slePortSchedultEntry 2 }

		
		-- *******.4.1.6296.**********
		slePortScheduleControl OBJECT IDENTIFIER ::= { slePortSchedule 2 }

		
		-- *******.4.1.6296.**********.1
		slePortScheduleControlRequest OBJECT-TYPE
			SYNTAX SlePortScheduleControlRequestType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortScheduleControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleScheduleControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortScheduleControl 2 }

		
		-- *******.4.1.6296.**********.3
		slePortScheduleControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortScheduleControl 3 }

		
		-- *******.4.1.6296.**********.4
		slePortScheduleControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortScheduleControl 4 }

		
		-- *******.4.1.6296.**********.5
		slePortScheduleControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortScheduleControl 5 }

		
		-- *******.4.1.6296.**********.6
		slePortScheduleControlInterfaceIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Index"
			::= { slePortScheduleControl 6 }

		
		-- *******.4.1.6296.**********.7
		slePortScheduleControlMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				strictqueueing(1),
				wrr(2),
				wfq(3),
				rr(4),
				drr(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The scheduling mode of port"
			::= { slePortScheduleControl 7 }

		
		-- *******.4.1.6296.**********
		slePortScheduleNotification OBJECT IDENTIFIER ::= { slePortSchedule 3 }

		
		-- *******.4.1.6296.**********.1
		slePortScheduleModeChanged NOTIFICATION-TYPE
			OBJECTS { slePortScheduleMode, slePortScheduleControlRequest, slePortScheduleControlTimeStamp, slePortScheduleControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePortScheduleNotification 1 }

		
--  2.7
		-- *******.4.1.6296.101.10.5
		sleQueue OBJECT IDENTIFIER ::= { sleQoS 5 }

		
		-- *******.4.1.6296.**********
		sleQueueTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleQueueEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueue 1 }

		
		-- *******.4.1.6296.**********.1
		sleQueueEntry OBJECT-TYPE
			SYNTAX SleQueueEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { slePortSchedulInterfaceIndex, sleQueueId }
			::= { sleQueueTable 1 }

		
		SleQueueEntry ::=
			SEQUENCE { 
				sleQueueId
					Integer32,
				sleQueueREDEnable
					INTEGER,
				sleQueueMappedCoS
					BITS,
				sleQueueWREDMinThreshold
					Integer32,
				sleQueueWREDMaxThreshold
					Integer32,
				sleQueueWREDProbMax
					Integer32,
				sleQueueMinBandwidth
					Integer32,
				sleQueueMaxBandwidth
					Integer32,
				sleQueueWeight
					Integer32,
				sleQueueMaxLatency
					Integer32,
				sleQueueMaxPackets
					Integer32,
				sleQueueQuantum
					INTEGER,
				sleQueueUsedPkts
					Counter32,
				sleQueueUsedBytes
					Counter32
			 }

		-- *******.4.1.6296.**********.1.1
		sleQueueId OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"queue index"
			::= { sleQueueEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleQueueREDEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { disable }
			::= { sleQueueEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleQueueMappedCoS OBJECT-TYPE
			SYNTAX BITS
				{
				cos0(0),
				cos1(1),
				cos2(2),
				cos3(3),
				cos4(4),
				cos5(5),
				cos6(6),
				cos7(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleQueueWREDMinThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets where WRED can start"
			::= { sleQueueEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleQueueWREDMaxThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Above max threshold, queue drops all ingress"
			::= { sleQueueEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleQueueWREDProbMax OBJECT-TYPE
			SYNTAX Integer32 (1..15)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The worst case random drop probability"
			::= { sleQueueEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleQueueMinBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value is unit of 64kbps"
			::= { sleQueueEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleQueueMaxBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value is unit of 64 kbps."
			::= { sleQueueEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleQueueWeight OBJECT-TYPE
			SYNTAX Integer32 (-1 | 1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The weight of this queue.
				The value of -1 is unlimited"
			::= { sleQueueEntry 9 }

		
		-- *******.4.1.6296.**********.1.10
		sleQueueMaxLatency OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueEntry 10 }

		
		-- *******.4.1.6296.**********.1.11
		sleQueueMaxPackets OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueEntry 11 }

		
		-- *******.4.1.6296.**********.1.12
		sleQueueQuantum OBJECT-TYPE
			SYNTAX INTEGER (-1 | 1..127)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The DRR quantum value."
			::= { sleQueueEntry 12 }

		
		-- *******.4.1.6296.**********.1.13
		sleQueueUsedPkts OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The used packets of port/queue."
			::= { sleQueueEntry 13 }

		
		-- *******.4.1.6296.**********.1.14
		sleQueueUsedBytes OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The used bytes of port/queue."
			::= { sleQueueEntry 14 }

		
		-- *******.4.1.6296.**********
		sleQueueControl OBJECT IDENTIFIER ::= { sleQueue 2 }

		
		-- *******.4.1.6296.**********.1
		sleQueueControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setQueueProfile(1),
				setQuantum(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleQueueControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleQueueControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleQueueControlTimeStamp OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleQueueControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleQueueControlInterfaceIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Index"
			::= { sleQueueControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleQueueControlId OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Queue index"
			::= { sleQueueControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleQueueControlREDEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { disable }
			::= { sleQueueControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleQueueControlMappedCoS OBJECT-TYPE
			SYNTAX BITS
				{
				cos0(0),
				cos1(1),
				cos2(2),
				cos3(3),
				cos4(4),
				cos5(5),
				cos6(6),
				cos7(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleQueueControlWREDMinThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The total number of packets where WRED can start"
			::= { sleQueueControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleQueueControlWREDMaxThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Above max threshold, queue drops all ingress"
			::= { sleQueueControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleQueueControlWREDProbMax OBJECT-TYPE
			SYNTAX Integer32 (1..15)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The worst case random drop probability"
			::= { sleQueueControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleQueueControlMinBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value is unit of 64kbps"
			::= { sleQueueControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleQueueControlMaxBandwidth OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value is unit of 64kbps"
			::= { sleQueueControl 14 }

		
		-- *******.4.1.6296.**********.15
		sleQueueControlWeight OBJECT-TYPE
			SYNTAX Integer32 (-1 | 1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The weight of this queue.
				The value of -1 is unlimited"
			::= { sleQueueControl 15 }

		
		-- *******.4.1.6296.**********.16
		sleQueueControlMaxLatency OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 16 }

		
		-- *******.4.1.6296.**********.17
		sleQueueControlMaxPackets OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleQueueControl 17 }

		
		-- *******.4.1.6296.**********.18
		sleQueueControlQuantum OBJECT-TYPE
			SYNTAX INTEGER (-1 | 1..127)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The DRR quantum value."
			::= { sleQueueControl 18 }

		
		-- *******.4.1.6296.**********
		sleQueueNotification OBJECT IDENTIFIER ::= { sleQueue 3 }

		
		-- *******.4.1.6296.**********.1
		sleQueueProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleQueueWREDMinThreshold, sleQueueWREDMaxThreshold, sleQueueWREDProbMax, sleQueueMinBandwidth, sleQueueMaxBandwidth, 
				sleQueueControlTimeStamp, sleQueueControlReqResult, sleQueueControlRequest, sleQueueWeight, sleQueueREDEnable, 
				sleQueueMappedCoS }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleQueueNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleQueueQuantumChanged NOTIFICATION-TYPE
			OBJECTS { sleQueueControlRequest, sleQueueControlTimeStamp, sleQueueControlReqResult, sleQueueControlQuantum }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleQueueNotification 2 }

		
		-- *******.4.1.6296.101.10.6
		sleCounter OBJECT IDENTIFIER ::= { sleQoS 6 }

		
		-- *******.4.1.6296.**********
		sleCounterTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleCounterEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCounter 1 }

		
		-- *******.4.1.6296.**********.1
		sleCounterEntry OBJECT-TYPE
			SYNTAX SleCounterEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleCounterIndex }
			::= { sleCounterTable 1 }

		
		SleCounterEntry ::=
			SEQUENCE { 
				sleCounterIndex
					INTEGER,
				sleCounterPackets
					Unsigned32
			 }

		-- *******.4.1.6296.**********.1.1
		sleCounterIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Counter Index"
			::= { sleCounterEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleCounterPackets OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of packets"
			::= { sleCounterEntry 2 }

		
		-- *******.4.1.6296.**********
		sleCounterControl OBJECT IDENTIFIER ::= { sleCounter 2 }

		
		-- *******.4.1.6296.**********.1
		sleCounterControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearCounter(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCounterControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleCounterControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCounterControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleCounterControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCounterControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleCounterControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCounterControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleCounterControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCounterControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleCounterControlIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCounterControl 6 }

		
		-- *******.4.1.6296.**********
		sleCounterNotification OBJECT IDENTIFIER ::= { sleCounter 3 }

		
		-- *******.4.1.6296.**********.1
		sleCounterCleared NOTIFICATION-TYPE
			OBJECTS { sleCounterControlRequest, sleCounterControlTimeStamp, sleCounterControlReqResult, sleCounterControlIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleCounterNotification 1 }

		
		-- *******.4.1.6296.101.10.7
		sleColorMarking OBJECT IDENTIFIER ::= { sleQoS 7 }

		
		-- *******.4.1.6296.**********
		sleColorMarkingTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleColorMarkingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleColorMarking 1 }

		
		-- *******.4.1.6296.**********.1
		sleColorMarkingEntry OBJECT-TYPE
			SYNTAX SleColorMarkingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleColorMarkingIndex }
			::= { sleColorMarkingTable 1 }

		
		SleColorMarkingEntry ::=
			SEQUENCE { 
				sleColorMarkingIndex
					INTEGER,
				sleColorMarkingType
					INTEGER,
				sleColorMarkingMode
					INTEGER,
				sleColorMarkingAwareMode
					INTEGER,
				sleColorMarkingCIR
					Integer32,
				sleColorMarkingCBS
					Integer32,
				sleColorMarkingPIR
					Integer32,
				sleColorMarkingPBS
					Integer32,
				sleColorMarkingEBS
					Integer32,
				sleColorMarkingRedAction
					BITS,
				sleColorMarkingYellowAction
					BITS,
				sleColorMarkingGreenAction
					BITS,
				sleColorMarkingRedDscp
					Integer32,
				sleColorMarkingYellowDscp
					Integer32,
				sleColorMarkingGreenDscp
					Integer32,
				sleColorMarkingUseCount
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1
		sleColorMarkingIndex OBJECT-TYPE
			SYNTAX INTEGER (1..2048)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Color marking table index."
			::= { sleColorMarkingEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleColorMarkingType OBJECT-TYPE
			SYNTAX INTEGER
				{
				srTCM(0),
				trTCM(1)
				}
			UNITS "Enum"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Color marking type.
				srTCM = Single Rate Three Color Marking.
				trTCM = Two Rate Three Color Marking.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleColorMarkingMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				blind(0),
				aware(1)
				}
			UNITS "Enum"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Color marking mode."
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleColorMarkingAwareMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				unprotected(1),
				protected(2)
				}
			UNITS "Enum"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Color marking aware mode.
				Used only when sleColorMarkingMode is aware."
			DEFVAL { 1 }
			::= { sleColorMarkingEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleColorMarkingCIR OBJECT-TYPE
			SYNTAX Integer32 (1..1000000)
			UNITS "Integer"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Committed Information Rate.
				The value is in unit for 64 kbps.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleColorMarkingCBS OBJECT-TYPE
			SYNTAX Integer32 (1..4000)
			UNITS "Integer"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Committed Burst Size.
				The value is in unit for square of 2 bits.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleColorMarkingPIR OBJECT-TYPE
			SYNTAX Integer32 (1..1000000)
			UNITS "Integer "
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Peak Information Rate. 
				*) Used only when sleColorMarkingMode is Aware.
				The value is in unit for 64 kbps.
				."
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 7 }

		
		-- *******.4.1.6296.**********.1.8
		sleColorMarkingPBS OBJECT-TYPE
			SYNTAX Integer32 (1..4000)
			UNITS "Integer "
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Peak Burst Size. 
				*) Used only when sleColorMarkingMode is Aware.
				The value is in unit for square of 2 bits.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 8 }

		
		-- *******.4.1.6296.**********.1.9
		sleColorMarkingEBS OBJECT-TYPE
			SYNTAX Integer32 (1..4000)
			UNITS "Integer "
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Excess Burst Size. 
				*) Used only when sleColorMarkingMode is Blind.
				The value is in unit for square of 2 bits.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 9 }

		
		-- *******.4.1.6296.**********.1.10
		sleColorMarkingRedAction OBJECT-TYPE
			SYNTAX BITS
				{
				reserve0(0),
				reserve1(1),
				reserve2(2),
				reserve3(3),
				reserve4(4),
				dropProcedence(5),
				marking(6),
				drop(7),
				dropPrecedenceGreen(8),
				dropPrecedenceYellow(9),
				dropPrecedenceRed(10)
				}
			UNITS "bits"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The action to be apply when incoming packet is colored in red."
			DEFVAL { { 0 } }
			::= { sleColorMarkingEntry 10 }

		
		-- *******.4.1.6296.**********.1.11
		sleColorMarkingYellowAction OBJECT-TYPE
			SYNTAX BITS
				{
				reserve0(0),
				reserve1(1),
				reserve2(2),
				reserve3(3),
				reserve4(4),
				dropProcedence(5),
				marking(6),
				drop(7),
				dropPrecedenceGreen(8),
				dropPrecedenceYellow(9),
				dropPrecedenceRed(10)
				}
			UNITS "bits"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The action to be apply when incoming packet is colored in yellow."
			DEFVAL { { 0 } }
			::= { sleColorMarkingEntry 11 }

		
		-- *******.4.1.6296.**********.1.12
		sleColorMarkingGreenAction OBJECT-TYPE
			SYNTAX BITS
				{
				reserve0(0),
				reserve1(1),
				reserve2(2),
				reserve3(3),
				reserve4(4),
				dropProcedence(5),
				marking(6),
				drop(7),
				dropPrecedenceGreen(8),
				dropPrecedenceYellow(9),
				dropPrecedenceRed(10)
				}
			UNITS "bits"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The action to be apply when incoming packet is colored in green. (not supported)"
			DEFVAL { { 6 } }
			::= { sleColorMarkingEntry 12 }

		
		-- *******.4.1.6296.**********.1.13
		sleColorMarkingRedDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			UNITS "Integer "
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The dscp value to be apply when incoming packet is colored in red."
			DEFVAL { 30 }
			::= { sleColorMarkingEntry 13 }

		
		-- *******.4.1.6296.**********.1.14
		sleColorMarkingYellowDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			UNITS "Integer "
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The dscp value to be apply when incoming packet is colored in yellow."
			DEFVAL { 28 }
			::= { sleColorMarkingEntry 14 }

		
		-- *******.4.1.6296.**********.1.15
		sleColorMarkingGreenDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			UNITS "Integer "
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The dscp value to be apply when incoming packet is colored in green. (not supported)"
			DEFVAL { 26 }
			::= { sleColorMarkingEntry 15 }

		
		-- *******.4.1.6296.**********.1.16
		sleColorMarkingUseCount OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of rules used this color marking profile."
			DEFVAL { 0 }
			::= { sleColorMarkingEntry 16 }

		
		-- *******.4.1.6296.**********
		sleColorMarkingControl OBJECT IDENTIFIER ::= { sleColorMarking 2 }

		
		-- *******.4.1.6296.**********.1
		sleColorMarkingControlReqest OBJECT-TYPE
			SYNTAX INTEGER { setColorMarkingProfile(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleColorMarkingControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleColorMarkingControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleColorMarkingControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleColorMarkingControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleColorMarkingControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleColorMarkingControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleColorMarkingControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleColorMarkingControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleColorMarkingControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleColorMarkingControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..2048)
			UNITS "index"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Color marking table index"
			DEFVAL { 1 }
			::= { sleColorMarkingControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleColorMarkingControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				srTCM(0),
				trTCM(1)
				}
			UNITS "enum"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Color marking type.
				srTCM = Single Rate Three Color Marking.
				trTCM = Two Rate Three Color Marking.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleColorMarkingControlMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				blind(0),
				aware(1)
				}
			UNITS "enum"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Color marking mode"
			DEFVAL { 0 }
			::= { sleColorMarkingControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleColorMarkingControlAwareMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				unprotected(1),
				protected(2)
				}
			UNITS "enum"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Color marking aware mode.
				Used only when sleColorMarkingControlMode is aware."
			DEFVAL { 1 }
			::= { sleColorMarkingControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleColorMarkingControlCIR OBJECT-TYPE
			SYNTAX Integer32 (1..1000000)
			UNITS "Integer"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Committed Information Rate.
				The value is in unit for 64 kbps
				"
			DEFVAL { 0 }
			::= { sleColorMarkingControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleColorMarkingControlCBS OBJECT-TYPE
			SYNTAX Integer32 (1..4000)
			UNITS "Integer "
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Committed Burst Size.
				The value is in unit for square of 2 bits.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleColorMarkingControlPIR OBJECT-TYPE
			SYNTAX Integer32 (1..1000000)
			UNITS "Integer "
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Peak Information Rate. 
				*) Used only when sleColorMarkingMode is Aware.
				The value is in unit for 64 kbps.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleColorMarkingControlPBS OBJECT-TYPE
			SYNTAX Integer32 (1..4000)
			UNITS "Integer "
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Peak Burst Size. 
				*) Used only when sleColorMarkingMode is Aware.
				The value is in unit for square of 2 bits.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleColorMarkingControlEBS OBJECT-TYPE
			SYNTAX Integer32 (1..4000)
			UNITS "Integer "
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Excess Burst Size. 
				*) Used only when sleColorMarkingMode is Blind.
				The value is in unit for square of 2 bits.
				"
			DEFVAL { 0 }
			::= { sleColorMarkingControl 14 }

		
		-- *******.4.1.6296.**********.15
		sleColorMarkingControlRedAction OBJECT-TYPE
			SYNTAX BITS
				{
				reserve0(0),
				reserve1(1),
				reserve2(2),
				reserve3(3),
				reserve4(4),
				dropProcedence(5),
				marking(6),
				drop(7),
				dropPrecedenceGreen(8),
				dropPrecedenceYellow(9),
				dropPrecedenceRed(10)
				}
			UNITS "bits"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The action to be apply when incoming packet is colored in red."
			DEFVAL { { 0 } }
			::= { sleColorMarkingControl 15 }

		
		-- *******.4.1.6296.**********.16
		sleColorMarkingControlYellowAction OBJECT-TYPE
			SYNTAX BITS
				{
				reserve0(0),
				reserve1(1),
				reserve2(2),
				reserve3(3),
				reserve4(4),
				dropProcedence(5),
				marking(6),
				drop(7),
				dropPrecedenceGreen(8),
				dropPrecedenceYellow(9),
				dropPrecedenceRed(10)
				}
			UNITS "bits"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The action to be apply when incoming packet is colored in yellow."
			DEFVAL { { 0 } }
			::= { sleColorMarkingControl 16 }

		
		-- *******.4.1.6296.**********.17
		sleColorMarkingControlGreenAction OBJECT-TYPE
			SYNTAX BITS
				{
				reserve0(0),
				reserve1(1),
				reserve2(2),
				reserve3(3),
				reserve4(4),
				dropProcedence(5),
				marking(6),
				drop(7),
				dropPrecedenceGreen(8),
				dropPrecedenceYellow(9),
				dropPrecedenceRed(10)
				}
			UNITS "bits"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The action to be apply when incoming packet is colored in green. (not supported)"
			DEFVAL { { 6 } }
			::= { sleColorMarkingControl 17 }

		
		-- *******.4.1.6296.**********.18
		sleColorMarkingControlRedDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			UNITS "Integer"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The dscp value to be apply when incoming packet is colored in red."
			DEFVAL { 30 }
			::= { sleColorMarkingControl 18 }

		
		-- *******.4.1.6296.**********.19
		sleColorMarkingControlYellowDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			UNITS "Integer "
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The dscp value to be apply when incoming packet is colored in yellow."
			DEFVAL { 28 }
			::= { sleColorMarkingControl 19 }

		
		-- *******.4.1.6296.**********.20
		sleColorMarkingControlGreenDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			UNITS "Integer "
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The dscp value to be apply when incoming packet is colored in green. (not supported)"
			DEFVAL { 26 }
			::= { sleColorMarkingControl 20 }

		
		-- *******.4.1.6296.**********
		sleColorMarkingNotification OBJECT IDENTIFIER ::= { sleColorMarking 3 }

		
		-- *******.4.1.6296.**********.1
		sleColorMarkingProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleColorMarkingControlReqest, sleColorMarkingControlReqResult, sleColorMarkingControlTimeStamp, sleColorMarkingType, sleColorMarkingMode, 
				sleColorMarkingAwareMode, sleColorMarkingCIR, sleColorMarkingCBS, sleColorMarkingPIR, sleColorMarkingPBS, 
				sleColorMarkingEBS, sleColorMarkingRedAction, sleColorMarkingYellowAction, sleColorMarkingGreenAction, sleColorMarkingRedDscp, 
				sleColorMarkingYellowDscp, sleColorMarkingGreenDscp }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleColorMarkingNotification 1 }

		
		-- *******.4.1.6296.101.10.8
		sleQoSGroup OBJECT-GROUP
			OBJECTS { sleQosUserFlowNum, sleQosTrafficProfileNum, sleQosCounterNum, sleQosQueueNum, sleTrafficProfileIndex, 
				sleTrafficProfileMinBandwidth, sleTrafficProfileMaxBandwidth, sleTrafficProfileMaxBurstSize, sleTrafficProfileUseCount, sleUserFlowName, 
				sleUserFlowIngressPorts, sleUserFlowEgressPorts, sleUserFlowEthernetType, sleUserFlowSrcMacAddr, sleUserFlowDstMacAddr, 
				sleUserFlowVlan, sleUserFlow8021p, sleUserFlowSrcIpAddr, sleUserFlowSrcPrefixLength, sleUserFlowDstIpAddr, 
				sleUserFlowDstPrefixLength, sleUserFlowIpPktPriorityType, sleUserFlowIpPktPriority, sleUserFlowProtocolType, sleUserFlowTcpFlag, 
				sleUserFlowSrcL4Port, sleUserFlowDstL4Port, sleUserFlowPktLen, sleUserFlowValidFlags, sleUserFlowMatchFlags, 
				sleUserFlowPriority, sleUserFlowMatchCounterId, sleUserFlowMatchTrafficProfileId, sleUserFlowMatchQueue, sleUserFlowMatchIpPktPriorityType, 
				sleUserFlowMatchIpPktPriority, sleUserFlowMatchRedirPort, sleUserFlowMatchVid, sleUserFlowMatchDstMac, sleUserFlowMatchPortMap, 
				sleUserFlowMatchEgressType, sleUserFlowMatchAction, sleUserFlowNomatchCounterId, sleUserFlowNomatchTrafficProfileId, sleUserFlowNomatchQueue, 
				sleUserFlowNomatchIpPktPriorityType, sleUserFlowNomatchIpPktPriority, sleUserFlowNomatchRedirPort, sleUserFlowNomatchVid, sleUserFlowNomatchAction, 
				sleUserFlowMatchColorMarkingId, sleUserFlowNomatchColorMarkingId, slePortSchedulInterfaceIndex, slePortScheduleMode, sleQueueId, 
				sleQueueREDEnable, sleQueueMappedCoS, sleQueueWREDMinThreshold, sleQueueWREDMaxThreshold, sleQueueWREDProbMax, 
				sleQueueMinBandwidth, sleQueueMaxBandwidth, sleQueueWeight, sleQueueMaxLatency, sleQueueMaxPackets, 
				sleCounterIndex, sleCounterPackets, sleColorMarkingIndex, sleColorMarkingType, sleColorMarkingMode, 
				sleColorMarkingAwareMode, sleColorMarkingCIR, sleColorMarkingCBS, sleColorMarkingPIR, sleColorMarkingPBS, 
				sleColorMarkingEBS, sleColorMarkingRedAction, sleColorMarkingYellowAction, sleColorMarkingGreenAction, sleColorMarkingRedDscp, 
				sleColorMarkingYellowDscp, sleColorMarkingGreenDscp, sleQosRuleMode, sleQoSBaseControlRequest, sleQoSBaseControlStatus, 
				sleQoSBaseControlTimer, sleQoSBaseControlTimeStamp, sleQoSBaseControlReqResult, sleQoSBaseControlRuleMode, sleTrafficProfileControlRequest, 
				sleTrafficProfileControlStatus, sleTrafficProfileControlTimer, sleTrafficProfileControlTimeStamp, sleTrafficProfileControlReqResult, sleTrafficProfileControlIndex, 
				sleTrafficProfileControlMinBandwidth, sleTrafficProfileControlMaxBandwidth, sleTrafficProfileControlMaxBurstSize, sleUserFlowMatchRoutePrimary, sleUserFlowMatchRouteSecondary, 
				sleUserFlowControlReqest, sleUserFlowControlStatus, sleUserFlowControlTimer, sleUserFlowControlTimeStamp, sleUserFlowControlReqResult, 
				sleUserFlowControlName, sleUserFlowControlIngressPorts, sleUserFlowControlEgressPorts, sleUserFlowControlEthernetType, sleUserFlowControlSrcMacAddr, 
				sleUserFlowControlDstMacAddr, sleUserFlowControlVlan, sleUserFlowControl8021p, sleUserFlowControlSrcIpAddr, sleUserFlowControlSrcPrefixLength, 
				sleUserFlowControlDstIpAddr, sleUserFlowControlDstPrefixLength, sleUserFlowControlIpPktPriorityType, sleUserFlowControlIpPktPriority, sleUserFlowControlProtocolType, 
				sleUserFlowControlTcpFlag, sleUserFlowControlSrcL4Port, sleUserFlowControlDstL4Port, sleUserFlowControlPktLen, sleUserFlowControlValidFlag, 
				sleUserFlowControlMatchFlag, sleUserFlowControlPriority, sleUserFlowControlMatchCounterId, sleUserFlowControlMatchTrafficProfileId, sleUserFlowControlMatchQueue, 
				sleUserFlowControlMatchIpPktPriorityType, sleUserFlowControlMatchIpPktPriority, sleUserFlowControlMatchRedirPort, sleUserFlowControlMatchVid, sleUserFlowControlMatchDstMac, 
				sleUserFlowControlMatchPortMap, sleUserFlowControlMatchEgressType, sleUserFlowControlMatchAction, sleUserFlowControlNomatchCounterId, sleUserFlowControlNomatchTrafficProfileId, 
				sleUserFlowControlNomatchQueue, sleUserFlowControlNomatchIpPktPriorityType, sleUserFlowControlNomatchIpPktPriority, sleUserFlowControlNomatchRedirPort, sleUserFlowControlNomatchVid, 
				sleUserFlowControlNomatchAction, sleUserFlowControlMatchColorMarkingId, sleUserFlowControlNomatchColorMarkingId, sleUserFlowControlInnerVlan, sleUserFlowControlInner8021p, 
				sleUserFlowControlMatchRoutePrimary, sleUserFlowControlMatchRouteSecondary, slePortScheduleControlRequest, sleScheduleControlStatus, slePortScheduleControlTimer, 
				slePortScheduleControlTimeStamp, slePortScheduleControlReqResult, slePortScheduleControlInterfaceIndex, slePortScheduleControlMode, sleQueueQuantum, 
				sleQueueControlRequest, sleQueueControlStatus, sleQueueControlTimer, sleQueueControlTimeStamp, sleQueueControlReqResult, 
				sleQueueControlInterfaceIndex, sleQueueControlId, sleQueueControlREDEnable, sleQueueControlMappedCoS, sleQueueControlWREDMinThreshold, 
				sleQueueControlWREDMaxThreshold, sleQueueControlWREDProbMax, sleQueueControlMinBandwidth, sleQueueControlMaxBandwidth, sleQueueControlWeight, 
				sleQueueControlMaxLatency, sleQueueControlMaxPackets, sleQueueControlQuantum, sleCounterControlRequest, sleCounterControlStatus, 
				sleCounterControlTimer, sleCounterControlTimeStamp, sleCounterControlReqResult, sleCounterControlIndex, sleColorMarkingControlReqest, 
				sleColorMarkingControlStatus, sleColorMarkingControlTimer, sleColorMarkingControlTimeStamp, sleColorMarkingControlReqResult, sleColorMarkingControlIndex, 
				sleColorMarkingControlType, sleColorMarkingControlMode, sleColorMarkingControlAwareMode, sleColorMarkingControlCIR, sleColorMarkingControlCBS, 
				sleColorMarkingControlPIR, sleColorMarkingControlPBS, sleColorMarkingControlEBS, sleColorMarkingControlRedAction, sleColorMarkingControlYellowAction, 
				sleColorMarkingControlGreenAction, sleColorMarkingControlRedDscp, sleColorMarkingControlYellowDscp, sleColorMarkingControlGreenDscp, sleQueueUsedPkts, 
				sleQueueUsedBytes, sleColorMarkingUseCount, sleQosColorMarkingProfileNum, sleUserFlowInnerVlan, sleUserFlowInner8021p
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleQoS 8 }

		
		-- *******.4.1.6296.101.10.9
		sleQoSControlGroup OBJECT-GROUP
			OBJECTS { sleTrafficProfileControlRequest, sleTrafficProfileControlStatus, sleTrafficProfileControlTimer, sleTrafficProfileControlTimeStamp, sleTrafficProfileControlReqResult, 
				sleTrafficProfileControlIndex, sleTrafficProfileControlMinBandwidth, sleTrafficProfileControlMaxBandwidth, sleTrafficProfileControlMaxBurstSize, sleUserFlowControlReqest, 
				sleUserFlowControlStatus, sleUserFlowControlTimer, sleUserFlowControlTimeStamp, sleUserFlowControlReqResult, sleUserFlowControlName, 
				sleUserFlowControlIngressPorts, sleUserFlowControlEgressPorts, sleUserFlowControlEthernetType, sleUserFlowControlSrcMacAddr, sleUserFlowControlDstMacAddr, 
				sleUserFlowControlVlan, sleUserFlowControl8021p, sleUserFlowControlSrcIpAddr, sleUserFlowControlSrcPrefixLength, sleUserFlowControlDstIpAddr, 
				sleUserFlowControlDstPrefixLength, sleUserFlowControlIpPktPriorityType, sleUserFlowControlIpPktPriority, sleUserFlowControlProtocolType, sleUserFlowControlTcpFlag, 
				sleUserFlowControlSrcL4Port, sleUserFlowControlDstL4Port, sleUserFlowControlPktLen, sleUserFlowControlValidFlag, sleUserFlowControlMatchFlag, 
				sleUserFlowControlPriority, sleUserFlowControlMatchCounterId, sleUserFlowControlMatchTrafficProfileId, sleUserFlowControlMatchQueue, sleUserFlowControlMatchIpPktPriorityType, 
				sleUserFlowControlMatchIpPktPriority, sleUserFlowControlMatchRedirPort, sleUserFlowControlMatchVid, sleUserFlowControlMatchDstMac, sleUserFlowControlMatchPortMap, 
				sleUserFlowControlMatchEgressType, sleUserFlowControlMatchAction, sleUserFlowControlNomatchCounterId, sleUserFlowControlNomatchTrafficProfileId, sleUserFlowControlNomatchQueue, 
				sleUserFlowControlNomatchIpPktPriorityType, sleUserFlowControlNomatchIpPktPriority, sleUserFlowControlNomatchRedirPort, sleUserFlowControlNomatchVid, sleUserFlowControlNomatchAction, 
				sleUserFlowControlMatchColorMarkingId, sleUserFlowControlNomatchColorMarkingId, sleQueueControlRequest, sleQueueControlStatus, sleQueueControlTimer, 
				sleQueueControlTimeStamp, sleQueueControlReqResult, sleQueueControlInterfaceIndex, sleQueueControlId, sleQueueControlREDEnable, 
				sleQueueControlMappedCoS, sleQueueControlWREDMinThreshold, sleQueueControlWREDMaxThreshold, sleQueueControlWREDProbMax, sleQueueControlMinBandwidth, 
				sleQueueControlMaxBandwidth, sleQueueControlWeight, sleQueueControlMaxLatency, sleUserFlowControlInnerVlan, sleUserFlowControlInner8021p, 
				sleQosUserFlowNum, sleQosTrafficProfileNum, sleQosCounterNum, sleQosQueueNum, sleQosColorMarkingProfileNum, 
				sleQosRuleMode, sleQoSBaseControlRequest, sleQoSBaseControlStatus, sleQoSBaseControlTimer, sleQoSBaseControlTimeStamp, 
				sleQoSBaseControlReqResult, sleQoSBaseControlRuleMode, sleTrafficProfileIndex, sleTrafficProfileMinBandwidth, sleTrafficProfileMaxBandwidth, 
				sleTrafficProfileMaxBurstSize, sleTrafficProfileUseCount, sleUserFlowName, sleUserFlowIngressPorts, sleUserFlowEgressPorts, 
				sleUserFlowEthernetType, sleUserFlowSrcMacAddr, sleUserFlowDstMacAddr, sleUserFlowVlan, sleUserFlow8021p, 
				sleUserFlowSrcIpAddr, sleUserFlowSrcPrefixLength, sleUserFlowDstIpAddr, sleUserFlowDstPrefixLength, sleUserFlowIpPktPriorityType, 
				sleUserFlowIpPktPriority, sleUserFlowProtocolType, sleUserFlowTcpFlag, sleUserFlowSrcL4Port, sleUserFlowDstL4Port, 
				sleUserFlowPktLen, sleUserFlowValidFlags, sleUserFlowMatchFlags, sleUserFlowPriority, sleUserFlowMatchCounterId, 
				sleUserFlowMatchTrafficProfileId, sleUserFlowMatchQueue, sleUserFlowMatchIpPktPriorityType, sleUserFlowMatchIpPktPriority, sleUserFlowMatchRedirPort, 
				sleUserFlowMatchVid, sleUserFlowMatchDstMac, sleUserFlowMatchPortMap, sleUserFlowMatchEgressType, sleUserFlowMatchAction, 
				sleUserFlowNomatchCounterId, sleUserFlowNomatchTrafficProfileId, sleUserFlowNomatchQueue, sleUserFlowNomatchIpPktPriorityType, sleUserFlowNomatchIpPktPriority, 
				sleUserFlowNomatchRedirPort, sleUserFlowNomatchVid, sleUserFlowNomatchAction, sleUserFlowMatchColorMarkingId, sleUserFlowNomatchColorMarkingId, 
				sleUserFlowInnerVlan, sleUserFlowInner8021p, sleUserFlowMatchRoutePrimary, sleUserFlowMatchRouteSecondary, sleUserFlowControlMatchRoutePrimary, 
				sleUserFlowControlMatchRouteSecondary, slePortSchedulInterfaceIndex, slePortScheduleMode, sleQueueId, sleQueueREDEnable, 
				sleQueueMappedCoS, sleQueueWREDMinThreshold, sleQueueWREDMaxThreshold, sleQueueWREDProbMax, sleQueueMinBandwidth, 
				sleQueueMaxBandwidth, sleQueueWeight, sleQueueMaxLatency, sleQueueMaxPackets, sleQueueQuantum, 
				sleQueueControlQuantum, sleCounterIndex, sleCounterPackets, sleColorMarkingIndex, sleColorMarkingType, 
				sleColorMarkingMode, sleColorMarkingAwareMode, sleColorMarkingCIR, sleColorMarkingCBS, sleColorMarkingPIR, 
				sleColorMarkingPBS, sleColorMarkingEBS, sleColorMarkingRedAction, sleColorMarkingYellowAction, sleColorMarkingGreenAction, 
				sleColorMarkingRedDscp, sleColorMarkingYellowDscp, sleColorMarkingGreenDscp, sleColorMarkingUseCount, sleQueueControlMaxPackets, 
				slePortScheduleControlRequest, sleScheduleControlStatus, slePortScheduleControlTimer, slePortScheduleControlTimeStamp, slePortScheduleControlReqResult, 
				slePortScheduleControlInterfaceIndex, slePortScheduleControlMode, sleCounterControlRequest, sleCounterControlStatus, sleCounterControlTimer, 
				sleCounterControlTimeStamp, sleCounterControlReqResult, sleCounterControlIndex, sleColorMarkingControlReqest, sleColorMarkingControlStatus, 
				sleColorMarkingControlTimer, sleColorMarkingControlTimeStamp, sleColorMarkingControlReqResult, sleColorMarkingControlIndex, sleColorMarkingControlType, 
				sleColorMarkingControlMode, sleColorMarkingControlAwareMode, sleColorMarkingControlCIR, sleColorMarkingControlCBS, sleColorMarkingControlPIR, 
				sleColorMarkingControlPBS, sleColorMarkingControlEBS, sleColorMarkingControlRedAction, sleColorMarkingControlYellowAction, sleColorMarkingControlGreenAction, 
				sleColorMarkingControlRedDscp, sleColorMarkingControlYellowDscp, sleColorMarkingControlGreenDscp }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleQoS 9 }

		
		-- *******.4.1.6296.101.10.11
		sleQoSNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleTrafficProfileChanged, sleUserFlowCreated, sleUserFlowDestroyed, sleUserFlowClassifierProfileChanged, sleUserFlowMatchActionProfileChanged, 
				sleUserFlowNomatchActionProfileChanged, slePortScheduleModeChanged, sleQueueProfileChanged, sleCounterCleared, sleColorMarkingProfileChanged, 
				sleQoSBaseRuleModeChanged, sleQueueQuantumChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleQoS 11 }

		
	
	END

--
-- sle-qos-mib.mib
--
