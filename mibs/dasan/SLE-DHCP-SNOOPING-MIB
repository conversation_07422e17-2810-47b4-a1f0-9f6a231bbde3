--
-- sle-Dhcp-snooping-mib.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 285
-- Monday, August 29, 2005 at 13:16:22
--

	SLE-DHCP-SNOOPING-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			InterfaceIndex			
				FROM IF-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, Inte<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OBJECT-TYPE, 
			MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-<PERSON><PERSON>			
			MacAddress			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.6296.101.12
		sleDhcpSnooping MODULE-IDENTITY 
			LAST-UPDATED "200507291407Z"		-- July 29, 2005 at 14:07 GMT
			ORGANIZATION 
				"HANASOFT"
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"This MIB contains all needed informations about DHCP Snooping
				 and all supported sle DHCP Snooping features."
			REVISION "200507291425Z"		-- July 29, 2005 at 14:25 GMT
			DESCRIPTION 
				" "
			::= { sleMgmt 12 }

		
	
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.12.1
		sleGlobal OBJECT IDENTIFIER::= { sleDhcpSnooping 1 }

		
		-- *******.4.1.6296.**********
		sleGlobalInfo OBJECT IDENTIFIER::= { sleGlobal 1 }

		
		-- *******.4.1.6296.**********.1
		sleFeatureEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				inactive(0),
				active(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates whether the DHCP Snooping feature is
				 enabled at the device level."
			::= { sleGlobalInfo 1 }

		
		-- *******.4.1.6296.**********
		sleGlobalControl OBJECT IDENTIFIER::= { sleGlobal 2 }

		
		-- *******.4.1.6296.**********.1
		sleGlobalControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setSnoopingEnable(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { sleGlobalControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleGlobalControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { sleGlobalControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleGlobalControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { sleGlobalControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleGlobalControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleGlobalControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleGlobalControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleGlobalControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleGlobalControlFeatureEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				inactive(0),
				active(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleGlobalControl 6 }

		
		-- *******.4.1.6296.**********
		sleGlobalNotification OBJECT IDENTIFIER::= { sleGlobal 3 }

		
		-- *******.4.1.6296.**********.1
		sleGlobalFeatureEnableChanged NOTIFICATION-TYPE
			OBJECTS { sleGlobalControlRequest, sleGlobalControlTimeStamp, sleGlobalControlReqResult, sleFeatureEnable }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleGlobalNotification 1 }

		
		-- *******.4.1.6296.101.12.2
		slePortSrcGuard OBJECT IDENTIFIER::= { sleDhcpSnooping 2 }

		
		-- *******.4.1.6296.**********
		slePortSrcGuardConfig OBJECT IDENTIFIER::= { slePortSrcGuard 1 }

		
		-- *******.4.1.6296.**********.1
		slePortSrcGuardConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePortSrcGuardConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A table provides the mechanism to enable or disable  
				 IP Source Guard at each physical port capable of
				 this feature. 
				
				 When DHCP Snooping is enabled at an interface, a list of
				 IP addresses is obtained through DHCP Snooping for this
				 particular port. If IP Source Guard is enabled, only
				 traffic from these IP addresses is allowed to pass through
				 the port."
			::= { slePortSrcGuardConfig 1 }

		
		-- *******.4.1.6296.**********.1.1
		slePortSrcGuardConfigEntry OBJECT-TYPE
			SYNTAX SlePortSrcGuardConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row instance contains the configuration to enable
				 or disable IP Source Guard at each physical port
				 capable of this feature."
			INDEX { slePortSrcGuardIndex }
			::= { slePortSrcGuardConfigTable 1 }

		
		SlePortSrcGuardConfigEntry ::=
			SEQUENCE { 
				slePortSrcGuardIndex
					INTEGER,
				slePortSrcGuardEnable
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1.1
		slePortSrcGuardIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Same as sleBridgePortIndex."
			::= { slePortSrcGuardConfigEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		slePortSrcGuardEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates whether IP Source Guard is enabled
				 at this port. 
				
				 If this object is set to 'true', IP Source Guard is
				 enabled. Traffic coming to this interface will be forwarded
				 if it is from the list of IP addresses obtained through
				 DHCP Snooping. Otherwise, it is denied.
				
				 If this object is set to 'false', IP Source Guard is
				 disabled."
			::= { slePortSrcGuardConfigEntry 2 }

		
		-- *******.4.1.6296.**********.2
		slePortSrcGuardConfigControl OBJECT IDENTIFIER::= { slePortSrcGuardConfig 2 }

		
		-- *******.4.1.6296.**********.2.1
		slePortSrcGuardConfigControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setPortSrcGuardConfig(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { slePortSrcGuardConfigControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		slePortSrcGuardConfigControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { slePortSrcGuardConfigControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		slePortSrcGuardConfigControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { slePortSrcGuardConfigControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		slePortSrcGuardConfigControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { slePortSrcGuardConfigControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		slePortSrcGuardConfigControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { slePortSrcGuardConfigControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		slePortSrcGuardConfigControlIndex OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The port number that IP Source Guard is enabled at."
			::= { slePortSrcGuardConfigControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		slePortSrcGuardConfigControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The flag to specify whether IP Source Guard is enabled or not."
			::= { slePortSrcGuardConfigControl 7 }

		
		-- *******.4.1.6296.**********.3
		slePortSrcGuardConfigNotification OBJECT IDENTIFIER::= { slePortSrcGuardConfig 3 }

		
		-- *******.4.1.6296.**********.3.1
		slePortSrcGuardConfigEnableChanged NOTIFICATION-TYPE
			OBJECTS { slePortSrcGuardConfigControlRequest, slePortSrcGuardConfigControlTImeStamp, slePortSrcGuardConfigControlReqResult, slePortSrcGuardEnable }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePortSrcGuardConfigNotification 1 }

		
		-- *******.4.1.6296.**********
		slePortSrcGuardAddress OBJECT IDENTIFIER::= { slePortSrcGuard 2 }

		
		-- *******.4.1.6296.**********.1
		slePortSrcGuardAddressTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePortSrcGuardAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A table provides the information on IP addresses used
				 for IP Source Guard purpose at each physical port
				 capable of this feature."
			::= { slePortSrcGuardAddress 1 }

		
		-- *******.4.1.6296.**********.1.1
		slePortSrcGuardAddressEntry OBJECT-TYPE
			SYNTAX SlePortSrcGuardAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row instance contains the IP address type and IP
				 address used for IP Source Guard purpose at each
				 physical port capable of this feature."
			INDEX { slePortSrcGuardIndex, slePortSrcGuardAddressIp }
			::= { slePortSrcGuardAddressTable 1 }

		
		SlePortSrcGuardAddressEntry ::=
			SEQUENCE { 
				slePortSrcGuardAddressIp
					IpAddress,
				slePortSrcGuardAddressMask
					IpAddress,
				slePortSrcGuardAddressMac
					MacAddress,
				slePortSrcGuardAddressLease
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1.1
		slePortSrcGuardAddressIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the IP address obtained at
				 this port through DHCP Snooping."
			::= { slePortSrcGuardAddressEntry 1 }

		
		-- *******.4.1.6296.**********.1.1.2
		slePortSrcGuardAddressMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the subnet mask of IP address 
				 obtained at this port through DHCP Snooping."
			::= { slePortSrcGuardAddressEntry 2 }

		
		-- *******.4.1.6296.**********.1.1.3
		slePortSrcGuardAddressMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the MAC responding to IP address 
				 obtained at this port through DHCP Snooping."
			::= { slePortSrcGuardAddressEntry 3 }

		
		-- *******.4.1.6296.**********.1.1.4
		slePortSrcGuardAddressLease OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the lease time of IP address 
				 obtained at this port through DHCP Snooping."
			::= { slePortSrcGuardAddressEntry 4 }

		
		-- *******.4.1.6296.**********.2
		slePortSrcGuardAddressControl OBJECT IDENTIFIER::= { slePortSrcGuardAddress 2 }

		
		-- *******.4.1.6296.**********.2.1
		slePortSrcGuardAddressControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createPortSrcGuardAddress(1),
				destroyPortSrcGuardAddress(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { slePortSrcGuardAddressControl 1 }

		
		-- *******.4.1.6296.**********.2.2
		slePortSrcGuardAddressControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { slePortSrcGuardAddressControl 2 }

		
		-- *******.4.1.6296.**********.2.3
		slePortSrcGuardAddressControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command."
			::= { slePortSrcGuardAddressControl 3 }

		
		-- *******.4.1.6296.**********.2.4
		slePortSrcGuardAddressControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { slePortSrcGuardAddressControl 4 }

		
		-- *******.4.1.6296.**********.2.5
		slePortSrcGuardAddressControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { slePortSrcGuardAddressControl 5 }

		
		-- *******.4.1.6296.**********.2.6
		slePortSrcGuardAddressControlIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortSrcGuardAddressControl 6 }

		
		-- *******.4.1.6296.**********.2.7
		slePortSrcGuardAddressControlIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortSrcGuardAddressControl 7 }

		
		-- *******.4.1.6296.**********.2.8
		slePortSrcGuardAddressControlMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePortSrcGuardAddressControl 8 }

		
		-- *******.4.1.6296.**********.3
		slePortSrcGuardAddressNotification OBJECT IDENTIFIER::= { slePortSrcGuardAddress 3 }

		
		-- *******.4.1.6296.**********.3.1
		slePortSrcGuardAddressCreated NOTIFICATION-TYPE
			OBJECTS { slePortSrcGuardAddressControlRequest, slePortSrcGuardAddressControlTImeStamp, slePortSrcGuardAddressControlReqResult, slePortSrcGuardAddressIp, slePortSrcGuardAddressMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePortSrcGuardAddressNotification 1 }

		
		-- *******.4.1.6296.**********.3.2
		slePortSrcGuardAddressDestroyed NOTIFICATION-TYPE
			OBJECTS { slePortSrcGuardAddressControlRequest, slePortSrcGuardAddressControlTImeStamp, slePortSrcGuardAddressControlReqResult, slePortSrcGuardAddressIp, slePortSrcGuardAddressMask
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePortSrcGuardAddressNotification 2 }

		
	
	END

--
-- sle-Dhcp-snooping-mib.my
--
