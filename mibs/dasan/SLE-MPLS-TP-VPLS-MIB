--
-- sle-mpls-tp-vpls-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, February 05, 2016 at 10:50:22
--

	SLE-MPLS-TP-VPLS-MIB DEFINITIONS ::= <PERSON><PERSON><PERSON>
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			IANAPwTypeTC			
				FROM IANA-PWE3-MIB			
			InterfaceIndexOrZero			
				FROM IF-MIB			
			MplsCcId, MplsIccId			
				FROM MPLS-TC-EXT-STD-MIB			
			MplsLabel			
				FROM MPLS-TC-STD-MIB			
			VlanIdOrAnyOrNone			
				FROM Q-BRIDGE-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			SnmpAdminString			
				FROM SNMP-FRAMEWORK-MIB			
			transmission, TimeTicks, Ip<PERSON>ddress, Unsigned<PERSON>, <PERSON>au<PERSON>32, 
			Counter64, OBJECT-TYPE, MODULE-IDENTI<PERSON>, OBJECT-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleMplsTpVpls MODULE-IDENTITY 
			LAST-UPDATED "201511030000Z"		-- November 03, 2015 at 00:00 GMT
			ORGANIZATION 
				"Dasan Networks"
			CONTACT-INFO 
				"Gyerok Kwon 
				Dasan Networks
				Email:  <EMAIL>
				
				Kantharaj B M
				Dasan Networks
				Email:  <EMAIL>
				
				DongChel Shin (Chris)
				Dasan Networks
				Email:  <EMAIL>
				
				Comments about this document should be emailed
				directly to the Dasan support email ID at
				<EMAIL>."
			DESCRIPTION 
				"This MIB module containg the managed object definition for
				MPLS-TP Virtual Private LAN Service(VPLS) operation.
				
				copyright (c) 2015 Dasan Networks and the persons identified
				as authors of the code. All rights reserved."
			REVISION "201511030000Z"		-- November 03, 2015 at 00:00 GMT
			DESCRIPTION 
				"Initial version."
			::= { sleMpls 16 }

		
	
	
--
-- Node definitions
--
	
		sleMpls OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"SLE MPLS."
			::= { sleMgmt 16 }

		
		sleMplsTpVplsCfg OBJECT IDENTIFIER ::= { sleMplsTpVpls 1 }

		
		sleMplsTpVplsCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpVplsCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for configuring and
				status monitoring that is common to all service types
				and PSN types of VPLS."
			::= { sleMplsTpVplsCfg 1 }

		
		sleMplsTpVplsCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpVplsCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpPwCfgNameInfo."
			INDEX { sleMplsTpVplsCfgInfoId }
			::= { sleMplsTpVplsCfgInfoTable 1 }

		
		SleMplsTpVplsCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpVplsCfgInfoId
					Unsigned32,
				sleMplsTpVplsCfgInfoName
					OCTET STRING,
				sleMplsTpVplsCfgInfoMacLearning
					INTEGER,
				sleMplsTpVplsCfgInfoMacLearningLimit
					INTEGER,
				sleMplsTpVplsCfgInfoServiceType
					IANAPwTypeTC,
				sleMplsTpVplsCfgInfoSignallingProto
					INTEGER,
				sleMplsTpVplsCfgInfoGroupId
					Unsigned32,
				sleMplsTpVplsCfgInfoDescription
					SnmpAdminString,
				sleMplsTpVplsCfgInfoMtu
					INTEGER
			 }

		sleMplsTpVplsCfgInfoId OBJECT-TYPE
			SYNTAX Unsigned32 (0..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"VPLS Id. It identifies an entry
				on this table."
			::= { sleMplsTpVplsCfgInfoEntry 1 }

		
		sleMplsTpVplsCfgInfoName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Name. It identifies an entry
				on this table."
			::= { sleMplsTpVplsCfgInfoEntry 2 }

		
		sleMplsTpVplsCfgInfoMacLearning OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use to indentify the MAC Learning status."
			::= { sleMplsTpVplsCfgInfoEntry 3 }

		
		sleMplsTpVplsCfgInfoMacLearningLimit OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"MAC Learning Limit of the VPLS.
				
				32767 is default mac learning limit."
			::= { sleMplsTpVplsCfgInfoEntry 4 }

		
		sleMplsTpVplsCfgInfoServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Service type of VPLS."
			DEFVAL { ethernet }
			::= { sleMplsTpVplsCfgInfoEntry 5 }

		
		sleMplsTpVplsCfgInfoSignallingProto OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				static(1),
				bgp(2),
				ldp(3),
				bgpAdLdp(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Signalling proto id of VPLS."
			::= { sleMplsTpVplsCfgInfoEntry 6 }

		
		sleMplsTpVplsCfgInfoGroupId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Group Id of VPLS."
			::= { sleMplsTpVplsCfgInfoEntry 7 }

		
		sleMplsTpVplsCfgInfoDescription OBJECT-TYPE
			SYNTAX SnmpAdminString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description of VPLS."
			::= { sleMplsTpVplsCfgInfoEntry 8 }

		
		sleMplsTpVplsCfgInfoMtu OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Mtu of VPLS."
			DEFVAL { 1500 }
			::= { sleMplsTpVplsCfgInfoEntry 9 }

		
		sleMplsTpVplsCfgControl OBJECT IDENTIFIER ::= { sleMplsTpVplsCfg 2 }

		
		sleMplsTpVplsCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createVpls(1),
				deleteVpls(2),
				setVplsMACLearningDisable(3),
				setVplsMACLearningLimit(4),
				setVplsACGroup(5),
				setVplsDescription(6),
				setVplsMtu(7),
				setVplsServiceType(8),
				unsetVplsMACLearningDisable(9),
				unsetVplsMACLearningLimit(10),
				unsetVplsACGroup(11),
				unsetVplsDesc(12),
				unsetVplsMtu(13),
				unsetVplsServiceType(14)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"his object holds the possible read-write columns that can be modified 
				in the sleMplsTpVplsCfg table. For each read-write column of 
				sleMplsTpVplsCfg table, a Set Operation control value is added in this 
				object."
			::= { sleMplsTpVplsCfgControl 1 }

		
		sleMplsTpVplsCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpVplsCfgControl 2 }

		
		sleMplsTpVplsCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpVplsCfgControl 3 }

		
		sleMplsTpVplsCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpVplsCfgControl 4 }

		
		sleMplsTpVplsCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpVplsCfgControl 5 }

		
		sleMplsTpVplsCfgControlId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Id. It identifies an entry
				on this table."
			::= { sleMplsTpVplsCfgControl 6 }

		
		sleMplsTpVplsCfgControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Name. It identifies an entry
				on this table."
			::= { sleMplsTpVplsCfgControl 7 }

		
		sleMplsTpVplsCfgControlMacLearningLimit OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"MAC Learning Limit of the VPLS."
			::= { sleMplsTpVplsCfgControl 8 }

		
		sleMplsTpVplsCfgControlServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Service type of VPLS."
			DEFVAL { ethernet }
			::= { sleMplsTpVplsCfgControl 9 }

		
		sleMplsTpVplsCfgControlGroupId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Group Id of VPLS."
			::= { sleMplsTpVplsCfgControl 10 }

		
		sleMplsTpVplsCfgControlDescription OBJECT-TYPE
			SYNTAX SnmpAdminString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description of VPLS."
			::= { sleMplsTpVplsCfgControl 11 }

		
		sleMplsTpVplsCfgControlMtu OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Mtu of VPLS."
			DEFVAL { 1500 }
			::= { sleMplsTpVplsCfgControl 12 }

		
		sleMplsTpVplsIfCfg OBJECT IDENTIFIER ::= { sleMplsTpVpls 2 }

		
		sleMplsTpVplsIfCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpVplsIfCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for binding the VPLS on 
				the MPLS-TP service provider interface."
			::= { sleMplsTpVplsIfCfg 1 }

		
		sleMplsTpVplsIfCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpVplsIfCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpVplsIfCfgInfoIndex."
			INDEX { sleMplsTpVplsIfCfgInfoName, sleMplsTpVplsIfCfgInfoIfIndex }
			::= { sleMplsTpVplsIfCfgInfoTable 1 }

		
		SleMplsTpVplsIfCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpVplsIfCfgInfoIfIndex
					InterfaceIndexOrZero,
				sleMplsTpVplsIfCfgInfoName
					OCTET STRING,
				sleMplsTpVplsIfCfgInfoServiceType
					IANAPwTypeTC,
				sleMplsTpVplsIfCfgInfoVlanId
					VlanIdOrAnyOrNone,
				sleMplsTpVplsIfCfgInfoInnerVlanId
					VlanIdOrAnyOrNone,
				sleMplsTpVplsIfCfgInfoAction
					INTEGER
			 }

		sleMplsTpVplsIfCfgInfoIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Customer-facing/service-provider Interface Index."
			::= { sleMplsTpVplsIfCfgInfoEntry 1 }

		
		sleMplsTpVplsIfCfgInfoName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"VPLS Name which bind on interface."
			::= { sleMplsTpVplsIfCfgInfoEntry 2 }

		
		sleMplsTpVplsIfCfgInfoServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Service Type of VPLS bind on interface."
			::= { sleMplsTpVplsIfCfgInfoEntry 3 }

		
		sleMplsTpVplsIfCfgInfoVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VLAN Id of bind VPLS. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpVplsIfCfgInfoEntry 4 }

		
		sleMplsTpVplsIfCfgInfoInnerVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Inner VLAN Id of bind VPLS. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpVplsIfCfgInfoEntry 5 }

		
		sleMplsTpVplsIfCfgInfoAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				noOperation(1),
				add(2),
				remove(3),
				replace(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS operation.When pkt come with vlan-id, it says
				the operation towards what should do at egress.It is 
				applicable at egress."
			::= { sleMplsTpVplsIfCfgInfoEntry 6 }

		
		sleMplsTpVplsIfCfgControl OBJECT IDENTIFIER ::= { sleMplsTpVplsIfCfg 2 }

		
		sleMplsTpVplsIfCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setBindVplsUntaggedMode(1),
				setBindVplsSvlan(2),
				setBindVplsTaggedMode(3),
				setBindVplsQinQ(4),
				setBindVplsQinQWithAction(5),
				unsetbindvplsUnTaggedMode(6),
				unsetBindVplsTaggedMode(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpVplsIfCfg table. For each read-write column of 
				sleMplsTpVplsIfCfg table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpVplsIfCfgControl 1 }

		
		sleMplsTpVplsIfCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpVplsIfCfgControl 2 }

		
		sleMplsTpVplsIfCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpVplsIfCfgControl 3 }

		
		sleMplsTpVplsIfCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpVplsIfCfgControl 4 }

		
		sleMplsTpVplsIfCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpVplsIfCfgControl 5 }

		
		sleMplsTpVplsIfCfgControlIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Customer-facing/service-provider Interface Index."
			::= { sleMplsTpVplsIfCfgControl 6 }

		
		sleMplsTpVplsIfCfgControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Name which bind on interface."
			::= { sleMplsTpVplsIfCfgControl 7 }

		
		sleMplsTpVplsIfCfgControlServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Service Type of VPLS bind on interface."
			::= { sleMplsTpVplsIfCfgControl 8 }

		
		sleMplsTpVplsIfCfgControlVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VLAN Id of bind VPLS. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpVplsIfCfgControl 9 }

		
		sleMplsTpVplsIfCfgControlInnerVlanId OBJECT-TYPE
			SYNTAX VlanIdOrAnyOrNone
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Inner VLAN Id of bind VPLS. It should have valid value when
				sleMplsTpPwIfCfgServiceType is ethernetTagged."
			::= { sleMplsTpVplsIfCfgControl 10 }

		
		sleMplsTpVplsIfCfgControlAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				noOperation(1),
				add(2),
				remove(3),
				replace(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS operation.When pkt come with vlan-id, it says
				the operation towards what should do at egress.It is 
				applicable at egress."
			::= { sleMplsTpVplsIfCfgControl 11 }

		
		sleMplsTpVplsMeshCfg OBJECT IDENTIFIER ::= { sleMplsTpVpls 3 }

		
		sleMplsTpVplsMeshCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpVplsMeshCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for VPLS Mesh peer's."
			::= { sleMplsTpVplsMeshCfg 1 }

		
		sleMplsTpVplsMeshCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpVplsMeshCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpVplsCfgInfoId and sleMplsTpVplsMeshInfoNodeId."
			INDEX { sleMplsTpVplsCfgInfoId, sleMplsTpVplsMeshCfgInfoPeerNodeId }
			::= { sleMplsTpVplsMeshCfgInfoTable 1 }

		
		SleMplsTpVplsMeshCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpVplsMeshCfgInfoPeerNodeId
					IpAddress,
				sleMplsTpVplsMeshCfgInfoPeerNodeType
					INTEGER,
				sleMplsTpVplsMeshCfgInfoPeerGlobalId
					Unsigned32,
				sleMplsTpVplsMeshCfgInfoPeerCc
					MplsCcId,
				sleMplsTpVplsMeshCfgInfoPeerIcc
					MplsIccId,
				sleMplsTpVplsMeshCfgInfoTunnelId
					INTEGER,
				sleMplsTpVplsMeshCfgInfoTunnelName
					OCTET STRING,
				sleMplsTpVplsMeshCfgInfoOwner
					INTEGER,
				sleMplsTpVplsMeshCfgInfoTunnelPath
					INTEGER,
				sleMplsTpVplsMeshCfgInfoInLabel
					MplsLabel,
				sleMplsTpVplsMeshCfgInfoOutLabel
					MplsLabel,
				sleMplsTpVplsMeshCfgInfoOutInterface
					InterfaceIndexOrZero,
				sleMplsTpVplsMeshCfgInfoTunnelLabel
					MplsLabel,
				sleMplsTpVplsMeshCfgInfoState
					INTEGER,
				sleMplsTpVplsMeshCfgInfoQosServicePolicy
					OCTET STRING
			 }

		sleMplsTpVplsMeshCfgInfoPeerNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Node Id."
			::= { sleMplsTpVplsMeshCfgInfoEntry 1 }

		
		sleMplsTpVplsMeshCfgInfoPeerNodeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Node Type."
			::= { sleMplsTpVplsMeshCfgInfoEntry 2 }

		
		sleMplsTpVplsMeshCfgInfoPeerGlobalId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Global Id."
			::= { sleMplsTpVplsMeshCfgInfoEntry 3 }

		
		sleMplsTpVplsMeshCfgInfoPeerCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Country Code(CC) Name."
			::= { sleMplsTpVplsMeshCfgInfoEntry 4 }

		
		sleMplsTpVplsMeshCfgInfoPeerIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Icc Name."
			::= { sleMplsTpVplsMeshCfgInfoEntry 5 }

		
		sleMplsTpVplsMeshCfgInfoTunnelId OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-id."
			::= { sleMplsTpVplsMeshCfgInfoEntry 6 }

		
		sleMplsTpVplsMeshCfgInfoTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-Name."
			::= { sleMplsTpVplsMeshCfgInfoEntry 7 }

		
		sleMplsTpVplsMeshCfgInfoOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(1),
				pwIdFecSignaling(2),
				genFecSignaling(3),
				l2tpControlProtocol(4),
				other(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-Name."
			::= { sleMplsTpVplsMeshCfgInfoEntry 8 }

		
		sleMplsTpVplsMeshCfgInfoTunnelPath OBJECT-TYPE
			SYNTAX INTEGER
				{
				forward(0),
				reverse(1),
				none(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-Path."
			DEFVAL { forward }
			::= { sleMplsTpVplsMeshCfgInfoEntry 9 }

		
		sleMplsTpVplsMeshCfgInfoInLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Fib entry In-label."
			::= { sleMplsTpVplsMeshCfgInfoEntry 10 }

		
		sleMplsTpVplsMeshCfgInfoOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Fib entry Out-label."
			::= { sleMplsTpVplsMeshCfgInfoEntry 11 }

		
		sleMplsTpVplsMeshCfgInfoOutInterface OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Fib entry Out-Interface."
			::= { sleMplsTpVplsMeshCfgInfoEntry 12 }

		
		sleMplsTpVplsMeshCfgInfoTunnelLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel out label."
			::= { sleMplsTpVplsMeshCfgInfoEntry 13 }

		
		sleMplsTpVplsMeshCfgInfoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer State."
			::= { sleMplsTpVplsMeshCfgInfoEntry 14 }

		
		sleMplsTpVplsMeshCfgInfoQosServicePolicy OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer qos input service policy."
			::= { sleMplsTpVplsMeshCfgInfoEntry 15 }

		
		sleMplsTpVplsMeshCfgControl OBJECT IDENTIFIER ::= { sleMplsTpVplsMeshCfg 2 }

		
		sleMplsTpVplsMeshCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createMplsTpVplsPeer(1),
				createMplsTpVplsPeerWithTunnelPath(2),
				deleteVplsPeer(3),
				setVplsPeerFibEntry(4),
				unsetVplsPeerFibEntry(5),
				setVplsPeerQosServicePolicy(6),
				unsetVplsPeerQosServicePolicy(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpVplsMeshCfg table. For each read-write column of 
				sleMplsTpVplsMeshCfg table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpVplsMeshCfgControl 1 }

		
		sleMplsTpVplsMeshCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpVplsMeshCfgControl 2 }

		
		sleMplsTpVplsMeshCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is configured for every control table."
			::= { sleMplsTpVplsMeshCfgControl 3 }

		
		sleMplsTpVplsMeshCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpVplsMeshCfgControl 4 }

		
		sleMplsTpVplsMeshCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpVplsMeshCfgControl 5 }

		
		sleMplsTpVplsMeshCfgControlVplsId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS ID which this peer should configure."
			::= { sleMplsTpVplsMeshCfgControl 6 }

		
		sleMplsTpVplsMeshCfgControlPeerNodeId OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Node Id."
			::= { sleMplsTpVplsMeshCfgControl 7 }

		
		sleMplsTpVplsMeshCfgControlPeerNodeType OBJECT-TYPE
			SYNTAX INTEGER
				{
				ietf(1),
				itut(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Node Type."
			::= { sleMplsTpVplsMeshCfgControl 8 }

		
		sleMplsTpVplsMeshCfgControlPeerGlobalId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Global Id."
			::= { sleMplsTpVplsMeshCfgControl 9 }

		
		sleMplsTpVplsMeshCfgControlPeerCc OBJECT-TYPE
			SYNTAX MplsCcId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Country Code(CC) Name."
			::= { sleMplsTpVplsMeshCfgControl 10 }

		
		sleMplsTpVplsMeshCfgControlPeerIcc OBJECT-TYPE
			SYNTAX MplsIccId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Icc Name."
			::= { sleMplsTpVplsMeshCfgControl 11 }

		
		sleMplsTpVplsMeshCfgControlTunnelId OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-id."
			::= { sleMplsTpVplsMeshCfgControl 12 }

		
		sleMplsTpVplsMeshCfgControlTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-Name."
			::= { sleMplsTpVplsMeshCfgControl 13 }

		
		sleMplsTpVplsMeshCfgControlOwner OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(1),
				pwIdFecSignaling(2),
				genFecSignaling(3),
				l2tpControlProtocol(4),
				other(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-Name."
			::= { sleMplsTpVplsMeshCfgControl 14 }

		
		sleMplsTpVplsMeshCfgControlTunnelPath OBJECT-TYPE
			SYNTAX INTEGER
				{
				forward(0),
				reverse(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Tunnel-Path."
			DEFVAL { forward }
			::= { sleMplsTpVplsMeshCfgControl 15 }

		
		sleMplsTpVplsMeshCfgControlInLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Fib entry In-label."
			::= { sleMplsTpVplsMeshCfgControl 16 }

		
		sleMplsTpVplsMeshCfgControlOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer Fib entry Out-label."
			::= { sleMplsTpVplsMeshCfgControl 17 }

		
		sleMplsTpVplsMeshCfgControlQosServicePolicy OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Mesh Peer qos input service policy."
			::= { sleMplsTpVplsMeshCfgControl 18 }

		
		sleMplsTpVplsSpokeCfg OBJECT IDENTIFIER ::= { sleMplsTpVpls 4 }

		
		sleMplsTpVplsSpokeCfgInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpVplsSpokeCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for VPLS Spoke vc."
			::= { sleMplsTpVplsSpokeCfg 1 }

		
		sleMplsTpVplsSpokeCfgInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpVplsSpokeCfgInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpVplsCfgInfoId and sleMplsTpVplsSpokeInfoVcName."
			INDEX { sleMplsTpVplsCfgInfoId, sleMplsTpVplsSpokeCfgInfoVcName }
			::= { sleMplsTpVplsSpokeCfgInfoTable 1 }

		
		SleMplsTpVplsSpokeCfgInfoEntry ::=
			SEQUENCE { 
				sleMplsTpVplsSpokeCfgInfoVcName
					OCTET STRING,
				sleMplsTpVplsSpokeCfgInfoTunnelName
					OCTET STRING,
				sleMplsTpVplsSpokeCfgInfoServiceType
					IANAPwTypeTC,
				sleMplsTpVplsSpokeCfgInfoInLabel
					MplsLabel,
				sleMplsTpVplsSpokeCfgInfoOutLabel
					MplsLabel,
				sleMplsTpVplsSpokeCfgInfoOutInterface
					InterfaceIndexOrZero,
				sleMplsTpVplsSpokeCfgInfoState
					INTEGER,
				sleMplsTpVplsSpokeCfgInfoQosServicePolicy
					OCTET STRING
			 }

		sleMplsTpVplsSpokeCfgInfoVcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke VC Name."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 1 }

		
		sleMplsTpVplsSpokeCfgInfoTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke Tunnel Name."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 2 }

		
		sleMplsTpVplsSpokeCfgInfoServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke Service Type."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 3 }

		
		sleMplsTpVplsSpokeCfgInfoInLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke Fib In label."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 4 }

		
		sleMplsTpVplsSpokeCfgInfoOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke Fib Out label."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 5 }

		
		sleMplsTpVplsSpokeCfgInfoOutInterface OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke Fib Out Interface."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 6 }

		
		sleMplsTpVplsSpokeCfgInfoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke Admin status."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 7 }

		
		sleMplsTpVplsSpokeCfgInfoQosServicePolicy OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VPLS Spoke qos input service policy."
			::= { sleMplsTpVplsSpokeCfgInfoEntry 8 }

		
		sleMplsTpVplsSpokeCfgControl OBJECT IDENTIFIER ::= { sleMplsTpVplsSpokeCfg 2 }

		
		sleMplsTpVplsSpokeCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createMplsTpVplsSpoke(1),
				deleteMplsTpVplsSpoke(2),
				setVplsSpokeFibEntry(3),
				unsetVplsSpokeFibEntry(4),
				setVplsSpokeQosServicePolicy(5),
				unsetVplsSpokeQosServicePolicy(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpVplsSpokeCfg table. For each read-write column of 
				sleMplsTpVplsSpokeCfg table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpVplsSpokeCfgControl 1 }

		
		sleMplsTpVplsSpokeCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpVplsSpokeCfgControl 2 }

		
		sleMplsTpVplsSpokeCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is 
				configured for every control table."
			::= { sleMplsTpVplsSpokeCfgControl 3 }

		
		sleMplsTpVplsSpokeCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpVplsSpokeCfgControl 4 }

		
		sleMplsTpVplsSpokeCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpVplsSpokeCfgControl 5 }

		
		sleMplsTpVplsSpokeCfgControlVplsId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Name which this peer should configure."
			::= { sleMplsTpVplsSpokeCfgControl 6 }

		
		sleMplsTpVplsSpokeCfgControlVcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Spoke VC Name."
			::= { sleMplsTpVplsSpokeCfgControl 7 }

		
		sleMplsTpVplsSpokeCfgControlTunnelName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Spoke Tunnel Name."
			::= { sleMplsTpVplsSpokeCfgControl 8 }

		
		sleMplsTpVplsSpokeCfgControlServiceType OBJECT-TYPE
			SYNTAX IANAPwTypeTC
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Spoke Service Type."
			::= { sleMplsTpVplsSpokeCfgControl 9 }

		
		sleMplsTpVplsSpokeCfgControlInLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Spoke Fib In label."
			::= { sleMplsTpVplsSpokeCfgControl 10 }

		
		sleMplsTpVplsSpokeCfgControlOutLabel OBJECT-TYPE
			SYNTAX MplsLabel
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Spoke Fib Out label."
			::= { sleMplsTpVplsSpokeCfgControl 11 }

		
		sleMplsTpVplsSpokeCfgControlOutInterface OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Spoke Fib Out Interface."
			::= { sleMplsTpVplsSpokeCfgControl 12 }

		
		sleMplsTpVplsSpokeCfgControlQosServicePolicy OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Spoke qos input service policy."
			::= { sleMplsTpVplsSpokeCfgControl 13 }

		
		sleMplsTpVplsMacLearning OBJECT IDENTIFIER ::= { sleMplsTpVpls 5 }

		
		sleMplsTpVplsMacLearningInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpVplsMacLearningInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for VPLS Mac learned."
			::= { sleMplsTpVplsMacLearning 1 }

		
		sleMplsTpVplsMacLearningInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpVplsMacLearningInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is indexed by 
				sleMplsTpVplsCfgInfoId and sleMplsTpVplsMacLearningInfoMacAddress."
			INDEX { sleMplsTpVplsCfgInfoId, sleMplsTpVplsMacLearningInfoMacAddress }
			::= { sleMplsTpVplsMacLearningInfoTable 1 }

		
		SleMplsTpVplsMacLearningInfoEntry ::=
			SEQUENCE { 
				sleMplsTpVplsMacLearningInfoMacAddress
					OCTET STRING,
				sleMplsTpVplsMacLearningInfoInterfacIndex
					InterfaceIndexOrZero,
				sleMplsTpVplsMacLearningInfoMeshAddress
					IpAddress
			 }

		sleMplsTpVplsMacLearningInfoMacAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Mac learned of VPLS service."
			::= { sleMplsTpVplsMacLearningInfoEntry 1 }

		
		sleMplsTpVplsMacLearningInfoInterfacIndex OBJECT-TYPE
			SYNTAX InterfaceIndexOrZero
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Interface index of the MAC Learned."
			::= { sleMplsTpVplsMacLearningInfoEntry 2 }

		
		sleMplsTpVplsMacLearningInfoMeshAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Mac Learned Mesh address."
			::= { sleMplsTpVplsMacLearningInfoEntry 3 }

		
		sleMplsTpVplsMacLearningControl OBJECT IDENTIFIER ::= { sleMplsTpVplsMacLearning 2 }

		
		sleMplsTpVplsMacLearningControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearVplsMacAddress(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpVplsMacLearning table. For each read-write column of 
				sleMplsTpVplsMacLearning table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpVplsMacLearningControl 1 }

		
		sleMplsTpVplsMacLearningControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpVplsMacLearningControl 2 }

		
		sleMplsTpVplsMacLearningControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is 
				configured for every control table."
			::= { sleMplsTpVplsMacLearningControl 3 }

		
		sleMplsTpVplsMacLearningControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpVplsMacLearningControl 4 }

		
		sleMplsTpVplsMacLearningControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpVplsMacLearningControl 5 }

		
		sleMplsTpVplsMacLearningControlVplsId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Name which this peer should configure."
			::= { sleMplsTpVplsMacLearningControl 6 }

		
		sleMplsTpVplsStatistics OBJECT IDENTIFIER ::= { sleMplsTpVpls 6 }

		
		sleMplsTpVplsAcStatistics OBJECT IDENTIFIER ::= { sleMplsTpVplsStatistics 1 }

		
		sleMplsTpVplsAcStatisticsInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpVplsAcStatisticsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for VPLS attachement circuit
				statistics."
			::= { sleMplsTpVplsAcStatistics 1 }

		
		sleMplsTpVplsAcStatisticsInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpVplsAcStatisticsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table contains stats info of a VPLS instance and 
				bound interface."
			INDEX { sleMplsTpVplsIfCfgInfoName, sleMplsTpVplsIfCfgInfoIfIndex }
			::= { sleMplsTpVplsAcStatisticsInfoTable 1 }

		
		SleMplsTpVplsAcStatisticsInfoEntry ::=
			SEQUENCE { 
				sleMplsTpVplsAcStatisticsInfoTxPackets
					Counter64,
				sleMplsTpVplsAcStatisticsInfoTxBytes
					Counter64,
				sleMplsTpVplsAcStatisticsInfoRxPackets
					Counter64,
				sleMplsTpVplsAcStatisticsInfoRxBytes
					Counter64
			 }

		sleMplsTpVplsAcStatisticsInfoTxPackets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Tx packet count."
			::= { sleMplsTpVplsAcStatisticsInfoEntry 1 }

		
		sleMplsTpVplsAcStatisticsInfoTxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Tx bytes count."
			::= { sleMplsTpVplsAcStatisticsInfoEntry 2 }

		
		sleMplsTpVplsAcStatisticsInfoRxPackets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Rx packet count."
			::= { sleMplsTpVplsAcStatisticsInfoEntry 3 }

		
		sleMplsTpVplsAcStatisticsInfoRxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Rx byte count."
			::= { sleMplsTpVplsAcStatisticsInfoEntry 4 }

		
		sleMplsTpVplsPeerStatistics OBJECT IDENTIFIER ::= { sleMplsTpVplsStatistics 2 }

		
		sleMplsTpVplsPeerStatisticsInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMplsTpVplsPeerStatisticsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table specifies information for VPLS Peer
				statistics."
			::= { sleMplsTpVplsPeerStatistics 1 }

		
		sleMplsTpVplsPeerStatisticsInfoEntry OBJECT-TYPE
			SYNTAX SleMplsTpVplsPeerStatisticsInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A row in this table is contains the managed object 
				for VPLS peer (Peer) stats."
			INDEX { sleMplsTpVplsCfgInfoId, sleMplsTpVplsMeshCfgInfoPeerNodeId }
			::= { sleMplsTpVplsPeerStatisticsInfoTable 1 }

		
		SleMplsTpVplsPeerStatisticsInfoEntry ::=
			SEQUENCE { 
				sleMplsTpVplsPeerStatisticsInfoTxPackets
					Counter64,
				sleMplsTpVplsPeerStatisticsInfoTxBytes
					Counter64,
				sleMplsTpVplsPeerStatisticsInfoRxPackets
					Counter64,
				sleMplsTpVplsPeerStatisticsInfoRxBytes
					Counter64
			 }

		sleMplsTpVplsPeerStatisticsInfoTxPackets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Tx packet count."
			::= { sleMplsTpVplsPeerStatisticsInfoEntry 1 }

		
		sleMplsTpVplsPeerStatisticsInfoTxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Tx bytes count."
			::= { sleMplsTpVplsPeerStatisticsInfoEntry 2 }

		
		sleMplsTpVplsPeerStatisticsInfoRxPackets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Rx packet count."
			::= { sleMplsTpVplsPeerStatisticsInfoEntry 3 }

		
		sleMplsTpVplsPeerStatisticsInfoRxBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Rx byte count."
			::= { sleMplsTpVplsPeerStatisticsInfoEntry 4 }

		
		sleMplsTpVplsStatisticsCfgControl OBJECT IDENTIFIER ::= { sleMplsTpVplsStatistics 3 }

		
		sleMplsTpVplsStatisticsCfgControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearVplsStatistics(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object holds the possible read-write columns that can be modified 
				in the sleMplsTpVplsStatisticsCfg table. For each read-write column of 
				sleMplsTpVplsStatisticsCfg table, a Set Operation control value is 
				added in this object."
			::= { sleMplsTpVplsStatisticsCfgControl 1 }

		
		sleMplsTpVplsStatisticsCfgControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object displays the status of the configuration done."
			::= { sleMplsTpVplsStatisticsCfgControl 2 }

		
		sleMplsTpVplsStatisticsCfgControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is based on the SLE style where a timer is 
				configured for every control table."
			::= { sleMplsTpVplsStatisticsCfgControl 3 }

		
		sleMplsTpVplsStatisticsCfgControlTimestamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object specifies the time at which the configuration is done."
			::= { sleMplsTpVplsStatisticsCfgControl 4 }

		
		sleMplsTpVplsStatisticsCfgControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The standard result of the SET operation is stored here."
			::= { sleMplsTpVplsStatisticsCfgControl 5 }

		
		sleMplsTpVplsStatisticsCfgControlVplsId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VPLS Name which this peer should configure."
			::= { sleMplsTpVplsStatisticsCfgControl 6 }

		
	
	END

--
-- sle-mpls-tp-vpls-mib.mib
--
