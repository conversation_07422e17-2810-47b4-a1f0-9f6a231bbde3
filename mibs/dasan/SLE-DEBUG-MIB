--
-- SLE-DEBUG-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 3.0 Build 285
-- Monday, January 22, 2007 at 17:04:05
--

	SLE-DEBUG-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Gauge32, BITS, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
-- 			sle			
-- FROM AN-MIB			
-- 
		-- *******.4.1.6296.101.99
		sleDebug MODULE-IDENTITY 
			LAST-UPDATED "200412080903Z"		-- December 08, 2004 at 09:03 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"This MIB contains all needed informations about rmon and
				all supported sle rmon features."
			::= { sleMgmt 99 }

		
	
--
-- Type definitions
--
	
-- 			::= { sle 14 }
-- textual conventions
		OwnerString ::= OCTET STRING (SIZE (0..255))

	
	
--
-- Node definitions
--
	
--  Node definitions
-- 
		-- *******.4.1.6296.101.99.1
		sleDebugBase OBJECT IDENTIFIER::= { sleDebug 1 }

		
		-- *******.4.1.6296.**********
		sleDebugStatus OBJECT IDENTIFIER::= { sleDebugBase 1 }

		
		-- *******.4.1.6296.**********.1
		sleDebugDhcpStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugFilter(0),
				debugLease(1),
				debugPacket(2),
				debugServices(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of dhcp daemon.
				debugFilter(0),
				debugLease(1),
				debugPacket(2),
				debugServices(3),
				
				The above value is denotes by 'or'.
				"
			::= { sleDebugStatus 1 }

		
		-- *******.4.1.6296.**********.2
		sleDebugIgmpStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugDecode(0),
				debugEncode(1),
				debugEvents(2),
				debugFsm(3),
				debugTib(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Igmp daemon.
				debugDecode(0),
				debugEncode(1),
				debugEvents(2),
				debugFsm(3),
				debugTib(4)
				
				The above value is denotes by 'or'.
				"
			::= { sleDebugStatus 2 }

		
		-- *******.4.1.6296.**********.3
		sleDebugIgmpSnoopStatus OBJECT-TYPE
			SYNTAX BITS { debugTcn(0) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Igmp Snoop function.
				debugTcn(0)
				
				The above value is denotes by 'or'."
			::= { sleDebugStatus 3 }

		
		-- *******.4.1.6296.**********.4
		sleDebugNsmStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEvents(0),
				debugKernel(1),
				debugPacketDetail(2),
				debugPacketRecv(3),
				debugPacketSend(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Nsm function.
				debugEvents(0),
				debugKernel(1),
				debugPacketDetail(2),
				debugPacketRecv(3),
				debugPacketSend(4)
				
				The above value is denotes by 'or'."
			::= { sleDebugStatus 4 }

		
		-- *******.4.1.6296.**********.5
		sleDebugNsmMcastStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugMcastFibMsg(0),
				debugMcastMrt(1),
				debugMcastRegister(2),
				debugMcastStats(3),
				debugMcastVif(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Nsm Mcast function.
				debugMcastFibMsg(0),
				debugMcastMrt(1),
				debugMcastRegister(2),
				debugMcastStats(3),
				debugMcastVif(4)
				
				The above value is denotes by 'or'."
			::= { sleDebugStatus 5 }

		
		-- *******.4.1.6296.**********.6
		sleDebugBgpStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugNormal(0),
				debugDampening(1),
				debugEvents(2),
				debugFilters(3),
				debugFsm(4),
				debugKeepalives(5),
				debugNsm(6),
				debugUpdatesIn(7),
				debugUpdatesOut(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Bgp deamon.
				debugNormal(0),
				debugDampening(1),
				debugEvents(2),
				debugFilters(3),
				debugFsm(4),
				debugKeepalives(5),
				debugNsm(6),
				debugUpdatesIn(7),
				debugUpdatesOut(8)
				
				The above value is denotes by 'or'."
			::= { sleDebugStatus 6 }

		
		-- *******.4.1.6296.**********.7
		sleDebugOspfStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEventAbr(0),
				debugEventAsbr(1),
				debugEventLsa(2),
				debugEventNssa(3),
				debugEventOs(4),
				debugEventRouter(5),
				debugEventVlink(6),
				debugIfsmEvent(7),
				debugIfsmStatus(8),
				debugIfsmTimers(9),
				debugLsaFlooding(10),
				debugLsaGenerate(11),
				debugLsaInstall(12),
				debugLsaMaxage(13),
				debugLsaRefrash(14),
				debugNfsmEvents(15),
				debugNfsmStatus(16),
				debugNfsmTimers(17),
				debugNsmInterface(18),
				debugNsmRedistribute(19),
				debugRouteAse(20),
				debugRouteIa(21),
				debugRouteInstall(22),
				debugRouteSpf(23),
				debugPacketDDDetail(24),
				debugPacketDDRecv(25),
				debugPacketDDSend(26),
				debugPacketHelloDetail(27),
				debugPacketHelloRecv(28),
				debugPacketHelloSend(29),
				debugPacketLsAckDetail(30),
				debugPacketLsAckRecv(31),
				debugPacketLsAckSend(32),
				debugPacketLsRequestDetail(33),
				debugPacketLsRequestRecv(34),
				debugPacketLsRequestSend(35),
				debugPacketLsUpdateDetail(36),
				debugPacketLsUpdateRecv(37),
				debugPacketLsUpdateSend(38)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Ospf deamon.
				debugEventAbr(0),
				debugEventAsbr(1),
				debugEventLsa(2),
				debugEventNssa(3),
				debugEventOs(4),
				debugEventRouter(5),
				debugEventVlink(6),
				debugIfsmEvent(7),
				debugIfsmStatus(8),
				debugIfsmTimers(9),
				debugLsaFlooding(10),
				debugLsaGenerate(11),
				debugLsaInstall(12),
				debugLsaMaxage(13),
				debugLsaRefrash(14),
				debugNfsmEvents(15),
				debugNfsmStatus(16),
				debugNfsmTimers(17),
				debugNsmInterface(18),
				debugNsmRedistribute(19),
				debugRouteAse(20),
				debugRouteIa(21),
				debugRouteInstall(22),
				debugRouteSpf(23),
				debugPacketDDDetail(24),
				debugPacketDDRecv(25),
				debugPacketDDSend(26),
				debugPacketHelloDetail(27),
				debugPacketHelloRecv(28),
				debugPacketHelloSend(29),
				debugPacketLsAckDetail(30),
				debugPacketLsAckRecv(31),
				debugPacketLsAckSend(32),
				debugPacketLsRequestDetail(33),
				debugPacketLsRequestRecv(34),
				debugPacketLsRequestSend(35),
				debugPacketLsUpdateDetail(36),
				debugPacketLsUpdateRecv(37),
				debugPacketLsUpdateSend(38)
				
				Thee above value is denotes by 'or'."
			::= { sleDebugStatus 7 }

		
		-- *******.4.1.6296.**********.8
		sleDebugPimStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEvent(0),
				debugMfc(1),
				debugMib(2),
				debugNexthop(3),
				debugNsm(4),
				debugPacketIn(5),
				debugPacketOut(6),
				debugState(7),
				debugTimerAssertAt(8),
				debugTimerBsr(9),
				debugTimerBsrBst(10),
				debugTimerBsrCrp(11),
				debugTimerHello(12),
				debugTimerHelloHt(13),
				debugTimerHelloNlt(14),
				debugTimerHelloTht(15),
				debugTimerJoinprune(16),
				debugTimerJoinpruneEt(17),
				debugTimerJoinpruneJt(18),
				debugTimerJoinpruneKat(19),
				debugTimerJoinpruneOt(20),
				debugTimerJoinprunePpt(21),
				debugTimerRegister(22)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Pim deamon.
				debugEvent(0),
				debugMfc(1),
				debugMib(2),
				debugNexthop(3),
				debugNsm(4),
				debugPacketIn(5),
				debugPacketOut(6),
				debugState(7),
				debugTimerAssertAt(8),
				debugTimerBsr(9),
				debugTimerBsrBst(10),
				debugTimerBsrCrp(11),
				debugTimerHello(12),
				debugTimerHelloHt(13),
				debugTimerHelloNlt(14),
				debugTimerHelloTht(15),
				debugTimerJoinprune(16),
				debugTimerJoinpruneEt(17),
				debugTimerJoinpruneJt(18),
				debugTimerJoinpruneKat(19),
				debugTimerJoinpruneOt(20),
				debugTimerJoinprunePpt(21),
				debugTimerRegister(22)
				
				The above value is denotes by 'or'."
			::= { sleDebugStatus 8 }

		
		-- *******.4.1.6296.**********.9
		sleDebugRipStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEvents(0),
				debugNsm(1),
				debugPacketDetail(2),
				debugPacketRecv(3),
				debugPacketSend(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The  debug option setting of Rip deamon.
				debugEvents(0),
				debugNsm(1),
				debugPacketDetail(2),
				debugPacketRecv(3),
				debugPacketSend(4)
				
				The above value is denotes by 'or'."
			::= { sleDebugStatus 9 }

		
		-- *******.4.1.6296.**********
		sleDebugStatusControl OBJECT IDENTIFIER::= { sleDebugBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleDebugControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setDhcpDebugStatus(1),
				setIgmpDebugStatus(2),
				setIgmpSnoopDebugStats(3),
				setNsmDebugStatus(4),
				setNsmMcastDebugStatus(5),
				setBgpDebugStatus(6),
				setOspfDebugStatus(7),
				setPimDebugStatus(8),
				setRipDebugStatus(9)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				.
				"
			::= { sleDebugStatusControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleDebugControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleDebugStatusControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleDebugControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleDebugStatusControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleDebugControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleDebugStatusControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleDebugControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleDebugStatusControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleDebugControlDhcpStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugFilter(0),
				debugLease(1),
				debugPacket(2),
				debugServices(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleDebugControlIgmpStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugDecode(0),
				debugEncode(1),
				debugEvents(2),
				debugFsm(3),
				debugTib(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleDebugControlIgmpSnoopStatus OBJECT-TYPE
			SYNTAX BITS { debugTcn(0) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleDebugControlNsmStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEvents(0),
				debugKernel(1),
				debugPacketDetail(2),
				debugPacketRecv(3),
				debugPacketSend(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleDebugControlNsmMcastStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugMcastFibMsg(0),
				debugMcastMrt(1),
				debugMcastRegister(2),
				debugMcastStats(3),
				debugMcastVif(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleDebugControlBgpStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugNormal(0),
				debugDampening(1),
				debugEvents(2),
				debugFilters(3),
				debugFsm(4),
				debugKeepalives(5),
				debugNsm(6),
				debugUpdatesIn(7),
				debugUpdatesOut(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 11 }

		
		-- *******.4.1.6296.**********.12
		sleDebugControlOspfStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEventAbr(0),
				debugEventAsbr(1),
				debugEventLsa(2),
				debugEventNssa(3),
				debugEventOs(4),
				debugEventRouter(5),
				debugEventVlink(6),
				debugIfsmEvent(7),
				debugIfsmStatus(8),
				debugIfsmTimers(9),
				debugLsaFlooding(10),
				debugLsaGenerate(11),
				debugLsaInstall(12),
				debugLsaMaxage(13),
				debugLsaRefrash(14),
				debugNfsmEvents(15),
				debugNfsmStatus(16),
				debugNfsmTimers(17),
				debugNsmInterface(18),
				debugNsmRedistribute(19),
				debugRouteAse(20),
				debugRouteIa(21),
				debugRouteInstall(22),
				debugRouteSpf(23),
				debugPacketDDDetail(24),
				debugPacketDDRecv(25),
				debugPacketDDSend(26),
				debugPacketHelloDetail(27),
				debugPacketHelloRecv(28),
				debugPacketHelloSend(29),
				debugPacketLsAckDetail(30),
				debugPacketLsAckRecv(31),
				debugPacketLsAckSend(32),
				debugPacketLsRequestDetail(33),
				debugPacketLsRequestRecv(34),
				debugPacketLsRequestSend(35),
				debugPacketLsUpdateDetail(36),
				debugPacketLsUpdateRecv(37),
				debugPacketLsUpdateSend(38)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 12 }

		
		-- *******.4.1.6296.**********.13
		sleDebugControlPimStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEvent(0),
				debugMfc(1),
				debugMib(2),
				debugNexthop(3),
				debugNsm(4),
				debugPacketIn(5),
				debugPacketOut(6),
				debugState(7),
				debugTimerAssertAt(8),
				debugTimerBsr(9),
				debugTimerBsrBst(10),
				debugTimerBsrCrp(11),
				debugTimerHello(12),
				debugTimerHelloHt(13),
				debugTimerHelloNlt(14),
				debugTimerHelloTht(15),
				debugTimerJoinprune(16),
				debugTimerJoinpruneEt(17),
				debugTimerJoinpruneJt(18),
				debugTimerJoinpruneKat(19),
				debugTimerJoinpruneOt(20),
				debugTimerJoinprunePpt(21),
				debugTimerRegister(22)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 13 }

		
		-- *******.4.1.6296.**********.14
		sleDebugControlRipStatus OBJECT-TYPE
			SYNTAX BITS
				{
				debugEvents(0),
				debugNsm(1),
				debugPacketDetail(2),
				debugPacketRecv(3),
				debugPacketSend(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleDebugStatusControl 14 }

		
		-- *******.4.1.6296.**********
		sleDebugStatusNotification OBJECT IDENTIFIER::= { sleDebugBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleDebugDhcpStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugDhcpStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleDebugIgmpStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugIgmpStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleDebugIgmpSnoopStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugIgmpSnoopStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleDebugNsmStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugNsmStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 4 }

		
		-- *******.4.1.6296.**********.5
		sleDebugNsmMcastStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugNsmMcastStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 5 }

		
		-- *******.4.1.6296.**********.6
		sleDebugBgpStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugBgpStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 6 }

		
		-- *******.4.1.6296.**********.7
		sleDebugOspfStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugOspfStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 7 }

		
		-- *******.4.1.6296.**********.8
		sleDebugPimStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugPimStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 8 }

		
		-- *******.4.1.6296.**********.9
		sleDebugRipStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleDebugControlRequest, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugRipStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebugStatusNotification 9 }

		
		-- *******.4.1.6296.101.99.2
		sleDebugGroup OBJECT-GROUP
			OBJECTS { sleDebugDhcpStatus, sleDebugIgmpStatus, sleDebugIgmpSnoopStatus, sleDebugNsmStatus, sleDebugNsmMcastStatus, 
				sleDebugBgpStatus, sleDebugOspfStatus, sleDebugPimStatus, sleDebugRipStatus, sleDebugControlRequest, 
				sleDebugControlStatus, sleDebugControlTimer, sleDebugControlTimeStamp, sleDebugControlReqResult, sleDebugControlDhcpStatus, 
				sleDebugControlIgmpStatus, sleDebugControlIgmpSnoopStatus, sleDebugControlNsmStatus, sleDebugControlNsmMcastStatus, sleDebugControlBgpStatus, 
				sleDebugControlOspfStatus, sleDebugControlPimStatus, sleDebugControlRipStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebug 2 }

		
		-- *******.4.1.6296.101.99.3
		sleDebugNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleDebugDhcpStatusChanged, sleDebugIgmpStatusChanged, sleDebugIgmpSnoopStatusChanged, sleDebugNsmStatusChanged, sleDebugNsmMcastStatusChanged, 
				sleDebugBgpStatusChanged, sleDebugOspfStatusChanged, sleDebugPimStatusChanged, sleDebugRipStatusChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleDebug 3 }

		
	
	END

--
-- SLE-DEBUG-MIB.my
--
