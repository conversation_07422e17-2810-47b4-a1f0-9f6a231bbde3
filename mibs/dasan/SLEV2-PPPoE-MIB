--
-- slev2-pppoe-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, July 04, 2014 at 16:47:16
--

	SLEV2-PPPoE-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleV2Mgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Integer32, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.102.16
		sleV2PPPoE MODULE-IDENTITY 
			LAST-UPDATED "201302061009Z"		-- February 06, 2013 at 10:09 GMT
			ORGANIZATION 
				"Dasan networks"
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleV2Mgmt 16 }

		
	
--
-- Type definitions
--
	
		Boolean ::= Integer32

	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.102.16.1
		sleV2PPPoEBase OBJECT IDENTIFIER ::= { sleV2PPPoE 1 }

		
		-- *******.4.1.6296.**********
		sleV2PPPoEInfo OBJECT IDENTIFIER ::= { sleV2PPPoEBase 1 }

		
		-- *******.4.1.6296.**********.1
		sleV2PPPoESnoopingActivity OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates whether PPPoE Snooping service is disable or enable"
			::= { sleV2PPPoEInfo 1 }

		
		-- *******.4.1.6296.**********
		sleV2PPPoEControl OBJECT IDENTIFIER ::= { sleV2PPPoEBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleV2PPPoEControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setPPPoESnoopingActivity(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" The request of a user command"
			::= { sleV2PPPoEControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2PPPoEControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" The status of user command"
			::= { sleV2PPPoEControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleV2PPPoEControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleV2PPPoEControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleV2PPPoEControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)"
			::= { sleV2PPPoEControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleV2PPPoEControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleV2PPPoEControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleV2PPPoEControlSnoopingActivity OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object indicates whether PPPoE Snooping service is disable or enable"
			::= { sleV2PPPoEControl 6 }

		
		-- *******.4.1.6296.**********
		sleV2PPPoENotification OBJECT IDENTIFIER ::= { sleV2PPPoEBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleV2PPPoESnoopingActivityChanged NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoEControlRequest, sleV2PPPoEControlTimeStamp, sleV2PPPoEControlReqResult, sleV2PPPoEControlSnoopingActivity }
			STATUS current
			DESCRIPTION 
				"setV2PPPoESnoopingActivity"
			::= { sleV2PPPoENotification 1 }

		
		-- *******.4.1.6296.102.16.2
		sleV2PPPoEFilter OBJECT IDENTIFIER ::= { sleV2PPPoE 2 }

		
		-- *******.4.1.6296.**********
		sleV2PPPoEFilterTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2PPPoEFilterEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2PPPoEFilter 1 }

		
		-- *******.4.1.6296.**********.1
		sleV2PPPoEFilterEntry OBJECT-TYPE
			SYNTAX SleV2PPPoEFilterEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2PPPoEFilterId, sleV2PPPoEFilterPortIndex, sleV2PPPoEFilterVlanId }
			::= { sleV2PPPoEFilterTable 1 }

		
		SleV2PPPoEFilterEntry ::=
			SEQUENCE { 
				sleV2PPPoEFilterId
					INTEGER,
				sleV2PPPoEFilterPortIndex
					INTEGER,
				sleV2PPPoEFilterVlanId
					INTEGER,
				sleV2PPPoEFilterAction
					INTEGER
			 }

		-- *******.4.1.6296.102.16.*******
		sleV2PPPoEFilterId OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Index of Filter"
			::= { sleV2PPPoEFilterEntry 1 }

		
		-- *******.4.1.6296.102.16.*******
		sleV2PPPoEFilterPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of PORT"
			::= { sleV2PPPoEFilterEntry 2 }

		
		-- *******.4.1.6296.102.16.*******
		sleV2PPPoEFilterVlanId OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The specific VLAN index"
			::= { sleV2PPPoEFilterEntry 3 }

		
		-- *******.4.1.6296.102.16.*******
		sleV2PPPoEFilterAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				drop(1),
				permit(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Specify the kind of PPPoE packet"
			::= { sleV2PPPoEFilterEntry 4 }

		
		-- *******.4.1.6296.**********
		sleV2PPPoEFilterControl OBJECT IDENTIFIER ::= { sleV2PPPoEFilter 2 }

		
		-- *******.4.1.6296.**********.1
		sleV2PPPoEFilterControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createPPPoEFilter(1),
				destroyPPPoEFilter(2),
				modifyPPPoEFilter(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleV2PPPoEFilterControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2PPPoEFilterControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleV2PPPoEFilterControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleV2PPPoEFilterControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleV2PPPoEFilterControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleV2PPPoEFilterControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)"
			::= { sleV2PPPoEFilterControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleV2PPPoEFilterControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleV2PPPoEFilterControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleV2PPPoEFilterControlId OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Index of Filter"
			::= { sleV2PPPoEFilterControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleV2PPPoEFilterControlPortIndex OBJECT-TYPE
			SYNTAX INTEGER (0 | 1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of PORT
				0: for all port"
			::= { sleV2PPPoEFilterControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleV2PPPoEFilterControlVlanId OBJECT-TYPE
			SYNTAX INTEGER (0 | 1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The VLAN identify
				0: for all VLAN ID"
			::= { sleV2PPPoEFilterControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleV2PPPoEFilterControlAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				drop(1),
				permit(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Specify the kind of action with PPPoE packet"
			::= { sleV2PPPoEFilterControl 9 }

		
		-- *******.4.1.6296.**********
		sleV2PPPoEFilterNotification OBJECT IDENTIFIER ::= { sleV2PPPoEFilter 4 }

		
		-- *******.4.1.6296.**********.1
		sleV2PPPoEFilterCreatedChanged NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoEFilterControlRequest, sleV2PPPoEFilterControlTimeStamp, sleV2PPPoEFilterControlReqResult, sleV2PPPoEFilterControlId, sleV2PPPoEFilterControlPortIndex, 
				sleV2PPPoEFilterControlVlanId, sleV2PPPoEFilterControlAction }
			STATUS current
			DESCRIPTION 
				"createPPPoEFilter"
			::= { sleV2PPPoEFilterNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2PPPoEFilterModifyChanged NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoEFilterControlRequest, sleV2PPPoEFilterControlTimeStamp, sleV2PPPoEFilterControlReqResult, sleV2PPPoEFilterControlId, sleV2PPPoEFilterControlPortIndex, 
				sleV2PPPoEFilterControlVlanId, sleV2PPPoEFilterControlAction }
			STATUS current
			DESCRIPTION 
				"modifyPPPoEFilter"
			::= { sleV2PPPoEFilterNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleV2PPPoEFilterDestroyChanged NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoEFilterControlRequest, sleV2PPPoEFilterControlTimeStamp, sleV2PPPoEFilterControlReqResult, sleV2PPPoEFilterControlId }
			STATUS current
			DESCRIPTION 
				"destroyPPPoEFilter"
			::= { sleV2PPPoEFilterNotification 3 }

		
		-- *******.4.1.62***********
		sleV2PPPoETag OBJECT IDENTIFIER ::= { sleV2PPPoE 3 }

		
		-- *******.4.1.62***********.1
		sleV2PPPoETagOper OBJECT IDENTIFIER ::= { sleV2PPPoETag 1 }

		
		-- *******.4.1.62***********.1.1
		sleV2PPPoETagOperTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2PPPoETagOperEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2PPPoETagOper 1 }

		
		-- *******.4.1.62***********.1.1.1
		sleV2PPPoETagOperEntry OBJECT-TYPE
			SYNTAX SleV2PPPoETagOperEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2PPPoETagOperId, sleV2PPPoETagOperVlanId, sleV2PPPoETagOperPortIndex }
			::= { sleV2PPPoETagOperTable 1 }

		
		SleV2PPPoETagOperEntry ::=
			SEQUENCE { 
				sleV2PPPoETagOperId
					INTEGER,
				sleV2PPPoETagOperType
					OCTET STRING,
				sleV2PPPoETagOperPortIndex
					INTEGER,
				sleV2PPPoETagOperVlanId
					INTEGER,
				sleV2PPPoETagOperAction
					INTEGER,
				sleV2PPPoETagOperTagFmt
					OCTET STRING
			 }

		-- *******.4.1.62***********.*******
		sleV2PPPoETagOperId OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of Tag operation"
			::= { sleV2PPPoETagOperEntry 1 }

		
		-- *******.4.1.62***********.1.1.1.2
		sleV2PPPoETagOperType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PPPoE tag type (e.g: 0x0105 for Vendor-Specific)"
			::= { sleV2PPPoETagOperEntry 2 }

		
		-- *******.4.1.62***********.1.1.1.3
		sleV2PPPoETagOperPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of PORT"
			::= { sleV2PPPoETagOperEntry 3 }

		
		-- *******.4.1.62***********.1.1.1.4
		sleV2PPPoETagOperVlanId OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The VLAN identify"
			::= { sleV2PPPoETagOperEntry 4 }

		
		-- *******.4.1.62***********.1.1.1.5
		sleV2PPPoETagOperAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				add(1),
				keep(2),
				remove(3),
				replace(4),
				update(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Action with PPPoE packet
				add: Add the Tag if is not exist
				keep: Keep the Tag
				remove: Remove the Tag
				replace: Replace the Tag if is existed
				update: Update or Add the Tag 
				regardless of existence of the Tag"
			::= { sleV2PPPoETagOperEntry 5 }

		
		-- *******.4.1.62***********.1.1.1.6
		sleV2PPPoETagOperTagFmt OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The name of tag format which is added/replaced/updated"
			::= { sleV2PPPoETagOperEntry 6 }

		
		-- *******.4.1.62***********.1.2
		sleV2PPPoETagOperControl OBJECT IDENTIFIER ::= { sleV2PPPoETagOper 2 }

		
		-- *******.4.1.62***********.1.2.1
		sleV2PPPoETagOperControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createPPPoETagOper(1),
				modifyPPPoETagOper(2),
				destroyPPPoETagOper(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleV2PPPoETagOperControl 1 }

		
		-- *******.4.1.62***********.1.2.2
		sleV2PPPoETagOperControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleV2PPPoETagOperControl 2 }

		
		-- *******.4.1.62***********.1.2.3
		sleV2PPPoETagOperControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleV2PPPoETagOperControl 3 }

		
		-- *******.4.1.62***********.1.2.4
		sleV2PPPoETagOperControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)"
			::= { sleV2PPPoETagOperControl 4 }

		
		-- *******.4.1.62***********.1.2.5
		sleV2PPPoETagOperControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleV2PPPoETagOperControl 5 }

		
		-- *******.4.1.62***********.1.2.6
		sleV2PPPoETagOperControlId OBJECT-TYPE
			SYNTAX INTEGER (1..16)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Index of Tag operation"
			::= { sleV2PPPoETagOperControl 6 }

		
		-- *******.4.1.62***********.1.2.7
		sleV2PPPoETagOperControlType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Type Field ofTagOper Control."
			::= { sleV2PPPoETagOperControl 7 }

		
		-- *******.4.1.62***********.1.2.8
		sleV2PPPoETagOperControlPortIndex OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of PORT
				0: all PORT"
			::= { sleV2PPPoETagOperControl 8 }

		
		-- *******.4.1.62***********.1.2.9
		sleV2PPPoETagOperControlVlanId OBJECT-TYPE
			SYNTAX INTEGER (0..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Vlan index
				0: for all Vlan index"
			::= { sleV2PPPoETagOperControl 9 }

		
		-- *******.4.1.62***********.1.2.10
		sleV2PPPoETagOperControlAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				add(1),
				keep(2),
				remove(3),
				replace(4),
				update(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Action with PPPoE packet
				add: Add the Tag if is not exist
				keep: Keep the Tag
				remove: Remove the Tag
				replace: Replace the Tag if is existed
				update: Update or Add the Tag 
				regardless of existence of the Tag"
			::= { sleV2PPPoETagOperControl 10 }

		
		-- *******.4.1.62***********.1.2.11
		sleV2PPPoETagOperControlTagFmt OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set the tag format name which is added/replaced/updated"
			::= { sleV2PPPoETagOperControl 11 }

		
		-- *******.4.1.62***********.1.3
		sleV2PPPoETagOperNotification OBJECT IDENTIFIER ::= { sleV2PPPoETagOper 3 }

		
		-- *******.4.1.62***********.1.3.1
		sleV2PPPoETagOperCreateChanged NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagOperControlRequest, sleV2PPPoETagOperControlTimeStamp, sleV2PPPoETagOperControlReqResult, sleV2PPPoETagOperControlId, sleV2PPPoETagOperControlType, 
				sleV2PPPoETagOperControlPortIndex, sleV2PPPoETagOperControlVlanId, sleV2PPPoETagOperControlAction, sleV2PPPoETagOperControlTagFmt }
			STATUS current
			DESCRIPTION 
				"createPPPoETagOper"
			::= { sleV2PPPoETagOperNotification 1 }

		
		-- *******.4.1.62***********.1.3.2
		sleV2PPPoETagOperDestroyChanged NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagOperControlRequest, sleV2PPPoETagOperControlTimeStamp, sleV2PPPoETagOperControlReqResult, sleV2PPPoETagOperControlId }
			STATUS current
			DESCRIPTION 
				"destroyPPPoETagOper"
			::= { sleV2PPPoETagOperNotification 2 }

		
		-- *******.4.1.62***********.1.3.3
		sleV2PPPoETagOperModifyChanged NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagOperControlRequest, sleV2PPPoETagOperControlTimeStamp, sleV2PPPoETagOperControlReqResult, sleV2PPPoETagOperControlId, sleV2PPPoETagOperControlType, 
				sleV2PPPoETagOperControlPortIndex, sleV2PPPoETagOperControlVlanId, sleV2PPPoETagOperControlAction, sleV2PPPoETagOperControlTagFmt }
			STATUS current
			DESCRIPTION 
				"modifyPPPoETagOper"
			::= { sleV2PPPoETagOperNotification 3 }

		
		-- *******.4.1.62***********.2
		sleV2PPPoETagFormat OBJECT IDENTIFIER ::= { sleV2PPPoETag 2 }

		
		-- *******.4.1.62***********.2.1
		sleV2PPPoETagFormatBase OBJECT IDENTIFIER ::= { sleV2PPPoETagFormat 1 }

		
		-- *******.4.1.62***********.2.1.1
		sleV2PPPoETagFormatTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2PPPoETagFormatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2PPPoETagFormatBase 1 }

		
		-- *******.4.1.62***********.*******
		sleV2PPPoETagFormatEntry OBJECT-TYPE
			SYNTAX SleV2PPPoETagFormatEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2PPPoETagFormatName }
			::= { sleV2PPPoETagFormatTable 1 }

		
		SleV2PPPoETagFormatEntry ::=
			SEQUENCE { 
				sleV2PPPoETagFormatName
					OCTET STRING
			 }

		-- *******.4.1.62***********.*******.1
		sleV2PPPoETagFormatName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The name of Tag Format"
			::= { sleV2PPPoETagFormatEntry 1 }

		
		-- *******.4.1.62***********.2.1.2
		sleV2PPPoETagFormatControl OBJECT IDENTIFIER ::= { sleV2PPPoETagFormatBase 2 }

		
		-- *******.4.1.62***********.2.1.2.1
		sleV2PPPoETagFormatControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createTagFormat(1),
				destroyTagFormat(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleV2PPPoETagFormatControl 1 }

		
		-- *******.4.1.62***********.2.1.2.2
		sleV2PPPoETagFormatControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleV2PPPoETagFormatControl 2 }

		
		-- *******.4.1.62***********.2.1.2.3
		sleV2PPPoETagFormatControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleV2PPPoETagFormatControl 3 }

		
		-- *******.4.1.62***********.2.1.2.4
		sleV2PPPoETagFormatControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)"
			::= { sleV2PPPoETagFormatControl 4 }

		
		-- *******.4.1.62***********.2.1.2.5
		sleV2PPPoETagFormatControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleV2PPPoETagFormatControl 5 }

		
		-- *******.4.1.62***********.2.1.2.6
		sleV2PPPoETagFormatControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The name of Tag Format"
			::= { sleV2PPPoETagFormatControl 6 }

		
		-- *******.4.1.62***********.2.1.3
		sleV2PPPoETagFormatNotification OBJECT IDENTIFIER ::= { sleV2PPPoETagFormatBase 3 }

		
		-- *******.4.1.62***********.2.1.3.1
		sleV2PPPoETagFormatCreated NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagFormatControlRequest, sleV2PPPoETagFormatControlTimeStamp, sleV2PPPoETagFormatControlReqResult, sleV2PPPoETagFormatControlName }
			STATUS current
			DESCRIPTION 
				"createTagFormat"
			::= { sleV2PPPoETagFormatNotification 1 }

		
		-- *******.4.1.62***********.2.1.3.2
		sleV2PPPoETagFormatDestroyed NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagFormatControlRequest, sleV2PPPoETagFormatControlTimeStamp, sleV2PPPoETagFormatControlReqResult, sleV2PPPoETagFormatControlName }
			STATUS current
			DESCRIPTION 
				"destroyTagFormat"
			::= { sleV2PPPoETagFormatNotification 2 }

		
		-- *******.4.1.62***********.2.2
		sleV2PPPoETagFormatAttribute OBJECT IDENTIFIER ::= { sleV2PPPoETagFormat 2 }

		
		-- *******.4.1.62***********.2.2.1
		sleV2PPPoETagFormatAttrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2PPPoETagFormatAttrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2PPPoETagFormatAttribute 1 }

		
		-- *******.4.1.62***********.*******
		sleV2PPPoETagFormatAttrEntry OBJECT-TYPE
			SYNTAX SleV2PPPoETagFormatAttrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2PPPoETagFormatName, sleV2PPPoETagFormattAttrID }
			::= { sleV2PPPoETagFormatAttrTable 1 }

		
		SleV2PPPoETagFormatAttrEntry ::=
			SEQUENCE { 
				sleV2PPPoETagFormattAttrID
					INTEGER,
				sleV2PPPoETagFormatAttrLength
					INTEGER,
				sleV2PPPoETagFormatAttrHiddenLength
					INTEGER,
				sleV2PPPoETagFormatAttrType
					INTEGER,
				sleV2PPPoETagFormatAttrVarType
					INTEGER,
				sleV2PPPoETagFormatAttrVarValue
					OCTET STRING
			 }

		-- *******.4.1.62***********.*******.1
		sleV2PPPoETagFormattAttrID OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Index of Tag Format Attr"
			::= { sleV2PPPoETagFormatAttrEntry 1 }

		
		-- *******.4.1.62***********.*******.2
		sleV2PPPoETagFormatAttrLength OBJECT-TYPE
			SYNTAX INTEGER (-1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Length Field of Tag Format Attr.
				The -1 of value means this field is not defined.
				The Zero of value means this field is variable."
			::= { sleV2PPPoETagFormatAttrEntry 2 }

		
		-- *******.4.1.62***********.*******.3
		sleV2PPPoETagFormatAttrHiddenLength OBJECT-TYPE
			SYNTAX INTEGER (-1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Hidden Length Field of Tag Format Attr.
				The -1 of value means this field is not defined.
				The Zero of value means this field is variable."
			::= { sleV2PPPoETagFormatAttrEntry 3 }

		
		-- *******.4.1.62***********.*******.4
		sleV2PPPoETagFormatAttrType OBJECT-TYPE
			SYNTAX INTEGER (-1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Type Field ofTag Format Attr.
				The -1 of value means this field is not defined"
			::= { sleV2PPPoETagFormatAttrEntry 4 }

		
		-- *******.4.1.62***********.*******.5
		sleV2PPPoETagFormatAttrVarType OBJECT-TYPE
			SYNTAX INTEGER
				{
				hex(1),
				index(2),
				ip(3),
				string(4),
				tagFmt(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Variable Type Field of Tag Format Attr."
			::= { sleV2PPPoETagFormatAttrEntry 5 }

		
		-- *******.4.1.62***********.*******.6
		sleV2PPPoETagFormatAttrVarValue OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Variable Value Field of Tag Format Attr."
			::= { sleV2PPPoETagFormatAttrEntry 6 }

		
		-- *******.4.1.62***********.2.2.2
		sleV2PPPoETagFormatAttrControl OBJECT IDENTIFIER ::= { sleV2PPPoETagFormatAttribute 2 }

		
		-- *******.4.1.62***********.2.2.2.1
		sleV2PPPoETagFormatAttrControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				addTagFormatAttr(1),
				deleteTagFormatAttr(2),
				modifyTagFormatAttr(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleV2PPPoETagFormatAttrControl 1 }

		
		-- *******.4.1.62***********.2.2.2.2
		sleV2PPPoETagFormatAttrControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of user command"
			::= { sleV2PPPoETagFormatAttrControl 2 }

		
		-- *******.4.1.62***********.2.2.2.3
		sleV2PPPoETagFormatAttrControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleV2PPPoETagFormatAttrControl 3 }

		
		-- *******.4.1.62***********.2.2.2.4
		sleV2PPPoETagFormatAttrControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)"
			::= { sleV2PPPoETagFormatAttrControl 4 }

		
		-- *******.4.1.62***********.2.2.2.5
		sleV2PPPoETagFormatAttrControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleV2PPPoETagFormatAttrControl 5 }

		
		-- *******.4.1.62***********.2.2.2.6
		sleV2PPPoETagFormatAttrControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The name of Tag Format."
			::= { sleV2PPPoETagFormatAttrControl 6 }

		
		-- *******.4.1.62***********.2.2.2.7
		sleV2PPPoETagFormatAttrControlID OBJECT-TYPE
			SYNTAX INTEGER (1..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Index of Tag Format Attr"
			::= { sleV2PPPoETagFormatAttrControl 7 }

		
		-- *******.4.1.62***********.2.2.2.8
		sleV2PPPoETagFormatAttrControlLength OBJECT-TYPE
			SYNTAX INTEGER (-1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Length Field of Tag Format Attr.
				The -1 of value means this field is not defined.
				The Zero of value means this field is variable."
			::= { sleV2PPPoETagFormatAttrControl 8 }

		
		-- *******.4.1.62***********.2.2.2.9
		sleV2PPPoETagFormatAttrControlHiddenLength OBJECT-TYPE
			SYNTAX INTEGER (-1..128)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Hidden Length Field of Tag Formatt Attr.
				The -1 of value means this field is not defined.
				The Zero of value means this field is variable."
			::= { sleV2PPPoETagFormatAttrControl 9 }

		
		-- *******.4.1.62***********.2.2.2.10
		sleV2PPPoETagFormatAttrControlType OBJECT-TYPE
			SYNTAX INTEGER (-1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Type Field of Tag Format Attr.
				The -1 of value means this field is not defined"
			::= { sleV2PPPoETagFormatAttrControl 10 }

		
		-- *******.4.1.62***********.2.2.2.11
		sleV2PPPoETagFormatAttrControlVarType OBJECT-TYPE
			SYNTAX INTEGER
				{
				hex(1),
				index(2),
				ip(3),
				string(4),
				tagFmt(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Variable Type Field of Tag Format Attr."
			::= { sleV2PPPoETagFormatAttrControl 11 }

		
		-- *******.4.1.62***********.2.2.2.12
		sleV2PPPoETagFormatAttrControlVarValue OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..255))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Variable Value Field of Tag Format Attr."
			::= { sleV2PPPoETagFormatAttrControl 12 }

		
		-- *******.4.1.62***********.2.2.3
		sleV2PPPoETagFormatAttrNotification OBJECT IDENTIFIER ::= { sleV2PPPoETagFormatAttribute 3 }

		
		-- *******.4.1.62***********.2.2.3.1
		sleV2PPPoETagFormatAttrAdded NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagFormatAttrControlRequest, sleV2PPPoETagFormatAttrControlTimeStamp, sleV2PPPoETagFormatAttrControlReqResult, sleV2PPPoETagFormatAttrControlName, sleV2PPPoETagFormatAttrControlID, 
				sleV2PPPoETagFormatAttrControlLength, sleV2PPPoETagFormatAttrControlHiddenLength, sleV2PPPoETagFormatAttrControlType, sleV2PPPoETagFormatAttrControlVarType, sleV2PPPoETagFormatAttrControlVarValue
				 }
			STATUS current
			DESCRIPTION 
				"addTagFormatAttr"
			::= { sleV2PPPoETagFormatAttrNotification 1 }

		
		-- *******.4.1.62***********.2.2.3.2
		sleV2PPPoETagFormatAttrDeleted NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagFormatAttrControlRequest, sleV2PPPoETagFormatAttrControlTimeStamp, sleV2PPPoETagFormatAttrControlReqResult, sleV2PPPoETagFormatAttrControlName, sleV2PPPoETagFormatAttrControlID
				 }
			STATUS current
			DESCRIPTION 
				"deleteTagFormatAttr"
			::= { sleV2PPPoETagFormatAttrNotification 2 }

		
		-- *******.4.1.62***********.2.2.3.3
		sleV2PPPoETagFormatAttrModified NOTIFICATION-TYPE
			OBJECTS { sleV2PPPoETagFormatAttrControlRequest, sleV2PPPoETagFormatAttrControlTimeStamp, sleV2PPPoETagFormatAttrControlReqResult, sleV2PPPoETagFormatAttrControlName, sleV2PPPoETagFormatAttrControlID, 
				sleV2PPPoETagFormatAttrControlLength, sleV2PPPoETagFormatAttrControlHiddenLength, sleV2PPPoETagFormatAttrControlType, sleV2PPPoETagFormatAttrControlVarType, sleV2PPPoETagFormatAttrControlVarValue
				 }
			STATUS current
			DESCRIPTION 
				"modifyTagFormatAttr"
			::= { sleV2PPPoETagFormatAttrNotification 3 }

		
		-- *******.4.1.6296.102.16.5
		sleV2PPPoEGroup OBJECT-GROUP
			OBJECTS { sleV2PPPoEControlRequest, sleV2PPPoEControlStatus, sleV2PPPoEControlTimer, sleV2PPPoEControlTimeStamp, sleV2PPPoEControlReqResult, 
				sleV2PPPoESnoopingActivity, sleV2PPPoEControlSnoopingActivity, sleV2PPPoETagFormatControlReqResult, sleV2PPPoETagFormatControlTimeStamp, sleV2PPPoETagFormatControlTimer, 
				sleV2PPPoETagFormatName, sleV2PPPoETagFormatControlRequest, sleV2PPPoETagFormatControlStatus, sleV2PPPoETagFormatControlName, sleV2PPPoETagFormatAttrControlVarType, 
				sleV2PPPoETagFormatAttrControlType, sleV2PPPoETagFormatAttrControlHiddenLength, sleV2PPPoETagFormatAttrControlLength, sleV2PPPoETagFormatAttrControlName, sleV2PPPoETagFormatAttrControlID, 
				sleV2PPPoETagFormatAttrControlReqResult, sleV2PPPoETagFormatAttrControlTimeStamp, sleV2PPPoETagFormatAttrControlTimer, sleV2PPPoETagFormatAttrControlStatus, sleV2PPPoETagFormatAttrVarValue, 
				sleV2PPPoETagFormatAttrControlRequest, sleV2PPPoETagFormatAttrVarType, sleV2PPPoETagFormatAttrType, sleV2PPPoETagFormatAttrHiddenLength, sleV2PPPoETagFormatAttrLength, 
				sleV2PPPoETagFormattAttrID, sleV2PPPoETagFormatAttrControlVarValue, sleV2PPPoETagOperControlAction, sleV2PPPoETagOperControlVlanId, sleV2PPPoETagOperControlPortIndex, 
				sleV2PPPoETagOperControlId, sleV2PPPoETagOperControlType, sleV2PPPoETagOperControlReqResult, sleV2PPPoETagOperControlTimeStamp, sleV2PPPoETagOperControlTimer, 
				sleV2PPPoETagOperControlStatus, sleV2PPPoETagOperControlRequest, sleV2PPPoETagOperAction, sleV2PPPoETagOperVlanId, sleV2PPPoETagOperPortIndex, 
				sleV2PPPoEFilterControlAction, sleV2PPPoEFilterControlVlanId, sleV2PPPoEFilterControlPortIndex, sleV2PPPoEFilterControlId, sleV2PPPoEFilterControlReqResult, 
				sleV2PPPoEFilterControlTimeStamp, sleV2PPPoEFilterControlTimer, sleV2PPPoEFilterControlStatus, sleV2PPPoEFilterControlRequest, sleV2PPPoEFilterVlanId, 
				sleV2PPPoEFilterPortIndex, sleV2PPPoEFilterId, sleV2PPPoETagOperType, sleV2PPPoETagOperId, sleV2PPPoETagOperControlTagFmt, 
				sleV2PPPoETagOperTagFmt, sleV2PPPoEFilterAction }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2PPPoE 5 }

		
		-- *******.4.1.6296.102.16.6
		sleV2PPPoENotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleV2PPPoESnoopingActivityChanged, sleV2PPPoETagFormatDestroyed, sleV2PPPoETagFormatCreated, sleV2PPPoETagFormatAttrModified, sleV2PPPoETagFormatAttrDeleted, 
				sleV2PPPoETagFormatAttrAdded, sleV2PPPoETagOperDestroyChanged, sleV2PPPoETagOperCreateChanged, sleV2PPPoETagOperModifyChanged, sleV2PPPoEFilterDestroyChanged, 
				sleV2PPPoEFilterModifyChanged, sleV2PPPoEFilterCreatedChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2PPPoE 6 }

		
	
	END

--
-- slev2-pppoe-mib.mib
--
