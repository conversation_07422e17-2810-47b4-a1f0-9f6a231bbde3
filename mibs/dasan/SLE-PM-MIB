--
-- sle-pm-mib.mib
-- MIB generated by MG-SO<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Wednesday, December 30, 2015 at 17:52:38
--

	SLE-PM-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			zeroDotZero, TimeTicks, Integer32, Unsigned32, <PERSON><PERSON>ge32, 
			Counter32, Counter64, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		slePmMgr MODULE-IDENTITY 
			LAST-UPDATED "201511300000Z"		-- November 30, 2015 at 00:00 GMT
			ORGANIZATION 
				" "
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains all needed informations about 
				Performance Manager."
			REVISION "201511300000Z"		-- November 30, 2015 at 00:00 GMT
			DESCRIPTION 
				"This MIB module defines the Performance objects ."
			::= { sleMgmt 94 }

		
	
--
-- Textual conventions
--
	
		PmClassId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER (1..65535)

		PmId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Current PM ID"
			SYNTAX INTEGER (1..65535)

		PmSrc ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"
				|type|length|value|type|length|value|....
				
				type (1-byte) : PM location type
				length(1-byte) : PM location value length
				value (length-bytes) : PM location value"
			SYNTAX OCTET STRING (SIZE (68))

		PmTcaState ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				" This status explains whether TRAP status is enabled or disabled.
				Enable(1) : TCA State is Enabled. Hence TRAPs will be sent for this.
				Disable(0) : TCA State is Disabled. So TCA won't be notified to user."
			SYNTAX INTEGER
				{
				enable(1),
				disable(0)
				}

		PmDateTime ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"TOD integer value"
			SYNTAX Unsigned32

	
--
-- Node definitions
--
	
		slePmNeId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (6))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"system MAC address"
			::= { slePmMgr 1 }

		
		slePmConfigBase OBJECT IDENTIFIER ::= { slePmMgr 2 }

		
		slePmConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePmConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" This table is the Performance Config table. 
				It gets populated when system init is done. 
				And it contains all the class performance supported for the device"
			::= { slePmConfigBase 1 }

		
		slePmConfigEntry OBJECT-TYPE
			SYNTAX SlePmConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { slePmConfigSeqId }
			::= { slePmConfigTable 1 }

		
		SlePmConfigEntry ::=
			SEQUENCE { 
				slePmConfigSeqId
					INTEGER,
				slePmConfigClassId
					PmClassId,
				slePmConfigPmId
					PmId,
				slePmConfigSource
					PmSrc,
				slePmConfigTcaStat
					INTEGER,
				slePmConfigTcaEnable
					PmTcaState,
				slePmConfigTh15Min
					Counter64,
				slePmConfigTh1Day
					Counter64
			 }

		slePmConfigSeqId OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePmConfigEntry 1 }

		
		slePmConfigClassId OBJECT-TYPE
			SYNTAX PmClassId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class ID starts from 1 to 255 for the system.
				And these IDs are generated internally"
			::= { slePmConfigEntry 2 }

		
		slePmConfigPmId OBJECT-TYPE
			SYNTAX PmId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current PM ID"
			::= { slePmConfigEntry 3 }

		
		slePmConfigSource OBJECT-TYPE
			SYNTAX PmSrc
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"
				|type|length|value|type|length|value|....
				
				type (1-byte) : PM location type
				length(1-byte) : PM location value length
				value (length-bytes) : PM location value"
			::= { slePmConfigEntry 4 }

		
		slePmConfigTcaStat OBJECT-TYPE
			SYNTAX INTEGER
				{
				tcaNormal(0),
				tcaOccur(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"TCA status    
				TCA Normal (0)
				TCA Occur  (1)	 "
			::= { slePmConfigEntry 5 }

		
		slePmConfigTcaEnable OBJECT-TYPE
			SYNTAX PmTcaState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" TCA enable Confguration : enabled or disabled.
				Enable(1) : TCA State is Enabled. Hence TRAPs will be sent for this.
				Disable(0) : TCA State is Disabled. So TCA won't be notified to user."
			::= { slePmConfigEntry 6 }

		
		slePmConfigTh15Min OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"15minute Performance TCA threshold configuration count"
			::= { slePmConfigEntry 7 }

		
		slePmConfigTh1Day OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"1day Performance TCA threshold configuration count"
			::= { slePmConfigEntry 8 }

		
		slePmConfigControl OBJECT IDENTIFIER ::= { slePmConfigBase 2 }

		
		slePmConfigControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setPmConfigTcaEnable(1),
				setPmConfigTh15Min(2),
				setPmConfigTh1Day(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { slePmConfigControl 1 }

		
		slePmConfigControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { slePmConfigControl 2 }

		
		slePmConfigControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { slePmConfigControl 3 }

		
		slePmConfigControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { slePmConfigControl 4 }

		
		slePmConfigControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { slePmConfigControl 5 }

		
		slePmConfigControlSeqId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PM Manager Class ID"
			::= { slePmConfigControl 6 }

		
		slePmConfigControlTcaEnable OBJECT-TYPE
			SYNTAX PmTcaState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" TCA enable Confguration : enabled or disabled.
				Enable(1) : TCA State is Enabled. Hence TRAPs will be sent for this.
				Disable(0) : TCA State is Disabled. So TCA won't be notified to user."
			::= { slePmConfigControl 7 }

		
		slePmConfigControlTh15Min OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"15minute Performance TCA threshold configuration count"
			::= { slePmConfigControl 8 }

		
		slePmConfigControlTh1Day OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"1day Performance TCA threshold configuration count"
			::= { slePmConfigControl 9 }

		
		slePmConfigNotification OBJECT IDENTIFIER ::= { slePmConfigBase 3 }

		
		slePmConfigTcaEnableChanged NOTIFICATION-TYPE
			OBJECTS { slePmNeId, slePmConfigControlRequest, slePmConfigControlTimeStamp, slePmConfigControlReqResult, slePmConfigControlSeqId, 
				slePmConfigControlTcaEnable }
			STATUS current
			DESCRIPTION 
				" Notification for Tca Enable change"
			::= { slePmConfigNotification 1 }

		
		slePmConfigTh15MinChanged NOTIFICATION-TYPE
			OBJECTS { slePmNeId, slePmConfigControlRequest, slePmConfigControlTimeStamp, slePmConfigControlReqResult, slePmConfigControlSeqId, 
				slePmConfigControlTh15Min }
			STATUS current
			DESCRIPTION 
				" Notification for 15minute theshold change"
			::= { slePmConfigNotification 2 }

		
		slePmConfigTh1DayChanged NOTIFICATION-TYPE
			OBJECTS { slePmNeId, slePmConfigControlRequest, slePmConfigControlTimeStamp, slePmConfigControlReqResult, slePmConfigControlSeqId, 
				slePmConfigControlTh1Day }
			STATUS current
			DESCRIPTION 
				" Notification for 1day theshold change"
			::= { slePmConfigNotification 3 }

		
		slePmCurrentBase OBJECT IDENTIFIER ::= { slePmMgr 3 }

		
		slePmCurrentTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePmCurrentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" This table contains the current PM count."
			::= { slePmCurrentBase 1 }

		
		slePmCurrentEntry OBJECT-TYPE
			SYNTAX SlePmCurrentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { slePmCurrentSeqId, slePmCurrentTerm }
			::= { slePmCurrentTable 1 }

		
		SlePmCurrentEntry ::=
			SEQUENCE { 
				slePmCurrentSeqId
					INTEGER,
				slePmCurrentClassId
					PmClassId,
				slePmCurrentPmId
					PmId,
				slePmCurrentSource
					PmSrc,
				slePmCurrentTerm
					INTEGER,
				slePmCurrentPmCount
					Counter64,
				slePmCurrentAccSecond
					Counter32,
				slePmCurrentTcaStat
					INTEGER,
				slePmCurrentTcaTime
					TimeTicks
			 }

		slePmCurrentSeqId OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePmCurrentEntry 1 }

		
		slePmCurrentClassId OBJECT-TYPE
			SYNTAX PmClassId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current Class ID"
			::= { slePmCurrentEntry 2 }

		
		slePmCurrentPmId OBJECT-TYPE
			SYNTAX PmId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current PM ID"
			::= { slePmCurrentEntry 3 }

		
		slePmCurrentSource OBJECT-TYPE
			SYNTAX PmSrc
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"
				|type|length|value|type|length|value|....
				
				type (1-byte) : PM location type
				length(1-byte) : PM location value length
				value (length-bytes) : PM location value"
			::= { slePmCurrentEntry 4 }

		
		slePmCurrentTerm OBJECT-TYPE
			SYNTAX INTEGER
				{
				min15(1),
				day1(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePmCurrentEntry 5 }

		
		slePmCurrentPmCount OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current Performance count"
			::= { slePmCurrentEntry 6 }

		
		slePmCurrentAccSecond OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current Performance data accumulation seconds(count)"
			::= { slePmCurrentEntry 7 }

		
		slePmCurrentTcaStat OBJECT-TYPE
			SYNTAX INTEGER
				{
				tcaNormal(0),
				tcaOccur(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"TCA status    
				TCA Normal (0)
				TCA Occur  (1)	 "
			::= { slePmCurrentEntry 8 }

		
		slePmCurrentTcaTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current PM Time and Date"
			::= { slePmCurrentEntry 9 }

		
		slePmCurrentControl OBJECT IDENTIFIER ::= { slePmCurrentBase 2 }

		
		slePmCurrentControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				clearCurrentPm(1),
				clearCurrentTca(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value.
				
				clearCurrentPm(1):
					Clears the current Performance count (both 15min and 1day)
					PM history(accumulation) count is not cleared									
					
				clearCurrentTca(2):
					Clears the current Performance TCA flag (both 15min and 1day)"
			::= { slePmCurrentControl 1 }

		
		slePmCurrentControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { slePmCurrentControl 2 }

		
		slePmCurrentControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { slePmCurrentControl 3 }

		
		slePmCurrentControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { slePmCurrentControl 4 }

		
		slePmCurrentControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { slePmCurrentControl 5 }

		
		slePmCurrentNotification OBJECT IDENTIFIER ::= { slePmCurrentBase 3 }

		
		slePmCurrentPmCleared NOTIFICATION-TYPE
			OBJECTS { slePmNeId, slePmCurrentControlRequest, slePmCurrentControlTimeStamp, slePmCurrentControlReqResult }
			STATUS current
			DESCRIPTION 
				" Notification for current PM clear"
			::= { slePmCurrentNotification 1 }

		
		slePMHistoryBase OBJECT IDENTIFIER ::= { slePmMgr 4 }

		
		slePmHistoryTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlePmHistoryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This Table is used to for keeping the PM History"
			::= { slePMHistoryBase 1 }

		
		slePmHistoryEntry OBJECT-TYPE
			SYNTAX SlePmHistoryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { slePmHistorySeqId, slePmHistoryTerm, slePmHistoryIndex }
			::= { slePmHistoryTable 1 }

		
		SlePmHistoryEntry ::=
			SEQUENCE { 
				slePmHistorySeqId
					INTEGER,
				slePmHistoryClassId
					PmClassId,
				slePmHistoryPmId
					PmId,
				slePmHistoryPmSource
					PmSrc,
				slePmHistoryTerm
					INTEGER,
				slePmHistoryIndex
					INTEGER,
				slePmHistoryPmCount
					Counter64,
				slePmHistoryAccCount
					Counter32,
				slePmHistoryStartTime
					TimeTicks
			 }

		slePmHistorySeqId OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePmHistoryEntry 1 }

		
		slePmHistoryClassId OBJECT-TYPE
			SYNTAX PmClassId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History Class ID."
			::= { slePmHistoryEntry 2 }

		
		slePmHistoryPmId OBJECT-TYPE
			SYNTAX PmId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History PM Id"
			::= { slePmHistoryEntry 3 }

		
		slePmHistoryPmSource OBJECT-TYPE
			SYNTAX PmSrc
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"
				|type|length|value|type|length|value|....
				
				type (1-byte) : PM location type
				length(1-byte) : PM location value length
				value (length-bytes) : PM location value"
			::= { slePmHistoryEntry 4 }

		
		slePmHistoryTerm OBJECT-TYPE
			SYNTAX INTEGER
				{
				min15(1),
				day1(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePmHistoryEntry 5 }

		
		slePmHistoryIndex OBJECT-TYPE
			SYNTAX INTEGER (1..31)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History Index:
				15min : 1 ~ 96,
				1-day : 1 ~ 7"
			::= { slePmHistoryEntry 6 }

		
		slePmHistoryPmCount OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History Performance count"
			::= { slePmHistoryEntry 7 }

		
		slePmHistoryAccCount OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePmHistoryEntry 8 }

		
		slePmHistoryStartTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { slePmHistoryEntry 9 }

		
		slePmHistoryControl OBJECT IDENTIFIER ::= { slePMHistoryBase 2 }

		
		slePmHistoryControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearPmHistory(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value.
				clearPmHistory : clears all the PM History Table"
			::= { slePmHistoryControl 1 }

		
		slePmHistoryControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { slePmHistoryControl 2 }

		
		slePmHistoryControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { slePmHistoryControl 3 }

		
		slePmHistoryControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { slePmHistoryControl 4 }

		
		slePmHistoryControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { slePmHistoryControl 5 }

		
		slePmHistoryNotification OBJECT IDENTIFIER ::= { slePMHistoryBase 3 }

		
		slePmHistoryPmCleared NOTIFICATION-TYPE
			OBJECTS { slePmNeId, slePmHistoryControlRequest, slePmHistoryControlTimeStamp, slePmHistoryControlReqResult }
			STATUS current
			DESCRIPTION 
				" Notification for PM History cleared"
			::= { slePmHistoryNotification 1 }

		
	
	END

--
-- sle-pm-mib.mib
--
