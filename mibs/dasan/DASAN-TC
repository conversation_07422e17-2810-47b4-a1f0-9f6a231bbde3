-- *****************************************************************
-- DASAN-TC.my:  Dasan MIB Textual Conventions
--
-- April 2001, <PERSON><PERSON><PERSON> Lee
--
-- Copyright (c) 2001 by Dasan Co., Ltd.
-- All rights reserved.
-- 
-- *****************************************************************
--

DASAN-TC DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    Gauge32,
    Integer32
        FROM SNMPv2-SMI
    TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    dasanModules
        FROM DASAN-SMI;


dasanTextualConventions MODULE-IDENTITY
    LAST-UPDATED    "200101180000Z"
    ORGANIZATION    "Dasan Co., Ltd."
    CONTACT-INFO
            "Dasan Co, Ltd."
    DESCRIPTION
        "This module defines textual conventions used throughout
        dasan enterprise mibs."
    REVISION        "200104190000Z"
    DESCRIPTION
                "Added DasanAlarmSeverity textual convention.
                 Changed SAPType display hint to d.  Changed
                 INTEGER to Integer32 in DasanPort and
                 DasanIpProtocol TCs.  Changed SnmpAdminString
                 to OCTET STRING in DasanLocationSpecifier.
                 Removed IMPORTs for dasanProducts and
                 SnmpAdminString."
    REVISION    "200011210000Z"
    DESCRIPTION
        "Added DasanLocationClass, DasanLocationSpecifier 
        DasanInetAddressMask, DasanAbsZeroBasedCounter32, 
        DasanSnapShotAbsCounter32 textual conventions."
    REVISION    "9810280000Z"
    DESCRIPTION
        "Added DasanRowOperStatus, EntPhysicalIndexOrZero,
        Port and IpProtocol textual conventions."
    REVISION    "9703130000Z"
    DESCRIPTION
        "Added CountryCode textual convention."
    REVISION    "9703130000Z"
    DESCRIPTION
        "Added SAPType textual convention."
    REVISION    "9608140000Z"
    DESCRIPTION
        "Added InterfaceIndexOrZero textual convention."
    REVISION    "9607080000Z"
    DESCRIPTION
        "Added new DasanNetworkProtocol enumerations."
    REVISION    "9602220000Z"
    DESCRIPTION
        "Added Unsigned32 textual conventions."
    REVISION    "9506070000Z"
    DESCRIPTION
        "Miscellaneous updates/corrections, including making
        DasanNetworkProtocol enumerations contiguous."
    ::= { dasanModules 1 }


DasanNetworkProtocol ::= TEXTUAL-CONVENTION
    STATUS    current
    DESCRIPTION
        "Represents the different types of network layer protocols."
    -- internal note: enumerations must match those in address.h
    SYNTAX    INTEGER {
        ip (1),
        decnet (2),
        pup (3),
        chaos (4),
        xns (5),
        x121 (6),
        appletalk (7),
        clns (8),
        lat (9),
        vines (10),
        cons (11),
        apollo (12),
        stun (13),
        novell (14),
        qllc (15),
        snapshot (16),
        atmIlmi (17),
        bstun (18),
        x25pvc (19),
        unknown (65535)
    }

DasanNetworkAddress ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1x:"
    STATUS        current
    DESCRIPTION
        "Represents a network layer address.  The length and format of
        the address is protocol dependent as follows:
        ip        4 octets
        decnet    2 octets
        pup       obsolete
        chaos     2 octets
        xns       10 octets
                  first 4 octets are the net number
                  last 6 octets are the host number
        x121    
        appletalk 3 octets
                  first 2 octets are the net number
                  last octet is the host number
        clns
        lat
        vines     6 octets
                  first 4 octets are the net number
                  last 2 octets are the host number
        cons
        apollo    10 octets
                  first 4 octets are the net number
                  last 6 octets are the host number
        stun      8 octets
        novell    10 octets
                  first 4 octets are the net number
                  last 6 octets are the host number
        qllc      6 octets
        bstun     1 octet - bi-sync serial tunnel 
        snapshot  1 octet
        atmIlmi   4 octets
        x25 pvc   2 octets (12 bits)
        "
    SYNTAX    OCTET STRING

InterfaceIndexOrZero ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS    current
    DESCRIPTION
        "Either the value 0, or the ifIndex value of an
        interface in the ifTable."
    SYNTAX    Integer32 (0..**********)

SAPType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS    current
    DESCRIPTION
        "Service Access Point - is a term that denotes the means
        by which a user entity in layer n+1 accesses a service
        of a provider entity in layer n."
    SYNTAX    Integer32 (0..254)

CountryCode ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "2a"
    STATUS    current
    DESCRIPTION
        "Represents a case-insensitive 2-letter country code taken 
        from ISO-3166. Unrecognized countries are represented as 
        empty string."
    SYNTAX    OCTET STRING (SIZE (0 | 2))
 
EntPhysicalIndexOrZero ::= TEXTUAL-CONVENTION
    STATUS    current
    DESCRIPTION
        "This textual convention is an extension of entPhysicalIndex.
        If non-zero, the object is an entPhysicalIndex. If zero, no
        appropriate entPhysicalIndex exists. Any additional semantics
        are object specific."
    SYNTAX    Integer32 (0..**********)

DasanRowOperStatus ::= TEXTUAL-CONVENTION
    STATUS    current
    DESCRIPTION
        "Represents the operational status of an table entry.
        This textual convention allows explicitly representing
        the states of rows dependent on rows in other tables.

        active(1) -
            Indicates this entry's RowStatus is active
            and the RowStatus for each dependency is active.

        activeDependencies(2) -
            Indicates that the RowStatus for each dependency
            is active, but the entry's RowStatus is not active.

        inactiveDependency(3) -
            Indicates that the RowStatus for at least one
            dependency is not active.

        missingDependency(4) -
            Indicates that at least one dependency does
            not exist in it's table.
        "
    SYNTAX    INTEGER {
        active(1),
        activeDependencies(2),
        inactiveDependency(3),
        missingDependency(4)
    }

DasanPort ::= TEXTUAL-CONVENTION
    STATUS    current
    DESCRIPTION
        "The TCP or UDP port number range."
    REFERENCE
        "Transmission Control Protocol. J. Postel. RFC793,
        User Datagram Protocol. J. Postel. RFC768"
    SYNTAX Integer32 ( 0..65535 )

DasanIpProtocol ::= TEXTUAL-CONVENTION
    STATUS    current
    DESCRIPTION
        "IP protocol number range."
    REFERENCE
        "Internet Protocol. J. Postel. RFC791"
    SYNTAX Integer32 ( 0..255 ) 



DasanLocationClass ::= TEXTUAL-CONVENTION
    STATUS    current
    DESCRIPTION
        "An enumerated value which provides an indication of
        the general location type of a particular physical and/or
        logical interface.
        chassis - a system framework for mounting one or more 
                  shelves/slots/cards.
        shelf - a cabinet that holds one or more slots.
        slot -  card or subSlot holder.
        subSlot - daughter-card holder.
        port - a physical port (e.g., a DS1 or DS3 physical port).
        subPort - a logical port on a physical port (e.g., a DS1 
                  subPort on a DS3 physical port).
        channel - a logical interface (e.g., a DS0 channel, signalling
                  channel, ATM port, other virtual interfaces).
        subChannel - a sub-channel on a logical interface.
        "
    SYNTAX    INTEGER  {
        chassis(1),
        shelf(2),
        slot(3),
        subSlot(4),
        port(5),
        subPort(6),
        channel(7),
        subChannel(8)
    }

DasanLocationSpecifier ::= TEXTUAL-CONVENTION
    STATUS    current
    DESCRIPTION
        "Use this TC to define objects that indicate the
        physical entity and/or logical interface location 
        of a managed entity on a managed device. In SNMP, a 
        standard mechanism for indicating the physical location
        of entities is via the ENTITY-MIB. However, that approach
        is not satisfactory in some cases because:

        1. The entity requiring a location-based naming may be 
           associated with an entity which can not be represented 
           as a physical entity in the ENTITY-MIB,
        2. NMS applications may desire a more direct 
           name/representation of a physical entity than is 
           available via the ENTITY-MIB, e.g., a physical entity
           which is named via a hierarchy of levels in the ENTITY-MIB.

        The value of an object defined using this TC is an ASCII 
        string consisting of zero or more elements separated by 
        commas. Each element is of the form <tag> = <value>. 

        An example of this syntax is 'slot=5,port=3'.

        The syntax of the string is formally specified using
        ABNF notation (with one exception, noted below), as
        follows:                 

        location-specifier =  elem *(',' elem) 
                       ; subject to 
                       ; size restriction specified in the SYNTAX
                       ; clause below

        elem    =   loctype '=' number

        number  = %x00-FFFFFFFF / %d0-4294967295

        loctype = 1*32VCHAR 

        It is recommended that loctype use one of the enumerated
        labels defined for DasanLocationClass.

        (NOTE: To conform to ABNF notation as defined in RFC2234, 
         substitute the single-quote symbol with a double-quote 
         symbol in the above rules.)

        A zero length of DasanLocationSpecifier is object-specific
        and must be defined as part of the description of any object
        which uses this syntax.
        "
    REFERENCE 
        "RFC2234, Augmented BNF for syntax specifications: ABNF"

    SYNTAX    OCTET STRING (SIZE (0..255)) 

DasanInetAddressMask ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Denotes a generic Internet subnet address mask.
        The Internet subnet address mask is represented as the
        number of contiguous 1-bit from MSB (most significant bit)
        of the Internet subnet address mask.
        A DasanInetAddressMask value is always interpreted within
        the context of an InetAddressType value. The 
        InetAddressType only object or InetAddressType with
        InetAddress objects which define the context must be
        registered immediately before the object which uses the
        DasanInetAddressMask textual convention.  In other words,
        the object identifiers for the InetAddressType object and
        the DasanInetAddressMask object MUST have the same length
        and the last sub-identifier of the InetAddressType object
        MUST be 1 less than the last sub-identifier of the 
        DasanInetAddressMask object and MUST be 2 less than the
        last sub-identifier of the DasanInetAddressMask object if
        an InetAddress object is defined between InetAddressType
        and DasanInetAddressMask objects.
        The maximum value of the DasanInetAddressMask TC is 32 for
        the value 'ipv4(1)' in InetAddressType object and 128 for
        the value 'ipv6(2)' in InetAddressType object.
        The value zero is object-specific and must therefore be
        defined as part of the description of any object which
        uses this syntax.  Examples of the usage of zero might
        include situations where Internet subnet mask was unknown,
        or when none subnet masks need to be referenced." 

    REFERENCE
        "RFC2851, Textual Conventions for Internet Network Addresses."

    SYNTAX      Unsigned32   (0..128)

DasanAbsZeroBasedCounter32 ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This TC describes an object which counts events with the
        following semantics: objects of this type will be set to
        zero(0) on creation and will thereafter count appropriate
        events, it locks at the maximum value of 4,294,967,295 if
        the counter overflows.
        This TC may be used only in situations where wrapping is
        not possible or extremely unlikely situation."
    SYNTAX      Gauge32

DasanSnapShotAbsCounter32 ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This TC describes an object which stores a snap-shot value
        with the following semantics: objects of this type will
        take a snap-shot value from their associated
        DasanAbsZeroBasedCounter32 type objects on creation."
    SYNTAX      Unsigned32 

DasanAlarmSeverity ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Represents the perceived alarm severity associated
        with a service or safety affecting condition and/or
        event.  These are based on ITU severities, except
        that info(7) is added.

	    cleared(1) -
		Indicates a previous alarm condition has been
		cleared.  It is not required (unless specifically
		stated elsewhere on a case by case basis) that an
		alarm condition that has been cleared will produce
		a notification or other event containing an
		alarm severity with this value.

	    indeterminate(2) -
		Indicates that the severity level cannot be
		determined. 

	    critical(3) -
		Indicates that a service or safety affecting
		condition has occurred and an immediate
		corrective action is required.

	    major(4) -
		Indicates that a service affecting condition has
		occurred and an urgent corrective action is
		required.

	    minor(5) -
		Indicates the existence of a non-service affecting
		condition and that corrective action should be
		taken in order to prevent a more serious (for
		example, service or safety affecting) condition.

	    warning(6) -
		Indicates the detection of a potential or impending
		service or safety affecting condition, before any
		significant effects have been felt.

	    info(7) -
		Indicates an alarm condition that does not
		meet any other severity definition.  This can
		include important, but non-urgent, notices or
		informational events.
	 "
    REFERENCE
	 "ITU-X.733"
    SYNTAX INTEGER {
	 cleared(1),
	 indeterminate(2),
	 critical(3),
	 major(4),
	 minor(5),
	 warning(6),
	 info(7)
    }

ModuleIndex ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
       "The value of this object identifies the
       module for which this entry contains
       management information.  The value of this
       object for a particular module has the same
       value as the moduleIndex object."
   SYNTAX Integer32

PortIndex ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
       "The value of this object identifies the
       port for which this entry contains
       management information.  The value of this
       object for a particular module has the same
       value as the portIndex object."
   SYNTAX Integer32

END
