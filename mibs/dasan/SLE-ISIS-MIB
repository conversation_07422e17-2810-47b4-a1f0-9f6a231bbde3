--
-- sle-isis-mib.mib
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, April 27, 2016 at 13:53:44
--

	SLE-ISIS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, Integer32, Unsigned32, Gauge32, 
			OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		sleISIS MODULE-IDENTITY 
			LAST-UPDATED "201603161455Z"		-- March 16, 2016 at 14:55 GMT
			ORGANIZATION 
				"HANASOFT"
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains information about RIP version 2."
			REVISION ""		-- 
			DESCRIPTION 
				"OSFPv2"
			::= { sleMgmt 56 }

		
	
	
--
-- Node definitions
--
	
		sleISISBase OBJECT IDENTIFIER ::= { sleISIS 1 }

		
		sleISISBaseInfo OBJECT IDENTIFIER ::= { sleISISBase 1 }

		
		sleISISRestartSuppressAdjacency OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ISIS restart suppress-adjacency"
			::= { sleISISBaseInfo 1 }

		
		sleISISRestartHelper OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ISIS restart helper"
			::= { sleISISBaseInfo 2 }

		
		sleISISRestartGracePeriod OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS Graceful-restart period.
				Seconds: <1-65535> Specify the number of seconds in the grace period.
				"
			::= { sleISISBaseInfo 3 }

		
		sleISISBaseControl OBJECT IDENTIFIER ::= { sleISISBase 2 }

		
		sleISISControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setISISRestartSuppressAdj(1),
				setISISRestartHelper(2),
				setISISRestartGracefulPeriod(3),
				clearISISClnsNeighbors(4),
				clearISISClnsIsNeighbors(5),
				clearISISCounter(6),
				clearISISInterfaceCounter(7),
				clearISISProcess(8),
				clearISISRoute(9),
				retartISISGraceful(10),
				snmpRestartISIS(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleISISBaseControl 1 }

		
		sleISISControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleISISBaseControl 2 }

		
		sleISISControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum time for the manager for a long running user command"
			::= { sleISISBaseControl 3 }

		
		sleISISControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command(end of command)"
			::= { sleISISBaseControl 4 }

		
		sleISISControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command"
			::= { sleISISBaseControl 5 }

		
		sleISISControlRestartSuppressAdjacency OBJECT-TYPE
			SYNTAX INTEGER
				{
				diable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISBaseControl 6 }

		
		sleISISControlRestartHelper OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISBaseControl 7 }

		
		sleISISControlRestartPeriod OBJECT-TYPE
			SYNTAX Integer32 (0..1800)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ISIS Graceful-restart period."
			::= { sleISISBaseControl 8 }

		
		sleISISControlClearSystemId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"System-ID Neighbor system ID in XXXX. XXXX. XXXX format."
			::= { sleISISBaseControl 9 }

		
		sleISISControlClearIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Interface Name of ISIS for clear"
			::= { sleISISBaseControl 10 }

		
		sleISISControlClearRouteTag OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Routing area tag"
			::= { sleISISBaseControl 11 }

		
		sleISISControlClearRouteMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				redistribution(1),
				all(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The route mode to clear.
				Redistribution: Clear ISIS local redistribution routes.
				all: Clear all of the ISIS routing table.s"
			::= { sleISISBaseControl 12 }

		
		sleISISBaseNotification OBJECT IDENTIFIER ::= { sleISISBase 3 }

		
		sleISISProc OBJECT IDENTIFIER ::= { sleISIS 2 }

		
		sleISISProcInfo OBJECT IDENTIFIER ::= { sleISISProc 1 }

		
		sleISISProcInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISProcInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcInfo 1 }

		
		sleISISProcInfoEntry OBJECT-TYPE
			SYNTAX SleISISProcInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISProcInfoInstanceID }
			::= { sleISISProcInfoTable 1 }

		
		SleISISProcInfoEntry ::=
			SEQUENCE { 
				sleISISProcInfoInstanceID
					Integer32,
				sleISISProcInfoTag
					OCTET STRING,
				sleISISProcBfdAllInterface
					INTEGER,
				sleISISProcCapCspf
					INTEGER,
				sleISISProcDynHostname
					INTEGER,
				sleISISProcDynHostnameAreaTag
					INTEGER,
				sleISISProcIgnLspErr
					INTEGER,
				sleISISProcRouteHighPriorityTag
					Gauge32,
				sleISISProcIspfLevel
					INTEGER,
				sleISISProcIsTypeLevel
					INTEGER,
				sleISISProcLspGenInterval
					INTEGER,
				sleISISProcLspGenIntervalLevel
					INTEGER,
				sleISISProcLspMtu
					INTEGER,
				sleISISProcLspMtuLevel
					INTEGER,
				sleISISProcLspRefreshInterval
					INTEGER,
				sleISISProcMaxAreaAddressNum
					INTEGER,
				sleISISProcMaxLspLifetime
					INTEGER,
				sleISISProcMetricStyle
					INTEGER,
				sleISISProcMetricStyleLevel
					INTEGER,
				sleISISProcMplsTrafficEngLevel
					INTEGER,
				sleISISProcMplsTrafficEngRouterID
					IpAddress,
				sleISISProcPrcIntervalExpMinDelay
					INTEGER,
				sleISISProcPrcIntervalExpMaxDelay
					INTEGER,
				sleISISProcProtocolTopolgy
					INTEGER,
				sleISISProcRestartTimerVal
					INTEGER,
				sleISISProcRestartTimerLevel
					INTEGER,
				sleISISProcSpfIntervalExpMin
					INTEGER,
				sleISISProcSpfIntervalExpMax
					INTEGER,
				sleISISProcSpfIntervalExpLevel
					INTEGER,
				sleISISProcAuthMode
					INTEGER,
				sleISISProcAuthModeLevel
					INTEGER,
				sleISISProcAuthSendOnly
					INTEGER,
				sleISISProcAuthSendOnlyLevel
					INTEGER,
				sleISISProcDomPassVal
					OCTET STRING,
				sleISISProcDomPassAuthSnp
					INTEGER,
				sleISISProcAreaPassVal
					OCTET STRING,
				sleISISProcAreaPassAuthSnp
					INTEGER,
				sleISISProcSetOverloadBit
					INTEGER,
				sleISISProcSetOverloadBitOnStartup
					INTEGER,
				sleISISProcSetOverloadBitOnStartupInterval
					Integer32,
				sleISISProcSetOverloadBitSuppress
					INTEGER,
				sleISISProcWaitTimerVal
					INTEGER,
				sleISISProcWaitTimerLevel
					INTEGER,
				sleISISProcAuthenKeyChainL1
					OCTET STRING,
				sleISISProcAuthenKeyChainL2
					OCTET STRING,
				sleISISProcAuthenKeyChainL1L2
					OCTET STRING
			 }

		sleISISProcInfoInstanceID OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Instance ID of ISIS Process"
			::= { sleISISProcInfoEntry 1 }

		
		sleISISProcInfoTag OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS Process Tag"
			::= { sleISISProcInfoEntry 2 }

		
		sleISISProcBfdAllInterface OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Enable BFD on all interfaces"
			::= { sleISISProcInfoEntry 3 }

		
		sleISISProcCapCspf OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Enable Constrained Shortest Path First
				"
			::= { sleISISProcInfoEntry 4 }

		
		sleISISProcDynHostname OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Enable dynamic hostname"
			::= { sleISISProcInfoEntry 5 }

		
		sleISISProcDynHostnameAreaTag OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use area-tag as hostname"
			::= { sleISISProcInfoEntry 6 }

		
		sleISISProcIgnLspErr OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ignore LSPs with bad checksums"
			::= { sleISISProcInfoEntry 7 }

		
		sleISISProcRouteHighPriorityTag OBJECT-TYPE
			SYNTAX Gauge32 (1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sets the high priority of route"
			::= { sleISISProcInfoEntry 8 }

		
		sleISISProcIspfLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2only(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Enable incremental SPF for ISIS proces"
			::= { sleISISProcInfoEntry 9 }

		
		sleISISProcIsTypeLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IS Level for ISIS process"
			::= { sleISISProcInfoEntry 10 }

		
		sleISISProcLspGenInterval OBJECT-TYPE
			SYNTAX INTEGER (1..120)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Minimum interval between regenerating same LSP"
			::= { sleISISProcInfoEntry 11 }

		
		sleISISProcLspGenIntervalLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IS Level for ISIS process"
			::= { sleISISProcInfoEntry 12 }

		
		sleISISProcLspMtu OBJECT-TYPE
			SYNTAX INTEGER (512..1492)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set lsp MTU in bytes	"
			::= { sleISISProcInfoEntry 13 }

		
		sleISISProcLspMtuLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IS Level for ISIS process"
			::= { sleISISProcInfoEntry 14 }

		
		sleISISProcLspRefreshInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set LSP refresh interval"
			::= { sleISISProcInfoEntry 15 }

		
		sleISISProcMaxAreaAddressNum OBJECT-TYPE
			SYNTAX INTEGER (3..254)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Configure isis maximum area address"
			::= { sleISISProcInfoEntry 16 }

		
		sleISISProcMaxLspLifetime OBJECT-TYPE
			SYNTAX INTEGER (350..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set maximum LSP lifetime"
			::= { sleISISProcInfoEntry 17 }

		
		sleISISProcMetricStyle OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				wide(1),
				wideTransition(2),
				transition(3),
				narrowTransition(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use old-style (ISO 10589) or new-style packet formats"
			::= { sleISISProcInfoEntry 18 }

		
		sleISISProcMetricStyleLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Level of Metric Style	"
			::= { sleISISProcInfoEntry 19 }

		
		sleISISProcMplsTrafficEngLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Traffice Engine level of MPLS routing protocol"
			::= { sleISISProcInfoEntry 20 }

		
		sleISISProcMplsTrafficEngRouterID OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Traffice Engine Router ID of MPLS routing protoco"
			::= { sleISISProcInfoEntry 21 }

		
		sleISISProcPrcIntervalExpMinDelay OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use exponential backoff min delays between PRC calculation"
			::= { sleISISProcInfoEntry 22 }

		
		sleISISProcPrcIntervalExpMaxDelay OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use exponential backoff max delays between PRC calculation"
			::= { sleISISProcInfoEntry 23 }

		
		sleISISProcProtocolTopolgy OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Protocol Topology"
			::= { sleISISProcInfoEntry 24 }

		
		sleISISProcRestartTimerVal OBJECT-TYPE
			SYNTAX INTEGER (5..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Restart timer interval"
			::= { sleISISProcInfoEntry 25 }

		
		sleISISProcRestartTimerLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Restart timer level"
			::= { sleISISProcInfoEntry 26 }

		
		sleISISProcSpfIntervalExpMin OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use exponential backoff min delays between SPF   calculations"
			::= { sleISISProcInfoEntry 27 }

		
		sleISISProcSpfIntervalExpMax OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use exponential backoff max delays between SPF   calculations"
			::= { sleISISProcInfoEntry 28 }

		
		sleISISProcSpfIntervalExpLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Level of SPF Interval Exp"
			::= { sleISISProcInfoEntry 29 }

		
		sleISISProcAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				md5(1),
				text(2),
				md5Text(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Authentication mode of ISIS Process	"
			::= { sleISISProcInfoEntry 30 }

		
		sleISISProcAuthModeLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Authentication level of ISIS Process"
			::= { sleISISProcInfoEntry 31 }

		
		sleISISProcAuthSendOnly OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Authentication send-only"
			::= { sleISISProcInfoEntry 32 }

		
		sleISISProcAuthSendOnlyLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Level of Authentication send-only"
			::= { sleISISProcInfoEntry 33 }

		
		sleISISProcDomPassVal OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS Routing domain password"
			::= { sleISISProcInfoEntry 34 }

		
		sleISISProcDomPassAuthSnp OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				validate(1),
				sendOnly(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"SNP Authentication of ISIS Routing domain "
			::= { sleISISProcInfoEntry 35 }

		
		sleISISProcAreaPassVal OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS Routing Area password"
			::= { sleISISProcInfoEntry 36 }

		
		sleISISProcAreaPassAuthSnp OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				validate(1),
				sendOnly(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"SNP Authentication of ISIS Routing Area"
			::= { sleISISProcInfoEntry 37 }

		
		sleISISProcSetOverloadBit OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Signal other routers not to use us in SPF"
			::= { sleISISProcInfoEntry 38 }

		
		sleISISProcSetOverloadBitOnStartup OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				overload(1),
				waitForBgp(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set overload-bit only temporarily after reboot"
			::= { sleISISProcInfoEntry 39 }

		
		sleISISProcSetOverloadBitOnStartupInterval OBJECT-TYPE
			SYNTAX Integer32 (5..86400)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Time in seconds to advertise ourself as overloaded after  reboot"
			::= { sleISISProcInfoEntry 40 }

		
		sleISISProcSetOverloadBitSuppress OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				external(1),
				interlevel(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If overload-bit set, suppress the following types of IP prefixes"
			::= { sleISISProcInfoEntry 41 }

		
		sleISISProcWaitTimerVal OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set lsp MTU in bytes	"
			::= { sleISISProcInfoEntry 42 }

		
		sleISISProcWaitTimerLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IS Level for ISIS process"
			::= { sleISISProcInfoEntry 43 }

		
		sleISISProcAuthenKeyChainL1 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The authentication key-chain with level1"
			::= { sleISISProcInfoEntry 44 }

		
		sleISISProcAuthenKeyChainL2 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The authentication key-chain with level2."
			::= { sleISISProcInfoEntry 45 }

		
		sleISISProcAuthenKeyChainL1L2 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The authentication key-chain with level1 and level2."
			::= { sleISISProcInfoEntry 46 }

		
		sleISISProcInfoControl OBJECT IDENTIFIER ::= { sleISISProcInfo 2 }

		
		sleISISProcInfoControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createISISProcess(1),
				deleteISISProcess(2),
				setISISProcBfdAllInterface(3),
				setISISProcCapabilityCspf(4),
				setISISProcDynamicHostname(5),
				setISISProcIgnoreLspErr(6),
				setISISProcRouteHighPriorityTag(7),
				setISISProcIspfLevel(8),
				setISISProcIsTypeLevel(9),
				setISISProcLspGenInterval(10),
				setISISProcLspMtu(11),
				setISISProcLspRefreshInterval(12),
				setISISProcMaxAreaAddress(13),
				setISISProcMaxLspLifetime(14),
				setISISProcMetricStyle(15),
				setISISProcMplsTrafficEngLevel(16),
				setISISProcMplsTrafficEngRouterID(17),
				setISISProcPrcIntervalExp(18),
				setISISProcProtocoalTopology(19),
				setISISProcRestartTimer(20),
				setISISProcSpfIntervalExp(21),
				setISISProcAuthMode(22),
				setISISProcAuthSendOnly(23),
				setISISProcDomPassword(24),
				setISISProcAreaPassword(25),
				setISISProcSetOverloadBit(26),
				setISISProcWaitTimer(27),
				setISISProcAuthenKeyChain(28),
				delISISProcAuthenKeyChain(29)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcInfoControl 1 }

		
		sleISISProcInfoControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcInfoControl 2 }

		
		sleISISProcInfoControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcInfoControl 3 }

		
		sleISISProcInfoControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcInfoControl 4 }

		
		sleISISProcInfoControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcInfoControl 5 }

		
		sleISISProcInfoControlTag OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ISIS Process Tag"
			::= { sleISISProcInfoControl 6 }

		
		sleISISProcInfoControlBfdAllInterface OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable BFD on all interfaces"
			::= { sleISISProcInfoControl 7 }

		
		sleISISProcInfoControlCapCspf OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable Constrained Shortest Path First"
			::= { sleISISProcInfoControl 8 }

		
		sleISISProcInfoControlDynHostname OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable dynamic hostname"
			::= { sleISISProcInfoControl 9 }

		
		sleISISProcInfoControlDynHostnameAreaTag OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use area-tag as hostname"
			::= { sleISISProcInfoControl 10 }

		
		sleISISProcInfoControlIgnLspErr OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ignore LSPs with bad checksums"
			::= { sleISISProcInfoControl 11 }

		
		sleISISProcInfoControlRouteHighPriorityTag OBJECT-TYPE
			SYNTAX Gauge32 (1..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Sets the high priority of route"
			::= { sleISISProcInfoControl 12 }

		
		sleISISProcInfoControlIspfLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable incremental SPF for ISIS proces"
			::= { sleISISProcInfoControl 13 }

		
		sleISISProcInfoControlIsTypeLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IS Level for ISISprocess"
			::= { sleISISProcInfoControl 14 }

		
		sleISISProcInfoControlLspGenInterval OBJECT-TYPE
			SYNTAX INTEGER (1..120)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Minimum interval between regenerating same LSP"
			::= { sleISISProcInfoControl 15 }

		
		sleISISProcInfoControlLspGenIntervalLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IS Level for ISISprocess"
			::= { sleISISProcInfoControl 16 }

		
		sleISISProcInfoControlLspMtu OBJECT-TYPE
			SYNTAX INTEGER (512..1492)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set lsp MTU in bytes"
			::= { sleISISProcInfoControl 17 }

		
		sleISISProcInfoControlLspMtuLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IS Level for ISISprocess"
			::= { sleISISProcInfoControl 18 }

		
		sleISISProcInfoControlLspRefreshInterval OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set LSP refresh interval"
			::= { sleISISProcInfoControl 19 }

		
		sleISISProcInfoControlMaxAreaAddressNum OBJECT-TYPE
			SYNTAX INTEGER (3..254)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Configure isis maximum area address"
			::= { sleISISProcInfoControl 20 }

		
		sleISISProcInfoControlMaxLspLifetime OBJECT-TYPE
			SYNTAX INTEGER (350..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set maximum LSP lifetime"
			::= { sleISISProcInfoControl 21 }

		
		sleISISProcInfoControlMetricStyle OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				wide(1),
				wideTransition(2),
				transition(3),
				narrowTransition(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use old-style (ISO 10589) or new-style packet formats"
			::= { sleISISProcInfoControl 22 }

		
		sleISISProcInfoControlMetricStyleLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Level of Metric Style"
			::= { sleISISProcInfoControl 23 }

		
		sleISISProcInfoControlMplsTrafficEngLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Traffice Engine level of MPLS routing protocol"
			::= { sleISISProcInfoControl 24 }

		
		sleISISProcInfoControlMplsTrafficEngRouterID OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Traffice Engine Router ID of MPLS routing protocol"
			::= { sleISISProcInfoControl 25 }

		
		sleISISProcInfoControlPrcIntervalExpMinDelay OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use exponential backoff min delays between PRC  calculation"
			::= { sleISISProcInfoControl 26 }

		
		sleISISProcInfoControlPrcIntervalExpMaxDelay OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use exponential backoff max delays between PRC  calculation"
			::= { sleISISProcInfoControl 27 }

		
		sleISISProcInfoControlProtocolTopolgy OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Protocol Topology"
			::= { sleISISProcInfoControl 28 }

		
		sleISISProcInfoControlRestartTimerVal OBJECT-TYPE
			SYNTAX INTEGER (5..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Protocol Topology"
			::= { sleISISProcInfoControl 29 }

		
		sleISISProcInfoControlRestartTimerLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Restart timer level"
			::= { sleISISProcInfoControl 30 }

		
		sleISISProcInfoControlSpfIntervalExpMin OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use exponential backoff min delays between SPF calculations"
			::= { sleISISProcInfoControl 31 }

		
		sleISISProcInfoControlSpfIntervalExpMax OBJECT-TYPE
			SYNTAX INTEGER (0..**********)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use exponential backoff max delays between SPF calculations"
			::= { sleISISProcInfoControl 32 }

		
		sleISISProcInfoControlSpfIntervalExpLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Level of SPF Interval Exp"
			::= { sleISISProcInfoControl 33 }

		
		sleISISProcInfoControlAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				md5(1),
				text(2),
				md5Text(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Authentication mode of ISIS Process"
			::= { sleISISProcInfoControl 34 }

		
		sleISISProcInfoControlAuthModeLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Authentication level of ISIS Process"
			::= { sleISISProcInfoControl 35 }

		
		sleISISProcInfoControlAuthSendOnly OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Authentication send-only"
			::= { sleISISProcInfoControl 36 }

		
		sleISISProcInfoControlAuthSendOnlyLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Level of Authentication send-only"
			::= { sleISISProcInfoControl 37 }

		
		sleISISProcInfoControlDomPassVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ISIS Routing domain password"
			::= { sleISISProcInfoControl 38 }

		
		sleISISProcInfoControlDomPassAuthSnp OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				validate(1),
				sendOnly(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SNP Authentication of ISIS Routing domain "
			::= { sleISISProcInfoControl 39 }

		
		sleISISProcInfoControlAreaPassVal OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ISIS Routing Area password"
			::= { sleISISProcInfoControl 40 }

		
		sleISISProcInfoControlAreaPassAuthSnp OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				validate(1),
				sendOnly(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SNP Authentication of ISIS Routing domain "
			::= { sleISISProcInfoControl 41 }

		
		sleISISProcInfoControlSetOverloadBit OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Signal other routers not to use us in SPF"
			::= { sleISISProcInfoControl 42 }

		
		sleISISProcInfoControlSetOverloadBitOnStartup OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				overload(1),
				waitForBgp(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set overload-bit only temporarily after reboot"
			::= { sleISISProcInfoControl 43 }

		
		sleISISProcInfoControlSetOverloadBitOnStartupInterval OBJECT-TYPE
			SYNTAX INTEGER (5..86400)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Time in seconds to advertise ourself as overloaded after   reboot"
			::= { sleISISProcInfoControl 44 }

		
		sleISISProcInfoControlSetOverloadBitSuppress OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				external(1),
				interlevel(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If overload-bit set, suppress the following types of IP prefixes"
			::= { sleISISProcInfoControl 45 }

		
		sleISISProcControlWaitTimerVal OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set lsp MTU in bytes"
			::= { sleISISProcInfoControl 46 }

		
		sleISISProcControlWaitTimerLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level1To2(2),
				level2Only(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable incremental SPF for ISIS proces"
			::= { sleISISProcInfoControl 47 }

		
		sleISISProcControlAuthenkeyChain OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The authentication key-chain word."
			::= { sleISISProcInfoControl 48 }

		
		sleISISProcControlAuthenkeyChainLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				level1(1),
				level2(2),
				level1And2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The authentication key-chain level."
			::= { sleISISProcInfoControl 49 }

		
		sleISISProcInfoNotification OBJECT IDENTIFIER ::= { sleISISProcInfo 3 }

		
		sleISISProcNet OBJECT IDENTIFIER ::= { sleISISProc 2 }

		
		sleISISProcNetTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISProcNetEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNet 1 }

		
		sleISISProcNetEntry OBJECT-TYPE
			SYNTAX SleISISProcNetEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISProcNetInstanceID, sleISISProcNetTitle }
			::= { sleISISProcNetTable 1 }

		
		SleISISProcNetEntry ::=
			SEQUENCE { 
				sleISISProcNetInstanceID
					Integer32,
				sleISISProcNetTitle
					OCTET STRING
			 }

		sleISISProcNetInstanceID OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetEntry 1 }

		
		sleISISProcNetTitle OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetEntry 2 }

		
		sleISISProcNetControl OBJECT IDENTIFIER ::= { sleISISProcNet 2 }

		
		sleISISProcNetControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createISISProcNet(1),
				deleteISISProcNet(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetControl 1 }

		
		sleISISProcNetControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetControl 2 }

		
		sleISISProcNetControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetControl 3 }

		
		sleISISProcNetControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetControl 4 }

		
		sleISISProcNetControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetControl 5 }

		
		sleISISProcNetControlInstanceID OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetControl 6 }

		
		sleISISProcNetControlTitle OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcNetControl 7 }

		
		sleISISProcNetNotification OBJECT IDENTIFIER ::= { sleISISProcNet 3 }

		
		sleISISProcDistanceV4 OBJECT IDENTIFIER ::= { sleISISProc 3 }

		
		sleISISProcDistanceV4Table OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISProcDistanceV4Entry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcDistanceV4 1 }

		
		sleISISProcDistanceV4Entry OBJECT-TYPE
			SYNTAX SleISISProcDistanceV4Entry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISProcDistanceV4InstanceID, sleISISProcDistanceV4SytemID }
			::= { sleISISProcDistanceV4Table 1 }

		
		SleISISProcDistanceV4Entry ::=
			SEQUENCE { 
				sleISISProcDistanceV4InstanceID
					Integer32,
				sleISISProcDistanceV4Dist
					Integer32,
				sleISISProcDistanceV4SytemID
					OCTET STRING,
				sleISISProcDistanceV4AccessList
					OCTET STRING
			 }

		sleISISProcDistanceV4InstanceID OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS Instance ID"
			::= { sleISISProcDistanceV4Entry 1 }

		
		sleISISProcDistanceV4Dist OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS Administrative Distance range"
			::= { sleISISProcDistanceV4Entry 2 }

		
		sleISISProcDistanceV4SytemID OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..112))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Source ID in XXXX.XXXX.XXXX format."
			::= { sleISISProcDistanceV4Entry 3 }

		
		sleISISProcDistanceV4AccessList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Access-list name."
			::= { sleISISProcDistanceV4Entry 4 }

		
		sleISISProcDistanceV4Control OBJECT IDENTIFIER ::= { sleISISProcDistanceV4 2 }

		
		sleISISProcDistanceV4ControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRIPNetworkIfname(1),
				deleteRIPNetworkIfname(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcDistanceV4Control 1 }

		
		sleISISProcDistanceV4ControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcDistanceV4Control 2 }

		
		sleISISProcDistanceV4ControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcDistanceV4Control 3 }

		
		sleISISProcDistanceV4TimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcDistanceV4Control 4 }

		
		sleISISProcDistanceV4ntrolReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISProcDistanceV4Control 5 }

		
		sleISISProcDistanceV4ControlInstanceID OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Set the ISIS Instance ID "
			::= { sleISISProcDistanceV4Control 6 }

		
		sleISISProcDistanceV4ControlDist OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS Administrative Distance range"
			::= { sleISISProcDistanceV4Control 7 }

		
		sleISISProcDistanceV4ControlSytemID OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Source ID in XXXX.XXXX.XXXX format."
			::= { sleISISProcDistanceV4Control 8 }

		
		sleISISProcDistanceV4ControlAccessList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Access-list name."
			::= { sleISISProcDistanceV4Control 9 }

		
		sleISISProcDistanceV4Notification OBJECT IDENTIFIER ::= { sleISISProcDistanceV4 3 }

		
		sleISISIf OBJECT IDENTIFIER ::= { sleISIS 3 }

		
		slsISISIfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SlsISISIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISIf 1 }

		
		slsISISIfEntry OBJECT-TYPE
			SYNTAX SlsISISIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISIfIndex }
			::= { slsISISIfTable 1 }

		
		SlsISISIfEntry ::=
			SEQUENCE { 
				sleISISIfIndex
					Integer32,
				sleISISIfMplsLdpIgpSync
					INTEGER,
				sleISISIfIpRouter
					INTEGER,
				sleISISIfAuthSendLevel1
					INTEGER,
				sleISISIfAuthSendLevel2
					INTEGER,
				sleISISIfAuthModeMd5Levle1
					INTEGER,
				sleISISIfAuthModeMd5Levle2
					INTEGER,
				sleISISIfAuthModeTextLevle1
					INTEGER,
				sleISISIfAuthModeTextLevle2
					INTEGER,
				sleISISIfAuthKeyChainLevle1
					OCTET STRING,
				sleISISIfAuthKeyChainLevle2
					OCTET STRING,
				sleISISIfBfd
					INTEGER,
				sleISISIfBfdDisable
					INTEGER,
				sleISISIfCircuitType
					INTEGER,
				sleISISIfCsnpIntervalLevel1
					Integer32,
				sleISISIfCsnpIntervalLevel2
					Integer32,
				sleISISIfHelloPadding
					INTEGER,
				sleISISIfHelloIntervalLevel1
					Integer32,
				sleISISIfHelloIntervalLevel2
					Integer32,
				sleISISIfHelloIntervalMinimalLevel1
					INTEGER,
				sleISISIfHelloIntervalMinimalLevel2
					INTEGER,
				sleISISIfHelloMultiplierLevel1
					Integer32,
				sleISISIfHelloMultiplierLevel2
					Integer32,
				sleISISIfLspInterval
					Unsigned32,
				sleISISIfMeshGroup
					Unsigned32,
				sleISISIfMetricLevel1
					Integer32,
				sleISISIfMetricLevel2
					Integer32,
				sleISISIfNetwork
					INTEGER,
				sleISISIfPriorityLevel1
					Integer32,
				sleISISIfPriorityLevel2
					Integer32,
				sleISISIfRestartHelloIntervalLevel1
					Integer32,
				sleISISIfRestartHelloIntervalLevel2
					Integer32,
				sleISISIfRetransmitInterval
					Integer32,
				sleISISIfWideMetricLevel1
					Integer32,
				sleISISIfWideMetricLevel2
					Integer32
			 }

		sleISISIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"vlan interface index for isis protocol."
			::= { slsISISIfEntry 1 }

		
		sleISISIfMplsLdpIgpSync OBJECT-TYPE
			SYNTAX INTEGER
				{
				diaable(0),
				level1(1),
				level1To2(2),
				level2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to enable LDP-ISIS synchronization.
				0 : diaable
				1 : level-1
				2 : level-1-2,(default)
				3 : level-2
				"
			::= { slsISISIfEntry 2 }

		
		sleISISIfIpRouter OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to enable ISIS IPv4 routing on the interface. 
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 3 }

		
		sleISISIfAuthSendLevel1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 4 }

		
		sleISISIfAuthSendLevel2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 5 }

		
		sleISISIfAuthModeMd5Levle1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the MD5 authentication mode.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 6 }

		
		sleISISIfAuthModeMd5Levle2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the MD5 authentication mode.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 7 }

		
		sleISISIfAuthModeTextLevle1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the MD5 authentication mode.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 8 }

		
		sleISISIfAuthModeTextLevle2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the MD5 authentication mode.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 9 }

		
		sleISISIfAuthKeyChainLevle1 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the key chain to be used for authentication on the interface-related packets.
				disable : null string
				"
			::= { slsISISIfEntry 10 }

		
		sleISISIfAuthKeyChainLevle2 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the key chain to be used for authentication on the interface-related packets.
				disable : null string
				"
			::= { slsISISIfEntry 11 }

		
		sleISISIfBfd OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to enable/disable the BFD check on interface.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 12 }

		
		sleISISIfBfdDisable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to enable/disable the BFD check on interface.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 13 }

		
		sleISISIfCircuitType OBJECT-TYPE
			SYNTAX INTEGER
				{
				level1(1),
				level1To2(2),
				level2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the circuit type for the interface.
				1 : level-1
				2 : level-1-2,(default)
				3 : level-2
				"
			::= { slsISISIfEntry 14 }

		
		sleISISIfCsnpIntervalLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set CSNP (Complete sequence number PDU) interval in seconds.
				range : 1 - 65535
				default : 10
				"
			::= { slsISISIfEntry 15 }

		
		sleISISIfCsnpIntervalLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set CSNP (Complete sequence number PDU) interval in seconds.
				range : 1 - 65535
				default : 10
				"
			::= { slsISISIfEntry 16 }

		
		sleISISIfHelloPadding OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to configure the padding of the ISIS Hello packet.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 17 }

		
		sleISISIfHelloIntervalLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				range : 1 - 65535
				default : 10
				"
			::= { slsISISIfEntry 18 }

		
		sleISISIfHelloIntervalLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				range : 1 - 65535
				default : 10
				"
			::= { slsISISIfEntry 19 }

		
		sleISISIfHelloIntervalMinimalLevel1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 20 }

		
		sleISISIfHelloIntervalMinimalLevel2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				1 : enable
				0 : disable
				"
			::= { slsISISIfEntry 21 }

		
		sleISISIfHelloMultiplierLevel1 OBJECT-TYPE
			SYNTAX Integer32 (2..10)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set multiplier for Hello holding time.
				range : 2 - 10
				default : 3
				"
			::= { slsISISIfEntry 22 }

		
		sleISISIfHelloMultiplierLevel2 OBJECT-TYPE
			SYNTAX Integer32 (2..10)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set multiplier for Hello holding time.
				range : 2 - 10
				default : 3
				"
			::= { slsISISIfEntry 23 }

		
		sleISISIfLspInterval OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the Link State Packet (LSP) transmission interval.
				range : 1 - 4294967295
				default : 33
				"
			::= { slsISISIfEntry 24 }

		
		sleISISIfMeshGroup OBJECT-TYPE
			SYNTAX Unsigned32 (1..4294967295)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set Mesh Group ID on the current interface.
				range : 1 - 4294967295
				disable : 0
				"
			::= { slsISISIfEntry 25 }

		
		sleISISIfMetricLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set default metric for the interface. 
				range : 1 - 63
				default : 10
				"
			::= { slsISISIfEntry 26 }

		
		sleISISIfMetricLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set default metric for the interface. 
				range : 1 - 63
				default : 10
				"
			::= { slsISISIfEntry 27 }

		
		sleISISIfNetwork OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				broadcast(1),
				pointToPoint(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to change a broadcast interface network type to a point-to-point network type.
				0 : disable
				1 : broadcast
				2 : point-to-point
				"
			::= { slsISISIfEntry 28 }

		
		sleISISIfPriorityLevel1 OBJECT-TYPE
			SYNTAX Integer32 (0..127)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the priority for LAN DIS election. 
				range : 0 - 127
				default : 64
				"
			::= { slsISISIfEntry 29 }

		
		sleISISIfPriorityLevel2 OBJECT-TYPE
			SYNTAX Integer32 (0..127)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set the priority for LAN DIS election. 
				range : 0 - 127
				default : 64
				"
			::= { slsISISIfEntry 30 }

		
		sleISISIfRestartHelloIntervalLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to configure the T1 timer, interval of ISIS Hello packet with restart TLV.
				range : 1 - 65535
				default : 3
				"
			::= { slsISISIfEntry 31 }

		
		sleISISIfRestartHelloIntervalLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to configure the T1 timer, interval of ISIS Hello packet with restart TLV.
				range : 1 - 65535
				default : 3
				"
			::= { slsISISIfEntry 32 }

		
		sleISISIfRetransmitInterval OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set LSP retransmission interval.
				range : 0 - 65535
				default : 5
				"
			::= { slsISISIfEntry 33 }

		
		sleISISIfWideMetricLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..16777214)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Use this command to set wide metric for the interface.
				range : 1 - 16777214
				default : 10
				"
			::= { slsISISIfEntry 34 }

		
		sleISISIfWideMetricLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..16777214)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set wide metric for the interface.
				range : 1 - 16777214
				default : 10
				"
			::= { slsISISIfEntry 35 }

		
		sleISISIfControl OBJECT IDENTIFIER ::= { sleISISIf 2 }

		
		sleISISIfControlRequest OBJECT-TYPE
			SYNTAX SleControlRequestResultType { setISISIfProfile(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISIfControl 1 }

		
		sleISISIfControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISIfControl 2 }

		
		sleISISIfControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISIfControl 3 }

		
		sleISISIfControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISIfControl 4 }

		
		sleISISIfControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISIfControl 5 }

		
		sleISISIfControlIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"vlan interface index for isis protocol."
			::= { sleISISIfControl 6 }

		
		sleISISIfControlMplsLdpIgpSync OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				level1(1),
				level1To2(2),
				level2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to enable LDP-ISIS synchronization.
				0 : diaable
				1 : level-1
				2 : level-1-2,(default)
				3 : level-2
				"
			::= { sleISISIfControl 7 }

		
		sleISISIfControlIpRouter OBJECT-TYPE
			SYNTAX INTEGER
				{
				dissable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to enable ISIS IPv4 routing on the interface. 
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 8 }

		
		sleISISIfControlAuthSendLevel1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 9 }

		
		sleISISIfControlAuthSendLevel2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 10 }

		
		sleISISIfControlAuthModeMd5Levle1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 11 }

		
		sleISISIfControlAuthModeMd5Levle2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 12 }

		
		sleISISIfControlAuthModeTextLevle1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 13 }

		
		sleISISIfControlAuthModeTextLevle2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the send-only option to the interface-related packets.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 14 }

		
		sleISISIfControlAuthKeyChainLevle1 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the key chain to be used for authentication on the interface-related packets.
				disable : null string
				"
			::= { sleISISIfControl 15 }

		
		sleISISIfControlAuthKeyChainLevle2 OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the key chain to be used for authentication on the interface-related packets.
				disable : null string
				"
			::= { sleISISIfControl 16 }

		
		sleISISIfControlBfd OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to enable/disable the BFD check on interface.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 17 }

		
		sleISISIfControlBfdDisable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to enable/disable the BFD check on interface.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 18 }

		
		sleISISIfControlCircuitType OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				level1(1),
				level1To2(2),
				level2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the circuit type for the interface.
				1 : level-1
				2 : level-1-2,(default)
				3 : level-2
				"
			::= { sleISISIfControl 19 }

		
		sleISISIfControlCsnpIntervalLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set CSNP (Complete sequence number PDU) interval in seconds.
				range : 1 ? 65535
				default : 10
				"
			::= { sleISISIfControl 20 }

		
		sleISISIfControlCsnpIntervalLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set CSNP (Complete sequence number PDU) interval in seconds.
				range : 1 ? 65535
				default : 10
				"
			::= { sleISISIfControl 21 }

		
		sleISISIfControlHelloPadding OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to configure the padding of the ISIS Hello packet.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 22 }

		
		sleISISIfControlHelloIntervalLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				range : 1 ? 65535
				default : 10
				"
			::= { sleISISIfControl 23 }

		
		sleISISIfControlHelloIntervalLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				range : 1 ? 65535
				default : 10
				"
			::= { sleISISIfControl 24 }

		
		sleISISIfControlHelloIntervalMinimalLevel1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 25 }

		
		sleISISIfControlHelloIntervalMinimalLevel2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the Hello interval in seconds.
				1 : enable
				0 : disable
				"
			::= { sleISISIfControl 26 }

		
		sleISISIfControlHelloMultiplierLevel1 OBJECT-TYPE
			SYNTAX Integer32 (2..10)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set multiplier for Hello holding time.
				range : 2 ? 10
				default : 3
				"
			::= { sleISISIfControl 27 }

		
		sleISISIfControlHelloMultiplierLevel2 OBJECT-TYPE
			SYNTAX Integer32 (2..10)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set multiplier for Hello holding time.
				range : 2 ? 10
				default : 3
				"
			::= { sleISISIfControl 28 }

		
		sleISISIfControlLspInterval OBJECT-TYPE
			SYNTAX Unsigned32 (2..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the Link State Packet (LSP) transmission interval.
				range : 1 ? 4294967295
				default : 33
				"
			::= { sleISISIfControl 29 }

		
		sleISISIfControlMeshGroup OBJECT-TYPE
			SYNTAX Unsigned32 (0 | 1..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set Mesh Group ID on the current interface.
				range : 1 ? 4294967295
				disable : 0
				"
			::= { sleISISIfControl 30 }

		
		sleISISIfControlMetricLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set default metric for the interface. 
				range : 1 ? 63
				default : 10
				"
			::= { sleISISIfControl 31 }

		
		sleISISIfControlMetricLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set default metric for the interface. 
				range : 1 ? 63
				default : 10
				"
			::= { sleISISIfControl 32 }

		
		sleISISIfControlNetwork OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				broadcast(1),
				pointToPoint(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to change a broadcast interface network type to a point-to-point network type.
				0 : disable
				1 : broadcast
				2 : point-to-point
				"
			::= { sleISISIfControl 33 }

		
		sleISISIfControlPriorityLevel1 OBJECT-TYPE
			SYNTAX Integer32 (0..127)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the priority for LAN DIS election. 
				range : 0 ? 127
				default : 64
				"
			::= { sleISISIfControl 34 }

		
		sleISISIfControlPriorityLevel2 OBJECT-TYPE
			SYNTAX Integer32 (0..127)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set the priority for LAN DIS election. 
				range : 0 ? 127
				default : 64
				"
			::= { sleISISIfControl 35 }

		
		sleISISIfControlRestartHelloIntervalLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to configure the T1 timer, interval of ISIS Hello packet with restart TLV.
				range : 1 ? 65535
				default : 3
				"
			::= { sleISISIfControl 36 }

		
		sleISISIfControlRestartHelloIntervalLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to configure the T1 timer, interval of ISIS Hello packet with restart TLV.
				range : 1 ? 65535
				default : 3
				"
			::= { sleISISIfControl 37 }

		
		sleISISIfControlRetransmitInterval OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set LSP retransmission interval.
				range : 0 ? 65535
				default : 5
				"
			::= { sleISISIfControl 38 }

		
		sleISISIfControlWideMetricLevel1 OBJECT-TYPE
			SYNTAX Integer32 (1..16777214)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set wide metric for the interface.
				range : 1 - 16777214
				default : 10
				"
			::= { sleISISIfControl 39 }

		
		sleISISIfControlWideMetricLevel2 OBJECT-TYPE
			SYNTAX Integer32 (1..16777214)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Use this command to set wide metric for the interface.
				range : 1 - 16777214
				default : 10
				"
			::= { sleISISIfControl 40 }

		
		slsISISNotification OBJECT IDENTIFIER ::= { sleISISIf 3 }

		
		sleISISIfStatusTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISIfStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISIf 4 }

		
		sleISISIfStatusEntry OBJECT-TYPE
			SYNTAX SleISISIfStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISIfStatusIfIndex }
			::= { sleISISIfStatusTable 1 }

		
		SleISISIfStatusEntry ::=
			SEQUENCE { 
				sleISISIfStatusIfIndex
					Integer32,
				sleIsisIfStatusIfStatus
					INTEGER,
				sleIsisIfStatusIsisTag
					OCTET STRING,
				sleIsisIfStatusNetworkType
					INTEGER,
				sleIsisIfStatusCircuitType
					INTEGER,
				sleIsisIfStatusLocalCircuitId
					Integer32,
				sleIsisIfStatusExtendedLocalCircuitId
					Integer32,
				sleIsisIfStatusLocalSnpa
					OCTET STRING,
				sleIsisIfStatusLdpSyncHoldTimer
					Integer32,
				sleIsisIfStatusLdpSyncRemainingTime
					Integer32,
				sleIsisIfStatusCircuitL1Metric
					Integer32,
				sleIsisIfStatusCircuitL1WideMetric
					Integer32,
				sleIsisIfStatusCircuitL1Priority
					Integer32,
				sleIsisIfStatusCircuitL1CircuitId
					Integer32,
				sleIsisIfStatusCircuitL1ActiveAdjacencies
					Integer32,
				sleIsisIfStatusCircuitL1LscpMtu
					Integer32,
				sleIsisIfStatusCircuitL2Metric
					Integer32,
				sleIsisIfStatusCircuitL2WideMetric
					Integer32,
				sleIsisIfStatusCircuitL2Priority
					Integer32,
				sleIsisIfStatusCircuitL2CircuitId
					Integer32,
				sleIsisIfStatusCircuitL2ActiveAdjacencies
					Integer32,
				sleIsisIfStatusCircuitL2LscpMtu
					Integer32,
				sleIsisIfStatusL1HelloTimerBroadcast
					OCTET STRING,
				sleIsisIfStatusL2HelloTimerBroadcast
					OCTET STRING,
				sleIsisIfStatusL1HellotimerPoint2Point
					OCTET STRING,
				sleIsisIfStatusL2HellotimerPoint2Point
					OCTET STRING
			 }

		sleISISIfStatusIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"vlan interface index for isis protocol."
			::= { sleISISIfStatusEntry 1 }

		
		sleIsisIfStatusIfStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				down(0),
				up(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interface up/down status"
			::= { sleISISIfStatusEntry 2 }

		
		sleIsisIfStatusIsisTag OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS tag"
			::= { sleISISIfStatusEntry 3 }

		
		sleIsisIfStatusNetworkType OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(0),
				broadcast(1),
				point2Point(2),
				loopback(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The network type"
			::= { sleISISIfStatusEntry 4 }

		
		sleIsisIfStatusCircuitType OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(0),
				level1(1),
				level2(2),
				level1And2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The circuit type"
			::= { sleISISIfStatusEntry 5 }

		
		sleIsisIfStatusLocalCircuitId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The local circuit id"
			::= { sleISISIfStatusEntry 6 }

		
		sleIsisIfStatusExtendedLocalCircuitId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The extended local circuit id"
			::= { sleISISIfStatusEntry 7 }

		
		sleIsisIfStatusLocalSnpa OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The local SNPA mac address"
			::= { sleISISIfStatusEntry 8 }

		
		sleIsisIfStatusLdpSyncHoldTimer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The LDP sync hold timer(sec)"
			::= { sleISISIfStatusEntry 9 }

		
		sleIsisIfStatusLdpSyncRemainingTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The LDP sync remaining time(sec)"
			::= { sleISISIfStatusEntry 10 }

		
		sleIsisIfStatusCircuitL1Metric OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The narrow metric of level1"
			::= { sleISISIfStatusEntry 11 }

		
		sleIsisIfStatusCircuitL1WideMetric OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The wide metric of level1"
			::= { sleISISIfStatusEntry 12 }

		
		sleIsisIfStatusCircuitL1Priority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The priority of level1"
			::= { sleISISIfStatusEntry 13 }

		
		sleIsisIfStatusCircuitL1CircuitId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The circuit id of level1"
			::= { sleISISIfStatusEntry 14 }

		
		sleIsisIfStatusCircuitL1ActiveAdjacencies OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The active adjacencies of level1"
			::= { sleISISIfStatusEntry 15 }

		
		sleIsisIfStatusCircuitL1LscpMtu OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The MTU of level1"
			::= { sleISISIfStatusEntry 16 }

		
		sleIsisIfStatusCircuitL2Metric OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The narrow metric of level2"
			::= { sleISISIfStatusEntry 17 }

		
		sleIsisIfStatusCircuitL2WideMetric OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The wide metric of level2"
			::= { sleISISIfStatusEntry 18 }

		
		sleIsisIfStatusCircuitL2Priority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The priority of level2"
			::= { sleISISIfStatusEntry 19 }

		
		sleIsisIfStatusCircuitL2CircuitId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The circuit id of level2"
			::= { sleISISIfStatusEntry 20 }

		
		sleIsisIfStatusCircuitL2ActiveAdjacencies OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The active adjacencies of level2"
			::= { sleISISIfStatusEntry 21 }

		
		sleIsisIfStatusCircuitL2LscpMtu OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The MTU of level2"
			::= { sleISISIfStatusEntry 22 }

		
		sleIsisIfStatusL1HelloTimerBroadcast OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Level1 hello timer of broadcast"
			::= { sleISISIfStatusEntry 23 }

		
		sleIsisIfStatusL2HelloTimerBroadcast OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Level2 hello timer of broadcast"
			::= { sleISISIfStatusEntry 24 }

		
		sleIsisIfStatusL1HellotimerPoint2Point OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Level1 hello timer of porint to point"
			::= { sleISISIfStatusEntry 25 }

		
		sleIsisIfStatusL2HellotimerPoint2Point OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Level2 hello timer of porint to point"
			::= { sleISISIfStatusEntry 26 }

		
		sleISISInstIf OBJECT IDENTIFIER ::= { sleISIS 4 }

		
		sleISISInstIfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISInstIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstIf 1 }

		
		sleISISInstIfEntry OBJECT-TYPE
			SYNTAX SleISISInstIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISInstIfInstanceId, sleISISInstIfInterfaceId }
			::= { sleISISInstIfTable 1 }

		
		SleISISInstIfEntry ::=
			SEQUENCE { 
				sleISISInstIfInstanceId
					Integer32,
				sleISISInstIfInterfaceId
					Integer32,
				sleISISPassiveInterface
					INTEGER
			 }

		sleISISInstIfInstanceId OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstIfEntry 1 }

		
		sleISISInstIfInterfaceId OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS interface index"
			::= { sleISISInstIfEntry 2 }

		
		sleISISPassiveInterface OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISInstIfEntry 3 }

		
		sleISISInstIfControl OBJECT IDENTIFIER ::= { sleISISInstIf 2 }

		
		sleISISInstIfControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setISISInstIfPassiveIf(1),
				delISISInstIfPassiveIf(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleISISInstIfControl 1 }

		
		sleISISInstIfControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstIfControl 2 }

		
		sleISISInstIfControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstIfControl 3 }

		
		sleISISInstIfControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstIfControl 4 }

		
		sleISISInstIfControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstIfControl 5 }

		
		sleISISInstIfControlInstanceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstIfControl 6 }

		
		sleISISInstIfControlInterfaceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstIfControl 7 }

		
		sleISISInstRedistribute OBJECT IDENTIFIER ::= { sleISIS 5 }

		
		sleISISInstRedistProtocol OBJECT IDENTIFIER ::= { sleISISInstRedistribute 1 }

		
		sleISISInstRedistProtocolTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISInstRedistProtocolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistProtocol 1 }

		
		sleISISInstRedistProtocolEntry OBJECT-TYPE
			SYNTAX SleISISInstRedistProtocolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISInstRedistProtocolInstanceId, sleISISInstRedistProtocolId }
			::= { sleISISInstRedistProtocolTable 1 }

		
		SleISISInstRedistProtocolEntry ::=
			SEQUENCE { 
				sleISISInstRedistProtocolInstanceId
					Integer32,
				sleISISInstRedistProtocolId
					INTEGER,
				sleISISInstRedistProtocolMetric
					Gauge32,
				sleISISInstRedistProtocolMetricType
					INTEGER,
				sleISISInstRedistProtocolRouteLevelType
					INTEGER,
				sleISISInstRedistProtocolRouteMapName
					OCTET STRING
			 }

		sleISISInstRedistProtocolInstanceId OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstRedistProtocolEntry 1 }

		
		sleISISInstRedistProtocolId OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				rip(4),
				ospf(5),
				bgp(6)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstRedistProtocolEntry 2 }

		
		sleISISInstRedistProtocolMetric OBJECT-TYPE
			SYNTAX Gauge32 (0..4261412864)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS interface index"
			::= { sleISISInstRedistProtocolEntry 3 }

		
		sleISISInstRedistProtocolMetricType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(-1),
				internal(0),
				external(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISInstRedistProtocolEntry 4 }

		
		sleISISInstRedistProtocolRouteLevelType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2),
				level1To2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISInstRedistProtocolEntry 5 }

		
		sleISISInstRedistProtocolRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISInstRedistProtocolEntry 6 }

		
		sleISISInstRedistProtocolControl OBJECT IDENTIFIER ::= { sleISISInstRedistProtocol 2 }

		
		sleISISInstRedistProtocolControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setISISInstIfRedistProtocol(1),
				delISISInstIfRedistProtocol(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleISISInstRedistProtocolControl 1 }

		
		sleISISInstRedistProtocolControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistProtocolControl 2 }

		
		sleISISInstRedistProtocolControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistProtocolControl 3 }

		
		sleISISInstRedistProtocolControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistProtocolControl 4 }

		
		sleISISInstRedistProtocolControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistProtocolControl 5 }

		
		sleISISInstRedistProtocolControlInstanceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstRedistProtocolControl 6 }

		
		sleISISInstRedistProtocolControlId OBJECT-TYPE
			SYNTAX INTEGER
				{
				kernel(1),
				connected(2),
				static(3),
				rip(4),
				ospf(5),
				bgp(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstRedistProtocolControl 7 }

		
		sleISISInstRedistProtocolControlMetric OBJECT-TYPE
			SYNTAX Gauge32 (0..4261412864)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstRedistProtocolControl 8 }

		
		sleISISInstRedistProtocolControlMetricType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(-1),
				internal(0),
				external(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstRedistProtocolControl 9 }

		
		sleISISInstRedistProtocolControlRouteLevelType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2),
				level1To2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstRedistProtocolControl 10 }

		
		sleISISInstRedistProtocolControlRouteMapName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstRedistProtocolControl 11 }

		
		sleISISInstRedistIsisProtocol OBJECT IDENTIFIER ::= { sleISISInstRedistribute 2 }

		
		sleISISInstRedistIsisProtocolTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISInstRedistIsisProtocolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistIsisProtocol 1 }

		
		sleISISInstRedistIsisProtocolEntry OBJECT-TYPE
			SYNTAX SleISISInstRedistIsisProtocolEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISInstRedistIsisProtocolInstanceId }
			::= { sleISISInstRedistIsisProtocolTable 1 }

		
		SleISISInstRedistIsisProtocolEntry ::=
			SEQUENCE { 
				sleISISInstRedistIsisProtocolInstanceId
					Integer32,
				sleISISInstRedistIsisProtocolInterAreaFromLv1ToLv2
					INTEGER,
				sleISISInstRedistIsisProtocolLv1Lv2DistributeList
					OCTET STRING,
				sleISISInstRedistIsisProtocolInterAreaFromLv2ToLv1
					INTEGER,
				sleISISInstRedistIsisProtocolLv2Lv1DistributeList
					OCTET STRING
			 }

		sleISISInstRedistIsisProtocolInstanceId OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstRedistIsisProtocolEntry 1 }

		
		sleISISInstRedistIsisProtocolInterAreaFromLv1ToLv2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstRedistIsisProtocolEntry 2 }

		
		sleISISInstRedistIsisProtocolLv1Lv2DistributeList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS interface index"
			::= { sleISISInstRedistIsisProtocolEntry 3 }

		
		sleISISInstRedistIsisProtocolInterAreaFromLv2ToLv1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISInstRedistIsisProtocolEntry 4 }

		
		sleISISInstRedistIsisProtocolLv2Lv1DistributeList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISInstRedistIsisProtocolEntry 5 }

		
		sleISISInstRedistIsisProtocolControl OBJECT IDENTIFIER ::= { sleISISInstRedistIsisProtocol 2 }

		
		sleISISInstRedistIsisProtocolControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setISISInstRedistIsisLv1Lv2(1),
				delISISInstRedistIsisLv1Lv2(2),
				setISISInstRedistIsisLv2Lv1(3),
				delISISInstRedistIsisLv2Lv1(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleISISInstRedistIsisProtocolControl 1 }

		
		sleISISInstRedistIsisProtocolControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistIsisProtocolControl 2 }

		
		sleISISInstRedistIsisProtocolControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistIsisProtocolControl 3 }

		
		sleISISInstRedistIsisProtocolControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistIsisProtocolControl 4 }

		
		sleISISInstRedistIsisProtocolControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISInstRedistIsisProtocolControl 5 }

		
		sleISISInstRedistIsisProtocolControlInstanceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISInstRedistIsisProtocolControl 6 }

		
		sleISISInstRedistIsisProtocolControlLv1Lv2DistributeList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstRedistIsisProtocolControl 7 }

		
		sleISISInstRedistIsisProtocolControlLv2Lv1DistributeList OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISInstRedistIsisProtocolControl 8 }

		
		sleISISSummanryAddress OBJECT IDENTIFIER ::= { sleISIS 6 }

		
		sleISISSummanryAddressTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISSummanryAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISSummanryAddress 1 }

		
		sleISISSummanryAddressEntry OBJECT-TYPE
			SYNTAX SleISISSummanryAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISSummanryAddressInstanceId, sleISISSummanryAddressIpValue, sleISISSummanryAddressIpNetmask }
			::= { sleISISSummanryAddressTable 1 }

		
		SleISISSummanryAddressEntry ::=
			SEQUENCE { 
				sleISISSummanryAddressInstanceId
					Integer32,
				sleISISSummanryAddressIpValue
					IpAddress,
				sleISISSummanryAddressIpNetmask
					Integer32,
				sleISISSummanryAddressLevel
					INTEGER,
				sleISISSummanryAddressMetric
					Integer32
			 }

		sleISISSummanryAddressInstanceId OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISSummanryAddressEntry 1 }

		
		sleISISSummanryAddressIpValue OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The ISIS interface index"
			::= { sleISISSummanryAddressEntry 2 }

		
		sleISISSummanryAddressIpNetmask OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISSummanryAddressEntry 3 }

		
		sleISISSummanryAddressLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2),
				level1To2(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISSummanryAddressEntry 4 }

		
		sleISISSummanryAddressMetric OBJECT-TYPE
			SYNTAX Integer32 (1..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The passive interface configuration."
			::= { sleISISSummanryAddressEntry 5 }

		
		sleISISSummanryAddressControl OBJECT IDENTIFIER ::= { sleISISSummanryAddress 2 }

		
		sleISISSummanryAddressControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setISISSummaryAddress(1),
				delISISSummaryAddress(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command"
			::= { sleISISSummanryAddressControl 1 }

		
		sleISISSummanryAddressControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISSummanryAddressControl 2 }

		
		sleISISSummanryAddressControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISSummanryAddressControl 3 }

		
		sleISISSummanryAddressControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISSummanryAddressControl 4 }

		
		sleISISSummanryAddressControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISSummanryAddressControl 5 }

		
		sleISISSummanryAddressControlInstanceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS instance index"
			::= { sleISISSummanryAddressControl 6 }

		
		sleISISSummanryAddressControlIpValue OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISSummanryAddressControl 7 }

		
		sleISISSummanryAddressControlIpNetmask OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISSummanryAddressControl 8 }

		
		sleISISSummanryAddressControlLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				level1(1),
				level2(2),
				level1To2(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISSummanryAddressControl 9 }

		
		sleISISSummanryAddressControlMetric OBJECT-TYPE
			SYNTAX Integer32 (1..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ISIS interface index ( 0 means all interface)"
			::= { sleISISSummanryAddressControl 10 }

		
		sleISISDatabase OBJECT IDENTIFIER ::= { sleISIS 7 }

		
		sleISISDatabaseTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISDatabaseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISDatabase 1 }

		
		sleISISDatabaseEntry OBJECT-TYPE
			SYNTAX SleISISDatabaseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISDatabaseTag, sleISISDatabaseLevel, sleISISDatabaseLspId }
			::= { sleISISDatabaseTable 1 }

		
		SleISISDatabaseEntry ::=
			SEQUENCE { 
				sleISISDatabaseTag
					OCTET STRING,
				sleISISDatabaseLevel
					Integer32,
				sleISISDatabaseLspId
					OCTET STRING,
				sleISISDatabaseLspSeqNum
					OCTET STRING,
				sleISISDatabaseLspChecksum
					OCTET STRING,
				sleISISDatabaseLspHoldtime
					OCTET STRING,
				sleISISDatabaseAtt
					Integer32,
				sleISISDatabaseP
					Integer32,
				sleISISDatabaseOl
					Integer32
			 }

		sleISISDatabaseTag OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database tag ID"
			::= { sleISISDatabaseEntry 1 }

		
		sleISISDatabaseLevel OBJECT-TYPE
			SYNTAX Integer32 (1..2)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database level index"
			::= { sleISISDatabaseEntry 2 }

		
		sleISISDatabaseLspId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp ID"
			::= { sleISISDatabaseEntry 3 }

		
		sleISISDatabaseLspSeqNum OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp sequence number"
			::= { sleISISDatabaseEntry 4 }

		
		sleISISDatabaseLspChecksum OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp Checksum"
			::= { sleISISDatabaseEntry 5 }

		
		sleISISDatabaseLspHoldtime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp Holdtime"
			::= { sleISISDatabaseEntry 6 }

		
		sleISISDatabaseAtt OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database att"
			::= { sleISISDatabaseEntry 7 }

		
		sleISISDatabaseP OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database partition"
			::= { sleISISDatabaseEntry 8 }

		
		sleISISDatabaseOl OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database overload"
			::= { sleISISDatabaseEntry 9 }

		
		sleISISDatabaseDetail OBJECT IDENTIFIER ::= { sleISIS 8 }

		
		sleISISDatabaseDetailTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleISISDatabaseDetailEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleISISDatabaseDetail 1 }

		
		sleISISDatabaseDetailEntry OBJECT-TYPE
			SYNTAX SleISISDatabaseDetailEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleISISDatabaseDetailTag, sleISISDatabaseDetailLevel, sleISISDatabaseDetailLspId, sleISISDatabaseDetailType, sleISISDatabaseDetailIndex
				 }
			::= { sleISISDatabaseDetailTable 1 }

		
		SleISISDatabaseDetailEntry ::=
			SEQUENCE { 
				sleISISDatabaseDetailTag
					OCTET STRING,
				sleISISDatabaseDetailLevel
					INTEGER,
				sleISISDatabaseDetailLspId
					OCTET STRING,
				sleISISDatabaseDetailType
					INTEGER,
				sleISISDatabaseDetailIndex
					Integer32,
				sleISISDatabaseDetailPad
					OCTET STRING,
				sleISISDatabaseDetailValue
					Integer32,
				sleISISDatabaseDetailDescription
					OCTET STRING
			 }

		sleISISDatabaseDetailTag OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database tag"
			::= { sleISISDatabaseDetailEntry 1 }

		
		sleISISDatabaseDetailLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				null(0),
				level1(1),
				level2(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database level"
			::= { sleISISDatabaseDetailEntry 2 }

		
		sleISISDatabaseDetailLspId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp id"
			::= { sleISISDatabaseDetailEntry 3 }

		
		sleISISDatabaseDetailType OBJECT-TYPE
			SYNTAX INTEGER
				{
				areaAddr(1),
				lspIsNeighbor(2),
				lspEsNeighbor(3),
				authInfo(10),
				extendedIsReachability(22),
				ipInternalReachability(128),
				protocolsSupportded(129),
				ipExternalReachability(130),
				ipIfAddr(132),
				ipAuthInfo(133),
				teRouteId(134),
				extendedIpReachability(135),
				hostname(137),
				sharedRiskLinkGroup(138),
				multiIsReachability(222),
				multiTopology(229),
				ipv6IfAddr(232),
				multiIpv4Reachability(235),
				ipv6Reachability(236),
				multiIpv6Reachability(237)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp detail type."
			::= { sleISISDatabaseDetailEntry 4 }

		
		sleISISDatabaseDetailIndex OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database detail index."
			::= { sleISISDatabaseDetailEntry 5 }

		
		sleISISDatabaseDetailPad OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp detail pad."
			::= { sleISISDatabaseDetailEntry 6 }

		
		sleISISDatabaseDetailValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database lsp detail vlaue."
			::= { sleISISDatabaseDetailEntry 7 }

		
		sleISISDatabaseDetailDescription OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ISIS database detail description."
			::= { sleISISDatabaseDetailEntry 8 }

		
		sleISISGroup OBJECT-GROUP
			OBJECTS { sleISISRestartSuppressAdjacency, sleISISRestartHelper, sleISISRestartGracePeriod, sleISISControlRequest, sleISISControlStatus, 
				sleISISControlTimer, sleISISControlTimeStamp, sleISISControlReqResult, sleISISControlRestartSuppressAdjacency, sleISISControlRestartHelper, 
				sleISISControlRestartPeriod, sleISISControlClearSystemId, sleISISControlClearIfName, sleISISControlClearRouteTag, sleISISControlClearRouteMode, 
				sleISISProcInfoInstanceID, sleISISProcInfoTag, sleISISProcBfdAllInterface, sleISISProcCapCspf, sleISISProcDynHostname, 
				sleISISProcDynHostnameAreaTag, sleISISProcIgnLspErr, sleISISProcRouteHighPriorityTag, sleISISProcIspfLevel, sleISISProcIsTypeLevel, 
				sleISISProcLspGenInterval, sleISISProcLspGenIntervalLevel, sleISISProcLspMtu, sleISISProcLspMtuLevel, sleISISProcLspRefreshInterval, 
				sleISISProcMaxAreaAddressNum, sleISISProcMaxLspLifetime, sleISISProcMetricStyle, sleISISProcMetricStyleLevel, sleISISProcMplsTrafficEngLevel, 
				sleISISProcMplsTrafficEngRouterID, sleISISProcPrcIntervalExpMinDelay, sleISISProcPrcIntervalExpMaxDelay, sleISISProcProtocolTopolgy, sleISISProcRestartTimerVal, 
				sleISISProcRestartTimerLevel, sleISISProcSpfIntervalExpMin, sleISISProcSpfIntervalExpMax, sleISISProcSpfIntervalExpLevel, sleISISProcAuthMode, 
				sleISISProcAuthModeLevel, sleISISProcAuthSendOnly, sleISISProcAuthSendOnlyLevel, sleISISProcDomPassVal, sleISISProcDomPassAuthSnp, 
				sleISISProcAreaPassVal, sleISISProcAreaPassAuthSnp, sleISISProcSetOverloadBit, sleISISProcSetOverloadBitOnStartup, sleISISProcSetOverloadBitOnStartupInterval, 
				sleISISProcSetOverloadBitSuppress, sleISISProcWaitTimerVal, sleISISProcWaitTimerLevel, sleISISProcInfoControlRequest, sleISISProcInfoControlStatus, 
				sleISISProcInfoControlTimer, sleISISProcInfoControlTimeStamp, sleISISProcInfoControlReqResult, sleISISProcInfoControlTag, sleISISProcInfoControlBfdAllInterface, 
				sleISISProcInfoControlCapCspf, sleISISProcInfoControlDynHostname, sleISISProcInfoControlDynHostnameAreaTag, sleISISProcInfoControlIgnLspErr, sleISISProcInfoControlRouteHighPriorityTag, 
				sleISISProcInfoControlIspfLevel, sleISISProcInfoControlIsTypeLevel, sleISISProcInfoControlLspGenInterval, sleISISProcInfoControlLspGenIntervalLevel, sleISISProcInfoControlLspMtu, 
				sleISISProcInfoControlLspMtuLevel, sleISISProcInfoControlLspRefreshInterval, sleISISProcInfoControlMaxAreaAddressNum, sleISISProcInfoControlMaxLspLifetime, sleISISProcInfoControlMetricStyle, 
				sleISISProcInfoControlMetricStyleLevel, sleISISProcInfoControlMplsTrafficEngLevel, sleISISProcInfoControlMplsTrafficEngRouterID, sleISISProcInfoControlPrcIntervalExpMinDelay, sleISISProcInfoControlPrcIntervalExpMaxDelay, 
				sleISISProcInfoControlProtocolTopolgy, sleISISProcInfoControlRestartTimerVal, sleISISProcInfoControlRestartTimerLevel, sleISISProcInfoControlSpfIntervalExpMin, sleISISProcInfoControlSpfIntervalExpMax, 
				sleISISProcInfoControlSpfIntervalExpLevel, sleISISProcInfoControlAuthMode, sleISISProcInfoControlAuthModeLevel, sleISISProcInfoControlAuthSendOnly, sleISISProcInfoControlAuthSendOnlyLevel, 
				sleISISProcInfoControlDomPassVal, sleISISProcInfoControlDomPassAuthSnp, sleISISProcInfoControlAreaPassVal, sleISISProcInfoControlAreaPassAuthSnp, sleISISProcInfoControlSetOverloadBit, 
				sleISISProcInfoControlSetOverloadBitOnStartup, sleISISProcInfoControlSetOverloadBitOnStartupInterval, sleISISProcInfoControlSetOverloadBitSuppress, sleISISProcControlWaitTimerVal, sleISISProcControlWaitTimerLevel, 
				sleISISProcNetInstanceID, sleISISProcNetTitle, sleISISProcNetControlRequest, sleISISProcNetControlStatus, sleISISProcNetControlTimer, 
				sleISISProcNetControlTimeStamp, sleISISProcNetControlReqResult, sleISISProcNetControlInstanceID, sleISISProcNetControlTitle, sleISISProcDistanceV4InstanceID, 
				sleISISProcDistanceV4Dist, sleISISProcDistanceV4SytemID, sleISISProcDistanceV4AccessList, sleISISProcDistanceV4ControlRequest, sleISISProcDistanceV4ControlStatus, 
				sleISISProcDistanceV4ControlTimer, sleISISProcDistanceV4TimeStamp, sleISISProcDistanceV4ntrolReqResult, sleISISProcDistanceV4ControlInstanceID, sleISISProcDistanceV4ControlDist, 
				sleISISProcDistanceV4ControlSytemID, sleISISProcDistanceV4ControlAccessList, sleISISIfIndex, sleISISIfMplsLdpIgpSync, sleISISIfIpRouter, 
				sleISISIfAuthSendLevel1, sleISISIfAuthSendLevel2, sleISISIfAuthModeMd5Levle1, sleISISIfAuthModeMd5Levle2, sleISISIfAuthModeTextLevle1, 
				sleISISIfAuthModeTextLevle2, sleISISIfAuthKeyChainLevle1, sleISISIfAuthKeyChainLevle2, sleISISIfBfd, sleISISIfBfdDisable, 
				sleISISIfCircuitType, sleISISIfCsnpIntervalLevel1, sleISISIfCsnpIntervalLevel2, sleISISIfHelloPadding, sleISISIfHelloIntervalLevel1, 
				sleISISIfHelloIntervalLevel2, sleISISIfHelloIntervalMinimalLevel1, sleISISIfHelloIntervalMinimalLevel2, sleISISIfHelloMultiplierLevel1, sleISISIfHelloMultiplierLevel2, 
				sleISISIfLspInterval, sleISISIfMeshGroup, sleISISIfMetricLevel1, sleISISIfMetricLevel2, sleISISIfNetwork, 
				sleISISIfPriorityLevel1, sleISISIfPriorityLevel2, sleISISIfRestartHelloIntervalLevel1, sleISISIfRestartHelloIntervalLevel2, sleISISIfRetransmitInterval, 
				sleISISIfWideMetricLevel1, sleISISIfWideMetricLevel2, sleISISIfControlRequest, sleISISIfControlStatus, sleISISIfControlTimer, 
				sleISISIfControlTimeStamp, sleISISIfControlReqResult, sleISISIfControlIndex, sleISISIfControlMplsLdpIgpSync, sleISISIfControlIpRouter, 
				sleISISIfControlAuthSendLevel1, sleISISIfControlAuthSendLevel2, sleISISIfControlAuthModeMd5Levle1, sleISISIfControlAuthModeMd5Levle2, sleISISIfControlAuthModeTextLevle1, 
				sleISISIfControlAuthModeTextLevle2, sleISISIfControlAuthKeyChainLevle1, sleISISIfControlAuthKeyChainLevle2, sleISISIfControlBfd, sleISISIfControlBfdDisable, 
				sleISISIfControlCircuitType, sleISISIfControlCsnpIntervalLevel1, sleISISIfControlCsnpIntervalLevel2, sleISISIfControlHelloPadding, sleISISIfControlHelloIntervalLevel1, 
				sleISISIfControlHelloIntervalLevel2, sleISISIfControlHelloIntervalMinimalLevel1, sleISISIfControlHelloIntervalMinimalLevel2, sleISISIfControlHelloMultiplierLevel1, sleISISIfControlHelloMultiplierLevel2, 
				sleISISIfControlLspInterval, sleISISIfControlMeshGroup, sleISISIfControlMetricLevel1, sleISISIfControlMetricLevel2, sleISISIfControlNetwork, 
				sleISISIfControlPriorityLevel1, sleISISIfControlPriorityLevel2, sleISISIfControlRestartHelloIntervalLevel1, sleISISIfControlRestartHelloIntervalLevel2, sleISISIfControlRetransmitInterval, 
				sleISISIfControlWideMetricLevel1, sleISISIfControlWideMetricLevel2, sleISISInstIfInstanceId, sleISISInstIfInterfaceId, sleISISPassiveInterface, 
				sleISISInstIfControlRequest, sleISISInstIfControlStatus, sleISISInstIfControlTimer, sleISISInstIfControlTimeStamp, sleISISInstIfControlReqResult, 
				sleISISInstIfControlInstanceId, sleISISInstIfControlInterfaceId, sleISISInstRedistProtocolInstanceId, sleISISInstRedistProtocolId, sleISISInstRedistProtocolMetric, 
				sleISISInstRedistProtocolMetricType, sleISISInstRedistProtocolRouteLevelType, sleISISInstRedistProtocolRouteMapName, sleISISInstRedistProtocolControlRequest, sleISISInstRedistProtocolControlStatus, 
				sleISISInstRedistProtocolControlTimer, sleISISInstRedistProtocolControlTimeStamp, sleISISInstRedistProtocolControlReqResult, sleISISInstRedistProtocolControlInstanceId, sleISISInstRedistProtocolControlId, 
				sleISISInstRedistProtocolControlMetric, sleISISInstRedistProtocolControlMetricType, sleISISInstRedistProtocolControlRouteLevelType, sleISISInstRedistProtocolControlRouteMapName, sleISISInstRedistIsisProtocolInstanceId, 
				sleISISInstRedistIsisProtocolInterAreaFromLv1ToLv2, sleISISInstRedistIsisProtocolLv1Lv2DistributeList, sleISISInstRedistIsisProtocolInterAreaFromLv2ToLv1, sleISISInstRedistIsisProtocolLv2Lv1DistributeList, sleISISInstRedistIsisProtocolControlRequest, 
				sleISISInstRedistIsisProtocolControlStatus, sleISISInstRedistIsisProtocolControlTimer, sleISISInstRedistIsisProtocolControlTimeStamp, sleISISInstRedistIsisProtocolControlReqResult, sleISISInstRedistIsisProtocolControlInstanceId, 
				sleISISInstRedistIsisProtocolControlLv1Lv2DistributeList, sleISISInstRedistIsisProtocolControlLv2Lv1DistributeList, sleISISSummanryAddressInstanceId, sleISISSummanryAddressIpValue, sleISISSummanryAddressIpNetmask, 
				sleISISSummanryAddressLevel, sleISISSummanryAddressMetric, sleISISSummanryAddressControlRequest, sleISISSummanryAddressControlStatus, sleISISSummanryAddressControlTimer, 
				sleISISSummanryAddressControlTimeStamp, sleISISSummanryAddressControlReqResult, sleISISSummanryAddressControlInstanceId, sleISISSummanryAddressControlIpValue, sleISISSummanryAddressControlIpNetmask, 
				sleISISSummanryAddressControlLevel, sleISISProcAuthenKeyChainL1, sleISISProcAuthenKeyChainL2, sleISISProcAuthenKeyChainL1L2, sleISISProcControlAuthenkeyChain, 
				sleISISProcControlAuthenkeyChainLevel, sleISISDatabaseTag, sleISISDatabaseLevel, sleISISDatabaseLspId, sleISISDatabaseLspSeqNum, 
				sleISISDatabaseLspChecksum, sleISISDatabaseLspHoldtime, sleISISDatabaseAtt, sleISISDatabaseP, sleISISDatabaseOl, 
				sleISISDatabaseDetailTag, sleISISDatabaseDetailLevel, sleISISDatabaseDetailLspId, sleISISDatabaseDetailType, sleISISDatabaseDetailIndex, 
				sleISISDatabaseDetailPad, sleISISDatabaseDetailValue, sleISISDatabaseDetailDescription, sleISISSummanryAddressControlMetric, sleISISIfStatusIfIndex, 
				sleIsisIfStatusIfStatus, sleIsisIfStatusIsisTag, sleIsisIfStatusNetworkType, sleIsisIfStatusCircuitType, sleIsisIfStatusLocalCircuitId, 
				sleIsisIfStatusExtendedLocalCircuitId, sleIsisIfStatusLocalSnpa, sleIsisIfStatusLdpSyncHoldTimer, sleIsisIfStatusLdpSyncRemainingTime, sleIsisIfStatusCircuitL1Metric, 
				sleIsisIfStatusCircuitL1WideMetric, sleIsisIfStatusCircuitL1Priority, sleIsisIfStatusCircuitL1CircuitId, sleIsisIfStatusCircuitL1ActiveAdjacencies, sleIsisIfStatusCircuitL1LscpMtu, 
				sleIsisIfStatusCircuitL2Metric, sleIsisIfStatusCircuitL2WideMetric, sleIsisIfStatusCircuitL2Priority, sleIsisIfStatusCircuitL2CircuitId, sleIsisIfStatusCircuitL2ActiveAdjacencies, 
				sleIsisIfStatusCircuitL2LscpMtu, sleIsisIfStatusL1HelloTimerBroadcast, sleIsisIfStatusL2HelloTimerBroadcast, sleIsisIfStatusL1HellotimerPoint2Point, sleIsisIfStatusL2HellotimerPoint2Point
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleISIS 9 }

		
	
	END

--
-- sle-isis-mib.mib
--
