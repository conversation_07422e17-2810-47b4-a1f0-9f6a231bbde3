--
-- sle-rmon-mib.mib
-- MIB generated by MG-SO<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Tuesday, December 22, 2015 at 14:23:02
--

--  SLE-RMON-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 285
-- Monday, January 22, 2007 at 16:52:34
-- 

	SLE-RMON-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Integer32, Gauge32, Counter32, OBJECT-TYPE, 
			MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			DisplayString			
				FROM SNMPv2-TC;
	
	
--  			sle			
-- FROM AN-MIB			
-- 
-- *******.4.1.6296.101.9
-- December 08, 2004 at 09:03 GMT
-- December 08, 2004 at 08:40 GMT
		-- *******.4.1.6296.101.9
		sleRmon MODULE-IDENTITY 
			LAST-UPDATED "200412080903Z"		-- December 08, 2004 at 09:03 GMT
			ORGANIZATION 
				"ORGANIZATION"
			CONTACT-INFO 
				"Contact "
			DESCRIPTION 
				"This MIB contains all needed informations about rmon and
				all supported sle rmon features."
			REVISION "200412080840Z"		-- December 08, 2004 at 08:40 GMT
			DESCRIPTION 
				"AFI"
			::= { sleMgmt 9 }

		
	
--
-- Type definitions
--
	
--  Type definitions
-- 
-- ::= { sle 14 }
-- textual conventions
		OwnerString ::= OCTET STRING (SIZE (0..255))

--   This data type is used to model an administratively
-- assigned name of the owner of a resource. This
-- information is taken from the NVT ASCII character
-- set.  It is suggested that this name contain one or
-- more of the following: IP address, management station
-- name, network manager's name, location, or phone
-- number.
-- In some cases the agent itself will be the owner of
-- an entry.  In these cases, this string shall be set
-- to a string starting with 'monitor'.
-- 
-- SNMP access control is articulated entirely in terms
-- of the contents of MIB views; access to a particular
-- SNMP object instance depends only upon its presence
-- or absence in a particular MIB view and never upon
-- its value or the value of related object instances.
-- Thus, objects of this type afford resolution of
-- resource contention only among cooperating managers;
-- they realize no access control function with respect
-- to uncooperative parties.
-- 
-- By convention, objects with this syntax are declared as
-- having
-- 
--      SIZE (0..127)
		SleEntryStatus ::= INTEGER
			{
			valid(1),
			createRequest(2),
			underCreation(3)
			}

	
	
--
-- Node definitions
--
	
--  Node definitions
-- 
-- *******.4.1.6296.101.9.1
		-- *******.4.1.6296.101.9.1
		sleHistory OBJECT IDENTIFIER ::= { sleRmon 1 }

		
--   The sleHistory Control Group
-- Implementation of the History Control group is optional.
-- 
-- The sleHistory control group controls the periodic statistical
-- sampling of data from various types of networks.  The
-- historyControlTable stores configuration entries that each
-- define an interface, polling period, and other parameters.
-- Once samples are taken, their data is stored in an entry
-- in a media-specific table.  Each such entry defines one
-- sample, and is associated with the historyControlEntry that
-- caused the sample to be taken.  Each counter in the
-- etherHistoryEntry counts the same event as its
-- similarly-named counterpart in the etherStatsEntry,
-- except that each value here is a cumulative sum during a
-- sampling period.
-- 
-- If the probe keeps track of the time of day, it should
-- start the first sample of the history at a time such that
-- when the next hour of the day begins, a sample is
-- started at that instant.  This tends to make more
-- user-friendly reports, and enables comparison of reports
-- from different probes that have relatively accurate time
-- of day.
-- 
-- The probe is encouraged to add two history control entries
-- per monitored interface upon initialization that describe
-- a short term and a long term polling period.  Suggested
-- parameters are 30 seconds for the short term polling period
-- and 30 minutes for the long term period.
-- *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleHistoryTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleHistoryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of history control entries."
			::= { sleHistory 1 }

		
--  *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleHistoryEntry OBJECT-TYPE
			SYNTAX SleHistoryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of parameters that set up a periodic sampling
				of statistics.  As an example, an instance of the
				sleHistoryControlInterval object might be named
				sleHistoryControlInterval.2"
			INDEX { sleHistoryIndex }
			::= { sleHistoryTable 1 }

		
		SleHistoryEntry ::=
			SEQUENCE { 
				sleHistoryIndex
					INTEGER,
				sleHistoryDataSource
					OBJECT IDENTIFIER,
				sleHistoryBucketsRequested
					INTEGER,
				sleHistoryBucketsGranted
					INTEGER,
				sleHistoryInterval
					INTEGER,
				sleHistoryOwner
					OwnerString,
				sleHistoryStatus
					SleEntryStatus
			 }

--  *******.4.1.6296.*********.1.1
		-- *******.4.1.6296.*********.1.1
		sleHistoryIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"An index that uniquely identifies an entry in the
				historyControl table.  Each such entry defines a
				set of samples at a particular interval for an
				interface on the device."
			::= { sleHistoryEntry 1 }

		
--  *******.4.1.6296.*********.1.2
		-- *******.4.1.6296.*********.1.2
		sleHistoryDataSource OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object identifies the source of the data for
				which historical data was collected and
				placed in a media-specific table on behalf of this
				historyControlEntry.  This source can be any
				interface on this device.  In order to identify
				a particular interface, this object shall identify
				the instance of the ifIndex object, defined
				in  RFC 1213 and RFC 1573 [4,6], for the desired
				interface.  For example, if an entry were to receive
				data from interface #1, this object would be set
				to ifIndex.1.
				
				The statistics in this group reflect all packets
				on the local network segment attached to the
				identified interface.
				
				An agent may or may not be able to tell if fundamental
				changes to the media of the interface have occurred
				and necessitate an invalidation of this entry.  For
				example, a hot-pluggable ethernet card could be
				pulled out and replaced by a token-ring card.  In
				such a case, if the agent has such knowledge of the
				change, it is recommended that it invalidate this
				entry.
				
				This object may not be modified if the associated
				historyControlStatus object is equal to valid(1)."
			::= { sleHistoryEntry 2 }

		
--  *******.4.1.6296.*********.1.3
		-- *******.4.1.6296.*********.1.3
		sleHistoryBucketsRequested OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The requested number of discrete time intervals
				over which data is to be saved in the part of the
				media-specific table associated with this
				historyControlEntry.
				
				When this object is created or modified, the probe
				should set historyControlBucketsGranted as closely to
				this object as is possible for the particular probe
				implementation and available resources."
			DEFVAL { 50 }
			::= { sleHistoryEntry 3 }

		
--  *******.4.1.6296.*********.1.4
		-- *******.4.1.6296.*********.1.4
		sleHistoryBucketsGranted OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of discrete sampling intervals
				over which data shall be saved in the part of
				the media-specific table associated with this
				historyControlEntry.
				
				When the associated historyControlBucketsRequested
				object is created or modified, the probe
				should set this object as closely to the requested
				value as is possible for the particular
				probe implementation and available resources.  The
				probe must not lower this value except as a result
				of a modification to the associated
				historyControlBucketsRequested object.
				
				There will be times when the actual number of
				buckets associated with this entry is less than
				the value of this object.  In this case, at the
				end of each sampling interval, a new bucket will
				be added to the media-specific table.
				
				When the number of buckets reaches the value of
				this object and a new bucket is to be added to the
				media-specific table, the oldest bucket associated
				with this historyControlEntry shall be deleted by
				the agent so that the new bucket can be added.
				
				When the value of this object changes to a value less
				than the current value, entries are deleted
				from the media-specific table associated with this
				historyControlEntry.  Enough of the oldest of these
				entries shall be deleted by the agent so that their
				number remains less than or equal to the new value of
				this object.
				
				When the value of this object changes to a value
				greater than the current value, the number of
				associated media- specific entries may be allowed to
				grow."
			::= { sleHistoryEntry 4 }

		
--  *******.4.1.6296.*********.1.5
		-- *******.4.1.6296.*********.1.5
		sleHistoryInterval OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interval in seconds over which the data is
				sampled for each bucket in the part of the
				media-specific table associated with this
				historyControlEntry.  This interval can
				be set to any number of seconds between 1 and
				3600 (1 hour).
				
				Because the counters in a bucket may overflow at their
				maximum value with no indication, a prudent manager
				will take into account the possibility of overflow
				in any of the associated counters.  It is important
				to consider the minimum time in which any counter
				could overflow on a particular media type and set
				the historyControlInterval object to a value less
				than this interval.  This is typically most
				important for the 'octets' counter in any
				media-specific table.  For example, on an Ethernet
				network, the etherHistoryOctets counter could
				overflow in about one hour at the Ethernet's maximum
				utilization.
				
				This object may not be modified if the associated
				historyControlStatus object is equal to valid(1)."
			DEFVAL { 1800 }
			::= { sleHistoryEntry 5 }

		
--  *******.4.1.6296.*********.1.6
		-- *******.4.1.6296.*********.1.6
		sleHistoryOwner OBJECT-TYPE
			SYNTAX OwnerString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The entity that configured this entry and is
				therefore using the resources assigned to it."
			::= { sleHistoryEntry 6 }

		
--  *******.4.1.6296.*********.1.7
		-- *******.4.1.6296.*********.1.7
		sleHistoryStatus OBJECT-TYPE
			SYNTAX SleEntryStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of this historyControl entry.
				
				Each instance of the media-specific table associated
				with this historyControlEntry will be deleted by the
				agent if this historyControlEntry is not equal to
				valid(1)."
			::= { sleHistoryEntry 7 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleHistoryControl OBJECT IDENTIFIER ::= { sleHistory 2 }

		
--   Node definitions
-- 
-- *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleHistoryControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createHistory(1),
				destroyHistory(2),
				setHistoryProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				createHistory:
				sleHistoryControlIndex
				sleHistoryControlDataSource
				sleHistoryControlBucketsRequested
				sleHistoryControlInterval
				sleHistoryControlOwner
				sleHistoryControlSts
				
				destroyHistory:
				sleHistoryControlIndex
				
				setHistoryProfile:
				sleHistoryControlIndex
				sleHistoryControlDataSource
				sleHistoryControlBucketsRequested
				sleHistoryControlInterval
				sleHistoryControlOwner
				sleHistoryControlSts
				
				"
			::= { sleHistoryControl 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleHistoryControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleHistoryControl 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleHistoryControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleHistoryControl 3 }

		
--  *******.4.1.6296.*********.4
		-- *******.4.1.6296.*********.4
		sleHistoryControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleHistoryControl 4 }

		
--  *******.4.1.6296.*********.5
		-- *******.4.1.6296.*********.5
		sleHistoryControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleHistoryControl 5 }

		
--  *******.4.1.6296.*********.6
		-- *******.4.1.6296.*********.6
		sleHistoryControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleHistoryControl 6 }

		
--  *******.4.1.6296.*********.7
		-- *******.4.1.6296.*********.7
		sleHistoryControlDataSource OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object identifies the source of the data for
				which historical data was collected and
				placed in a media-specific table on behalf of this
				historyControlEntry.  This source can be any
				interface on this device.  In order to identify
				a particular interface, this object shall identify
				the instance of the ifIndex object, defined
				in  RFC 1213 and RFC 1573 [4,6], for the desired
				interface.  For example, if an entry were to receive
				data from interface #1, this object would be set
				to ifIndex.1.
				
				The statistics in this group reflect all packets
				on the local network segment attached to the
				identified interface.
				
				An agent may or may not be able to tell if fundamental
				changes to the media of the interface have occurred
				and necessitate an invalidation of this entry.  For
				example, a hot-pluggable ethernet card could be
				pulled out and replaced by a token-ring card.  In
				such a case, if the agent has such knowledge of the
				change, it is recommended that it invalidate this
				entry.
				
				This object may not be modified if the associated
				historyControlStatus object is equal to valid(1)."
			::= { sleHistoryControl 7 }

		
--  *******.4.1.6296.*********.8
		-- *******.4.1.6296.*********.8
		sleHistoryControlBucketsRequested OBJECT-TYPE
			SYNTAX INTEGER (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The requested number of discrete time intervals
				over which data is to be saved in the part of the
				media-specific table associated with this
				historyControlEntry.
				
				When this object is created or modified, the probe
				should set historyControlBucketsGranted as closely to
				this object as is possible for the particular probe
				implementation and available resources."
			DEFVAL { 50 }
			::= { sleHistoryControl 8 }

		
--  *******.4.1.6296.*********.9
		-- *******.4.1.6296.*********.9
		sleHistoryControlInterval OBJECT-TYPE
			SYNTAX INTEGER (1..3600)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The interval in seconds over which the data is
				sampled for each bucket in the part of the
				media-specific table associated with this
				historyControlEntry.  This interval can
				be set to any number of seconds between 1 and
				3600 (1 hour).
				
				Because the counters in a bucket may overflow at their
				maximum value with no indication, a prudent manager
				will take into account the possibility of overflow
				in any of the associated counters.  It is important
				to consider the minimum time in which any counter
				could overflow on a particular media type and set
				the historyControlInterval object to a value less
				than this interval.  This is typically most
				important for the 'octets' counter in any
				media-specific table.  For example, on an Ethernet
				network, the etherHistoryOctets counter could
				overflow in about one hour at the Ethernet's maximum
				utilization.
				
				This object may not be modified if the associated
				historyControlStatus object is equal to valid(1)."
			DEFVAL { 1800 }
			::= { sleHistoryControl 9 }

		
--  *******.4.1.6296.*********.10
		-- *******.4.1.6296.*********.10
		sleHistoryControlOwner OBJECT-TYPE
			SYNTAX OwnerString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The entity that configured this entry and is
				therefore using the resources assigned to it."
			::= { sleHistoryControl 10 }

		
--  *******.4.1.6296.*********.11
		-- *******.4.1.6296.*********.11
		sleHistoryControlSts OBJECT-TYPE
			SYNTAX SleEntryStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The status of this historyControl entry.
				
				Each instance of the media-specific table associated
				with this historyControlEntry will be deleted by the
				agent if this historyControlEntry is not equal to
				valid(1)."
			::= { sleHistoryControl 11 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleHistoryNotification OBJECT IDENTIFIER ::= { sleHistory 3 }

		
--  *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleHistoryCreated NOTIFICATION-TYPE
			OBJECTS { sleHistoryDataSource, sleHistoryBucketsRequested, sleHistoryInterval, sleHistoryOwner, sleHistoryStatus, 
				sleHistoryControlRequest, sleHistoryControlTimeStamp, sleHistoryControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleHistoryNotification 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleHistoryDestroyed NOTIFICATION-TYPE
			OBJECTS { sleHistoryIndex, sleHistoryControlRequest, sleHistoryControlTimeStamp, sleHistoryControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleHistoryNotification 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleHistoryProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleHistoryDataSource, sleHistoryBucketsRequested, sleHistoryInterval, sleHistoryOwner, sleHistoryStatus, 
				sleHistoryControlRequest, sleHistoryControlTimeStamp, sleHistoryControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleHistoryNotification 3 }

		
--  *******.4.1.6296.101.9.2
		-- *******.4.1.6296.101.9.2
		sleAlarm OBJECT IDENTIFIER ::= { sleRmon 2 }

		
--   The sleAlarm Group
-- Implementation of the Alarm group is optional.
-- 
-- The sleAlarm Group requires the implementation of the Event
-- group.
-- 
-- The sleAlarm group periodically takes
-- statistical samples from variables in the probe and
-- compares them to thresholds that have been
-- configured.  The alarm table stores configuration
-- entries that each define a variable, polling period,
-- and threshold parameters.  If a sample is found to
-- cross the threshold values, an event is generated.
-- Only variables that resolve to an ASN.1 primitive
-- type of INTEGER (INTEGER, Counter, Gauge, or
-- TimeTicks) may be monitored in this way.
-- 
-- This function has a hysteresis mechanism to limit
-- the generation of events.  This mechanism generates
-- one event as a threshold is crossed in the
-- appropriate direction.  No more events are generated
-- for that threshold until the opposite threshold is
-- crossed.
-- 
-- In the case of a sampling a deltaValue, a probe may
-- implement this mechanism with more precision if it
-- takes a delta sample twice per period, each time
-- comparing the sum of the latest two samples to the
-- threshold.  This allows the detection of threshold
-- crossings that span the sampling boundary.  Note
-- that this does not require any special configuration
-- of the threshold value.  It is suggested that probes
-- implement this more precise algorithm.
-- *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleAlarmTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleAlarmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of alarm entries."
			::= { sleAlarm 1 }

		
--  *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleAlarmEntry OBJECT-TYPE
			SYNTAX SleAlarmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of parameters that set up a periodic checking
				for alarm conditions.  For example, an instance of the
				sleAlarmValue object might be named sleAlarmValue.8"
			INDEX { sleAlarmIndex }
			::= { sleAlarmTable 1 }

		
		SleAlarmEntry ::=
			SEQUENCE { 
				sleAlarmIndex
					INTEGER,
				sleAlarmInterval
					Integer32,
				sleAlarmVariable
					OBJECT IDENTIFIER,
				sleAlarmSampleType
					INTEGER,
				sleAlarmValue
					Integer32,
				sleAlarmStartupAlarm
					INTEGER,
				sleAlarmRisingThreshold
					Integer32,
				sleAlarmFallingThreshold
					Integer32,
				sleAlarmRisingEventIndex
					INTEGER,
				sleAlarmFallingEventIndex
					INTEGER,
				sleAlarmOwner
					OwnerString,
				sleAlarmStatus
					SleEntryStatus
			 }

--  *******.4.1.6296.*********.1.1
		-- *******.4.1.6296.*********.1.1
		sleAlarmIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"An index that uniquely identifies an entry in the
				alarm table.  Each such entry defines a
				diagnostic sample at a particular interval
				for an object on the device."
			::= { sleAlarmEntry 1 }

		
--  *******.4.1.6296.*********.1.2
		-- *******.4.1.6296.*********.1.2
		sleAlarmInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The interval in seconds over which the data is
				sampled and compared with the rising and falling
				thresholds.  When setting this variable, care
				should be taken in the case of deltaValue
				sampling - the interval should be set short enough
				that the sampled variable is very unlikely to
				increase or decrease by more than 2^31 - 1 during
				a single sampling interval.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 2 }

		
--  *******.4.1.6296.*********.1.3
		-- *******.4.1.6296.*********.1.3
		sleAlarmVariable OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The object identifier of the particular variable to
				be sampled.  Only variables that resolve to an ASN.1
				primitive type of INTEGER (INTEGER, Counter, Gauge,
				or TimeTicks) may be sampled.
				
				Because SNMP access control is articulated entirely
				in terms of the contents of MIB views, no access
				control mechanism exists that can restrict the value
				of this object to identify only those objects that
				exist in a particular MIB view.  Because there is
				thus no acceptable means of restricting the read
				access that could be obtained through the alarm
				mechanism, the probe must only grant write access to
				this object in those views that have read access to
				all objects on the probe.
				
				During a set operation, if the supplied variable
				name is not available in the selected MIB view, a
				badValue error must be returned.  If at any time the
				variable name of an establisled alarmEntry is no
				longer available in the selected MIB view, the probe
				must change the status of this alarmEntry to
				invalid(4).
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 3 }

		
--  *******.4.1.6296.*********.1.4
		-- *******.4.1.6296.*********.1.4
		sleAlarmSampleType OBJECT-TYPE
			SYNTAX INTEGER
				{
				absoluteValue(1),
				deltaValue(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The method of sampling the selected variable and
				calculating the value to be compared against the
				thresholds.  If the value of this object is
				absoluteValue(1), the value of the selected variable
				will be compared directly with the thresholds at the
				end of the sampling interval.  If the value of this
				object is deltaValue(2), the value of the selected
				variable at the last sample will be subtracted from
				the current value, and the difference compared with
				the thresholds.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 4 }

		
--  *******.4.1.6296.*********.1.5
		-- *******.4.1.6296.*********.1.5
		sleAlarmValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of the statistic during the last sampling
				period.  For example, if the sample type is
				deltaValue, this value will be the difference
				between the samples at the beginning and end of the
				period.  If the sample type is absoluteValue, this
				value will be the sampled value at the end of the
				period.
				
				This is the value that is compared with the rising and
				falling thresholds.
				
				The value during the current sampling period is not
				made available until the period is completed and will
				remain available until the next period completes."
			::= { sleAlarmEntry 5 }

		
--  *******.4.1.6296.*********.1.6
		-- *******.4.1.6296.*********.1.6
		sleAlarmStartupAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				risingAlarm(1),
				fallingAlarm(2),
				risingOrFallingAlarm(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The alarm that may be sent when this entry is first
				set to valid.  If the first sample after this entry
				becomes valid is greater than or equal to the
				risingThreshold and alarmStartupAlarm is equal to
				risingAlarm(1) or risingOrFallingAlarm(3), then a
				single rising alarm will be generated.  If the first
				sample after this entry becomes valid is less than
				or equal to the fallingThreshold and
				alarmStartupAlarm is equal to fallingAlarm(2) or
				risingOrFallingAlarm(3), then a single falling alarm
				will be generated.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 6 }

		
--  *******.4.1.6296.*********.1.7
		-- *******.4.1.6296.*********.1.7
		sleAlarmRisingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A threshold for the sampled statistic.  When the
				current sampled value is greater than or equal to
				this threshold, and the value at the last sampling
				interval was less than this threshold, a single
				event will be generated.  A single event will also
				be generated if the first sample after this entry
				becomes valid is greater than or equal to this
				threshold and the associated alarmStartupAlarm is
				equal to risingAlarm(1) or risingOrFallingAlarm(3).
				
				After a rising event is generated, another such event
				will not be generated until the sampled value
				falls below this threshold and reaches the
				alarmFallingThreshold.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 7 }

		
--  *******.4.1.6296.*********.1.8
		-- *******.4.1.6296.*********.1.8
		sleAlarmFallingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A threshold for the sampled statistic.  When the
				current sampled value is less than or equal to this
				threshold, and the value at the last sampling
				interval was greater than this threshold, a single
				event will be generated.  A single event will also
				be generated if the first sample after this entry
				becomes valid is less than or equal to this
				threshold and the associated alarmStartupAlarm is
				equal to fallingAlarm(2) or risingOrFallingAlarm(3).
				
				After a falling event is generated, another such event
				will not be generated until the sampled value
				rises above this threshold and reaches the
				alarmRisingThreshold.
				
				This object may not be modified if the associated
				sleAlarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 8 }

		
--  *******.4.1.6296.*********.1.9
		-- *******.4.1.6296.*********.1.9
		sleAlarmRisingEventIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of the eventEntry that is
				used when a rising threshold is crossed.  The
				eventEntry identified by a particular value of
				this index is the same as identified by the same value
				of the eventIndex object.  If there is no
				corresponding entry in the eventTable, then
				no association exists.  In particular, if this value
				is zero, no associated event will be generated, as
				zero is not a valid event index.
				
				This object may not be modified if the associated
				sleAlarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 9 }

		
--  *******.4.1.6296.*********.1.10
		-- *******.4.1.6296.*********.1.10
		sleAlarmFallingEventIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of the eventEntry that is
				used when a falling threshold is crossed.  The
				eventEntry identified by a particular value of
				this index is the same as identified by the same value
				of the eventIndex object.  If there is no
				corresponding entry in the eventTable, then
				no association exists.  In particular, if this value
				is zero, no associated event will be generated, as
				zero is not a valid event index.
				
				This object may not be modified if the associated
				sleAlarmStatus object is equal to valid(1)."
			::= { sleAlarmEntry 10 }

		
--  *******.4.1.6296.*********.1.11
		-- *******.4.1.6296.*********.1.11
		sleAlarmOwner OBJECT-TYPE
			SYNTAX OwnerString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The entity that configured this entry and is
				therefore using the resources assigned to it."
			::= { sleAlarmEntry 11 }

		
--  *******.4.1.6296.*********.1.12
		-- *******.4.1.6296.*********.1.12
		sleAlarmStatus OBJECT-TYPE
			SYNTAX SleEntryStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of this alarm entry."
			::= { sleAlarmEntry 12 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleAlarmControl OBJECT IDENTIFIER ::= { sleAlarm 2 }

		
--   Node definitions
-- 
-- *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleAlarmControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createAlarm(1),
				destroyAlarm(2),
				setAlarmProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				createAlarm:
				(sleAlarmControlIndex)
				sleAlarmInterval
				sleAlarmControlVariable
				sleAlarmControlSampleType
				sleAlarmControlStartupAlarm
				sleAlarmControlRisingThreshold
				sleAlarmControlFallingThreshold
				sleAlarmControlRisingEventIndex
				sleAlarmControlFallingEventIndex
				sleAlarmControlOwner
				sleAlarmControlSts
				
				destroyAlarm:
				sleAlarmControlIndex
				
				setAlarmProfile:
				sleAlarmControlIndex
				sleAlarmControlInterval
				sleAlarmControlVariable
				sleAlarmControlSampleType
				sleAlarmControlStartupAlarm
				sleAlarmControlRisingThreshold
				sleAlarmControlFallingThreshold
				sleAlarmControlRisingEventIndex
				sleAlarmControlFallingEventIndex
				sleAlarmControlOwner
				sleAlarmControlSts
				
				"
			::= { sleAlarmControl 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleAlarmControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleAlarmControl 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleAlarmControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleAlarmControl 3 }

		
--  *******.4.1.6296.*********.4
		-- *******.4.1.6296.*********.4
		sleAlarmControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleAlarmControl 4 }

		
--  *******.4.1.6296.*********.5
		-- *******.4.1.6296.*********.5
		sleAlarmControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleAlarmControl 5 }

		
--  *******.4.1.6296.*********.6
		-- *******.4.1.6296.*********.6
		sleAlarmControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAlarmControl 6 }

		
--  *******.4.1.6296.*********.7
		-- *******.4.1.6296.*********.7
		sleAlarmControlInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The interval in seconds over which the data is
				sampled and compared with the rising and falling
				thresholds.  When setting this variable, care
				should be taken in the case of deltaValue
				sampling - the interval should be set short enough
				that the sampled variable is very unlikely to
				increase or decrease by more than 2^31 - 1 during
				a single sampling interval.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 7 }

		
--  *******.4.1.6296.*********.8
		-- *******.4.1.6296.*********.8
		sleAlarmControlVariable OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The object identifier of the particular variable to
				be sampled.  Only variables that resolve to an ASN.1
				primitive type of INTEGER (INTEGER, Counter, Gauge,
				or TimeTicks) may be sampled.
				
				Because SNMP access control is articulated entirely
				in terms of the contents of MIB views, no access
				control mechanism exists that can restrict the value
				of this object to identify only those objects that
				exist in a particular MIB view.  Because there is
				thus no acceptable means of restricting the read
				access that could be obtained through the alarm
				mechanism, the probe must only grant write access to
				this object in those views that have read access to
				all objects on the probe.
				
				During a set operation, if the supplied variable
				name is not available in the selected MIB view, a
				badValue error must be returned.  If at any time the
				variable name of an establisled alarmEntry is no
				longer available in the selected MIB view, the probe
				must change the status of this alarmEntry to
				invalid(4).
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 8 }

		
--  *******.4.1.6296.*********.9
		-- *******.4.1.6296.*********.9
		sleAlarmControlSampleType OBJECT-TYPE
			SYNTAX INTEGER
				{
				absoluteValue(1),
				deltaValue(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The method of sampling the selected variable and
				calculating the value to be compared against the
				thresholds.  If the value of this object is
				absoluteValue(1), the value of the selected variable
				will be compared directly with the thresholds at the
				end of the sampling interval.  If the value of this
				object is deltaValue(2), the value of the selected
				variable at the last sample will be subtracted from
				the current value, and the difference compared with
				the thresholds.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 9 }

		
--  *******.4.1.6296.*********.10
		-- *******.4.1.6296.*********.10
		sleAlarmControlStartupAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				risingAlarm(1),
				fallingAlarm(2),
				risingOrFallingAlarm(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The alarm that may be sent when this entry is first
				set to valid.  If the first sample after this entry
				becomes valid is greater than or equal to the
				risingThreshold and alarmStartupAlarm is equal to
				risingAlarm(1) or risingOrFallingAlarm(3), then a
				single rising alarm will be generated.  If the first
				sample after this entry becomes valid is less than
				or equal to the fallingThreshold and
				alarmStartupAlarm is equal to fallingAlarm(2) or
				risingOrFallingAlarm(3), then a single falling alarm
				will be generated.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 10 }

		
--  *******.4.1.6296.*********.11
		-- *******.4.1.6296.*********.11
		sleAlarmControlRisingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A threshold for the sampled statistic.  When the
				current sampled value is greater than or equal to
				this threshold, and the value at the last sampling
				interval was less than this threshold, a single
				event will be generated.  A single event will also
				be generated if the first sample after this entry
				becomes valid is greater than or equal to this
				threshold and the associated alarmStartupAlarm is
				equal to risingAlarm(1) or risingOrFallingAlarm(3).
				
				After a rising event is generated, another such event
				will not be generated until the sampled value
				falls below this threshold and reaches the
				alarmFallingThreshold.
				
				This object may not be modified if the associated
				alarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 11 }

		
--  *******.4.1.6296.*********.12
		-- *******.4.1.6296.*********.12
		sleAlarmControlFallingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A threshold for the sampled statistic.  When the
				current sampled value is less than or equal to this
				threshold, and the value at the last sampling
				interval was greater than this threshold, a single
				event will be generated.  A single event will also
				be generated if the first sample after this entry
				becomes valid is less than or equal to this
				threshold and the associated alarmStartupAlarm is
				equal to fallingAlarm(2) or risingOrFallingAlarm(3).
				
				After a falling event is generated, another such event
				will not be generated until the sampled value
				rises above this threshold and reaches the
				alarmRisingThreshold.
				
				This object may not be modified if the associated
				sleAlarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 12 }

		
--  *******.4.1.6296.*********.13
		-- *******.4.1.6296.*********.13
		sleAlarmControlRisingEventIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of the eventEntry that is
				used when a rising threshold is crossed.  The
				eventEntry identified by a particular value of
				this index is the same as identified by the same value
				of the eventIndex object.  If there is no
				corresponding entry in the eventTable, then
				no association exists.  In particular, if this value
				is zero, no associated event will be generated, as
				zero is not a valid event index.
				
				This object may not be modified if the associated
				sleAlarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 13 }

		
--  *******.4.1.6296.*********.14
		-- *******.4.1.6296.*********.14
		sleAlarmControlFallingEventIndex OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The index of the eventEntry that is
				used when a falling threshold is crossed.  The
				eventEntry identified by a particular value of
				this index is the same as identified by the same value
				of the eventIndex object.  If there is no
				corresponding entry in the eventTable, then
				no association exists.  In particular, if this value
				is zero, no associated event will be generated, as
				zero is not a valid event index.
				
				This object may not be modified if the associated
				sleAlarmStatus object is equal to valid(1)."
			::= { sleAlarmControl 14 }

		
--  *******.4.1.6296.*********.15
		-- *******.4.1.6296.*********.15
		sleAlarmControlOwner OBJECT-TYPE
			SYNTAX OwnerString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The entity that configured this entry and is
				therefore using the resources assigned to it."
			::= { sleAlarmControl 15 }

		
--  *******.4.1.6296.*********.16
		-- *******.4.1.6296.*********.16
		sleAlarmControlSts OBJECT-TYPE
			SYNTAX SleEntryStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The status of this alarm entry."
			::= { sleAlarmControl 16 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleAlarmNotification OBJECT IDENTIFIER ::= { sleAlarm 3 }

		
--  *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleAlarmCreated NOTIFICATION-TYPE
			OBJECTS { sleAlarmInterval, sleAlarmVariable, sleAlarmSampleType, sleAlarmStartupAlarm, sleAlarmRisingThreshold, 
				sleAlarmFallingThreshold, sleAlarmRisingEventIndex, sleAlarmFallingEventIndex, sleAlarmOwner, sleAlarmStatus, 
				sleAlarmControlRequest, sleAlarmControlTimeStamp, sleAlarmControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleAlarmNotification 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleAlarmDestroyed NOTIFICATION-TYPE
			OBJECTS { sleAlarmIndex, sleAlarmControlRequest, sleAlarmControlTimeStamp, sleAlarmControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleAlarmNotification 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleAlarmProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleAlarmControlRequest, sleAlarmControlTimeStamp, sleAlarmControlReqResult, sleAlarmInterval, sleAlarmVariable, 
				sleAlarmSampleType, sleAlarmStartupAlarm, sleAlarmRisingThreshold, sleAlarmFallingThreshold, sleAlarmRisingEventIndex, 
				sleAlarmFallingEventIndex, sleAlarmOwner, sleAlarmStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleAlarmNotification 3 }

		
--   The status of a table entry.
-- 
-- Setting this object to the value invalid(4) has the
-- effect of invalidating the corresponding entry.
-- That is, it effectively disassociates the mapping
-- identified with said entry.
-- It is an implementation-specific matter as to whether
-- the agent removes an invalidated entry from the table.
-- Accordingly, management stations must be prepared to
-- receive tabular information from agents that
-- corresponds to entries currently not in use.  Proper
-- interpretation of such entries requires examination
-- of the relevant EntryStatus object.
-- 
-- An existing instance of this object cannot be set to
-- createRequest(2).  This object may only be set to
-- createRequest(2) when this instance is created.  When
-- this object is created, the agent may wish to create
-- supplemental object instances with default values
-- to complete a conceptual row in this table.  Because
-- the creation of these default objects is entirely at
-- the option of the agent, the manager must not assume
-- that any will be created, but may make use of any that
-- are created. Immediately after completing the create
-- operation, the agent must set this object to
-- underCreation(3).
-- 
-- When in the underCreation(3) state, an entry is
-- allowed to exist in a possibly incomplete, possibly
-- inconsistent state, usually to allow it to be
-- modified in mutiple PDUs.  When in this state, an
-- entry is not fully active.  Entries shall exist in
-- the underCreation(3) state until the management
-- station is finisled configuring the entry and sets
-- this object to valid(1) or aborts, setting this
-- object to invalid(4).  If the agent determines that
-- an entry has been in the underCreation(3) state for
-- an abnormally long time, it may decide that the
-- management station has crasled.  If the agent makes
-- this decision, it may set this object to invalid(4)
-- to reclaim the entry.  A prudent agent will
-- understand that the management station may need to
-- wait for human input and will allow for that
-- possibility in its determination of this abnormally
-- long period.
-- 
-- An entry in the valid(1) state is fully configured and
-- consistent and fully represents the configuration or
-- operation such a row is intended to represent.  For
-- example, it could be a statistical function that is
-- configured and active, or a filter that is available
-- in the list of filters processed by the packet capture
-- process.
-- 
-- A manager is restricted to changing the state of an
-- entry in the following ways:
-- 
--                       create   under
--      To:       valid  Request  Creation  invalid
-- From:
-- valid             OK       NO        OK       OK
-- createRequest    N/A      N/A       N/A      N/A
-- underCreation     OK       NO        OK       OK
-- invalid           NO       NO        NO       OK
-- nonExistent       NO       OK        NO       OK
-- 
-- In the table above, it is not applicable to move the
-- state from the createRequest state to any other
-- state because the manager will never find the
-- variable in that state.  The nonExistent state is
-- not a value of the enumeration, rather it means that
-- the entryStatus variable does not exist at all.
-- 
-- An agent may allow an entryStatus variable to change
-- state in additional ways, so long as the semantics
-- of the states are followed.  This allowance is made
-- to ease the implementation of the agent and is made
-- despite the fact that managers should never
-- excercise these additional state transitions.
-- *******.4.1.6296.101.9.3
		-- *******.4.1.6296.101.9.3
		sleEvent OBJECT IDENTIFIER ::= { sleRmon 3 }

		
--   The sleEvent Group
-- Implementation of the Event group is optional.
-- 
-- The Event group controls the generation and notification
-- of events from this device.  Each entry in the eventTable
-- describes the parameters of the event that can be
-- triggered. Each event entry is fired by an associated
-- condition located elsewhere in the MIB.  An event entry
-- may also be associated- with a function elsewhere in the
-- MIB that will be executed when the event is generated.  For
-- example, a channel may be turned on or off by the firing
-- of an event.
-- 
-- Each eventEntry may optionally specify that a log entry
-- be created on its behalf whenever the event occurs.
-- Each entry may also specify that notification should
-- occur by way of SNMP trap messages.  In this case, the
-- community for the trap message is given in the associated
-- eventCommunity object.  The enterprise and specific trap
-- fields of the trap are determined by the condition that
-- triggered the event.  Two traps are defined: risingAlarm
-- and fallingAlarm.  If the eventTable is triggered by a
-- condition specified elsewhere, the enterprise and
-- specific trap fields must be specified for traps
-- generated for that condition.
-- *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleEventTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleEventEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A list of events to be generated."
			::= { sleEvent 1 }

		
--  *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleEventEntry OBJECT-TYPE
			SYNTAX SleEventEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A set of parameters that describe an event to be
				generated when certain conditions are met.  As an
				example, an instance of the sleEventLastTimeSent object
				might be named sleEventLastTimeSent.6"
			INDEX { sleEventIndex }
			::= { sleEventTable 1 }

		
		SleEventEntry ::=
			SEQUENCE { 
				sleEventIndex
					INTEGER,
				sleEventDescription
					DisplayString,
				sleEventType
					INTEGER,
				sleEventCommunity
					OCTET STRING,
				sleEventLastTimeSent
					TimeTicks,
				sleEventOwner
					OwnerString,
				sleEventStatus
					SleEntryStatus
			 }

--  *******.4.1.6296.*********.1.1
		-- *******.4.1.6296.*********.1.1
		sleEventIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"An index that uniquely identifies an entry in the
				event table.  Each such entry defines one event that
				is to be generated when the appropriate conditions
				occur."
			::= { sleEventEntry 1 }

		
--  *******.4.1.6296.*********.1.2
		-- *******.4.1.6296.*********.1.2
		sleEventDescription OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..127))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"A comment describing this event entry."
			::= { sleEventEntry 2 }

		
--   send an SNMP trap
-- *******.4.1.6296.*********.1.3
		-- *******.4.1.6296.*********.1.3
		sleEventType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(1),
				log(2),
				snmpTrap(3),
				logAndTrap(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of notification that the probe will make
				about this event.  In the case of log, an entry is
				made in the log table for each event.  In the case of
				snmp-trap, an SNMP trap is sent to one or more
				management stations."
			::= { sleEventEntry 3 }

		
--  *******.4.1.6296.*********.1.4
		-- *******.4.1.6296.*********.1.4
		sleEventCommunity OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..127))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If an SNMP trap is to be sent, it will be sent to
				the SNMP community specified by this octet string.
				In the future this table will be extended to include
				the party security mechanism.  This object shall be
				set to a string of length zero if it is intended that
				that mechanism be used to specify the destination of
				the trap."
			::= { sleEventEntry 4 }

		
--  *******.4.1.6296.*********.1.5
		-- *******.4.1.6296.*********.1.5
		sleEventLastTimeSent OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of sysUpTime at the time this event
				entry last generated an event.  If this entry has
				not generated any events, this value will be
				zero."
			::= { sleEventEntry 5 }

		
--  *******.4.1.6296.*********.1.6
		-- *******.4.1.6296.*********.1.6
		sleEventOwner OBJECT-TYPE
			SYNTAX OwnerString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The entity that configured this entry and is
				therefore using the resources assigned to it.
				
				If this object contains a string starting with
				'monitor' and has associated entries in the log
				table, all connected management stations should
				retrieve those log entries, as they may have
				significance to all management stations connected to
				this device"
			::= { sleEventEntry 6 }

		
--  *******.4.1.6296.*********.1.7
		-- *******.4.1.6296.*********.1.7
		sleEventStatus OBJECT-TYPE
			SYNTAX SleEntryStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of this event entry.
				
				If this object is not equal to valid(1), all
				associated log entries shall be deleted by the
				agent."
			::= { sleEventEntry 7 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleEventControl OBJECT IDENTIFIER ::= { sleEvent 2 }

		
--   Node definitions
-- 
-- *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleEventControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createEvent(1),
				destroyEvent(2),
				setEventProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				createEvent:
				(sleEventControlIndex)
				sleEventControlDescription
				sleEventControlType
				sleEventControlCommunity
				sleEventControlOwner
				sleEventControlSts
				
				destroyEvent:
				sleEventControlIndex
				
				setEventProfile:
				sleEventControlIndex
				sleEventControlDescription
				sleEventControlType
				sleEventControlCommunity
				sleEventControlOwner
				sleEventControlSts
				
				"
			::= { sleEventControl 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleEventControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleEventControl 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleEventControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleEventControl 3 }

		
--  *******.4.1.6296.*********.4
		-- *******.4.1.6296.*********.4
		sleEventControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleEventControl 4 }

		
--  *******.4.1.6296.*********.5
		-- *******.4.1.6296.*********.5
		sleEventControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleEventControl 5 }

		
--  *******.4.1.6296.*********.6
		-- *******.4.1.6296.*********.6
		sleEventControlIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleEventControl 6 }

		
--  *******.4.1.6296.*********.7
		-- *******.4.1.6296.*********.7
		sleEventControlDescription OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..127))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"A comment describing this event entry."
			::= { sleEventControl 7 }

		
--   send an SNMP trap
-- *******.4.1.6296.*********.8
		-- *******.4.1.6296.*********.8
		sleEventControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(1),
				log(2),
				snmpTrap(3),
				logAndTrap(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The type of notification that the probe will make
				about this event.  In the case of log, an entry is
				made in the log table for each event.  In the case of
				snmp-trap, an SNMP trap is sent to one or more
				management stations."
			::= { sleEventControl 8 }

		
--  *******.4.1.6296.*********.9
		-- *******.4.1.6296.*********.9
		sleEventControlCommunity OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..127))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"If an SNMP trap is to be sent, it will be sent to
				the SNMP community specified by this octet string.
				In the future this table will be extended to include
				the party security mechanism.  This object shall be
				set to a string of length zero if it is intended that
				that mechanism be used to specify the destination of
				the trap."
			::= { sleEventControl 9 }

		
--  *******.4.1.6296.*********.10
		-- *******.4.1.6296.*********.10
		sleEventControlOwner OBJECT-TYPE
			SYNTAX OwnerString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The entity that configured this entry and is
				therefore using the resources assigned to it.
				
				If this object contains a string starting with
				'monitor' and has associated entries in the log
				table, all connected management stations should
				retrieve those log entries, as they may have
				significance to all management stations connected to
				this device"
			::= { sleEventControl 10 }

		
--  *******.4.1.6296.*********.11
		-- *******.4.1.6296.*********.11
		sleEventControlSts OBJECT-TYPE
			SYNTAX SleEntryStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The status of this event entry.
				
				If this object is not equal to valid(1), all
				associated log entries shall be deleted by the
				agent."
			::= { sleEventControl 11 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleEventNotification OBJECT IDENTIFIER ::= { sleEvent 3 }

		
--  *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleEventCreated NOTIFICATION-TYPE
			OBJECTS { sleEventDescription, sleEventType, sleEventCommunity, sleEventOwner, sleEventStatus, 
				sleEventControlRequest, sleEventControlTimeStamp, sleEventControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleEventNotification 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleEventDestroyed NOTIFICATION-TYPE
			OBJECTS { sleEventIndex, sleEventControlRequest, sleEventControlTimeStamp, sleEventControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleEventNotification 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleEventProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleEventDescription, sleEventType, sleEventCommunity, sleEventOwner, sleEventStatus, 
				sleEventControlRequest, sleEventControlTimeStamp, sleEventControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleEventNotification 3 }

		
--  *******.4.1.6296.101.9.4
		-- *******.4.1.6296.101.9.4
		sleStatistics OBJECT IDENTIFIER ::= { sleRmon 4 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleEtherStats OBJECT IDENTIFIER ::= { sleStatistics 1 }

		
--  *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleEtherStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleEtherStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleEtherStats 1 }

		
--  *******.4.1.6296.*********.1.1
		-- *******.4.1.6296.*********.1.1
		sleEtherStatsEntry OBJECT-TYPE
			SYNTAX SleEtherStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleEtherStatsIndex }
			::= { sleEtherStatsTable 1 }

		
		SleEtherStatsEntry ::=
			SEQUENCE { 
				sleEtherStatsIndex
					INTEGER,
				sleEtherStatsDataSource
					OBJECT IDENTIFIER,
				sleEtherStatsDropEvents
					Counter32,
				sleEtherStatsOctets
					Counter32,
				sleEtherStatsPkts
					Counter32,
				sleEtherStatsBroadcastPkts
					Counter32,
				sleEtherStatsMulticastPkts
					Counter32,
				sleEtherStatsCRCAlignErrors
					Counter32,
				sleEtherStatsUndersizePkts
					Counter32,
				sleEtherStatsOversizePkts
					Counter32,
				sleEtherStatsFragments
					Counter32,
				sleEtherStatsJabbers
					Counter32,
				sleEtherStatsCollisions
					Counter32,
				sleEtherStatsPkts64Octets
					Counter32,
				sleEtherStatsPkts65to127Octets
					Counter32,
				sleEtherStatsPkts128to255Octets
					Counter32,
				sleEtherStatsPkts256to511Octets
					Counter32,
				sleEtherStatsPkts512to1023Octets
					Counter32,
				sleEtherStatsPkts1024to1518Octets
					Counter32,
				sleEtherStatsOwner
					OCTET STRING,
				sleEtherStatsStatus
					SleEntryStatus,
				sleEtherStatsClearedTime
					TimeTicks
			 }

--  *******.4.1.6296.*********.1.1.1
		-- *******.4.1.6296.*********.1.1.1
		sleEtherStatsIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of this object uniquely identifies this sleEtherStats entry"
			::= { sleEtherStatsEntry 1 }

		
--  *******.4.1.6296.*********.1.1.2
		-- *******.4.1.6296.*********.1.1.2
		sleEtherStatsDataSource OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object identifies the source of the data that
				this sleEtherStats entry is configured to analyze.  This
				source can be any ethernet interface on this device.
				In order to identify a particular interface, this
				object shall identify the instance of the ifIndex
				object, defined in RFC 1213 and RFC 1573 [4,6], for
				the desired interface.  For example, if an entry
				were to receive data from interface #1, this object
				would be set to ifIndex.1.
				 	
				The statistics in this group reflect all packets
				on the local network segment attached to the
				identified interface.
				 	
				An agent may or may not be able to tell if
				fundamental changes to the media of the interface
				have occurred and necessitate an invalidation of
				this entry.  For example, a hot-pluggable ethernet
				card could be pulled out and replaced by a
				token-ring card.  In such a case, if the agent has
				such knowledge of the change, it is recommended that
				it invalidate this entry.
				 	
				This object may not be modified if the associated
				sleEtherStatsStatus object is equal to valid(1)."
			::= { sleEtherStatsEntry 2 }

		
--  *******.4.1.6296.*********.1.1.3
		-- *******.4.1.6296.*********.1.1.3
		sleEtherStatsDropEvents OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of events in which packets
				were dropped by the probe due to lack of resources.
				Note that this number is not necessarily the number of
				packets dropped; it is just the number of times this
				condition has been detected."
			::= { sleEtherStatsEntry 3 }

		
--  *******.4.1.6296.*********.1.1.4
		-- *******.4.1.6296.*********.1.1.4
		sleEtherStatsOctets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of octets of data (including
				those in bad packets) received on the
				network (excluding framing bits but including
				FCS octets).
				 	
				This object can be used as a reasonable estimate of
				ethernet utilization.  If greater precision is
				desired, the etherStatsPkts and etherStatsOctets
				objects should be sampled before and after a common
				interval.  The differences in the sampled values are
				Pkts and Octets, respectively, and the number of
				seconds in the interval is Interval.  These values
				are used to calculate the Utilization as follows:
				 	
				              Pkts * (9.6 + 6.4) + (Octets * .8)
				Utilization = -------------------------------------
				                   Interval * 10,000
				 	
				The result of this equation is the value Utilization
				which is the percent utilization of the ethernet
				segment on a scale of 0 to 100 percent.
				"
			::= { sleEtherStatsEntry 4 }

		
--  *******.4.1.6296.*********.1.1.5
		-- *******.4.1.6296.*********.1.1.5
		sleEtherStatsPkts OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets (including bad packets,
				broadcast packets, and multicast packets) received."
			::= { sleEtherStatsEntry 5 }

		
--  *******.4.1.6296.*********.1.1.6
		-- *******.4.1.6296.*********.1.1.6
		sleEtherStatsBroadcastPkts OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of good packets received that were
				directed to the broadcast address.  Note that this
				does not include multicast packets."
			::= { sleEtherStatsEntry 6 }

		
--  *******.4.1.6296.*********.1.1.7
		-- *******.4.1.6296.*********.1.1.7
		sleEtherStatsMulticastPkts OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of good packets received that were
				directed to a multicast address.  Note that this
				number does not include packets directed to the
				broadcast address.
				"
			::= { sleEtherStatsEntry 7 }

		
--  *******.4.1.6296.*********.1.1.8
		-- *******.4.1.6296.*********.1.1.8
		sleEtherStatsCRCAlignErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets received that
				had a length (excluding framing bits, but
				including FCS octets) of between 64 and 1518
				octets, inclusive, but but had either a bad
				Frame Check Sequence (FCS) with an integral
				number of octets (FCS Error) or a bad FCS with
				a non-integral number of octets (Alignment Error).
				"
			::= { sleEtherStatsEntry 8 }

		
--  *******.4.1.6296.*********.1.1.9
		-- *******.4.1.6296.*********.1.1.9
		sleEtherStatsUndersizePkts OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets received that were
				less than 64 octets long (excluding framing bits,
				but including FCS octets) and were otherwise well
				formed.
				"
			::= { sleEtherStatsEntry 9 }

		
--  *******.4.1.6296.*********.1.1.10
		-- *******.4.1.6296.*********.1.1.10
		sleEtherStatsOversizePkts OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets received that were
				longer than 1518 octets (excluding framing bits,
				but including FCS octets) and were otherwise
				well formed.
				"
			::= { sleEtherStatsEntry 10 }

		
--  *******.4.1.6296.*********.1.1.11
		-- *******.4.1.6296.*********.1.1.11
		sleEtherStatsFragments OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets received that were less
				than 64 octets in length (excluding framing bits but
				including FCS octets) and had either a bad Frame
				Check Sequence (FCS) with an integral number of
				octets (FCS Error) or a bad FCS with a non-integral
				number of octets (Alignment Error).
				"
			::= { sleEtherStatsEntry 11 }

		
--  *******.4.1.6296.*********.1.1.12
		-- *******.4.1.6296.*********.1.1.12
		sleEtherStatsJabbers OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets received that were
				longer than 1518 octets (excluding framing bits,
				but including FCS octets), and had either a bad
				Frame Check Sequence (FCS) with an integral number
				of octets (FCS Error) or a bad FCS with a
				non-integral number of octets (Alignment Error).
				"
			::= { sleEtherStatsEntry 12 }

		
--  *******.4.1.6296.*********.1.1.13
		-- *******.4.1.6296.*********.1.1.13
		sleEtherStatsCollisions OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The best estimate of the total number of collisions
				on this Ethernet segment.
				 	
				The value returned will depend on the location of
				the RMON probe. Section ******* (10BASE-5) and
				section ******** (10BASE-2) of IEEE standard 802.3
				states that a station must detect a collision, in
				the receive mode, if three or more stations are
				transmitting simultaneously.  A repeater port must
				detect a collision when two or more stations are
				transmitting simultaneously.  Thus a probe placed on
				a repeater port could record more collisions than a
				probe connected to a station on the same segment
				would.
				 	
				Probe location plays a much smaller role when 
				considering 10BASE-T.  ******** (10BASE-T) of IEEE
				standard 802.3 defines a collision as the
				simultaneous presence of signals on the DO and RD
				circuits (transmitting and receiving at the same
				time).  A 10BASE-T station can only detect
				collisions when it is transmitting.  Thus probes
				placed on a station and a repeater, should report
				the same number of collisions.
				 	
				Note also that an RMON probe inside a repeater
				should ideally report collisions between the
				repeater and one or more other hosts (transmit
				collisions as defined by IEEE 802.3k) plus receiver
				collisions observed on any coax segments to which
				the repeater is connected.
				"
			::= { sleEtherStatsEntry 13 }

		
--  *******.4.1.6296.*********.1.1.14
		-- *******.4.1.6296.*********.1.1.14
		sleEtherStatsPkts64Octets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets (including bad
				packets) received that were 64 octets in length
				(excluding framing bits but including FCS octets).
				"
			::= { sleEtherStatsEntry 14 }

		
--  *******.4.1.6296.*********.1.1.15
		-- *******.4.1.6296.*********.1.1.15
		sleEtherStatsPkts65to127Octets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets (including bad
				packets) received that were between
				65 and 127 octets in length inclusive
				(excluding framing bits but including FCS octets)."
			::= { sleEtherStatsEntry 15 }

		
--  *******.4.1.6296.*********.1.1.16
		-- *******.4.1.6296.*********.1.1.16
		sleEtherStatsPkts128to255Octets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets (including bad
				packets) received that were between
				128 and 255 octets in length inclusive
				(excluding framing bits but including FCS octets)."
			::= { sleEtherStatsEntry 16 }

		
--  *******.4.1.6296.*********.1.1.17
		-- *******.4.1.6296.*********.1.1.17
		sleEtherStatsPkts256to511Octets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets (including bad
				packets) received that were between
				256 and 511 octets in length inclusive
				(excluding framing bits but including FCS octets)."
			::= { sleEtherStatsEntry 17 }

		
--  *******.4.1.6296.*********.1.1.18
		-- *******.4.1.6296.*********.1.1.18
		sleEtherStatsPkts512to1023Octets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets (including bad
				packets) received that were between
				512 and 1023 octets in length inclusive
				(excluding framing bits but including FCS octets)."
			::= { sleEtherStatsEntry 18 }

		
--  *******.4.1.6296.*********.1.1.19
		-- *******.4.1.6296.*********.1.1.19
		sleEtherStatsPkts1024to1518Octets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The total number of packets (including bad
				packets) received that were between
				1024 and 1518 octets in length inclusive
				(excluding framing bits but including FCS octets)."
			::= { sleEtherStatsEntry 19 }

		
--  *******.4.1.6296.*********.1.1.20
		-- *******.4.1.6296.*********.1.1.20
		sleEtherStatsOwner OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The entity that configured this entry and is
				therefore using the resources assigned to it.
				"
			::= { sleEtherStatsEntry 20 }

		
--  *******.4.1.6296.*********.1.1.21
		-- *******.4.1.6296.*********.1.1.21
		sleEtherStatsStatus OBJECT-TYPE
			SYNTAX SleEntryStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of this etherStats entry.
				"
			::= { sleEtherStatsEntry 21 }

		
--  *******.4.1.6296.*********.1.1.22
		-- *******.4.1.6296.*********.1.1.22
		sleEtherStatsClearedTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleEtherStatsEntry 22 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********.2
		sleEtherStatsControl OBJECT IDENTIFIER ::= { sleEtherStats 2 }

		
--   Node definitions
-- 
-- *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.2.1
		sleEtherStatsControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearRmonStat(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				createEvent:
				(sleEventControlIndex)
				sleEventControlDescription
				sleEventControlType
				sleEventControlCommunity
				sleEventControlOwner
				sleEventControlSts
				
				destroyEvent:
				sleEventControlIndex
				
				setEventProfile:
				sleEventControlIndex
				sleEventControlDescription
				sleEventControlType
				sleEventControlCommunity
				sleEventControlOwner
				sleEventControlSts
				
				"
			::= { sleEtherStatsControl 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2.2
		sleEtherStatsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleEtherStatsControl 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.2.3
		sleEtherStatsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleEtherStatsControl 3 }

		
--  *******.4.1.6296.*********.4
		-- *******.4.1.6296.*********.2.4
		sleEtherStatsControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleEtherStatsControl 4 }

		
--  *******.4.1.6296.*********.5
		-- *******.4.1.6296.*********.2.5
		sleEtherStatsControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleEtherStatsControl 5 }

		
--  *******.4.1.6296.*********.6
		-- *******.4.1.6296.*********.2.6
		sleEtherStatsControlIfIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleEtherStatsControl 6 }

		
		-- *******.4.1.6296.101.9.5
		sleRmonSimple OBJECT IDENTIFIER ::= { sleRmon 5 }

		
		-- *******.4.1.6296.*********
		sleRmonSimpleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRmonSimpleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRmonSimple 1 }

		
		-- *******.4.1.6296.*********.1
		sleRmonSimpleEntry OBJECT-TYPE
			SYNTAX SleRmonSimpleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRmonSimplePortIndex, sleRmonSimpleSampleVariable }
			::= { sleRmonSimpleTable 1 }

		
		SleRmonSimpleEntry ::=
			SEQUENCE { 
				sleRmonSimplePortIndex
					Integer32,
				sleRmonSimpleSampleVariable
					INTEGER,
				sleRmonSimpleSampleInterval
					Integer32,
				sleRmonSimpleRisingThreshold
					Integer32,
				sleRmonSimpleFallingThreshold
					Integer32
			 }

		-- *******.4.1.6296.*********.1.1
		sleRmonSimplePortIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The port index."
			::= { sleRmonSimpleEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleRmonSimpleSampleVariable OBJECT-TYPE
			SYNTAX INTEGER
				{
				crcAlignError(1),
				jabber(2),
				oversizePackets(3),
				undersizePackets(4),
				fragments(5),
				dropEvents(6),
				collisions(7),
				ifInDiscards(8),
				ifInErrors(9),
				ifOutDiscards(10),
				ifOutErrors(11),
				ifInPauseFrame(12),
				ifOutPauseFrame(13)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The sample variable type."
			::= { sleRmonSimpleEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleRmonSimpleSampleInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The sample interval. (10: minimum)"
			::= { sleRmonSimpleEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleRmonSimpleRisingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The rising threshold."
			::= { sleRmonSimpleEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleRmonSimpleFallingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The falling threshold."
			::= { sleRmonSimpleEntry 5 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleRmonSimpleControl OBJECT IDENTIFIER ::= { sleRmonSimple 2 }

		
--   Node definitions
-- 
-- *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleRmonSimpleControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRmonSimple(1),
				delRmonSimple(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description"
			::= { sleRmonSimpleControl 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleRmonSimpleControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleRmonSimpleControl 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleRmonSimpleControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleRmonSimpleControl 3 }

		
--  *******.4.1.6296.*********.4
		-- *******.4.1.6296.*********.4
		sleRmonSimpleControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleRmonSimpleControl 4 }

		
--  *******.4.1.6296.*********.5
		-- *******.4.1.6296.*********.5
		sleRmonSimpleControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleRmonSimpleControl 5 }

		
--  *******.4.1.6296.*********.6
		-- *******.4.1.6296.*********.6
		sleRmonSimpleControlPortIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The port index."
			::= { sleRmonSimpleControl 6 }

		
--  *******.4.1.6296.*********.7
		-- *******.4.1.6296.*********.7
		sleRmonSimpleControlSampleVariable OBJECT-TYPE
			SYNTAX INTEGER
				{
				crcAlignError(1),
				jabber(2),
				oversizePackets(3),
				undersizePackets(4),
				fragments(5),
				dropEvents(6),
				collisions(7),
				ifInDiscards(8),
				ifInErrors(9),
				ifOutDiscards(10),
				ifOutErrors(11),
				ifInPauseFrame(12),
				ifOutPauseFrame(13),
				allTypes(255)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The sample variable type. (255: set all sample variable types)"
			::= { sleRmonSimpleControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleRmonSimpleControlSampleInterval OBJECT-TYPE
			SYNTAX Integer32 (10..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The sample interval.(10: minimum)"
			::= { sleRmonSimpleControl 8 }

		
--   send an SNMP trap
-- *******.4.1.6296.*********.8
		-- *******.4.1.6296.*********.9
		sleRmonSimpleControlRisingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The rising threshold."
			::= { sleRmonSimpleControl 9 }

		
--  *******.4.1.6296.*********.9
		-- *******.4.1.6296.*********.10
		sleRmonSimpleControlFallingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The falling threshold."
			::= { sleRmonSimpleControl 10 }

		
		-- *******.4.1.6296.*********
		sleRmonSimpleNotification OBJECT IDENTIFIER ::= { sleRmonSimple 3 }

		
		-- *******.4.1.6296.*********.1
		sleRmonSimpleCreated NOTIFICATION-TYPE
			OBJECTS { sleRmonSimpleControlRequest, sleRmonSimpleControlTimeStamp, sleRmonSimpleControlReqResult, sleRmonSimpleControlPortIndex, sleRmonSimpleControlSampleVariable, 
				sleRmonSimpleControlSampleInterval, sleRmonSimpleControlRisingThreshold, sleRmonSimpleControlFallingThreshold }
			STATUS current
			DESCRIPTION 
				"createRmonSimple"
			::= { sleRmonSimpleNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleRmonSimpleDeleted NOTIFICATION-TYPE
			OBJECTS { sleRmonSimpleControlRequest, sleRmonSimpleControlTimeStamp, sleRmonSimpleControlReqResult, sleRmonSimpleControlPortIndex, sleRmonSimpleControlSampleVariable
				 }
			STATUS current
			DESCRIPTION 
				"delRmonSimple"
			::= { sleRmonSimpleNotification 2 }

		
		-- *******.4.1.6296.101.9.6
		sleRmonSimpleQueue OBJECT IDENTIFIER ::= { sleRmon 6 }

		
		-- *******.4.1.6296.*********
		sleRmonSimpleQueueTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleRmonSimpleQueueEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleRmonSimpleQueue 1 }

		
		-- *******.4.1.6296.*********.1
		sleRmonSimpleQueueEntry OBJECT-TYPE
			SYNTAX SleRmonSimpleQueueEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleRmonSimpleQueuePortIndex, sleRmonSimpleQueueSampleVariable, sleRmonSimpleQueueQueueNumber }
			::= { sleRmonSimpleQueueTable 1 }

		
		SleRmonSimpleQueueEntry ::=
			SEQUENCE { 
				sleRmonSimpleQueuePortIndex
					Integer32,
				sleRmonSimpleQueueSampleVariable
					INTEGER,
				sleRmonSimpleQueueQueueNumber
					Integer32,
				sleRmonSimpleQueueSampleInterval
					Integer32,
				sleRmonSimpleQueueRisingThreshold
					Integer32,
				sleRmonSimpleQueueFallingThreshold
					Integer32
			 }

		-- *******.4.1.6296.*********.1.1
		sleRmonSimpleQueuePortIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The port index."
			::= { sleRmonSimpleQueueEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleRmonSimpleQueueSampleVariable OBJECT-TYPE
			SYNTAX INTEGER
				{
				transmitCount(1),
				dropCount(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The sample variable type."
			::= { sleRmonSimpleQueueEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleRmonSimpleQueueQueueNumber OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The queue number 0~7"
			::= { sleRmonSimpleQueueEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleRmonSimpleQueueSampleInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The sample interval. (10: minimum)"
			::= { sleRmonSimpleQueueEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleRmonSimpleQueueRisingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The rising threshold."
			::= { sleRmonSimpleQueueEntry 5 }

		
		-- *******.4.1.6296.*********.1.6
		sleRmonSimpleQueueFallingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The falling threshold."
			::= { sleRmonSimpleQueueEntry 6 }

		
--  *******.4.1.6296.*********
		-- *******.4.1.6296.*********
		sleRmonSimpleQueueControl OBJECT IDENTIFIER ::= { sleRmonSimpleQueue 2 }

		
--   Node definitions
-- 
-- *******.4.1.6296.*********.1
		-- *******.4.1.6296.*********.1
		sleRmonSimpleQueueControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRmonSimpleQueue(1),
				delRmonSimpleQueue(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description"
			::= { sleRmonSimpleQueueControl 1 }

		
--  *******.4.1.6296.*********.2
		-- *******.4.1.6296.*********.2
		sleRmonSimpleQueueControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of a user command. 
				"
			::= { sleRmonSimpleQueueControl 2 }

		
--  *******.4.1.6296.*********.3
		-- *******.4.1.6296.*********.3
		sleRmonSimpleQueueControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait time for the manager for a long running user command. 
				"
			::= { sleRmonSimpleQueueControl 3 }

		
--  *******.4.1.6296.*********.4
		-- *******.4.1.6296.*********.4
		sleRmonSimpleQueueControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time stamp of the last command (end of command)."
			::= { sleRmonSimpleQueueControl 4 }

		
--  *******.4.1.6296.*********.5
		-- *******.4.1.6296.*********.5
		sleRmonSimpleQueueControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { sleRmonSimpleQueueControl 5 }

		
--  *******.4.1.6296.*********.6
		-- *******.4.1.6296.*********.6
		sleRmonSimpleQueueControlPortIndex OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The port index."
			::= { sleRmonSimpleQueueControl 6 }

		
--  *******.4.1.6296.*********.7
		-- *******.4.1.6296.*********.7
		sleRmonSimpleQueueControlSampleVariable OBJECT-TYPE
			SYNTAX INTEGER
				{
				transmitCount(1),
				dropCount(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The sample variable type."
			::= { sleRmonSimpleQueueControl 7 }

		
--  *******.4.1.6296.*********.6
		-- *******.4.1.6296.*********.8
		sleRmonSimpleQueueControlQueueNumber OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The queue number 0~7"
			::= { sleRmonSimpleQueueControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleRmonSimpleQueueControlSampleInterval OBJECT-TYPE
			SYNTAX Integer32 (10..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The sample interval. (10: minimum)"
			::= { sleRmonSimpleQueueControl 9 }

		
--   send an SNMP trap
-- *******.4.1.6296.*********.8
		-- *******.4.1.6296.*********.10
		sleRmonSimpleQueueControlRisingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The rising threshold."
			::= { sleRmonSimpleQueueControl 10 }

		
--  *******.4.1.6296.*********.9
		-- *******.4.1.6296.*********.11
		sleRmonSimpleQueueControlFallingThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The falling threshold."
			::= { sleRmonSimpleQueueControl 11 }

		
		-- *******.4.1.6296.*********
		sleRmonSimpleQueueNotification OBJECT IDENTIFIER ::= { sleRmonSimpleQueue 3 }

		
		-- *******.4.1.6296.*********.1
		sleRmonSimpleQueueCreated NOTIFICATION-TYPE
			OBJECTS { sleRmonSimpleQueueControlRequest, sleRmonSimpleQueueControlTimeStamp, sleRmonSimpleQueueControlReqResult, sleRmonSimpleQueueControlPortIndex, sleRmonSimpleQueueControlSampleVariable, 
				sleRmonSimpleQueueControlQueueNumber, sleRmonSimpleQueueControlSampleInterval, sleRmonSimpleQueueControlRisingThreshold, sleRmonSimpleQueueControlFallingThreshold }
			STATUS current
			DESCRIPTION 
				"createRmonSimpleQueue"
			::= { sleRmonSimpleQueueNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleRmonSimpleQueueDeleted NOTIFICATION-TYPE
			OBJECTS { sleRmonSimpleQueueControlRequest, sleRmonSimpleQueueControlTimeStamp, sleRmonSimpleQueueControlReqResult, sleRmonSimpleQueueControlPortIndex, sleRmonSimpleQueueControlSampleVariable, 
				sleRmonSimpleQueueControlQueueNumber }
			STATUS current
			DESCRIPTION 
				"createRmonSimpleQueue"
			::= { sleRmonSimpleQueueNotification 2 }

		
--  *******.4.1.6296.101.9.5
		-- *******.4.1.6296.101.9.7
		sleRmonGroup OBJECT-GROUP
			OBJECTS { sleHistoryIndex, sleHistoryDataSource, sleHistoryBucketsRequested, sleHistoryBucketsGranted, sleHistoryInterval, 
				sleHistoryOwner, sleHistoryStatus, sleHistoryControlRequest, sleHistoryControlStatus, sleHistoryControlTimer, 
				sleHistoryControlTimeStamp, sleHistoryControlReqResult, sleHistoryControlIndex, sleHistoryControlDataSource, sleHistoryControlBucketsRequested, 
				sleHistoryControlInterval, sleHistoryControlOwner, sleHistoryControlSts, sleAlarmIndex, sleAlarmInterval, 
				sleAlarmVariable, sleAlarmSampleType, sleAlarmValue, sleAlarmStartupAlarm, sleAlarmRisingThreshold, 
				sleAlarmFallingThreshold, sleAlarmRisingEventIndex, sleAlarmFallingEventIndex, sleAlarmOwner, sleAlarmStatus, 
				sleAlarmControlRequest, sleAlarmControlStatus, sleAlarmControlTimer, sleAlarmControlTimeStamp, sleAlarmControlReqResult, 
				sleAlarmControlIndex, sleAlarmControlInterval, sleAlarmControlVariable, sleAlarmControlSampleType, sleAlarmControlStartupAlarm, 
				sleAlarmControlRisingThreshold, sleAlarmControlFallingThreshold, sleAlarmControlRisingEventIndex, sleAlarmControlFallingEventIndex, sleAlarmControlOwner, 
				sleAlarmControlSts, sleEventIndex, sleEventDescription, sleEventType, sleEventCommunity, 
				sleEventLastTimeSent, sleEventOwner, sleEventStatus, sleEventControlRequest, sleEventControlStatus, 
				sleEventControlTimer, sleEventControlTimeStamp, sleEventControlReqResult, sleEventControlIndex, sleEventControlDescription, 
				sleEventControlType, sleEventControlCommunity, sleEventControlOwner, sleEventControlSts, sleEtherStatsIndex, 
				sleEtherStatsDataSource, sleEtherStatsDropEvents, sleEtherStatsOctets, sleEtherStatsPkts, sleEtherStatsBroadcastPkts, 
				sleEtherStatsMulticastPkts, sleEtherStatsCRCAlignErrors, sleEtherStatsUndersizePkts, sleEtherStatsOversizePkts, sleEtherStatsFragments, 
				sleEtherStatsJabbers, sleEtherStatsCollisions, sleEtherStatsPkts64Octets, sleEtherStatsPkts65to127Octets, sleEtherStatsPkts128to255Octets, 
				sleEtherStatsPkts256to511Octets, sleEtherStatsPkts512to1023Octets, sleEtherStatsPkts1024to1518Octets, sleEtherStatsOwner, sleEtherStatsStatus, 
				sleEtherStatsClearedTime, sleRmonSimplePortIndex, sleRmonSimpleSampleVariable, sleRmonSimpleSampleInterval, sleRmonSimpleRisingThreshold, 
				sleRmonSimpleFallingThreshold, sleRmonSimpleControlRequest, sleRmonSimpleControlStatus, sleRmonSimpleControlTimer, sleRmonSimpleControlTimeStamp, 
				sleRmonSimpleControlReqResult, sleRmonSimpleControlPortIndex, sleRmonSimpleControlSampleVariable, sleRmonSimpleControlRisingThreshold, sleRmonSimpleControlFallingThreshold, 
				sleRmonSimpleControlSampleInterval, sleRmonSimpleQueuePortIndex, sleRmonSimpleQueueQueueNumber, sleRmonSimpleQueueSampleVariable, sleRmonSimpleQueueSampleInterval, 
				sleRmonSimpleQueueRisingThreshold, sleRmonSimpleQueueFallingThreshold, sleRmonSimpleQueueControlRequest, sleRmonSimpleQueueControlStatus, sleRmonSimpleQueueControlTimer, 
				sleRmonSimpleQueueControlTimeStamp, sleRmonSimpleQueueControlReqResult, sleRmonSimpleQueueControlPortIndex, sleEtherStatsControlRequest, sleEtherStatsControlStatus, 
				sleEtherStatsControlTimer, sleEtherStatsControlTimeStamp, sleEtherStatsControlReqResult, sleEtherStatsControlIfIndex, sleRmonSimpleQueueControlQueueNumber, 
				sleRmonSimpleQueueControlSampleVariable, sleRmonSimpleQueueControlSampleInterval, sleRmonSimpleQueueControlRisingThreshold, sleRmonSimpleQueueControlFallingThreshold }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRmon 7 }

		
--  *******.4.1.6296.101.9.6
		-- *******.4.1.6296.101.9.8
		sleRmonNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleHistoryCreated, sleHistoryDestroyed, sleHistoryProfileChanged, sleAlarmCreated, sleAlarmDestroyed, 
				sleAlarmProfileChanged, sleEventCreated, sleEventDestroyed, sleEventProfileChanged, sleRmonSimpleQueueCreated, 
				sleRmonSimpleQueueDeleted, sleRmonSimpleDeleted, sleRmonSimpleCreated }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleRmon 8 }

		
	
	END

--
-- sle-rmon-mib.mib
--
