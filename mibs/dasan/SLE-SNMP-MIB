--
-- sle-snmp-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, November 05, 2015 at 16:36:46
--

	SLE-SNMP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>32, OBJECT-TYPE, 
			MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.8
		sleSnmp MODULE-IDENTITY 
			LAST-UPDATED "200412092259Z"		-- December 09, 2004 at 22:59 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleMgmt 8 }

		
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.8.1
		sleSnmpBase OBJECT IDENTIFIER ::= { sleSnmp 1 }

		
		-- *******.4.1.6296.*********
		sleSnmpBaseInfo OBJECT IDENTIFIER ::= { sleSnmpBase 1 }

		
		-- *******.4.1.6296.*********.1
		sleSnmpSysContact OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseInfo 1 }

		
		-- *******.4.1.6296.*********.2
		sleSnmpSysLocation OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseInfo 2 }

		
		-- *******.4.1.6296.*********.3
		sleSnmpLogStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of setrequest user-history"
			::= { sleSnmpBaseInfo 3 }

		
		-- *******.4.1.6296.*********.4
		sleSnmpConnectionType OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				lct(2),
				tmn(3),
				unknown(255)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The connection type."
			::= { sleSnmpBaseInfo 4 }

		
		-- *******.4.1.6296.*********.5
		sleSnmpInformTrapRetry OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The inform trap configuration - retry
				default:5 (times)"
			::= { sleSnmpBaseInfo 5 }

		
		-- *******.4.1.6296.*********.6
		slesnmpInformTrapInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The inform trap configuration - interval
				default:3 (sec)
				"
			::= { sleSnmpBaseInfo 6 }

		
		-- *******.4.1.6296.*********.7
		sleSnmpTrapModeStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				event(0),
				alarmReport(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The SNMP trap mode
				1: event
				2: alarm-report"
			::= { sleSnmpBaseInfo 7 }

		
		-- *******.4.1.6296.*********
		sleSnmpBaseControl OBJECT IDENTIFIER ::= { sleSnmpBase 2 }

		
		-- *******.4.1.6296.*********.1
		sleSnmpControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSnmpSysInfoProfile(1),
				setSnmpLogStatus(2),
				setSnmpConnectionType(3),
				setInformTrapConf(4),
				setSnmpTrapMode(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleSnmpControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleSnmpControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleSnmpControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleSnmpControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleSnmpBaseControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleSnmpControlSysContact OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleSnmpControlSysLocation OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpBaseControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleSnmpControlLogStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The status of setrequest user-history"
			::= { sleSnmpBaseControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleSnmpControlConnectionType OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				lct(2),
				tmn(3),
				unknown(255)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The connection type."
			::= { sleSnmpBaseControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleSnmpControlInformTrapRetry OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The inform trap configuration - retry
				default:5 (times)"
			::= { sleSnmpBaseControl 10 }

		
		-- *******.4.1.6296.*********.11
		sleSnmpControlInformTrapInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The inform trap configuration - interval
				default:3 (sec)
				"
			::= { sleSnmpBaseControl 11 }

		
		-- *******.4.1.6296.*********.12
		sleSnmpControlTrapModeStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				event(0),
				alarmReport(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The SNMP trap mode
				1: event
				2: alarm-report"
			::= { sleSnmpBaseControl 12 }

		
		-- *******.4.1.6296.*********
		sleSnmpBaseNotification OBJECT IDENTIFIER ::= { sleSnmpBase 3 }

		
		-- *******.4.1.6296.*********.1
		sleSnmpSysInfoProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpControlRequest, sleSnmpControlReqResult, sleSnmpControlSysContact, sleSnmpControlSysLocation, sleSnmpControlTimeStamp
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpBaseNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleSnmpSysInfoLogStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpControlRequest, sleSnmpControlTimeStamp, sleSnmpControlReqResult, sleSnmpControlLogStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpBaseNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleSnmpSysInfoConnectionTypeChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpControlRequest, sleSnmpControlTimeStamp, sleSnmpControlReqResult, sleSnmpControlConnectionType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpBaseNotification 3 }

		
		-- *******.4.1.6296.*********.4
		sleSnmpInformTrapConfChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpControlRequest, sleSnmpControlTimeStamp, sleSnmpControlReqResult, sleSnmpControlInformTrapRetry, sleSnmpControlInformTrapInterval
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpBaseNotification 4 }

		
		-- *******.4.1.6296.*********.5
		sleSnmpTrapModeStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpControlRequest, sleSnmpControlTimeStamp, sleSnmpControlReqResult, sleSnmpControlTrapModeStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpBaseNotification 5 }

		
-- ----------------
--  sleCom2Sec
		-- *******.4.1.6296.101.8.2
		sleCom2Sec OBJECT IDENTIFIER ::= { sleSnmp 2 }

		
		-- *******.4.1.6296.*********
		sleCom2SecTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleCom2SecEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2Sec 1 }

		
		-- *******.4.1.6296.*********.1
		sleCom2SecEntry OBJECT-TYPE
			SYNTAX SleCom2SecEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleCom2SecName }
			::= { sleCom2SecTable 1 }

		
		SleCom2SecEntry ::=
			SEQUENCE { 
				sleCom2SecName
					OCTET STRING,
				sleCom2SecHost
					IpAddress,
				sleCom2SecHostMaskLen
					Integer32,
				sleCom2SecCommunity
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleCom2SecName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Com2sec Name"
			::= { sleCom2SecEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleCom2SecHost OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Accessable host address"
			::= { sleCom2SecEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleCom2SecHostMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Mask length for Host address.
				If sleSNMPCom2secHost is network address, 
				sleSNMPCom2secHostMaskLen is 0.
				"
			::= { sleCom2SecEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleCom2SecCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Community"
			::= { sleCom2SecEntry 4 }

		
		-- *******.4.1.6296.*********
		sleCom2SecControl OBJECT IDENTIFIER ::= { sleCom2Sec 2 }

		
		-- *******.4.1.6296.*********.1
		sleCom2SecControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createCom2Sec(1),
				destroyCom2Sec(2),
				setCom2SecProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2SecControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleCom2SecControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2SecControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleCom2SecControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2SecControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleCom2SecControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2SecControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleCom2SecControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command"
			::= { sleCom2SecControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleCom2SecControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2SecControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleCom2SecControlHost OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Accessable host address"
			::= { sleCom2SecControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleCom2secControlHostMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2SecControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleCom2SecControlCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCom2SecControl 9 }

		
		-- *******.4.1.6296.*********
		sleCom2SecNotification OBJECT IDENTIFIER ::= { sleCom2Sec 3 }

		
		-- *******.4.1.6296.*********.1
		sleCom2SecCreated NOTIFICATION-TYPE
			OBJECTS { sleCom2SecControlRequest, sleCom2SecControlReqResult, sleCom2SecControlTimeStamp, sleCom2SecHost, sleCom2SecHostMaskLen, 
				sleCom2SecCommunity }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleCom2SecNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleCom2SecDestroyed NOTIFICATION-TYPE
			OBJECTS { sleCom2SecControlRequest, sleCom2SecControlReqResult, sleCom2SecControlTimeStamp, sleCom2SecControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleCom2SecNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleCom2SecProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleCom2SecControlRequest, sleCom2SecControlReqResult, sleCom2SecControlTimeStamp, sleCom2SecHost, sleCom2SecHostMaskLen, 
				sleCom2SecCommunity }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleCom2SecNotification 3 }

		
-- 
--  sleTrapHost
		-- *******.4.1.6296.101.8.3
		sleTrapHost OBJECT IDENTIFIER ::= { sleSnmp 3 }

		
		-- *******.4.1.6296.*********
		sleTrapHostTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleTrapHostEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHost 1 }

		
		-- *******.4.1.6296.*********.1
		sleTrapHostEntry OBJECT-TYPE
			SYNTAX SleTrapHostEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleTrapHostType, sleTrapHostAddress }
			::= { sleTrapHostTable 1 }

		
		SleTrapHostEntry ::=
			SEQUENCE { 
				sleTrapHostType
					INTEGER,
				sleTrapHostAddress
					IpAddress,
				sleTrapHostCommunity
					OCTET STRING,
				sleTrapHostPort
					Integer32,
				sleTrapHostVrfName
					OCTET STRING,
				sleTrapHostUser
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleTrapHostType OBJECT-TYPE
			SYNTAX INTEGER
				{
				trapHost(1),
				trap2Host(2),
				informTrapHost(3),
				trap3Host(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleTrapHostAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Host address for received traps"
			::= { sleTrapHostEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleTrapHostCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Trap commnity"
			::= { sleTrapHostEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleTrapHostPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Trap commnunication port. (advanced)"
			::= { sleTrapHostEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleTrapHostVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The SNMP trap host VRF name."
			::= { sleTrapHostEntry 5 }

		
		-- *******.4.1.6296.*********.1.6
		sleTrapHostUser OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Trap host user ,for snmp version 3 trap."
			::= { sleTrapHostEntry 6 }

		
		-- *******.4.1.6296.*********
		sleTrapHostControl OBJECT IDENTIFIER ::= { sleTrapHost 2 }

		
		-- *******.4.1.6296.*********.1
		sleTrapHostControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createTrapHost(1),
				destroyTrapHost(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"setInformTrapConf"
			::= { sleTrapHostControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleTrapHostControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleTrapHostControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleTrapHostControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleTrapHostControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleTrapHostControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				trapHost(1),
				trap2Host(2),
				informTrapHost(3),
				trap3Host(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleTrapHostControlAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Host address for received traps"
			::= { sleTrapHostControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleTrapHostControlCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleTrapHostControlPort OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleTrapHostControlVrfName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The SNMP trap host VRF name."
			::= { sleTrapHostControl 10 }

		
		-- *******.4.1.6296.*********.11
		sleTrapHostControlUser OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Trap host user ,for snmp version 3 trap."
			::= { sleTrapHostControl 11 }

		
		-- *******.4.1.6296.*********
		sleTrapHostNotification OBJECT IDENTIFIER ::= { sleTrapHost 3 }

		
		-- *******.4.1.6296.*********.1
		sleTrapHostCreated NOTIFICATION-TYPE
			OBJECTS { sleTrapHostControlRequest, sleTrapHostControlReqResult, sleTrapHostControlTimeStamp, sleTrapHostCommunity }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleTrapHostNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleTrapHostDestroyed NOTIFICATION-TYPE
			OBJECTS { sleTrapHostControlRequest, sleTrapHostControlReqResult, sleTrapHostControlTimeStamp, sleTrapHostControlType, sleTrapHostControlAddress
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleTrapHostNotification 2 }

		
-- 
--  sleCommunity
		-- *******.4.1.6296.101.8.4
		sleCommunity OBJECT IDENTIFIER ::= { sleSnmp 4 }

		
		-- *******.4.1.6296.*********
		sleCommunityTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleCommunityEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCommunity 1 }

		
		-- *******.4.1.6296.*********.1
		sleCommunityEntry OBJECT-TYPE
			SYNTAX SleCommunityEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleCommunityType, sleCommunityValue }
			::= { sleCommunityTable 1 }

		
		SleCommunityEntry ::=
			SEQUENCE { 
				sleCommunityType
					INTEGER,
				sleCommunityValue
					OCTET STRING,
				sleCommunityHost
					IpAddress,
				sleCommunityOID
					OBJECT IDENTIFIER,
				sleCommunityHostMaskLen
					Integer32
			 }

		-- *******.4.1.6296.*********.1.1
		sleCommunityType OBJECT-TYPE
			SYNTAX INTEGER
				{
				readonly(1),
				readwrite(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Community Type"
			::= { sleCommunityEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleCommunityValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Community key"
			::= { sleCommunityEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleCommunityHost OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Accessable host address"
			::= { sleCommunityEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleCommunityOID OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Accessable Object ID"
			::= { sleCommunityEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleCommunityHostMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Mask length for host address"
			::= { sleCommunityEntry 5 }

		
		-- *******.4.1.6296.*********
		sleCommunityControl OBJECT IDENTIFIER ::= { sleCommunity 2 }

		
		-- *******.4.1.6296.*********.1
		sleCommunityControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createCommunity(1),
				destroyCommunity(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCommunityControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleCommunityControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCommunityControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleCommunityControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCommunityControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleCommunityControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleCommunityControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleCommunityControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command"
			::= { sleCommunityControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleCommunityControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				readonly(1),
				readwrite(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Community Type"
			::= { sleCommunityControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleCommunityControlValue OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Community key"
			::= { sleCommunityControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleCommunityControlHost OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Accessable host address"
			::= { sleCommunityControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleCommunityControlOID OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Accessable Object ID"
			::= { sleCommunityControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleCommunityControlHostMaskLen OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Mask length for host address"
			::= { sleCommunityControl 10 }

		
		-- *******.4.1.6296.*********
		sleCommunityNotification OBJECT IDENTIFIER ::= { sleCommunity 3 }

		
		-- *******.4.1.6296.*********.1
		sleCommunityCreated NOTIFICATION-TYPE
			OBJECTS { sleCommunityControlRequest, sleCommunityControlReqResult, sleCommunityControlTimeStamp, sleCommunityHostMaskLen, sleCommunityHost, 
				sleCommunityOID }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleCommunityNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleCommunityDestroyed NOTIFICATION-TYPE
			OBJECTS { sleCommunityControlRequest, sleCommunityControlReqResult, sleCommunityControlTimeStamp, sleCommunityControlType, sleCommunityControlValue
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleCommunityNotification 2 }

		
-- 
--  sleViewTreeFamily
		-- *******.4.1.6296.101.8.5
		sleViewTreeFamily OBJECT IDENTIFIER ::= { sleSnmp 5 }

		
		-- *******.4.1.6296.*********
		sleViewTreeFamilyTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleViewTreeFamilyEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamily 1 }

		
		-- *******.4.1.6296.*********.1
		sleViewTreeFamilyEntry OBJECT-TYPE
			SYNTAX SleViewTreeFamilyEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleViewTreeFamilyName, sleViewTreeFamilySubtree }
			::= { sleViewTreeFamilyTable 1 }

		
		SleViewTreeFamilyEntry ::=
			SEQUENCE { 
				sleViewTreeFamilyName
					OCTET STRING,
				sleViewTreeFamilySubtree
					OBJECT IDENTIFIER,
				sleViewTreeFamilyMask
					OCTET STRING,
				sleViewTreeFamilyType
					INTEGER
			 }

		-- *******.4.1.6296.*********.1.1
		sleViewTreeFamilyName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"View Name"
			::= { sleViewTreeFamilyEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleViewTreeFamilySubtree OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The MIB subtree which when combined with 
				the corresponding instance of ViewTreeFamilyMask 
				defines a family of view subtrees."
			::= { sleViewTreeFamilyEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleViewTreeFamilyMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The bit mask which, in combination with 
				the corresponding instance of ViewTreeFamilySubtree,
				defines a family of view subtrees."
			::= { sleViewTreeFamilyEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleViewTreeFamilyType OBJECT-TYPE
			SYNTAX INTEGER
				{
				included(1),
				excluded(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates whether the corresponding
				instances of ViewTreeFamilySubtree and ViewTreeFamilyMask
				define a family of view subtrees which is included in or 
				excluded from the MIB view."
			::= { sleViewTreeFamilyEntry 4 }

		
		-- *******.4.1.6296.*********
		sleViewTreeFamilyControl OBJECT IDENTIFIER ::= { sleViewTreeFamily 2 }

		
		-- *******.4.1.6296.*********.1
		sleViewTreeFamilyControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createViewTreeFamily(1),
				destroyViewTreeFamily(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleViewTreeFamilyControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleViewTreeFamilyControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleViewTreeFamilyControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleViewTreeFamilyControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleViewTreeFamilyControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"View Name"
			::= { sleViewTreeFamilyControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleViewTreeFamilyControlSubtree OBJECT-TYPE
			SYNTAX OBJECT IDENTIFIER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleViewTreeFamilyControlMask OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleViewTreeFamilyControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				included(1),
				excluded(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleViewTreeFamilyControl 9 }

		
		-- *******.4.1.6296.*********
		sleViewTreeFamilyNotification OBJECT IDENTIFIER ::= { sleViewTreeFamily 3 }

		
		-- *******.4.1.6296.*********.1
		sleViewTreeFamilyCreated NOTIFICATION-TYPE
			OBJECTS { sleViewTreeFamilyControlRequest, sleViewTreeFamilyControlReqResult, sleViewTreeFamilyControlTimeStamp, sleViewTreeFamilyMask, sleViewTreeFamilyType
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleViewTreeFamilyNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleViewTreeFamilyDestroyed NOTIFICATION-TYPE
			OBJECTS { sleViewTreeFamilyControlRequest, sleViewTreeFamilyControlReqResult, sleViewTreeFamilyControlTimeStamp, sleViewTreeFamilyControlName, sleViewTreeFamilyControlSubtree
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleViewTreeFamilyNotification 2 }

		
-- 
--  sleAccess
		-- *******.4.1.6296.101.8.6
		sleAccess OBJECT IDENTIFIER ::= { sleSnmp 6 }

		
		-- *******.4.1.6296.*********
		sleAccessTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleAccessEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAccess 1 }

		
		-- *******.4.1.6296.*********.1
		sleAccessEntry OBJECT-TYPE
			SYNTAX SleAccessEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleAccessGroupName }
			::= { sleAccessTable 1 }

		
		SleAccessEntry ::=
			SEQUENCE { 
				sleAccessGroupName
					OCTET STRING,
				sleAccessSecurityModel
					INTEGER,
				sleAccessSecurityLevel
					INTEGER,
				sleAccessReadViewName
					OCTET STRING,
				sleAccessWriteViewName
					OCTET STRING,
				sleAccessNotifyViewName
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleAccessGroupName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The name of the group to which this entry belongs"
			::= { sleAccessEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleAccessSecurityModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				any(0),
				snmpV1(1),
				snmpV2(2),
				usm(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Security Model"
			::= { sleAccessEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleAccessSecurityLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				noAuthNoPrivn(1),
				authNoPriv(2),
				authPriv(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Security Level"
			::= { sleAccessEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleAccessReadViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of an instance of this object
				identifies the MIB view of the SNMP context to which 
				this conceptual row authorizes read access."
			::= { sleAccessEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleAccessWriteViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of an instance of this object identifies 
				the MIB view of the SNMP context to which 
				this conceptual row authorizes write access."
			::= { sleAccessEntry 5 }

		
		-- *******.4.1.6296.*********.1.6
		sleAccessNotifyViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of an instance of this object identifies
				the MIB view of the SNMP context to which this 
				conceptual row authorizes access for notifications."
			::= { sleAccessEntry 6 }

		
		-- *******.4.1.6296.*********
		sleAccessControl OBJECT IDENTIFIER ::= { sleAccess 2 }

		
		-- *******.4.1.6296.*********.1
		sleAccessControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createAccess(1),
				destroyAccess(2),
				setAccessProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAccessControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleAccessControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAccessControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleAccessControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAccessControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleAccessControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAccessControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleAccessControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAccessControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleAccessControlGroupName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAccessControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleAccessControlSecurityModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				any(0),
				snmpV1(1),
				snmpV2(2),
				usm(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Security Model"
			::= { sleAccessControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleAccessControlSecurityLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				noAuthNoPrivn(1),
				authNoPriv(2),
				authPriv(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Security Level"
			::= { sleAccessControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleAccessControlReadViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value of an instance of this object identifies
				the MIB view of the SNMP context to which this 
				conceptual row authorizes read access."
			::= { sleAccessControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleAccessControlWriteViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value of an instance of this object identifies 
				the MIB view of the SNMP context to which this 
				conceptual row authorizes write access."
			::= { sleAccessControl 10 }

		
		-- *******.4.1.6296.*********.11
		sleAccessControlNotifyViewName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The value of an instance of this object identifies 
				the MIB view of the SNMP context to which this 
				conceptual row authorizes access for notifications."
			::= { sleAccessControl 11 }

		
		-- *******.4.1.6296.*********
		sleAccessNotification OBJECT IDENTIFIER ::= { sleAccess 3 }

		
		-- *******.4.1.6296.*********.1
		sleAccessCreated NOTIFICATION-TYPE
			OBJECTS { sleAccessControlRequest, sleAccessControlReqResult, sleAccessControlTimeStamp, sleAccessSecurityModel, sleAccessSecurityLevel, 
				sleAccessReadViewName, sleAccessWriteViewName, sleAccessNotifyViewName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleAccessNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleAccessDestroyed NOTIFICATION-TYPE
			OBJECTS { sleAccessControlRequest, sleAccessControlReqResult, sleAccessControlTimeStamp, sleAccessControlGroupName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleAccessNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleAccessProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleAccessControlRequest, sleAccessControlReqResult, sleAccessControlTimeStamp, sleAccessSecurityModel, sleAccessSecurityLevel, 
				sleAccessReadViewName, sleAccessWriteViewName, sleAccessNotifyViewName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleAccessNotification 3 }

		
-- 
--  sleSecurityToGroup
		-- *******.4.1.6296.101.8.7
		sleSecurityToGroup OBJECT IDENTIFIER ::= { sleSnmp 7 }

		
		-- *******.4.1.6296.*********
		sleSecurityToGroupTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSecurityToGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSecurityToGroup 1 }

		
		-- *******.4.1.6296.*********.1
		sleSecurityToGroupEntry OBJECT-TYPE
			SYNTAX SleSecurityToGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSecurityToGroupSName, sleSecurityToGroupModel }
			::= { sleSecurityToGroupTable 1 }

		
		SleSecurityToGroupEntry ::=
			SEQUENCE { 
				sleSecurityToGroupSName
					OCTET STRING,
				sleSecurityToGroupModel
					INTEGER,
				sleSecurityToGroupGName
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleSecurityToGroupSName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The securityName for the principal, 
				which is mapped by this entry to a groupName."
			::= { sleSecurityToGroupEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleSecurityToGroupModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				any(0),
				snmpV1(1),
				snmpV2(2),
				usm(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Security Model"
			::= { sleSecurityToGroupEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleSecurityToGroupGName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The name of the group to which this entry belongs."
			::= { sleSecurityToGroupEntry 3 }

		
		-- *******.4.1.6296.*********
		sleSecurityToGroupControl OBJECT IDENTIFIER ::= { sleSecurityToGroup 2 }

		
		-- *******.4.1.6296.*********.1
		sleSecurityToGroupControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSecurityToGroup(1),
				destroySecurityToGroup(2),
				setSecurityToGroupProfile(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSecurityToGroupControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleSecurityToGroupControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSecurityToGroupControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleSecurityToGroupControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSecurityToGroupControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleSecurityToGroupControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Time stamp attribute set at the end of a command"
			::= { sleSecurityToGroupControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleSecurityToGroupControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSecurityToGroupControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleSecurityToGroupControlGName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The name of the group to which this entry belongs."
			::= { sleSecurityToGroupControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleSecurityToGroupControlModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				any(0),
				snmpV1(1),
				snmpV2(2),
				usm(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSecurityToGroupControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleSecurityToGroupControlSName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The securityName for the principal, 
				which is mapped by this entry to a groupName."
			::= { sleSecurityToGroupControl 8 }

		
		-- *******.4.1.6296.*********
		sleSecurityToGroupNotification OBJECT IDENTIFIER ::= { sleSecurityToGroup 3 }

		
		-- *******.4.1.6296.*********.1
		sleSecurityToGroupCreated NOTIFICATION-TYPE
			OBJECTS { sleSecurityToGroupControlRequest, sleSecurityToGroupControlReqResult, sleSecurityToGroupControlTimeStamp, sleSecurityToGroupSName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSecurityToGroupNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleSecurityToGroupDestroyed NOTIFICATION-TYPE
			OBJECTS { sleSecurityToGroupControlRequest, sleSecurityToGroupControlReqResult, sleSecurityToGroupControlTimeStamp, sleSecurityToGroupControlGName, sleSecurityToGroupControlModel
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSecurityToGroupNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleSecurityToGroupProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleSecurityToGroupControlTimeStamp, sleSecurityToGroupControlReqResult, sleSecurityToGroupControlRequest, sleSecurityToGroupSName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSecurityToGroupNotification 3 }

		
-- 
--  sleUser
		-- *******.4.1.6296.101.8.8
		sleUser OBJECT IDENTIFIER ::= { sleSnmp 8 }

		
		-- *******.4.1.6296.*********
		sleUserTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUser 1 }

		
		-- *******.4.1.6296.*********.1
		sleUserEntry OBJECT-TYPE
			SYNTAX SleUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleUserName }
			::= { sleUserTable 1 }

		
		SleUserEntry ::=
			SEQUENCE { 
				sleUserName
					OCTET STRING,
				sleUserAuthType
					INTEGER,
				sleUserAuthKey
					OCTET STRING,
				sleUserPrivType
					INTEGER,
				sleUserPrivKey
					OCTET STRING
			 }

		-- *******.4.1.6296.*********.1.1
		sleUserName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"User name"
			::= { sleUserEntry 1 }

		
		-- *******.4.1.6296.*********.1.2
		sleUserAuthType OBJECT-TYPE
			SYNTAX INTEGER
				{
				md5(1),
				sha(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Authentication type"
			::= { sleUserEntry 2 }

		
		-- *******.4.1.6296.*********.1.3
		sleUserAuthKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Authentication key (length >=8)"
			::= { sleUserEntry 3 }

		
		-- *******.4.1.6296.*********.1.4
		sleUserPrivType OBJECT-TYPE
			SYNTAX INTEGER
				{
				noPrivacy(1),
				des(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Privacy type"
			::= { sleUserEntry 4 }

		
		-- *******.4.1.6296.*********.1.5
		sleUserPrivKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Privacy key (length >=8)"
			::= { sleUserEntry 5 }

		
		-- *******.4.1.6296.*********
		sleUserControl OBJECT IDENTIFIER ::= { sleUser 2 }

		
		-- *******.4.1.6296.*********.1
		sleUserControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createUser(1),
				destroyUser(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleUserControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleUserControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleUserControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleUserControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleUserControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleUserControlName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"User name"
			::= { sleUserControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleUserControlAuthType OBJECT-TYPE
			SYNTAX INTEGER
				{
				md5(1),
				sha(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Authentication type"
			::= { sleUserControl 7 }

		
		-- *******.4.1.6296.*********.8
		sleUserControlAuthKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Authentication key (length >=8)"
			::= { sleUserControl 8 }

		
		-- *******.4.1.6296.*********.9
		sleUserControlPrivType OBJECT-TYPE
			SYNTAX INTEGER
				{
				noPrivacy(1),
				des(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Privacy type"
			::= { sleUserControl 9 }

		
		-- *******.4.1.6296.*********.10
		sleUserControlPrivKey OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Privacy key (length >=8)"
			::= { sleUserControl 10 }

		
		-- *******.4.1.6296.*********
		sleUserNotification OBJECT IDENTIFIER ::= { sleUser 3 }

		
		-- *******.4.1.6296.*********.1
		sleUserCreated NOTIFICATION-TYPE
			OBJECTS { sleUserControlRequest, sleUserControlReqResult, sleUserControlTimeStamp, sleUserAuthType, sleUserAuthKey, 
				sleUserPrivType, sleUserPrivKey }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleUserNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleUserDestroyed NOTIFICATION-TYPE
			OBJECTS { sleUserControlRequest, sleUserControlReqResult, sleUserControlTimeStamp, sleUserControlName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleUserNotification 2 }

		
		-- *******.4.1.6296.101.8.9
		sleAgent OBJECT IDENTIFIER ::= { sleSnmp 9 }

		
		-- *******.4.1.6296.*********
		sleAgentInfo OBJECT IDENTIFIER ::= { sleAgent 1 }

		
		-- *******.4.1.6296.*********.1
		sleAgentAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp agent address
				If this value is setted, sleAgentInterface value is NULL
				"
			::= { sleAgentInfo 1 }

		
		-- *******.4.1.6296.*********.2
		sleAgentInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp agent interface
				If this value is setted, sleAgentAddress value is 0
				"
			::= { sleAgentInfo 2 }

		
		-- *******.4.1.6296.*********
		sleAgentControl OBJECT IDENTIFIER ::= { sleAgent 2 }

		
		-- *******.4.1.6296.*********.1
		sleAgentControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSnmpAgentAddress(1),
				setSnmpAgentInterface(2),
				destroySnmpAgentAddress(3),
				destroySnmpAgentInterface(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAgentControl 1 }

		
		-- *******.4.1.6296.*********.2
		sleAgentControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAgentControl 2 }

		
		-- *******.4.1.6296.*********.3
		sleAgentControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAgentControl 3 }

		
		-- *******.4.1.6296.*********.4
		sleAgentControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAgentControl 4 }

		
		-- *******.4.1.6296.*********.5
		sleAgentControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleAgentControl 5 }

		
		-- *******.4.1.6296.*********.6
		sleAgentControlAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"snmp agent address"
			::= { sleAgentControl 6 }

		
		-- *******.4.1.6296.*********.7
		sleAgentControlInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Snmp agent interface"
			::= { sleAgentControl 7 }

		
		-- *******.4.1.6296.*********
		sleAgentNotification OBJECT IDENTIFIER ::= { sleAgent 3 }

		
		-- *******.4.1.6296.*********.1
		sleAgentAddressChanged NOTIFICATION-TYPE
			OBJECTS { sleAgentControlRequest, sleAgentControlTimeStamp, sleAgentControlReqResult, sleAgentAddress }
			STATUS current
			DESCRIPTION 
				"Create agent address"
			::= { sleAgentNotification 1 }

		
		-- *******.4.1.6296.*********.2
		sleAgentInterfaceChanged NOTIFICATION-TYPE
			OBJECTS { sleAgentControlRequest, sleAgentControlTimeStamp, sleAgentControlReqResult, sleAgentInterface }
			STATUS current
			DESCRIPTION 
				"Create agent interface"
			::= { sleAgentNotification 2 }

		
		-- *******.4.1.6296.*********.3
		sleAgentAddressDestroyed NOTIFICATION-TYPE
			OBJECTS { sleAgentControlRequest, sleAgentControlTimeStamp, sleAgentControlReqResult }
			STATUS current
			DESCRIPTION 
				"Destroy agent address"
			::= { sleAgentNotification 3 }

		
		-- *******.4.1.6296.*********.4
		sleAgentInterfaceDestroyed NOTIFICATION-TYPE
			OBJECTS { sleAgentControlRequest, sleAgentControlTimeStamp, sleAgentControlReqResult }
			STATUS current
			DESCRIPTION 
				"Destroy agent interface"
			::= { sleAgentNotification 4 }

		
		-- *******.4.1.6296.101.8.10
		sleSnmpSesson OBJECT IDENTIFIER ::= { sleSnmp 10 }

		
		-- *******.4.1.6296.**********
		sleSnmpSessionInfo OBJECT IDENTIFIER ::= { sleSnmpSesson 1 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpSessionTimeout OBJECT-TYPE
			SYNTAX Integer32
			UNITS "sec"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp session timeout 
				
				"
			::= { sleSnmpSessionInfo 1 }

		
		-- *******.4.1.6296.**********
		sleSnmpSessionTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSnmpSessionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpSesson 2 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpSessionEntry OBJECT-TYPE
			SYNTAX SleSnmpSessionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSnmpSessionIpAddr, sleSnmpSessionCommunity }
			::= { sleSnmpSessionTable 1 }

		
		SleSnmpSessionEntry ::=
			SEQUENCE { 
				sleSnmpSessionIpAddr
					IpAddress,
				sleSnmpSessionCommunity
					OCTET STRING,
				sleSnmpSessionSnmpVersion
					Integer32,
				sleSnmpSessionAccessTIme
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleSnmpSessionIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp session ip address"
			::= { sleSnmpSessionEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSnmpSessionCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp session SNMP version"
			::= { sleSnmpSessionEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSnmpSessionSnmpVersion OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp session SNMP version"
			::= { sleSnmpSessionEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleSnmpSessionAccessTIme OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp session last access time"
			::= { sleSnmpSessionEntry 4 }

		
		-- *******.4.1.6296.**********
		sleSnmpSessiontControl OBJECT IDENTIFIER ::= { sleSnmpSesson 3 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpSessionControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setSnmpsessionTimeout(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpSessiontControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpSessionControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpSessiontControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSnmpSessionControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpSessiontControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSnmpSessionControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpSessiontControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSnmpSessionControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpSessiontControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSnmpSessionControlTimeout OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpSessiontControl 6 }

		
		-- *******.4.1.6296.**********
		sleSnmpSessionNotifications OBJECT IDENTIFIER ::= { sleSnmpSesson 4 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpSessionTimeoutChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpSessionControlRequest, sleSnmpSessionControlReqResult, sleSnmpSessionControlTimeStamp, sleSnmpSessionTimeout }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpSessionNotifications 1 }

		
		-- *******.4.1.6296.101.8.11
		sleSnmpTrap OBJECT IDENTIFIER ::= { sleSnmp 11 }

		
		-- *******.4.1.6296.**********
		sleSnmpTrapTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSnmpTrapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrap 1 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpTrapEntry OBJECT-TYPE
			SYNTAX SleSnmpTrapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSnmpTrapIndex }
			::= { sleSnmpTrapTable 1 }

		
		SleSnmpTrapEntry ::=
			SEQUENCE { 
				sleSnmpTrapIndex
					INTEGER,
				sleSnmpTrapStatus
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSnmpTrapIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				linkUp(1),
				linkDown(2),
				authFail(3),
				coldStart(4),
				cpuThreshold(5),
				portThreshold(6),
				power(7),
				module(8),
				fan(9),
				dhcpLease(10),
				temperThreshold(11),
				memoryThreshold(12),
				igmp(13),
				autoCli(14),
				autoReset(15),
				door(16),
				battery(17),
				epon(18),
				adminAccess(19),
				swWatchdog(20),
				cliHistory(50),
				commandLog(51)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSnmpTrapStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapEntry 2 }

		
		-- *******.4.1.6296.**********
		sleSnmpTrapPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSnmpTrapPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrap 2 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpTrapPortEntry OBJECT-TYPE
			SYNTAX SleSnmpTrapPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSnmpTrapPortIndex, sleSnmpTrapPortType }
			::= { sleSnmpTrapPortTable 1 }

		
		SleSnmpTrapPortEntry ::=
			SEQUENCE { 
				sleSnmpTrapPortIndex
					INTEGER,
				sleSnmpTrapPortType
					INTEGER,
				sleSnmpTrapPortStatus
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSnmpTrapPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..512)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapPortEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSnmpTrapPortType OBJECT-TYPE
			SYNTAX INTEGER
				{
				linkUp(1),
				linkDown(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapPortEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSnmpTrapPortStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapPortEntry 3 }

		
		-- *******.4.1.6296.**********
		sleSnmpTrapControl OBJECT IDENTIFIER ::= { sleSnmpTrap 3 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpTrapControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSnmpTrapStatus(1),
				setSnmpTrapStatusByport(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpTrapControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSnmpTrapControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSnmpTrapControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSnmpTrapControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSnmpTrapControlTrapIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				linkUp(1),
				linkDown(2),
				authFail(3),
				coldStart(4),
				cpuThreshold(5),
				portThreshold(6),
				power(7),
				module(8),
				fan(9),
				dhcpLease(10),
				temperThreshold(11),
				memoryThreshold(12),
				igmp(13),
				autoCli(14),
				autoReset(15),
				door(16),
				battery(17),
				epon(18),
				adminAccess(19),
				swWatchdog(20),
				cliHistory(50),
				commandLog(51)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSnmpTrapControlPortIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleSnmpTrapControlTrapStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapControl 8 }

		
		-- *******.4.1.6296.**********
		sleSnmpTrapNotification OBJECT IDENTIFIER ::= { sleSnmpTrap 4 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpTrapStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpTrapControlRequest, sleSnmpTrapControlReqResult, sleSnmpTrapControlTimeStamp, sleSnmpTrapControlTrapStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpTrapNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpTrapPortStatusChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpTrapControlRequest, sleSnmpTrapControlTimeStamp, sleSnmpTrapControlReqResult, sleSnmpTrapControlTrapStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpTrapNotification 2 }

		
		-- *******.4.1.6296.101.8.12
		sleSnmpTrapSource OBJECT IDENTIFIER ::= { sleSnmp 12 }

		
		-- *******.4.1.6296.**********
		sleSnmpTrapSourceInfo OBJECT IDENTIFIER ::= { sleSnmpTrapSource 1 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpTrapSourceAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp Trap Source address
				If this value is setted, sleTrapSourceInterface value is NULL
				"
			::= { sleSnmpTrapSourceInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpTrapSourceInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Snmp Trap Source interface
				If this value is setted, sleTrapSourceAddress value is 0
				"
			::= { sleSnmpTrapSourceInfo 2 }

		
		-- *******.4.1.6296.**********
		sleSnmpTrapSourceControl OBJECT IDENTIFIER ::= { sleSnmpTrapSource 2 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpTrapSourceControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSnmpTrapSourceAddress(1),
				setSnmpTrapSourceInterface(2),
				destroySnmpTrapSourceAddress(3),
				destroySnmpTrapSourceInterface(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapSourceControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpTrapSourceControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapSourceControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSnmpTrapSourceControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapSourceControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSnmpTrapSourceControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapSourceControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSnmpTrapSourceControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpTrapSourceControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSnmpTrapSourceControlAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"snmp trap source address"
			::= { sleSnmpTrapSourceControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSnmpTrapSourceControlInterface OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Snmp trap source interface"
			::= { sleSnmpTrapSourceControl 7 }

		
		-- *******.4.1.6296.**********
		sleSnmpTrapSourceNotification OBJECT IDENTIFIER ::= { sleSnmpTrapSource 3 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpTrapSourceAddressChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpTrapSourceControlRequest, sleSnmpTrapSourceControlReqResult, sleSnmpTrapSourceControlTimeStamp, sleSnmpTrapSourceAddress }
			STATUS current
			DESCRIPTION 
				"Create trap source address"
			::= { sleSnmpTrapSourceNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpTrapSourceInterfaceChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpTrapSourceControlRequest, sleSnmpTrapSourceControlReqResult, sleSnmpTrapSourceControlTimeStamp, sleSnmpTrapSourceInterface }
			STATUS current
			DESCRIPTION 
				"Create trap source interface"
			::= { sleSnmpTrapSourceNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleSnmpTrapSourcetAddressDestroyed NOTIFICATION-TYPE
			OBJECTS { sleSnmpTrapSourceControlRequest, sleSnmpTrapSourceControlTimeStamp, sleSnmpTrapSourceControlReqResult }
			STATUS current
			DESCRIPTION 
				"Destroy trap source address"
			::= { sleSnmpTrapSourceNotification 3 }

		
		-- *******.4.1.6296.**********.4
		sleSnmpTrapSourceInterfaceDestroyed NOTIFICATION-TYPE
			OBJECTS { sleSnmpTrapSourceControlRequest, sleSnmpTrapSourceControlTimeStamp, sleSnmpTrapSourceControlReqResult }
			STATUS current
			DESCRIPTION 
				"Destroy trap source interface"
			::= { sleSnmpTrapSourceNotification 4 }

		
		-- *******.4.1.6296.101.8.13
		sleSnmpLog OBJECT IDENTIFIER ::= { sleSnmp 13 }

		
		-- *******.4.1.6296.**********
		sleSnmpLogTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSnmpLogEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpLog 1 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpLogEntry OBJECT-TYPE
			SYNTAX SleSnmpLogEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSnmpLogIndex }
			::= { sleSnmpLogTable 1 }

		
		SleSnmpLogEntry ::=
			SEQUENCE { 
				sleSnmpLogIndex
					INTEGER,
				sleSnmpLogText
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1
		sleSnmpLogIndex OBJECT-TYPE
			SYNTAX INTEGER (1..2000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The index of SNMP Trap Log"
			::= { sleSnmpLogEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSnmpLogText OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Textual description of specific problem reported by SNMP trap. To be discussed: Option is to define an SNMP trap for every problem."
			::= { sleSnmpLogEntry 2 }

		
		-- *******.4.1.6296.**********
		sleSnmpLogControl OBJECT IDENTIFIER ::= { sleSnmpLog 2 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpLogControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearSnmpLog(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpLogControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpLogControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpLogControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSnmpLogControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpLogControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSnmpLogControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpLogControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSnmpLogControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleSnmpLogControl 5 }

		
		-- *******.4.1.6296.**********
		sleSnmpLogNotifications OBJECT IDENTIFIER ::= { sleSnmpLog 3 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpLogCleared NOTIFICATION-TYPE
			OBJECTS { sleSnmpLogControlRequest, sleSnmpLogControlTimeStamp, sleSnmpLogControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmpLogNotifications 1 }

		
-- 
--  sleTrapHost
		-- *******.4.1.6296.101.8.14
		sleTrapHostDomainName OBJECT IDENTIFIER ::= { sleSnmp 14 }

		
		-- *******.4.1.6296.**********
		sleTrapHostDNTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleTrapHostDNEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostDomainName 1 }

		
		-- *******.4.1.6296.**********.1
		sleTrapHostDNEntry OBJECT-TYPE
			SYNTAX SleTrapHostDNEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleTrapHostDNType, sleTrapHostDNName }
			::= { sleTrapHostDNTable 1 }

		
		SleTrapHostDNEntry ::=
			SEQUENCE { 
				sleTrapHostDNType
					INTEGER,
				sleTrapHostDNName
					OCTET STRING,
				sleTrapHostDNCommunity
					OCTET STRING
			 }

		-- *******.4.1.6296.**********.1.1
		sleTrapHostDNType OBJECT-TYPE
			SYNTAX INTEGER
				{
				trapHost(1),
				trap2Host(2),
				informTrapHost(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The snmp trap host type."
			::= { sleTrapHostDNEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleTrapHostDNName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..512))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The snmp trap host domain-name"
			::= { sleTrapHostDNEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleTrapHostDNCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The trap host commnity"
			::= { sleTrapHostDNEntry 3 }

		
		-- *******.4.1.6296.**********
		sleTrapHostDNControl OBJECT IDENTIFIER ::= { sleTrapHostDomainName 2 }

		
		-- *******.4.1.6296.**********.1
		sleTrapHostDNControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createTrapHost(1),
				destroyTrapHost(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"setInformTrapConf"
			::= { sleTrapHostDNControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleTrapHostDNControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostDNControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleTrapHostDNControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostDNControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleTrapHostDNControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostDNControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleTrapHostDNControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleTrapHostDNControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleTrapHostDNControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				trapHost(1),
				trap2Host(2),
				informTrapHost(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The snmp trap host type."
			::= { sleTrapHostDNControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleTrapHostDNControlName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..512))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The snmp trap host domain-name"
			::= { sleTrapHostDNControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleTrapHostDNControlCommunity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The trap host commnity"
			::= { sleTrapHostDNControl 8 }

		
		-- *******.4.1.6296.**********
		sleTrapHostDNNotification OBJECT IDENTIFIER ::= { sleTrapHostDomainName 3 }

		
		-- *******.4.1.6296.**********.1
		sleTrapHostDNCreated NOTIFICATION-TYPE
			OBJECTS { sleTrapHostDNControlRequest, sleTrapHostDNControlTimeStamp, sleTrapHostDNControlReqResult, sleTrapHostDNControlType, sleTrapHostDNControlName, 
				sleTrapHostDNControlCommunity }
			STATUS current
			DESCRIPTION 
				"createTrapHost"
			::= { sleTrapHostDNNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleTrapHostDNDestroyed NOTIFICATION-TYPE
			OBJECTS { sleTrapHostDNControlRequest, sleTrapHostDNControlTimeStamp, sleTrapHostDNControlReqResult, sleTrapHostDNControlType, sleTrapHostDNControlName
				 }
			STATUS current
			DESCRIPTION 
				"destroyTrapHost"
			::= { sleTrapHostDNNotification 2 }

		
		-- *******.4.1.6296.101.8.15
		sleSnmpPing OBJECT IDENTIFIER ::= { sleSnmp 15 }
-- The ping MIB for LG U+
		
		-- *******.4.1.6296.**********
		sleSnmpPingTable OBJECT IDENTIFIER ::= { sleSnmpPing 1 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpPingSourceIp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The source Ip address."
			::= { sleSnmpPingTable 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpPingDestinationIp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The destination Ip address."
			::= { sleSnmpPingTable 2 }

		
		-- *******.4.1.6296.**********.3
		sleSnmpPingPacketLossResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of packet loss.(%)"
			::= { sleSnmpPingTable 3 }

		
		-- *******.4.1.6296.**********
		sleSnmpPingControl OBJECT IDENTIFIER ::= { sleSnmpPing 2 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpPingControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setPingDefault(1),
				setPingWithSourceIp(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"setInformTrapConf"
			::= { sleSnmpPingControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpPingControlStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpPingControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSnmpPingControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpPingControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSnmpPingControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpPingControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSnmpPingControlReqResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSnmpPingControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSnmpPingControlSourceIp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The source Ip address."
			::= { sleSnmpPingControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSnmpPingControlDestinationIp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The destination Ip address."
			::= { sleSnmpPingControl 7 }

		
		-- *******.4.1.6296.**********
		sleSnmpPingNotification OBJECT IDENTIFIER ::= { sleSnmpPing 3 }

		
		-- *******.4.1.6296.**********.1
		sleSnmpPingDefaultChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpPingControlRequest, sleSnmpPingControlTimeStamp, sleSnmpPingControlReqResult, sleSnmpPingControlDestinationIp, sleSnmpPingPacketLossResult
				 }
			STATUS current
			DESCRIPTION 
				"setPingDefault."
			::= { sleSnmpPingNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSnmpPingWithSourceIpChanged NOTIFICATION-TYPE
			OBJECTS { sleSnmpPingControlRequest, sleSnmpPingControlTimeStamp, sleSnmpPingControlReqResult, sleSnmpPingControlSourceIp, sleSnmpPingControlDestinationIp, 
				sleSnmpPingPacketLossResult }
			STATUS current
			DESCRIPTION 
				"setPingWithSourceIp"
			::= { sleSnmpPingNotification 2 }

		
		-- *******.4.1.6296.101.8.16
		sleSnmpGroup OBJECT-GROUP
			OBJECTS { sleSnmpSysContact, sleSnmpControlRequest, sleSnmpControlStatus, sleSnmpControlTimer, sleSnmpControlTimeStamp, 
				sleSnmpControlReqResult, sleSnmpControlSysContact, sleCom2SecName, sleCom2SecHost, sleCom2SecHostMaskLen, 
				sleCom2SecCommunity, sleCom2SecControlRequest, sleCom2SecControlStatus, sleCom2SecControlTimer, sleCom2SecControlTimeStamp, 
				sleCom2SecControlReqResult, sleCom2SecControlName, sleCom2SecControlHost, sleCom2secControlHostMaskLen, sleCom2SecControlCommunity, 
				sleTrapHostType, sleTrapHostAddress, sleTrapHostCommunity, sleTrapHostPort, sleTrapHostControlRequest, 
				sleTrapHostControlStatus, sleTrapHostControlTimer, sleTrapHostControlTimeStamp, sleTrapHostControlReqResult, sleTrapHostControlType, 
				sleTrapHostControlAddress, sleTrapHostControlCommunity, sleTrapHostControlPort, sleCommunityType, sleCommunityValue, 
				sleCommunityHost, sleCommunityOID, sleCommunityHostMaskLen, sleCommunityControlRequest, sleCommunityControlStatus, 
				sleCommunityControlTimer, sleCommunityControlTimeStamp, sleCommunityControlReqResult, sleCommunityControlType, sleCommunityControlValue, 
				sleCommunityControlHost, sleCommunityControlOID, sleCommunityControlHostMaskLen, sleViewTreeFamilyName, sleViewTreeFamilySubtree, 
				sleViewTreeFamilyMask, sleViewTreeFamilyType, sleViewTreeFamilyControlRequest, sleViewTreeFamilyControlStatus, sleViewTreeFamilyControlTimer, 
				sleViewTreeFamilyControlTimeStamp, sleViewTreeFamilyControlReqResult, sleViewTreeFamilyControlName, sleViewTreeFamilyControlSubtree, sleViewTreeFamilyControlMask, 
				sleViewTreeFamilyControlType, sleAccessGroupName, sleAccessSecurityModel, sleAccessSecurityLevel, sleAccessReadViewName, 
				sleAccessWriteViewName, sleAccessNotifyViewName, sleAccessControlRequest, sleAccessControlStatus, sleAccessControlTimer, 
				sleAccessControlTimeStamp, sleAccessControlReqResult, sleAccessControlGroupName, sleAccessControlSecurityModel, sleAccessControlSecurityLevel, 
				sleAccessControlReadViewName, sleAccessControlWriteViewName, sleAccessControlNotifyViewName, sleSecurityToGroupGName, sleSnmpLogStatus, 
				sleSnmpControlLogStatus, sleSnmpLogIndex, sleSnmpLogText, sleSnmpLogControlRequest, sleSnmpLogControlStatus, 
				sleSnmpLogControlTimer, sleSnmpLogControlTimeStamp, sleSnmpLogControlReqResult, sleSecurityToGroupModel, sleSecurityToGroupSName, 
				sleSecurityToGroupControlRequest, sleSecurityToGroupControlStatus, sleSecurityToGroupControlTimer, sleSecurityToGroupControlTimeStamp, sleSecurityToGroupControlReqResult, 
				sleSecurityToGroupControlGName, sleSecurityToGroupControlModel, sleSecurityToGroupControlSName, sleUserName, sleUserAuthType, 
				sleUserAuthKey, sleUserPrivType, sleUserPrivKey, sleUserControlRequest, sleUserControlStatus, 
				sleUserControlTimer, sleUserControlTimeStamp, sleUserControlReqResult, sleUserControlName, sleUserControlAuthType, 
				sleUserControlAuthKey, sleUserControlPrivType, sleUserControlPrivKey, sleAgentAddress, sleAgentInterface, 
				sleAgentControlRequest, sleAgentControlStatus, sleAgentControlTimer, sleAgentControlTimeStamp, sleAgentControlReqResult, 
				sleAgentControlAddress, sleAgentControlInterface, sleSnmpSessionTimeout, sleSnmpSessionIpAddr, sleSnmpSessionCommunity, 
				sleSnmpSessionSnmpVersion, sleSnmpSessionAccessTIme, sleSnmpSessionControlRequest, sleSnmpSessionControlStatus, sleSnmpSessionControlTimer, 
				sleSnmpSessionControlTimeStamp, sleSnmpSessionControlReqResult, sleSnmpSessionControlTimeout, sleSnmpTrapIndex, sleSnmpTrapStatus, 
				sleSnmpTrapPortIndex, sleSnmpTrapPortType, sleSnmpTrapPortStatus, sleSnmpTrapControlRequest, sleSnmpTrapControlStatus, 
				sleSnmpTrapControlTimer, sleSnmpTrapControlTimeStamp, sleSnmpTrapControlReqResult, sleSnmpTrapControlTrapIndex, sleSnmpTrapControlPortIndex, 
				sleSnmpTrapControlTrapStatus, sleSnmpTrapSourceAddress, sleSnmpTrapSourceInterface, sleSnmpTrapSourceControlRequest, sleSnmpTrapSourceControlStatus, 
				sleSnmpTrapSourceControlTimer, sleSnmpTrapSourceControlTimeStamp, sleSnmpTrapSourceControlReqResult, sleSnmpTrapSourceControlAddress, sleSnmpConnectionType, 
				sleSnmpControlConnectionType, sleSnmpTrapSourceControlInterface, sleSnmpSysLocation, sleSnmpPingSourceIp, sleSnmpPingDestinationIp, 
				sleSnmpPingControlRequest, sleSnmpPingControlStatus, sleSnmpPingControlTimer, sleSnmpPingControlTimeStamp, sleSnmpPingControlReqResult, 
				sleSnmpPingControlSourceIp, sleSnmpPingControlDestinationIp, sleSnmpTrapModeStatus, sleSnmpControlTrapModeStatus, sleTrapHostVrfName, 
				sleTrapHostControlVrfName, sleTrapHostControlUser, sleTrapHostUser, sleSnmpControlSysLocation, sleSnmpInformTrapRetry, 
				slesnmpInformTrapInterval, sleSnmpControlInformTrapRetry, sleSnmpControlInformTrapInterval, sleTrapHostDNType, sleTrapHostDNName, 
				sleTrapHostDNCommunity, sleTrapHostDNControlRequest, sleTrapHostDNControlStatus, sleTrapHostDNControlTimer, sleTrapHostDNControlTimeStamp, 
				sleTrapHostDNControlReqResult, sleTrapHostDNControlType, sleTrapHostDNControlName, sleTrapHostDNControlCommunity, sleSnmpPingPacketLossResult
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmp 16 }

		
		-- *******.4.1.6296.101.8.17
		sleSnmpNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleSnmpSysInfoProfileChanged, sleCom2SecCreated, sleCom2SecDestroyed, sleCom2SecProfileChanged, sleTrapHostCreated, 
				sleTrapHostDestroyed, sleCommunityCreated, sleCommunityDestroyed, sleViewTreeFamilyCreated, sleViewTreeFamilyDestroyed, 
				sleAccessCreated, sleAccessDestroyed, sleAccessProfileChanged, sleUserCreated, sleUserDestroyed, 
				sleAgentAddressChanged, sleAgentInterfaceChanged, sleAgentAddressDestroyed, sleAgentInterfaceDestroyed, sleSnmpSessionTimeoutChanged, 
				sleSnmpTrapStatusChanged, sleSnmpTrapPortStatusChanged, sleSnmpTrapSourceInterfaceChanged, sleSnmpTrapSourcetAddressDestroyed, sleSnmpTrapSourceInterfaceDestroyed, 
				sleSnmpTrapSourceAddressChanged, sleSecurityToGroupProfileChanged, sleSnmpSysInfoLogStatusChanged, sleSnmpLogCleared, sleSnmpSysInfoConnectionTypeChanged, 
				sleSecurityToGroupDestroyed, sleTrapHostDNCreated, sleTrapHostDNDestroyed, sleSnmpPingDefaultChanged, sleSnmpPingWithSourceIpChanged, 
				sleSnmpTrapModeStatusChanged, sleSecurityToGroupCreated, sleSnmpInformTrapConfChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSnmp 17 }

		
	
	END

--
-- sle-snmp-mib.mib
--
