--
-- sle-pppoe-mib.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, April 13, 2011 at 19:19:02
--

	SLE-PPPOE-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Gauge32, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.24
		slePppoe MODULE-IDENTITY 
			LAST-UPDATED "201104081633Z"		-- April 08, 2011 at 16:33 GMT
			ORGANIZATION 
				"Dasan Co., Ltd."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleMgmt 24 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.24.1
		slePppoeBase OBJECT IDENTIFIER ::= { slePppoe 1 }

		
		-- *******.4.1.6296.101.24.2
		slePppoeIntermediateAgent OBJECT IDENTIFIER ::= { slePppoe 2 }

		
		-- *******.4.1.6296.**********
		slePpppoeIABaseInfo OBJECT IDENTIFIER ::= { slePppoeIntermediateAgent 1 }

		
		-- *******.4.1.6296.**********.1
		slePpppoeIAEnableStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Enable/Disable Intermediate Agent on the switch"
			::= { slePpppoeIABaseInfo 1 }

		
		-- *******.4.1.6296.**********.2
		slePpppoeIAAccessNode OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Format Type : Access Node Identifier of the switch"
			::= { slePpppoeIABaseInfo 2 }

		
		-- *******.4.1.6296.**********.3
		slePpppoeIACircuitId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Format Type : Circuit ID"
			::= { slePpppoeIABaseInfo 3 }

		
		-- *******.4.1.6296.**********.4
		slePpppoeIARemoteId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Format Type : Remote ID"
			::= { slePpppoeIABaseInfo 4 }

		
		-- *******.4.1.6296.**********
		slePpppoeIAControl OBJECT IDENTIFIER ::= { slePppoeIntermediateAgent 2 }

		
		-- *******.4.1.6296.**********.1
		slePpppoeIAControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setIntermediateAgentEnableStatus(1),
				setFormatTypeAccessnodeId(2),
				setFormatTypeCircuitId(3),
				setFormatTypeRemoteId(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The request of a user command."
			::= { slePpppoeIAControl 1 }

		
		-- *******.4.1.6296.**********.2
		slePpppoeIAControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The status of a user command."
			::= { slePpppoeIAControl 2 }

		
		-- *******.4.1.6296.**********.3
		slePpppoeIAControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The maximum wait Time for the manager for a long running user command."
			::= { slePpppoeIAControl 3 }

		
		-- *******.4.1.6296.**********.4
		slePpppoeIAControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Time stamp of the last command (end of command)"
			::= { slePpppoeIAControl 4 }

		
		-- *******.4.1.6296.**********.5
		slePpppoeIAControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The result of the last user command."
			::= { slePpppoeIAControl 5 }

		
		-- *******.4.1.6296.**********.6
		slePpppoeIAControlEnableStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable/Disable Intermediate Agent on the switch"
			::= { slePpppoeIAControl 6 }

		
		-- *******.4.1.6296.**********.7
		slePpppoeIAControlAccessNode OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Format Type : Access Node Identifier of the switch"
			::= { slePpppoeIAControl 7 }

		
		-- *******.4.1.6296.**********.8
		slePpppoeIAControlCircuitId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Format Type : Circuit ID"
			::= { slePpppoeIAControl 8 }

		
		-- *******.4.1.6296.**********.9
		slePpppoeIAControlRemoteId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Format Type : Remote ID"
			::= { slePpppoeIAControl 9 }

		
		-- *******.4.1.6296.**********
		slePpppoeIANotification OBJECT IDENTIFIER ::= { slePppoeIntermediateAgent 3 }

		
		-- *******.4.1.6296.**********.1
		slePpppoeIAEnableStatuschanged NOTIFICATION-TYPE
			OBJECTS { slePpppoeIAControlRequest, slePpppoeIAControlTimeStamp, slePpppoeIAControlEnableStatus }
			STATUS current
			DESCRIPTION 
				"Enable/Disable Intermediate Agent on the switch"
			::= { slePpppoeIANotification 1 }

		
		-- *******.4.1.6296.**********.2
		slePpppoeIAAccessnodeIdChanged NOTIFICATION-TYPE
			OBJECTS { slePpppoeIAControlRequest, slePpppoeIAControlTimeStamp, slePpppoeIAControlAccessNode }
			STATUS current
			DESCRIPTION 
				"Format Type : Access Node Identifier of the switch"
			::= { slePpppoeIANotification 2 }

		
		-- *******.4.1.6296.**********.3
		slePpppoeIACircuitIdChanged NOTIFICATION-TYPE
			OBJECTS { slePpppoeIAControlRequest, slePpppoeIAControlTimeStamp, slePpppoeIAControlCircuitId }
			STATUS current
			DESCRIPTION 
				"Format Type : Circuit ID"
			::= { slePpppoeIANotification 3 }

		
		-- *******.4.1.6296.**********.4
		slePpppoeIARemoteIdChanged NOTIFICATION-TYPE
			OBJECTS { slePpppoeIAControlRequest, slePpppoeIAControlTimeStamp, slePpppoeIAControlRemoteId }
			STATUS current
			DESCRIPTION 
				"Format Type : Remote ID"
			::= { slePpppoeIANotification 4 }

		
		-- *******.4.1.6296.101.24.3
		slePppoeGroup OBJECT-GROUP
			OBJECTS { slePpppoeIAEnableStatus, slePpppoeIAAccessNode, slePpppoeIACircuitId, slePpppoeIARemoteId, slePpppoeIAControlRequest, 
				slePpppoeIAControlStatus, slePpppoeIAControlTimer, slePpppoeIAControlTimeStamp, slePpppoeIAControlReqResult, slePpppoeIAControlEnableStatus, 
				slePpppoeIAControlAccessNode, slePpppoeIAControlCircuitId, slePpppoeIAControlRemoteId }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePppoe 3 }

		
		-- *******.4.1.6296.101.24.4
		slePppoeNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { slePpppoeIAEnableStatuschanged, slePpppoeIAAccessnodeIdChanged, slePpppoeIACircuitIdChanged, slePpppoeIARemoteIdChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { slePppoe 4 }

		
	
	END

--
-- sle-pppoe-mib.mib
--
