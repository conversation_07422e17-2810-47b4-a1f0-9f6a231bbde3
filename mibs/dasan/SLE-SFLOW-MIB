--
-- SLE-SFLOW-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 3.0 Build 285
-- Friday, June 09, 2006 at 14:03:22
--

	SLE-SFLOW-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			InterfaceIndex			
				FROM IF-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.6296.101.21
		sleSFlow MODULE-IDENTITY 
			LAST-UPDATED "200605181609Z"		-- May 18, 2006 at 16:09 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleMgmt 21 }

		
	
	
--
-- Node definitions
--
	
		-- *******.4.1.6296.101.21.1
		sleSFlowBase OBJECT IDENTIFIER::= { sleSFlow 1 }

		
		-- *******.4.1.6296.**********
		sleSFlowInfo OBJECT IDENTIFIER::= { sleSFlowBase 1 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowInfo 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowInfo 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowAgentAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowInfo 3 }

		
		-- *******.4.1.6296.**********.4
		sleSFlowMaxInstance OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowInfo 4 }

		
		-- *******.4.1.6296.**********
		sleSFlowControl OBJECT IDENTIFIER::= { sleSFlowBase 2 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setSFlowEnable(1),
				setSFlowAgentAddress(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSFlowControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSFlowControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSFlowControlEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSFlowControlAgentAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowControl 7 }

		
		-- *******.4.1.6296.**********
		sleSFlowNotification OBJECT IDENTIFIER::= { sleSFlowBase 3 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowEnableChanged NOTIFICATION-TYPE
			OBJECTS { sleSFlowControlRequest, sleSFlowControlTimeStamp, sleSFlowControlReqResult, sleSFlowEnable }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowAgentAddressChanged NOTIFICATION-TYPE
			OBJECTS { sleSFlowControlRequest, sleSFlowControlTimeStamp, sleSFlowControlReqResult, sleSFlowAgentAddress }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowNotification 2 }

		
		-- *******.4.1.6296.101.21.2
		sleSFlowRcvr OBJECT IDENTIFIER::= { sleSFlow 2 }

		
		-- *******.4.1.6296.**********
		sleSFlowRcvrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSFlowRcvrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvr 1 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowRcvrEntry OBJECT-TYPE
			SYNTAX SleSFlowRcvrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSFlowRcvrIndex }
			::= { sleSFlowRcvrTable 1 }

		
		SleSFlowRcvrEntry ::=
			SEQUENCE { 
				sleSFlowRcvrIndex
					INTEGER,
				sleSFlowRcvrOwner
					OCTET STRING,
				sleSFlowRcvrTimeout
					INTEGER,
				sleSFlowRcvrMaxDatagramSize
					INTEGER,
				sleSFlowRcvrAddress
					IpAddress,
				sleSFlowRcvrPort
					INTEGER,
				sleSFlowRcvrDatagramVersion
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSFlowRcvrIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSFlowRcvrOwner OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..127))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSFlowRcvrTimeout OBJECT-TYPE
			SYNTAX INTEGER (0..2147483647)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowRcvrEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleSFlowRcvrMaxDatagramSize OBJECT-TYPE
			SYNTAX INTEGER (256..1400)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1400 }
			::= { sleSFlowRcvrEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleSFlowRcvrAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrEntry 5 }

		
		-- *******.4.1.6296.**********.1.6
		sleSFlowRcvrPort OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 6343 }
			::= { sleSFlowRcvrEntry 6 }

		
		-- *******.4.1.6296.**********.1.7
		sleSFlowRcvrDatagramVersion OBJECT-TYPE
			SYNTAX INTEGER { version5(5) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrEntry 7 }

		
		-- *******.4.1.6296.**********
		sleSFlowRcvrControl OBJECT IDENTIFIER::= { sleSFlowRcvr 2 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowRcvrControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSFlowRcvr(1),
				setSFlowRcvr(2),
				destroySFlowRcvr(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowRcvrControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowRcvrControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSFlowRcvrControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSFlowRcvrControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSFlowRcvrControlIndex OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSFlowRcvrControlOwner OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..127))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleSFlowRcvrControlTimeout OBJECT-TYPE
			SYNTAX INTEGER (0..2147483647)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowRcvrControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleSFlowRcvrControlMaxDatagramSize OBJECT-TYPE
			SYNTAX INTEGER (256..1400)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 1400 }
			::= { sleSFlowRcvrControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleSFlowRcvrControlAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowRcvrControl 10 }

		
		-- *******.4.1.6296.**********.11
		sleSFlowRcvrControlPort OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 6343 }
			::= { sleSFlowRcvrControl 11 }

		
		-- *******.4.1.6296.**********
		sleSFlowRcvrNotification OBJECT IDENTIFIER::= { sleSFlowRcvr 3 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowRcvrCreated NOTIFICATION-TYPE
			OBJECTS { sleSFlowRcvrControlRequest, sleSFlowRcvrControlTimeStamp, sleSFlowRcvrControlReqResult, sleSFlowRcvrOwner, sleSFlowRcvrTimeout, 
				sleSFlowRcvrMaxDatagramSize, sleSFlowRcvrAddress, sleSFlowRcvrPort, sleSFlowRcvrDatagramVersion }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowRcvrNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowRcvrChanged NOTIFICATION-TYPE
			OBJECTS { sleSFlowRcvrControlRequest, sleSFlowRcvrControlTimeStamp, sleSFlowRcvrControlReqResult, sleSFlowRcvrOwner, sleSFlowRcvrTimeout, 
				sleSFlowRcvrMaxDatagramSize, sleSFlowRcvrAddress, sleSFlowRcvrPort, sleSFlowRcvrDatagramVersion }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowRcvrNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowRcvrDestroyed NOTIFICATION-TYPE
			OBJECTS { sleSFlowRcvrControlRequest, sleSFlowRcvrControlTimeStamp, sleSFlowRcvrControlReqResult, sleSFlowRcvrControlIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowRcvrNotification 3 }

		
		-- *******.4.1.6296.101.21.3
		sleSFlowFs OBJECT IDENTIFIER::= { sleSFlow 3 }

		
		-- *******.4.1.6296.**********
		sleSFlowFsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSFlowFsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFs 1 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowFsEntry OBJECT-TYPE
			SYNTAX SleSFlowFsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSFlowFsDataSource, sleSFlowFsInstance }
			::= { sleSFlowFsTable 1 }

		
		SleSFlowFsEntry ::=
			SEQUENCE { 
				sleSFlowFsDataSource
					InterfaceIndex,
				sleSFlowFsInstance
					INTEGER,
				sleSFlowFsReceiver
					INTEGER,
				sleSFlowFsPacketSamplingRate
					INTEGER,
				sleSFlowFsMaxHeaderSize
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSFlowFsDataSource OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSFlowFsInstance OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSFlowFsReceiver OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowFsEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleSFlowFsPacketSamplingRate OBJECT-TYPE
			SYNTAX INTEGER (0..2000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowFsEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleSFlowFsMaxHeaderSize OBJECT-TYPE
			SYNTAX INTEGER (16..256)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 128 }
			::= { sleSFlowFsEntry 5 }

		
		-- *******.4.1.6296.**********
		sleSFlowFsControl OBJECT IDENTIFIER::= { sleSFlowFs 2 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowFsControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSFlowFs(1),
				setSFlowFs(2),
				destroySFlowFs(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowFsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowFsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSFlowFsControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSFlowFsControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSFlowFsControlDataSource OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSFlowFsControlInstance OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowFsControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleSFlowFsControlReceiver OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowFsControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleSFlowFsControlPacketSamplingRate OBJECT-TYPE
			SYNTAX INTEGER (0..2000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowFsControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleSFlowFsControlMaxHeaderSize OBJECT-TYPE
			SYNTAX INTEGER (16..256)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 128 }
			::= { sleSFlowFsControl 10 }

		
		-- *******.4.1.6296.**********
		sleSFlowFsNotification OBJECT IDENTIFIER::= { sleSFlowFs 3 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowFsCreated NOTIFICATION-TYPE
			OBJECTS { sleSFlowFsControlRequest, sleSFlowFsControlTimeStamp, sleSFlowFsControlReqResult, sleSFlowFsReceiver, sleSFlowFsPacketSamplingRate, 
				sleSFlowFsMaxHeaderSize }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowFsNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowFsChanged NOTIFICATION-TYPE
			OBJECTS { sleSFlowFsControlRequest, sleSFlowFsControlTimeStamp, sleSFlowFsControlReqResult, sleSFlowFsReceiver, sleSFlowFsPacketSamplingRate, 
				sleSFlowFsMaxHeaderSize }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowFsNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowFsDestroyed NOTIFICATION-TYPE
			OBJECTS { sleSFlowFsControlRequest, sleSFlowFsControlTimeStamp, sleSFlowFsControlReqResult, sleSFlowFsControlDataSource, sleSFlowFsControlInstance
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowFsNotification 3 }

		
		-- *******.4.1.6296.101.21.4
		sleSFlowCp OBJECT IDENTIFIER::= { sleSFlow 4 }

		
		-- *******.4.1.6296.**********
		sleSFlowCpTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleSFlowCpEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCp 1 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowCpEntry OBJECT-TYPE
			SYNTAX SleSFlowCpEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleSFlowCpDataSource, sleSFlowCpInstance }
			::= { sleSFlowCpTable 1 }

		
		SleSFlowCpEntry ::=
			SEQUENCE { 
				sleSFlowCpDataSource
					InterfaceIndex,
				sleSFlowCpInstance
					INTEGER,
				sleSFlowCpReceiver
					INTEGER,
				sleSFlowCpInterval
					INTEGER
			 }

		-- *******.4.1.6296.**********.1.1
		sleSFlowCpDataSource OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleSFlowCpInstance OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleSFlowCpReceiver OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowCpEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleSFlowCpInterval OBJECT-TYPE
			SYNTAX INTEGER (0..1000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowCpEntry 4 }

		
		-- *******.4.1.6296.**********
		sleSFlowCpControl OBJECT IDENTIFIER::= { sleSFlowCp 2 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowCpControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createSFlowCp(1),
				setSFlowCp(2),
				destroySFlowCp(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowCpControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowCpControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleSFlowCpControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleSFlowCpControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleSFlowCpControlDataSource OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleSFlowCpControlInstance OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleSFlowCpControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleSFlowCpControlReceiver OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowCpControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleSFlowCpControlInterval OBJECT-TYPE
			SYNTAX INTEGER (0..1000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 0 }
			::= { sleSFlowCpControl 9 }

		
		-- *******.4.1.6296.**********
		sleSFlowCpNotification OBJECT IDENTIFIER::= { sleSFlowCp 3 }

		
		-- *******.4.1.6296.**********.1
		sleSFlowCpCreated NOTIFICATION-TYPE
			OBJECTS { sleSFlowCpControlRequest, sleSFlowCpControlTimeStamp, sleSFlowCpControlReqResult, sleSFlowCpReceiver, sleSFlowCpInterval
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowCpNotification 1 }

		
		-- *******.4.1.6296.**********.2
		sleSFlowCpChanged NOTIFICATION-TYPE
			OBJECTS { sleSFlowCpControlRequest, sleSFlowCpControlTimeStamp, sleSFlowCpControlReqResult, sleSFlowCpReceiver, sleSFlowCpInterval
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowCpNotification 2 }

		
		-- *******.4.1.6296.**********.3
		sleSFlowCpDestroyed NOTIFICATION-TYPE
			OBJECTS { sleSFlowCpControlRequest, sleSFlowCpControlTimeStamp, sleSFlowCpControlReqResult, sleSFlowCpControlDataSource, sleSFlowCpControlInstance
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlowCpNotification 3 }

		
		-- *******.4.1.6296.101.21.5
		sleSFlowGroup OBJECT-GROUP
			OBJECTS { sleSFlowEnable, sleSFlowVersion, sleSFlowAgentAddress, sleSFlowMaxInstance, sleSFlowRcvrIndex, 
				sleSFlowRcvrOwner, sleSFlowRcvrTimeout, sleSFlowRcvrMaxDatagramSize, sleSFlowRcvrAddress, sleSFlowRcvrPort, 
				sleSFlowRcvrDatagramVersion, sleSFlowFsDataSource, sleSFlowFsInstance, sleSFlowFsReceiver, sleSFlowFsPacketSamplingRate, 
				sleSFlowFsMaxHeaderSize, sleSFlowCpDataSource, sleSFlowCpInstance, sleSFlowCpReceiver, sleSFlowCpInterval
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlow 5 }

		
		-- *******.4.1.6296.101.21.6
		sleSFlowControlGroup OBJECT-GROUP
			OBJECTS { sleSFlowControlRequest, sleSFlowControlStatus, sleSFlowControlTimer, sleSFlowControlTimeStamp, sleSFlowControlReqResult, 
				sleSFlowControlEnable, sleSFlowControlAgentAddress, sleSFlowRcvrControlRequest, sleSFlowRcvrControlStatus, sleSFlowRcvrControlTimer, 
				sleSFlowRcvrControlTimeStamp, sleSFlowRcvrControlReqResult, sleSFlowRcvrControlIndex, sleSFlowRcvrControlOwner, sleSFlowRcvrControlTimeout, 
				sleSFlowRcvrControlMaxDatagramSize, sleSFlowRcvrControlAddress, sleSFlowRcvrControlPort, sleSFlowFsControlRequest, sleSFlowFsControlStatus, 
				sleSFlowFsControlTimer, sleSFlowFsControlTimeStamp, sleSFlowFsControlReqResult, sleSFlowFsControlDataSource, sleSFlowFsControlInstance, 
				sleSFlowFsControlReceiver, sleSFlowFsControlPacketSamplingRate, sleSFlowFsControlMaxHeaderSize, sleSFlowCpControlRequest, sleSFlowCpControlStatus, 
				sleSFlowCpControlTimer, sleSFlowCpControlTimeStamp, sleSFlowCpControlReqResult, sleSFlowCpControlDataSource, sleSFlowCpControlInstance, 
				sleSFlowCpControlReceiver, sleSFlowCpControlInterval }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlow 6 }

		
		-- *******.4.1.6296.101.21.7
		sleSFlowNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleSFlowEnableChanged, sleSFlowAgentAddressChanged, sleSFlowRcvrCreated, sleSFlowRcvrChanged, sleSFlowRcvrDestroyed, 
				sleSFlowFsCreated, sleSFlowFsChanged, sleSFlowFsDestroyed, sleSFlowCpCreated, sleSFlowCpChanged, 
				sleSFlowCpDestroyed }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleSFlow 7 }

		
	
	END

--
-- SLE-SFLOW-MIB.my
--
