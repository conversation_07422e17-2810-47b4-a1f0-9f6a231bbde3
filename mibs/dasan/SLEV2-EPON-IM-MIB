--
-- SLEV2-EPON-IM-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, October 25, 2007 at 17:42:26
--

	SLEV2-EPON-IM-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleV2Mgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, Integer32, <PERSON><PERSON><PERSON>32, Counter32, Counter64, 
			OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
--   *******.4.1.6296.102.15
-- *******.4.1.6296.102.15
-- October 24, 2006 at 11:42 GMT
		-- *******.4.1.6296.102.15
		sleV2EponIM MODULE-IDENTITY 
			LAST-UPDATED "200610241142Z"		-- October 24, 2006 at 11:42 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleV2Mgmt 15 }

		
	
--
-- Type definitions
--
	
--  Type definitions
-- 
		EponOnuState ::= INTEGER
			{
			disable(0),
			enable(1)
			}

--   Type definitions
-- 
		EponPortType ::= INTEGER
			{
			nni(1),
			mgmt(2),
			uni(3),
			pon(4),
			aggr(5),
			llid(6)
			}

		EponOnuLinkStatus ::= INTEGER
			{
			down(0),
			up(1)
			}

		EponOnuStatus ::= INTEGER
			{
			registered(1),
			deregistered(2),
			discovered(3),
			lost(4)
			}

	
	
--
-- Node definitions
--
	
--  Node definitions
-- 
-- 
-- TAG : Onu 
--  
-- *******.4.1.6296.102.15.1
		-- *******.4.1.6296.102.15.1
		sleV2EponIMOnu OBJECT IDENTIFIER ::= { sleV2EponIM 1 }

		
--  *******.4.1.6296.**********
		-- *******.4.1.6296.**********
		sleV2EponIMOnuBase OBJECT IDENTIFIER ::= { sleV2EponIMOnu 1 }

		
--  *******.4.1.6296.**********.1
		-- *******.4.1.6296.**********.1
		sleV2EponIMOnuInfo OBJECT IDENTIFIER ::= { sleV2EponIMOnuBase 1 }

		
--  *******.4.1.6296.**********.1.1
		-- *******.4.1.6296.**********.1.1
		sleV2EponIMOnuConfigMask OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"mask to indicate valid fields"
			::= { sleV2EponIMOnuInfo 1 }

		
--  *******.4.1.6296.**********.1.2
		-- *******.4.1.6296.**********.1.2
		sleV2EponIMOnuConfigData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"eeprom startup config data"
			::= { sleV2EponIMOnuInfo 2 }

		
--  *******.4.1.6296.**********.1.3
		-- *******.4.1.6296.**********.1.3
		sleV2EponIMOnuRegistered OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of the ONU port"
			::= { sleV2EponIMOnuInfo 3 }

		
--  *******.4.1.6296.**********.1.4
		-- *******.4.1.6296.**********.1.4
		sleV2EponIMOnuAuthenticated OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of the ONU port authentication"
			::= { sleV2EponIMOnuInfo 4 }

		
--  *******.4.1.6296.**********.1.5
		-- *******.4.1.6296.**********.1.5
		sleV2EponIMOnuImageVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU software version"
			::= { sleV2EponIMOnuInfo 5 }

		
--  *******.4.1.6296.**********.1.6
		-- *******.4.1.6296.**********.1.6
		sleV2EponIMOnuLoadVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU loader version"
			::= { sleV2EponIMOnuInfo 6 }

		
--  *******.4.1.6296.**********.1.7
		-- *******.4.1.6296.**********.1.7
		sleV2EponIMOnuChipVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"chip version"
			::= { sleV2EponIMOnuInfo 7 }

		
--  *******.4.1.6296.**********.1.8
		-- *******.4.1.6296.**********.1.8
		sleV2EponIMOnuSerialNumber OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU serial number"
			::= { sleV2EponIMOnuInfo 8 }

		
--  *******.4.1.6296.**********.1.9
		-- *******.4.1.6296.**********.1.9
		sleV2EponIMOnuRxOpticPower OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"value of the ONU rx optical power, UNIT : dBm"
			::= { sleV2EponIMOnuInfo 9 }

		
--  *******.4.1.6296.**********.1.10
		-- *******.4.1.6296.**********.1.10
		sleV2EponIMRegInfoOltId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the OLT id from which the registration comes"
			::= { sleV2EponIMOnuInfo 10 }

		
--  *******.4.1.6296.**********.1.11
		-- *******.4.1.6296.**********.1.11
		sleV2EponIMRegInfoOltLlidPort OBJECT-TYPE
			SYNTAX INTEGER (1..512)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the OLT LLID based logical port at which the ONU is registered"
			::= { sleV2EponIMOnuInfo 11 }

		
--  *******.4.1.6296.**********.1.12
		-- *******.4.1.6296.**********.1.12
		sleV2EponIMRegInfoOltPonPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the OLT PON port at which the ONU is registered"
			::= { sleV2EponIMOnuInfo 12 }

		
--  *******.4.1.6296.**********.1.13
		-- *******.4.1.6296.**********.1.13
		sleV2EponIMRegInfoOnuMacAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the ONU MAC address"
			::= { sleV2EponIMOnuInfo 13 }

		
--  *******.4.1.6296.**********.1.14
		-- *******.4.1.6296.**********.1.14
		sleV2EponIMRegInfoOnuLlid OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the logical link ID"
			::= { sleV2EponIMOnuInfo 14 }

		
--  *******.4.1.6296.**********.1.15
		-- *******.4.1.6296.**********.1.15
		sleV2EponIMOnuId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU authentication user name"
			::= { sleV2EponIMOnuInfo 15 }

		
--  *******.4.1.6296.**********.1.16
		-- *******.4.1.6296.**********.1.16
		sleV2EponIMOnuSecret OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU authentication password"
			::= { sleV2EponIMOnuInfo 16 }

		
--  *******.4.1.6296.**********.1.17
		-- *******.4.1.6296.**********.1.17
		sleV2EponIMOnuVendorCode OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"vendor code"
			::= { sleV2EponIMOnuInfo 17 }

		
--  *******.4.1.6296.**********.1.18
		-- *******.4.1.6296.**********.1.18
		sleV2EponIMOnuAuthTimer OBJECT-TYPE
			SYNTAX INTEGER (1..6000)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU Auth timer"
			::= { sleV2EponIMOnuInfo 18 }

		
--  *******.4.1.6296.**********.1.19
		-- *******.4.1.6296.**********.1.19
		sleV2EponIMOnuAuthTimeoutTimer OBJECT-TYPE
			SYNTAX INTEGER (1..15360)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU Timeout timer"
			::= { sleV2EponIMOnuInfo 19 }

		
--  *******.4.1.6296.**********.1.20
		-- *******.4.1.6296.**********.1.20
		sleV2EponIMOnuAuthRejectTimer OBJECT-TYPE
			SYNTAX INTEGER (1..15360)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU Reject timer"
			::= { sleV2EponIMOnuInfo 20 }

		
		-- *******.4.1.6296.**********.1.21
		sleV2EponIMOnuAutoUpgrade OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuInfo 21 }

		
		-- *******.4.1.6296.**********.1.22
		sleV2EponIMOnuForceOpticPowerOff OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuInfo 22 }

		
		-- *******.4.1.6296.**********.1.23
		sleV2EponIMOnuOpticButtonPushed OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuInfo 23 }

		
--  *******.4.1.6296.**********.2
		-- *******.4.1.6296.**********.2
		sleV2EponIMOnuControl OBJECT IDENTIFIER ::= { sleV2EponIMOnuBase 2 }

		
--  *******.4.1.6296.**********.2.1
		-- *******.4.1.6296.**********.2.1
		sleV2EponIMOnuControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setOnuSecret(1),
				setOnuVendorCode(2),
				setOnuAuthTimer(3),
				setOnuConfigApply(4),
				setOnuAutoUpgrade(5),
				setOnuForcePowerOff(6),
				setOnuReset(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuControl 1 }

		
--  *******.4.1.6296.**********.2.2
		-- *******.4.1.6296.**********.2.2
		sleV2EponIMOnuControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				".
				"
			::= { sleV2EponIMOnuControl 2 }

		
--  *******.4.1.6296.**********.2.3
		-- *******.4.1.6296.**********.2.3
		sleV2EponIMOnuControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				".
				"
			::= { sleV2EponIMOnuControl 3 }

		
--  *******.4.1.6296.**********.2.4
		-- *******.4.1.6296.**********.2.4
		sleV2EponIMOnuControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				".."
			::= { sleV2EponIMOnuControl 4 }

		
--  *******.4.1.6296.**********.2.5
		-- *******.4.1.6296.**********.2.5
		sleV2EponIMOnuControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuControl 5 }

		
--  *******.4.1.6296.**********.2.6
		-- *******.4.1.6296.**********.2.6
		sleV2EponIMOnuControlSecret OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuControl 6 }

		
--  *******.4.1.6296.**********.2.7
		-- *******.4.1.6296.**********.2.7
		sleV2EponIMOnuControlOnuVendorCode OBJECT-TYPE
			SYNTAX INTEGER (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuControl 7 }

		
--  *******.4.1.6296.**********.2.8
		-- *******.4.1.6296.**********.2.8
		sleV2EponIMOnuControlAuthTimer OBJECT-TYPE
			SYNTAX INTEGER (1..6000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuControl 8 }

		
--  *******.4.1.6296.**********.2.9
		-- *******.4.1.6296.**********.2.9
		sleV2EponIMOnuControlAuthTimeoutTimer OBJECT-TYPE
			SYNTAX INTEGER (1..15360)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuControl 9 }

		
--  *******.4.1.6296.**********.2.10
		-- *******.4.1.6296.**********.2.10
		sleV2EponIMOnuControlAuthRejectTimer OBJECT-TYPE
			SYNTAX INTEGER (1..15360)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuControl 10 }

		
		-- *******.4.1.6296.**********.2.11
		sleV2EponIMOnuControlAutoUpgrade OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuControl 11 }

		
		-- *******.4.1.6296.**********.2.12
		sleV2EponIMOnuControlForceOpticPowerOff OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuControl 12 }

		
--  *******.4.1.6296.**********.3
		-- *******.4.1.6296.**********.3
		sleV2EponIMOnuNotification OBJECT IDENTIFIER ::= { sleV2EponIMOnuBase 3 }

		
--  *******.4.1.6296.**********.3.1
		-- *******.4.1.6296.**********.3.1
		sleV2EponIMOnuSecretChanged NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult, sleV2EponIMOnuSecret }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuNotification 1 }

		
--  *******.4.1.6296.**********.3.2
		-- *******.4.1.6296.**********.3.2
		sleV2EponIMOnuVendorCodeChanged NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult, sleV2EponIMOnuVendorCode }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuNotification 2 }

		
--  *******.4.1.6296.**********.3.3
		-- *******.4.1.6296.**********.3.3
		sleV2EponIMOnuAuthTimerChanged NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult, sleV2EponIMOnuAuthTimer, sleV2EponIMOnuAuthTimeoutTimer, 
				sleV2EponIMOnuAuthRejectTimer }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuNotification 3 }

		
--  *******.4.1.6296.**********.3.4
		-- *******.4.1.6296.**********.3.4
		sleV2EponIMOnuConfigApplySetted NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuNotification 4 }

		
		-- *******.4.1.6296.**********.3.5
		sleV2EponIMOnuAutoUpgradeChanged NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult, sleV2EponIMOnuControlAuthRejectTimer }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuNotification 5 }

		
		-- *******.4.1.6296.**********.3.6
		sleV2EponIMOnuForcePowerOffChanged NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult, sleV2EponIMOnuForceOpticPowerOff }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuNotification 6 }

		
		-- *******.4.1.6296.**********.3.7
		sleV2EponIMOnuReset NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuNotification 7 }

		
--  *******.4.1.6296.**********
		-- *******.4.1.6296.**********
		sleV2EponIMOnuFW OBJECT IDENTIFIER ::= { sleV2EponIMOnu 2 }

		
		-- *******.4.1.6296.**********.1
		sleV2EponIMOnuFWTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2EponIMOnuFWEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuFW 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleV2EponIMOnuFWEntry OBJECT-TYPE
			SYNTAX SleV2EponIMOnuFWEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2EponIMOnuFWName }
			::= { sleV2EponIMOnuFWTable 1 }

		
		SleV2EponIMOnuFWEntry ::=
			SEQUENCE { 
				sleV2EponIMOnuFWName
					OCTET STRING
			 }

--  *******.4.1.6296.**********.1.1.1
		-- *******.4.1.6296.**********.1.1.1
		sleV2EponIMOnuFWName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"firmware name"
			::= { sleV2EponIMOnuFWEntry 1 }

		
--  *******.4.1.6296.**********.2
		-- *******.4.1.6296.**********.2
		sleV2EponIMOnuFWControl OBJECT IDENTIFIER ::= { sleV2EponIMOnuFW 2 }

		
--  *******.4.1.6296.**********.2.1
		-- *******.4.1.6296.**********.2.1
		sleV2EponIMOnuFWControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				copyFirmware(1),
				destroyFirmware(2),
				updateFirmware(3),
				commitFirmware(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuFWControl 1 }

		
--  *******.4.1.6296.**********.2.2
		-- *******.4.1.6296.**********.2.2
		sleV2EponIMOnuFWControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuFWControl 2 }

		
--  *******.4.1.6296.**********.2.3
		-- *******.4.1.6296.**********.2.3
		sleV2EponIMOnuFWControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuFWControl 3 }

		
--  *******.4.1.6296.**********.2.4
		-- *******.4.1.6296.**********.2.4
		sleV2EponIMOnuFWControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuFWControl 4 }

		
--  *******.4.1.6296.**********.2.5
		-- *******.4.1.6296.**********.2.5
		sleV2EponIMOnuFWControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuFWControl 5 }

		
--  *******.4.1.6296.**********.2.6
		-- *******.4.1.6296.**********.2.6
		sleV2EponIMOnuFWControlServerIp OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuFWControl 6 }

		
--  *******.4.1.6296.**********.2.7
		-- *******.4.1.6296.**********.2.7
		sleV2EponIMOnuFWControlUpDownFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				upload(1),
				download(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuFWControl 7 }

		
--  *******.4.1.6296.**********.2.8
		-- *******.4.1.6296.**********.2.8
		sleV2EponIMOnuFWControlUserId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuFWControl 8 }

		
--  *******.4.1.6296.**********.2.9
		-- *******.4.1.6296.**********.2.9
		sleV2EponIMOnuFWControlPassword OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuFWControl 9 }

		
--  *******.4.1.6296.**********.2.10
		-- *******.4.1.6296.**********.2.10
		sleV2EponIMOnuFWControlFileName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuFWControl 10 }

		
--  *******.4.1.6296.**********.2.11
		-- *******.4.1.6296.**********.2.11
		sleV2EponIMOnuFWControlUpdateCommitTime OBJECT-TYPE
			SYNTAX INTEGER (60..86400)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuFWControl 11 }

		
--  *******.4.1.6296.**********.3
		-- *******.4.1.6296.**********.3
		sleV2EponIMOnuFWNotification OBJECT IDENTIFIER ::= { sleV2EponIMOnuFW 3 }

		
--  *******.4.1.6296.**********.3.1
		-- *******.4.1.6296.**********.3.1
		sleV2EponIMOnuFWFirmwareCpSetted NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuFWControlRequest, sleV2EponIMOnuFWControlTimeStamp, sleV2EponIMOnuFWControlReqResult, sleV2EponIMOnuFWControlServerIp, sleV2EponIMOnuFWControlUpDownFlag, 
				sleV2EponIMOnuFWControlUserId, sleV2EponIMOnuFWControlPassword }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuFWNotification 1 }

		
--  *******.4.1.6296.**********.3.2
		-- *******.4.1.6296.**********.3.2
		sleV2EponIMOnuFWFirmwareDestroyed NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuFWControlRequest, sleV2EponIMOnuFWControlTimeStamp, sleV2EponIMOnuFWControlReqResult, sleV2EponIMOnuFWControlFileName }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuFWNotification 2 }

		
--  *******.4.1.6296.**********.3.3
		-- *******.4.1.6296.**********.3.3
		sleV2EponIMOnuFWFirmwareUpgradeSetted NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuFWControlRequest, sleV2EponIMOnuFWControlTimeStamp, sleV2EponIMOnuFWControlReqResult, sleV2EponIMOnuFWControlUpdateCommitTime, sleV2EponIMOnuFWControlFileName
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuFWNotification 3 }

		
--  *******.4.1.6296.**********.3.4
		-- *******.4.1.6296.**********.3.4
		sleV2EponIMOnuFWFirmwareCommitSetted NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuFWControlRequest, sleV2EponIMOnuFWControlTimeStamp, sleV2EponIMOnuFWControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuFWNotification 4 }

		
--   TAG : OnuBridge 
-- 
-- *******.4.1.6296.102.15.2
		-- *******.4.1.6296.102.15.2
		sleV2EponIMOnuBridge OBJECT IDENTIFIER ::= { sleV2EponIM 2 }

		
--  *******.4.1.6296.**********
		-- *******.4.1.6296.**********
		sleV2EponIMOnuPort OBJECT IDENTIFIER ::= { sleV2EponIMOnuBridge 1 }

		
		-- *******.4.1.6296.**********.1
		sleV2EponIMOnuPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2EponIMOnuPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPort 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleV2EponIMOnuPortEntry OBJECT-TYPE
			SYNTAX SleV2EponIMOnuPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2EponIMOnuPortIndex }
			::= { sleV2EponIMOnuPortTable 1 }

		
		SleV2EponIMOnuPortEntry ::=
			SEQUENCE { 
				sleV2EponIMOnuPortIndex
					INTEGER,
				sleV2EponIMOnuPortId
					Integer32,
				sleV2EponIMOnuPortType
					EponPortType,
				sleV2EponIMOnuPortAdminStatus
					EponOnuLinkStatus,
				sleV2EponIMOnuPortOperStatus
					EponOnuLinkStatus,
				sleV2EponIMOnuPortLinkupTime
					OCTET STRING,
				sleV2EponIMOnuPortUpTime
					TimeTicks,
				sleV2EponIMOnuPortFECtxEnable
					EponOnuState,
				sleV2EponIMOnuPortFECrxEnable
					EponOnuState,
				sleV2EponIMOnuPortLaserAlwaysOn
					EponOnuState,
				sleV2EponIMOnuPortDropInMcastTraffic
					EponOnuState,
				sleV2EponIMOnuPortDropInBcastTraffic
					EponOnuState,
				sleV2EponIMOnuPortBlockDataTraffic
					EponOnuState
			 }

--  *******.4.1.6296.**********.1.1.1
		-- *******.4.1.6296.**********.1.1.1
		sleV2EponIMOnuPortIndex OBJECT-TYPE
			SYNTAX INTEGER (1..512)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEntry 1 }

		
--  *******.4.1.6296.**********.1.1.2
		-- *******.4.1.6296.**********.1.1.2
		sleV2EponIMOnuPortId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEntry 2 }

		
--  *******.4.1.6296.**********.1.1.3
		-- *******.4.1.6296.**********.1.1.3
		sleV2EponIMOnuPortType OBJECT-TYPE
			SYNTAX EponPortType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEntry 3 }

		
--  *******.4.1.6296.**********.1.1.4
		-- *******.4.1.6296.**********.1.1.4
		sleV2EponIMOnuPortAdminStatus OBJECT-TYPE
			SYNTAX EponOnuLinkStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEntry 4 }

		
--  *******.4.1.6296.**********.1.1.5
		-- *******.4.1.6296.**********.1.1.5
		sleV2EponIMOnuPortOperStatus OBJECT-TYPE
			SYNTAX EponOnuLinkStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEntry 5 }

		
--  *******.4.1.6296.**********.1.1.6
		-- *******.4.1.6296.**********.1.1.6
		sleV2EponIMOnuPortLinkupTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEntry 6 }

		
--  *******.4.1.6296.**********.1.1.7
		-- *******.4.1.6296.**********.1.1.7
		sleV2EponIMOnuPortUpTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEntry 7 }

		
		-- *******.4.1.6296.**********.1.1.8
		sleV2EponIMOnuPortFECtxEnable OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortEntry 8 }

		
		-- *******.4.1.6296.**********.1.1.9
		sleV2EponIMOnuPortFECrxEnable OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortEntry 9 }

		
		-- *******.4.1.6296.**********.1.1.10
		sleV2EponIMOnuPortLaserAlwaysOn OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortEntry 10 }

		
		-- *******.4.1.6296.**********.1.1.11
		sleV2EponIMOnuPortDropInMcastTraffic OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortEntry 11 }

		
		-- *******.4.1.6296.**********.1.1.12
		sleV2EponIMOnuPortDropInBcastTraffic OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortEntry 12 }

		
		-- *******.4.1.6296.**********.1.1.13
		sleV2EponIMOnuPortBlockDataTraffic OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortEntry 13 }

		
--  *******.4.1.6296.**********.2
		-- *******.4.1.6296.**********.2
		sleV2EponIMOnuPortControl OBJECT IDENTIFIER ::= { sleV2EponIMOnuPort 2 }

		
--  *******.4.1.6296.**********.2.1
		-- *******.4.1.6296.**********.2.1
		sleV2EponIMOnuPortControlRequest OBJECT-TYPE
			SYNTAX INTEGER { changeAdminStatus(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortControl 1 }

		
--  *******.4.1.6296.**********.2.2
		-- *******.4.1.6296.**********.2.2
		sleV2EponIMOnuPortControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortControl 2 }

		
--  *******.4.1.6296.**********.2.3
		-- *******.4.1.6296.**********.2.3
		sleV2EponIMOnuPortControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortControl 3 }

		
--  *******.4.1.6296.**********.2.4
		-- *******.4.1.6296.**********.2.4
		sleV2EponIMOnuPortControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortControl 4 }

		
--  *******.4.1.6296.**********.2.5
		-- *******.4.1.6296.**********.2.5
		sleV2EponIMOnuPortControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortControl 5 }

		
--  *******.4.1.6296.**********.2.6
		-- *******.4.1.6296.**********.2.6
		sleV2EponIMOnuPortControlAdminState OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortControl 6 }

		
--  *******.4.1.6296.**********.3
		-- *******.4.1.6296.**********.3
		sleV2EponIMOnuPortNotification OBJECT IDENTIFIER ::= { sleV2EponIMOnuPort 3 }

		
--  *******.4.1.6296.**********.3.1
		-- *******.4.1.6296.**********.3.1
		sleV2EponIMOnuPortAdminStateChanged NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuPortControlRequest, sleV2EponIMOnuPortControlTimeStamp, sleV2EponIMOnuPortControlReqResult, sleV2EponIMOnuPortAdminStatus }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuPortNotification 1 }

		
--  *******.4.1.6296.**********
		-- *******.4.1.6296.**********
		sleV2EponIMOnuBridgeBase OBJECT IDENTIFIER ::= { sleV2EponIMOnuBridge 2 }

		
--  *******.4.1.6296.**********.1
		-- *******.4.1.6296.**********.1
		sleV2EponIMOnuBridgeInfo OBJECT IDENTIFIER ::= { sleV2EponIMOnuBridgeBase 1 }

		
--  *******.4.1.6296.**********.1.1
		-- *******.4.1.6296.**********.1.1
		sleV2EponIMOnuRxFlowCtrl OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeInfo 1 }

		
--  *******.4.1.6296.**********.1.2
		-- *******.4.1.6296.**********.1.2
		sleV2EponIMOnuTxFlowCtrl OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeInfo 2 }

		
--  *******.4.1.6296.**********.1.3
		-- *******.4.1.6296.**********.1.3
		sleV2EponIMOnuFlowCtrlLowThreshold OBJECT-TYPE
			SYNTAX INTEGER (0..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeInfo 3 }

		
--  *******.4.1.6296.**********.1.4
		-- *******.4.1.6296.**********.1.4
		sleV2EponIMOnuFlowCtrlHighThreshold OBJECT-TYPE
			SYNTAX INTEGER (0..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeInfo 4 }

		
--  *******.4.1.6296.**********.2
		-- *******.4.1.6296.**********.2
		sleV2EponIMOnuBridgeControl OBJECT IDENTIFIER ::= { sleV2EponIMOnuBridgeBase 2 }

		
--  *******.4.1.6296.**********.2.1
		-- *******.4.1.6296.**********.2.1
		sleV2EponIMOnuBridgeControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setFlowControl(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeControl 1 }

		
--  *******.4.1.6296.**********.2.2
		-- *******.4.1.6296.**********.2.2
		sleV2EponIMOnuBridgeControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				".
				"
			::= { sleV2EponIMOnuBridgeControl 2 }

		
--  *******.4.1.6296.**********.2.3
		-- *******.4.1.6296.**********.2.3
		sleV2EponIMOnuBridgeControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				".
				"
			::= { sleV2EponIMOnuBridgeControl 3 }

		
--  *******.4.1.6296.**********.2.4
		-- *******.4.1.6296.**********.2.4
		sleV2EponIMOnuBridgeControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				".."
			::= { sleV2EponIMOnuBridgeControl 4 }

		
--  *******.4.1.6296.**********.2.5
		-- *******.4.1.6296.**********.2.5
		sleV2EponIMOnuBridgeControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeControl 5 }

		
--  *******.4.1.6296.**********.2.6
		-- *******.4.1.6296.**********.2.6
		sleV2EponIMOnuBridgeControlRxFlowCtrl OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeControl 6 }

		
--  *******.4.1.6296.**********.2.7
		-- *******.4.1.6296.**********.2.7
		sleV2EponIMOnuBridgeControlTxFlowCtrl OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeControl 7 }

		
--  *******.4.1.6296.**********.2.8
		-- *******.4.1.6296.**********.2.8
		sleV2EponIMOnuBridgeControlFlowCtrlLowThreshold OBJECT-TYPE
			SYNTAX INTEGER (0..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeControl 8 }

		
--  *******.4.1.6296.**********.2.9
		-- *******.4.1.6296.**********.2.9
		sleV2EponIMOnuBridgeControlFlowCtrlHighThreshold OBJECT-TYPE
			SYNTAX INTEGER (0..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuBridgeControl 9 }

		
--  *******.4.1.6296.**********.3
		-- *******.4.1.6296.**********.3
		sleV2EponIMOnuBridgeNotification OBJECT IDENTIFIER ::= { sleV2EponIMOnuBridgeBase 3 }

		
--  *******.4.1.6296.**********.3.1
		-- *******.4.1.6296.**********.3.1
		sleV2EponIMOnuBridgeFlowCtrlChagned NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuBridgeControlRequest, sleV2EponIMOnuBridgeControlTimeStamp, sleV2EponIMOnuBridgeControlReqResult, sleV2EponIMOnuRxFlowCtrl, sleV2EponIMOnuTxFlowCtrl, 
				sleV2EponIMOnuFlowCtrlLowThreshold, sleV2EponIMOnuFlowCtrlHighThreshold }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuBridgeNotification 1 }

		
--    
-- TAG : OnuStats 
--  
-- *******.4.1.6296.102.15.3
		-- *******.4.1.6296.102.15.3
		sleV2EponIMOnuStats OBJECT IDENTIFIER ::= { sleV2EponIM 3 }

		
--  *******.4.1.6296.**********
		-- *******.4.1.6296.**********
		sleV2EponIMOnuPortStats OBJECT IDENTIFIER ::= { sleV2EponIMOnuStats 1 }

		
		-- *******.4.1.6296.**********.1
		sleV2EponIMOnuPortIfStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2EponIMOnuPortIfStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortStats 1 }

		
		-- *******.4.1.6296.**********.1.1
		sleV2EponIMOnuPortIfStatsEntry OBJECT-TYPE
			SYNTAX SleV2EponIMOnuPortIfStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2EponIMOnuPortIndex }
			::= { sleV2EponIMOnuPortIfStatsTable 1 }

		
		SleV2EponIMOnuPortIfStatsEntry ::=
			SEQUENCE { 
				sleV2EponIMOnuPortIfInOctets
					Counter64,
				sleV2EponIMOnuPortIfInUnicast
					Counter64,
				sleV2EponIMOnuPortIfInMulticast
					Counter64,
				sleV2EponIMOnuPortIfInBroadcast
					Counter64,
				sleV2EponIMOnuPortIfInDiscards
					Counter64,
				sleV2EponIMOnuPortIfInErrors
					Counter64,
				sleV2EponIMOnuPortIfInUnknownProtos
					Counter64,
				sleV2EponIMOnuPortIfOutOctets
					Counter64,
				sleV2EponIMOnuPortIfOutUnicast
					Counter64,
				sleV2EponIMOnuPortIfOutMulticast
					Counter64,
				sleV2EponIMOnuPortIfOutBroadcast
					Counter64,
				sleV2EponIMOnuPortIfOutDiscards
					Counter64,
				sleV2EponIMOnuPortIfOutErrors
					Counter64
			 }

--  *******.4.1.6296.**********.1.1.1
		-- *******.4.1.6296.**********.1.1.1
		sleV2EponIMOnuPortIfInOctets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 1 }

		
--  *******.4.1.6296.**********.1.1.2
		-- *******.4.1.6296.**********.1.1.2
		sleV2EponIMOnuPortIfInUnicast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 2 }

		
--  *******.4.1.6296.**********.1.1.3
		-- *******.4.1.6296.**********.1.1.3
		sleV2EponIMOnuPortIfInMulticast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 3 }

		
--  *******.4.1.6296.**********.1.1.4
		-- *******.4.1.6296.**********.1.1.4
		sleV2EponIMOnuPortIfInBroadcast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 4 }

		
--  *******.4.1.6296.**********.1.1.5
		-- *******.4.1.6296.**********.1.1.5
		sleV2EponIMOnuPortIfInDiscards OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 5 }

		
--  *******.4.1.6296.**********.1.1.6
		-- *******.4.1.6296.**********.1.1.6
		sleV2EponIMOnuPortIfInErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 6 }

		
--  *******.4.1.6296.**********.1.1.7
		-- *******.4.1.6296.**********.1.1.7
		sleV2EponIMOnuPortIfInUnknownProtos OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 7 }

		
--  *******.4.1.6296.**********.1.1.8
		-- *******.4.1.6296.**********.1.1.8
		sleV2EponIMOnuPortIfOutOctets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 8 }

		
--  *******.4.1.6296.**********.1.1.9
		-- *******.4.1.6296.**********.1.1.9
		sleV2EponIMOnuPortIfOutUnicast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 9 }

		
--  *******.4.1.6296.**********.1.1.10
		-- *******.4.1.6296.**********.1.1.10
		sleV2EponIMOnuPortIfOutMulticast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 10 }

		
--  *******.4.1.6296.**********.1.1.11
		-- *******.4.1.6296.**********.1.1.11
		sleV2EponIMOnuPortIfOutBroadcast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 11 }

		
--  *******.4.1.6296.**********.1.1.12
		-- *******.4.1.6296.**********.1.1.12
		sleV2EponIMOnuPortIfOutDiscards OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 12 }

		
--  *******.4.1.6296.**********.1.1.13
		-- *******.4.1.6296.**********.1.1.13
		sleV2EponIMOnuPortIfOutErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortIfStatsEntry 13 }

		
		-- *******.4.1.6296.**********.2
		sleV2EponIMOnuPortEtherStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2EponIMOnuPortEtherStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortStats 2 }

		
		-- *******.4.1.6296.**********.2.1
		sleV2EponIMOnuPortEtherStatsEntry OBJECT-TYPE
			SYNTAX SleV2EponIMOnuPortEtherStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2EponIMOnuPortIndex }
			::= { sleV2EponIMOnuPortEtherStatsTable 1 }

		
		SleV2EponIMOnuPortEtherStatsEntry ::=
			SEQUENCE { 
				sleV2EponIMOnuPortEtherAlignmentErrors
					Counter64,
				sleV2EponIMOnuPortEtherFcsErrors
					Counter64,
				sleV2EponIMOnuPortEtherSingleCollision
					Counter32,
				sleV2EponIMOnuPortEtherMultipleCollision
					Counter32,
				sleV2EponIMOnuPortEtherSqeTestErrors
					Counter32,
				sleV2EponIMOnuPortEtherDeferredTransmissions
					Counter32,
				sleV2EponIMOnuPortEtherLateCollisions
					Counter32,
				sleV2EponIMOnuPortEtherExcessiveCollisions
					Counter32,
				sleV2EponIMOnuPortEtherInternalMacTxErrors
					Counter64,
				sleV2EponIMOnuPortEtherCarrierSenseErrors
					Counter32,
				sleV2EponIMOnuPortEtherFrameTooLongs
					Counter64,
				sleV2EponIMOnuPortEtherInternalMacRxErrors
					Counter64,
				sleV2EponIMOnuPortEtherSymbolErrors
					Counter64,
				sleV2EponIMOnuPortEtherInUnknownOpcode
					Counter64,
				sleV2EponIMOnuPortEtherInPauseFrames
					Counter64,
				sleV2EponIMOnuPortEtherOutPauseFrames
					Counter64
			 }

--  *******.4.1.6296.**********.2.1.1
		-- *******.4.1.6296.**********.2.1.1
		sleV2EponIMOnuPortEtherAlignmentErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 1 }

		
--  *******.4.1.6296.**********.2.1.2
		-- *******.4.1.6296.**********.2.1.2
		sleV2EponIMOnuPortEtherFcsErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 2 }

		
--  *******.4.1.6296.**********.2.1.3
		-- *******.4.1.6296.**********.2.1.3
		sleV2EponIMOnuPortEtherSingleCollision OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 3 }

		
--  *******.4.1.6296.**********.2.1.4
		-- *******.4.1.6296.**********.2.1.4
		sleV2EponIMOnuPortEtherMultipleCollision OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 4 }

		
--  *******.4.1.6296.**********.2.1.5
		-- *******.4.1.6296.**********.2.1.5
		sleV2EponIMOnuPortEtherSqeTestErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 5 }

		
--  *******.4.1.6296.**********.2.1.6
		-- *******.4.1.6296.**********.2.1.6
		sleV2EponIMOnuPortEtherDeferredTransmissions OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 6 }

		
--  *******.4.1.6296.**********.2.1.7
		-- *******.4.1.6296.**********.2.1.7
		sleV2EponIMOnuPortEtherLateCollisions OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 7 }

		
--  *******.4.1.6296.**********.2.1.8
		-- *******.4.1.6296.**********.2.1.8
		sleV2EponIMOnuPortEtherExcessiveCollisions OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 8 }

		
--  *******.4.1.6296.**********.2.1.9
		-- *******.4.1.6296.**********.2.1.9
		sleV2EponIMOnuPortEtherInternalMacTxErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 9 }

		
--  *******.4.1.6296.**********.2.1.10
		-- *******.4.1.6296.**********.2.1.10
		sleV2EponIMOnuPortEtherCarrierSenseErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 10 }

		
--  *******.4.1.6296.**********.2.1.11
		-- *******.4.1.6296.**********.2.1.11
		sleV2EponIMOnuPortEtherFrameTooLongs OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 11 }

		
--  *******.4.1.6296.**********.2.1.12
		-- *******.4.1.6296.**********.2.1.12
		sleV2EponIMOnuPortEtherInternalMacRxErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 12 }

		
--  *******.4.1.6296.**********.2.1.13
		-- *******.4.1.6296.**********.2.1.13
		sleV2EponIMOnuPortEtherSymbolErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 13 }

		
--  *******.4.1.6296.**********.2.1.14
		-- *******.4.1.6296.**********.2.1.14
		sleV2EponIMOnuPortEtherInUnknownOpcode OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 14 }

		
--  *******.4.1.6296.**********.2.1.15
		-- *******.4.1.6296.**********.2.1.15
		sleV2EponIMOnuPortEtherInPauseFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 15 }

		
--  *******.4.1.6296.**********.2.1.16
		-- *******.4.1.6296.**********.2.1.16
		sleV2EponIMOnuPortEtherOutPauseFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEtherStatsEntry 16 }

		
		-- *******.4.1.6296.**********.3
		sleV2EponIMOnuPortEponStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2EponIMOnuPortEponStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortStats 3 }

		
		-- *******.4.1.6296.**********.3.1
		sleV2EponIMOnuPortEponStatsEntry OBJECT-TYPE
			SYNTAX SleV2EponIMOnuPortEponStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2EponIMOnuPortIndex }
			::= { sleV2EponIMOnuPortEponStatsTable 1 }

		
		SleV2EponIMOnuPortEponStatsEntry ::=
			SEQUENCE { 
				sleV2EponIMOnuPortEponMacCtrlFrameRx
					Counter32,
				sleV2EponIMOnuPortEponDiscWindowsSent
					Counter32,
				sleV2EponIMOnuPortEponDiscTimeout
					Counter32,
				sleV2EponIMOnuPortEponTxRegRequest
					Counter32,
				sleV2EponIMOnuPortEponRxReqRequest
					Counter32,
				sleV2EponIMOnuPortEponTxReqAck
					Counter32,
				sleV2EponIMOnuPortEponRxReqAck
					Counter32,
				sleV2EponIMOnuPortEponTxReport
					Counter32,
				sleV2EponIMOnuPortEponRxReport
					Counter32,
				sleV2EponIMOnuPortEponTxGate
					Counter32,
				sleV2EponIMOnuPortEponRxGate
					Counter32,
				sleV2EponIMOnuPortEponTxRegister
					Counter32,
				sleV2EponIMOnuPortEponRxRegister
					Counter32,
				sleV2EponIMOnuPortEponRxNotSupported
					Counter32,
				sleV2EponIMOnuPortEponSldErrors
					Counter32,
				sleV2EponIMOnuPortEponCrc8Errors
					Counter32,
				sleV2EponIMOnuPortEponBadLlid
					Counter32,
				sleV2EponIMOnuPortEponGoodLlid
					Counter32,
				sleV2EponIMOnuPortEponOnuPonCastLlid
					Counter32,
				sleV2EponIMOnuPortEponOltPonCastLlid
					Counter32,
				sleV2EponIMOnuPortEponBcastLlidNotOnuId
					Counter32,
				sleV2EponIMOnuPortEponOnuLlidNotBcast
					Counter32,
				sleV2EponIMOnuPortEponBcastLlidPlusOnuId
					Counter32,
				sleV2EponIMOnuPortEponNotBcastLlidNotOnuId
					Counter32,
				sleV2EponIMOnuPortEponPcsCodingViolation
					Counter32,
				sleV2EponIMOnuPortEponFecCorrectedBlocks
					Counter32,
				sleV2EponIMOnuPortEponFecUnCorrectedBlocks
					Counter32,
				sleV2EponIMOnuPortEponBufferHeadCodingViolation
					Counter32
			 }

--  *******.4.1.6296.**********.3.1.1
		-- *******.4.1.6296.**********.3.1.1
		sleV2EponIMOnuPortEponMacCtrlFrameRx OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 1 }

		
--  *******.4.1.6296.**********.3.1.2
		-- *******.4.1.6296.**********.3.1.2
		sleV2EponIMOnuPortEponDiscWindowsSent OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 2 }

		
--  *******.4.1.6296.**********.3.1.3
		-- *******.4.1.6296.**********.3.1.3
		sleV2EponIMOnuPortEponDiscTimeout OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 3 }

		
--  *******.4.1.6296.**********.3.1.4
		-- *******.4.1.6296.**********.3.1.4
		sleV2EponIMOnuPortEponTxRegRequest OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 4 }

		
--  *******.4.1.6296.**********.3.1.5
		-- *******.4.1.6296.**********.3.1.5
		sleV2EponIMOnuPortEponRxReqRequest OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 5 }

		
--  *******.4.1.6296.**********.3.1.6
		-- *******.4.1.6296.**********.3.1.6
		sleV2EponIMOnuPortEponTxReqAck OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 6 }

		
--  *******.4.1.6296.**********.3.1.7
		-- *******.4.1.6296.**********.3.1.7
		sleV2EponIMOnuPortEponRxReqAck OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 7 }

		
--  *******.4.1.6296.**********.3.1.8
		-- *******.4.1.6296.**********.3.1.8
		sleV2EponIMOnuPortEponTxReport OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 8 }

		
--  *******.4.1.6296.**********.3.1.9
		-- *******.4.1.6296.**********.3.1.9
		sleV2EponIMOnuPortEponRxReport OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 9 }

		
--  *******.4.1.6296.**********.3.1.10
		-- *******.4.1.6296.**********.3.1.10
		sleV2EponIMOnuPortEponTxGate OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 10 }

		
--  *******.4.1.6296.**********.3.1.11
		-- *******.4.1.6296.**********.3.1.11
		sleV2EponIMOnuPortEponRxGate OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 11 }

		
--  *******.4.1.6296.**********.3.1.12
		-- *******.4.1.6296.**********.3.1.12
		sleV2EponIMOnuPortEponTxRegister OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 12 }

		
--  *******.4.1.6296.**********.3.1.13
		-- *******.4.1.6296.**********.3.1.13
		sleV2EponIMOnuPortEponRxRegister OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 13 }

		
--  *******.4.1.6296.**********.3.1.14
		-- *******.4.1.6296.**********.3.1.14
		sleV2EponIMOnuPortEponRxNotSupported OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 14 }

		
--  *******.4.1.6296.**********.3.1.15
		-- *******.4.1.6296.**********.3.1.15
		sleV2EponIMOnuPortEponSldErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 15 }

		
--  *******.4.1.6296.**********.3.1.16
		-- *******.4.1.6296.**********.3.1.16
		sleV2EponIMOnuPortEponCrc8Errors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 16 }

		
--  *******.4.1.6296.**********.3.1.17
		-- *******.4.1.6296.**********.3.1.17
		sleV2EponIMOnuPortEponBadLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 17 }

		
--  *******.4.1.6296.**********.3.1.18
		-- *******.4.1.6296.**********.3.1.18
		sleV2EponIMOnuPortEponGoodLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 18 }

		
--  *******.4.1.6296.**********.3.1.19
		-- *******.4.1.6296.**********.3.1.19
		sleV2EponIMOnuPortEponOnuPonCastLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 19 }

		
--  *******.4.1.6296.**********.3.1.20
		-- *******.4.1.6296.**********.3.1.20
		sleV2EponIMOnuPortEponOltPonCastLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 20 }

		
--  *******.4.1.6296.**********.3.1.21
		-- *******.4.1.6296.**********.3.1.21
		sleV2EponIMOnuPortEponBcastLlidNotOnuId OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 21 }

		
--  *******.4.1.6296.**********.3.1.22
		-- *******.4.1.6296.**********.3.1.22
		sleV2EponIMOnuPortEponOnuLlidNotBcast OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 22 }

		
--  *******.4.1.6296.**********.3.1.23
		-- *******.4.1.6296.**********.3.1.23
		sleV2EponIMOnuPortEponBcastLlidPlusOnuId OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 23 }

		
--  *******.4.1.6296.**********.3.1.24
		-- *******.4.1.6296.**********.3.1.24
		sleV2EponIMOnuPortEponNotBcastLlidNotOnuId OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 24 }

		
--  *******.4.1.6296.**********.3.1.25
		-- *******.4.1.6296.**********.3.1.25
		sleV2EponIMOnuPortEponPcsCodingViolation OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 25 }

		
--  *******.4.1.6296.**********.3.1.26
		-- *******.4.1.6296.**********.3.1.26
		sleV2EponIMOnuPortEponFecCorrectedBlocks OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 26 }

		
--  *******.4.1.6296.**********.3.1.27
		-- *******.4.1.6296.**********.3.1.27
		sleV2EponIMOnuPortEponFecUnCorrectedBlocks OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 27 }

		
--  *******.4.1.6296.**********.3.1.28
		-- *******.4.1.6296.**********.3.1.28
		sleV2EponIMOnuPortEponBufferHeadCodingViolation OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortEponStatsEntry 28 }

		
--  *******.4.1.6296.**********.4
		-- *******.4.1.6296.**********.4
		sleV2EponIMOnuPortStatsControl OBJECT IDENTIFIER ::= { sleV2EponIMOnuPortStats 4 }

		
--  *******.4.1.6296.**********.4.1
		-- *******.4.1.6296.**********.4.1
		sleV2EponIMOnuPortStatsControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearPortStats(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortStatsControl 1 }

		
--  *******.4.1.6296.**********.4.2
		-- *******.4.1.6296.**********.4.2
		sleV2EponIMOnuPortStatsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortStatsControl 2 }

		
--  *******.4.1.6296.**********.4.3
		-- *******.4.1.6296.**********.4.3
		sleV2EponIMOnuPortStatsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortStatsControl 3 }

		
--  *******.4.1.6296.**********.4.4
		-- *******.4.1.6296.**********.4.4
		sleV2EponIMOnuPortStatsControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortStatsControl 4 }

		
--  *******.4.1.6296.**********.4.5
		-- *******.4.1.6296.**********.4.5
		sleV2EponIMOnuPortStatsControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuPortStatsControl 5 }

		
--  *******.4.1.6296.**********.4.6
		-- *******.4.1.6296.**********.4.6
		sleV2EponIMOnuPortStatsControlPortIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMOnuPortStatsControl 6 }

		
--  *******.4.1.6296.**********.5
		-- *******.4.1.6296.**********.5
		sleV2EponIMOnuPortStatsNotification OBJECT IDENTIFIER ::= { sleV2EponIMOnuPortStats 5 }

		
--  *******.4.1.6296.**********.5.1
		-- *******.4.1.6296.**********.5.1
		sleV2EponIMOnuPortStatsCleared NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuPortStatsControlRequest, sleV2EponIMOnuPortStatsControlTimeStamp, sleV2EponIMOnuPortStatsControlReqResult, sleV2EponIMOnuPortStatsControlPortIndex }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuPortStatsNotification 1 }

		
--  *******.4.1.6296.**********
		-- *******.4.1.6296.**********
		sleV2EponIMOnuLlidStats OBJECT IDENTIFIER ::= { sleV2EponIMOnuStats 2 }

		
--  *******.4.1.6296.**********.1
		-- *******.4.1.6296.**********.1
		sleV2EponIMOnuLlidIfStats OBJECT IDENTIFIER ::= { sleV2EponIMOnuLlidStats 1 }

		
--  *******.4.1.6296.**********.1.1
		-- *******.4.1.6296.**********.1.1
		sleV2EponIMOnuLlidIfInOctets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 1 }

		
--  *******.4.1.6296.**********.1.2
		-- *******.4.1.6296.**********.1.2
		sleV2EponIMOnuLlidIfInUnicast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 2 }

		
--  *******.4.1.6296.**********.1.3
		-- *******.4.1.6296.**********.1.3
		sleV2EponIMOnuLlidIfInMulticast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 3 }

		
--  *******.4.1.6296.**********.1.4
		-- *******.4.1.6296.**********.1.4
		sleV2EponIMOnuLlidIfInBroadcast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 4 }

		
--  *******.4.1.6296.**********.1.5
		-- *******.4.1.6296.**********.1.5
		sleV2EponIMOnuLlidIfInDiscards OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 5 }

		
--  *******.4.1.6296.**********.1.6
		-- *******.4.1.6296.**********.1.6
		sleV2EponIMOnuLlidIfInErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 6 }

		
--  *******.4.1.6296.**********.1.7
		-- *******.4.1.6296.**********.1.7
		sleV2EponIMOnuLlidIfInUnknownProtos OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 7 }

		
--  *******.4.1.6296.**********.1.8
		-- *******.4.1.6296.**********.1.8
		sleV2EponIMOnuLlidIfOutOctets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 8 }

		
--  *******.4.1.6296.**********.1.9
		-- *******.4.1.6296.**********.1.9
		sleV2EponIMOnuLlidIfOutUnicast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 9 }

		
--  *******.4.1.6296.**********.1.10
		-- *******.4.1.6296.**********.1.10
		sleV2EponIMOnuLlidIfOutMulticast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 10 }

		
--  *******.4.1.6296.**********.1.11
		-- *******.4.1.6296.**********.1.11
		sleV2EponIMOnuLlidIfOutBroadcast OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 11 }

		
--  *******.4.1.6296.**********.1.12
		-- *******.4.1.6296.**********.1.12
		sleV2EponIMOnuLlidIfOutDiscards OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 12 }

		
--  *******.4.1.6296.**********.1.13
		-- *******.4.1.6296.**********.1.13
		sleV2EponIMOnuLlidIfOutErrors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidIfStats 13 }

		
--  *******.4.1.6296.**********.2
		-- *******.4.1.6296.**********.2
		sleV2EponIMOnuLlidEponStats OBJECT IDENTIFIER ::= { sleV2EponIMOnuLlidStats 2 }

		
--  *******.4.1.6296.**********.2.1
		-- *******.4.1.6296.**********.2.1
		sleV2EponIMOnuLlidEponMacCtrlFrameRx OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 1 }

		
--  *******.4.1.6296.**********.2.2
		-- *******.4.1.6296.**********.2.2
		sleV2EponIMOnuLlidEponDiscWindowsSent OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 2 }

		
--  *******.4.1.6296.**********.2.3
		-- *******.4.1.6296.**********.2.3
		sleV2EponIMOnuLlidEponDiscTimeout OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 3 }

		
--  *******.4.1.6296.**********.2.4
		-- *******.4.1.6296.**********.2.4
		sleV2EponIMOnuLlidEponTxRegRequest OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 4 }

		
--  *******.4.1.6296.**********.2.5
		-- *******.4.1.6296.**********.2.5
		sleV2EponIMOnuLlidEponRxReqRequest OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 5 }

		
--  *******.4.1.6296.**********.2.6
		-- *******.4.1.6296.**********.2.6
		sleV2EponIMOnuLlidEponTxReqAck OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 6 }

		
--  *******.4.1.6296.**********.2.7
		-- *******.4.1.6296.**********.2.7
		sleV2EponIMOnuLlidEponRxReqAck OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 7 }

		
--  *******.4.1.6296.**********.2.8
		-- *******.4.1.6296.**********.2.8
		sleV2EponIMOnuLlidEponTxReport OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 8 }

		
--  *******.4.1.6296.**********.2.9
		-- *******.4.1.6296.**********.2.9
		sleV2EponIMOnuLlidEponRxReport OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 9 }

		
--  *******.4.1.6296.**********.2.10
		-- *******.4.1.6296.**********.2.10
		sleV2EponIMOnuLlidEponTxGate OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 10 }

		
--  *******.4.1.6296.**********.2.11
		-- *******.4.1.6296.**********.2.11
		sleV2EponIMOnuLlidEponRxGate OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 11 }

		
--  *******.4.1.6296.**********.2.12
		-- *******.4.1.6296.**********.2.12
		sleV2EponIMOnuLlidEponTxRegister OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 12 }

		
--  *******.4.1.6296.**********.2.13
		-- *******.4.1.6296.**********.2.13
		sleV2EponIMOnuLlidEponRxRegister OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 13 }

		
--  *******.4.1.6296.**********.2.14
		-- *******.4.1.6296.**********.2.14
		sleV2EponIMOnuLlidEponRxNotSupported OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 14 }

		
--  *******.4.1.6296.**********.2.15
		-- *******.4.1.6296.**********.2.15
		sleV2EponIMOnuLlidEponSldErrors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 15 }

		
--  *******.4.1.6296.**********.2.16
		-- *******.4.1.6296.**********.2.16
		sleV2EponIMOnuLlidEponCrc8Errors OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 16 }

		
--  *******.4.1.6296.**********.2.17
		-- *******.4.1.6296.**********.2.17
		sleV2EponIMOnuLlidEponBadLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 17 }

		
--  *******.4.1.6296.**********.2.18
		-- *******.4.1.6296.**********.2.18
		sleV2EponIMOnuLlidEponGoodLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 18 }

		
--  *******.4.1.6296.**********.2.19
		-- *******.4.1.6296.**********.2.19
		sleV2EponIMOnuLlidEponOnuPonCastLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 19 }

		
--  *******.4.1.6296.**********.2.20
		-- *******.4.1.6296.**********.2.20
		sleV2EponIMOnuLlidEponOltPonCastLlid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 20 }

		
--  *******.4.1.6296.**********.2.21
		-- *******.4.1.6296.**********.2.21
		sleV2EponIMOnuLlidEponBcastLlidNotOnuId OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 21 }

		
--  *******.4.1.6296.**********.2.22
		-- *******.4.1.6296.**********.2.22
		sleV2EponIMOnuLlidEponOnuLlidNotBcast OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 22 }

		
--  *******.4.1.6296.**********.2.23
		-- *******.4.1.6296.**********.2.23
		sleV2EponIMOnuLlidEponBcastLlidPlusOnuId OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 23 }

		
--  *******.4.1.6296.**********.2.24
		-- *******.4.1.6296.**********.2.24
		sleV2EponIMOnuLlidEponNotBcastLlidNotOnuId OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 24 }

		
--  *******.4.1.6296.**********.2.25
		-- *******.4.1.6296.**********.2.25
		sleV2EponIMOnuLlidEponPcsCodingViolation OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 25 }

		
--  *******.4.1.6296.**********.2.26
		-- *******.4.1.6296.**********.2.26
		sleV2EponIMOnuLlidEponFecCorrectedBlocks OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 26 }

		
--  *******.4.1.6296.**********.2.27
		-- *******.4.1.6296.**********.2.27
		sleV2EponIMOnuLlidEponFecUnCorrectedBlocks OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 27 }

		
--  *******.4.1.6296.**********.2.28
		-- *******.4.1.6296.**********.2.28
		sleV2EponIMOnuLlidEponBufferHeadCodingViolation OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidEponStats 28 }

		
--  *******.4.1.6296.**********.3
		-- *******.4.1.6296.**********.3
		sleV2EponIMOnuLlidStatsControl OBJECT IDENTIFIER ::= { sleV2EponIMOnuLlidStats 3 }

		
--  *******.4.1.6296.**********.3.1
		-- *******.4.1.6296.**********.3.1
		sleV2EponIMOnuLlidStatsControlRequest OBJECT-TYPE
			SYNTAX INTEGER { clearPortStats(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidStatsControl 1 }

		
--  *******.4.1.6296.**********.3.2
		-- *******.4.1.6296.**********.3.2
		sleV2EponIMOnuLlidStatsControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidStatsControl 2 }

		
--  *******.4.1.6296.**********.3.3
		-- *******.4.1.6296.**********.3.3
		sleV2EponIMOnuLlidStatsControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidStatsControl 3 }

		
--  *******.4.1.6296.**********.3.4
		-- *******.4.1.6296.**********.3.4
		sleV2EponIMOnuLlidStatsControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidStatsControl 4 }

		
--  *******.4.1.6296.**********.3.5
		-- *******.4.1.6296.**********.3.5
		sleV2EponIMOnuLlidStatsControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"."
			::= { sleV2EponIMOnuLlidStatsControl 5 }

		
--  *******.4.1.6296.**********.4
		-- *******.4.1.6296.**********.4
		sleV2EponIMOnuLlidStatsNotification OBJECT IDENTIFIER ::= { sleV2EponIMOnuLlidStats 4 }

		
--  *******.4.1.6296.**********.4.1
		-- *******.4.1.6296.**********.4.1
		sleV2EponIMOnuLlidStatsCleared NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMOnuLlidStatsControlRequest, sleV2EponIMOnuLlidStatsControlTimeStamp, sleV2EponIMOnuLlidStatsControlReqResult }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMOnuLlidStatsNotification 1 }

		
		-- *******.4.1.6296.102.15.4
		sleV2EponIMAlarm OBJECT IDENTIFIER ::= { sleV2EponIM 4 }

		
		-- *******.4.1.6296.**********
		sleV2EponIMAlarmTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleV2EponIMAlarmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMAlarm 1 }

		
		-- *******.4.1.6296.**********.1
		sleV2EponIMAlarmEntry OBJECT-TYPE
			SYNTAX SleV2EponIMAlarmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleV2EponIMAlarmOnuPortIndex, sleV2EponIMAlarmOnuAlarmIndex }
			::= { sleV2EponIMAlarmTable 1 }

		
		SleV2EponIMAlarmEntry ::=
			SEQUENCE { 
				sleV2EponIMAlarmOnuPortIndex
					INTEGER,
				sleV2EponIMAlarmOnuAlarmIndex
					INTEGER,
				sleV2EponIMAlarmOnuAlarmStatus
					EponOnuState,
				sleV2EponIMAlarmOnuWindow
					Integer32,
				sleV2EponIMAlarmOnuThreshold
					Integer32
			 }

		-- *******.4.1.6296.**********.1.1
		sleV2EponIMAlarmOnuPortIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				pon(1),
				uni(2),
				llid(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU port, which is valid only for port specific alarms."
			::= { sleV2EponIMAlarmEntry 1 }

		
		-- *******.4.1.6296.**********.1.2
		sleV2EponIMAlarmOnuAlarmIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				errFramePeriod(1),
				errFrame(2),
				errFrameSecond(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm type.
				err_frame_period - errored frame period alarm is raised when a threshold number of errored frame occur in the specified window time period.
				err_frame - errored frame alarm is raised when a threshold number of errored frames occur among the specified number of frames.
				err_frame_second - errored frame seconds summary alarm is raised when a threshold number of errored frames occur in the specified window time period.
				                   this alarm type is a summary of the errored frame period alarm.
				"
			::= { sleV2EponIMAlarmEntry 2 }

		
		-- *******.4.1.6296.**********.1.3
		sleV2EponIMAlarmOnuAlarmStatus OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"whether to enable alarm or not."
			::= { sleV2EponIMAlarmEntry 3 }

		
		-- *******.4.1.6296.**********.1.4
		sleV2EponIMAlarmOnuWindow OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the window during which the alarm threshold is measured."
			::= { sleV2EponIMAlarmEntry 4 }

		
		-- *******.4.1.6296.**********.1.5
		sleV2EponIMAlarmOnuThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the threshold valud leading to the alarm.
				"
			::= { sleV2EponIMAlarmEntry 5 }

		
		-- *******.4.1.6296.**********
		sleV2EponIMAlarmControl OBJECT IDENTIFIER ::= { sleV2EponIMAlarm 2 }

		
		-- *******.4.1.6296.**********.1
		sleV2EponIMAlarmControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setEponIMAlarmProfileChanged(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMAlarmControl 1 }

		
		-- *******.4.1.6296.**********.2
		sleV2EponIMAlarmControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMAlarmControl 2 }

		
		-- *******.4.1.6296.**********.3
		sleV2EponIMAlarmControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMAlarmControl 3 }

		
		-- *******.4.1.6296.**********.4
		sleV2EponIMAlarmControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMAlarmControl 4 }

		
		-- *******.4.1.6296.**********.5
		sleV2EponIMAlarmControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleV2EponIMAlarmControl 5 }

		
		-- *******.4.1.6296.**********.6
		sleV2EponIMAlarmControlOnuPortIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				pon(1),
				uni(2),
				llid(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ONU port, which is valid only for port specific alarms."
			::= { sleV2EponIMAlarmControl 6 }

		
		-- *******.4.1.6296.**********.7
		sleV2EponIMAlarmControlOnuAlarmIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				errFramePeriod(1),
				errFrame(2),
				errFrameSecond(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm type.
				err_frame_period - errored frame period alarm is raised when a threshold number of errored frame occur in the specified window time period.
				err_frame - errored frame alarm is raised when a threshold number of errored frames occur among the specified number of frames.
				err_frame_second - errored frame seconds summary alarm is raised when a threshold number of errored frames occur in the specified window time period.
				                   this alarm type is a summary of the errored frame period alarm.
				"
			::= { sleV2EponIMAlarmControl 7 }

		
		-- *******.4.1.6296.**********.8
		sleV2EponIMAlarmControlOnuAlarmStatus OBJECT-TYPE
			SYNTAX EponOnuState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"whether to enable alarm or not."
			::= { sleV2EponIMAlarmControl 8 }

		
		-- *******.4.1.6296.**********.9
		sleV2EponIMAlarmControlWindow OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the window during which the alarm threshold is measured."
			::= { sleV2EponIMAlarmControl 9 }

		
		-- *******.4.1.6296.**********.10
		sleV2EponIMAlarmControlThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the threshold valud leading to the alarm."
			::= { sleV2EponIMAlarmControl 10 }

		
		-- *******.4.1.6296.**********
		sleV2EponIMAlarmNotification OBJECT IDENTIFIER ::= { sleV2EponIMAlarm 4 }

		
		-- *******.4.1.6296.**********.1
		sleV2EponIMAlarmProfileChanged NOTIFICATION-TYPE
			OBJECTS { sleV2EponIMAlarmControlRequest, sleV2EponIMAlarmControlTimeStamp, sleV2EponIMAlarmControlReqResult, sleV2EponIMAlarmOnuAlarmStatus, sleV2EponIMAlarmOnuWindow, 
				sleV2EponIMAlarmOnuThreshold }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIMAlarmNotification 1 }

		
--  *******.4.1.6296.102.15.4
		-- *******.4.1.6296.102.15.5
		sleV2EponIMGroup OBJECT-GROUP
			OBJECTS { sleV2EponIMOnuConfigMask, sleV2EponIMOnuConfigData, sleV2EponIMOnuRegistered, sleV2EponIMOnuAuthenticated, sleV2EponIMOnuImageVersion, 
				sleV2EponIMOnuLoadVersion, sleV2EponIMOnuChipVersion, sleV2EponIMOnuSerialNumber, sleV2EponIMOnuRxOpticPower, sleV2EponIMRegInfoOltId, 
				sleV2EponIMRegInfoOltLlidPort, sleV2EponIMRegInfoOltPonPort, sleV2EponIMRegInfoOnuMacAddress, sleV2EponIMRegInfoOnuLlid, sleV2EponIMOnuId, 
				sleV2EponIMOnuSecret, sleV2EponIMOnuVendorCode, sleV2EponIMOnuAuthTimer, sleV2EponIMOnuAuthTimeoutTimer, sleV2EponIMOnuAuthRejectTimer, 
				sleV2EponIMOnuControlRequest, sleV2EponIMOnuControlStatus, sleV2EponIMOnuControlTimer, sleV2EponIMOnuControlTimeStamp, sleV2EponIMOnuControlReqResult, 
				sleV2EponIMOnuControlSecret, sleV2EponIMOnuControlOnuVendorCode, sleV2EponIMOnuControlAuthTimer, sleV2EponIMOnuControlAuthTimeoutTimer, sleV2EponIMOnuControlAuthRejectTimer, 
				sleV2EponIMOnuFWName, sleV2EponIMOnuFWControlRequest, sleV2EponIMOnuFWControlStatus, sleV2EponIMOnuFWControlTimer, sleV2EponIMOnuFWControlTimeStamp, 
				sleV2EponIMOnuFWControlReqResult, sleV2EponIMOnuFWControlServerIp, sleV2EponIMOnuFWControlUpDownFlag, sleV2EponIMOnuFWControlUserId, sleV2EponIMOnuFWControlPassword, 
				sleV2EponIMOnuFWControlFileName, sleV2EponIMOnuFWControlUpdateCommitTime, sleV2EponIMOnuPortIndex, sleV2EponIMOnuPortId, sleV2EponIMOnuPortType, 
				sleV2EponIMOnuPortAdminStatus, sleV2EponIMOnuPortOperStatus, sleV2EponIMOnuPortLinkupTime, sleV2EponIMOnuPortUpTime, sleV2EponIMOnuPortControlRequest, 
				sleV2EponIMOnuPortControlStatus, sleV2EponIMOnuPortControlTimer, sleV2EponIMOnuPortControlTimeStamp, sleV2EponIMOnuPortControlReqResult, sleV2EponIMOnuPortControlAdminState, 
				sleV2EponIMOnuRxFlowCtrl, sleV2EponIMOnuTxFlowCtrl, sleV2EponIMOnuFlowCtrlLowThreshold, sleV2EponIMOnuFlowCtrlHighThreshold, sleV2EponIMOnuBridgeControlRequest, 
				sleV2EponIMOnuBridgeControlStatus, sleV2EponIMOnuBridgeControlTimer, sleV2EponIMOnuBridgeControlTimeStamp, sleV2EponIMOnuBridgeControlReqResult, sleV2EponIMOnuBridgeControlRxFlowCtrl, 
				sleV2EponIMOnuBridgeControlTxFlowCtrl, sleV2EponIMOnuBridgeControlFlowCtrlLowThreshold, sleV2EponIMOnuBridgeControlFlowCtrlHighThreshold, sleV2EponIMOnuPortIfInOctets, sleV2EponIMOnuPortIfInUnicast, 
				sleV2EponIMOnuPortIfInMulticast, sleV2EponIMOnuPortIfInBroadcast, sleV2EponIMOnuPortIfInDiscards, sleV2EponIMOnuPortIfInErrors, sleV2EponIMOnuPortIfInUnknownProtos, 
				sleV2EponIMOnuPortIfOutOctets, sleV2EponIMOnuPortIfOutUnicast, sleV2EponIMOnuPortIfOutMulticast, sleV2EponIMOnuPortIfOutBroadcast, sleV2EponIMOnuPortIfOutDiscards, 
				sleV2EponIMOnuPortIfOutErrors, sleV2EponIMOnuPortEtherAlignmentErrors, sleV2EponIMOnuPortEtherFcsErrors, sleV2EponIMOnuPortEtherSingleCollision, sleV2EponIMOnuPortEtherMultipleCollision, 
				sleV2EponIMOnuPortEtherSqeTestErrors, sleV2EponIMOnuPortEtherDeferredTransmissions, sleV2EponIMOnuPortEtherLateCollisions, sleV2EponIMOnuPortEtherExcessiveCollisions, sleV2EponIMOnuPortEtherInternalMacTxErrors, 
				sleV2EponIMOnuPortEtherCarrierSenseErrors, sleV2EponIMOnuPortEtherFrameTooLongs, sleV2EponIMOnuPortEtherInternalMacRxErrors, sleV2EponIMOnuPortEtherSymbolErrors, sleV2EponIMOnuPortEtherInUnknownOpcode, 
				sleV2EponIMOnuPortEtherInPauseFrames, sleV2EponIMOnuPortEtherOutPauseFrames, sleV2EponIMOnuPortEponMacCtrlFrameRx, sleV2EponIMOnuPortEponDiscWindowsSent, sleV2EponIMOnuPortEponDiscTimeout, 
				sleV2EponIMOnuPortEponTxRegRequest, sleV2EponIMOnuPortEponRxReqRequest, sleV2EponIMOnuPortEponTxReqAck, sleV2EponIMOnuPortEponRxReqAck, sleV2EponIMOnuPortEponTxReport, 
				sleV2EponIMOnuPortEponRxReport, sleV2EponIMOnuPortEponTxGate, sleV2EponIMOnuPortEponRxGate, sleV2EponIMOnuPortEponTxRegister, sleV2EponIMOnuPortEponRxRegister, 
				sleV2EponIMOnuPortEponRxNotSupported, sleV2EponIMOnuPortEponSldErrors, sleV2EponIMOnuPortEponCrc8Errors, sleV2EponIMOnuPortEponBadLlid, sleV2EponIMOnuPortEponGoodLlid, 
				sleV2EponIMOnuPortEponOnuPonCastLlid, sleV2EponIMOnuPortEponOltPonCastLlid, sleV2EponIMOnuPortEponBcastLlidNotOnuId, sleV2EponIMOnuPortEponOnuLlidNotBcast, sleV2EponIMOnuPortEponBcastLlidPlusOnuId, 
				sleV2EponIMOnuPortEponNotBcastLlidNotOnuId, sleV2EponIMOnuPortEponPcsCodingViolation, sleV2EponIMOnuPortEponFecCorrectedBlocks, sleV2EponIMOnuPortEponFecUnCorrectedBlocks, sleV2EponIMOnuPortEponBufferHeadCodingViolation, 
				sleV2EponIMOnuPortStatsControlRequest, sleV2EponIMOnuPortStatsControlStatus, sleV2EponIMOnuPortStatsControlTimer, sleV2EponIMOnuPortStatsControlTimeStamp, sleV2EponIMOnuPortStatsControlReqResult, 
				sleV2EponIMOnuPortStatsControlPortIndex, sleV2EponIMOnuLlidIfInOctets, sleV2EponIMOnuLlidIfInUnicast, sleV2EponIMOnuLlidIfInMulticast, sleV2EponIMOnuLlidIfInBroadcast, 
				sleV2EponIMOnuLlidIfInDiscards, sleV2EponIMOnuLlidIfInErrors, sleV2EponIMOnuLlidIfInUnknownProtos, sleV2EponIMOnuLlidIfOutOctets, sleV2EponIMOnuLlidIfOutUnicast, 
				sleV2EponIMOnuLlidIfOutMulticast, sleV2EponIMOnuLlidIfOutBroadcast, sleV2EponIMOnuLlidIfOutDiscards, sleV2EponIMOnuLlidIfOutErrors, sleV2EponIMOnuLlidEponMacCtrlFrameRx, 
				sleV2EponIMOnuLlidEponDiscWindowsSent, sleV2EponIMOnuLlidEponDiscTimeout, sleV2EponIMOnuLlidEponTxRegRequest, sleV2EponIMOnuLlidEponRxReqRequest, sleV2EponIMOnuLlidEponTxReqAck, 
				sleV2EponIMOnuLlidEponRxReqAck, sleV2EponIMOnuLlidEponTxReport, sleV2EponIMOnuLlidEponRxReport, sleV2EponIMOnuLlidEponTxGate, sleV2EponIMOnuLlidEponRxGate, 
				sleV2EponIMOnuLlidEponTxRegister, sleV2EponIMOnuLlidEponRxRegister, sleV2EponIMOnuLlidEponRxNotSupported, sleV2EponIMOnuLlidEponSldErrors, sleV2EponIMOnuLlidEponCrc8Errors, 
				sleV2EponIMOnuLlidEponBadLlid, sleV2EponIMOnuLlidEponGoodLlid, sleV2EponIMOnuLlidEponOnuPonCastLlid, sleV2EponIMOnuLlidEponOltPonCastLlid, sleV2EponIMOnuLlidEponBcastLlidNotOnuId, 
				sleV2EponIMOnuLlidEponOnuLlidNotBcast, sleV2EponIMOnuLlidEponBcastLlidPlusOnuId, sleV2EponIMOnuLlidEponNotBcastLlidNotOnuId, sleV2EponIMOnuLlidEponPcsCodingViolation, sleV2EponIMOnuLlidEponFecCorrectedBlocks, 
				sleV2EponIMOnuLlidEponFecUnCorrectedBlocks, sleV2EponIMOnuLlidEponBufferHeadCodingViolation, sleV2EponIMOnuLlidStatsControlRequest, sleV2EponIMOnuLlidStatsControlStatus, sleV2EponIMOnuLlidStatsControlTimer, 
				sleV2EponIMOnuLlidStatsControlTimeStamp, sleV2EponIMOnuLlidStatsControlReqResult, sleV2EponIMAlarmOnuPortIndex, sleV2EponIMAlarmOnuAlarmIndex, sleV2EponIMAlarmOnuAlarmStatus, 
				sleV2EponIMAlarmOnuWindow, sleV2EponIMAlarmOnuThreshold, sleV2EponIMAlarmControlRequest, sleV2EponIMAlarmControlStatus, sleV2EponIMAlarmControlTimer, 
				sleV2EponIMAlarmControlTimeStamp, sleV2EponIMAlarmControlReqResult, sleV2EponIMAlarmControlOnuPortIndex, sleV2EponIMAlarmControlOnuAlarmIndex, sleV2EponIMAlarmControlOnuAlarmStatus, 
				sleV2EponIMAlarmControlWindow, sleV2EponIMOnuControlForceOpticPowerOff, sleV2EponIMOnuForceOpticPowerOff, sleV2EponIMOnuPortFECtxEnable, sleV2EponIMOnuPortFECrxEnable, 
				sleV2EponIMOnuPortLaserAlwaysOn, sleV2EponIMOnuPortDropInMcastTraffic, sleV2EponIMOnuPortDropInBcastTraffic, sleV2EponIMOnuPortBlockDataTraffic, sleV2EponIMOnuOpticButtonPushed, 
				sleV2EponIMAlarmControlThreshold, sleV2EponIMOnuAutoUpgrade, sleV2EponIMOnuControlAutoUpgrade }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIM 5 }

		
--  *******.4.1.6296.102.15.5
		-- *******.4.1.6296.102.15.6
		sleV2EponIMNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleV2EponIMOnuSecretChanged, sleV2EponIMOnuVendorCodeChanged, sleV2EponIMOnuAuthTimerChanged, sleV2EponIMOnuConfigApplySetted, sleV2EponIMOnuFWFirmwareCpSetted, 
				sleV2EponIMOnuFWFirmwareDestroyed, sleV2EponIMOnuFWFirmwareUpgradeSetted, sleV2EponIMOnuFWFirmwareCommitSetted, sleV2EponIMOnuPortAdminStateChanged, sleV2EponIMOnuBridgeFlowCtrlChagned, 
				sleV2EponIMOnuPortStatsCleared, sleV2EponIMOnuLlidStatsCleared, sleV2EponIMOnuForcePowerOffChanged, sleV2EponIMAlarmProfileChanged, sleV2EponIMOnuAutoUpgradeChanged, 
				sleV2EponIMOnuReset }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleV2EponIM 6 }

		
	
	END

--
-- SLEV2-EPON-IM-MIB.my
--
