--
-- sle-am-mib.mib
-- MIB generated by MG-SO<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Friday, February 12, 2016 at 15:20:46
--

	SLE-AM-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			zeroDotZero, TimeTicks, Integer32, Unsigned32, <PERSON>auge32, 
			OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		sleAlarmMgr MODULE-IDENTITY 
			LAST-UPDATED "201402060000Z"		-- February 06, 2014 at 00:00 GMT
			ORGANIZATION 
				" "
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"This MIB contains all needed informations about 
				Alarm Manager."
			REVISION "201402060000Z"		-- February 06, 2014 at 00:00 GMT
			DESCRIPTION 
				"This MIB module defines the managed objects that support the
				monitoring of alarms generated by physical entities contained
				by the system, including chassis, slots, modules, ports, power
				supplies and fans."
			::= { sleMgmt 15 }

		
	
--
-- Textual conventions
--
	
		AMAlarmClassId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"alarm class id
				1~65535"
			SYNTAX INTEGER (1..65535)

		AMAlarmId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Current Alarm ID
				1~65535"
			SYNTAX INTEGER (1..65535)

		AMAlarmSeverity ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Severity of Alarm"
			SYNTAX INTEGER
				{
				critical(1),
				major(2),
				minor(3),
				warning(4),
				intermediate(5),
				default(6)
				}

		AMTrapState ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				" This status explains whether TRAP status is enabled or disabled.
				Enable(1) : Alarm Trap State is Enabled. Hence TRAPs will be sent for this.
				Disable(0) : Alarm Trap State is Disabled. So Alarms won't be notified to user."
			SYNTAX INTEGER
				{
				enable(1),
				disable(0)
				}

		AMAlarmGuardTime ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"alarm guard time
				1~30 (second)
				0, no alarm guard time apply"
			SYNTAX INTEGER (0..30)

		AMAlarmSrc ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"
				|type|length|value|type|length|value|....
				
				type (1-byte) : alarm location type
				length(1-byte) : alarm location value length
				value (length-bytes) : alarm location value"
			SYNTAX OCTET STRING (SIZE (68))

		AlarmStatus ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				" This is the status of the alarm.
				Cleared(0) : Alarm status is cleared
				Raised(1) : Alarm status is raised.
				Masked(2) : alarm is suppressed.
				Disable(3) : Alarm disable 
				Forced-clear(4) : Init alarm
				Event(5) : Event report
				Unmasked(6) :Alarm suppression release
				
				"
			SYNTAX INTEGER
				{
				cleared(0),
				raised(1),
				masked(2),
				disabled(3),
				forcedClear(4),
				event(5),
				unmasked(6)
				}

		AMAlarmReason ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Alarm reason Description string
				(include location infomatiln)"
			SYNTAX OCTET STRING (SIZE (0..256))

		AMDateTime ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"TOD integer value"
			SYNTAX Unsigned32

		AMAlarmAco ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"ACO control configuration
				acoOff(1)  : buzzer operation enable.
				acoOn(2)   : buzzer operation disable.
				acoOpr(3)  : current buzzer cut."
			SYNTAX INTEGER
				{
				acoOff(1),
				acoOn(2),
				acoOpr(3)
				}

		AMAlarmLed ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"LED control configuration
				ledOff(1)  : LED operation disable.
				ledOn(2)   : LED operation enable.
				ledOpr(3)  : current LED off."
			SYNTAX INTEGER
				{
				setLedOff(1),
				setLedOn(2),
				oprLed(3)
				}

	
--
-- Node definitions
--
	
		sleAMAlarmTrapNeId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (6))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"use for NE idendification when alarm(or event) trap
				system MAC address"
			::= { sleAlarmMgr 1 }

		
		sleAMConfigBase OBJECT IDENTIFIER ::= { sleAlarmMgr 2 }

		
		sleAMConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleAMConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" This table is the class Master Alarm table. 
				It gets populated when system init is done. 
				And it contains all the class alarms supported for the device"
			::= { sleAMConfigBase 1 }

		
		sleAMConfigEntry OBJECT-TYPE
			SYNTAX SleAMConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleAMConfigAlarmClassId, sleAMConfigAlarmId }
			::= { sleAMConfigTable 1 }

		
		SleAMConfigEntry ::=
			SEQUENCE { 
				sleAMConfigAlarmClassId
					AMAlarmClassId,
				sleAMConfigAlarmId
					AMAlarmId,
				sleAMConfigAlarmName
					OCTET STRING,
				sleAMConfigAlarmSeverity
					AMAlarmSeverity,
				sleAMConfigAlarmEnableState
					AMTrapState,
				sleAMConfigAlarmRaiseGuardTime
					AMAlarmGuardTime,
				sleAMConfigAlarmClearGuardTime
					AMAlarmGuardTime,
				sleAMConfigAlarmLed
					INTEGER,
				sleAMConfigSpecificId
					Integer32
			 }

		sleAMConfigAlarmClassId OBJECT-TYPE
			SYNTAX AMAlarmClassId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class ID starts from 1 to 65535 for the system.
				And these IDs are defined by alarm-client"
			::= { sleAMConfigEntry 1 }

		
		sleAMConfigAlarmId OBJECT-TYPE
			SYNTAX AMAlarmId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm ID
				Alarm ID starts from 1 to 65535 for the system.
				And these IDs are defined by alarm-client"
			::= { sleAMConfigEntry 2 }

		
		sleAMConfigAlarmName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm Name string"
			::= { sleAMConfigEntry 3 }

		
		sleAMConfigAlarmSeverity OBJECT-TYPE
			SYNTAX AMAlarmSeverity
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm severity: The default severity is pre-defined by each Alarm manager clients
				critical(1),
				major(2),
				minor(3),
				warning(4),
				intermediate(5),"
			::= { sleAMConfigEntry 4 }

		
		sleAMConfigAlarmEnableState OBJECT-TYPE
			SYNTAX AMTrapState
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm Trap State. Based on the trap state, 
				the alarm will be notified to NMS. If the TRAP STATE is enabled 
				then the alarm will be notified else wont be notified.
				enable (1)
				disabe (0) "
			::= { sleAMConfigEntry 5 }

		
		sleAMConfigAlarmRaiseGuardTime OBJECT-TYPE
			SYNTAX AMAlarmGuardTime
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm raise Soaking time. 
				When any alarm is raised, this attribute
				specifies how much time the alarm should be soaked before notifying
				to Alarm manager
				range:1 ~ 30, (if '0', no soaking time)"
			::= { sleAMConfigEntry 6 }

		
		sleAMConfigAlarmClearGuardTime OBJECT-TYPE
			SYNTAX AMAlarmGuardTime
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm clear Soaking time. When any alarm is cleared, this attribute
				specifies how much time the alarm should be soaked before notifying
				to Alarm manager
				range:1 ~ 30, (if '0', no soaking time)"
			::= { sleAMConfigEntry 7 }

		
		sleAMConfigAlarmLed OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Alarm led configuration.
				Determinde led operate or not, when alarms occur.
				off(0): led not operate
				on(1) : led operate"
			::= { sleAMConfigEntry 8 }

		
		sleAMConfigSpecificId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm Configuration specific ID(4bytes)
				classId and alarmID are combinded with a specificID
				* 1st,2nd Bytes - ClassId
				* 3rd,4th bytes - alarmID
				"
			::= { sleAMConfigEntry 9 }

		
		sleAMConfigControl OBJECT IDENTIFIER ::= { sleAMConfigBase 2 }

		
		sleAMConfigControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setAMConfigTrapEnableState(1),
				setAMConfigRaiseGuardTime(2),
				setAMConfigClearGuardTime(3),
				setAMConfigSeverity(4),
				setAMConfigLed(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { sleAMConfigControl 1 }

		
		sleAMConfigControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { sleAMConfigControl 2 }

		
		sleAMConfigControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { sleAMConfigControl 3 }

		
		sleAMConfigControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleAMConfigControl 4 }

		
		sleAMConfigControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleAMConfigControl 5 }

		
		sleAMConfigControlAlarmClassId OBJECT-TYPE
			SYNTAX AMAlarmClassId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm Manager Class ID"
			::= { sleAMConfigControl 6 }

		
		sleAMConfigControlAlarmId OBJECT-TYPE
			SYNTAX AMAlarmId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm Manager Alarm Index"
			::= { sleAMConfigControl 7 }

		
		sleAMConfigControlSeverity OBJECT-TYPE
			SYNTAX AMAlarmSeverity
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm Class Severity
				critical(1),
				major(2),
				minor(3),
				warning(4),
				ignore(5),
				default(6)"
			::= { sleAMConfigControl 8 }

		
		sleAMConfigControlEnableState OBJECT-TYPE
			SYNTAX AMTrapState
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm Trap State. Based on the trap state, 
				the alarm will be notified to NMS. If the TRAP STATE is enabled 
				then the alarm will be notified else wont be notified.
				enable (1)
				disabe (0) "
			::= { sleAMConfigControl 9 }

		
		sleAMConfigControlRaiseGuardTime OBJECT-TYPE
			SYNTAX AMAlarmGuardTime
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm Raise Soaking time. When any alarm is raised, this attribute
				specifies how much time the alarm should be soaked before notifying
				to Alarm manager
				range:1 ~ 30, (if '0', no soaking time)"
			::= { sleAMConfigControl 10 }

		
		sleAMConfigControlClearGuardTime OBJECT-TYPE
			SYNTAX AMAlarmGuardTime
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm clear Soaking time. When any alarm is cleared, this attribute
				specifies how much time the alarm should be soaked before notifying
				to Alarm manager
				range:1 ~ 30, (if '0', no soaking time)"
			::= { sleAMConfigControl 11 }

		
		sleAMConfigControlLed OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" Alarm led configuration.
				Determinde led operate or not, when alarms occur.
				off(0): led not operate
				on(1) : led operate"
			::= { sleAMConfigControl 12 }

		
		sleAMConfigNotification OBJECT IDENTIFIER ::= { sleAMConfigBase 3 }

		
		sleAMConfigSeverityChanged NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMConfigControlRequest, sleAMConfigControlTimeStamp, sleAMConfigControlReqResult, sleAMConfigControlAlarmClassId, 
				sleAMConfigControlAlarmId, sleAMConfigControlSeverity }
			STATUS current
			DESCRIPTION 
				" Notification for Alarm severity change"
			::= { sleAMConfigNotification 1 }

		
		sleAMConfigEnableStateChanged NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMConfigControlRequest, sleAMConfigControlTimeStamp, sleAMConfigControlReqResult, sleAMConfigControlAlarmClassId, 
				sleAMConfigControlAlarmId, sleAMConfigControlEnableState }
			STATUS current
			DESCRIPTION 
				" Notification for Class Trap State change"
			::= { sleAMConfigNotification 2 }

		
		sleAMConfigRaiseGuardTimeChanged NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMConfigControlRequest, sleAMConfigControlReqResult, sleAMConfigControlTimeStamp, sleAMConfigControlAlarmClassId, 
				sleAMConfigControlAlarmId, sleAMConfigControlRaiseGuardTime }
			STATUS current
			DESCRIPTION 
				" Notification for Raise Soak time change"
			::= { sleAMConfigNotification 3 }

		
		sleAMConfigClearGuardTimeChanged NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMConfigControlRequest, sleAMConfigControlTimeStamp, sleAMConfigControlReqResult, sleAMConfigControlAlarmClassId, 
				sleAMConfigControlAlarmId, sleAMConfigControlClearGuardTime }
			STATUS current
			DESCRIPTION 
				" Notification for Clear Soak time change"
			::= { sleAMConfigNotification 4 }

		
		sleAMConfigLedChanged NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMConfigControlRequest, sleAMConfigControlTimeStamp, sleAMConfigControlReqResult, sleAMConfigControlAlarmClassId, 
				sleAMConfigControlAlarmId, sleAMConfigControlLed }
			STATUS current
			DESCRIPTION 
				" Notification for Alarm led configuration change"
			::= { sleAMConfigNotification 5 }

		
		sleAMCurrentBase OBJECT IDENTIFIER ::= { sleAlarmMgr 3 }

		
		sleAMCurrentTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleAMCurrentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" This table contains the current active
				alarms for the Alarm Class. Each Row in this table
				gets populated when any Alarm is raised"
			::= { sleAMCurrentBase 1 }

		
		sleAMCurrentEntry OBJECT-TYPE
			SYNTAX SleAMCurrentEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleAMCurrentSeqId }
			::= { sleAMCurrentTable 1 }

		
		SleAMCurrentEntry ::=
			SEQUENCE { 
				sleAMCurrentSeqId
					Unsigned32,
				sleAMCurrentAlarmSource
					AMAlarmSrc,
				sleAMCurrentAlarmClassId
					AMAlarmClassId,
				sleAMCurrentAlarmId
					AMAlarmId,
				sleAMCurrentAlarmStatus
					AlarmStatus,
				sleAMCurrentAlarmSeverity
					AMAlarmSeverity,
				sleAMCurrentAlarmReason
					AMAlarmReason,
				sleAMCurrentTimeAndDate
					TimeTicks,
				sleAMCurrentSpecificId
					INTEGER
			 }

		sleAMCurrentSeqId OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"alarm sequence id
				key of current alarm table
				it generated by alarm-manager automatically
				"
			::= { sleAMCurrentEntry 1 }

		
		sleAMCurrentAlarmSource OBJECT-TYPE
			SYNTAX AMAlarmSrc
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"alarm location infomation
				
				|type|length|value|type|length|value|....
				type (1-byte) : alarm location type
				length(1-byte) : alarm location value length
				value (length-bytes) : alarm location value"
			::= { sleAMCurrentEntry 2 }

		
		sleAMCurrentAlarmClassId OBJECT-TYPE
			SYNTAX AMAlarmClassId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class ID starts from 1 to 65535 for the system.
				And these IDs are defined by alarm-client"
			::= { sleAMCurrentEntry 3 }

		
		sleAMCurrentAlarmId OBJECT-TYPE
			SYNTAX AMAlarmId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm ID
				Alarm ID starts from 1 to 65535 for the system.
				And these IDs are defined by alarm-client
				"
			::= { sleAMCurrentEntry 4 }

		
		sleAMCurrentAlarmStatus OBJECT-TYPE
			SYNTAX AlarmStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current Alarm State: 
				raised (1),
				masked(2)"
			::= { sleAMCurrentEntry 5 }

		
		sleAMCurrentAlarmSeverity OBJECT-TYPE
			SYNTAX AMAlarmSeverity
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current Alarm Severity: 
				critical(1),
				major(2),
				minor(3),
				warning(4)"
			::= { sleAMCurrentEntry 6 }

		
		sleAMCurrentAlarmReason OBJECT-TYPE
			SYNTAX AMAlarmReason
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"reasion string"
			::= { sleAMCurrentEntry 7 }

		
		sleAMCurrentTimeAndDate OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current Alarm Time and Date"
			::= { sleAMCurrentEntry 8 }

		
		sleAMCurrentSpecificId OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Current Alarm specific ID(4bytes)
				classId and alarmID are combinded with a specificID
				* 1st,2nd Bytes - ClassId
				* 3rd,4th bytes - alarmID
				
				
				
				"
			::= { sleAMCurrentEntry 9 }

		
		sleAMCurrentControl OBJECT IDENTIFIER ::= { sleAMCurrentBase 2 }

		
		sleAMCurrentControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				allAlarmClear(1),
				alarmClearBySeqId(2),
				alarmClearBySource(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value.
				
				1: all current alarm clear
				2: current alarm clear by SeqId
				3: current alarm clear by Source(location)"
			::= { sleAMCurrentControl 1 }

		
		sleAMCurrentControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { sleAMCurrentControl 2 }

		
		sleAMCurrentControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { sleAMCurrentControl 3 }

		
		sleAMCurrentControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleAMCurrentControl 4 }

		
		sleAMCurrentControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleAMCurrentControl 5 }

		
		sleAMCurrentControlSeqId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				current alarmControl Sequence ID.(0, all mcurrent alarm cleared)
				
				Valid only if the sleAMHistoryControlRequest value is set to 2(alarmHistoryClearBySeqId)."
			::= { sleAMCurrentControl 6 }

		
		sleAMCurrentControlSource OBJECT-TYPE
			SYNTAX AMAlarmSrc
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"current alarm clear location infomation
				
				|type|length|value|type|length|value|....
				type (1-byte) : alarm location type
				length(1-byte) : alarm location value length
				value (length-bytes) : alarm location value"
			::= { sleAMCurrentControl 7 }

		
		sleAMCurrentNotification OBJECT IDENTIFIER ::= { sleAMCurrentBase 3 }

		
		sleAMCurrentAlarmCleared NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMCurrentControlRequest, sleAMCurrentControlTimeStamp, sleAMCurrentControlReqResult, sleAMCurrentControlSeqId
				 }
			STATUS current
			DESCRIPTION 
				" Notification for current alarm clear"
			::= { sleAMCurrentNotification 1 }

		
		sleAlarmTrapAlarm NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMCurrentSeqId, sleAMCurrentAlarmSource, sleAMCurrentAlarmReason, sleAMCurrentSpecificId, 
				sleAMCurrentAlarmId, sleAMCurrentAlarmClassId, sleAMCurrentAlarmStatus, sleAMCurrentAlarmSeverity, sleAMCurrentTimeAndDate
				 }
			STATUS current
			DESCRIPTION 
				"Notify Alarm Raise/Clear TRAPS to NMS for each entry in the Table"
			::= { sleAMCurrentNotification 2 }

		
		sleAlarmTrapEvent NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMCurrentSeqId, sleAMCurrentAlarmSource, sleAMCurrentAlarmClassId, sleAMCurrentAlarmId, 
				sleAMCurrentAlarmStatus, sleAMCurrentAlarmSeverity, sleAMCurrentSpecificId, sleAMCurrentTimeAndDate, sleAMCurrentAlarmReason
				 }
			STATUS current
			DESCRIPTION 
				"Notify Event Occur TRAPS to NMS for each entry in the Table"
			::= { sleAMCurrentNotification 3 }

		
		sleAMHistoryBase OBJECT IDENTIFIER ::= { sleAlarmMgr 4 }

		
		sleAMHistoryTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleAMHistoryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This Table is used to for keeping the Alarm History"
			::= { sleAMHistoryBase 1 }

		
		sleAMHistoryEntry OBJECT-TYPE
			SYNTAX SleAMHistoryEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleAMHistorySeqId }
			::= { sleAMHistoryTable 1 }

		
		SleAMHistoryEntry ::=
			SEQUENCE { 
				sleAMHistorySeqId
					Unsigned32,
				sleAMHistoryAlarmSource
					AMAlarmSrc,
				sleAMHistoryAlarmClassId
					AMAlarmClassId,
				sleAMHistoryAlarmId
					AMAlarmId,
				sleAMHistoryAlarmStatus
					AlarmStatus,
				sleAMHistoryAlarmSeverity
					AMAlarmSeverity,
				sleAMHistoryAlarmReason
					AMAlarmReason,
				sleAMHistoryAlarmTimeDate
					TimeTicks,
				sleAMHistorySpecificId
					Integer32
			 }

		sleAMHistorySeqId OBJECT-TYPE
			SYNTAX Unsigned32 (1..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Alarm History Sequence ID. This sequence Id is 
				maintained based on the TRAP sent. "
			::= { sleAMHistoryEntry 1 }

		
		sleAMHistoryAlarmSource OBJECT-TYPE
			SYNTAX AMAlarmSrc
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"
				|type|length|value|type|length|value|....
				
				type (1-byte) : alarm location type
				length(1-byte) : alarm location value length
				value (length-bytes) : alarm location value"
			::= { sleAMHistoryEntry 2 }

		
		sleAMHistoryAlarmClassId OBJECT-TYPE
			SYNTAX AMAlarmClassId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Class ID starts from 1 to 65535 for the system.
				And these IDs are defined by alarm-client"
			::= { sleAMHistoryEntry 3 }

		
		sleAMHistoryAlarmId OBJECT-TYPE
			SYNTAX AMAlarmId
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History Alarm ID
				Alarm ID starts from 1 to 65535 for the system.
				And these IDs are defined by alarm-client"
			::= { sleAMHistoryEntry 4 }

		
		sleAMHistoryAlarmStatus OBJECT-TYPE
			SYNTAX AlarmStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History Alarm State:
				cleared (0),
				raised (1),
				masked(2)"
			::= { sleAMHistoryEntry 5 }

		
		sleAMHistoryAlarmSeverity OBJECT-TYPE
			SYNTAX AMAlarmSeverity
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History alarm Severity
				critical(1),
				major(2),
				minor(3),
				warning(4)"
			::= { sleAMHistoryEntry 6 }

		
		sleAMHistoryAlarmReason OBJECT-TYPE
			SYNTAX AMAlarmReason
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"History alarm Reason string"
			::= { sleAMHistoryEntry 7 }

		
		sleAMHistoryAlarmTimeDate OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"History Alarm Time and Date"
			::= { sleAMHistoryEntry 8 }

		
		sleAMHistorySpecificId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm History specific ID(4bytes)
				classId and alarmID are combinded with a specificID
				* 1st,2nd Bytes - ClassId
				* 3rd,4th bytes - alarmID
				"
			::= { sleAMHistoryEntry 9 }

		
		sleAMHistoryControl OBJECT IDENTIFIER ::= { sleAMHistoryBase 2 }

		
		sleAMHistoryControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				allAlarmHistoryClear(1),
				alarmHistoryClearBySeqId(2),
				alarmHistoryClearBySource(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value.
				clearAlarmHistory : clears the alarms from the History Table
				
				
				1: all alarm history clear
				2: alarm history clear by SeqId
				3: alarm history clear by Source(location)"
			::= { sleAMHistoryControl 1 }

		
		sleAMHistoryControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { sleAMHistoryControl 2 }

		
		sleAMHistoryControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { sleAMHistoryControl 3 }

		
		sleAMHistoryControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleAMHistoryControl 4 }

		
		sleAMHistoryControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleAMHistoryControl 5 }

		
		sleAMHistoryControSeqId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Alarm History Control Sequence ID.
				Specify cleared history alarm ID
				(0, all alarm history cleared)
				
				"
			::= { sleAMHistoryControl 6 }

		
		sleAMHistoryControSource OBJECT-TYPE
			SYNTAX AMAlarmSrc
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"alarm history clear location infomation
				Valid only if the sleAMHistoryControlRequest value is set to 3(alarmHistoryClearBySource).
				
				|type|length|value|type|length|value|....
				type (1-byte) : alarm location type
				length(1-byte) : alarm location value length
				value (length-bytes) : alarm location value"
			::= { sleAMHistoryControl 7 }

		
		sleAMHistoryNotification OBJECT IDENTIFIER ::= { sleAMHistoryBase 3 }

		
		sleAMHistoryAlarmCleared NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMHistoryControlRequest, sleAMHistoryControlTimeStamp, sleAMHistoryControlReqResult, sleAMHistoryControSeqId
				 }
			STATUS current
			DESCRIPTION 
				" Notification for History Class cleared"
			::= { sleAMHistoryNotification 1 }

		
		sleAMAcoBase OBJECT IDENTIFIER ::= { sleAlarmMgr 5 }

		
		sleAMAcoInfoEntry OBJECT IDENTIFIER ::= { sleAMAcoBase 1 }

		
		sleAMAcoInfo OBJECT-TYPE
			SYNTAX AMAlarmAco
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ACO control configuration
				acoOff(1)  : buzzer operation enable.
				acoOn(2)   : buzzer operation disable.
				acoOpr(3)  : current buzzer cut."
			::= { sleAMAcoInfoEntry 1 }

		
		sleAMAcoControl OBJECT IDENTIFIER ::= { sleAMAcoBase 2 }

		
		sleAMAcoControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				oprAco(1),
				setAco(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { sleAMAcoControl 1 }

		
		sleAMAcogControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { sleAMAcoControl 2 }

		
		sleAMAcoControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { sleAMAcoControl 3 }

		
		sleAMAcoControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleAMAcoControl 4 }

		
		sleAMAcoControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleAMAcoControl 5 }

		
		sleAMAcoControlSet OBJECT-TYPE
			SYNTAX AMAlarmAco
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ACO control 
				acoOff(1)  : buzzer operation enable.
				acoOn(2)   : buzzer operation disable.
				acoOpr(3)  : current buzzer cut."
			::= { sleAMAcoControl 6 }

		
		sleAMAcoNotification OBJECT IDENTIFIER ::= { sleAMAcoBase 3 }

		
		sleAMAcoChanged NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMAcoControlRequest, sleAMAcoControlTimeStamp, sleAMAcoControlReqResult, sleAMAcoControlSet
				 }
			STATUS current
			DESCRIPTION 
				" Notification for Alarm ACO configuration change"
			::= { sleAMAcoNotification 1 }

		
		sleAMLedBase OBJECT IDENTIFIER ::= { sleAlarmMgr 6 }

		
		sleAMLedTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleAMLedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" This table contains the LED status and configuration."
			::= { sleAMLedBase 1 }

		
		sleAMLedEntry OBJECT-TYPE
			SYNTAX SleAMLedEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { sleAMLedSeverity }
			::= { sleAMLedTable 1 }

		
		SleAMLedEntry ::=
			SEQUENCE { 
				sleAMLedSeverity
					INTEGER,
				sleAMLedSet
					AMAlarmLed,
				sleAMLedCount
					Integer32
			 }

		sleAMLedSeverity OBJECT-TYPE
			SYNTAX INTEGER
				{
				critical(1),
				major(2),
				minor(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LED Alarm Severity
				critical(1),
				major(2),
				minor(3),"
			::= { sleAMLedEntry 1 }

		
		sleAMLedSet OBJECT-TYPE
			SYNTAX AMAlarmLed
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ACO control configuration
				acoOff(1)  : buzzer operation enable.
				acoOn(2)   : buzzer operation disable.
				acoOpr(3)  : current buzzer cut."
			::= { sleAMLedEntry 2 }

		
		sleAMLedCount OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"alarm count of the associated led"
			::= { sleAMLedEntry 3 }

		
		sleAMLedControl OBJECT IDENTIFIER ::= { sleAMLedBase 2 }

		
		sleAMLedControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				oprLed(1),
				setLed(2),
				ledCount(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The configuration commands, and user can configure 
				functions via setting this entry as proper value."
			::= { sleAMLedControl 1 }

		
		sleAMLedControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"status of user command. User have to check this 
				value as .busy. or .idle. before do setRequest."
			::= { sleAMLedControl 2 }

		
		sleAMLedControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the wait-time until setRequest 
				end. In case of short-time command, this value is 0"
			::= { sleAMLedControl 3 }

		
		sleAMLedControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"the time stamp of the last command. (don.t care)"
			::= { sleAMLedControl 4 }

		
		sleAMLedControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Result of the last command."
			::= { sleAMLedControl 5 }

		
		sleAMLedControlSeverity OBJECT-TYPE
			SYNTAX INTEGER
				{
				all(0),
				critical(1),
				major(2),
				minor(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LED Alarm Severity
				critical(1),
				major(2),
				minor(3),"
			::= { sleAMLedControl 6 }

		
		sleAMLedControlSet OBJECT-TYPE
			SYNTAX AMAlarmLed
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"LED control configuration
				ledOff(1)  : LED operation disable.
				ledOn(2)   : LED operation enable.
				ledOpr(3)  : current LED off."
			::= { sleAMLedControl 7 }

		
		sleAMLedNotification OBJECT IDENTIFIER ::= { sleAMLedBase 3 }

		
		sleAMLedChanged NOTIFICATION-TYPE
			OBJECTS { sleAMAlarmTrapNeId, sleAMLedControlRequest, sleAMLedControlTimeStamp, sleAMLedControlReqResult, sleAMLedControlSeverity, 
				sleAMLedControlSet }
			STATUS current
			DESCRIPTION 
				" Notification for LED configuration change"
			::= { sleAMLedNotification 1 }

		
	
	END

--
-- sle-am-mib.mib
--
