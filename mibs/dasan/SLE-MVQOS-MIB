--
-- SLE-MVQOS-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 285
-- Tuesday, June 27, 2006 at 09:04:53
--

	SLE-MVQOS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			sleMgmt			
				FROM DASAN-SMI			
			InterfaceIndex			
				FROM IF-MIB			
			SleControlStatusType, SleControlRequestResultType			
				FROM SLE-TC-MIB			
			OBJECT-GROUP, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			TimeTicks, <PERSON>p<PERSON><PERSON><PERSON>, <PERSON><PERSON>ge32, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
		-- *******.*********.14
		sleMVQoS MODULE-IDENTITY 
			LAST-UPDATED "200605102253Z"		-- May 10, 2006 at 22:53 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { sleMgmt 14 }

		
	
--
-- Type definitions
--
	
		IntQueue ::= INTEGER (-1 | 0..7)

		IntQueueIndex ::= INTEGER (1..8)

		IntEtherType ::= INTEGER (-1 | 0..65535)

		IntEtherTypeIndex ::= INTEGER (1..65535)

		IntIpAddressMask ::= INTEGER (1..31)

		IntQueueDirection ::= INTEGER
			{
			nothing(-1),
			source(1),
			destination(2)
			}

		IntCoS ::= INTEGER (-1 | 0..7)

		IntCoSIndex ::= INTEGER (1..8)

		IntDp ::= INTEGER (-1 | 0..2)

		IntDSCP ::= INTEGER (-1 | 0..63)

	
	
--
-- Node definitions
--
	
		-- *******.*********.14.1
		sleMVQoSBase OBJECT IDENTIFIER::= { sleMVQoS 1 }

		
		-- *******.*********.14.2
		sleMVQoS4 OBJECT IDENTIFIER::= { sleMVQoS 2 }

		
		-- *******.*********.14.2.1
		sleMVQoS4Base OBJECT IDENTIFIER::= { sleMVQoS4 1 }

		
		-- *******.*********.14.2.2
		sleMVQoS4BridgeBase OBJECT IDENTIFIER::= { sleMVQoS4 2 }

		
		-- *******.*********.********
		sleMVQoS4BridgePort2TCMark OBJECT IDENTIFIER::= { sleMVQoS4BridgeBase 1 }

		
		-- *******.*********.********.1
		sleMVQoS4BridgePort2TCMarkTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMVQoS4BridgePort2TCMarkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMark 1 }

		
		-- *******.*********.********.1.1
		sleMVQoS4BridgePort2TCMarkEntry OBJECT-TYPE
			SYNTAX SleMVQoS4BridgePort2TCMarkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleMVQoS4BridgePort2TCMarkPortIndex }
			::= { sleMVQoS4BridgePort2TCMarkTable 1 }

		
		SleMVQoS4BridgePort2TCMarkEntry ::=
			SEQUENCE { 
				sleMVQoS4BridgePort2TCMarkPortIndex
					InterfaceIndex,
				sleMVQoS4BridgePort2TCMarkQueue
					IntQueue
			 }

		-- *******.*********.********.1.1.1
		sleMVQoS4BridgePort2TCMarkPortIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkEntry 1 }

		
		-- *******.*********.********.1.1.2
		sleMVQoS4BridgePort2TCMarkQueue OBJECT-TYPE
			SYNTAX IntQueue
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkEntry 2 }

		
		-- *******.*********.********.2
		sleMVQoS4BridgePort2TCMarkControl OBJECT IDENTIFIER::= { sleMVQoS4BridgePort2TCMark 2 }

		
		-- *******.*********.********.2.1
		sleMVQoS4BridgePort2TCMarkControlRequest OBJECT-TYPE
			SYNTAX INTEGER { setBridgePort2TCMark(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkControl 1 }

		
		-- *******.*********.********.2.2
		sleMVQoS4BridgePort2TCMarkControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkControl 2 }

		
		-- *******.*********.********.2.3
		sleMVQoS4BridgePort2TCMarkControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkControl 3 }

		
		-- *******.*********.********.2.4
		sleMVQoS4BridgePort2TCMarkControlTmeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkControl 4 }

		
		-- *******.*********.********.2.5
		sleMVQoS4BridgePort2TCMarkControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkControl 5 }

		
		-- *******.*********.********.2.6
		sleMVQoS4BridgePort2TCMarkControlPortIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkControl 6 }

		
		-- *******.*********.********.2.7
		sleMVQoS4BridgePort2TCMarkControlQueue OBJECT-TYPE
			SYNTAX IntQueue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkControl 7 }

		
		-- *******.*********.********.3
		sleMVQoS4BridgePort2TCMarkNotification OBJECT IDENTIFIER::= { sleMVQoS4BridgePort2TCMark 3 }

		
		-- *******.*********.********.3.1
		sleBridgePort2TCMarkChanged NOTIFICATION-TYPE
			OBJECTS { sleMVQoS4BridgePort2TCMarkControlRequest, sleMVQoS4BridgePort2TCMarkControlTmeStamp, sleMVQoS4BridgePort2TCMarkControlReqResult, sleMVQoS4BridgePort2TCMarkQueue }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS4BridgePort2TCMarkNotification 1 }

		
		-- *******.*********.14.2.3
		sleMVQoS4InLIF OBJECT IDENTIFIER::= { sleMVQoS4 3 }

		
		-- *******.*********.********
		sleMVQoS4InLIFMark OBJECT IDENTIFIER::= { sleMVQoS4InLIF 1 }

		
		-- *******.*********.********.1
		sleMVQoS4InLIFMarkTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMVQoS4InLIFMarkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMark 1 }

		
		-- *******.*********.********.1.1
		sleMVQoS4InLIFMarkEntry OBJECT-TYPE
			SYNTAX SleMVQoS4InLIFMarkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleMVQoS4InLIFMarkPortIndex }
			::= { sleMVQoS4InLIFMarkTable 1 }

		
		SleMVQoS4InLIFMarkEntry ::=
			SEQUENCE { 
				sleMVQoS4InLIFMarkPortIndex
					InterfaceIndex,
				sleMVQoS4InLIFMarkCoS
					IntCoS,
				sleMVQoS4InLIFMarkDSCP
					IntDSCP
			 }

		-- *******.*********.********.1.1.1
		sleMVQoS4InLIFMarkPortIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkEntry 1 }

		
		-- *******.*********.********.1.1.2
		sleMVQoS4InLIFMarkCoS OBJECT-TYPE
			SYNTAX IntCoS
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkEntry 2 }

		
		-- *******.*********.********.1.1.3
		sleMVQoS4InLIFMarkDSCP OBJECT-TYPE
			SYNTAX IntDSCP
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkEntry 3 }

		
		-- *******.*********.********.2
		sleMVQoS4InLIFMarkControl OBJECT IDENTIFIER::= { sleMVQoS4InLIFMark 2 }

		
		-- *******.*********.********.2.1
		sleMVQoS4InLIFMarkControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				setInLIFUp(1),
				setInLIFDscp(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 1 }

		
		-- *******.*********.********.2.2
		sleMVQoS4InLIFMarkControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 2 }

		
		-- *******.*********.********.2.3
		sleMVQoS4InLIFMarkControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 3 }

		
		-- *******.*********.********.2.4
		sleMVQoS4InLIFMarkControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 4 }

		
		-- *******.*********.********.2.5
		sleMVQoS4InLIFMarkControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 5 }

		
		-- *******.*********.********.2.6
		sleMVQoS4InLIFMarkControlPortIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 6 }

		
		-- *******.*********.********.2.7
		sleMVQoS4InLIFMarkControlCoS OBJECT-TYPE
			SYNTAX IntCoS
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 7 }

		
		-- *******.*********.********.2.8
		sleMVQoS4InLIFMarkControlDSCP OBJECT-TYPE
			SYNTAX IntDSCP
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4InLIFMarkControl 8 }

		
		-- *******.*********.********.3
		sleMVQoS4InLIFMarkNotification OBJECT IDENTIFIER::= { sleMVQoS4InLIFMark 3 }

		
		-- *******.*********.********.3.1
		sleInLIFMarkUpChanged NOTIFICATION-TYPE
			OBJECTS { sleMVQoS4InLIFMarkControlRequest, sleMVQoS4InLIFMarkControlTimeStamp, sleMVQoS4InLIFMarkControlReqResult, sleMVQoS4InLIFMarkCoS }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS4InLIFMarkNotification 1 }

		
		-- *******.*********.********.3.2
		sleInLIFMarkDscpChanged NOTIFICATION-TYPE
			OBJECTS { sleMVQoS4InLIFMarkControlRequest, sleMVQoS4InLIFMarkControlTimeStamp, sleMVQoS4InLIFMarkControlReqResult, sleMVQoS4InLIFMarkDSCP }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS4InLIFMarkNotification 2 }

		
		-- *******.*********.14.2.4
		sleMVQoS4Router OBJECT IDENTIFIER::= { sleMVQoS4 4 }

		
		-- *******.*********.********
		sleMVQoS4RouterMark OBJECT IDENTIFIER::= { sleMVQoS4Router 1 }

		
		-- *******.*********.********.1
		sleMVQoS4RouterMarkTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SleMVQoS4RouterMarkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMark 1 }

		
		-- *******.*********.********.1.1
		sleMVQoS4RouterMarkEntry OBJECT-TYPE
			SYNTAX SleMVQoS4RouterMarkEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sleMVQoS4RouterMarkNextHop }
			::= { sleMVQoS4RouterMarkTable 1 }

		
		SleMVQoS4RouterMarkEntry ::=
			SEQUENCE { 
				sleMVQoS4RouterMarkNextHop
					IpAddress,
				sleMVQoS4RouterMarkQueue
					IntQueue,
				sleMVQoS4RouterMarkDp
					IntDp,
				sleMVQoS4RouterMarkCoS
					IntCoS
			 }

		-- *******.*********.********.1.1.1
		sleMVQoS4RouterMarkNextHop OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkEntry 1 }

		
		-- *******.*********.********.1.1.2
		sleMVQoS4RouterMarkQueue OBJECT-TYPE
			SYNTAX IntQueue
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkEntry 2 }

		
		-- *******.*********.********.1.1.3
		sleMVQoS4RouterMarkDp OBJECT-TYPE
			SYNTAX IntDp
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkEntry 3 }

		
		-- *******.*********.********.1.1.4
		sleMVQoS4RouterMarkCoS OBJECT-TYPE
			SYNTAX IntCoS
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkEntry 4 }

		
		-- *******.*********.********.2
		sleMVQoS4RouterMarkControl OBJECT IDENTIFIER::= { sleMVQoS4RouterMark 2 }

		
		-- *******.*********.********.2.1
		sleMVQoS4RouterMarkControlRequest OBJECT-TYPE
			SYNTAX INTEGER
				{
				createRouterMarkEntry(1),
				setRouterMarkEntry(2),
				destroyRouterMarkEntry(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 1 }

		
		-- *******.*********.********.2.2
		sleMVQoS4RouterMarkControlStatus OBJECT-TYPE
			SYNTAX SleControlStatusType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 2 }

		
		-- *******.*********.********.2.3
		sleMVQoS4RouterMarkControlTimer OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 3 }

		
		-- *******.*********.********.2.4
		sleMVQoS4RouterMarkControlTimeStamp OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 4 }

		
		-- *******.*********.********.2.5
		sleMVQoS4RouterMarkControlReqResult OBJECT-TYPE
			SYNTAX SleControlRequestResultType
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 5 }

		
		-- *******.*********.********.2.6
		sleMVQoS4RouterMarkControlNextHop OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 6 }

		
		-- *******.*********.********.2.7
		sleMVQoS4RouterMarkControlQueue OBJECT-TYPE
			SYNTAX IntQueue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 7 }

		
		-- *******.*********.********.2.8
		sleMVQoS4RouterMarkControlDp OBJECT-TYPE
			SYNTAX IntDp
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 8 }

		
		-- *******.*********.********.2.9
		sleMVQoS4RouterMarkControlCoS OBJECT-TYPE
			SYNTAX IntCoS
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sleMVQoS4RouterMarkControl 9 }

		
		-- *******.*********.********.3
		sleMVQoS4RouterMarkNotification OBJECT IDENTIFIER::= { sleMVQoS4RouterMark 3 }

		
		-- *******.*********.********.3.1
		sleRouterMarkEntryCreated NOTIFICATION-TYPE
			OBJECTS { sleMVQoS4RouterMarkControlRequest, sleMVQoS4RouterMarkControlTimeStamp, sleMVQoS4RouterMarkControlReqResult, sleMVQoS4RouterMarkQueue, sleMVQoS4RouterMarkDp, 
				sleMVQoS4RouterMarkCoS }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS4RouterMarkNotification 1 }

		
		-- *******.*********.********.3.2
		sleRouterMarkEntryChanged NOTIFICATION-TYPE
			OBJECTS { sleMVQoS4RouterMarkControlRequest, sleMVQoS4RouterMarkControlTimeStamp, sleMVQoS4RouterMarkControlReqResult, sleMVQoS4RouterMarkQueue, sleMVQoS4RouterMarkDp, 
				sleMVQoS4RouterMarkCoS }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS4RouterMarkNotification 2 }

		
		-- *******.*********.********.3.3
		sleRouterMarkEntryDestroyed NOTIFICATION-TYPE
			OBJECTS { sleMVQoS4RouterMarkControlRequest, sleMVQoS4RouterMarkControlTimeStamp, sleMVQoS4RouterMarkControlReqResult, sleMVQoS4RouterMarkControlNextHop }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS4RouterMarkNotification 3 }

		
		-- *******.*********.14.3
		sleMVQoS6 OBJECT IDENTIFIER::= { sleMVQoS 3 }

		
		-- *******.*********.14.4
		sleMVQoSGroup OBJECT-GROUP
			OBJECTS { sleMVQoS4BridgePort2TCMarkPortIndex, sleMVQoS4BridgePort2TCMarkQueue, sleMVQoS4InLIFMarkPortIndex, sleMVQoS4InLIFMarkCoS, sleMVQoS4InLIFMarkDSCP, 
				sleMVQoS4RouterMarkNextHop, sleMVQoS4RouterMarkQueue, sleMVQoS4RouterMarkDp, sleMVQoS4RouterMarkCoS }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS 4 }

		
		-- *******.*********.14.5
		sleMVQoSControlGroup OBJECT-GROUP
			OBJECTS { sleMVQoS4BridgePort2TCMarkControlRequest, sleMVQoS4BridgePort2TCMarkControlStatus, sleMVQoS4BridgePort2TCMarkControlTimer, sleMVQoS4BridgePort2TCMarkControlTmeStamp, sleMVQoS4BridgePort2TCMarkControlReqResult, 
				sleMVQoS4BridgePort2TCMarkControlPortIndex, sleMVQoS4BridgePort2TCMarkControlQueue, sleMVQoS4RouterMarkControlRequest, sleMVQoS4RouterMarkControlStatus, sleMVQoS4RouterMarkControlTimer, 
				sleMVQoS4RouterMarkControlTimeStamp, sleMVQoS4RouterMarkControlReqResult, sleMVQoS4RouterMarkControlNextHop, sleMVQoS4RouterMarkControlQueue, sleMVQoS4RouterMarkControlDp, 
				sleMVQoS4RouterMarkControlCoS, sleMVQoS4InLIFMarkControlRequest, sleMVQoS4InLIFMarkControlStatus, sleMVQoS4InLIFMarkControlTimer, sleMVQoS4InLIFMarkControlTimeStamp, 
				sleMVQoS4InLIFMarkControlReqResult, sleMVQoS4InLIFMarkControlPortIndex, sleMVQoS4InLIFMarkControlCoS, sleMVQoS4InLIFMarkControlDSCP }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS 5 }

		
		-- *******.*********.14.6
		sleMVQoSNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { sleBridgePort2TCMarkChanged, sleInLIFMarkUpChanged, sleInLIFMarkDscpChanged, sleRouterMarkEntryDestroyed, sleRouterMarkEntryCreated, 
				sleRouterMarkEntryChanged }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { sleMVQoS 6 }

		
	
	END

--
-- SLE-MVQOS-MIB.my
--
