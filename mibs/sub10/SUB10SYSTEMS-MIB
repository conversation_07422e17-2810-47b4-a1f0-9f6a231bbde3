SUB10SYSTEMS-MIB DEFINITIONS ::=

BEGIN

-- ****************************************************************************
-- Title:    Sub10 Systems - MIB
-- Version:  1.9
-- Date:     2015-06-03 00:00
-- Comments: This MIB models Sub10 Systems Wireless Ethernet Bridge
--           range of products. For more product information see
--           www.sub10systems.com 
-- ****************************************************************************

-- ****************************************************************************
--           Sub10 Systems Ltd Copyright 2014
-- ****************************************************************************


IMPORTS
    MODULE-IDENTITY FROM SNMPv2-SMI

    OBJECT-TYPE FROM SNMPv2-SMI

    NOTIFICATION-TYPE FROM SNMPv2-SMI

    Unsigned32 FROM SNMPv2-SMI

    Integer32 FROM SNMPv2-SMI

    Counter32 FROM SNMPv2-SMI

    Counter64 FROM SNMPv2-SMI

    enterprises FROM SNMPv2-SMI

    TEXTUAL-CONVENTION FROM SNMPv2-TC

    DisplayString FROM SNMPv2-TC

    DateAndTime FROM SNMPv2-TC

    RowStatus FROM SNMPv2-TC

    MODULE-COMPLIANCE FROM SNMPv2-CONF

    NOTIFICATION-GROUP FROM SNMPv2-CONF

    OBJECT-GROUP FROM SNMPv2-CONF;


sub10Systems MODULE-IDENTITY
    LAST-UPDATED   "201506030000Z"
    ORGANIZATION   "Sub10 Systems Ltd"
    CONTACT-INFO   "<EMAIL>"
    DESCRIPTION    "Sub10 Systems MIB"


    REVISION       "201506030000Z"
    DESCRIPTION    "-  Airside maximum data rate
                    -  Removed Eth Link Status from Sub10MeasuredObject"


    REVISION       "201504070000Z"
    DESCRIPTION    "-  Multi-Ethernet port MIB.
                    -  Stats History Time objects read-only"


    REVISION       "201503300000Z"
    DESCRIPTION    "-  Default Alarm Severities changed
                    -  Updated descriptions"


    REVISION       "201503260000Z"
    DESCRIPTION    "-  Firmware Upload and Copy control"


    REVISION       "201503060000Z"
    DESCRIPTION    "-  Rx and Tx Modulation Mode included in Alarm Management."


    REVISION       "201503050000Z"
    DESCRIPTION    "-  Reset Action for Alarm Configuration.
                    -  Modulation Mode Timers in radio stats."


    REVISION       "201502270000Z"
    DESCRIPTION    "-  VLAN filtering and QoS not per ethernet port
                    -  SNMPv3 Configuration
                    -  QosState default changed to disabled.
                    -  Sub10NotificationName type changed to Sub10AlarmName.
                    -  Added Measured Object Value to Alarm Management and Alarm Notifications.
                    -  Changed QoSState to QoSActiveState.
                    -  Added new default Alarm State stateUnknown.
                    -  Extended Alarm table sub10UnitMgmtAlarmTable to accomodate 64 Fixed and 64 User Defined alarms.
                    -  Alarm thresholds now Float64 translated as Octetstring
                    -  SNMP Trap option to Alarm Management.
                    -  Removed UAS, SES, BBE and ES counters.
                    -  Added Air Frame Error Ratio (AFER) objects to Radio
                    -  Rx / Tx Mbps throughput added to ethernet stats and Alarm measured object
                    -  Remote unit stats added to radio and ethernet current stats
                    -  Stats history counters make Counter64 where required"


    REVISION       "201412180000Z"
    DESCRIPTION    "-  Added Modulation Mode Change counters
                    -  Radio Link state enum change
                    -  Snmpv3 control removed till later release"


    REVISION       "201411190000Z"
    DESCRIPTION    "-  SyncE configuration moved from sub10EthernetMgmt to sub10UnitMgmt
                    -  TxPower now TxPowerLimit and change in enumerations
                    -  Added textual conventions Sub10RadioDataRate, Sub10TxPowerLimit,
                    -  Sub10MWUType, Sub10AlarmOperation, Sub10AlarmState.
                    -  Sub10DateTime
                    -  Notication Alarms now restricted to three types sub10UnitAlarm, sub10EthernetAlarm and sub10RadioAlarm.
                    -  sub10UnitLclType and sub10UnitLclId changed to sub10UnitLclUnitType and sub10UnitLclUnitId
                    -  Some DisplayString based objects now Octet String with defined sizes.
                    -  sub10UnitLclTranceiverTemp and sub10RmtUnitTranceiverTemp removed
                    -  sub10UnitLclActiveAlarmTable changed to sub10UnitLclAlarmStateTable and all table objects changed name.
                    -  sub10UnitLclAlarmStateTable simplified to contain only alarm state and alarm time and
                    -  the sub10UnitLclAlarmStateIndex maps to the same index in the sub10UnitMgmtAlarmTable.
                    -  Added sub10UnitLclRadioDataRate, sub10UnitLclMWUType.
                    -  Names of sub10RmtUnitxxx changed to sub10UnitRmtxxx to be consistent with local equivelents.
                    -  sub10UnitMgmtAlarmTable upgraded to support flexibale configurable monitoring interval, raise and clear
                    -  operational behaviour, raise and clear intervals. The sub10UnitMgmtAlarmIndex now maps to
                    -  sub10UnitLclAlarmStateIndex for indication of the state of each configurable alarm.
                    -  sub10EthMgmtVlan configuration upgrade. Added sub10EthMgmtVlanFiltering, sub10EthMgmtVlanDefaultEnabled,
                    -  sub10EthMgmtVlanDefaultId, sub10EthMgmtVlanDefaultPriority, sub10EthMgmtVlanDefaultDEI, sub10EthMgmtVlanIngressAction,
                    -  sub10EthMgmtVlanEgressAction (both currently fixed) and sub10EthMgmtVlanAllowedTable.
                    -  Removed sub10EthMgmtVlanTrafficAllow, sub10EthMgmtVlanNumber and sub10EthMgmtVlanTable.
                    -  Added sub10EthStats15mHistTime, sub10RadioStats1mHistTime, sub10RAdioStats15mHistTime, sub10RAdioStats1dHistTime.
                    -  Added sub10RadioLclAlignmentMode, sub10RadioLclDataRate, sub10RadioLclMWUType, sub10RadioRmtAlignmentMode.
                    -  sub10RadioMgmtTxPower changed to sub10RadioMgmtTxPowerLimit.
                    -  Added sub10RadioMgmtTxRxFreq.
                    -  sub10RadioMgmtAtpc changed to sub10RadioMgmtAPCMode to support Auto Power Control.
                    -  Added sub10RadioMgmtMWUChannelWidth
                    -  Added sub10RadioMgmtStats1dPersist.
                    -  Removed sub10RadioMgmtOperationMode now sub10RadioLclAlignmentMode.
                    -  Removed sub10RadioStatsGdot826HistoryTable and moved ES, SES, BE and UAS counters into
                    -  sub10RadioStats1mHistTable, sub10RadioStats1dHistTable and sub10RadioStats1dHistTable.
                    -  Added sub10UnitMgmtActions for commit / rollback based configuration
                    -  Firmware Objects
                    -  Stats status table"


    REVISION       "201404070000Z"
    DESCRIPTION    "The MIB module for all Sub10 Systems Radio products.
                    Some of the objects within this MIB may only be
                    supported by certain types of product. In these
                    cases the object description will state product compliance.
                    
                    Note : This MIB does not include the v320-liberator MIB
                    for the Sub10 Systems v320 legacy product. Please refer
                    to the liberator-v320 MIB. Some products may
                    be backwards compatible and provide support for the
                    liberator-v320 MIB.
                    
                    Copyright (C) Sub10Systems (2014)."

    ::= { enterprises 39003 }



-- ****************************************************************************
-- Textual Conventions
-- ****************************************************************************
Sub10EthInterfaceIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "d"
    STATUS         current
    DESCRIPTION    "A unique value, greater than zero, for each ethernet type interface."
    SYNTAX         Unsigned32(1..2)


Sub10EntryStatus ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "A general configuration type to indicate if a table row is in use or not in use.
                    This differs from RowStatus which is a settable object used to create the existence
                    of a row in a configurable table. Sub10EntryStatus is intended to be read-only
                    and is set internally by the Agent when a table row is populated with valid data."
    SYNTAX         INTEGER
                   {
                     entryInUse(1),
                     entryNotInUse(2)
                   }


Sub10State ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "A general configuration type to indicate enabled or disabled."
    SYNTAX         INTEGER
                   {
                     stateDisabled(0),
                     stateEnabled(1)
                   }


Sub10UnitType ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Sub10Systems unit type."
    SYNTAX         INTEGER
                   {
                     baseband100A(0),
                     baseband100B(1),
                     baseband1000A(2),
                     baseband1000B(3),
                     v100ROWA(4),
                     v100ROWB(5),
                     v100FCCA(6),
                     v100FCCB(7),
                     v1000ROWA(8),
                     v1000ROWB(9),
                     v1000FCCA(10),
                     v1000FCCB2(11),
                     e1000ROWA(12),
                     e1000ROWB(13),
                     e1000FCCA(14),
                     e1000FCCB(15)
                   }


Sub10TerminalType ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Sub10Systems terminal type. A link must
                    have an 'A' end terminal and a 'B' end terminal."
    SYNTAX         INTEGER
                   {
                     terminalA(0),
                     terminalB(1)
                   }


Sub10Availability ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Availability selects RSSI thresholds for modulation changes."
    SYNTAX         INTEGER
                   {
                     availabilityHigh(0),
                     availabilityMedium(1),
                     availabilityLow(2)
                   }


Sub10AlignmentMode ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Alignment mode is modeAlignment(1) if the alignment / reset cap
                    has been removed for > 5sec otherwise modeNormal(0). The alignment
                    mode is managed using sub10RadioMgmtAlignmentMode."
    SYNTAX         INTEGER
                   {
                     modeNormal(0),
                     modeAlignment(1)
                   }


Sub10AlignmentModeLock ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "If locked, the unit will not go into alignment mode when the
                    alignment cap is removed."
    SYNTAX         INTEGER
                   {
                     modeUnlocked(0),
                     modeLocked(1)
                   }


Sub10OperStatus ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The status of the ethernet link, this maps directly to ifOperStatus."
    SYNTAX         INTEGER
                   {
                     opStatusUp(1),
                     opStatusDown(2),
                     opStatusTesting(3),
                     opStatusUnknown(4),
                     opStatusDormant(5),
                     opStatusNotPresent(6),
                     opStatusLowerLayerDown(7)
                   }


Sub10Duplex ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The operation of a duplex link link"
    SYNTAX         INTEGER
                   {
                     halfDuplex(1),
                     fullDuplex(2),
                     unknownDuplex(3)
                   }


Sub10MDIType ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The operation of a duplex link link.
                    The setting of 'auto' is used only
                    for configuration puprposes."
    SYNTAX         INTEGER
                   {
                     mdi(1),
                     mdix(2),
                     mdiUnknown(3)
                   }


Sub10RadioLinkState ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The status of the radio link"
    SYNTAX         INTEGER
                   {
                     radioLinkStateDown(0),
                     radioLinkStateUp(1),
                     radioLinkStateUnknown(2)
                   }


Sub10AlarmName ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "32a"
    STATUS         current
    DESCRIPTION    "Name convention for all notification types."
    SYNTAX         OCTET STRING (SIZE (1..32))


Sub10MacAddress ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "17a"
    STATUS         current
    DESCRIPTION    "MAC Address in readable string format."
    SYNTAX         OCTET STRING (SIZE (11..17))


Sub10AlarmState ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Alarm state."
    SYNTAX         INTEGER
                   {
                     cleared(1),
                     raised(2),
                     stateUnknown(3)
                   }


Sub10DateTime ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "19a"
    STATUS         current
    DESCRIPTION    "The generic Sub10 format for a date time string.
                    Format is YYYY-MM-DD HH:MM:SS e.g. 2014-05-26 13:30:15"
    SYNTAX         OCTET STRING (SIZE (19..19))


Sub10AlarmIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "d"
    STATUS         current
    DESCRIPTION    "The MIB index used to define the range of indexing and size of the Alarm tables
                    sub10UnitLclAlarmStateTable and sub10UnitMgmtAlarmTable."
    SYNTAX         Unsigned32(1..128)


Sub10MeasuredObject ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "A MIB object that may be measured by Alarm Management or any other sub-system."
    SYNTAX         INTEGER
                   {
                     sub10UnitLclMWUTemperature(1),
                     sub10RadioLclRxPower(2),
                     sub10RadioLclVectErr(3),
                     sub10RadioLclLnkLoss(4),
                     sub10RadioLclTxPower(5),
                     sub10RadioMgmtTxRxFreq(6),
                     sub10RadioMgmtAPCMode(7),
                     sub10RadioMgmtModulationMode(8),
                     sub10RadioMgmtAlignmentMode(9),
                     sub10RadioLclLinkStatus(10),
                     sub10RadioLclRxModulationMode(11),
                     sub10RadioLclTxModulationMode(12),
                     sub10RadioStatsCurrTxPowerMin(20),
                     sub10RadioStatsCurrTxPowerMax(21),
                     sub10RadioStatsCurrTxPowerAvg(22),
                     sub10RadioStatsCurrRxPowerMin(23),
                     sub10RadioStatsCurrRxPowerMax(24),
                     sub10RadioStatsCurrRxPowerAvg(25),
                     sub10RadioStatsCurrVectErrMin(26),
                     sub10RadioStatsCurrVectErrMax(27),
                     sub10RadioStatsCurrVectErrAvg(28),
                     sub10RadioStatsCurrLnkLossMin(29),
                     sub10RadioStatsCurrLnkLossMax(30),
                     sub10RadioStatsCurrLnkLossAvg(31),
                     sub10RadioStatsCurrMWUTempMin(32),
                     sub10RadioStatsCurrMWUTempMax(33),
                     sub10RadioStatsCurrMWUTempAvg(34),
                     sub10RadioStatsCurrAFERMin(35),
                     sub10RadioStatsCurrAFERMax(36),
                     sub10RadioStatsCurrAFERAvg(37),
                     sub10RadioStats1mHistTxPowerMin(50),
                     sub10RadioStats1mHistTxPowerMax(51),
                     sub10RadioStats1mHistTxPowerAvg(52),
                     sub10RadioStats1mHistRxPowerMin(53),
                     sub10RadioStats1mHistRxPowerMax(54),
                     sub10RadioStats1mHistRxPowerAvg(55),
                     sub10RadioStats1mHistVectErrMin(56),
                     sub10RadioStats1mHistVectErrMax(57),
                     sub10RadioStats1mHistVectErrAvg(58),
                     sub10RadioStats1mHistLnkLossMin(59),
                     sub10RadioStats1mHistLnkLossMax(60),
                     sub10RadioStats1mHistLnkLossAvg(61),
                     sub10RadioStats1mHistMWUTempMin(62),
                     sub10RadioStats1mHistMWUTempMax(63),
                     sub10RadioStats1mHistMWUTempAvg(64),
                     sub10RadioStats1mHistAFERMin(65),
                     sub10RadioStats1mHistAFERMax(66),
                     sub10RadioStats1mHistAFERAvg(67),
                     sub10RadioStats1mHistRxPkts(68),
                     sub10RadioStats1mHistTxPkts(69),
                     sub10RadioStats1mHistRxMgmtPkts(70),
                     sub10RadioStats1mHistTxMgmtPkts(71),
                     sub10RadioStats1mHistRxBadFrms(72),
                     sub10RadioStats1mHistQPSKTo8PSK(73),
                     sub10RadioStats1mHist8PSKToQPSK(74),
                     sub10RadioStats1mHistRxQPSK(75),
                     sub10RadioStats1mHistRx8PSK(76),
                     sub10RadioStats1mHistTxQPSK(77),
                     sub10RadioStats1mHistTx8PSK(78),
                     sub10RadioStats15mHistTxPowerMin(90),
                     sub10RadioStats15mHistTxPowerMax(91),
                     sub10RadioStats15mHistTxPowerAvg(92),
                     sub10RadioStats15mHistRxPowerMin(93),
                     sub10RadioStats15mHistRxPowerMax(94),
                     sub10RadioStats15mHistRxPowerAvg(95),
                     sub10RadioStats15mHistVectErrMin(96),
                     sub10RadioStats15mHistVectErrMax(97),
                     sub10RadioStats15mHistVectErrAvg(98),
                     sub10RadioStats15mHistLnkLossMin(99),
                     sub10RadioStats15mHistLnkLossMax(100),
                     sub10RadioStats15mHistLnkLossAvg(101),
                     sub10RadioStats15mHistMWUTempMin(102),
                     sub10RadioStats15mHistMWUTempMax(103),
                     sub10RadioStats15mHistMWUTempAvg(104),
                     sub10RadioStats15mHistAFERMin(105),
                     sub10RadioStats15mHistAFERMax(106),
                     sub10RadioStats15mHistAFERAvg(107),
                     sub10RadioStats15mHistRxPkts(108),
                     sub10RadioStats15mHistTxPkts(109),
                     sub10RadioStats15mHistRxMgmtPkts(110),
                     sub10RadioStats15mHistTxMgmtPkts(111),
                     sub10RadioStats15mHistRxBadFrms(112),
                     sub10RadioStats15mHistQPSKTo8PSK(113),
                     sub10RadioStats15mHist8PSKToQPSK(114),
                     sub10RadioStats15mHistRxQPSK(115),
                     sub10RadioStats15mHistRx8PSK(116),
                     sub10RadioStats15mHistTxQPSK(117),
                     sub10RadioStats15mHistTx8PSK(118),
                     sub10EthStatsCurrRxMbps(140),
                     sub10EthStatsCurrTxMbps(141),
                     sub10EthStatsCurrRxMbpsMin(142),
                     sub10EthStatsCurrRxMbpsMax(143),
                     sub10EthStatsCurrRxMbpsAvg(144),
                     sub10EthStatsCurrTxMbpsMin(145),
                     sub10EthStatsCurrTxMbpsMax(146),
                     sub10EthStatsCurrTxMbpsAvg(147),
                     sub10EthStats1mHistRxOctets(160),
                     sub10EthStats1mHistRxGoodFrms(161),
                     sub10EthStats1mHistRxBcastFrms(162),
                     sub10EthStats1mHistRxMcastFrms(163),
                     sub10EthStats1mHistRxPauseFrms(164),
                     sub10EthStats1mHistRxCRCErrs(165),
                     sub10EthStats1mHistRxAlignErrs(166),
                     sub10EthStats1mHistRxOversized(167),
                     sub10EthStats1mHistRxJabberFrms(168),
                     sub10EthStats1mHistRxUndersized(169),
                     sub10EthStats1mHistRxFragments(170),
                     sub10EthStats1mHistRxSOFOvrns(171),
                     sub10EthStats1mHistTxOctets(172),
                     sub10EthStats1mHistTxGoodFrms(173),
                     sub10EthStats1mHistTxBcastFrms(174),
                     sub10EthStats1mHistTxMcastFrms(175),
                     sub10EthStats1mHistTxPauseFrms(176),
                     sub10EthStats1mHistTxDeferred(177),
                     sub10EthStats1mHistTxCollsn(178),
                     sub10EthStats1mHistTxSnglCollsn(179),
                     sub10EthStats1mHistTxMlplCollsn(180),
                     sub10EthStats1mHistTxExsvCollsn(181),
                     sub10EthStats1mHistTxLtCollsn(182),
                     sub10EthStats1mHistTxCSenseErrs(183),
                     sub10EthStats1mHistPkts64Octets(184),
                     sub10EthStats1mHistPkts65T127(185),
                     sub10EthStats1mHistPkts128T255(186),
                     sub10EthStats1mHistPkts256T511(187),
                     sub10EthStats1mHistPkts512T1023(188),
                     sub10EthStats1mHistPkts1024TMax(189),
                     sub10EthStats1mHistRxMbpsMin(190),
                     sub10EthStats1mHistRxMbpsMax(191),
                     sub10EthStats1mHistRxMbpsAvg(192),
                     sub10EthStats1mHistTxMbpsMin(193),
                     sub10EthStats1mHistTxMbpsMax(194),
                     sub10EthStats1mHistTxMbpsAvg(195),
                     sub10EthStats15mHistRxOctets(210),
                     sub10EthStats15mHistRxGoodFrms(211),
                     sub10EthStats15mHistRxBcastFrms(212),
                     sub10EthStats15mHistRxMcastFrms(213),
                     sub10EthStats15mHistRxPauseFrms(214),
                     sub10EthStats15mHistRxCRCErrs(215),
                     sub10EthStats15mHistRxAlignErrs(216),
                     sub10EthStats15mHistRxOversized(217),
                     sub10EthStats15mHistRxJabberFrms(218),
                     sub10EthStats15mHistRxUndersized(219),
                     sub10EthStats15mHistRxFragments(220),
                     sub10EthStats15mHistRxSOFOvrns(221),
                     sub10EthStats15mHistTxOctets(222),
                     sub10EthStats15mHistTxGoodFrms(223),
                     sub10EthStats15mHistTxBcastFrms(224),
                     sub10EthStats15mHistTxMcastFrms(225),
                     sub10EthStats15mHistTxPauseFrms(226),
                     sub10EthStats15mHistTxDeferred(227),
                     sub10EthStats15mHistTxCollsn(228),
                     sub10EthStats15mHistTxSnglCollsn(229),
                     sub10EthStats15mHistTxMlplCollsn(230),
                     sub10EthStats15mHistTxExsvCollsn(231),
                     sub10EthStats15mHistTxLtCollsn(232),
                     sub10EthStats15mHistTxCSenseErrs(233),
                     sub10EthStats15mHistPkts64Octets(234),
                     sub10EthStats15mHistPkts65T127(235),
                     sub10EthStats15mHistPkts128T255(236),
                     sub10EthStats15mHistPkts256T511(237),
                     sub10EthStats15mHistPkts512T1023(238),
                     sub10EthStats15mHistPkts1024TMax(239),
                     sub10EthStats15mHistRxMbpsMin(240),
                     sub10EthStats15mHistRxMbpsMax(241),
                     sub10EthStats15mHistRxMbpsAvg(242),
                     sub10EthStats15mHistTxMbpsMin(243),
                     sub10EthStats15mHistTxMbpsMax(244),
                     sub10EthStats15mHistTxMbpsAvg(245),
                     sub10RadioStats1dHistTxPowerMin(260),
                     sub10RadioStats1dHistTxPowerMax(261),
                     sub10RadioStats1dHistTxPowerAvg(262),
                     sub10RadioStats1dHistRxPowerMin(263),
                     sub10RadioStats1dHistRxPowerMax(264),
                     sub10RadioStats1dHistRxPowerAvg(265),
                     sub10RadioStats1dHistVectErrMin(266),
                     sub10RadioStats1dHistVectErrMax(267),
                     sub10RadioStats1dHistVectErrAvg(268),
                     sub10RadioStats1dHistLnkLossMin(269),
                     sub10RadioStats1dHistLnkLossMax(270),
                     sub10RadioStats1dHistLnkLossAvg(271),
                     sub10RadioStats1dHistMWUTempMin(272),
                     sub10RadioStats1dHistMWUTempMax(273),
                     sub10RadioStats1dHistMWUTempAvg(274),
                     sub10RadioStats1dHistAFERMin(275),
                     sub10RadioStats1dHistAFERMax(276),
                     sub10RadioStats1dHistAFERAvg(277),
                     sub10RadioStats1dHistRxPkts(278),
                     sub10RadioStats1dHistTxPkts(279),
                     sub10RadioStats1dHistRxMgmtPkts(280),
                     sub10RadioStats1dHistTxMgmtPkts(281),
                     sub10RadioStats1dHistRxBadFrms(282),
                     sub10RadioStats1dHistQPSKTo8PSK(283),
                     sub10RadioStats1dHist8PSKToQPSK(284),
                     sub10RadioStats1dHistRxQPSK(285),
                     sub10RadioStats1dHistRx8PSK(286),
                     sub10RadioStats1dHistTxQPSK(287),
                     sub10RadioStats1dHistTx8PSK(288),
                     sub10EthStats1dHistRxOctets(300),
                     sub10EthStats1dHistRxGoodFrms(301),
                     sub10EthStats1dHistRxBcastFrms(302),
                     sub10EthStats1dHistRxMcastFrms(303),
                     sub10EthStats1dHistRxPauseFrms(304),
                     sub10EthStats1dHistRxCRCErrs(305),
                     sub10EthStats1dHistRxAlignErrs(306),
                     sub10EthStats1dHistRxOversized(307),
                     sub10EthStats1dHistRxJabberFrms(308),
                     sub10EthStats1dHistRxUndersized(309),
                     sub10EthStats1dHistRxFragments(310),
                     sub10EthStats1dHistRxSOFOvrns(311),
                     sub10EthStats1dHistTxOctets(312),
                     sub10EthStats1dHistTxGoodFrms(313),
                     sub10EthStats1dHistTxBcastFrms(314),
                     sub10EthStats1dHistTxMcastFrms(315),
                     sub10EthStats1dHistTxPauseFrms(316),
                     sub10EthStats1dHistTxDeferred(317),
                     sub10EthStats1dHistTxCollsn(318),
                     sub10EthStats1dHistTxSnglCollsn(319),
                     sub10EthStats1dHistTxMlplCollsn(320),
                     sub10EthStats1dHistTxExsvCollsn(321),
                     sub10EthStats1dHistTxLtCollsn(322),
                     sub10EthStats1dHistTxCSenseErrs(323),
                     sub10EthStats1dHistPkts64Octets(324),
                     sub10EthStats1dHistPkts65T127(325),
                     sub10EthStats1dHistPkts128T255(326),
                     sub10EthStats1dHistPkts256T511(327),
                     sub10EthStats1dHistPkts512T1023(328),
                     sub10EthStats1dHistPkts1024TMax(329),
                     sub10EthStats1dHistRxMbpsMin(330),
                     sub10EthStats1dHistRxMbpsMax(331),
                     sub10EthStats1dHistRxMbpsAvg(332),
                     sub10EthStats1dHistTxMbpsMin(333),
                     sub10EthStats1dHistTxMbpsMax(334),
                     sub10EthStats1dHistTxMbpsAvg(335)
                   }


Sub10AlarmSeverity ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Severity for notifications of type alarm"
    SYNTAX         INTEGER
                   {
                     critical(1),
                     major(2),
                     minor(3),
                     warning(4)
                   }


Sub10AlarmOperation ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The operation that is used to compare a measured value with
                    a configured alarm threshold which controls the raising
                    and clearing of alarms."
    SYNTAX         INTEGER
                   {
                     lessThan(1),
                     greaterThan(2),
                     lessThanOrEqual(3),
                     greaterThanOrEqual(4),
                     equal(5),
                     notEqual(6)
                   }


Sub10AlarmType ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The type of the alarm and defines the type of notification (trap) used
                    to inform management entities."
    SYNTAX         INTEGER
                   {
                     sub10UnitAlarm(1),
                     sub10EthernetAlarm(2),
                     sub10RadioAlarm(3)
                   }


Sub10NTPSyncStatus ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The indication that the systems clock is synchronised with the
                    NTP server."
    SYNTAX         INTEGER
                   {
                     ntpOutOfSync(0),
                     ntpInSync(1)
                   }


Sub10VlanId ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "d"
    STATUS         current
    DESCRIPTION    "A VLAN Id (12 bits in the TCF part of an ethernet frame)."
    SYNTAX         Unsigned32(0..4095)


Sub10VlanTagAction ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Used in Vlan Mgmt to support Vlan tag manipulaton on the Ethernet interface."
    SYNTAX         INTEGER
                   {
                     tagActionNone(0),
                     untag(1),
                     tag(2),
                     drop(3)
                   }


Sub10VlanPriority ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "d"
    STATUS         current
    DESCRIPTION    "A VLAN Priority or PCP bits (3 bits in the TCF
                    part of an ethernet frame). The priority PCP
                    bits are based on 802.1p where 0 is the lowest
                    and 7 is the highest.
                    
                    Priority  Acronym  Traffic Types
                    0       BK      Background
                    1       BE      Best Effort
                    2       EE      Excellent Effort
                    3       CA      Critical Applications
                    4       VI      Video, 100 ms latency and jitter
                    5       VO      Voice, 10 ms latency and jitter
                    6       IC      Internetwork Control
                    7       NC      Network Control."
    SYNTAX         Unsigned32(0..7)


Sub10QoSQueue ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "d"
    STATUS         current
    DESCRIPTION    "Identifies the QoS queue number for up to 8 queues."
    SYNTAX         Unsigned32(0..7)


Sub10TxPowerLimit ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The settings for Transmit Power limit on the Radio interface.
                    A value of txPowerLimitNone means the TxPower is not limited
                    by the radio."
    SYNTAX         INTEGER
                   {
                     txPowerLimitNone(0),
                     txPowerLimitMinus3(1),
                     txPowerLimitMinus6(2),
                     txPowerLimitMinus9(3),
                     txPowerLimitMinus12(4),
                     txPowerLimitMinus15(5)
                   }


Sub10RadioDataRate ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The maximum data rate of the Airside interface."
    SYNTAX         INTEGER
                   {
                     radioDataRate1000(0),
                     radioDataRate700(1),
                     radioDataRate500(2),
                     radioDataRate300(3),
                     radioDataRate100(4)
                   }


Sub10UserGroup ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The user group types. A user is assigned to a group where each group
                    has specific access privileges."
    SYNTAX         INTEGER
                   {
                     operation(1),
                     administration(2),
                     maintenance(3),
                     engineer(4)
                   }


Sub10Snmpv3SecurityLevel ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The SNMPv3 security level assigned to a group of SNMPv3 users."
    SYNTAX         INTEGER
                   {
                     noAuthNoPriv(1),
                     authNoPriv(2),
                     authPriv(3)
                   }


Sub10Snmpv3AuthProtocol ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The SNMPv3 authentication protocol assigned to a group of SNMPv3 users."
    SYNTAX         INTEGER
                   {
                     noAuth(1),
                     md5(2),
                     sha1(3)
                   }


Sub10Snmpv3PrivProtocol ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The SNMPv3 privacy protocol assigned to a group of SNMPv3 users."
    SYNTAX         INTEGER
                   {
                     noPriv(1),
                     des(2),
                     aes(3)
                   }


Sub10MWUType ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The type of MWU."
    SYNTAX         INTEGER
                   {
                     mwuTypeNone(0),
                     mwuTypeVBand(1),
                     mwuTypeEBand(2)
                   }


Sub10FirmwareBank ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "d"
    STATUS         current
    DESCRIPTION    "Identifies the firmware bank number."
    SYNTAX         Unsigned32(1..3)


Sub10StatsGroup ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "The type of stats."
    SYNTAX         INTEGER
                   {
                     statsGroupNone(1),
                     statsGroupAll(2),
                     statsGroupRadioCurr(3),
                     statsGroupRadio1s(4),
                     statsGroupRadio1m(5),
                     statsGroupRadio15m(6),
                     statsGroupRadio1d(7),
                     statsGroupEthernetCurr(8),
                     statsGroupEthernet1s(9),
                     statsGroupEthernet1m(10),
                     statsGroupEthernet15m(11),
                     statsGroupEthernet1d(12),
                     statsGroupRadioCurr60s(13)
                   }


Sub10ThroughputMbps ::= TEXTUAL-CONVENTION
    DISPLAY-HINT   "32a"
    STATUS         current
    DESCRIPTION    "Throughput in Mbps (MegaBits per second)."
    SYNTAX         OCTET STRING (SIZE (0..32))


Sub10ModulationMode ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Radio Modulation Mode."
    SYNTAX         INTEGER
                   {
                     modebpsk(0),
                     modeqpsk(1),
                     mode8psk(2)
                   }





-- ****************************************************************************
-- Object Nodes
-- ****************************************************************************
sub10Notifications                      OBJECT IDENTIFIER ::= { sub10Systems 0 }
sub10Unit                               OBJECT IDENTIFIER ::= { sub10Systems 3 }
sub10UnitStatus                         OBJECT IDENTIFIER ::= { sub10Unit 1 }
sub10UnitLocalStatus                    OBJECT IDENTIFIER ::= { sub10UnitStatus 1 }
sub10UnitRemoteStatus                   OBJECT IDENTIFIER ::= { sub10UnitStatus 2 }
sub10UnitMgmt                           OBJECT IDENTIFIER ::= { sub10Unit 2 }
sub10UnitMgmtSystem                     OBJECT IDENTIFIER ::= { sub10UnitMgmt 1 }
sub10UnitMgmtIp                         OBJECT IDENTIFIER ::= { sub10UnitMgmt 2 }
sub10UnitMgmtVlan                       OBJECT IDENTIFIER ::= { sub10UnitMgmt 3 }
sub10UnitMgmtUsers                      OBJECT IDENTIFIER ::= { sub10UnitMgmt 4 }
sub10UnitMgmtTime                       OBJECT IDENTIFIER ::= { sub10UnitMgmt 5 }
sub10UnitMgmtAlarms                     OBJECT IDENTIFIER ::= { sub10UnitMgmt 6 }
sub10UnitMgmtSnmp                       OBJECT IDENTIFIER ::= { sub10UnitMgmt 7 }
sub10UnitMgmtSmtp                       OBJECT IDENTIFIER ::= { sub10UnitMgmt 8 }
sub10UnitMgmtFirmware                   OBJECT IDENTIFIER ::= { sub10UnitMgmt 9 }
sub10UnitMgmtDNS                        OBJECT IDENTIFIER ::= { sub10UnitMgmt 10 }
sub10UnitMgmtEncryption                 OBJECT IDENTIFIER ::= { sub10UnitMgmt 11 }
sub10UnitMgmtLicense                    OBJECT IDENTIFIER ::= { sub10UnitMgmt 12 }
sub10UnitMgmtSyncE                      OBJECT IDENTIFIER ::= { sub10UnitMgmt 13 }
sub10UnitMgmtActions                    OBJECT IDENTIFIER ::= { sub10UnitMgmt 20 }
sub10Ethernet                           OBJECT IDENTIFIER ::= { sub10Systems 4 }
sub10EthernetStatus                     OBJECT IDENTIFIER ::= { sub10Ethernet 1 }
sub10EthernetLocalStatus                OBJECT IDENTIFIER ::= { sub10EthernetStatus 1 }
sub10EthernetRemoteStatus               OBJECT IDENTIFIER ::= { sub10EthernetStatus 2 }
sub10EthernetMgmt                       OBJECT IDENTIFIER ::= { sub10Ethernet 2 }
sub10EthMgmtPhy                         OBJECT IDENTIFIER ::= { sub10EthernetMgmt 1 }
sub10EthMgmtVlan                        OBJECT IDENTIFIER ::= { sub10EthernetMgmt 2 }
sub10EthMgmtQoS                         OBJECT IDENTIFIER ::= { sub10EthernetMgmt 3 }
sub10EthMgmtStats                       OBJECT IDENTIFIER ::= { sub10EthernetMgmt 4 }
sub10EthernetStats                      OBJECT IDENTIFIER ::= { sub10Ethernet 3 }
sub10EthernetStatsCurrent               OBJECT IDENTIFIER ::= { sub10EthernetStats 2 }
sub10EthernetStatsHistory               OBJECT IDENTIFIER ::= { sub10EthernetStats 3 }
sub10EthernetStats15mHistory            OBJECT IDENTIFIER ::= { sub10EthernetStatsHistory 1 }
sub10EthStats1dHistory                  OBJECT IDENTIFIER ::= { sub10EthernetStatsHistory 2 }
sub10Radio                              OBJECT IDENTIFIER ::= { sub10Systems 5 }
sub10RadioStatus                        OBJECT IDENTIFIER ::= { sub10Radio 1 }
sub10RadioLocalStatus                   OBJECT IDENTIFIER ::= { sub10RadioStatus 1 }
sub10RadioRemoteStatus                  OBJECT IDENTIFIER ::= { sub10RadioStatus 2 }
sub10RadioMgmt                          OBJECT IDENTIFIER ::= { sub10Radio 2 }
sub10RadioMgmtStats                     OBJECT IDENTIFIER ::= { sub10RadioMgmt 9 }
sub10RadioStats                         OBJECT IDENTIFIER ::= { sub10Radio 3 }
sub10RadioStatsCurrent                  OBJECT IDENTIFIER ::= { sub10RadioStats 2 }
sub10RadioStatsHistory                  OBJECT IDENTIFIER ::= { sub10RadioStats 3 }
sub10RadioStats1mHistory                OBJECT IDENTIFIER ::= { sub10RadioStatsHistory 1 }
sub10RadioStats15mHistory               OBJECT IDENTIFIER ::= { sub10RadioStatsHistory 2 }
sub10RadioStats1dHistory                OBJECT IDENTIFIER ::= { sub10RadioStatsHistory 3 }
sub10MIBConformance                     OBJECT IDENTIFIER ::= { sub10Systems 20 }
sub10MIBCompliances                     OBJECT IDENTIFIER ::= { sub10MIBConformance 1 }
sub10MIBGroups                          OBJECT IDENTIFIER ::= { sub10MIBConformance 2 }

-- ****************************************************************************
-- .sub10Systems.sub10Notifications Objects
-- ****************************************************************************
sub10UnitAlarm NOTIFICATION-TYPE
    OBJECTS        { sub10UnitMgmtAlarmName,
                     sub10UnitLclAlarmState,
                     sub10UnitMgmtAlarmSeverity,
                     sub10UnitMgmtAlarmMeasObject,
                     sub10UnitMgmtAlarmMeasObjectVal,
                     sub10UnitMgmtAlarmRaiseOper,
                     sub10UnitMgmtAlarmRaiseThresh,
                     sub10UnitMgmtAlarmClearOper,
                     sub10UnitMgmtAlarmClearThresh
                   }
    STATUS         current
    DESCRIPTION    "A measured object relating the Unit subsystem has been raised or cleared according
                    to the configured thresholds. For alarm configuration see sub10UnitMgmtAlarmTable."

    ::= { sub10Notifications 1 }

sub10EthernetAlarm NOTIFICATION-TYPE
    OBJECTS        { sub10UnitMgmtAlarmName,
                     sub10UnitLclAlarmState,
                     sub10UnitMgmtAlarmSeverity,
                     sub10UnitMgmtAlarmMeasObject,
                     sub10UnitMgmtAlarmMeasObjectVal,
                     sub10UnitMgmtAlarmRaiseOper,
                     sub10UnitMgmtAlarmRaiseThresh,
                     sub10UnitMgmtAlarmClearOper,
                     sub10UnitMgmtAlarmClearThresh
                   }
    STATUS         current
    DESCRIPTION    "A measured object relating the Ethernet subsystem has been raised or cleared according
                    to the configured thresholds. For alarm configuration see sub10UnitMgmtAlarmTable."

    ::= { sub10Notifications 2 }

sub10RadioAlarm NOTIFICATION-TYPE
    OBJECTS        { sub10UnitMgmtAlarmName,
                     sub10UnitLclAlarmState,
                     sub10UnitMgmtAlarmSeverity,
                     sub10UnitMgmtAlarmMeasObject,
                     sub10UnitMgmtAlarmMeasObjectVal,
                     sub10UnitMgmtAlarmRaiseOper,
                     sub10UnitMgmtAlarmRaiseThresh,
                     sub10UnitMgmtAlarmClearOper,
                     sub10UnitMgmtAlarmClearThresh
                   }
    STATUS         current
    DESCRIPTION    "A measured object relating the Radio subsystem has been raised or cleared according
                    to the configured thresholds. For alarm configuration see sub10UnitMgmtAlarmTable."

    ::= { sub10Notifications 3 }


-- ****************************************************************************
-- .sub10UnitStatus.sub10UnitLocalStatus Objects
-- ****************************************************************************

sub10UnitLclTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local system time as a display string in the format:-
                    YYYY-MM-DD,HH:MM:SS e.g. 2014-5-26,13:30:15"

    ::= { sub10UnitLocalStatus 1 }


sub10UnitLclUnitType OBJECT-TYPE
    SYNTAX         Sub10UnitType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Uniquely identifies the local unit product type"

    ::= { sub10UnitLocalStatus 2 }


sub10UnitLclDescription OBJECT-TYPE
    SYNTAX         DisplayString
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The description of the local unit.
                    This is synonymous with MIB-II sysDescr."

    ::= { sub10UnitLocalStatus 3 }


sub10UnitLclHWSerialNumber OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..18))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The hardware serial number of the Local Unit.
                    This also identifies whether the unit is a V-band or E-band product.
                    See sub10UnitLclMWUType."

    ::= { sub10UnitLocalStatus 4 }


sub10UnitLclTerminalName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..63))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The administratively assigned name for the unit.
                    This is synonymous with MIB-II sysName and can be
                    administratively set via either sysName or sub10UnitMgmtTerminalName."

    ::= { sub10UnitLocalStatus 5 }


sub10UnitLclTerminalType OBJECT-TYPE
    SYNTAX         Sub10TerminalType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Uniquely identifies the local terminal type A or B."

    ::= { sub10UnitLocalStatus 6 }


sub10UnitLclLinkName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..63))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The administratively assigned name for the radio link between
                    the local and remote units. This generally identifies geographically
                    the two ends or a radio link within a site or location.
                    This is synonymous with MIB-II sysLocation and can be
                    administratively set via either sysLocation or sub10UnitMgmtLinkName."

    ::= { sub10UnitLocalStatus 7 }


sub10UnitLclLinkId OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The administratively assigned identifier for the radio link between
                    the local and remote units. The LinkId must be the same value on
                    both local and remote units."

    ::= { sub10UnitLocalStatus 8 }


sub10UnitLclSiteName OBJECT-TYPE
    SYNTAX         DisplayString
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The administratively assigned physical location of the local unit."

    ::= { sub10UnitLocalStatus 9 }


sub10UnitLclFirmwareLoadedBank OBJECT-TYPE
    SYNTAX         Sub10FirmwareBank
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Currently loaded bank. The firmware version running in this bank is
                    defined by sub10UnitLclFirmwareVersion."

    ::= { sub10UnitLocalStatus 10 }


sub10UnitLclFirmwareVersion OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The firmware version currently running on the local unit.
                    This is by definition the firmware loaded into the bank
                    number defined in sub10UnitLclFirmwareLoadedBank."

    ::= { sub10UnitLocalStatus 11 }


sub10UnitLclIpAddress OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (7..15))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The IP address of the local ethernet interface."

    ::= { sub10UnitLocalStatus 12 }


sub10UnitLclMWUTemperature OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The modem temperature on the local unit in degrees celsius."
    DEFVAL { 0 }

    ::= { sub10UnitLocalStatus 13 }


sub10UnitLclNTPSyncStatus OBJECT-TYPE
    SYNTAX         Sub10NTPSyncStatus
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The indication that the systems clock is synchronised with the
                    NTP server."
    DEFVAL { ntpOutOfSync }

    ::= { sub10UnitLocalStatus 14 }


sub10UnitLclAlarmStateTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitLclAlarmStateEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The table listing the each active alarm the local unit.
                    An active alarm is where the alarm state is 'raised' at the
                    specific severity level.
                    
                    The entries in the table correspond to the configured entries in
                    sub10UnitMgmtAlarmTable.
                    
                    This table has a single entry per alarm where the index to the table is
                    the same index as the associated Alarm configuration
                    table sub10UnitMgmtAlarmTable."

    ::= { sub10UnitLocalStatus 15 }


sub10UnitLclAlarmStateEntry OBJECT-TYPE
    SYNTAX         Sub10UnitLclAlarmStateEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitLclAlarmStateTable."
    INDEX          { sub10UnitLclAlarmStateIndex }

    ::= { sub10UnitLclAlarmStateTable 1 }

Sub10UnitLclAlarmStateEntry ::= SEQUENCE {
    sub10UnitLclAlarmStateIndex                       Sub10AlarmIndex,
    sub10UnitLclAlarmState                            Sub10AlarmState,
    sub10UnitLclAlarmStateTime                        Sub10DateTime
}

sub10UnitLclAlarmStateIndex OBJECT-TYPE
    SYNTAX         Sub10AlarmIndex
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitLclAlarmStateTable index.
                    The index represents the same index as used in sub10UnitMgmtAlarmTable.
                    The entries in the table correspond to the configured entries in
                    sub10UnitMgmtAlarmTable."

    ::= { sub10UnitLclAlarmStateEntry 1 }


sub10UnitLclAlarmState OBJECT-TYPE
    SYNTAX         Sub10AlarmState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Internal object to indicate if this alarm is currently in the
                    raised or cleared state."
    DEFVAL { stateUnknown }

    ::= { sub10UnitLclAlarmStateEntry 2 }


sub10UnitLclAlarmStateTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local date and time when the last Alarm State
                    transition occurred.
                    
                    This object facilitates retrieving all instances of
                    alarms that have been raised or have changed state
                    since a given point in time.
                    
                    Implementations MUST include the offset from UTC,
                    if available.  Implementation in environments in which
                    the UTC offset is not available is NOT RECOMMENDED."

    ::= { sub10UnitLclAlarmStateEntry 3 }


sub10UnitLclRadioDataRate OBJECT-TYPE
    SYNTAX         Sub10RadioDataRate
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum data rate of the Airside interface. This has the same
                    value as sub10RadioLclDataRate."

    ::= { sub10UnitLocalStatus 16 }


sub10UnitLclMWUType OBJECT-TYPE
    SYNTAX         Sub10MWUType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local MWU type"

    ::= { sub10UnitLocalStatus 17 }


sub10UnitLclFPGAVersion OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The FPGA Version."

    ::= { sub10UnitLocalStatus 18 }


-- ****************************************************************************
-- .sub10UnitStatus.sub10UnitRemoteStatus Objects
-- ****************************************************************************

sub10UnitRmtUnitType OBJECT-TYPE
    SYNTAX         Sub10UnitType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Uniquely identifies the remote unit product type"

    ::= { sub10UnitRemoteStatus 1 }


sub10UnitRmtTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The remote unit local system time as a display string in the format:-
                    YYYY-MM-DD,HH:MM:SS e.g. 2014-5-26,13:30:15"

    ::= { sub10UnitRemoteStatus 2 }


sub10UnitRmtTerminalName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..63))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The administratively assigned name for the remote unit.
                    This is synonymous with MIB-II sysName."

    ::= { sub10UnitRemoteStatus 3 }


sub10UnitRmtTerminalType OBJECT-TYPE
    SYNTAX         Sub10TerminalType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Uniquely identifies the remote terminal type A or B."

    ::= { sub10UnitRemoteStatus 4 }


sub10UnitRmtLinkName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..63))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The administratively assigned name on the remote unit for the radio link between
                    the local and remote units. This generally identifies geographically
                    the two ends or a radio link within a site or location."

    ::= { sub10UnitRemoteStatus 5 }


sub10UnitRmtLinkId OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The administratively assigned identifier on the remote unit for the radio link between
                    the local and remote units. The LinkId must be the same value on
                    both local and remote units."

    ::= { sub10UnitRemoteStatus 6 }


sub10UnitRmtHWSerialNumber OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..18))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The hardware serial number of the Remote Unit.
                    This also identifies whether the unit is a V-band or E-band product."

    ::= { sub10UnitRemoteStatus 7 }


sub10UnitRmtFirmwareVersion OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The firmware version currently running on the remote unit.
                    This is by definition the firmware loaded into the bank
                    currently used for boot. See sub10UnitMgmtFirmware."

    ::= { sub10UnitRemoteStatus 8 }


sub10UnitRmtIpAddress OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (7..15))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The IP address of the remote ethernet interface."

    ::= { sub10UnitRemoteStatus 9 }


sub10UnitRmtMWUTemperature OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The modem temperature on the local unit in degrees celsius."

    ::= { sub10UnitRemoteStatus 10 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtSystem Objects
-- ****************************************************************************

sub10UnitMgmtTerminalName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..63))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned name for the unit.
                    This is synonymous with MIB-II sysName and sub10UnitLocalStatus
                    sub10UnitLclTerminalName."

    ::= { sub10UnitMgmtSystem 1 }


sub10UnitMgmtLinkName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..63))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned name for the radio link between
                    the local and remote units. This generally identifies geographically
                    the two ends or a radio link within a site or location. This is
                    synonymous with sub10UnitLocalStatus sub10UnitLclLinkName"

    ::= { sub10UnitMgmtSystem 2 }


sub10UnitMgmtLinkId OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned identifier for the radio link between
                    the local and remote units. The LinkId must be the same value on
                    both local and remote units. This is synonymous with sub10UnitLocalStatus
                    sub10UnitLclLinkId.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."

    ::= { sub10UnitMgmtSystem 3 }


sub10UnitMgmtSiteName OBJECT-TYPE
    SYNTAX         DisplayString
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned physical location of the unit.
                    This is synonymous with MIB-II sysLocation and sub10UnitLocalStatus
                    sub10UnitLclSiteName."

    ::= { sub10UnitMgmtSystem 4 }


sub10UnitMgmtContactName OBJECT-TYPE
    SYNTAX         DisplayString
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned contact for support of the unit.
                    This is synonymous with MIB-II sysContact and sub10UnitLocalStatus
                    sub10UnitLclContactName."
    DEFVAL { "<EMAIL>" }

    ::= { sub10UnitMgmtSystem 5 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtIp Objects
-- ****************************************************************************

sub10UnitMgmtIpMode OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     ipv4(1),
                     ipv6(2),
                     dhcp(3)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "IP Address configuration is either manually set as ipv4 or ipv6
                    type addreessing or alternatively can be dynamically assigned
                    automatically using the Dynamic Host Control Protocol (DHCP).
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { ipv4 }

    ::= { sub10UnitMgmtIp 1 }


sub10UnitMgmtIpAddress OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (7..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned IP Address for the management
                    channel. This is reflected in the MIB-II ipAddrTable.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { "************" }

    ::= { sub10UnitMgmtIp 2 }


sub10UnitMgmtIpSubnetMask OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (7..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned sub-net mask for the management
                    channel. This is reflected in the MIB-II ipAddrTable.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { "*************" }

    ::= { sub10UnitMgmtIp 3 }


sub10UnitMgmtIpDefGateway OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (7..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned default gateway for the management
                    channel.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { "***********" }

    ::= { sub10UnitMgmtIp 4 }


sub10UnitMgmtIpDHCP OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Dynamic Host Control Protocol (DHCP) is enabled or disabled.
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtIp 5 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtVlan Objects
-- ****************************************************************************

sub10UnitMgmtVlanState OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Enable VLAN for Management traffic.           ."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtVlan 1 }


sub10UnitMgmtVlanId OBJECT-TYPE
    SYNTAX         Sub10VlanId
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The VLAN Id used to carry management traffic. Only valid
                    when the object is set to a non-zero value.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { 0 }

    ::= { sub10UnitMgmtVlan 2 }


sub10UnitMgmtVlanPriority OBJECT-TYPE
    SYNTAX         Unsigned32(0..7)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The VLAN priority inserted into the ethernet frame for all
                    management traffic. Only valid when the object
                    sub10UnitMgmtVlanId is set to non zero.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."

    ::= { sub10UnitMgmtVlan 3 }


sub10UnitMgmtVlanDSCP OBJECT-TYPE
    SYNTAX         Unsigned32(0..63)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The Differentiated Services Code Point priority inserted
                    into the IP header of all management traffic.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { 0 }

    ::= { sub10UnitMgmtVlan 4 }


sub10UnitMgmtVlanDEI OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Set the Drop Eligible Indicator inserted into the ethernet frame for all
                    management traffic. Only valid when the object sub10UnitMgmtVlanId is set to non zero.
                    The DEI bit is set into the tag control information (TCI)
                    field of each management frame. The DEI is a 1 bit field immediately
                    following the PCP bits (3 bit field)
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtVlan 5 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtUsers Objects
-- ****************************************************************************

sub10UnitMgmtUsersNumber OBJECT-TYPE
    SYNTAX         Unsigned32(1..10)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of valid sub10UnitMgmtUsersTable table entries present on this system."

    ::= { sub10UnitMgmtUsers 1 }


sub10UnitMgmtUserTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtUserEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The table listing the users configured on the unit.
                    The number of user entries is sub10UnitMgmtUsersNumber.
                    
                    This table is indexed by user name (sub10UnitMgmtUserName).
                    Multiple entries with the same sub10UnitMgmtUserName are not possible.
                    
                    NB. As part of SNMPv3 support this table will be superceded by 'usmUserTable'."

    ::= { sub10UnitMgmtUsers 2 }


sub10UnitMgmtUserEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtUserEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtUserTable.
                    If the rowStatus is set to notInService(2) then
                    the entry is not valid and no columnar objects are returned.
                    Usage is as follows:
                    
                    active(1)       - Entry is created and in use. Any attempt
                    to set this value will first check for
                    consistent objects. If objects are found
                    to be inconsistent then the entry will not
                    be created and an error will be returned.
                    
                    notInService(2) - Entry is not created and not in use. Any attempt
                    to set this value will result in the the entry
                    remaining created but not accessible and not used.
                    
                    notReady(3)     - Entry is created but has inconsistent objects, these
                    must be set to correct values before the object can
                    be set to active(1).
                    
                    createAndGo(4)  - Set this to create a new entry and attempt to make
                    the entry active(1). If the entry has inconsistent objects
                    then this will set the entry to notReady(3) and an error
                    will be returned.
                    
                    createAndWait(5)- Set this to create a new entry.
                    This will set rowStatus = notReady(3). Related
                    objects can then be set to consistent values
                    before row_status can be set to active(1).
                    
                    destroy(6)      - Set this to delete an entry."
    INDEX          { sub10UnitMgmtUserIndex }

    ::= { sub10UnitMgmtUserTable 1 }

Sub10UnitMgmtUserEntry ::= SEQUENCE {
    sub10UnitMgmtUserIndex                            Unsigned32,
    sub10UnitMgmtUserRowStatus                        RowStatus,
    sub10UnitMgmtUserName                             OCTET STRING,
    sub10UnitMgmtUserGroup                            Sub10UserGroup,
    sub10UnitMgmtUserPassword                         OCTET STRING,
    sub10UnitMgmtUserPasswordVerify                   OCTET STRING
}

sub10UnitMgmtUserIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..16)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtUserTable index."

    ::= { sub10UnitMgmtUserEntry 1 }


sub10UnitMgmtUserRowStatus OBJECT-TYPE
    SYNTAX         RowStatus
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION    "The row status of this row.
                    All table rows are fixed in size
                    and as such do not require conceptual row
                    creation. Instead the RowStatus is set to
                    active(1) or notInService(2) which indicates
                    that the row is operationally in use or not.
                    The default value is notInService(2) meaning the
                    entry is not used by default. To make
                    the entry valid this object must be set
                    to enabled(1), createAndGo(4) or createAndWait(5).
                    To delete the row set this to destroy(6)."
    DEFVAL { notInService }

    ::= { sub10UnitMgmtUserEntry 2 }


sub10UnitMgmtUserName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (1..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The unique user name provided by the system administrator."

    ::= { sub10UnitMgmtUserEntry 3 }


sub10UnitMgmtUserGroup OBJECT-TYPE
    SYNTAX         Sub10UserGroup
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The user group provided by the system administrator. This
                    restricts a user to certain key functions on the web GUI."
    DEFVAL { operation }

    ::= { sub10UnitMgmtUserEntry 4 }


sub10UnitMgmtUserPassword OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (6..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The unique user password provided by the system administrator.
                    This object can be set but is not visible when read."

    ::= { sub10UnitMgmtUserEntry 5 }


sub10UnitMgmtUserPasswordVerify OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (6..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The unique user password provided by the system administrator
                    use to verify the setting of sub10UnitMgmtUserPassword.
                    This object can be set but is not visible when read."

    ::= { sub10UnitMgmtUserEntry 6 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtTime Objects
-- ****************************************************************************

sub10UnitMgmtTimeLocal OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned system time defined
                    as a display string in the format:-
                    
                    YYYY-MM-DD HH:MM:SS e.g. 2014-05-26 13:30:15"

    ::= { sub10UnitMgmtTime 1 }


sub10UnitMgmtTimeNTPEnabled OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned state to enable or disable NTP.
                    system cloack assignment automatically using the
                    Network Time Protocol (NTP)."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtTime 2 }


sub10UnitMgmtTimeNTPServer1 OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (1..127))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned primary NTP Server name."

    ::= { sub10UnitMgmtTime 3 }


sub10UnitMgmtTimeNTPServer2 OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (1..127))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned secondary NTP Server name to
                    be used when the primary server is not responding."

    ::= { sub10UnitMgmtTime 4 }


sub10UnitMgmtTimeNTPPort OBJECT-TYPE
    SYNTAX         Unsigned32(1..65535)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned NTP Port number name to
                    be used when contacting the NTP server."
    DEFVAL { 123 }

    ::= { sub10UnitMgmtTime 5 }


sub10UnitMgmtTimeNTPSyncStatus OBJECT-TYPE
    SYNTAX         Sub10NTPSyncStatus
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The indication that the systems clock is synchronised with the
                    NTP server."
    DEFVAL { ntpOutOfSync }

    ::= { sub10UnitMgmtTime 6 }


sub10UnitMgmtDateTime OBJECT-TYPE
    SYNTAX         DateAndTime
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned system defined
                    as a standard date-time specification.
                    
                    field  octets  contents                  range
                    -----  ------  --------                  -----
                    1      1-2   year*                     0..65536
                    2       3    month                     1..12
                    3       4    day                       1..31
                    4       5    hour                      0..23
                    5       6    minutes                   0..59
                    6       7    seconds                   0..60
                    (use 60 for leap-second)
                    7       8    deci-seconds              0..9
                    8       9    direction from UTC        '+' / '-'
                    9      10    hours from UTC*           0..13
                    10      11    minutes from UTC          0..59
                    
                    * Notes:
                    - the value of year is in network-byte order
                    - daylight saving time in New Zealand is +13
                    
                    For example, Tuesday May 26, 1992 at 1:30:15 PM EDT would be
                    displayed as:
                    
                    1992-5-26,13:30:15.0,-4:0
                    
                    Note that if only local time is known, then timezone
                    information (fields 8-10) is not present."

    ::= { sub10UnitMgmtTime 7 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtAlarms Objects
-- ****************************************************************************

sub10UnitMgmtAlarmTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtAlarmEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtAlarmTable defines the alarm configuration.
                    The first 'N' rows of the table is reserved for fixed alarms
                    defined by the manufacturer. Fixed alarms cannot be deleted or their
                    names changed, other attributes of a fixed alarm may be modified.
                    
                    The remainder of rows are available for user defined alarms which
                    can be created and deleted. The index value 'N' is defined by
                    'sub10UnitMgmtAlarmsUserDefStart'. Any attempt to create or delete a row
                    in the table with an index less than value of 'sub10UnitMgmtAlarmsUserDefStart'
                    will be rejected.
                    
                    This table is indexed by alarm index a number ranging from 1
                    to the maximum number of entries in the alarm table.
                    User defined alarm name and and alarm severity are configurable allowing multiple
                    entries for the same alarm to be raised at different severity levels
                    according to different threshold levels. A user defined alarm can be created
                    with the same name as a fixed alarm if required. For example the fixed alarm
                    'Rx Power Low' monitors the radio receive power and raises an alarm if the
                    value is less than a certain threshold. A user defined alarm may be created
                    with the same name but a less stringent threshold and a lower seveirty level.
                    This allows same alarms to be raised at increasing severity as the condition
                    causing the alarm worsens.
                    
                    This table allows configuration of threshold passing behavior,
                    threshold values for both raising and clearing the alarms and
                    the perceived severity. The alarm forwarding mechanisms can
                    be defined for example SNMP trap, syslog and SMTP.
                    
                    No validation of thresholds across different entries of the table
                    therefore the administrator must ensure that thresholds are correctly
                    set accordingly for the severity of each alarm. This is particularly
                    important where a single alarm name is configured to be raised at
                    different severity levels."

    ::= { sub10UnitMgmtAlarms 1 }


sub10UnitMgmtAlarmEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtAlarmEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtAlarmTable.
                    If the rowStatus is set to notInService(2) then
                    the entry is not valid and no columnar objects are returned."
    INDEX          { sub10UnitMgmtAlarmIndex }

    ::= { sub10UnitMgmtAlarmTable 1 }

Sub10UnitMgmtAlarmEntry ::= SEQUENCE {
    sub10UnitMgmtAlarmIndex                           Sub10AlarmIndex,
    sub10UnitMgmtAlarmRowStatus                       RowStatus,
    sub10UnitMgmtAlarmName                            Sub10AlarmName,
    sub10UnitMgmtAlarmSeverity                        Sub10AlarmSeverity,
    sub10UnitMgmtAlarmMeasObject                      Sub10MeasuredObject,
    sub10UnitMgmtAlarmMonitorIntvl                    Unsigned32,
    sub10UnitMgmtAlarmRaiseOper                       Sub10AlarmOperation,
    sub10UnitMgmtAlarmRaiseThresh                     OCTET STRING,
    sub10UnitMgmtAlarmClearOper                       Sub10AlarmOperation,
    sub10UnitMgmtAlarmClearThresh                     OCTET STRING,
    sub10UnitMgmtAlarmRaiseIntvls                     Unsigned32,
    sub10UnitMgmtAlarmClearIntvls                     Unsigned32,
    sub10UnitMgmtAlarmType                            Sub10AlarmType,
    sub10UnitMgmtAlarmSmtpAddress                     OCTET STRING,
    sub10UnitMgmtAlarmToSyslog                        Sub10State,
    sub10UnitMgmtAlarmEnabled                         Sub10State,
    sub10UnitMgmtAlarmMeasObjectVal                   DisplayString,
    sub10UnitMgmtAlarmToSNMP                          Sub10State,
    sub10UnitMgmtAlarmMeasObjIndex                    Unsigned32
}

sub10UnitMgmtAlarmIndex OBJECT-TYPE
    SYNTAX         Sub10AlarmIndex
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtAlarmTable index."

    ::= { sub10UnitMgmtAlarmEntry 1 }


sub10UnitMgmtAlarmRowStatus OBJECT-TYPE
    SYNTAX         RowStatus
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION    "The row status of this row.
                    All table rows are fixed in size
                    and as such do not require conceptual row
                    creation. Instead the RowStatus is set to
                    active(1) or notInService(2) which indicates
                    that the row is operationally in use or not.
                    The default value is notInService(2) meaning the
                    entry is not used by default. To make
                    the entry valid this object must be set
                    to enabled(1), createAndGo(4) or createAndWait(5).
                    To delete the row set this to destroy(6)."
    DEFVAL { notInService }

    ::= { sub10UnitMgmtAlarmEntry 2 }


sub10UnitMgmtAlarmName OBJECT-TYPE
    SYNTAX         Sub10AlarmName
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The alarm name. This is a short descriptive name for the alarm.
                    Alarm names can be duplicated across entries in order to define
                    the same alarm to be raised and cleared according to different
                    thresholds and different severity levels. If the user defines mutiple alarms
                    with the same name it important to correctly configure severity and
                    thresholds to avoid multiple alarms being generated for the same condition.
                    Fixed alarm names cannot be modified. Fixed alarms are populated
                    automatically in rows 1 to ('sub10UnitMgmtAlarmsUserDefStart'-1)"

    ::= { sub10UnitMgmtAlarmEntry 3 }


sub10UnitMgmtAlarmSeverity OBJECT-TYPE
    SYNTAX         Sub10AlarmSeverity
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The alarm severity. This is added to each alarm as a parameter."

    ::= { sub10UnitMgmtAlarmEntry 4 }


sub10UnitMgmtAlarmMeasObject OBJECT-TYPE
    SYNTAX         Sub10MeasuredObject
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The measured object. This is the object name of the
                    object in the MIB with which the alarm is associated and
                    which the configured thresholds are compared with when
                    deriving the state of the alarm.
                    
                    It is not neccessary for all alarm measurement values to
                    be identified in the MIB since a single alarm may be dependant
                    on checks against entities or objects which are not present
                    in the MIB and therefore do not have an associated OID.
                    
                    In general where an alarm and its thresholds relate to the
                    monitoring of the value of a single MIB object then the OID of
                    that object will be defined here otherwise the value
                    is set to empty string."

    ::= { sub10UnitMgmtAlarmEntry 5 }


sub10UnitMgmtAlarmMonitorIntvl OBJECT-TYPE
    SYNTAX         Unsigned32(1..86400)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The interval in seconds when the measured object is to be read and compared with
                    the raise and clear thresholds.
                    An alarm cannot be raised or cleared quicker than the configured interval.
                    Please also see related sub10UnitMgmtAlarmRaiseIntvls and sub10UnitMgmtAlarmClearIntvls
                    objects which also affect when alarms are raised and cleared. This object
                    should be set appropriately for the measured object to be monitored (see
                    sub10UnitMgmtAlarmRaiseMeasObject) and the resulting alarm to be raised and cleared
                    (see sub10UnitMgmtAlarmName). This also affects the forwarding of alarms for example as
                    SNMP notifications.
                    
                    Higher severity alarms may have a shorter interval than those at a lower severity.
                    The correct setting of sub10UnitMgmtAlarmMonitorIntvl and related objects
                    sub10UnitMgmtAlarmRaiseIntvls and sub10UnitMgmtAlarmClearIntvls are important to avoid
                    potential network loading caused by alarm storms."
    DEFVAL { 1 }

    ::= { sub10UnitMgmtAlarmEntry 6 }


sub10UnitMgmtAlarmRaiseOper OBJECT-TYPE
    SYNTAX         Sub10AlarmOperation
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Type of operation which determines when an alarm
                    is to be raised.
                    
                    Definition of each type is as follows:
                    
                    lt - Measured objects value is less than raise threshold
                    gt - Measured objects value is greater than raise threshold
                    le - Measured objects value is less than or equal to raise threshold
                    ge - Measured objects value is greater than or equal to raise threshold
                    eq - Measured objects value is equal to raise threshold
                    neq - Measured objects value is not equal to raise threshold
                    
                    The alarm operation defines how to apply the configured
                    thresholds when raising the alarm. This defines
                    whether the alarm is raised when the measured object value
                    crosses the threshold from low to high or from high to low or
                    if the measured value hits a certain value or not.
                    
                    Example:
                    
                    Alarm Name                Alarm Threshold Raise Operation
                    High Temperature               70              gt
                    Low Temperature               -30              le
                    
                    High Temperature Alarm is raised when the measured temperature
                    is above 70 and Low Temperature raised when the measured temperature
                    is lower than or equal to -30."
    DEFVAL { greaterThanOrEqual }

    ::= { sub10UnitMgmtAlarmEntry 7 }


sub10UnitMgmtAlarmRaiseThresh OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The threshold value which the measured object's value
                    crosses causes the alarm to be raised. The behaviour of a
                    measured value and alarm threshold comparison is
                    defined by the object sub10UnitMgmtAlarmThreshType."
    DEFVAL { "0" }

    ::= { sub10UnitMgmtAlarmEntry 8 }


sub10UnitMgmtAlarmClearOper OBJECT-TYPE
    SYNTAX         Sub10AlarmOperation
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Type of operation which determines when an alarm
                    is to be cleared.
                    
                    Definition of each type is as follows:
                    
                    lt - Measured objects value is less than clear threshold
                    gt - Measured objects value is greater than clear threshold
                    le - Measured objects value is less than or equal to clear threshold
                    ge - Measured objects value is greater than or equal to clear threshold
                    eq - Measured objects value is equal to clear threshold
                    neq - Measured objects value is not equal to clear threshold
                    
                    The alarm operation defines how to apply the configured
                    thresholds when clearing the alarm. This defines
                    whether the alarm is cleared when the measured object value
                    crosses the threshold from low to high or from high to low or
                    if the measured value hits a certain value or not.
                    
                    Example:
                    
                    Alarm Name                Alarm Threshold Clear Operation
                    High Temperature               60              le
                    Low Temperature               -30              gt
                    
                    Low Temperature Alarm is cleared when the measured temperature
                    is lower or equal to 60 and Low Temperature cleared when the
                    measured temperature is greater than -30."
    DEFVAL { lessThan }

    ::= { sub10UnitMgmtAlarmEntry 9 }


sub10UnitMgmtAlarmClearThresh OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The threshold value which the measured object's value
                    crosses causes the alarm to be cleared. The behaviour of a
                    measured value and alarm threshold comparison is
                    defined by the object sub10UnitMgmtAlarmThreshType.
                    
                    The sub10UnitMgmtAlarmClearThresh value may be different
                    to the sub10UnitMgmtAlarmRaiseThresh allowing some hysteresis
                    bewteen raising and clearing of alarms thus avoiding an alarm
                    being continually raised and cleared if the measured value
                    is changing frequently around the threshold.
                    If different raise and clear thresholds are not required then the
                    sub10UnitMgmtAlarmClearThresh and sub10UnitMgmtAlarmRaiseThresh
                    should be set to the same value."
    DEFVAL { "0" }

    ::= { sub10UnitMgmtAlarmEntry 10 }


sub10UnitMgmtAlarmRaiseIntvls OBJECT-TYPE
    SYNTAX         Unsigned32(0..255)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The number of continuous monitoring intervals (see sub10UnitMgmtAlarmMonitorIntvl)
                    where the measured object has crossed the raise threshold before the
                    alarm state is set to raised. This is intended to reduce the number of
                    alarm state transitions when the measured object value
                    is rapidly changing close to the configured raise threshold."
    DEFVAL { 1 }

    ::= { sub10UnitMgmtAlarmEntry 11 }


sub10UnitMgmtAlarmClearIntvls OBJECT-TYPE
    SYNTAX         Unsigned32(0..255)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The number of continuous monitoring intervals (see sub10UnitMgmtAlarmMonitorIntvl)
                    where the measured object has crossed the clear threshold before the
                    alarm state is set to cleared. This is intended to reduce the number
                    of alarm state transitions when the measured object value
                    is rapidly changing close to the configured clear threshold."
    DEFVAL { 1 }

    ::= { sub10UnitMgmtAlarmEntry 12 }


sub10UnitMgmtAlarmType OBJECT-TYPE
    SYNTAX         Sub10AlarmType
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The type of the alarm and defines the type of notification (trap) used
                    to inform management entities."
    DEFVAL { sub10UnitAlarm }

    ::= { sub10UnitMgmtAlarmEntry 13 }


sub10UnitMgmtAlarmSmtpAddress OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..63))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The smtp mail address. If set the alarm
                    is suitable formatted and sent to the given
                    mail address."

    ::= { sub10UnitMgmtAlarmEntry 14 }


sub10UnitMgmtAlarmToSyslog OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the alarm is suitably formatted
                    and sent to syslog."
    DEFVAL { stateEnabled }

    ::= { sub10UnitMgmtAlarmEntry 15 }


sub10UnitMgmtAlarmEnabled OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the alarm is enabled."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtAlarmEntry 16 }


sub10UnitMgmtAlarmMeasObjectVal OBJECT-TYPE
    SYNTAX         DisplayString (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The value of sub10UnitMgmtAlarmMeasObject formatted as a displayString which when
                    measured by the alarm subsystem caused an alarm to be either raised or cleared.
                    This is therefore the value of the object which was compared with and found to have
                    crossed the configured threshold causing the alarm to change state.
                    The value is not the current value of the measured object it is the observed value
                    which caused the change in alarm state."

    ::= { sub10UnitMgmtAlarmEntry 17 }


sub10UnitMgmtAlarmToSNMP OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the alarm is forwarded as an SNMP notification / trap."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtAlarmEntry 18 }


sub10UnitMgmtAlarmMeasObjIndex OBJECT-TYPE
    SYNTAX         Unsigned32(0..255)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The measured object's index which is a value from 1 and is the
                    instance of the measured object to be monitored. For scalar
                    objects this value is 0 and for non-scalar (or column)
                    objects this value must be 0 or greater and must match
                    the index value defined for the object where the object is
                    defined as a column in a table and the table has an index
                    defined in a certain range."
    DEFVAL { 0 }

    ::= { sub10UnitMgmtAlarmEntry 19 }


sub10UnitMgmtAlarmsUserDefStart OBJECT-TYPE
    SYNTAX         Sub10AlarmIndex
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "This defines the row index from which point rows in the sub10UnitMgmtAlarmTable can be
                    created and deleted and therefore where user defined alarms start.
                    Any attempt to create or delete a row in the sub10UnitMgmtAlarmTable with an index
                    below this value will fail."
    DEFVAL { 65 }

    ::= { sub10UnitMgmtAlarms 2 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtSnmp Objects
-- ****************************************************************************

sub10UnitMgmtSnmpAgent OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the agent will respond to valid v1/2c SNMP requests
                    for GET/SET/GET-NEXT of supported MIB objects. If this object and the
                    object sub10UnitMgmtSnmpv3 are both set to disabled then the unit
                    will no longer respond to SNMP requests. If this object is set to
                    enabled and sub10UnitMgmtSnmpv3 is set to enabled then the agent will respond
                    to both both v1/2c and v3 SNMP requests.
                    
                    !! IMPORTANT !!
                    If this object and sub10UnitMgmtSnmpv3 is set to disabled then the Agent
                    will no longer respond therefore it will not be possible to re-enable the agent using
                    SNMP. The agent can be re-enabled using the web GUI only.
                    
                    If this object is disabled then traps will not be forwarded by the unit."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtSnmp 1 }


sub10UnitMgmtSnmpTraps OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the agent will forward traps to all configured
                    trap destinations. If disabled traps are discarded.
                    Traps are not sent if sub10UnitMgmtSnmpAgent is 'disabled(2)'
                    regardless of the setting of this object."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtSnmp 2 }


sub10UnitMgmtSnmpv320Mib OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the agent will support the Sub10 Systems
                    v320 legacy MIB (LIBERATOR-MIB) objects and traps."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtSnmp 3 }


sub10UnitMgmtSnmpv3 OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the agent will support SNMPv3
                    PDUs and the SNMPv3 MIB configuration. This can be set to enabled or
                    disabled independently of the object sub10UnitMgmtSnmpAgent which
                    enables or disables the v1/2c agent.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtSnmp 4 }


sub10UnitMgmtSnmpTrpDstTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtSnmpTrpDstEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The table listing the each trap destination the local unit.
                    Each trap destination has an IpAddress and a trap community string"

    ::= { sub10UnitMgmtSnmp 5 }


sub10UnitMgmtSnmpTrpDstEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtSnmpTrpDstEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtSnmpTrpDstTable.
                    If the rowStatus is set to notInService(2) then the entry
                    is not valid and no columnar objects are returned."
    INDEX          { sub10UnitMgmtSnmpTrpDstIndex }

    ::= { sub10UnitMgmtSnmpTrpDstTable 1 }

Sub10UnitMgmtSnmpTrpDstEntry ::= SEQUENCE {
    sub10UnitMgmtSnmpTrpDstIndex                      Unsigned32,
    sub10UnitMgmtSnmpTrpDstRowStatus                  RowStatus,
    sub10UnitMgmtSnmpTrpDstIpAddr                     OCTET STRING,
    sub10UnitMgmtSnmpTrpDstCommunity                  OCTET STRING
}

sub10UnitMgmtSnmpTrpDstIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..3)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtSnmpTrpDstTable index.
                    If the rowStatus is not set to enabled(1) then
                    the entry is not valid and no columnar objects are returned
                    except for the rowStatus object itself."

    ::= { sub10UnitMgmtSnmpTrpDstEntry 1 }


sub10UnitMgmtSnmpTrpDstRowStatus OBJECT-TYPE
    SYNTAX         RowStatus
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION    "The row status of this row.
                    All table rows are fixed in size
                    and as such do not require conceptual row
                    creation. Instead the RowStatus is set to
                    active(1) or notInService(2) which indicates
                    that the row is operationally in use or not.
                    The default value is notInService(2) meaning the
                    entry is not used by default. To make
                    the entry valid this object must be set
                    to enabled(1), createAndGo(4) or createAndWait(5).
                    To delete the row set this to destroy(6)."
    DEFVAL { notInService }

    ::= { sub10UnitMgmtSnmpTrpDstEntry 2 }


sub10UnitMgmtSnmpTrpDstIpAddr OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned SNMP Trap Destination IP."

    ::= { sub10UnitMgmtSnmpTrpDstEntry 3 }


sub10UnitMgmtSnmpTrpDstCommunity OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The trap community name sent with each trap to this trap destination.
                    The trap community allows for some security in the target management terminal"

    ::= { sub10UnitMgmtSnmpTrpDstEntry 4 }


sub10UnitMgmtSnmpEngineIdFormat OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     v4IpAddress(1),
                     v6IpAddress(2),
                     macAddress(3),
                     textString(4)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The format of this SNMP v3 agent's engine id. The engine id is administratively
                    defined and is between 5 and 32 characters long and can be based on a number
                    of formats defined here."
    DEFVAL { textString }

    ::= { sub10UnitMgmtSnmp 6 }


sub10UnitMgmtSnmpEngineIdText OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..25))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "When sub10UnitMgmtSnmpEngineIdFormat='textString(4)' this object contains the administratively defined
                    string used to build the SNMP v3 engine id in sub10UnitMgmtSnmpEngineId."
    DEFVAL { "Sub10Systems" }

    ::= { sub10UnitMgmtSnmp 7 }


sub10UnitMgmtSnmpEngineId OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (5..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The  administratively assigned SNMP v3 engine id. The construction of the
                    engine id is dependant on the setting of sub10UnitMgmtSnmpEngineIdFormat and
                    must be unique to the unit."

    ::= { sub10UnitMgmtSnmp 8 }


sub10UnitMgmtSnmpOperAuthProto OBJECT-TYPE
    SYNTAX         Sub10Snmpv3AuthProtocol
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The authentication protocol currently assigned to users of group type operation."
    DEFVAL { noAuth }

    ::= { sub10UnitMgmtSnmp 9 }


sub10UnitMgmtSnmpOperPrivProto OBJECT-TYPE
    SYNTAX         Sub10Snmpv3PrivProtocol
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The privacy protocol currently assigned to users of group type operation."
    DEFVAL { noPriv }

    ::= { sub10UnitMgmtSnmp 10 }


sub10UnitMgmtSnmpAdminAuthProto OBJECT-TYPE
    SYNTAX         Sub10Snmpv3AuthProtocol
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The authentication protocol currently assigned to users of group type administration."
    DEFVAL { md5 }

    ::= { sub10UnitMgmtSnmp 11 }


sub10UnitMgmtSnmpAdminPrivProto OBJECT-TYPE
    SYNTAX         Sub10Snmpv3PrivProtocol
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The privacy protocol currently assigned to users of group type administration."
    DEFVAL { noPriv }

    ::= { sub10UnitMgmtSnmp 12 }


sub10UnitMgmtSnmpMaintAuthProto OBJECT-TYPE
    SYNTAX         Sub10Snmpv3AuthProtocol
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The authentication protocol currently assigned to users of group type maintenance."
    DEFVAL { md5 }

    ::= { sub10UnitMgmtSnmp 13 }


sub10UnitMgmtSnmpMaintPrivProto OBJECT-TYPE
    SYNTAX         Sub10Snmpv3PrivProtocol
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The privacy protocol currently assigned to users of group type maintenance."
    DEFVAL { des }

    ::= { sub10UnitMgmtSnmp 14 }


sub10UnitMgmtSnmpUserTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtSnmpUserEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The table of SNMPv3 User configuration."

    ::= { sub10UnitMgmtSnmp 15 }


sub10UnitMgmtSnmpUserEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtSnmpUserEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtSnmpUserTable.
                    If the rowStatus is set to notInService(2) then the entry
                    is not valid and no columnar objects are returned."
    INDEX          { sub10UnitMgmtSnmpUserIndex }

    ::= { sub10UnitMgmtSnmpUserTable 1 }

Sub10UnitMgmtSnmpUserEntry ::= SEQUENCE {
    sub10UnitMgmtSnmpUserIndex                        Unsigned32,
    sub10UnitMgmtSnmpUserRowStatus                    RowStatus,
    sub10UnitMgmtSnmpUserName                         OCTET STRING,
    sub10UnitMgmtSnmpUserGroup                        Sub10UserGroup,
    sub10UnitMgmtSnmpUserAuthPwd                      OCTET STRING,
    sub10UnitMgmtSnmpUserAuthPwdChk                   OCTET STRING,
    sub10UnitMgmtSnmpUserPrivPwd                      OCTET STRING,
    sub10UnitMgmtSnmpUserPrivPwdChk                   OCTET STRING
}

sub10UnitMgmtSnmpUserIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..10)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtSnmpUserTable index.
                    If the rowStatus is not set to enabled(1) then
                    the entry is not valid and no columnar objects are returned
                    except for the rowStatus object itself."

    ::= { sub10UnitMgmtSnmpUserEntry 1 }


sub10UnitMgmtSnmpUserRowStatus OBJECT-TYPE
    SYNTAX         RowStatus
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION    "The row status of this row.
                    All table rows are fixed in size
                    and as such do not require conceptual row
                    creation. Instead the RowStatus is set to
                    active(1) or notInService(2) which indicates
                    that the row is operationally in use or not.
                    The default value is notInService(2) meaning the
                    entry is not used by default. To make
                    the entry valid this object must be set
                    to enabled(1), createAndGo(4) or createAndWait(5).
                    To delete the row set this to destroy(6)."
    DEFVAL { notInService }

    ::= { sub10UnitMgmtSnmpUserEntry 2 }


sub10UnitMgmtSnmpUserName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned SNMPv3 User name."

    ::= { sub10UnitMgmtSnmpUserEntry 3 }


sub10UnitMgmtSnmpUserGroup OBJECT-TYPE
    SYNTAX         Sub10UserGroup
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The user group type. Authentication and Privacy policies are assigned to a
                    user group and not individual users."
    DEFVAL { operation }

    ::= { sub10UnitMgmtSnmpUserEntry 4 }


sub10UnitMgmtSnmpUserAuthPwd OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (6..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The unique SNMPv3 authentication protocol password provided by the system administrator.
                    This object can be set but is not visible when read."

    ::= { sub10UnitMgmtSnmpUserEntry 5 }


sub10UnitMgmtSnmpUserAuthPwdChk OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (6..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The unique SNMPv3 authentication protocol password provided by the system administrator
                    used to verify the setting of sub10UnitMgmtSnmpUserAuthPassword.
                    This object can be set but is not visible when read."

    ::= { sub10UnitMgmtSnmpUserEntry 6 }


sub10UnitMgmtSnmpUserPrivPwd OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (6..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The unique SNMPv3 privacy protocol password provided by the system administrator.
                    This object can be set but is not visible when read."

    ::= { sub10UnitMgmtSnmpUserEntry 7 }


sub10UnitMgmtSnmpUserPrivPwdChk OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (6..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The unique SNMPv3 privacy protocol password provided by the system administrator
                    used to verify the setting of sub10UnitMgmtSnmpUserPrivPassword.
                    This object can be set but is not visible when read."

    ::= { sub10UnitMgmtSnmpUserEntry 8 }


sub10UnitMgmtSnmpAccessTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtSnmpAccessEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The table of SNMPv3 Access configuration."

    ::= { sub10UnitMgmtSnmp 16 }


sub10UnitMgmtSnmpAccessEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtSnmpAccessEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtSnmpAccessTable.
                    If the rowStatus is set to notInService(2) then the entry
                    is not valid and no columnar objects are returned."
    INDEX          { sub10UnitMgmtSnmpAccessIndex }

    ::= { sub10UnitMgmtSnmpAccessTable 1 }

Sub10UnitMgmtSnmpAccessEntry ::= SEQUENCE {
    sub10UnitMgmtSnmpAccessIndex                      Unsigned32,
    sub10UnitMgmtSnmpAccessRowStatus                  RowStatus,
    sub10UnitMgmtSnmpAccessName                       OCTET STRING,
    sub10UnitMgmtSnmpAccessIpAddr                     OCTET STRING
}

sub10UnitMgmtSnmpAccessIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..3)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtSnmpAccessTable index.
                    If the rowStatus is not set to enabled(1) then
                    the entry is not valid and no columnar objects are returned
                    except for the rowStatus object itself."

    ::= { sub10UnitMgmtSnmpAccessEntry 1 }


sub10UnitMgmtSnmpAccessRowStatus OBJECT-TYPE
    SYNTAX         RowStatus
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION    "The row status of this row.
                    All table rows are fixed in size
                    and as such do not require conceptual row
                    creation. Instead the RowStatus is set to
                    active(1) or notInService(2) which indicates
                    that the row is operationally in use or not.
                    The default value is notInService(2) meaning the
                    entry is not used by default. To make
                    the entry valid this object must be set
                    to enabled(1), createAndGo(4) or createAndWait(5).
                    To delete the row set this to destroy(6)."
    DEFVAL { notInService }

    ::= { sub10UnitMgmtSnmpAccessEntry 2 }


sub10UnitMgmtSnmpAccessName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned SNMPv3 access name. This is used as an alias for the
                    access address for purposes of identification. The name is not used in the SNMP protocol."

    ::= { sub10UnitMgmtSnmpAccessEntry 3 }


sub10UnitMgmtSnmpAccessIpAddr OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned SNMP access IP address. Only SNMP requests from this
                    address will be serviced by the SNMP agent."

    ::= { sub10UnitMgmtSnmpAccessEntry 4 }


sub10UnitMgmtSnmpTargetTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtSnmpTargetEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The table of SNMPv3 Target configuration.
                    This table defines the addresses of target SNMPv3 managers to send
                    Notifications."

    ::= { sub10UnitMgmtSnmp 17 }


sub10UnitMgmtSnmpTargetEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtSnmpTargetEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtSnmpTargetTable.
                    If the rowStatus is set to notInService(2) then the entry
                    is not valid and no columnar objects are returned."
    INDEX          { sub10UnitMgmtSnmpTargetIndex }

    ::= { sub10UnitMgmtSnmpTargetTable 1 }

Sub10UnitMgmtSnmpTargetEntry ::= SEQUENCE {
    sub10UnitMgmtSnmpTargetIndex                      Unsigned32,
    sub10UnitMgmtSnmpTargetRowStatus                  RowStatus,
    sub10UnitMgmtSnmpTargetName                       OCTET STRING,
    sub10UnitMgmtSnmpTargetIpAddr                     OCTET STRING,
    sub10UnitMgmtSnmpTargetUserName                   OCTET STRING
}

sub10UnitMgmtSnmpTargetIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..3)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtSnmpTargetTable index.
                    If the rowStatus is not set to enabled(1) then
                    the entry is not valid and no columnar objects are returned
                    except for the rowStatus object itself."

    ::= { sub10UnitMgmtSnmpTargetEntry 1 }


sub10UnitMgmtSnmpTargetRowStatus OBJECT-TYPE
    SYNTAX         RowStatus
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION    "The row status of this row.
                    All table rows are fixed in size
                    and as such do not require conceptual row
                    creation. Instead the RowStatus is set to
                    active(1) or notInService(2) which indicates
                    that the row is operationally in use or not.
                    The default value is notInService(2) meaning the
                    entry is not used by default. To make
                    the entry valid this object must be set
                    to enabled(1), createAndGo(4) or createAndWait(5).
                    To delete the row set this to destroy(6)."
    DEFVAL { notInService }

    ::= { sub10UnitMgmtSnmpTargetEntry 2 }


sub10UnitMgmtSnmpTargetName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned SNMPv3 target name."

    ::= { sub10UnitMgmtSnmpTargetEntry 3 }


sub10UnitMgmtSnmpTargetIpAddr OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned SNMP target IP address. When enabled SNMPv3 Notifications
                    are sent to this address."

    ::= { sub10UnitMgmtSnmpTargetEntry 4 }


sub10UnitMgmtSnmpTargetUserName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned SNMPv3 target user name.
                    The security model configured for this user is used when sending
                    notifications to the target address."

    ::= { sub10UnitMgmtSnmpTargetEntry 5 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtFirmware Objects
-- ****************************************************************************

sub10UnitMgmtFirmwareSelectBank OBJECT-TYPE
    SYNTAX         Sub10FirmwareBank
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Currently selected bank which unit uses to boot."

    ::= { sub10UnitMgmtFirmware 1 }


sub10UnitMgmtFirmwareLoadedBank OBJECT-TYPE
    SYNTAX         Sub10FirmwareBank
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Currently loaded bank. The firmware version number running in this bank is
                    defined by sub10UnitMgmtFirmwareVersion."

    ::= { sub10UnitMgmtFirmware 2 }


sub10UnitMgmtFirmwareVersion OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The firmware version currently running on the local unit.
                    This is the version of the firmware loaded into the bank
                    number defined in sub10UnitMgmtFirmwareLoadedBank."

    ::= { sub10UnitMgmtFirmware 3 }


sub10UnitMgmtFirmwareBootVersion OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The firmware boot version currently running on the local unit.
                    This is the boot version of the firmware loaded into the bank
                    number defined in sub10UnitMgmtFirmwareLoadedBank."

    ::= { sub10UnitMgmtFirmware 4 }


sub10UnitMgmtFirmwareAction OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     fmwNone(1),
                     fmwReboot(2),
                     fmwCopyInactiveBank(3),
                     fmwUploadInactiveBank(4)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The firmware bank action object is used to perform certain
                    actions on a firmware bank. Using this it is possible to reboot the
                    unit, copy banks and upload software to the currently inactive bank.
                    
                    After a Set of this object it's value is always reset to fmwNone(1).
                    
                    The progress of the action may be monitored using a get of the objects
                    'sub10UnitMgmtFirmwareActStatus' and 'sub10UnitMgmtFirmwareActProgress'.
                    
                    fmwNone(1) -
                    Perform no firmware action  unit.
                    
                    fmwReboot(2) -
                    Perform immediate unit reboot of the unit and start the
                    firmware currently loaded in 'sub10UnitMgmtFirmwareSelectBank'.
                    
                    fmwCopyInactiveBank(3) -
                    Copy the firmware currently loaded in 'sub10UnitMgmtFirmwareLoadedBank'
                    to the inactive bank.
                    
                    fmwUploadInactiveBank(4) -
                    Upload the firmware image named by 'sub10UnitMgmtFirmwareUplImage' from the
                    server address 'sub10UnitFirmwareUplServerIp' and load
                    to the inactive bank."

    ::= { sub10UnitMgmtFirmware 5 }


sub10UnitMgmtFirmwareBankTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtFirmwareBankEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This tables contains information regarding the firmware banks."

    ::= { sub10UnitMgmtFirmware 6 }


sub10UnitMgmtFirmwareBankEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtFirmwareBankEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtFirmwareBankTable."
    INDEX          { sub10UnitMgmtFirmwareBankIndex }

    ::= { sub10UnitMgmtFirmwareBankTable 1 }

Sub10UnitMgmtFirmwareBankEntry ::= SEQUENCE {
    sub10UnitMgmtFirmwareBankIndex                    Sub10FirmwareBank,
    sub10UnitMgmtFirmwareBankVersion                  OCTET STRING,
    sub10UnitMgmtFirmwareBankImage                    OCTET STRING
}

sub10UnitMgmtFirmwareBankIndex OBJECT-TYPE
    SYNTAX         Sub10FirmwareBank
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtFirmwareBankTable index."

    ::= { sub10UnitMgmtFirmwareBankEntry 1 }


sub10UnitMgmtFirmwareBankVersion OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..15))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The firmware version currently installed in this bank."

    ::= { sub10UnitMgmtFirmwareBankEntry 2 }


sub10UnitMgmtFirmwareBankImage OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..64))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The firmware image name currently installed in this bank."

    ::= { sub10UnitMgmtFirmwareBankEntry 3 }


sub10UnitMgmtFirmwareUplImage OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..64))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The firmware image name to be uploaded from the server
                    defined by 'sub10UnitFirmwareUplServerIp' using the firmware action
                    'sub10UnitFirmwareAction=fmwUpload(4)'."

    ::= { sub10UnitMgmtFirmware 7 }


sub10UnitMgmtFirmwareUplSvrIp OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (7..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The IP address of the  server used to upload the firmware image
                    defined by sub10UnitMgmtFirmwareUplImage."

    ::= { sub10UnitMgmtFirmware 8 }


sub10UnitMgmtFirmwareFromBank OBJECT-TYPE
    SYNTAX         Sub10FirmwareBank
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The bank currently being copied from by the Firmware action sub10UnitMgmtFirmwareAction = fmwCopyInactiveBank(4)."

    ::= { sub10UnitMgmtFirmware 9 }


sub10UnitMgmtFirmwareToBank OBJECT-TYPE
    SYNTAX         Sub10FirmwareBank
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The bank currently being uploaded or copied to by the Firmware actions sub10UnitMgmtFirmwareAction = fmwUploadInactiveBank(4)
                    or sub10UnitMgmtFirmwareAction = fmwCopyInactiveBank(4)."

    ::= { sub10UnitMgmtFirmware 10 }


sub10UnitMgmtFirmwareActStatus OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     fmwUploadSuccess(1),
                     fmwUploadFailed(2),
                     fmwUploadTimeout(3),
                     fmwUploadFileNotFound(4),
                     fmwUploadInvalid(5),
                     fmwUploadingImage(6),
                     fmwUploadingImageComplete(7),
                     fmwUploadWritingBank(8),
                     fmwUploadWritingBankComplete(9),
                     fmwValidatingImage(10),
                     fmwImageValidateSuccess(11),
                     fmwImageValidateFailed(12),
                     fmwCopyingBank(13),
                     fmwCopyingBankComplete(14),
                     fmwCopyBankFailed(15),
                     fmwCopyBankSuccess(16)
                   }
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The status of the firmware bank action which
                    can be repeatedly read using a Get to report the progress of the
                    the action and verify that it has either succeeded or failed.  The
                    value is left in the final state after the Firmware Action has completed."

    ::= { sub10UnitMgmtFirmware 11 }


sub10UnitMgmtFirmwareActProgress OBJECT-TYPE
    SYNTAX         Integer32(0..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The % progress of firmware action when loading the firmware image
                    to a firmware bank or copying a firmware image from on1 bank to another bank."
    DEFVAL { 0 }

    ::= { sub10UnitMgmtFirmware 12 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtDNS Objects
-- ****************************************************************************

sub10UnitMgmtDNSTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10UnitMgmtDNSEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This tables defines the Domain Name Servers."

    ::= { sub10UnitMgmtDNS 1 }


sub10UnitMgmtDNSEntry OBJECT-TYPE
    SYNTAX         Sub10UnitMgmtDNSEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtDNSTable."
    INDEX          { sub10UnitMgmtDNSIndex }

    ::= { sub10UnitMgmtDNSTable 1 }

Sub10UnitMgmtDNSEntry ::= SEQUENCE {
    sub10UnitMgmtDNSIndex                             Unsigned32,
    sub10UnitMgmtDNServer                             OCTET STRING
}

sub10UnitMgmtDNSIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..2)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10UnitMgmtDNSTable index."

    ::= { sub10UnitMgmtDNSEntry 1 }


sub10UnitMgmtDNServer OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (7..15))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned DNS server IP for the unit.
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."

    ::= { sub10UnitMgmtDNSEntry 2 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtEncryption Objects
-- ****************************************************************************

sub10UnitMgmtEncryptMode OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     encryptNone(0),
                     encryptAES128(1),
                     encryptAES192(2),
                     encryptAES256(3)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned License key for the unit.
                    The license key enables capabilities within the unit.
                    The capabilities currently available by the configured
                    license key are readable objects."

    ::= { sub10UnitMgmtEncryption 1 }


sub10UnitMgmtEncryptKey OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..128))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned Encryption key."

    ::= { sub10UnitMgmtEncryption 2 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtLicense Objects
-- ****************************************************************************

sub10UnitMgmtLicenseKey OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..128))
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The administratively assigned License key for the unit.
                    The license key enables capabilities within the unit.
                    The capabilities currently available by the configured
                    license key are readable objects."

    ::= { sub10UnitMgmtLicense 1 }


sub10UnitMgmtLicenseAES OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Indication if AES encryption is included in the current license key."
    DEFVAL { stateDisabled }

    ::= { sub10UnitMgmtLicense 2 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtSyncE Objects
-- ****************************************************************************

sub10UnitMgmtSyncEMode OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     syncENone(0),
                     syncEProvider(1),
                     syncEConsumer(2)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The SyncE Mode. Setting this to syncEProvider(1) configures the unit as
                    a Slave clock. In this case the unit at the other end of the
                    link should be set to syncEConsumer(2).
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."

    ::= { sub10UnitMgmtSyncE 1 }


-- ****************************************************************************
-- .sub10UnitMgmt.sub10UnitMgmtActions Objects
-- ****************************************************************************

sub10UnitMgmtTransaction OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     transactionNone(1),
                     transactionCommit(2),
                     transactionRollback(3)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "This is used to commit or rollback the setting of object configuration.
                    It only applies where the objects are subject to transactional requirements.
                    For example the setting of the Unit's IP Address requires that a transaction
                    be committed. If the transaction is not committed after a rollback timeout then
                    the unit will automatically action a rollback to the previous value.
                    
                    The rollback timeout is configurable 'sub10UnitMgmtRollbackTimeout'"

    ::= { sub10UnitMgmtActions 1 }


sub10UnitMgmtTransactionStatus OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     transStatusNone(1),
                     transStatusActive(2),
                     transStatusCommitted(3),
                     transStatusRollback(4)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "This indicates either a transaction is currently in progress or
                    if the last transaction was committed successfully or was rolled
                    back."

    ::= { sub10UnitMgmtActions 2 }


sub10UnitMgmtRollbackTimeout OBJECT-TYPE
    SYNTAX         Unsigned32(30..300)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The timeout in seconds after which uncommitted Sets
                    will be rolled back to their previous values.
                    A rollback only affects those objects which are
                    subject to transactions."
    DEFVAL { 180 }

    ::= { sub10UnitMgmtActions 3 }


sub10UnitMgmtTransactionMode OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Enables and Disables transactions. If sub10UnitMgmTransactionMode is
                    stateDisabled(0) then no objects are subject to commit and rollback."
    DEFVAL { stateEnabled }

    ::= { sub10UnitMgmtActions 4 }


sub10UnitMgmtResetAction OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     resetNone(1),
                     resetFactoryDefaults(2),
                     resetFactoryDefaultsNoSave(3),
                     resetStatistics(4),
                     resetAlarmConfig(5)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "This is used to reset unit configuration and stats.
                    resetFactoryDefaults(2)
                    - Resets all configuration to Factory Defaults, requires a reboot.
                    
                    resetStatistics(3)
                    - Resets according to setting of sub10UnitMgmtResetStatsGroup
                    For reset of all stats sub10UnitMgmtResetStatsGroup = 'statsGroupAll'.
                    
                    resetAlarmConfig(4)
                    - Resets according to setting of sub10UnitMgmtResetAlarmsType
                    For reset of all alarms sub10UnitMgmtResetAlarmsType = 'resetAlarmTypeAll'."

    ::= { sub10UnitMgmtActions 5 }


sub10UnitMgmtResetStatsGroup OBJECT-TYPE
    SYNTAX         Sub10StatsGroup
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "This is used to reset specific statistics when set prior to submitting
                    sub10UnitMgmtResetAction = resetStatistics(4).
                    
                    For reset of all stats sub10UnitMgmtResetStatsGroup = 'statsGroupAll'."

    ::= { sub10UnitMgmtActions 6 }


sub10UnitMgmtResetAlarmsType OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     resetAlarmTypeAll(1),
                     resetAlarmTypeFixed(2),
                     resetAlarmTypeUser(3)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "This is used to reset specific alarm configuration when set prior to submitting
                    sub10UnitMgmtResetAction = resetAlarmConfig(5).
                    
                    For reset of all alarms sub10UnitMgmtResetAlarmsType = resetAlarmTypeAll(1)."

    ::= { sub10UnitMgmtActions 7 }


-- ****************************************************************************
-- .sub10EthernetStatus.sub10EthernetLocalStatus Objects
-- ****************************************************************************

sub10EthLclStatusTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthLclStatusEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table defines the local status objects of the ethernet interface."

    ::= { sub10EthernetLocalStatus 1 }


sub10EthLclStatusEntry OBJECT-TYPE
    SYNTAX         Sub10EthLclStatusEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthLclStatusTable."
    INDEX          { sub10EthIfIndex }

    ::= { sub10EthLclStatusTable 1 }

Sub10EthLclStatusEntry ::= SEQUENCE {
    sub10EthLclLinkStatus                             Sub10OperStatus,
    sub10EthLclMacAddress                             Sub10MacAddress,
    sub10EthLclSpeed                                  Unsigned32,
    sub10EthLclDuplex                                 Sub10Duplex,
    sub10EthLclMDI                                    Sub10MDIType,
    sub10EthIfIndex                                   Sub10EthInterfaceIndex
}

sub10EthLclLinkStatus OBJECT-TYPE
    SYNTAX         Sub10OperStatus
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The status of the ethernet link. Synonymous with interfaces OperState"

    ::= { sub10EthLclStatusEntry 1 }


sub10EthLclMacAddress OBJECT-TYPE
    SYNTAX         Sub10MacAddress
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The MAC address of the local ethernet interface. Synonymous with interfaces ifPhysAddress"

    ::= { sub10EthLclStatusEntry 2 }


sub10EthLclSpeed OBJECT-TYPE
    SYNTAX         Unsigned32(0..4294967295)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The estimated speed of the local ethernet interface in bits per second. Synonymous with interfaces ifSpeed"

    ::= { sub10EthLclStatusEntry 3 }


sub10EthLclDuplex OBJECT-TYPE
    SYNTAX         Sub10Duplex
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The duplex operation of the local ethernet interface"

    ::= { sub10EthLclStatusEntry 4 }


sub10EthLclMDI OBJECT-TYPE
    SYNTAX         Sub10MDIType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The MDI/MDIX state of the local ethernet interface"

    ::= { sub10EthLclStatusEntry 5 }


sub10EthIfIndex OBJECT-TYPE
    SYNTAX         Sub10EthInterfaceIndex
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "A unique value, greater than zero, for each ethernet interface.  It
                    is recommended that values are assigned contiguously
                    starting from 1. This is used as an index to the ethernet based
                    enterprise MIB tables and will reflect the number of physical
                    ethernet ports."

    ::= { sub10EthLclStatusEntry 6 }


-- ****************************************************************************
-- .sub10EthernetStatus.sub10EthernetRemoteStatus Objects
-- ****************************************************************************

sub10EthRmtStatusTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthRmtStatusEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table defines the remote status objects of the ethernet interface."

    ::= { sub10EthernetRemoteStatus 1 }


sub10EthRmtStatusEntry OBJECT-TYPE
    SYNTAX         Sub10EthRmtStatusEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthRmtStatusTable."
    INDEX          { sub10EthIfIndex }

    ::= { sub10EthRmtStatusTable 1 }

Sub10EthRmtStatusEntry ::= SEQUENCE {
    sub10EthRmtLinkStatus                             Sub10OperStatus,
    sub10EthRmtMacAddress                             Sub10MacAddress,
    sub10EthRmtSpeed                                  Unsigned32,
    sub10EthRmtDuplex                                 Sub10Duplex,
    sub10EthRmtMDI                                    Sub10MDIType
}

sub10EthRmtLinkStatus OBJECT-TYPE
    SYNTAX         Sub10OperStatus
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The status of the remote ethernet interface. Synonymous but
                    not identical with interfaces OperState on the remote unit"

    ::= { sub10EthRmtStatusEntry 1 }


sub10EthRmtMacAddress OBJECT-TYPE
    SYNTAX         Sub10MacAddress
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The MAC address of the remote ethernet interface. Synonymous
                    with interfaces ifPhysAddress on the remote unit."

    ::= { sub10EthRmtStatusEntry 2 }


sub10EthRmtSpeed OBJECT-TYPE
    SYNTAX         Unsigned32(0..4294967295)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The estimated speed of the remote ethernet interface in
                    bits per second. Synonymous with interfaces ifSpeed on the remote unit"

    ::= { sub10EthRmtStatusEntry 3 }


sub10EthRmtDuplex OBJECT-TYPE
    SYNTAX         Sub10Duplex
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The duplex operation of the remote ethernet interface"

    ::= { sub10EthRmtStatusEntry 4 }


sub10EthRmtMDI OBJECT-TYPE
    SYNTAX         Sub10MDIType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The MDI/MDIX state of the remote ethernet interface"

    ::= { sub10EthRmtStatusEntry 5 }


-- ****************************************************************************
-- .sub10EthernetMgmt.sub10EthMgmtPhy Objects
-- ****************************************************************************

sub10EthMgmtPhyTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtPhyEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table defines the status objects of the ethernet interface."

    ::= { sub10EthMgmtPhy 1 }


sub10EthMgmtPhyEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtPhyEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthMgmtPhyTable."
    INDEX          { sub10EthIfIndex }

    ::= { sub10EthMgmtPhyTable 1 }

Sub10EthMgmtPhyEntry ::= SEQUENCE {
    sub10EthMgmtPhyAutoNeg                            Sub10State,
    sub10EthMgmtPhySpeed                              Unsigned32,
    sub10EthMgmtPhyDuplex                             Sub10Duplex,
    sub10EthMgmtPhyMDI                                Sub10MDIType
}

sub10EthMgmtPhyAutoNeg OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Auto-negotiation of ethernet interface parameters."
    DEFVAL { stateDisabled }

    ::= { sub10EthMgmtPhyEntry 1 }


sub10EthMgmtPhySpeed OBJECT-TYPE
    SYNTAX         Unsigned32(0..4294967295)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Speed of the ethernet interface in bits per second used
                    only when sub10UnitMgmtPhyAutoNeg=disabled."

    ::= { sub10EthMgmtPhyEntry 2 }


sub10EthMgmtPhyDuplex OBJECT-TYPE
    SYNTAX         Sub10Duplex
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The duplex operation of the ethernet interface used
                    only when sub10UnitMgmtPhyAutoNeg=disabled."

    ::= { sub10EthMgmtPhyEntry 3 }


sub10EthMgmtPhyMDI OBJECT-TYPE
    SYNTAX         Sub10MDIType
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The duplex operation of the ethernet interface used
                    only when sub10UnitMgmtPhyAutoNeg=disabled."

    ::= { sub10EthMgmtPhyEntry 4 }


-- ****************************************************************************
-- .sub10EthernetMgmt.sub10EthMgmtVlan Objects
-- ****************************************************************************

sub10EthMgmtVlanFiltering OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "VLAN filtering on the ethernet interface.
                    This applies to user traffic only not management traffic however
                    to enable VLAN filtering a Unit Management VLAN configuration
                    must be set. See sub10UnitMgmtVlan.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { stateDisabled }

    ::= { sub10EthMgmtVlan 1 }


sub10EthMgmtVlanDefaultEnabled OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Enable Default VLAN tagging and untagging on the ethernet interface.
                    Only valid when sub10EthMgmtVlanFiltering is enabled.
                    
                    Default VAN tagging allows manipulation of untagged traffic ingressing
                    the interface by applying the specified Default VLAN Id, priority and
                    DEI and manipulation of traffic egressing the ethernet interface that
                    is tagged with the Default VLAN by stripping off the VLAN and transmitting
                    as untagged traffic.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { stateDisabled }

    ::= { sub10EthMgmtVlan 2 }


sub10EthMgmtVlanDefaultId OBJECT-TYPE
    SYNTAX         Sub10VlanId
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The default vlan id which is tagged to all incoming untagged
                    user traffic ingressing the ethernet interface. The default VLAN
                    is also untagged from all user traffic egressing the ethernet
                    interface.
                    
                    The default VLAN must be present in the allowed VLAN table
                    sub10EthMgmtVlanAllowedTable and is only valid when
                    sub10EthMgmtVlanDefaultEnabled and sub10EthMgmtVlanFiltering are enabled.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { 0 }

    ::= { sub10EthMgmtVlan 3 }


sub10EthMgmtVlanDefaultPriority OBJECT-TYPE
    SYNTAX         Sub10VlanPriority
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The default vlan priority used when tagging the default VLAN
                    to all incoming untagged user traffic ingressing the ethernet interface.
                    
                    Only valid when sub10EthMgmtVlanDefaultEnabled and sub10EthMgmtVlanFiltering are enabled.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { 0 }

    ::= { sub10EthMgmtVlan 4 }


sub10EthMgmtVlanDefaultDEI OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The default vlan drop eligible indicator used when tagging the default VLAN
                    to all incoming untagged user traffic ingressing the ethernet interface.
                    
                    Only valid when sub10EthMgmtVlanDefaultEnabled and sub10EthMgmtVlanFiltering are enabled.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { stateDisabled }

    ::= { sub10EthMgmtVlan 5 }


sub10EthMgmtVlanIngressAction OBJECT-TYPE
    SYNTAX         Sub10VlanTagAction
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The manipulation action used for all ethernet frames
                    being received (ingress) in from the ethernet interface.
                    Only if the default VLAN is set sub10EthMgmtVlanDefaultVlanId > 0
                    and sub10EthMgmtVlanFiltering is enabled.
                    
                    untag      - untag all incoming frames that are tagged with sub10EthMgmtVlanDefaultVlanId.
                    tag        - tag all incoming untagged frames with sub10EthMgmtVlanDefaultVlanId,
                    ,sub10EthMgmtVlanDefaultPriority and sub10EthMgmtVlanDefaultDEI.
                    and priority sub10EthMgmtTagWithVlanPriority.
                    drop       - discard the frame, do not forward.
                    
                    This is currently fixed to tag all ingressing traffic
                    if a default VLAN is set sub10EthMgmtVlanDefaultVlanId > 0."
    DEFVAL { tag }

    ::= { sub10EthMgmtVlan 6 }


sub10EthMgmtVlanEgressAction OBJECT-TYPE
    SYNTAX         Sub10VlanTagAction
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The manipulation action used for all ethernet frames
                    being transmitted (egress) out of the ethernet interface.
                    Only if the default VLAN is set sub10EthMgmtVlanDefaultVlanId > 0
                    and sub10EthMgmtVlanFiltering is enabled.
                    
                    untag      - untag all outgoing frames that are tagged with sub10EthMgmtVlanDefaultVlanId.
                    tag        - tag all outgoing untagged frames with sub10EthMgmtVlanDefaultVlanId,
                    ,sub10EthMgmtVlanDefaultPriority and sub10EthMgmtVlanDefaultDEI.
                    and priority sub10EthMgmtTagWithVlanPriority.
                    drop       - discard the frame, do not forward.
                    
                    This is currently fixed to untag all egressing traffic."
    DEFVAL { untag }

    ::= { sub10EthMgmtVlan 7 }


sub10EthMgmtVlanAllowedTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtVlanAllowedEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This tables defines the VLANs that are allowed to pass through the ethernet
                    interface. The table is only used when the value of sub10EthMgmtVlanFiltering is enabled.
                    
                    The table only contains an entry if the specific vlan is to be allowed to flow through the interface.
                    If a VLAN is not in the table then the ethernet frame is dropped.
                    
                    If sub10EthMgmtVlanFiltering is enabled this table must contain at least 1 VLAN.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."

    ::= { sub10EthMgmtVlan 8 }


sub10EthMgmtVlanAllowedEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtVlanAllowedEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10UnitMgmtVlanTable.
                    If the rowStatus is set to notInService(2) then
                    the entry is not valid and no columnar objects are returned."
    INDEX          { sub10EthMgmtVlanAllowedIndex }

    ::= { sub10EthMgmtVlanAllowedTable 1 }

Sub10EthMgmtVlanAllowedEntry ::= SEQUENCE {
    sub10EthMgmtVlanAllowedIndex                      Unsigned32,
    sub10EthMgmtVlanAllowedRowStatus                  RowStatus,
    sub10EthMgmtVlanAllowedId                         Sub10VlanId
}

sub10EthMgmtVlanAllowedIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..60)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10EthMgmtVlanTable index."

    ::= { sub10EthMgmtVlanAllowedEntry 1 }


sub10EthMgmtVlanAllowedRowStatus OBJECT-TYPE
    SYNTAX         RowStatus
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION    "The row status of this row.
                    All table rows are fixed in size
                    and as such do not require conceptual row
                    creation. Instead the RowStatus is set to
                    active(1) or notInService(2) which indicates
                    that the row is operationally in use or not.
                    The default value is notInService(2) meaning
                    the entry is not used by default. To make
                    the entry valid with already consistent row
                    objects this object must be set to active(1).
                    If row objects are not yet set to consistent
                    values the row status must first be set to
                    createAndWait(5) after which the row status is
                    set to 'notReady(3)'. When in the 'notReady(3)'
                    state row objects can then be set to consistent
                    values before row status can be set to active(1).
                    To delete the row set this to destroy(6)."
    DEFVAL { notInService }

    ::= { sub10EthMgmtVlanAllowedEntry 2 }


sub10EthMgmtVlanAllowedId OBJECT-TYPE
    SYNTAX         Sub10VlanId
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The vlan id which is allowed to pass through the ethernet interface.
                    Duplicate vlan ids in multiple entries of the table with RowStatus 'active(1)
                    are not allowed and any attempt to set a duplicate active entry will be rejected.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { 0 }

    ::= { sub10EthMgmtVlanAllowedEntry 3 }


-- ****************************************************************************
-- .sub10EthernetMgmt.sub10EthMgmtQoS Objects
-- ****************************************************************************

sub10EthMgmtQoSActiveState OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "This defines if QoS is enabled or disabled."
    DEFVAL { stateDisabled }

    ::= { sub10EthMgmtQoS 1 }


sub10EthMgmtQoSMode OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     qosEthernet(1),
                     qosIPMPLS(2)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Defines the mode of QoS action to be applied to
                    the QoS queues.
                    
                    qosEthernet:
                    
                    - Traffic is mapped to QoS queues
                    - based on Ethernet protocol
                    - VLANId and/or PCP Bits.
                    - See sub10EthMgmtQoSVlanMappingTable
                    - and sub10EthMgmtQoSPCPMappingTable.
                    
                    qosIPMPLS:
                    
                    - Traffic is mapped to QoS queues
                    - based on IP protocol
                    - DSCP markings and/or MPLS traffic
                    - classes.
                    
                    See sub10EthMgmtQoSDSCPMappingTable and sub10EthMgmtQoSMPLSMappingTable."
    DEFVAL { qosEthernet }

    ::= { sub10EthMgmtQoS 2 }


sub10EthMgmtQoSUntaggedQueue OBJECT-TYPE
    SYNTAX         Sub10QoSQueue
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "This defines the QoS queue where all untagged
                    ethernet frames are placed.
                    
                    Only valid when sub10EthMgmtQoSQType = 'qosEthernet'
                    
                    For tagged frames see sub10EthMgmtQoSVlanMappingTable
                    and sub10EthMgmtQoSPCPMappingTable."
    DEFVAL { 0 }

    ::= { sub10EthMgmtQoS 3 }


sub10EthMgmtQoSQTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtQoSQEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table configures the characteristics of a Quality of
                    Service queue. There are a fixed number of 8 queues in total
                    and therefore 8 entries in this table indexed 1 thru 8.
                    
                    The number of queue entries in the table is given by the value
                    of sub10EthMgmtQoSQNumber."

    ::= { sub10EthMgmtQoS 4 }


sub10EthMgmtQoSQEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtQoSQEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthMgmtQoSQTable."
    INDEX          { sub10EthMgmtQoSQIndex }

    ::= { sub10EthMgmtQoSQTable 1 }

Sub10EthMgmtQoSQEntry ::= SEQUENCE {
    sub10EthMgmtQoSQIndex                             Unsigned32,
    sub10EthMgmtQoSQSchedulingType                    INTEGER,
    sub10EthMgmtQoSQDWRRWeight                        Unsigned32,
    sub10EthMgmtQoSQCongestionPolicy                  INTEGER,
    sub10EthMgmtQoSQSizeMax                           Unsigned32,
    sub10EthMgmtQoSQLen                               Unsigned32
}

sub10EthMgmtQoSQIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..8)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The queue number used as an index, fixed to
                    8 standard queues."

    ::= { sub10EthMgmtQoSQEntry 1 }


sub10EthMgmtQoSQSchedulingType OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     qosSPQ(1),
                     qosDWRR(2)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The scheduling discipline applied to the
                    queue.
                    
                    qosSPQ - Strict Priority Queueing
                    
                    Queues are serviced in strict priority
                    order. Packets on the queue are servicedd
                    and transmitted until either the queue is empty
                    or a packet is found in a higher queue,
                    in the latter case the higher priority queue
                    take precedence.
                    
                    Individual PCP (CoS) bits or DSCP markings in each
                    packet are not observed within the
                    queue and are not therefore transmitted first
                    according to their relative priority. Instead
                    it is possible to map certain VLANs, PCP bits
                    and DSCP marking to certain queues to achieve
                    priority transmission.
                    
                    The mapping of VLAN, PCP bits or DSCP markings
                    to a specific queue are managed using
                    tables :
                    
                    sub10EthMgmtQoSVlanTable
                    sub10EthMgmtQoSPCPTable
                    sub10EthMgmtQoSDSCPTable
                    
                    qosDWRR - Deficit Weighted Round Robin
                    
                    A weighting is allocated to each queue
                    which defines the number of bytes
                    that can be transmitted from that
                    queue in a single visit of the
                    scheduler. This is used to calculate
                    a quantum which is the number of bytes
                    that can always be transmitted in each visit
                    of the scheduler.
                    
                    A deficit counter is also maintained across
                    visits from the scheduler, at each visit
                    the quantum is added to the deficit. This
                    ensures that on average a queue that is not
                    loaded to its intended capacity will utilise
                    the calculated deficit at times when the queue
                    is busy. At startup Deficit counter is initialised
                    to zero.
                    
                    On each visit of the scheduler packets are
                    transmitted according to the following
                    rules...
                    
                    Deficit = Quantum + Deficit.
                    
                    while (Queue not empty and PktLength <= Deficit)
                    Transmit Pkt
                    Deficit = Deficit - PktLength
                    
                    Remaining Deficit is carried forward to the
                    next visit of the scheduler.
                    
                    Effective behaviour of DWRR is as follows:
                    
                    1. Available bandwidth is shared fairly when queues
                    are not saturated
                    2. Lower priority saturated queues will transmit
                    at their allocated service rate.
                    3. Any remaining bandwidth is shared amongst
                    unsaturated queues.
                    
                    NB. In a mixed configuration where some queues are
                    configured as Strict Priority (SP) and some configured
                    as Deficit Weighted Round Robin (DWRR), then queues configured
                    as SP should all be higher in scheduling priority than those
                    configured as DWRR. This is to avoid the lower priority queues
                    having transmission precedence over higher priority queues."
    DEFVAL { qosSPQ }

    ::= { sub10EthMgmtQoSQEntry 2 }


sub10EthMgmtQoSQDWRRWeight OBJECT-TYPE
    SYNTAX         Unsigned32(0..100)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "A percentage weighting factor used to calculate
                    the quantum value used to calculate the starting
                    transmission credit at each visit of the queue
                    scheduler. Only for scheduling type  Deficit
                    Weighted Round Robin.
                    sub10EthMgmtQoSQSchedulingType = 'qosDWRR'
                    
                    The weighting is calculated as the ratio of the
                    transmit rate configured as a percentage. The
                    internal implementation of DWRR will calculate
                    the correct quantum based on total bandwidth
                    available."
    DEFVAL { 100 }

    ::= { sub10EthMgmtQoSQEntry 3 }


sub10EthMgmtQoSQCongestionPolicy OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     qosTailDrop(1)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The policy used when a queue is congested."
    DEFVAL { qosTailDrop }

    ::= { sub10EthMgmtQoSQEntry 4 }


sub10EthMgmtQoSQSizeMax OBJECT-TYPE
    SYNTAX         Unsigned32(0..4294967295)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum size of the queue expressed in packets."
    DEFVAL { 100 }

    ::= { sub10EthMgmtQoSQEntry 5 }


sub10EthMgmtQoSQLen OBJECT-TYPE
    SYNTAX         Unsigned32(0..4294967295)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current length of the queue expressed in packets."

    ::= { sub10EthMgmtQoSQEntry 6 }


sub10EthMgmtQoSVlanMappingNumber OBJECT-TYPE
    SYNTAX         Unsigned32(0..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of sub10EthMgmtQoSVlanMappingTable table entries present
                    on this system."

    ::= { sub10EthMgmtQoS 5 }


sub10EthMgmtQoSVlanTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtQoSVlanEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table configures the Quality of Service
                    Vlan to queue mapping.
                    
                    Multiple Vlans may be mapped to any queue. On egress of the
                    ethernet interface all pkts tagged with the specific Vlan Id
                    are inserted onto the specified queue.
                    
                    See sub10EthMgmtQoSQTable for more information on QoS
                    queueing.
                    
                    The number of entries in the table is given by the value
                    of sub10EthMgmtQoSVlanNumber."

    ::= { sub10EthMgmtQoS 6 }


sub10EthMgmtQoSVlanEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtQoSVlanEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthMgmtQoSVlanMappingTable."
    INDEX          { sub10EthMgmtQoSVlanIndex }

    ::= { sub10EthMgmtQoSVlanTable 1 }

Sub10EthMgmtQoSVlanEntry ::= SEQUENCE {
    sub10EthMgmtQoSVlanIndex                          Unsigned32,
    sub10EthMgmtQoSVlanId                             Sub10VlanId,
    sub10EthMgmtQoSVlanQueue                          Sub10QoSQueue
}

sub10EthMgmtQoSVlanIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..16)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10EthMgmtQoSVlanMappingTable index."

    ::= { sub10EthMgmtQoSVlanEntry 1 }


sub10EthMgmtQoSVlanId OBJECT-TYPE
    SYNTAX         Sub10VlanId
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The vlan id used as an index to the table and which
                    is egressing through the ethernet interface and which
                    should be placed on the queue specified by
                    sub10EthMgmtQoSVlanQueueIndex."
    DEFVAL { 0 }

    ::= { sub10EthMgmtQoSVlanEntry 2 }


sub10EthMgmtQoSVlanQueue OBJECT-TYPE
    SYNTAX         Sub10QoSQueue
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The queue number which identifies the specific queue where the packets
                    that are tagged with the Vlan Id specified in sub10EthMgmtQoSVlanId
                    are inserted."

    ::= { sub10EthMgmtQoSVlanEntry 3 }


sub10EthMgmtQoSPCPTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtQoSPCPEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table configures the Quality of Service Priority
                    Code Point (PCP) or Pbits to queue mapping.
                    
                    Multiple PCPs may be mapped to the same queue but the same
                    PCP cannot be mapped to more than one queue. This table
                    has a maximum size of 8 entries with exactly 1 entry per
                    PCP bit.
                    
                    On egress of the ethernet interface all pkts tagged with the
                    specific PCP bit (irrespective of its Vlan Id) is inserted
                    on the specified queue.
                    
                    See sub10EthMgmtQoSQTable for more information on QoS
                    queueing.
                    
                    The number of entries in the table is given by the value
                    of sub10EthMgmtQoSPCPMappingNumber (maximum 8)."

    ::= { sub10EthMgmtQoS 8 }


sub10EthMgmtQoSPCPEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtQoSPCPEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthMgmtQoSPCPMappingTable."
    INDEX          { sub10EthMgmtQoSPCPIndex }

    ::= { sub10EthMgmtQoSPCPTable 1 }

Sub10EthMgmtQoSPCPEntry ::= SEQUENCE {
    sub10EthMgmtQoSPCPIndex                           Unsigned32,
    sub10EthMgmtQoSPCPQueue                           Sub10QoSQueue
}

sub10EthMgmtQoSPCPIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..8)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10EthMgmtQoSPCPTable index.
                    This represents the PCP bits priority
                    value.
                    
                    sub10EthMgmtQoSPCPIndex(1)=PCPBit(0)
                    sub10EthMgmtQoSPCPIndex(2)=PCPBit(1)
                    sub10EthMgmtQoSPCPIndex(3)=PCPBit(2)
                    sub10EthMgmtQoSPCPIndex(4)=PCPBit(3)
                    sub10EthMgmtQoSPCPIndex(5)=PCPBit(4)
                    sub10EthMgmtQoSPCPIndex(6)=PCPBit(5)
                    sub10EthMgmtQoSPCPIndex(7)=PCPBit(6)
                    sub10EthMgmtQoSPCPIndex(8)=PCPBit(7)"

    ::= { sub10EthMgmtQoSPCPEntry 1 }


sub10EthMgmtQoSPCPQueue OBJECT-TYPE
    SYNTAX         Sub10QoSQueue
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The queue number in the sub10EthMgmtQoSQTable
                    which identifies the specific queue where the packets
                    tagged with the specific PCP bit sub10EthMgmtQoSPCPIndex
                    are inserted. There are up to 8 queues."

    ::= { sub10EthMgmtQoSPCPEntry 2 }


sub10EthMgmtQoSDSCPMappingNumber OBJECT-TYPE
    SYNTAX         Unsigned32(0..64)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of sub10EthMgmtQoSDSCPMappingTable table entries present
                    on this system."

    ::= { sub10EthMgmtQoS 9 }


sub10EthMgmtQoSDSCPTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtQoSDSCPEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table configures the Quality of Service Differentiated
                    Services Code Point (DSCP) to queue mapping.
                    
                    Multiple DSCPs may be mapped to the same queue but the same
                    DSCP cannot be mapped to more than one queue. This table has
                    a maximum size of 64 with 1 entry per DSCP value.
                    
                    On egress of the ethernet interface all pkts tagged with the
                    specific DSCP marking in the IP header will be inserted
                    onto the specified queue.
                    
                    See sub10EthMgmtQoSQTable for more information on QoS
                    queueing.
                    
                    The number of entries in the table is given by the value
                    of sub10EthMgmtQoSDSCPMappingNumber (maximum 64)."

    ::= { sub10EthMgmtQoS 10 }


sub10EthMgmtQoSDSCPEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtQoSDSCPEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthMgmtQoSDSCPMappingTable."
    INDEX          { sub10EthMgmtQoSDSCPIndex }

    ::= { sub10EthMgmtQoSDSCPTable 1 }

Sub10EthMgmtQoSDSCPEntry ::= SEQUENCE {
    sub10EthMgmtQoSDSCPIndex                          Unsigned32,
    sub10EthMgmtQoSDSCPMarking                        Unsigned32,
    sub10EthMgmtQoSDSCPQueue                          Sub10QoSQueue
}

sub10EthMgmtQoSDSCPIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..64)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10EthMgmtQoSDSCPTable index."

    ::= { sub10EthMgmtQoSDSCPEntry 1 }


sub10EthMgmtQoSDSCPMarking OBJECT-TYPE
    SYNTAX         Unsigned32(0..63)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The DSCP value used as an index to the table. This defines
                    the DSCP value which when egressing through the ethernet
                    interface is placed on the queue specified by
                    sub10EthMgmtQoSVlanQueueIndex."

    ::= { sub10EthMgmtQoSDSCPEntry 2 }


sub10EthMgmtQoSDSCPQueue OBJECT-TYPE
    SYNTAX         Sub10QoSQueue
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The queue number in the sub10EthMgmtQoSQTable
                    which identifies the specific queue where the packets
                    tagged with the specific DSCP marking sub10EthMgmtQoSDSCP
                    are inserted."

    ::= { sub10EthMgmtQoSDSCPEntry 3 }


sub10EthMgmtQoSMPLSMappingNumber OBJECT-TYPE
    SYNTAX         Unsigned32(0..64)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of sub10EthMgmtQoSMPLSMappingTable table entries present
                    on this system.This is equivelent to the number of MPLS Traffic
                    classes"

    ::= { sub10EthMgmtQoS 11 }


sub10EthMgmtQoSMPLSTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtQoSMPLSEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table configures the Quality of Service Multi-Protocol
                    Label Switching protocol (MPLS) Traffic Class (TC) to queue mapping.
                    
                    Multiple MPLS TCs may be mapped to the same queue but the same
                    MPLS TC cannot be mapped to more than one queue. This table has
                    a maximum size of 64 with 1 entry per MPLS TC value.
                    
                    On egress of the ethernet interface all pkts tagged with the
                    specific MPLS TC in the IP header will be inserted
                    onto the specified queue.
                    
                    See sub10EthMgmtQoSQTable for more information on QoS
                    queueing.
                    
                    The number of entries in the table is given by the value
                    of sub10EthMgmtQoSMPLSMappingNumber (maximum 8)."

    ::= { sub10EthMgmtQoS 12 }


sub10EthMgmtQoSMPLSEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtQoSMPLSEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthMgmtQoSMPLSMappingTable."
    INDEX          { sub10EthMgmtQoSMPLSIndex }

    ::= { sub10EthMgmtQoSMPLSTable 1 }

Sub10EthMgmtQoSMPLSEntry ::= SEQUENCE {
    sub10EthMgmtQoSMPLSIndex                          Unsigned32,
    sub10EthMgmtQoSMPLSTrafficClass                   Unsigned32,
    sub10EthMgmtQoSMPLSQueue                          Sub10QoSQueue
}

sub10EthMgmtQoSMPLSIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..8)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The sub10EthMgmtQoSMPLSMappingTable index."

    ::= { sub10EthMgmtQoSMPLSEntry 1 }


sub10EthMgmtQoSMPLSTrafficClass OBJECT-TYPE
    SYNTAX         Unsigned32(0..7)
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The MPLS Traffic Class value used as an index to the table.
                    This defines the MPLS TC value which when egressing through
                    the ethernet interface is placed on the queue specified by
                    sub10EthMgmtQoSVlanQueueIndex."

    ::= { sub10EthMgmtQoSMPLSEntry 2 }


sub10EthMgmtQoSMPLSQueue OBJECT-TYPE
    SYNTAX         Sub10QoSQueue
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The queue number in the sub10EthMgmtQoSQTable
                    which identifies the specific queue where the packets
                    tagged with the specific MPLS Traffic Class
                    sub10EthMgmtQoSMPLSTrafficClass are inserted."

    ::= { sub10EthMgmtQoSMPLSEntry 3 }


-- ****************************************************************************
-- .sub10EthernetMgmt.sub10EthMgmtStats Objects
-- ****************************************************************************

sub10EthMgmtStatsActiveTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthMgmtStatsActiveEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table maintains an entry for each Ethernet
                    statistic that is enabled and active."

    ::= { sub10EthMgmtStats 1 }


sub10EthMgmtStatsActiveEntry OBJECT-TYPE
    SYNTAX         Sub10EthMgmtStatsActiveEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry in the sub10EthMgmtStatsActiveTable.
                    Each entry represents an object instance of ethernet
                    stats and it's current state."
    INDEX          { sub10EthMgmtStatsActiveIndex }

    ::= { sub10EthMgmtStatsActiveTable 1 }

Sub10EthMgmtStatsActiveEntry ::= SEQUENCE {
    sub10EthMgmtStatsActiveIndex                      Unsigned32,
    sub10EthMgmtStatsActiveName                       OCTET STRING,
    sub10EthMgmtStatsActiveState                      Sub10State
}

sub10EthMgmtStatsActiveIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..30)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The number of ethernet stats objects
                    used as an index to this table."

    ::= { sub10EthMgmtStatsActiveEntry 1 }


sub10EthMgmtStatsActiveName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The stats object name. This is the name of the
                    ethernet stats object in the sub10EthernetStatsCurrent
                    group."

    ::= { sub10EthMgmtStatsActiveEntry 2 }


sub10EthMgmtStatsActiveState OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the statistic is active."
    DEFVAL { stateEnabled }

    ::= { sub10EthMgmtStatsActiveEntry 3 }


-- ****************************************************************************
-- .sub10Ethernet.sub10EthernetStats Objects
-- ****************************************************************************

sub10EthStatsTimeElapsed OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time that has elapsed from the beginning
                    of the statistical measurement period.
                    
                    This is normally time from just after the last reboot time
                    when the statistics module begins it's calculations.
                    
                    If, for some reason, such as an adjustment in the
                    system's time-of-day clock, the current interval
                    exceeds the maximum value, the agent will return
                    the maximum value."

    ::= { sub10EthernetStats 1 }


-- ****************************************************************************
-- .sub10EthernetStats.sub10EthernetStatsCurrent Objects
-- ****************************************************************************

sub10EthernetStatsCurrTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthernetStatsCurrEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table defines the current stats objects of the ethernet interface."

    ::= { sub10EthernetStatsCurrent 1 }


sub10EthernetStatsCurrEntry OBJECT-TYPE
    SYNTAX         Sub10EthernetStatsCurrEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry (row) in the sub10EthernetStatsCurrTable."
    INDEX          { sub10EthIfIndex }

    ::= { sub10EthernetStatsCurrTable 1 }

Sub10EthernetStatsCurrEntry ::= SEQUENCE {
    sub10EthStatsCurrRxOctets                         Counter32,
    sub10EthStatsCurrRxGoodFrms                       Counter32,
    sub10EthStatsCurrRxBcastFrms                      Counter32,
    sub10EthStatsCurrRxMcastFrms                      Counter32,
    sub10EthStatsCurrRxPauseFrms                      Counter32,
    sub10EthStatsCurrRxCRCErrs                        Counter32,
    sub10EthStatsCurrRxAlignErrs                      Counter32,
    sub10EthStatsCurrRxOversized                      Counter32,
    sub10EthStatsCurrRxJabberFrms                     Counter32,
    sub10EthStatsCurrRxUndersized                     Counter32,
    sub10EthStatsCurrRxFragments                      Counter32,
    sub10EthStatsCurrRxSOFOvrns                       Counter32,
    sub10EthStatsCurrTxOctets                         Counter32,
    sub10EthStatsCurrTxGoodFrms                       Counter32,
    sub10EthStatsCurrTxBcastFrms                      Counter32,
    sub10EthStatsCurrTxMcastFrms                      Counter32,
    sub10EthStatsCurrTxPauseFrms                      Counter32,
    sub10EthStatsCurrTxDeferred                       Counter32,
    sub10EthStatsCurrTxCollsn                         Counter32,
    sub10EthStatsCurrTxSnglCollsn                     Counter32,
    sub10EthStatsCurrTxMlplCollsn                     Counter32,
    sub10EthStatsCurrTxExsvCollsn                     Counter32,
    sub10EthStatsCurrTxLtCollsn                       Counter32,
    sub10EthStatsCurrTxCSenseErrs                     Counter32,
    sub10EthStatsCurrPkts64Octets                     Counter32,
    sub10EthStatsCurrPkts65T127                       Counter32,
    sub10EthStatsCurrPkts128T255                      Counter32,
    sub10EthStatsCurrPkts256T511                      Counter32,
    sub10EthStatsCurrPkts512T1023                     Counter32,
    sub10EthStatsCurrPkts1024TMax                     Counter32,
    sub10EthStatsCurrRxMbps                           Sub10ThroughputMbps,
    sub10EthStatsCurrTxMbps                           Sub10ThroughputMbps,
    sub10EthStatsCurrRxMbpsMin                        Sub10ThroughputMbps,
    sub10EthStatsCurrRxMbpsMax                        Sub10ThroughputMbps,
    sub10EthStatsCurrRxMbpsAvg                        Sub10ThroughputMbps,
    sub10EthStatsCurrTxMbpsMin                        Sub10ThroughputMbps,
    sub10EthStatsCurrTxMbpsMax                        Sub10ThroughputMbps,
    sub10EthStatsCurrTxMbpsAvg                        Sub10ThroughputMbps,
    sub10EthStatsCurrRmtRxMbpsAvg                     Sub10ThroughputMbps,
    sub10EthStatsCurrRmtTxMbpsAvg                     Sub10ThroughputMbps
}

sub10EthStatsCurrRxOctets OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of octets received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 1 }


sub10EthStatsCurrRxGoodFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of good frames received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 2 }


sub10EthStatsCurrRxBcastFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of broadcast frames received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 3 }


sub10EthStatsCurrRxMcastFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of multicast frames received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 4 }


sub10EthStatsCurrRxPauseFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Pause frames received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 5 }


sub10EthStatsCurrRxCRCErrs OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of CRC errors received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 6 }


sub10EthStatsCurrRxAlignErrs OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Alignment coding errors received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 7 }


sub10EthStatsCurrRxOversized OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Oversized frames received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 8 }


sub10EthStatsCurrRxJabberFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Jabber frames received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 9 }


sub10EthStatsCurrRxUndersized OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Undersized frames received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 10 }


sub10EthStatsCurrRxFragments OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of frame fragments received over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 11 }


sub10EthStatsCurrRxSOFOvrns OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of frames with Start of Frame (SOF) Overruns
                    received over the Ethernet interface or were dropped due to
                    FIFO resource limitations since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 12 }


sub10EthStatsCurrTxOctets OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of octets transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 13 }


sub10EthStatsCurrTxGoodFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of ethernet frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 14 }


sub10EthStatsCurrTxBcastFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of broadcast frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 15 }


sub10EthStatsCurrTxMcastFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of multicast frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 16 }


sub10EthStatsCurrTxPauseFrms OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Pause frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 17 }


sub10EthStatsCurrTxDeferred OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Deferred frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 18 }


sub10EthStatsCurrTxCollsn OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Collision frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 19 }


sub10EthStatsCurrTxSnglCollsn OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Single Collision frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 20 }


sub10EthStatsCurrTxMlplCollsn OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Multiple Collision frames transmitted over the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 21 }


sub10EthStatsCurrTxExsvCollsn OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Excessive Collision for which transmission was
                    abandoned over the Ethernet interface since the last power
                    cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 22 }


sub10EthStatsCurrTxLtCollsn OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Late Collision for which transmission was
                    abandoned over the Ethernet interface since the last power
                    cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 23 }


sub10EthStatsCurrTxCSenseErrs OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Carrier Sense Errored frames transmitted over the
                    Ethernet interface since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 24 }


sub10EthStatsCurrPkts64Octets OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of 64-byte frames received and transmitted on the
                    Ethernet interface since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 25 }


sub10EthStatsCurrPkts65T127 OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 65 to 127 bytes received
                    and transmitted on the Ethernet interface since the last
                    power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 26 }


sub10EthStatsCurrPkts128T255 OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 128 to 255 bytes received
                    and transmitted on the Ethernet interface since the last
                    power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 27 }


sub10EthStatsCurrPkts256T511 OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 256 to 511 bytes received
                    and transmitted on the Ethernet interface since the last power
                    cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 28 }


sub10EthStatsCurrPkts512T1023 OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 512 to 1023 bytes received
                    and transmitted on the Ethernet interface since the last power
                    cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 29 }


sub10EthStatsCurrPkts1024TMax OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size  1024 to RX_MAXLEN bytes for
                    receive or 1024 up for transmit on the Ethernet interface
                    since the last power cycle / reset of the unit.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthernetStatsCurrEntry 30 }


sub10EthStatsCurrRxMbps OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The Rx throughput in Mbps in this second."

    ::= { sub10EthernetStatsCurrEntry 31 }


sub10EthStatsCurrTxMbps OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The Tx throughput in Mbps in this second."

    ::= { sub10EthernetStatsCurrEntry 32 }


sub10EthStatsCurrRxMbpsMin OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum Rx throughput in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 33 }


sub10EthStatsCurrRxMbpsMax OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum Rx throughput in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 34 }


sub10EthStatsCurrRxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Rx throughput in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 35 }


sub10EthStatsCurrTxMbpsMin OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum Tx throughput in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 36 }


sub10EthStatsCurrTxMbpsMax OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum Tx throughput in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 37 }


sub10EthStatsCurrTxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Tx throughput in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 38 }


sub10EthStatsCurrRmtRxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Rx throughput of the remote unit in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 39 }


sub10EthStatsCurrRmtTxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Tx throughput of the remote unit in Mbps in the last rolling minute."

    ::= { sub10EthernetStatsCurrEntry 40 }


-- ****************************************************************************
-- .sub10EthernetStatsHistory.sub10EthernetStats15mHistory Objects
-- ****************************************************************************

sub10EthStats15mHistIntvls OBJECT-TYPE
    SYNTAX         Unsigned32(0..96)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of valid table entries in sub10EthStats15mHistTable
                    on this system. There is one entry per 15 minute interval.
                    If the unit has been running for a minimum of 24 hours
                    and collecting valid data then the number of entries will be 96,
                    this represents a total maximum of 24 hours worth of data."

    ::= { sub10EthernetStats15mHistory 1 }


sub10EthStats15mHistTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthStats15mHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table maintains a history of Ethernet statistics
                    based on 15 minute intervals. There are a maximum
                    of 96 intervals which represent 24 hours of data.
                    
                    This table along with the 1m and 1d tables provide a
                    history of the performance of the radio interface. This
                    information may then be post processed and used as a
                    troubleshooting tool.
                    
                    An entry in the table represents a 15 minute interval
                    where each interval is synchronised to the clock on the
                    hour. There are up to 96 intervals starting at 1. The
                    number of valid entries in the table is given by the value
                    of sub10EthStats15mHistIntvls of which the minimum
                    is 0 and the maximum is 96 therefore providing
                    up to a maximum of 24 hours of 15 minute interval data.
                    
                    The first entry indexed by 1 represents the most recent
                    completed 15 minute interval. At the end of each interval
                    all entries of 'index' are copied into entry 'index+1'. If the
                    number of valid intervals is 96 then the least recent interval
                    entry is summarised into 24 hour stats and then discarded."

    ::= { sub10EthernetStats15mHistory 2 }


sub10EthStats15mHistEntry OBJECT-TYPE
    SYNTAX         Sub10EthStats15mHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry in the sub10EthStats15mHistTable.
                    Each entry represents 15 minute of performance
                    management data where the first entry with index=1
                    is the most recent 15 minute interval and the last
                    entry with index=n (up to 96) is the least recent
                    15 minute interval. The number of valid intervals
                    is sub10EthStats15mHistIntvls."
    INDEX          { sub10EthIfIndex, sub10EthStats15mHistIntvl }

    ::= { sub10EthStats15mHistTable 1 }

Sub10EthStats15mHistEntry ::= SEQUENCE {
    sub10EthStats15mHistIntvl                         Unsigned32,
    sub10EthStats15mHistTime                          Sub10DateTime,
    sub10EthStats15mHistRxOctets                      Counter64,
    sub10EthStats15mHistRxGoodFrms                    Counter64,
    sub10EthStats15mHistRxBcastFrms                   Counter64,
    sub10EthStats15mHistRxMcastFrms                   Counter64,
    sub10EthStats15mHistRxPauseFrms                   Counter64,
    sub10EthStats15mHistRxCRCErrs                     Counter64,
    sub10EthStats15mHistRxAlignErrs                   Counter64,
    sub10EthStats15mHistRxOversized                   Counter64,
    sub10EthStats15mHistRxJabberFrms                  Counter64,
    sub10EthStats15mHistRxUndersized                  Counter64,
    sub10EthStats15mHistRxFragments                   Counter64,
    sub10EthStats15mHistRxSOFOvrns                    Counter64,
    sub10EthStats15mHistTxOctets                      Counter64,
    sub10EthStats15mHistTxGoodFrms                    Counter64,
    sub10EthStats15mHistTxBcastFrms                   Counter64,
    sub10EthStats15mHistTxMcastFrms                   Counter64,
    sub10EthStats15mHistTxPauseFrms                   Counter64,
    sub10EthStats15mHistTxDeferred                    Counter64,
    sub10EthStats15mHistTxCollsn                      Counter64,
    sub10EthStats15mHistTxSnglCollsn                  Counter64,
    sub10EthStats15mHistTxMlplCollsn                  Counter64,
    sub10EthStats15mHistTxExsvCollsn                  Counter64,
    sub10EthStats15mHistTxLtCollsn                    Counter64,
    sub10EthStats15mHistTxCSenseErrs                  Counter64,
    sub10EthStats15mHistPkts64Octets                  Counter64,
    sub10EthStats15mHistPkts65T127                    Counter64,
    sub10EthStats15mHistPkts128T255                   Counter64,
    sub10EthStats15mHistPkts256T511                   Counter64,
    sub10EthStats15mHistPkts512T1023                  Counter64,
    sub10EthStats15mHistPkts1024TMax                  Counter64,
    sub10EthStats15mHistRxMbpsMin                     Sub10ThroughputMbps,
    sub10EthStats15mHistRxMbpsMax                     Sub10ThroughputMbps,
    sub10EthStats15mHistRxMbpsAvg                     Sub10ThroughputMbps,
    sub10EthStats15mHistTxMbpsMin                     Sub10ThroughputMbps,
    sub10EthStats15mHistTxMbpsMax                     Sub10ThroughputMbps,
    sub10EthStats15mHistTxMbpsAvg                     Sub10ThroughputMbps
}

sub10EthStats15mHistIntvl OBJECT-TYPE
    SYNTAX         Unsigned32(1..96)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The interval number used as an index to this
                    table. Interval numbers start at 1 (the most
                    recent 15 minute interval) and have a maximum value of 96
                    (the least recent 15 minute interval)."

    ::= { sub10EthStats15mHistEntry 1 }


sub10EthStats15mHistTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local system time when this 15m history entry was created.
                    This represents the end of the 15 minute interval for which the
                    associated stats with the same sub10EthStats15mHistIntvl
                    table index are relevant."

    ::= { sub10EthStats15mHistEntry 2 }


sub10EthStats15mHistRxOctets OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of octets received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 3 }


sub10EthStats15mHistRxGoodFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of good frames received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 4 }


sub10EthStats15mHistRxBcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of broadcast frames received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 5 }


sub10EthStats15mHistRxMcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of multicast frames received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 6 }


sub10EthStats15mHistRxPauseFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Pause frames received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 7 }


sub10EthStats15mHistRxCRCErrs OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of CRC errors received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 8 }


sub10EthStats15mHistRxAlignErrs OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Alignment coding errors received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 9 }


sub10EthStats15mHistRxOversized OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Oversized frames received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 10 }


sub10EthStats15mHistRxJabberFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Jabber frames received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 11 }


sub10EthStats15mHistRxUndersized OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Undersized frames received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 12 }


sub10EthStats15mHistRxFragments OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of frame fragments received over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 13 }


sub10EthStats15mHistRxSOFOvrns OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of frames with Start of Frame (SOF) Overruns
                    received over the Ethernet interface or were dropped due to
                    FIFO resource limitations in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 14 }


sub10EthStats15mHistTxOctets OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of octets transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 15 }


sub10EthStats15mHistTxGoodFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of ethernet frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 16 }


sub10EthStats15mHistTxBcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of broadcast frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 17 }


sub10EthStats15mHistTxMcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of multicast frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 18 }


sub10EthStats15mHistTxPauseFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Pause frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 19 }


sub10EthStats15mHistTxDeferred OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Deferred frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 20 }


sub10EthStats15mHistTxCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Collision frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 21 }


sub10EthStats15mHistTxSnglCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Single Collision frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 22 }


sub10EthStats15mHistTxMlplCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Multiple Collision frames transmitted over the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 23 }


sub10EthStats15mHistTxExsvCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Excessive Collision for which transmission was
                    abandoned over the Ethernet interface in this 1 minute interval.
                    5
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 24 }


sub10EthStats15mHistTxLtCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Late Collision for which transmission was
                    abandoned over the Ethernet interface in this 15 minute interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 25 }


sub10EthStats15mHistTxCSenseErrs OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Carrier Sense Errored frames transmitted over the
                    Ethernet interface in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 26 }


sub10EthStats15mHistPkts64Octets OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of 64-byte frames received and transmitted on the
                    Ethernet interface in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 27 }


sub10EthStats15mHistPkts65T127 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 65 to 127 bytes received
                    and transmitted on the Ethernet interface in this 15 minute interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 28 }


sub10EthStats15mHistPkts128T255 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 128 to 255 bytes received
                    and transmitted on the Ethernet interface in this 15 minute interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 29 }


sub10EthStats15mHistPkts256T511 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 256 to 511 bytes received
                    and transmitted on the Ethernet interface in this 15 minute interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 30 }


sub10EthStats15mHistPkts512T1023 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 512 to 1023 bytes received
                    and transmitted on the Ethernet interface in this 15 minute interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 31 }


sub10EthStats15mHistPkts1024TMax OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size  1024 to RX_MAXLEN bytes for
                    receive or 1024 up for transmit on the Ethernet interface
                    in this 15m interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats15mHistEntry 32 }


sub10EthStats15mHistRxMbpsMin OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum Rx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats15mHistEntry 33 }


sub10EthStats15mHistRxMbpsMax OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum Rx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats15mHistEntry 34 }


sub10EthStats15mHistRxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Rx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats15mHistEntry 35 }


sub10EthStats15mHistTxMbpsMin OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum Tx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats15mHistEntry 36 }


sub10EthStats15mHistTxMbpsMax OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum Tx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats15mHistEntry 37 }


sub10EthStats15mHistTxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Tx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats15mHistEntry 38 }


-- ****************************************************************************
-- .sub10EthernetStatsHistory.sub10EthStats1dHistory Objects
-- ****************************************************************************

sub10EthStats1dHistIntvls OBJECT-TYPE
    SYNTAX         Unsigned32(0..30)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of valid table entries in sub10EthStats1dHistTable
                    on this system. There is one entry per 1 day interval.
                    
                    If the unit has been running for a minimum  of 30 days
                    and collecting valid data then the number of entries will be 30,
                    this represents a total maximum of 30 days worth of data."

    ::= { sub10EthStats1dHistory 1 }


sub10EthStats1dHistTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10EthStats1dHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table maintains a history of Radio statistics
                    based on 1 day intervals. There are a maximum
                    of 30 intervals which represent 30 days of data.
                    
                    This table along with the 1m and 15m tables provide a
                    history of the performance of the radio interface. This
                    information may then be post processed and used as a
                    troubleshooting tool.
                    
                    An entry in the table represents a 1 day interval
                    where each interval is synchronised to the clock on the
                    hour. There are up to 30 intervals starting at 1. The
                    number of valid entries in the table is given by the value
                    of sub10EthStats1dHistIntvls of which the minimum
                    is 0 and the maximum is 30 therefore providing
                    up to a maximum of 30 days of 1 day interval data.
                    
                    The first entry indexed by 1 represents the most recent
                    completed 1 day interval. At the end of each interval
                    all entries of 'index' are copied into entry 'index+1'. If the
                    number of valid intervals is 30 then the least recent interval
                    entry is discarded."

    ::= { sub10EthStats1dHistory 2 }


sub10EthStats1dHistEntry OBJECT-TYPE
    SYNTAX         Sub10EthStats1dHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry in the sub10EthStats1dHistTable.
                    Each entry represents 1 day of performance
                    management data where the first entry with index=1
                    is the most recent 1 day interval and the last
                    entry with index=n (up to 30) is the least recent
                    1 day interval.
                    
                    The number of valid intervals is given by the
                    value of sub10EthStats1dHistIntvls."
    INDEX          { sub10EthIfIndex, sub10EthStats1dHistIntvl }

    ::= { sub10EthStats1dHistTable 1 }

Sub10EthStats1dHistEntry ::= SEQUENCE {
    sub10EthStats1dHistIntvl                          Unsigned32,
    sub10EthStats1dHistTime                           Sub10DateTime,
    sub10EthStats1dHistRxOctets                       Counter64,
    sub10EthStats1dHistRxGoodFrms                     Counter64,
    sub10EthStats1dHistRxBcastFrms                    Counter64,
    sub10EthStats1dHistRxMcastFrms                    Counter64,
    sub10EthStats1dHistRxPauseFrms                    Counter64,
    sub10EthStats1dHistRxCRCErrs                      Counter64,
    sub10EthStats1dHistRxAlignErrs                    Counter64,
    sub10EthStats1dHistRxOversized                    Counter64,
    sub10EthStats1dHistRxJabberFrms                   Counter64,
    sub10EthStats1dHistRxUndersized                   Counter64,
    sub10EthStats1dHistRxFragments                    Counter64,
    sub10EthStats1dHistRxSOFOvrns                     Counter64,
    sub10EthStats1dHistTxOctets                       Counter64,
    sub10EthStats1dHistTxGoodFrms                     Counter64,
    sub10EthStats1dHistTxBcastFrms                    Counter64,
    sub10EthStats1dHistTxMcastFrms                    Counter64,
    sub10EthStats1dHistTxPauseFrms                    Counter64,
    sub10EthStats1dHistTxDeferred                     Counter64,
    sub10EthStats1dHistTxCollsn                       Counter64,
    sub10EthStats1dHistTxSnglCollsn                   Counter64,
    sub10EthStats1dHistTxMlplCollsn                   Counter64,
    sub10EthStats1dHistTxExsvCollsn                   Counter64,
    sub10EthStats1dHistTxLtCollsn                     Counter64,
    sub10EthStats1dHistTxCSenseErrs                   Counter64,
    sub10EthStats1dHistPkts64Octets                   Counter64,
    sub10EthStats1dHistPkts65T127                     Counter64,
    sub10EthStats1dHistPkts128T255                    Counter64,
    sub10EthStats1dHistPkts256T511                    Counter64,
    sub10EthStats1dHistPkts512T1023                   Counter64,
    sub10EthStats1dHistPkts1024TMax                   Counter64,
    sub10EthStats1dHistRxMbpsMin                      Sub10ThroughputMbps,
    sub10EthStats1dHistRxMbpsMax                      Sub10ThroughputMbps,
    sub10EthStats1dHistRxMbpsAvg                      Sub10ThroughputMbps,
    sub10EthStats1dHistTxMbpsMin                      Sub10ThroughputMbps,
    sub10EthStats1dHistTxMbpsMax                      Sub10ThroughputMbps,
    sub10EthStats1dHistTxMbpsAvg                      Sub10ThroughputMbps
}

sub10EthStats1dHistIntvl OBJECT-TYPE
    SYNTAX         Unsigned32(1..30)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The interval number used as an index to this
                    table. Interval numbers start at 1 (the most
                    recent 1 day interval) and have a maximum value of 30
                    (the least recent 1 day interval)."

    ::= { sub10EthStats1dHistEntry 1 }


sub10EthStats1dHistTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local system time when this 1d history entry was created.
                    This represents the end of the 1 day interval for which the
                    associated stats with the same sub10EthStats1dHistIntvl
                    table index are relevant."

    ::= { sub10EthStats1dHistEntry 2 }


sub10EthStats1dHistRxOctets OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of octets received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 3 }


sub10EthStats1dHistRxGoodFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of good frames received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 4 }


sub10EthStats1dHistRxBcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of broadcast frames received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 5 }


sub10EthStats1dHistRxMcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of multicast frames received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 6 }


sub10EthStats1dHistRxPauseFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Pause frames received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 7 }


sub10EthStats1dHistRxCRCErrs OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of CRC errors received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 8 }


sub10EthStats1dHistRxAlignErrs OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Alignment coding errors received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 9 }


sub10EthStats1dHistRxOversized OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Oversized frames received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 10 }


sub10EthStats1dHistRxJabberFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Jabber frames received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 11 }


sub10EthStats1dHistRxUndersized OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Undersized frames received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 12 }


sub10EthStats1dHistRxFragments OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of frame fragments received over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 13 }


sub10EthStats1dHistRxSOFOvrns OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of frames with Start of Frame (SOF) Overruns
                    received over the Ethernet interface or were dropped due to
                    FIFO resource limitations in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 14 }


sub10EthStats1dHistTxOctets OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of octets transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 15 }


sub10EthStats1dHistTxGoodFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of ethernet frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 16 }


sub10EthStats1dHistTxBcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of broadcast frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 17 }


sub10EthStats1dHistTxMcastFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of multicast frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 18 }


sub10EthStats1dHistTxPauseFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Pause frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 19 }


sub10EthStats1dHistTxDeferred OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Deferred frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 20 }


sub10EthStats1dHistTxCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Collision frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 21 }


sub10EthStats1dHistTxSnglCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Single Collision frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 22 }


sub10EthStats1dHistTxMlplCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Multiple Collision frames transmitted over the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 23 }


sub10EthStats1dHistTxExsvCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Excessive Collision for which transmission was
                    abandoned over the Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 24 }


sub10EthStats1dHistTxLtCollsn OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Late Collision for which transmission was
                    abandoned over the Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 25 }


sub10EthStats1dHistTxCSenseErrs OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Carrier Sense Errored frames transmitted over the
                    Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 26 }


sub10EthStats1dHistPkts64Octets OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of 64-byte frames received and transmitted on the
                    Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 27 }


sub10EthStats1dHistPkts65T127 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 65 to 127 bytes received
                    and transmitted on the Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 28 }


sub10EthStats1dHistPkts128T255 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 128 to 255 bytes received
                    and transmitted on the Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 29 }


sub10EthStats1dHistPkts256T511 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 256 to 511 bytes received
                    and transmitted on the Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 30 }


sub10EthStats1dHistPkts512T1023 OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size 512 to 1023 bytes received
                    and transmitted on the Ethernet interface in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 31 }


sub10EthStats1dHistPkts1024TMax OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The total number of frames of size  1024 to RX_MAXLEN bytes for
                    receive or 1024 up for transmit on the Ethernet interface
                    in this 1 day interval.
                    
                    For more information refer to online techical documentation at
                    http://www.ti.com. Product c667xDSP 'Gigabit Ethernet
                    Switch Subsystem for KeyStone Devices User's Guide'."

    ::= { sub10EthStats1dHistEntry 32 }


sub10EthStats1dHistRxMbpsMin OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum Rx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats1dHistEntry 33 }


sub10EthStats1dHistRxMbpsMax OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum Rx throughput in Mbps in this 15 minute interval."

    ::= { sub10EthStats1dHistEntry 34 }


sub10EthStats1dHistRxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Rx throughput in Mbps in this 1 day interval."

    ::= { sub10EthStats1dHistEntry 35 }


sub10EthStats1dHistTxMbpsMin OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum Tx throughput in Mbps in this 1 day interval."

    ::= { sub10EthStats1dHistEntry 36 }


sub10EthStats1dHistTxMbpsMax OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum Tx throughput in Mbps in this 1 day interval."

    ::= { sub10EthStats1dHistEntry 37 }


sub10EthStats1dHistTxMbpsAvg OBJECT-TYPE
    SYNTAX         Sub10ThroughputMbps
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average Tx throughput in Mbps in this 1 day interval."

    ::= { sub10EthStats1dHistEntry 38 }


-- ****************************************************************************
-- .sub10RadioStatus.sub10RadioLocalStatus Objects
-- ****************************************************************************

sub10RadioLclLinkStatus OBJECT-TYPE
    SYNTAX         Sub10RadioLinkState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The status of the local radio link."

    ::= { sub10RadioLocalStatus 1 }


sub10RadioLclTxPower OBJECT-TYPE
    SYNTAX         Integer32(-30..30)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current transmit power of the local radio link,
                    measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioLocalStatus 2 }


sub10RadioLclRxPower OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current receive power of the local radio link,
                    measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioLocalStatus 3 }


sub10RadioLclVectErr OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current vector error of the local radio link,
                    measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioLocalStatus 4 }


sub10RadioLclLnkLoss OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current link loss of the local radio link,
                    measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioLocalStatus 5 }


sub10RadioLclAlignmentMode OBJECT-TYPE
    SYNTAX         Sub10AlignmentMode
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "Alignment mode is modeAlignment(1) if the alignment / reset cap
                    has been removed for > 5sec otherwise modeNormal(0). The alignment
                    mode is managed using sub10RadioMgmtAlignmentMode."

    ::= { sub10RadioLocalStatus 6 }


sub10RadioLclDataRate OBJECT-TYPE
    SYNTAX         Sub10RadioDataRate
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum data rate of the Airside interface."

    ::= { sub10RadioLocalStatus 7 }


sub10RadioLclMWUType OBJECT-TYPE
    SYNTAX         Sub10MWUType
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local MWU type"

    ::= { sub10RadioLocalStatus 8 }


sub10RadioLclAFER OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The Air Frame Error Ratio in the last second. This
                    is the % of lost air frames based on the UFEC count in
                    the previous second divided by the
                    expected Air Frame Rate(AFR) in 1 second."

    ::= { sub10RadioLocalStatus 9 }


sub10RadioLclRxModulationMode OBJECT-TYPE
    SYNTAX         Sub10ModulationMode
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current Receive Modulation Mode on the local unit."

    ::= { sub10RadioLocalStatus 10 }


sub10RadioLclTxModulationMode OBJECT-TYPE
    SYNTAX         Sub10ModulationMode
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current Transmit Modulation Mode on the local unit."

    ::= { sub10RadioLocalStatus 11 }


-- ****************************************************************************
-- .sub10RadioStatus.sub10RadioRemoteStatus Objects
-- ****************************************************************************

sub10RadioRmtLinkStatus OBJECT-TYPE
    SYNTAX         Sub10RadioLinkState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The status of the remote radio link."

    ::= { sub10RadioRemoteStatus 1 }


sub10RadioRmtTxPower OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current transmit power of the remote radio link,
                    measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioRemoteStatus 2 }


sub10RadioRmtRxPower OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current receive power of the remote radio link,
                    measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioRemoteStatus 3 }


sub10RadioRmtVectErr OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current vector error of the remote radio link,
                    measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioRemoteStatus 4 }


sub10RadioRmtLnkLoss OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The current link loss of the remote radio link,
                    measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioRemoteStatus 5 }


sub10RadioRmtAlignmentMode OBJECT-TYPE
    SYNTAX         Sub10AlignmentMode
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The remote radio unit's Alignment mode.
                    Alignment mode is modeAlignment(1) if the alignment / reset cap
                    has been removed for > 5sec otherwise modeNormal(0)."

    ::= { sub10RadioRemoteStatus 7 }


sub10RadioRmtAFER OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The remote unit Air Frame Error Ratio in this second. This
                    is the % of lost air frames based on the UFEC count in
                    the previous second divided by the
                    expected Air Frame Rate(AFR) in 1 second."

    ::= { sub10RadioRemoteStatus 8 }


-- ****************************************************************************
-- .sub10Radio.sub10RadioMgmt Objects
-- ****************************************************************************

sub10RadioMgmtTxPowerLimit OBJECT-TYPE
    SYNTAX         Sub10TxPowerLimit
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The configured transmit power of radio link.
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { txPowerLimitNone }

    ::= { sub10RadioMgmt 1 }


sub10RadioMgmtTxRxFreq OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     e250Tx71250Rx81250(0),
                     e250Tx71500Rx81500(1),
                     e250Tx71750Rx81750(2),
                     e250Tx72000Rx82000(3),
                     e250Tx72250Rx82250(4),
                     e250Tx72500Rx82500(5),
                     e250Tx72750Rx82750(6),
                     e250Tx73000Rx83000(7),
                     e250Tx73250Rx83250(8),
                     e250Tx73500Rx83500(9),
                     e250Tx73750Rx83750(10),
                     e250Tx74000Rx84000(11),
                     e250Tx74250Rx84250(12),
                     e250Tx74500Rx84500(13),
                     e250Tx74750Rx84750(14),
                     e250Tx75000Rx85000(15),
                     e250Tx75250Rx85250(16),
                     e250Tx75500Rx85500(17),
                     e250Tx75750Rx85750(18),
                     e500Tx72375Rx82375(19),
                     e500Tx72625Rx82625(20),
                     e500Tx72875Rx82875(21),
                     e500Tx73125Rx83125(22),
                     e500Tx73375Rx83375(23),
                     e500Tx73625Rx83625(24),
                     e500Tx73875Rx83875(25),
                     e500Tx74125Rx84125(26),
                     e500Tx74375Rx84375(27),
                     e500Tx74625Rx84625(28),
                     v500Tx58500Rx61500(29),
                     v500Tx58500Rx62000(30),
                     v500Tx58500Rx62500(31),
                     v500Tx59000Rx61500(32),
                     v500Tx59000Rx62000(33),
                     v500Tx59000Rx62500(34),
                     v500Tx59500Rx61500(35),
                     v500Tx59500Rx62000(36),
                     v500Tx59500Rx62500(37)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The configured TX/RX frequency pairs of radio link. Each enumerated option
                    represents the TX and RX frequency pair settings in MHz.
                    Options are available for E-Band at channel width 250MHz and 500MHz
                    and E-Band channel width 500MHz.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { v500Tx59500Rx62000 }

    ::= { sub10RadioMgmt 2 }


sub10RadioMgmtAPCMode OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     apcModeDisabled(0),
                     apcModeVariable(1),
                     apcModeFixed(2)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Automatic Transmit Power Control. The initial setting of transmit
                    power used is defined by sub10RadioMgmtTxPower. If sub10RadioMgmtAPCMode
                    is enabled then the transmit power may then vary according to the
                    RSSI measured from the remote end of the link. The actual transmit power is
                    readable in sub10RadioLclTxPower at the local end and sub10RadioRmtTxPower
                    at the remote end of the link.
                    
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { apcModeDisabled }

    ::= { sub10RadioMgmt 3 }


sub10RadioMgmtModulationMode OBJECT-TYPE
    SYNTAX         Sub10ModulationMode
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Set the highest order Modulation Mode attempted by Adaptive Modulation.
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { mode8psk }

    ::= { sub10RadioMgmt 4 }


sub10RadioMgmtAmod OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Adaptive Modulation mode."
    DEFVAL { stateDisabled }

    ::= { sub10RadioMgmt 5 }


sub10RadioMgmtAlignmentMode OBJECT-TYPE
    SYNTAX         Sub10AlignmentMode
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "Alignment mode is modeAlignment(1) if the alignment / reset cap
                    has been removed for > 5sec otherwise modeNormal(0). The alignment
                    mode can be manually changed by setting this object."

    ::= { sub10RadioMgmt 6 }


sub10RadioMgmtMWUChannelWidth OBJECT-TYPE
    SYNTAX         INTEGER
                   {
                     channelWidth250(0),
                     channelWidth500(1)
                   }
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "MWU Channel width.
                    IMPORTANT: This object is controlled by transaction management.
                    Any changes to this object must be transactionally acknowledged
                    following a successful set thus protecting against loss of
                    communication with the unit. To acknowledge the transaction
                    set the object sub10UnitMgmtTransaction to transactionCommit(2)."
    DEFVAL { channelWidth500 }

    ::= { sub10RadioMgmt 8 }


sub10RadioMgmtDataRate OBJECT-TYPE
    SYNTAX         Sub10RadioDataRate
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "The maximum data rate of the Airside interface up to the licensed value."

    ::= { sub10RadioMgmt 10 }


-- ****************************************************************************
-- .sub10RadioMgmt.sub10RadioMgmtStats Objects
-- ****************************************************************************

sub10RadioMgmtStats1dPersist OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'stateEnabled(1) the sub10RadioStats1dHistTable objects
                    are stored and read in on startup. This makes the history
                    non-volatile across unit resets."
    DEFVAL { stateEnabled }

    ::= { sub10RadioMgmtStats 1 }


sub10RadioMgmtStatsActiveTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10RadioMgmtStatsActiveEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table maintains an entry for each Radio
                    statistic that is enabled and active."

    ::= { sub10RadioMgmtStats 4 }


sub10RadioMgmtStatsActiveEntry OBJECT-TYPE
    SYNTAX         Sub10RadioMgmtStatsActiveEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry in the sub10RadioMgmtStatsActiveTable.
                    Each entry represents an object instance of Radio
                    stats and it's current state."
    INDEX          { sub10RadioMgmtStatsActiveIndex }

    ::= { sub10RadioMgmtStatsActiveTable 1 }

Sub10RadioMgmtStatsActiveEntry ::= SEQUENCE {
    sub10RadioMgmtStatsActiveIndex                    Unsigned32,
    sub10RadioMgmtStatsActiveName                     OCTET STRING,
    sub10RadioMgmtStatsActiveState                    Sub10State
}

sub10RadioMgmtStatsActiveIndex OBJECT-TYPE
    SYNTAX         Unsigned32(1..29)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The number of Radio stats objects
                    used as an index to this table."

    ::= { sub10RadioMgmtStatsActiveEntry 1 }


sub10RadioMgmtStatsActiveName OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..22))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The stats object name. This is the name of the
                    ethernet stats object in the sub10RadioStatsCurrent
                    group."

    ::= { sub10RadioMgmtStatsActiveEntry 2 }


sub10RadioMgmtStatsActiveState OBJECT-TYPE
    SYNTAX         Sub10State
    MAX-ACCESS     read-write
    STATUS         current
    DESCRIPTION    "If set to 'enabled' the statistic is active."
    DEFVAL { stateEnabled }

    ::= { sub10RadioMgmtStatsActiveEntry 3 }


-- ****************************************************************************
-- .sub10Radio.sub10RadioStats Objects
-- ****************************************************************************

sub10RadioStatsTimeElapsed OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time that has elapsed from the beginning
                    of the statistical measurement period.
                    
                    This is normally time from just after the last reboot time
                    when the statistics module begins it's calculations.
                    
                    If, for some reason, such as an adjustment in the
                    system's time-of-day clock, the current interval
                    exceeds the maximum value, the agent will return
                    the maximum value."

    ::= { sub10RadioStats 1 }


-- ****************************************************************************
-- .sub10RadioStats.sub10RadioStatsCurrent Objects
-- ****************************************************************************

sub10RadioStatsCurrTxPowerMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the transmit power of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 1 }


sub10RadioStatsCurrTxPowerMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the transmit power of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 2 }


sub10RadioStatsCurrTxPowerAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the transmit power of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 3 }


sub10RadioStatsCurrRxPowerMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the receive power of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 4 }


sub10RadioStatsCurrRxPowerMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the receive power of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 5 }


sub10RadioStatsCurrRxPowerAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the receive power of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 6 }


sub10RadioStatsCurrVectErrMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the vector error of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 7 }


sub10RadioStatsCurrVectErrMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the vector error of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 8 }


sub10RadioStatsCurrVectErrAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the vector error of the local radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 9 }


sub10RadioStatsCurrLnkLossMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the link loss of the local radio
                    link measured over the previous rolling 60 seconds."

    ::= { sub10RadioStatsCurrent 10 }


sub10RadioStatsCurrLnkLossMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the link loss of the local radio
                    link measured over the previous rolling 60 seconds."

    ::= { sub10RadioStatsCurrent 11 }


sub10RadioStatsCurrLnkLossAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the link loss of the local radio
                    link measured over the previous rolling 60 seconds."

    ::= { sub10RadioStatsCurrent 12 }


sub10RadioStatsCurrRxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames received over the Radio interface.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data.
                    
                    Set to zero on unit reset."

    ::= { sub10RadioStatsCurrent 13 }


sub10RadioStatsCurrTxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames transmitted over the Radio interface.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data.
                    
                    Set to zero on unit reset."

    ::= { sub10RadioStatsCurrent 14 }


sub10RadioStatsCurrRxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface.
                    This includes all packets whether destined for the management
                    sub-system on this unit or to be sent direct to the Ethernet interface.
                    
                    Set to zero on unit reset."

    ::= { sub10RadioStatsCurrent 15 }


sub10RadioStatsCurrTxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the Ethernet interface. This does not include
                    packets originating from the management sub-system.
                    
                    Set to zero on unit reset."

    ::= { sub10RadioStatsCurrent 16 }


sub10RadioStatsCurrRxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface
                    and which are destined for the management sub-system on this unit.
                    
                    Set to zero on unit reset."

    ::= { sub10RadioStatsCurrent 17 }


sub10RadioStatsCurrTxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the management sub-system. This does not include
                    packets originating from the Ethernet interface.
                    
                    Set to zero on unit reset."

    ::= { sub10RadioStatsCurrent 18 }


sub10RadioStatsCurrRxBadFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The count of bad frames detected on the
                    receive side of the Radio interface. A bad
                    frame could be caused by any one of the following
                    detected errors:
                    
                    UFEC Error
                    Header CRC Error
                    Frame CRC Error
                    Bad header
                    
                    Set to zero on unit reset."

    ::= { sub10RadioStatsCurrent 19 }


sub10RadioStatsCurrMWUTempMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum MWU temperature measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 24 }


sub10RadioStatsCurrMWUTempMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum MWU temperature measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 25 }


sub10RadioStatsCurrMWUTempAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average MWU temperature measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 26 }


sub10RadioStatsCurrQPSKTo8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from QPSK to 8PSK since unit reboot."

    ::= { sub10RadioStatsCurrent 27 }


sub10RadioStatsCurr8PSKToQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from 8PSK to QPSK since unit reboot."

    ::= { sub10RadioStatsCurrent 28 }


sub10RadioStatsCurrAFERMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum AFER (Air Frame Error Ratio) in the previous rolling 60 seconds.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStatsCurrent 29 }


sub10RadioStatsCurrAFERMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum AFER (Air Frame Error Ratio) in the previous rolling 60 seconds.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStatsCurrent 30 }


sub10RadioStatsCurrAFERAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average AFER (Air Frame Error Ratio) in the previous rolling 60 seconds.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStatsCurrent 31 }


sub10RadioStatsCurrRmtTxPowerAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the transmit power of the remote radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 32 }


sub10RadioStatsCurrRmtRxPowerAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the receive power of the remote radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 33 }


sub10RadioStatsCurrRmtVectErrAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the vector error of the remote radio
                    link measured over the previous rolling 60 seconds."
    DEFVAL { "0" }

    ::= { sub10RadioStatsCurrent 34 }


sub10RadioStatsCurrRmtLnkLossAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value of the link loss of the remote radio
                    link measured over the previous rolling 60 seconds."

    ::= { sub10RadioStatsCurrent 35 }


sub10RadioStatsCurrRmtMWUTempAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average MWU temperature of the remote unit measured over the previous rolling 60 seconds."
    DEFVAL { 0 }

    ::= { sub10RadioStatsCurrent 36 }


sub10RadioStatsCurrRmtAFERAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average AFER (Air Frame Error Ratio) of the remote unit in the previous rolling 60 seconds."

    ::= { sub10RadioStatsCurrent 37 }


sub10RadioStatsCurrRxQPSK OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that Rx modulation mode has been in QPSK since last reboot."

    ::= { sub10RadioStatsCurrent 38 }


sub10RadioStatsCurrRx8PSK OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that Rx modulation mode has been in 8PSK since last reboot."

    ::= { sub10RadioStatsCurrent 39 }


sub10RadioStatsCurrTxQPSK OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that Txmodulation mode has been in QPSK since last reboot."

    ::= { sub10RadioStatsCurrent 40 }


sub10RadioStatsCurrTx8PSK OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that Tx modulation mode has been in 8PSK since last reboot."

    ::= { sub10RadioStatsCurrent 41 }


-- ****************************************************************************
-- .sub10RadioStatsHistory.sub10RadioStats1mHistory Objects
-- ****************************************************************************

sub10RadioStats1mHistIntvls OBJECT-TYPE
    SYNTAX         Unsigned32(0..60)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of valid table entries in sub10RadioStats1mHistTable
                    on this system. There is one entry per 1 minute interval.
                    If the unit has been running for a minimum of 15 minutes
                    and collecting valid data then the number of entries will be 15,
                    this represents a total maximum of 5 minutes worth of data."

    ::= { sub10RadioStats1mHistory 1 }


sub10RadioStats1mHistTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10RadioStats1mHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table maintains a history of Radio statistics
                    based on 1 minute intervals. There are a maximum
                    of 60 intervals which represent 1 hour of data.
                    
                    This table along with the 15m and 1d tables provide a
                    history of the performance of the radio interface. This
                    information may then be post processed and used as a
                    troubleshooting tool.
                    
                    An entry in the table represents a 1 minute interval
                    where each interval is synchronised to the clock on the
                    hour. There are up to 60 intervals starting at 1. The
                    number of valid entries in the table is given by the value
                    of sub10RadioStats1mHistIntvls of which the minimum
                    is 0 and the maximum is 60 therefore providing
                    up to a maximum of 60 minutes of 1 minute interval data.
                    
                    The first entry indexed by 1 represents the most recent
                    completed 1 minute interval. At the end of each interval
                    all entries of 'index' are copied into entry 'index+1'. If the
                    number of valid intervals is at least 15 then these 15 intervals
                    are summarised into 15 minutes stats. At the end of a 60 minute
                    period the least recent interval is discarded to accomodate
                    the next minute interval."

    ::= { sub10RadioStats1mHistory 2 }


sub10RadioStats1mHistEntry OBJECT-TYPE
    SYNTAX         Sub10RadioStats1mHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry in the sub10RadioStats1mHistTable.
                    Each entry represents 1 minute of performance
                    management data where the first entry with index=1
                    is the most recent 1 minute interval and the last
                    entry with index=n (up to 60) is the least recent
                    1 minute interval.
                    
                    The number of valid intervals is given by the
                    value of sub10RadioStats1mHistIntvls."
    INDEX          { sub10RadioStats1mHistIntvl }

    ::= { sub10RadioStats1mHistTable 1 }

Sub10RadioStats1mHistEntry ::= SEQUENCE {
    sub10RadioStats1mHistIntvl                        Unsigned32,
    sub10RadioStats1mHistTime                         Sub10DateTime,
    sub10RadioStats1mHistTxPowerMin                   Integer32,
    sub10RadioStats1mHistTxPowerMax                   Integer32,
    sub10RadioStats1mHistTxPowerAvg                   Integer32,
    sub10RadioStats1mHistRxPowerMin                   OCTET STRING,
    sub10RadioStats1mHistRxPowerMax                   OCTET STRING,
    sub10RadioStats1mHistRxPowerAvg                   OCTET STRING,
    sub10RadioStats1mHistVectErrMin                   OCTET STRING,
    sub10RadioStats1mHistVectErrMax                   OCTET STRING,
    sub10RadioStats1mHistVectErrAvg                   OCTET STRING,
    sub10RadioStats1mHistLnkLossMin                   OCTET STRING,
    sub10RadioStats1mHistLnkLossMax                   OCTET STRING,
    sub10RadioStats1mHistLnkLossAvg                   OCTET STRING,
    sub10RadioStats1mHistMWUTempMin                   Integer32,
    sub10RadioStats1mHistMWUTempMax                   Integer32,
    sub10RadioStats1mHistMWUTempAvg                   Integer32,
    sub10RadioStats1mHistRxFrms                       Counter64,
    sub10RadioStats1mHistTxFrms                       Counter64,
    sub10RadioStats1mHistRxPkts                       Counter64,
    sub10RadioStats1mHistTxPkts                       Counter64,
    sub10RadioStats1mHistRxMgmtPkts                   Counter64,
    sub10RadioStats1mHistTxMgmtPkts                   Counter64,
    sub10RadioStats1mHistRxBadFrms                    Counter64,
    sub10RadioStats1mHistQPSKTo8PSK                   Counter32,
    sub10RadioStats1mHist8PSKToQPSK                   Counter32,
    sub10RadioStats1mHistAFERMin                      OCTET STRING,
    sub10RadioStats1mHistAFERMax                      OCTET STRING,
    sub10RadioStats1mHistAFERAvg                      OCTET STRING,
    sub10RadioStats1mHistRxQPSK                       Counter32,
    sub10RadioStats1mHistRx8PSK                       Counter32,
    sub10RadioStats1mHistTxQPSK                       Counter32,
    sub10RadioStats1mHistTx8PSK                       Counter32
}

sub10RadioStats1mHistIntvl OBJECT-TYPE
    SYNTAX         Unsigned32(1..60)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The interval number used as an index to this
                    table. Interval numbers start at 1 (the most
                    recent 1 minute interval) and have a maximum value of 60
                    (the least recent 1 minute interval)."

    ::= { sub10RadioStats1mHistEntry 1 }


sub10RadioStats1mHistTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local system time when this 1m history entry was created.
                    This represents the end of the 1 minute interval for which the
                    associated stats with the same sub10RadioStats1mHistIntvl
                    table index are relevant."

    ::= { sub10RadioStats1mHistEntry 2 }


sub10RadioStats1mHistTxPowerMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the transmit power of the local radio
                    link has reached in this minute interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats1mHistEntry 3 }


sub10RadioStats1mHistTxPowerMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the transmit power of the local radio
                    link has reached in this minute interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats1mHistEntry 4 }


sub10RadioStats1mHistTxPowerAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the transmit power of the local radio
                    link has reached in this minute interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats1mHistEntry 5 }


sub10RadioStats1mHistRxPowerMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the receive power of the local radio
                    link has reached in this minute interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats1mHistEntry 6 }


sub10RadioStats1mHistRxPowerMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the receive power of the local radio
                    link has reached in this minute interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats1mHistEntry 7 }


sub10RadioStats1mHistRxPowerAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the receive power of the local radio
                    link has reached in this minute interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats1mHistEntry 8 }


sub10RadioStats1mHistVectErrMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the vector error of the local radio
                    link has reached in this minute interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats1mHistEntry 9 }


sub10RadioStats1mHistVectErrMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the vector error of the local radio
                    link has reached in this minute interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats1mHistEntry 10 }


sub10RadioStats1mHistVectErrAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the vector error of the local radio
                    link has reached in this minute interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats1mHistEntry 11 }


sub10RadioStats1mHistLnkLossMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the link loss of the local radio
                    link has reached in this minute interval, measured in dB."

    ::= { sub10RadioStats1mHistEntry 12 }


sub10RadioStats1mHistLnkLossMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the link loss of the local radio
                    link has reached in this minute interval, measured in dB."

    ::= { sub10RadioStats1mHistEntry 13 }


sub10RadioStats1mHistLnkLossAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the link loss of the local radio
                    link has reached in this minute interval, measured in dB."

    ::= { sub10RadioStats1mHistEntry 14 }


sub10RadioStats1mHistMWUTempMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum MWU temperature measured in this minute interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats1mHistEntry 15 }


sub10RadioStats1mHistMWUTempMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum MWU temperature measured in this minute interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats1mHistEntry 16 }


sub10RadioStats1mHistMWUTempAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average MWU temperature measured in this minute interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats1mHistEntry 17 }


sub10RadioStats1mHistRxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames received over the Radio interface in this minute.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data."

    ::= { sub10RadioStats1mHistEntry 22 }


sub10RadioStats1mHistTxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames transmitted over the Radio interface in this minute.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data."

    ::= { sub10RadioStats1mHistEntry 23 }


sub10RadioStats1mHistRxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface in this minute.
                    This includes all packets whether destined for the management
                    sub-system on this unit or to be sent direct to the Ethernet interface."

    ::= { sub10RadioStats1mHistEntry 24 }


sub10RadioStats1mHistTxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the Ethernet interface in this minute. This does not include
                    packets originating from the management sub-system."

    ::= { sub10RadioStats1mHistEntry 25 }


sub10RadioStats1mHistRxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface  in this minute
                    and which are destined for the management sub-system on this unit."

    ::= { sub10RadioStats1mHistEntry 26 }


sub10RadioStats1mHistTxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the management sub-system  in this minute.
                    This does not include packets originating from the Ethernet interface."

    ::= { sub10RadioStats1mHistEntry 27 }


sub10RadioStats1mHistRxBadFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The count of bad frames detected on the
                    receive side of the Radio interface in this minute."

    ::= { sub10RadioStats1mHistEntry 28 }


sub10RadioStats1mHistQPSKTo8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from QPSK to 8PSK in this 1 minute interval."

    ::= { sub10RadioStats1mHistEntry 29 }


sub10RadioStats1mHist8PSKToQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from 8PSK to QPSK in this 1 minute interval."

    ::= { sub10RadioStats1mHistEntry 30 }


sub10RadioStats1mHistAFERMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum AFER (Air Frame Error Ratio) in this minute.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats1mHistEntry 31 }


sub10RadioStats1mHistAFERMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum AFER (Air Frame Error Ratio) in this minute.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats1mHistEntry 32 }


sub10RadioStats1mHistAFERAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average AFER (Air Frame Error Ratio) in this minute.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats1mHistEntry 33 }


sub10RadioStats1mHistRxQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that  the Rx Modulation Mode was QPSK
                    in this 1 minute interval."

    ::= { sub10RadioStats1mHistEntry 34 }


sub10RadioStats1mHistRx8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Rx Modulation Mode was 8PSK
                    in this 1 minute interval."

    ::= { sub10RadioStats1mHistEntry 35 }


sub10RadioStats1mHistTxQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Tx Modulation Mode was QPSK
                    in this 1 minute interval."

    ::= { sub10RadioStats1mHistEntry 36 }


sub10RadioStats1mHistTx8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Tx Modulation Mode was 8PSK
                    in this 1 minute interval."

    ::= { sub10RadioStats1mHistEntry 37 }


-- ****************************************************************************
-- .sub10RadioStatsHistory.sub10RadioStats15mHistory Objects
-- ****************************************************************************

sub10RadioStats15mHistIntvls OBJECT-TYPE
    SYNTAX         Unsigned32(0..96)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of valid table entries in sub10RadioStats15mHistTable
                    on this system. There is one entry per 15 minute interval.
                    If the unit has been running for a minimum of 24 hours
                    and collecting valid data then the number of entries will be 96,
                    this represents a total maximum of 24 hours worth of data."

    ::= { sub10RadioStats15mHistory 1 }


sub10RadioStats15mHistTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10RadioStats15mHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table maintains a history of Radio statistics
                    based on 15 minute intervals. There are a maximum
                    of 96 intervals which represent 24 hours of data.
                    
                    This table along with the 1m and 1d tables provide a
                    history of the performance of the radio interface. This
                    information may then be post processed and used as a
                    troubleshooting tool.
                    
                    An entry in the table represents a 15 minute interval
                    where each interval is synchronised to the clock on the
                    hour. There are up to 96 intervals starting at 1. The
                    number of valid entries in the table is given by the value
                    of sub10RadioStats15mHistIntvls of which the minimum
                    is 0 and the maximum is 96 therefore providing
                    up to a maximum of 24 hours of 15 minute interval data.
                    
                    The first entry indexed by 1 represents the most recent
                    completed 15 minute interval. At the end of each interval
                    all entries of 'index' are copied into entry 'index+1'. If the
                    number of valid intervals is 96 then the least recent interval
                    entry is summarised into 24 hour stats and then discarded."

    ::= { sub10RadioStats15mHistory 2 }


sub10RadioStats15mHistEntry OBJECT-TYPE
    SYNTAX         Sub10RadioStats15mHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry in the sub10RadioStats15mHistTable.
                    Each entry represents 15 minutes performance
                    management data where the first entry with index=1
                    is the most recent 15 minute interval and the last
                    entry with index=n (up to 96) is the least recent
                    15 minute interval. The number of valid intervals
                    is sub10RadioStats15mHistIntvls."
    INDEX          { sub10RadioStats15mHistIntvl }

    ::= { sub10RadioStats15mHistTable 1 }

Sub10RadioStats15mHistEntry ::= SEQUENCE {
    sub10RadioStats15mHistIntvl                       Unsigned32,
    sub10RadioStats15mHistTime                        Sub10DateTime,
    sub10RadioStats15mHistTxPowerMin                  Integer32,
    sub10RadioStats15mHistTxPowerMax                  Integer32,
    sub10RadioStats15mHistTxPowerAvg                  Integer32,
    sub10RadioStats15mHistRxPowerMin                  OCTET STRING,
    sub10RadioStats15mHistRxPowerMax                  OCTET STRING,
    sub10RadioStats15mHistRxPowerAvg                  OCTET STRING,
    sub10RadioStats15mHistVectErrMin                  OCTET STRING,
    sub10RadioStats15mHistVectErrMax                  OCTET STRING,
    sub10RadioStats15mHistVectErrAvg                  OCTET STRING,
    sub10RadioStats15mHistLnkLossMin                  OCTET STRING,
    sub10RadioStats15mHistLnkLossMax                  OCTET STRING,
    sub10RadioStats15mHistLnkLossAvg                  OCTET STRING,
    sub10RadioStats15mHistMWUTempMin                  Integer32,
    sub10RadioStats15mHistMWUTempMax                  Integer32,
    sub10RadioStats15mHistMWUTempAvg                  Integer32,
    sub10RadioStats15mHistRxFrms                      Counter64,
    sub10RadioStats15mHistTxFrms                      Counter64,
    sub10RadioStats15mHistRxPkts                      Counter64,
    sub10RadioStats15mHistTxPkts                      Counter64,
    sub10RadioStats15mHistRxMgmtPkts                  Counter64,
    sub10RadioStats15mHistTxMgmtPkts                  Counter64,
    sub10RadioStats15mHistRxBadFrms                   Counter64,
    sub10RadioStats15mHistQPSKTo8PSK                  Counter32,
    sub10RadioStats15mHist8PSKToQPSK                  Counter32,
    sub10RadioStats15mHistAFERMin                     OCTET STRING,
    sub10RadioStats15mHistAFERMax                     OCTET STRING,
    sub10RadioStats15mHistAFERAvg                     OCTET STRING,
    sub10RadioStats15mHistRxQPSK                      Counter32,
    sub10RadioStats15mHistRx8PSK                      Counter32,
    sub10RadioStats15mHistTxQPSK                      Counter32,
    sub10RadioStats15mHistTx8PSK                      Counter32
}

sub10RadioStats15mHistIntvl OBJECT-TYPE
    SYNTAX         Unsigned32(1..96)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The interval number used as an index to this
                    table. Interval numbers start at 1 (the most
                    recent 15 minute interval) and have a maximum value of 96
                    (the least recent 15 minute interval)."

    ::= { sub10RadioStats15mHistEntry 1 }


sub10RadioStats15mHistTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local system time when this 15m history entry was created.
                    This represents the end of the 15 minute interval for which the
                    associated stats with the same sub10RadioStats15mHistIntvl
                    table index are relevant."

    ::= { sub10RadioStats15mHistEntry 2 }


sub10RadioStats15mHistTxPowerMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the transmit power of the local radio
                    link has reached in this 15 minute interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats15mHistEntry 3 }


sub10RadioStats15mHistTxPowerMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the transmit power of the local radio
                    link has reached in this 15 minute interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats15mHistEntry 4 }


sub10RadioStats15mHistTxPowerAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the transmit power of the local radio
                    link has reached in this 15 minute interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats15mHistEntry 5 }


sub10RadioStats15mHistRxPowerMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the receive power of the local radio
                    link has reached in this 15 minute interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats15mHistEntry 6 }


sub10RadioStats15mHistRxPowerMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the receive power of the local radio
                    link has reached in this 15 minute interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats15mHistEntry 7 }


sub10RadioStats15mHistRxPowerAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the receive power of the local radio
                    link has reached in this 15 minute interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats15mHistEntry 8 }


sub10RadioStats15mHistVectErrMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the vector error of the local radio
                    link has reached in this 15 minute interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats15mHistEntry 9 }


sub10RadioStats15mHistVectErrMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the vector error of the local radio
                    link has reached in this 15 minute interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats15mHistEntry 10 }


sub10RadioStats15mHistVectErrAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the vector error of the local radio
                    link has reached in this 15 minute interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats15mHistEntry 11 }


sub10RadioStats15mHistLnkLossMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the link loss of the local radio
                    link has reached in this 15 minute interval, measured in dB."

    ::= { sub10RadioStats15mHistEntry 12 }


sub10RadioStats15mHistLnkLossMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the link loss of the local radio
                    link has reached in this 15 minute interval, measured in dB."

    ::= { sub10RadioStats15mHistEntry 13 }


sub10RadioStats15mHistLnkLossAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the link loss of the local radio
                    link has reached in this 15 minute interval, measured in dB."

    ::= { sub10RadioStats15mHistEntry 14 }


sub10RadioStats15mHistMWUTempMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum MWU temperature measured in this 15 minute interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats15mHistEntry 15 }


sub10RadioStats15mHistMWUTempMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum MWU temperature measured in this 15 minute interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats15mHistEntry 16 }


sub10RadioStats15mHistMWUTempAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average MWU temperature measured in this 15 minute interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats15mHistEntry 17 }


sub10RadioStats15mHistRxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames received over the Radio interface in this 15 minutes.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data."

    ::= { sub10RadioStats15mHistEntry 22 }


sub10RadioStats15mHistTxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames transmitted over the Radio interface in this 15 minutes.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data."

    ::= { sub10RadioStats15mHistEntry 23 }


sub10RadioStats15mHistRxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface in this 15 minutes.
                    This includes all packets whether destined for the management
                    sub-system on this unit or to be sent direct to the Ethernet interface."

    ::= { sub10RadioStats15mHistEntry 24 }


sub10RadioStats15mHistTxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the Ethernet interface in this 15 minutes. This does not include
                    packets originating from the management sub-system."

    ::= { sub10RadioStats15mHistEntry 25 }


sub10RadioStats15mHistRxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface  in this 15 minutes
                    and which are destined for the management sub-system on this unit."

    ::= { sub10RadioStats15mHistEntry 26 }


sub10RadioStats15mHistTxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the management sub-system  in this 15 minutes.
                    This does not include packets originating from the Ethernet interface."

    ::= { sub10RadioStats15mHistEntry 27 }


sub10RadioStats15mHistRxBadFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The count of bad frames detected on the
                    receive side of the Radio interface in this 15 minutes."

    ::= { sub10RadioStats15mHistEntry 28 }


sub10RadioStats15mHistQPSKTo8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from QPSK to 8PSK in this 15 minute interval."

    ::= { sub10RadioStats15mHistEntry 29 }


sub10RadioStats15mHist8PSKToQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from 8PSK to QPSK in this 15 minute interval."

    ::= { sub10RadioStats15mHistEntry 30 }


sub10RadioStats15mHistAFERMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum AFER (Air Frame Error Ratio) in this 15 minute interval.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats15mHistEntry 31 }


sub10RadioStats15mHistAFERMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum AFER (Air Frame Error Ratio) in this 15 minute interval.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats15mHistEntry 32 }


sub10RadioStats15mHistAFERAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average AFER (Air Frame Error Ratio) in this 15 minute interval.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats15mHistEntry 33 }


sub10RadioStats15mHistRxQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that  the Rx Modulation Mode was QPSK
                    in this 15 minute interval."

    ::= { sub10RadioStats15mHistEntry 34 }


sub10RadioStats15mHistRx8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Rx Modulation Mode was 8PSK
                    in this 15 minute interval."

    ::= { sub10RadioStats15mHistEntry 35 }


sub10RadioStats15mHistTxQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Tx Modulation Mode was QPSK
                    in this 15 minute interval."

    ::= { sub10RadioStats15mHistEntry 36 }


sub10RadioStats15mHistTx8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Tx Modulation Mode was 8PSK
                    in this 15 minute interval."

    ::= { sub10RadioStats15mHistEntry 37 }


-- ****************************************************************************
-- .sub10RadioStatsHistory.sub10RadioStats1dHistory Objects
-- ****************************************************************************

sub10RadioStats1dHistIntvls OBJECT-TYPE
    SYNTAX         Unsigned32(0..30)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of valid table entries in sub10RadioStats1dHistTable
                    on this system. There is one entry per 1 day interval.
                    
                    If the unit has been running for a minimum  of 30 days
                    and collecting valid data then the number of entries will be 30,
                    this represents a total maximum of 30 days worth of data."

    ::= { sub10RadioStats1dHistory 1 }


sub10RadioStats1dHistTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF Sub10RadioStats1dHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "This table maintains a history of Radio statistics
                    based on 1 day intervals. There are a maximum
                    of 30 intervals which represent 30 days of data.
                    
                    This table along with the 1m and 15m tables provide a
                    history of the performance of the radio interface. This
                    information may then be post processed and used as a
                    troubleshooting tool.
                    
                    An entry in the table represents a 1 day interval
                    where each interval is synchronised to the clock on the
                    hour. There are up to 30 intervals starting at 1. The
                    number of valid entries in the table is given by the value
                    of sub10RadioStats1dHistIntvls of which the minimum
                    is 0 and the maximum is 30 therefore providing
                    up to a maximum of 30 days of 1 day interval data.
                    
                    The first entry indexed by 1 represents the most recent
                    completed 1 day interval. At the end of each interval
                    all entries of 'index' are copied into entry 'index+1'. If the
                    number of valid intervals is 30 then the least recent interval
                    entry is discarded."

    ::= { sub10RadioStats1dHistory 2 }


sub10RadioStats1dHistEntry OBJECT-TYPE
    SYNTAX         Sub10RadioStats1dHistEntry
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "An entry in the sub10RadioStats1dHistTable.
                    Each entry represents 1 day of performance
                    management data where the first entry with index=1
                    is the most recent 1 day interval and the last
                    entry with index=n (up to 30) is the least recent
                    1 day interval.
                    
                    The number of valid intervals is given by the
                    value of sub10RadioStats1dHistIntvls."
    INDEX          { sub10RadioStats1dHistIntvl }

    ::= { sub10RadioStats1dHistTable 1 }

Sub10RadioStats1dHistEntry ::= SEQUENCE {
    sub10RadioStats1dHistIntvl                        Unsigned32,
    sub10RadioStats1dHistTime                         Sub10DateTime,
    sub10RadioStats1dHistTxPowerMin                   Integer32,
    sub10RadioStats1dHistTxPowerMax                   Integer32,
    sub10RadioStats1dHistTxPowerAvg                   Integer32,
    sub10RadioStats1dHistRxPowerMin                   OCTET STRING,
    sub10RadioStats1dHistRxPowerMax                   OCTET STRING,
    sub10RadioStats1dHistRxPowerAvg                   OCTET STRING,
    sub10RadioStats1dHistVectErrMin                   OCTET STRING,
    sub10RadioStats1dHistVectErrMax                   OCTET STRING,
    sub10RadioStats1dHistVectErrAvg                   OCTET STRING,
    sub10RadioStats1dHistLnkLossMin                   OCTET STRING,
    sub10RadioStats1dHistLnkLossMax                   OCTET STRING,
    sub10RadioStats1dHistLnkLossAvg                   OCTET STRING,
    sub10RadioStats1dHistMWUTempMin                   Integer32,
    sub10RadioStats1dHistMWUTempMax                   Integer32,
    sub10RadioStats1dHistMWUTempAvg                   Integer32,
    sub10RadioStats1dHistRxFrms                       Counter64,
    sub10RadioStats1dHistTxFrms                       Counter64,
    sub10RadioStats1dHistRxPkts                       Counter64,
    sub10RadioStats1dHistTxPkts                       Counter64,
    sub10RadioStats1dHistRxMgmtPkts                   Counter64,
    sub10RadioStats1dHistTxMgmtPkts                   Counter64,
    sub10RadioStats1dHistRxBadFrms                    Counter64,
    sub10RadioStats1dHistQPSKTo8PSK                   Counter64,
    sub10RadioStats1dHist8PSKToQPSK                   Counter64,
    sub10RadioStats1dHistAFERMin                      OCTET STRING,
    sub10RadioStats1dHistAFERMax                      OCTET STRING,
    sub10RadioStats1dHistAFERAvg                      OCTET STRING,
    sub10RadioStats1dHistRxQPSK                       Counter32,
    sub10RadioStats1dHistRx8PSK                       Counter32,
    sub10RadioStats1dHistTxQPSK                       Counter32,
    sub10RadioStats1dHistTx8PSK                       Counter32
}

sub10RadioStats1dHistIntvl OBJECT-TYPE
    SYNTAX         Unsigned32(1..30)
    MAX-ACCESS     not-accessible
    STATUS         current
    DESCRIPTION    "The interval number used as an index to this
                    table. Interval numbers start at 1 (the most
                    recent 1 day interval) and have a maximum value of 24
                    (the least recent 1 day interval)."

    ::= { sub10RadioStats1dHistEntry 1 }


sub10RadioStats1dHistTime OBJECT-TYPE
    SYNTAX         Sub10DateTime
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The local system time when this 1d history entry was created.
                    This represents the end of the 1 day interval for which the
                    associated stats with the same sub10RadioStats1dHistIntvl
                    table index are relevant."

    ::= { sub10RadioStats1dHistEntry 2 }


sub10RadioStats1dHistTxPowerMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the transmit power of the local radio
                    link has reached in this day interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats1dHistEntry 3 }


sub10RadioStats1dHistTxPowerMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the transmit power of the local radio
                    link has reached in this day interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats1dHistEntry 4 }


sub10RadioStats1dHistTxPowerAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the transmit power of the local radio
                    link has reached in this day interval, measured in dBm."
    DEFVAL { 0 }

    ::= { sub10RadioStats1dHistEntry 5 }


sub10RadioStats1dHistRxPowerMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the receive power of the local radio
                    link has reached in this day interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats1dHistEntry 6 }


sub10RadioStats1dHistRxPowerMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the receive power of the local radio
                    link has reached in this day interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats1dHistEntry 7 }


sub10RadioStats1dHistRxPowerAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the receive power of the local radio
                    link has reached in this day interval, measured in dBm."
    DEFVAL { "0" }

    ::= { sub10RadioStats1dHistEntry 8 }


sub10RadioStats1dHistVectErrMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the vector error of the local radio
                    link has reached in this day interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats1dHistEntry 9 }


sub10RadioStats1dHistVectErrMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the vector error of the local radio
                    link has reached in this day interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats1dHistEntry 10 }


sub10RadioStats1dHistVectErrAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the vector error of the local radio
                    link has reached in this day interval, measured in dB."
    DEFVAL { "0" }

    ::= { sub10RadioStats1dHistEntry 11 }


sub10RadioStats1dHistLnkLossMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum value that the link loss of the local radio
                    link has reached in this day interval, measured in dB."

    ::= { sub10RadioStats1dHistEntry 12 }


sub10RadioStats1dHistLnkLossMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum value that the link loss of the local radio
                    link has reached in this day interval, measured in dB."

    ::= { sub10RadioStats1dHistEntry 13 }


sub10RadioStats1dHistLnkLossAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    UNITS          "dBm"
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average value that the link loss of the local radio
                    link has reached in this day interval, measured in dB."

    ::= { sub10RadioStats1dHistEntry 14 }


sub10RadioStats1dHistMWUTempMin OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum MWU temperature measured in this day interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats1dHistEntry 15 }


sub10RadioStats1dHistMWUTempMax OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum MWU temperature measured in this day interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats1dHistEntry 16 }


sub10RadioStats1dHistMWUTempAvg OBJECT-TYPE
    SYNTAX         Integer32(-100..100)
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average MWU temperature measured in this day interval."
    DEFVAL { 0 }

    ::= { sub10RadioStats1dHistEntry 17 }


sub10RadioStats1dHistRxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames received over the Radio interface in this day interval.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data."

    ::= { sub10RadioStats1dHistEntry 22 }


sub10RadioStats1dHistTxFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Airside frames transmitted over the Radio interface in this day interval.
                    This is irrespective of whether the Airside frame transmitted contained
                    any ethernet data."

    ::= { sub10RadioStats1dHistEntry 23 }


sub10RadioStats1dHistRxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface in this day interval.
                    This includes all packets whether destined for the management
                    sub-system on this unit or to be sent direct to the Ethernet interface."

    ::= { sub10RadioStats1dHistEntry 24 }


sub10RadioStats1dHistTxPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the Ethernet interface in this day interval. This does not include
                    packets originating from the management sub-system."

    ::= { sub10RadioStats1dHistEntry 25 }


sub10RadioStats1dHistRxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets received over the Radio interface  in this day interval
                    and which are destined for the management sub-system on this unit."

    ::= { sub10RadioStats1dHistEntry 26 }


sub10RadioStats1dHistTxMgmtPkts OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number of Ethernet packets transmitted over the Radio interface
                    which originated from the management sub-system  in this day interval.
                    This does not include packets originating from the Ethernet interface."

    ::= { sub10RadioStats1dHistEntry 27 }


sub10RadioStats1dHistRxBadFrms OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The count of bad frames detected on the
                    receive side of the Radio interface in this day interval."

    ::= { sub10RadioStats1dHistEntry 28 }


sub10RadioStats1dHistQPSKTo8PSK OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from QPSK to 8PSK in this 1 day interval."

    ::= { sub10RadioStats1dHistEntry 29 }


sub10RadioStats1dHist8PSKToQPSK OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The number transitions of Modulation Mode configuration
                    from 8PSK to QPSK in this 1 day interval."

    ::= { sub10RadioStats1dHistEntry 30 }


sub10RadioStats1dHistAFERMin OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The minimum AFER (Air Frame Error Ratio) in this 1 day interval.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats1dHistEntry 31 }


sub10RadioStats1dHistAFERMax OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The maximum AFER (Air Frame Error Ratio) in this 1 day interval.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats1dHistEntry 32 }


sub10RadioStats1dHistAFERAvg OBJECT-TYPE
    SYNTAX         OCTET STRING (SIZE (0..32))
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The average AFER (Air Frame Error Ratio) in this 1 day interval.
                    See sub10RadioLclAFER."

    ::= { sub10RadioStats1dHistEntry 33 }


sub10RadioStats1dHistRxQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that  the Rx Modulation Mode was QPSK
                    in this 1 day interval."

    ::= { sub10RadioStats1dHistEntry 34 }


sub10RadioStats1dHistRx8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Rx Modulation Mode was 8PSK
                    in this 1 day interval."

    ::= { sub10RadioStats1dHistEntry 35 }


sub10RadioStats1dHistTxQPSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Tx Modulation Mode was QPSK
                    in this 1 day interval."

    ::= { sub10RadioStats1dHistEntry 36 }


sub10RadioStats1dHistTx8PSK OBJECT-TYPE
    SYNTAX         Counter32
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION    "The time in seconds that the Tx Modulation Mode was 8PSK
                    in this 1 day interval."

    ::= { sub10RadioStats1dHistEntry 37 }


-- ****************************************************************************
-- .sub10MIBConformance.sub10MIBCompliances Objects
-- ****************************************************************************
sub10Compliance MODULE-COMPLIANCE
    STATUS         current
    DESCRIPTION    "The compliance statement for Sub10 entities which implement the SUB10SYSTEMS-MIB."
    MODULE         -- this module
    MANDATORY-GROUPS { sub10NotificationsGrp,
                       sub10UnitLocalStatusGrp,
                       sub10UnitLclAlarmStateEntryGrp,
                       sub10UnitRemoteStatusGrp,
                       sub10UnitMgmtSystemGrp,
                       sub10UnitMgmtIpGrp,
                       sub10UnitMgmtVlanGrp,
                       sub10UnitMgmtUsersGrp,
                       sub10UnitMgmtUserEntryGrp,
                       sub10UnitMgmtTimeGrp,
                       sub10UnitMgmtAlarmEntryGrp,
                       sub10UnitMgmtAlarmsGrp,
                       sub10UnitMgmtSnmpGrp,
                       sub10UnitMgmtSnmpTrpDstEntryGrp,
                       sub10UnitMgmtSnmpUserEntryGrp,
                       sub10UnitMgmtSnmpAccessEntryGrp,
                       sub10UnitMgmtSnmpTargetEntryGrp,
                       sub10UnitMgmtFirmwareGrp,
                       sub10UnitMgmtFirmwareBankEntryGrp,
                       sub10UnitMgmtDNSEntryGrp,
                       sub10UnitMgmtEncryptionGrp,
                       sub10UnitMgmtLicenseGrp,
                       sub10UnitMgmtSyncEGrp,
                       sub10UnitMgmtActionsGrp,
                       sub10EthLclStatusEntryGrp,
                       sub10EthRmtStatusEntryGrp,
                       sub10EthMgmtPhyEntryGrp,
                       sub10EthMgmtVlanGrp,
                       sub10EthMgmtVlanAllowedEntryGrp,
                       sub10EthMgmtQoSGrp,
                       sub10EthMgmtQoSQEntryGrp,
                       sub10EthMgmtQoSVlanEntryGrp,
                       sub10EthMgmtQoSPCPEntryGrp,
                       sub10EthMgmtQoSDSCPEntryGrp,
                       sub10EthMgmtQoSMPLSEntryGrp,
                       sub10EthMgmtStatsActiveEntryGrp,
                       sub10EthernetStatsGrp,
                       sub10EthernetStatsCurrEntryGrp,
                       sub10EthernetStats15mHistoryGrp,
                       sub10EthStats15mHistEntryGrp,
                       sub10EthStats1dHistoryGrp,
                       sub10EthStats1dHistEntryGrp,
                       sub10RadioLocalStatusGrp,
                       sub10RadioRemoteStatusGrp,
                       sub10RadioMgmtGrp,
                       sub10RadioMgmtStatsGrp,
                       sub10RadioMgmtStatsActiveEntryGrp,
                       sub10RadioStatsGrp,
                       sub10RadioStatsCurrentGrp,
                       sub10RadioStats1mHistoryGrp,
                       sub10RadioStats1mHistEntryGrp,
                       sub10RadioStats15mHistoryGrp,
                       sub10RadioStats15mHistEntryGrp,
                       sub10RadioStats1dHistoryGrp,
                       sub10RadioStats1dHistEntryGrp
                     }


    ::= { sub10MIBCompliances 1 }


-- ****************************************************************************
-- .sub10MIBConformance.sub10MIBGroups Objects
-- ****************************************************************************
sub10NotificationsGrp NOTIFICATION-GROUP
    NOTIFICATIONS { 
        sub10UnitAlarm,
        sub10EthernetAlarm,
        sub10RadioAlarm
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10NotificationsGrp group"

    ::= { sub10MIBGroups 1 }

sub10UnitLocalStatusGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitLclTime,
        sub10UnitLclUnitType,
        sub10UnitLclDescription,
        sub10UnitLclHWSerialNumber,
        sub10UnitLclTerminalName,
        sub10UnitLclTerminalType,
        sub10UnitLclLinkName,
        sub10UnitLclLinkId,
        sub10UnitLclSiteName,
        sub10UnitLclFirmwareLoadedBank,
        sub10UnitLclFirmwareVersion,
        sub10UnitLclIpAddress,
        sub10UnitLclMWUTemperature,
        sub10UnitLclNTPSyncStatus,
        sub10UnitLclRadioDataRate,
        sub10UnitLclMWUType,
        sub10UnitLclFPGAVersion
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitLocalStatusGrp group"

    ::= { sub10MIBGroups 2 }

sub10UnitLclAlarmStateEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitLclAlarmState,
        sub10UnitLclAlarmStateTime
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitLclAlarmStateEntryGrp group"

    ::= { sub10MIBGroups 3 }

sub10UnitRemoteStatusGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitRmtUnitType,
        sub10UnitRmtTime,
        sub10UnitRmtTerminalName,
        sub10UnitRmtTerminalType,
        sub10UnitRmtLinkName,
        sub10UnitRmtLinkId,
        sub10UnitRmtHWSerialNumber,
        sub10UnitRmtFirmwareVersion,
        sub10UnitRmtIpAddress,
        sub10UnitRmtMWUTemperature
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitRemoteStatusGrp group"

    ::= { sub10MIBGroups 4 }

sub10UnitMgmtSystemGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtTerminalName,
        sub10UnitMgmtLinkName,
        sub10UnitMgmtLinkId,
        sub10UnitMgmtSiteName,
        sub10UnitMgmtContactName
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtSystemGrp group"

    ::= { sub10MIBGroups 5 }

sub10UnitMgmtIpGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtIpMode,
        sub10UnitMgmtIpAddress,
        sub10UnitMgmtIpSubnetMask,
        sub10UnitMgmtIpDefGateway,
        sub10UnitMgmtIpDHCP
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtIpGrp group"

    ::= { sub10MIBGroups 6 }

sub10UnitMgmtVlanGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtVlanState,
        sub10UnitMgmtVlanId,
        sub10UnitMgmtVlanPriority,
        sub10UnitMgmtVlanDSCP,
        sub10UnitMgmtVlanDEI
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtVlanGrp group"

    ::= { sub10MIBGroups 7 }

sub10UnitMgmtUsersGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtUsersNumber
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtUsersGrp group"

    ::= { sub10MIBGroups 8 }

sub10UnitMgmtUserEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtUserRowStatus,
        sub10UnitMgmtUserName,
        sub10UnitMgmtUserGroup,
        sub10UnitMgmtUserPassword,
        sub10UnitMgmtUserPasswordVerify
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtUserEntryGrp group"

    ::= { sub10MIBGroups 9 }

sub10UnitMgmtTimeGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtTimeLocal,
        sub10UnitMgmtTimeNTPEnabled,
        sub10UnitMgmtTimeNTPServer1,
        sub10UnitMgmtTimeNTPServer2,
        sub10UnitMgmtTimeNTPPort,
        sub10UnitMgmtTimeNTPSyncStatus,
        sub10UnitMgmtDateTime
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtTimeGrp group"

    ::= { sub10MIBGroups 10 }

sub10UnitMgmtAlarmEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtAlarmRowStatus,
        sub10UnitMgmtAlarmName,
        sub10UnitMgmtAlarmSeverity,
        sub10UnitMgmtAlarmMeasObject,
        sub10UnitMgmtAlarmMonitorIntvl,
        sub10UnitMgmtAlarmRaiseOper,
        sub10UnitMgmtAlarmRaiseThresh,
        sub10UnitMgmtAlarmClearOper,
        sub10UnitMgmtAlarmClearThresh,
        sub10UnitMgmtAlarmRaiseIntvls,
        sub10UnitMgmtAlarmClearIntvls,
        sub10UnitMgmtAlarmType,
        sub10UnitMgmtAlarmSmtpAddress,
        sub10UnitMgmtAlarmToSyslog,
        sub10UnitMgmtAlarmEnabled,
        sub10UnitMgmtAlarmMeasObjectVal,
        sub10UnitMgmtAlarmToSNMP,
        sub10UnitMgmtAlarmMeasObjIndex
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtAlarmEntryGrp group"

    ::= { sub10MIBGroups 11 }

sub10UnitMgmtAlarmsGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtAlarmsUserDefStart
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtAlarmsGrp group"

    ::= { sub10MIBGroups 12 }

sub10UnitMgmtSnmpGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtSnmpAgent,
        sub10UnitMgmtSnmpTraps,
        sub10UnitMgmtSnmpv320Mib,
        sub10UnitMgmtSnmpv3,
        sub10UnitMgmtSnmpEngineIdFormat,
        sub10UnitMgmtSnmpEngineIdText,
        sub10UnitMgmtSnmpEngineId,
        sub10UnitMgmtSnmpOperAuthProto,
        sub10UnitMgmtSnmpOperPrivProto,
        sub10UnitMgmtSnmpAdminAuthProto,
        sub10UnitMgmtSnmpAdminPrivProto,
        sub10UnitMgmtSnmpMaintAuthProto,
        sub10UnitMgmtSnmpMaintPrivProto
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtSnmpGrp group"

    ::= { sub10MIBGroups 13 }

sub10UnitMgmtSnmpTrpDstEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtSnmpTrpDstRowStatus,
        sub10UnitMgmtSnmpTrpDstIpAddr,
        sub10UnitMgmtSnmpTrpDstCommunity
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtSnmpTrpDstEntryGrp group"

    ::= { sub10MIBGroups 14 }

sub10UnitMgmtSnmpUserEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtSnmpUserRowStatus,
        sub10UnitMgmtSnmpUserName,
        sub10UnitMgmtSnmpUserGroup,
        sub10UnitMgmtSnmpUserAuthPwd,
        sub10UnitMgmtSnmpUserAuthPwdChk,
        sub10UnitMgmtSnmpUserPrivPwd,
        sub10UnitMgmtSnmpUserPrivPwdChk
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtSnmpUserEntryGrp group"

    ::= { sub10MIBGroups 15 }

sub10UnitMgmtSnmpAccessEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtSnmpAccessRowStatus,
        sub10UnitMgmtSnmpAccessName,
        sub10UnitMgmtSnmpAccessIpAddr
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtSnmpAccessEntryGrp group"

    ::= { sub10MIBGroups 16 }

sub10UnitMgmtSnmpTargetEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtSnmpTargetRowStatus,
        sub10UnitMgmtSnmpTargetName,
        sub10UnitMgmtSnmpTargetIpAddr,
        sub10UnitMgmtSnmpTargetUserName
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtSnmpTargetEntryGrp group"

    ::= { sub10MIBGroups 17 }

sub10UnitMgmtFirmwareGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtFirmwareSelectBank,
        sub10UnitMgmtFirmwareLoadedBank,
        sub10UnitMgmtFirmwareVersion,
        sub10UnitMgmtFirmwareBootVersion,
        sub10UnitMgmtFirmwareAction,
        sub10UnitMgmtFirmwareUplImage,
        sub10UnitMgmtFirmwareUplSvrIp,
        sub10UnitMgmtFirmwareFromBank,
        sub10UnitMgmtFirmwareToBank,
        sub10UnitMgmtFirmwareActStatus,
        sub10UnitMgmtFirmwareActProgress
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtFirmwareGrp group"

    ::= { sub10MIBGroups 18 }

sub10UnitMgmtFirmwareBankEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtFirmwareBankVersion,
        sub10UnitMgmtFirmwareBankImage
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtFirmwareBankEntryGrp group"

    ::= { sub10MIBGroups 19 }

sub10UnitMgmtDNSEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtDNServer
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtDNSEntryGrp group"

    ::= { sub10MIBGroups 20 }

sub10UnitMgmtEncryptionGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtEncryptMode,
        sub10UnitMgmtEncryptKey
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtEncryptionGrp group"

    ::= { sub10MIBGroups 21 }

sub10UnitMgmtLicenseGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtLicenseKey,
        sub10UnitMgmtLicenseAES
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtLicenseGrp group"

    ::= { sub10MIBGroups 22 }

sub10UnitMgmtSyncEGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtSyncEMode
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtSyncEGrp group"

    ::= { sub10MIBGroups 23 }

sub10UnitMgmtActionsGrp OBJECT-GROUP
    OBJECTS { 
        sub10UnitMgmtTransaction,
        sub10UnitMgmtTransactionStatus,
        sub10UnitMgmtRollbackTimeout,
        sub10UnitMgmtTransactionMode,
        sub10UnitMgmtResetAction,
        sub10UnitMgmtResetStatsGroup,
        sub10UnitMgmtResetAlarmsType
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10UnitMgmtActionsGrp group"

    ::= { sub10MIBGroups 24 }

sub10EthLclStatusEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthLclLinkStatus,
        sub10EthLclMacAddress,
        sub10EthLclSpeed,
        sub10EthLclDuplex,
        sub10EthLclMDI
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthLclStatusEntryGrp group"

    ::= { sub10MIBGroups 25 }

sub10EthRmtStatusEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthRmtLinkStatus,
        sub10EthRmtMacAddress,
        sub10EthRmtSpeed,
        sub10EthRmtDuplex,
        sub10EthRmtMDI
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthRmtStatusEntryGrp group"

    ::= { sub10MIBGroups 26 }

sub10EthMgmtPhyEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtPhyAutoNeg,
        sub10EthMgmtPhySpeed,
        sub10EthMgmtPhyDuplex,
        sub10EthMgmtPhyMDI
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtPhyEntryGrp group"

    ::= { sub10MIBGroups 27 }

sub10EthMgmtVlanGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtVlanFiltering,
        sub10EthMgmtVlanDefaultEnabled,
        sub10EthMgmtVlanDefaultId,
        sub10EthMgmtVlanDefaultPriority,
        sub10EthMgmtVlanDefaultDEI,
        sub10EthMgmtVlanIngressAction,
        sub10EthMgmtVlanEgressAction
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtVlanGrp group"

    ::= { sub10MIBGroups 28 }

sub10EthMgmtVlanAllowedEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtVlanAllowedRowStatus,
        sub10EthMgmtVlanAllowedId
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtVlanAllowedEntryGrp group"

    ::= { sub10MIBGroups 29 }

sub10EthMgmtQoSGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtQoSActiveState,
        sub10EthMgmtQoSMode,
        sub10EthMgmtQoSUntaggedQueue,
        sub10EthMgmtQoSVlanMappingNumber,
        sub10EthMgmtQoSDSCPMappingNumber,
        sub10EthMgmtQoSMPLSMappingNumber
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtQoSGrp group"

    ::= { sub10MIBGroups 30 }

sub10EthMgmtQoSQEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtQoSQSchedulingType,
        sub10EthMgmtQoSQDWRRWeight,
        sub10EthMgmtQoSQCongestionPolicy,
        sub10EthMgmtQoSQSizeMax,
        sub10EthMgmtQoSQLen
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtQoSQEntryGrp group"

    ::= { sub10MIBGroups 31 }

sub10EthMgmtQoSVlanEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtQoSVlanId,
        sub10EthMgmtQoSVlanQueue
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtQoSVlanEntryGrp group"

    ::= { sub10MIBGroups 32 }

sub10EthMgmtQoSPCPEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtQoSPCPQueue
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtQoSPCPEntryGrp group"

    ::= { sub10MIBGroups 33 }

sub10EthMgmtQoSDSCPEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtQoSDSCPMarking,
        sub10EthMgmtQoSDSCPQueue
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtQoSDSCPEntryGrp group"

    ::= { sub10MIBGroups 34 }

sub10EthMgmtQoSMPLSEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtQoSMPLSTrafficClass,
        sub10EthMgmtQoSMPLSQueue
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtQoSMPLSEntryGrp group"

    ::= { sub10MIBGroups 35 }

sub10EthMgmtStatsActiveEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthMgmtStatsActiveName,
        sub10EthMgmtStatsActiveState
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthMgmtStatsActiveEntryGrp group"

    ::= { sub10MIBGroups 36 }

sub10EthernetStatsGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthStatsTimeElapsed
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthernetStatsGrp group"

    ::= { sub10MIBGroups 37 }

sub10EthernetStatsCurrEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthStatsCurrRxOctets,
        sub10EthStatsCurrRxGoodFrms,
        sub10EthStatsCurrRxBcastFrms,
        sub10EthStatsCurrRxMcastFrms,
        sub10EthStatsCurrRxPauseFrms,
        sub10EthStatsCurrRxCRCErrs,
        sub10EthStatsCurrRxAlignErrs,
        sub10EthStatsCurrRxOversized,
        sub10EthStatsCurrRxJabberFrms,
        sub10EthStatsCurrRxUndersized,
        sub10EthStatsCurrRxFragments,
        sub10EthStatsCurrRxSOFOvrns,
        sub10EthStatsCurrTxOctets,
        sub10EthStatsCurrTxGoodFrms,
        sub10EthStatsCurrTxBcastFrms,
        sub10EthStatsCurrTxMcastFrms,
        sub10EthStatsCurrTxPauseFrms,
        sub10EthStatsCurrTxDeferred,
        sub10EthStatsCurrTxCollsn,
        sub10EthStatsCurrTxSnglCollsn,
        sub10EthStatsCurrTxMlplCollsn,
        sub10EthStatsCurrTxExsvCollsn,
        sub10EthStatsCurrTxLtCollsn,
        sub10EthStatsCurrTxCSenseErrs,
        sub10EthStatsCurrPkts64Octets,
        sub10EthStatsCurrPkts65T127,
        sub10EthStatsCurrPkts128T255,
        sub10EthStatsCurrPkts256T511,
        sub10EthStatsCurrPkts512T1023,
        sub10EthStatsCurrPkts1024TMax,
        sub10EthStatsCurrRxMbps,
        sub10EthStatsCurrTxMbps,
        sub10EthStatsCurrRxMbpsMin,
        sub10EthStatsCurrRxMbpsMax,
        sub10EthStatsCurrRxMbpsAvg,
        sub10EthStatsCurrTxMbpsMin,
        sub10EthStatsCurrTxMbpsMax,
        sub10EthStatsCurrTxMbpsAvg,
        sub10EthStatsCurrRmtRxMbpsAvg,
        sub10EthStatsCurrRmtTxMbpsAvg
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthernetStatsCurrEntryGrp group"

    ::= { sub10MIBGroups 38 }

sub10EthernetStats15mHistoryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthStats15mHistIntvls
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthernetStats15mHistoryGrp group"

    ::= { sub10MIBGroups 39 }

sub10EthStats15mHistEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthStats15mHistTime,
        sub10EthStats15mHistRxOctets,
        sub10EthStats15mHistRxGoodFrms,
        sub10EthStats15mHistRxBcastFrms,
        sub10EthStats15mHistRxMcastFrms,
        sub10EthStats15mHistRxPauseFrms,
        sub10EthStats15mHistRxCRCErrs,
        sub10EthStats15mHistRxAlignErrs,
        sub10EthStats15mHistRxOversized,
        sub10EthStats15mHistRxJabberFrms,
        sub10EthStats15mHistRxUndersized,
        sub10EthStats15mHistRxFragments,
        sub10EthStats15mHistRxSOFOvrns,
        sub10EthStats15mHistTxOctets,
        sub10EthStats15mHistTxGoodFrms,
        sub10EthStats15mHistTxBcastFrms,
        sub10EthStats15mHistTxMcastFrms,
        sub10EthStats15mHistTxPauseFrms,
        sub10EthStats15mHistTxDeferred,
        sub10EthStats15mHistTxCollsn,
        sub10EthStats15mHistTxSnglCollsn,
        sub10EthStats15mHistTxMlplCollsn,
        sub10EthStats15mHistTxExsvCollsn,
        sub10EthStats15mHistTxLtCollsn,
        sub10EthStats15mHistTxCSenseErrs,
        sub10EthStats15mHistPkts64Octets,
        sub10EthStats15mHistPkts65T127,
        sub10EthStats15mHistPkts128T255,
        sub10EthStats15mHistPkts256T511,
        sub10EthStats15mHistPkts512T1023,
        sub10EthStats15mHistPkts1024TMax,
        sub10EthStats15mHistRxMbpsMin,
        sub10EthStats15mHistRxMbpsMax,
        sub10EthStats15mHistRxMbpsAvg,
        sub10EthStats15mHistTxMbpsMin,
        sub10EthStats15mHistTxMbpsMax,
        sub10EthStats15mHistTxMbpsAvg
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthStats15mHistEntryGrp group"

    ::= { sub10MIBGroups 40 }

sub10EthStats1dHistoryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthStats1dHistIntvls
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthStats1dHistoryGrp group"

    ::= { sub10MIBGroups 41 }

sub10EthStats1dHistEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10EthStats1dHistTime,
        sub10EthStats1dHistRxOctets,
        sub10EthStats1dHistRxGoodFrms,
        sub10EthStats1dHistRxBcastFrms,
        sub10EthStats1dHistRxMcastFrms,
        sub10EthStats1dHistRxPauseFrms,
        sub10EthStats1dHistRxCRCErrs,
        sub10EthStats1dHistRxAlignErrs,
        sub10EthStats1dHistRxOversized,
        sub10EthStats1dHistRxJabberFrms,
        sub10EthStats1dHistRxUndersized,
        sub10EthStats1dHistRxFragments,
        sub10EthStats1dHistRxSOFOvrns,
        sub10EthStats1dHistTxOctets,
        sub10EthStats1dHistTxGoodFrms,
        sub10EthStats1dHistTxBcastFrms,
        sub10EthStats1dHistTxMcastFrms,
        sub10EthStats1dHistTxPauseFrms,
        sub10EthStats1dHistTxDeferred,
        sub10EthStats1dHistTxCollsn,
        sub10EthStats1dHistTxSnglCollsn,
        sub10EthStats1dHistTxMlplCollsn,
        sub10EthStats1dHistTxExsvCollsn,
        sub10EthStats1dHistTxLtCollsn,
        sub10EthStats1dHistTxCSenseErrs,
        sub10EthStats1dHistPkts64Octets,
        sub10EthStats1dHistPkts65T127,
        sub10EthStats1dHistPkts128T255,
        sub10EthStats1dHistPkts256T511,
        sub10EthStats1dHistPkts512T1023,
        sub10EthStats1dHistPkts1024TMax,
        sub10EthStats1dHistRxMbpsMin,
        sub10EthStats1dHistRxMbpsMax,
        sub10EthStats1dHistRxMbpsAvg,
        sub10EthStats1dHistTxMbpsMin,
        sub10EthStats1dHistTxMbpsMax,
        sub10EthStats1dHistTxMbpsAvg
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10EthStats1dHistEntryGrp group"

    ::= { sub10MIBGroups 42 }

sub10RadioLocalStatusGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioLclLinkStatus,
        sub10RadioLclTxPower,
        sub10RadioLclRxPower,
        sub10RadioLclVectErr,
        sub10RadioLclLnkLoss,
        sub10RadioLclAlignmentMode,
        sub10RadioLclDataRate,
        sub10RadioLclMWUType,
        sub10RadioLclAFER,
        sub10RadioLclRxModulationMode,
        sub10RadioLclTxModulationMode
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioLocalStatusGrp group"

    ::= { sub10MIBGroups 43 }

sub10RadioRemoteStatusGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioRmtLinkStatus,
        sub10RadioRmtTxPower,
        sub10RadioRmtRxPower,
        sub10RadioRmtVectErr,
        sub10RadioRmtLnkLoss,
        sub10RadioRmtAlignmentMode,
        sub10RadioRmtAFER
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioRemoteStatusGrp group"

    ::= { sub10MIBGroups 44 }

sub10RadioMgmtGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioMgmtTxPowerLimit,
        sub10RadioMgmtTxRxFreq,
        sub10RadioMgmtAPCMode,
        sub10RadioMgmtModulationMode,
        sub10RadioMgmtAmod,
        sub10RadioMgmtAlignmentMode,
        sub10RadioMgmtMWUChannelWidth,
        sub10RadioMgmtDataRate
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioMgmtGrp group"

    ::= { sub10MIBGroups 45 }

sub10RadioMgmtStatsGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioMgmtStats1dPersist
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioMgmtStatsGrp group"

    ::= { sub10MIBGroups 46 }

sub10RadioMgmtStatsActiveEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioMgmtStatsActiveName,
        sub10RadioMgmtStatsActiveState
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioMgmtStatsActiveEntryGrp group"

    ::= { sub10MIBGroups 47 }

sub10RadioStatsGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStatsTimeElapsed
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStatsGrp group"

    ::= { sub10MIBGroups 48 }

sub10RadioStatsCurrentGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStatsCurrTxPowerMin,
        sub10RadioStatsCurrTxPowerMax,
        sub10RadioStatsCurrTxPowerAvg,
        sub10RadioStatsCurrRxPowerMin,
        sub10RadioStatsCurrRxPowerMax,
        sub10RadioStatsCurrRxPowerAvg,
        sub10RadioStatsCurrVectErrMin,
        sub10RadioStatsCurrVectErrMax,
        sub10RadioStatsCurrVectErrAvg,
        sub10RadioStatsCurrLnkLossMin,
        sub10RadioStatsCurrLnkLossMax,
        sub10RadioStatsCurrLnkLossAvg,
        sub10RadioStatsCurrRxFrms,
        sub10RadioStatsCurrTxFrms,
        sub10RadioStatsCurrRxPkts,
        sub10RadioStatsCurrTxPkts,
        sub10RadioStatsCurrRxMgmtPkts,
        sub10RadioStatsCurrTxMgmtPkts,
        sub10RadioStatsCurrRxBadFrms,
        sub10RadioStatsCurrMWUTempMin,
        sub10RadioStatsCurrMWUTempMax,
        sub10RadioStatsCurrMWUTempAvg,
        sub10RadioStatsCurrQPSKTo8PSK,
        sub10RadioStatsCurr8PSKToQPSK,
        sub10RadioStatsCurrAFERMin,
        sub10RadioStatsCurrAFERMax,
        sub10RadioStatsCurrAFERAvg,
        sub10RadioStatsCurrRmtTxPowerAvg,
        sub10RadioStatsCurrRmtRxPowerAvg,
        sub10RadioStatsCurrRmtVectErrAvg,
        sub10RadioStatsCurrRmtLnkLossAvg,
        sub10RadioStatsCurrRmtMWUTempAvg,
        sub10RadioStatsCurrRmtAFERAvg,
        sub10RadioStatsCurrRxQPSK,
        sub10RadioStatsCurrRx8PSK,
        sub10RadioStatsCurrTxQPSK,
        sub10RadioStatsCurrTx8PSK
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStatsCurrentGrp group"

    ::= { sub10MIBGroups 49 }

sub10RadioStats1mHistoryGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStats1mHistIntvls
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStats1mHistoryGrp group"

    ::= { sub10MIBGroups 50 }

sub10RadioStats1mHistEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStats1mHistTime,
        sub10RadioStats1mHistTxPowerMin,
        sub10RadioStats1mHistTxPowerMax,
        sub10RadioStats1mHistTxPowerAvg,
        sub10RadioStats1mHistRxPowerMin,
        sub10RadioStats1mHistRxPowerMax,
        sub10RadioStats1mHistRxPowerAvg,
        sub10RadioStats1mHistVectErrMin,
        sub10RadioStats1mHistVectErrMax,
        sub10RadioStats1mHistVectErrAvg,
        sub10RadioStats1mHistLnkLossMin,
        sub10RadioStats1mHistLnkLossMax,
        sub10RadioStats1mHistLnkLossAvg,
        sub10RadioStats1mHistMWUTempMin,
        sub10RadioStats1mHistMWUTempMax,
        sub10RadioStats1mHistMWUTempAvg,
        sub10RadioStats1mHistRxFrms,
        sub10RadioStats1mHistTxFrms,
        sub10RadioStats1mHistRxPkts,
        sub10RadioStats1mHistTxPkts,
        sub10RadioStats1mHistRxMgmtPkts,
        sub10RadioStats1mHistTxMgmtPkts,
        sub10RadioStats1mHistRxBadFrms,
        sub10RadioStats1mHistQPSKTo8PSK,
        sub10RadioStats1mHist8PSKToQPSK,
        sub10RadioStats1mHistAFERMin,
        sub10RadioStats1mHistAFERMax,
        sub10RadioStats1mHistAFERAvg,
        sub10RadioStats1mHistRxQPSK,
        sub10RadioStats1mHistRx8PSK,
        sub10RadioStats1mHistTxQPSK,
        sub10RadioStats1mHistTx8PSK
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStats1mHistEntryGrp group"

    ::= { sub10MIBGroups 51 }

sub10RadioStats15mHistoryGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStats15mHistIntvls
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStats15mHistoryGrp group"

    ::= { sub10MIBGroups 52 }

sub10RadioStats15mHistEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStats15mHistTime,
        sub10RadioStats15mHistTxPowerMin,
        sub10RadioStats15mHistTxPowerMax,
        sub10RadioStats15mHistTxPowerAvg,
        sub10RadioStats15mHistRxPowerMin,
        sub10RadioStats15mHistRxPowerMax,
        sub10RadioStats15mHistRxPowerAvg,
        sub10RadioStats15mHistVectErrMin,
        sub10RadioStats15mHistVectErrMax,
        sub10RadioStats15mHistVectErrAvg,
        sub10RadioStats15mHistLnkLossMin,
        sub10RadioStats15mHistLnkLossMax,
        sub10RadioStats15mHistLnkLossAvg,
        sub10RadioStats15mHistMWUTempMin,
        sub10RadioStats15mHistMWUTempMax,
        sub10RadioStats15mHistMWUTempAvg,
        sub10RadioStats15mHistRxFrms,
        sub10RadioStats15mHistTxFrms,
        sub10RadioStats15mHistRxPkts,
        sub10RadioStats15mHistTxPkts,
        sub10RadioStats15mHistRxMgmtPkts,
        sub10RadioStats15mHistTxMgmtPkts,
        sub10RadioStats15mHistRxBadFrms,
        sub10RadioStats15mHistQPSKTo8PSK,
        sub10RadioStats15mHist8PSKToQPSK,
        sub10RadioStats15mHistAFERMin,
        sub10RadioStats15mHistAFERMax,
        sub10RadioStats15mHistAFERAvg,
        sub10RadioStats15mHistRxQPSK,
        sub10RadioStats15mHistRx8PSK,
        sub10RadioStats15mHistTxQPSK,
        sub10RadioStats15mHistTx8PSK
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStats15mHistEntryGrp group"

    ::= { sub10MIBGroups 53 }

sub10RadioStats1dHistoryGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStats1dHistIntvls
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStats1dHistoryGrp group"

    ::= { sub10MIBGroups 54 }

sub10RadioStats1dHistEntryGrp OBJECT-GROUP
    OBJECTS { 
        sub10RadioStats1dHistTime,
        sub10RadioStats1dHistTxPowerMin,
        sub10RadioStats1dHistTxPowerMax,
        sub10RadioStats1dHistTxPowerAvg,
        sub10RadioStats1dHistRxPowerMin,
        sub10RadioStats1dHistRxPowerMax,
        sub10RadioStats1dHistRxPowerAvg,
        sub10RadioStats1dHistVectErrMin,
        sub10RadioStats1dHistVectErrMax,
        sub10RadioStats1dHistVectErrAvg,
        sub10RadioStats1dHistLnkLossMin,
        sub10RadioStats1dHistLnkLossMax,
        sub10RadioStats1dHistLnkLossAvg,
        sub10RadioStats1dHistMWUTempMin,
        sub10RadioStats1dHistMWUTempMax,
        sub10RadioStats1dHistMWUTempAvg,
        sub10RadioStats1dHistRxFrms,
        sub10RadioStats1dHistTxFrms,
        sub10RadioStats1dHistRxPkts,
        sub10RadioStats1dHistTxPkts,
        sub10RadioStats1dHistRxMgmtPkts,
        sub10RadioStats1dHistTxMgmtPkts,
        sub10RadioStats1dHistRxBadFrms,
        sub10RadioStats1dHistQPSKTo8PSK,
        sub10RadioStats1dHist8PSKToQPSK,
        sub10RadioStats1dHistAFERMin,
        sub10RadioStats1dHistAFERMax,
        sub10RadioStats1dHistAFERAvg,
        sub10RadioStats1dHistRxQPSK,
        sub10RadioStats1dHistRx8PSK,
        sub10RadioStats1dHistTxQPSK,
        sub10RadioStats1dHistTx8PSK
    }
    STATUS         current
    DESCRIPTION    "This group defines objects that are members of the sub10RadioStats1dHistEntryGrp group"

    ::= { sub10MIBGroups 55 }


END

