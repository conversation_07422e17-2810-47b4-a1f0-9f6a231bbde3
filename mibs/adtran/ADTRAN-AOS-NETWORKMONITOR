       ADTRAN-AOS-NE<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>TOR DEFINITIONS ::= BEGIN

       IMPORTS
           TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, Integer32, Unsigned32, Counter32,
           Counter64, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE
               FROM SNMPv2-SMI
           DisplayString, RowStatus, TruthValue, TEXTUAL-CONVENTION
               FROM SNMPv2-TC
           MODULE-COMPLIANCE, OBJECT-G<PERSON><PERSON>, NOTIFICATION-GROUP
               FROM SNMPv2-CONF
           adIdentity
               FROM ADTRAN-MIB
           adGenAOSConformance, adGenAOSRouter
               FROM ADTRAN-AOS;

       adGenAOSNetMonMib MODULE-IDENTITY
        LAST-UPDATED "201010270000Z"  -- Oct 27, 2010 / YYYYMMDDHHMMZ
        ORGANIZATION "ADTRAN, Inc."
        CONTACT-INFO
               "Technical Support Dept.
                Postal: ADTRAN, Inc.
                901 Explorer Blvd.
                Huntsville, AL 35806

                Tel: ****** 726-8663
                Fax: ****** 963 6217
                E-mail: <EMAIL>"

            DESCRIPTION
                "This MIB contains network monitoring information."

            REVISION "201010270000Z"  -- Oct 27, 2010 / YYYYMMDDHHMMZ
            DESCRIPTION
                "Added adGenAOSnmTrackTraps and a limited RO adGenAOSnmTrackTable
                to allow OID support for the adGenAOSnmTrackStateChgFail
                and adGenAOSnmTrackStateChgPass trap additions.
                Changes by Joe Sieja."

            REVISION "200901200000Z"  -- Jan 20, 2009 / YYYYMMDDHHMMZ
            DESCRIPTION
                "Added adGenAOSnmICMPTSPktSentCount to the
                adGenAOSnmICMPTSHistoryTable and adGenAOSnmTwPktSentCount to
                the adGenAOSnmTwampHistoryTable.
                Changes by Reba Holland."

            REVISION "200809300000Z"  -- Sept 30, 2008 / YYYYMMDDHHMMZ
            DESCRIPTION
                "Added adGenAOSnmICMPTSResponderStatsTable and
                 adGenAOSnmTWAMPResponderStatsTable. These tables will be used
                 to retrieve the statistics available for enabled responders.
                 Changes by Reba Holland."

            REVISION "200808120000Z"  -- Aug 12, 2008 / YYYYMMDDHHMMZ
            DESCRIPTION
                "Changed adGenAOSNetMon to use adGenAOSnmIndex in all
                tables that were using adGenAOSnmName.  Added ICMP Timestamp
                and TWAMP responders. Removed adGenAOSnmICMPTSHistoryIndex and
                adGenAOSnmTwHistoryIndex from the adGenAOSnmICMPTSHistoryTable
                and adGenAOSnmTwampHistoryTable. Using adGenAOSnmICMPTsSeqNum
                and adGenAOSnmTwSeqNum as indices in these tables instead.
                Added adGenAOSnmProbeTableNextIndex. Changed adGenAOSnmIndex
                to have a maximum value of 1000.
                Changes by Reba Holland."

            REVISION "200806250000Z"  -- June 25, 2008 / YYYYMMDDHHMMZ
            DESCRIPTION
                "Changed adGenAOSNetMon to be under the adGenAOSRouter tree
                instead of the adGenAOSCommon tree.
                Changes by Reba Holland."


            REVISION    "200708230000Z"  -- August 23, 2007
            DESCRIPTION
                "First Draft of ADTRAN-AOS-NETWORKMONITOR MIB module."


          ::= { adIdentity 10000 53 2 2 }

      adGenAOSNetMon    OBJECT IDENTIFIER ::= { adGenAOSRouter 2 }

-- ========================================================================

     adGenAOSnmTrackTraps OBJECT IDENTIFIER ::= { adGenAOSNetMon 0 }

     adGenAOSnmProbeTableNextIndex OBJECT-TYPE
        SYNTAX      Integer32 (0..1000)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "This object contains an unused value for adGenAOSnmIndex in
         the adGenAOSnmProbeTable, or a zero to indicate that none exist."
        ::= { adGenAOSNetMon 1 }

     --

     --  Network Monitor Probe Table

     --  The variables that create a NetMon probe.

     adGenAOSnmProbeTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Create a network monitor probe."
         ::= { adGenAOSNetMon 2 }

     adGenAOSnmProbeEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The parameters for a particular network monitor probe."
         INDEX { adGenAOSnmIndex }
         ::= { adGenAOSnmProbeTable 1 }


     AdGenAOSnmProbeEntry ::=
         SEQUENCE {
             adGenAOSnmIndex                Integer32,
             adGenAOSnmName                 OCTET STRING,
             adGenAOSnmType                 INTEGER,
             adGenAOSnmRowStatus            RowStatus
     }


     adGenAOSnmIndex OBJECT-TYPE
         SYNTAX      Integer32 (0..1000)
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
                "Uniquely identifies a row in the adGenAOSnmProbeTable.
                This is the index chosen by the management station for
                row creation. If the adGenAOSnmIndex is already in use when
                attempting to create a new row with a set operation, then an
                error will be returned."
         ::= { adGenAOSnmProbeEntry 1 }



     adGenAOSnmName OBJECT-TYPE
         SYNTAX    OCTET STRING (SIZE(1..40))
         MAX-ACCESS read-create
         STATUS  current
         DESCRIPTION
            "Unique string to identify probe."
         ::= { adGenAOSnmProbeEntry 2 }


     adGenAOSnmType OBJECT-TYPE
         SYNTAX  INTEGER     {
                 icmpEcho (1),
                 tcpConnect (2),
                 httpRequest (3),
                 icmpTimeStamp (4),
                 twamp (5)

         }
         MAX-ACCESS  read-create
         STATUS  current
         DESCRIPTION
            "Specifies the type of probe."
         ::= { adGenAOSnmProbeEntry 3 }


     adGenAOSnmRowStatus OBJECT-TYPE
         SYNTAX   RowStatus
         MAX-ACCESS   read-create
         STATUS   current
         DESCRIPTION
            "SNMP Version 2 Row Status Variable. The status of the conceptual
            row. In order for this object to become active, the following
            row objects MUST be defined: adGenAOSnmName and adGenAOSnmType.
            The control row objects adGenAOSnmName and adGenAOSnmType cannot be
            modified once this conceptual row has been created. The value
            'notInService' and 'createAndWait' will not be supported. This
            object can be set to 'destroy' from any value at any time."
         ::= { adGenAOSnmProbeEntry 4 }

     --

     --  Network Monitor Configure Probe Table

     --  The variables that configure a NetMon probe.

     adGenAOSnmConfigProbeTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmConfigProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Configure a network monitor probe."
         ::= { adGenAOSNetMon 3 }

     adGenAOSnmConfigProbeEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmConfigProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The parameters used to configure a particular network monitor
            probe."
         INDEX { adGenAOSnmIndex }
         ::= { adGenAOSnmConfigProbeTable 1 }


     AdGenAOSnmConfigProbeEntry ::=
         SEQUENCE {
             adGenAOSnmCfgName              OCTET STRING,
             adGenAOSnmAdminStatus          INTEGER,
             adGenAOSnmPollPeriod           Integer32,
             adGenAOSnmTimeoutPeriod        Unsigned32,
             adGenAOSnmToleranceMode        INTEGER,
             adGenAOSnmFailTolerance        INTEGER,
             adGenAOSnmPassTolerance        INTEGER,
             adGenAOSnmToleranceTestSize    INTEGER,
             adGenAOSnmClearCounters        INTEGER
     }

     adGenAOSnmCfgName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding probe entry. The probe identified by
            a particular value of this index is the same probe as identified
            by the same value as a adGenAOSnmName object instance."
         ::= { adGenAOSnmConfigProbeEntry 1 }

     adGenAOSnmAdminStatus OBJECT-TYPE
        SYNTAX  INTEGER
                {
                  enabled (1),
                  disabled (2)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies ability to enable or disable the probe."
         ::= { adGenAOSnmConfigProbeEntry 2 }

     adGenAOSnmPollPeriod OBJECT-TYPE
        SYNTAX   Integer32 (0..65535)
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the poll period, in seconds, of the probe."
         ::= { adGenAOSnmConfigProbeEntry 3 }

     adGenAOSnmTimeoutPeriod OBJECT-TYPE
        SYNTAX   Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Time to wait for a response after which a packet is considered
            lost."
         ::= { adGenAOSnmConfigProbeEntry 4 }

     adGenAOSnmToleranceMode OBJECT-TYPE
        SYNTAX  INTEGER
                {
                  none (1),
                  rate (2),
                  consecutive (3)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the tolerance mode of the probe."
         ::= { adGenAOSnmConfigProbeEntry 5 }

     adGenAOSnmFailTolerance OBJECT-TYPE
        SYNTAX  INTEGER (1..254)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies how many probe operations must fail before the probe
            state transitions to the fail state."
         ::= { adGenAOSnmConfigProbeEntry 6 }

     adGenAOSnmPassTolerance OBJECT-TYPE
        SYNTAX  INTEGER (1..255)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies how many probe operations must fail before the probe
            state transitions to the fail state."
         ::= { adGenAOSnmConfigProbeEntry 7 }

     adGenAOSnmToleranceTestSize OBJECT-TYPE
        SYNTAX  INTEGER (1..255)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies test size for rate calculations when the tolerance mode
             is set to 'rate'."
         ::= { adGenAOSnmConfigProbeEntry 8 }

     adGenAOSnmClearCounters OBJECT-TYPE
        SYNTAX INTEGER {
                clear (1)
        }
        MAX-ACCESS  read-write
         STATUS  current
         DESCRIPTION
            "Clear the counters and statistics history of a probe.
            This is a write-only variable. A read will result in no action
            being taken."
         ::= { adGenAOSnmConfigProbeEntry 9 }

     --

     --  Network Monitor Probe Status Table

     --  The variables that display the status of a NetMon probe.

     adGenAOSnmProbeStatusTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmProbeStatusEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "View the status of a network monitor probe."
         ::= { adGenAOSNetMon 4 }

     adGenAOSnmProbeStatusEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmProbeStatusEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The parameters used to view the status of a particular network
            monitor probe."
         INDEX { adGenAOSnmIndex }
         ::= { adGenAOSnmProbeStatusTable 1 }


     AdGenAOSnmProbeStatusEntry ::=
         SEQUENCE {
             adGenAOSnmStatusName                   OCTET STRING,
             adGenAOSnmTestStatus                   INTEGER,
             adGenAOSnmTestsRun                     Counter32,
             adGenAOSnmTestsFailed                  Counter32,
             adGenAOSnmStatsToleranceTestSize       Counter32,
             adGenAOSnmStatsToleranceTestValue      Counter32,
             adGenAOSnmTimeSinceLastStatusChange    TimeTicks
     }

     adGenAOSnmStatusName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding probe entry. The probe identified by
            a particular value of this index is the same probe as identified
            by the same value as a adGenAOSnmName object instance."
         ::= { adGenAOSnmProbeStatusEntry 1 }

     adGenAOSnmTestStatus OBJECT-TYPE
        SYNTAX  INTEGER
                {
                  fail (1),
                  pass (2)
                }
         MAX-ACCESS read-only
         STATUS  current
         DESCRIPTION
            "Current test state of the probe."
         ::= { adGenAOSnmProbeStatusEntry 2 }

     adGenAOSnmTestsRun OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
            "Number of test run on the probe."
         ::= { adGenAOSnmProbeStatusEntry 3 }

     adGenAOSnmTestsFailed OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
            "Number of test that failed."
         ::= { adGenAOSnmProbeStatusEntry 4 }

     adGenAOSnmStatsToleranceTestSize OBJECT-TYPE
        SYNTAX  Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
            "Specifies the test size."
         ::= { adGenAOSnmProbeStatusEntry 5 }

     adGenAOSnmStatsToleranceTestValue OBJECT-TYPE
        SYNTAX  Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
            "Specifies number of tests that passed or failed.
            Check 'adGenAOSnmTestStatus' for test state."
         ::= { adGenAOSnmProbeStatusEntry 6 }

     adGenAOSnmTimeSinceLastStatusChange OBJECT-TYPE
        SYNTAX   TimeTicks
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
            "Time since last status change."
         ::= { adGenAOSnmProbeStatusEntry 7 }


     --

     --  Network Monitor Configure TWAMP Probe Table

     --  Used to configure TWAMP-specific probe parameters.

     adGenAOSnmCfgTwampProbeTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmCfgTwampProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Configure a network monitor TWAMP probe."
         ::= { adGenAOSNetMon 5 }

     adGenAOSnmCfgTwampProbeEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmCfgTwampProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The parameters used to configure a particular network monitor
            TWAMP probe."
         INDEX { adGenAOSnmIndex }
         ::= { adGenAOSnmCfgTwampProbeTable 1 }


     AdGenAOSnmCfgTwampProbeEntry ::=
         SEQUENCE {
             adGenAOSnmCfgTwName                        OCTET STRING,
             adGenAOSnmTwDestHostname                   DisplayString,
             adGenAOSnmTwDestPort                       INTEGER,
             adGenAOSnmTwSrcIP                          IpAddress,
             adGenAOSnmTwSrcPort                        INTEGER,
             adGenAOSnmTwDscp                           INTEGER,
             adGenAOSnmTwPaddingLen                     INTEGER,
             adGenAOSnmTwPaddingFormat                  INTEGER,
             adGenAOSnmTwPaddingPattern                 OCTET STRING,
             adGenAOSnmTwDataPadType                    INTEGER,
             adGenAOSnmTwPktSendCnt                     INTEGER,
             adGenAOSnmTwSendScheduleType               INTEGER,
             adGenAOSnmTwSendScheduleValue              INTEGER,
             adGenAOSnmTwIpdvAbsInMinFail               Unsigned32,
             adGenAOSnmTwIpdvAbsInAvgFail               Unsigned32,
             adGenAOSnmTwIpdvAbsInMaxFail               Unsigned32,
             adGenAOSnmTwIpdvAbsInMinPass               Unsigned32,
             adGenAOSnmTwIpdvAbsInAvgPass               Unsigned32,
             adGenAOSnmTwIpdvAbsInMaxPass               Unsigned32,
             adGenAOSnmTwIpdvAbsOutMinFail              Unsigned32,
             adGenAOSnmTwIpdvAbsOutAvgFail              Unsigned32,
             adGenAOSnmTwIpdvAbsOutMaxFail              Unsigned32,
             adGenAOSnmTwIpdvAbsOutMinPass              Unsigned32,
             adGenAOSnmTwIpdvAbsOutAvgPass              Unsigned32,
             adGenAOSnmTwIpdvAbsOutMaxPass              Unsigned32,
             adGenAOSnmTwIpdvAbsRtMinFail               Unsigned32,
             adGenAOSnmTwIpdvAbsRtAvgFail               Unsigned32,
             adGenAOSnmTwIpdvAbsRtMaxFail               Unsigned32,
             adGenAOSnmTwIpdvAbsRtMinPass               Unsigned32,
             adGenAOSnmTwIpdvAbsRtAvgPass               Unsigned32,
             adGenAOSnmTwIpdvAbsRtMaxPass               Unsigned32,
             adGenAOSnmTwDelayInMinFail                 Integer32,
             adGenAOSnmTwDelayInAvgFail                 Integer32,
             adGenAOSnmTwDelayInMaxFail                 Integer32,
             adGenAOSnmTwDelayInMinPass                 Integer32,
             adGenAOSnmTwDelayInAvgPass                 Integer32,
             adGenAOSnmTwDelayInMaxPass                 Integer32,
             adGenAOSnmTwDelayOutMinFail                Integer32,
             adGenAOSnmTwDelayOutAvgFail                Integer32,
             adGenAOSnmTwDelayOutMaxFail                Integer32,
             adGenAOSnmTwDelayOutMinPass                Integer32,
             adGenAOSnmTwDelayOutAvgPass                Integer32,
             adGenAOSnmTwDelayOutMaxPass                Integer32,
             adGenAOSnmTwDelayRtMinFail                 Integer32,
             adGenAOSnmTwDelayRtAvgFail                 Integer32,
             adGenAOSnmTwDelayRtMaxFail                 Integer32,
             adGenAOSnmTwDelayRtMinPass                 Integer32,
             adGenAOSnmTwDelayRtAvgPass                 Integer32,
             adGenAOSnmTwDelayRtMaxPass                 Integer32,
             adGenAOSnmTwPktRtLossFail                  INTEGER ,
             adGenAOSnmTwPktRtLossPass                  INTEGER ,
             adGenAOSnmTwHistoryDepth                   INTEGER


     }

     adGenAOSnmCfgTwName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding TWAMP probe entry. The probe
            identified by a particular instance of this object is the same
            probe as identified by the same value as a adGenAOSnmName
            object instance."
         ::= { adGenAOSnmCfgTwampProbeEntry 1 }

     adGenAOSnmTwDestHostname OBJECT-TYPE
         SYNTAX     DisplayString
         MAX-ACCESS   read-write
         STATUS   current
         DESCRIPTION
            "Specifies the destination hostname or IP address as a string in
            dotted decimal format."
         ::= { adGenAOSnmCfgTwampProbeEntry 2 }

     adGenAOSnmTwDestPort OBJECT-TYPE
        SYNTAX  INTEGER (0..65535)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the destination port. If zero then the actual destination
            port will be dynamically negotiated with the responder."
         ::= { adGenAOSnmCfgTwampProbeEntry 3 }

     adGenAOSnmTwSrcIP OBJECT-TYPE
       SYNTAX     IpAddress
       MAX-ACCESS read-write
       STATUS	  current
       DESCRIPTION
               "Specifies the source IPv4 address."
         ::= { adGenAOSnmCfgTwampProbeEntry 4 }

     adGenAOSnmTwSrcPort OBJECT-TYPE
        SYNTAX  INTEGER (0..65535)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the source port. If zero then the actual source port will
            be dynamically assigned ever probe operation."
         ::= { adGenAOSnmCfgTwampProbeEntry 5 }

     adGenAOSnmTwDscp OBJECT-TYPE
        SYNTAX  INTEGER (0..63)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the DiffServ Code Point value that will be used on
            packets sent and will be negotiated with the responder."
         ::= { adGenAOSnmCfgTwampProbeEntry 6 }

     adGenAOSnmTwPaddingLen OBJECT-TYPE
        SYNTAX  INTEGER (0..1462)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Size of TWAMP padding length."
         ::= { adGenAOSnmCfgTwampProbeEntry 7 }

     adGenAOSnmTwPaddingFormat OBJECT-TYPE
        SYNTAX  INTEGER
                {
                        ascii (1),
                        hex (2)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the format of the padding pattern."
         ::= { adGenAOSnmCfgTwampProbeEntry 8 }

     adGenAOSnmTwPaddingPattern OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..80))
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies data pattern to pad the packet. This can be an ascii
            or hexadecimal pattern and must match the type set in
            adGenAOSnmTwPktFormat. In addition, adGenAOSnmTwDataPadType must be
            set to 'custom'. "
         ::= { adGenAOSnmCfgTwampProbeEntry 9 }

     adGenAOSnmTwDataPadType OBJECT-TYPE
        SYNTAX  INTEGER
                {
                  zeroes (1),
                  random (2),
                  custom (3)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies how the TWAMP padding payload is filled."
         ::= { adGenAOSnmCfgTwampProbeEntry 10 }


     adGenAOSnmTwPktSendCnt OBJECT-TYPE
        SYNTAX  INTEGER (1..1000)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the number of packets to send and receive for one probe
            operation.  More than one is required for inter-packet delay
            variation measurements."
         ::= { adGenAOSnmCfgTwampProbeEntry 11 }

     adGenAOSnmTwSendScheduleType OBJECT-TYPE
        SYNTAX  INTEGER {
                        periodic (1),
                        poisson (2)   -- for future use, not currently supported
                       }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies in milliseconds the time between test packets during a
            single probe operation."
         ::= { adGenAOSnmCfgTwampProbeEntry 12 }

     adGenAOSnmTwSendScheduleValue OBJECT-TYPE
        SYNTAX  INTEGER (5..5000)
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies in milliseconds the time between start of each packet
            send operation."
         ::= { adGenAOSnmCfgTwampProbeEntry 13 }

     adGenAOSnmTwIpdvAbsInMinFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the minimum inbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 14 }

     adGenAOSnmTwIpdvAbsInAvgFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the average inbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 15 }

     adGenAOSnmTwIpdvAbsInMaxFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the maximum inbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 16 }

     adGenAOSnmTwIpdvAbsInMinPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for minimum inbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 17 }

     adGenAOSnmTwIpdvAbsInAvgPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for average inbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 18 }

     adGenAOSnmTwIpdvAbsInMaxPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for maximum inbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 19 }

     adGenAOSnmTwIpdvAbsOutMinFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the minimum outbound absolute-value inter-packet
            delay variation threshold in milliseconds that will cause the
            probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 20 }

     adGenAOSnmTwIpdvAbsOutAvgFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the average outbound absolute-value inter-packet
            delay variation threshold in milliseconds that will cause the
            probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 21 }

     adGenAOSnmTwIpdvAbsOutMaxFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the maximum outbound absolute-value inter-packet
            delay variation threshold in milliseconds that will cause the
            probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 22 }

     adGenAOSnmTwIpdvAbsOutMinPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for minimum outbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 23 }

     adGenAOSnmTwIpdvAbsOutAvgPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for average outbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 24 }

     adGenAOSnmTwIpdvAbsOutMaxPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for maximum outbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 25 }

     adGenAOSnmTwIpdvAbsRtMinFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the minimum round-trip absolute-value inter-packet
            delay variation threshold in milliseconds that will cause the
            probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 26 }

     adGenAOSnmTwIpdvAbsRtAvgFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the average round-trip absolute-value inter-packet
            delay variation threshold in milliseconds that will cause the
            probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 27 }

     adGenAOSnmTwIpdvAbsRtMaxFail OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the maximum round-trip absolute-value inter-packet
            delay variation threshold in milliseconds that will cause the
            probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 28 }

     adGenAOSnmTwIpdvAbsRtMinPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for minimum round-trip
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 29 }

     adGenAOSnmTwIpdvAbsRtAvgPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for average round-trip
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 30 }

     adGenAOSnmTwIpdvAbsRtMaxPass OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for maximum round-trip
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 31 }

     adGenAOSnmTwDelayInMinFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the minimum inbound delay threshold in milliseconds that
            will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 32 }

     adGenAOSnmTwDelayInAvgFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the average inbound delay threshold in milliseconds that
            will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 33 }

     adGenAOSnmTwDelayInMaxFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the maximum inbound delay threshold in milliseconds that
            will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 34 }

     adGenAOSnmTwDelayInMinPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for minimum inbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 35 }

     adGenAOSnmTwDelayInAvgPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for average inbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 36 }

     adGenAOSnmTwDelayInMaxPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for maximum inbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 37 }

     adGenAOSnmTwDelayOutMinFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the minimum outbound delay threshold in milliseconds that
            will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 38 }

     adGenAOSnmTwDelayOutAvgFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the average outbound delay threshold in milliseconds that
            will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 39 }

     adGenAOSnmTwDelayOutMaxFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the maximum outbound delay threshold in milliseconds that
            will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 40 }

     adGenAOSnmTwDelayOutMinPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for minimum outbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 41 }

     adGenAOSnmTwDelayOutAvgPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for average outbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 42 }

     adGenAOSnmTwDelayOutMaxPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for maximum outbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 43 }

     adGenAOSnmTwDelayRtMinFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the minimum round-trip delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 44 }

     adGenAOSnmTwDelayRtAvgFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the average round-trip delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 45 }

     adGenAOSnmTwDelayRtMaxFail OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the maximum round-trip delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 46 }

     adGenAOSnmTwDelayRtMinPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for minimum round-trip
            delay that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 47 }

     adGenAOSnmTwDelayRtAvgPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for average round-trip
            delay that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 48 }

     adGenAOSnmTwDelayRtMaxPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the threshold in milliseconds for maximum round-trip
            delay that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgTwampProbeEntry 49 }

     adGenAOSnmTwPktRtLossFail OBJECT-TYPE
        SYNTAX  INTEGER (0..1000)
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the round-trip packet-loss threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgTwampProbeEntry 50 }

     adGenAOSnmTwPktRtLossPass OBJECT-TYPE
        SYNTAX  INTEGER (0..1000)
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the round-trip packet-loss threshold in milliseconds
            that will allow the probe operation to change to the pass state."
         ::= { adGenAOSnmCfgTwampProbeEntry 51 }

     adGenAOSnmTwHistoryDepth OBJECT-TYPE
        SYNTAX  INTEGER (1..127)
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies the number of probe operation statistic results to keep."
         ::= { adGenAOSnmCfgTwampProbeEntry 52 }



     --

     --  Network Monitor TWAMP History Table

     --  The variables that display the history for a NetMon TWAMP probe.

     adGenAOSnmTwampHistoryTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmTwampHistoryEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Operation history for a network monitor TWAMP probe."
         ::= { adGenAOSNetMon 6 }

     adGenAOSnmTwampHistoryEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmTwampHistoryEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The read-only history of a particular network monitor TWAMP probe."
         INDEX { adGenAOSnmIndex, adGenAOSnmTwSeqNum }
         ::= { adGenAOSnmTwampHistoryTable 1 }


     AdGenAOSnmTwampHistoryEntry ::=
         SEQUENCE {
             adGenAOSnmTwSeqNum              Integer32,
             adGenAOSnmTwHistoryName         OCTET STRING,
             adGenAOSnmTwStartTime           DisplayString,
             adGenAOSnmTwEndTime             DisplayString,
             adGenAOSnmTwLocalSyncState      TruthValue,
             adGenAOSnmTwLocalClkErr         Counter64,
             adGenAOSnmTwRemoteSyncState     TruthValue,
             adGenAOSnmTwRemoteClkErr        Counter64,
             adGenAOSnmTwDelayInMin          Integer32,
             adGenAOSnmTwDelayInMax          Integer32,
             adGenAOSnmTwDelayOutMin         Integer32,
             adGenAOSnmTwDelayOutMax         Integer32,
             adGenAOSnmTwDelayRtMin          Integer32,
             adGenAOSnmTwDelayRtMax          Integer32,
             adGenAOSnmTwLossRoundTrip       Counter32,
             adGenAOSnmTwDelayOutSum         Integer32,
             adGenAOSnmTwDelayOutSum2        Counter64,
             adGenAOSnmTwDelayOutNum         Counter32,
             adGenAOSnmTwDelayInSum          Integer32,
             adGenAOSnmTwDelayInSum2         Counter64,
             adGenAOSnmTwDelayInNum          Counter32,
             adGenAOSnmTwDelayRtSum          Integer32,
             adGenAOSnmTwDelayRtSum2         Counter64,
             adGenAOSnmTwDelayRtNum          Counter32,
             adGenAOSnmTwIpvPosInMin         Counter32,
             adGenAOSnmTwIpvPosInMax         Counter32,
             adGenAOSnmTwIpvPosInSum         Counter32,
             adGenAOSnmTwIpvPosInSum2        Counter64,
             adGenAOSnmTwIpvPosInNum         Counter32,
             adGenAOSnmTwIpvPosOutMin        Counter32,
             adGenAOSnmTwIpvPosOutMax        Counter32,
             adGenAOSnmTwIpvPosOutSum        Counter32,
             adGenAOSnmTwIpvPosOutSum2       Counter64,
             adGenAOSnmTwIpvPosOutNum        Counter32,
             adGenAOSnmTwIpvPosRtMin         Counter32,
             adGenAOSnmTwIpvPosRtMax         Counter32,
             adGenAOSnmTwIpvPosRtSum         Counter32,
             adGenAOSnmTwIpvPosRtSum2        Counter64,
             adGenAOSnmTwIpvPosRtNum         Counter32,
             adGenAOSnmTwIpvNegInMin         Counter32,
             adGenAOSnmTwIpvNegInMax         Counter32,
             adGenAOSnmTwIpvNegInSum         Counter32,
             adGenAOSnmTwIpvNegInSum2        Counter64,
             adGenAOSnmTwIpvNegInNum         Counter32,
             adGenAOSnmTwIpvNegOutMin        Counter32,
             adGenAOSnmTwIpvNegOutMax        Counter32,
             adGenAOSnmTwIpvNegOutSum        Counter32,
             adGenAOSnmTwIpvNegOutSum2       Counter64,
             adGenAOSnmTwIpvNegOutNum        Counter32,
             adGenAOSnmTwIpvNegRtMin         Counter32,
             adGenAOSnmTwIpvNegRtMax         Counter32,
             adGenAOSnmTwIpvNegRtSum         Counter32,
             adGenAOSnmTwIpvNegRtSum2        Counter64,
             adGenAOSnmTwIpvNegRtNum         Counter32,
             adGenAOSnmTwIpvAbsInMin         Counter32,
             adGenAOSnmTwIpvAbsInMax         Counter32,
             adGenAOSnmTwIpvAbsInSum         Counter32,
             adGenAOSnmTwIpvAbsInSum2        Counter64,
             adGenAOSnmTwIpvAbsInNum         Counter32,
             adGenAOSnmTwIpvAbsOutMin        Counter32,
             adGenAOSnmTwIpvAbsOutMax        Counter32,
             adGenAOSnmTwIpvAbsOutSum        Counter32,
             adGenAOSnmTwIpvAbsOutSum2       Counter64,
             adGenAOSnmTwIpvAbsOutNum        Counter32,
             adGenAOSnmTwIpvAbsRtMin         Counter32,
             adGenAOSnmTwIpvAbsRtMax         Counter32,
             adGenAOSnmTwIpvAbsRtSum         Counter32,
             adGenAOSnmTwIpvAbsRtSum2        Counter64,
             adGenAOSnmTwIpvAbsRtNum         Counter32,
             adGenAOSnmTwPktSentCount        Counter32
        }

     adGenAOSnmTwSeqNum OBJECT-TYPE
        SYNTAX   Integer32 (1..120)
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Along with adGenAOSnmTwHistoryName, uniquely identifies a
                row in the adGenAOSTwampHistoryTable.  A sequence number of
                '1' signifies the current history entry."
         ::= { adGenAOSnmTwampHistoryEntry 1 }

     adGenAOSnmTwHistoryName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding TWAMP probe entry. The probe
            identified by a particular value of this object is the same probe as
            identified by the same value as a adGenAOSnmName object instance."
         ::= { adGenAOSnmTwampHistoryEntry 2 }

     adGenAOSnmTwStartTime OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Date and time the probe operation started."
         ::= { adGenAOSnmTwampHistoryEntry 3 }

     adGenAOSnmTwEndTime OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Date and time the probe operation ended."
         ::= { adGenAOSnmTwampHistoryEntry 4 }

     adGenAOSnmTwLocalSyncState OBJECT-TYPE
        SYNTAX   TruthValue
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "If return value is true, local clock is in sync."
         ::= { adGenAOSnmTwampHistoryEntry 5 }

     adGenAOSnmTwLocalClkErr OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Local clock error estimate in nanoseconds."
         ::= { adGenAOSnmTwampHistoryEntry 6 }

     adGenAOSnmTwRemoteSyncState OBJECT-TYPE
        SYNTAX   TruthValue
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "If return value is true, remote clock is in sync."
         ::= { adGenAOSnmTwampHistoryEntry 7 }

     adGenAOSnmTwRemoteClkErr OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Remote clock error estimate in nanoseconds."
         ::= { adGenAOSnmTwampHistoryEntry 8 }

     adGenAOSnmTwDelayInMin OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The inbound minimum delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 9 }

     adGenAOSnmTwDelayInMax OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The inbound maximum delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 10 }

     adGenAOSnmTwDelayOutMin OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The outbound minimum delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 11 }

     adGenAOSnmTwDelayOutMax OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The outbound maximum delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 12 }

     adGenAOSnmTwDelayRtMin OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The round-trip minimum delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 13 }

     adGenAOSnmTwDelayRtMax OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The round-trip maximum delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 14 }

      adGenAOSnmTwLossRoundTrip OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The round-trip packet loss."
         ::= { adGenAOSnmTwampHistoryEntry 15 }

      adGenAOSnmTwDelayOutSum OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of outbound delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 16 }

      adGenAOSnmTwDelayOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of outbound delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 17 }

      adGenAOSnmTwDelayOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of individual outbound delay samples."
         ::= { adGenAOSnmTwampHistoryEntry 18 }

      adGenAOSnmTwDelayInSum OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of inbound delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 19 }

      adGenAOSnmTwDelayInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of inbound delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 20 }

      adGenAOSnmTwDelayInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of individual inbound delay samples."
         ::= { adGenAOSnmTwampHistoryEntry 21 }

      adGenAOSnmTwDelayRtSum OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of round-trip delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 22 }

      adGenAOSnmTwDelayRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of round-trip delay in milliseconds."
         ::= { adGenAOSnmTwampHistoryEntry 23 }

      adGenAOSnmTwDelayRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of individual round-trip delay samples."
         ::= { adGenAOSnmTwampHistoryEntry 24 }

      adGenAOSnmTwIpvPosInMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of positive inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 25 }

      adGenAOSnmTwIpvPosInMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of positive inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 26 }

      adGenAOSnmTwIpvPosInSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of positive inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 27 }

      adGenAOSnmTwIpvPosInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of positive inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 28 }

      adGenAOSnmTwIpvPosInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of positive inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 29 }

      adGenAOSnmTwIpvPosOutMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of positive outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 30 }

      adGenAOSnmTwIpvPosOutMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of positive outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 31 }

      adGenAOSnmTwIpvPosOutSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of positive outbound inter-packet delay variation
                 measurements."
         ::= { adGenAOSnmTwampHistoryEntry 32 }

      adGenAOSnmTwIpvPosOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of positive outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 33 }

      adGenAOSnmTwIpvPosOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of positive outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 34 }

      adGenAOSnmTwIpvPosRtMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of positive round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 35 }

      adGenAOSnmTwIpvPosRtMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of positive round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 36 }

      adGenAOSnmTwIpvPosRtSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of positive round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 37 }

      adGenAOSnmTwIpvPosRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of of positive round-trip inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 38 }

      adGenAOSnmTwIpvPosRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of positive round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 39 }

      adGenAOSnmTwIpvNegInMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of negative inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 40 }

      adGenAOSnmTwIpvNegInMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of negative inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 41 }

      adGenAOSnmTwIpvNegInSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of negative inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 42 }

      adGenAOSnmTwIpvNegInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of negative inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 43 }

      adGenAOSnmTwIpvNegInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of negative inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 44 }

      adGenAOSnmTwIpvNegOutMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of negative outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 45 }

      adGenAOSnmTwIpvNegOutMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of negative outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 46 }

      adGenAOSnmTwIpvNegOutSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of negative outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 47 }

      adGenAOSnmTwIpvNegOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of negative outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 48 }

      adGenAOSnmTwIpvNegOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of negative outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 49 }

      adGenAOSnmTwIpvNegRtMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of negative round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 50 }

      adGenAOSnmTwIpvNegRtMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of negative round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 51 }

      adGenAOSnmTwIpvNegRtSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of negative round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 52 }

      adGenAOSnmTwIpvNegRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of negative round-trip inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 53 }

      adGenAOSnmTwIpvNegRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of negative round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 54 }

      adGenAOSnmTwIpvAbsInMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of absolute inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 55 }

      adGenAOSnmTwIpvAbsInMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of absolute inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 56 }

      adGenAOSnmTwIpvAbsInSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of absolute inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 57 }

      adGenAOSnmTwIpvAbsInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of absolute inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 58 }

      adGenAOSnmTwIpvAbsInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of absolute inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 59 }

      adGenAOSnmTwIpvAbsOutMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of absolute outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 60 }

      adGenAOSnmTwIpvAbsOutMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of absolute outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 61 }

      adGenAOSnmTwIpvAbsOutSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of absolute outbound inter-packet delay variation
                 measurements."
         ::= { adGenAOSnmTwampHistoryEntry 62 }

      adGenAOSnmTwIpvAbsOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of absolute outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 63 }

      adGenAOSnmTwIpvAbsOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of absolute outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 64 }

      adGenAOSnmTwIpvAbsRtMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of absolute round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 65 }

      adGenAOSnmTwIpvAbsRtMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of absolute round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 66 }

      adGenAOSnmTwIpvAbsRtSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of absolute round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 67 }

      adGenAOSnmTwIpvAbsRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of absolute round-trip inter-packet
                delay variation measurements."
         ::= { adGenAOSnmTwampHistoryEntry 68 }

      adGenAOSnmTwIpvAbsRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of absolute round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmTwampHistoryEntry 69 }

      adGenAOSnmTwPktSentCount  OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of packets sent."
         ::= { adGenAOSnmTwampHistoryEntry 70 }
     --

     --  Network Monitor Configure ICMP Timestamp Probe Table

     --  The variables that configure a NetMon ICMP Timestamp probe.

     adGenAOSnmCfgICMPTSProbeTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmCfgICMPTSProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Configure a network monitor ICMP timestamp probe."
         ::= { adGenAOSNetMon 7 }

     adGenAOSnmCfgICMPTSProbeEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmCfgICMPTSProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The parameters used to configure a particular network monitor ICMP
            timestamp probe."
         INDEX { adGenAOSnmIndex }
         ::= { adGenAOSnmCfgICMPTSProbeTable 1 }


     AdGenAOSnmCfgICMPTSProbeEntry ::=
         SEQUENCE {
             adGenAOSnmCfgICMPTSName                    OCTET STRING,
             adGenAOSnmICMPTSDestHostname               DisplayString,
             adGenAOSnmICMPTSSrcIP                      IpAddress,
             adGenAOSnmICMPTSDscp                       INTEGER,
             adGenAOSnmICMPTSPaddingLen                 INTEGER,
             adGenAOSnmICMPTSPaddingFormat              INTEGER,
             adGenAOSnmICMPTSPaddingPattern             OCTET STRING,
             adGenAOSnmICMPTSDataPadType                INTEGER,
             adGenAOSnmICMPTSPktSendCnt                 INTEGER,
             adGenAOSnmICMPTSSendScheduleType           INTEGER,
             adGenAOSnmICMPTSSendScheduleValue          INTEGER,
             adGenAOSnmICMPTSIpdvAbsInMinFail           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsInAvgFail           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsInMaxFail           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsInMinPass           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsInAvgPass           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsInMaxPass           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsOutMinFail          Unsigned32,
             adGenAOSnmICMPTSIpdvAbsOutAvgFail          Unsigned32,
             adGenAOSnmICMPTSIpdvAbsOutMaxFail          Unsigned32,
             adGenAOSnmICMPTSIpdvAbsOutMinPass          Unsigned32,
             adGenAOSnmICMPTSIpdvAbsOutAvgPass          Unsigned32,
             adGenAOSnmICMPTSIpdvAbsOutMaxPass          Unsigned32,
             adGenAOSnmICMPTSIpdvAbsRtMinFail           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsRtAvgFail           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsRtMaxFail           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsRtMinPass           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsRtAvgPass           Unsigned32,
             adGenAOSnmICMPTSIpdvAbsRtMaxPass           Unsigned32,
             adGenAOSnmICMPTSDelayInMinFail             Integer32,
             adGenAOSnmICMPTSDelayInAvgFail             Integer32,
             adGenAOSnmICMPTSDelayInMaxFail             Integer32,
             adGenAOSnmICMPTSDelayInMinPass             Integer32,
             adGenAOSnmICMPTSDelayInAvgPass             Integer32,
             adGenAOSnmICMPTSDelayInMaxPass             Integer32,
             adGenAOSnmICMPTSDelayOutMinFail            Integer32,
             adGenAOSnmICMPTSDelayOutAvgFail            Integer32,
             adGenAOSnmICMPTSDelayOutMaxFail            Integer32,
             adGenAOSnmICMPTSDelayOutMinPass            Integer32,
             adGenAOSnmICMPTSDelayOutAvgPass            Integer32,
             adGenAOSnmICMPTSDelayOutMaxPass            Integer32,
             adGenAOSnmICMPTSDelayRtMinFail             Integer32,
             adGenAOSnmICMPTSDelayRtAvgFail             Integer32,
             adGenAOSnmICMPTSDelayRtMaxFail             Integer32,
             adGenAOSnmICMPTSDelayRtMinPass             Integer32,
             adGenAOSnmICMPTSDelayRtAvgPass             Integer32,
             adGenAOSnmICMPTSDelayRtMaxPass             Integer32,
             adGenAOSnmICMPTSPktRtLossFail              INTEGER,
             adGenAOSnmICMPTSPktRtLossPass              INTEGER,
             adGenAOSnmICMPTSHistoryDepth               INTEGER
        }

     adGenAOSnmCfgICMPTSName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding ICMP timestamp probe entry.
            The probe identified by a particular value of this object is the
            same probe as identified by the same value as a
            adGenAOSnmName object instance."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 1 }

     adGenAOSnmICMPTSDestHostname OBJECT-TYPE
         SYNTAX     DisplayString
         MAX-ACCESS   read-write
         STATUS   current
         DESCRIPTION
            "Specifies the destination hostname or IP address as a string in
            dotted decimal format."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 2 }

     adGenAOSnmICMPTSSrcIP OBJECT-TYPE
       SYNTAX     IpAddress
       MAX-ACCESS read-write
       STATUS	  current
       DESCRIPTION
               "Specifies the source IPv4 address."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 3 }

     adGenAOSnmICMPTSDscp OBJECT-TYPE
        SYNTAX  INTEGER (0..63)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the DiffServ Code Point value that will be used on
            packets sent and will be negotiated with the responder."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 4 }

     adGenAOSnmICMPTSPaddingLen OBJECT-TYPE
        SYNTAX  INTEGER (0..1462)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Size of ICMP padding length."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 5 }

     adGenAOSnmICMPTSPaddingFormat OBJECT-TYPE
        SYNTAX  INTEGER
                {
                        ascii (1),
                        hex (2)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the format of the padding pattern."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 6 }

     adGenAOSnmICMPTSPaddingPattern OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..80))
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies data pattern to pad the packet. This can be an ascii or
            hexadecimal pattern and must match the type set in
            adGenAOSnmICMPTSPktFormat. In addition, adGenAOSnmICMPTSDataPadType
            must be set to 'custom'. "
         ::= { adGenAOSnmCfgICMPTSProbeEntry 7 }

     adGenAOSnmICMPTSDataPadType OBJECT-TYPE
        SYNTAX  INTEGER
                {
                  zeroes (1),
                  random (2),
                  custom (3)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies how the ICMP padding paylod is filled."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 8 }


     adGenAOSnmICMPTSPktSendCnt OBJECT-TYPE
        SYNTAX  INTEGER (1..1000)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the number of packets to send and receive for one probe
            operation.  More than one is required for inter-packet delay
            variation measurements."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 9 }

     adGenAOSnmICMPTSSendScheduleType OBJECT-TYPE
        SYNTAX  INTEGER {
                        periodic (1),
                        poisson (2)  -- for future use, not currently supported
                       }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies in milliseconds the time between test packets during a
            single probe operation."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 10 }

     adGenAOSnmICMPTSSendScheduleValue OBJECT-TYPE
        SYNTAX  INTEGER (5..5000)
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "Specifies in milliseconds the time between start of each packet
            send operation."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 11 }

     adGenAOSnmICMPTSIpdvAbsInMinFail OBJECT-TYPE
         SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the minimum inbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 12 }

     adGenAOSnmICMPTSIpdvAbsInAvgFail OBJECT-TYPE
         SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the average inbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
             operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 13 }

     adGenAOSnmICMPTSIpdvAbsInMaxFail OBJECT-TYPE
         SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the maximum inbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 14 }

     adGenAOSnmICMPTSIpdvAbsInMinPass OBJECT-TYPE
         SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for minimum inbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 15 }

     adGenAOSnmICMPTSIpdvAbsInAvgPass OBJECT-TYPE
         SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for average inbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 16 }

     adGenAOSnmICMPTSIpdvAbsInMaxPass OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for maximum inbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 17 }

     adGenAOSnmICMPTSIpdvAbsOutMinFail OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the minimum outbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 18 }

     adGenAOSnmICMPTSIpdvAbsOutAvgFail OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the average outbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 19 }

     adGenAOSnmICMPTSIpdvAbsOutMaxFail OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the maximum outbound absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 20 }

     adGenAOSnmICMPTSIpdvAbsOutMinPass OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for minimum outbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 21 }

     adGenAOSnmICMPTSIpdvAbsOutAvgPass OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for average outbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 22 }

     adGenAOSnmICMPTSIpdvAbsOutMaxPass OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for maximum outbound
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 23 }

     adGenAOSnmICMPTSIpdvAbsRtMinFail OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the minimum round-trip absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 24 }

     adGenAOSnmICMPTSIpdvAbsRtAvgFail OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the average round-trip absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 25 }

     adGenAOSnmICMPTSIpdvAbsRtMaxFail OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the maximum round-trip absolute-value inter-packet delay
            variation threshold in milliseconds that will cause the probe
            operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 26 }

     adGenAOSnmICMPTSIpdvAbsRtMinPass OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for minimum round-trip
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 27 }

     adGenAOSnmICMPTSIpdvAbsRtAvgPass OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for average round-trip
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 28 }

     adGenAOSnmICMPTSIpdvAbsRtMaxPass OBJECT-TYPE
        SYNTAX  Unsigned32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for maximum round-trip
            absolute-value inter-packet delay variation that will allow the
            probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 29 }

     adGenAOSnmICMPTSDelayInMinFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the minimum inbound delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 30 }

     adGenAOSnmICMPTSDelayInAvgFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the average inbound delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 31 }

     adGenAOSnmICMPTSDelayInMaxFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the maximum inbound delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 32 }

     adGenAOSnmICMPTSDelayInMinPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for minimum inbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 33 }

     adGenAOSnmICMPTSDelayInAvgPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for average inbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 34 }

     adGenAOSnmICMPTSDelayInMaxPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for maximum inbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 35 }

     adGenAOSnmICMPTSDelayOutMinFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the minimum outbound delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 36 }

     adGenAOSnmICMPTSDelayOutAvgFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the average outbound delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 37 }

     adGenAOSnmICMPTSDelayOutMaxFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the maximum outbound delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 38 }

     adGenAOSnmICMPTSDelayOutMinPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for minimum outbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 39 }

     adGenAOSnmICMPTSDelayOutAvgPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for average outbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 40 }

     adGenAOSnmICMPTSDelayOutMaxPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for maximum outbound delay
            that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 41 }

     adGenAOSnmICMPTSDelayRtMinFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the minimum round-trip delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 42 }

     adGenAOSnmICMPTSDelayRtAvgFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the average round-trip delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 43 }

     adGenAOSnmICMPTSDelayRtMaxFail OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the maximum round-trip delay threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 44 }

     adGenAOSnmICMPTSDelayRtMinPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for minimum round-trip
            delay that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 45 }

     adGenAOSnmICMPTSDelayRtAvgPass OBJECT-TYPE
        SYNTAX  Integer32
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for average round-trip
            delay that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 46 }

     adGenAOSnmICMPTSDelayRtMaxPass OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the threshold in milliseconds for maximum round-trip
            delay that will allow the probe operation to pass."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 47 }

     adGenAOSnmICMPTSPktRtLossFail OBJECT-TYPE
        SYNTAX  INTEGER (0..1000)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the round-trip packet-loss threshold in milliseconds
            that will cause the probe operation to fail."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 48 }

     adGenAOSnmICMPTSPktRtLossPass OBJECT-TYPE
        SYNTAX  INTEGER (0..1000)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the round-trip packet-loss threshold in milliseconds
            that will allow the probe operation to change to the pass state."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 49 }

     adGenAOSnmICMPTSHistoryDepth OBJECT-TYPE
        SYNTAX  INTEGER (1..127)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the number of probe operation statistic results to keep."
         ::= { adGenAOSnmCfgICMPTSProbeEntry 50 }



     --

     --  Network Monitor ICMP timestamp History Table

     --  The variables that display the history for a NetMon ICMP timestamp
     --  probe.

     adGenAOSnmICMPTSHistoryTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmICMPTSHistoryEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Operation history for a network monitor ICMP timestamp probe."
         ::= { adGenAOSNetMon 8 }

     adGenAOSnmICMPTSHistoryEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmICMPTSHistoryEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The read-only history of a particular network monitor ICMP
            timestamp probe."
         INDEX { adGenAOSnmIndex, adGenAOSnmICMPTSSeqNum  }
         ::= { adGenAOSnmICMPTSHistoryTable 1 }


     AdGenAOSnmICMPTSHistoryEntry ::=
         SEQUENCE {
             adGenAOSnmICMPTSSeqNum             Integer32,
             adGenAOSnmICMPTSHistoryName        OCTET STRING,
             adGenAOSnmICMPTSStartTime          DisplayString,
             adGenAOSnmICMPTSEndTime            DisplayString,
             adGenAOSnmICMPTSDelayInMin         Integer32,
             adGenAOSnmICMPTSDelayInMax         Integer32,
             adGenAOSnmICMPTSDelayOutMin        Integer32,
             adGenAOSnmICMPTSDelayOutMax        Integer32,
             adGenAOSnmICMPTSDelayRtMin         Integer32,
             adGenAOSnmICMPTSDelayRtMax         Integer32,
             adGenAOSnmICMPTSLossRoundTrip      Counter32,
             adGenAOSnmICMPTSDelayOutSum        Integer32,
             adGenAOSnmICMPTSDelayOutSum2       Counter64,
             adGenAOSnmICMPTSDelayOutNum        Counter32,
             adGenAOSnmICMPTSDelayInSum         Integer32,
             adGenAOSnmICMPTSDelayInSum2        Counter64,
             adGenAOSnmICMPTSDelayInNum         Counter32,
             adGenAOSnmICMPTSDelayRtSum         Integer32,
             adGenAOSnmICMPTSDelayRtSum2        Counter64,
             adGenAOSnmICMPTSDelayRtNum         Counter32,
             adGenAOSnmICMPTSIpvPosInMin        Counter32,
             adGenAOSnmICMPTSIpvPosInMax        Counter32,
             adGenAOSnmICMPTSIpvPosInSum        Counter32,
             adGenAOSnmICMPTSIpvPosInSum2       Counter64,
             adGenAOSnmICMPTSIpvPosInNum        Counter32,
             adGenAOSnmICMPTSIpvPosOutMin       Counter32,
             adGenAOSnmICMPTSIpvPosOutMax       Counter32,
             adGenAOSnmICMPTSIpvPosOutSum       Counter32,
             adGenAOSnmICMPTSIpvPosOutSum2      Counter64,
             adGenAOSnmICMPTSIpvPosOutNum       Counter32,
             adGenAOSnmICMPTSIpvPosRtMin        Counter32,
             adGenAOSnmICMPTSIpvPosRtMax        Counter32,
             adGenAOSnmICMPTSIpvPosRtSum        Counter32,
             adGenAOSnmICMPTSIpvPosRtSum2       Counter64,
             adGenAOSnmICMPTSIpvPosRtNum        Counter32,
             adGenAOSnmICMPTSIpvNegInMin        Counter32,
             adGenAOSnmICMPTSIpvNegInMax        Counter32,
             adGenAOSnmICMPTSIpvNegInSum        Counter32,
             adGenAOSnmICMPTSIpvNegInSum2       Counter64,
             adGenAOSnmICMPTSIpvNegInNum        Counter32,
             adGenAOSnmICMPTSIpvNegOutMin       Counter32,
             adGenAOSnmICMPTSIpvNegOutMax       Counter32,
             adGenAOSnmICMPTSIpvNegOutSum       Counter32,
             adGenAOSnmICMPTSIpvNegOutSum2      Counter64,
             adGenAOSnmICMPTSIpvNegOutNum       Counter32,
             adGenAOSnmICMPTSIpvNegRtMin        Counter32,
             adGenAOSnmICMPTSIpvNegRtMax        Counter32,
             adGenAOSnmICMPTSIpvNegRtSum        Counter32,
             adGenAOSnmICMPTSIpvNegRtSum2       Counter64,
             adGenAOSnmICMPTSIpvNegRtNum        Counter32,
             adGenAOSnmICMPTSIpvAbsInMin        Counter32,
             adGenAOSnmICMPTSIpvAbsInMax        Counter32,
             adGenAOSnmICMPTSIpvAbsInSum        Counter32,
             adGenAOSnmICMPTSIpvAbsInSum2       Counter64,
             adGenAOSnmICMPTSIpvAbsInNum        Counter32,
             adGenAOSnmICMPTSIpvAbsOutMin       Counter32,
             adGenAOSnmICMPTSIpvAbsOutMax       Counter32,
             adGenAOSnmICMPTSIpvAbsOutSum       Counter32,
             adGenAOSnmICMPTSIpvAbsOutSum2      Counter64,
             adGenAOSnmICMPTSIpvAbsOutNum       Counter32,
             adGenAOSnmICMPTSIpvAbsRtMin        Counter32,
             adGenAOSnmICMPTSIpvAbsRtMax        Counter32,
             adGenAOSnmICMPTSIpvAbsRtSum        Counter32,
             adGenAOSnmICMPTSIpvAbsRtSum2       Counter64,
             adGenAOSnmICMPTSIpvAbsRtNum        Counter32,
             adGenAOSnmICMPTSPktSentCount       Counter32
        }

     adGenAOSnmICMPTSSeqNum OBJECT-TYPE
        SYNTAX   Integer32 (1..120)
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Along with adGenAOSnmICMPTSHistoryName, uniquely identifies a
                row in the adGenAOSnmICMPTSHistoryTable. A sequence number of
                '1' signifies the current history entry."
         ::= { adGenAOSnmICMPTSHistoryEntry 1 }

     adGenAOSnmICMPTSHistoryName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding ICMP timestamp probe entry.
            The probe identified by a particular value of this object is the
            same probe as identified by the same value as a
            adGenAOSnmName object instance."
         ::= { adGenAOSnmICMPTSHistoryEntry 2 }

     adGenAOSnmICMPTSStartTime OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The date and time the probe operation started."
         ::= { adGenAOSnmICMPTSHistoryEntry 3 }

     adGenAOSnmICMPTSEndTime OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The date and time the probe operation ended."
         ::= { adGenAOSnmICMPTSHistoryEntry 4 }

     adGenAOSnmICMPTSDelayInMin OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The inbound minimum delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 5 }

     adGenAOSnmICMPTSDelayInMax OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The inbound maximum delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 6 }

     adGenAOSnmICMPTSDelayOutMin OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The outbound minimum delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 7 }

     adGenAOSnmICMPTSDelayOutMax OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The outbound maximum delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 8 }

     adGenAOSnmICMPTSDelayRtMin OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The round-trip minimum delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 9 }

     adGenAOSnmICMPTSDelayRtMax OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The round-trip maximum delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 10 }

      adGenAOSnmICMPTSLossRoundTrip OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The round-trip packet loss."
         ::= { adGenAOSnmICMPTSHistoryEntry 11 }

      adGenAOSnmICMPTSDelayOutSum OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of outbound delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 12 }

      adGenAOSnmICMPTSDelayOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of outbound delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 13 }

      adGenAOSnmICMPTSDelayOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of individual outbound delay samples."
         ::= { adGenAOSnmICMPTSHistoryEntry 14 }

      adGenAOSnmICMPTSDelayInSum OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of inbound delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 15 }

      adGenAOSnmICMPTSDelayInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of inbound delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 16 }

      adGenAOSnmICMPTSDelayInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of individual inbound delay samples."
         ::= { adGenAOSnmICMPTSHistoryEntry 17 }

      adGenAOSnmICMPTSDelayRtSum OBJECT-TYPE
        SYNTAX   Integer32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of round-trip delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 18 }

      adGenAOSnmICMPTSDelayRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of round-trip delay in milliseconds."
         ::= { adGenAOSnmICMPTSHistoryEntry 19 }

      adGenAOSnmICMPTSDelayRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of individual round-trip delay samples."
         ::= { adGenAOSnmICMPTSHistoryEntry 20 }

      adGenAOSnmICMPTSIpvPosInMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of positive inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 21 }

      adGenAOSnmICMPTSIpvPosInMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of positive inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 22 }

      adGenAOSnmICMPTSIpvPosInSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of positive inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 23 }

      adGenAOSnmICMPTSIpvPosInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of positive inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 24 }

      adGenAOSnmICMPTSIpvPosInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of positive inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 25 }

      adGenAOSnmICMPTSIpvPosOutMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of positive outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 26 }

      adGenAOSnmICMPTSIpvPosOutMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of positive outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 27 }

      adGenAOSnmICMPTSIpvPosOutSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of positive outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 28 }

      adGenAOSnmICMPTSIpvPosOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of positive outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 29 }

      adGenAOSnmICMPTSIpvPosOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of positive outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 30 }

      adGenAOSnmICMPTSIpvPosRtMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of positive round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 31 }

      adGenAOSnmICMPTSIpvPosRtMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of positive round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 32 }

      adGenAOSnmICMPTSIpvPosRtSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of positive round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 33 }

      adGenAOSnmICMPTSIpvPosRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of positive round-trip inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 34 }

      adGenAOSnmICMPTSIpvPosRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of positive round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 35 }

      adGenAOSnmICMPTSIpvNegInMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of negative inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 36 }

      adGenAOSnmICMPTSIpvNegInMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of negative inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 37 }

      adGenAOSnmICMPTSIpvNegInSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of negative inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 38 }

      adGenAOSnmICMPTSIpvNegInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of negative inbound inter-packet delay
                variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 39 }

      adGenAOSnmICMPTSIpvNegInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of negative inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 40 }

      adGenAOSnmICMPTSIpvNegOutMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of negative outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 41 }

      adGenAOSnmICMPTSIpvNegOutMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of negative outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 42 }

      adGenAOSnmICMPTSIpvNegOutSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of negative outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 43 }

      adGenAOSnmICMPTSIpvNegOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of negative outbound inter-packet delay
                variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 44 }

      adGenAOSnmICMPTSIpvNegOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of negative outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 45 }

      adGenAOSnmICMPTSIpvNegRtMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of negative round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 46 }

      adGenAOSnmICMPTSIpvNegRtMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of negative round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 47 }

      adGenAOSnmICMPTSIpvNegRtSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of negative round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 48 }

      adGenAOSnmICMPTSIpvNegRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of negative round-trip inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 49 }

      adGenAOSnmICMPTSIpvNegRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of negative round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 50 }

      adGenAOSnmICMPTSIpvAbsInMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of absolute inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 51 }

      adGenAOSnmICMPTSIpvAbsInMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of absolute inbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 52 }

      adGenAOSnmICMPTSIpvAbsInSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of absolute inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 53 }

      adGenAOSnmICMPTSIpvAbsInSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of absolute inbound inter-packet delay
                variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 54 }

      adGenAOSnmICMPTSIpvAbsInNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of absolute inbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 55 }

      adGenAOSnmICMPTSIpvAbsOutMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of absolute outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 56 }

      adGenAOSnmICMPTSIpvAbsOutMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of absolute outbound inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 57 }

      adGenAOSnmICMPTSIpvAbsOutSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of absolute outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 58 }

      adGenAOSnmICMPTSIpvAbsOutSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of absolute outbound inter-packet delay
                variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 59 }

      adGenAOSnmICMPTSIpvAbsOutNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of absolute outbound inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 60 }

      adGenAOSnmICMPTSIpvAbsRtMin OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The minimum value in the set of absolute round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 61 }

      adGenAOSnmICMPTSIpvAbsRtMax OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The maximum value in the set of absolute round-trip
                inter-packet delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 62 }

      adGenAOSnmICMPTSIpvAbsRtSum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of absolute round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 63 }

      adGenAOSnmICMPTSIpvAbsRtSum2 OBJECT-TYPE
        SYNTAX   Counter64
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The sum of the squares of absolute round-trip inter-packet
                delay variation measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 64 }

      adGenAOSnmICMPTSIpvAbsRtNum OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of absolute round-trip inter-packet delay variation
                measurements."
         ::= { adGenAOSnmICMPTSHistoryEntry 65 }

      adGenAOSnmICMPTSPktSentCount  OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "The number of packets sent."
         ::= { adGenAOSnmICMPTSHistoryEntry 66 }
     --
     --  Network Monitor ICMP Timestamp Responder.
     --
     adGenAOSnmICMPTSResponder OBJECT-TYPE
        SYNTAX  INTEGER
                {
                  enabled (1),
                  disabled (2)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies ability to enable or disable the ICMP Timstamp
            responder to respond to ICMP timestamp requests."
         ::= { adGenAOSNetMon 9 }

     --
     --  Network Monitor TWAMP Responder.
     --
     adGenAOSnmTWAMPResponder OBJECT-TYPE
        SYNTAX  INTEGER
                {
                  enabled (1),
                  disabled (2)
                }
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies ability to enable or disable the TWAMP
            responder to respond to ICMP timestamp requests."
         ::= { adGenAOSNetMon 10 }
     --

     --  Network Monitor ICMP timestamp Responder Statistics Table

     --  The variables that display the statistics for a NetMon ICMP timestamp
     --  responder.

     adGenAOSnmICMPTSResponderStatsTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmICMPTSResponderStatsEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Operation history for a network monitor ICMP timestamp probe."
         ::= { adGenAOSNetMon 11 }

     adGenAOSnmICMPTSResponderStatsEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmICMPTSResponderStatsEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The read-only statistics of the network monitor ICMP
            timestamp responder."
         INDEX { adGenAOSnmICMPTSResponderStatsIndex  }
         ::= { adGenAOSnmICMPTSResponderStatsTable 1 }


     AdGenAOSnmICMPTSResponderStatsEntry ::=
         SEQUENCE {
             adGenAOSnmICMPTSResponderStatsIndex        Unsigned32,
             adGenAOSnmICMPTSResponderPacketsReceived   Counter32,
             adGenAOSnmICMPTSResponderPacketsSent       Counter32,
             adGenAOSnmClearICMPTSResponderCounters     INTEGER
        }

      adGenAOSnmICMPTSResponderStatsIndex OBJECT-TYPE
        SYNTAX   Unsigned32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Uniquely identifies a row in the
                adGenAOSnmICMPTSResponderStatsTable."
         ::= { adGenAOSnmICMPTSResponderStatsEntry 1 }

      adGenAOSnmICMPTSResponderPacketsReceived OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Number of packets received by the responder."
         ::= { adGenAOSnmICMPTSResponderStatsEntry 2 }

      adGenAOSnmICMPTSResponderPacketsSent OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Number of packets sent by the responder."
         ::= { adGenAOSnmICMPTSResponderStatsEntry 3 }

     adGenAOSnmClearICMPTSResponderCounters OBJECT-TYPE
        SYNTAX INTEGER {
                clear (1)
        }
        MAX-ACCESS  read-write
         STATUS  current
         DESCRIPTION
            "Clear the counters of the ICMP timestamp responder.
            This is a write-only variable. A read will result in no action
            being taken."
         ::= { adGenAOSnmICMPTSResponderStatsEntry 4 }

     --

     --  Network Monitor TWAMP Responder Statistics Table

     --  The variables that display the statistics for a NetMon TWAMP
     --  responder.

     adGenAOSnmTwampResponderStatsTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmTwampResponderStatsEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Operation history for a network monitor TWAMP probe."
         ::= { adGenAOSNetMon 12 }

     adGenAOSnmTwampResponderStatsEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmTwampResponderStatsEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The read-only statistics of the network monitor TWAMP responder."
         INDEX { adGenAOSnmTwampResponderStatsIndex  }
         ::= { adGenAOSnmTwampResponderStatsTable 1 }


     AdGenAOSnmTwampResponderStatsEntry ::=
         SEQUENCE {
                adGenAOSnmTwampResponderStatsIndex        Unsigned32,
                adGenAOSnmTwampResponderPacketsReceived   Counter32,
                adGenAOSnmTwampResponderPacketsSent       Counter32,
                adGenAOSnmTwampResponderSessionClosed     Counter32,
                adGenAOSnmTwampResponderSessionOpened     Counter32,
                adGenAOSnmTwampResponderSessionRejected   Counter32,
                adGenAOSnmClearTwampResponderCounters     INTEGER
        }

      adGenAOSnmTwampResponderStatsIndex OBJECT-TYPE
        SYNTAX   Unsigned32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Uniquely identifies a row in the
                adGenAOSnmTwampResponderStatsTable."
         ::= { adGenAOSnmTwampResponderStatsEntry 1 }

      adGenAOSnmTwampResponderPacketsReceived OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Number of packets received by the responder."
         ::= { adGenAOSnmTwampResponderStatsEntry 2 }

      adGenAOSnmTwampResponderPacketsSent OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Number of packets sent by the responder."
         ::= { adGenAOSnmTwampResponderStatsEntry 3 }

      adGenAOSnmTwampResponderSessionClosed OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Number of responder sessions closed."
         ::= { adGenAOSnmTwampResponderStatsEntry 4 }

      adGenAOSnmTwampResponderSessionOpened OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Number of responder sessions opened."
         ::= { adGenAOSnmTwampResponderStatsEntry 5 }

      adGenAOSnmTwampResponderSessionRejected OBJECT-TYPE
        SYNTAX   Counter32
        MAX-ACCESS read-only
        STATUS  current
        DESCRIPTION
                "Number of responder sessions closed."
         ::= { adGenAOSnmTwampResponderStatsEntry 6 }

     adGenAOSnmClearTwampResponderCounters OBJECT-TYPE
        SYNTAX INTEGER {
                clear (1)
        }
        MAX-ACCESS  read-write
         STATUS  current
         DESCRIPTION
            "Clear the counters of the TWAMP responder.
            This is a write-only variable. A read will result in no action
            being taken."
         ::= { adGenAOSnmTwampResponderStatsEntry 7 }

     --  Network Monitor Configure ICMP Echo Probe Table

     --  The variables that configure a NetMon ICMP Echo probe.

     adGenAOSnmCfgIEProbeTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmCfgIEProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Configure a network monitor ICMP echo probe."
         ::= { adGenAOSNetMon 13 }

     adGenAOSnmCfgIEProbeEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmCfgIEProbeEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "The parameters used to configure a particular network monitor ICMP
            echo probe."
         INDEX { adGenAOSnmIndex }
         ::= { adGenAOSnmCfgIEProbeTable 1 }


     AdGenAOSnmCfgIEProbeEntry ::=
         SEQUENCE {
             adGenAOSnmCfgIEName                    OCTET STRING,
             adGenAOSnmIEDestHostname               DisplayString,
             adGenAOSnmIESrcIP                      IpAddress,
             adGenAOSnmIEPacketLength               INTEGER,
             adGenAOSnmIEPacketPattern              OCTET STRING
        }

     adGenAOSnmCfgIEName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding ICMP echo probe entry.
            The probe identified by a particular value of this object is the
            same probe as identified by the same value as a
            adGenAOSnmName object instance."
         ::= { adGenAOSnmCfgIEProbeEntry 1 }

     adGenAOSnmIEDestHostname OBJECT-TYPE
         SYNTAX     DisplayString
         MAX-ACCESS   read-write
         STATUS   current
         DESCRIPTION
            "Specifies the destination hostname or IP address as a string in
            dotted decimal format."
         ::= { adGenAOSnmCfgIEProbeEntry 2 }

     adGenAOSnmIESrcIP OBJECT-TYPE
       SYNTAX     IpAddress
       MAX-ACCESS read-write
       STATUS	  current
       DESCRIPTION
               "Specifies the source IPv4 address."
         ::= { adGenAOSnmCfgIEProbeEntry 3 }

     adGenAOSnmIEPacketLength OBJECT-TYPE
        SYNTAX  INTEGER (0..1462)
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Length of the ICMP packet."
         ::= { adGenAOSnmCfgIEProbeEntry 4 }

     adGenAOSnmIEPacketPattern OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..4))
         MAX-ACCESS read-write
         STATUS  current
         DESCRIPTION
            "Specifies the hexadecimal pattern for the ICMP packet."
         ::= { adGenAOSnmCfgIEProbeEntry 5 }


     --  Network Monitor Track Table

     --  The variables that configure a NetMon Track.

     adGenAOSnmTrackTable OBJECT-TYPE
         SYNTAX   SEQUENCE OF AdGenAOSnmTrackEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Network monitor Track."
         ::= { adGenAOSNetMon 14 }

     adGenAOSnmTrackEntry OBJECT-TYPE
         SYNTAX   AdGenAOSnmTrackEntry
         MAX-ACCESS   not-accessible
         STATUS   current
         DESCRIPTION
            "Network monitor Track entry."
         INDEX { adGenAOSnmTrackIndex }
         ::= { adGenAOSnmTrackTable 1 }


     AdGenAOSnmTrackEntry ::=
         SEQUENCE {
             adGenAOSnmTrackIndex                   Integer32,
             adGenAOSnmTrackName                    OCTET STRING
        }

     adGenAOSnmTrackIndex OBJECT-TYPE
         SYNTAX      Integer32 (0..1000)
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
                "Uniquely identifies a row in the adGenAOSnmTrackTable."

         ::= { adGenAOSnmTrackEntry 1 }

     adGenAOSnmTrackName OBJECT-TYPE
         SYNTAX     OCTET STRING (SIZE(1..40))
         MAX-ACCESS   read-only
         STATUS   current
         DESCRIPTION
            "The name of the corresponding Track entry."

         ::= { adGenAOSnmTrackEntry 2 }


     -- conformance information

     adGenAOSnmConformance OBJECT IDENTIFIER ::= { adGenAOSConformance 7 }
     adGenAOSnmGroups      OBJECT IDENTIFIER ::= { adGenAOSnmConformance 1 }
     adGenAOSnmCompliances OBJECT IDENTIFIER ::= { adGenAOSnmConformance 2 }

--
-- MIB Compliance statements.
--

-- Full compliance statement
     adGenAOSnmFullCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
        "The compliance statement for SNMP entities which implement
        version 2 of the adGenAOSNetMon MIB. When this MIB is implemented
        with support for read-create, then such an implementation can claim
        full compliance. Network Monitor probes can then be both monitored
        and configured with this MIB.  No more than 10 rows can be created
        in the adGenAOSnmProbeTable at this time. Attempting to create more
        than this value will return an error."

        MODULE  -- this module
        MANDATORY-GROUPS { adGenAOSnmProbeGroup }

         GROUP adGenAOSnmProbeTableNextIndexGroup
         DESCRIPTION
            "This optional group is only required for systems
             that support the creation of entries in
             the adGenAOSnmProbeTable."

         OBJECT      adGenAOSnmProbeTableNextIndex
         MIN-ACCESS  read-only
         DESCRIPTION
            "This object is only required for systems
             that support the creation of entries in
             the adGenAOSnmProbeTable."

         GROUP  adGenAOSnmConfigProbeGroup
         DESCRIPTION
            "This optional group is used to configure the probes created with
            adGenAOSnmProbeGroup."

        GROUP  adGenAOSnmProbeStatusGroup
        DESCRIPTION
        "This optional group is used to view the statistics of the probes."

        GROUP  adGenAOSnmCfgTwampProbeGroup
        DESCRIPTION
        "This optional group is used to configure the TWAMP probes."

        GROUP  adGenAOSnmTwampHistoryGroup
        DESCRIPTION
        "This optional group is used to view the statistics of the TWAMP
        probes."

        GROUP  adGenAOSnmCfgICMPTSProbeGroup
        DESCRIPTION
        "This optional group is used to configure the ICMP timestamp probes."

        GROUP  adGenAOSnmICMPTSHistoryGroup
        DESCRIPTION
        "This optional group is used to view the statistics of the ICMP
        timestamp probes."

        GROUP  adGenAOSnmICMPTSResponderGroup
        DESCRIPTION
        "This optional group is used to enable/disable the ICMP
        timestamp responder."

        GROUP  adGenAOSnmTWAMPResponderGroup
        DESCRIPTION
        "This optional group is used to enable/disable the TWAMP responder."

        GROUP  adGenAOSnmICMPTSResponderStatsGroup
        DESCRIPTION
        "This optional group is used to ICMP responder statistics."

        GROUP  adGenAOSnmTwampResponderStatsGroup
        DESCRIPTION
        "This optional group is used to TWAMP responder statistics."

        GROUP  adGenAOSnmCfgIEProbeGroup
        DESCRIPTION
        "This optional group is used to configure the ICMP echo probes."

        GROUP  adGenAOSnmTrackGroup
        DESCRIPTION
        "This optional group is used to retrieve track information."

        OBJECT       adGenAOSnmRowStatus
        SYNTAX       RowStatus { active(1), notInService(2) }
        WRITE-SYNTAX RowStatus { active(1), createAndGo(4),
                               destroy(6) }
        DESCRIPTION
        "In order for this object to become active, the following
         row objects MUST be defined: adGenAOSnmName and adGenAOSnmType.
         The control row objects adGenAOSnmName and adGenAOSnmType cannot be
         modified once this conceptual row has been created. Writes of the value
         'notInService' and 'createAndWait' will not be supported. This
         object can be set to 'destroy' from any value at any time."

        OBJECT       adGenAOSnmName
        MIN-ACCESS   read-only
        DESCRIPTION
        "Must be defined before adGenAOSnmRowStatus can be set to 'active'.
        This object cannot be modified once the conceptual row has been
        created."

        OBJECT       adGenAOSnmType
        MIN-ACCESS   read-only
        DESCRIPTION
        "Must be defined before adGenAOSnmRowStatus can be set to 'active'.
        This object cannot be modified once the conceptual row has been
        created."

         ::= { adGenAOSnmCompliances 1 }


--
-- Read-Only Compliance
--
     adGenAOSnmReadOnlyCompliance MODULE-COMPLIANCE
         STATUS  current
         DESCRIPTION
            "The compliance statement for SNMP entities which implement
            version 2 of the adGenAOSNetMon MIB. When this MIB is implemented
            without support for read-create (i.e. in read-only mode),
            then such an implementation can claim read-only compliance.
            A network monitor probe can then be monitored but cannot
            be configured with this MIB."

        MODULE  -- this module

         GROUP adGenAOSnmProbeTableNextIndexGroup
         DESCRIPTION
            "This optional group is only required for systems
             that support the creation of entries in
             the adGenAOSnmProbeTable."

         OBJECT      adGenAOSnmProbeTableNextIndex
         MIN-ACCESS  read-only
         DESCRIPTION
            "This object is only required for systems
             that support the creation of entries in
             the adGenAOSnmProbeTable."


         GROUP  adGenAOSnmConfigProbeGroup
         DESCRIPTION
            "This optional group is used to configure the probes created with
            adGenAOSnmProbeGroup."

         GROUP  adGenAOSnmProbeStatusGroup
         DESCRIPTION
            "This optional group is used to view the statistics of the probes."

         GROUP  adGenAOSnmCfgTwampProbeGroup
         DESCRIPTION
            "This optional group is used to configure the TWAMP probes."

        GROUP  adGenAOSnmTwampHistoryGroup
        DESCRIPTION
        "This optional group is used to view the statistics of the TWAMP
        probes."

        GROUP  adGenAOSnmCfgICMPTSProbeGroup
        DESCRIPTION
        "This optional group is used to configure the ICMP timestamp probes."

        GROUP  adGenAOSnmICMPTSHistoryGroup
        DESCRIPTION
        "This optional group is used to view the statistics of the ICMP
        timestamp probes."

        GROUP  adGenAOSnmICMPTSResponderGroup
        DESCRIPTION
        "This optional group is used to enable/disable the ICMP
        timestamp responder."

        GROUP  adGenAOSnmTWAMPResponderGroup
        DESCRIPTION
        "This optional group is used to enable/disable the TWAMP responder."

        GROUP  adGenAOSnmICMPTSResponderStatsGroup
        DESCRIPTION
        "This optional group is used to ICMP responder statistics."

        GROUP  adGenAOSnmTwampResponderStatsGroup
        DESCRIPTION
        "This optional group is used to TWAMP responder statistics."

        GROUP  adGenAOSnmCfgIEProbeGroup
        DESCRIPTION
        "This optional group is used to configure the ICMP echo probes."

        GROUP  adGenAOSnmTrackGroup
        DESCRIPTION
        "This optional group is used to retrieve track information."

        OBJECT       adGenAOSnmRowStatus
        MIN-ACCESS   read-only
        DESCRIPTION
        "Write access is not allowed."

        OBJECT       adGenAOSnmName
        MIN-ACCESS   read-only
        DESCRIPTION
        "Write access is not allowed."

        OBJECT       adGenAOSnmType
        MIN-ACCESS   read-only
        DESCRIPTION
        "Write access is not allowed."

         GROUP  adGenAOSnmNotificationGroup
         DESCRIPTION
            "This optional group defines the asynchronous
            notifications generated by Network Monitoring Monitoring."



         ::= { adGenAOSnmCompliances 2 }

     -- units of conformance

     adGenAOSnmProbeTableNextIndexGroup    OBJECT-GROUP
         OBJECTS {
                        adGenAOSnmProbeTableNextIndex
                }
         STATUS  current
         DESCRIPTION
            "The object necessary to get the next index for creation of the
            network monitor probe."
         ::= { adGenAOSnmGroups 1 }

     adGenAOSnmProbeGroup    OBJECT-GROUP
         OBJECTS {
                        adGenAOSnmIndex, adGenAOSnmName,
                        adGenAOSnmType, adGenAOSnmRowStatus
                }
         STATUS  current
         DESCRIPTION
            "The objects necessary to create the network monitor probe."
         ::= { adGenAOSnmGroups 2 }

     adGenAOSnmConfigProbeGroup    OBJECT-GROUP
         OBJECTS {
                        adGenAOSnmCfgName, adGenAOSnmAdminStatus,
                        adGenAOSnmPollPeriod, adGenAOSnmTimeoutPeriod,
                        adGenAOSnmToleranceMode, adGenAOSnmFailTolerance,
                        adGenAOSnmPassTolerance,    adGenAOSnmToleranceTestSize,
                        adGenAOSnmClearCounters
                 }
         STATUS  current
         DESCRIPTION
            "The objects necessary to configure the network monitor probe."
         ::= { adGenAOSnmGroups 3 }


     adGenAOSnmProbeStatusGroup    OBJECT-GROUP
         OBJECTS {
                        adGenAOSnmStatusName, adGenAOSnmTestStatus, adGenAOSnmTestsRun,
                        adGenAOSnmTestsFailed, adGenAOSnmStatsToleranceTestSize,
                        adGenAOSnmStatsToleranceTestValue, adGenAOSnmTimeSinceLastStatusChange
                }
         STATUS  current
         DESCRIPTION
            "Objects designed to assist in retrieving the statistics of the
            probes."
         ::= { adGenAOSnmGroups 4 }

     adGenAOSnmCfgTwampProbeGroup    OBJECT-GROUP
         OBJECTS {
                        adGenAOSnmCfgTwName, adGenAOSnmTwDestHostname,
                        adGenAOSnmTwDestPort, adGenAOSnmTwSrcIP, adGenAOSnmTwSrcPort,
                        adGenAOSnmTwDscp, adGenAOSnmTwPaddingLen, adGenAOSnmTwPaddingFormat,
                        adGenAOSnmTwPaddingPattern, adGenAOSnmTwDataPadType,
                        adGenAOSnmTwPktSendCnt,  adGenAOSnmTwSendScheduleType,
                        adGenAOSnmTwSendScheduleValue, adGenAOSnmTwIpdvAbsInMinFail,
                        adGenAOSnmTwIpdvAbsInAvgFail, adGenAOSnmTwIpdvAbsInMaxFail,
                        adGenAOSnmTwIpdvAbsInMinPass, adGenAOSnmTwIpdvAbsInAvgPass,
                        adGenAOSnmTwIpdvAbsInMaxPass, adGenAOSnmTwIpdvAbsOutMinFail,
                        adGenAOSnmTwIpdvAbsOutAvgFail, adGenAOSnmTwIpdvAbsOutMaxFail,
                        adGenAOSnmTwIpdvAbsOutMinPass, adGenAOSnmTwIpdvAbsOutAvgPass,
                        adGenAOSnmTwIpdvAbsOutMaxPass, adGenAOSnmTwIpdvAbsRtMinFail,
                        adGenAOSnmTwIpdvAbsRtAvgFail, adGenAOSnmTwIpdvAbsRtMaxFail,
                        adGenAOSnmTwIpdvAbsRtMinPass, adGenAOSnmTwIpdvAbsRtAvgPass,
                        adGenAOSnmTwIpdvAbsRtMaxPass, adGenAOSnmTwDelayInMinFail,
                        adGenAOSnmTwDelayInAvgFail, adGenAOSnmTwDelayInMaxFail,
                        adGenAOSnmTwDelayInMinPass, adGenAOSnmTwDelayInAvgPass,
                        adGenAOSnmTwDelayInMaxPass, adGenAOSnmTwDelayOutMinFail,
                        adGenAOSnmTwDelayOutAvgFail, adGenAOSnmTwDelayOutMaxFail,
                        adGenAOSnmTwDelayOutMinPass, adGenAOSnmTwDelayOutAvgPass,
                        adGenAOSnmTwDelayOutMaxPass, adGenAOSnmTwDelayRtMinFail,
                        adGenAOSnmTwDelayRtAvgFail, adGenAOSnmTwDelayRtMaxFail,
                        adGenAOSnmTwDelayRtMinPass, adGenAOSnmTwDelayRtAvgPass,
                        adGenAOSnmTwDelayRtMaxPass, adGenAOSnmTwPktRtLossFail,
                        adGenAOSnmTwPktRtLossPass, adGenAOSnmTwHistoryDepth


                }
         STATUS  current
         DESCRIPTION
            "The objects necessary to configure the network monitor TWAMP
             probe."
         ::= { adGenAOSnmGroups 5 }

     adGenAOSnmTwampHistoryGroup    OBJECT-GROUP
         OBJECTS {
             adGenAOSnmTwSeqNum, adGenAOSnmTwHistoryName,
             adGenAOSnmTwStartTime, adGenAOSnmTwEndTime,
             adGenAOSnmTwLocalSyncState, adGenAOSnmTwLocalClkErr,
             adGenAOSnmTwRemoteSyncState, adGenAOSnmTwRemoteClkErr,
             adGenAOSnmTwDelayInMin, adGenAOSnmTwDelayInMax,
             adGenAOSnmTwDelayOutMin, adGenAOSnmTwDelayOutMax,
             adGenAOSnmTwDelayRtMin, adGenAOSnmTwDelayRtMax,
             adGenAOSnmTwLossRoundTrip, adGenAOSnmTwDelayOutSum,
             adGenAOSnmTwDelayOutSum2, adGenAOSnmTwDelayOutNum,
             adGenAOSnmTwDelayInSum, adGenAOSnmTwDelayInSum2,
             adGenAOSnmTwDelayInNum, adGenAOSnmTwDelayRtSum,
             adGenAOSnmTwDelayRtSum2, adGenAOSnmTwDelayRtNum,
             adGenAOSnmTwIpvPosInMin, adGenAOSnmTwIpvPosInMax,
             adGenAOSnmTwIpvPosInSum, adGenAOSnmTwIpvPosInSum2,
             adGenAOSnmTwIpvPosInNum, adGenAOSnmTwIpvPosOutMin,
             adGenAOSnmTwIpvPosOutMax, adGenAOSnmTwIpvPosOutSum,
             adGenAOSnmTwIpvPosOutSum2, adGenAOSnmTwIpvPosOutNum,
             adGenAOSnmTwIpvPosRtMin, adGenAOSnmTwIpvPosRtMax,
             adGenAOSnmTwIpvPosRtSum, adGenAOSnmTwIpvPosRtSum2,
             adGenAOSnmTwIpvPosRtNum, adGenAOSnmTwIpvNegInMin,
             adGenAOSnmTwIpvNegInMax, adGenAOSnmTwIpvNegInSum,
             adGenAOSnmTwIpvNegInSum2, adGenAOSnmTwIpvNegInNum,
             adGenAOSnmTwIpvNegOutMin,  adGenAOSnmTwIpvNegOutMax,
             adGenAOSnmTwIpvNegOutSum, adGenAOSnmTwIpvNegOutSum2,
             adGenAOSnmTwIpvNegOutNum, adGenAOSnmTwIpvNegRtMin,
             adGenAOSnmTwIpvNegRtMax, adGenAOSnmTwIpvNegRtSum,
             adGenAOSnmTwIpvNegRtSum2, adGenAOSnmTwIpvNegRtNum,
             adGenAOSnmTwIpvAbsInMin, adGenAOSnmTwIpvAbsInMax,
             adGenAOSnmTwIpvAbsInSum, adGenAOSnmTwIpvAbsInSum2,
             adGenAOSnmTwIpvAbsInNum, adGenAOSnmTwIpvAbsOutMin,
             adGenAOSnmTwIpvAbsOutMax, adGenAOSnmTwIpvAbsOutSum,
             adGenAOSnmTwIpvAbsOutSum2, adGenAOSnmTwIpvAbsOutNum,
             adGenAOSnmTwIpvAbsRtMin, adGenAOSnmTwIpvAbsRtMax,
             adGenAOSnmTwIpvAbsRtSum, adGenAOSnmTwIpvAbsRtSum2,
             adGenAOSnmTwIpvAbsRtNum, adGenAOSnmTwPktSentCount
         }
         STATUS  current
         DESCRIPTION
            "Objects designed to assist in retrieving the statistics of the
            TWAMP probe."
         ::= { adGenAOSnmGroups 6 }

     adGenAOSnmCfgICMPTSProbeGroup    OBJECT-GROUP
         OBJECTS {
                adGenAOSnmCfgICMPTSName, adGenAOSnmICMPTSDestHostname,
                adGenAOSnmICMPTSSrcIP, adGenAOSnmICMPTSDscp,
                adGenAOSnmICMPTSPaddingLen, adGenAOSnmICMPTSPaddingFormat,
                adGenAOSnmICMPTSPaddingPattern, adGenAOSnmICMPTSDataPadType,
                adGenAOSnmICMPTSPktSendCnt, adGenAOSnmICMPTSSendScheduleType,
                adGenAOSnmICMPTSSendScheduleValue, adGenAOSnmICMPTSIpdvAbsInMinFail,
                adGenAOSnmICMPTSIpdvAbsInAvgFail, adGenAOSnmICMPTSIpdvAbsInMaxFail,
                adGenAOSnmICMPTSIpdvAbsInMinPass, adGenAOSnmICMPTSIpdvAbsInAvgPass,
                adGenAOSnmICMPTSIpdvAbsInMaxPass, adGenAOSnmICMPTSIpdvAbsOutMinFail,
                adGenAOSnmICMPTSIpdvAbsOutAvgFail, adGenAOSnmICMPTSIpdvAbsOutMaxFail,
                adGenAOSnmICMPTSIpdvAbsOutMinPass, adGenAOSnmICMPTSIpdvAbsOutAvgPass,
                adGenAOSnmICMPTSIpdvAbsOutMaxPass, adGenAOSnmICMPTSIpdvAbsRtMinFail,
                adGenAOSnmICMPTSIpdvAbsRtAvgFail, adGenAOSnmICMPTSIpdvAbsRtMaxFail,
                adGenAOSnmICMPTSIpdvAbsRtMinPass, adGenAOSnmICMPTSIpdvAbsRtAvgPass,
                adGenAOSnmICMPTSIpdvAbsRtMaxPass, adGenAOSnmICMPTSDelayInMinFail,
                adGenAOSnmICMPTSIpvPosInMin, adGenAOSnmICMPTSIpvPosInMax,
                adGenAOSnmICMPTSIpvPosInSum, adGenAOSnmICMPTSIpvPosInSum2,
                adGenAOSnmICMPTSIpvPosInNum, adGenAOSnmICMPTSIpvPosOutMin,
                adGenAOSnmICMPTSIpvPosOutMax, adGenAOSnmICMPTSIpvPosOutSum,
                adGenAOSnmICMPTSIpvPosOutSum2, adGenAOSnmICMPTSIpvPosOutNum,
                adGenAOSnmICMPTSIpvPosRtMin, adGenAOSnmICMPTSIpvPosRtMax,
                adGenAOSnmICMPTSIpvPosRtSum, adGenAOSnmICMPTSIpvPosRtSum2,
                adGenAOSnmICMPTSIpvPosRtNum, adGenAOSnmICMPTSIpvNegInMin,
                adGenAOSnmICMPTSIpvNegInMax, adGenAOSnmICMPTSIpvNegInSum,
                adGenAOSnmICMPTSIpvNegInSum2, adGenAOSnmICMPTSIpvNegInNum,
                adGenAOSnmICMPTSIpvNegOutMin,  adGenAOSnmICMPTSIpvNegOutMax,
                adGenAOSnmICMPTSIpvNegOutSum, adGenAOSnmICMPTSIpvNegOutSum2,
                adGenAOSnmICMPTSIpvNegOutNum, adGenAOSnmICMPTSIpvNegRtMin,
                adGenAOSnmICMPTSIpvNegRtMax, adGenAOSnmICMPTSIpvNegRtSum,
                adGenAOSnmICMPTSDelayInAvgFail, adGenAOSnmICMPTSDelayInMaxFail,
                adGenAOSnmICMPTSDelayInMinPass, adGenAOSnmICMPTSDelayInAvgPass,
                adGenAOSnmICMPTSDelayInMaxPass, adGenAOSnmICMPTSDelayOutMinFail,
                adGenAOSnmICMPTSDelayOutAvgFail, adGenAOSnmICMPTSDelayOutMaxFail,
                adGenAOSnmICMPTSDelayOutMinPass, adGenAOSnmICMPTSDelayOutAvgPass,
                adGenAOSnmICMPTSDelayOutMaxPass, adGenAOSnmICMPTSDelayRtMinFail,
                adGenAOSnmICMPTSDelayRtAvgFail, adGenAOSnmICMPTSDelayRtMaxFail,
                adGenAOSnmICMPTSDelayRtMinPass, adGenAOSnmICMPTSDelayRtAvgPass,
                adGenAOSnmICMPTSDelayRtMaxPass, adGenAOSnmICMPTSPktRtLossFail,
                adGenAOSnmICMPTSPktRtLossPass, adGenAOSnmICMPTSHistoryDepth

         }
         STATUS  current
         DESCRIPTION
            "The objects necessary to configure the network monitor ICMP
            timestamp probe."
         ::= { adGenAOSnmGroups 7 }

     adGenAOSnmICMPTSHistoryGroup    OBJECT-GROUP
         OBJECTS {
             adGenAOSnmICMPTSSeqNum, adGenAOSnmICMPTSHistoryName,
             adGenAOSnmICMPTSStartTime, adGenAOSnmICMPTSEndTime,
             adGenAOSnmICMPTSDelayInMin, adGenAOSnmICMPTSDelayInMax,
             adGenAOSnmICMPTSDelayOutMin, adGenAOSnmICMPTSDelayOutMax,
             adGenAOSnmICMPTSDelayRtMin, adGenAOSnmICMPTSDelayRtMax,
             adGenAOSnmICMPTSLossRoundTrip, adGenAOSnmICMPTSDelayOutSum,
             adGenAOSnmICMPTSDelayOutSum2, adGenAOSnmICMPTSDelayOutNum,
             adGenAOSnmICMPTSDelayInSum,  adGenAOSnmICMPTSDelayInSum2,
             adGenAOSnmICMPTSDelayInNum, adGenAOSnmICMPTSDelayRtSum,
             adGenAOSnmICMPTSDelayRtSum2, adGenAOSnmICMPTSDelayRtNum,
             adGenAOSnmICMPTSIpvPosInMin, adGenAOSnmICMPTSIpvPosInMax,
             adGenAOSnmICMPTSIpvPosInSum, adGenAOSnmICMPTSIpvPosInSum2,
             adGenAOSnmICMPTSIpvPosInNum, adGenAOSnmICMPTSIpvPosOutMin,
             adGenAOSnmICMPTSIpvPosOutMax, adGenAOSnmICMPTSIpvPosOutSum,
             adGenAOSnmICMPTSIpvPosOutSum2, adGenAOSnmICMPTSIpvPosOutNum,
             adGenAOSnmICMPTSIpvPosRtMin, adGenAOSnmICMPTSIpvPosRtMax,
             adGenAOSnmICMPTSIpvPosRtSum, adGenAOSnmICMPTSIpvPosRtSum2,
             adGenAOSnmICMPTSIpvPosRtNum, adGenAOSnmICMPTSIpvNegInMin,
             adGenAOSnmICMPTSIpvNegInMax, adGenAOSnmICMPTSIpvNegInSum,
             adGenAOSnmICMPTSIpvNegInSum2, adGenAOSnmICMPTSIpvNegInNum,
             adGenAOSnmICMPTSIpvNegOutMin,  adGenAOSnmICMPTSIpvNegOutMax,
             adGenAOSnmICMPTSIpvNegOutSum, adGenAOSnmICMPTSIpvNegOutSum2,
             adGenAOSnmICMPTSIpvNegOutNum, adGenAOSnmICMPTSIpvNegRtMin,
             adGenAOSnmICMPTSIpvNegRtMax, adGenAOSnmICMPTSIpvNegRtSum,
             adGenAOSnmICMPTSIpvNegRtSum2, adGenAOSnmICMPTSIpvNegRtNum,
             adGenAOSnmICMPTSIpvAbsInMin, adGenAOSnmICMPTSIpvAbsInMax,
             adGenAOSnmICMPTSIpvAbsInSum, adGenAOSnmICMPTSIpvAbsInSum2,
             adGenAOSnmICMPTSIpvAbsInNum, adGenAOSnmICMPTSIpvAbsOutMin,
             adGenAOSnmICMPTSIpvAbsOutMax, adGenAOSnmICMPTSIpvAbsOutSum,
             adGenAOSnmICMPTSIpvAbsOutSum2, adGenAOSnmICMPTSIpvAbsOutNum,
             adGenAOSnmICMPTSIpvAbsRtMin, adGenAOSnmICMPTSIpvAbsRtMax,
             adGenAOSnmICMPTSIpvAbsRtSum, adGenAOSnmICMPTSIpvAbsRtSum2,
             adGenAOSnmICMPTSIpvAbsRtNum, adGenAOSnmICMPTSPktSentCount
                }
         STATUS  current
         DESCRIPTION
            "Objects designed to assist in retrieving the statistics of the
            ICMP timestamp probe."
         ::= { adGenAOSnmGroups 8 }


         adGenAOSnmICMPTSResponderGroup OBJECT-GROUP
         OBJECTS {
                adGenAOSnmICMPTSResponder
         }
         STATUS  current
         DESCRIPTION
            "Object designed to assist in changing the state of the ICMP
            timestamp responder."
         ::= { adGenAOSnmGroups 9 }

         adGenAOSnmTWAMPResponderGroup  OBJECT-GROUP
         OBJECTS {
                adGenAOSnmTWAMPResponder
         }
         STATUS  current
         DESCRIPTION
            "Object designed to assist in changing the state of the TWAMP
            responder."
         ::= { adGenAOSnmGroups 10 }

         adGenAOSnmICMPTSResponderStatsGroup  OBJECT-GROUP
         OBJECTS {
             adGenAOSnmICMPTSResponderStatsIndex,
             adGenAOSnmICMPTSResponderPacketsReceived,
             adGenAOSnmICMPTSResponderPacketsSent,
             adGenAOSnmClearICMPTSResponderCounters
         }
         STATUS  current
         DESCRIPTION
            "Objects designed to assist in retrieving the statistics of the
            ICMP timestamp responder."
         ::= { adGenAOSnmGroups 11 }

         adGenAOSnmTwampResponderStatsGroup  OBJECT-GROUP
         OBJECTS {
                adGenAOSnmTwampResponderStatsIndex,
                adGenAOSnmTwampResponderPacketsReceived,
                adGenAOSnmTwampResponderPacketsSent,
                adGenAOSnmTwampResponderSessionClosed,
                adGenAOSnmTwampResponderSessionOpened,
                adGenAOSnmTwampResponderSessionRejected,
                adGenAOSnmClearTwampResponderCounters
         }
         STATUS  current
         DESCRIPTION
            "Objects designed to assist in retrieving the statistics of the
            TWAMP responder."
         ::= { adGenAOSnmGroups 12 }

     adGenAOSnmCfgIEProbeGroup    OBJECT-GROUP
         OBJECTS {
                adGenAOSnmCfgIEName,
                adGenAOSnmIEDestHostname,
                adGenAOSnmIESrcIP,
                adGenAOSnmIEPacketLength,
                adGenAOSnmIEPacketPattern
                }
         STATUS  current
         DESCRIPTION
            "Objects designed to assist in configuring the ICMP echo probe."
         ::= { adGenAOSnmGroups 13 }

     adGenAOSnmTrackGroup    OBJECT-GROUP
         OBJECTS {
             adGenAOSnmTrackIndex,
             adGenAOSnmTrackName
                }
         STATUS  current
         DESCRIPTION
            "Objects designed to assist in retrieving track information."
         ::= { adGenAOSnmGroups 14 }

     adGenAOSnmNotificationGroup NOTIFICATION-GROUP
         NOTIFICATIONS { adGenAOSnmTrackStateChgFail,
                         adGenAOSnmTrackStateChgPass }
         STATUS  current
         DESCRIPTION
            "Traps which may be used to enhance event driven
            management of Network Monitoring traps."
         ::= { adGenAOSnmGroups 15 }


 -- definition of track-related traps.

	adGenAOSnmTrackStateChgFail NOTIFICATION-TYPE
	    OBJECTS { adGenAOSnmTrackIndex, adGenAOSnmTrackName }
	    STATUS  current
	    DESCRIPTION
	            "A stateChangeFail trap indicates that the specified track
	            has changed states from Pass to Fail."
	    ::= { adGenAOSnmTrackTraps 1 }

    adGenAOSnmTrackStateChgPass NOTIFICATION-TYPE
	    OBJECTS { adGenAOSnmTrackIndex, adGenAOSnmTrackName }
	    STATUS  current
	    DESCRIPTION
	            "A stateChangePass trap indicates that the specified track
	            has changed states from Fail to Pass."
	    ::= { adGenAOSnmTrackTraps 2 }
     END



