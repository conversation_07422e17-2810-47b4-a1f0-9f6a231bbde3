ADTRAN-AOS-OVER-TEMP-PROTECTION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    adIdentity
    	FROM ADTRAN-MIB
    adGenAOSConformance, adGenAOSCommon
        FROM ADTRAN-AOS;

adGenAOSOverTempProtectionMib MODULE-IDENTITY
    LAST-UPDATED "201411041615Z"
    ORGANIZATION "ADTRAN, Inc."
    CONTACT-INFO
           "        Technical Support Dept.
            		Postal: ADTRAN, Inc.
                    901 Explorer Blvd.
                    Huntsville, AL 35806

               Tel: ****** 726-8663
               Fax: ****** 963 6217
            E-mail: <EMAIL>"
    DESCRIPTION
            "The MIB module defines over-temperature configuration information and traps for AdtranOS products."
    REVISION "201411041615Z"  -- November 4, 2014 / YYYYMMDDHHMMZ
    DESCRIPTION
            "Created the adGenAosOverTempProtection MIB. Revision R11.6"
            

    ::= { adIdentity 10000 53 1 10 }

adGenAOSOverTempProtection OBJECT IDENTIFIER ::= { adGenAOSCommon 10 }
adGenAOSOverTempProtectionTrap  OBJECT IDENTIFIER ::= { adGenAOSOverTempProtection 0 }

adGenAOSOverTempProtectionWarning NOTIFICATION-TYPE
    STATUS  current
    DESCRIPTION
            "An over-temperature warning trap signifies that the warning temperature threshold has been exceeded."
    ::= { adGenAOSOverTempProtectionTrap 1 }

adGenAOSOverTempProtectionShutdown NOTIFICATION-TYPE
    STATUS  current
    DESCRIPTION
            "An over-temperature shutdown trap signifies that the shutdown temperature threshold has been exceeded, and the unit will restart into low-power mode."
    ::= { adGenAOSOverTempProtectionTrap 2 }
    
    ------------------------------------------------------------
-- Conformance information
--
adGenAOSOverTempProtectionConformance OBJECT IDENTIFIER
   ::= { adGenAOSConformance 19}

adGenAOSOverTempProtectionGroups OBJECT IDENTIFIER
   ::= { adGenAOSOverTempProtectionConformance 1 }

adGenAOSOverTempProtectionCompliances OBJECT IDENTIFIER
   ::= { adGenAOSOverTempProtectionConformance 2 }

-- Compliance statements
--

-- Full compliance statement
     adGenAOSOverTempProtectionFullCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
        "The compliance statement for SNMP entities which implement
        version 2 of the adGenAosOverTempProtection MIB. When the implementation of this MIB 
        supports adGenAOSOverTempProtectionNotificationGroup, then such an implementation can claim
        full compliance."

        MODULE  -- this module

        GROUP  adGenAOSOverTempProtectionNotificationGroup
         DESCRIPTION
            "This optional group defines the asynchronous
            notifications generated by over-temperature protection events."
            ::= { adGenAOSOverTempProtectionCompliances 1 }
 	
     adGenAOSOverTempProtectionNotificationGroup NOTIFICATION-GROUP
         NOTIFICATIONS { adGenAOSOverTempProtectionWarning, adGenAOSOverTempProtectionShutdown }
         STATUS  current
         DESCRIPTION
            "Traps which may be used to enhance event driven
            management of the chassis's over-temperature protection subsystem."
         ::= { adGenAOSOverTempProtectionGroups 1 }
    

END

