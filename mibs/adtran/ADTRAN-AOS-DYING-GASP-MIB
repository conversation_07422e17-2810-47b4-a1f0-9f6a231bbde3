ADTRAN-AOS-DYING-GASP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    adIdentity
        FROM ADTRAN-MIB
    adGenAOSConformance, adGenAOSCommon
        FROM ADTRAN-AOS;

adGenAOSDyingGaspMib MODULE-IDENTITY
    LAST-UPDATED "201501050000Z"
    ORGANIZATION "ADTRAN, Inc."
    CONTACT-INFO
           "        Technical Support Dept.
                    Postal: ADTRAN, Inc.
                    901 Explorer Blvd.
                    Huntsville, AL 35806

               Tel: ****** 726-8663
               Fax: ****** 963 6217
            E-mail: <EMAIL>"
    DESCRIPTION
            "The MIB module defines dying gasp traps for AdtranOS products."
    REVISION "201501050000Z"  -- January 5, 2015 / YYYYMMDDHHMMZ
    DESCRIPTION
            "Created the adGenAosDyingGasp MIB. Revision R11.6"
            

    ::= { adIdentity 10000 53 1 11 }

adGenAOSDyingGasp OBJECT IDENTIFIER ::= { adGenAOSCommon 11 }
adGenAOSDyingGaspTrap  OBJECT IDENTIFIER ::= { adGenAOSDyingGasp 0 }

adGenAOSDyingGaspEvent NOTIFICATION-TYPE
    STATUS  current
    DESCRIPTION
            "A dying gasp event trap signifies that the unit has unexpectedly lost power."
    ::= { adGenAOSDyingGaspTrap 1 }
    
    ------------------------------------------------------------
-- Conformance information
--
adGenAOSDyingGaspConformance OBJECT IDENTIFIER
   ::= { adGenAOSConformance 25 }

adGenAOSDyingGaspGroups OBJECT IDENTIFIER
   ::= { adGenAOSDyingGaspConformance 1 }

adGenAOSDyingGaspCompliances OBJECT IDENTIFIER
   ::= { adGenAOSDyingGaspConformance 2 }

-- Compliance statements
--

-- Full compliance statement
     adGenAOSDyingGaspFullCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
        "The compliance statement for SNMP entities which implement
        version 2 of the adGenAosDyingGasp MIB. When the implementation of this MIB 
        supports adGenAOSDyingGaspGroup, then such an implementation can claim
        full compliance."

        MODULE  -- this module

        GROUP  adGenAOSDyingGaspGroup
         DESCRIPTION
            "This optional group defines the notification generated by dying gasp events."
            ::= { adGenAOSDyingGaspCompliances 1 }
    
     adGenAOSDyingGaspGroup NOTIFICATION-GROUP
         NOTIFICATIONS { adGenAOSDyingGaspEvent }
         STATUS  current
         DESCRIPTION
            "Trap which may be used to indicate an unexpected power loss of the system."
         ::= { adGenAOSDyingGaspGroups 1 }
    

END