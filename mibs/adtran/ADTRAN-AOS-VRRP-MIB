ADTRAN-AOS-VRRP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, NOTIFICATION-TYPE, Integer32,
    OBJECT-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE, <PERSON>BJECT-<PERSON><PERSON><PERSON>, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    adIdentity
        FROM ADTRAN-MIB
    InetAddress, InetAddressType
        FROM INET-ADDRESS-MIB
    ifIndex  FROM IF-MIB
    adGenAOSConformance, adGenAOSRouter
        FROM ADTRAN-AOS;

adGenAOSVrrpMib MODULE-IDENTITY
    LAST-UPDATED "201404170000Z"
    ORGANIZATION "ADTRAN, Inc."
    CONTACT-INFO
       "      Technical Support Dept.
             Postal: ADTRAN, Inc.
             901 Explorer Blvd.
             Huntsville, AL 35806

             Tel: ****** 726-8663
             Fax: ****** 963 6217
            E-mail: <EMAIL>"
    DESCRIPTION
       "The MIB module defines VRRP V2 and V3 notifications for AdtranOS
        products and provides information about the virtual router instance."
    REVISION "201407290000Z"  -- July 29, 2014 / YYYYMMDDHHMMZ
    DESCRIPTION
    	"Revised text in descriptions."
    REVISION "201404170000Z"  -- April 17, 2014 / YYYYMMDDHHMMZ
    DESCRIPTION
       "Created the adGenAosVrrp MIB. Revision R11.3"
            
    ::= { adIdentity 10000 53 2 3}

adGenAOSVrrp     OBJECT IDENTIFIER ::= { adGenAOSRouter 3 }
--=============================================================================================

adGenAOSVrrpTrap  OBJECT IDENTIFIER ::= { adGenAOSVrrp 0 }
adGenAOSVrrpTrapCntl   OBJECT IDENTIFIER ::= { adGenAOSVrrp 1 }

--Define Vrrp Table and Entry for the table
adGenAOSVrrpTable OBJECT-TYPE
       SYNTAX   SEQUENCE OF AdGenAOSVrrpEntry
       MAX-ACCESS   not-accessible
       STATUS     current
       DESCRIPTION
            "Vrrp Router instances."
      ::= { adGenAOSVrrp 2 }

adGenAOSVrrpEntry OBJECT-TYPE
       SYNTAX       AdGenAOSVrrpEntry
       MAX-ACCESS   not-accessible
       STATUS       current
       DESCRIPTION 
          "The parameters of a particular VRRP Instance."
         INDEX { ifIndex, adGenAOSVrrpVersion, adGenAOSVrrpId,
                  adGenAOSVrrpInetAddrType  }
         ::= { adGenAOSVrrpTable 1 }


AdGenAOSVrrpEntry ::= 
       SEQUENCE {
            adGenAOSVrrpVersion        INTEGER,
            adGenAOSVrrpId             Integer32,
            adGenAOSVrrpInetAddrType   InetAddressType,            
            adGenAOSVrrpInetAddr       InetAddress,
            adGenAOSVrrpOperStatus     INTEGER,
            adGenAOSVrrpPriority       Integer32    
       }

adGenAOSVrrpVersion OBJECT-TYPE
       SYNTAX  INTEGER    {
            v2(2),
            v3(3)
       }
       MAX-ACCESS  not-accessible
       STATUS      current
       DESCRIPTION
          "Specifies the version of VRRP used in the current Virtual Router instance."
       ::= { adGenAOSVrrpEntry 1 }
       
adGenAOSVrrpId OBJECT-TYPE
       SYNTAX      Integer32 (1..255)
       MAX-ACCESS  not-accessible
       STATUS      current
       DESCRIPTION
           "Identifies a row in the AdGenAOSVrrpTable. The ID is unique to the VRRP 
            instance of VRRP Type (v2 and v3). "
       ::= { adGenAOSVrrpEntry 2 }

adGenAOSVrrpInetAddrType OBJECT-TYPE
       SYNTAX      InetAddressType
       MAX-ACCESS  not-accessible
       STATUS      current
       DESCRIPTION
                "Identifies the primary IP address type."
       ::= { adGenAOSVrrpEntry 3 }

adGenAOSVrrpInetAddr  OBJECT-TYPE
       SYNTAX      InetAddress
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
                "Identifies the primary IP address."
       ::= { adGenAOSVrrpEntry 4 }

adGenAOSVrrpOperStatus OBJECT-TYPE
       SYNTAX      INTEGER { 
            initialize(1),
            backup(2),
            master(3)
        }
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
                "Identifies if the router is the master or backup."
       ::= { adGenAOSVrrpEntry 5 }

adGenAOSVrrpPriority OBJECT-TYPE
       SYNTAX      Integer32 (0..255)
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
              "Displays the priority of the virtual router instance."
       ::= { adGenAOSVrrpEntry 6 }

--Trap Control information
adGenAOSVrrpMasterTrapCntl OBJECT-TYPE
       SYNTAX      INTEGER   { enabled(1), disabled(2) }
       MAX-ACCESS  read-write
       STATUS      current
       DESCRIPTION
            "This variable indicates whether the system produces
            the adGenAOSVrrpMaster trap."
       DEFVAL { disabled }
       ::= { adGenAOSVrrpTrapCntl 1 }

--Trap notification information
adGenAOSVrrpMasterTrap NOTIFICATION-TYPE
       OBJECTS {
       		adGenAOSVrrpOperStatus
       }
       STATUS  current
       DESCRIPTION
            "A Master Trap signifies that one of the virtual routers has changed 
            its state. Either from Master to Backup or Backup to Master"
       ::= { adGenAOSVrrpTrap 1 }
    
    ------------------------------------------------------------
-- Conformance information
--
adGenAOSVrrpConformance OBJECT IDENTIFIER
    ::= { adGenAOSConformance  20 }

adGenAOSVrrpGroups OBJECT IDENTIFIER
    ::= { adGenAOSVrrpConformance 1 }

adGenAOSVrrpCompliances OBJECT IDENTIFIER
    ::= { adGenAOSVrrpConformance 2 }

-- Compliance statements
--

-- Full compliance statement
    adGenAOSVrrpFullCompliance MODULE-COMPLIANCE
       STATUS  current
       DESCRIPTION
        "The compliance statement for SNMP entities which implement
        VRRP V2 and V3 in adGenAosVrrp MIB. When this MIB is implemented
        with support for read-write, then such an implementation can claim
        full compliance."

       MODULE  -- this module

       GROUP adGenAOSVrrpObjectGroup
          DESCRIPTION
            "A collection of objects that constitute the VRRP Table."

       GROUP adGenAOSVrrpTrapCfgGroup
          DESCRIPTION
            "A collection of objects providing configuration for the VRRP trap."
        
       GROUP adGenAOSVrrpTrapGroup
          DESCRIPTION
            "This group is used for the management of
            asynchronous notifications for VRRP state changes."

       GROUP  adGenAOSVrrpNotificationGroup
          DESCRIPTION
            "This optional group defines the asynchronous
            notifications generated VRRP state changes."

      ::= {adGenAOSVrrpCompliances 1}
            
--
-- Read-Only Compliance
--
    adGenAOSVrrpReadOnlyCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
            "The compliance statement for SNMP entities which implement
            VRRP V2 and V3 in adGenAOSVrrp MIB. When this MIB is implemented
            without support for read-create (i.e. in read-only mode),
            then such an implementation can claim read-only compliance.
      		A virtual router can be defined but cannot be modified using 
      		this MIB."

        MODULE  -- this module

       GROUP  adGenAOSVrrpObjectGroup
          DESCRIPTION
           "A collection of objects that constitute the VRRP Table."

      ::= { adGenAOSVrrpCompliances 2 }

--Group Definitions
    adGenAOSVrrpObjectGroup   OBJECT-GROUP
       OBJECTS{  adGenAOSVrrpInetAddr, adGenAOSVrrpPriority }
       STATUS current
       DESCRIPTION
            "The objects that define VRRP table."
       ::= { adGenAOSVrrpGroups 1 }
   
    adGenAOSVrrpTrapCfgGroup    OBJECT-GROUP
       OBJECTS {
                adGenAOSVrrpMasterTrapCntl
          }
       STATUS  current
       DESCRIPTION
            "This group contains the objects necessary to enable/disable
            VRRP traps."
       ::= { adGenAOSVrrpGroups 2 }
        
    adGenAOSVrrpTrapGroup    OBJECT-GROUP
       OBJECTS { 
               adGenAOSVrrpOperStatus
         }
       STATUS  current
       DESCRIPTION
            "The objects necessary to control VRRP state notification messages."
       ::= { adGenAOSVrrpGroups 3 }

    adGenAOSVrrpNotificationGroup NOTIFICATION-GROUP
       NOTIFICATIONS { adGenAOSVrrpMasterTrap }
       STATUS  current
       DESCRIPTION
          "Traps which may be used to detect the change of state
          in any of the virtual router instances."
       ::= { adGenAOSVrrpGroups 4 }

END

