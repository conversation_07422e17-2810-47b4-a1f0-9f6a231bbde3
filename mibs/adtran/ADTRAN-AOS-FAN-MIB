ADTRAN-AOS-FAN-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, NOTIFICATION-TYPE, Integer32,
    OBJECT-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE, OBJECT-G<PERSON><PERSON>, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    adIdentity
    	FROM ADTRAN-MIB
    adGenAOSConformance, adGenAOSCommon
        FROM ADTRAN-AOS;

adGenAOSFanMib MODULE-IDENTITY
    LAST-UPDATED "201310220000Z"
    ORGANIZATION "ADTRAN, Inc."
    CONTACT-INFO
           "        Technical Support Dept.
            		Postal: ADTRAN, Inc.
                    901 Explorer Blvd.
                    Huntsville, AL 35806

               Tel: ****** 726-8663
               Fax: ****** 963 6217
            E-mail: <EMAIL>"
    DESCRIPTION
            "The MIB module defines fan configuration information and traps for AdtranOS products."
    REVISION "201310220000Z"  -- October 22, 2013 / YYYYMMDDHHMMZ
    DESCRIPTION
            "Created the adGenAosFan MIB. Revision R10.11"
            

    ::= { adIdentity 10000 53 1 8 }

adGenAOSFan OBJECT IDENTIFIER ::= { adGenAOSCommon 8 }
adGenAOSFanTrap  OBJECT IDENTIFIER ::= { adGenAOSFan 0 }
adGenAOSFanTrapControl   OBJECT IDENTIFIER ::= { adGenAOSFan 1 }
adGenAOSFanInfo OBJECT IDENTIFIER ::= { adGenAOSFan 2 }


adGenAOSFanTrapEnable OBJECT-TYPE
         SYNTAX  INTEGER     { enabled(1), disabled(2) }
         MAX-ACCESS   read-write
         STATUS   current
         DESCRIPTION
            "This variable indicates whether the system produces
            the fan failure trap."
         DEFVAL { disabled }
	::= { adGenAOSFanTrapControl 1 }

adGenAOSFanNumber OBJECT-TYPE
        SYNTAX    Integer32
	    MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
        "A numerical representation of the chassis's fan."
        ::= { adGenAOSFanInfo 1 }

adGenAOSFanFailure NOTIFICATION-TYPE
	OBJECTS { adGenAOSFanNumber }
    STATUS  current
    DESCRIPTION
            "A fan failure trap signifies that one of the fans inside the chassis has failed."
    ::= { adGenAOSFanTrap 1 }
    
    ------------------------------------------------------------
-- Conformance information
--
adGenAOSFanConformance OBJECT IDENTIFIER
   ::= { adGenAOSConformance 17 }

adGenAOSFanGroups OBJECT IDENTIFIER
   ::= { adGenAOSFanConformance 1 }

adGenAOSFanCompliances OBJECT IDENTIFIER
   ::= { adGenAOSFanConformance 2 }

-- Compliance statements
--

-- Full compliance statement
     adGenAOSFanFullCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
        "The compliance statement for SNMP entities which implement
        version 2 of the adGenAosFan MIB. When this MIB is implemented
        with support for read-write, then such an implementation can claim
        full compliance."

         MODULE  -- this module

         GROUP adGenAOSFanTrapCfgGroup
         DESCRIPTION
            "A collection of objects providing configuration for the fan trap."
 		
 		GROUP adGenAOSFanTrapGroup
         DESCRIPTION
            "This group is used for the management of
            asynchronous notifications of fan failures."
        
        GROUP  adGenAOSFanNotificationGroup
         DESCRIPTION
            "This optional group defines the asynchronous
            notifications generated by fan failures."
            ::= { adGenAOSFanCompliances 1 }
 	
 		adGenAOSFanTrapCfgGroup    OBJECT-GROUP
         OBJECTS {
         			adGenAOSFanTrapEnable
         		 }
         STATUS  current
         DESCRIPTION
            "This group contains the objects necessary to enable/disable
            fan failure traps."
         ::= { adGenAOSFanGroups 1 }
 		
 		adGenAOSFanTrapGroup    OBJECT-GROUP
         OBJECTS {
         			adGenAOSFanNumber
                 }
         STATUS  current
         DESCRIPTION
            "The objects necessary to control fan notification messages."
         ::= { adGenAOSFanGroups 2 }

     adGenAOSFanNotificationGroup NOTIFICATION-GROUP
         NOTIFICATIONS { adGenAOSFanFailure }
         STATUS  current
         DESCRIPTION
            "Traps which may be used to enhance event driven
            management of the chassis's fan."
         ::= { adGenAOSFanGroups 3 }
    

END
