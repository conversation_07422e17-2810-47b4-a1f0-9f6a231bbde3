ADTRAN-MEF-PER-COS-PER-UNI-PERF-HISTORY-MIB DEFINITIONS ::= BEGIN

IMPORTS
   MODULE-IDENTITY,
   OBJECT-TYPE,
   Integer32, Unsigned32
      FROM SNMPv2-SMI
   ifIndex
      FROM IF-MIB
   HCPerfTimeElapsed,
   HCPerfValidIntervals,
   HCPerfInvalidIntervals,
   HCPerfCurrentCount,
   HCPer<PERSON>IntervalCount,
   HCPerfTotalCount
      FROM HC-PerfHist-TC-MIB
   MODULE-COMPLIANCE,
   OBJECT-GROUP
      FROM SNMPv2-CONF
   adIdentity
      FROM ADTRAN-MIB
   adGenAOSMef,
   adGenAOSConformance
      FROM ADTRAN-AOS;

adGenAosMefPerCosPerUniPerfHistoryMib MODULE-IDENTITY
   LAST-UPDATED "201409100000Z" -- September 10, 2014
   ORGANIZATION "ADTRAN Inc."
   CONTACT-INFO
     "Info:   www.adtran.com
      Postal: ADTRAN, Inc.
              901 Explorer Blvd.
              Huntsville, AL 35806
      Tel:    ****** 423-8726
      E-mail: <EMAIL>"

   DESCRIPTION
     "This MIB module defines high capacity performance statistics
      per COS per UNI within an AOS product.

      Copyright (C) ADTRAN, Inc. (2014)."

   REVISION    "201409100000Z" -- September 10, 2014
   DESCRIPTION
     "Initial version"
    ::= { adIdentity 10000 53 9 2 }

adGenAosMefPerCosPerUniPerfHistory OBJECT IDENTIFIER ::= { adGenAOSMef 2 }

------------------------------------------------------------
-- Current Table for Interface Performance History
--
adMefPerCosPerUniPhCurTable OBJECT-TYPE
   SYNTAX      SEQUENCE OF AdMefPerCosPerUniPhCurEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "This table contains current performance history information that has been
      recorded since the last 15 minute interval ended and from when the last
      1 day interval ended.  This table is indexed by ifIndex and 
      adMefPerCosPerUniPhCurQueueNumber."
   ::= { adGenAosMefPerCosPerUniPerfHistory 1 }

adMefPerCosPerUniPhCurEntry OBJECT-TYPE
   SYNTAX      AdMefPerCosPerUniPhCurEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "This specifies the information contained in one entry of the
      adMefPerCosPerUniPhCurTable.  It is indexed by an interface's ifIndex,
      and the queue number."
   INDEX { ifIndex, adMefPerCosPerUniPhCurQueueNumber }
   ::= { adMefPerCosPerUniPhCurTable 1 }

AdMefPerCosPerUniPhCurEntry ::=
   SEQUENCE
   {
      adMefPerCosPerUniPhCurQueueNumber                      Unsigned32,
      adMefPerCosPerUniPhCurTimeElapsed15Min                 HCPerfTimeElapsed,
      adMefPerCosPerUniPhCurValidIntervals15Min              HCPerfValidIntervals,
      adMefPerCosPerUniPhCurInvalidIntervals15Min            HCPerfInvalidIntervals,
      adMefPerCosPerUniPhCurIngressGreenOctets15Min          HCPerfCurrentCount,
      adMefPerCosPerUniPhCurIngressGreenFrames15Min          HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenOctets15Min           HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenFrames15Min           HCPerfCurrentCount,
      adMefPerCosPerUniPhCurIngressGreenFrameDiscards15Min   HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenFrameDiscards15Min    HCPerfCurrentCount,
      adMefPerCosPerUniPhCurIngressGreenOctetDiscards15Min   HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenOctetDiscards15Min    HCPerfCurrentCount,
      adMefPerCosPerUniPhCurTimeElapsed1Day                  HCPerfTimeElapsed,
      adMefPerCosPerUniPhCurValidIntervals1Day               HCPerfValidIntervals,
      adMefPerCosPerUniPhCurInvalidIntervals1Day             HCPerfInvalidIntervals,
      adMefPerCosPerUniPhCurIngressGreenOctets1Day           HCPerfCurrentCount,
      adMefPerCosPerUniPhCurIngressGreenFrames1Day           HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenOctets1Day            HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenFrames1Day            HCPerfCurrentCount,
      adMefPerCosPerUniPhCurIngressGreenFrameDiscards1Day    HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenFrameDiscards1Day     HCPerfCurrentCount,
      adMefPerCosPerUniPhCurIngressGreenOctetDiscards1Day    HCPerfCurrentCount,
      adMefPerCosPerUniPhCurEgressGreenOctetDiscards1Day     HCPerfCurrentCount
   }

adMefPerCosPerUniPhCurQueueNumber OBJECT-TYPE
   SYNTAX      Unsigned32 (0..7)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "UNI Interface queue number."
   ::= { adMefPerCosPerUniPhCurEntry 1 }

adMefPerCosPerUniPhCurTimeElapsed15Min OBJECT-TYPE
   SYNTAX      HCPerfTimeElapsed
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Total elapsed seconds in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 2 }

adMefPerCosPerUniPhCurValidIntervals15Min OBJECT-TYPE
   SYNTAX      HCPerfValidIntervals
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Number of valid 15 minute intervals over the last 24 hours."
   ::= { adMefPerCosPerUniPhCurEntry 3 }

adMefPerCosPerUniPhCurInvalidIntervals15Min OBJECT-TYPE
   SYNTAX      HCPerfInvalidIntervals
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Number of invalid 15 minute intervals over the last 24 hours."
   ::= { adMefPerCosPerUniPhCurEntry 4 }

adMefPerCosPerUniPhCurIngressGreenOctets15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 5 }

adMefPerCosPerUniPhCurIngressGreenFrames15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 6 }

adMefPerCosPerUniPhCurEgressGreenOctets15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 7 }

adMefPerCosPerUniPhCurEgressGreenFrames15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 8 }

adMefPerCosPerUniPhCurIngressGreenFrameDiscards15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames discarded in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 9 }

adMefPerCosPerUniPhCurEgressGreenFrameDiscards15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames discarded in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 10 }

adMefPerCosPerUniPhCurIngressGreenOctetDiscards15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets discarded in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 11 }

adMefPerCosPerUniPhCurEgressGreenOctetDiscards15Min OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green octets discarded in the current 15 minute interval."
   ::= { adMefPerCosPerUniPhCurEntry 12 }

adMefPerCosPerUniPhCurTimeElapsed1Day OBJECT-TYPE
   SYNTAX      HCPerfTimeElapsed
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Total elapsed seconds in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 13 }

adMefPerCosPerUniPhCurValidIntervals1Day OBJECT-TYPE
   SYNTAX      HCPerfValidIntervals
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Number of valid 1 day intervals available."
   ::= { adMefPerCosPerUniPhCurEntry 14 }

adMefPerCosPerUniPhCurInvalidIntervals1Day OBJECT-TYPE
   SYNTAX      HCPerfInvalidIntervals
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Number of invalid 1 day intervals available."
   ::= { adMefPerCosPerUniPhCurEntry 15 }

adMefPerCosPerUniPhCurIngressGreenOctets1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 16 }

adMefPerCosPerUniPhCurIngressGreenFrames1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 17 }

adMefPerCosPerUniPhCurEgressGreenOctets1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green octets in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 18 }

adMefPerCosPerUniPhCurEgressGreenFrames1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 19 }

adMefPerCosPerUniPhCurIngressGreenFrameDiscards1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames discarded in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 20 }

adMefPerCosPerUniPhCurEgressGreenFrameDiscards1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames discarded in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 21 }

adMefPerCosPerUniPhCurIngressGreenOctetDiscards1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets discarded in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 22 }

adMefPerCosPerUniPhCurEgressGreenOctetDiscards1Day OBJECT-TYPE
   SYNTAX      HCPerfCurrentCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green octets discarded in the current 1 day interval."
   ::= { adMefPerCosPerUniPhCurEntry 23 }

------------------------------------------------------------
-- 15 Minute Interval Table for Interface Performance History
--
adMefPerCosPerUniPh15MinIntervalTable OBJECT-TYPE
   SYNTAX      SEQUENCE OF AdMefPerCosPerUniPh15MinIntervalEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "This table contains performance history information for each valid 15
      minute interval.  This table is indexed by ifIndex, the queue number,
      and the interval number."
   ::= { adGenAosMefPerCosPerUniPerfHistory 2 }

adMefPerCosPerUniPh15MinIntervalEntry OBJECT-TYPE
   SYNTAX      AdMefPerCosPerUniPh15MinIntervalEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "An entry in the adMefPerCosPerUniPh15MinIntervalTable."
   INDEX { ifIndex, adMefPerCosPerUniPh15MinQueueNumber, adMefPerCosPerUniPh15MinIntervalNumber }
   ::= { adMefPerCosPerUniPh15MinIntervalTable 1 }

AdMefPerCosPerUniPh15MinIntervalEntry ::=
   SEQUENCE
   {
      adMefPerCosPerUniPh15MinQueueNumber                    Unsigned32,
      adMefPerCosPerUniPh15MinIntervalNumber                 Integer32,
      adMefPerCosPerUniPh15MinIngressGreenOctets             HCPerfIntervalCount,
      adMefPerCosPerUniPh15MinIngressGreenFrames             HCPerfIntervalCount,
      adMefPerCosPerUniPh15MinEgressGreenOctets              HCPerfIntervalCount,
      adMefPerCosPerUniPh15MinEgressGreenFrames              HCPerfIntervalCount,
      adMefPerCosPerUniPh15MinIngressGreenFrameDiscards      HCPerfIntervalCount,
      adMefPerCosPerUniPh15MinEgressGreenFrameDiscards       HCPerfIntervalCount,
      adMefPerCosPerUniPh15MinIngressGreenOctetDiscards      HCPerfIntervalCount,
      adMefPerCosPerUniPh15MinEgressGreenOctetDiscards       HCPerfIntervalCount
   }

adMefPerCosPerUniPh15MinQueueNumber OBJECT-TYPE
   SYNTAX      Unsigned32 (0..7)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "UNI Interface queue number."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 1 }

adMefPerCosPerUniPh15MinIntervalNumber OBJECT-TYPE
   SYNTAX      Integer32 (1..96)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "Performance history interval number.  Interval 1 is the most
      recent previous interval; interval 96 is 24 hours ago.
      Intervals 2..96 are optional."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 2 }

adMefPerCosPerUniPh15MinIngressGreenOctets OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 3 }

adMefPerCosPerUniPh15MinIngressGreenFrames OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 4 }

adMefPerCosPerUniPh15MinEgressGreenOctets OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green octets in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 5 }

adMefPerCosPerUniPh15MinEgressGreenFrames OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 6 }

adMefPerCosPerUniPh15MinIngressGreenFrameDiscards OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames discarded in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 7 }

adMefPerCosPerUniPh15MinEgressGreenFrameDiscards OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames discarded in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 8 }

adMefPerCosPerUniPh15MinIngressGreenOctetDiscards OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets discarded in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 9 }

adMefPerCosPerUniPh15MinEgressGreenOctetDiscards OBJECT-TYPE
   SYNTAX      HCPerfIntervalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green octets discarded in the 15 minute interval."
   ::= { adMefPerCosPerUniPh15MinIntervalEntry 10 }

------------------------------------------------------------
-- 1 Day Interval Table for Interface Performance History
--
adMefPerCosPerUniPh1DayIntervalTable OBJECT-TYPE
   SYNTAX      SEQUENCE OF AdMefPerCosPerUniPh1DayIntervalEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "This table contains performance history information for each valid 1
      day interval.  This table is indexed by by ifIndex, the queue number,
      and the interval number."
   ::= { adGenAosMefPerCosPerUniPerfHistory 3 }

adMefPerCosPerUniPh1DayIntervalEntry OBJECT-TYPE
   SYNTAX      AdMefPerCosPerUniPh1DayIntervalEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "An entry in the adMefPerCosPerUniPh1DayIntervalTable."
   INDEX { ifIndex, adMefPerCosPerUniPh1DayQueueNumber, adMefPerCosPerUniPh1DayIntervalNumber }
   ::= { adMefPerCosPerUniPh1DayIntervalTable 1 }

AdMefPerCosPerUniPh1DayIntervalEntry ::=
   SEQUENCE
   {
      adMefPerCosPerUniPh1DayQueueNumber                     Unsigned32,
      adMefPerCosPerUniPh1DayIntervalNumber                  Integer32,
      adMefPerCosPerUniPh1DayIngressGreenOctets              HCPerfTotalCount,
      adMefPerCosPerUniPh1DayIngressGreenFrames              HCPerfTotalCount,
      adMefPerCosPerUniPh1DayEgressGreenOctets               HCPerfTotalCount,
      adMefPerCosPerUniPh1DayEgressGreenFrames               HCPerfTotalCount,
      adMefPerCosPerUniPh1DayIngressGreenFrameDiscards       HCPerfTotalCount,
      adMefPerCosPerUniPh1DayEgressGreenFrameDiscards        HCPerfTotalCount,
      adMefPerCosPerUniPh1DayIngressGreenOctetDiscards       HCPerfTotalCount,
      adMefPerCosPerUniPh1DayEgressGreenOctetDiscards        HCPerfTotalCount
   }

adMefPerCosPerUniPh1DayQueueNumber OBJECT-TYPE
   SYNTAX      Unsigned32 (0..7)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "UNI Interface queue number."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 1 }

adMefPerCosPerUniPh1DayIntervalNumber OBJECT-TYPE
   SYNTAX      Integer32 (1..30)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
     "Performance history interval number.  Interval 1 is the most recent
      previous day; interval 7 is 7 days ago.  Intervals 2..30 are optional."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 2 }

adMefPerCosPerUniPh1DayIngressGreenOctets OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 3 }

adMefPerCosPerUniPh1DayIngressGreenFrames OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 4 }

adMefPerCosPerUniPh1DayEgressGreenOctets OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green octets in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 5 }

adMefPerCosPerUniPh1DayEgressGreenFrames OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 6 }

adMefPerCosPerUniPh1DayIngressGreenFrameDiscards OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green frames discarded in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 7 }

adMefPerCosPerUniPh1DayEgressGreenFrameDiscards OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green frames discarded in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 8 }

adMefPerCosPerUniPh1DayIngressGreenOctetDiscards OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of ingress green octets discarded in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 9 }

adMefPerCosPerUniPh1DayEgressGreenOctetDiscards OBJECT-TYPE
   SYNTAX      HCPerfTotalCount
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
     "Count of egress green octets discarded in the 1 day interval."
   ::= { adMefPerCosPerUniPh1DayIntervalEntry 10 }

------------------------------------------------------------
-- Conformance information
--
adGenAosMefPerCosPerUniPerfHistoryConformance OBJECT IDENTIFIER
   ::= { adGenAOSConformance 21 }

adGenAosMefPerCosPerUniPerfHistoryGroups OBJECT IDENTIFIER
   ::= { adGenAosMefPerCosPerUniPerfHistoryConformance 1 }

adGenAosMefPerCosPerUniPerfHistoryCompliances OBJECT IDENTIFIER
   ::= { adGenAosMefPerCosPerUniPerfHistoryConformance 2 }

-- Compliance statements
--
adGenAosMefPerCosPerUniPerfHistoryCompliance MODULE-COMPLIANCE
   STATUS  current
   DESCRIPTION
     "The compliance statement for SNMPv2 entities which
      implement UNI interface per-queue performance history."
   MODULE
   MANDATORY-GROUPS {
      adMefPerCosPerUniPhCurGroup,
      adMefPerCosPerUniPh15MinIntervalGroup,
      adMefPerCosPerUniPh1DayIntervalGroup
   }
   ::= { adGenAosMefPerCosPerUniPerfHistoryCompliances 1 }

-- Units of conformance
--
adMefPerCosPerUniPhCurGroup OBJECT-GROUP
   OBJECTS {
      adMefPerCosPerUniPhCurTimeElapsed15Min,
      adMefPerCosPerUniPhCurValidIntervals15Min,
      adMefPerCosPerUniPhCurInvalidIntervals15Min,
      adMefPerCosPerUniPhCurIngressGreenOctets15Min,
      adMefPerCosPerUniPhCurIngressGreenFrames15Min,
      adMefPerCosPerUniPhCurEgressGreenOctets15Min,
      adMefPerCosPerUniPhCurEgressGreenFrames15Min,
      adMefPerCosPerUniPhCurIngressGreenFrameDiscards15Min,
      adMefPerCosPerUniPhCurEgressGreenFrameDiscards15Min,
      adMefPerCosPerUniPhCurIngressGreenOctetDiscards15Min,
      adMefPerCosPerUniPhCurEgressGreenOctetDiscards15Min,
      adMefPerCosPerUniPhCurTimeElapsed1Day,
      adMefPerCosPerUniPhCurValidIntervals1Day,
      adMefPerCosPerUniPhCurInvalidIntervals1Day,
      adMefPerCosPerUniPhCurIngressGreenOctets1Day,
      adMefPerCosPerUniPhCurIngressGreenFrames1Day,
      adMefPerCosPerUniPhCurEgressGreenOctets1Day,
      adMefPerCosPerUniPhCurEgressGreenFrames1Day,
      adMefPerCosPerUniPhCurIngressGreenFrameDiscards1Day,
      adMefPerCosPerUniPhCurEgressGreenFrameDiscards1Day,
      adMefPerCosPerUniPhCurIngressGreenOctetDiscards1Day,
      adMefPerCosPerUniPhCurEgressGreenOctetDiscards1Day
   }
   STATUS  current
   DESCRIPTION
     "The Current Group."
   ::= { adGenAosMefPerCosPerUniPerfHistoryGroups 1 }

adMefPerCosPerUniPh15MinIntervalGroup OBJECT-GROUP
   OBJECTS {
      adMefPerCosPerUniPh15MinIngressGreenOctets,
      adMefPerCosPerUniPh15MinIngressGreenFrames,
      adMefPerCosPerUniPh15MinEgressGreenOctets,
      adMefPerCosPerUniPh15MinEgressGreenFrames,
      adMefPerCosPerUniPh15MinIngressGreenFrameDiscards,
      adMefPerCosPerUniPh15MinEgressGreenFrameDiscards,
      adMefPerCosPerUniPh15MinIngressGreenOctetDiscards,
      adMefPerCosPerUniPh15MinEgressGreenOctetDiscards
   }
   STATUS  current
   DESCRIPTION
     "The 15 minute interval group."
   ::= { adGenAosMefPerCosPerUniPerfHistoryGroups 2 }

adMefPerCosPerUniPh1DayIntervalGroup OBJECT-GROUP
   OBJECTS {
      adMefPerCosPerUniPh1DayIngressGreenOctets,
      adMefPerCosPerUniPh1DayIngressGreenFrames,
      adMefPerCosPerUniPh1DayEgressGreenOctets,
      adMefPerCosPerUniPh1DayEgressGreenFrames,
      adMefPerCosPerUniPh1DayIngressGreenFrameDiscards,
      adMefPerCosPerUniPh1DayEgressGreenFrameDiscards,
      adMefPerCosPerUniPh1DayIngressGreenOctetDiscards,
      adMefPerCosPerUniPh1DayEgressGreenOctetDiscards
   }
   STATUS  current
   DESCRIPTION
     "The 1 day interval group."
   ::= { adGenAosMefPerCosPerUniPerfHistoryGroups 3 }

END

