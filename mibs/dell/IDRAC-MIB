------------------------------------------------------------------------------
--
-- Title: iDRAC MIB
--
-- Version: 3.2
-- Date: 19 January 2015
--
-- Description: This MIB defines MIB objects that make iDRAC data available
-- to SNMP management applications.
--
-- Note that the iDRAC MIB file is published in both types of SMI (Structure
-- of Managed Information) notations: SMIv1 and SMIv2. This copy of the iDRAC
-- MIB file is the SMIv1 version of the MIB file.
--
-- Copyright (c) 2012-2015 Dell Inc.
-- All Rights Reserved.
--
-- Note: The information and functionality described by this MIB file,
-- like many MIB files, is subject to change without notice.
-- Please examine the version number of this MIB and compare it
-- to the version number you are expecting.
--
-- OID Format Legend:
--   <a> = attribute ID
--   <i> = index ID
--   <n> = trap ID
--
------------------------------------------------------------------------------


------------------------------------------------------------------------------
-- Begin MIB
------------------------------------------------------------------------------

IDRAC-MIB DEFINITIONS ::= BEGIN

IMPORTS
    enterprises
        FROM RFC1155-SMI
    OBJECT-TYPE
        FROM RFC-1212
    TRAP-TYPE
        FROM RFC-1215
    DisplayString
        FROM RFC1213-MIB;

------------------------------------------------------------------------------
-- Object Identifiers
------------------------------------------------------------------------------

-- Enterprise ID
dell                            OBJECT IDENTIFIER ::= { enterprises    674   }

-- Server3 Branch
server3                         OBJECT IDENTIFIER ::= { dell           10892 }

-- Server3 Groups
outOfBandGroup                  OBJECT IDENTIFIER ::= { server3        5     }

-- Server3 Out-of-Band Groups
--
-- The informationGroup provides information to discover the system (RAC,
-- modular enclosure/chassis, and server) and its associated properties.
--
informationGroup                OBJECT IDENTIFIER ::= { outOfBandGroup 1     }
racInfoGroup                    OBJECT IDENTIFIER ::= { informationGroup 1   }
chassisInfoGroup                OBJECT IDENTIFIER ::= { informationGroup 2   }
systemInfoGroup                 OBJECT IDENTIFIER ::= { informationGroup 3   }
statusGroup                     OBJECT IDENTIFIER ::= { outOfBandGroup 2     }
alertGroup                      OBJECT IDENTIFIER ::= { outOfBandGroup 3     }
alertVariablesGroup             OBJECT IDENTIFIER ::= { alertGroup     1     }
alertTrapGroup                  OBJECT IDENTIFIER ::= { alertGroup     2     }
systemAlertTrapGroup            OBJECT IDENTIFIER ::= { alertTrapGroup 1     }
storageAlertTrapGroup           OBJECT IDENTIFIER ::= { alertTrapGroup 2     }
updatesAlertTrapGroup           OBJECT IDENTIFIER ::= { alertTrapGroup 3     }
auditAlertTrapGroup             OBJECT IDENTIFIER ::= { alertTrapGroup 4     }
configurationAlertTrapGroup     OBJECT IDENTIFIER ::= { alertTrapGroup 5     }

-- System details start
systemDetailsGroup              OBJECT IDENTIFIER ::= { outOfBandGroup 4        }
mIBVersionGroup                 OBJECT IDENTIFIER ::= { systemDetailsGroup 1    }
systemStateGroup                OBJECT IDENTIFIER ::= { systemDetailsGroup 200  }
chassisInformationGroup         OBJECT IDENTIFIER ::= { systemDetailsGroup 300  }
powerGroup                      OBJECT IDENTIFIER ::= { systemDetailsGroup 600  }
thermalGroup                    OBJECT IDENTIFIER ::= { systemDetailsGroup 700  }
deviceGroup                     OBJECT IDENTIFIER ::= { systemDetailsGroup 1100 }
slotGroup                       OBJECT IDENTIFIER ::= { systemDetailsGroup 1200  }
fruGroup                        OBJECT IDENTIFIER ::= { systemDetailsGroup 2000  }
-- System details end

-- Storage details start
storageDetailsGroup             OBJECT IDENTIFIER ::= { outOfBandGroup 5      }
software                        OBJECT IDENTIFIER ::= { storageDetailsGroup 1 }
storageManagement               OBJECT IDENTIFIER ::= { software 20           }
physicalDevices                 OBJECT IDENTIFIER ::= { storageManagement 130 }
logicalDevices                  OBJECT IDENTIFIER ::= { storageManagement 140 }
-- Storage details end


------------------------------------------------------------------------------
-- Textual Conventions
------------------------------------------------------------------------------

StringType                      ::= DisplayString (SIZE (0..1023))
String64                        ::= DisplayString (SIZE (0..64))
FQDDString                      ::= DisplayString (SIZE (0..512))
MACAddress                      ::= OCTET STRING (SIZE(6))
ObjectRange                     ::= INTEGER (1..128)
Unsigned8BitRange               ::= INTEGER (0..255)
Unsigned16BitRange              ::= INTEGER (0..65535)
Unsigned32BitRange              ::= INTEGER (0..2147483647)
Signed32BitRange                ::= INTEGER (-2147483647..2147483647)
BooleanType                     ::= INTEGER (0..1)    -- 0 = FALSE, 1 = TRUE

-- DateName dates are defined in the displayable format
--   yyyymmddHHMMSS.uuuuuu+ooo
-- where yyyy is the year, mm is the month number, dd is the day of the month,
-- HHMMSS are the hours, minutes and seconds, respectively, uuuuuu is the
-- number of microseconds, and +ooo is the offset from UTC in minutes. If east
-- of UTC, the number is preceded by a plus (+) sign, and if west of UTC, the
-- number is preceded by a minus (-) sign.
--
-- For example, Wednesday, May 25, 1994, at 1:30:15 PM EDT
--   would be represented as: 19940525133015.000000-300
--
-- Values must be zero-padded if necessary, like "05" in the example above.
-- If a value is not supplied for a field, each character in the field
-- must be replaced with asterisk ('*') characters.
DateName                    ::= DisplayString (SIZE (25))

-- Note About Bit Fields:
-- Attributes with definitions of xxxCapabilities, xxxCapabilitiesUnique,
-- xxxSettings, xxxSettingsUnique and xxxFeatureFlags are integers
-- representing a series of bit definitions.  They are NOT enumerations and
-- should be treated as bit fields.  The value is passed as a decimal value;
-- it should be converted to hex, and the appropriate bits should be parsed
-- from that.  Some of the more common bit combinations are defined in some
-- variables, but not all combinations are or will be defined.

--
-- Generic State Capabilities
--
StateCapabilitiesFlags          ::= INTEGER {
    -- If set to 0 (zero), object has no state capabilities
    unknownCapabilities(1),     -- object's state capabilities are unknown
    -- The object's state capabilities allow it to be set to:
    enableCapable(2),           -- be disabled (offline) or be enabled (online)
    notReadyCapable(4),         -- not ready
    enableAndNotReadyCapable(6)
}

--
-- Generic State Settings
--
StateSettingsFlags              ::= INTEGER {
    -- If set to 0 (zero), object has no state settings enabled and is disabled
    unknown(1),             -- object's state is unknown
    enabled(2),             -- object's state is disabled (offline) if bit is off
                            -- or enabled (online) if bit is on
    notReady(4),            -- object's state is not ready
    enabledAndNotReady(6)
}

--
-- Probe Capabilities
--
ProbeCapabilitiesFlags                          ::= INTEGER {
    -- If set to 0 (zero) the object has no probe capabilities
    -- The object's probe capabilities allow it to be set to:
    upperNonCriticalThresholdSetCapable(1),     -- upper noncritical threshold can be set
    lowerNonCriticalThresholdSetCapable(2),     -- lower noncritical threshold can be set
    upperNonCriticalThresholdDefaultCapable(4), -- upper noncritical threshold can be set to default
    lowerNonCriticalThresholdDefaultCapable(8)  -- lower noncritical threshold can be set to default
}

--
-- Probe Status
--
StatusProbeEnum             ::= INTEGER {
    other(1),               -- probe status is not one of the following:
    unknown(2),             -- probe status is unknown (not known or monitored)
    ok(3),                  -- probe is reporting a value within the thresholds
    nonCriticalUpper(4),    -- probe has crossed upper noncritical threshold
    criticalUpper(5),       -- probe has crossed upper critical threshold
    nonRecoverableUpper(6), -- probe has crossed upper non-recoverable threshold
    nonCriticalLower(7),    -- probe has crossed lower noncritical threshold
    criticalLower(8),       -- probe has crossed lower critical threshold
    nonRecoverableLower(9), -- probe has crossed lower non-recoverable threshold
    failed(10)              -- probe is not functional
}

--
-- Redundancy Status
--
StatusRedundancyEnum        ::= INTEGER {
    other(1),               -- redundancy status is not one of the following:
    unknown(2),             -- redundancy status is unknown (not known or monitored)
    full(3),                -- object is fully redundant
    degraded(4),            -- object's redundancy has been degraded
    lost(5),                -- object's redundancy has been lost
    notRedundant(6),        -- redundancy status does not apply or object is not redundant
    redundancyOffline(7)    -- redundancy object taken offline
}

--
-- Status
--
ObjectStatusEnum        ::= INTEGER {
    other(1),           -- the status of the object is not one of the
                        --  following:
    unknown(2),         -- the status of the object is unknown
                        --  (not known or monitored)
    ok(3),              -- the status of the object is ok
    nonCritical(4),     -- the status of the object is warning, non-critical
    critical(5),        -- the status of the object is critical (failure)
    nonRecoverable(6)   -- the status of the object is non-recoverable (dead)
}

RacTypeEnum                 ::= INTEGER {
    other(1),               -- the RAC type is not one of the following
    unknown(2),             -- the RAC type is unknown
    idrac7monolithic(16),   -- iDRAC7 Monolithic
    idrac7modular(17),      -- iDRAC7 Modular
    idrac8monolithic(32),   -- iDRAC8 Monolithic
    idrac8modular(33)       -- iDRAC8 Modular
}

SystemFormFactorEnum   ::= INTEGER {
    other(1),                       -- the form factor is not one of the following:
    unknown(2),                     -- the form factor is unknown
    u1(3),                          -- 1U Monolithic
    u2(4),                          -- 2U Monolithic
    u4(5),                          -- 4U Monolithic
    u7(6),                          -- 7U Monolithic
    singleWidthHalfHeight(7),       -- Single width, half height Modular
    dualWidthHalfHeight(8),         -- Dual width, half height Modular
    singleWidthFullHeight(9),       -- Single width, full height Modular
    dualWidthFullHeight(10),        -- Dual width, full height Modular
    singleWidthQuarterHeight(11),   -- Single width, quarter height Modular
    u5(12),                         -- 5U Monolithic
    u1HalfWidth(13),                -- 1U, half width Modular
    u1QuarterWidth(14),             -- 1U, quarter width Modular
    u1FullWidth(15)                 -- 1U, full width Modular
}

BladeGeometryEnum               ::= INTEGER {
    other(1),                       -- the modular geometry is not one of the following:
    unknown(2),                     -- the modular geometry is unknown
    singleWidthHalfHeight(3),       -- Single width, half height Modular
    dualWidthHalfHeight(4),         -- Dual width, half height Modular
    singleWidthFullHeight(5),       -- Single width, full height Modular
    dualWidthFullHeight(6),         -- Dual width, full height Modular
    singleWidthQuarterHeight(7),    -- Single width, quarter height Modular
    u1HalfWidth(8),                 -- 1U, half width Modular
    u1QuarterWidth(9),              -- 1U, quarter width Modular
    u1FullWidth(10)                 -- 1U, full width Modular
}

PowerStateStatusEnum            ::= INTEGER {
    other(1),                       -- the power state status is not one of the following:
    unknown(2),                     -- the power state status is unknown
    off(3),                         -- system power is off
    on(4)                           -- system power is on
}


------------------------------------------------------------------------------
--
-- MIB Groups
--
------------------------------------------------------------------------------


------------------------------------------------------------------------------
-- RAC Information Group
--
-- OID Format: *******.4.1.674.10892.5.1.1.<a>.0
------------------------------------------------------------------------------

racName OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the product name of a remote access card."
    ::= { racInfoGroup 1 }

racShortName OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the short product name of a remote access
        card."
   ::= { racInfoGroup 2 }

racDescription OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the product description of a remote access
        card."
    ::= { racInfoGroup 3 }

racManufacturer OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the product manufacturer of a remote access
        card."
    ::= { racInfoGroup 4 }

racVersion OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the product version of a remote access card."
    ::= { racInfoGroup 5 }

racURL OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the out-of-band UI URL of a remote access
        card."
    ::= { racInfoGroup 6 }

racType OBJECT-TYPE
    SYNTAX          RacTypeEnum 
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the type of a remote access card."
    ::= { racInfoGroup 7 }

racFirmwareVersion OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the firmware version of a remote access card."
    ::= { racInfoGroup 8 }

------------------------------------------------------------------------------
-- Chassis Information Group
-- (for modular chassis)
--
-- OID Format: *******.4.1.674.10892.5.1.2.<a>.0
------------------------------------------------------------------------------

chassisServiceTag OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the service tag of the modular chassis.
         The value is zero length if not a modular system."
    ::= { chassisInfoGroup 1 }

chassisNameModular OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the chassis name of the modular chassis.
         The value is zero length if not a modular system."
    ::= { chassisInfoGroup 2 }

chassisModelModular OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the model of the modular chassis.
         The value is zero length if not a modular system."
    ::= { chassisInfoGroup 3 }

------------------------------------------------------------------------------
-- System Information Group
--
-- OID Format: *******.4.1.674.10892.5.1.3.<a>.0
------------------------------------------------------------------------------

systemFQDN OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the fully qualified domain name of the system.
         For example, hostname.domainname."
    ::= { systemInfoGroup 1 }

systemServiceTag OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the service tag of the system."
    ::= { systemInfoGroup 2 }

systemExpressServiceCode OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the express service code of the system."
    ::= { systemInfoGroup 3 }

systemAssetTag OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the asset tag of the system."
    ::= { systemInfoGroup 4 }

systemBladeSlotNumber OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the slot number of the system in the modular
         chassis."
    ::= { systemInfoGroup 5 }

systemOSName OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the name of the operating system that the host
         is running."
    ::= { systemInfoGroup 6 }

systemFormFactor OBJECT-TYPE
    SYNTAX          SystemFormFactorEnum 
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the form factor of the system."
    ::= { systemInfoGroup 7 }

systemDataCenterName OBJECT-TYPE
    SYNTAX          StringType 
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the Data Center locator of the system."
    ::= { systemInfoGroup 8 }

systemAisleName OBJECT-TYPE
    SYNTAX          StringType 
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the Aisle locator of the system."
    ::= { systemInfoGroup 9 }

systemRackName OBJECT-TYPE
    SYNTAX          StringType 
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the Rack locator of the system."
    ::= { systemInfoGroup 10 }

systemRackSlot OBJECT-TYPE
    SYNTAX          StringType 
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the Rack Slot locator of the system."
    ::= { systemInfoGroup 11 }

systemModelName OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the model name of the system."
    ::= { systemInfoGroup 12 }

systemSystemID OBJECT-TYPE
    SYNTAX          Unsigned16BitRange
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the system ID of the system."
    ::= { systemInfoGroup 13 }

systemOSVersion OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the version of the operating system that the
        host is running."
    ::= { systemInfoGroup 14 }

systemRoomName OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the Room locator of the system."
    ::= { systemInfoGroup 15 }

systemChassisSystemHeight OBJECT-TYPE
    SYNTAX          Unsigned8BitRange
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the height of the system, in 'U's.
        A U is a standard unit of measure for the height of a rack or
        rack-mountable component.
        (If not applicable, a 'no such name' error is returned.)"
    ::= { systemInfoGroup 16 }

systemBladeGeometry OBJECT-TYPE
    SYNTAX          BladeGeometryEnum 
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the geometry for a modular system.
        (If not applicable, a 'no such name' error is returned.)"
    ::= { systemInfoGroup 17 }

systemNodeID OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the node ID of the system.  The node ID
        provides a unique identifier for the system."
    ::= { systemInfoGroup 18 }


------------------------------------------------------------------------------
-- Status Group
--
-- OID Format: *******.4.1.674.10892.5.2.<a>.0
------------------------------------------------------------------------------

globalSystemStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the overall rollup status of all
        components in the system being monitored by the remote
        access card. Includes system, storage, IO devices, iDRAC,
        CPU, memory, etc."
    ::= { statusGroup 1 }

systemLCDStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the system status as it is reflected by
        the LCD front panel. Not all system components may be included."
    ::= { statusGroup 2 }

globalStorageStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the overall storage status being
        monitored by the remote access card."
    ::= { statusGroup 3 }

systemPowerState OBJECT-TYPE
    SYNTAX          PowerStateStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the power state of the system."
    ::= { statusGroup 4 }

systemPowerUpTime OBJECT-TYPE
    SYNTAX          Unsigned32BitRange
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This attribute defines the power-up time of the system in seconds."
    ::= { statusGroup 5 }


------------------------------------------------------------------------------
-- Alert Group
--
-- OID Format: *******.4.1.674.10892.5.3
------------------------------------------------------------------------------


------------------------------------------------------------------------------
-- Alert Variables Group
--
-- OID Format: *******.4.1.674.10892.5.3.1.<a>.0
------------------------------------------------------------------------------

alertMessageID OBJECT-TYPE
    SYNTAX          DisplayString (SIZE (0..8))
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Message ID of the event."
    ::= { alertVariablesGroup 1 }

alertMessage OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Message describing the alert."
    ::= { alertVariablesGroup 2 }

alertCurrentStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Current status of object causing the alert, if applicable."
    ::= { alertVariablesGroup 3 }

alertSystemServiceTag OBJECT-TYPE
    SYNTAX          DisplayString (SIZE (0..16))
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Service tag of the system."
    ::= { alertVariablesGroup 4 }

alertSystemFQDN OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Fully qualified domain name of the system."
    ::= { alertVariablesGroup 5 }

alertFQDD OBJECT-TYPE
    SYNTAX          DisplayString (SIZE (0..512))
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Fully qualified device descriptor of the device."
    ::= { alertVariablesGroup 6 }

alertDeviceDisplayName OBJECT-TYPE
    SYNTAX          DisplayString (SIZE (0..512))
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Display name of the device/FQDD."
    ::= { alertVariablesGroup 7 }

alertMessageArguments OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Concatenated set of strings representing the message arguments of the
         event. Each message argument string is enclosed in double quotes,
         and there is a comma after the ending double quote of each message
         argument string, except the last one. Any double quotes found within
         a message argument string are preprocessed and changed to single
         quotes."
    ::= { alertVariablesGroup 8 }

alertChassisServiceTag OBJECT-TYPE
    SYNTAX          DisplayString (SIZE (0..16))
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "For modular systems, the service tag of the enclosing chassis.
         For rack and tower systems, this varbind will be empty (zero
         length)."
    ::= { alertVariablesGroup 9 }

alertChassisName OBJECT-TYPE
    SYNTAX          DisplayString (SIZE (0..255))
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "For modular systems, the chassis name of the enclosing chassis.
         For rack and tower systems, this varbind will be empty (zero
         length)."
    ::= { alertVariablesGroup 10 }

alertRacFQDN OBJECT-TYPE
    SYNTAX          StringType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Fully qualified domain name of the remote access card."
    ::= { alertVariablesGroup 11 }


-------------------------------------------------------------------------------
-- System Details Group
--
-- OID Format: *******.4.1.674.10892.5.4
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
-- MIB Version Group
--
-- OID Format: *******.4.1.674.10892.5.4.1.<a>.0
-------------------------------------------------------------------------------

mIBMajorVersionNumber OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0001.0001 This attribute defines the major version number for the
        version of this MIB supported by the iDRAC."
    ::= { mIBVersionGroup 1 }

mIBMinorVersionNumber OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0001.0002 This attribute defines the minor version number for the
        version of this MIB supported by the iDRAC."
    ::= { mIBVersionGroup 2 }

mIBMaintenanceVersionNumber OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0001.0003 This attribute defines the maintenance version number for
        the version of this MIB supported by the iDRAC."
    ::= { mIBVersionGroup 3 }


-------------------------------------------------------------------------------
-- System State Group
--
-- OID Format: *******.4.1.674.10892.5.4.200
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
-- System State Table
--
-- OID Format: *******.4.1.674.10892.**********.1.<a>.<i1>
-------------------------------------------------------------------------------

SystemStateTableEntry                              ::= SEQUENCE {
    systemStatechassisIndex                        ObjectRange,
    systemStateGlobalSystemStatus                  ObjectStatusEnum,
    systemStateChassisState                        StateSettingsFlags,
    systemStateChassisStatus                       ObjectStatusEnum,
    systemStatePowerUnitStateDetails               OCTET STRING,
    systemStatePowerUnitStatusRedundancy           StatusRedundancyEnum,
    systemStatePowerUnitStatusDetails              OCTET STRING,
    systemStatePowerSupplyStateDetails             OCTET STRING,
    systemStatePowerSupplyStatusCombined           ObjectStatusEnum,
    systemStatePowerSupplyStatusDetails            OCTET STRING,
    systemStateVoltageStateDetails                 OCTET STRING,
    systemStateVoltageStatusCombined               ObjectStatusEnum,
    systemStateVoltageStatusDetails                OCTET STRING,
    systemStateAmperageStateDetails                OCTET STRING,
    systemStateAmperageStatusCombined              ObjectStatusEnum,
    systemStateAmperageStatusDetails               OCTET STRING,
    systemStateCoolingUnitStateDetails             OCTET STRING,
    systemStateCoolingUnitStatusRedundancy         StatusRedundancyEnum,
    systemStateCoolingUnitStatusDetails            OCTET STRING,
    systemStateCoolingDeviceStateDetails           OCTET STRING,
    systemStateCoolingDeviceStatusCombined         ObjectStatusEnum,
    systemStateCoolingDeviceStatusDetails          OCTET STRING,
    systemStateTemperatureStateDetails             OCTET STRING,
    systemStateTemperatureStatusCombined           ObjectStatusEnum,
    systemStateTemperatureStatusDetails            OCTET STRING,
    systemStateMemoryDeviceStateDetails            OCTET STRING,
    systemStateMemoryDeviceStatusCombined          ObjectStatusEnum,
    systemStateMemoryDeviceStatusDetails           OCTET STRING,
    systemStateChassisIntrusionStateDetails        OCTET STRING,
    systemStateChassisIntrusionStatusCombined      ObjectStatusEnum,
    systemStateChassisIntrusionStatusDetails       OCTET STRING,
    systemStatePowerUnitStatusCombined             ObjectStatusEnum,
    systemStatePowerUnitStatusList                 OCTET STRING,
    systemStateCoolingUnitStatusCombined           ObjectStatusEnum,
    systemStateCoolingUnitStatusList               OCTET STRING,
    systemStateProcessorDeviceStatusCombined       ObjectStatusEnum,
    systemStateProcessorDeviceStatusList           OCTET STRING,
    systemStateBatteryStatusCombined               ObjectStatusEnum,
    systemStateBatteryStatusList                   OCTET STRING,
    systemStateSDCardUnitStatusCombined            ObjectStatusEnum,
    systemStateSDCardUnitStatusList                OCTET STRING,
    systemStateSDCardDeviceStatusCombined          ObjectStatusEnum,
    systemStateSDCardDeviceStatusList              OCTET STRING,
    systemStateIDSDMCardUnitStatusCombined         ObjectStatusEnum,
    systemStateIDSDMCardUnitStatusList             OCTET STRING,
    systemStateIDSDMCardDeviceStatusCombined       ObjectStatusEnum,
    systemStateIDSDMCardDeviceStatusList           OCTET STRING,
    systemStateTemperatureStatisticsStateDetails   OCTET STRING,
    systemStateTemperatureStatisticsStatusCombined ObjectStatusEnum,
    systemStateTemperatureStatisticsStatusDetails  OCTET STRING
}

systemStateTable                                OBJECT-TYPE
    SYNTAX      SEQUENCE OF SystemStateTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0200.0010 This object defines the System State Table."
    ::= { systemStateGroup 10 }

systemStateTableEntry                           OBJECT-TYPE
    SYNTAX      SystemStateTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001 This object defines the System State Table Entry."
    INDEX       { systemStatechassisIndex }
    ::= { systemStateTable 1 }

systemStatechassisIndex                        OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0001 This attribute defines the index (one based) of
        this system chassis."
    ::= { systemStateTableEntry 1 }

systemStateGlobalSystemStatus                   OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0002 This attribute defines the global system status
        of all system chassis being monitored by the systems management software."
    ::= { systemStateTableEntry 2 }

systemStateChassisState                        OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0003 This attribute defines the state settings of this
        system chassis."
    ::= { systemStateTableEntry 3 }

systemStateChassisStatus                       OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0004 This attribute defines the status of this system
        chassis."
    ::= { systemStateTableEntry 4 }

systemStatePowerUnitStateDetails                OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0005 This attribute lists the state settings of each
        power unit of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of a power unit.  The first byte returned represents the state settings
        of the first power unit, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 5 }

systemStatePowerUnitStatusRedundancy            OBJECT-TYPE
    SYNTAX      StatusRedundancyEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0006 This attribute defines the combined redundancy status
        of all power units of this system."
    ::= { systemStateTableEntry 6 }

systemStatePowerUnitStatusDetails               OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0007 This attribute lists the redundancy status of each
        power unit of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the redundancy status
        of a power unit.  The first byte returned represents the redundancy status
        of the first power unit, etc.  The bytes have the same definition type as
        StatusRedundancyEnum."
    ::= { systemStateTableEntry 7 }

systemStatePowerSupplyStateDetails              OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0008 This attribute lists the state settings of each
        power supply of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of a power supply.  The first byte returned represents the state settings
        of the first power supply, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 8 }

systemStatePowerSupplyStatusCombined            OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0009 This attribute defines the combined status of all
        power supplies of this system."
    ::= { systemStateTableEntry 9 }

systemStatePowerSupplyStatusDetails             OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0010 This attribute lists the status of each power supply
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a power supply.
        The first byte returned represents the status of the first power supply,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 10 }

systemStateVoltageStateDetails                  OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0011 This attribute lists the state settings of each
        voltage probe of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of a voltage probe.  The first byte returned represents the state settings
        of the first voltage probe, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 11 }

systemStateVoltageStatusCombined                OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0012 This attribute defines the combined status of all
        voltage probes of this system."
    ::= { systemStateTableEntry 12 }

systemStateVoltageStatusDetails                 OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0013 This attribute lists the status of each voltage probe
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a voltage probe.
        The first byte returned represents the status of the first voltage probe,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 13 }

systemStateAmperageStateDetails                 OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0014 This attribute lists the state settings of each
        amperage probe of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of an amperage probe.  The first byte returned represents the state settings
        of the first amperage probe, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 14 }

systemStateAmperageStatusCombined               OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0015 This attribute defines the combined status of all
        amperage probes of this system."
    ::= { systemStateTableEntry 15 }

systemStateAmperageStatusDetails                OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0016 This attribute lists the status of each amperage probe
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of an amperage probe.
        The first byte returned represents the status of the first amperage probe,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 16 }

systemStateCoolingUnitStateDetails              OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0017 This attribute lists the state settings of each
        cooling unit of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of a cooling unit.  The first byte returned represents the state settings
        of the first cooling unit, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 17 }

systemStateCoolingUnitStatusRedundancy          OBJECT-TYPE
    SYNTAX      StatusRedundancyEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0018 This attribute defines the combined redundancy status
        of all cooling units of this system."
    ::= { systemStateTableEntry 18 }

systemStateCoolingUnitStatusDetails             OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0019 This attribute lists the redundancy status of each
        cooling unit of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the redundancy status
        of a cooling unit.  The first byte returned represents the redundancy status
        of the first cooling unit, etc.  The bytes have the same definition type as
        StatusRedundancyEnum."
    ::= { systemStateTableEntry 19 }

systemStateCoolingDeviceStateDetails            OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0020 This attribute lists the state settings of each
        cooling device of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of a cooling device.  The first byte returned represents the state settings
        of the first cooling device, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 20 }

systemStateCoolingDeviceStatusCombined          OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0021 This attribute defines the combined status of all
        cooling devices of this system."
    ::= { systemStateTableEntry 21 }

systemStateCoolingDeviceStatusDetails           OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0022 This attribute lists the status of each cooling device
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a cooling device.
        The first byte returned represents the status of the first cooling device,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 22 }

systemStateTemperatureStateDetails              OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0023 This attribute lists the state settings of each
        temperature probe of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of a temperature probe.  The first byte returned represents the state settings
        of the first temperature probe, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 23 }

systemStateTemperatureStatusCombined            OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0024 This attribute defines the combined status of all
        temperature probes of this system."
    ::= { systemStateTableEntry 24 }

systemStateTemperatureStatusDetails             OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0025 This attribute lists the status of each temperature probe
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a temperature probe.
        The first byte returned represents the status of the first temperature probe,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 25 }

systemStateMemoryDeviceStateDetails             OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0026 This attribute lists the state settings of each
        memory device of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the state settings
        of a memory device.  The first byte returned represents the state settings
        of the first memory device, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 26 }

systemStateMemoryDeviceStatusCombined           OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0027 This attribute defines the combined status of all
        memory devices of this system."
    ::= { systemStateTableEntry 27 }

systemStateMemoryDeviceStatusDetails            OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0028 This attribute lists the status of each memory device
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a memory device.
        The first byte returned represents the status of the first memory device,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 28 }

systemStateChassisIntrusionStateDetails         OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0029 This attribute lists the state settings of each
        intrusion detection device of this system chassis.  The results are returned as
        a binary octet string where each byte of the octet string represents the
        state settings of an intrusion detection device.  The first byte returned
        represents the state settings of the first intrusion detection device, etc.
        The bytes have the same definition type as StateSettingsFlags."
    ::= { systemStateTableEntry 29 }

systemStateChassisIntrusionStatusCombined       OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0030 This attribute defines the combined status of all
        intrusion detection devices of this system chassis."
    ::= { systemStateTableEntry 30 }

systemStateChassisIntrusionStatusDetails        OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0031 This attribute lists the status of each intrusion
        detection device of this system chassis.  The results are returned as a binary
        octet string where each byte of the octet string represents the status
        of an intrusion detection device.  The first byte returned represents the
        status of the first intrusion detection device, etc.  The bytes have the
        same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 31 }

systemStatePowerUnitStatusCombined              OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0042 This attribute defines the combined status
        of all power units of this chassis."
    ::= { systemStateTableEntry 42 }

systemStatePowerUnitStatusList                  OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0043 This attribute lists the status of each
        power unit of this chassis.  The results are returned as a binary octet
        string where each byte of the octet string represents the status
        of a power unit.  The first byte returned represents the status
        of the first power unit, etc.  The bytes have the same definition type
        as ObjectStatusEnum."
    ::= { systemStateTableEntry 43 }

systemStateCoolingUnitStatusCombined            OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0044 This attribute defines the combined status
        of all cooling units of this system."
    ::= { systemStateTableEntry 44 }

systemStateCoolingUnitStatusList                OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0045 This attribute lists the status of each
        cooling unit of this system.  The results are returned as a binary octet
        string where each byte of the octet string represents the status
        of a cooling unit.  The first byte returned represents the status
        of the first cooling unit, etc.  The bytes have the same definition type
        as ObjectStatusEnum."
    ::= { systemStateTableEntry 45 }

systemStateProcessorDeviceStatusCombined        OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0050 This attribute defines the combined status of all
        processor devices of this system."
    ::= { systemStateTableEntry 50 }

systemStateProcessorDeviceStatusList            OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0051 This attribute lists the status of each processor device
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a processor device.
        The first byte returned represents the status of the first processor device,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 51 }

systemStateBatteryStatusCombined                OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0052 This attribute defines the combined status of all
        batteries of this system."
    ::= { systemStateTableEntry 52 }

systemStateBatteryStatusList                    OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0053 This attribute lists the status of each battery
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a battery.
        The first byte returned represents the status of the first battery,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 53 }

systemStateSDCardUnitStatusCombined             OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0054 This attribute defines the combined status
        of all SD Card units of this system."
    ::= { systemStateTableEntry 54 }

systemStateSDCardUnitStatusList                 OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0055 This attribute lists the status of each
        SD Card unit of this system.  The results are returned as a binary
        octet string where each byte of the octet string represents the status
        of a SD Card unit.  The first byte returned represents the status
        of the first SD Card unit, etc.  The bytes have the same definition
        type as ObjectStatusEnum."
    ::= { systemStateTableEntry 55 }

systemStateSDCardDeviceStatusCombined           OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0056 This attribute defines the combined status of all
        SD Card devices of this system."
    ::= { systemStateTableEntry 56 }

systemStateSDCardDeviceStatusList               OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0057 This attribute lists the status of each SD Card device
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of a SD Card device.
        The first byte returned represents the status of the first SD Card device,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 57 }

systemStateIDSDMCardUnitStatusCombined          OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0058 This attribute defines the combined status
        of all IDSDM Card units of this system."
    ::= { systemStateTableEntry 58 }

systemStateIDSDMCardUnitStatusList              OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0059 This attribute lists the status of each
        IDSDM Card unit of this system.  The results are returned as a binary
        octet string where each byte of the octet string represents the status
        of an IDSDM Card unit.  The first byte returned represents the status
        of the first IDSDM Card unit, etc.  The bytes have the same definition
        type as ObjectStatusEnum."
    ::= { systemStateTableEntry 59 }

systemStateIDSDMCardDeviceStatusCombined        OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0060 This attribute defines the combined status of all
        IDSDM Card devices of this system."
    ::= { systemStateTableEntry 60 }

systemStateIDSDMCardDeviceStatusList            OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0061 This attribute lists the status of each IDSDM Card device
        of this system.  The results are returned as a binary octet string where
        each byte of the octet string represents the status of an IDSDM Card device.
        The first byte returned represents the status of the first IDSDM Card device,
        etc.  The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 61 }

systemStateTemperatureStatisticsStateDetails    OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0062 This attribute lists the state settings of each
        temperature statistics object of this system.  The results are returned
        as a binary octet string where each byte of the octet string represents
        the state settings of a temperature statistics object.  The first byte
        returned represents the state settings of the first temperature
        statistics object, etc.  The bytes have the same definition type
        as StateSettingsFlags."
    ::= { systemStateTableEntry 62 }

systemStateTemperatureStatisticsStatusCombined  OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0063 This attribute defines the combined status of all
         temperature statistics objects of this system."
    ::= { systemStateTableEntry 63 }

systemStateTemperatureStatisticsStatusDetails   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0200.0010.0001.0064 This attribute lists the status of each
        temperature statistics object of this system.  The results are returned
        as a binary octet string where each byte of the octet string represents
        the status of a temperature statistics object.  The first byte returned
        represents the status of the first temperature statistics object, etc.
        The bytes have the same definition type as ObjectStatusEnum."
    ::= { systemStateTableEntry 64 }


-------------------------------------------------------------------------------
-- Chassis Information Group
--
-- OID Format: *******.4.1.674.10892.5.4.300
-------------------------------------------------------------------------------

StateCapabilitiesLogUniqueFlags                 ::= INTEGER {
    -- Note: These values are bit masks, so combination values are possible.
    unknown(1),                                 -- log state capabilities are unknown
    onlineCapable(2),                           -- log can be enabled (online) or disabled (offline)
    notReadyCapable(4),                         -- log can be not ready
    resetCapable(8)                             -- log can be reset
}

StateSettingsLogUniqueFlags                     ::= INTEGER {
    -- Note: These values are bit masks, so combination values are possible.
    unknown(1),                                 -- log state settings are unknown
    online(2),                                  -- log is enabled (online)
    notReady(4),                                -- log is not ready
    reset(8)                                    -- reset log
}

LogFormatType                                   ::= INTEGER {
    raw(1),                                     -- format is Raw
    ascii(2),                                   -- format is ASCII
    uniCode(3)                                  -- format is Unicode
}


-------------------------------------------------------------------------------
-- Chassis Information Group Attributes
--
-- OID Format: *******.4.1.674.10892.5.4.300.<a>.0
-------------------------------------------------------------------------------

numEventLogEntries                              OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0001.0000 This attribute provides the number of entries
        currently in the eventLogTable."
    ::= { chassisInformationGroup 1 }

-- Note: You can only access the numLCLogEntries attribute via SNMPv3 queries.
--       Access to the attribute is blocked for SNMPv1 and SNMPv2c queries.
numLCLogEntries                                 OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0002.0000 This attribute provides the number of entries
        currently in the lcLogTable.
        Note: This attribute can only be accessed via SNMPv3 queries."
    ::= { chassisInformationGroup 2 }


-------------------------------------------------------------------------------
-- Chassis Information Table
--
-- OID Format: *******.4.1.674.10892.5.4.300.10.1.<a>.<i1>
-------------------------------------------------------------------------------

ChassisTypeEnum                                 ::= INTEGER {
    other(1),                                   -- type is other than following values
    unknown(2),                                 -- type is unknown
    desktop(3),                                 -- type is Desktop
    lowProfileDesktop(4),                       -- type is Low Profile Desktop
    pizzaBox(5),                                -- type is Pizza Box
    miniTower(6),                               -- type is MiniTower
    tower(7),                                   -- type is Tower
    portable(8),                                -- type is Portable
    lapTop(9),                                  -- type is Laptop
    noteBook(10),                               -- type is Notebook
    handHeld(11),                               -- type is Handheld
    dockingStation(12),                         -- type is Docking Station
    allInOne(13),                               -- type is All-In-One
    subNoteBook(14),                            -- type is SubNotebook
    spaceSaving(15),                            -- type is Spacesaver
    lunchBox(16),                               -- type is Lunchbox
    mainSystemChassis(17),                      -- type is Main System Chassis
    expansionChassis(18),                       -- type is Expansion Chassis
    subChassis(19),                             -- type is SubChassis
    busExpansionChassis(20),                    -- type is Bus Expansion Chassis
    peripheralChassis(21),                      -- type is Peripheral Chassis
    raidChassis(22),                            -- type is RAID Chassis
    rackMountChassis(23),                       -- type is Rack-mounted Chassis
    sealedCasePC(24),                           -- type is Sealed-case PC
    multiSystemChassis(25)                      -- type is Multi-system Chassis
}

ChassisSystemClassEnum                          ::= INTEGER {
    other(1),                                   -- class is other than following values
    unknown(2),                                 -- class is unknown
    workstationClass(3),                        -- class is Workstation
    serverClass(4),                             -- class is Server
    desktopClass(5),                            -- class is Desktop
    portableClass(6),                           -- class is Portable
    netPCClass(7),                              -- class is Net PC
    storageClass(8)                             -- class is Storage
}

LEDControlCapabilitiesFlags                     ::= INTEGER {
    -- If 0 (zero), there are no LED Control capabilities
    unknown(1),                                 -- LED control capabilities are unknown
    alertOnErrorCapable(2),                     -- LED can alert on error condition
    alertOnWarningAndErrorCapable(4),           -- LED can alert on error and warning condition
    alertOnWarningOrErrorCapable(6)             -- LED can alert on error or warning condition
}

LEDControlSettingsFlags                         ::= INTEGER {
    -- If 0 (zero), there are no LED Control settings
    unknown(1),                                 -- LED control settings are unknown
    alertOnError(2),                            -- LED set to alert on error condition
    alertOnWarningAndError(4)                   -- LED set to alert on error or warning condition
}

ChassisIdentifyControlCapabilitiesFlags         ::= INTEGER {
    -- If 0 (zero), there are no Chassis Identify Control capabilities
    unknownCapabilities(1),                     -- chassis identify capabilities are unknown
    -- The objects capabilities allow it to be set to:
    enableCapable(2),                           -- chassis identify can be enabled (online) or disabled (offline)
    notReadyCapable(4),                         -- chassis identify can be not ready
    identifyCapable(8)                          -- chassis idenfity can be made to identify chassis
}

ChassisIdentifyControlSettingsFlags             ::= INTEGER {
    -- If 0 (zero), there are no Chassis Identify Control settings
    unknown(1),                                 -- chassis identify settings are unknown
    enabled(2),                                 -- chassis identify is enabled (online)
    notReady(4),                                -- chassis identify is not ready
    identifyChassis(8),                         -- identify chassis
    identifyChassisAndEnable(10)                -- identify chassis and enabled
}

HostControlCapabilitiesFlags                    ::= INTEGER {
    -- If 0 (zero), there are no Host Control capabilities
    manualRebootCapable(1),                     -- host can be rebooted
    manualPowerOFFCapable(2),                   -- host can be powered off
    manualPowerCycleCapable(4),                 -- host can be power cycled
    manualAllExceptOperatingSystemShutdownCapable(7),  -- all host control capabilities except OS shutdown
    manualOperatingSystemShutdownCapable(8),    -- operating system can be shutdown
    manualFullyCapable(15),                     -- all host control capabilities
    manualRebootWithOSShutdownCapable(16),      -- host can be rebooted with operating system shutdown
    manualRebootWithoutOSShutdownCapable(32),   -- host can be rebooted without operating system shutdown
    manualPowerOffWithOSShutdownCapable(64),    -- host can be powered off with operating system shutdown
    manualPowerOffWithoutOSShutdownCapable(128),-- host can be powered off without operating system shutdown
    manualPowerCycleWithOSShutdownCapable(256), -- host can be power cycled with operating system shutdown
    manualPowerCycleWithoutOSShutdownCapable(512)  -- host can be power cycled with operating system shutdown
}

HostControlSettingsFlags                        ::= INTEGER {
    -- If 0 (zero), there are no Host Control settings
    manualReboot(1),                            -- reboot host
    manualPowerOFF(2),                          -- power off host
    manualPowerCycle(4),                        -- power cycle host
    manualOperatingSystemShutdown(8),           -- shutdown operating system on host
    manualOperatingSystemShutdownThenReboot(9), -- shutdown operating system on host then reboot host
    manualOperatingSystemShutdownThenPowerOFF(10),   -- shutdown operating system on host then power off host
    manualOperatingSystemShutdownThenPowerCycle(12)  -- shutdown operating system on host then power cycle host
}

WatchDogControlCapabilitiesFlags                ::= INTEGER {
    -- If 0 (zero), there are no Watchdog Control capabilities
    automaticRebootCapable(1),                  -- watchdog can reboot host
    automaticPowerCycleCapable(2),              -- watchdog can power cycle host
    automaticNotificationCapable(4),            -- watchdog can notify
    automaticWatchDogTimerCapable(8),           -- watchdog supports timer
    automaticPowerOffCapable(16),               -- watchdog can power off host
    automaticAllExceptNotificationCapable(27),  -- all capabilities except notification
    automaticFullyCapable(31)                   -- all watchdog control capabilities
}

WatchControlSettingsFlags                       ::= INTEGER {
    -- If 0 (zero), there are no Watchdog Control settings
    automaticRebootEnabled(1),                  -- watchdog set for automatic reboot
    automaticPowerCycleEnabled(2),              -- watchdog set for automatic power cycle
    automaticNotificationEnabled(4),            -- watchdog set for automatic notification
    automaticPowerOffEnabled(8)                 -- watchdog set for automatic power off
}

WatchDogTimerCapabilitiesFlags                   ::= INTEGER {
    -- If 0 (zero), there are no Watchdog Timer capabilities
    type1Capable(1),                            -- watchdog can time in range of 20-480 seconds
    type2Capable(2),                            -- watchdog can time in 30, 60, 120 and 480 second intervals
    type3Capable(4)                             -- watchdog can time in 60 second intervals
}

PowerButtonControlCapabilitiesFlags             ::= INTEGER {
    -- If 0 (zero), there are no Power Button Control capabilities
    unknownCapabilities(1),                     -- power button capabilities are unknown
    enableCapable(2)                            -- power button can be enabled or disabled
}

PowerButtonControlSettingsFlags                 ::= INTEGER {
    -- If 0 (zero), there are no Power Button Control settings
    unknown(1),                                 -- power button settings are unknown
    enabled(2),                                 -- power button is enabled
    disabled(4)                                 -- power button disabled
}

NMIButtonControlCapabilitiesFlags               ::= INTEGER {
    -- If 0 (zero), there are no NMI Button Control capabilities
    unknownCapabilities(1),                     -- NMI button capabilities are unknown
    enableCapable(2)                            -- NMI button can be enabled or disabled
}

NMIButtonControlSettingsFlags                   ::= INTEGER {
    -- If 0 (zero), there are no NMI Button Control settings
    unknown(1),                                 -- NMI button settings are unknown
    enabled(2),                                 -- NMI button is enabled
    disabled(4)                                 -- NMI button disabled
}

SystemPropertiesFlags                           ::= INTEGER {
    -- Note: These values are bit masks, so combination values are possible.
    -- none(0),                                 -  no properties
    energySmart(1)                              -- Energy Smart system
}

ChassisInformationTableEntry                    ::= SEQUENCE {
    chassisIndexChassisInformation              ObjectRange,
    chassisStateCapabilities                    StateCapabilitiesFlags,
    chassisStateSettings                        StateSettingsFlags,
    chassisStatus                               ObjectStatusEnum,
    chassisparentIndexReference                 ObjectRange,
    chassisType                                 ChassisTypeEnum,
    chassisName                                 String64,
    chassisManufacturerName                     String64,
    chassisModelTypeName                        String64,
    chassisAssetTagName                         DisplayString,
    chassisServiceTagName                       DisplayString,
    chassisID                                   Unsigned8BitRange,
    chassisIDExtension                          Unsigned16BitRange,
    chassisSystemClass                          ChassisSystemClassEnum,
    chassisSystemName                           String64,
    chassisLEDControlCapabilitiesUnique         LEDControlCapabilitiesFlags,
    chassisLEDControlSettingsUnique             LEDControlSettingsFlags,
    chassisIdentifyFlashControlCapabilities     ChassisIdentifyControlCapabilitiesFlags,
    chassisIdentifyFlashControlSettings         ChassisIdentifyControlSettingsFlags,
    chassisLockPresent                          BooleanType,
    chassishostControlCapabilitiesUnique        HostControlCapabilitiesFlags,
    chassishostControlSettingsUnique            HostControlSettingsFlags,
    chassiswatchDogControlCapabilitiesUnique    WatchDogControlCapabilitiesFlags,
    chassiswatchDogControlSettingsUnique        WatchControlSettingsFlags,
    chassiswatchDogControlExpiryTimeCapabilitiesUnique WatchDogTimerCapabilitiesFlags,
    chassiswatchDogControlExpiryTime            Unsigned16BitRange,
    chassisPowerButtonControlCapabilitiesUnique PowerButtonControlCapabilitiesFlags,
    chassisPowerButtonControlSettingsUnique     PowerButtonControlSettingsFlags,
    chassisNMIButtonControlCapabilitiesUnique   NMIButtonControlCapabilitiesFlags,
    chassisNMIButtonControlSettingsUnique       NMIButtonControlSettingsFlags,
    chassisSystemProperties                     SystemPropertiesFlags,
    chassisSystemRevisionNumber                 Unsigned8BitRange,
    chassisSystemRevisionName                   String64,
    chassisExpressServiceCodeName               String64
}

chassisInformationTable                         OBJECT-TYPE
    SYNTAX      SEQUENCE OF ChassisInformationTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0010 This object defines the Chassis Information Table."
    ::= { chassisInformationGroup 10 }

chassisInformationTableEntry                    OBJECT-TYPE
    SYNTAX      ChassisInformationTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001 This object defines the Chassis Information Table Entry."
    INDEX       { chassisIndexChassisInformation }
    ::= { chassisInformationTable 1 }

chassisIndexChassisInformation                  OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0001 This attribute defines the index (one based) of
        the system chassis."
    ::= { chassisInformationTableEntry 1 }

chassisStateCapabilities                        OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0002 This attribute defines the state capabilities of the system chassis."
    ::= { chassisInformationTableEntry 2 }

chassisStateSettings                            OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0003 This attribute defines the state settings of the system chassis."
    ::= { chassisInformationTableEntry 3 }

chassisStatus                                   OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0004 This attribute defines the status of the system chassis."
    ::= { chassisInformationTableEntry 4 }

chassisparentIndexReference                     OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0005 This attribute defines the index (one based) to the
        parent system of this system chassis, if any."
    ::= { chassisInformationTableEntry 5 }

chassisType                                     OBJECT-TYPE
    SYNTAX      ChassisTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0006 This attribute defines the system type of the system chassis."
    ::= { chassisInformationTableEntry 6 }

chassisName                                     OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0007 This attribute defines the user-assigned name of the system chassis."
    ::= { chassisInformationTableEntry 7 }

chassisManufacturerName                         OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0008 This attribute defines the name of the manufacturer
        of the system chassis."
    ::= { chassisInformationTableEntry 8 }

chassisModelTypeName                            OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0009 This attribute defines the system model type of the system chassis."
    ::= { chassisInformationTableEntry 9 }

chassisAssetTagName                             OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..10))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0010 This attribute defines the asset tag name of the system chassis."
    ::= { chassisInformationTableEntry 10 }

chassisServiceTagName                           OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..7))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0011 This attribute defines the service tag name of the system chassis."
    ::= { chassisInformationTableEntry 11 }

chassisID                                       OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0012 This attribute defines the system ID.  If the value
        is 254 (0xFE), the attribute systemIDExtension provides the system ID."
    ::= { chassisInformationTableEntry 12 }

chassisIDExtension                              OBJECT-TYPE
    SYNTAX      Unsigned16BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0013 This attribute defines the system ID extension."
    ::= { chassisInformationTableEntry 13 }

chassisSystemClass                              OBJECT-TYPE
    SYNTAX      ChassisSystemClassEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0014 This attribute defines the system class."
    ::= { chassisInformationTableEntry 14 }

chassisSystemName                               OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0015 This attribute defines the host name of the system chassis."
    ::= { chassisInformationTableEntry 15 }

chassisLEDControlCapabilitiesUnique             OBJECT-TYPE
    SYNTAX      LEDControlCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0024 This attribute defines the capabilities of the
        LED control hardware in the system chassis."
    ::= { chassisInformationTableEntry 24 }

chassisLEDControlSettingsUnique                 OBJECT-TYPE
    SYNTAX      LEDControlSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0025 This attribute defines the reading and setting of the
        LED control hardware in the system chassis."
    ::= { chassisInformationTableEntry 25 }

chassisIdentifyFlashControlCapabilities         OBJECT-TYPE
    SYNTAX      ChassisIdentifyControlCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0028 This attribute defines if the system allows setting
        of the system front panel LED to flash."
    ::= { chassisInformationTableEntry 28 }

chassisIdentifyFlashControlSettings             OBJECT-TYPE
    SYNTAX      ChassisIdentifyControlSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0029 This attribute setting causes the system front panel
        LED to flash."
    ::= { chassisInformationTableEntry 29 }

chassisLockPresent                              OBJECT-TYPE
    SYNTAX      BooleanType
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0030 If true, a system lock is present on the system chassis."
    ::= { chassisInformationTableEntry 30 }

chassishostControlCapabilitiesUnique            OBJECT-TYPE
    SYNTAX      HostControlCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0031 This attribute defines the capabilities of the
        host control function."
    ::= { chassisInformationTableEntry 31 }

chassishostControlSettingsUnique                OBJECT-TYPE
    SYNTAX      HostControlSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0032 This attribute defines the settings of the
        host control function."
    ::= { chassisInformationTableEntry 32 }

chassiswatchDogControlCapabilitiesUnique        OBJECT-TYPE
    SYNTAX      WatchDogControlCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0033 This attribute defines the capabilities of the
        watchdog control function."
    ::= { chassisInformationTableEntry 33 }

chassiswatchDogControlSettingsUnique            OBJECT-TYPE
    SYNTAX      WatchControlSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0034 This attribute defines the settings of the
        watchdog control function."
    ::= { chassisInformationTableEntry 34 }

chassiswatchDogControlExpiryTimeCapabilitiesUnique OBJECT-TYPE
    SYNTAX      WatchDogTimerCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0035 This attribute defines the capabilities of the
        watchdog control expiry timer function."
    ::= { chassisInformationTableEntry 35 }

chassiswatchDogControlExpiryTime                OBJECT-TYPE
    SYNTAX      Unsigned16BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0036 This attribute defines the current watchdog timer
        value in seconds."
    ::= { chassisInformationTableEntry 36 }

chassisPowerButtonControlCapabilitiesUnique     OBJECT-TYPE
    SYNTAX      PowerButtonControlCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0038 This attribute defines the capabilities of the
        power button control hardware in the system chassis."
    ::= { chassisInformationTableEntry 38 }

chassisPowerButtonControlSettingsUnique         OBJECT-TYPE
    SYNTAX      PowerButtonControlSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0039 This attribute defines the reading and setting of
        the power button control hardware in the system chassis."
    ::= { chassisInformationTableEntry 39 }

chassisNMIButtonControlCapabilitiesUnique       OBJECT-TYPE
    SYNTAX      NMIButtonControlCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0044 This attribute defines the capabilities of the
        NMI button control hardware in the system chassis."
    ::= { chassisInformationTableEntry 44 }

chassisNMIButtonControlSettingsUnique           OBJECT-TYPE
    SYNTAX      NMIButtonControlSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0045 This attribute defines the reading and setting of
        the NMI button control hardware in the system chassis."
    ::= { chassisInformationTableEntry 45 }

chassisSystemProperties                         OBJECT-TYPE
    SYNTAX      SystemPropertiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0046 This attribute defines the properties of the system chassis."
    ::= { chassisInformationTableEntry 46 }

chassisSystemRevisionNumber                     OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0047 This attribute defines the revision number of the system
        where zero indicates the original version of the system chassis.  The revision number
        is not available on all systems."
    ::= { chassisInformationTableEntry 47 }

chassisSystemRevisionName                       OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0048 This attribute defines the revision name of the system,
        if applicable."
    ::= { chassisInformationTableEntry 48 }

chassisExpressServiceCodeName                   OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0010.0001.0049 This attribute defines the Express Service Code of the system chassis."
    ::= { chassisInformationTableEntry 49 }


-------------------------------------------------------------------------------
-- Event (ESM) Log Table
--
-- OID Format: *******.4.1.674.10892.5.4.300.40.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

EventLogTableEntry                              ::= SEQUENCE {
    eventLogchassisIndex                        ObjectRange,
    eventLogRecordIndex                         Unsigned32BitRange,
    eventLogStateCapabilitiesUnique             StateCapabilitiesLogUniqueFlags,
    eventLogStateSettingsUnique                 StateSettingsLogUniqueFlags,
    eventLogRecord                              DisplayString,
    eventLogFormat                              LogFormatType,
    eventLogSeverityStatus                      ObjectStatusEnum,
    eventLogDateName                            DateName
}

eventLogTable                                   OBJECT-TYPE
    SYNTAX      SEQUENCE OF EventLogTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0040 This object defines the Event (ESM) Log Table."
    ::= { chassisInformationGroup 40 }

eventLogTableEntry                              OBJECT-TYPE
    SYNTAX      EventLogTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001 This object defines the Event (ESM) Log Table Entry."
    INDEX       { eventLogchassisIndex,
                  eventLogRecordIndex }
    ::= { eventLogTable 1 }

eventLogchassisIndex                            OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { eventLogTableEntry 1 }

eventLogRecordIndex                             OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0002 This attribute defines the index (one based) of the
        event log record."
    ::= { eventLogTableEntry 2 }

eventLogStateCapabilitiesUnique                 OBJECT-TYPE
    SYNTAX      StateCapabilitiesLogUniqueFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0003 This attribute defines the state capabilities of the
        object that is writing the event log."
    ::= { eventLogTableEntry 3 }

eventLogStateSettingsUnique                     OBJECT-TYPE
    SYNTAX      StateSettingsLogUniqueFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0004 This attribute defines the state settings of the
        object that is writing the event log."
    ::= { eventLogTableEntry 4 }

eventLogRecord                                  OBJECT-TYPE
    SYNTAX       DisplayString (SIZE (0..1024))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0005 This attribute defines the data of the event log record."
    ::= { eventLogTableEntry 5 }

eventLogFormat                                  OBJECT-TYPE
    SYNTAX      LogFormatType
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0006 This attribute defines the format of the event log record."
    ::= { eventLogTableEntry 6 }

eventLogSeverityStatus                          OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0007 This attribute defines the severity of the
        event log record."
    ::= { eventLogTableEntry 7 }

eventLogDateName                                OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0040.0001.0008 This attribute defines the date and time of the
        event log record."
    ::= { eventLogTableEntry 8 }


-------------------------------------------------------------------------------
-- System BIOS Table
--
-- OID Format: *******.4.1.674.10892.5.4.300.50.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

SystemBIOSTableEntry                            ::= SEQUENCE {
    systemBIOSchassisIndex                      ObjectRange,
    systemBIOSIndex                             ObjectRange,
    systemBIOSStateCapabilities                 StateCapabilitiesFlags,
    systemBIOSStateSettings                     StateSettingsFlags,
    systemBIOSStatus                            ObjectStatusEnum,
    systemBIOSReleaseDateName                   DateName,
    systemBIOSVersionName                       String64,
    systemBIOSManufacturerName                  String64
}

systemBIOSTable                                 OBJECT-TYPE
    SYNTAX      SEQUENCE OF SystemBIOSTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0050 This object defines the System BIOS Table."
    ::= { chassisInformationGroup 50 }

systemBIOSTableEntry                            OBJECT-TYPE
    SYNTAX      SystemBIOSTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001 This object defines the System BIOS Table Entry."
    INDEX       { systemBIOSchassisIndex,
                  systemBIOSIndex }
    ::= { systemBIOSTable 1 }

systemBIOSchassisIndex                          OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { systemBIOSTableEntry 1 }

systemBIOSIndex                                 OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0002 This attribute defines the index (one based) of the
        system BIOS."
    ::= { systemBIOSTableEntry 2 }

systemBIOSStateCapabilities                     OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0003 This attribute defines the state capabilities of the
        system BIOS."
    ::= { systemBIOSTableEntry 3 }

systemBIOSStateSettings                         OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0004 This attribute defines the state settings of the
        system BIOS."
    ::= { systemBIOSTableEntry 4 }

systemBIOSStatus                                OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0005 This attribute defines the status of the system BIOS."
    ::= { systemBIOSTableEntry 5 }

systemBIOSReleaseDateName                       OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0007 This attribute defines the release date name of the
        system BIOS."
    ::= { systemBIOSTableEntry 7 }

systemBIOSVersionName                           OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0008 This attribute defines the version name of the
        system BIOS."
    ::= { systemBIOSTableEntry 8 }

systemBIOSManufacturerName                      OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0050.0001.0011 This attribute defines the name of the manufacturer
        of the system BIOS."
    ::= { systemBIOSTableEntry 11 }


-------------------------------------------------------------------------------
-- Firmware Table
--
-- OID Format: *******.4.1.674.10892.5.4.300.60.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

FirmwareType                                    ::= INTEGER {
    other(1),                                   -- type is other than following values
    unknown(2),                                 -- type is unknown
    lifecycleController(20),                    -- type is Lifecycle Controller
    iDRAC7(21),                                 -- type is Integrated Dell Remote Access Controller 7
    iDRAC8(22)                                  -- type is Integrated Dell Remote Access Controller 8
}

FirmwareTableEntry                              ::= SEQUENCE {
    firmwarechassisIndex                        ObjectRange,
    firmwareIndex                               ObjectRange,
    firmwareStateCapabilities                   StateCapabilitiesFlags,
    firmwareStateSettings                       StateSettingsFlags,
    firmwareStatus                              ObjectStatusEnum,
    firmwareSize                                Unsigned16BitRange,
    firmwareType                                FirmwareType,
    firmwareTypeName                            String64,
    firmwareUpdateCapabilities                  Unsigned16BitRange,
    firmwareVersionName                         String64
}

firmwareTable                                   OBJECT-TYPE
    SYNTAX      SEQUENCE OF FirmwareTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0060 This object defines the Firmware Table."
    ::= { chassisInformationGroup 60 }

firmwareTableEntry                              OBJECT-TYPE
    SYNTAX      FirmwareTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001 This object defines the Firmware Table Entry."
    INDEX       { firmwarechassisIndex,
                  firmwareIndex }
    ::= { firmwareTable 1 }

firmwarechassisIndex                            OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { firmwareTableEntry 1 }

firmwareIndex                                   OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0002 This attribute defines the index (one based) of the
        firmware."
    ::= { firmwareTableEntry 2 }

firmwareStateCapabilities                       OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0003 This attribute defines the state capabilities of the
        firmware."
    ::= { firmwareTableEntry 3 }

firmwareStateSettings                           OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0004 This attribute defines the state settings of the
        firmware."
    ::= { firmwareTableEntry 4 }

firmwareStatus                                  OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0005 This attribute defines the status of the firmware."
    ::= { firmwareTableEntry 5 }

firmwareSize                                    OBJECT-TYPE
    SYNTAX      Unsigned16BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0006 This attribute defines the image size of the firmware
        in KBytes.  Zero indicates size is unknown."
    ::= { firmwareTableEntry 6 }

firmwareType                                    OBJECT-TYPE
    SYNTAX      FirmwareType
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0007 This attribute defines the type of firmware."
    ::= { firmwareTableEntry 7 }

firmwareTypeName                                OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0008 This attribute defines the type name of the firmware."
    ::= { firmwareTableEntry 8 }

firmwareUpdateCapabilities                      OBJECT-TYPE
    SYNTAX      Unsigned16BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0009 This attribute defines the bitmap of supported methods
        for firmware update."
    ::= { firmwareTableEntry 9 }

firmwareVersionName                             OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0060.0001.0011 This attribute defines the version of the firmware."
    ::= { firmwareTableEntry 11 }


-------------------------------------------------------------------------------
-- Intrusion Table
--
-- OID Format: *******.4.1.674.10892.5.4.300.70.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

IntrusionReadingEnum                            ::= INTEGER {
    chassisNotBreached(1),                      -- chassis not breached and no uncleared breaches
    chassisBreached(2),                         -- chassis currently breached
    chassisBreachedPrior(3),                    -- chassis breached prior to boot and has not been cleared
    chassisBreachSensorFailure(4)               -- intrusion sensor has failed
}

IntrusionTypeEnum                               ::= INTEGER {
    chassisBreachDetectionWhenPowerON(1),       -- type is detect intrusion while power on
    chassisBreachDetectionWhenPowerOFF(2)       -- type is detect intrusion while power off
}

IntrusionTableEntry                             ::= SEQUENCE {
    intrusionchassisIndex                       ObjectRange,
    intrusionIndex                              ObjectRange,
    intrusionStateCapabilities                  StateCapabilitiesFlags,
    intrusionStateSettings                      StateSettingsFlags,
    intrusionStatus                             ObjectStatusEnum,
    intrusionReading                            IntrusionReadingEnum,
    intrusionType                               IntrusionTypeEnum,
    intrusionLocationName                       String64
}

intrusionTable                                  OBJECT-TYPE
    SYNTAX      SEQUENCE OF IntrusionTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0070 This object defines the Intrusion Table."
    ::= { chassisInformationGroup 70 }

intrusionTableEntry                             OBJECT-TYPE
    SYNTAX      IntrusionTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001 This object defines the Intrusion Table Entry."
    INDEX       { intrusionchassisIndex,
                  intrusionIndex }
    ::= { intrusionTable 1 }

intrusionchassisIndex                           OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { intrusionTableEntry 1 }

intrusionIndex                                  OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0002 This attribute defines the index (one based) of the
        intrusion sensor."
    ::= { intrusionTableEntry 2 }

intrusionStateCapabilities                      OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0003 This attribute defines the state capabilities of the
        intrusion sensor."
    ::= { intrusionTableEntry 3 }

intrusionStateSettings                          OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0004 This attribute defines the state settings of the
        intrusion sensor."
    ::= { intrusionTableEntry 4 }

intrusionStatus                                 OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0005 This attribute defines the status of the
        intrusion sensor."
    ::= { intrusionTableEntry 5 }

intrusionReading                                OBJECT-TYPE
    SYNTAX      IntrusionReadingEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0006 This attribute defines the reading of the
        intrusion sensor."
    ::= { intrusionTableEntry 6 }

intrusionType                                   OBJECT-TYPE
    SYNTAX      IntrusionTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0007 This attribute defines the type of the
        intrusion sensor."
    ::= { intrusionTableEntry 7 }

intrusionLocationName                           OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0070.0001.0008 This attribute defines the location of the
        intrusion sensor."
    ::= { intrusionTableEntry 8 }


-------------------------------------------------------------------------------
-- Lifecycle (LC) Log Table
--
-- OID Format: *******.4.1.674.10892.5.4.300.90.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

LcLogCategoryEnum                               ::= INTEGER {
    system(1),                                  -- System Health category
    storage(2),                                 -- Storage category
    updates(3),                                 -- Updates category
    audit(4),                                   -- Audit category
    configuration(5),                           -- Configuration category
    workNotes(6)                                -- Work Notes category
}

LcLogTableEntry                                 ::= SEQUENCE {
    lcLogChassisIndex                           ObjectRange,
    lcLogRecordIndex                            Unsigned32BitRange,
    lcLogSequenceNumber                         Unsigned32BitRange,
    lcLogCategory                               LcLogCategoryEnum,
    lcLogSeverityStatus                         ObjectStatusEnum,
    lcLogDateName                               DateName,
    lcLogFQDD                                   FQDDString,
    lcLogMessageID                              DisplayString,
    lcLogMessage                                DisplayString,
    lcLogDetailedDescription                    DisplayString,
    lcLogRecommededAction                       DisplayString,
    lcLogComment                                DisplayString
}

-- Note: You can only access the lcLogTable table via SNMPv3 queries.
--       Access to the table is blocked for SNMPv1 and SNMPv2c queries.
lcLogTable                                      OBJECT-TYPE
    SYNTAX      SEQUENCE OF LcLogTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0090 This object defines the Lifecycle (LC) Log Table.
        Lifecycle (LC) Log table records are ordered from oldest to newest.
        Note: This table can only be accessed via SNMPv3 queries."
    ::= { chassisInformationGroup 90 }

lcLogTableEntry                                 OBJECT-TYPE
    SYNTAX      LcLogTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001 This object defines the Lifcycle (LC) Log Table Entry."
    INDEX       { lcLogChassisIndex,
                  lcLogRecordIndex }
    ::= { lcLogTable 1 }

lcLogChassisIndex                               OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0001 This attribute defines the index (one based)
        of the associated system chassis."
    ::= { lcLogTableEntry 1 }

lcLogRecordIndex                                OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0002 This attribute defines the index (one based)
        of the LC log record."
    ::= { lcLogTableEntry 2 }

lcLogSequenceNumber                             OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0003 This attribute defines the LC Log sequence number
        of the event associated with the LC log record."
    ::= { lcLogTableEntry 3 }

lcLogCategory                                   OBJECT-TYPE
    SYNTAX      LcLogCategoryEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0004 This attribute defines the category
        of the event associated with the LC log record."
    ::= { lcLogTableEntry 4 }

lcLogSeverityStatus                             OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0005 This attribute defines the severity
        of the event associated with the LC log record."
    ::= { lcLogTableEntry 5 }

lcLogDateName                                   OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0006 This attribute defines the date and time
        of the event associated with the LC log record."
    ::= { lcLogTableEntry 6 }

lcLogFQDD                                       OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0007 Fully qualified device descriptor (FQDD)
        of the device associated with the event associated with the LC log record."
    ::= { lcLogTableEntry 7 }

lcLogMessageID                                  OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..8))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0008 This attribute defines the Message ID
        of the event associated with the LC log record."
    ::= { lcLogTableEntry 8 }

lcLogMessage                                    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..512))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0009 This attribute defines the message
        of the event associated with the LC log record."
    ::= { lcLogTableEntry 9 }

lcLogDetailedDescription                        OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..2048))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0010 This attribute defines the detailed description
        of the event associated with the LC log record."
    ::= { lcLogTableEntry 10 }

lcLogRecommededAction                           OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..2048))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0011 This attribute defines an optional recommended action
        associated with the event associated with the LC log record."
    ::= { lcLogTableEntry 11 }

lcLogComment                                    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..128))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0300.0090.0001.0012 This attribute defines an optional user comment
        associated with the event associated with the LC log record."
    ::= { lcLogTableEntry 12 }



-------------------------------------------------------------------------------
-- Power Group
--
-- OID Format: *******.4.1.674.10892.5.4.600
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
-- Power Unit Table
--
-- OID Format: *******.4.1.674.10892.5.4.600.10.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

PowerUnitTableEntry                             ::= SEQUENCE {
    powerUnitchassisIndex                       ObjectRange,
    powerUnitIndex                              ObjectRange,
    powerUnitStateCapabilities                  StateCapabilitiesFlags,
    powerUnitStateSettings                      StateSettingsFlags,
    powerUnitRedundancyStatus                   StatusRedundancyEnum,
    powerSupplyCountForRedundancy               ObjectRange,
    powerUnitName                               String64,
    powerUnitStatus                             ObjectStatusEnum
}

powerUnitTable                                  OBJECT-TYPE
    SYNTAX      SEQUENCE OF PowerUnitTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0010 This object defines the Power Unit Table."
    ::= { powerGroup 10 }

powerUnitTableEntry                             OBJECT-TYPE
    SYNTAX      PowerUnitTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001 This object defines the Power Unit Table Entry."
    INDEX       { powerUnitchassisIndex,
                  powerUnitIndex }
    ::= { powerUnitTable 1 }

powerUnitchassisIndex                           OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0001 This attribute defines the index (one based) of
         the system chassis."
    ::= { powerUnitTableEntry 1 }

powerUnitIndex                                  OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0002 This attribute defines the index (one based) of the
        power unit."
    ::= { powerUnitTableEntry 2 }

powerUnitStateCapabilities                      OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0003 This attribute defines the state capabilities of the
        power unit."
    ::= { powerUnitTableEntry 3 }

powerUnitStateSettings                          OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0004 This attribute defines the state settings of the
        power unit."
    ::= { powerUnitTableEntry 4 }

powerUnitRedundancyStatus                       OBJECT-TYPE
    SYNTAX      StatusRedundancyEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0005 This attribute defines the redundancy status of the
        power unit."
    ::= { powerUnitTableEntry 5 }

powerSupplyCountForRedundancy                   OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0006 This attribute defines the total number of power supplies
        required for this power unit to have full redundancy."
    ::= { powerUnitTableEntry 6 }

powerUnitName                                   OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0007 This attribute defines the name of the power unit."
    ::= { powerUnitTableEntry 7 }

powerUnitStatus                                 OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0010.0001.0008 This attribute defines the status of the power unit."
    ::= { powerUnitTableEntry 8 }


-------------------------------------------------------------------------------
-- Power Supply Table
--
-- OID Format: *******.4.1.674.10892.5.4.600.12.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

PowerSupplyStateCapabilitiesUniqueFlags         ::= INTEGER {
    -- If 0 (zero), there are no power supply state capabilities
    -- Note: These values are bit masks, so combination values are possible.
    unknown(1),                                 -- state capabilities are unknown
    onlineCapable(2),                           -- power supply can be enabled (online) or disabled (offline)
    notReadyCapable(4)                          -- power supply can be not ready
}

PowerSupplyStateSettingsUniqueFlags             ::= INTEGER {
    -- If 0 (zero), there are no power supply state settings
    -- Note: These values are bit masks, so combination values are possible.
    unknown(1),                                 -- state settings are unknown
    onLine(2),                                  -- power supply is enabled (online)
    notReady(4),                                -- power supply is not ready
    fanFailure(8),                              -- power supply fan has failed
    onlineAndFanFailure(10),
    powerSupplyIsON(16),                        -- power supply is supplying power
    powerSupplyIsOK(32),                        -- power supply is indicating it is OK
    acSwitchIsON(64),                           -- power supply is indicating AC power switch is on
    onlineandAcSwitchIsON(66),
    acPowerIsON(128),                           -- power supply is indicating AC power is on
    onlineAndAcPowerIsON(130),
    onlineAndPredictiveFailure(210),
    acPowerAndSwitchAreOnPowerSupplyIsOnIsOkAndOnline(242)
}

PowerSupplyTypeEnum                             ::= INTEGER {
    powerSupplyTypeIsOther(1),                  -- type is other than following values
    powerSupplyTypeIsUnknown(2),                -- type is unknown
    powerSupplyTypeIsLinear(3),                 -- type is Linear
    powerSupplyTypeIsSwitching(4),              -- type is Switching
    powerSupplyTypeIsBattery(5),                -- type is Battery
    powerSupplyTypeIsUPS(6),                    -- type is Uninterruptible Power Supply
    powerSupplyTypeIsConverter(7),              -- type is Converter
    powerSupplyTypeIsRegulator(8),              -- type is Regulator
    powerSupplyTypeIsAC(9),                     -- type is AC
    powerSupplyTypeIsDC(10),                    -- type is DC
    powerSupplyTypeIsVRM(11)                    -- type is VRM
}

PowerSupplySensorStateFlags                     ::= INTEGER {
    -- Note: These values are bit masks, so combination values are possible.
    presenceDetected(1),                        -- state is Presence detected
    psFailureDetected(2),                       -- state is PS Failure detected
    predictiveFailure(4),                       -- state is Predictive Failure
    psACLost(8),                                -- state is PS AC lost
    acLostOrOutOfRange(16),                     -- state is AC lost or out-of-range
    acOutOfRangeButPresent(32),                 -- state is AC out-of-range, but present
    configurationError(64)                      -- state is Configuration error
}

PowerSupplyConfigurationErrorTypeEnum           ::= INTEGER {
    vendorMismatch(1),                          -- error type is Vendor mismatch
    revisionMismatch(2),                        -- error type is Revision mismatch
    processorMissing(3)                         -- error type is Processor missing
}

PowerSupplyTableEntry                           ::= SEQUENCE {
    powerSupplychassisIndex                     ObjectRange,
    powerSupplyIndex                            ObjectRange,
    powerSupplyStateCapabilitiesUnique          PowerSupplyStateCapabilitiesUniqueFlags,
    powerSupplyStateSettingsUnique              PowerSupplyStateSettingsUniqueFlags,
    powerSupplyStatus                           ObjectStatusEnum,
    powerSupplyOutputWatts                      Signed32BitRange,
    powerSupplyType                             PowerSupplyTypeEnum,
    powerSupplyLocationName                     String64,
    powerSupplyMaximumInputVoltage              Signed32BitRange,
    powerSupplypowerUnitIndexReference          ObjectRange,
    powerSupplySensorState                      PowerSupplySensorStateFlags,
    powerSupplyConfigurationErrorType           PowerSupplyConfigurationErrorTypeEnum,
    powerSupplyPowerMonitorCapable              BooleanType,
    powerSupplyRatedInputWattage                Signed32BitRange,
    powerSupplyFQDD                             FQDDString,
    powerSupplyCurrentInputVoltage              Signed32BitRange
}

powerSupplyTable                                OBJECT-TYPE
    SYNTAX      SEQUENCE OF PowerSupplyTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0012 This object defines the Power Supply Table."
    ::= { powerGroup 12 }

powerSupplyTableEntry                           OBJECT-TYPE
    SYNTAX      PowerSupplyTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001 This object defines the Power Supply Table Entry."
    INDEX       { powerSupplychassisIndex,
                  powerSupplyIndex }
    ::= { powerSupplyTable 1 }

powerSupplychassisIndex                         OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0001 This attribute defines the index (one based) of
         the system chassis."
    ::= { powerSupplyTableEntry 1 }

powerSupplyIndex                                OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0002 This attribute defines the index (one based) of the
        power supply."
    ::= { powerSupplyTableEntry 2 }

powerSupplyStateCapabilitiesUnique              OBJECT-TYPE
    SYNTAX      PowerSupplyStateCapabilitiesUniqueFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0003 This attribute defines the state capabilities of the
        power supply."
    ::= { powerSupplyTableEntry 3 }

powerSupplyStateSettingsUnique                  OBJECT-TYPE
    SYNTAX      PowerSupplyStateSettingsUniqueFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0004 This attribute defines the state settings of the
        power supply."
    ::= { powerSupplyTableEntry 4 }

powerSupplyStatus                               OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0005 This attribute defines the status of the power supply."
    ::= { powerSupplyTableEntry 5 }

powerSupplyOutputWatts                          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0006 This attribute defines the maximum sustained output
        wattage of the power supply (in tenths of Watts)."
    ::= { powerSupplyTableEntry 6 }

powerSupplyType                                 OBJECT-TYPE
    SYNTAX      PowerSupplyTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0007 This attribute defines the type of the power supply."
    ::= { powerSupplyTableEntry 7 }

powerSupplyLocationName                         OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0008 This attribute defines the location of the power supply."
    ::= { powerSupplyTableEntry 8 }

powerSupplyMaximumInputVoltage                  OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0009 This attribute defines the maximum input voltage of the
        power supply (in Volts)."
    ::= { powerSupplyTableEntry 9 }

powerSupplypowerUnitIndexReference              OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0010 This attribute defines the index to the associated
        power unit if the power supply is part of a power unit."
    ::= { powerSupplyTableEntry 10 }

powerSupplySensorState                          OBJECT-TYPE
    SYNTAX      PowerSupplySensorStateFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0011 This attribute defines the state reported by the
        power supply sensor.  This attribute supplements the attribute
        powerSupplyStateSettingsUnique."
    ::= { powerSupplyTableEntry 11 }

powerSupplyConfigurationErrorType               OBJECT-TYPE
    SYNTAX      PowerSupplyConfigurationErrorTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0012 This attribute defines the type of configuration error
        reported by the power supply sensor.  When the configurationError bit is on
        in the value for the attribute powerSupplySensorState, a value is returned
        for this attribute; otherwise, a value is not returned for this attribute."
    ::= { powerSupplyTableEntry 12 }

powerSupplyPowerMonitorCapable                  OBJECT-TYPE
    SYNTAX      BooleanType
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0013 This attribute defines a boolean value that reports
        whether the power supply is capable of monitoring power consumption."
    ::= { powerSupplyTableEntry 13 }

powerSupplyRatedInputWattage                    OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0014 This attribute defines the rated input wattage of the
        power supply (in tenths of Watts)."
    ::= { powerSupplyTableEntry 14 }

powerSupplyFQDD                                 OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0015 Fully qualified device descriptor (FQDD) of the
        power supply."
    ::= { powerSupplyTableEntry 15 }

powerSupplyCurrentInputVoltage                  OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0012.0001.0016 This attribute defines the current input voltage to the
        power supply (in Volts)."
    ::= { powerSupplyTableEntry 16 }


-------------------------------------------------------------------------------
-- Voltage Probe Table
--
-- OID Format: *******.4.1.674.10892.5.4.600.20.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

VoltageTypeEnum                                 ::= INTEGER {
    voltageProbeTypeIsOther(1),                 -- type is other than following values
    voltageProbeTypeIsUnknown(2),               -- type is unknown
    voltageProbeTypeIs1Point5Volt(3),           -- type is 1.5 volt probe
    voltageProbeTypeIs3Point3Volt(4),           -- type is 3.3 volt probe
    voltageProbeTypeIs5Volt(5),                 -- type is 5 volt probe
    voltageProbeTypeIsMinus5Volt(6),            -- type is -5 volt probe
    voltageProbeTypeIs12Volt(7),                -- type is 12 volt probe
    voltageProbeTypeIsMinus12Volt(8),           -- type is -12 volt probe
    voltageProbeTypeIsIO(9),                    -- type is I/O probe
    voltageProbeTypeIsCore(10),                 -- type is Core probe
    voltageProbeTypeIsFLEA(11),                 -- type is FLEA (standby) probe
    voltageProbeTypeIsBattery(12),              -- type is Battery probe
    voltageProbeTypeIsTerminator(13),           -- type is SCSI Termination probe
    voltageProbeTypeIs2Point5Volt(14),          -- type is 2.5 volt probe
    voltageProbeTypeIsGTL(15),                  -- type is GTL (ground termination logic) probe
    voltageProbeTypeIsDiscrete(16),             -- type is voltage probe with discrete reading
    voltageProbeTypeIsGenericDiscrete(17),      -- type is generic discrete reading
    voltageProbeTypeIsPSVoltage(18),            -- type is Power Supply voltage probe
    voltageProbeTypeIsMemoryStatus(19)          -- type is Memory Status probe
}

VoltageDiscreteReadingEnum                      ::= INTEGER {
    voltageIsGood(1),                           -- voltage reading is Good
    voltageIsBad(2)                             -- voltage reading is Bad
}

VoltageProbeTableEntry                          ::= SEQUENCE {
    voltageProbechassisIndex                    ObjectRange,
    voltageProbeIndex                           ObjectRange,
    voltageProbeStateCapabilities               StateCapabilitiesFlags,
    voltageProbeStateSettings                   StateSettingsFlags,
    voltageProbeStatus                          StatusProbeEnum,
    voltageProbeReading                         Signed32BitRange,
    voltageProbeType                            VoltageTypeEnum,
    voltageProbeLocationName                    String64,
    voltageProbeUpperNonRecoverableThreshold    Signed32BitRange,
    voltageProbeUpperCriticalThreshold          Signed32BitRange,
    voltageProbeUpperNonCriticalThreshold       Signed32BitRange,
    voltageProbeLowerNonCriticalThreshold       Signed32BitRange,
    voltageProbeLowerCriticalThreshold          Signed32BitRange,
    voltageProbeLowerNonRecoverableThreshold    Signed32BitRange,
    voltageProbeProbeCapabilities               ProbeCapabilitiesFlags,
    voltageProbeDiscreteReading                 VoltageDiscreteReadingEnum
}

voltageProbeTable                               OBJECT-TYPE
    SYNTAX      SEQUENCE OF VoltageProbeTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0020 This object defines the Voltage Probe Table."
    ::= { powerGroup 20 }

voltageProbeTableEntry                          OBJECT-TYPE
    SYNTAX      VoltageProbeTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.001 This object defines the Voltage Probe Table Entry."
    INDEX       { voltageProbechassisIndex,
                  voltageProbeIndex }
    ::= { voltageProbeTable 1 }

voltageProbechassisIndex                        OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0001 This attribute defines the index (one based) of
         the system chassis."
    ::= { voltageProbeTableEntry 1 }

voltageProbeIndex                               OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0002 This attribute defines the index (one based) of the
        voltage probe."
    ::= { voltageProbeTableEntry 2 }

voltageProbeStateCapabilities                   OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0003 This attribute defines the state capabilities of the
        voltage probe."
    ::= { voltageProbeTableEntry 3 }

voltageProbeStateSettings                       OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0004 This attribute defines the state settings of the
        voltage probe."
    ::= { voltageProbeTableEntry 4 }

voltageProbeStatus                              OBJECT-TYPE
    SYNTAX      StatusProbeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0005 This attribute defines the probe status of the
        voltage probe."
    ::= { voltageProbeTableEntry 5 }

voltageProbeReading                             OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0006 This attribute defines the reading for a voltage
        probe of type other than voltageProbeTypeIsDiscrete.  When the value
        for voltageProbeType is other than voltageProbeTypeIsDiscrete, the value
        returned for this attribute is the voltage that the probe is reading
        in millivolts.  When the value for voltageProbeType is
        voltageProbeTypeIsDiscrete, a value is not returned for this attribute."
    ::= { voltageProbeTableEntry 6 }

voltageProbeType                                OBJECT-TYPE
    SYNTAX      VoltageTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0007 This attribute defines the type of the voltage probe."
    ::= { voltageProbeTableEntry 7 }

voltageProbeLocationName                        OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0008 This attribute defines the location name of the
        voltage probe."
    ::= { voltageProbeTableEntry 8 }

voltageProbeUpperNonRecoverableThreshold        OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0009 This attribute defines the upper nonrecoverable threshold
        of the voltage probe.  The value is an integer representing the voltage
        of the threshold in millivolts."
    ::= { voltageProbeTableEntry 9 }

voltageProbeUpperCriticalThreshold              OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0010 This attribute defines the upper critical threshold
        of the voltage probe.  The value is an integer representing the voltage
        of the threshold in millivolts."
    ::= { voltageProbeTableEntry 10 }

voltageProbeUpperNonCriticalThreshold           OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0011 This attribute defines the upper noncritical threshold
        of the voltage probe.  The value is an integer representing the voltage
        of the threshold in millivolts."
    ::= { voltageProbeTableEntry 11 }

voltageProbeLowerNonCriticalThreshold           OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0012 This attribute defines the lower noncritical threshold
        of the voltage probe.  The value is an integer representing the voltage
        of the threshold in millivolts."
    ::= { voltageProbeTableEntry 12 }

voltageProbeLowerCriticalThreshold              OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0013 This attribute defines the lower critical threshold
        of the voltage probe.  The value is an integer representing the voltage
        of the threshold in millivolts."
    ::= { voltageProbeTableEntry 13 }

voltageProbeLowerNonRecoverableThreshold        OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0014 This attribute defines the lower nonrecoverable threshold
        of the voltage probe.  The value is an integer representing the voltage
        of the threshold in millivolts."
    ::= { voltageProbeTableEntry 14 }

voltageProbeProbeCapabilities                   OBJECT-TYPE
    SYNTAX      ProbeCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0015 This attribute defines the probe capabilities of the
        voltage probe."
    ::= { voltageProbeTableEntry 15 }

voltageProbeDiscreteReading                     OBJECT-TYPE
    SYNTAX      VoltageDiscreteReadingEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0020.0001.0016 This attribute defines the reading for a voltage
        probe of type voltageProbeTypeIsDiscrete.  When the value for voltageProbeType
        is other than voltageProbeTypeIsDiscrete, a value is not returned for this
        attribute.  When the value for voltageProbeType is voltageProbeTypeIsDiscrete,
        the value returned for this attribute is the discrete reading for the probe."
    ::= { voltageProbeTableEntry 16 }


-------------------------------------------------------------------------------
-- Amperage Probe Table
--
-- OID Format: *******.4.1.674.10892.5.4.600.30.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

AmperageProbeTypeEnum                           ::= INTEGER {
    amperageProbeTypeIsOther(1),                -- type is other than following values
    amperageProbeTypeIsUnknown(2),              -- type is unknown
    amperageProbeTypeIs1Point5Volt(3),          -- type is 1.5 amperage probe
    amperageProbeTypeIs3Point3volt(4),          -- type is 3.3 amperage probe
    amperageProbeTypeIs5Volt(5),                -- type is 5 amperage probe
    amperageProbeTypeIsMinus5Volt(6),           -- type is -5 amperage probe
    amperageProbeTypeIs12Volt(7),               -- type is 12 amperage probe
    amperageProbeTypeIsMinus12Volt(8),          -- type is -12 amperage probe
    amperageProbeTypeIsIO(9),                   -- type is I/O probe
    amperageProbeTypeIsCore(10),                -- type is Core probe
    amperageProbeTypeIsFLEA(11),                -- type is FLEA (standby) probe
    amperageProbeTypeIsBattery(12),             -- type is Battery probe
    amperageProbeTypeIsTerminator(13),          -- type is SCSI Termination probe
    amperageProbeTypeIs2Point5Volt(14),         -- type is 2.5 amperage probe
    amperageProbeTypeIsGTL(15),                 -- type is GTL (ground termination logic) probe
    amperageProbeTypeIsDiscrete(16),            -- type is amperage probe with discrete reading
    amperageProbeTypeIsPowerSupplyAmps(23),     -- type is Power Supply probe with reading in Amps
    amperageProbeTypeIsPowerSupplyWatts(24),    -- type is Power Supply probe with reading in Watts
    amperageProbeTypeIsSystemAmps(25),          -- type is System probe with reading in Amps
    amperageProbeTypeIsSystemWatts(26)          -- type is System probe with reading in Watts
}

AmperageDiscreteReadingEnum                     ::= INTEGER {
    amperageIsGood(1),                          -- amperage reading is Good
    amperageIsBad(2)                            -- amperage reading is Bad
}

AmperageProbeTableEntry                         ::= SEQUENCE {
    amperageProbechassisIndex                   ObjectRange,
    amperageProbeIndex                          ObjectRange,
    amperageProbeStateCapabilities              StateCapabilitiesFlags,
    amperageProbeStateSettings                  StateSettingsFlags,
    amperageProbeStatus                         StatusProbeEnum,
    amperageProbeReading                        Signed32BitRange,
    amperageProbeType                           AmperageProbeTypeEnum,
    amperageProbeLocationName                   String64,
    amperageProbeUpperNonRecoverableThreshold   Signed32BitRange,
    amperageProbeUpperCriticalThreshold         Signed32BitRange,
    amperageProbeUpperNonCriticalThreshold      Signed32BitRange,
    amperageProbeLowerNonCriticalThreshold      Signed32BitRange,
    amperageProbeLowerCriticalThreshold         Signed32BitRange,
    amperageProbeLowerNonRecoverableThreshold   Signed32BitRange,
    amperageProbeProbeCapabilities              ProbeCapabilitiesFlags,
    amperageProbeDiscreteReading                AmperageDiscreteReadingEnum
}

amperageProbeTable                              OBJECT-TYPE
    SYNTAX      SEQUENCE OF AmperageProbeTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0030 This object defines the Amperage Probe Table."
    ::= { powerGroup 30 }

amperageProbeTableEntry                         OBJECT-TYPE
    SYNTAX      AmperageProbeTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001 This object defines the Amperage Probe Table Entry."
    INDEX       { amperageProbechassisIndex,
                  amperageProbeIndex }
    ::= { amperageProbeTable 1 }

amperageProbechassisIndex                       OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0001 This attribute defines the index (one based) of
         the system chassis."
    ::= { amperageProbeTableEntry 1 }

amperageProbeIndex                              OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0002 This attribute defines the index (one based) of the
        amperage probe."
    ::= { amperageProbeTableEntry 2 }

amperageProbeStateCapabilities                  OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0003 This attribute defines the state capabilities of the
        amperage probe."
    ::= { amperageProbeTableEntry 3 }

amperageProbeStateSettings                      OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0004 This attribute defines the state settings of the
        amperage probe."
    ::= { amperageProbeTableEntry 4 }

amperageProbeStatus                             OBJECT-TYPE
    SYNTAX      StatusProbeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0005 This attribute defines the probe status of the
        amperage probe."
    ::= { amperageProbeTableEntry 5 }

amperageProbeReading                            OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0006 This attribute defines the reading for an amperage
        probe of type other than amperageProbeTypeIsDiscrete.

        When the value for amperageProbeType is amperageProbeTypeIsPowerSupplyAmps
        or amperageProbeTypeIsSystemAmps, the value returned for this attribute
        is the power usage that the probe is reading in tenths of Amps.

        When the value for amperageProbeType is amperageProbeTypeIsPowerSupplyWatts
        or amperageProbeTypeIsSystemWatts, the value returned for this attribute
        is the power usage that the probe is reading in Watts.

        When the value for amperageProbeType is other than amperageProbeTypeIsDiscrete,
        amperageProbeTypeIsPowerSupplyAmps, amperageProbeTypeIsPowerSupplyWatts,
        amperageProbeTypeIsSystemAmps or amperageProbeTypeIsSystemWatts,
        the value returned for this attribute is the amperage that the probe is
        reading in Milliamps.

        When the value for amperageProbeType is amperageProbeTypeIsDiscrete,
        a value is not returned for this attribute."
    ::= { amperageProbeTableEntry 6 }

amperageProbeType                               OBJECT-TYPE
    SYNTAX      AmperageProbeTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0007 This attribute defines the type of the amperage probe."
    ::= { amperageProbeTableEntry 7 }

amperageProbeLocationName                       OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0008 This attribute defines the location of the amperage probe."
    ::= { amperageProbeTableEntry 8 }

amperageProbeUpperNonRecoverableThreshold       OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0009 This attribute defines the upper nonrecoverable threshold
        of the amperage probe.  The value is an integer representing the amperage
        of the threshold in milliamps."
    ::= { amperageProbeTableEntry 9 }

amperageProbeUpperCriticalThreshold             OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0010 This attribute defines the upper critical threshold
        of the amperage probe.  The value is an integer representing the amperage
        of the threshold in milliamps."
    ::= { amperageProbeTableEntry 10 }

amperageProbeUpperNonCriticalThreshold          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0011 This attribute defines the upper noncritical threshold
        of the amperage probe.  The value is an integer representing the amperage
        of the threshold in milliamps."
    ::= { amperageProbeTableEntry 11 }

amperageProbeLowerNonCriticalThreshold          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0012 This attribute defines the lower noncritical threshold
        of the amperage probe.  The value is an integer representing the amperage
        of the threshold in milliamps."
    ::= { amperageProbeTableEntry 12 }

amperageProbeLowerCriticalThreshold             OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0013 This attribute defines the lower critical threshold
        of the amperage probe.  The value is an integer representing the amperage
        of the threshold in milliamps."
    ::= { amperageProbeTableEntry 13 }

amperageProbeLowerNonRecoverableThreshold       OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0014 This attribute defines the lower nonrecoverable threshold
        of the amperage probe.  The value is an integer representing the amperage
        of the threshold in milliamps."
    ::= { amperageProbeTableEntry 14 }

amperageProbeProbeCapabilities                  OBJECT-TYPE
    SYNTAX      ProbeCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0015 This attribute defines the probe capabilities of the
        amperage probe."
    ::= { amperageProbeTableEntry 15 }

amperageProbeDiscreteReading                    OBJECT-TYPE
    SYNTAX      AmperageDiscreteReadingEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0030.0001.0016 This attribute defines the reading for an amperage
        probe of type amperageProbeTypeIsDiscrete.  When the value for amperageProbeType
        is other than amperageProbeTypeIsDiscrete, a value is not returned for this
        attribute.  When the value for amperageProbeType is amperageProbeTypeIsDiscrete,
        the value returned for this attribute is the discrete reading for the probe."
    ::= { amperageProbeTableEntry 16 }


-------------------------------------------------------------------------------
-- System Battery Table
--
-- OID Format: *******.4.1.674.10892.5.4.600.50.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

SystemBatteryReadingFlags                       ::= INTEGER {
    -- Note: These values are bit masks, so combination values are possible.
    predictiveFailure(1),                       -- battery predictive failure
    failed(2),                                  -- battery failed
    presenceDetected(4)                         -- battery presence detected
}

SystemBatteryTableEntry                         ::= SEQUENCE {
    systemBatteryChassisIndex                   ObjectRange,
    systemBatteryIndex                          ObjectRange,
    systemBatteryStateCapabilities              StateCapabilitiesFlags,
    systemBatteryStateSettings                  StateSettingsFlags,
    systemBatteryStatus                         ObjectStatusEnum,
    systemBatteryReading                        SystemBatteryReadingFlags,
    systemBatteryLocationName                   String64
}

systemBatteryTable                              OBJECT-TYPE
    SYNTAX      SEQUENCE OF SystemBatteryTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0050 This object defines the System Battery Table."
    ::= { powerGroup 50 }

systemBatteryTableEntry                         OBJECT-TYPE
    SYNTAX      SystemBatteryTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001 This object defines the System Battery Table Entry."
    INDEX       { systemBatteryChassisIndex,
                  systemBatteryIndex }
    ::= { systemBatteryTable 1 }

systemBatteryChassisIndex                        OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001.0001 This attribute defines the index (one based) of
         the system chassis that contains the battery."
    ::= { systemBatteryTableEntry 1 }

systemBatteryIndex                              OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001.0002 This attribute defines the index (one based) of the battery."
    ::= { systemBatteryTableEntry 2 }

systemBatteryStateCapabilities                  OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001.0003 This attribute defines the state capabilities of the battery."
    ::= { systemBatteryTableEntry 3 }

systemBatteryStateSettings                      OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001.0004 This attribute defines the state settings of the battery."
    ::= { systemBatteryTableEntry 4 }

systemBatteryStatus                             OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001.0005 This attribute defines the status of the battery."
    ::= { systemBatteryTableEntry 5 }

systemBatteryReading                            OBJECT-TYPE
    SYNTAX      SystemBatteryReadingFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001.0006 This attribute defines the reading of the battery."
    ::= { systemBatteryTableEntry 6 }

systemBatteryLocationName                       OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0050.0001.0007 This attribute defines the location of the battery."
    ::= { systemBatteryTableEntry 7 }


-------------------------------------------------------------------------------
-- Power Usage Table
--
-- OID Format: *******.4.1.674.10892.5.4.600.60.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

PowerCapCapabilitiesFlags                       ::= INTEGER {
    -- Note: These values are bit masks, so combination values are possible.
    -- none(0),                                 -  no power cap capabilities
    enable(1),                                  -- power cap can be enabled
    disable(2)                                  -- power cap can be disabled
}

PowerCapSettingEnum                             ::= INTEGER {
    -- disabled(0),                             -  power cap disabled
    enabled(1)                                  -- power cap enabled
}

PowerUsageTableEntry                            ::= SEQUENCE {
    powerUsageChassisIndex                      ObjectRange,
    powerUsageIndex                             ObjectRange,
    powerUsageStateCapabilities                 StateCapabilitiesFlags,
    powerUsageStateSettings                     StateSettingsFlags,
    powerUsageStatus                            ObjectStatusEnum,
    powerUsageEntityName                        String64,
    powerUsageCumulativeWattage                 Unsigned32BitRange,
    powerUsageCumulativeWattageStartDateName    DateName,
    powerUsagePeakWatts                         Unsigned32BitRange,
    powerUsagePeakWattsStartDateName            DateName,
    powerUsagePeakWattsReadingDateName          DateName,
    powerUsagePeakAmps                          Unsigned32BitRange,
    powerUsagePeakAmpsStartDateName             DateName,
    powerUsagePeakAmpsReadingDateName           DateName,
    powerUsageIdlePower                         Unsigned32BitRange,
    powerUsageMaxPotentialPower                 Unsigned32BitRange,
    powerUsagePowerCapCapabilities              PowerCapCapabilitiesFlags,
    powerUsagePowerCapSetting                   PowerCapSettingEnum,
    powerUsagePowerCapValue                     Unsigned32BitRange,
    powerUsageInstantaneousHeadroom             Unsigned32BitRange,
    powerUsagePeakHeadroom                      Unsigned32BitRange
}

powerUsageTable                                 OBJECT-TYPE
    SYNTAX      SEQUENCE OF PowerUsageTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0060 This object defines the Power Usage Table."
    ::= { powerGroup 60 }

powerUsageTableEntry                            OBJECT-TYPE
    SYNTAX      PowerUsageTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001 This object defines the Power Usage Table Entry."
    INDEX       { powerUsageChassisIndex,
                  powerUsageIndex }
    ::= { powerUsageTable 1 }

powerUsageChassisIndex                          OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0001 This attribute defines the index (one based) of
         the associated system chassis."
    ::= { powerUsageTableEntry 1 }

powerUsageIndex                                 OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0002 This attribute defines the index (one based) of the
        power usage information."
    ::= { powerUsageTableEntry 2 }

powerUsageStateCapabilities                     OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0003 This attribute defines the state capabilities of the
        power usage information."
    ::= { powerUsageTableEntry 3 }

powerUsageStateSettings                         OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0004 This attribute defines the state settings of the
        power usage information."
    ::= { powerUsageTableEntry 4 }

powerUsageStatus                                OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0005 This attribute defines the status of the
        power usage information."
    ::= { powerUsageTableEntry 5 }

powerUsageEntityName                            OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0006 This attribute defines the name of the entity
        associated with this power usage information."
    ::= { powerUsageTableEntry 6 }

powerUsageCumulativeWattage                     OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0007 This attribute defines the total wattage used
        (in Watt-hours) by this entity since the date and time specified
        by the powerUsageCumulativeWattageStartDateName attribute."
    ::= { powerUsageTableEntry 7 }

powerUsageCumulativeWattageStartDateName        OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0008 This attribute defines the date and time at
        which the data collection started for the value reported by the 
        powerUsageCumulativeWattage attribute."
    ::= { powerUsageTableEntry 8 }

powerUsagePeakWatts                             OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0009 This attribute defines the peak wattage reading
        (in Watts) for this entity since the date and time specified by the
        powerUsagePeakWattsStartDateName attribute."
    ::= { powerUsageTableEntry 9 }

powerUsagePeakWattsStartDateName                OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0010 This attribute defines the date and time at
        which the data collection started for the value reported by the 
        powerUsagePeakWatts attribute."
    ::= { powerUsageTableEntry 10 }

powerUsagePeakWattsReadingDateName              OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0011 This attribute defines the date and time at
        which the value reported by the powerUsagePeakWatts attribute was
        measured."
    ::= { powerUsageTableEntry 11 }

powerUsagePeakAmps                              OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0012 This attribute defines the peak amperage reading
        (in tenths of Amps) for this entity since the date and time specified
        by the powerUsagePeakAmpsStartDateName attribute."
    ::= { powerUsageTableEntry 12 }

powerUsagePeakAmpsStartDateName                 OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0013 This attribute defines the date and time at
        which the data collection started for the value reported by the 
        powerUsagePeakAmps attribute."
    ::= { powerUsageTableEntry 13 }

powerUsagePeakAmpsReadingDateName               OBJECT-TYPE
    SYNTAX      DateName
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0014 This attribute defines the date and time at
        which the value reported by the powerUsagePeakAmps attribute was
        measured."
    ::= { powerUsageTableEntry 14 }

powerUsageIdlePower                             OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0015 This attribute defines the system idle power
        (in Watts).  This is the minimum power the system can consume
        based on the current hardware configuration."
    ::= { powerUsageTableEntry 15 }

powerUsageMaxPotentialPower                     OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0016 This attribute defines the system maximum potential
        power (in Watts).  This is the maximum power the system can consume
        based on the current hardware configuration."
    ::= { powerUsageTableEntry 16 }

powerUsagePowerCapCapabilities                  OBJECT-TYPE
    SYNTAX      PowerCapCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0017 This attribute defines the system power cap capabilities."
    ::= { powerUsageTableEntry 17 }

powerUsagePowerCapSetting                       OBJECT-TYPE
    SYNTAX      PowerCapSettingEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0018 This attribute defines the system power cap setting."
    ::= { powerUsageTableEntry 18 }

powerUsagePowerCapValue                         OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0019 This attribute defines the system power cap value
        (in Watts)."
    ::= { powerUsageTableEntry 19 }

powerUsageInstantaneousHeadroom                 OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0020 This attribute defines the system instantaneous
        headroom (in Watts).  This is the theoretical maximum power drawn by
        the power supply minus instantaneous power draw."
    ::= { powerUsageTableEntry 20 }

powerUsagePeakHeadroom                          OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0600.0060.0001.0021 This attribute defines the system peak headroom
        (in Watts).  This is the theoretical maximum power drawn by the power
        supply minus peak power draw."
    ::= { powerUsageTableEntry 21 }


-------------------------------------------------------------------------------
-- Thermal Group
--
-- OID Format: *******.4.1.674.10892.5.4.700
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
-- Cooling Unit Table
--
-- OID Format: *******.4.1.674.10892.5.4.700.10.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

CoolingUnitTableEntry                           ::= SEQUENCE {
    coolingUnitchassisIndex                     ObjectRange,
    coolingUnitIndex                            ObjectRange,
    coolingUnitStateCapabilties                 StateCapabilitiesFlags,
    coolingUnitStateSettings                    StateSettingsFlags,
    coolingUnitRedundancyStatus                 StatusRedundancyEnum,
    coolingDeviceCountForRedundancy             ObjectRange,
    coolingUnitName                             String64,
    coolingUnitStatus                           ObjectStatusEnum
}

coolingUnitTable                                OBJECT-TYPE
    SYNTAX      SEQUENCE OF CoolingUnitTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0700.0010 This object defines the Cooling Unit Table."
    ::= { thermalGroup 10 }

coolingUnitTableEntry                           OBJECT-TYPE
    SYNTAX      CoolingUnitTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001 This object defines the Cooling Unit Table Entry."
    INDEX       { coolingUnitchassisIndex,
                  coolingUnitIndex }
    ::= { coolingUnitTable 1 }

coolingUnitchassisIndex                         OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0001 This attribute defines the index (one based) of
        the associated system chassis."
    ::= { coolingUnitTableEntry 1 }

coolingUnitIndex                                OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0002 This attribute defines the index (one based) of the
        cooling unit."
    ::= { coolingUnitTableEntry 2 }

coolingUnitStateCapabilties                     OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0003 This attribute defines the state capabilities of the
        cooling unit."
    ::= { coolingUnitTableEntry 3 }

coolingUnitStateSettings                        OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0004 This attribute defines the state settings of the
        cooling unit."
    ::= { coolingUnitTableEntry 4 }

coolingUnitRedundancyStatus                     OBJECT-TYPE
    SYNTAX      StatusRedundancyEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0005 This attribute defines the redundancy status of the
        cooling unit."
    ::= { coolingUnitTableEntry 5 }

coolingDeviceCountForRedundancy                 OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0006 This attribute defines the total number of cooling devices
        required for this cooling unit to have full redundancy."
    ::= { coolingUnitTableEntry 6 }

coolingUnitName                                 OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0007 This attribute defines the name of the cooling unit."
    ::= { coolingUnitTableEntry 7 }

coolingUnitStatus                               OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0010.0001.0008 This attribute defines the status of the cooling unit."
    ::= { coolingUnitTableEntry 8 }


-------------------------------------------------------------------------------
-- Cooling Device Table
--
-- OID Format: *******.4.1.674.10892.5.4.700.12.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

CoolingDeviceTypeEnum                           ::= INTEGER {
    coolingDeviceTypeIsOther(1),                -- type is other than following values
    coolingDeviceTypeIsUnknown(2),              -- type is unknown
    coolingDeviceTypeIsAFan(3),                 -- type is Fan
    coolingDeviceTypeIsABlower(4),              -- type is Centrifugal Blower
    coolingDeviceTypeIsAChipFan(5),             -- type is Fan on Integrated Circuit
    coolingDeviceTypeIsACabinetFan(6),          -- type is Cabinet Fan
    coolingDeviceTypeIsAPowerSupplyFan(7),      -- type is Power Supply Fan
    coolingDeviceTypeIsAHeatPipe(8),            -- type is Heat Pipe
    coolingDeviceTypeIsRefrigeration(9),        -- type is Integrated Refrigeration Unit
    coolingDeviceTypeIsActiveCooling(10),       -- type is Active Cooling Device
    coolingDeviceTypeIsPassiveCooling(11)       -- type is Passive Cooling Device
}

CoolingDeviceSubTypeEnum                        ::= INTEGER {
    coolingDeviceSubTypeIsOther(1),             -- subtype is other than following values
    coolingDeviceSubTypeIsUnknown(2),           -- subtype is unknown
    coolingDeviceSubTypeIsAFanThatReadsInRPM(3),-- subtype is Fan that reads RPM
    coolingDeviceSubTypeIsAFanReadsONorOFF(4),  -- subtype is Fan that reads Off or On
    coolingDeviceSubTypeIsAPowerSupplyFanThatReadsinRPM(5),  -- subtype is Power Supply Fan that reads RPM
    coolingDeviceSubTypeIsAPowerSupplyFanThatReadsONorOFF(6),-- subtype is Power Supply Fan that reads Off or On
    coolingDeviceSubTypeIsDiscrete(16)          -- subtype is cooling device with discrete reading
}

CoolingDeviceDiscreteReadingEnum                ::= INTEGER {
    coolingDeviceIsGood(1),                     -- cooling device is Good
    coolingDeviceIsBad(2)                       -- cooling device is Bad
}

CoolingDeviceTableEntry                         ::= SEQUENCE {
    coolingDevicechassisIndex                   ObjectRange,
    coolingDeviceIndex                          ObjectRange,
    coolingDeviceStateCapabilities              StateCapabilitiesFlags,
    coolingDeviceStateSettings                  StateSettingsFlags,
    coolingDeviceStatus                         StatusProbeEnum,
    coolingDeviceReading                        Signed32BitRange,
    coolingDeviceType                           CoolingDeviceTypeEnum,
    coolingDeviceLocationName                   String64,
    coolingDeviceUpperNonRecoverableThreshold   Signed32BitRange,
    coolingDeviceUpperCriticalThreshold         Signed32BitRange,
    coolingDeviceUpperNonCriticalThreshold      Signed32BitRange,
    coolingDeviceLowerNonCriticalThreshold      Signed32BitRange,
    coolingDeviceLowerCriticalThreshold         Signed32BitRange,
    coolingDeviceLowerNonRecoverableThreshold   Signed32BitRange,
    coolingDevicecoolingUnitIndexReference      ObjectRange,
    coolingDeviceSubType                        CoolingDeviceSubTypeEnum,
    coolingDeviceProbeCapabilities              ProbeCapabilitiesFlags,
    coolingDeviceDiscreteReading                CoolingDeviceDiscreteReadingEnum,
    coolingDeviceFQDD                           FQDDString
}

coolingDeviceTable                              OBJECT-TYPE
    SYNTAX      SEQUENCE OF CoolingDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0700.0012 This object defines the Cooling Device Table."
    ::= {  thermalGroup 12 }

coolingDeviceTableEntry                         OBJECT-TYPE
    SYNTAX      CoolingDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001 This object defines the Cooling Device Table Entry."
    INDEX       { coolingDevicechassisIndex,
                  coolingDeviceIndex }
    ::= { coolingDeviceTable 1 }

coolingDevicechassisIndex                       OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { coolingDeviceTableEntry 1 }

coolingDeviceIndex                              OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0002 This attribute defines the index (one based) of the
        cooling device."
    ::= { coolingDeviceTableEntry 2 }

coolingDeviceStateCapabilities                  OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0003 This attribute defines the state capabilities of the
        cooling device."
    ::= { coolingDeviceTableEntry 3 }

coolingDeviceStateSettings                      OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0004 This attribute defines the state settings of the
        cooling device."
    ::= { coolingDeviceTableEntry 4 }

coolingDeviceStatus                             OBJECT-TYPE
    SYNTAX      StatusProbeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0005 This attribute defines the probe status of the
        cooling device."
    ::= { coolingDeviceTableEntry 5 }

coolingDeviceReading                            OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0006 This attribute defines the reading for a cooling device
        of subtype other than coolingDeviceSubTypeIsDiscrete.  When the value
        for coolingDeviceSubType is other than coolingDeviceSubTypeIsDiscrete, the
        value returned for this attribute is the speed in RPM or the OFF/ON value
        of the cooling device.  When the value for coolingDeviceSubType is
        coolingDeviceSubTypeIsDiscrete, a value is not returned for this attribute."
    ::= { coolingDeviceTableEntry 6 }

coolingDeviceType                               OBJECT-TYPE
    SYNTAX      CoolingDeviceTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0007 This attribute defines the type of the cooling device."
    ::= { coolingDeviceTableEntry 7 }

coolingDeviceLocationName                       OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0008 This attribute defines the location name of the
        cooling device."
    ::= { coolingDeviceTableEntry 8 }

coolingDeviceUpperNonRecoverableThreshold       OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0009 This attribute defines the upper nonrecoverable threshold
        of the cooling device.  The value is an integer representing fan speed
        in revolutions per minute (RPM).  It is not applicable to OFF/ON type
        cooling devices or non-cooling device types."
    ::= { coolingDeviceTableEntry 9 }

coolingDeviceUpperCriticalThreshold             OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0010 This attribute defines the upper critical threshold
        of the cooling device.  The value is an integer representing fan speed
        in revolutions per minute (RPM).  It is not applicable to OFF/ON type
        cooling devices or non-cooling device types."
    ::= { coolingDeviceTableEntry 10 }

coolingDeviceUpperNonCriticalThreshold          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0011 This attribute defines the upper noncritical threshold
        of the cooling device.  The value is an integer representing fan speed
        in revolutions per minute (RPM).  It is not applicable to OFF/ON type
        cooling devices or non-cooling device types."
    ::= { coolingDeviceTableEntry 11 }

coolingDeviceLowerNonCriticalThreshold          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0012 This attribute defines the lower noncritical threshold
        of the cooling device.  The value is an integer representing fan speed
        in revolutions per minute (RPM).  It is not applicable to OFF/ON type
        cooling devices or non-cooling device types."
    ::= { coolingDeviceTableEntry 12 }

coolingDeviceLowerCriticalThreshold             OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0013 This attribute defines the lower critical threshold
        of the cooling device.  The value is an integer representing fan speed
        in revolutions per minute (RPM).  It is not applicable to OFF/ON type
        cooling devices or non-cooling device types."
    ::= { coolingDeviceTableEntry 13 }

coolingDeviceLowerNonRecoverableThreshold       OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0014 This attribute defines the lower nonrecoverable threshold
        of the cooling device.  The value is an integer representing fan speed
        in revolutions per minute (RPM).  It is not applicable to OFF/ON type
        cooling devices or non-cooling device types."
    ::= { coolingDeviceTableEntry 14 }

coolingDevicecoolingUnitIndexReference          OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0015 This attribute defines the index to the associated
        cooling unit."
    ::= { coolingDeviceTableEntry 15 }

coolingDeviceSubType                            OBJECT-TYPE
    SYNTAX      CoolingDeviceSubTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0016 This attribute defines the subtype of the cooling device."
    ::= { coolingDeviceTableEntry 16 }

coolingDeviceProbeCapabilities                  OBJECT-TYPE
    SYNTAX      ProbeCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0017 This attribute defines the probe capabilities of the
        cooling device."
    ::= { coolingDeviceTableEntry 17 }

coolingDeviceDiscreteReading                    OBJECT-TYPE
    SYNTAX      CoolingDeviceDiscreteReadingEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0018 This attribute defines the reading for a cooling device
        of type coolingDeviceSubTypeIsDiscrete.  When the value for
        coolingDeviceSubType is other than coolingDeviceSubTypeIsDiscrete, a value
        is not returned for this attribute.  When the value for coolingDeviceSubType
        is coolingDeviceSubTypeIsDiscrete, the value returned for this attribute
        is the discrete reading for the cooling device."
    ::= { coolingDeviceTableEntry 18 }

coolingDeviceFQDD                               OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0012.0001.0019 Fully qualified device descriptor (FQDD) of the
        cooling device."
    ::= { coolingDeviceTableEntry 19 }


-------------------------------------------------------------------------------
-- Temperature Probe Table
--
-- OID Format: *******.4.1.674.10892.5.4.700.20.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

TemperatureProbeTypeEnum                        ::= INTEGER {
    temperatureProbeTypeIsOther(1),             -- type is other than following values
    temperatureProbeTypeIsUnknown(2),           -- type is unknown
    temperatureProbeTypeIsAmbientESM(3),        -- type is Ambient Embedded Systems Management temperature probe
    temperatureProbeTypeIsDiscrete(16)          -- type is temperature probe with discrete reading
}

TemperatureDiscreteReadingEnum                  ::= INTEGER {
    temperatureIsGood(1),                       -- temperature reading is Good
    temperatureIsBad(2)                         -- temperature reading is Bad
}

TemperatureProbeTableEntry                      ::= SEQUENCE {
    temperatureProbechassisIndex                ObjectRange,
    temperatureProbeIndex                       ObjectRange,
    temperatureProbeStateCapabilities           StateCapabilitiesFlags,
    temperatureProbeStateSettings               StateSettingsFlags,
    temperatureProbeStatus                      StatusProbeEnum,
    temperatureProbeReading                     Signed32BitRange,
    temperatureProbeType                        TemperatureProbeTypeEnum,
    temperatureProbeLocationName                String64,
    temperatureProbeUpperNonRecoverableThreshold Signed32BitRange,
    temperatureProbeUpperCriticalThreshold      Signed32BitRange,
    temperatureProbeUpperNonCriticalThreshold   Signed32BitRange,
    temperatureProbeLowerNonCriticalThreshold   Signed32BitRange,
    temperatureProbeLowerCriticalThreshold      Signed32BitRange,
    temperatureProbeLowerNonRecoverableThreshold Signed32BitRange,
    temperatureProbeProbeCapabilities           ProbeCapabilitiesFlags,
    temperatureProbeDiscreteReading             TemperatureDiscreteReadingEnum
}

temperatureProbeTable                           OBJECT-TYPE
    SYNTAX      SEQUENCE OF TemperatureProbeTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0700.0020 This object defines the Temperature Probe Table."
    ::= { thermalGroup 20 }

temperatureProbeTableEntry                      OBJECT-TYPE
    SYNTAX      TemperatureProbeTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001 This object defines the Temperature Probe Table Entry."
    INDEX       { temperatureProbechassisIndex,
                  temperatureProbeIndex }
    ::= { temperatureProbeTable 1 }

temperatureProbechassisIndex                    OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { temperatureProbeTableEntry 1 }

temperatureProbeIndex                           OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0002 This attribute defines the index (one based) of the
        temperature probe."
    ::= { temperatureProbeTableEntry 2 }

temperatureProbeStateCapabilities               OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0003 This attribute defines the state capabilities of the
        temperature probe."
    ::= { temperatureProbeTableEntry 3 }

temperatureProbeStateSettings                   OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0004 This attribute defines the state settings of the
        temperature probe."
    ::= { temperatureProbeTableEntry 4 }

temperatureProbeStatus                          OBJECT-TYPE
    SYNTAX      StatusProbeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0005 This attribute defines the probe status of the
        temperature probe."
    ::= { temperatureProbeTableEntry 5 }

temperatureProbeReading                         OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0006 This attribute defines the reading for a temperature
        probe of type other than temperatureProbeTypeIsDiscrete.  When the value
        for temperatureProbeType is other than temperatureProbeTypeIsDiscrete,
        the value returned for this attribute is the temperature that the probe
        is reading in tenths of degrees Centigrade.  When the value for
        temperatureProbeType is temperatureProbeTypeIsDiscrete, a value is not
        returned for this attribute."
    ::= { temperatureProbeTableEntry 6 }

temperatureProbeType                            OBJECT-TYPE
    SYNTAX      TemperatureProbeTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0007 This attribute defines the type of the temperature probe."
    ::= { temperatureProbeTableEntry 7 }

temperatureProbeLocationName                    OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0008 This attribute defines the location name of the
        temperature probe."
    ::= { temperatureProbeTableEntry 8 }

temperatureProbeUpperNonRecoverableThreshold    OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0009 This attribute defines the upper nonrecoverable threshold
        of the temperature probe.  The value is an integer representing the temperature
        of the threshold in tenths of degrees Centigrade."
    ::= { temperatureProbeTableEntry 9 }

temperatureProbeUpperCriticalThreshold          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0010 This attribute defines the upper critical threshold
        of the temperature probe.  The value is an integer representing the temperature
        of the threshold in tenths of degrees Centigrade."
    ::= { temperatureProbeTableEntry 10 }

temperatureProbeUpperNonCriticalThreshold       OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0011 This attribute defines the upper noncritical threshold
        of the temperature probe.  The value is an integer representing the temperature
        of the threshold in tenths of degrees Centigrade."
    ::= { temperatureProbeTableEntry 11 }

temperatureProbeLowerNonCriticalThreshold       OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0012 This attribute defines the lower noncritical threshold
        of the temperature probe.  The value is an integer representing the temperature
        of the threshold in tenths of degrees Centigrade."
    ::= { temperatureProbeTableEntry 12 }

temperatureProbeLowerCriticalThreshold          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0013 This attribute defines the lower critical threshold
        of the temperature probe.  The value is an integer representing the temperature
        of the threshold in tenths of degrees Centigrade."
    ::= { temperatureProbeTableEntry 13 }

temperatureProbeLowerNonRecoverableThreshold    OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0014 This attribute defines the lower nonrecoverable threshold
        of the temperature probe.  The value is an integer representing the temperature
        of the threshold in tenths of degrees Centigrade."
    ::= { temperatureProbeTableEntry 14 }

temperatureProbeProbeCapabilities               OBJECT-TYPE
    SYNTAX      ProbeCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0015 This attribute defines the probe capabilities of the
        temperature probe."
    ::= { temperatureProbeTableEntry 15 }

temperatureProbeDiscreteReading                 OBJECT-TYPE
    SYNTAX      TemperatureDiscreteReadingEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "0700.0020.0001.0016 This attribute defines the reading for a temperature
        probe of type temperatureProbeTypeIsDiscrete.  When the value for
        temperatureProbeType is other than temperatureProbeTypeIsDiscrete, a value
        is not returned for this attribute.  When the value for temperatureProbeType
        is temperatureProbeTypeIsDiscrete, the value returned for this attribute
        is the discrete reading for the probe."
    ::= { temperatureProbeTableEntry 16 }


-------------------------------------------------------------------------------
-- Device Group
--
-- OID Format: *******.4.1.674.10892.5.4.1100
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
-- Processor Device Table
--
-- OID Format: *******.4.1.674.10892.5.4.1100.30.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

ProcessorDeviceType                             ::= INTEGER {
    deviceTypeIsOther(1),                       -- type is other than following values
    deviceTypeIsUnknown(2),                     -- type is unknown
    deviceTypeIsCPU(3),                         -- type is Central Processing Unit
    deviceTypeIsMathProcessor(4),               -- type is Math Processor
    deviceTypeIsDSP(5),                         -- type is Digital Signal Processor
    deviceTypeIsAVideoProcessor(6)              -- type is Video Processor
}

ProcessorDeviceFamily                           ::= INTEGER {
    deviceFamilyIsOther(1),                     -- family is Other
    deviceFamilyIsUnknown(2),                   -- family is Unknown
    deviceFamilyIs8086(3),                      -- family is 8086
    deviceFamilyIs80286(4),                     -- family is 80286
    deviceFamilyIsIntel386(5),                  -- family is Intel386 processor
    deviceFamilyIsIntel486(6),                  -- family is Intel486 processor
    deviceFamilyIs8087(7),                      -- family is 8087
    deviceFamilyIs80287(8),                     -- family is 80287
    deviceFamilyIs80387(9),                     -- family is 80387
    deviceFamilyIs80487(10),                    -- family is 80487
    deviceFamilyIsPentium(11),                  -- family is Pentium processor Family
    deviceFamilyIsPentiumPro(12),               -- family is Pentium Pro processor
    deviceFamilyIsPentiumII(13),                -- family is Pentium II processor
    deviceFamilyIsPentiumMMX(14),               -- family is Pentium processor with MMX technology
    deviceFamilyIsCeleron(15),                  -- family is Celeron processor
    deviceFamilyIsPentiumIIXeon(16),            -- family is Pentium II Xeon processor
    deviceFamilyIsPentiumIII(17),               -- family is Pentium III processor
    deviceFamilyIsPentiumIIIXeon(18),           -- family is Pentium III Xeon processor
    deviceFamilyIsPentiumIIISpeedStep(19),      -- family is Pentium III Processor with Intel SpeedStep Technology
    deviceFamilyIsItanium(20),                  -- family is Itanium processor
    deviceFamilyIsIntelXeon(21),                -- family is Intel Xeon
    deviceFamilyIsPentium4(22),                 -- family is Pentium 4 Processor
    deviceFamilyIsIntelXeonMP(23),              -- family is Intel Xeon processor MP
    deviceFamilyIsIntelItanium2(24),            -- family is Intel Itanium 2 processor
    deviceFamilyIsK5(25),                       -- family is K5 Family
    deviceFamilyIsK6(26),                       -- family is K6 Family
    deviceFamilyIsK6Dash2(27),                  -- family is K6-2
    deviceFamilyIsK6Dash3(28),                  -- family is K6-3
    deviceFamilyIsAMDAthlon(29),                -- family is AMD Athlon Processor Family
    deviceFamilyIsAMD2900(30),                  -- family is AMD2900 Family
    deviceFamilyIsK6Dash2Plus(31),              -- family is K6-2+
    deviceFamilyIsPowerPC(32),                  -- family is Power PC Family
    deviceFamilyIsPowerPC601(33),               -- family is Power PC 601
    deviceFamilyIsPowerPC603(34),               -- family is Power PC 603
    deviceFamilyIsPowerPC603Plus(35),           -- family is Power PC 603+
    deviceFamilyIsPowerPC604(36),               -- family is Power PC 604
    deviceFamilyIsPowerPC620(37),               -- family is Power PC 620
    deviceFamilyIsPowerPCx704(38),              -- family is Power PC x704
    deviceFamilyIsPowerPC750(39),               -- family is Power PC 750
    deviceFamilyIsIntelCoreDuo(40),             -- family is Intel(R) Core(TM) Duo processor
    deviceFamilyIsIntelCoreDuoMobile(41),       -- family is Intel(R) Core(TM) Duo mobile processor
    deviceFamilyIsIntelCoreSoloMobile(42),      -- family is Intel(R) Core(TM) Solo mobile processor
    deviceFamilyIsIntelAtom(43),                -- family is Intel(R) Atom(TM) processor
    deviceFamilyIsAlpha(48),                    -- family is Alpha Family
    deviceFamilyIsAlpha21064(49),               -- family is Alpha 21064
    deviceFamilyIsAlpha21066(50),               -- family is Alpha 21066
    deviceFamilyIsAlpha21164(51),               -- family is Alpha 21164
    deviceFamilyIsAlpha21164PC(52),             -- family is Alpha 21164PC
    deviceFamilyIsAlpha21164a(53),              -- family is Alpha 21164a
    deviceFamilyIsAlpha21264(54),               -- family is Alpha 21264
    deviceFamilyIsAlpha21364(55),               -- family is Alpha 21364
    deviceFamilyIsAMDTurionIIUltraDualMobileM(56), -- family is AMD Turion(TM) II Ultra Dual-Core Mobile M Processor Family
    deviceFamilyIsAMDTurionIIDualMobileM(57),   -- family is AMD Turion(TM) II Dual-Core Mobile M Processor Family
    deviceFamilyIsAMDAthlonIIDualMobileM(58),   -- family is AMD Athlon(TM) II Dual-Core Mobile M Processor Family
    deviceFamilyIsAMDOpteron6100(59),           -- family is AMD Opteron(TM) 6100 Series Processor
    deviceFamilyIsAMDOpteron4100(60),           -- family is AMD Opteron(TM) 4100 Series Processor
    deviceFamilyIsAMDOpteron6200(61),           -- family is AMD Opteron(TM) 6200 Series Processor
    deviceFamilyIsAMDOpteron4200(62),           -- family is AMD Opteron(TM) 4200 Series Processor
    deviceFamilyIsMIPS(64),                     -- family is MIPS Family
    deviceFamilyIsMIPSR4000(65),                -- family is MIPS R4000
    deviceFamilyIsMIPSR4200(66),                -- family is MIPS R4200
    deviceFamilyIsMIPSR4400(67),                -- family is MIPS R4400
    deviceFamilyIsMIPSR4600(68),                -- family is MIPS R4600
    deviceFamilyIsMIPSR10000(69),               -- family is MIPS R10000
    deviceFamilyIsSPARC(80),                    -- family is SPARC Family
    deviceFamilyIsSuperSPARC(81),               -- family is SuperSPARC
    deviceFamilyIsmicroSPARCII(82),             -- family is microSPARC II
    deviceFamilyIsmicroSPARCIIep(83),           -- family is microSPARC IIep
    deviceFamilyIsUltraSPARC(84),               -- family is UltraSPARC
    deviceFamilyIsUltraSPARCII(85),             -- family is UltraSPARC II
    deviceFamilyIsUltraSPARCIIi(86),            -- family is UltraSPARC IIi
    deviceFamilyIsUltraSPARCIII(87),            -- family is UltraSPARC III
    deviceFamilyIsUltraSPARCIIIi(88),           -- family is UltraSPARC IIIi
    deviceFamilyIs68040(96),                    -- family is 68040 Family
    deviceFamilyIs68xxx(97),                    -- family is 68xxx
    deviceFamilyIs68000(98),                    -- family is 68000
    deviceFamilyIs68010(99),                    -- family is 68010
    deviceFamilyIs68020(100),                   -- family is 68020
    deviceFamilyIs68030(101),                   -- family is 68030
    deviceFamilyIsHobbit(112),                  -- family is Hobbit Family
    deviceFamilyIsCrusoeTM5000(120),            -- family is Crusoe TM5000 Family
    deviceFamilyIsCrusoeTM3000(121),            -- family is Crusoe TM3000 Family
    deviceFamilyIsEfficeonTM8000(122),          -- family is Efficeon TM8000 Family
    deviceFamilyIsWeitek(128),                  -- family is Weitek
    deviceFamilyIsIntelCeleronM(130),           -- family is Intel(R) Celeron(R) M processor
    deviceFamilyIsAMDAthlon64(131),             -- family is AMD Athlon 64 Processor Family
    deviceFamilyIsAMDOpteron(132),              -- family is AMD Opteron Processor Family
    deviceFamilyIsAMDSempron(133),              -- family is AMD Sempron Processor Family
    deviceFamilyIsAMDTurion64Mobile(134),       -- family is AMD Turion 64 Mobile Technology
    deviceFamilyIsDualCoreAMDOpteron(135),      -- family is Dual-Core AMD Opteron(TM) Processor Family
    deviceFamilyIsAMDAthlon64X2DualCore(136),   -- family is AMD Athlon 64 X2 Dual-Core Processor Family
    deviceFamilyIsAMDTurion64X2Mobile(137),     -- family is AMD Turion(TM) 64 X2 Mobile Technology
    deviceFamilyIsQuadCoreAMDOpteron(138),      -- family is Quad-Core AMD Opteron(TM) Processor Family
    deviceFamilyIsThirdGenerationAMDOpteron(139), -- family is Third-Generation AMD Opteron(TM) Processor Family
    deviceFamilyIsAMDPhenomFXQuadCore(140),     -- family is AMD Phenom(TM) FX Quad-Core Processor Family
    deviceFamilyIsAMDPhenomX4QuadCore(141),     -- family is AMD Phenom(TM) X4 Quad-Core Processor Family
    deviceFamilyIsAMDPhenomX2DualCore(142),     -- family is AMD Phenom(TM) X2 Dual-Core Processor Family
    deviceFamilyIsAMDAthlonX2DualCore(143),     -- family is AMD Athlon(TM) X2 Dual-Core Processor Family
    deviceFamilyIsPARISC(144),                  -- family is PA-RISC Family
    deviceFamilyIsPARISC8500(145),              -- family is PA-RISC 8500
    deviceFamilyIsPARISC8000(146),              -- family is PA-RISC 8000
    deviceFamilyIsPARISC7300LC(147),            -- family is PA-RISC 7300LC
    deviceFamilyIsPARISC7200(148),              -- family is PA-RISC 7200
    deviceFamilyIsPARISC7100LC(149),            -- family is PA-RISC 7100LC
    deviceFamilyIsPARISC7100(150),              -- family is PA-RISC 7100
    deviceFamilyIsV30(160),                     -- family is V30 Family
    deviceFamilyIsQuadCoreIntelXeon3200(161),   -- family is Quad-Core Intel(R) Xeon(R) processor 3200 Series
    deviceFamilyIsDualCoreIntelXeon3000(162),   -- family is Dual-Core Intel(R) Xeon(R) processor 3000 Series
    deviceFamilyIsQuadCoreIntelXeon5300(163),   -- family is Quad-Core Intel(R) Xeon(R) processor 5300 Series
    deviceFamilyIsDualCoreIntelXeon5100(164),   -- family is Dual-Core Intel(R) Xeon(R) processor 5100 Series
    deviceFamilyIsDualCoreIntelXeon5000(165),   -- family is Dual-Core Intel(R) Xeon(R) processor 5000 Series
    deviceFamilyIsDualCoreIntelXeonLV(166),     -- family is Dual-Core Intel(R) Xeon(R) processor LV
    deviceFamilyIsDualCoreIntelXeonULV(167),    -- family is Dual-Core Intel(R) Xeon(R) processor ULV
    deviceFamilyIsDualCoreIntelXeon7100(168),   -- family is Dual-Core Intel(R) Xeon(R) processor 7100 Series
    deviceFamilyIsQuadCoreIntelXeon5400(169),   -- family is Quad-Core Intel(R) Xeon(R) processor 5400 Series
    deviceFamilyIsQuadCoreIntelXeon(170),       -- family is Quad-Core Intel(R) Xeon(R) processor
    deviceFamilyIsDualCoreIntelXeon5200(171),   -- family is Dual-Core Intel(R) Xeon(R) processor 5200 Series
    deviceFamilyIsDualCoreIntelXeon7200(172),   -- family is Dual-Core Intel(R) Xeon(R) processor 7200 Series
    deviceFamilyIsQuadCoreIntelXeon7300(173),   -- family is Quad-Core Intel(R) Xeon(R) processor 7300 Series
    deviceFamilyIsQuadCoreIntelXeon7400(174),   -- family is Quad-Core Intel(R) Xeon(R) processor 7400 Series
    deviceFamilyIsMultiCoreIntelXeon7400(175),  -- family is Multi-Core Intel(R) Xeon(R) processor 7400 Series
    deviceFamilyIsM1(176),                      -- family is M1 Family
    deviceFamilyIsM2(177),                      -- family is M2 Family
    deviceFamilyIsIntelPentium4HT(179),         -- family is Intel(R) Pentium(R) 4 HT processor
    deviceFamilyIsAS400(180),                   -- family is AS400 Family
    deviceFamilyIsAMDAthlonXP(182),             -- family is AMD Athlon XP Processor Family
    deviceFamilyIsAMDAthlonMP(183),             -- family is AMD Athlon MP Processor Family
    deviceFamilyIsAMDDuron(184),                -- family is AMD Duron Processor Family
    deviceFamilyIsIntelPentiumM(185),           -- family is Intel Pentium M processor
    deviceFamilyIsIntelCeleronD(186),           -- family is Intel Celeron D processor
    deviceFamilyIsIntelPentiumD(187),           -- family is Intel Pentium D processor
    deviceFamilyIsIntelPentiumExtreme(188),     -- family is Intel Pentium Processor Extreme Edition
    deviceFamilyIsIntelCoreSolo(189),           -- family is Intel(R) Core(TM) Solo processor
    deviceFamilyIsIntelCore2(190),              -- family is Intel(R) Core(TM)2 processor
    deviceFamilyIsIntelCore2Duo(191),           -- family is Intel(R) Core(TM)2 Duo processor
    deviceFamilyIsIntelCore2Solo(192),          -- family is Intel(R) Core(TM)2 Solo processor
    deviceFamilyIsIntelCore2Extreme(193),       -- family is Intel(R) Core(TM)2 Extreme processor
    deviceFamilyIsIntelCore2Quad(194),          -- family is Intel(R) Core(TM)2 Quad processor
    deviceFamilyIsIntelCore2ExtremeMobile(195), -- family is Intel(R) Core(TM)2 Extreme mobile processor
    deviceFamilyIsIntelCore2DuoMobile(196),     -- family is Intel(R) Core(TM)2 Duo mobile processor
    deviceFamilyIsIntelCore2SoloMobile(197),    -- family is Intel(R) Core(TM)2 Solo mobile processor
    deviceFamilyIsIntelCorei7(198),             -- family is Intel(R) Core(TM) i7 processor
    deviceFamilyIsDualCoreIntelCeleron(199),    -- family is Dual-Core Intel(R) Celeron(R) Processor
    deviceFamilyIsIBM390(200),                  -- family is IBM390 Family
    deviceFamilyIsG4(201),                      -- family is G4
    deviceFamilyIsG5(202),                      -- family is G5
    deviceFamilyIsESA390G6(203),                -- family is ESA/390 G6
    deviceFamilyIszArchitectur(204),            -- family is z/Architectur base
    deviceFamilyIsIntelCorei5(205),             -- family is Intel(R) Core(TM) i5 processor
    deviceFamilyIsIntelCorei3(206),             -- family is Intel(R) Core(TM) i3 processor
    deviceFamilyIsVIAC7M(210),                  -- family is VIA C7(TM)-M Processor Family
    deviceFamilyIsVIAC7D(211),                  -- family is VIA C7(TM)-D Processor Family
    deviceFamilyIsVIAC7(212),                   -- family is VIA C7(TM) Processor Family
    deviceFamilyIsVIAEden(213),                 -- family is VIA Eden(TM) Processor Family
    deviceFamilyIsMultiCoreIntelXeon(214),      -- family is Multi-Core Intel(R) Xeon(R) processor
    deviceFamilyIsDualCoreIntelXeon3xxx(215),   -- family is Dual-Core Intel(R) Xeon(R) processor 3xxx Series
    deviceFamilyIsQuadCoreIntelXeon3xxx(216),   -- family is Quad-Core Intel(R) Xeon(R) processor 3xxx Series
    deviceFamilyIsVIANano(217),                 -- family is VIA Nano(TM) Processor Family
    deviceFamilyIsDualCoreIntelXeon5xxx(218),   -- family is Dual-Core Intel(R) Xeon(R) processor 5xxx Series
    deviceFamilyIsQuadCoreIntelXeon5xxx(219),   -- family is Quad-Core Intel(R) Xeon(R)  processor 5xxx Series
    deviceFamilyIsDualCoreIntelXeon7xxx(221),   -- family is Dual-Core Intel(R) Xeon(R) processor 7xxx Series
    deviceFamilyIsQuadCoreIntelXeon7xxx(222),   -- family is Quad-Core Intel(R) Xeon(R) processor 7xxx Series
    deviceFamilyIsMultiCoreIntelXeon7xxx(223),  -- family is Multi-Core Intel(R) Xeon(R) processor 7xxx Series
    deviceFamilyIsMultiCoreIntelXeon3400(224),  -- family is Multi-Core Intel(R) Xeon(R) processor 3400 Series 
    deviceFamilyIsEmbeddedAMDOpertonQuadCore(230), -- family is Embedded AMD Opteron(TM) Quad-Core Processor Family
    deviceFamilyIsAMDPhenomTripleCore(231),     -- family is AMD Phenom(TM) Triple-Core Processor Family
    deviceFamilyIsAMDTurionUltraDualCoreMobile(232), -- family is AMD Turion(TM) Ultra Dual-Core Mobile Processor Family
    deviceFamilyIsAMDTurionDualCoreMobile(233), -- family is AMD Turion(TM) Dual-Core Mobile Processor Family
    deviceFamilyIsAMDAthlonDualCore(234),       -- family is AMD Athlon(TM) Dual-Core Processor Family
    deviceFamilyIsAMDSempronSI(235),            -- family is AMD Sempron(TM) SI Processor Family
    deviceFamilyIsAMDPhenomII(236),             -- family is AMD Phenom(TM) II Processor Family
    deviceFamilyIsAMDAthlonII(237),             -- family is AMD Athlon(TM) II Processor Family
    deviceFamilyIsSixCoreAMDOpteron(238),       -- family is Six-Core AMD Opteron(TM) Processor Family
    deviceFamilyIsAMDSempronM(239),             -- family is AMD Sempron(TM) M Processor Family
    deviceFamilyIsi860(250),                    -- family is i860
    deviceFamilyIsi960(251)                     -- family is i960
}

ProcessorDeviceStatusState                  ::= INTEGER {
    other(1),                                   -- state is other than following values
    unknown(2),                                 -- state is unknown
    enabled(3),                                 -- state is enabled
    userDisabled(4),                            -- state is disabled by user via BIOS setup
    biosDisabled(5),                            -- state is disabled by BIOS (POST error)
    idle(6)                                     -- state is idle
}

ProcessorDeviceTableEntry                       ::= SEQUENCE {
    processorDevicechassisIndex                 ObjectRange,
    processorDeviceIndex                        ObjectRange,
    processorDeviceStateCapabilities            StateCapabilitiesFlags,
    processorDeviceStateSettings                StateSettingsFlags,
    processorDeviceStatus                       ObjectStatusEnum,
    processorDeviceType                         ProcessorDeviceType,
    processorDeviceManufacturerName             String64,
    processorDeviceStatusState                  ProcessorDeviceStatusState,
    processorDeviceFamily                       ProcessorDeviceFamily,
    processorDeviceMaximumSpeed                 Unsigned32BitRange,
    processorDeviceCurrentSpeed                 Unsigned32BitRange,
    processorDeviceExternalClockSpeed           Unsigned32BitRange,
    processorDeviceVoltage                      Signed32BitRange,
    processorDeviceVersionName                  String64,
    processorDeviceCoreCount                    Unsigned32BitRange,
    processorDeviceCoreEnabledCount             Unsigned32BitRange,
    processorDeviceThreadCount                  Unsigned32BitRange,
    processorDeviceCharacteristics              Unsigned16BitRange,
    processorDeviceExtendedCapabilities         Unsigned16BitRange,
    processorDeviceExtendedSettings             Unsigned16BitRange,
    processorDeviceBrandName                    String64,
    processorDeviceFQDD                         FQDDString
}

processorDeviceTable                            OBJECT-TYPE
    SYNTAX      SEQUENCE OF ProcessorDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0030 This object defines the Processor Device Table."
    ::= { deviceGroup 30 }

processorDeviceTableEntry                       OBJECT-TYPE
    SYNTAX      ProcessorDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001 This object defines the Processor Device Table Entry."
    INDEX       { processorDevicechassisIndex,
                  processorDeviceIndex }
    ::= { processorDeviceTable 1 }

processorDevicechassisIndex                     OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { processorDeviceTableEntry 1 }

processorDeviceIndex                            OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0002 This attribute defines the index (one based) of the
        processor device."
    ::= { processorDeviceTableEntry 2 }

processorDeviceStateCapabilities                OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0003 This attribute defines the state capabilities of the
        processor device."
    ::= { processorDeviceTableEntry 3 }

processorDeviceStateSettings                    OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0004 This attribute defines the state settings of the
        processor device."
    ::= { processorDeviceTableEntry 4 }

processorDeviceStatus                           OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0005 This attribute defines the status of the
        processor device."
    ::= { processorDeviceTableEntry 5 }

processorDeviceType                             OBJECT-TYPE
    SYNTAX      ProcessorDeviceType
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0007 This attribute defines the type of the processor device."
    ::= { processorDeviceTableEntry 7 }

processorDeviceManufacturerName                 OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0008 This attribute defines the name of the manufacturer
        of the processor device."
    ::= { processorDeviceTableEntry 8 }

processorDeviceStatusState                      OBJECT-TYPE
    SYNTAX      ProcessorDeviceStatusState
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0009 This attribute defines the status state of the
        processor device."
    ::= { processorDeviceTableEntry 9 }

processorDeviceFamily                           OBJECT-TYPE
    SYNTAX      ProcessorDeviceFamily
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0010 This attribute defines the family of the
        processor device."
    ::= { processorDeviceTableEntry 10 }

processorDeviceMaximumSpeed                     OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0011 This attribute defines the maximum speed of the
        processor device in MHz.  Zero indicates the maximum speed is unknown."
    ::= { processorDeviceTableEntry 11 }

processorDeviceCurrentSpeed                     OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0012 This attribute defines the current speed of the
        processor device in MHz.  Zero indicates the current speed is unknown."
    ::= { processorDeviceTableEntry 12 }

processorDeviceExternalClockSpeed               OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0013 This attribute defines the speed of the
        external clock for the processor device in MHz.  Zero indicates
        the external clock speed is unknown."
    ::= { processorDeviceTableEntry 13 }

processorDeviceVoltage                          OBJECT-TYPE
    SYNTAX      Signed32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0014 This attribute defines the voltage powering the
        processor device in millivolts.  Zero indicates the voltage is unknown."
    ::= { processorDeviceTableEntry 14 }

processorDeviceVersionName                      OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0016 This attribute defines the version of the
        processor device.  On some systems, this value contains the
        brand and stepping information; on other systems, this value
        contains the model and stepping information."
    ::= { processorDeviceTableEntry 16 }

processorDeviceCoreCount                        OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0017 This attribute defines the number of processor cores
        detected for the processor device."
    ::= { processorDeviceTableEntry 17 }

processorDeviceCoreEnabledCount                 OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0018 This attribute defines the number of processor cores
        enabled for the processor device."
    ::= { processorDeviceTableEntry 18 }

processorDeviceThreadCount                      OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0019 This attribute defines the number of processor threads
        detected for the processor device."
    ::= { processorDeviceTableEntry 19 }

processorDeviceCharacteristics                  OBJECT-TYPE
    SYNTAX      Unsigned16BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0020 This attribute defines characteristics of the
        processor device.  This attribute is a bit field where a bit has the meaning
        defined below when set to 1 (one).

        NOTE: Bits 2-15 need to be examined in the context of bit 1.
        If bit 1 is set, the processor charactistics are unknown and bits 2-15 cannot
        be used to determine if the functions associated with the bits are supported.

            Bit
            Position    Meaning if Set
            --------    --------------
            Bit 0       Reserved
            Bit 1       Unknown
            Bit 2       64-bit capable
            Bit 3-15    Reserved"
    ::= { processorDeviceTableEntry 20 }

processorDeviceExtendedCapabilities             OBJECT-TYPE
    SYNTAX      Unsigned16BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0021 This attribute defines extended capabilities of the
        processor device.  This attribute is a bit field where a bit has the meaning
        defined below when set to 1 (one).

            Bit
            Position    Meaning if Set
            --------    --------------
            Bit 0       Virtualization Technology (VT) supported
            Bit 2       eXecute Disable (XD) supported
            Bit 3       Hyper-Threading (HT) supported
            Bit 4       Turbo Mode supported"
    ::= { processorDeviceTableEntry 21 }

processorDeviceExtendedSettings                 OBJECT-TYPE
    SYNTAX      Unsigned16BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0022 This attribute defines extended settings of the
        processor device.  This attribute is a bit field where a bit has the meaning
        defined below when set to 1 (one).

            Bit
            Position    Meaning if Set
            --------    --------------
            Bit 0       Virtualization Technology (VT) enabled
            Bit 2       eXecute Disable (XD) enabled
            Bit 3       Hyper-Threading (HT) enabled
            Bit 4       Turbo Mode enabled"
    ::= { processorDeviceTableEntry 22 }

processorDeviceBrandName                        OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0023 This attribute defines the brand of the
        processor device."
    ::= { processorDeviceTableEntry 23 }

processorDeviceFQDD                             OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0030.0001.0026 Fully qualified device descriptor (FQDD) of the
        processor device."
    ::= { processorDeviceTableEntry 26 }


-------------------------------------------------------------------------------
-- Processor Device Status Table
--
-- OID Format: *******.4.1.674.10892.5.4.1100.32.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

ProcessorDeviceStatusReadingFlags                ::= INTEGER {
    -- Note: These values are bit masks, so combination values are possible.
    internalError(1),                           -- Internal Error
    thermalTrip(2),                             -- Thermal Trip
    configurationError(32),                     -- Configuration Error
    processorPresent(128),                      -- Processor Present
    processorDisabled(256),                     -- Processor Disabled
    terminatorPresent(512),                     -- Terminator Present
    processorThrottled(1024)                    -- Processor Throttled
}

ProcessorDeviceStatusTableEntry                 ::= SEQUENCE {
    processorDeviceStatusChassisIndex           ObjectRange,
    processorDeviceStatusIndex                  ObjectRange,
    processorDeviceStatusStateCapabilities      StateCapabilitiesFlags,
    processorDeviceStatusStateSettings          StateSettingsFlags,
    processorDeviceStatusStatus                 ObjectStatusEnum,
    processorDeviceStatusReading                ProcessorDeviceStatusReadingFlags,
    processorDeviceStatusLocationName           String64
}

processorDeviceStatusTable                      OBJECT-TYPE
    SYNTAX      SEQUENCE OF ProcessorDeviceStatusTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0032 This object defines the Processor Device Status Table."
    ::= { deviceGroup 32 }

processorDeviceStatusTableEntry                 OBJECT-TYPE
    SYNTAX      ProcessorDeviceStatusTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001 This object defines the Processor Device Status Table Entry."
    INDEX       { processorDeviceStatusChassisIndex,
                  processorDeviceStatusIndex }
    ::= { processorDeviceStatusTable 1 }

processorDeviceStatusChassisIndex               OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { processorDeviceStatusTableEntry 1 }

processorDeviceStatusIndex                      OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001.0002 This attribute defines the index (one based) of the
        processor device status probe."
    ::= { processorDeviceStatusTableEntry 2 }

processorDeviceStatusStateCapabilities          OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001.0003 This attribute defines the state capabilities of the
        processor device status probe."
    ::= { processorDeviceStatusTableEntry 3 }

processorDeviceStatusStateSettings              OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001.0004 This attribute defines the state settings of the
        processor device status probe."
    ::= { processorDeviceStatusTableEntry 4 }

processorDeviceStatusStatus                     OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001.0005 This attribute defines the status of the
        processor device status probe.  This status will be joined into
        the processorDeviceStatus attribute."
    ::= { processorDeviceStatusTableEntry 5 }

processorDeviceStatusReading                    OBJECT-TYPE
    SYNTAX      ProcessorDeviceStatusReadingFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001.0006 This attribute defines the reading of the
        processor device status probe."
    ::= { processorDeviceStatusTableEntry 6 }

processorDeviceStatusLocationName               OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0032.0001.0007 This attribute defines the location name of the
        processor device status probe."
    ::= { processorDeviceStatusTableEntry 7 }


-------------------------------------------------------------------------------
-- Memory Device Table
--
-- OID Format: *******.4.1.674.10892.5.4.1100.50.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

MemoryDeviceTypeEnum                            ::= INTEGER {
    deviceTypeIsOther(1),                       -- type is other than following values
    deviceTypeIsUnknown(2),                     -- type is unknown
    deviceTypeIsDRAM(3),                        -- type is DRAM
    deviceTypeIsEDRAM(4),                       -- type is EDRAM
    deviceTypeIsVRAM(5),                        -- type is VRAM
    deviceTypeIsSRAM(6),                        -- type is SRAM
    deviceTypeIsRAM(7),                         -- type is RAM
    deviceTypeIsROM(8),                         -- type is ROM
    deviceTypeIsFLASH(9),                       -- type is FLASH
    deviceTypeIsEEPROM(10),                     -- type is EEPROM
    deviceTypeIsFEPROM(11),                     -- type is FEPROM
    deviceTypeIsEPROM(12),                      -- type is EPROM
    deviceTypeIsCDRAM(13),                      -- type is CDRAM
    deviceTypeIs3DRAM(14),                      -- type is 3DRAM
    deviceTypeIsSDRAM(15),                      -- type is SDRAM
    deviceTypeIsSGRAM(16),                      -- type is SGRAM
    deviceTypeIsRDRAM(17),                      -- type is RDRAM
    deviceTypeIsDDR(18),                        -- type is DDR
    deviceTypeIsDDR2(19),                       -- type is DDR2
    deviceTypeIsDDR2FBDIMM(20),                 -- type is DDR2 FB-DIMM
    deviceTypeIsDDR3(24),                       -- type is DDR3
    deviceTypeIsFBD2(25),                       -- type is FBD2
    deviceTypeIsDDR4(26)                        -- type is DDR4
}

MemoryDeviceTableEntry                          ::= SEQUENCE {
    memoryDevicechassisIndex                    ObjectRange,
    memoryDeviceIndex                           ObjectRange,
    memoryDeviceStateCapabilities               StateCapabilitiesFlags,
    memoryDeviceStateSettings                   StateSettingsFlags,
    memoryDeviceStatus                          ObjectStatusEnum,
    memoryDeviceType                            MemoryDeviceTypeEnum,
    memoryDeviceLocationName                    String64,
    memoryDeviceBankLocationName                String64,
    memoryDeviceSize                            Unsigned32BitRange,
    memoryDeviceSpeed                           Unsigned32BitRange,
    memoryDeviceManufacturerName                String64,
    memoryDevicePartNumberName                  String64,
    memoryDeviceSerialNumberName                String64,
    memoryDeviceFQDD                            FQDDString,
    memoryDeviceCurrentOperatingSpeed           Unsigned32BitRange
}

memoryDeviceTable                               OBJECT-TYPE
    SYNTAX      SEQUENCE OF MemoryDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0050 This object defines the Memory Device Table."
    ::= { deviceGroup 50 }

memoryDeviceTableEntry                          OBJECT-TYPE
    SYNTAX      MemoryDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001 This object defines the Memory Device Table Entry."
    INDEX       { memoryDevicechassisIndex,
                  memoryDeviceIndex }
    ::= { memoryDeviceTable 1 }

memoryDevicechassisIndex                        OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { memoryDeviceTableEntry 1 }

memoryDeviceIndex                               OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0002 This attribute defines the index (one based) of the
        memory device."
    ::= { memoryDeviceTableEntry 2 }

memoryDeviceStateCapabilities                   OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0003 This attribute defines the state capabilities of the
        memory device."
    ::= { memoryDeviceTableEntry 3 }

memoryDeviceStateSettings                       OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0004 This attribute defines the state settings of the
        memory device."
    ::= { memoryDeviceTableEntry 4 }

memoryDeviceStatus                              OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0005 This attribute defines the status of the memory device."
    ::= { memoryDeviceTableEntry 5 }

memoryDeviceType                                OBJECT-TYPE
    SYNTAX      MemoryDeviceTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0007 This attribute defines the type of the memory device."
    ::= { memoryDeviceTableEntry 7 }

memoryDeviceLocationName                        OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0008 This attribute defines the location of the memory device."
    ::= { memoryDeviceTableEntry 8 }

memoryDeviceBankLocationName                    OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0010 This attribute defines the location of the bank for the
        memory device."
    ::= { memoryDeviceTableEntry 10 }

memoryDeviceSize                                OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0014 This attribute defines the size in KBytes of the
        memory device.  Zero indicates no memory installed; 2,147,483,647 indicates
        an unknown memory size."
    ::= { memoryDeviceTableEntry 14 }

memoryDeviceSpeed                               OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0015 This attribute defines the maximum capable speed
        in megahertz (MHz) of the memory device.  Zero indicates an unknown speed."
    ::= { memoryDeviceTableEntry 15 }

memoryDeviceManufacturerName                    OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0021 This attribute defines the manufacturer of the
        memory device."
    ::= { memoryDeviceTableEntry 21 }

memoryDevicePartNumberName                      OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0022 This attribute defines the manufacturer's part number
        for the memory device."
    ::= { memoryDeviceTableEntry 22 }

memoryDeviceSerialNumberName                    OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0023 This attribute defines the serial number of the
        memory device."
    ::= { memoryDeviceTableEntry 23 }

memoryDeviceFQDD                                OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0026 Fully qualified device descriptor (FQDD) of the
        memory device."
    ::= { memoryDeviceTableEntry 26 }

memoryDeviceCurrentOperatingSpeed               OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0050.0001.0027 This attribute defines the current operating speed
        in megahertz (MHz) of the memory device.  Zero indicates an unknown speed."
    ::= { memoryDeviceTableEntry 27 }


-------------------------------------------------------------------------------
-- PCI Device Table
--
-- OID Format: *******.4.1.674.10892.5.4.1100.80.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

PCIDeviceTableEntry                             ::= SEQUENCE {
    pCIDevicechassisIndex                       ObjectRange,
    pCIDeviceIndex                              ObjectRange,
    pCIDeviceStateCapabilities                  StateCapabilitiesFlags,
    pCIDeviceStateSettings                      StateSettingsFlags,
    pCIDeviceStatus                             ObjectStatusEnum,
    pCIDeviceDataBusWidth                       Unsigned32BitRange,
    pCIDeviceManufacturerName                   String64,
    pCIDeviceDescriptionName                    String64,
    pCIDeviceFQDD                               FQDDString
}

pCIDeviceTable                                  OBJECT-TYPE
    SYNTAX      SEQUENCE OF PCIDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0080 This object defines the PCI Device Table."
    ::= { deviceGroup 80 }

pCIDeviceTableEntry                             OBJECT-TYPE
    SYNTAX      PCIDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001 This object defines the PCI Device Table Entry."
    INDEX       { pCIDevicechassisIndex,
                  pCIDeviceIndex }
    ::= { pCIDeviceTable 1 }

pCIDevicechassisIndex                           OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { pCIDeviceTableEntry 1 }

pCIDeviceIndex                                  OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0002 This attribute defines the index (one based) of the
        PCI device."
    ::= { pCIDeviceTableEntry 2 }

pCIDeviceStateCapabilities                      OBJECT-TYPE
    SYNTAX      StateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0003 This attribute defines the state capabilities of the
        PCI device."
    ::= { pCIDeviceTableEntry 3 }

pCIDeviceStateSettings                          OBJECT-TYPE
    SYNTAX      StateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0004 This attribute defines the state settings of the
        PCI device."
    ::= { pCIDeviceTableEntry 4 }

pCIDeviceStatus                                 OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0005 This attribute defines the status of the PCI device."
    ::= { pCIDeviceTableEntry 5 }

pCIDeviceDataBusWidth                           OBJECT-TYPE
    SYNTAX      Unsigned32BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0007 This attribute defines the width of the data bus
        of the PCI device. This attribute contains an enumeration value.
        The possible values and their meanings are defined below.

            Value       Meaning
            ----------  --------------
            0x00000001  Other
            0x00000002  Unknown
            0x00000003  8 bit
            0x00000004  16 bit
            0x00000005  32 bit
            0x00000006  64 bit
            0x00000007  128 bit
            0x00000008  1x or x1
            0x00000009  2x or x2
            0x0000000A  4x or x4
            0x0000000B  8x or x8
            0x0000000C  12x or x12
            0x0000000D  16x or x16
            0x0000000E  32x or x32"
    ::= { pCIDeviceTableEntry 7 }

pCIDeviceManufacturerName                       OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0008 This attribute defines the name of the manufacturer
        of the PCI device."
    ::= { pCIDeviceTableEntry 8 }

pCIDeviceDescriptionName                        OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0009 This attribute defines the description of the PCI device."
    ::= { pCIDeviceTableEntry 9 }

pCIDeviceFQDD                                   OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0080.0001.0012 Fully qualified device descriptor (FQDD) of the
        PCI device."
    ::= { pCIDeviceTableEntry 12 }


-------------------------------------------------------------------------------
-- Network Device Table
--
-- OID Format: *******.4.1.674.10892.5.4.1100.90.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

NetworkDeviceConnectionStatusEnum               ::= INTEGER {
    -- unknown(0),                              -  unable to determine connection status
    connected(1),                               -- media reports connected
    disconnected(2),                            -- media reports disconnected
    driverBad(3),                               -- driver cannot be opened to determine status
    driverDisabled(4),                          -- driver is disabled
    hardwareInitalizing(10),                    -- hardware is initializing
    hardwareResetting(11),                      -- hardware is resetting
    hardwareClosing(12),                        -- hardware is closing down
    hardwareNotReady(13)                        -- hardware is not ready
}

NetworkDeviceTOECapabilityFlags                 ::= INTEGER {
    -- Note: These values are bit fields, so combination values are possible.
    -- none(0),                                 -  querying for TOE capability is not supported
    unknown(1),                                 -- querying for TOE capability is supported but query returned an error
    available(2),                               -- device has TOE capability
    notAvailable(4),                            -- device does not have TOE capability
    cannotBeDetermined(8),                      -- querying for TOE capability is supported but an error prevented querying
    driverNotResponding(16)                     -- querying for TOE capability is supported but driver did not respond to query
}

NetworkDeviceiSCSICapabilityFlags               ::= INTEGER {
    -- Note: These values are bit fields, so combination values are possible.
    -- none(0),                                 -  querying for iSCSI capability is not supported
    unknown(1),                                 -- querying for iSCSI capability is supported but query returned an error
    available(2),                               -- device has iSCSI capability
    notAvailable(4),                            -- device does not have iSCSI capability
    cannotBeDetermined(8),                      -- querying for iSCSI capability is supported but an error prevented querying
    driverNotResponding(16)                     -- querying for iSCSI capability is supported but driver did not respond to query
}

NetworkDeviceCapabilitiesFlags                  ::= INTEGER {
    -- Note: These values are bit fields, so combination values are possible.
    -- notSupported(0),                         -  device does not support reporting capabilities via this attribute
    supported(1),                               -- device supports reporting capabilities via this attribute
    toe(2),                                     -- device has TOE capability
    iscsiOffload(4),                            -- device has iSCSI Offload capability
    fcoeOffload(8)                              -- device has FCoE Offload capability
}

NetworkDeviceTableEntry                         ::= SEQUENCE {
    networkDeviceChassisIndex                   ObjectRange,
    networkDeviceIndex                          ObjectRange,
    networkDeviceStatus                         ObjectStatusEnum,
    networkDeviceConnectionStatus               NetworkDeviceConnectionStatusEnum,
    networkDeviceProductName                    String64,
    networkDeviceVendorName                     String64,
    networkDeviceCurrentMACAddress              MACAddress,
    networkDevicePermanentMACAddress            MACAddress,
    networkDevicePCIBusNumber                   Unsigned8BitRange,
    networkDevicePCIDeviceNumber                Unsigned8BitRange,
    networkDevicePCIFunctionNumber              Unsigned8BitRange,
    networkDeviceTOECapabilityFlags             NetworkDeviceTOECapabilityFlags,
    networkDeviceiSCSICapabilityFlags           NetworkDeviceiSCSICapabilityFlags,
    networkDeviceiSCSIEnabled                   BooleanType,
    networkDeviceCapabilities                   NetworkDeviceCapabilitiesFlags,
    networkDeviceFQDD                           FQDDString
}

networkDeviceTable                              OBJECT-TYPE
    SYNTAX      SEQUENCE OF NetworkDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0090 This object defines the Network Device Table."
    ::= { deviceGroup 90 }

networkDeviceTableEntry                         OBJECT-TYPE
    SYNTAX      NetworkDeviceTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001 This object defines the Network Device Table Entry."
    INDEX       { networkDeviceChassisIndex,
                  networkDeviceIndex }
    ::= { networkDeviceTable 1 }

networkDeviceChassisIndex                       OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0001 This attribute defines the index (one based) of the
        system chassis that contains the network device."
    ::= { networkDeviceTableEntry 1 }

networkDeviceIndex                              OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0002 This attribute defines the index (one based) of the
        network device."
    ::= { networkDeviceTableEntry 2 }

networkDeviceStatus                             OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0003 This attribute defines the status of the network device."
    ::= { networkDeviceTableEntry 3 }

networkDeviceConnectionStatus                   OBJECT-TYPE
    SYNTAX      NetworkDeviceConnectionStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0004 This attribute defines the connection status of the
        network device."
    ::= { networkDeviceTableEntry 4 }

networkDeviceProductName                        OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0006 This attribute defines the product name of the
        network device."
    ::= { networkDeviceTableEntry 6 }

networkDeviceVendorName                         OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0007 This attribute defines the name of the vendor of the
        network device."
    ::= { networkDeviceTableEntry 7 }

networkDeviceCurrentMACAddress                  OBJECT-TYPE
    SYNTAX      MACAddress
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0015 This attribute defines the current MAC address of the
        network device."
    ::= { networkDeviceTableEntry 15 }

networkDevicePermanentMACAddress                OBJECT-TYPE
    SYNTAX      MACAddress
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0016 This attribute defines the permanent MAC address of the
        network device."
    ::= { networkDeviceTableEntry 16 }

networkDevicePCIBusNumber                       OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0017 This attribute defines the PCI bus number of the
        network device."
    ::= { networkDeviceTableEntry 17 }

networkDevicePCIDeviceNumber                    OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0018 This attribute defines the PCI device number of the
        network device."
    ::= { networkDeviceTableEntry 18 }

networkDevicePCIFunctionNumber                  OBJECT-TYPE
    SYNTAX      Unsigned8BitRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0019 This attribute defines the PCI function number of the
        network device."
    ::= { networkDeviceTableEntry 19 }

networkDeviceTOECapabilityFlags                 OBJECT-TYPE
    SYNTAX      NetworkDeviceTOECapabilityFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0023 This attribute defines the TCP/IP Offload Engine (TOE)
        capability flags of the network device."
    ::= { networkDeviceTableEntry 23 }

networkDeviceiSCSICapabilityFlags               OBJECT-TYPE
    SYNTAX      NetworkDeviceiSCSICapabilityFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0027 This attribute defines the Internet Small Computer
        System Interface (iSCSI) capability flags of the network device."
    ::= { networkDeviceTableEntry 27 }

networkDeviceiSCSIEnabled                       OBJECT-TYPE
    SYNTAX      BooleanType
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0028 This attribute defines if iSCSI is enabled for the
        network device."
    ::= { networkDeviceTableEntry 28 }

networkDeviceCapabilities                       OBJECT-TYPE
    SYNTAX      NetworkDeviceCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0029 This attribute defines the capabilities of the network device.
        If this value is notSupported(0), the networkDeviceTOECapabilityFlags,
        networkDeviceiSCSICapabilityFlags and networkDeviceiSCSIEnabled attributes should
        be used to determine the network device capabilities.  If the supported(1) bit
        is on, this attribute should be used to determine the network device capabilities,
        and the attributes mentioned above should not be used.  NOTE: For a network device
        on Converged Network Adapter (CNA), this attribute provides capability information
        for the CNA and not for the network device. For more information read vendor
        documentation."
    ::= { networkDeviceTableEntry 29 }

networkDeviceFQDD                               OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1100.0090.0001.0030 Fully qualified device descriptor (FQDD) of the
        network device."
    ::= { networkDeviceTableEntry 30 }


-------------------------------------------------------------------------------
-- Slot Group
--
-- OID Format: *******.4.1.674.10892.5.4.1200
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
-- System Slot Table
--
-- OID Format: *******.4.1.674.10892.5.4.1200.10.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

SystemSlotStateCapabilitiesFlags                ::= INTEGER {
    systemSlotHotPlugIsUnknown(1),              -- state capabilities are unknown
    systemSlotHotPlugIsHotPluggableCapable(2),  -- slot can support Hot Plug
    systemSlotHotPlugCanBePoweredOn(4),         -- slot power (and corresponding LED) can be powered on
    systemSlotHotPlugCanSignalAttention(8),     -- slot attention state (and corresponding LED) can be set
    systemSlotHotPlugCanSignalPowerFault(16),   -- slot power on fault (and corresponding LED) can be detected due to a short or overcurrent
    systemSlotHotPlugCanSignalAdapterPresent(32),  -- slot adapter (card) present in slot (may not be powered) can be detected
    systemSlotHotPlugCanSignalPowerButtonPressed(64),  -- slot power button can be pressed to signal a toggle of the power state
    canSupportAllHotPlugCapabilities(126),      -- slot can support all Hot Plug capabilities
    systemSlotCanProvide5Volts(128),            -- slot can provide 5 volt supply
    systemSlotCanProvide3Point3Volts(256),      -- slot can provide 3.3 volt supply
    systemSlotCanSignalIfShared(512),           -- slot opening if shared with another slot can be detected
    systemSlotCanSupportCard16(1024),           -- slot can support PC Card-16
    systemSlotCanSupportCardBus(2048),          -- slot can support CardBus
    systemSlotCanSupportZoomVideo(4096),        -- slot can support Zoom Video
    systemSlotCanSupportModemRingResume(8192),  -- slot can support Modem Ring Resume
    systemSlotCanSupportPMESignal(16384),       -- slot can support Power Management Enable (PME#) signal
    canSupportAllSlotCapabilities(32640),       -- slot can support all Slot capabilities
    canSupportAllSlotAndAllHotPlugCapabilities(32766) -- slot can support all Slot and all Hot Plug capabilities
}

SystemSlotStateSettingsFlags                    ::= INTEGER {
    systemSlotHotPlugIsUnknown(1),              -- state settings are unknown
    systemSlotHotPlugIsHotPluggable(2),         -- slot supports Hot Plug
    systemSlotHotPlugIsPoweredOn(4),            -- slot has power (and corresponding LED) ON
    systemSlotHotPlugIsAtAttention(8),          -- slot is at attention state (and corresponding LED) is ON
    systemSlotHotPlugHasPowerFaulted(16),       -- slot has power on fault (and corresponding LED) was detected due to a short or overcurrent
    systemSlotHotPlugAdapterIsPresent(32),      -- slot adapter (card) present in slot (may not be powered on)
    systemSlotHotPlugAdapterPresentAndPoweredOn(36),  -- slot adapter (card) present in slot and powered on
    systemSlotHotPlugPowerButtonPressed(64),    -- slot power button pressed to signal toggle of power state
    systemSlotProvides5Volts(128),              -- slot provides 5 volt supply
    systemSlotProvides3Point3Volts(256),        -- slot provides 3.3 volt supply
    systemSlotIsShared(512),                    -- slot opening is shared with another slot (e.g. PCI/EISA shared slot)
    systemSlotSupportsCard16(1024),             -- slot supports PC Card-16
    systemSlotSupportsCardBus(2048),            -- slot supports CardBus
    systemSlotSupportsZoomVideo(4096),          -- slot supports Zoom Video
    systemSlotSupportsModemRingResume(8192),    -- slot supports Modem Ring Resume
    systemSlotSupportsPMESignal(16384),         -- slot supports Power Management Enable (PME#) signal
    supportsPMEand3P3Vand5VandHotPluggable(16770),
    supportsPMEand3P3Vand5VhasAdapterOn(16804),
    supportsPMEand3P3Vand5VhasAdapterOnandisHotPluggable(16806),
    supportsPMEand3P3VIsSharedand5VhasAdapterOnandHotPlugable(17316)
}

SystemSlotTypeEnum                              ::= INTEGER {
    systemSlotIsOther(1),                       -- type is Other
    systemSlotIsUnknown(2),                     -- type is Unknown
    systemSlotIsISA(3),                         -- type is ISA
    systemSlotIsMCA(4),                         -- type is MCA
    systemSlotIsEISA(5),                        -- type is EISA
    systemSlotIsPCI(6),                         -- type is PCI
    systemSlotIsPCMCIA(7),                      -- type is PCMCIA
    systemSlotIsVLVESA(8),                      -- type is VL-VESA
    systemSlotIsProprietary(9),                 -- type is Proprietary
    systemSlotIsProcessorCard(10),              -- type is Processor Card Slot
    systemSlotIsProprietaryMemory(11),          -- type is Proprietary Memory Card Slot
    systemSlotIsIORiserCard(12),                -- type is I/O Riser Card Slot
    systemSlotIsNuBUS(13),                      -- type is NuBus
    systemSlotIsPCI66MHz(14),                   -- type is PCI - 66MHz Capable
    systemSlotIsAGP(15),                        -- type is AGP
    systemSlotIsAGP2X(16),                      -- type is AGP 2X
    systemSlotIsAGP4X(17),                      -- type is AGP 4X
    systemSlotIsPC98C20(18),                    -- type is PC-98/C20
    systemSlotIsPC98C24(19),                    -- type is PC-98/C24
    systemSlotIsPC98E(20),                      -- type is PC-98/E
    systemSlotIsPC98LocalBus(21),               -- type is PC-98/Local Bus
    systemSlotIsPC98Card(22),                   -- type is PC-98/Card
    systemSlotIsPCIX(23),                       -- type is PCI-X
    systemSlotIsPCIExpress(24),                 -- type is PCI Express
    systemSlotIsAGP8X(25),                      -- type is AGP 8X
    systemSlotIsPCIExpressX1(166),              -- type is PCI Express x1
    systemSlotIsPCIExpressX2(167),              -- type is PCI Express x2
    systemSlotIsPCIExpressX4(168),              -- type is PCI Express x4
    systemSlotIsPCIExpressX8(169),              -- type is PCI Express x8
    systemSlotIsPCIExpressX16(170),             -- type is PCI Express x16
    systemSlotIsPCIExpressGen2(171),            -- type is PCI Express Gen 2
    systemSlotIsPCIExpressGen2X1(172),          -- type is PCI Express Gen 2 x1
    systemSlotIsPCIExpressGen2X2(173),          -- type is PCI Express Gen 2 x2
    systemSlotIsPCIExpressGen2X4(174),          -- type is PCI Express Gen 2 x4
    systemSlotIsPCIExpressGen2X8(175),          -- type is PCI Express Gen 2 x8
    systemSlotIsPCIExpressGen2X16(176)          -- type is PCI Express Gen 2 x16
}

SystemSlotUsageEnum                             ::= INTEGER {
    systemSlotUsageIsOther(1),                  -- usage is other than following values
    systemSlotUsageIsUnknown(2),                -- usage is unknown
    systemSlotUsageIsAvailable(3),              -- usage is available
    systemSlotUsageIsInUse(4)                   -- usage is in use
}

SystemSlotCategoryEnum                          ::= INTEGER {
    systemSlotCategoryIsOther(1),               -- category is other than following values
    systemSlotCategoryIsUnknown(2),             -- category is unknown
    systemSlotCategoryIsBusConnector(3),        -- category is Bus Connector
    systemSlotCategoryIsPCMCIA(4),              -- category is PCMCIA
    systemSlotCategoryIsMotherboard(5)          -- category is Motherboard
}

SystemSlotTableEntry                            ::= SEQUENCE {
    systemSlotchassisIndex                      ObjectRange,
    systemSlotIndex                             ObjectRange,
    systemSlotStateCapabilitiesUnique           SystemSlotStateCapabilitiesFlags,
    systemSlotStateSettingsUnique               SystemSlotStateSettingsFlags,
    systemSlotStatus                            ObjectStatusEnum,
    systemSlotCurrentUsage                      SystemSlotUsageEnum,
    systemSlotType                              SystemSlotTypeEnum,
    systemSlotSlotExternalSlotName              String64,
    systemSlotCategory                          SystemSlotCategoryEnum
}

systemSlotTable                                 OBJECT-TYPE
    SYNTAX      SEQUENCE OF SystemSlotTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1200.0010 This object defines the System Slot Table."
    ::= { slotGroup 10 }

systemSlotTableEntry                            OBJECT-TYPE
    SYNTAX      SystemSlotTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001 This object defines the System Slot Table Entry."
    INDEX       { systemSlotchassisIndex,
                  systemSlotIndex }
    ::= { systemSlotTable 1 }

systemSlotchassisIndex                          OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0001 This attribute defines the index (one based) of the
        associated system chassis."
    ::= { systemSlotTableEntry 1 }

systemSlotIndex                                 OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0002 This attribute defines the index (one based) of the
        system slot."
    ::= { systemSlotTableEntry 2 }

systemSlotStateCapabilitiesUnique               OBJECT-TYPE
    SYNTAX      SystemSlotStateCapabilitiesFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0003 This attribute defines the state capabilities of the
        system slot."
    ::= { systemSlotTableEntry 3 }

systemSlotStateSettingsUnique                   OBJECT-TYPE
    SYNTAX      SystemSlotStateSettingsFlags
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0004 This attribute defines the state settings of the
        system slot."
    ::= { systemSlotTableEntry 4 }

systemSlotStatus                                OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0005 This attribute defines the status of the system slot."
    ::= { systemSlotTableEntry 5 }

systemSlotCurrentUsage                          OBJECT-TYPE
    SYNTAX      SystemSlotUsageEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0006 This attribute defines the current usage of the
        system slot."
    ::= { systemSlotTableEntry 6 }

systemSlotType                                  OBJECT-TYPE
    SYNTAX      SystemSlotTypeEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0007 This attribute defines the type of the system slot."
    ::= { systemSlotTableEntry 7 }

systemSlotSlotExternalSlotName                  OBJECT-TYPE
    SYNTAX      String64
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0008 This attribute defines the name of the external
        connector name of the system slot."
    ::= { systemSlotTableEntry 8 }

systemSlotCategory                              OBJECT-TYPE
    SYNTAX      SystemSlotCategoryEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "1200.0010.0001.0011 This attribute defines the category of the system slot."
    ::= { systemSlotTableEntry 11 }


-------------------------------------------------------------------------------
-- Field Replaceable Unit (FRU) Group
--
-- OID: *******.4.1.674.10892.5.4.2000
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
-- Field Replaceable Unit (FRU) Table
--
-- OID Format: *******.4.1.674.10892.5.4.2000.10.1.<a>.<i1>.<i2>
-------------------------------------------------------------------------------

FruTableEntry                                   ::= SEQUENCE {
    fruChassisIndex                             ObjectRange,
    fruIndex                                    ObjectRange,
    fruInformationStatus                        ObjectStatusEnum,
    fruManufacturerName                         DisplayString,
    fruSerialNumberName                         DisplayString,
    fruPartNumberName                           DisplayString,
    fruRevisionName                             DisplayString,
    fruFQDD                                     FQDDString
}

fruTable                                        OBJECT-TYPE
    SYNTAX      SEQUENCE OF FruTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "2000.0010 This object defines the Field Replaceable Unit table."
    ::= { fruGroup 10 }

fruTableEntry                                   OBJECT-TYPE
    SYNTAX      FruTableEntry
    ACCESS      not-accessible
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001 This object defines the Field Replaceable Unit table entry."
    INDEX       { fruChassisIndex,
                  fruIndex }
    ::= { fruTable 1 }

fruChassisIndex                                 OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0001 This attribute defines the index (one based) of the
        system chassis containing the field replaceable unit."
    ::= { fruTableEntry 1 }

fruIndex                                        OBJECT-TYPE
    SYNTAX      ObjectRange
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0002 This attribute defines the index (one based) of the
        field replaceable unit."
    ::= { fruTableEntry 2 }

fruInformationStatus                            OBJECT-TYPE
    SYNTAX      ObjectStatusEnum
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0003 This attribute defines the status of the
        field replaceable unit information."
    ::= { fruTableEntry 3 }

fruManufacturerName                             OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..64))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0006 This attribute defines the manufacturer of the
        field replaceable unit."
    ::= { fruTableEntry 6 }

fruSerialNumberName                             OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..64))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0007 This attribute defines the serial number of the
        field replaceable unit."
    ::= { fruTableEntry 7 }

fruPartNumberName                               OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..64))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0008 This attribute defines the part number of the
        field replaceable unit."
    ::= { fruTableEntry 8 }

fruRevisionName                                 OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..64))
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0009 This attribute defines the revision of the
        field replaceable unit."
    ::= { fruTableEntry 9 }

fruFQDD                                         OBJECT-TYPE
    SYNTAX      FQDDString
    ACCESS      read-only
    STATUS      mandatory
    DESCRIPTION
        "2000.0010.0001.0012 Fully qualified device descriptor (FQDD) of the
        field replaceable unit."
    ::= { fruTableEntry 12 }


-------------------------------------------------------------------------------
-- Storage Details Group
--
-- OID Format: *******.4.1.674.10892.5.5
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
-- Battery Table
--
-- OID Format: *******.4.1.674.10892.********.130.15.1.<a>.<i1>
-------------------------------------------------------------------------------

BatteryTableEntry                               ::=SEQUENCE { 
    batteryNumber                               INTEGER,
    batteryState                                INTEGER,
    batteryComponentStatus                      ObjectStatusEnum,
    batteryPredictedCapacity                    INTEGER,
    batteryFQDD                                 DisplayString,
    batteryDisplayName                          DisplayString
}

batteryTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF BatteryTableEntry
    ACCESS         not-accessible
    STATUS         mandatory
    DESCRIPTION
        "A table of managed batteries. The number of 
        entries is related to number of Batteries
        discovered in the system.  The maximum number of entries 
        is implementation dependent.
        Note: The properties in this table may not be applicable to all 
        entries.
        "
    ::= { physicalDevices 15 }

batteryTableEntry OBJECT-TYPE
    SYNTAX         BatteryTableEntry
    ACCESS         not-accessible
    STATUS         mandatory
    DESCRIPTION
        "An entry in the battery table. A row in this table cannot be
        created or deleted by SNMP operations on columns of the table.
        "
        INDEX { batteryNumber }
    ::= { batteryTable 1 }

batteryNumber OBJECT-TYPE
    SYNTAX         INTEGER (1..255)
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "Instance number of this battery entry.
        "
    ::= { batteryTableEntry 1 }

batteryState OBJECT-TYPE
    SYNTAX         INTEGER
		   {
		   unknown(1),
		   ready(2),
		   failed(3),
		   degraded(4),
		   missing(5),
		   charging(6),
		   belowThreshold(7)
		   }
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "Current state of battery.
        Possible values:
        1: The current state could not be determined.
        2: The battery is operating normally.
        3: The battery has failed and needs to be replaced.
        4: The battery temperature is high or charge level is depleting.
        5: The battery is missing or not detected. 
        6: The battery is undergoing the re-charge phase.
        7: The battery voltage or charge level is below the threshold.
        "
    ::= { batteryTableEntry 4 }

batteryComponentStatus OBJECT-TYPE
    SYNTAX         ObjectStatusEnum
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The status of the battery itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other
        2: Unknown
        3: OK 
        4: Non-critical 
        5: Critical
        6: Non-recoverable"
    ::= { batteryTableEntry 6 }

batteryPredictedCapacity OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    failed(2),
                    ready(3)
                    }
    ACCESS          read-only
    STATUS          obsolete
    DESCRIPTION
        "This entry is obsolete. Use the batteryComponentStatus or 
        batteryState instead.
        "
    ::= { batteryTableEntry 10 }

batteryFQDD OBJECT-TYPE
    SYNTAX         DisplayString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The battery's Fully Qualified Device Descriptor (FQDD) as 
        represented in Storage Management.
        "
    ::= { batteryTableEntry 20 }

batteryDisplayName OBJECT-TYPE
    SYNTAX         DisplayString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The battery's friendly FQDD as represented in Storage Management."
    ::= { batteryTableEntry 21 }


-------------------------------------------------------------------------------
-- Controller Table
--
-- OID Format: *******.4.1.674.10892.********.130.1.1.<a>.<i1>
-------------------------------------------------------------------------------

ControllerTableEntry                            ::=SEQUENCE { 
    controllerNumber                            INTEGER,
    controllerName                              DisplayString,
    controllerRebuildRate                       INTEGER,
    controllerFWVersion                         DisplayString,
    controllerCacheSizeInMB                     INTEGER,
    controllerRollUpStatus                      ObjectStatusEnum,
    controllerComponentStatus                   ObjectStatusEnum,
    controllerDriverVersion                     DisplayString,
    controllerPCISlot                           DisplayString,
    controllerReconstructRate                   INTEGER,
    controllerPatrolReadRate                    INTEGER,
    controllerBGIRate                           INTEGER,
    controllerCheckConsistencyRate              INTEGER,
    controllerPatrolReadMode                    INTEGER,
    controllerPatrolReadState                   INTEGER,
    controllerPersistentHotSpare                BooleanType,
    controllerSpinDownUnconfiguredDrives        BooleanType,
    controllerSpinDownHotSpareDrives            BooleanType,
    controllerSpinDownTimeInterval              INTEGER,
    controllerPreservedCache                    BooleanType,
    controllerCheckConsistencyMode              INTEGER,
    controllerCopyBackMode                      INTEGER,
    controllerSecurityStatus                    INTEGER,
    controllerEncryptionKeyPresent              BooleanType,
    controllerEncryptionCapability              INTEGER,
    controllerLoadBalanceSetting                INTEGER,    
    controllerMaxCapSpeed                       INTEGER,
    controllerSASAddress                        DisplayString,
    controllerFQDD                              FQDDString,
    controllerDisplayName                       DisplayString,
    controllerT10PICapability                   INTEGER,
    controllerRAID10UnevenSpansSupported        BooleanType,
    controllerEnhancedAutoImportForeignConfigMode INTEGER,  
    controllerBootModeSupported                 BooleanType,
    controllerBootMode                          INTEGER
}

controllerTable OBJECT-TYPE
    SYNTAX         SEQUENCE OF ControllerTableEntry
    ACCESS         not-accessible
    STATUS         mandatory
    DESCRIPTION
        "A table of managed RAID controllers. The number of entries
        is related to number of RAID controllers discovered in the
        system. The maximum number of entries is implementation dependent.
        Note: The properties in this table may not be applicable to all 
        entries.
        "
    ::= { physicalDevices 1 }

controllerTableEntry OBJECT-TYPE
    SYNTAX         ControllerTableEntry
    ACCESS         not-accessible
    STATUS         mandatory
    DESCRIPTION
        "An entry in the table of RAID controllers. A row in this table 
        cannot be created or deleted by SNMP operations on columns of 
        the table."
    INDEX         { controllerNumber }
    ::= { controllerTable 1 }

controllerNumber OBJECT-TYPE
    SYNTAX         INTEGER (1..255)
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "Instance number of this controller entry."
    ::= { controllerTableEntry 1 }


controllerName OBJECT-TYPE
    SYNTAX         DisplayString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The controller's name as represented in Storage Management.
        "
    ::= { controllerTableEntry 2 }

controllerRebuildRate OBJECT-TYPE
    SYNTAX         INTEGER (0..100)
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The rebuild rate is the percentage of the controller's 
        resources dedicated to rebuilding a failed disk when a rebuild 
        is necessary.
        "
    ::= { controllerTableEntry 7 }

controllerFWVersion OBJECT-TYPE
    SYNTAX         DisplayString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The controller's current firmware version.
        "
    ::= { controllerTableEntry 8 }

controllerCacheSizeInMB OBJECT-TYPE
    SYNTAX         INTEGER
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The controller's current amount of cache memory in megabytes.  
        "
    ::= { controllerTableEntry 9 }

controllerRollUpStatus OBJECT-TYPE
    SYNTAX         ObjectStatusEnum
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "Severity of the controller state.  
        This is the combined status of the controller and its components.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK 
        4: Non-critical 
        5: Critical.
        6: Non-recoverable.
        "
    ::= { controllerTableEntry 37 }

controllerComponentStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the controller itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other
        2: Unknown
        3: OK 
        4: Non-critical 
        5: Critical
        6: Non-recoverable"
    ::= { controllerTableEntry 38 }

controllerDriverVersion OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Currently installed driver version for this controller on the host.
        "
    ::= { controllerTableEntry 41 }

controllerPCISlot OBJECT-TYPE
    SYNTAX         DisplayString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The PCI slot on the server where the controller is seated. This 
        data is not reported for embedded or integrated controllers, 
        "
    ::= { controllerTableEntry 42 }

controllerReconstructRate OBJECT-TYPE
    SYNTAX         INTEGER (0..100)
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The reconstruct rate is the percentage of the controller's resources 
        dedicated to reconstructing a disk group after adding a physical disk 
        or changing the RAID level of a virtual disk residing on the disk 
        group.
        "
    ::= { controllerTableEntry 48 }

controllerPatrolReadRate OBJECT-TYPE
    SYNTAX         INTEGER (0..100)
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The patrol read rate is the percentage of the controller's 
        resources dedicated to perform a patrol read on disks participating
        in a virtual disk or hot spares.
        "
    ::= { controllerTableEntry 49 }

controllerBGIRate OBJECT-TYPE
    SYNTAX         INTEGER (0..100)
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The background initialization (BGI) rate is the percentage of the 
        controller's resources dedicated to performing the background 
        initialization of a redundant virtual disk after it is created.
        "
    ::= { controllerTableEntry 50 }

controllerCheckConsistencyRate OBJECT-TYPE
    SYNTAX         INTEGER (0..100)
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The check consistency rate is the percentage of the 
        controller's resources dedicated to performing a check consistency 
        on a redundant virtual disk.
        "
    ::= { controllerTableEntry 51 }

controllerPatrolReadMode OBJECT-TYPE
    SYNTAX         INTEGER
           {
           other(1),
           notSupported(2),
           disabled(3),
           auto(4),
           manual(5)
           }
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "Identifies the patrol read mode setting for the controller.
        Possible values:
        1: Not one of the following or could not be determined.
        2: Not Supported on this controller. 
        3: Disabled.
        4: Automatic.
        5: Manual.
        "
    ::= { controllerTableEntry 52 }
    
controllerPatrolReadState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    stopped(2),
                    active(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This property displays the current state of the patrol read process.
         Possible values:
        1: Not one of the following or could not be determined.
        2: Patrol read is not running.
        3: Patrol read is running.
        "
    ::= { controllerTableEntry 53 }

controllerPersistentHotSpare OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether hot spare drives would be restored on insertion 
        into the same slot.
        "
    ::= { controllerTableEntry 59 }

controllerSpinDownUnconfiguredDrives OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether un-configured drives would be put in power
        save mode by the controller.
        "
    ::= { controllerTableEntry 60 }

controllerSpinDownHotSpareDrives OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether hot spare drives would be put in power
        save mode by the controller.
        "
    ::= { controllerTableEntry 61 }

controllerSpinDownTimeInterval OBJECT-TYPE
    SYNTAX          INTEGER (30..1440)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The duration in minutes after which, the unconfigured or hot 
        spare drives will be spun down to power save mode.
        "
    ::= { controllerTableEntry 62 }

controllerPreservedCache OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether preserved cache or pinned cache is 
        present on the controller.
        "
    ::= { controllerTableEntry 69 }

controllerCheckConsistencyMode OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    unsupported(2),
                    normal(3),
                    stopOnError(4)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current check consistency mode setting 
        for the controller.
        Possible values:
        1: Not one of the following.
        2: Not supported on this controller.
        3: Normal check consistency operation.
        4: Check consistency operation will stop on encountering 
        an error.
        "
    ::= { controllerTableEntry 70 }

controllerCopyBackMode OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    unsupported(2),
                    on(3),
                    onWithSmart(4),
                    off(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current copy back mode setting 
        for the controller.
        Possible values:
        1: Not one of the following.
        2: Not supported on this controller.
        3: Disks assigned as spares could revert back to spare status.
        4: Data from physical disk participating in a 
        virtual disk could be automatically copied to the assigned 
        hot spare in case former has a predictive failure event. 
        5: Copyback mode is disabled. 
        "
    ::= { controllerTableEntry 71 }

controllerSecurityStatus OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    none(2),
                    lkm(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The controller's current security/encryption status..
        Possible values:
        1: The current status could not be determined.
        2: Controller is not operating in an encryption mode.
        3: Controller is operating in the Local Key Management
        (LKM) encryption mode.
        "
    ::= { controllerTableEntry 72 }

controllerEncryptionKeyPresent OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether encryption key is assigned for the controller.
        "
    ::= { controllerTableEntry 73 }

controllerEncryptionCapability OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    none(2),
                    lkm(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
    "The type of encryption supported by the controller.
    Possible values:
    1: Not one of the following.
    2: No encryption supported, 
    3: Local Key Management,
    "
    ::= { controllerTableEntry 74 }

controllerLoadBalanceSetting OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    unsupported(2),
                    auto(3),
                    none(4)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The ability of the controller to automatically use both 
        controller ports (or connectors) connected to the same enclosure in 
        order to route I/O requests.
        Possible values:
        1: Not one of the following.
        2: Not supported.
        3: Automatic load balancing is active.
        4: Load balancing is inactive.
        "
    ::= { controllerTableEntry 75 }

controllerMaxCapSpeed OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    oneDotFiveGbps(2),
                    threeGbps(3),
                    sixGbps(4),
                    twelveGbps(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The maximum speed of the controller.in 
        Gigbits per second (Gbps).
        Possible values:
        1: The speed could not be determined.
        2. 1.5 Gbps
        3: 3.0 Gbps
        4: 6.0 Gbps
        5: 12.0 Gbps
        "
    ::= { controllerTableEntry 76 }

controllerSASAddress OBJECT-TYPE
    SYNTAX         DisplayString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The SAS address of the controller.
        "
    ::= { controllerTableEntry 77 }

controllerFQDD OBJECT-TYPE
    SYNTAX         FQDDString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The controller's Fully Qualified Device Descriptor (FQDD) as 
        represented in Storage Management.
        "
    ::= { controllerTableEntry 78 }

controllerDisplayName OBJECT-TYPE
    SYNTAX         DisplayString
    ACCESS         read-only
    STATUS         mandatory
    DESCRIPTION
        "The controller's friendly FQDD as represented in Storage 
    Management."
    ::= { controllerTableEntry 79 }

controllerT10PICapability OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    capable(2),
                    notCapable(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the controller supports the T10 PI (Protection 
        Information). These protection fields are known as DIF
        (Data Integrity Fields).
        Possible values:
        1: Not one of the following.
        2: Capable of supporting T10 PI.
        3: Not capable of supporting T10 PI.
        "
    ::= { controllerTableEntry 80 }

controllerRAID10UnevenSpansSupported OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether uneven spans for RAID 10 virtual disk 
        is supported on the controller.
        "
    ::= { controllerTableEntry 81 }

controllerEnhancedAutoImportForeignConfigMode OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    notSupported(2),
                    disabled(3),
                    enabled(4)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates the status of enhanced auto-import of foreign 
        configuration property of the controller.
        1: Not one of the following.
        2: Not Supported.
        3: Disabled.
        4: Enabled.
        "
    ::= { controllerTableEntry 82 }

controllerBootModeSupported OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether headless boot mode settings are supported 
        on the controller.
        "
    ::= { controllerTableEntry 83 }

controllerBootMode OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    notApplicable(1),
                    user(2),
                    contOnError(3),
                    headlessContOnError(4),
                    headlessSafe(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates the boot mode of the controller.
        Possible values:
        1: Not applicable for this controller.
        2: User mode: User interaction required for all boot messages (not 
        applicable for uEFI environments).
        3: Continue Boot On Error. User interaction only required for 
        critical messages.
        4: Headless Mode Continue On Error. User interaction is not required.
        Controller boot may halt on Error.
        5: Headless Safe Mode. Controller shall boot to safe mode on critical
        errors.
        "
    ::= { controllerTableEntry 84 }


-------------------------------------------------------------------------------
-- Physical Disk Table
--
-- OID Format: *******.4.1.674.10892.********.130.4.1.<a>.<i1>
-------------------------------------------------------------------------------

PhysicalDiskTableEntry                          ::=SEQUENCE { 
    physicalDiskNumber                          INTEGER,
    physicalDiskName                            DisplayString,
    physicalDiskManufacturer                    DisplayString,    
    physicalDiskState                           INTEGER,
    physicalDiskProductID                       DisplayString,
    physicalDiskSerialNo                        DisplayString,
    physicalDiskRevision                        DisplayString,
    physicalDiskCapacityInMB                    INTEGER,
    physicalDiskUsedSpaceInMB                   INTEGER,
    physicalDiskFreeSpaceInMB                   INTEGER,
    physicalDiskBusType                         INTEGER,
    physicalDiskSpareState                      INTEGER,
    physicalDiskComponentStatus                 ObjectStatusEnum,    
    physicalDiskPartNumber                      DisplayString,
    physicalDiskSASAddress                      DisplayString,
    physicalDiskNegotiatedSpeed                 INTEGER,
    physicalDiskCapableSpeed                    INTEGER,
    physicalDiskSmartAlertIndication            BooleanType,
    physicalDiskManufactureDay                  DisplayString,
    physicalDiskManufactureWeek                 DisplayString,
    physicalDiskManufactureYear                 DisplayString,
    physicalDiskMediaType                       INTEGER,
    physicalDiskPowerState                      INTEGER,
    physicalDiskRemainingRatedWriteEndurance    INTEGER,
    physicalDiskOperationalState                INTEGER,
    physicalDiskProgress                        INTEGER,
    physicalDiskSecurityStatus                  INTEGER,
    physicalDiskFormFactor                      INTEGER,
    physicalDiskFQDD                            FQDDString,
    physicalDiskDisplayName                     DisplayString,
    physicalDiskT10PICapability                 INTEGER,
    physicalDiskBlockSizeInBytes                INTEGER,
    physicalDiskProtocolVersion                 DisplayString,
    physicalDiskPCIeNegotiatedLinkWidth         INTEGER,
    physicalDiskPCIeCapableLinkWidth            INTEGER
 }

physicalDiskTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF PhysicalDiskTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "A table of managed physical disks. The number of entries is 
        related to number of physical Disks discovered in the system.
        The maximum number of entries is implementation dependent.
        Note: The properties in this table may not be applicable to 
        all entries.
        "
    ::= { physicalDevices 4 }

physicalDiskTableEntry OBJECT-TYPE
    SYNTAX          PhysicalDiskTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "An entry in the physical Disk table. A row in this table cannot be
        created or deleted by SNMP operations on columns of the table.
        "
    INDEX         { physicalDiskNumber }
    ::= { physicalDiskTable 1 }

physicalDiskNumber OBJECT-TYPE
    SYNTAX          INTEGER (1..*********0)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Instance number of this physical disk entry.
        "
    ::= { physicalDiskTableEntry 1 }

physicalDiskName OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The physical disk's name as represented in Storage Management.
        "
    ::= { physicalDiskTableEntry 2 }

physicalDiskManufacturer OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The name of the physical disk's manufacturer.
        "
    ::= { physicalDiskTableEntry 3 }

physicalDiskState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    ready(2),
                    online(3),
                    foreign(4),
                    offline(5),
                    blocked(6),
                    failed(7),
                    nonraid(8),
                    removed(9),
                    readonly(10)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current state of this physical disk.
        Possible states:
        1: The current state could not be determined.
        2: The physical disk is available for use, but no RAID configuration 
        has been assigned. 
        3: A RAID configuration has been assigned to the physical disk.
        4: The physical disk has been moved from another
        controller and contains all or some portion of a virtual disk.
        5: The physical disk is not available to the RAID
        controller. 
        6: The physical disk is currently blocked by
        controller.
        7: The physical disk is not operational.
        8: The physical disk is not a RAID capable disk 
        9: The physical disk has been removed.
        10:The physical disk media has been placed in read only mode.
        "
    ::= { physicalDiskTableEntry 4 }

physicalDiskProductID OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The model number of the physical disk.
        "
    ::= { physicalDiskTableEntry 6 }

physicalDiskSerialNo OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The physical disk's unique identification number 
        from the manufacturer.
        "
    ::= { physicalDiskTableEntry 7 }

physicalDiskRevision OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The firmware version of the physical disk.
        "
    ::= { physicalDiskTableEntry 8 }

physicalDiskCapacityInMB OBJECT-TYPE
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The size of the physical disk in megabytes.
        "        
    ::= { physicalDiskTableEntry 11 }

physicalDiskUsedSpaceInMB OBJECT-TYPE
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The amount of used space in megabytes on the physical
        disk. This is not applicable for NVMe devices.
        "
    ::= { physicalDiskTableEntry 17 }

physicalDiskFreeSpaceInMB OBJECT-TYPE
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The amount of free space in megabytes on the physical
        disk. This is not applicable for NVMe devices.
        "
    ::= { physicalDiskTableEntry 19 }    

physicalDiskBusType OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    scsi(2),
                    sas(3),
                    sata(4),
                    fibre(5),
                    pcie(6)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The bus type of the physical disk.
        Possible values:
        1: The bus type could not be determined.
        2: Small Computer System Interface (SCSI).
        3: Serial Attached SCSI (SAS).
        4: Serial Advanced Technology Attachment (SATA).
        5: Fibre channel.
        6: PCIe.
        "
    ::= { physicalDiskTableEntry 21 }    

physicalDiskSpareState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    notASpare(1),
                    dedicatedHotSpare(2),
                    globalHotSpare(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the disk as a spare.
        Possible values:
        1: Physical disk is not a spare.
        2: Physical disk is a dedicated hot spare.
        3: Physical disk is a global hot spare.
        "
    ::= { physicalDiskTableEntry 22 }

physicalDiskComponentStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the physical disk itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other
        2: Unknown
        3: OK 
        4: Non-critical 
        5: Critical
        6: Non-recoverable
        "
    ::= { physicalDiskTableEntry 24 }

physicalDiskPartNumber OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The part number of the disk.
        "
    ::= { physicalDiskTableEntry 27 }

physicalDiskSASAddress OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The SAS address of the physical disk.
        "
    ::= { physicalDiskTableEntry 28 }

physicalDiskNegotiatedSpeed OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    oneDotFiveGbps(2),
                    threeGbps(3),
                    sixGbps(4),
                    twelveGbps(5),
                    fiveGTps(6),
                    eightGTps(7)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The data transfer speed that the disk negotiated while spinning up 
        in Gigbits per second (Gbps).
        Possible values:
        1: The speed could not be determined.
        2. 1.5 Gbps
        3: 3.0 Gbps
        4: 6.0 Gbps
        5: 12.0 Gbps
        6: 5 GT/s (applicable for NVMe devices).
        7: 8 GT/s (applicable for NVMe devices).
        "
    ::= { physicalDiskTableEntry 29 }

physicalDiskCapableSpeed OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    oneDotFiveGbps(2),
                    threeGbps(3),
                    sixGbps(4),
                    twelveGbps(5),
                    fiveGTps(6),
                    eightGTps(7)

                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The maximum data transfer speed supported by the disk 
        in Gigbits per second (Gbps).
        Possible values:
        1: The speed could not be determined.
        2. 1.5 Gbps
        3: 3.0 Gbps
        4: 6.0 Gbps
        5: 12.0 Gbps
        6: 5 GT/s (applicable for NVMe devices).
        7: 8 GT/s (applicable for NVMe devices).
        "
    ::= { physicalDiskTableEntry 30 }

physicalDiskSmartAlertIndication OBJECT-TYPE
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the physical disk has received a predictive 
        failure alert.
        "
    ::= { physicalDiskTableEntry 31 }

physicalDiskManufactureDay OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The day of the week (1=Sunday thru 7=Saturday) 
        on which the physical disk was manufactured.
        "
    ::= { physicalDiskTableEntry 32 }

physicalDiskManufactureWeek OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The week (1 thru 53) in which the physical disk 
        was manufactured.
        "
    ::= { physicalDiskTableEntry 33 }

physicalDiskManufactureYear OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The four digit year in which the physical disk was manufactured.
        "
    ::= { physicalDiskTableEntry 34 }

physicalDiskMediaType OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    hdd(2),
                    ssd(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The media type of the physical disk.
        Possible Values:
        1: The media type could not be determined.
        2: Hard Disk Drive (HDD).
        3: Solid State Device (SSD).
        "
    ::= { physicalDiskTableEntry 35 }

physicalDiskPowerState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    spunUp(2),
                    spunDown(3),
                    transition(4),
                    on(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The power state of the physical disk.
        Possible Values:
        1: Not one of the following.
        2: The physical disk is in the spun up state.
        3: The physical disk is in the spun down state.
        4: The physical disk is changing from spun down state
        to spun up state or vice versa.
        5: The Solid State Device (SSD) is powered on.
        "
    ::= { physicalDiskTableEntry 42 }

physicalDiskRemainingRatedWriteEndurance OBJECT-TYPE
    SYNTAX          INTEGER (0..255)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "This property is applicable to SSD media type only. This indicates 
        the wear-out percentage of the SSD. Typically it is a value between 
        0 to 100. However, if the value is not available or not applicable 
        (in the case of HDD media type) the value will be 255.
        "
    ::= { physicalDiskTableEntry 49 }    

physicalDiskOperationalState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    notApplicable(1),
                    rebuild(2),
                    clear(3),
                    copyback(4)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The state of the physical disk when there are progressive
        operations ongoing.
        Possible values:
        1: There is no active operation running.
        2: Data from a redundant virtual disk is 
        currently being rebuilt onto the physical disk.
        3: Data on the disk is being erased.
        4: Data is being copied from a hot spare disk to 
        the physical disk or vice versa.
         "
    ::= { physicalDiskTableEntry 50 }

physicalDiskProgress OBJECT-TYPE
    SYNTAX          INTEGER (0..100)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The progress percentage of the operation that is being 
        performed on the physical disk. This is applicable 
        only if there is a progressive operations ongoing
        "
    ::= { physicalDiskTableEntry 51 }

physicalDiskSecurityStatus OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    supported(1),
                    notSupported(2),
                    secured(3),
                    locked(4),
                    foreign(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The security/encryption status of the physical disk.
        Possible Values:
        1: The physical disk supports encryption.
        2: The physical disk does not support encryption
        3: The physical disk is encrypted.
        4: The physical disk is locked by a key.
        5: The physical disk is locked by a foreign key.
        "
    ::= { physicalDiskTableEntry 52 }

physicalDiskFormFactor OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    oneDotEight(2),
                    twoDotFive(3),
                    threeDotFive(4)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The form factor of the physical disk.
        Possible values:
        1: The form factor could not be determined.
        2: 1.8 inch.
        3: 2.5 inch.
        4: 3.5 inch.
        "
    ::= { physicalDiskTableEntry 53 }

physicalDiskFQDD OBJECT-TYPE
    SYNTAX          FQDDString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The physical disk's Fully Qualified Device Descriptor (FQDD) 
        as represented in Storage Management.
        "
    ::= { physicalDiskTableEntry 54 }

physicalDiskDisplayName OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The physical disk's friendly FQDD as represented in Storage 
        Management.
        "
    ::= { physicalDiskTableEntry 55 }

physicalDiskT10PICapability OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    capable(2),
                    notCapable(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the physical disk supports the T10 PI (Protection 
        Information). These protection fields are known as DIF 
        (Data Integrity Fields).
        Possible values:
        1: Not one of the following.
        2: Capable of supporting T10 PI.
        3: Not capable of supporting T10 PI.
        "
    ::= { physicalDiskTableEntry 57 }

physicalDiskBlockSizeInBytes OBJECT-TYPE
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The block size (in bytes) of the physical disk. This is not 
        applicable for NVMe devices.
        Possible values:
        1: 512.
        2: 4096
        "
    ::= { physicalDiskTableEntry 58 }
    
physicalDiskProtocolVersion OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Applicable for NVMe devices only. The NVMe protocol version supported 
        by the device.
        "
    ::= { physicalDiskTableEntry 59 }

physicalDiskPCIeNegotiatedLinkWidth OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    notApplicable(2),
                    byOne(3),
                    byTwp(4),
                    byFour(5),
                    byEight(6),
                    bySixteen(7),
                    byThirtyTwp(8)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Applicable for NVMe devices  only. The PCIe link width negotiated with the host
        during device initialization.
        "
    ::= { physicalDiskTableEntry 60 }

physicalDiskPCIeCapableLinkWidth OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    notApplicable(2),
                    byOne(3),
                    byTwp(4),
                    byFour(5),
                    byEight(6),
                    bySixteen(7),
                    byThirtyTwp(8)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Applicable for NVMe devices only. The PCIe link widths the device is capable of 
        supporting.
        "
    ::= { physicalDiskTableEntry 61 }


-------------------------------------------------------------------------------
-- Virtual Disk Table
--
-- OID Format: *******.4.1.674.10892.********.140.1.1.<a>.<i1>
-------------------------------------------------------------------------------

VirtualDiskTableEntry                          ::=SEQUENCE { 
    virtualDiskNumber                           INTEGER,
    virtualDiskName                             DisplayString,
    virtualDiskState                            INTEGER,
    virtualDiskSizeInMB                         INTEGER,
    virtualDiskWritePolicy                      INTEGER,
    virtualDiskReadPolicy                       INTEGER,
    virtualDiskLayout                           INTEGER,
    virtualDiskStripeSize                       INTEGER,
    virtualDiskComponentStatus                  ObjectStatusEnum,        
    virtualDiskBadBlocksDetected                BooleanType,
    virtualDiskSecured                          BooleanType,
    virtualDiskIsCacheCade                      BooleanType,
    virtualDiskDiskCachePolicy                  INTEGER,
    virtualDiskOperationalState                 INTEGER,
    virtualDiskProgress                         INTEGER,
    virtualDiskAvailableProtocols               DisplayString,
    virtualDiskMediaType                        DisplayString,
    virtualDiskRemainingRedundancy              INTEGER,
    virtualDiskFQDD                             FQDDString,
    virtualDiskDisplayName                      DisplayString,
    virtualDiskT10PIStatus                      INTEGER,
    virtualDiskBlockSizeInBytes                 INTEGER
}

virtualDiskTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF VirtualDiskTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "A table of managed virtual disks. The number of entries is related
        to number of virtual disks discovered in the system. 
        The maximum number of entries is implementation dependent.
        Note: The properties in this table may not be applicable to all
        entries.
        "
    ::= { logicalDevices 1 }

virtualDiskTableEntry OBJECT-TYPE
    SYNTAX          VirtualDiskTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "An entry in the virtual disk table. A row in this table cannot be
        created or deleted by SNMP operations on columns of the table.
        "
    INDEX         { virtualDiskNumber }
    ::= { virtualDiskTable 1 }
    
virtualDiskNumber OBJECT-TYPE
    SYNTAX          INTEGER (1..*********)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Instance number of this virtual disk entry.
        "
        ::= { virtualDiskTableEntry 1 }

virtualDiskName OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The virtual disk's label as entered by the user.
        "
    ::= { virtualDiskTableEntry 2 }

virtualDiskState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    online(2),
                    failed(3),
                    degraded(4)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current state of this virtual disk
        (which includes any member physical disks.)
        Possible states:
        1: The current state could not be determined.
        2: The virtual disk is operating normally or optimally.
        3: The virtual disk has encountered a failure. The data on disk 
        is lost or is about to be lost.
        4: The virtual disk encounterd a failure with one or all of the 
        constituent redundant physical disks. The data on the virtual 
        disk might no longer be fault tolerant.
        "
    ::= { virtualDiskTableEntry 4 }

virtualDiskSizeInMB OBJECT-TYPE
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The size of the virtual disk in megabytes.
        "
    ::= { virtualDiskTableEntry 6 }

virtualDiskWritePolicy OBJECT-TYPE           
    SYNTAX          INTEGER
                    {
                    writeThrough(1),
                    writeBack(2),
                    writeBackForce(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The write policy used by the controller for write operations on 
        this virtual disk.
        Possible values:
        1: Write Through.
        2: Write Back.
        3: Force Write Back.
        "
    ::= { virtualDiskTableEntry 10 }

virtualDiskReadPolicy OBJECT-TYPE        
    SYNTAX          INTEGER
                    {
                    noReadAhead(1),
                    readAhead(2),
                    adaptiveReadAhead(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The read policy used by the controller for read operations on 
        this virtual disk.
        Possible values:
        1: No Read Ahead.
        2: Read Ahead.
        3: Adaptive Read Ahead.
        "
    ::= { virtualDiskTableEntry 11 }

virtualDiskLayout OBJECT-TYPE            
    SYNTAX          INTEGER
                    {
                    other(1),
                    r0(2),
                    r1(3),
                    r5(4),
                    r6(5),
                    r10(6),
                    r50(7),
                    r60(8),
                    concatRaid1(9),
                    concatRaid5(10)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The virtual disk's RAID type.
         Possible values:
         1: Not one of the following
         2: RAID-0
         3: RAID-1
         4: RAID-5
         5: RAID-6
         6: RAID-10
         7: RAID-50
         8: RAID-60
         9: Concatenated RAID 1
        10: Concatenated RAID 5
        "
    ::= { virtualDiskTableEntry 13 }

virtualDiskStripeSize OBJECT-TYPE        
    SYNTAX          INTEGER
                    {
                    other(1),
                    default(2),
                    fiveHundredAndTwelvebytes(3),
                    oneKilobytes(4),
                    twoKilobytes(5),
                    fourKilobytes(6),
                    eightKilobytes(7),
                    sixteenKilobytes(8),
                    thirtyTwoKilobytes(9),
                    sixtyFourKilobytes(10),
                    oneTwentyEightKilobytes(11),
                    twoFiftySixKilobytes(12),
                    fiveOneTwoKilobytes(13),
                    oneMegabye(14),
                    twoMegabytes(15),
                    fourMegabytes(16),
                    eightMegabytes(17),
                    sixteenMegabytes(18)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The stripe size of this virtual disk.
        Possible values:
        1: Not one of the following
        2: Default.
        3: 512 bytes
        4: 1 kB,
        5: 2 kB,
        6: 4 kB,
        7: 8 kB,
        8: 16 kB,
        9: 32 kB,
        10: 64 kB,
        11: 128 kB,
        12: 256 kB,
        13: 512 kB,
        14: 1 MB,
        15: 2 MB,
        16: 4 MB,
        17: 8 MB,
        18: 16 MB
        "
    ::= { virtualDiskTableEntry 14 }

virtualDiskComponentStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the virtual disk itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK.
        4: Non-critical .
        5: Critical.
        6: Non-recoverable.
        "
    ::= { virtualDiskTableEntry 20 }

virtualDiskBadBlocksDetected OBJECT-TYPE    
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the virtual disk has bad blocks.
        "
    ::= { virtualDiskTableEntry 23 }

virtualDiskSecured OBJECT-TYPE            
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the virtual disk is secured or not.
        "
    ::= { virtualDiskTableEntry 24 }

virtualDiskIsCacheCade OBJECT-TYPE        
    SYNTAX          BooleanType
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the virtual disk is being used as a secondary 
        cache by the controller.
        "
    ::= { virtualDiskTableEntry 25 }

virtualDiskDiskCachePolicy OBJECT-TYPE        
    SYNTAX          INTEGER
                    {
                    enabled(1),
                    disabled(2),
                    defullt(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The cache policy of the physical disks that are 
        part of this virtual disk 
        Possible values:
        1: Enabled.
        2: Disabled.
        3: Default.
        "
    ::= { virtualDiskTableEntry 26 }

virtualDiskOperationalState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    notApplicable(1),
                    reconstructing(2),
                    resynching(3),
                    initializing(4),
                    backgroundInit(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The state of the virtual disk when there are progressive
        operations ongoing.
        Possible values:
        1: There is no active operation running.
        2: The virtual disk configuration has changed.
        The physical disks included in the virtual disk are being 
        modified to support the new configuration.
        3: A Consistency Check (CC) is being performed 
        on the virtual disk.
        4: The virtual disk is being initialized.
        5: BackGround Initialization (BGI) is being performed 
        on the virtual disk.
        "
    ::= { virtualDiskTableEntry 30 }

virtualDiskProgress OBJECT-TYPE
    SYNTAX          INTEGER (0..100)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The progress percentage of the operation that is being 
        performed on the virtual disk. This is applicable 
        only if there is a progressive operations ongoing
        "
    ::= { virtualDiskTableEntry 31 }

virtualDiskAvailableProtocols OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "List of protocols support by physical disks part of this virtual
        disk. For e.g. SAS for Serial Attached SCSI or SATA for 
        Serial Advanced Technology Attachment.
        "
    ::= { virtualDiskTableEntry 32 }

virtualDiskMediaType OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "List of media types of the physical disks part of this virtual
        disk. For e.g. HDD for Hard Disk Drive or SSD for Solid State Device.
        "
    ::= { virtualDiskTableEntry 33 }

virtualDiskRemainingRedundancy OBJECT-TYPE    
    SYNTAX          INTEGER (0..2)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The number of physical disks which can be lost before the
        virtual disk loses its redundancy.
        "
    ::= { virtualDiskTableEntry 34 }

virtualDiskFQDD OBJECT-TYPE   
    SYNTAX          FQDDString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The virtual disk's Fully Qualified Device Descriptor (FQDD) as 
        represented in Storage Management.
        "
    ::= { virtualDiskTableEntry 35 }

virtualDiskDisplayName OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The virtual disk's friendly FQDD as represented in Storage 
        Management.
        "
    ::= { virtualDiskTableEntry 36 }

virtualDiskT10PIStatus OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    other(1),
                    enabled(2),
                    disabled(3)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the virtual disk supports the T10 PI (Protection 
        Information). These protection fields are known as DIF 
        (Data Integrity Fields).
        Possible values:
        1: Not one of the following.
        2: Enabled.
        3: Disabled.
        "
    ::= { virtualDiskTableEntry 37 }

virtualDiskBlockSizeInBytes OBJECT-TYPE
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The block size (in bytes) of the physical disk part of the virtual disk.
        Possible values:
        1: 512.
        2: 4096
        "
    ::= { virtualDiskTableEntry 38 }
   

-------------------------------------------------------------------------------
-- Enclosure Table
--
-- OID Format: *******.4.1.674.10892.********.130.3.1.<a>.<i1>
-------------------------------------------------------------------------------

EnclosureTableEntry                             ::=SEQUENCE { 
    enclosureNumber                             INTEGER,
    enclosureName                               DisplayString,
    enclosureState                              INTEGER,
    enclosureServiceTag                         DisplayString,
    enclosureAssetTag                           DisplayString,
    enclosureConnectedPort                      DisplayString,
    enclosureRollUpStatus                       ObjectStatusEnum,    
    enclosureComponentStatus                    ObjectStatusEnum, 
    enclosureFirmwareVersion                    DisplayString,
    enclosureSASAddress                         DisplayString,
    enclosureDriveCount                         INTEGER,
    enclosureTotalSlots                         INTEGER,
    enclosureFanCount                           DisplayString,
    enclosurePSUCount                           DisplayString,
    enclosureEMMCount                           DisplayString,
    enclosureTempProbeCount                     DisplayString,
    enclosureRedundantPath                      DisplayString,
    enclosurePosition                           DisplayString,
    enclosureBackplaneBayID                     DisplayString,
    enclosureFQDD                               FQDDString,
    enclosureDisplayName                        DisplayString,
    enclosureType	                        INTEGER
}

enclosureTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF EnclosureTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "A table of managed enclosures/backplanes. The number of entries is 
        related to number of internal backplane(s) discovered in the system 
        and external storage enclosure(s) attached to the system..
        The maximum number of entries is implementation dependent.
        Note: The properties in this table may not be applicable to all 
        entries.
        "
    ::= { physicalDevices 3 }

enclosureTableEntry OBJECT-TYPE
    SYNTAX          EnclosureTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "An entry in the enclosure table. A row in this table cannot be
        created or deleted by SNMP operations on columns of the table.
        "
    INDEX { enclosureNumber }
    ::= { enclosureTable 1 }

enclosureNumber OBJECT-TYPE    
    SYNTAX          INTEGER (1..255)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Instance number of this enclossre/backplane.
        "
    ::= { enclosureTableEntry 1 }

enclosureName OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The enclosure/backplane's name as represented in Storage Management.
        "
    ::= { enclosureTableEntry 2 }

enclosureState OBJECT-TYPE        
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    ready(2),
                    failed(3),
                    missing(4),
                    degraded(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current state of this enclosure/backplane.
        Possible states:
        1: The current state could not be determined.
        2: The enclosure is operating normally.
        3: The enclosure has encountered a hardware problem or is not 
        responding.
        4: The enclosure is no longer connected to the controller or 
        there exists a problem communicating to the enclosure.
        5: The enclosure is unstable.
        "
    ::= { enclosureTableEntry 4 }

enclosureServiceTag OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Enclosure identification used when consulting customer support.
        "
    ::= { enclosureTableEntry 8 }

enclosureAssetTag OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The asset tag information for the enclosure."
    ::= { enclosureTableEntry 9 }

enclosureConnectedPort OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The port on the controller to which the 
        storage enclosure is connected.
        "
    ::= { enclosureTableEntry 19 }

enclosureRollUpStatus OBJECT-TYPE    
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Severity of the enclosure/backplane state.
        This is the combined status of the enclosure and its sub-components.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK.
        4: Non-critical.
        5: Critical.
        6: Non-recoverable.
        "
    ::= { enclosureTableEntry 23 }

enclosureComponentStatus OBJECT-TYPE    
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the enclosure/backplane.itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK.
        4: Non-critical .
        5: Critical.
        6: Non-recoverable.
        "
    ::= { enclosureTableEntry 24 }

enclosureFirmwareVersion OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The firmware information for the enclosure/backplane.
        "
    ::= { enclosureTableEntry 26 }

enclosureSASAddress OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The SAS address of the enclosure/backplane.
        "
    ::= { enclosureTableEntry 30 }

enclosureDriveCount OBJECT-TYPE        
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The number of disks present in the enclosure/backplane.
        "    
    ::= { enclosureTableEntry 31 }

enclosureTotalSlots OBJECT-TYPE    
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The total physical drive slots in a storage enclosure 
        or server backplane.
        "
    ::= { enclosureTableEntry 32 }

enclosureFanCount OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The number of fans present in the storage enclosure.
        "    
    ::= { enclosureTableEntry 40 }

enclosurePSUCount OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The number of Power Supply Units (PSU) present 
        in the storage enclosure.
        "    
    ::= { enclosureTableEntry 41 }

enclosureEMMCount OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The number of Enclosure Management Modules (EMM) 
        present in the storage enclosure.
        "    
    ::= { enclosureTableEntry 42 }

enclosureTempProbeCount OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The number of temperature sensing devices 
        present in the storage enclosure.
        "
    ::= { enclosureTableEntry 43 }

enclosureRedundantPath OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates whether the controller has multiply paths to 
        reach the storage enclosure.
        "
    ::= { enclosureTableEntry 44 }

enclosurePosition OBJECT-TYPE        
     SYNTAX         DisplayString
     ACCESS         read-only
     STATUS         mandatory
     DESCRIPTION
         "The possition of the storage enclosure within a daisy chain.
         "
    ::= { enclosureTableEntry 45 }

enclosureBackplaneBayID OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The unique bay ID of the backplane.
        "
    ::= { enclosureTableEntry 46 }

enclosureFQDD OBJECT-TYPE        
    SYNTAX          FQDDString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The enclosure/backplane's Fully Qualified Device Descriptor (FQDD) 
        as represented in Storage Management.
        "
    ::= { enclosureTableEntry 47 }
    
enclosureDisplayName OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The enclosure/backplane's friendly FQDD as represented in 
        Storage Management.
        "
    ::= { enclosureTableEntry 48 }

enclosureType OBJECT-TYPE        
    SYNTAX          INTEGER
                    {
                    other(1),
                    notApplicable(2),
                    sassata(3),
                    pcie(4),
                    universal(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The protocol supported by the backplane.
        Possible states:
        1: Not one of the following or could not be determined.
        2: Not applicable (i.e. object is not a backplane).
        3: Supports SAS/SATA.
        4: Supports PCIe.
        5: Both SAS/SATA and PCIe.
        "
    ::= { enclosureTableEntry 49 }


-------------------------------------------------------------------------------
-- Enclosure Management Module Table
--
-- OID Format: *******.4.1.674.10892.********.130.13.1.<a>.<i1>
-------------------------------------------------------------------------------

EnclosureManagementModuleTableEntry             ::=SEQUENCE { 
    enclosureManagementModuleNumber             INTEGER,
    enclosureManagementModuleName               DisplayString,
    enclosureManagementModuleState              INTEGER,
    enclosureManagementModulePartNumber         DisplayString,
    enclosureManagementModuleFWVersion          DisplayString,
    enclosureManagementModuleComponentStatus    ObjectStatusEnum, 
    enclosureManagementModuleFQDD               FQDDString,
    enclosureManagementModuleDisplayName        DisplayString
}

enclosureManagementModuleTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF EnclosureManagementModuleTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "A table of managed Enclosure Management Modules (EMM) 
        in the external storage enclosure(s). The number of 
        entries is related to number of enclosure management modules
        discovered in the enclosure(s). The maximum number of entries 
        is implementation dependent.
        Note: The properties in this table may not be applicable to all 
        entries.
        "
    ::= { physicalDevices 13 }

enclosureManagementModuleTableEntry OBJECT-TYPE
    SYNTAX          EnclosureManagementModuleTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "An entry in the enclosure management module table. A row in 
        this table cannot be created or deleted by SNMP operations 
        on columns of the table.
        "
    INDEX { enclosureManagementModuleNumber }
    ::= { enclosureManagementModuleTable 1 }

enclosureManagementModuleNumber OBJECT-TYPE
    SYNTAX          INTEGER (1..255)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Instance number of this enclosure management module.
        "
    ::= { enclosureManagementModuleTableEntry 1 }

enclosureManagementModuleName OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The enclosure management module's name as 
        represented in Storage Management.
        "
    ::= { enclosureManagementModuleTableEntry 2 }
 
enclosureManagementModuleState OBJECT-TYPE    
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    ready(2),
                    failed(3),
                    missing(4),
                    degraded(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current state of this enclosure management module.
        Possible states:
        1: The current state could not be determined.
        2: The enclosure management module is operating normally.
        3: The enclosure management module has encountered a 
        hardware problem or is not responding.
        4: The enclosure management module is no longer connected
        to the enclosure or there exists a problem communicating to it.
        5: The enclosure management module is unstable.
        "
    ::= { enclosureManagementModuleTableEntry 4 }

enclosureManagementModulePartNumber OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The part number of the enclosure management module.
        "
    ::= { enclosureManagementModuleTableEntry 6 }
 
enclosureManagementModuleFWVersion OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Firmware version of the enclosure management module.
        "    
    ::= { enclosureManagementModuleTableEntry 8 }

enclosureManagementModuleComponentStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the enclosure management module.itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK.
        4: Non-critical .
        5: Critical.
        6: Non-recoverable.
        "
    ::= { enclosureManagementModuleTableEntry 11 }

enclosureManagementModuleFQDD OBJECT-TYPE        
    SYNTAX          FQDDString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The enclosure management module's Fully Qualified Device 
        Descriptor (FQDD) as represented in Storage Management.
        "
    ::= { enclosureManagementModuleTableEntry 15 }

enclosureManagementModuleDisplayName OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The enclosure management module's friendly FQDD as represented in 
        Storage Management.
        "
    ::= { enclosureManagementModuleTableEntry 16 }

-------------------------------------------------------------------------------
-- Enclosure Fan Table
--
-- OID Format: *******.4.1.674.10892.********.130.7.1.<a>.<i1>
-------------------------------------------------------------------------------

EnclosureFanTableEntry                          ::=SEQUENCE { 
    enclosureFanNumber                          INTEGER,
    enclosureFanName                            DisplayString,
    enclosureFanState                           INTEGER,
    enclosureFanSpeed                           INTEGER,
    enclosureFanComponentStatus                 ObjectStatusEnum, 
    enclosureFanFQDD                            FQDDString,
    enclosureFanDisplayName                     DisplayString
}

enclosureFanTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF EnclosureFanTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "A table of managed fans in the external storage enclosure(s). 
        The number of entries is related to number of fans discovered in 
        the enclosure(s). The maximum number of entries is implementation 
        dependent.
        Note: The properties in this table may not be applicable to all 
        entries.
        "
    ::= { physicalDevices 7 }
    
enclosureFanTableEntry OBJECT-TYPE
    SYNTAX          EnclosureFanTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "An entry in the fan table. A row in this table cannot be
        created or deleted by SNMP operations on columns of the table.
        "
    INDEX { enclosureFanNumber }
    ::= { enclosureFanTable 1 }

enclosureFanNumber OBJECT-TYPE
    SYNTAX          INTEGER (1..255)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Instance number of this fan.
        "
    ::= { enclosureFanTableEntry 1 }

enclosureFanName OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The fan's name as represented in Storage Management.
        "
    ::= { enclosureFanTableEntry 2 }

enclosureFanState OBJECT-TYPE
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    ready(2),
                    failed(3),
                    missing(4),
                    degraded(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current state of this fan.
        Possible states:
        1: The current state could not be determined.
        2: The fan is operating normally.
        3: The fan has encountered a hardware problem or is not 
        responding.
        4: The fan is no longer connected to the enclosure or 
        there exists a problem communicating to it.
        5: The fan is unstable.
        "
    ::= { enclosureFanTableEntry 4 }

enclosureFanSpeed OBJECT-TYPE            
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    stopped(2),
                    slow(3),
                    medium(4),
                    fast(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Indicates the current relative speed of the fan.
        "
    ::= { enclosureFanTableEntry 11 }

enclosureFanComponentStatus OBJECT-TYPE    
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the fan itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK.
        4: Non-critical .
        5: Critical.
        6: Non-recoverable.
        "
    ::= { enclosureFanTableEntry 15 }

enclosureFanFQDD OBJECT-TYPE
    SYNTAX          FQDDString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The fan's Fully Qualified Device Descriptor (FQDD) 
        as represented in Storage Management.
        "
    ::= { enclosureFanTableEntry 20 }

enclosureFanDisplayName OBJECT-TYPE
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The fan's friendly FQDD as represented in 
        Storage Management.
        "
    ::= { enclosureFanTableEntry 21 }


-------------------------------------------------------------------------------
-- Enclosure Power Supply Table
--
-- OID Format: *******.4.1.674.10892.********.130.9.1.<a>.<i1>
-------------------------------------------------------------------------------

EnclosurePowerSupplyTableEntry                 ::=SEQUENCE { 
    enclosurePowerSupplyNumber                  INTEGER,
    enclosurePowerSupplyName                    DisplayString,
    enclosurePowerSupplyState                   INTEGER,
    enclosurePowerSupplyPartNumber              DisplayString,
    enclosurePowerSupplyComponentStatus         ObjectStatusEnum, 
    enclosurePowerSupplyFQDD                    FQDDString,
    enclosurePowerSupplyDisplayName             DisplayString
}

enclosurePowerSupplyTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF EnclosurePowerSupplyTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "A table of managed Power Supply Units(PSU) in the external 
        storage enclosure(s). The number of entries is related to number 
        of power supply unit(s) discovered in the enclosure(s). The 
        maximum number of entries is implementation 
        dependent.
        Note: The properties in this table may not be applicable to all 
        entries.
        "
    ::= { physicalDevices 9 }
    
enclosurePowerSupplyTableEntry OBJECT-TYPE
    SYNTAX          EnclosurePowerSupplyTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "An entry in the power supply unit table. A row in this table cannot 
        be created or deleted by SNMP operations on columns of the table.
        "
    INDEX { enclosurePowerSupplyNumber }
    ::= { enclosurePowerSupplyTable 1 }

enclosurePowerSupplyNumber OBJECT-TYPE
    SYNTAX          INTEGER (1..255)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Instance number of this power supply unit.
        "
    ::= { enclosurePowerSupplyTableEntry 1 }

enclosurePowerSupplyName OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The power supply unit's name as represented in 
        Storage Management.
        "
    ::= { enclosurePowerSupplyTableEntry 2 }

enclosurePowerSupplyState OBJECT-TYPE        
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    ready(2),
                    failed(3),
                    missing(4),
                    degraded(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current state of this power supply unit.
        Possible states:
        1: The current state could not be determined.
        2: The power supply unit is operating normally.
        3: The power supply unit has encountered a hardware problem 
        or is not responding.
        4: The power supply unit is no longer connected to the enclosure 
        or there exists a problem communicating to it.
        5: The power supply unit is unstable.
        "
    ::= { enclosurePowerSupplyTableEntry 4 }

enclosurePowerSupplyPartNumber OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The part number of the power supply unit. 
        "
    ::= { enclosurePowerSupplyTableEntry 7 }

enclosurePowerSupplyComponentStatus OBJECT-TYPE     
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the power supply unit itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK.
        4: Non-critical .
        5: Critical.
        6: Non-recoverable.
        "
    ::= { enclosurePowerSupplyTableEntry 9 }

enclosurePowerSupplyFQDD OBJECT-TYPE        
    SYNTAX          FQDDString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The power supply unit's Fully Qualified Device Descriptor (FQDD)
        as represented in Storage Management.
        "
    ::= { enclosurePowerSupplyTableEntry 15 }

enclosurePowerSupplyDisplayName OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The power supply unit's friendly FQDD as represented in 
        Storage Management.
        "
    ::= { enclosurePowerSupplyTableEntry 16 }


-------------------------------------------------------------------------------
-- Enclosure Temperature Probe Table
--
-- OID Format: *******.4.1.674.10892.********.130.11.1.<a>.<i1>
-------------------------------------------------------------------------------

EnclosureTemperatureProbeTableEntry             ::=SEQUENCE { 
    enclosureTemperatureProbeNumber             INTEGER,
    enclosureTemperatureProbeName               DisplayString,
    enclosureTemperatureProbeState              INTEGER,
    enclosureTemperatureProbeMinWarningValue    INTEGER,
    enclosureTemperatureProbeMinCriticalValue   INTEGER,
    enclosureTemperatureProbeMaxWarningValue    INTEGER,
    enclosureTemperatureProbeMaxCriticalValue   INTEGER,
    enclosureTemperatureProbeCurValue           INTEGER,
    enclosureTemperatureProbeComponentStatus    ObjectStatusEnum, 
    enclosureTemperatureProbeFQDD               FQDDString,
    enclosureTemperatureProbeDisplayName        DisplayString
}

enclosureTemperatureProbeTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF EnclosureTemperatureProbeTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "A table of managed temperature probes in the external storage
        enclosure(s). The number of entries is related to number of 
        temperature probes discovered in the enclosure(s). The maximum 
        number of entries is implementation dependent.
        Note: The properties in this table may not be applicable to all 
        entries.
        "
    ::= { physicalDevices 11 }
    
enclosureTemperatureProbeTableEntry OBJECT-TYPE
    SYNTAX          EnclosureTemperatureProbeTableEntry
    ACCESS          not-accessible
    STATUS          mandatory
    DESCRIPTION
        "An entry in the temperature probe table. A row in this table cannot be
        created or deleted by SNMP operations on columns of the table.
        "
    INDEX { enclosureTemperatureProbeNumber }
    ::= { enclosureTemperatureProbeTable 1 }

enclosureTemperatureProbeNumber OBJECT-TYPE
    SYNTAX          INTEGER (1..255)
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "Instance number of this temperature probe.
        "
    ::= { enclosureTemperatureProbeTableEntry 1 }

enclosureTemperatureProbeName OBJECT-TYPE        
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The temperature probe's name as represented in 
        Storage Management.
        "
    ::= { enclosureTemperatureProbeTableEntry 2 }

enclosureTemperatureProbeState OBJECT-TYPE    
    SYNTAX          INTEGER
                    {
                    unknown(1),
                    ready(2),
                    failed(3),
                    missing(4),
                    degraded(5)
                    }
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The current state of this temperature probe.
        Possible states:
        1: The current state could not be determined.
        2: The temperature probe is operating normally.
        3: The temperature probe has encountered a hardware problem 
        or is not responding.
        4: The temperature probe is no longer connected to the enclosure 
        or there exists a problem communicating to it.
        5: The temperature probe is unstable.
        "
    ::= { enclosureTemperatureProbeTableEntry 4 }

enclosureTemperatureProbeMinWarningValue OBJECT-TYPE    
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The minimum temperature that will force the probe into
        a warning state.
        "
    ::= { enclosureTemperatureProbeTableEntry 7 }

enclosureTemperatureProbeMinCriticalValue OBJECT-TYPE    
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The minimum temperature that will force the probe into
        a error state.
        "
    ::= { enclosureTemperatureProbeTableEntry 8 }

enclosureTemperatureProbeMaxWarningValue OBJECT-TYPE    
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The maximum temperature that will force the probe into
        a warning state.
        "
    ::= { enclosureTemperatureProbeTableEntry 9 }

enclosureTemperatureProbeMaxCriticalValue OBJECT-TYPE    
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The maximum temperature that will force the probe into
        a warning state.
        "
    ::= { enclosureTemperatureProbeTableEntry 10 }
    
enclosureTemperatureProbeCurValue OBJECT-TYPE        
    SYNTAX          INTEGER
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The maximum temperature that will force the probe into
        a warning state.
        "
    ::= { enclosureTemperatureProbeTableEntry 11 }

enclosureTemperatureProbeComponentStatus OBJECT-TYPE
    SYNTAX          ObjectStatusEnum
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The status of the enclosure management module.itself without the 
        propagation of any contained component status.
        Possible values:
        1: Other.
        2: Unknown.
        3: OK.
        4: Non-critical .
        5: Critical.
        6: Non-recoverable.
        "
    ::= { enclosureTemperatureProbeTableEntry 13 }    
    
enclosureTemperatureProbeFQDD OBJECT-TYPE
    SYNTAX          FQDDString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The temperature probe's Fully Qualified Device Descriptor (FQDD)
        as represented in Storage Management.
        "
    ::= { enclosureTemperatureProbeTableEntry 15 }
    
enclosureTemperatureProbeDisplayName OBJECT-TYPE    
    SYNTAX          DisplayString
    ACCESS          read-only
    STATUS          mandatory
    DESCRIPTION
        "The temperature probe's friendly FQDD as represented 
        in Storage Management.
        "
    ::= { enclosureTemperatureProbeTableEntry 16 }


------------------------------------------------------------------------------
-- Alert Trap Group
--
-- OID Format: *******.4.1.674.10892.5.3.2
------------------------------------------------------------------------------


------------------------------------------------------------------------------
-- System Alert Trap Group
-- System Traps (a.k.a, System Health Traps)
-- Category: System/1
--
-- OID Format: *******.4.1.674.10892.*******
------------------------------------------------------------------------------

------------------------------------------------------------------------------
-- Amperage Probe Traps
--
-- Category: System/1
-- Subcategory: AMP/16
------------------------------------------------------------------------------

alertAmperageProbeNormal TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Current sensor reading is within range."
    --#TYPE       "System: Amperage Normal"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2179

alertAmperageProbeWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Current sensor has detected a warning value."
    --#TYPE       "System: Amperage Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2178

alertAmperageProbeFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Current sensor has detected a failure value."
    --#TYPE       "System: Amperage Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2177

------------------------------------------------------------------------------
-- Automatic System Recovery Trap
--
-- Category: System/1
-- Subcategory: ASR/23
------------------------------------------------------------------------------

alertAutomaticSystemRecovery TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Automatic system recovery (ASR) was performed."
    --#TYPE       "System: Automatic System Recovery"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2233
    
------------------------------------------------------------------------------
-- Battery Traps
--
-- Category: System/1
-- Subcategory: BAT/22
------------------------------------------------------------------------------

alertBatteryNormal TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Battery state has returned to normal;
         or battery presence had been detected."
    --#TYPE       "System: Battery Normal"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2227

alertBatteryWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Battery is low."
    --#TYPE       "System: Battery Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2226

alertBatteryFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Battery has failed or battery is absent."
    --#TYPE       "System: Battery Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2225

------------------------------------------------------------------------------
-- Cable Traps
--
-- Category: System/1
-- Subcategory: CBL/43
------------------------------------------------------------------------------

alertCableFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Cable failure or critical event."
    --#TYPE       "System: Cable Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2393

------------------------------------------------------------------------------
-- CMC Traps
--
-- Category: System/1
-- Subcategory: CMC/62
------------------------------------------------------------------------------

alertCMCWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Chassis Management Controller detected a warning."
    --#TYPE       "System: CMC Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2546

alertCMCFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Chassis Management Controller detected an error."
    --#TYPE       "System: CMC Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2545

------------------------------------------------------------------------------
-- Processor Device Status Traps
--
-- Category: System/1
-- Subcategory: CPU/24
------------------------------------------------------------------------------

alertProcessorDeviceStatusNormal TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Processor device status has returned to normal."
    --#TYPE       "System: Processor Device Status Normal"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2243

alertProcessorDeviceStatusWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Processor device status has detected a warning."
    --#TYPE       "System: Processor Device Status Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2242

alertProcessorDeviceStatusFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Processor device status has detected a failure."
    --#TYPE       "System: Processor Device Status Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2241

------------------------------------------------------------------------------
-- Processor Device Absent Trap
--
-- Category: System/1
-- Subcategory: CPUA/51
------------------------------------------------------------------------------

alertProcessorDeviceAbsent TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Processor device is absent."
    --#TYPE       "System: Processor Device Absent"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2457

------------------------------------------------------------------------------
-- Fan Traps
--
-- Category: System/1
-- Subcategory: FAN/13
------------------------------------------------------------------------------

alertFanInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fan information."
    --#TYPE       "System: Fan Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2155

alertFanWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fan warning."
    --#TYPE       "System: Fan Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2154

alertFanFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fan failure."
    --#TYPE       "System: Fan Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2153

------------------------------------------------------------------------------
-- Fiber Channel Traps
--
-- Category: System/1
-- Subcategory: FC/61
------------------------------------------------------------------------------

alertFiberChannelInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fiber Channel information."
    --#TYPE       "System: Fiber Channel Information "
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2539

alertFiberChannelWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fiber Channel warning."
    --#TYPE       "System: Fiber Channel Warning "
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2538

alertFiberChannelFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fiber Channel failure or critical event."
    --#TYPE       "System: Fiber Channel Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2537

------------------------------------------------------------------------------
-- Hardware Configuration Traps
--
-- Category: System/1
-- Subcategory: HWC/35
------------------------------------------------------------------------------

alertHardwareConfigurationInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Hardware configuration information."
    --#TYPE       "System: Hardware Configuration Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2331

alertHardwareConfigurationWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Hardware configuration warning."
    --#TYPE       "System: Hardware Configuration Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2330

alertHardwareConfigurationFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Hardware configuration failure or critical event."
    --#TYPE       "System: Hardware Configuration Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2329

------------------------------------------------------------------------------
-- IO Virtualization Traps
--
-- Category: System/1
-- Subcategory: IOV/63
------------------------------------------------------------------------------

alertIOVirtualizationFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "IO Virtualization failure or critical event."
    --#TYPE       "System: IO Virtualization Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2553

------------------------------------------------------------------------------
-- Link Status Traps
--
-- Category: System/1
-- Subcategory: LNK/25
------------------------------------------------------------------------------

alertLinkStatusInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Link status information."
    --#TYPE       "System: Link Status Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2251

alertLinkStatusWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Link status warning."
    --#TYPE       "System: Link Status Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2250

alertLinkStatusFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Link status failure or critical event."
    --#TYPE       "System: Link Status Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2249

------------------------------------------------------------------------------
-- Memory Device Traps
--
-- Category: System/1
-- Subcategory: MEM/27
------------------------------------------------------------------------------

alertMemoryDeviceInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Memory device informational event."
    --#TYPE       "System: Memory Device Infomation"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2267

alertMemoryDeviceWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Memory device status is noncritical."
    --#TYPE       "System: Memory Device Noncritical"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2266

alertMemoryDeviceFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Memory device status is critical."
    --#TYPE       "System: Memory Device Critical"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2265

------------------------------------------------------------------------------
-- NIC Traps
--
-- Category: System/1
-- Subcategory: NIC/5
------------------------------------------------------------------------------

alertNetworkInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Network information."
    --#TYPE       "System: Network Information "
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2091

alertNetworkWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Network warning."
    --#TYPE       "System: Network Warning "
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2090

alertNetworkFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Network failure or critical event."
    --#TYPE       "System: Network Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2089

------------------------------------------------------------------------------
-- Operation System ("OS") Event Traps
--
-- Category: System/1
-- Subcategory: OSE/45
------------------------------------------------------------------------------

alertOSInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "An OS graceful stop occurred;
         or an OS graceful shut-down occurred."
    --#TYPE       "System: Operating System Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2411

alertOSFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "A critical stop occurred during OS load;
         or a runtime critical stop occurred."
    --#TYPE       "System: Operating System Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2409

------------------------------------------------------------------------------
-- PCI Device Traps
--
-- Category: System/1
-- Subcategory: PCI/46
------------------------------------------------------------------------------

alertPCIDeviceInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "An informational event was detected for a PCI device."
    --#TYPE       "System: PCI Device Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2419

alertPCIDeviceWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "A warning event was detected for a PCI device."
    --#TYPE       "System: PCI Device Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2418

alertPCIDeviceFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "An error was detected for a PCI device."
    --#TYPE       "System: PCI Device Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2417

------------------------------------------------------------------------------
-- Physical Disk Traps
--
-- Category: System/1
-- Subcategory: PDR/31
------------------------------------------------------------------------------

alertPhysicalDiskInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Physical disk information."
    --#TYPE       "System: Physical Disk Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2299

alertPhysicalDiskWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Physical disk warning."
    --#TYPE       "System: Physical Disk Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2298

alertPhysicalDiskFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Physical disk failure."
    --#TYPE       "System: Physical Disk Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 2297

------------------------------------------------------------------------------
-- BIOS POST Trap
--
-- Category: System/1
-- Subcategory: PST/47
------------------------------------------------------------------------------

alertBiosPostFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "System BIOS detected a failure."
    --#TYPE       "System: BIOS POST Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 2425

------------------------------------------------------------------------------
-- Power Supply Traps
--
-- Category: System/1
-- Subcategory: PSU/17
------------------------------------------------------------------------------

alertPowerSupplyNormal TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power supply has returned to normal."
    --#TYPE       "System: Power Supply Normal"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2187

alertPowerSupplyWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power supply has detected a warning."
    --#TYPE       "System: Power Supply Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2186

alertPowerSupplyFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power supply has detected a failure."
    --#TYPE       "System: Power Supply Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2185

------------------------------------------------------------------------------
-- Power Supply Absent Trap
--
-- Category: System/1
-- Subcategory: PSUA/52
------------------------------------------------------------------------------

alertPowerSupplyAbsent TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power supply is absent."
    --#TYPE       "System: Power Supply Absent"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2465

------------------------------------------------------------------------------
-- Power Usage Traps
--
-- Category: System/1
-- Subcategory: PWR/28
------------------------------------------------------------------------------

alertPowerUsageInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "System performance restored."
    --#TYPE       "System: Power Usage Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2275

alertPowerUsageWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "System performance degraded."
    --#TYPE       "System: Power Usage Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2274

alertPowerUsageFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "The system halted because system power exceeds capacity;
         or the system performance degraded because power draw exceeds the
         power threshold."
    --#TYPE       "System: Power Usage Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2273

------------------------------------------------------------------------------
-- Redundancy Traps
--
-- Category: System/1
-- Subcategory: RDU/53
------------------------------------------------------------------------------

alertRedundancyInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Redundancy information."
    --#TYPE       "System: Redundancy Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2475

alertRedundancyDegraded TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Redundancy is degraded."
    --#TYPE       "System: Redundancy Degraded"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2474

alertRedundancyLost TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Redundancy is lost."
    --#TYPE       "System: Redundancy Lost"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2473

------------------------------------------------------------------------------
-- Integrated Dual SD Module Traps
--
-- Category: System/1
-- Subcategory: RFL/20
------------------------------------------------------------------------------

alertIntegratedDualSDModuleInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Integrated Dual SD Module information."
    --#TYPE       "System: Integrated Dual SD Module Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2211

alertIntegratedDualSDModuleWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Integrated Dual SD Module warning."
    --#TYPE       "System: Integrated Dual SD Module Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2210

alertIntegratedDualSDModuleFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Integrated Dual SD Module failure."
    --#TYPE       "System: Integrated Dual SD Module Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2209

------------------------------------------------------------------------------
-- Integrated Dual SD Module Absent Trap
--
-- Category: System/1
-- Subcategory: RFLA/54
------------------------------------------------------------------------------

alertIntegratedDualSDModuleAbsent TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Integrated Dual SD Module is absent."
    --#TYPE       "System: Integrated Dual SD Module absent."
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2481

------------------------------------------------------------------------------
-- Integrated Dual SD Module Redundancy Traps
--
-- Category: System/1
-- Subcategory: RRDU/55
------------------------------------------------------------------------------

alertIntegratedDualSDModuleRedundancyInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Integrated Dual SD Module redundancy information."
    --#TYPE       "System: Integrated Dual SD Module Redundancy Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2491

alertIntegratedDualSDModuleRedundancyDegraded TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Integrated Dual SD Module redundancy is degraded."
    --#TYPE       "System: Integrated Dual SD Module Redundancy Degraded"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2490

alertIntegratedDualSDModuleRedundancyLost TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Integrated Dual SD Module redundancy is lost."
    --#TYPE       "System: Integrated Dual SD Module Redundancy Lost"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2489

------------------------------------------------------------------------------
-- Security Event Traps
--
-- Category: System/1
-- Subcategory: SEC/42
------------------------------------------------------------------------------

alertSecurityInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Security information."
    --#TYPE       "System: Security Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2387

alertSecurityWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Security warning."
    --#TYPE       "System: Security Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2386

alertSecurityFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Security failure or critical event."
    --#TYPE       "System: Security Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2385

------------------------------------------------------------------------------
-- System Event Log Traps
--
-- Category: System/1
-- Subcategory: SEL/41
------------------------------------------------------------------------------

alertSystemEventLogInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "System Event Log information."
    --#TYPE       "System: System Event Log Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2379

alertSystemEventLogWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "System Event Log warning."
    --#TYPE       "System: System Event Log Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2378

alertSystemEventLogFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "System Event Log failure or critical event."
    --#TYPE       "System: System Event Log Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2377

------------------------------------------------------------------------------
-- Software Configuration Traps
--
-- Category: System/1
-- Subcategory: SWC/36
------------------------------------------------------------------------------

alertSoftwareConfigurationInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Software configuration information."
    --#TYPE       "System: Software Configuration Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2339

alertSoftwareConfigurationWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Software configuration warning."
    --#TYPE       "System: Software Configuration Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2338

alertSoftwareConfigurationFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Software configuration failure."
    --#TYPE       "System: Software Configuration Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2337

------------------------------------------------------------------------------
-- Temperature Probe Traps
--
-- Category: System/1
-- Subcategory: TMP/14
------------------------------------------------------------------------------

alertTemperatureProbeNormal TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature sensor value is within range."
    --#TYPE       "System: Temperature Normal"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2163

alertTemperatureProbeWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature sensor has detected a warning value."
    --#TYPE       "System: Temperature Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2162

alertTemperatureProbeFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature sensor has detected a failure value."
    --#TYPE       "System: Temperature Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2161

------------------------------------------------------------------------------
-- Temperature Statistics Traps
--
-- Category: System/1
-- Subcategory: TMPS/59
------------------------------------------------------------------------------

alertTemperatureStatisticsWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature has been above the warning or critical threshold level
         for a long enough period of time to be considered in a warning state."
    --#TYPE       "System: Temperature Statistics Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2522

alertTemperatureStatisticsFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature has been above the warning or critical threshold level
         for a long enough period of time to be considered in a critical state."
    --#TYPE       "System: Temperature Statistics Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2521

------------------------------------------------------------------------------
-- vFlash Media Device Traps
--
-- Category: System/1
-- Subcategory: VFL/57
------------------------------------------------------------------------------

alertvFlashMediaDeviceInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "vFlash Media device information."
    --#TYPE       "System: vFlash Media Device Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2507

alertvFlashMediaDeviceWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "vFlash Media device warning."
    --#TYPE       "System: vFlash Media Device Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2506

alertvFlashMediaDeviceFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "vFlash Media device failure."
    --#TYPE       "System: vFlash Media Device Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2505

------------------------------------------------------------------------------
-- vFlash Media Device Absent Trap
--
-- Category: System/1
-- Subcategory: VFLA/58
------------------------------------------------------------------------------

alertvFlashMediaDeviceAbsent TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "vFlash Media device is absent."
    --#TYPE       "System: vFlash Media Device Absent"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2515

------------------------------------------------------------------------------
-- Voltage Probe Traps
--
-- Category: System/1
-- Subcategory: VLT/15
------------------------------------------------------------------------------

alertVoltageProbeNormal TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Voltage sensor reading is within range."
    --#TYPE       "System: Voltage Normal"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2171

alertVoltageProbeWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Voltage sensor has detected a warning value."
    --#TYPE       "System: Voltage Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2170

alertVoltageProbeFailure TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Voltage sensor has detected a failure value."
    --#TYPE       "System: Voltage Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2169

------------------------------------------------------------------------------
-- RAC Traps
--
-- Category: System/1
-- Subcategory: RAC/60
------------------------------------------------------------------------------

alertRACInformation TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "RAC information."
    --#TYPE       "System: RAC information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 2531

------------------------------------------------------------------------------
-- System Performance Traps
--
-- Category: System/1
-- Subcategory: PFM/75
------------------------------------------------------------------------------

alertSystemPerformanceWarning TRAP-TYPE
    ENTERPRISE  systemAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "System Performance warning."
    --#TYPE       "System: Performance Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 2650


------------------------------------------------------------------------------
-- Storage Alert Trap Group
-- Storage Traps
-- Category: Storage/2
--
-- OID Format: *******.4.1.674.10892.*******
------------------------------------------------------------------------------

------------------------------------------------------------------------------
-- Battery Traps
--
-- Category: Storage/2
-- Subcategory: BAT/22
------------------------------------------------------------------------------

alertStorageBatteryInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Battery information."
    --#TYPE       "Storage: Battery Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4275

alertStorageBatteryWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Battery warning."
    --#TYPE       "Storage: Battery Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4274

alertStorageBatteryFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Battery failure."
    --#TYPE       "Storage: Battery Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4273

------------------------------------------------------------------------------
-- Controller Traps
--
-- Category: Storage/2
-- Subcategory: CTL/29
------------------------------------------------------------------------------

alertStorageControllerInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Controller information."
    --#TYPE       "Storage: Controller Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4331

alertStorageControllerWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Controller warning."
    --#TYPE       "Storage: Controller Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4330

alertStorageControllerFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Controller failure."
    --#TYPE       "Storage: Controller Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4329

------------------------------------------------------------------------------
-- Enclosure Traps
--
-- Category: Storage/2
-- Subcategory: ENC/30
------------------------------------------------------------------------------

alertStorageEnclosureInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Enclosure information."
    --#TYPE       "Storage: Enclosure Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4339

alertStorageEnclosureWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Enclosure warning."
    --#TYPE       "Storage: Enclosure Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4338

alertStorageEnclosureFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Enclosure failure."
    --#TYPE       "Storage: Enclosure Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4337

------------------------------------------------------------------------------
-- Fan Traps
--
-- Category: Storage/2
-- Subcategory: FAN/13
------------------------------------------------------------------------------

alertStorageFanInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fan information."
    --#TYPE       "Storage: Fan Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4203

alertStorageFanWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fan warning."
    --#TYPE       "Storage: Fan Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4202

alertStorageFanFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Fan failure."
    --#TYPE       "Storage: Fan Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4201

------------------------------------------------------------------------------
-- Physical Disk Traps
--
-- Category: Storage/2
-- Subcategory: PDR/31
------------------------------------------------------------------------------

alertStoragePhysicalDiskInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Physical disk information."
    --#TYPE       "Storage: Physical Disk Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4347

alertStoragePhysicalDiskWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Physical disk warning."
    --#TYPE       "Storage: Physical Disk Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4346

alertStoragePhysicalDiskFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Physical disk failure."
    --#TYPE       "Storage: Physical Disk Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4345

------------------------------------------------------------------------------
-- Power Supply Traps
--
-- Category: Storage/2
-- Subcategory: PSU/17
------------------------------------------------------------------------------

alertStoragePowerSupplyInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power supply information."
    --#TYPE       "Storage: Power Supply Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4235

alertStoragePowerSupplyWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power supply warning."
    --#TYPE       "Storage: Power Supply Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4234

alertStoragePowerSupplyFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power supply failure."
    --#TYPE       "Storage: Power Supply Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4233

------------------------------------------------------------------------------
-- Security Event Traps
--
-- Category: Storage/2
-- Subcategory: SEC/42
------------------------------------------------------------------------------

alertStorageSecurityInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Storage Security information."
    --#TYPE       "Storage: Security Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4435

alertStorageSecurityWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Storage Security warning."
    --#TYPE       "Storage: Security Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4434

alertStorageSecurityFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Storage Security failure or critical event."
    --#TYPE       "Storage: Security Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4433

------------------------------------------------------------------------------
-- Storage Management Status Traps
--
-- Category: Storage/2
-- Subcategory: STOR/10
------------------------------------------------------------------------------

alertStorageManagementInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Storage Management Information.
        There is no global status change associated with this trap."
    --#TYPE       "Storage: Storage Management Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4179

alertStorageManagementWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Storage Management has detected a device independent warning
        condition. There is no global status change associated with this
        trap."
    --#TYPE       "Storage: Storage Management Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4178

alertStorageManagementFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Storage Management has detected a device independent error condition.
        There is no global status change associated with this trap."
    --#TYPE       "Storage: Storage Management Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4177

------------------------------------------------------------------------------
-- Temperature Probe Traps
--
-- Category: Storage/2
-- Subcategory: TMP/14
------------------------------------------------------------------------------

alertStorageTemperatureProbeInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature probe information."
    --#TYPE       "Storage: Temperature Probe Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4211

alertStorageTemperatureProbeWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature probe warning."
    --#TYPE       "Storage: Temperature Probe Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4210

alertStorageTemperatureProbeFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Temperature probe failure."
    --#TYPE       "Storage: Temperature Probe Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4209

------------------------------------------------------------------------------
-- Virtual Disk Traps
--
-- Category: Storage/2
-- Subcategory: VDR/32
------------------------------------------------------------------------------

alertStorageVirtualDiskInformation TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Virtual disk information."
    --#TYPE       "Storage: Virtual Disk Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 4355

alertStorageVirtualDiskWarning TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Virtual disk warning."
    --#TYPE       "Storage: Virtual Disk Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 4354

alertStorageVirtualDiskFailure TRAP-TYPE
    ENTERPRISE  storageAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Virtual disk failure."
    --#TYPE       "Storage: Virtual Disk Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      FAILED
    --#STATUS     MANDATORY
    ::= 4353


------------------------------------------------------------------------------
-- Updates Alert Trap Group
-- Updates Traps
-- Category: Updates/3
--
-- OID Format: *******.4.1.674.10892.*******
------------------------------------------------------------------------------

------------------------------------------------------------------------------
-- Update Job Trap
--
-- Category: Updates/3
-- Subcategory: RED/8
------------------------------------------------------------------------------

alertUpdateJobInformation TRAP-TYPE
    ENTERPRISE  updatesAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Update job information"
    --#TYPE       "Updates: Update Job Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 6211

------------------------------------------------------------------------------
-- Software Change Traps
--
-- Category: Updates/3
-- Subcategory: SWU/21
------------------------------------------------------------------------------

alertSoftwareChangeUpdateWarning TRAP-TYPE
    ENTERPRISE  updatesAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Software change update warning."
    --#TYPE       "Updates: Software Change Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 6314


------------------------------------------------------------------------------
-- Audit Alert Trap Group
-- Audit Traps
-- Category: Audit/4
--
-- OID Format: *******.4.1.674.10892.*******
------------------------------------------------------------------------------

------------------------------------------------------------------------------
-- CMC Traps
--
-- Category: Audit/4
-- Subcategory: CMC/62
------------------------------------------------------------------------------

alertCMCAuditInformation TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Chassis Management Controller audit information."
    --#TYPE       "Audit: CMC Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 8691

alertCMCAuditWarning TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Chassis Management Controller audit warning."
    --#TYPE       "Audit: CMC Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8690

alertCMCAuditFailure TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Chassis Management Controller audit failure or critical event."
    --#TYPE       "Audit: CMC Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8689

------------------------------------------------------------------------------
-- Debug Traps
--
-- Category: Audit/4
-- Subcategory: FSD/50
------------------------------------------------------------------------------

alertDebugInformation TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Debug authorized."
    --#TYPE       "Audit: Debug Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 8595

alertDebugWarning TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Debug authorization failed."
    --#TYPE       "Audit: Debug Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8594

------------------------------------------------------------------------------
-- iDRAC IP Address Change Trap
--
-- Category: Audit/4
-- Subcategory: IPA/38
------------------------------------------------------------------------------

alertiDRACIPAddressChange TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "iDRAC IP address has changed."
    --#TYPE       "iDRAC IP address has changed"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY

    ::= 8499

------------------------------------------------------------------------------
-- License Traps
--
-- Category: Audit/4
-- Subcategory: LIC/40
------------------------------------------------------------------------------

alertLicenseInformation TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "License information."
    --#TYPE       "Audit: License Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 8515

alertLicenseWarning TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "License warning."
    --#TYPE       "Audit: License Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8514

alertLicenseFailure TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "License failure."
    --#TYPE       "Audit: License Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8513

------------------------------------------------------------------------------
-- PCI Device Traps
--
-- Category: Audit/4
-- Subcategory: PCI/46
------------------------------------------------------------------------------

alertPCIDeviceAuditWarning TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "PCI device audit warning."
    --#TYPE       "Audit: PCI Device Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8562

------------------------------------------------------------------------------
-- Power Supply Traps
--
-- Category: Audit/4
-- Subcategory: PSU/17
------------------------------------------------------------------------------

alertPowerSupplyAuditWarning TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power Supply audit warning."
    --#TYPE       "Audit: Power Supply Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8330

alertPowerSupplyAuditFailure TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power Supply audit failure or critical event."
    --#TYPE       "Audit: Power Supply Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8329

------------------------------------------------------------------------------
-- Power Usage Traps
--
-- Category: Audit/4
-- Subcategory: PWR/28
------------------------------------------------------------------------------

alertPowerUsageAuditInformation TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power usage audit information."
    --#TYPE       "Audit: Power Usage Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 8419

alertPowerUsageAuditWarning TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power usage audit warning."
    --#TYPE       "Audit: Power Usage Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8418

alertPowerUsageAuditFailure TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Power usage audit failure or critical event."
    --#TYPE       "Audit: Power Usage Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8417

------------------------------------------------------------------------------
-- System Power State Change Trap
--
-- Category: Audit/4
-- Subcategory: SYS/48
------------------------------------------------------------------------------

alertSystemPowerStateChangeInformation TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Host is going through a power state change
         (powering on or powering off)."
    --#TYPE       "Audit: System Power State Change Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 8579

------------------------------------------------------------------------------
-- User Tracking Trap
--
-- Category: Audit/4
-- Subcategory: USR/37
------------------------------------------------------------------------------

alertUserTrackingWarning TRAP-TYPE
    ENTERPRISE  auditAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "User Tracking warning."
    --#TYPE       "Audit: User Tracking"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 8490


------------------------------------------------------------------------------
-- Configuration Alert Trap Group
-- Configuration Traps
-- Category: Configuration/5
--
-- OID Format: *******.4.1.674.10892.*******
------------------------------------------------------------------------------

------------------------------------------------------------------------------
-- AutoDiscovery Traps
--
-- Category: Configuration/5
-- Subcategory: DIS/49
------------------------------------------------------------------------------

alertAutoDiscoveryInformation TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Auto discovery information."
    --#TYPE       "Configuration: Auto Discovery Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 10635

------------------------------------------------------------------------------
-- NIC Configuration Traps
--
-- Category: Configuration/5
-- Subcategory: IOID/66
------------------------------------------------------------------------------

alertNetworkConfigurationInformation TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Network configuration information."
    --#TYPE       "Configuration: Network Configuration Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 10771

alertNetworkConfigurationWarning TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Network configuration warning."
    --#TYPE       "Configuration: Network Configuration Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 10770

------------------------------------------------------------------------------
-- IP Address Traps
--
-- Category: Configuration/5
-- Subcategory: IPA/38
------------------------------------------------------------------------------

alertIPAddressConfigurationInformation TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "IP Address configuration information."
    --#TYPE       "Configuration: IP Address Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 10547

------------------------------------------------------------------------------
-- Job Control Traps
--
-- Category: Configuration/5
-- Subcategory: JCP/3
------------------------------------------------------------------------------

alertJobControlConfigurationInformation TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Job Control configuration information."
    --#TYPE       "Configuration: Job Control Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 10267

------------------------------------------------------------------------------
-- PCI Device Traps
--
-- Category: Configuration/5
-- Subcategory: PCI/46
------------------------------------------------------------------------------

alertPCIDeviceConfigurationInformation TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "PCI device configuration information."
    --#TYPE       "Configuration: PCI Device Information"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 10611

------------------------------------------------------------------------------
-- Security Event Traps
--
-- Category: Configuration/5
-- Subcategory: SEC/42
------------------------------------------------------------------------------

alertSecurityConfigurationWarning TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Security configuration warning."
    --#TYPE       "Configuration: Security Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 10578

------------------------------------------------------------------------------
-- Software Configuration Traps
--
-- Category: Configuration/5
-- Subcategory: SWC/36
------------------------------------------------------------------------------

alertSWCConfigurationWarning TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Software configuration warning."
    --#TYPE       "Configuration: Software Configuration Warning"
    --#SEVERITY   MINOR
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 10530

alertSWCConfigurationFailure TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "Software configuration failure."
    --#TYPE       "Configuration: Software Configuration Failure"
    --#SEVERITY   CRITICAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Error Events"
    --#STATE      DEGRADED
    --#STATUS     MANDATORY
    ::= 10529

------------------------------------------------------------------------------
-- Test Trap
--
-- Category: Configuration/5
-- Subcategory: TST/19
------------------------------------------------------------------------------

alertTestTrapEvent TRAP-TYPE
    ENTERPRISE  configurationAlertTrapGroup
    VARIABLES { alertMessageID,
                alertMessage,
                alertCurrentStatus,
                alertSystemServiceTag,
                alertSystemFQDN,
                alertFQDD,
                alertDeviceDisplayName,
                alertMessageArguments,
                alertChassisServiceTag,
                alertChassisName,
                alertRacFQDN }
    DESCRIPTION
        "The iDRAC generated a test trap event in response to a user request."
    --#TYPE       "Configuration: iDRAC Test Trap Event"
    --#SEVERITY   INFORMATIONAL
    --#SUMMARY    "Message ID: %s, Device Display Name: %s, Message: %s,"
    --#SUMMARY    "Device Status: %d, Device FQDD: %s,"
    --#SUMMARY    "System Service Tag: %s, System Name: %s,"
    --#SUMMARY    "Chassis Service Tag: %s, Chassis Name: %s,"
    --#SUMMARY    "RAC FQDN: %s"
    --#ARGUMENTS  {0, 6, 1, 2, 5, 3, 4, 8, 9, 10}
    --#CATEGORY   "Status Events"
    --#STATE      OPERATIONAL
    --#STATUS     MANDATORY
    ::= 10395

END

------------------------------------------------------------------------------
-- End MIB
------------------------------------------------------------------------------

