HP-SN-IGMP-MIB DEFINITIONS ::= BEGIN


-- Foundry IGMP Group MIB Release 1.0.0
-- Revision 0 2/13/98

-- Copyright 1996-98 Foundry Networks, Inc.
-- All rights reserved.
-- This Foundry Networks SNMP Management Information Base 
-- Specification embodies Foundry Networks' confidential and
-- proprietary intellectual property. Foundry Networks retains all
-- title and ownership in the Specification, including any
-- revisions.

-- This Specification is supplied "AS IS," and Foundry Networks makes
-- no warranty, either express or implied, as to the use,
-- operation, condition, or performance of the Specification.

-- SECTION 1: Top Level Definitions


-- Imports

IMPORTS
	IpAddress
		FROM RFC1155-SMI
	OBJECT-TYPE
		FROM RFC-1212
	snIgmp
		FROM HP-SN-ROOT-MIB;


-- SECTION 2: MIB 

-- IGMP MIB

snIgmpMIBObjects OBJECT IDENTIFIER ::= { snIgmp 1 }

snIgmpQueryInterval OBJECT-TYPE
    SYNTAX     INTEGER (1..3600)
    ACCESS     read-write
    STATUS     mandatory
    DESCRIPTION
            "This is the time interval at which this entity sends out
			IGMP host query packets. For a router, snDvmrpEnable must
			have been set to 'enabled' before this object is writen.
			For a switch, snSwGroupIpMcastMode must have been set to
			'enabled' and snSwIpMcastQuerierMode must have been set
			to 'querier' before this object is writen."
    DEFVAL     { 60 }
    ::= { snIgmpMIBObjects 1 }

snIgmpGroupMembershipTime OBJECT-TYPE
    SYNTAX     INTEGER (1..7200)
    ACCESS     read-write
    STATUS     mandatory
    DESCRIPTION
            "This is the time interval after which a group is considered
            inactive. For a router, snDvmrpEnable must have been set to
            'enabled' before this object is writen. For a switch,
            snSwGroupIpMcastMode must have been set to 'enabled' before
            this object is writen."
    DEFVAL     { 140 }
    ::= { snIgmpMIBObjects 2 }

-- The IGMP Interface Table

snIgmpIfTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF SnIgmpIfEntry
    ACCESS     not-accessible
    STATUS     mandatory
    DESCRIPTION
            "This table comprises of a list of IGMP interface entries."
    ::= { snIgmpMIBObjects 3 }

snIgmpIfEntry OBJECT-TYPE
    SYNTAX     SnIgmpIfEntry
    ACCESS     not-accessible
    STATUS     mandatory
    DESCRIPTION
            "An entry (conceptual row) contains the group
            membership information for that port."
    INDEX      { snIgmpIfEntryIndex }
    ::= { snIgmpIfTable 1 }

SnIgmpIfEntry ::= SEQUENCE {
    snIgmpIfEntryIndex         INTEGER,
    snIgmpIfPortNumber         INTEGER,
    snIgmpIfGroupAddress       IpAddress,
    snIgmpIfGroupAge           INTEGER }

snIgmpIfEntryIndex OBJECT-TYPE
    SYNTAX     INTEGER
    ACCESS     read-only
    STATUS     mandatory
    DESCRIPTION
            "The table entry index."
    ::= { snIgmpIfEntry 1 }

snIgmpIfPortNumber OBJECT-TYPE
    SYNTAX     INTEGER
    ACCESS     read-only
    STATUS     mandatory
    DESCRIPTION
            "This is the interface on which the group was learnt."
    ::= { snIgmpIfEntry 2 }

snIgmpIfGroupAddress OBJECT-TYPE
    SYNTAX     IpAddress
    ACCESS     read-only
    STATUS     mandatory
    DESCRIPTION
            "The group address learnt from the interface."
    ::= { snIgmpIfEntry 3 }

snIgmpIfGroupAge OBJECT-TYPE
    SYNTAX     INTEGER
    ACCESS     read-only
    STATUS     mandatory
    DESCRIPTION
            "This is the interval (seconds) after which the group
            information will be aged out."
    ::= { snIgmpIfEntry 4 }

-- The IGMP Static Group Table

snIgmpStaticGroupTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF SnIgmpStaticGroupEntry
    ACCESS     not-accessible
    STATUS     mandatory
    DESCRIPTION
            "This table comprises of a list of IGMP static group entries."
    ::= { snIgmpMIBObjects 4 }

snIgmpStaticGroupEntry OBJECT-TYPE
    SYNTAX     SnIgmpStaticGroupEntry
    ACCESS     not-accessible
    STATUS     mandatory
    DESCRIPTION
            "An entry (conceptual row) contains the IGMP static group
            membership information."
    INDEX      { snIgmpStaticGroupIfIndex, snIgmpStaticGroupAddress }
    ::= { snIgmpStaticGroupTable 1 }

SnIgmpStaticGroupEntry ::= SEQUENCE {
    snIgmpStaticGroupIfIndex    INTEGER,
    snIgmpStaticGroupAddress    IpAddress,
    snIgmpStaticGroupPortList   OCTET STRING,
    snIgmpStaticGroupRowStatus  INTEGER }

snIgmpStaticGroupIfIndex OBJECT-TYPE
    SYNTAX     INTEGER
    ACCESS     read-only
    STATUS     mandatory
    DESCRIPTION
            "The interface of which the static group was configured."
    ::= { snIgmpStaticGroupEntry 1 }

snIgmpStaticGroupAddress OBJECT-TYPE
    SYNTAX     IpAddress
    ACCESS     read-only
    STATUS     mandatory
    DESCRIPTION
            "The group address of which the static group was configured."
    ::= { snIgmpStaticGroupEntry 2 }

snIgmpStaticGroupPortList OBJECT-TYPE
    SYNTAX     OCTET STRING
    ACCESS     read-write
    STATUS     mandatory
    DESCRIPTION
            "A list of ports which are the port membership of the
            static group. Each port is a 16-bit integer ifIndex."
    ::= { snIgmpStaticGroupEntry 3 }

snIgmpStaticGroupRowStatus OBJECT-TYPE
    SYNTAX     INTEGER {
                 other(1),
                 valid(2),
                 delete(3),
                 create(4),
                 modify(5) }
    ACCESS     read-write
    STATUS     mandatory
    DESCRIPTION
            "To create or delete a static group entry."
    ::= { snIgmpStaticGroupEntry 4 }

END
