HP-ENTITY-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE
        FROM SNMPv2-<PERSON><PERSON>
    TDomain, TAddress, Display<PERSON>tring, TEXTUAL-CONVENTION,
    AutonomousType, RowPointer, TimeStamp
        FROM SNMPv2-TC
    MODULE-COMPLIANCE, OB<PERSON>ECT-<PERSON><PERSON><PERSON>, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    icf
        FROM HP-ICF-OID;

hpEntityMIB MODULE-IDENTITY
    LAST-UPDATED "200011030636Z"  -- November 3, 2000
    ORGANIZATION "Hewlett Packard Company,
                  Network Infrastructure Solutions"
    CONTACT-INFO
            "Hewlett Packard Company
             8000 Foothills Blvd.
             Roseville, CA 95747"
    DESCRIPTION
            "The MIB module for representing multiple logical
            entities supported by a single SNMP agent.

            This is an exact copy of draft 7 of the IETF
            Entity MIB.  The only changes are to actually
            assign an OID to it, and add hp to the beginning
            of all the labels.  This was done only because
            a product that uses this MIB is scheduled to ship
            before the IETF MIB will be published as an RFC,
            and we needed to give it an OID.

            It is expected that this MIB module will only be
            supported until the IETF actually publishes the
            official version as an RFC.  At that time, we will
            support the IETF version of this MIB."

    REVISION     "200011030636Z" -- November 3, 2000
    DESCRIPTION
            "Update division name.  Since this MIB was only
            supported on a single product, and all subsequent
            products now support the IETF Entity MIB, mark the
            entire MIB as obsolete."

    REVISION     "9703060326Z"  -- March 6, 1997
    DESCRIPTION
            "Uncommented NOTIFICATION-GROUP, and add import."

    REVISION     "9609062135Z"  -- September 6, 1996
    DESCRIPTION
            "Initial (and probably only) revision of this MIB module.
            Released with the AdvanceStack 10BaseT Switching Hubs."
    ::= { icf 9 }

hpEntityMIBObjects OBJECT IDENTIFIER ::= { hpEntityMIB 1 }

-- MIB contains four groups











hpEntityPhysical OBJECT IDENTIFIER ::= { hpEntityMIBObjects 1 }
hpEntityLogical  OBJECT IDENTIFIER ::= { hpEntityMIBObjects 2 }
hpEntityMapping  OBJECT IDENTIFIER ::= { hpEntityMIBObjects 3 }
hpEntityGeneral  OBJECT IDENTIFIER ::= { hpEntityMIBObjects 4 }


-- Textual Conventions
PhysicalIndex ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
            "An arbitrary value which uniquely identifies the physical
            entity.  The value is a small positive integer; index values
            for different physical entities are not necessarily
            contiguous."
    SYNTAX          INTEGER (1..2147483647)


PhysicalClass ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
            "An enumerated value which provides an indication of the
            general hardware type of a particular physical entity."
    SYNTAX      INTEGER  {
        other(1),
        unknown(2),
        chassis(3),
        backplane(4),
        container(5),   -- e.g. slot or daughter-card holder
        powerSupply(6),
        fan(7),
        sensor(8),
        module(9),      -- e.g. plug-in card or daughter-card
        port(10)
    }






















--           The Physical Entity Table

hpEntPhysicalTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HpEntPhysicalEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "This table contains one row per physical entity.  There is
            always at least one row for an 'overall' physical entity."
    ::= { hpEntityPhysical 1 }

hpEntPhysicalEntry       OBJECT-TYPE
    SYNTAX      HpEntPhysicalEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "Information about a particular physical entity.

            Each entry provides objects (hpEntPhysicalDescr,
            hpEntPhysicalVendorType, and hpEntPhysicalClass) to help an NMS
            identify and characterize the entry, and objects
            (hpEntPhysicalContainedIn and hpEntPhysicalParentRelPos) to help
            an NMS relate the particular entry to other entries in this
            table."
    INDEX   { hpEntPhysicalIndex }
    ::= { hpEntPhysicalTable 1 }

HpEntPhysicalEntry ::= SEQUENCE {
      hpEntPhysicalIndex          PhysicalIndex,
      hpEntPhysicalDescr          DisplayString,
      hpEntPhysicalVendorType     AutonomousType,
      hpEntPhysicalContainedIn    INTEGER,
      hpEntPhysicalClass          PhysicalClass,
      hpEntPhysicalParentRelPos   INTEGER,
      hpEntPhysicalName           DisplayString
}

hpEntPhysicalIndex    OBJECT-TYPE
    SYNTAX      PhysicalIndex
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "The index for this entry."
    ::= { hpEntPhysicalEntry 1 }












hpEntPhysicalDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "A textual description of physical entity.  This object
            should contain a string which identifies the manufacturer's
            name for the physical entity, and should be set to a
            distinct value for each version or model of the physical
            entity. "
    ::= { hpEntPhysicalEntry 2 }

hpEntPhysicalVendorType OBJECT-TYPE
    SYNTAX      AutonomousType
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "An indication of the vendor-specific hardware type of the
            physical entity. Note that this is different from the
            definition of MIB-II's sysObjectID.

            An agent should set this object to a enterprise-specific
            registration identifier value indicating the specific
            equipment type in detail.  The associated instance of
            hpEntPhysicalClass is used to indicate the general type of
            hardware device.

            If no vendor-specific registration identifier exists for
            this physical entity, or the value is unknown by this agent,
            then the value { 0 0 } is returned."
    ::= { hpEntPhysicalEntry 3 }

hpEntPhysicalContainedIn OBJECT-TYPE
    SYNTAX      INTEGER (0..2147483647)
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "The value of hpEntPhysicalIndex for the physical entity which
            'contains' this physical entity.  A value of zero indicates
            this physical entity is not contained in any other physical
            entity.  Note that the set of 'containment' relationships
            define a strict hierarchy; that is, recursion is not
            allowed."
    ::= { hpEntPhysicalEntry 4 }












hpEntPhysicalClass OBJECT-TYPE
    SYNTAX      PhysicalClass
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "An indication of the general hardware type of the physical
            entity.

            An agent should set this object to the standard enumeration
            value which most accurately indicates the general class of
            the physical entity, or the primary class if there is more
            than one.

            If no appropriate standard registration identifier exists
            for this physical entity, then the value 'other(1)' is
            returned. If the value is unknown by this agent, then the
            value 'unknown(2)' is returned."
    ::= { hpEntPhysicalEntry 5 }

hpEntPhysicalParentRelPos OBJECT-TYPE
    SYNTAX      INTEGER (-1..2147483647)
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "An indication of the relative position of this 'child'
            component among all its 'sibling' components. Sibling
            components are defined as hpEntPhysicalEntries which share the
            same instance values of each of the hpEntPhysicalContainedIn
            and hpEntPhysicalClass objects.

            An NMS can use this object to identify the relative ordering
            for all sibling components of a particular parent
            (identified by the hpEntPhysicalContainedIn instance in each
            sibling entry).

            This value should match any external labeling of the
            physical component if possible. For example, for a module
            labeled as 'card #3', hpEntPhysicalParentRelPos should have
            the value '3'.

            If the physical position of this component does not match
            any external numbering or clearly visible ordering, then
            user documentation or other external reference material
            should be used to determine the parent-relative position. If
            this is not possible, then the the agent should assign a











            consistent (but possibly arbitrary) ordering to a given set
            of 'sibling' components, perhaps based on internal
            representation of the components.

            If the agent cannot determine the parent-relative position
            for some reason, or if the associated value of
            hpEntPhysicalContainedIn is '0', then the value '-1' is
            returned. Otherwise a non-negative integer is returned,
            indicating the parent-relative position of this physical
            entity.

            Parent-relative ordering normally starts from '1' and
            continues to 'N', where 'N' represents the highest
            positioned child entity.  However, if the physical entities
            (e.g. slots) are labeled from a starting position of zero,
            then the first sibling should be associated with a
            hpEntPhysicalParentRelPos value of '0'.  Note that this
            ordering may be sparse or dense, depending on agent
            implementation.

            The actual values returned are not globally meaningful, as
            each 'parent' component may use different numbering
            algorithms. The ordering is only meaningful among siblings
            of the same parent component.

            The agent should retain parent-relative position values
            across reboots, either through algorithmic assignment or use
            of non-volatile storage."
    ::= { hpEntPhysicalEntry 6 }


hpEntPhysicalName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "The textual name of the physical entity.  The value of this
            object should be the name of the component as assigned by
            the local device and should be suitable for use in commands
            entered at the device's `console'.  This might be a text
            name, such as `console' or a simple component number (e.g.
            port or module number), such as `1', depending on the
            physical component naming syntax of the device.

            If there is no local name, or this object is otherwise not











            applicable, then this object contains a zero-length string.

            Note that the value of hpEntPhysicalName for two physical
            entities will be the same in the event that the console
            interface does not distinguish between them, e.g., slot-1
            and the card in slot-1."
    ::= { hpEntPhysicalEntry 7 }

















































--           The Logical Entity Table
hpEntLogicalTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HpEntLogicalEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "This table contains one row per logical entity.  At least
            one entry must exist."
    ::= { hpEntityLogical 1 }

hpEntLogicalEntry       OBJECT-TYPE
    SYNTAX      HpEntLogicalEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "Information about a particular logical entity.  Entities
            may be managed by this agent or other SNMP agents (possibly)
            in the same chassis."
    INDEX       { hpEntLogicalIndex }
    ::= { hpEntLogicalTable 1 }

HpEntLogicalEntry ::= SEQUENCE {
      hpEntLogicalIndex            INTEGER,
      hpEntLogicalDescr            DisplayString,
      hpEntLogicalType             AutonomousType,
      hpEntLogicalCommunity        OCTET STRING,
      hpEntLogicalTAddress         TAddress,
      hpEntLogicalTDomain          TDomain
}

hpEntLogicalIndex OBJECT-TYPE
    SYNTAX      INTEGER (1..2147483647)
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "The value of this object uniquely identifies the logical
            entity. The value is a small positive integer; index values
            for different logical entities are are not necessarily
            contiguous."
    ::= { hpEntLogicalEntry 1 }

hpEntLogicalDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      obsolete











    DESCRIPTION
            "A textual description of the logical entity.  This object
            should contain a string which identifies the manufacturer's
            name for the logical entity, and should be set to a distinct
            value for each version of the logical entity. "
    ::= { hpEntLogicalEntry 2 }

hpEntLogicalType OBJECT-TYPE
    SYNTAX      AutonomousType
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "An indication of the type of logical entity.  This will
            typically be the OBJECT IDENTIFIER name of the node in the
            SMI's naming hierarchy which represents the major MIB
            module, or the majority of the MIB modules, supported by the
            logical entity.  For example:
               a logical entity of a regular host/router -> mib-2
               a logical entity of a 802.1d bridge -> dot1dBridge
               a logical entity of a 802.3 repeater -> snmpDot3RptrMgmt
            If an appropriate node in the SMI's naming hierarchy cannot
            be identified, the value 'mib-2' should be used."
    ::= { hpEntLogicalEntry 3 }

hpEntLogicalCommunity OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (1..255))
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "An SNMPv1 or SNMPv2C community-string which can be used to
            access detailed management information for this logical
            entity.  The agent should allow read access with this
            community string (to an appropriate subset of all managed
            objects) and may also choose to return a community string
            based on the privileges of the request used to read this
            object.  Note that an agent may choose to return a community
            string with read-only privileges, even if this object is
            accessed with a read-write community string. However, the
            agent must take care not to return a community string which
            allows more privileges than the community string used to
            access this object.

            A compliant SNMP agent may wish to conserve naming scopes by
            representing multiple logical entities in a single 'main'
            naming scope.  This is possible when the logical entities











            represented by the same value of hpEntLogicalCommunity have no
            object instances in common.  For example, 'bridge1' and
            'repeater1' may be part of the main naming scope, but at
            least one additional community string is needed to represent
            'bridge2' and 'repeater2'.

            Logical entities 'bridge1' and 'repeater1' would be
            represented by sysOREntries associated with the 'main'
            naming scope.

            For agents not accessible via SNMPv1 or SNMPv2C, the value
            of this object is the empty-string."
    ::= { hpEntLogicalEntry 4 }

hpEntLogicalTAddress OBJECT-TYPE
    SYNTAX      TAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "The transport service address by which the logical entity
            receives network management traffic, formatted according to
            the corresponding value of hpEntLogicalTDomain.

            For snmpUDPDomain, a TAddress is 6 octets long, the initial
            4 octets containing the IP-address in network-byte order and
            the last 2 containing the UDP port in network-byte order.
            Consult 'Transport Mappings for Version 2 of the Simple
            Network Management Protocol' (RFC 1906 [8]) for further
            information on snmpUDPDomain."
    ::= { hpEntLogicalEntry 5 }

hpEntLogicalTDomain OBJECT-TYPE
    SYNTAX      TDomain
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "Indicates the kind of transport service by which the
            logical entity receives network management traffic.
            Possible values for this object are presently found in the
            Transport Mappings for SNMPv2 document (RFC 1906 [8])."
    ::= { hpEntLogicalEntry 6 }















hpEntLPMappingTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HpEntLPMappingEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "This table contains zero or more rows of logical entity to
            physical equipment associations. For each logical entity
            known by this agent, there are zero or more mappings to the
            physical resources which are used to realize that logical
            entity.

            An agent should limit the number and nature of entries in
            this table such that only meaningful and non-redundant
            information is returned. For example, in a system which
            contains a single power supply, mappings between logical
            entities and the power supply are not useful and should not
            be included.

            Also, only the most appropriate physical component which is
            closest to the root of a particular containment tree should
            be identified in an hpEntLPMapping entry.

            For example, suppose a bridge is realized on a particular
            module, and all ports on that module are ports on this
            bridge. A mapping between the bridge and the module would be
            useful, but additional mappings between the bridge and each
            of the ports on that module would be redundant (since the
            hpEntPhysicalContainedIn hierarchy can provide the same
            information). If, on the other hand, more than one bridge
            was utilizing ports on this module, then mappings between
            each bridge and the ports it used would be appropriate.

            Also, in the case of a single backplane repeater, a mapping
            for the backplane to the single repeater entity is not
            necessary."
    ::= { hpEntityMapping 1 }

hpEntLPMappingEntry       OBJECT-TYPE
    SYNTAX      HpEntLPMappingEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "Information about a particular logical entity to physical
            equipment association. Note that the nature of the
            association is not specifically identified in this entry. It











            is expected that sufficient information exists in the MIBs
            used to manage a particular logical entity to infer how
            physical component information is utilized."
    INDEX       { hpEntLogicalIndex, hpEntLPPhysicalIndex }
    ::= { hpEntLPMappingTable 1 }

HpEntLPMappingEntry ::= SEQUENCE {
      hpEntLPPhysicalIndex         PhysicalIndex
}

hpEntLPPhysicalIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "The value of this object identifies the index value of a
            particular hpEntPhysicalEntry associated with the indicated
            hpEntLogicalEntity."
    ::= { hpEntLPMappingEntry 1 }





































-- logical entity/component to alias table
hpEntAliasMappingTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HpEntAliasMappingEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "This table contains zero or more rows, representing
            mappings of logical entity and physical component to
            external MIB identifiers.  Each physical port in the system
            may be associated with a mapping to an external identifier,
            which itself is associated with a particular logical
            entity's naming scope. A 'wildcard' mechanism is provided to
            indicate that an identifier is associated with more than one
            logical entity."
    ::= { hpEntityMapping 2 }

hpEntAliasMappingEntry       OBJECT-TYPE
    SYNTAX      HpEntAliasMappingEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "Information about a particular physical equipment, logical
            entity to external identifier binding. Each logical
            entity/physical component pair may be associated with one
            alias mapping.  The logical entity index may also be used as
            a 'wildcard' (refer to the hpEntAliasLogicalIndexOrZero object
            DESCRIPTION clause for details.)

            Note that only hpEntPhysicalIndex values which represent
            physical ports (i.e. associated hpEntPhysicalClass value is
            'port(10)') are permitted to exist in this table."
    INDEX { hpEntPhysicalIndex, hpEntAliasLogicalIndexOrZero }
    ::= { hpEntAliasMappingTable 1 }

HpEntAliasMappingEntry ::= SEQUENCE {
      hpEntAliasLogicalIndexOrZero        INTEGER,
      hpEntAliasMappingIdentifier         RowPointer
}

hpEntAliasLogicalIndexOrZero OBJECT-TYPE
    SYNTAX      INTEGER (0..2147483647)
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "The value of this object uniquely identifies the logical











            entity which defines the naming scope for the associated
            instance of the 'hpEntAliasMappingIdentifier' object.

            If this object has a non-zero value, then it identifies the
            logical entity named by the same value of hpEntLogicalIndex.

            If this object has a value of zero, then the mapping between
            the physical component and the alias identifier for this
            hpEntAliasMapping entry is associated with all unspecified
            logical entities. That is, a value of zero (the default
            mapping) identifies any logical entity which does not have
            an explicit entry in this table for a particular
            hpEntPhysicalIndex/hpEntAliasMappingIdentifier pair.

            For example, to indicate that a particular interface (e.g.
            physical component 33) is identified by the same value of
            ifIndex for all logical entities, the following instance
            might exist:

                    hpEntAliasMappingIdentifier.33.0 = ifIndex.5

            In the event an hpEntPhysicalEntry is associated differently
            for some logical entities, additional hpEntAliasMapping
            entries may exist, e.g.:

                    hpEntAliasMappingIdentifier.33.0 = ifIndex.6
                    hpEntAliasMappingIdentifier.33.4 =  ifIndex.1
                    hpEntAliasMappingIdentifier.33.5 =  ifIndex.1
                    hpEntAliasMappingIdentifier.33.10 = ifIndex.12

            Note that entries with non-zero hpEntAliasLogicalIndexOrZero
            index values have precedence over any zero-indexed entry. In
            this example, all logical entities except 4, 5, and 10,
            associate physical entity 33 with ifIndex.6."
    ::= { hpEntAliasMappingEntry 1 }


hpEntAliasMappingIdentifier OBJECT-TYPE
    SYNTAX      RowPointer
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "The value of this object identifies a particular conceptual
            row associated with the indicated hpEntPhysicalIndex and
            hpEntLogicalIndex pair.











            Since only physical ports are modeled in this table, only
            entries which represent interfaces or ports are allowed.  If
            an ifEntry exists on behalf of a particular physical port,
            then this object should identify the associated 'ifEntry'.
            For repeater ports, the appropriate row in the
            'rptrPortGroupTable' should be identified instead.

            For example, suppose a physical port was represented by
            hpEntPhysicalEntry.3, hpEntLogicalEntry.15 existed for a
            repeater, and hpEntLogicalEntry.22 existed for a bridge.  Then
            there might be two related instances of
            hpEntAliasMappingIdentifier:
               hpEntAliasMappingIdentifier.3.15 == rptrPortGroupIndex.5.2
               hpEntAliasMappingIdentifier.3.22 == ifIndex.17
            It is possible that other mappings (besides interfaces and
            repeater ports) may be defined in the future, as required.

            Bridge ports are identified by examining the Bridge MIB and
            appropriate ifEntries associated with each 'dot1dBasePort',
            and are thus not represented in this table."
    ::= { hpEntAliasMappingEntry 2 }



































-- physical mapping table
hpEntPhysicalContainsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HpEntPhysicalContainsEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "A table which exposes the container/containee relationships
            between physical entities. This table provides equivalent
            information found by constructing the virtual containment
            tree for a given hpEntPhysicalTable but in a more direct
            format."
    ::= { hpEntityMapping 3 }

hpEntPhysicalContainsEntry OBJECT-TYPE
    SYNTAX      HpEntPhysicalContainsEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "A single container/containee relationship."
    INDEX       { hpEntPhysicalIndex, hpEntPhysicalChildIndex }
    ::= { hpEntPhysicalContainsTable 1 }

HpEntPhysicalContainsEntry ::= SEQUENCE {
      hpEntPhysicalChildIndex     PhysicalIndex
}

hpEntPhysicalChildIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "The value of hpEntPhysicalIndex for the contained physical
            entity."
    ::= { hpEntPhysicalContainsEntry 1 }






















-- last change time stamp for the whole MIB
hpEntLastChangeTime OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
            "The value of sysUpTime at the time any of these events
            occur:
                * a conceptual row is created or deleted in any
                  of these tables:
                    - hpEntPhysicalTable
                    - hpEntLogicalTable
                    - hpEntLPMappingTable
                    - hpEntAliasMappingTable
                    - hpEntPhysicalContainsTable

                * any instance in the following list of objects
                  changes value:
                    - hpEntPhysicalDescr
                    - hpEntPhysicalVendorType
                    - hpEntPhysicalContainedIn
                    - hpEntPhysicalClass
                    - hpEntPhysicalParentRelPos
                    - hpEntPhysicalName
                    - hpEntLogicalDescr
                    - hpEntLogicalType
                    - hpEntLogicalCommunity
                    - hpEntLogicalTAddress
                    - hpEntLogicalTDomain
                    - hpEntAliasMappingIdentifier "
    ::= { hpEntityGeneral 1 }

























-- Entity MIB Trap Definitions
hpEntityMIBTraps      OBJECT IDENTIFIER ::= { hpEntityMIB 2 }
hpEntityMIBTrapPrefix OBJECT IDENTIFIER ::= { hpEntityMIBTraps 0 }

hpEntConfigChange NOTIFICATION-TYPE
    STATUS             obsolete
    DESCRIPTION
            "An hpEntConfigChange trap is sent when the value of
            hpEntLastChangeTime changes. It can be utilized by an NMS to
            trigger logical/physical entity table maintenance polls.

            An agent must not generate more than one hpEntConfigChange
            'trap-event' in a five second period, where a 'trap-event'
            is the transmission of a single trap PDU to a list of trap
            destinations.  If additional configuration changes occur
            within the five second 'throttling' period, then these
            trap-events should be suppressed by the agent. An NMS should
            periodically check the value of hpEntLastChangeTime to detect
            any missed hpEntConfigChange trap-events, e.g. due to
            throttling or transmission loss."
   ::= { hpEntityMIBTrapPrefix 1 }



































-- conformance information
hpEntityConformance OBJECT IDENTIFIER ::= { hpEntityMIB 3 }

hpEntityCompliances OBJECT IDENTIFIER ::= { hpEntityConformance 1 }
hpEntityGroups      OBJECT IDENTIFIER ::= { hpEntityConformance 2 }

-- compliance statements

hpEntityCompliance MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for SNMP entities which implement
            the Entity MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { hpEntityPhysicalGroup,
                           hpEntityLogicalGroup,
                           hpEntityMappingGroup,
                           hpEntityGeneralGroup,
                           hpEntityNotificationsGroup }
    ::= { hpEntityCompliances 1 }

-- MIB groupings

hpEntityPhysicalGroup    OBJECT-GROUP
    OBJECTS {
              hpEntPhysicalDescr,
              hpEntPhysicalVendorType,
              hpEntPhysicalContainedIn,
              hpEntPhysicalClass,
              hpEntPhysicalParentRelPos,
              hpEntPhysicalName
            }
    STATUS  obsolete
    DESCRIPTION
            "The collection of objects which are used to represent
            physical system components, for which a single agent
            provides management information."
    ::= { hpEntityGroups 1 }

hpEntityLogicalGroup    OBJECT-GROUP
    OBJECTS {
              hpEntLogicalDescr,
              hpEntLogicalType,
              hpEntLogicalCommunity,
              hpEntLogicalTAddress,











              hpEntLogicalTDomain
            }
    STATUS  obsolete
    DESCRIPTION
            "The collection of objects which are used to represent the
            list of logical entities for which a single agent provides
            management information."
    ::= { hpEntityGroups 2 }

hpEntityMappingGroup    OBJECT-GROUP
    OBJECTS {
              hpEntLPPhysicalIndex,
              hpEntAliasMappingIdentifier,
              hpEntPhysicalChildIndex
            }
    STATUS  obsolete
    DESCRIPTION
            "The collection of objects which are used to represent the
            associations between multiple logical entities, physical
            components, interfaces, and port identifiers for which a
            single agent provides management information."
    ::= { hpEntityGroups 3 }

hpEntityGeneralGroup    OBJECT-GROUP
    OBJECTS {
              hpEntLastChangeTime
            }
    STATUS  obsolete
    DESCRIPTION
            "The collection of objects which are used to represent
            general entity information for which a single agent provides
            management information."
    ::= { hpEntityGroups 4 }

hpEntityNotificationsGroup NOTIFICATION-GROUP
    NOTIFICATIONS { hpEntConfigChange }
    STATUS        obsolete
    DESCRIPTION
            "The collection of notifications used to indicate Entity MIB
            data consistency and general status information."
    ::= { hpEntityGroups 5 }


END
