




-- ************************************************************** --
-- 			LAC MIB					   --
--                                                                 --
-- This MIB makes reference to two new terms: wireless port and    --
-- wireless interface.  A wireless port refers to the physical     --
-- connection between a LAC and an access point, together with the --
-- access point chassis.  Thus a wireless port is the unit of mani --
-- pulation for images, version, etc.                              --
--                                                                 --
-- A wireless interface is a channel or antenna on a single wirele --
-- ss port.  There can be one or more wireless interfaces on a     --
-- wireless port.                                                  --
-- ************************************************************** --


EXTREME-WIRELESS-MIB DEFINITIONS ::= BEGIN

	IMPORTS 
		ifIndex			FROM IF-MIB
		InterfaceIndex          FROM IF-MIB
        MODULE-IDENTITY 	FROM SNMPv2-SMI
		OBJECT-TYPE     	FROM SNMPv2-SMI
		NOTIFICATION-TYPE	FROM SNMPv2-SMI
		RowStatus		FROM SNMPv2-TC
        Unsigned32		FROM SNMPv2-SMI
		IpAddress		FROM SNMPv2-SMI
		TimeTicks		FROM SNMPv2-SMI
		Counter32 		FROM SNMPv2-SMI
        Counter64 		FROM SNMPv2-SMI
        Integer32               FROM SNMPv2-SMI
        TruthValue     	FROM SNMPv2-TC
		MacAddress		FROM SNMPv2-TC
		DisplayString  	FROM RFC1213-MIB
        extremeAgent   	FROM EXTREME-BASE-MIB
        ExtremeDeviceId     FROM EXTREME-BASE-MIB
		extremeV2Traps		FROM EXTREME-BASE-MIB
        ClientAuthType          FROM EXTREME-BASE-MIB
		WPACipherSet 		FROM EXTREME-BASE-MIB
		WPAKeyMgmtSet 		FROM EXTREME-BASE-MIB
        WirelessRemoteConnectBindingType FROM EXTREME-BASE-MIB
		InetAddressType		FROM INET-ADDRESS-MIB
		InetAddress    		FROM INET-ADDRESS-MIB
		extremeAP		FROM EXTREME-BASE-MIB
		extremeLAC		FROM EXTREME-BASE-MIB
		TEXTUAL-CONVENTION	FROM SNMPv2-TC;

        extremeWireless MODULE-IDENTITY
                LAST-UPDATED "0211230000Z"
                ORGANIZATION "Extreme Networks, Inc."
                CONTACT-INFO "www.extremenetworks.com"
                DESCRIPTION "Extreme Wireless Access Tables"
        ::= { extremeAgent 25 }

	extremeAPTraps 		OBJECT IDENTIFIER ::= { extremeV2Traps 5 }
	extremeAPTrapsPrefix 	OBJECT IDENTIFIER ::= { extremeAPTraps 0 }
	extremeLACGeneral 	OBJECT IDENTIFIER ::= { extremeLAC 1 }
	extremeProfile 		OBJECT IDENTIFIER ::= { extremeLAC 2 }


-- ************************************************************** --
-- ************************************************************** --
Dot11Type ::= TEXTUAL-CONVENTION
	  STATUS	current
	  DESCRIPTION
		"The current standard supports A, B, G interfaces."
	  SYNTAX INTEGER { a(1), b(2), g(3), bg(4) }

Dot11Speed ::= TEXTUAL-CONVENTION
	  STATUS	current
	  DESCRIPTION   "Specifies the speed in Mbps to use for the interface. A value of 0 indicates auto."
	SYNTAX BITS { 	speed1(0),
			speed2(1),
			speed5(2),
			speed11(3),
			speed6(4),
			speed9(5),
			speed12(6),
			speed18(7),
			speed24(8),
			speed36(9),
			speed48(10),
			speed54(11)}

-- Added for DFS support. To be used for listing channels on 
-- which Radar interference was detected.
Dot11AChannel ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION "Specifies the channel number for 802.11A interface."
    SYNTAX BITS { 
            channel36(0),
            channel40(1),
            channel44(2),
            channel52(3),
            channel56(4),
            channel60(5),
            channel64(6),
            channel100(7),
            channel104(8),
            channel108(9),
            channel112(10),
            channel116(11),
            channel120(12),
            channel124(13),
            channel128(14),
            channel132(15),
            channel140(16)
            }

Dot11AuthMode ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION "Specifies the allowed authentication type."
	SYNTAX		INTEGER { open(0), shared(1)}

NetworkAuthMode ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION "Specifies the allowed authentication type."
	SYNTAX		INTEGER { none(0), webNetlogin(1), macRadius(2), wpaPlusLegacy(3), wpaOnly(4), wpa2PlusWPA(5), wpa2Only(6)}

ExtremeWirelessCountryCode ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION "Specifies the country code the AP operates in."
    SYNTAX INTEGER { 
                        unitedStates(840),
                        japan(392),
                        taiwan(158),
                        newZealand(554),
                        albania              ( 8),       
                        algeria              ( 12),      
                        argentina            ( 32),      
                        armenia              ( 51),      
                        australia            ( 36),      
                        austria              ( 40),      
                        azerbaijan           ( 31),      
                        bahrain              ( 48),      
                        belarus              ( 112),     
                        belgium              ( 56),      
                        belize               ( 84),      
                        bolivia              ( 68),      
                        brazil               ( 76),      
                        bruneiDarussalam    ( 96),      
                        bulgaria             ( 100),     
                        canada               ( 124),     
                        chile                ( 152),     
                        china                ( 156),     
                        colombia             ( 170),     
                        costaRica           ( 188),     
                        croatia              ( 191),     
                        cyprus               ( 196),
                        czech                ( 203),     
                        denmark              ( 208),     
                        dominicanRepublic   ( 214),     
                        ecuador              ( 218),     
                        egypt                ( 818),     
                        elSalvador          ( 222),     
                        estonia              ( 233),     
                        faeroeIslands       ( 234),     
                        finland              ( 246),     
                        france               ( 250),     
                        georgia              ( 268),     
                        germany              ( 276),     
                        greece               ( 300),     
                        guatemala            ( 320),     
                        honduras             ( 340),     
                        hongKong            ( 344),     
                        hungary              ( 348),     
                        iceland              ( 352),     
                        india                ( 356),     
                        indonesia            ( 360),     
                        iran                 ( 364),     
                        iraq                 ( 368),     
                        ireland              ( 372),     
                        israel               ( 376),     
                        italy                ( 380),     
                        jamaica              ( 388),     
                        japan1               ( 393),     
                        japan2               ( 394),     
                        japan3               ( 395),     
                        jordan               ( 400),     
                        kazakhstan           ( 398),     
                        kenya                ( 404),     
                        koreaNorth          ( 408),     
                        koreaRoc            ( 410),     
                        koreaRoc2           ( 411),     
                        kuwait               ( 414),     
                        latvia               ( 428),     
                        lebanon              ( 422),     
                        libya                ( 434),     
                        liechtenstein        ( 438),     
                        lithuania            ( 440),     
                        luxembourg           ( 442),     
                        macau                ( 446),     
                        macedonia            ( 807),     
                        malaysia             ( 458),     
                        mexico               ( 484),     
                        monaco               ( 492),     
                        morocco              ( 504),     
                        netherlands          ( 528),     
                        nicaragua            ( 558),     
                        norway               ( 578),     
                        oman                 ( 512),     
                        pakistan             ( 586),     
                        panama               ( 591),     
                        paraguay             ( 600),     
                        peru                 ( 604),     
                        philippines          ( 608),     
                        poland               ( 616),     
                        portugal             ( 620),     
                        puertoRico          ( 630),     
                        qatar                ( 634),     
                        romania              ( 642),     
                        russia               ( 643),     
                        saudiArabia         ( 682),     
                        singapore            ( 702),     
                        slovakia             ( 703),     
                        slovenia             ( 705),     
                        southAfrica         ( 710),     
                        spain                ( 724),     
                        sweden               ( 752),     
                        switzerland          ( 756),     
                        syria                ( 760),     
                        thailand             ( 764),     
                        trinidadTYobago    ( 780),     
                        tunisia              ( 788),     
                        turkey               ( 792),     
                        uae                  ( 784),     
                        ukraine              ( 804),     
                        unitedKingdom       ( 826),     
                        uruguay              ( 858),     
                        uzbekistan           ( 860),     
                        venezuela            ( 862),     
                        vietNam             ( 704),     
                        yemen                ( 887),     
                        zimbabwe             ( 716),     
                        restOfTheWorld       ( 0),
                        extremeDefault       ( 998),
                        unknown              ( 999)
                        }

ExtremeWirelessAntennaType ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION "Specifies Antenna type attached to the access-point. 
    The integrated antenna is specified by CTL table on PCI eeprom.
    The detachable model 15901 is an omni-directional antenna.
    The detachable model 15902 is a  directional antenna."
    SYNTAX    INTEGER { integrated(0), detachable15901(1), detachable15902(2) }


ExtremeWirelessAntennaLocation ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION "Specifies whether the antenna is an indoor or outdoor antenna"
    SYNTAX	 INTEGER { indoor(0), outdoor(1) }

ExtremeWirelessPhysInterfaceIndex ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION  "The value of a physical interface number. Each radio on an AP
			corresponds to a physical interface. There can be multiple
			virtual interface on each physical interface. The format
			for the physical interface is xxxxyy where xxxx is the
			port IF index to which the AP is connected. YY is the 
			interface index within that AP."
    SYNTAX      Integer32(1..2147483647)

ExtremeWirelessVirtInterfaceIndex ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION  "The value of a virtual interface number. Each radio on an AP
			corresponds to a physical interface. There can be multiple
			virtual interface on each physical interface. The format
			for the virtual interface is xxxxyyzz where xxxx is the
			port IF index to which the AP is connected. YY is the 
			physical interface index within that AP and ZZ is the index
			of the virtual interface within that physical interface."
    SYNTAX      Integer32(1..2147483647)

ExtremeWirelessChannelAutoSelectStatus ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION   "Specifies the status of the channel auto-selection process."
    SYNTAX BITS {       notApplicable(0),
                        inProgress(1),
                        success(2),
                        radarInterferenceDuringScan(3),
                        radarInterferenceDuringOperation(4),
                        restartedDueToInterference(5)}


-- ************************************************************** --
--                      Device wide Properties                     --
-- ************************************************************** --	
extremeAPTotalAuthFailures OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS 	read-only
	STATUS		current
	DESCRIPTION
		"This variable gives the total number of
		 Authentication failures that have been seen by all
		 the wireless ports."
	::= { extremeLACGeneral 1 }

extremeAPTotalDetectedStations OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This variable gives the total number of detected
		 stations on all the wireless ports."
	::= { extremeLACGeneral 2 }

extremeAPTotalAssociatedStations OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This variable gives the total number of associated
		 stations on all the wireless ports."
	::= { extremeLACGeneral 3 }

extremeAPTotalAuthenticatedStations OBJECT-TYPE
	SYNTAX          INTEGER
	MAX-ACCESS      read-only
	STATUS          current
	DESCRIPTION
	   "This variable gives the total number of authenticated
       stations on all the wireless ports."
	::= { extremeLACGeneral 4 }

extremeWirelessCfgMgmtVLAN OBJECT-TYPE
        SYNTAX          INTEGER
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
                "This value indicates the VLAN tag that should be
                 applied to all control traffic between the AP and
                 the platform."
        ::= { extremeLACGeneral 5 }

extremeWirelessCfgNetmask OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value is the subnet of the Mgmt Vlan"
	::= { extremeLACGeneral 6 }

extremeWirelessCfgGateway OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value is assigned to the APs as the gateway
		for the management vlan."
	::= { extremeLACGeneral 7 }

extremeWirelessCfgEnableWirelessTraps OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"If set to TRUE the wireless port will send traps for
		 Authentication and association events.  
		 If set to FALSE no traps will be generated.
		 Note: We always generate the wireless port status
		 traps."
	::= { extremeLACGeneral 8 }

extremeWirelessCfgCountryCode OBJECT-TYPE
	SYNTAX		 ExtremeWirelessCountryCode
        MAX-ACCESS       read-write
        STATUS           current
        DESCRIPTION
                "This variable allows configuration of the 
                 country code in which the AP will operate.

                 Setting this value will enable the AP to range
                 check frequency and other regulatory settings."
        ::= { extremeLACGeneral 10 }


-- ************************************************************** --
--             Wireless Port Configuration Table                   --
--                                                                 --
-- The configuration table contains one entry for each port on the --
-- device which can host wireless function.  Configuration of the  --
-- port is done without regard to the presence or absence of an AP --
-- on that port.                                                   --
--                                                                 --
-- ************************************************************** --
extremeWirelessPortCfgTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessPortCfgEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains one entry per wireless port that
		 the user has configured.  The AP does not have to be
		 plugged in for the user to configure it via this 
		 table."
	::= { extremeLAC 3 }

extremeWirelessPortCfgEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessPortCfgEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		""
	INDEX { extremeWirelessPortIfIndex }
	::= { extremeWirelessPortCfgTable 1 }

ExtremeWirelessPortCfgEntry ::= SEQUENCE {
	extremeWirelessPortIfIndex		        InterfaceIndex,
	extremeWirelessPortCfgIpAddress		    IpAddress,
	extremeWirelessPortCfgLocation		    DisplayString,
	extremeWirelessPortCfgDetectedTimeout	TimeTicks,
	extremeWirelessPortCfgClientWatchdog    TruthValue,
	extremeWirelessPortLastChange		    TimeTicks,
	extremeWirelessPortState		        INTEGER,
    extremeWirelessPortAntennaType          ExtremeWirelessAntennaType,
    extremeWirelessPortAntennaLocation      ExtremeWirelessAntennaLocation,
    extremeWirelessPortBootstrapServerAddressType  InetAddressType,
    extremeWirelessPortBootstrapServerAddress      InetAddress,
    extremeWirelessPortBootstrapFilePath    DisplayString,
    extremeWirelessPortBootLoaderServerAddressType InetAddressType,
    extremeWirelessPortBootLoaderServerAddress     InetAddress,
    extremeWirelessPortBootLoaderFilePath   DisplayString,
    extremeWirelessPortRuntimeServerAddressType    InetAddressType,
    extremeWirelessPortRuntimeServerAddress        InetAddress,
    extremeWirelessPortRuntimeFilePath      DisplayString,
    extremeWirelessPortMultiBootMode        INTEGER
}


extremeWirelessPortIfIndex OBJECT-TYPE
	SYNTAX		InterfaceIndex
	MAX-ACCESS	read-only 
	STATUS		current
	DESCRIPTION
		"This is the MIB-II ifIndex of the port this entry
		 describes."
	::= { extremeWirelessPortCfgEntry 1 }

extremeWirelessPortCfgIpAddress OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value is assigned to the port for external
		 management."
	::= { extremeWirelessPortCfgEntry 2 }

extremeWirelessPortCfgLocation OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This describes the physical location of the wireless 
		 port."
	::= { extremeWirelessPortCfgEntry 3 }

extremeWirelessPortCfgDetectedTimeout OBJECT-TYPE
	SYNTAX		TimeTicks
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the number of seconds a station can stay
		 in the detected station table without authenticating."
	::= { extremeWirelessPortCfgEntry 4 }

extremeWirelessPortCfgClientWatchdog OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"Setting this value to true will force the AP to 
		 run a watch-dog process for system health."
	::= { extremeWirelessPortCfgEntry 5 }

extremeWirelessPortLastChange OBJECT-TYPE
	SYNTAX		TimeTicks
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"The value of sysUpTime when the port entered its last state"
	::= { extremeWirelessPortCfgEntry 6 }

extremeWirelessPortState OBJECT-TYPE
	SYNTAX 		INTEGER {
				disabled(1),
				enabled(2),
				reset(3),
				addressing(4),
				register(5),
				syncing(6),
				online(7),
				error(8)
			}
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"The current state of the wireless part of this port.

		 enabled  : Port is actively looking for wireless
			    interfaces.

		 disabled : Port is administratively unable to detect
			    new interfaces.

		 reset    : Interfaces have been detected and are 
			    being brought up (access point is booting).

		 addressing : IP Address is being assigned.

		 register : Interfaces are registering for service

		 syncing  : Interfaces are retrieving their 
			    configuration and updating their state.

		 online   : Interfaces are operational.

		 If an error occurs the wireless port will 
		 transistion to the error state."
	::= { extremeWirelessPortCfgEntry 7 }

-- TODO: What is the meaning of this variable?
extremeWirelessPortAntennaType OBJECT-TYPE
	SYNTAX	        ExtremeWirelessAntennaType	
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the type of the Antenna for Detachable product.
		 For Altitude-2d series, further selection is available
                 through this variable."
	::= { extremeWirelessPortCfgEntry 8 }

extremeWirelessPortAntennaLocation OBJECT-TYPE
	SYNTAX		ExtremeWirelessAntennaLocation
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies the location the detachable antenna."
	::= { extremeWirelessPortCfgEntry 9 }

extremeWirelessPortBootstrapServerAddressType OBJECT-TYPE
	SYNTAX		InetAddressType
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
        "The type of address specified in the object 'extremeWirelessPortBootstrapServerAddress'.
        Currently, only 'ipv4' and 'dns' are supported."
	::= { extremeWirelessPortCfgEntry 10 }

extremeWirelessPortBootstrapServerAddress OBJECT-TYPE
	SYNTAX		InetAddress (SIZE (1..255))
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies the TFTP server address from where the AP gets the Bootstrap Image.
        This can be a DNS name or an IPv4 address."
	::= { extremeWirelessPortCfgEntry 11 }

extremeWirelessPortBootstrapFilePath OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies the TFTP FileName of the AP Bootstrap Image."
	::= { extremeWirelessPortCfgEntry 12 }

extremeWirelessPortBootLoaderServerAddressType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "The type of address specified in the object 'extremeWirelessPortBootLoaderServerAddress'.
    Currently, only 'ipv4' and 'dns' are supported."
    ::= { extremeWirelessPortCfgEntry 13 }

extremeWirelessPortBootLoaderServerAddress OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (1..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "This specifies the TFTP server address from where the AP gets the Bootloader Image.
    This can be a DNS name or an IPv4 address."
    ::= { extremeWirelessPortCfgEntry 14 }

extremeWirelessPortBootLoaderFilePath OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies the TFTP FileName of the AP Bootloader Image."
	::= { extremeWirelessPortCfgEntry 15 }

extremeWirelessPortRuntimeServerAddressType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "The type of address specified in the object 'extremeWirelessPortRuntimeServerAddress'.
    Currently, only 'ipv4' and 'dns' are supported."
    ::= { extremeWirelessPortCfgEntry 16 }

extremeWirelessPortRuntimeServerAddress OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (1..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "This specifies the TFTP server address from where the AP gets the Runtime Image.
    This can be a DNS name or an IPv4 address."
    ::= { extremeWirelessPortCfgEntry 17 }

extremeWirelessPortRuntimeFilePath OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies the TFTP FileName of the AP Runtime Image."
	::= { extremeWirelessPortCfgEntry 18 }

extremeWirelessPortMultiBootMode OBJECT-TYPE
    SYNTAX INTEGER { a300 (0),
        a-bp (1)}
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is used to identify the image with which the AP boots up. This is enumerated as follows:
        0 - a300 - This specifies that AP will bootup with Extreme's A300 image
        1 - a-bp - This specifies that AP will bootup with Siemen''s Beacon Point Image."
	::= { extremeWirelessPortCfgEntry 19 }
-- ************************************************************** --
--                    Wireless Port Status Table                   --
--                                                                 --
-- Status table has one entry for every attached AP.  Each entry   --
-- contains a duplicate of the values which are configured in the  --
-- configuration table, as well as status and other information    --
-- learned from the device.                                        --
-- ************************************************************** --
extremeWirelessPortStatusTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessPortStatusEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains one entry per detected AP."
	::= { extremeAP 1 }

extremeWirelessPortStatusEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessPortStatusEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		""
	INDEX { extremeWirelessPortIfIndex }
	::= { extremeWirelessPortStatusTable 1 }

ExtremeWirelessPortStatusEntry ::= SEQUENCE {
	extremeWirelessPortIpAddress		     IpAddress,
	extremeWirelessPortNetmask		         IpAddress,
	extremeWirelessPortGateway		         IpAddress,
	extremeWirelessPortManagementIP		     IpAddress,
	extremeWirelessPortEnableWirelessTraps   TruthValue,
	extremeWirelessPortLocation		         DisplayString,
	extremeWirelessPortDetectedMaxAge	     TimeTicks,
	extremeWirelessPortMgmtVLAN		         INTEGER,
	extremeWirelessPortBootromVersion	     DisplayString,
	extremeWirelessPortSWVersion		     DisplayString,
	extremeWirelessPortSysDescr		         DisplayString,
	extremeWirelessPortManufacturerName	     DisplayString,
	extremeWirelessPortProductName		     DisplayString,
	extremeWirelessPortSerialNumber 	     DisplayString,
	extremeWirelessPortEdpNeighborId	     ExtremeDeviceId,
	extremeWirelessPortClearCounters	     TruthValue,
	extremeWirelessPortClearLog		         TruthValue,
	extremeWirelessPortWatchdogReset	     TruthValue,
	extremeWirelessPortNumPhysInterfaces	 INTEGER,
	extremeWirelessPortHWVersion	         DisplayString,
	extremeWirelessPortMacAddress            MacAddress,
	extremeWirelessPortRadiusPortID		     DisplayString,
    extremeWirelessPortBootUpTime            TimeTicks,
    extremeWirelessPortCountryCode		     ExtremeWirelessCountryCode,
	extremeWirelessPortWallclockTime         INTEGER,
	extremeWirelessPortPartNumber		     DisplayString,
	extremeWirelessPortPartRevision          DisplayString,
	extremeWirelessPortUpTime                TimeTicks,
    extremeWirelessPortStatusAntennaType     ExtremeWirelessAntennaType,
    extremeWirelessPortStatusAntennaLocation ExtremeWirelessAntennaLocation,
    extremeWirelessPortStatusAntenna2point4GHZGain INTEGER,
    extremeWirelessPortStatusAntenna5GHZGain INTEGER,
    extremeWirelessPortPrimaryBootloaderVersion     DisplayString,
    extremeWirelessPortSecondaryBootloaderVersion   DisplayString,
    extremeWirelessPortCurrentBootloaderInUse       INTEGER
}


extremeWirelessPortIpAddress OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value is assigned to the port for external
		 management."
	::= { extremeWirelessPortStatusEntry 1 }

extremeWirelessPortNetmask OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value is assigned to the AP for external
		 management."
	::= { extremeWirelessPortStatusEntry 2 }

extremeWirelessPortGateway OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value is assigned to the AP for external
		 management."
	::= { extremeWirelessPortStatusEntry 3 }

extremeWirelessPortManagementIP OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value is used by the AP when sending out Management 
		packets to the external world. This IP is used as src-ip 
		address in Syslog messages, as NAS-Identifier in Radius requests, 
		and as Agent-Addr in SNMP trap pdu's."
	::= { extremeWirelessPortStatusEntry 4 }

extremeWirelessPortEnableWirelessTraps OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"If set to TRUE the wireless port will send traps for
		 Authentication and association successful events.  
		 If set to FALSE no traps will be generated.

		 Note: We always generate the wireless port status
		 traps."
	::= { extremeWirelessPortStatusEntry 5 }

extremeWirelessPortLocation OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This describes the physical location of the wireless 
		 port."
	::= { extremeWirelessPortStatusEntry 6 }

extremeWirelessPortDetectedMaxAge OBJECT-TYPE
	SYNTAX		TimeTicks
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the number of seconds a station can stay
		 in the detected station table without authenticating."
	::= { extremeWirelessPortStatusEntry 7 }

extremeWirelessPortMgmtVLAN OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This value indicates the VLAN tag that should be
		 applied to all control traffic between the AP and
		 the platform."
	::= { extremeWirelessPortStatusEntry 8 }

extremeWirelessPortBootromVersion OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS		read-only
	STATUS		current
	DESCRIPTION
		"This is the current version of the bootrom on this AP."
	::= { extremeWirelessPortStatusEntry 9 }

extremeWirelessPortSWVersion OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is the current software version of this AP."
	::= { extremeWirelessPortStatusEntry 10 }

extremeWirelessPortSysDescr OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is the equivalent value of sysDescr from the AP."
	::= { extremeWirelessPortStatusEntry 11 }

extremeWirelessPortManufacturerName OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		" "
	REFERENCE "dot11ManufacturerName"
	::= { extremeWirelessPortStatusEntry 12 }

extremeWirelessPortProductName OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		" "
	REFERENCE "dot11ProductName"
	::= { extremeWirelessPortStatusEntry 13 }

extremeWirelessPortSerialNumber OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is the unique serial number for this device."
	::= { extremeWirelessPortStatusEntry 14 }

extremeWirelessPortEdpNeighborId  OBJECT-TYPE
	SYNTAX		ExtremeDeviceId
	MAX-ACCESS 	read-only
	STATUS		current
	DESCRIPTION
      		"EDP assigns a unique ID to each neighbor to 
		 disambiguate references.  This can be used to index 
		 into the extremeEdpTable."
	::= { extremeWirelessPortStatusEntry 15 }

extremeWirelessPortClearCounters OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value can be set to true to force the device
		 to reset it's counters.  A read will return false."
	::= { extremeWirelessPortStatusEntry 16 }

extremeWirelessPortClearLog OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value can be set to true to force the device
		 to clear its local log store.  A read will return
		 false."
	::= { extremeWirelessPortStatusEntry 17 }

extremeWirelessPortWatchdogReset OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is to enable/disable watchdog timer reset on timeout. 
		Default is enable. If it is enabled, system should reset
		when the watchdog kicks in. On next bootup, it should 
		send out an syslog message to syslog server.  If it is 
		disabled, it should record the error and not reset the system."
	::= { extremeWirelessPortStatusEntry 18 }

extremeWirelessPortNumPhysInterfaces OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current		
	DESCRIPTION
		"Each wireless port has a set of physical interface which are
		 numbered 1-N.  This variable gives the number of 
		 physical interfaces on this AP."
	::= { extremeWirelessPortStatusEntry 19 }

extremeWirelessPortHWVersion OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is the current hardware version of this AP."
	::= { extremeWirelessPortStatusEntry 20 }


extremeWirelessPortMacAddress OBJECT-TYPE
        SYNTAX MacAddress
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Unique MAC Address assigned to the ethernet interface on the AP."
	::= { extremeWirelessPortStatusEntry 21 }

extremeWirelessPortRadiusPortID OBJECT-TYPE
	SYNTAX	DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"A string value that should be sent by the AP to the radius
		server as the Port-ID radius attribute in authentication requests"
	::= { extremeWirelessPortStatusEntry 22 }

extremeWirelessPortBootUpTime OBJECT-TYPE
        	SYNTAX	TimeTicks
        	MAX-ACCESS 	read-write
        	STATUS	current
        	DESCRIPTION	
                "The sysUpTime of the LAC when the port was booted up.
		Should be set only during the AP boot up."
	::= { extremeWirelessPortStatusEntry 23 }

extremeWirelessPortCountryCode OBJECT-TYPE
               	SYNTAX ExtremeWirelessCountryCode
        	MAX-ACCESS 	read-write
        	STATUS	current
        	DESCRIPTION	
                "The country code that the AP is programmed to operate in."
	::= { extremeWirelessPortStatusEntry 24 }

extremeWirelessPortWallclockTime OBJECT-TYPE
        	SYNTAX	INTEGER
        	MAX-ACCESS 	read-write
        	STATUS	current
        	DESCRIPTION	
                "The wall clock time as known to the LAC. 
			Expressed in number of seconds since Jan 1, 1970"
	::= { extremeWirelessPortStatusEntry 25 }

extremeWirelessPortPartNumber OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This describes the part number of the AP."
	::= { extremeWirelessPortStatusEntry 26 }

extremeWirelessPortPartRevision OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This describes the revision of the AP."
	::= { extremeWirelessPortStatusEntry 27 }

extremeWirelessPortUpTime OBJECT-TYPE
        	SYNTAX	TimeTicks
        	MAX-ACCESS 	read-write
        	STATUS	current
        	DESCRIPTION	
                "The number of seconds the port has been in the online state."
	::= { extremeWirelessPortStatusEntry 28 }

extremeWirelessPortStatusAntennaType OBJECT-TYPE
        SYNTAX          ExtremeWirelessAntennaType
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
                "This is the type of the Antenna for Detachable product.
                 For Altitude-2d series, further selection is available
                 through this variable."
        ::= { extremeWirelessPortStatusEntry 29 }

extremeWirelessPortStatusAntennaLocation OBJECT-TYPE
        SYNTAX          ExtremeWirelessAntennaLocation
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
                "This specifies the location the detachable antenna."
        ::= { extremeWirelessPortStatusEntry 30 }

extremeWirelessPortStatusAntenna2point4GHZGain OBJECT-TYPE
        SYNTAX      INTEGER
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
               "This specifies 2.4GHz gain value for detachable antenna in dbi"
        ::= { extremeWirelessPortStatusEntry 31 }

extremeWirelessPortStatusAntenna5GHZGain OBJECT-TYPE
        SYNTAX      INTEGER
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
               "This specifies 5GHz gain value for detachable antenna in dbi"
        ::= { extremeWirelessPortStatusEntry 32 }

extremeWirelessPortPrimaryBootloaderVersion OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "This is the version of the primary bootloader on this AP."
        ::= { extremeWirelessPortStatusEntry 33 }

extremeWirelessPortSecondaryBootloaderVersion OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "This is the version of the Secondary bootloader on this AP."
        ::= { extremeWirelessPortStatusEntry 34 }

extremeWirelessPortCurrentBootloaderInUse OBJECT-TYPE
	SYNTAX INTEGER { primary(1),
			         secondary(2) }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "This specifies if the bootloader booted is primary or secondary one."
        ::= { extremeWirelessPortStatusEntry 35 }
-- ************************************************************** --
--                 Wireless Port Syslog Status Table               --
-- ************************************************************** --
extremeWirelessPortLogStatusTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessPortLogStatusEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains one entry for each wireless port
		 which is configured via the configuration table."
	::= { extremeAP 2 }

extremeWirelessPortLogStatusEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessPortLogStatusEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		""
	INDEX { extremeWirelessPortIfIndex, 
		extremeWirelessPortLogStatusIndex }
	::= { extremeWirelessPortLogStatusTable 1 }

ExtremeWirelessPortLogStatusEntry ::= SEQUENCE {
	extremeWirelessPortLogStatusIndex	INTEGER,
	extremeWirelessPortLogStatusDestIp	InetAddress,
	extremeWirelessPortLogStatusDestIpType  InetAddressType,
	extremeWirelessPortLogStatusPort	INTEGER,
	extremeWirelessPortLogStatusFacility	INTEGER,
	extremeWirelessPortLogStatusSeverity	INTEGER,
	extremeWirelessPortLogStatusStatus      TruthValue }

extremeWirelessPortLogStatusIndex OBJECT-TYPE
	SYNTAX		INTEGER(1..9)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"There are 9 entries in this table to correspond to
		 the 8 entries configured in the cfg table plus one
		 for the LAC."
	::= { extremeWirelessPortLogStatusEntry 1 }

extremeWirelessPortLogStatusDestIp OBJECT-TYPE
	SYNTAX		InetAddress
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the IP address to which syslog messsages
		 will be sent.  A value of 0.0.0.0 indicates that
		 this entry is disabled. The source IP for every syslog
		message comes from the ManagementIP field in 
		extremeWirelessPortStatusTable"
	::= { extremeWirelessPortLogStatusEntry 2 }

extremeWirelessPortLogStatusDestIpType          OBJECT-TYPE
        SYNTAX                                  InetAddressType
        MAX-ACCESS                              read-write
        STATUS                                  current
        DESCRIPTION
            "The type of address specified in the object
                'extremeWirelessPortLogStatusDestIp.
             Currently, only 'ipv4' and 'dns' are supported."
        DEFVAL { ipv4 }
      ::= { extremeWirelessPortLogStatusEntry 3 }

extremeWirelessPortLogStatusPort OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the port to which syslog messages will be
		 sent."
	::= { extremeWirelessPortLogStatusEntry 4 }

extremeWirelessPortLogStatusFacility OBJECT-TYPE
	SYNTAX		INTEGER(0..7)
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"Optional value for syslog facility local0-7."
	::= { extremeWirelessPortLogStatusEntry 5 }

extremeWirelessPortLogStatusSeverity               OBJECT-TYPE
            SYNTAX  INTEGER { 
				critical (0),
				error (1),
				warning (2),
				notice (3),
				info (4),
				debugSummary (5),
				debugVerbose (6),
				debugData (7)					
				}
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                    "The syslog message severity. All syslog
 			messages that have a severity >= specified severity, 
			will be sent to	this syslog server."
            DEFVAL { debugData }
            ::= { extremeWirelessPortLogStatusEntry 6 }

extremeWirelessPortLogStatusStatus OBJECT-TYPE
                SYNTAX TruthValue
                MAX-ACCESS  read-write
                STATUS current
                DESCRIPTION
                        "Specifies whether the log server is enabled or not."
        ::= { extremeWirelessPortLogStatusEntry 7 }


-- ************************************************************** --
--                   Wireless Port Log Table                       --
-- ************************************************************** --
extremeWirelessPortLogTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessPortLogEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table allows access to the syslog entries on
		 particular wireless ports."
	::= { extremeAP 3 }

extremeWirelessPortLogEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessPortLogEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		""
	INDEX { extremeWirelessPortIfIndex, 
		extremeWirelessPortLogIndex }
	::= { extremeWirelessPortLogTable 1 }

ExtremeWirelessPortLogEntry ::= SEQUENCE {
	extremeWirelessPortLogIndex	INTEGER,
	extremeWirelessPortLogMessage	DisplayString }

extremeWirelessPortLogIndex OBJECT-TYPE
	SYNTAX		INTEGER(0..65535)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This is an arbitrary integer which is assinged to
		 entries in order of their insertion."
	::= { extremeWirelessPortLogEntry 1 }

extremeWirelessPortLogMessage OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is the formatted syslog message which was sent."
	::= { extremeWirelessPortLogEntry 2 }
	
-- ************************************************************** --
--           Wireless Physical Interface Ctl Table                 --
-- This table allows for the configuration of parameters that deal --
-- with physical interfaces. Each radio on the AP is represented by--
-- a physical interface. Each physical interface can have multiple --
-- (upto 8) virtual interfaces. RF properties are configured on the--
-- physical interface using the extremeWirelessInterfaceRFCtlTable --
-- Security parameters are configured on the virtual interface     --
-- using the extremeWirelessInterfaceSecurityCtlTable          --
-- ************************************************************** --

extremeWirelessPhysInterfaceCtlTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessPhysInterfaceCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table provides configuration of per-physical interface
		 values."
	::= { extremeAP 4 }

ExtremeWirelessPhysInterfaceCtlEntry ::= SEQUENCE {
	extremeWirelessPhysInterfaceIndex 		ExtremeWirelessPhysInterfaceIndex,
	extremeWirelessPhysInterfacePHYChannel	     INTEGER,
	extremeWirelessPhysInterfaceSpeed			Dot11Speed,
	extremeWirelessPhysInterfaceNumVirtInterfaces INTEGER,
    extremeWirelessPhysInterfaceChannelAutoSelectStatus ExtremeWirelessChannelAutoSelectStatus,
    extremeWirelessPhysInterfaceRadarInterfaceChannelList Dot11AChannel
}


extremeWirelessPhysInterfaceCtlEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessPhysInterfaceCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current	
	DESCRIPTION
		"Each entry is indexed by the wirelessInterfaceIndex, and
		represents a wireless interface."
	INDEX { extremeWirelessPhysInterfaceIndex }
	::= { extremeWirelessPhysInterfaceCtlTable 1 }

extremeWirelessPhysInterfaceIndex OBJECT-TYPE
	SYNTAX	ExtremeWirelessPhysInterfaceIndex
	MAX-ACCESS	accessible-for-notify
	STATUS		current
	DESCRIPTION
		"This is the ifIndex of an individual physical interface on an AP.
		Each radio will have a unique value."
	::= { extremeWirelessPhysInterfaceCtlEntry 1 }


extremeWirelessPhysInterfacePHYChannel OBJECT-TYPE
	SYNTAX		INTEGER (0..99)
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the RF channel which will be used by 
		 this transmitter.  Range should be established
		 by configuration of RegDomain and PHYType."
	::= { extremeWirelessPhysInterfaceCtlEntry 2 }

extremeWirelessPhysInterfaceSpeed OBJECT-TYPE
	SYNTAX		Dot11Speed 
	MAX-ACCESS	 read-write
	STATUS		 current
	DESCRIPTION
		"This value indicates the current setting for 
		 Tx speed for the interface. Only one of thebits of this bit set can be set.
		 If all bits are unset (ie a 0 is passed as a value) then 
		 it means that the interface should choose a speed
		 based on some algorithm."
	::= { extremeWirelessPhysInterfaceCtlEntry 3 }

extremeWirelessPhysInterfaceNumVirtInterfaces OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	 read-write
	STATUS		 current
	DESCRIPTION
		"Each physical interface has a number of virtual interfaces
	 	 This specifies the number of virt interfaces."
	::= { extremeWirelessPhysInterfaceCtlEntry 4 }

-- Added for DFS
extremeWirelessPhysInterfaceChannelAutoSelectStatus OBJECT-TYPE
    SYNTAX          ExtremeWirelessChannelAutoSelectStatus 
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION
        "This OID reflects the status of the channel auto selection process."
        ::= { extremeWirelessPhysInterfaceCtlEntry 5 }

-- Added for DFS
extremeWirelessPhysInterfaceRadarInterfaceChannelList OBJECT-TYPE
        SYNTAX          Dot11AChannel 
        MAX-ACCESS       read-only
        STATUS           current
        DESCRIPTION
                "This OID reflects the list oc 802.11(a) channels on which
radar interference was detected."
        ::= { extremeWirelessPhysInterfaceCtlEntry 6 }

	
	
-- ************************************************************** --
--           Wireless Virtual  Interface Ctl Table                 --
-- This table allows for the configuration of parameters that deal --
-- with virtual interfaces. Each radio on the AP is represented by--
-- a physical interface. Each physical interface can have multiple --
-- (upto 8) virtual interfaces. RF properties are configured on the--
-- physical interface using the extremeWirelessInterfaceRFCtlTable --
-- Security parameters are configured on the virtual interface     --
-- using the extremeWirelessInterfaceSecurityCtlTable          --
-- ************************************************************** --
extremeWirelessVirtInterfaceCtlTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessVirtInterfaceCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table provides configuration of per-physical-interface
		 values."
	::= { extremeAP 6 }

ExtremeWirelessVirtInterfaceCtlEntry ::= SEQUENCE {
	extremeWirelessVirtInterfaceIndex 		        ExtremeWirelessVirtInterfaceIndex,
	extremeWirelessVirtInterfaceMacAddress		    MacAddress,
	extremeWirelessVirtInterfaceMaxClients		    INTEGER,
	extremeWirelessVirtInterfaceWirelessBridging	TruthValue,
	extremeWirelessVirtInterfaceLastStateChange	    TimeTicks,
	extremeWirelessVirtInterfaceState		        INTEGER,
    extremeWirelessVirtInterfaceIappEnabled         TruthValue,
    extremeWirelessVirtInterfaceSvpEnabled          TruthValue
}

extremeWirelessVirtInterfaceCtlEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessVirtInterfaceCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current	
	DESCRIPTION
		"Each entry is indexed by the wirelessInterfaceIndex, and
		represents a virtual wireless interface."
	INDEX { extremeWirelessVirtInterfaceIndex }
	::= { extremeWirelessVirtInterfaceCtlTable 1 }

extremeWirelessVirtInterfaceIndex OBJECT-TYPE
	SYNTAX	ExtremeWirelessVirtInterfaceIndex
	MAX-ACCESS	accessible-for-notify
	STATUS		current
	DESCRIPTION
		"This is the ifIndex of an individual interface on an AP.
		Each virtual interface will have a unique value."
	::= { extremeWirelessVirtInterfaceCtlEntry 1 }
	
extremeWirelessVirtInterfaceMacAddress OBJECT-TYPE
	SYNTAX		MacAddress
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"The maximum number of clients that can associate with this virtual interface."
	::= { extremeWirelessVirtInterfaceCtlEntry 2 }

extremeWirelessVirtInterfaceMaxClients OBJECT-TYPE
	SYNTAX		INTEGER (1..128)
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"The maximum number of clients that can associate with this virtual interface."
	::= { extremeWirelessVirtInterfaceCtlEntry 3 }

extremeWirelessVirtInterfaceWirelessBridging OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies whether wireless bridging is enabled on this interface.
		 If wireless bridging is enabled then traffic originating from a client on this
		 interface will be bridged to clients on the same VLAN on this or other 
		 virtual interfaces that have wireless bridging enabled. So basically
		 wireless bridging should be enabled on both the source and the destination
		 virtual interface for traffic to be bridged."
	::= { extremeWirelessVirtInterfaceCtlEntry 4 }

extremeWirelessVirtInterfaceLastStateChange OBJECT-TYPE
	SYNTAX		TimeTicks
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"The value of sysUpTime when the virtual interface entered its current state"
	::= { extremeWirelessVirtInterfaceCtlEntry 5 }

extremeWirelessVirtInterfaceState OBJECT-TYPE
	SYNTAX		INTEGER { enabled(1), disabled(2) } 
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value specifies whether the virtual interface is active or not"
	::= { extremeWirelessVirtInterfaceCtlEntry 6 }

extremeWirelessVirtInterfaceIappEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
    "IAPP status. A value of TRUE indicates IAPP is enabled on the
    interface."
    ::= { extremeWirelessVirtInterfaceCtlEntry 7 }

extremeWirelessVirtInterfaceSvpEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
    "SVP status. A value of TRUE indicates SVP is enabled on the
    interface."
    ::= { extremeWirelessVirtInterfaceCtlEntry 8 }

-- ************************************************************** --
--              Wireless Interface Security Ctl Table              --
-- This table allows for the configuration of per-wireless-intf    --
-- security parameters. Each Wireless interface is represented in  --
-- ifTable, and is represented by extremeWirelessInterfaceIfIndex. --
-- ************************************************************** --
extremeWirelessVirtInterfaceSecurityCtlTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessVirtInterfaceSecurityCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table provides configuration of per-interface
		 values."
	::= { extremeAP 7 }

extremeWirelessVirtInterfaceSecurityCtlEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessVirtInterfaceSecurityCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current	
	DESCRIPTION
		"Each entry is indexed by the wirelessInterfaceIndex, and
		represents a wireless interface."
	INDEX { extremeWirelessVirtInterfaceIndex }
	::= { extremeWirelessVirtInterfaceSecurityCtlTable 1 }

ExtremeWirelessVirtInterfaceSecurityCtlEntry ::= SEQUENCE {
	extremeWirelessVirtInterfaceESSName		OCTET STRING,
	extremeWirelessVirtInterfaceSSIDInBeacon	TruthValue,
	extremeWirelessVirtInterfaceDot11AuthMode	Dot11AuthMode, 
	extremeWirelessVirtInterfaceNetworkAuthMode	NetworkAuthMode, 
	extremeWirelessVirtInterfaceDataVlan	INTEGER,
	extremeWirelessVirtInterfaceIgnoreVSAVlan	TruthValue,
	extremeWirelessVirtInterfaceWEPDefaultKey	INTEGER,
	extremeWirelessVirtInterfaceEncryptionLength 		INTEGER
}

extremeWirelessVirtInterfaceESSName OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"The ESS ID of this interface."
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 1 }

extremeWirelessVirtInterfaceSSIDInBeacon OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"Does this interface generate a beacon with the
		 SSID in it?"
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 2 }

extremeWirelessVirtInterfaceDot11AuthMode OBJECT-TYPE
	SYNTAX		Dot11AuthMode
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"Setting this to true indicates whether to use open or shared authentication"
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 3 }

extremeWirelessVirtInterfaceNetworkAuthMode OBJECT-TYPE
	SYNTAX		NetworkAuthMode
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies the authentication mode to use on top of dot11 MAC authentication"
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 4 }

extremeWirelessVirtInterfaceDataVlan OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the VLAN to use for client data in case of the VLAN is not received as a VSA. 
		This VLAN is also used for authentication types like PSK, WEP, etc"
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 5 }

extremeWirelessVirtInterfaceIgnoreVSAVlan OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"If this is set then the VLAN returned as a VSA will be ignored and the Data Vlan specified above
		will be used instead"
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 6 }

extremeWirelessVirtInterfaceWEPDefaultKey OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value indicates the index in dot11WEPDefaultKeyTable that 
		identifies the key this interface will use to decrypt packets."
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 7 }

extremeWirelessVirtInterfaceEncryptionLength OBJECT-TYPE
	SYNTAX      INTEGER { none(0),
	 		      sixtyfour(64),
                              onetwentyeight(128)  }
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the length of the encryption key to use in case
	         of dot1x legacy mode and WEP. If this length is 0 then no WEP encryption."
	::= { extremeWirelessVirtInterfaceSecurityCtlEntry 8 }

-- ************************************************************** --
--              Wireless Interface Dot1x Ctl Table              --
-- This table allows for the configuration of per-wireless-intf    --
-- dot1x parameters. Each Wireless interface is represented in  --
-- ifTable, and is represented by extremeWirelessVirtInterfaceIfIndex. --
-- ************************************************************** --
extremeWirelessVirtInterfaceDot1xCtlTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeWirelessVirtInterfaceDot1xCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"There is one entry in this table for each virtual interface 
		It reflects the dot1x security config"
	::= { extremeAP 8 }

extremeWirelessVirtInterfaceDot1xCtlEntry OBJECT-TYPE
	SYNTAX	ExtremeWirelessVirtInterfaceDot1xCtlEntry
	MAX-ACCESS	not-accessible
	STATUS	current
	DESCRIPTION
		""
	INDEX { extremeWirelessVirtInterfaceIndex }
	::= { extremeWirelessVirtInterfaceDot1xCtlTable 1 }

ExtremeWirelessVirtInterfaceDot1xCtlEntry ::= SEQUENCE {
	extremeWirelessVirtInterfaceKeyMgmtSuite		WPAKeyMgmtSet,
	extremeWirelessVirtInterfaceMcastCipherSuite	WPACipherSet,
	extremeWirelessVirtInterfaceDot1xPSKValue		OCTET STRING,
	extremeWirelessVirtInterfaceDot1xPSKPassPhrase	DisplayString,
	extremeWirelessVirtInterfaceDot1xReAuthPeriod	INTEGER,
	extremeWirelessVirtInterfaceGroupUpdateTimeOut	Unsigned32,
	extremeWirelessVirtInterfacePairwiseUpdateTimeOut Unsigned32,
        extremeWirelessVirtInterfaceDot11iPreauthEnable TruthValue
}

extremeWirelessVirtInterfaceKeyMgmtSuite OBJECT-TYPE
	SYNTAX	WPAKeyMgmtSet
	MAX-ACCESS	read-write
	STATUS	current
	DESCRIPTION
		"This bitmask configures the authentication suites to be used."
	::= { extremeWirelessVirtInterfaceDot1xCtlEntry 1 }

extremeWirelessVirtInterfaceMcastCipherSuite OBJECT-TYPE
	SYNTAX	WPACipherSet
	MAX-ACCESS	read-write
	STATUS	current
	DESCRIPTION
		"This configures the cipher suite to use for mcast traffic. The cipher suite to use for unicast traffic
		 is derived from this using the following algorithm:
			Mcast cipher = WEP (64/128), Unicast cipher = TKIP
			Mcast cipher = TKIP, Unicast cipher = TKIP
			Mcast cipher = AES, Unicast cipher = AES.
		 This therefore determines the max unicast cipher suite the client can use to associate with 
		 this interface."
	::= { extremeWirelessVirtInterfaceDot1xCtlEntry 2 }


extremeWirelessVirtInterfaceDot1xPSKValue OBJECT-TYPE
	SYNTAX		OCTET STRING (SIZE(32))
	MAX-ACCESS	read-write
	STATUS		current	
	DESCRIPTION
		"The Pre-Shared Key (PSK) for when WPA in PSK mode is
		 the selected authentication suite."
	REFERENCE "dot11RSNConfigPSKValue"
	::= { extremeWirelessVirtInterfaceDot1xCtlEntry 3 }

extremeWirelessVirtInterfaceDot1xPSKPassPhrase OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
	"The PSK, for when WPA in PSK mode is the selected authentication
	 suite, is configured by extremeWirelessVirtInterfaceKeyMgmtSuite.
	 An alternative manner of setting the PSK uses the password-to-key
	 algorithm defined in section XXX. This variable provides a means
	 to enter a pass phrase. When this object is written, the RSN
	 entity shall use the password-to-key algorithm specified in
	 section XXX to derive a pre-shared and populate
	 extremeWirelessVirtInerfaceDot1xPSKValue with this key.
	 This object is logically write-only. Reading this variable shall
	 return unsuccessful status or null or zero."
	::= { extremeWirelessVirtInterfaceDot1xCtlEntry 4 }

extremeWirelessVirtInterfaceDot1xReAuthPeriod OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"Number of seconds a clients authentication will last before
		 the AP automatically issues a reauthentication request."
	::= { extremeWirelessVirtInterfaceDot1xCtlEntry 5 }

extremeWirelessVirtInterfaceGroupUpdateTimeOut OBJECT-TYPE
	SYNTAX		Unsigned32 (1..1440)
	UNITS		"minutes"
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"The time in minutes after which the RSN group
		 update handshake will be retried. The timer shall
		 start at the moment the group update message is
		 sent."
	REFERENCE "dot11RSNConfigGroupUpdateTimeOut"
	DEFVAL		{ 100 } -- 
	::= { extremeWirelessVirtInterfaceDot1xCtlEntry 6 }

extremeWirelessVirtInterfacePairwiseUpdateTimeOut OBJECT-TYPE
	SYNTAX		Unsigned32 (1..1440)
	UNITS		"minutes"
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"The time in minutes after which the RSN 4-way
		 handshake will be retried. The timer shall start at
		 the moment a 4-way message is sent."
	REFERENCE "dot11RSNConfigPairwiseUpdateTimeOut"
	DEFVAL		{ 100 } -- 
	::= { extremeWirelessVirtInterfaceDot1xCtlEntry 7 }

extremeWirelessVirtInterfaceDot11iPreauthEnable OBJECT-TYPE
        SYNTAX          TruthValue
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
                "Enables/Disable 802.11i pre-authentication."
        ::= { extremeWirelessVirtInterfaceDot1xCtlEntry 8 }

-- ************************************************************** --
--                     Default WEP Key Table                       --
-- ************************************************************** --
extremeWirelessVirtInterfaceWEPKeyTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeWirelessVirtInterfaceWEPKeyEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains per-profile WEP keys.

		 Reference ieee802dot11.1.2.3."
	::= { extremeAP 9 }

extremeWirelessVirtInterfaceWEPKeyEntry OBJECT-TYPE
	SYNTAX	ExtremeWirelessVirtInterfaceWEPKeyEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeWirelessVirtInterfaceIndex, extremeWirelessVirtInterfaceWEPKeyIndex }
	::= { extremeWirelessVirtInterfaceWEPKeyTable 1 }
	
ExtremeWirelessVirtInterfaceWEPKeyEntry ::= SEQUENCE {
	extremeWirelessVirtInterfaceWEPKeyIndex	INTEGER,
	extremeWirelessVirtInterfaceWEPKey		OCTET STRING,
	extremeWirelessVirtInterfaceWEPKeyStatus	TruthValue }
	
extremeWirelessVirtInterfaceWEPKeyIndex OBJECT-TYPE
	SYNTAX 		INTEGER(1..4)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"There can be upto 4 wep keys for any wireless
		 interface. Four of the keys are to be used with
		 WEP Authentication, and the other four with MAC Auth."
	::= { extremeWirelessVirtInterfaceWEPKeyEntry 1 }

extremeWirelessVirtInterfaceWEPKey OBJECT-TYPE
	SYNTAX		OCTET STRING (SIZE(1..19))
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is a write-only wep key."
	::= { extremeWirelessVirtInterfaceWEPKeyEntry 2 }

extremeWirelessVirtInterfaceWEPKeyStatus OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"Specifies whether the key is present or not."
	::= { extremeWirelessVirtInterfaceWEPKeyEntry 3 }

-- ************************************************************** --
--              Wireless Interface RF Ctl Table                    --
-- This table allows for the configuration of per-wireless-intf    --
-- RF parameters. Each Wireless interface is represented in        --
-- ifTable, and is represented by extremeWirelessPhysInterfaceIfIndex. --
-- ************************************************************** --
extremeWirelessPhysInterfaceRFCtlTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessPhysInterfaceRFCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table provides configuration of per-interface
		 values."
	::= { extremeAP 10 }

extremeWirelessPhysInterfaceRFCtlEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessPhysInterfaceRFCtlEntry
	MAX-ACCESS	not-accessible
	STATUS		current	
	DESCRIPTION
		"Each entry is indexed by the wirelessInterfaceIndex, and
		represents a wireless interface."
	INDEX { extremeWirelessPhysInterfaceIndex }
	::= { extremeWirelessPhysInterfaceRFCtlTable 1 }

ExtremeWirelessPhysInterfaceRFCtlEntry ::= SEQUENCE {
	extremeWirelessPhysInterfaceBeaconPeriod 	INTEGER,
	extremeWirelessPhysInterfaceTxPowerLevel 	INTEGER,
	extremeWirelessPhysInterfaceDTIMPeriod 	INTEGER,
	extremeWirelessPhysInterfaceFragLength	INTEGER,
	extremeWirelessPhysInterfaceLongRetry 	INTEGER,	
	extremeWirelessPhysInterfaceShortRetry 	INTEGER,
	extremeWirelessPhysInterfaceRTSThreshold 	INTEGER,
	extremeWirelessPhysInterfaceSupportedDataRatesRxValue Dot11Speed,
	extremeWirelessPhysInterfaceSupportedDataRatesTxValue Dot11Speed,
	extremeWirelessPhysInterfacePHYType		     Dot11Type,
	extremeWirelessPhysInterfacePHYSupportedTypes     BITS,
	extremeWirelessPhysInterfacePreamble		INTEGER,
    extremeWirelessPhysInterfaceAbsTxPowerLevel INTEGER
}

extremeWirelessPhysInterfaceBeaconPeriod OBJECT-TYPE
	SYNTAX		INTEGER 
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the beacon interval to use"
	::= { extremeWirelessPhysInterfaceRFCtlEntry 1 }

extremeWirelessPhysInterfaceTxPowerLevel OBJECT-TYPE
	SYNTAX		INTEGER (1..100)
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the % of max power to transmit at."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 2 }

extremeWirelessPhysInterfaceDTIMPeriod OBJECT-TYPE
	SYNTAX		INTEGER 
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the DTIM period."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 3 }

extremeWirelessPhysInterfaceFragLength OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		" "
	REFERENCE "ieee802dot11.*******."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 4 }

extremeWirelessPhysInterfaceLongRetry OBJECT-TYPE
	SYNTAX		INTEGER (1..255)
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION		
		"Attribute indicates the maximum number of transmission
		 attempts of a frame whose size is > RTS."
	REFERENCE "dot11LongRetryLimit"
	::= { extremeWirelessPhysInterfaceRFCtlEntry 5 }

extremeWirelessPhysInterfaceShortRetry OBJECT-TYPE
	SYNTAX		INTEGER (1..255)
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION		
		"Attribute indicates the maximum number of transmission
		 attempts of a frame whose size is <= RTS."
	REFERENCE "dot11ShortRetryLimit"
	::= { extremeWirelessPhysInterfaceRFCtlEntry 6 }

extremeWirelessPhysInterfaceRTSThreshold OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current	
	DESCRIPTION 
		" "
	REFERENCE "ieee802dot11.*******."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 7 }

extremeWirelessPhysInterfaceSupportedDataRatesRxValue OBJECT-TYPE
	SYNTAX		Dot11Speed
	MAX-ACCESS	read-only
	STATUS		current	
	DESCRIPTION 
		"A bitmask of all supported datarates for Rx."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 8 }
	
extremeWirelessPhysInterfaceSupportedDataRatesTxValue OBJECT-TYPE
	SYNTAX		Dot11Speed
	MAX-ACCESS	read-only
	STATUS		current	
	DESCRIPTION 
		"A bitmask of all supported datarates for Tx."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 9 }

extremeWirelessPhysInterfacePHYType OBJECT-TYPE
	SYNTAX	       Dot11Type
	MAX-ACCESS     read-write
	STATUS	       current
	DESCRIPTION
		"This variable selects between 802.11 a/b/g 
		 for this transmitter.  It should be used by
		 ap to range check frequency, etc."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 10 }

extremeWirelessPhysInterfacePHYSupportedTypes OBJECT-TYPE
	SYNTAX 		BITS { bsupported(0),
			       asupported(1),
			       gsupported(2) }
	MAX-ACCESS 	read-only
	STATUS 		current
	DESCRIPTION
		"This is the 802.11 standard supported by this
		 interface as a bitmask"
	::= { extremeWirelessPhysInterfaceRFCtlEntry 11 }

extremeWirelessPhysInterfacePreamble OBJECT-TYPE
	SYNTAX		INTEGER { short(0),
				  long(1) }
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This changes the 802 preamble expected by the
		 interface."
	::= { extremeWirelessPhysInterfaceRFCtlEntry 12 }

extremeWirelessPhysInterfaceAbsTxPowerLevel OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "This is the absolute value of max power to transmit level in dBm."
    ::= { extremeWirelessPhysInterfaceRFCtlEntry 13 }

-- ************************************************************** --
-- Wireless Interface Status Table
-- ************************************************************** --
extremeWirelessInterfaceStatusTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessInterfaceStatusEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
	"This table provides statistics and information on the
	current condition of each wireless interace.
	There is one entry per virtual and physical wireless 
	interface in this table.  The values for the physical 
	interfaces represent the sum of the values for all sub
	interfaces."
	::= { extremeAP 5 }

extremeWirelessInterfaceStatusEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessInterfaceStatusEntry
	MAX-ACCESS	not-accessible
	STATUS		current	
	DESCRIPTION
	"Each entry in this table is indexed by either a virtual or
	a physical ifIndex."
	INDEX { ifIndex }
	::= { extremeWirelessInterfaceStatusTable 1 }
	
ExtremeWirelessInterfaceStatusEntry ::= SEQUENCE {
	extremeWirelessIntfTotalDetected    Unsigned32,
	extremeWirelessIntfTotalAuthed      Unsigned32,
	extremeWirelessIntfTotalAuthFailed  Unsigned32,
	extremeWirelessIntfTotalAssoc       Unsigned32,
    extremeWirelessIntfTotalAssocFailed Unsigned32,
	extremeWirelessIntfRateDetected     Unsigned32,
	extremeWirelessIntfRateAuthed       Unsigned32,
	extremeWirelessIntfRateAuthFailed   Unsigned32,
    extremeWirelessIntfRateAssoc        Unsigned32,
    extremeWirelessIntfRateAssocFailed  Unsigned32,
    extremeWirelessIntfBlockTime        Unsigned32,
	extremeWirelessIntfCurrentDetected    Unsigned32,
	extremeWirelessIntfCurrentAuthed      Unsigned32,
	extremeWirelessIntfCurrentAssoc       Unsigned32,
	extremeWirelessIntfCurrentForwarding  Unsigned32}

	
extremeWirelessIntfTotalDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of stations which have entered
	the detected state on this wireless interface since the last
	time the counters were cleared."
	::= { extremeWirelessInterfaceStatusEntry 1 }
	
extremeWirelessIntfTotalAuthed OBJECT-TYPE	
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of stations which have entered
	the authenticated state on this wireless interface since the 
	last time the counters were cleared.  Note that this is equal 
	to the number of stations which have entered the forwarding 
	state.  This is not necessaryly equivalent to the number of
	stations which have succeeded in MAC-level authentication."
	::= { extremeWirelessInterfaceStatusEntry 2 }
	
extremeWirelessIntfTotalAuthFailed OBJECT-TYPE	
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of stations which have failed
	to authenticate on this wireless interface.  Note that no
	distinction is made between a MAC-layer or higher-layer
	authentication failure."
	::= { extremeWirelessInterfaceStatusEntry 3 }
	
extremeWirelessIntfTotalAssoc OBJECT-TYPE	
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of stations which have entered
	the associated state on this wireless interface since the 
	last time the counters were cleared."
	::= { extremeWirelessInterfaceStatusEntry 4 }

extremeWirelessIntfTotalAssocFailed OBJECT-TYPE   
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the total number of stations which have failed
    the association on this wireless interface since the 
    last time the counters were cleared."
    ::= { extremeWirelessInterfaceStatusEntry 5 }

extremeWirelessIntfRateDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the rate of clients being detected on this 
	 interface over a 5 second period using EMWA averaging."
	::= { extremeWirelessInterfaceStatusEntry 6 }

extremeWirelessIntfRateAuthed OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives teh rate of clients being authenticated on this 
    interface over a 5 second period using EMWA averaging."
	::= { extremeWirelessInterfaceStatusEntry 7 }

extremeWirelessIntfRateAuthFailed OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the rate of clients failed authentication on this
	 interface over a 5 second period using EMWA averaging."
	::= { extremeWirelessInterfaceStatusEntry 8 }

extremeWirelessIntfRateAssoc OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the rate of clients being associated on this 
    interface over a 5 second period using EMWA averaging."
    ::= { extremeWirelessInterfaceStatusEntry 9 }

extremeWirelessIntfRateAssocFailed OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the rate of clients failing association on this 
    interface over a 5 second period using EMWA averaging."
    ::= { extremeWirelessInterfaceStatusEntry 10 }

extremeWirelessIntfBlockTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "This value specifies the number of ticks until traffic will be 
    allowed back on this interface.  A non-zero value indicates that
    traffic has been blocked, most likely due to countermeasures."
    ::= { extremeWirelessInterfaceStatusEntry 11 }

extremeWirelessIntfCurrentDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the current number of stations which are in
	the detected state on this wireless interface."
	::= { extremeWirelessInterfaceStatusEntry 12 }
	
extremeWirelessIntfCurrentAuthed OBJECT-TYPE	
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the current number of stations which are in
	the authenticated state on this wireless interface."
	::= { extremeWirelessInterfaceStatusEntry 13 }
	
extremeWirelessIntfCurrentAssoc OBJECT-TYPE	
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the current number of stations which are in
	the associated state on this wireless interface."
	::= { extremeWirelessInterfaceStatusEntry 14 }

extremeWirelessIntfCurrentForwarding OBJECT-TYPE	
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the current number of stations which are in
	the forwarding state on this wireless interface."
	::= { extremeWirelessInterfaceStatusEntry 15 }

--TODO: Do we need to fix this table
-- ************************************************************** --
--                     AP Wireless Client Table                    --
-- ************************************************************** --
extremeWirelessClientTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessClientEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table augments the Dot11AccessPointAddressTableEntry
		 to add a per-client VLAN entry."
	::= { extremeAP 11 }
	
extremeWirelessClientEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessClientEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
	"An entry in the extremeWirelessClientTable. It is possible
	 for there to be multiple IEEE 802.11 interfaces on one agent,
	 each with its unique MAC address. The relationship between an
	 IEEE 802.11 interface and an interface in the context of the
	 Internet-standard MIB is one-to-one. As such, the value of an
	 ifIndex object instance can be directly used to identify
	 corresponding instances of the objects defined herein. by
	 ifIndex."
    INDEX {ifIndex, extremeWirelessClientID} 
	::= { extremeWirelessClientTable 1 }

ExtremeWirelessClientEntry ::= SEQUENCE {
	extremeWirelessClientID 		MacAddress, 
	extremeWirelessClientState 		INTEGER, 
	extremeWirelessClientEncryption 	WPACipherSet, 
	extremeWirelessClientSignalStrength INTEGER, 
	extremeWirelessClientLinkQuality 	INTEGER, 
	extremeWirelessClientVLAN		INTEGER,
	extremeWirelessClientPriority		INTEGER,
	extremeWirelessClientAuthType		ClientAuthType,
	extremeWirelessClientLastStateChangeTime 	TimeTicks, 
	extremeWirelessClientTxFrames		Counter32,
	extremeWirelessClientRxFrames		Counter32,
	extremeWirelessClientTxBytes		Counter64,
	extremeWirelessClientRxBytes		Counter64,
	extremeWirelessClientLastPacketType	INTEGER,
	extremeWirelessClientSSID		OCTET STRING,
	extremeWirelessClientStatus		RowStatus,
    extremeWirelessClientIP	                IpAddress,
    extremeWirelessClientUsername           DisplayString,
    extremeWirelessClientDecryptionFailures Counter32,
    extremeWirelessClientMICFailures        Counter32}

extremeWirelessClientID OBJECT-TYPE 
    SYNTAX MacAddress 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
	"The Unique MAC Address of the station for which this address 
	 table entry pertains." 
    ::= { extremeWirelessClientEntry 1 } 

extremeWirelessClientState OBJECT-TYPE 
    SYNTAX INTEGER { detected (1),
			authenticated (2), 
			associated (3) ,
			data-forwarding (4)} 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
	"This attribute shall indicate the current state of the connection
	 between a wireless station and the AP. The attribute is
	 enumerated as follows: 

	1 - Detected - station has been detected, but not is communicating with AP
	2 - Authenticated - station is authenticated but not currently 
			associated. 
	3 - Associated - station is authenticated and associated 
	4 - Data-forwarding - station is on the network
	It is assumed that if an station is deauthenticated, or disassociated 
	then it no longer has an entry the AP's Address Table." 
    ::= { extremeWirelessClientEntry 2 } 

extremeWirelessClientEncryption OBJECT-TYPE 
    SYNTAX WPACipherSet
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
	"This attribute is contains the encryption mechanism being used 
	 by the station in an AP that allows mixed encryption modes." 
    ::= { extremeWirelessClientEntry 3 } 

extremeWirelessClientSignalStrength OBJECT-TYPE 
    SYNTAX INTEGER (1..100) 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
	"This attribute shall specify the signal strength of the last
	 frame received from the station in - dBm. e.g. a value of 50
	 implies -50 dBm." 
    ::= { extremeWirelessClientEntry 4 } 

extremeWirelessClientLinkQuality OBJECT-TYPE 
    SYNTAX INTEGER (1..100) 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
	"This attribute shall contain an indication of the quality 
	 of the signal as measured in the last frame received from the 
	 station. TBD format of this attribute" 
    ::= { extremeWirelessClientEntry 5 } 

extremeWirelessClientVLAN OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is the per-client VLAN which was pushed over
		 by a RADIUS server or other mechanism."
	::= { extremeWirelessClientEntry 6 }

extremeWirelessClientPriority OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is a per-client 802.1p value which was either
		 configured by the RADIUS server or statically through
		 the port configuration table."
	::= { extremeWirelessClientEntry 7 }

extremeWirelessClientAuthType OBJECT-TYPE
	SYNTAX		ClientAuthType
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is an indication of what access method was 
		 used to authenticated the client.  The strongest
		 mechanism used should be listed here, in case more
		 than one was used.  For example, if a combination of
		 mac-based shared-key and dot1x were used, this value
		 should indicated dot1x."
	::= { extremeWirelessClientEntry 8 }

extremeWirelessClientLastStateChangeTime OBJECT-TYPE
	SYNTAX		TimeTicks
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"This is the sysUpTime of the switch when the clients state changed last"
	::= { extremeWirelessClientEntry 9 }

extremeWirelessClientTxFrames OBJECT-TYPE
	SYNTAX	Counter32
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"This is the number of packets transmitted to the client"
	::= { extremeWirelessClientEntry 10 }

extremeWirelessClientRxFrames OBJECT-TYPE
	SYNTAX	Counter32
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"This is the number of packets transmitted by the client"
	::= { extremeWirelessClientEntry 11 }

extremeWirelessClientTxBytes OBJECT-TYPE
	SYNTAX	Counter64
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"This is the number of bytes transmitted to the client"
	::= { extremeWirelessClientEntry 12 }

extremeWirelessClientRxBytes OBJECT-TYPE
	SYNTAX	Counter64
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"This is the number of bytes transmitted by the client"
	::= { extremeWirelessClientEntry 13 }

extremeWirelessClientLastPacketType OBJECT-TYPE
	SYNTAX	INTEGER { data (0),
				psPoll (1),
				probeRequest (2),
				disassociation (3),
				deauthentication (4),
				association (5),
				reassociation (6),
				authentication (7) }
--				inf-beacon (8),
--				adhoc-beacon (9) }				
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"This the the last packet type seen from the specific client"
	::= { extremeWirelessClientEntry 14 }

extremeWirelessClientSSID OBJECT-TYPE
	SYNTAX	OCTET STRING
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"The SSID that this client was using"
	::= { extremeWirelessClientEntry 15 }

extremeWirelessClientStatus OBJECT-TYPE
	SYNTAX	RowStatus
	MAX-ACCESS	read-write
	STATUS	current
	DESCRIPTION
		"This variable is used to delete an entry from the table.
		The only value this can be set to, is delete (6).
		
		If an entry is deleted, that is already in Associated or 
		data-forwarding state, then the AP should dis-associate the 
		particular client by sending a disassociate message.
		
		If an entry is in Authenticated state, and is deleted, then 
		the AP should 'deAuthenticate' that client.
		
		If an entry is in 'detected' state and is deleted, then the
		entry should just be removed from the table."
	::= { extremeWirelessClientEntry 16 }

extremeWirelessClientIP OBJECT-TYPE
	SYNTAX		IpAddress
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"IP Address of the wireless client."
	::= { extremeWirelessClientEntry 17 }

    extremeWirelessClientUsername OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Username (if any) of the wireless client."
    ::= { extremeWirelessClientEntry 18 }

extremeWirelessClientDecryptionFailures OBJECT-TYPE
	SYNTAX	Counter32
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"This is the number of failures during decrypting packets from this client."
	::= { extremeWirelessClientEntry 19 }

extremeWirelessClientMICFailures OBJECT-TYPE
	SYNTAX	Counter32
	MAX-ACCESS	read-only
	STATUS	current
	DESCRIPTION
		"This is the number of MIC failures for this client."
	::= { extremeWirelessClientEntry 20 }

-- ************************************************************** --
--                  RF Profile Table                         	   --
-- ************************************************************** --
extremeRFProfile OBJECT IDENTIFIER ::= { extremeProfile 1 }

extremeRFProfileTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeRFProfileEntry
	MAX-ACCESS 	not-accessible
	STATUS		current	
	DESCRIPTION
		"There is one entry in this table for each RF profile
		 defined on the LAC.  Configuration for wireless intf.
		 should only be done by manipulating these variables.

		 Changes here will be reflected in the corresponding
		 802dot11 MIB variables (see references below).

		 On bootup this table should be populated with one
		 default entry for each supported type (A, G, etc)."
	::= { extremeRFProfile 1 } 

extremeRFProfileEntry OBJECT-TYPE
	SYNTAX		ExtremeRFProfileEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		""
	INDEX { extremeRFProfileIndex }
	::= { extremeRFProfileTable 1 }

ExtremeRFProfileEntry ::= SEQUENCE {
	extremeRFProfileIndex		INTEGER,
	extremeRFProfileName		DisplayString,
	extremeRFProfileType		Dot11Type,
	extremeRFProfileBeaconInterval 	INTEGER,
	extremeRFProfileDTIM		INTEGER,
	extremeRFProfileFragLength	INTEGER,
	extremeRFProfileRTSThresh	INTEGER,
	extremeRFProfilePreamble	INTEGER,
	extremeRFProfileShortRetry	INTEGER,
	extremeRFProfileLongRetry	INTEGER,
	extremeRFProfileStatus		RowStatus }

extremeRFProfileIndex OBJECT-TYPE
	SYNTAX		INTEGER(1..34)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"Integer index for this table.  This is here to avoid
		 having to index into the table via the string name."
	::= { extremeRFProfileEntry 1 }

extremeRFProfileName OBJECT-TYPE
	SYNTAX		DisplayString (SIZE(1..32))
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"String name of this profile.  The value |default| is
		 a special token and should not be allowed."
	::= { extremeRFProfileEntry 2 }

extremeRFProfileType OBJECT-TYPE
	SYNTAX		Dot11Type
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the type of this RF profile.  This
		 will decide the ranges of some of the values
		 (i.e. channel)."
	::= { extremeRFProfileEntry 3 }

extremeRFProfileBeaconInterval OBJECT-TYPE
	SYNTAX		INTEGER(1..65535)
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the number of TU between beacon frames."
	REFERENCE "ieee802dot11.********."
	::= { extremeRFProfileEntry 5 }

extremeRFProfileDTIM OBJECT-TYPE
	SYNTAX		INTEGER(1..255)
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		" "
	REFERENCE "ieee802dot11.********."
	::= { extremeRFProfileEntry 6 }

extremeRFProfileFragLength OBJECT-TYPE
	SYNTAX		INTEGER(256..2345)
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		" "
	REFERENCE "ieee802dot11.*******."
	::= { extremeRFProfileEntry 7 }

extremeRFProfileRTSThresh OBJECT-TYPE
	SYNTAX		INTEGER(0..2347)
	MAX-ACCESS	read-create
	STATUS		current	
	DESCRIPTION 
		" "
	REFERENCE "ieee802dot11.*******."
	::= { extremeRFProfileEntry 8 }

extremeRFProfilePreamble OBJECT-TYPE
	SYNTAX		INTEGER { short(0),
				  long(1) }
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This changes the 802 preamble expected by the
		 interface."
	::= { extremeRFProfileEntry 9 }

extremeRFProfileShortRetry OBJECT-TYPE
        SYNTAX	        INTEGER (1..255)
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"Attribute indicates the maximum number of transmission
		 attempts of a frame whose size is <= RTS."
	REFERENCE "dot11ShortRetryLimit"
	::= { extremeRFProfileEntry 11 }

extremeRFProfileLongRetry OBJECT-TYPE
	SYNTAX		INTEGER (1..255)
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION		
		"Attribute indicates the maximum number of transmission
		 attempts of a frame whose size is > RTS."
	REFERENCE "dot11LongRetryLimit"
	::= { extremeRFProfileEntry 12 }

extremeRFProfileStatus OBJECT-TYPE
	SYNTAX		RowStatus
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"Standard row-status semantics."
	::= { extremeRFProfileEntry 13 }


-- ************************************************************** --
--                      Security Profile Table                     --
-- ************************************************************** --


extremeSecurityProfile OBJECT IDENTIFIER ::= { extremeProfile 2 }

extremeSecurityProfileTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeSecurityProfileEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"There is one entry in this table per security 
		 profile on the LAC.  Each profile is identified by a
		 string name."
	::= { extremeSecurityProfile 1 }

extremeSecurityProfileEntry OBJECT-TYPE
	SYNTAX		ExtremeSecurityProfileEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		""
	INDEX { extremeSecurityProfileIndex }
	::= { extremeSecurityProfileTable 1 }

ExtremeSecurityProfileEntry ::= SEQUENCE {
	extremeSecurityProfileIndex 		INTEGER,
	extremeSecurityProfileName		DisplayString,
	extremeSecurityProfileESSName		OCTET STRING,
	extremeSecurityProfileSSIDInBeacon		TruthValue,
	extremeSecurityProfileDot11AuthMode	Dot11AuthMode,
	extremeSecurityProfileNetworkAuthMode	NetworkAuthMode,
	extremeSecurityProfileDataVlan			INTEGER,
	extremeSecurityProfileIgnoreVSAVlan		TruthValue,
    extremeSecurityWEPDefaultKey  INTEGER,
	extremeSecurityProfileEncryptionLength 	INTEGER, 
	extremeSecurityProfileStatus		RowStatus }

extremeSecurityProfileIndex OBJECT-TYPE
	SYNTAX		INTEGER(1..32)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This is an integer index used instead of string
		 name."
	::= { extremeSecurityProfileEntry 1 }

extremeSecurityProfileName OBJECT-TYPE
	SYNTAX		DisplayString (SIZE(1..32))
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is a unique string name which identifies this
		 profile.  The token |default| is special and should
		 not be allowed."
	::= { extremeSecurityProfileEntry 2 }

extremeSecurityProfileESSName OBJECT-TYPE
	SYNTAX OCTET STRING (SIZE(0..32))
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the desired ESS name."
	REFERENCE "ieee802dot11.*******"
	::= { extremeSecurityProfileEntry 3 }

extremeSecurityProfileSSIDInBeacon OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"Setting this value to true will have the channel
		 place the ssid in the beacon frame."
	::= { extremeSecurityProfileEntry 4 }

extremeSecurityProfileDot11AuthMode OBJECT-TYPE
	SYNTAX		Dot11AuthMode
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"Specified whether to use open or shared authentication"
	::= { extremeSecurityProfileEntry 5 }

extremeSecurityProfileNetworkAuthMode OBJECT-TYPE
	SYNTAX		NetworkAuthMode
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This specifies the authentication to use on top of dot11 mac authentication"
	::= { extremeSecurityProfileEntry 6 }

extremeSecurityProfileDataVlan OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This specifies the VLAN to use in case it does not come back as a VSA or in case the IgnoreVSAVlan is set
		 to true"
	::= { extremeSecurityProfileEntry 7 }

extremeSecurityProfileIgnoreVSAVlan OBJECT-TYPE
 	SYNTAX		TruthValue
 	MAX-ACCESS	read-create
 	STATUS		current
 	DESCRIPTION     "This specifies if Vlan associations in VSAs from the radius server has to be ignored or not"
 	::= { extremeSecurityProfileEntry 8 }

extremeSecurityWEPDefaultKey OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This value indicates the index in extremeWEPKeyTable that 
		identifies the key this interface will use to decrypt packets."
	::= { extremeSecurityProfileEntry 9 }

extremeSecurityProfileEncryptionLength OBJECT-TYPE
    SYNTAX      INTEGER { none(0),
			  sixtyfour(64),
                          onetwentyeight(128)  }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This gives the size in bits of the key.
         This variable defines the length of all WEP keys and also
         the encryption to use in case of legacy dot1x.
         If the encryption is set to none then no WEP encryption is set."
    ::= { extremeSecurityProfileEntry 10 }

-- extremeSecurityEnableWebNetlogin OBJECT-TYPE
--        SYNTAX          TruthValue
--        MAX-ACCESS      read-create
--        STATUS          current
--        DESCRIPTION
--                "Enable disable netlogin on the wireless interface where
--                security profile is applied."
--        ::= { extremeSecurityProfileEntry 11 }

extremeSecurityProfileStatus OBJECT-TYPE
	SYNTAX		RowStatus
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"Standard row-creation semantics."
	::= { extremeSecurityProfileEntry 11 }

extremeSecurityDot1xConfigTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeSecurityDot1xConfigEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"There is one entry in this table per security 
		 profile on the LAC. It reflects the dot1x security config"
	::= { extremeSecurityProfile 2 }

extremeSecurityDot1xConfigEntry OBJECT-TYPE
	SYNTAX	ExtremeSecurityDot1xConfigEntry
	MAX-ACCESS	not-accessible
	STATUS	current
	DESCRIPTION
		""
	INDEX { extremeSecurityProfileIndex }
	::= { extremeSecurityDot1xConfigTable 1 }

ExtremeSecurityDot1xConfigEntry ::= SEQUENCE {
	extremeSecurityKeyMgmtSuite		WPAKeyMgmtSet,
	extremeSecurityMcastCipherSuite	WPACipherSet,
	extremeSecurityDot1xPSKValue		OCTET STRING,
	extremeSecurityDot1xPSKPassPhrase	DisplayString,
	extremeSecurityDot1xReAuthPeriod	INTEGER,
	extremeSecurityGroupUpdateTimeOut	Unsigned32,
	extremeSecurityPairwiseUpdateTimeOut Unsigned32,
        extremeSecurityDot11iPreauthEnabled     TruthValue
}

extremeSecurityKeyMgmtSuite OBJECT-TYPE
	SYNTAX	WPAKeyMgmtSet
	MAX-ACCESS	read-write
	STATUS	current
	DESCRIPTION
		"This bitmask configures the authentication suites to be used."
	::= { extremeSecurityDot1xConfigEntry 1 }

extremeSecurityMcastCipherSuite OBJECT-TYPE
	SYNTAX	WPACipherSet
	MAX-ACCESS	read-write
	STATUS	current
	DESCRIPTION
		"This configures the cipher suite to use for mcast traffic. The cipher suite to use for unicast traffic
		 is derived from this using the following algorithm:
			Mcast cipher = WEP (64/128), Unicast cipher = TKIP
			Mcast cipher = TKIP, Unicast cipher = TKIP
			Mcast cipher = AES, Unicast cipher = AES.
		 This therefore determines the max unicast cipher suite the client can use to associate with 
		 this interface."
	::= { extremeSecurityDot1xConfigEntry 2 }


extremeSecurityDot1xPSKValue OBJECT-TYPE
	SYNTAX		OCTET STRING (SIZE(32))
	MAX-ACCESS	read-write
	STATUS		current	
	DESCRIPTION
		"The Pre-Shared Key (PSK) for when RSN in PSK mode is
		 the selected authentication suite."
	REFERENCE "dot11RSNConfigPSKValue"
	::= { extremeSecurityDot1xConfigEntry 3 }

extremeSecurityDot1xPSKPassPhrase OBJECT-TYPE
	SYNTAX		DisplayString
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
	"The PSK, for when WPA in PSK mode is the selected authentication
	 suite, is configured by extremeSecurityKeyMgmtSuite.
	 An alternative manner of setting the PSK uses the password-to-key
	 algorithm defined in section XXX. This variable provides a means
	 to enter a pass phrase. When this object is written, the RSN entity
	 shall use the password-to-key algorithm specified in section XXX to
	 derive a pre-shared and populate extremeSecurityDot1xPSKValue with this key.
	 This object is logically write-only. Reading this variable shall
	 return unsuccessful status or null or zero."
	::= { extremeSecurityDot1xConfigEntry 4 }

extremeSecurityDot1xReAuthPeriod OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"Number of seconds a clients authentication will last before
		 the AP automatically issues a reauthentication request."
	::= { extremeSecurityDot1xConfigEntry 5 }

extremeSecurityGroupUpdateTimeOut OBJECT-TYPE
	SYNTAX		Unsigned32 (1..1440)
	UNITS		"minutes"
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"The time in minutes after which the RSN group
		 update handshake will be retried. The timer shall
		 start at the moment the group update message is
		 sent."
	REFERENCE "dot11RSNConfigGroupUpdateTimeOut"
	DEFVAL		{ 100 } -- 
	::= { extremeSecurityDot1xConfigEntry 6 }

extremeSecurityPairwiseUpdateTimeOut OBJECT-TYPE
	SYNTAX		Unsigned32 (1..1440)
	UNITS		"minutes"
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"The time in minutes after which the RSN 4-way
		 handshake will be retried. The timer shall start at
		 the moment a 4-way message is sent."
	REFERENCE "dot11RSNConfigPairwiseUpdateTimeOut"
	DEFVAL		{ 100 } -- 
	::= { extremeSecurityDot1xConfigEntry 7 }

extremeSecurityDot11iPreauthEnabled OBJECT-TYPE
        SYNTAX          TruthValue
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION     "Whether preauth is enabled or not"
        ::= { extremeSecurityDot1xConfigEntry 8 }


-- ************************************************************** --
--                     Default WEP Key Table                       --
-- ************************************************************** --
extremeWEPKeyTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeWEPKeyEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains per-profile WEP keys.

		 Reference ieee802dot11.1.2.3."
	::= { extremeSecurityProfile 4 }

extremeWEPKeyEntry OBJECT-TYPE
	SYNTAX	ExtremeWEPKeyEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeSecurityProfileIndex, extremeWEPKeyIndex }
	::= { extremeWEPKeyTable 1 }
	
ExtremeWEPKeyEntry ::= SEQUENCE {
	extremeWEPKeyIndex	INTEGER,
	extremeWEPKey		OCTET STRING,
	extremeWEPKeyStatus	RowStatus }
	
extremeWEPKeyIndex OBJECT-TYPE
	SYNTAX 		INTEGER(0..8)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"There can be upto 8 wep keys for any wireless
		 interface. Four of the keys are to be used with
		 WEP Authentication, and the other four with MAC Auth."
	::= { extremeWEPKeyEntry 1 }

extremeWEPKey OBJECT-TYPE
	SYNTAX		OCTET STRING (SIZE(1..19))
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is a write-only wep key."
	::= { extremeWEPKeyEntry 2 }

extremeWEPKeyStatus OBJECT-TYPE
	SYNTAX		RowStatus
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"Standard row-creation semantics."
	::= { extremeWEPKeyEntry 3 }

-- ************************************************************** --
--              Wireless Physical Interface Config Table           --
-- ************************************************************** --
extremeWirelessPhysInterfaceConfigTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeWirelessPhysInterfaceConfigEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains the configured bindings between	
		 physical wireless interfaces and RF profiles and other
		 RF properties. There is one instance of a physical wireless
		 interface per radio on the AP.

		 Entries in this table have a duration that exceeds
		 the lifetime of entries in the wirelessPhysInterfaceCtlTable.
		 A configured binding spans the reset or removal of 
		 a wireless interface.

		 Entries can be removed from this table by the user.

		 If there is a mismatch between a new wireless port
		 and a previously configured profile, the default
		 profile will be applied and this table will be
		 updated."
	::= { extremeProfile 3 }

extremeWirelessPhysInterfaceConfigEntry OBJECT-TYPE
	SYNTAX	ExtremeWirelessPhysInterfaceConfigEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeWirelessPhysInterfaceIndex }
	::= { extremeWirelessPhysInterfaceConfigTable 1 }

ExtremeWirelessPhysInterfaceConfigEntry ::= SEQUENCE {
	extremeWirelessPhysInterfaceConfigRFProfile		INTEGER,
	extremeWirelessPhysInterfaceConfigRFChannel		INTEGER,
	extremeWirelessPhysInterfaceConfigSpeed		Dot11Speed,
	extremeWirelessPhysInterfaceConfigPowerLevel	INTEGER 
}
	
extremeWirelessPhysInterfaceConfigRFProfile OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the index into the extremeRFProfileTable for
		 the RF profile that this wireless interface is bound 
		 to.  

		 The default value is equivalent to the profile index for
		 the interface-appropriate default in the table."
	::= { extremeWirelessPhysInterfaceConfigEntry 1 }
		 
extremeWirelessPhysInterfaceConfigRFChannel	OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the RF channel which has been selected for this
		 wireless interface.  It should be one of the supported 
		 channels as given by the phy tables in the dot11 mib."
	::= { extremeWirelessPhysInterfaceConfigEntry 2 }

extremeWirelessPhysInterfaceConfigSpeed OBJECT-TYPE
	SYNTAX		Dot11Speed
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the speed to configure the interface at. A value of 0 indicates
		 automatic speed detection."
	::= { extremeWirelessPhysInterfaceConfigEntry 3 }

extremeWirelessPhysInterfaceConfigPowerLevel OBJECT-TYPE
	SYNTAX		INTEGER 
	MAX-ACCESS 	read-create
	STATUS		current
	DESCRIPTION
		"This value specifies the tx power (in % of max power) to use."
	::= { extremeWirelessPhysInterfaceConfigEntry 4 }

-- ************************************************************** --
--              Wireless Virtual  Interface Config Table           --
-- ************************************************************** --
extremeWirelessVirtInterfaceConfigTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeWirelessVirtInterfaceConfigEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains the configured bindings between	
		 virtual wireless interfaces and security profiles and other
		 security properties. There are upto 8 virtual interface for
		 each physical interface.

		 Entries in this table have a duration that exceeds
		 the lifetime of entries in the wirelessVirtInterfaceCtlTable.
		 A configured binding spans the reset or removal of 
		 a wireless interface.

		 Entries can be removed from this table by the user."

	::= { extremeProfile 4 }

extremeWirelessVirtInterfaceConfigEntry OBJECT-TYPE
	SYNTAX	ExtremeWirelessVirtInterfaceConfigEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeWirelessVirtInterfaceIndex }
	::= { extremeWirelessVirtInterfaceConfigTable 1 }

ExtremeWirelessVirtInterfaceConfigEntry ::= SEQUENCE {
	extremeWirelessVirtInterfaceConfigSecurityProfile	INTEGER,
	extremeWirelessVirtInterfaceConfigMaxClients			INTEGER,
	extremeWirelessVirtInterfaceConfigWirelessBridging		TruthValue,
	extremeWirelessVirtInterfaceConfigState			INTEGER
}
	
extremeWirelessVirtInterfaceConfigSecurityProfile OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the index into the extremeSecurityProfileTable for
		 the security profile that this wireless interface is bound 
		 to.  

		 The default value is equivalent to the profile index for
		 the interface-appropriate default in the table."
	::= { extremeWirelessVirtInterfaceConfigEntry 1 }

extremeWirelessVirtInterfaceConfigMaxClients OBJECT-TYPE
	SYNTAX		INTEGER (1..128)
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
		"The maximum number of clients that can associate with this virtual interface."
	::= { extremeWirelessVirtInterfaceConfigEntry 2 }

extremeWirelessVirtInterfaceConfigWirelessBridging OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This specifies whether wireless bridging is enabled on this interface.
		 If wireless bridging is enabled then traffic originating from a client on this
		 interface will be bridged to clients on the same VLAN on this or other 
		 virtual interfaces that have wireless bridging enabled. So basically
		 wireless bridging should be enabled on both the source and the destination
		 virtual interface for traffic to be bridged."
	::= { extremeWirelessVirtInterfaceConfigEntry 3 }

extremeWirelessVirtInterfaceConfigState OBJECT-TYPE
	SYNTAX		INTEGER { enabled(0), disabled(1) } 
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This value specifies whether the virtual interface is active or not"
	::= { extremeWirelessVirtInterfaceConfigEntry 4 }

-- ************************************************************** --
--                      Antenna Profile Table                     --
-- ************************************************************** --

extremeAntennaProfile OBJECT IDENTIFIER ::= { extremeProfile 5 }

extremeAntennaProfileTable OBJECT-TYPE
SYNTAX  SEQUENCE OF ExtremeAntennaProfileEntry
    	MAX-ACCESS  not-accessible
    	STATUS      current
    	DESCRIPTION
        	"There is one entry in this table per  antenna
         	profile on the LAC.  Each profile is identified by a
         	string name."
    	::= { extremeAntennaProfile 1 }

extremeAntennaProfileEntry OBJECT-TYPE
SYNTAX      ExtremeAntennaProfileEntry
MAX-ACCESS  not-accessible
STATUS      current
DESCRIPTION
""
    	INDEX { extremeAntennaProfileIndex }
    	::= { extremeAntennaProfileTable 1 }

ExtremeAntennaProfileEntry ::= SEQUENCE
{
    extremeAntennaProfileIndex 		INTEGER,
    extremeAntennaProfileName		DisplayString,
    extremeAntennaProfile2point4GHZGain	INTEGER,
    extremeAntennaProfile5GHZGain		INTEGER,
    extremeAntennaProfileStatus		RowStatus 
}

extremeAntennaProfileIndex OBJECT-TYPE
    SYNTAX      INTEGER(1..32)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
    "This is an integer index used instead of string name."
    ::= { extremeAntennaProfileEntry 1 }

extremeAntennaProfileName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
    "This is a unique string name which identifies this
      profile.  The token |default| is special and should
      not be allowed."
    ::= { extremeAntennaProfileEntry 2 }

extremeAntennaProfile2point4GHZGain OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This specifies 2.4GHz gain value for detachable antenna in dbi"
    ::= { extremeAntennaProfileEntry 3 }

extremeAntennaProfile5GHZGain OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This specifies 5GHz gain value for detachable antenna in dbi"
    ::= { extremeAntennaProfileEntry 4 }

extremeAntennaProfileStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Standard row-creation semantics."
    ::= { extremeAntennaProfileEntry 5 }

-- ************************************************************** --
--                   RemoteConnect configuration                         --
-- ************************************************************** --


extremeWirelessRemoteConnectGlobalCfgGroup OBJECT IDENTIFIER ::=   { extremeProfile 6 }

extremeWirelessRemoteConnectGlobalBindingType OBJECT-TYPE
  SYNTAX		WirelessRemoteConnectBindingType
  MAX-ACCESS  read-write
  STATUS current
  DESCRIPTION
  "Global Binding Tye"
 ::= { extremeWirelessRemoteConnectGlobalCfgGroup 1 }

extremeWirelessRemoteConnectBindingTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessRemoteConnectBindingEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table specifies the RemoteConnect binding configuration for various ports."
	::= { extremeProfile 7 }

extremeWirelessRemoteConnectBindingEntry OBJECT-TYPE
	SYNTAX 		ExtremeWirelessRemoteConnectBindingEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeWirelessRemoteConnectBindingPortIndex }
	::= { extremeWirelessRemoteConnectBindingTable 1 }

ExtremeWirelessRemoteConnectBindingEntry ::= SEQUENCE {
	extremeWirelessRemoteConnectBindingPortIndex	INTEGER,
	extremeWirelessRemoteConnectBindingIfIndex	Integer32,
	extremeWirelessRemoteConnectBindingType	WirelessRemoteConnectBindingType,
	extremeWirelessRemoteConnectBindingMAC	MacAddress,
	extremeWirelessRemoteConnectBindingSerial	OCTET STRING,
	extremeWirelessRemoteConnectBindingIPAddressType	InetAddressType,
	extremeWirelessRemoteConnectBindingIPAddress	InetAddress,
	extremeWirelessRemoteConnectBindingEnabled	TruthValue,
	extremeWirelessRemoteConnectBindingBound	TruthValue,
	extremeWirelessRemoteConnectBindingRowStatus	RowStatus}

extremeWirelessRemoteConnectBindingPortIndex	OBJECT-TYPE
	SYNTAX	INTEGER(1..48)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION	"The index of this binding. This maps one-to-one with the value
			specified in the CLI. For example, if you say v:1 in the CLI, then
			this value should be 1."
	::= { extremeWirelessRemoteConnectBindingEntry 1}

extremeWirelessRemoteConnectBindingIfIndex	OBJECT-TYPE
	SYNTAX	Integer32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION	"This is the IF index that is used in the extremeWirelessPortConfigTable
			etc to refer to this RemoteConnect port."
	::= { extremeWirelessRemoteConnectBindingEntry 2}

extremeWirelessRemoteConnectBindingType OBJECT-TYPE
	SYNTAX		WirelessRemoteConnectBindingType
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the type of binding. Based on this, one of the following columns will be valid."
	::= { extremeWirelessRemoteConnectBindingEntry 3 }

extremeWirelessRemoteConnectBindingMAC OBJECT-TYPE
	SYNTAX		MacAddress
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The MAC address of the AP that is bound to this virtual port. 
			This is valid only if the extremeWirelessRemoteConnectBindingType is set to
			mac-address"
	::= { extremeWirelessRemoteConnectBindingEntry 4}

extremeWirelessRemoteConnectBindingSerial OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The serial number of the AP that is bound to this virtual port. 
			This is valid only if the extremeWirelessRemoteConnectBindingType is set to
			serial-number"
	::= { extremeWirelessRemoteConnectBindingEntry 5}

extremeWirelessRemoteConnectBindingIPAddressType OBJECT-TYPE
	SYNTAX		InetAddressType
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The IP address type of the AP that is bound to this virtual port. 
			This is valid only if the extremeWirelessRemoteConnectBindingType is set to
			ip-address"
	::= { extremeWirelessRemoteConnectBindingEntry 6}

extremeWirelessRemoteConnectBindingIPAddress OBJECT-TYPE
	SYNTAX		InetAddress
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The IP address of the AP that is bound to this virtual port. 
			This is valid only if the extremeWirelessRemoteConnectBindingType is set to
			ip-address"
	::= { extremeWirelessRemoteConnectBindingEntry 7}

extremeWirelessRemoteConnectBindingEnabled	OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS 		current
	DESCRIPTION	"An entry in this table can be created but left as disabled. If you 
			set this variable to True then the RemoteConnect controller will consider
			this binding when trying to map APs to RemoteConnect ports. Else, it will
			ignore it."
	::= {extremeWirelessRemoteConnectBindingEntry 8}

extremeWirelessRemoteConnectBindingBound	OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS 		current
	DESCRIPTION	"Specifies if an AP has come up and has been matched to this binding.
			If this is true, then the extremeWirelessPortStatus table will have
			an entry for this RemoteConnect port."
	::= {extremeWirelessRemoteConnectBindingEntry 9}

extremeWirelessRemoteConnectBindingRowStatus	OBJECT-TYPE
	SYNTAX		RowStatus
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"RowStatus attribute. When this row is deleted, the binding goes away"
	::= {extremeWirelessRemoteConnectBindingEntry 10}

-- **************************************************************************************
-- This table specifies how the controller should choose to redirect certain APs to other
-- controllers
-- **************************************************************************************
extremeWirelessRemoteConnectRedirectBindingTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessRemoteConnectRedirectBindingEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table specifies the RemoteConnect redirection configuration for various ports."
	::= { extremeProfile 8 }

extremeWirelessRemoteConnectRedirectBindingEntry OBJECT-TYPE
	SYNTAX 		ExtremeWirelessRemoteConnectRedirectBindingEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeWirelessRemoteConnectRedirectBindingIndex }
	::= { extremeWirelessRemoteConnectRedirectBindingTable 1 }

ExtremeWirelessRemoteConnectRedirectBindingEntry ::= SEQUENCE {
	extremeWirelessRemoteConnectRedirectBindingIndex	INTEGER,
	extremeWirelessRemoteConnectRedirectBindingType	WirelessRemoteConnectBindingType,
	extremeWirelessRemoteConnectRedirectBindingMAC	MacAddress,
	extremeWirelessRemoteConnectRedirectBindingSerial	OCTET STRING,
	extremeWirelessRemoteConnectRedirectBindingIPAddressType	InetAddressType,
	extremeWirelessRemoteConnectRedirectBindingIPAddress	InetAddress,
	extremeWirelessRemoteConnectRedirectBindAttachSwitchIPAddrType	InetAddressType,
	extremeWirelessRemoteConnectRedirectBindAttachSwitchIPAddr	InetAddress,
	extremeWirelessRemoteConnectRedirectBindingEnabled		TruthValue,
	extremeWirelessRemoteConnectRedirectBindingNumRedirects	Counter32,
	extremeWirelessRemoteConnectRedirectBindingRowStatus		RowStatus}

extremeWirelessRemoteConnectRedirectBindingIndex	OBJECT-TYPE
	SYNTAX	INTEGER(1..255)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION	"The index of this RedirectBinding."
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 1}

extremeWirelessRemoteConnectRedirectBindingType OBJECT-TYPE
	SYNTAX		WirelessRemoteConnectBindingType
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION
		"This is the type of RedirectBinding. Based on this, one of the following columns will be valid."
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 2 }

extremeWirelessRemoteConnectRedirectBindingMAC OBJECT-TYPE
	SYNTAX		MacAddress
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The MAC address of the AP that will be redirected. 
			This is valid only if the extremeWirelessRemoteConnectRedirectBindingType is set to
			mac-address"
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 3}

extremeWirelessRemoteConnectRedirectBindingSerial OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The serial number of the AP that is bound to this virtual port. 
			This is valid only if the extremeWirelessRemoteConnectRedirectBindingType 
			is set to serial-number"
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 4}

extremeWirelessRemoteConnectRedirectBindingIPAddressType OBJECT-TYPE
	SYNTAX		InetAddressType
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The IP address type of the AP that will be redirected. 
			This is valid only if the extremeWirelessRemoteConnectRedirectBindingType 
			is set to ip-address"
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 5}

extremeWirelessRemoteConnectRedirectBindingIPAddress OBJECT-TYPE
	SYNTAX		InetAddress
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The IP address of the AP that will be redirected. 
			This is valid only if the extremeWirelessRemoteConnectRedirectBindingType 
			is set to ip-address"
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 6}

extremeWirelessRemoteConnectRedirectBindAttachSwitchIPAddrType OBJECT-TYPE
	SYNTAX		InetAddressType
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The IP address type of the slave "
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 7}

extremeWirelessRemoteConnectRedirectBindAttachSwitchIPAddr OBJECT-TYPE
	SYNTAX		InetAddress
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"The IP address of the slave"
	::= { extremeWirelessRemoteConnectRedirectBindingEntry 8}

extremeWirelessRemoteConnectRedirectBindingEnabled	OBJECT-TYPE
	SYNTAX		TruthValue
	MAX-ACCESS	read-write
	STATUS 		current
	DESCRIPTION	"An entry in this table can be created but left as disabled. If you 
			set this variable to True then the RemoteConnect controller will consider
			this RedirectBinding when trying to redirect APs. Else, it will
			ignore it."
	::= {extremeWirelessRemoteConnectRedirectBindingEntry 9}

extremeWirelessRemoteConnectRedirectBindingNumRedirects	OBJECT-TYPE
	SYNTAX		Counter32
	MAX-ACCESS	read-only
	STATUS 		current
	DESCRIPTION	"The number of times this redirect has been invoked."
	::= {extremeWirelessRemoteConnectRedirectBindingEntry 10}

extremeWirelessRemoteConnectRedirectBindingRowStatus	OBJECT-TYPE
	SYNTAX	RowStatus
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION	"Standard row status semantics."
	::= {extremeWirelessRemoteConnectRedirectBindingEntry 11}

-- **************************************************************************************
-- This table specifies the list of APs that could not be bound or redirected due to 
-- a lack of mapping on this controller
-- **************************************************************************************

extremeWirelessRemoteConnectDeviceDBGroup OBJECT IDENTIFIER ::=   { extremeProfile 9 }

extremeWirelessRemoteConnectDeviceDBTimeOut OBJECT-TYPE
  SYNTAX  INTEGER (0 | 30..3600)
  MAX-ACCESS  read-write
  STATUS current
  DESCRIPTION
  "Timeout value for unbound APs entry in seconds. 0 for disable"
 ::= { extremeWirelessRemoteConnectDeviceDBGroup 1 }


extremeWirelessRemoteConnectUnboundAPsTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessRemoteConnectUnboundAPsEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table contains the list of APs that could not be bound or redirected."
	::= { extremeProfile 10 }

extremeWirelessRemoteConnectUnboundAPsEntry OBJECT-TYPE
	SYNTAX 		ExtremeWirelessRemoteConnectUnboundAPsEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeWirelessRemoteConnectUnboundAPsIndex }
	::= { extremeWirelessRemoteConnectUnboundAPsTable 1 }

ExtremeWirelessRemoteConnectUnboundAPsEntry ::= SEQUENCE {
	extremeWirelessRemoteConnectUnboundAPsIndex	INTEGER,
	extremeWirelessRemoteConnectUnboundAPsMAC	MacAddress,
	extremeWirelessRemoteConnectUnboundAPsSerial	OCTET STRING,
	extremeWirelessRemoteConnectUnboundAPsIPAddressType	InetAddressType,
	extremeWirelessRemoteConnectUnboundAPsIPAddress	InetAddress,
	extremeWirelessRemoteConnectUnboundAPsNumAttempts	Counter32,
	extremeWirelessRemoteConnectUnboundAPsRowStatus	RowStatus}

extremeWirelessRemoteConnectUnboundAPsIndex	OBJECT-TYPE
	SYNTAX	INTEGER(0..65535)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION	"The index of this table."
	::= { extremeWirelessRemoteConnectUnboundAPsEntry 1}

extremeWirelessRemoteConnectUnboundAPsMAC OBJECT-TYPE
	SYNTAX		MacAddress
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION	"The MAC address of the unbound AP."
	::= { extremeWirelessRemoteConnectUnboundAPsEntry 2}

extremeWirelessRemoteConnectUnboundAPsSerial OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION	"The serial number of the unbound AP"
	::= { extremeWirelessRemoteConnectUnboundAPsEntry 3}

extremeWirelessRemoteConnectUnboundAPsIPAddressType OBJECT-TYPE
	SYNTAX		InetAddressType
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION	"The IP address type of the unbound AP"
	::= { extremeWirelessRemoteConnectUnboundAPsEntry 4}

extremeWirelessRemoteConnectUnboundAPsIPAddress OBJECT-TYPE
	SYNTAX		InetAddress
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION	"The IP address of the unbound AP"
	::= { extremeWirelessRemoteConnectUnboundAPsEntry 5}

extremeWirelessRemoteConnectUnboundAPsNumAttempts	OBJECT-TYPE
	SYNTAX		Counter32
	MAX-ACCESS	read-only
	STATUS 		current
	DESCRIPTION	"The number of times this AP contacted this controller."
	::= {extremeWirelessRemoteConnectUnboundAPsEntry 6}

extremeWirelessRemoteConnectUnboundAPsRowStatus	OBJECT-TYPE
	SYNTAX	RowStatus
	MAX-ACCESS	read-create
	STATUS		current
	DESCRIPTION	"Standard row status semantics. Only valid value to set is destroy (6)."
	::= {extremeWirelessRemoteConnectUnboundAPsEntry 7}

-- ************************************************************** --
--                   RADIUS Server Configuration                   --
-- The Radius Server Table on the Ethernet switch is implemented   --
-- using the extremeAuthServerTable, defines under extremeServices --
--                                                                 --
-- The extremeAPAuthServerTable is implemented per wireless port.  --
-- Each AP will have to implement this table to allow the LAC to   --
-- propogate configuration information.                            --
-- ************************************************************** --
extremeAPAuthServerTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF ExtremeAPAuthServerEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"This table is proxied by the wireless APs.  It 
		 allows the wireless concentrator to configure the 
		 APs security settings."
	::= { extremeAP 12 }

extremeAPAuthServerEntry OBJECT-TYPE
	SYNTAX 		ExtremeAPAuthServerEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { extremeWirelessPortIfIndex, extremeAPAuthServerIndex }
	::= { extremeAPAuthServerTable 1 }

ExtremeAPAuthServerEntry ::= SEQUENCE {
	extremeAPAuthServerIndex		INTEGER,
	extremeAPAuthServerAddressType          InetAddressType,
	extremeAPAuthServerAddress 		InetAddress,
	extremeAPAuthServerPort                 INTEGER,
	extremeAPAuthServerSecret 		OCTET STRING,
	extremeAPAuthServerReTransmit		INTEGER,
	extremeAPAuthServerStatus		TruthValue}

extremeAPAuthServerIndex OBJECT-TYPE
	SYNTAX		INTEGER (1..2)
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		"Integer representing either primary (1) or backup
		 (2) server."
	::= { extremeAPAuthServerEntry 1 }

extremeAPAuthServerAddressType          OBJECT-TYPE
        SYNTAX                          InetAddressType
        MAX-ACCESS                      read-only
        STATUS                          current
        DESCRIPTION
            "The type of address specified in the object
                'extremeAuthServerAddress'.
             Currently, only 'ipv4' and 'dns' are supported."
        DEFVAL { ipv4 }
      ::= { extremeAPAuthServerEntry 2 }

extremeAPAuthServerAddress OBJECT-TYPE
        SYNTAX          InetAddress
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
                "IP address of the radius server.  The act of
                 assigning an IP address enables the client to use
                 the server.  Setting this value to 0.0.0.0 disables
                 this entry."
        ::= { extremeAPAuthServerEntry 3 }

extremeAPAuthServerPort                    OBJECT-TYPE
       SYNTAX                          INTEGER (0..65535)
       MAX-ACCESS                      read-only
       STATUS                          current
       DESCRIPTION
           "The UDP port number of the remote syslog server
                to which syslog messages will be sent."
     ::= { extremeAPAuthServerEntry 4 }

extremeAPAuthServerSecret OBJECT-TYPE
        SYNTAX          OCTET STRING
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
                "This is the shared secret between the Authenticator
                 and radius server.  This is logically write-only."
        ::= { extremeAPAuthServerEntry 5 }

extremeAPAuthServerReTransmit OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-write
	STATUS		current
	DESCRIPTION
		"This is the timeout in seconds after which the 
		 Authenticator will re transmit requests to the
		 radius server."
	::= { extremeAPAuthServerEntry 6 }

extremeAPAuthServerStatus OBJECT-TYPE
        SYNTAX TruthValue
        MAX-ACCESS  read-write
        STATUS current
        DESCRIPTION
                "Specifies whether the DNS entry is in use or not"
	::= { extremeAPAuthServerEntry 7 }

-- ************************************************************** --
-- Scan Configuration Table : This table provides the ability to   --
-- configure the parameters associated with ON-Channel scanning    --
-- for other access points.                                        --
-- ************************************************************** --
extremeWirelessScanCfgTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessScanCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per wireless interface.  It
	 allows for configuration of active and passive scan
	 parameters."
	 ::= { extremeAP 24 }

extremeWirelessScanCfgEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessScanCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessScanCfgTable 1 }

ExtremeWirelessScanCfgEntry ::= SEQUENCE {
	extremeWirelessScanEnable			TruthValue,
	extremeWirelessScanSendProbe			TruthValue,
	extremeWirelessScanProbeInterval		Unsigned32,
	extremeWirelessScanResultTableSize		Unsigned32,
	extremeWirelessScanResultTimeout		Unsigned32,
	extremeWirelessScanResetStats			TruthValue,
	extremeWirelessScanClearTable			TruthValue,
	extremeWirelessScanSendAPAddedTrap		TruthValue,
	extremeWirelessScanSendAPRemovedTrap		TruthValue,
	extremeWirelessScanSendAPUpdatedTrap		TruthValue
}

extremeWirelessScanEnable OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"By setting this to true the wireless interface will collect
	 information from beacons and (possible) probe responses.  If
	 this value is set to false this information will not be 
	 collected."
	DEFVAL { true }
	 ::= { extremeWirelessScanCfgEntry 1 }

extremeWirelessScanSendProbe OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"If this value is set to TRUE the wireless interface will send
	 probe requests to the broadcast SSID every ProbeInterval.  The
	 results of these probes will be recorded in the scan results
	 table.  If this is false, the AP will only collect information
	 from beacon frames."
	DEFVAL { false }
	 ::= { extremeWirelessScanCfgEntry 2 }

extremeWirelessScanProbeInterval OBJECT-TYPE
	SYNTAX Unsigned32
	UNITS "milliseconds"
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This allows for the configuration of the interval over which
	 probe requests will be sent."
	DEFVAL { 100 }
	 ::= { extremeWirelessScanCfgEntry 3 }

extremeWirelessScanResultTableSize OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This parameter permits the configuration of the result table
	 size.  If there are more entries discovered than there are 
	 room for the new entry will replace the oldest entry in the
	 table.  In this case a new entry trap will be sent with the
	 OverFlow value set to TRUE."
	DEFVAL { 1024 }
	 ::= { extremeWirelessScanCfgEntry 4 }

extremeWirelessScanResultTimeout OBJECT-TYPE
	SYNTAX Unsigned32
	UNITS "seconds"
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"By default the scan result table will age out entries which
	 haven't been updated for some time.  When an entry is aged
	 out of the table a scanResultAgeOut trap is generated.  
	 Setting this value to 0 will disable the aging out of entries."
	DEFVAL { 3600 }
	 ::= { extremeWirelessScanCfgEntry 5 }

extremeWirelessScanResetStats OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the scan module to reset its
	 internal statistics, as well as the packet counts for each 
	 entry in the scan results table.  It will *not* remove any
	 entries in the scan table."
	 ::= { extremeWirelessScanCfgEntry 6 }

extremeWirelessScanClearTable OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the scan module to reset its
	 internal table.  All currently detected stations will be 
	 removed.  Scan statistics (i.e. watermark) will not be reset."
	 ::= { extremeWirelessScanCfgEntry 7 }

extremeWirelessScanSendAPAddedTrap OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the scan module to send a trap
	 whenever a new entry is added to the scan results table.  If
	 this value is false the scan module will suppress the trap."
	DEFVAL { true }
	 ::= { extremeWirelessScanCfgEntry 8 }

extremeWirelessScanSendAPRemovedTrap OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the scan module to send a trap
	 whenever an entry is removed from the scan results table."
	DEFVAL { true }
	 ::= { extremeWirelessScanCfgEntry 9 }

extremeWirelessScanSendAPUpdatedTrap OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the scan module to send a trap
	 whenever an entry is updated in the scan results table.  This 
	 will happen if the list of IEs sent from a particular MAC address
	 changes."
	DEFVAL { true }
	 ::= { extremeWirelessScanCfgEntry 10 }

-- ************************************************************** --
-- Off-Channel AP Scan : This table provides the ability to confi  --
-- ure and start an off-channel scan for other APs.                --
-- ************************************************************** --
extremeWirelessOffChannelScanCfgTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessOffChannelScanCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per wireless interface.  It
	 allows for configuration of off-channel scan.  To start an
	 off-channel scan the management station should write values
	 for channel list, min and max wait.  Toggling the start
	 value at this point will start an off-channel scan, unless
	 one is already running.
	 Results of the off-channel scan are reported to the scan
	 results table together with the results of passive scan."
	 ::= { extremeAP 25 }

extremeWirelessOffChannelScanCfgEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessOffChannelScanCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessOffChannelScanCfgTable 1 }

ExtremeWirelessOffChannelScanCfgEntry ::= SEQUENCE {
	extremeWirelessOffChannelScanStart		TruthValue,
	extremeWirelessOffChannelScanList		BITS,
	extremeWirelessOffChannelScanMinWait		Unsigned32,
	extremeWirelessOffChannelScanMaxWait		Unsigned32,
	extremeWirelessOffChannelContinuous		TruthValue
}

extremeWirelessOffChannelScanStart OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the scan module to start an
	 off-channel scan.  The radio will be disabled for the 
	 duration of the scan.  The scan module will change to each 
	 of the specified channels in order.  It will then conduct 
	 passive (and perhaps active) scans on that channel for the 
	 configured intervals.  When the scan is finished the radio 
	 will be re-enabled.
	 The scan will wait on each channel for at least MinWait 
	 milliseconds even if it sees no traffic on the channel. It
	 will wait for at most MaxWait msecs even if it sees traffic.
	 As a result the best case time for the scan is given by:
	 	# Of Channels * extremeWirelessOffChannelScanMinWait
 	 The worst case completion time is given by:
	 	# Of Channels * extremeWirelessOffChannelScanMaxWait
	 Results of the off-channnel scan a reported to the scan 
	 results table."
	 ::= { extremeWirelessOffChannelScanCfgEntry 1 }

extremeWirelessOffChannelScanList OBJECT-TYPE
	SYNTAX BITS {
		scanAll(0),
		scanEvery(1),
		scanChannel1(2),
		scanChannel2(3),
		scanChannel3(4),
		scanChannel4(5),
		scanChannel5(6),
		scanChannel6(7),
		scanChannel7(8),
		scanChannel8(9),
		scanChannel9(10),
		scanChannel10(11),
		scanChannel11(12),
		scanChannel12(13),
		scanChannel13(14),
		scanChannel14(15),
		scanChannel34(16),
		scanChannel36(17),
		scanChannel40(18),
		scanChannel44(19),
		scanChannel48(20),
		scanChannel52(21),
		scanChannel56(22),
		scanChannel60(23),
		scanChannel64(24),
		scanChannel100(25),
		scanChannel104(26),
		scanChannel108(27),
		scanChannel113(28),
		scanChannel116(29),
		scanChannel120(30),
		scanChannel124(31),
		scanChannel128(32),
		scanChannel132(33),
		scanChannel140(34),
		scanChannel149(35),
		scanChannel153(36),
		scanChannel157(37),
		scanChannel161(38),
		scanChannel165(39)
	}
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This value controls the channels which will be scanned during
	 the off-channel scan.  Two special values should be noted:
	 
	 all -   causes the radio to scan all channels supported by
	         the configured country code.  While on each channel
		     the radio will send out probe requests if the value
		     of extremeWirelessScanSendProbeRequest is true.
	 every - causes the radio to scan all channels, even those
	 	     which are restricted by the country code.  The
			 radio will only send probe request on channels that
			 are legal for that country code."
	 ::= { extremeWirelessOffChannelScanCfgEntry 2 }

extremeWirelessOffChannelScanMinWait OBJECT-TYPE
	SYNTAX Unsigned32
	UNITS "milliseconds"
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This value controls the minimum time the off-channel scan
	 will wait on a particular channel if it doesn't see any
	 traffic."
	 DEFVAL { 1 }
	 ::= { extremeWirelessOffChannelScanCfgEntry 3 }

extremeWirelessOffChannelScanMaxWait OBJECT-TYPE
	SYNTAX Unsigned32
	UNITS "milliseconds"
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This value controls the maximum time the off-channel scan will
	 wait on a particular channel even if it is receiving beacons
	 etc."
	 DEFVAL { 10 }
	 ::= { extremeWirelessOffChannelScanCfgEntry 4 }

extremeWirelessOffChannelContinuous OBJECT-TYPE
 	SYNTAX TruthValue
 	MAX-ACCESS read-write
 	STATUS current
 
 	DESCRIPTION
 	"Setting this value to true results in the off-channel scan
 	looping on the configured values until the channel scan is
 	disabled."
 	DEFVAL { false }
 	 ::= { extremeWirelessOffChannelScanCfgEntry 5 }
 


-- ************************************************************** --
-- Scan Status Table : This table contains information about the   --
-- current status of the scan feature.                             --
-- ************************************************************** --
extremeWirelessScanStatusTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessScanStatusEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per wireless interface.  It
	 provides status and statistics information for the scan 
	 feature operating on that wireless interface."
	 ::= { extremeAP 26 }

extremeWirelessScanStatusEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessScanStatusEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessScanStatusTable 1 }

ExtremeWirelessScanStatusEntry ::= SEQUENCE {
	extremeWirelessScanCurrentTableSize 	Unsigned32,
	extremeWirelessScanTableWatermark 	Unsigned32,
	extremeWirelessScanTotalOverflows	Unsigned32,
	extremeWirelessScanTotalTimeouts	Unsigned32,
	extremeWirelessScanOffChannelRunning	TruthValue,
	extremeWirelessScanCurrentChannel	Unsigned32,
	extremeWirelessScanLastElement		TimeTicks,
	extremeWirelessScanNumProbes		Unsigned32
}

extremeWirelessScanCurrentTableSize OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This indicates the current number of entries in the scan results
	 table."
	 ::= { extremeWirelessScanStatusEntry 1 }

extremeWirelessScanTableWatermark OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This indicates the largest size the results table has reached
	 since the status values have been reset, or the system has
	 booted."
	 ::= { extremeWirelessScanStatusEntry 2 }

extremeWirelessScanTotalOverflows OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of entries which have been removed
	 to make room for a newer entry.   This should be equal to the
	 number of AP added traps the system has generated with the 
	 overflow value set to true."
	 ::= { extremeWirelessScanStatusEntry 3 }

extremeWirelessScanTotalTimeouts OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of entries which have been removed
	 because they have become stale.  This should be equal to the 
	 number of AP removed traps sent by the system."
	 ::= { extremeWirelessScanStatusEntry 4 }

extremeWirelessScanOffChannelRunning OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"If this value is true it indicates that an off-channel scan is
	 currently running.  This implies that the radio is current
	 disabled."
	 ::= { extremeWirelessScanStatusEntry 5 }

extremeWirelessScanCurrentChannel OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"If the value of extremeWirelessScanOffChannelRunning is true
	 this variable give the value of the current channel the AP is
	 scanning on.  If a scan is not running, the value of this variable
	 is undefined."
	 ::= { extremeWirelessScanStatusEntry 6 }

extremeWirelessScanLastElement OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the sysUpTime when the last new station was inserted
	 in the results table."
	 ::= { extremeWirelessScanStatusEntry 7 }

extremeWirelessScanNumProbes OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of probe requests sent by this wireless 
	 interface."
	 ::= { extremeWirelessScanStatusEntry 8 }

-- ************************************************************** --
-- Scan Results Table : This table provides information collected  --
-- during on and off-channel scans about the APs discovered.       --
-- ************************************************************** --
extremeWirelessScanResultsTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessScanResultsEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per wireless interface.  It
	 provides status and statistics information for the scan 
	 feature operating on that wireless interface."
	 ::= { extremeAP 27 }

extremeWirelessScanResultsEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessScanResultsEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex, extremeWirelessScanResultsStationId }
	 ::= { extremeWirelessScanResultsTable 1 }

ExtremeWirelessScanResultsEntry ::= SEQUENCE {
	extremeWirelessScanResultsStationId		 MacAddress,
	extremeWirelessScanResultsFirstSeen		 TimeTicks,
	extremeWirelessScanResultsLastChange		 TimeTicks,
	extremeWirelessScanResultsPacketTime		 INTEGER,
	extremeWirelessScanResultsPacketRate	 	 INTEGER,
	extremeWirelessScanResultsChannel		 INTEGER,
	extremeWirelessScanResultsMinRSS		 INTEGER,
	extremeWirelessScanResultsMaxRSS		 INTEGER,
	extremeWirelessScanResultsAvgRSS		 INTEGER,
	extremeWirelessScanResultsTotalBeacons		 Unsigned32,
	extremeWirelessScanResultsTotalProbes		 Unsigned32,
	extremeWirelessScanResultsDiscoveredBy		 INTEGER,
	extremeWirelessScanResultsDSSOFDM		 TruthValue,
	extremeWirelessScanResultsRSNEnabled		 TruthValue,
	extremeWirelessScanResultsGShortSlot		 TruthValue,
	extremeWirelessScanResultsChannelAgility	 TruthValue,
	extremeWirelessScanResultsPBCC			 TruthValue,
	extremeWirelessScanResultsPreamble		 TruthValue,
	extremeWirelessScanResultsPrivacy		 TruthValue,
	extremeWirelessScanResultsCFPollReq		 TruthValue,
	extremeWirelessScanResultsCFPollable		 TruthValue,
	extremeWirelessScanResultsNetworkType 		 INTEGER,
	extremeWirelessScanResultsSSID 			 OCTET STRING,
	extremeWirelessScanResultsRateSet		 OCTET STRING,
	extremeWirelessScanResultsExtRateSet		 OCTET STRING,
	extremeWirelessScanResultsDSSParameter		 INTEGER,
	extremeWirelessScanResultsTIMCount	 	 INTEGER,
	extremeWirelessScanResultsTIMPeriod	 	 INTEGER,
	extremeWirelessScanResultsTIMTrafficInd	 	 TruthValue,
	extremeWirelessScanResultsCountryCode		 OCTET STRING,
	extremeWirelessScanWPAIEPresent			 TruthValue,
	extremeWirelessScanWPAVersion			 Unsigned32,
	extremeWirelessScanWPAIEMcastCipher		 WPACipherSet,
	extremeWirelessScanWPAUcastCipherCount		 Unsigned32,
	extremeWirelessScanWPAUcastCipher		 WPACipherSet,
	extremeWirelessScanWPAKeyMgmtCount		 Unsigned32,
	extremeWirelessScanWPAKeyMgmtSuite		 WPAKeyMgmtSet,
	extremeWirelessScanResultsIEBlob	 	 OCTET STRING
}
	
extremeWirelessScanResultsStationId OBJECT-TYPE
	SYNTAX MacAddress
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the MAC address of the detected station."
	 ::= { extremeWirelessScanResultsEntry 1 }

extremeWirelessScanResultsFirstSeen OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the sysUpTime when this station entry was created."
	 ::= { extremeWirelessScanResultsEntry 2 }

extremeWirelessScanResultsLastChange OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the sysUpTime of the station entry the last time it was
	 updated (i.e. a beacon etc was received)."
	 ::= { extremeWirelessScanResultsEntry 3 }

extremeWirelessScanResultsPacketTime OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the timestamp field from the last beacon/probe response
	 received from this station."
	 ::= { extremeWirelessScanResultsEntry 4 }

extremeWirelessScanResultsPacketRate OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the rate at which the last packet was sent by the
	 station.

	 TODO: What are the units here on rate?"
	 ::= { extremeWirelessScanResultsEntry 5 }

extremeWirelessScanResultsChannel OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the channel the last packet received by this station
	 was recieved on."
	 ::= { extremeWirelessScanResultsEntry 6 }

extremeWirelessScanResultsMinRSS OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the smallest RSS value of any packet recieved from this
	 station."
	 ::= { extremeWirelessScanResultsEntry 7 }

extremeWirelessScanResultsMaxRSS OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the largest RSS value of any packet recieved from this
	 station."
	 ::= { extremeWirelessScanResultsEntry 8 }

extremeWirelessScanResultsAvgRSS OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is a running average of RSS values of all the packets
	 received from this station."
	 ::= { extremeWirelessScanResultsEntry 9 }

extremeWirelessScanResultsTotalBeacons OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This counter gives the total number of beacons received from this
	 station."
	 ::= { extremeWirelessScanResultsEntry 10 }

extremeWirelessScanResultsTotalProbes OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This counter gives the total number of probe responses
	 received from this station."
	 ::= { extremeWirelessScanResultsEntry 11 }

extremeWirelessScanResultsDiscoveredBy OBJECT-TYPE
	SYNTAX INTEGER { probe (1),
   		         beacon (2) }
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the type of packet which cause this results entry to be
	 created."
	 ::= { extremeWirelessScanResultsEntry 12 }

extremeWirelessScanResultsDSSOFDM OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO : What is this?"
	 ::= { extremeWirelessScanResultsEntry 13 }

extremeWirelessScanResultsRSNEnabled OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Indicates if RSN capabilities are enabled on the sending station."
	 ::= { extremeWirelessScanResultsEntry 14 }

extremeWirelessScanResultsGShortSlot OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO : What is this?"
	 ::= { extremeWirelessScanResultsEntry 15 }

extremeWirelessScanResultsChannelAgility OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO : What is this?"
	 ::= { extremeWirelessScanResultsEntry 16 }

extremeWirelessScanResultsPBCC OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO : What is this?"
	 ::= { extremeWirelessScanResultsEntry 17 }

extremeWirelessScanResultsPreamble OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO : What is this?"
	 ::= { extremeWirelessScanResultsEntry 18 }

extremeWirelessScanResultsPrivacy OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is true if MAC-based privacy is invoked on the sending
	 station."
	 ::= { extremeWirelessScanResultsEntry 19 }

extremeWirelessScanResultsCFPollReq OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO: What is this?"
	 ::= { extremeWirelessScanResultsEntry 20 }

extremeWirelessScanResultsCFPollable OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO: What is this?"
	 ::= { extremeWirelessScanResultsEntry 21 }

extremeWirelessScanResultsNetworkType OBJECT-TYPE
	SYNTAX INTEGER { ibss (1),
   		   	 bss  (2) }
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This indicates what type of network it is.  If this value is IBSS
	 the last beacon/probe response was sent by a host operating in
	 ad-hoc mode."
	 ::= { extremeWirelessScanResultsEntry 22 }

extremeWirelessScanResultsSSID OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This provides the value of the SSID value as is was recieved in
	 the beacon or probe response.  If no SSID was sent in the probe
	 this value is NULL."
	 ::= { extremeWirelessScanResultsEntry 23 }

extremeWirelessScanResultsRateSet OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This provides the value of the rate set IE as it was received in
	 the beacon or probe response.  If no rate set was recieved then
	 this value is NULL."
	 ::= { extremeWirelessScanResultsEntry 24 }

extremeWirelessScanResultsExtRateSet OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This provides the value of the extended rate set IE as it was 
	 received in the beacon or probe response.  If no rate set was 
	 recieved then this value is NULL."
	 ::= { extremeWirelessScanResultsEntry 25 }

extremeWirelessScanResultsDSSParameter OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Provides the value of the DSS parameter IE, which is the channel."
	 ::= { extremeWirelessScanResultsEntry 26 }

extremeWirelessScanResultsTIMCount OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO: What is this?"
	 ::= { extremeWirelessScanResultsEntry 27 }

extremeWirelessScanResultsTIMPeriod OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO: What is this?"
	 ::= { extremeWirelessScanResultsEntry 28 }

extremeWirelessScanResultsTIMTrafficInd OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO: What is this?"
	 ::= { extremeWirelessScanResultsEntry 29 }

extremeWirelessScanResultsCountryCode OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Value of country code supplied by the IE"
	 ::= { extremeWirelessScanResultsEntry 30 }

extremeWirelessScanWPAIEPresent OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"If this value is true it indicates that WPA information was
	 gathered from this AP in the last beacon.  It implies that
	 the WPA variables below are valid.
	 If this value is false it indicates that this AP did not 
	 send a WPA IE in its last beacon, and the values below are
	 meaningless."
	::= { extremeWirelessScanResultsEntry 31 }

extremeWirelessScanWPAVersion OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value gives the version of WPA supported by this AP.
	 See IEEE 802.11i Draft 3.0 section 7 for meaning of various
	 values."
	::= { extremeWirelessScanResultsEntry 32 }

extremeWirelessScanWPAIEMcastCipher OBJECT-TYPE
	SYNTAX WPACipherSet
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value gives the single multicast cipher supported by this
	 AP."
	::= { extremeWirelessScanResultsEntry 33 }

extremeWirelessScanWPAUcastCipherCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value gives the total number of unicast ciphers supported
	 by this AP.  It corresponds to the number of bits set in the 
	 extremeWirelessScanWPAUcastCipher variable."
	::= { extremeWirelessScanResultsEntry 34 }

extremeWirelessScanWPAUcastCipher OBJECT-TYPE
	SYNTAX WPACipherSet
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the list of cipher suites which are supported by 
	 this AP."
	::= { extremeWirelessScanResultsEntry 35 }

extremeWirelessScanWPAKeyMgmtCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of key-management schemes supported by
	 this AP.  It corresponds to the number of bits set in the
	 extremeWirelessScanWPAKeyMgmtSuite variable."
	::= { extremeWirelessScanResultsEntry 36 }

extremeWirelessScanWPAKeyMgmtSuite OBJECT-TYPE
	SYNTAX WPAKeyMgmtSet
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This indicates the supported key-management suites supported
	 by this AP."
	::= { extremeWirelessScanResultsEntry 37 }

extremeWirelessScanResultsIEBlob OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"All the IEs received in the last packet encoded as a blob of
	 Type, Length, Value tuples."
	 ::= { extremeWirelessScanResultsEntry 38 }

-- ************************************************************** --
-- Probe Info Configuration Table : This table allows for config   --
-- for the collection of probe request packets.  The AP uses this  --
-- to populate the client information tables.                      --
-- ************************************************************** --
extremeWirelessProbeInfoCfgTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessProbeInfoCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per physical and virtual interface.
	 It controls the collection of client information via PROBE REQ
	 packets."
	 ::= { extremeAP 28 } 

extremeWirelessProbeInfoCfgEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessProbeInfoCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that the ifIndex here can be either a physical or virtual
	 wireless interface index."
	INDEX { ifIndex }
	 ::= { extremeWirelessProbeInfoCfgTable 1 }

ExtremeWirelessProbeInfoCfgEntry ::= SEQUENCE {
	extremeWirelessProbeInfoEnable		 TruthValue,
	extremeWirelessProbeInfoKeepIEs		 TruthValue,
	extremeWirelessProbeInfoTableSize	 Unsigned32,
	extremeWirelessProbeInfoTimeout		 Unsigned32,
	extremeWirelessProbeInfoTableClear	 TruthValue,
	extremeWirelessProbeInfoSourceClear	 MacAddress,
	extremeWirelessProbeInfoTableStatsClear  TruthValue,
	extremeWirelessProbeInfoSourceStatsClear MacAddress,
	extremeWirelessProbeInfoSendAddedTrap    TruthValue,
	extremeWirelessProbeInfoSendRemovedTrap  TruthValue }

extremeWirelessProbeInfoEnable OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"If this is set to TRUE then this virtual or physical
	interface will register with the MAC layer to receive
	notification of PROBE REQ packets.  In the case of 
	a virtual interface this implies the receipt of directed
	PROBE REQ packets.  If set to FALSE no PROBE REQ packets
	will be received, and current entries will eventually
	timeout."
	DEFVAL { true }
	 ::= { extremeWirelessProbeInfoCfgEntry 1 }

extremeWirelessProbeInfoKeepIEs OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"If this value is set to TRUE then the rate IE which
	 is carried in the PROBE REQ packet will be recorded.
	 If FALSE, no rate information will be kept."
	 DEFVAL { false }
	 ::= { extremeWirelessProbeInfoCfgEntry 2 }

extremeWirelessProbeInfoTableSize OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This value sets the maximum size of the probe info 
	 results table, which limits the number of clients this
	 AP can record PROBE REQ information from."
	 DEFVAL { 128 }
	 ::= { extremeWirelessProbeInfoCfgEntry 3 }

extremeWirelessProbeInfoTimeout OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This is the timeout, in seconds, for an entry in the 
	 probe info table.  Once a PROBE REQ has not been heard
	 from a client in this time period, it's entry is removed.
	 The value 0 is a sentinel, indicating no timeout."
	 DEFVAL { 3600 }
	 ::= { extremeWirelessProbeInfoCfgEntry 4 }

extremeWirelessProbeInfoTableClear OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this value to TRUE causes all entries in the 
	 probe info table to be removed.  Reading this value
	 has no meaning."
	 ::= { extremeWirelessProbeInfoCfgEntry 5 }

extremeWirelessProbeInfoSourceClear OBJECT-TYPE
	SYNTAX MacAddress
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to the value of some MAC address results in
	 any matching MAC address in the probe info table being
	 removed.  Reading this value has no meaning."
	 ::= { extremeWirelessProbeInfoCfgEntry 6 }

extremeWirelessProbeInfoTableStatsClear OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE results in all historical statistics
	 in the probe info table being set to 0.  No entries will
	 be removed.  Reading this has no meaning."
	 ::= { extremeWirelessProbeInfoCfgEntry 7 }

extremeWirelessProbeInfoSourceStatsClear OBJECT-TYPE
	SYNTAX MacAddress
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to a MAC address will clear the historical
	 stats associated with the matching MAC address in the
	 probe info table.  Reading this has no meaning."
	 ::= { extremeWirelessProbeInfoCfgEntry 8 }

extremeWirelessProbeInfoSendAddedTrap OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the management module to
	 send a trap whenever a new PROBE REQ source is added to 
	 the probe info table.  Setting this to FALSE will 
	 suppress the trap."
	 DEFVAL { false }
	 ::= { extremeWirelessProbeInfoCfgEntry 9 }

extremeWirelessProbeInfoSendRemovedTrap OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE will cause the management module to
	 send a trap whenever a PROBE REQ source is removed from
	 the probe info table.  Setting this to false will 
	 suppress the trap."
	 DEFVAL { false }
	 ::= { extremeWirelessProbeInfoCfgEntry 10 }

-- ************************************************************** --
-- Probe Info Status Table : This table gives current status of    --
-- the probe request storage feature.                              --
-- ************************************************************** --
extremeWirelessProbeInfoStatusTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessProbeInfoStatusEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per physical and virtual interface.
	 It gives debugging and status information for the operation of the
	 probe info feature on that interface."
	 ::= { extremeAP 29 }

extremeWirelessProbeInfoStatusEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessProbeInfoStatusEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that the ifIndex here can be either a physical or virtual
	 wireless interface index."
	INDEX { ifIndex }
	 ::= { extremeWirelessProbeInfoStatusTable 1 }

ExtremeWirelessProbeInfoStatusEntry ::= SEQUENCE {
	extremeWirelessProbeInfoStatusCurrentTableSize Unsigned32,
	extremeWirelessProbeInfoStatusWatermark	       Unsigned32,
	extremeWirelessProbeInfoStatusTotalOverflows   Unsigned32,
	extremeWirelessProbeInfoStatusTotalTimeouts    Unsigned32,
	extremeWirelessProbeInfoStatusLastElement      TimeTicks,
	extremeWirelessProbeInfoStatusTotalProbes      Unsigned32
}

extremeWirelessProbeInfoStatusCurrentTableSize OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the current number of entries in the probe 
	 info table for this interface."
	 ::= { extremeWirelessProbeInfoStatusEntry 1 }

extremeWirelessProbeInfoStatusWatermark OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the maximum size the probe info table has been
	 since the statistics have been reset."
	 ::= { extremeWirelessProbeInfoStatusEntry 2 }

extremeWirelessProbeInfoStatusTotalOverflows OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of times an entry in the probe info
	 table was overwritten because the table was full."
	 ::= { extremeWirelessProbeInfoStatusEntry 3 }

extremeWirelessProbeInfoStatusTotalTimeouts OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of times an entry in the probe info
	 table was timed out."
	 ::= { extremeWirelessProbeInfoStatusEntry 4 }

extremeWirelessProbeInfoStatusLastElement OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the sysUpTime the last time an entry was added
	 to the probe info table (or modified)."
	 ::= { extremeWirelessProbeInfoStatusEntry 5 }

extremeWirelessProbeInfoStatusTotalProbes OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of PROBE REQ packets that
	 this interface has gotten."
	 ::= { extremeWirelessProbeInfoStatusEntry 6 }

-- ************************************************************** --
-- Probe Info Table : This table contains information on the probe --
-- request packets recieved from particular hosts.                 --
-- ************************************************************** --
extremeWirelessProbeInfoTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessProbeInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry for each source MAC from which
	 a PROBE REQ packet has been received.  Entries are added when
	 a packet is recieved from a new source mac.  Entries are 
	 removed via management action, or by timeout or overflow."
	 ::= { extremeAP 30 }

extremeWirelessProbeInfoEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessProbeInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that the ifIndex here can be either a physical or virtual
	 wireless interface index.  In addition, this table is indexed
	 by the MAC address of the PROBE REQ source."
	INDEX { ifIndex, extremeWirelessProbeInfoSource }
	 ::= { extremeWirelessProbeInfoTable 1 }

ExtremeWirelessProbeInfoEntry ::= SEQUENCE {
	extremeWirelessProbeInfoSource		MacAddress,
	extremeWirelessProbeInfoTotalProbes	Unsigned32,
	extremeWirelessProbeInfoTotalProbeResp	Unsigned32,
	extremeWirelessProbeInfoRateIESize	Unsigned32,
	extremeWirelessProbeInfoRateIE		OCTET STRING,
	extremeWirelessProbeInfoFirstSeen 	TimeTicks,
	extremeWirelessProbeInfoLastChange	TimeTicks,
	extremeWirelessProbeInfoLastRSS		INTEGER,
	extremeWirelessProbeInfoLastRate	INTEGER,
	extremeWirelessProbeInfoLastChannel	INTEGER }

extremeWirelessProbeInfoSource OBJECT-TYPE
	SYNTAX MacAddress
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the MAC address of the source which sent us the 
	 PROBE REQ packets described by this entry."
	 ::= { extremeWirelessProbeInfoEntry 1 }

extremeWirelessProbeInfoTotalProbes OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of PROBE REQ packets we have
	 received from this source since the last time the stats
	 were cleared."
	 ::= { extremeWirelessProbeInfoEntry 2 }

extremeWirelessProbeInfoTotalProbeResp OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of PROBE RESP packets we have
	 sent to this source.  This may not be equal to PROBE REQ
	 based on rxmit, misses, etc."
	 ::= { extremeWirelessProbeInfoEntry 3 }

extremeWirelessProbeInfoRateIESize OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the size of the rate-ie field in bytes.  This
	 may be 0, at which point there is no rate-ie."
	 ::= { extremeWirelessProbeInfoEntry 4 }

extremeWirelessProbeInfoRateIE OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the rate IE as recieved in the last PROBE REQ
	 packet.  It is encoded as a TLV exactly as it was in the
	 frame.  This field may return NULL if storing of rate IE
	 fields is disabled."
	 ::= { extremeWirelessProbeInfoEntry 5 }

extremeWirelessProbeInfoFirstSeen OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the sysUpTime the first time a PROBE REQ was
	 seen from this source."
	 ::= { extremeWirelessProbeInfoEntry 6 }

extremeWirelessProbeInfoLastChange OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the sysUpTime the last time a PROBE REQ was
	 seen from this source."
	 ::= { extremeWirelessProbeInfoEntry 7 }

extremeWirelessProbeInfoLastRSS OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the RSSI from the last PROBE REQ packet received
	 from this station."
	 ::= { extremeWirelessProbeInfoEntry 8 }

extremeWirelessProbeInfoLastRate OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the rate at which the last packet was received
	 from this station."
	 ::= { extremeWirelessProbeInfoEntry 9 }

extremeWirelessProbeInfoLastChannel OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the channel on which the last PROBE REQ from
	 this station was heard.  PROBE REQs may be heard during
	 an off-channel scan, at which point this may not be
	 equal to the current channel."
	 ::= { extremeWirelessProbeInfoEntry 10 }

-- ************************************************************** --
-- Per Client Historical Diagnostic Table : This table allows for  --
-- configuration of diagnostic information collection.             --
-- ************************************************************** -- 
extremeWirelessClientDiagCfgTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientDiagCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one row per wireless interface.  Each row
	 contains configuration values for collection of per-client
	 diagnostic information."
	 ::= { extremeAP 31 }

extremeWirelessClientDiagCfgEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientDiagCfgEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table per virtual interface
	 (SSID)."
	INDEX { ifIndex }
	 ::= { extremeWirelessClientDiagCfgTable 1 }

ExtremeWirelessClientDiagCfgEntry ::= SEQUENCE {
	extremeWirelessClientDiagCfgEnable		TruthValue,
	extremeWirelessClientDiagCfgClearClient		MacAddress,
	extremeWirelessClientDiagCfgTableSize		Unsigned32,
	extremeWirelessClientDiagCfgTimeout		Unsigned32
}

extremeWirelessClientDiagCfgEnable OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to TRUE enables the collection of historical 
	 information on this virtual interface.  This implies that 
	 stations which send ASSOC or AUTH packets will have new 
	 entries created in the table for this interface."
	 DEFVAL { true }
	 ::= { extremeWirelessClientDiagCfgEntry 1 }

extremeWirelessClientDiagCfgClearClient OBJECT-TYPE
	SYNTAX MacAddress
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this to the MAC address of a client in the diag
	 tables will clear the counters for that client.  The entry
	 will not be removed from the table, but all summation 
	 counters will be cleared."
	 ::= { extremeWirelessClientDiagCfgEntry 2 }

extremeWirelessClientDiagCfgTableSize OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This value will configure the maximum size of the diag table.
	 The value is specified in number of entries.  A value of 0
	 implies no upper limit."
	 DEFVAL { 128 }
	 ::= { extremeWirelessClientDiagCfgEntry 3 }

extremeWirelessClientDiagCfgTimeout OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"This value will configure the timeout of an entry from the
	 diagnostics tables.  The value is given in seconds.  A value
	 of 0 implies no timeout."
	 DEFVAL { 3600 }
	 ::= { extremeWirelessClientDiagCfgEntry 4 }

-- ************************************************************** --
-- Per client historical feature status                            --
-- ************************************************************** --
extremeWirelessClientDiagStatusTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientDiagStatusEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per virtual interface.  Values
	 stored here give current status of the historical client diag
	 feature."
	 ::= { extremeAP 32 }

extremeWirelessClientDiagStatusEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientDiagStatusEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that ifIndex here is virtual ifIndex (SSID)."
	INDEX { ifIndex }
	 ::= { extremeWirelessClientDiagStatusTable 1 }

ExtremeWirelessClientDiagStatusEntry ::= SEQUENCE {
	extremeWirelessClientDiagCurrentTableSize	Unsigned32,
	extremeWirelessClientDiagTableWatermark		Unsigned32,
	extremeWirelessClientDiagTotalOverflows		Unsigned32,
	extremeWirelessClientDiagTotalTimeouts		Unsigned32,
	extremeWirelessClientDiagLastElement		TimeTicks,
	extremeWirelessClientDiagSupportsSizeCounters	TruthValue,
	extremeWirelessClientDiagSupportsSpeedCounters	TruthValue,
	extremeWirelessClientDiagSupportsPacketCounters	TruthValue
}

extremeWirelessClientDiagCurrentTableSize OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the current size, in entries of the client diag
	 tables."
	 ::= { extremeWirelessClientDiagStatusEntry 1 }

extremeWirelessClientDiagTableWatermark OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the maximum size the client diag table has been
	 since the last time the statistics were cleared."
	 ::= { extremeWirelessClientDiagStatusEntry 2 }

extremeWirelessClientDiagTotalOverflows OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of times an entry has been
	 replaced by a newer entry because there was no room
	 left in the table."
	 ::= { extremeWirelessClientDiagStatusEntry 3 }

extremeWirelessClientDiagTotalTimeouts OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of times an entry has been
	 aged-out of the table."
	 ::= { extremeWirelessClientDiagStatusEntry 4 }

extremeWirelessClientDiagLastElement OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is sysUpTime when the last entry was added to
	 the table."
	 ::= { extremeWirelessClientDiagStatusEntry 5 }

extremeWirelessClientDiagSupportsSpeedCounters OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value indicates if this AP supports the speed-based
	 counters from the extremeWirelessClientSpeedCountersTable."
	 ::= { extremeWirelessClientDiagStatusEntry 6 }

extremeWirelessClientDiagSupportsSizeCounters OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value indicates if this AP supports the size-based
	 counters from the extremeWirelessClientMACSizeCounterTable."
	 ::= { extremeWirelessClientDiagStatusEntry 7 }

extremeWirelessClientDiagSupportsPacketCounters OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value indicates if this AP supports the packet-based
	 counters from the extremeWirelessClientPacketCountersTable."
	 ::= { extremeWirelessClientDiagStatusEntry 8 }

-- ************************************************************** --
-- Per Client Historical Diagnostics : This table contains info    --
-- about state transitions for each client.
-- ************************************************************** --
extremeWirelessClientDiagTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientDiagEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains information on a per-client basis.  Rows are
	 indexed first by virtual interface, then by client MAC."
	 ::= { extremeAP 33 }

extremeWirelessClientDiagEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientDiagEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that ifIndex here is virtual ifIndex (SSID)."
	INDEX { ifIndex, extremeWirelessClientDiagMac }
	 ::= { extremeWirelessClientDiagTable 1 }

ExtremeWirelessClientDiagEntry ::= SEQUENCE {
	extremeWirelessClientDiagMac			MacAddress,
	extremeWirelessClientDiagStateWatermark		INTEGER,
	extremeWirelessClientDiagEntersInDetected	Unsigned32,
	extremeWirelessClientDiagErrorsInDetected	Unsigned32,
	extremeWirelessClientDiagAuthReqInDetected	Unsigned32,
	extremeWirelessClientDiagOtherReqInDetected	Unsigned32,
	extremeWirelessClientDiagMgmtActionInDetected	Unsigned32,
	extremeWirelessClientDiagTimeOutInDetected	Unsigned32,
	extremeWirelessClientDiagEntersInAuth		Unsigned32,
	extremeWirelessClientDiagErrorsInAuth		Unsigned32,
	extremeWirelessClientDiagAssocReqInAuth		Unsigned32,
	extremeWirelessClientDiagOtherReqInAuth		Unsigned32,
	extremeWirelessClientDiagMgmtActionInAuth	Unsigned32,
	extremeWirelessClientDiagTimeOutInAuth		Unsigned32,
	extremeWirelessClientDiagEntersInAssoc		Unsigned32,
	extremeWirelessClientDiagErrorsInAssoc		Unsigned32,
	extremeWirelessClientDiagMgmtActionInAssoc	Unsigned32,
	extremeWirelessClientDiagTimeOutInAssoc		Unsigned32,
	extremeWirelessClientDiagEntersInForward	Unsigned32,
	extremeWirelessClientDiagMgmtActionInForward	Unsigned32,
	extremeWirelessClientDiagTimeOutInForward	Unsigned32,
	extremeWirelessClientDiagTotal802Auths		Unsigned32,
	extremeWirelessClientDiagTotalNetLoginAuths	Unsigned32
}

extremeWirelessClientDiagMac OBJECT-TYPE
	SYNTAX MacAddress
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the MAC address of the station this entry 
	 describes."
	 ::= { extremeWirelessClientDiagEntry 1 }

extremeWirelessClientDiagStateWatermark OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the highest state this client has been in."
	::= { extremeWirelessClientDiagEntry 2 }

extremeWirelessClientDiagEntersInDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times the client has entered
	 the detected state."
	::= { extremeWirelessClientDiagEntry 3 }

extremeWirelessClientDiagErrorsInDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of errors this client has 
	 experienced since the counters were cleared."
	::= { extremeWirelessClientDiagEntry 4 }

extremeWirelessClientDiagAuthReqInDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of AUTH REQ packets received
	 from this client while it was in the detected state."
	::= { extremeWirelessClientDiagEntry 5 }

extremeWirelessClientDiagOtherReqInDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of REQ packets received from
	 this client while it was in detected state that were
	 not AUTH REQ packets."
	::= { extremeWirelessClientDiagEntry 6 }

extremeWirelessClientDiagMgmtActionInDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times management initiated
	 action (i.e. force-disassociate) was received for this
	 client in the detected state."
	::= { extremeWirelessClientDiagEntry 7 }

extremeWirelessClientDiagTimeOutInDetected OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of timeouts which happened to
	 this client in the detected state."
	::= { extremeWirelessClientDiagEntry 8 }

extremeWirelessClientDiagEntersInAuth OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times the client has entered
	 the AUTHED state."
	::= { extremeWirelessClientDiagEntry 9 }

extremeWirelessClientDiagErrorsInAuth OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times the client has had
	 errors in the AUTHED state."
	::= { extremeWirelessClientDiagEntry 10 }

extremeWirelessClientDiagAssocReqInAuth OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of ASSOC REQ or REASSOC REQ
	 packets received from this client while in the AUTHED 
	 state."
	::= { extremeWirelessClientDiagEntry 11 }

extremeWirelessClientDiagOtherReqInAuth OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of non-ASSOC/REASSOC packets
	 received from this client while in the AUTHED state."
	::= { extremeWirelessClientDiagEntry 12 }

extremeWirelessClientDiagMgmtActionInAuth OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of management initiated action
	 which this client experienced while in the AUTHED state."
	::= { extremeWirelessClientDiagEntry 13 }

extremeWirelessClientDiagTimeOutInAuth OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times this client timed out
	 while in the authed state."
	::= { extremeWirelessClientDiagEntry 14 }

extremeWirelessClientDiagEntersInAssoc OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times this client has entered
	 the associated state."
	::= { extremeWirelessClientDiagEntry 15 }

extremeWirelessClientDiagErrorsInAssoc OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of errors this client has had
	 in the associated state."
	::= { extremeWirelessClientDiagEntry 16 }

extremeWirelessClientDiagMgmtActionInAssoc OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of management actions which have
	 happened to this client in the associated state."
	::= { extremeWirelessClientDiagEntry 17 }

extremeWirelessClientDiagTimeOutInAssoc OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times this station has been
	 timed-out in the associated state."
	::= { extremeWirelessClientDiagEntry 18 }

extremeWirelessClientDiagEntersInForward OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times this client has entered
	 the forwarding state."
	::= { extremeWirelessClientDiagEntry 19 }

extremeWirelessClientDiagMgmtActionInForward OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times this client has
	 experience a management action while in forwarding."
	::= { extremeWirelessClientDiagEntry 20 }

extremeWirelessClientDiagTimeOutInForward OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of timeouts this client has
	 experienced while in forwarding."
	::= { extremeWirelessClientDiagEntry 21 }

extremeWirelessClientDiagTotal802Auths OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times this client has tried
	 to authenticate using 802.1x."
	::= { extremeWirelessClientDiagEntry 22 }

extremeWirelessClientDiagTotalNetLoginAuths OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"This is the total number of times this client has tried
	 to authenticate using NetLogin."
	::= { extremeWirelessClientDiagEntry 23 }

-- ************************************************************** --
-- Per Client Association Information : This table gives history   --
-- on the association packets sent by each client on the AP.       --
-- ************************************************************** --
extremeWirelessClientAssocInfoTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientAssocInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one row per client on a wireless interface.
	 It gives information about the association history and parameters
	 of that client."
	 ::= { extremeAP 34 }

extremeWirelessClientAssocInfoEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientAssocInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that ifIndex here is virtual ifIndex (SSID)."
	INDEX { ifIndex, extremeWirelessClientDiagMac }
	 ::= { extremeWirelessClientAssocInfoTable 1 }

ExtremeWirelessClientAssocInfoEntry ::= SEQUENCE {
	extremeWirelessClientAssocInfoAssociated		TruthValue,
	extremeWirelessClientAssocInfoTotalAssocReq		Unsigned32,
	extremeWirelessClientAssocInfoTotalReAssocReq		Unsigned32,
	extremeWirelessClientAssocInfoTotalAssocResp		Unsigned32,
	extremeWirelessClientAssocInfoTotalAssocOK		Unsigned32,
	extremeWirelessClientAssocInfoTotalAssocFail		Unsigned32,
	extremeWirelessClientAssocInfoTotalDisassocReq		Unsigned32,
	extremeWirelessClientAssocInfoTotalDisassocResp		Unsigned32,
	extremeWirelessClientAssocInfoRateIE			OCTET STRING,
	extremeWirelessClientAssocInfoLastAssoc			TimeTicks,
	extremeWirelessClientAssocInfoLastError			TimeTicks,
	extremeWirelessClientAssocInfoLastErrorType		INTEGER,
	extremeWirelessClientAssocInfoErrorRateMismatch		Unsigned32,
	extremeWirelessClientAssocInfoErrorBadState		Unsigned32,
	extremeWirelessClientAssocInfoErrorBadCapability	Unsigned32,
	extremeWirelessClientAssocInfoErrorCounterMeasure	Unsigned32,
	extremeWirelessClientAssocInfoErrorMcastCipher		Unsigned32,
	extremeWirelessClientAssocInfoErrorMaxAssoc 		Unsigned32,
	extremeWirelessClientAssocInfoErrorRSNRequired		Unsigned32,
	extremeWirelessClientAssocInfoErrorRSNMismatch 		Unsigned32,
	extremeWirelessClientAssocInfoErrorOther		Unsigned32,
	extremeWirelessClientAssocInfoWPAIEPresent		TruthValue,
	extremeWirelessClientAssocInfoWPAVersion		Unsigned32,
	extremeWirelessClientAssocInfoWPAIEMcastCipher		WPACipherSet,
	extremeWirelessClientAssocInfoWPAUcastCipherCount	Unsigned32,
	extremeWirelessClientAssocInfoWPAUcastCipher		WPACipherSet,
	extremeWirelessClientAssocInfoWPAKeyMgmtCount		Unsigned32,
	extremeWirelessClientAssocInfoWPAKeyMgmtSuite		WPAKeyMgmtSet,
	extremeWirelessClientAssocIEBlob			OCTET STRING
}

extremeWirelessClientAssocInfoAssociated OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the current MAC-layer association status of 
	 this client."
	 ::= { extremeWirelessClientAssocInfoEntry 1 }

extremeWirelessClientAssocInfoTotalAssocReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of ASSOC REQ pdus recieved
	 from this client since the last time the counters were
	 cleared."
	 ::= { extremeWirelessClientAssocInfoEntry 2 }

extremeWirelessClientAssocInfoTotalReAssocReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of REASSOC REQ PDUs received
	 from this client since the last time the counters were
	 cleared."
	 ::= { extremeWirelessClientAssocInfoEntry 3 }

extremeWirelessClientAssocInfoTotalAssocResp OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of ASSOC RESP pdus sent to
	 this client since the last time the counters were cleared."
	 ::= { extremeWirelessClientAssocInfoEntry 4 }

extremeWirelessClientAssocInfoTotalAssocOK OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of ASSOC RESP PDUs which
	 carried a status = ok response."
	 ::= { extremeWirelessClientAssocInfoEntry 5 }

extremeWirelessClientAssocInfoTotalAssocFail OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of ASSOC RESP PDUs which
	 carried a status = failed response."
	 ::= { extremeWirelessClientAssocInfoEntry 6 }

extremeWirelessClientAssocInfoTotalDisassocReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of ASSOC RESP PDUs which
	 carried a status = failed response."
	 ::= { extremeWirelessClientAssocInfoEntry 7 }

extremeWirelessClientAssocInfoTotalDisassocResp OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of ASSOC RESP PDUs which
	 carried a status = failed response."
	 ::= { extremeWirelessClientAssocInfoEntry 8 }

extremeWirelessClientAssocInfoRateIE OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the value of the rate IE carried in the last
	 ASSOC REQ packet if this wireless interface is configured
	 to keep IEs.  If it's not, this will be NULL.  The IE
	 is encoded as a series of bytes exactly as the value 
	 portion of the IE in the PDU."
	 ::= { extremeWirelessClientAssocInfoEntry 9 }

extremeWirelessClientAssocInfoLastAssoc OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is sysUpTime when the last ASSOC REQ was received
	 from this client."
	 ::= { extremeWirelessClientAssocInfoEntry 10 }

extremeWirelessClientAssocInfoLastError OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is sysUpTime when the last ASSOC RESP was sent to
	 this client with a failure status."
	 ::= { extremeWirelessClientAssocInfoEntry 11 }

extremeWirelessClientAssocInfoLastErrorType OBJECT-TYPE
	SYNTAX INTEGER { noError(0),
			 rateMismatch(1),
			 badState(2),
			 badCapability(3),
			 couterMeasure(4),
			 mcastCipher(5),
			 maxAssoc(6),
			 rsnRequired(7),
			 rsnMismatch(8),
			 otherError(9) }
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is an enumerated type which indicates which was the
	 last error this client experienced during association."
	 ::= { extremeWirelessClientAssocInfoEntry 12 }

extremeWirelessClientAssocInfoErrorRateMismatch OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 a mismatch of the rate IEs."
	 ::= { extremeWirelessClientAssocInfoEntry 13 }

extremeWirelessClientAssocInfoErrorBadState OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the client being in the wrong state."
	 ::= { extremeWirelessClientAssocInfoEntry 14 }

extremeWirelessClientAssocInfoErrorBadCapability OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the client having a capability mismatch with the AP."
	 ::= { extremeWirelessClientAssocInfoEntry 15 }

extremeWirelessClientAssocInfoErrorCounterMeasure OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the client being subject to a counter measure."
	 ::= { extremeWirelessClientAssocInfoEntry 16 }

extremeWirelessClientAssocInfoErrorMcastCipher OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the client not supporting the current multicast cipher."
	 ::= { extremeWirelessClientAssocInfoEntry 17 }

extremeWirelessClientAssocInfoErrorMaxAssoc OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the AP having too many associations already."
	 ::= { extremeWirelessClientAssocInfoEntry 18 }

extremeWirelessClientAssocInfoErrorRSNRequired OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the client not supplying the (required) RSN IE."
	 ::= { extremeWirelessClientAssocInfoEntry 19 }

extremeWirelessClientAssocInfoErrorRSNMismatch OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the client's RSN IE not matching supported features on 
	 the AP."
	 ::= { extremeWirelessClientAssocInfoEntry 20 }

extremeWirelessClientAssocInfoErrorOther OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of failed associations due to 
	 the client's RSN IE not matching supported features on 
	 the AP."
	 ::= { extremeWirelessClientAssocInfoEntry 21 }

extremeWirelessClientAssocInfoWPAIEPresent OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"If this value is true it indicates that WPA information was
	 gathered from this station in the last association request.
	 If true, it implies the values of the WPA variables below 
	 are valid.  If false, it indicates that this station did not
	 send a WPA IE in its last association."
	::= { extremeWirelessClientAssocInfoEntry 22 }

extremeWirelessClientAssocInfoWPAVersion OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value gives the version of WPA supported by this station.
	 See IEEE 802.11i Draft 3.0 section 7 for meaning of various
	 values."
	::= { extremeWirelessClientAssocInfoEntry 23 }

extremeWirelessClientAssocInfoWPAIEMcastCipher OBJECT-TYPE
	SYNTAX WPACipherSet
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value gives the single multicast cipher supported by this
	 client."
	::= { extremeWirelessClientAssocInfoEntry 24 }

extremeWirelessClientAssocInfoWPAUcastCipherCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of set bits in the ucast cipher
	 variable below.  In this case this number should always
	 be 1."
	::= { extremeWirelessClientAssocInfoEntry 25 }

extremeWirelessClientAssocInfoWPAUcastCipher OBJECT-TYPE
	SYNTAX WPACipherSet
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the cipher suites which was requested by 
	 this client during its last assoc req."
	::= { extremeWirelessClientAssocInfoEntry 26 }

extremeWirelessClientAssocInfoWPAKeyMgmtCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This value gives the number of key management suites 
	 requested by this client in the last assoc. req message.
	 It indicates the number of set bits in the mgmt suite 
	 variable below."
	::= { extremeWirelessClientAssocInfoEntry 27 }

extremeWirelessClientAssocInfoWPAKeyMgmtSuite OBJECT-TYPE
	SYNTAX WPAKeyMgmtSet
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Gives the key management suite requested by this client 
	 during its last association request."
	::= { extremeWirelessClientAssocInfoEntry 28 }

extremeWirelessClientAssocIEBlob OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the IE list sent in the association frame encoded as
	 a series of TLVs."
	::= { extremeWirelessClientAssocInfoEntry 29 }


-- ************************************************************** --
-- Authentication Information Table : This table contains infor on --
-- the MAC-layer authentication of each client which has tried to  --
-- authenticate with this AP.
-- ************************************************************** --
extremeWirelessClientAuthInfoTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientAuthInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one row per client on a wireless interface.
	 It gives information about the authentication history and params
	 of that client."
	 ::= { extremeAP 35 }

extremeWirelessClientAuthInfoEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientAuthInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that ifIndex here is virtual ifIndex (SSID)."
	INDEX { ifIndex, extremeWirelessClientDiagMac }
	 ::= { extremeWirelessClientAuthInfoTable 1 }

ExtremeWirelessClientAuthInfoEntry ::= SEQUENCE {
	extremeWirelessClientAuthInfoCurrentAuth	TruthValue,
	extremeWirelessClientAuthInfoTotalAuths		Unsigned32,
	extremeWirelessClientAuthInfoTotalAuthResp	Unsigned32,
	extremeWirelessClientAuthInfoTotalAuthsOK	Unsigned32,
	extremeWirelessClientAuthInfoTotalAuthsFailed	Unsigned32,
	extremeWirelessClientAuthInfoTotalDeauthReq     Unsigned32,
	extremeWirelessClientAuthInfoTotalDeauthResp    Unsigned32,
	extremeWirelessClientAuthInfoTotalOpen		Unsigned32,
	extremeWirelessClientAuthInfoTotalShared	Unsigned32,
	extremeWirelessClientAuthInfoLastAuth		TimeTicks,
	extremeWirelessClientAuthInfoLastAuthType	INTEGER,
	extremeWirelessClientAuthInfoLastError		TimeTicks,
	extremeWirelessClientAuthInfoLastErrorType	INTEGER,
	extremeWirelessClientAuthInfoErrorResourceFailure  Unsigned32,
	extremeWirelessClientAuthInfoErrorSequenceNum	Unsigned32,
	extremeWirelessClientAuthInfoErrorChallengeText	Unsigned32,
	extremeWirelessClientAuthInfoErrorTypeMismatch	Unsigned32,
	extremeWirelessClientAuthInfoErrorKeyIndex	Unsigned32,
	extremeWirelessClientAuthInfoErrorOther         Unsigned32
}

extremeWirelessClientAuthInfoCurrentAuth OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the current MAC-layer authentication status of the
	 client."
	 ::= { extremeWirelessClientAuthInfoEntry 1 }

extremeWirelessClientAuthInfoTotalAuths OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of AUTH REQ packets received from 
	 this client since the last time the counters were cleared."
	 ::= { extremeWirelessClientAuthInfoEntry 2 }

extremeWirelessClientAuthInfoTotalAuthResp OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of AUTH RESP packets sent to
	 this client since the last time the counters were cleared."
	 ::= { extremeWirelessClientAuthInfoEntry 3 }

extremeWirelessClientAuthInfoTotalAuthsOK OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of AUTH RESP packets sent to
	 this client which carried a STATUS = OK message."
	 ::= { extremeWirelessClientAuthInfoEntry 4 }

extremeWirelessClientAuthInfoTotalAuthsFailed OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of AUTH RESP packets sent to
	 this client which carried a STATUS = Failed message."
	 ::= { extremeWirelessClientAuthInfoEntry 5 }

extremeWirelessClientAuthInfoTotalDeauthReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of DEAUTH REQ packets sent from
	 this client."
	 ::= { extremeWirelessClientAuthInfoEntry 6 }

extremeWirelessClientAuthInfoTotalDeauthResp OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of DEAUTH RESP packets sent to
	 this client."
	 ::= { extremeWirelessClientAuthInfoEntry 7 }

extremeWirelessClientAuthInfoTotalOpen OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of AUTH REQ packets which 
	 requested open authentication from the AP."
	 ::= { extremeWirelessClientAuthInfoEntry 8 }

extremeWirelessClientAuthInfoTotalShared OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of AUTH REQ packets which 
	 requested shared authentication from the AP."
	 ::= { extremeWirelessClientAuthInfoEntry 9 }

extremeWirelessClientAuthInfoLastAuth OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the sysUpTime the last time this client sent
	 a request for authentication."
	 ::= { extremeWirelessClientAuthInfoEntry 10 }

extremeWirelessClientAuthInfoLastAuthType OBJECT-TYPE
	SYNTAX INTEGER { open(1), shared(2) }
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the last type of authentication requested by 
	 this client."
	 ::= { extremeWirelessClientAuthInfoEntry 11 }

extremeWirelessClientAuthInfoLastError OBJECT-TYPE
	SYNTAX TimeTicks
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the sysUpTime of the last authentication failure
	 for this client."
	 ::= { extremeWirelessClientAuthInfoEntry 12 }

extremeWirelessClientAuthInfoLastErrorType OBJECT-TYPE
    SYNTAX INTEGER { noError(0),
        resourceFailure(1),
        sequenceNumber(2),
        challengeText(3),
        algorithmMismatch(4),
        keyIndex(5),
        otherError(6) }
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the OID of the last error counter that was 
	 incremented for this client."
	 ::= { extremeWirelessClientAuthInfoEntry 13 }

extremeWirelessClientAuthInfoErrorResourceFailure OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of authentication failures 
	 which have resulted from resource failures."
	 ::= { extremeWirelessClientAuthInfoEntry 14 }

extremeWirelessClientAuthInfoErrorSequenceNum OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of authentication failures 
	 which have resulted from bad packet sequence numbers."
	 ::= { extremeWirelessClientAuthInfoEntry 15 }

extremeWirelessClientAuthInfoErrorChallengeText OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of authentication failures 
	 which have resutled from challenge text mismatch."
	 ::= { extremeWirelessClientAuthInfoEntry 16 }

extremeWirelessClientAuthInfoErrorTypeMismatch OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of authentication failures 
	 which have resutled from the requested authentication
	 type (i.e. open/shared) not matching what this virutal
	 interface is configured for."
	 ::= { extremeWirelessClientAuthInfoEntry 17 }

extremeWirelessClientAuthInfoErrorKeyIndex OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of authentication failures 
	 which have resulted from key index mismatches."
	 ::= { extremeWirelessClientAuthInfoEntry 18 }

extremeWirelessClientAuthInfoErrorOther OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This is the total number of authentication failures 
	 which have resulted from unspecified errors."
	 ::= { extremeWirelessClientAuthInfoEntry 19 }


-- ************************************************************** --
-- Client MAC information : This table contains statistics and     --
-- information on each client which has tried to authenticate with --
-- this virtual interface.
-- ************************************************************** --
extremeWirelessClientMACInfoTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientMACInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains one entry per source MAC which has tried to
	 authenticate with this virtual interface (SSID).  Entries
	 are created when AUTH or REATH REQ packets are received from
	 new clients.  Entries can be aged out, removed by management
	 action, or overwritten by an overflow."
	 ::= { extremeAP 36 }

extremeWirelessClientMACInfoEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientMACInfoEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex, extremeWirelessClientDiagMac }
	 ::= { extremeWirelessClientMACInfoTable 1 }

ExtremeWirelessClientMACInfoEntry ::= SEQUENCE {
	extremeWirelessClientMACInfoMinRSS		INTEGER,
	extremeWirelessClientMACInfoMaxRSS		INTEGER,
	extremeWirelessClientMACInfoAvgRSS		INTEGER,
	extremeWirelessClientMACInfoTotalProbeReq	Unsigned32,
	extremeWirelessClientMACInfoTotalAuthReq	Unsigned32,
	extremeWirelessClientMACInfoTotalAssocReq	Unsigned32,
	extremeWirelessClientMACInfoTotalReAssocReq	Unsigned32,
	extremeWirelessClientMACInfoTotalDeAssocReq	Unsigned32,
	extremeWirelessClientMACInfoTotalDeAuthReq	Unsigned32,
	extremeWirelessClientMACInfoTotalPsPoll		Unsigned32,
	extremeWirelessClientMACInfoTotalData		Unsigned32,
	extremeWirelessClientMACInfoNavValue		Unsigned32
}

extremeWirelessClientMACInfoMinRSS OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the minimum RSS indication of any packet we have
	 seen from this client.  A value of 0 indicates that RSSI has
	 been unavailable."
	 ::= { extremeWirelessClientMACInfoEntry 1 }

extremeWirelessClientMACInfoMaxRSS OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the maximum RSS indication of any packet we have
	 seen from this client.  A value of 0 indicates that RSSI has
	 been unavailable."
	 ::= { extremeWirelessClientMACInfoEntry 2 }

extremeWirelessClientMACInfoAvgRSS OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the average RSS indication of any packet we have
	 seen from this client.  A value of 0 indicates that RSSI has
	 been unavailable."
	 ::= { extremeWirelessClientMACInfoEntry 3 }

extremeWirelessClientMACInfoTotalProbeReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of PROBE REQ packets recieved from
	 this client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 4 }

extremeWirelessClientMACInfoTotalAuthReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of AUTH REQ packets recieved from
	 this client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 5 }

extremeWirelessClientMACInfoTotalAssocReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of ASSOC REQ packets recieved from
	 this client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 6 }

extremeWirelessClientMACInfoTotalReAssocReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of REASSOC REQ packets recieved from
	 this client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 7 }

extremeWirelessClientMACInfoTotalDeAssocReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of DEASSOC REQ packets recieved from
	 this client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 8 }

extremeWirelessClientMACInfoTotalDeAuthReq OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of DEAUTH REQ packets recieved from
	 this client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 9 }

extremeWirelessClientMACInfoTotalPsPoll OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of PS POLL packets recieved from
	 this client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 10 }

extremeWirelessClientMACInfoTotalData OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of data frames received from this
	 client since the counters have been cleared."
	 ::= { extremeWirelessClientMACInfoEntry 11 }

extremeWirelessClientMACInfoNavValue OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"TODO: Is this an average, total, what?"
	 ::= { extremeWirelessClientMACInfoEntry 12 }


-- ************************************************************** --
-- Size Based Counters : This table gives per-client histogram     --
-- counters based on the size of received packets/fragments.       --
-- ************************************************************** --
extremeWirelessClientSizeCounterTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientSizeCounterEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table for each client which has
	 tried to authenticate with this virtual interface.  A new 
	 entry is created when an AUTH or REATH REQ packet is received
	 from a new station.  Entries are removed by management action,
	 timeout or overwritten due to overflow.
	 
	 Entries in this table are also indexed by virtual interface.
	 A client which has tried to authenticate to multiple virtual	
	 interfaces may have more than one entry in this table."
	 ::= { extremeAP 37 }

extremeWirelessClientSizeCounterEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientSizeCounterEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex, extremeWirelessClientDiagMac }
	 ::= { extremeWirelessClientSizeCounterTable 1 }

ExtremeWirelessClientSizeCounterEntry ::= SEQUENCE {
	extremeWirelessClientFrameSizeReXmit64		Unsigned32,
	extremeWirelessClientFrameSizeReXmit128		Unsigned32,
	extremeWirelessClientFrameSizeReXmit256		Unsigned32,
	extremeWirelessClientFrameSizeReXmit512		Unsigned32,
	extremeWirelessClientFrameSizeReXmit1024	Unsigned32,
	extremeWirelessClientFrameSizeReXmit2048	Unsigned32,
	extremeWirelessClientFrameSizeTX64		Unsigned32,
	extremeWirelessClientFrameSizeTX128		Unsigned32,
	extremeWirelessClientFrameSizeTX256		Unsigned32,
	extremeWirelessClientFrameSizeTX512		Unsigned32,
	extremeWirelessClientFrameSizeTX1024		Unsigned32,
	extremeWirelessClientFrameSizeTX2048		Unsigned32,
	extremeWirelessClientFrameSizeRX64		Unsigned32,
	extremeWirelessClientFrameSizeRX128		Unsigned32,
	extremeWirelessClientFrameSizeRX256		Unsigned32,
	extremeWirelessClientFrameSizeRX512		Unsigned32,
	extremeWirelessClientFrameSizeRX1024		Unsigned32,
	extremeWirelessClientFrameSizeRX2048		Unsigned32,
	extremeWirelessClientFrameSizeErrorTX64		Unsigned32,
	extremeWirelessClientFrameSizeErrorTX128	Unsigned32,
	extremeWirelessClientFrameSizeErrorTX256	Unsigned32,
	extremeWirelessClientFrameSizeErrorTX512	Unsigned32,
	extremeWirelessClientFrameSizeErrorTX1024	Unsigned32,
	extremeWirelessClientFrameSizeErrorTX2048	Unsigned32,
	extremeWirelessClientFrameSizeErrorRX64		Unsigned32,
	extremeWirelessClientFrameSizeErrorRX128	Unsigned32,
	extremeWirelessClientFrameSizeErrorRX256	Unsigned32,
	extremeWirelessClientFrameSizeErrorRX512	Unsigned32,
	extremeWirelessClientFrameSizeErrorRX1024	Unsigned32,
	extremeWirelessClientFrameSizeErrorRX2048	Unsigned32,
	extremeWirelessClientPacketSizeTX64		Unsigned32,
	extremeWirelessClientPacketSizeTX128		Unsigned32,
	extremeWirelessClientPacketSizeTX256		Unsigned32,
	extremeWirelessClientPacketSizeTX512		Unsigned32,
	extremeWirelessClientPacketSizeTX1024		Unsigned32,
	extremeWirelessClientPacketSizeTX2048		Unsigned32,
	extremeWirelessClientPacketSizeRX64		Unsigned32,
	extremeWirelessClientPacketSizeRX128		Unsigned32,
	extremeWirelessClientPacketSizeRX256		Unsigned32,
	extremeWirelessClientPacketSizeRX512		Unsigned32,
	extremeWirelessClientPacketSizeRX1024		Unsigned32,
	extremeWirelessClientPacketSizeRX2048		Unsigned32
}

extremeWirelessClientFrameSizeReXmit64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags retransmitted to this
	 client which were less than or equal to 64 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 1 }

extremeWirelessClientFrameSizeReXmit128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags retransmitted to this
	 client which were between 64 and 128 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 2 }

extremeWirelessClientFrameSizeReXmit256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags retransmitted to this
	 client which were between 128 and 256 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 3 }

extremeWirelessClientFrameSizeReXmit512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags retransmitted to this
	 client which were between 256 and 512 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 4 }

extremeWirelessClientFrameSizeReXmit1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags retransmitted to this
	 client which were between 512 and 1024 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 5 }

extremeWirelessClientFrameSizeReXmit2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags retransmitted to this
	 client which were between 1024 and 2048 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 6 }

extremeWirelessClientFrameSizeTX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags transmitted to this
	 client which were less than or equal to 64 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 7 }

extremeWirelessClientFrameSizeTX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags transmitted to this
	 client which were between 64 and 128 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 8 }

extremeWirelessClientFrameSizeTX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags transmitted to this
	 client which were between 128 and 256 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 9 }

extremeWirelessClientFrameSizeTX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags transmitted to this
	 client which were between 256 and 512 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 10 }

extremeWirelessClientFrameSizeTX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags transmitted to this
	 client which were between 512 and 1024 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 11 }

extremeWirelessClientFrameSizeTX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags transmitted to this
	 client which were between 1024 and 2048 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 12 }

extremeWirelessClientFrameSizeRX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags received from this
	 client which were less than or equal to 64 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 13 }

extremeWirelessClientFrameSizeRX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags received from this
	 client which were between 64 and 128 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 14 }

extremeWirelessClientFrameSizeRX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags received from this
	 client which were between 128 and 256 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 15 }

extremeWirelessClientFrameSizeRX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags received from this
	 client which were between 256 and 512 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 16 }

extremeWirelessClientFrameSizeRX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags received from this
	 client which were between 512 and 1024 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 17 }

extremeWirelessClientFrameSizeRX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags received from this
	 client which were between 1024 and 2048 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 18 }

extremeWirelessClientFrameSizeErrorTX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which failed to be sent from
	 this client with a length between 0 and 64 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 19 }

extremeWirelessClientFrameSizeErrorTX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which failed to be sent from
	 this client with a length between 65 and 128 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 20 }

extremeWirelessClientFrameSizeErrorTX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which failed to be sent from
	 this client with a length between 129 and 256 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 21 }

extremeWirelessClientFrameSizeErrorTX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which failed to be sent from
	 this client with a length between 257 and 512 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 22 }

extremeWirelessClientFrameSizeErrorTX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which failed to be sent from
	 this client with a length between 513 and 1024 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 23 }

extremeWirelessClientFrameSizeErrorTX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which failed to be sent from
	 this client with a length between 1025 and 2048 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 24 }

extremeWirelessClientFrameSizeErrorRX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received with
	 an FCS or other error and a length between 0 and 64 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 25 }

extremeWirelessClientFrameSizeErrorRX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received with
	 an FCS or other error and a length between 65 and 128 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 26 }

extremeWirelessClientFrameSizeErrorRX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received with
	 an FCS or other error and a length between 129 and 256 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 27 }

extremeWirelessClientFrameSizeErrorRX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received with
	 an FCS or other error and a length between 257 and 512 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 28 }

extremeWirelessClientFrameSizeErrorRX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received with
	 an FCS or other error and a length between 513 and 1024 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 29 }

extremeWirelessClientFrameSizeErrorRX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received with
	 an FCS or other error and a length between 1025 and 2048 bytes."
	 ::= { extremeWirelessClientSizeCounterEntry 30 }

extremeWirelessClientPacketSizeTX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets transmitted to this
	 client which were less than or equal to 64 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 31 }

extremeWirelessClientPacketSizeTX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets transmitted to this
	 client which were between 64 and 128 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 32 }

extremeWirelessClientPacketSizeTX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets transmitted to this
	 client which were between 128 and 256 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 33 }

extremeWirelessClientPacketSizeTX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets transmitted to this
	 client which were between 256 and 512 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 34 }

extremeWirelessClientPacketSizeTX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets transmitted to this
	 client which were between 512 and 1024 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 35 }

extremeWirelessClientPacketSizeTX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets transmitted to this
	 client which were between 1024 and 2048 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 36 }

extremeWirelessClientPacketSizeRX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets received from this
	 client which were less than or equal to 64 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 37 }

extremeWirelessClientPacketSizeRX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets received from this
	 client which were between 64 and 128 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 38 }

extremeWirelessClientPacketSizeRX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets received from this
	 client which were between 128 and 256 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 39 }

extremeWirelessClientPacketSizeRX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets received from this
	 client which were between 256 and 512 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 40 }

extremeWirelessClientPacketSizeRX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets received from this
	 client which were between 512 and 1024 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 41 }

extremeWirelessClientPacketSizeRX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of packets received from this
	 client which were between 1024 and 2048 bytes in length."
	 ::= { extremeWirelessClientSizeCounterEntry 42 }

-- ************************************************************** --
-- Per Client Speed : This table gives a histogram of statistics   --
-- based on the speed of transmission/reception of packets on a    --
-- virtual interface.                                              --
-- ************************************************************** --
extremeWirelessClientSpeedCounterTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientSpeedCounterEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table is indexed by wireless interface index.  These
	 are the virtual interfaces corresponding to the SSID.  Support
	 for this table is optional, indicated by the value of the variable
	 extremeWirelessClientDiagSupportsSpeedCounters in the status table."
	 ::= { extremeAP 38 }

extremeWirelessClientSpeedCounterEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientSpeedCounterEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"The ifIndex here is the wireless interface (SSID).  It is expected
	 that an entry will be created in this table only after a client
	 sends a AUTH or REATH packet to the SSID."
	INDEX { ifIndex, extremeWirelessClientDiagMac }
	 ::= { extremeWirelessClientSpeedCounterTable 1 }

ExtremeWirelessClientSpeedCounterEntry ::= SEQUENCE {
	extremeWirelessClientSpeedReXmit1	Unsigned32,
	extremeWirelessClientSpeedReXmit2	Unsigned32,
	extremeWirelessClientSpeedReXmit5p5	Unsigned32,
	extremeWirelessClientSpeedReXmit6	Unsigned32,
	extremeWirelessClientSpeedReXmit9	Unsigned32,
	extremeWirelessClientSpeedReXmit12	Unsigned32,
	extremeWirelessClientSpeedReXmit18	Unsigned32,
	extremeWirelessClientSpeedReXmit24	Unsigned32,
	extremeWirelessClientSpeedReXmit36	Unsigned32,
	extremeWirelessClientSpeedReXmit48	Unsigned32,
	extremeWirelessClientSpeedReXmit54	Unsigned32,
	extremeWirelessClientSpeedTX1		Unsigned32,
	extremeWirelessClientSpeedTX2		Unsigned32,
	extremeWirelessClientSpeedTX5p5		Unsigned32,
	extremeWirelessClientSpeedTX6		Unsigned32,
	extremeWirelessClientSpeedTX9		Unsigned32,
	extremeWirelessClientSpeedTX12		Unsigned32,
	extremeWirelessClientSpeedTX18		Unsigned32,
	extremeWirelessClientSpeedTX24		Unsigned32,
	extremeWirelessClientSpeedTX36		Unsigned32,
	extremeWirelessClientSpeedTX48		Unsigned32,
	extremeWirelessClientSpeedTX54		Unsigned32,
	extremeWirelessClientSpeedRX1		Unsigned32,
	extremeWirelessClientSpeedRX2		Unsigned32,
	extremeWirelessClientSpeedRX5p5		Unsigned32,
	extremeWirelessClientSpeedRX6		Unsigned32,
	extremeWirelessClientSpeedRX9		Unsigned32,
	extremeWirelessClientSpeedRX12		Unsigned32,
	extremeWirelessClientSpeedRX18		Unsigned32,
	extremeWirelessClientSpeedRX24		Unsigned32,
	extremeWirelessClientSpeedRX36		Unsigned32,
	extremeWirelessClientSpeedRX48		Unsigned32,
	extremeWirelessClientSpeedRX54		Unsigned32
}

extremeWirelessClientSpeedReXmit1	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 1 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 1 }

extremeWirelessClientSpeedReXmit2	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 2 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 2 }

extremeWirelessClientSpeedReXmit5p5 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 5 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 3 }

extremeWirelessClientSpeedReXmit6 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 6 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 4 }

extremeWirelessClientSpeedReXmit9 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 9 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 5 }

extremeWirelessClientSpeedReXmit12 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 12 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 6 }

extremeWirelessClientSpeedReXmit18 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 18 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 7 }

extremeWirelessClientSpeedReXmit24 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 24 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 8 }
	
extremeWirelessClientSpeedReXmit36 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 36 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 9 }

extremeWirelessClientSpeedReXmit48 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 48 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 10 }

extremeWirelessClientSpeedReXmit54 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were retransmitted to
	 the given client at 54 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 11 }

extremeWirelessClientSpeedTX1	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 1 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 12 }

extremeWirelessClientSpeedTX2	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 2 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 13 }

extremeWirelessClientSpeedTX5p5 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 5 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 14 }

extremeWirelessClientSpeedTX6 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 6 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 15 }

extremeWirelessClientSpeedTX9 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 9 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 16 }

extremeWirelessClientSpeedTX12 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 12 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 17 }

extremeWirelessClientSpeedTX18 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 18 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 18 }

extremeWirelessClientSpeedTX24 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 24 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 19 }
	
extremeWirelessClientSpeedTX36 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 36 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 20 }

extremeWirelessClientSpeedTX48 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 48 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 21 }

extremeWirelessClientSpeedTX54 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were transmitted to
	 the given client at 54 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 22 }

extremeWirelessClientSpeedRX1	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 1 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 23 }

extremeWirelessClientSpeedRX2	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 2 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 24 }

extremeWirelessClientSpeedRX5p5 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 5 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 25 }

extremeWirelessClientSpeedRX6 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 6 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 26 }

extremeWirelessClientSpeedRX9 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 9 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 27 }

extremeWirelessClientSpeedRX12 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 12 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 28 }

extremeWirelessClientSpeedRX18 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 18 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 29 }

extremeWirelessClientSpeedRX24 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 24 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 30 }
	
extremeWirelessClientSpeedRX36 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 36 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 31 }

extremeWirelessClientSpeedRX48 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 48 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 32 }

extremeWirelessClientSpeedRX54 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frags which were received from
	 the given client at 54 Mbps."
	::= { extremeWirelessClientSpeedCounterEntry 33 }

-- ************************************************************** --
-- Frame Size Table : This table presents a historgram of frames   --
--                    based on type and size.                      --
-- ************************************************************** --
extremeWirelessIntfFrameSizeTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessIntfFrameSizeEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table for each virtual and
	 physical interface.  Values for physical interfaces are
	 summations of the values for the constituent virtual 
	 interfaces."
	 ::= { extremeAP 39 }

extremeWirelessIntfFrameSizeEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessIntfFrameSizeEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessIntfFrameSizeTable 1 }
ExtremeWirelessIntfFrameSizeEntry ::= SEQUENCE {
	extremeWirelessIntfFrameSizeMgmtTX64	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtTX128	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtTX256	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtTX512	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtTX1024	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtTX2048	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtRX64	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtRX128	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtRX256	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtRX512	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtRX1024	Unsigned32,
	extremeWirelessIntfFrameSizeMgmtRX2048	Unsigned32,
	extremeWirelessIntfFrameSizeCtlTX64	Unsigned32,
	extremeWirelessIntfFrameSizeCtlTX128	Unsigned32,
	extremeWirelessIntfFrameSizeCtlTX256	Unsigned32,
	extremeWirelessIntfFrameSizeCtlTX512	Unsigned32,
	extremeWirelessIntfFrameSizeCtlTX1024	Unsigned32,
	extremeWirelessIntfFrameSizeCtlTX2048	Unsigned32,
	extremeWirelessIntfFrameSizeCtlRX64	Unsigned32,
	extremeWirelessIntfFrameSizeCtlRX128	Unsigned32,
	extremeWirelessIntfFrameSizeCtlRX256	Unsigned32,
	extremeWirelessIntfFrameSizeCtlRX512	Unsigned32,
	extremeWirelessIntfFrameSizeCtlRX1024	Unsigned32,
	extremeWirelessIntfFrameSizeCtlRX2048	Unsigned32,
	extremeWirelessIntfFrameSizeDataTX64	Unsigned32,
	extremeWirelessIntfFrameSizeDataTX128	Unsigned32,
	extremeWirelessIntfFrameSizeDataTX256	Unsigned32,
	extremeWirelessIntfFrameSizeDataTX512	Unsigned32,
	extremeWirelessIntfFrameSizeDataTX1024	Unsigned32,
	extremeWirelessIntfFrameSizeDataTX2048	Unsigned32,
	extremeWirelessIntfFrameSizeDataRX64	Unsigned32,
	extremeWirelessIntfFrameSizeDataRX128	Unsigned32,
	extremeWirelessIntfFrameSizeDataRX256	Unsigned32,
	extremeWirelessIntfFrameSizeDataRX512	Unsigned32,
	extremeWirelessIntfFrameSizeDataRX1024	Unsigned32,
	extremeWirelessIntfFrameSizeDataRX2048	Unsigned32 }

extremeWirelessIntfFrameSizeMgmtTX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames transmitted 
	 on this interface which were less than or equal to 64 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 1 }

extremeWirelessIntfFrameSizeMgmtTX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames transmitted 
	 on this interface which were between 65 and 128 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 2 }

extremeWirelessIntfFrameSizeMgmtTX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames transmitted 
	 on this interface which were between 129 and 256 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 3 }

extremeWirelessIntfFrameSizeMgmtTX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames transmitted 
	 on this interface which were between 257 and 512 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 4 }

extremeWirelessIntfFrameSizeMgmtTX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames transmitted 
	 on this interface which were between 513 and 1024 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 5 }

extremeWirelessIntfFrameSizeMgmtTX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames transmitted 
	 on this interface which were between 1025 and 2048 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 6 }

extremeWirelessIntfFrameSizeMgmtRX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames received 
	 on this interface which were less than or equal to 64 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 7 }

extremeWirelessIntfFrameSizeMgmtRX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames received 
	 on this interface which were between 65 and 128 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 8 }

extremeWirelessIntfFrameSizeMgmtRX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames received 
	 on this interface which were between 129 and 256 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 9 }

extremeWirelessIntfFrameSizeMgmtRX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames received 
	 on this interface which were between 257 and 512 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 10 }

extremeWirelessIntfFrameSizeMgmtRX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames received 
	 on this interface which were between 513 and 1024 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 11 }

extremeWirelessIntfFrameSizeMgmtRX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of management-type frames received 
	 on this interface which were between 1025 and 2048 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 12 }

extremeWirelessIntfFrameSizeCtlTX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames transmitted 
	 on this interface which were less than or equal to 64 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 13 }

extremeWirelessIntfFrameSizeCtlTX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames transmitted 
	 on this interface which were between 65 and 128 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 14 }

extremeWirelessIntfFrameSizeCtlTX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames transmitted 
	 on this interface which were between 129 and 256 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 15 }

extremeWirelessIntfFrameSizeCtlTX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames transmitted 
	 on this interface which were between 257 and 512 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 16 }

extremeWirelessIntfFrameSizeCtlTX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames transmitted 
	 on this interface which were between 513 and 1024 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 17 }

extremeWirelessIntfFrameSizeCtlTX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames transmitted 
	 on this interface which were between 1025 and 2048 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 18 }

extremeWirelessIntfFrameSizeCtlRX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames received 
	 on this interface which were less than or equal to 64 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 19 }

extremeWirelessIntfFrameSizeCtlRX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames received 
	 on this interface which were between 65 and 128 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 20 }

extremeWirelessIntfFrameSizeCtlRX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames received 
	 on this interface which were between 129 and 256 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 21 }

extremeWirelessIntfFrameSizeCtlRX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames received 
	 on this interface which were between 257 and 512 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 22 }

extremeWirelessIntfFrameSizeCtlRX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames received 
	 on this interface which were between 513 and 1024 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 23 }

extremeWirelessIntfFrameSizeCtlRX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of control-type frames received 
	 on this interface which were between 1025 and 2048 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 24 }

extremeWirelessIntfFrameSizeDataTX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames transmitted 
	 on this interface which were less than or equal to 64 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 25 }

extremeWirelessIntfFrameSizeDataTX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames transmitted 
	 on this interface which were between 65 and 128 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 26 }

extremeWirelessIntfFrameSizeDataTX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames transmitted 
	 on this interface which were between 129 and 256 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 27 }

extremeWirelessIntfFrameSizeDataTX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames transmitted 
	 on this interface which were between 257 and 512 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 28 }

extremeWirelessIntfFrameSizeDataTX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames transmitted 
	 on this interface which were between 513 and 1024 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 29 }

extremeWirelessIntfFrameSizeDataTX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames transmitted 
	 on this interface which were between 1025 and 2048 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 30 }

extremeWirelessIntfFrameSizeDataRX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames received 
	 on this interface which were less than or equal to 64 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 31 }

extremeWirelessIntfFrameSizeDataRX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames received 
	 on this interface which were between 65 and 128 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 32 }

extremeWirelessIntfFrameSizeDataRX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames received 
	 on this interface which were between 129 and 256 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 33 }

extremeWirelessIntfFrameSizeDataRX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames received 
	 on this interface which were between 257 and 512 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 34 }

extremeWirelessIntfFrameSizeDataRX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames received 
	 on this interface which were between 513 and 1024 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 35 }

extremeWirelessIntfFrameSizeDataRX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of data-type frames received 
	 on this interface which were between 1025 and 2048 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeEntry 36 }

-- ************************************************************** --
-- Frame errors by size.                                           --
-- ************************************************************** --
extremeWirelessIntfFrameSizeErrorTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessIntfFrameSizeErrorEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table for each virtual and
	 physical interface.  Values for physical interfaces are
	 summations of the values for the constituent virtual 
	 interfaces."
	 ::= { extremeAP 40 }

extremeWirelessIntfFrameSizeErrorEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessIntfFrameSizeErrorEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessIntfFrameSizeErrorTable 1 }


ExtremeWirelessIntfFrameSizeErrorEntry ::= SEQUENCE {
	extremeWirelessIntfFrameSizeReXmit64	Unsigned32,
	extremeWirelessIntfFrameSizeReXmit128	Unsigned32,
	extremeWirelessIntfFrameSizeReXmit256	Unsigned32,
	extremeWirelessIntfFrameSizeReXmit512	Unsigned32,
	extremeWirelessIntfFrameSizeReXmit1024	Unsigned32,
	extremeWirelessIntfFrameSizeReXmit2048	Unsigned32,
	extremeWirelessIntfFrameSizeErrorTX64	Unsigned32,
	extremeWirelessIntfFrameSizeErrorTX128	Unsigned32,
	extremeWirelessIntfFrameSizeErrorTX256	Unsigned32,
	extremeWirelessIntfFrameSizeErrorTX512	Unsigned32,
	extremeWirelessIntfFrameSizeErrorTX1024	Unsigned32,
	extremeWirelessIntfFrameSizeErrorTX2048	Unsigned32,
	extremeWirelessIntfFrameSizeErrorRX64	Unsigned32,
	extremeWirelessIntfFrameSizeErrorRX128	Unsigned32,
	extremeWirelessIntfFrameSizeErrorRX256	Unsigned32,
	extremeWirelessIntfFrameSizeErrorRX512	Unsigned32,
	extremeWirelessIntfFrameSizeErrorRX1024	Unsigned32,
	extremeWirelessIntfFrameSizeErrorRX2048	Unsigned32 }

extremeWirelessIntfFrameSizeReXmit64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames retransmitted on this 
	 interface which were less than or equal to 64 bytes
	 in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 1 }

extremeWirelessIntfFrameSizeReXmit128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames retransmitted on this 
	 interface which were between 65 and 128 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 2 }

extremeWirelessIntfFrameSizeReXmit256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames retransmitted on this 
	 interface which were between 129 and 256 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 3 }

extremeWirelessIntfFrameSizeReXmit512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames retransmitted on this 
	 interface which were between 257 and 512 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 4 }

extremeWirelessIntfFrameSizeReXmit1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames retransmitted on this 
	 interface which were between 513 and 1024 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 5 }

extremeWirelessIntfFrameSizeReXmit2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames retransmitted on this 
	 interface which were between 1025 and 2048 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 6 }

extremeWirelessIntfFrameSizeErrorTX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which failed to be 
	 transmitted interface which were less than or equal 
	 to 64 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 7 }

extremeWirelessIntfFrameSizeErrorTX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which failed to be 
	 transmitted interface which were between 65 and 128
	 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 8 }

extremeWirelessIntfFrameSizeErrorTX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which failed to be 
	 transmitted interface which were between 129 and 256
	 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 9 }

extremeWirelessIntfFrameSizeErrorTX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which failed to be 
	 transmitted interface which were between 257 and 512
	 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 10 }

extremeWirelessIntfFrameSizeErrorTX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which failed to be 
	 transmitted interface which were between 513 and 1024
	 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 11 }

extremeWirelessIntfFrameSizeErrorTX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which failed to be 
	 transmitted interface which were between 1025 and 2048
	 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 12 }

extremeWirelessIntfFrameSizeErrorRX64 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which had errors on
	 receive on this interface which were less than or equal 
	 to 64 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 13 }

extremeWirelessIntfFrameSizeErrorRX128 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which had errors on
	 receive on this interface which were between 65 and
	 128 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 14 }

extremeWirelessIntfFrameSizeErrorRX256 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which had errors on
	 receive on this interface which were between 129 and
	 256 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 15 }

extremeWirelessIntfFrameSizeErrorRX512 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which had errors on
	 receive on this interface which were between 257 and
	 512 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 16 }

extremeWirelessIntfFrameSizeErrorRX1024 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which had errors on
	 receive on this interface which were between 513 and
	 1024 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 17 }

extremeWirelessIntfFrameSizeErrorRX2048 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which had errors on
	 receive on this interface which were between 1025 and
	 2048 bytes in length."
	 ::= { extremeWirelessIntfFrameSizeErrorEntry 18 }

-- ************************************************************** --
-- TX/RX Histogram based on speed.
-- ************************************************************** --
extremeWirelessIntfFrameSpeedTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessIntfFrameSpeedEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table for each virtual and
	 physical interface.  Values for physical interfaces are
	 summations of the values for the constituent virtual 
	 interfaces."
	 ::= { extremeAP 41 }

extremeWirelessIntfFrameSpeedEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessIntfFrameSpeedEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessIntfFrameSpeedTable 1 }

ExtremeWirelessIntfFrameSpeedEntry ::= SEQUENCE {
	extremeWirelessIntfSpeedMgmtTX1		Unsigned32,
	extremeWirelessIntfSpeedMgmtTX2		Unsigned32,
	extremeWirelessIntfSpeedMgmtTX5p5	Unsigned32,
	extremeWirelessIntfSpeedMgmtTX6		Unsigned32,
	extremeWirelessIntfSpeedMgmtTX9		Unsigned32,
    extremeWirelessIntfSpeedMgmtTX11    Unsigned32,
	extremeWirelessIntfSpeedMgmtTX12	Unsigned32,
	extremeWirelessIntfSpeedMgmtTX18	Unsigned32,
	extremeWirelessIntfSpeedMgmtTX24	Unsigned32,
	extremeWirelessIntfSpeedMgmtTX36	Unsigned32,
	extremeWirelessIntfSpeedMgmtTX48	Unsigned32,
	extremeWirelessIntfSpeedMgmtTX54	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX1		Unsigned32,
	extremeWirelessIntfSpeedMgmtRX2		Unsigned32,
	extremeWirelessIntfSpeedMgmtRX5p5	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX6		Unsigned32,
	extremeWirelessIntfSpeedMgmtRX9		Unsigned32,
	extremeWirelessIntfSpeedMgmtRX11	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX12	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX18	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX24	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX36	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX48	Unsigned32,
	extremeWirelessIntfSpeedMgmtRX54	Unsigned32,
	extremeWirelessIntfSpeedCtlTX1		Unsigned32,
	extremeWirelessIntfSpeedCtlTX2		Unsigned32,
	extremeWirelessIntfSpeedCtlTX5p5	Unsigned32,
	extremeWirelessIntfSpeedCtlTX6		Unsigned32,
	extremeWirelessIntfSpeedCtlTX9		Unsigned32,
    extremeWirelessIntfSpeedCtlTX11     Unsigned32,
	extremeWirelessIntfSpeedCtlTX12		Unsigned32,
	extremeWirelessIntfSpeedCtlTX18		Unsigned32,
	extremeWirelessIntfSpeedCtlTX24		Unsigned32,
	extremeWirelessIntfSpeedCtlTX36		Unsigned32,
	extremeWirelessIntfSpeedCtlTX48		Unsigned32,
	extremeWirelessIntfSpeedCtlTX54		Unsigned32,
	extremeWirelessIntfSpeedCtlRX1		Unsigned32,
	extremeWirelessIntfSpeedCtlRX2		Unsigned32,
	extremeWirelessIntfSpeedCtlRX5p5	Unsigned32,
	extremeWirelessIntfSpeedCtlRX6		Unsigned32,
	extremeWirelessIntfSpeedCtlRX9		Unsigned32,
    extremeWirelessIntfSpeedCtlRX11     Unsigned32,
	extremeWirelessIntfSpeedCtlRX12		Unsigned32,
	extremeWirelessIntfSpeedCtlRX18		Unsigned32,
	extremeWirelessIntfSpeedCtlRX24		Unsigned32,
	extremeWirelessIntfSpeedCtlRX36		Unsigned32,
	extremeWirelessIntfSpeedCtlRX48		Unsigned32,
	extremeWirelessIntfSpeedCtlRX54		Unsigned32,
	extremeWirelessIntfSpeedDataTX1		Unsigned32,
	extremeWirelessIntfSpeedDataTX2		Unsigned32,
	extremeWirelessIntfSpeedDataTX5p5	Unsigned32,
	extremeWirelessIntfSpeedDataTX6		Unsigned32,
	extremeWirelessIntfSpeedDataTX9		Unsigned32,
    extremeWirelessIntfSpeedDataTX11    Unsigned32,
	extremeWirelessIntfSpeedDataTX12	Unsigned32,
	extremeWirelessIntfSpeedDataTX18	Unsigned32,
	extremeWirelessIntfSpeedDataTX24	Unsigned32,
	extremeWirelessIntfSpeedDataTX36	Unsigned32,
	extremeWirelessIntfSpeedDataTX48	Unsigned32,
	extremeWirelessIntfSpeedDataTX54	Unsigned32,
	extremeWirelessIntfSpeedDataRX1		Unsigned32,
	extremeWirelessIntfSpeedDataRX2		Unsigned32,
	extremeWirelessIntfSpeedDataRX5p5	Unsigned32,
	extremeWirelessIntfSpeedDataRX6		Unsigned32,
	extremeWirelessIntfSpeedDataRX9		Unsigned32,
    extremeWirelessIntfSpeedDataRX11    Unsigned32,
	extremeWirelessIntfSpeedDataRX12	Unsigned32,
	extremeWirelessIntfSpeedDataRX18	Unsigned32,
	extremeWirelessIntfSpeedDataRX24	Unsigned32,
	extremeWirelessIntfSpeedDataRX36	Unsigned32,
	extremeWirelessIntfSpeedDataRX48	Unsigned32,
	extremeWirelessIntfSpeedDataRX54	Unsigned32 }

extremeWirelessIntfSpeedMgmtTX1 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 1Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 1 }

extremeWirelessIntfSpeedMgmtTX2 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 2Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 2 }

extremeWirelessIntfSpeedMgmtTX5p5 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 5.5Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 3 }

extremeWirelessIntfSpeedMgmtTX6 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 6Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 4 }

extremeWirelessIntfSpeedMgmtTX9 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 9Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 5 }

extremeWirelessIntfSpeedMgmtTX11 OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This gives the number of management-type frames transmitted
     on this interface at 11Mbps."
     ::= { extremeWirelessIntfFrameSpeedEntry 6 }

extremeWirelessIntfSpeedMgmtTX12 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 12Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 7 }

extremeWirelessIntfSpeedMgmtTX18 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 18Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 8 }

extremeWirelessIntfSpeedMgmtTX24 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 24Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 9 }

extremeWirelessIntfSpeedMgmtTX36 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 36Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 10 }

extremeWirelessIntfSpeedMgmtTX48 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 48Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 11 }

extremeWirelessIntfSpeedMgmtTX54 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames transmitted
	 on this interface at 54Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 12 }

extremeWirelessIntfSpeedMgmtRX1 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 1Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 13 }

extremeWirelessIntfSpeedMgmtRX2 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 2Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 14 }

extremeWirelessIntfSpeedMgmtRX5p5 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 5.5Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 15 }

extremeWirelessIntfSpeedMgmtRX6 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 6Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 16 }

extremeWirelessIntfSpeedMgmtRX9 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 9Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 17 }

extremeWirelessIntfSpeedMgmtRX11 OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This gives the number of management-type frames recieved
     on this interface at 11Mbps."
     ::= { extremeWirelessIntfFrameSpeedEntry 18 }

extremeWirelessIntfSpeedMgmtRX12 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 12Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 19 }

extremeWirelessIntfSpeedMgmtRX18 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 18Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 20 }

extremeWirelessIntfSpeedMgmtRX24 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 24Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 21 }

extremeWirelessIntfSpeedMgmtRX36 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 36Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 22 }

extremeWirelessIntfSpeedMgmtRX48 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 48Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 23 }

extremeWirelessIntfSpeedMgmtRX54 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of management-type frames recieved
	 on this interface at 54Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 24 }

extremeWirelessIntfSpeedCtlTX1 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 1Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 25 }

extremeWirelessIntfSpeedCtlTX2 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 2Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 26 }

extremeWirelessIntfSpeedCtlTX5p5 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 5.5Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 27 }

extremeWirelessIntfSpeedCtlTX6 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 6Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 28 }

extremeWirelessIntfSpeedCtlTX9 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 9Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 29 }

extremeWirelessIntfSpeedCtlTX11 OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This gives the number of control-type frames transmitted
     on this interface at 11Mbps."
     ::= { extremeWirelessIntfFrameSpeedEntry 30 }

extremeWirelessIntfSpeedCtlTX12 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 12Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 31 }

extremeWirelessIntfSpeedCtlTX18 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 18Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 32 }

extremeWirelessIntfSpeedCtlTX24 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 24Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 33 }

extremeWirelessIntfSpeedCtlTX36 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 36Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 34 }

extremeWirelessIntfSpeedCtlTX48 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 48Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 35 }

extremeWirelessIntfSpeedCtlTX54 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames transmitted
	 on this interface at 54Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 36 }

extremeWirelessIntfSpeedCtlRX1 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 1Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 37 }

extremeWirelessIntfSpeedCtlRX2 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 2Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 38 }

extremeWirelessIntfSpeedCtlRX5p5 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 5.5Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 39 }

extremeWirelessIntfSpeedCtlRX6 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 6Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 40 }

extremeWirelessIntfSpeedCtlRX9 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 9Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 41 }

extremeWirelessIntfSpeedCtlRX11 OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This gives the number of control-type frames recieved
     on this interface at 11Mbps."
     ::= { extremeWirelessIntfFrameSpeedEntry 42 }


extremeWirelessIntfSpeedCtlRX12 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 12Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 43 }

extremeWirelessIntfSpeedCtlRX18 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 18Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 44 }

extremeWirelessIntfSpeedCtlRX24 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 24Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 45 }

extremeWirelessIntfSpeedCtlRX36 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 36Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 46 }

extremeWirelessIntfSpeedCtlRX48 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 48Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 47 }

extremeWirelessIntfSpeedCtlRX54 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of control-type frames recieved
	 on this interface at 54Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 48 }

extremeWirelessIntfSpeedDataTX1 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 1Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 49 }

extremeWirelessIntfSpeedDataTX2 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 2Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 50 }

extremeWirelessIntfSpeedDataTX5p5 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 5.5Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 51 }

extremeWirelessIntfSpeedDataTX6 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 6Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 52 }

extremeWirelessIntfSpeedDataTX9 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 9Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 53 }

extremeWirelessIntfSpeedDataTX11 OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This gives the number of data-type frames transmitted
     on this interface at 11Mbps."
     ::= { extremeWirelessIntfFrameSpeedEntry 54 }

extremeWirelessIntfSpeedDataTX12 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 12Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 55 }

extremeWirelessIntfSpeedDataTX18 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 18Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 56 }

extremeWirelessIntfSpeedDataTX24 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 24Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 57 }

extremeWirelessIntfSpeedDataTX36 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 36Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 58 }

extremeWirelessIntfSpeedDataTX48 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 48Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 59 }

extremeWirelessIntfSpeedDataTX54 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames transmitted
	 on this interface at 54Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 60 }

extremeWirelessIntfSpeedDataRX1 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 1Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 61 }

extremeWirelessIntfSpeedDataRX2 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 2Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 62 }

extremeWirelessIntfSpeedDataRX5p5 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 5.5Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 63 }

extremeWirelessIntfSpeedDataRX6 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 6Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 64 }

extremeWirelessIntfSpeedDataRX9 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 9Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 65 }

extremeWirelessIntfSpeedDataRX11 OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This gives the number of data-type frames recieved
     on this interface at 11Mbps."
     ::= { extremeWirelessIntfFrameSpeedEntry 66 }

extremeWirelessIntfSpeedDataRX12 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 12Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 67 }

extremeWirelessIntfSpeedDataRX18 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 18Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 68 }

extremeWirelessIntfSpeedDataRX24 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 24Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 69 }

extremeWirelessIntfSpeedDataRX36 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 36Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 70 }

extremeWirelessIntfSpeedDataRX48 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 48Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 71 }

extremeWirelessIntfSpeedDataRX54 OBJECT-TYPE
	SYNTAX		Unsigned32
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This gives the number of data-type frames recieved
	 on this interface at 54Mbps."
	 ::= { extremeWirelessIntfFrameSpeedEntry 72 }

-- ************************************************************** --
-- Error Histograms of retransmission by frame speed.              --
-- ************************************************************** --
extremeWirelessIntfFrameSpeedErrorTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessIntfFrameSpeedErrorEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table for each virtual and
	 physical interface.  Values for physical interfaces are
	 summations of the values for the constituent virtual 
	 interfaces."
	 ::= { extremeAP 42 }

extremeWirelessIntfFrameSpeedErrorEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessIntfFrameSpeedErrorEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessIntfFrameSpeedErrorTable 1 }

ExtremeWirelessIntfFrameSpeedErrorEntry ::= SEQUENCE {
	extremeWirelessIntfSpeedReXmit1		Unsigned32,
	extremeWirelessIntfSpeedReXmit2		Unsigned32,
	extremeWirelessIntfSpeedReXmit5p5	Unsigned32,
	extremeWirelessIntfSpeedReXmit6		Unsigned32,
	extremeWirelessIntfSpeedReXmit9		Unsigned32,
    extremeWirelessIntfSpeedReXmit11    Unsigned32,
	extremeWirelessIntfSpeedReXmit12	Unsigned32,
	extremeWirelessIntfSpeedReXmit18	Unsigned32,
	extremeWirelessIntfSpeedReXmit24	Unsigned32,
	extremeWirelessIntfSpeedReXmit36	Unsigned32,
	extremeWirelessIntfSpeedReXmit48	Unsigned32,
	extremeWirelessIntfSpeedReXmit54	Unsigned32,
    extremeWirelessIntfSpeedErrorTX1    Unsigned32,
    extremeWirelessIntfSpeedErrorTX2    Unsigned32,
    extremeWirelessIntfSpeedErrorTX5p5  Unsigned32,
    extremeWirelessIntfSpeedErrorTX6    Unsigned32,
    extremeWirelessIntfSpeedErrorTX9    Unsigned32,
    extremeWirelessIntfSpeedErrorTX11   Unsigned32,
    extremeWirelessIntfSpeedErrorTX12   Unsigned32,
    extremeWirelessIntfSpeedErrorTX18   Unsigned32,
    extremeWirelessIntfSpeedErrorTX24   Unsigned32,
    extremeWirelessIntfSpeedErrorTX36   Unsigned32,
    extremeWirelessIntfSpeedErrorTX48   Unsigned32,
    extremeWirelessIntfSpeedErrorTX54   Unsigned32,
	extremeWirelessIntfSpeedErrorRX1	Unsigned32,
	extremeWirelessIntfSpeedErrorRX2	Unsigned32,
	extremeWirelessIntfSpeedErrorRX5p5	Unsigned32,
	extremeWirelessIntfSpeedErrorRX6	Unsigned32,
	extremeWirelessIntfSpeedErrorRX9	Unsigned32,
    extremeWirelessIntfSpeedErrorRX11   Unsigned32,
	extremeWirelessIntfSpeedErrorRX12	Unsigned32,
	extremeWirelessIntfSpeedErrorRX18	Unsigned32,
	extremeWirelessIntfSpeedErrorRX24	Unsigned32,
	extremeWirelessIntfSpeedErrorRX36	Unsigned32,
	extremeWirelessIntfSpeedErrorRX48	Unsigned32,
	extremeWirelessIntfSpeedErrorRX54	Unsigned32 }

extremeWirelessIntfSpeedReXmit1	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 1 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 1 }

extremeWirelessIntfSpeedReXmit2	OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 2 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 2 }

extremeWirelessIntfSpeedReXmit5p5 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 5 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 3 }

extremeWirelessIntfSpeedReXmit6 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 6 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 4 }

extremeWirelessIntfSpeedReXmit9 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 9 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 5 }

extremeWirelessIntfSpeedReXmit11 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were retransmitted on
    this interface at 11 Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 6 }

extremeWirelessIntfSpeedReXmit12 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 12 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 7 }

extremeWirelessIntfSpeedReXmit18 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 18 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 8 }

extremeWirelessIntfSpeedReXmit24 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 24 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 9 }
	
extremeWirelessIntfSpeedReXmit36 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 36 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 10 }

extremeWirelessIntfSpeedReXmit48 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 48 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 11 }

extremeWirelessIntfSpeedReXmit54 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were retransmitted on
	 this interface at 54 Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 12 }

extremeWirelessIntfSpeedErrorTX1 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 1Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 13 }

extremeWirelessIntfSpeedErrorTX2 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 2Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 14 }

extremeWirelessIntfSpeedErrorTX5p5 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 5.5Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 15 }

extremeWirelessIntfSpeedErrorTX6 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 6Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 16 }

extremeWirelessIntfSpeedErrorTX9 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 9Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 17 }

extremeWirelessIntfSpeedErrorTX11 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 11Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 18 }

extremeWirelessIntfSpeedErrorTX12 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 12Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 19 }

extremeWirelessIntfSpeedErrorTX18 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 18Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 20 }

extremeWirelessIntfSpeedErrorTX24 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 24Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 21 }

extremeWirelessIntfSpeedErrorTX36 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 36Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 22 }

extremeWirelessIntfSpeedErrorTX48 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 48Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 23 }

extremeWirelessIntfSpeedErrorTX54 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were transmited with errors
    on this interface at 54Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 24 }

extremeWirelessIntfSpeedErrorRX1 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 1Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 25 }

extremeWirelessIntfSpeedErrorRX2 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 2Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 26 }

extremeWirelessIntfSpeedErrorRX5p5 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 5.5Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 27 }

extremeWirelessIntfSpeedErrorRX6 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 6Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 28 }

extremeWirelessIntfSpeedErrorRX9 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 9Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 29 }

extremeWirelessIntfSpeedErrorRX11 OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "This gives the number of frames which were received with errors
    on this interface at 11Mbps."
    ::= { extremeWirelessIntfFrameSpeedErrorEntry 30 }

extremeWirelessIntfSpeedErrorRX12 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 12Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 31 }

extremeWirelessIntfSpeedErrorRX18 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 18Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 32 }

extremeWirelessIntfSpeedErrorRX24 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 24Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 33 }

extremeWirelessIntfSpeedErrorRX36 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 36Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 34 }

extremeWirelessIntfSpeedErrorRX48 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 48Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 35 }

extremeWirelessIntfSpeedErrorRX54 OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the number of frames which were received with errors
	 on this interface at 54Mbps."
	::= { extremeWirelessIntfFrameSpeedErrorEntry 36 }

-- ************************************************************** --
-- Interface utilization/noise.                                    --
-- ************************************************************** --
extremeWirelessIntfUtilizationTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessIntfUtilizationEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table for each virtual and
	 physical interface.  Values for physical interfaces are
	 summations of the values for the constituent virtual 
	 interfaces."
	 ::= { extremeAP 43 }

extremeWirelessIntfUtilizationEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessIntfUtilizationEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	 ::= { extremeWirelessIntfUtilizationTable 1 }

ExtremeWirelessIntfUtilizationEntry ::= SEQUENCE {
	extremeWirelessIntfUtilizationNav	         Unsigned32,
	extremeWirelessIntfUtilizationNoiseFloor	 Unsigned32 }

extremeWirelessIntfUtilizationNav OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the instantaneous NAV value on this interface, or
	 some increasing-metric of utilization."
	::= { extremeWirelessIntfUtilizationEntry 1 }

extremeWirelessIntfUtilizationNoiseFloor OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the noise floor in dBm for this interface."
	::= { extremeWirelessIntfUtilizationEntry 2 }

-- ************************************************************** --
-- Opaque client MIBs. These mibs are provided as a performance    --
-- gain for the LAC management of the switch.                      --
--                                                                 --
-- Some of the items in this table are encoded as a series of TLVs.--
-- In this case, they are encoded as:                              --
--                                                                 --
--   | Type (8 bits) | length (8 bits) | value (variable) |        --
--                                                                 --
-- type - equal to the index field of the element                  --
--        in the table this value corresponds to.                  --
--                                                                 --
-- length - equal to the length in bytes of the value              --
--                                                                 --
-- value -  this is the value.  Integer value are NBO,             --
--          strings are non-null terminated ASCII.                 --
--                                                                 --
-- ************************************************************** --
extremeWirelessOpaqueTable OBJECT-TYPE
	SYNTAX		SEQUENCE OF ExtremeWirelessOpaqueEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	::= { extremeAP 99 }

extremeWirelessOpaqueEntry OBJECT-TYPE
	SYNTAX		ExtremeWirelessOpaqueEntry
	MAX-ACCESS	not-accessible
	STATUS		current
	DESCRIPTION
		" "
	INDEX { ifIndex }
	::= { extremeWirelessOpaqueTable 1 }

ExtremeWirelessOpaqueEntry ::= SEQUENCE {
	extremeWirelessClientData		OCTET STRING,
	extremeWirelessPAEStatsData		OCTET STRING,
	extremeWirelessPAEDiagData		OCTET STRING,
	extremeWirelessScanResultsData		OCTET STRING,
	extremeWirelessProbeInfoData		OCTET STRING,
	extremeWirelessClientDiagData		OCTET STRING,
	extremeWirelessClientAssocData		OCTET STRING,
	extremeWirelessClientAuthData		OCTET STRING,
	extremeWirelessClientMACInfoData	OCTET STRING,
	extremeWirelessSizeCounterData		OCTET STRING,
	extremeWirelessSpeedCounterData		OCTET STRING }

extremeWirelessClientData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	""
	::= { extremeWirelessOpaqueEntry 1 }	

extremeWirelessPAEStatsData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	""
	::= { extremeWirelessOpaqueEntry 2 }	

extremeWirelessPAEDiagData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	""
	::= { extremeWirelessOpaqueEntry 3 }

extremeWirelessScanResultsData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the scan results table encoded
	 as a series of TLVs."
	::= { extremeWirelessOpaqueEntry 4 }

extremeWirelessProbeInfoData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the probe info table encoded 
	 as a series of TLVs.    Each item is encoded as the
	 entry above."
	::= { extremeWirelessOpaqueEntry 5 }

extremeWirelessClientDiagData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the client diag table encoded 
	 as a series of TLVs.    Each item is encoded as the
	 entry above."
	::= { extremeWirelessOpaqueEntry 6 }

extremeWirelessClientAssocData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the client assoc table encoded 
	 as a series of TLVs.    Each item is encoded as the
	 entry above.  Note that since the MAC address is not a
	 member of the table (it is an index) it will be encoded
	 as type = 0."
	::= { extremeWirelessOpaqueEntry 7 }

extremeWirelessClientAuthData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the client auth table encoded 
	 as a series of TLVs.    Each item is encoded as the
	 entry above.  Note that since the MAC address is not a
	 member of the table (it is an index) it will be encoded
	 as type = 0."
	::= { extremeWirelessOpaqueEntry 8 }

extremeWirelessClientMACInfoData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the client MAC info table 
	 encoded as a series of TLVs.    Each item is encoded 
	 as the entry above.  Note that since the MAC address 
	 is not a member of the table (it is an index) it will 
	 be encoded as type = 0."
	::= { extremeWirelessOpaqueEntry 9 }

extremeWirelessSizeCounterData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the client size counter table
	 encoded as a series of TLVs.    Each item is encoded 
	 as the entry above.  Note that since the MAC address 
	 is not a member of the table (it is an index) it will 
	 be encoded as type = 0."
	::= { extremeWirelessOpaqueEntry 10 }

extremeWirelessSpeedCounterData OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the contents of the client speed counter table
	 encoded as a series of TLVs.    Each item is encoded 
	 as the entry above.  Note that since the MAC address 
	 is not a member of the table (it is an index) it will 
	 be encoded as type = 0."
	::= { extremeWirelessOpaqueEntry 11 }

-- ************************************************************** --
-- TRACE MIBS                                                      --
--                                                                 --
-- These mibs are provided on a per-wireless port basis to support --
-- configurable tracing.  Each subsystem associated with a         --
-- wireless port has an entry in the trace module table.  These    --
-- entries allow for configuration of the current trace level.     --
-- statistics on each modules tracing output are also kept.  At    --
-- a global level several statistics are available from the trace  --
-- table.                                                          --
-- ************************************************************** --
extremeWirelessTraceTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessTraceTableEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	 "This table contains one entry per wireless port.  This
          allows access to various stastics about the tracing mod  --
          ule on that wireless port."
	::= { extremeAP 100 }

extremeWirelessTraceEntry OBJECT-TYPE
	SYNTAX 		ExtremeWirelessTraceTableEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex }
	::= { extremeWirelessTraceTable 1 }

ExtremeWirelessTraceTableEntry ::= SEQUENCE {
	extremeWirelessTraceMsgsOut	INTEGER,
	extremeWirelessTraceBytesOut	INTEGER,
	extremeWirelessTraceSuppressed  INTEGER,
	extremeWirelessTraceOtherErr	INTEGER,
	extremeWirelessTraceOpaque	OCTET STRING }

extremeWirelessTraceMsgsOut OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS 		current
	DESCRIPTION
	"This gives the number of trace messages the system has
	 sent out to the syslog server.  Suppressed messages are
	 not counted toward this total."
	::= { extremeWirelessTraceEntry 1 }

extremeWirelessTraceBytesOut OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This give the number of bytes total sent out by the
	 trace system.  This is a simple sum of the string
	 lengths of all messages sent out."
	::= { extremeWirelessTraceEntry 2 }

extremeWirelessTraceSuppressed OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This variable is incremented whenever a message is
	 not sent out because the trace level of that system
	 is lower than the message level."
	::= { extremeWirelessTraceEntry 3 }

extremeWirelessTraceOtherErr OBJECT-TYPE
	SYNTAX		INTEGER
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This variable is incremented when a message is not 
	 sent out due to internal errors (i.e. out of memory)."
	::= { extremeWirelessTraceEntry 4 }

extremeWirelessTraceOpaque OBJECT-TYPE
	SYNTAX		OCTET STRING
	MAX-ACCESS	read-only
	STATUS		current
	DESCRIPTION
	"This is the opaque representation of the module table
	 to optimize retreival."
	::= { extremeWirelessTraceEntry 5 }

extremeWirelessTraceModuleTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessTraceModuleEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	 "This table contains one entry per trace module.  Each entry
	  is indexed by an arbitrary integer value."
	::= { extremeAP 101 }

extremeWirelessTraceModuleEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessTraceModuleEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	""
	INDEX { ifIndex, extremeWirelessTraceModuleId }
	::= { extremeWirelessTraceModuleTable 1 }

ExtremeWirelessTraceModuleEntry ::= SEQUENCE {
	extremeWirelessTraceModuleId	     INTEGER,
	extremeWirelessTraceModuleDesc       DisplayString,
	extremeWirelessTraceModuleHeader     DisplayString,
	extremeWirelessTraceModuleLevel      INTEGER,
	extremeWirelessTraceModuleSuppressed INTEGER,
	extremeWirelessTraceModuleMsgsOut    INTEGER,
	extremeWirelessTraceModuleBytesOut   INTEGER }

extremeWirelessTraceModuleId	     OBJECT-TYPE
	SYNTAX	INTEGER(0..65535)
	MAX-ACCESS not-accessible
	STATUS 	current
	DESCRIPTION
	"This is an arbitrary integer index which is assigned to each
	 trace module."
	::= { extremeWirelessTraceModuleEntry 1 }

extremeWirelessTraceModuleDesc	     OBJECT-TYPE
	SYNTAX	DisplayString
	MAX-ACCESS read-only
	STATUS 	current
	DESCRIPTION
	"This is a string which briefly describes the purpose of this
	 debug trace.  It may include whitespace.  An example is:
		802.1x State Machine"
	::= { extremeWirelessTraceModuleEntry 2 }

extremeWirelessTraceModuleHeader     OBJECT-TYPE
	SYNTAX	DisplayString
	MAX-ACCESS read-only
	STATUS 	current
	DESCRIPTION
	"This is a string which is prepended to the syslog messages
	 sent out by this debug level.  It is short (5-6 characters) 
	 and does not contain white space. (i.e. DOT1X)"
	::= { extremeWirelessTraceModuleEntry 3 }

extremeWirelessTraceModuleLevel     OBJECT-TYPE
	SYNTAX	INTEGER(0..5)
	MAX-ACCESS read-write
	STATUS 	current
	DESCRIPTION
	"This value is the current debug level for this module.  A
	 lower value indicates that fewer messages will be generated
	 by the component.  A write on this value will update the
	 current debug level."
	::= { extremeWirelessTraceModuleEntry 4 }

extremeWirelessTraceModuleSuppressed     OBJECT-TYPE
	SYNTAX	INTEGER
	MAX-ACCESS read-only
	STATUS 	current
	DESCRIPTION
	"This value indicates the number of messages logged by
	 this module which have not been sent out because the
	 current log level is less than the level at which the
	 message should be logged."
	::= { extremeWirelessTraceModuleEntry 5 }

extremeWirelessTraceModuleMsgsOut     OBJECT-TYPE
	SYNTAX	INTEGER
	MAX-ACCESS read-only
	STATUS 	current
	DESCRIPTION
	"This value indicates the number messages which have
	 been logged by this component."
	::= { extremeWirelessTraceModuleEntry 6 }

extremeWirelessTraceModuleBytesOut     OBJECT-TYPE
	SYNTAX	INTEGER
	MAX-ACCESS read-only
	STATUS 	current
	DESCRIPTION
	"This value indicates the number of bytes which have
	 been logged by this component.  This count includes 
	 the header specified in extremeWirelessTraceModuleHeader."
	::= { extremeWirelessTraceModuleEntry 7 }

-- ************************************************************** --
--                    Log Diagnostic Counters                      --
--                                                                 --
-- The logging subsystem uses the following division : the event   --
-- logging system recieves all messages.  These message are then   --
-- passed to the syslog facility for remote logging, and then msgs --
-- may be stored in the NVRAM in the case where the log level is   --
-- greater than the log threshold.                                 --
-- ************************************************************** --
extremeWirelessLogDiagTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessLogDiagEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains counters per-log level for the event log,
	 syslog and NVRAM subsystems for logging."
	::= { extremeAP 102 }

extremeWirelessLogDiagEntry OBJECT-TYPE
	SYNTAX	ExtremeWirelessLogDiagEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"There is one entry in this table per physical port."
	INDEX { ifIndex }
	::= { extremeWirelessLogDiagTable 1 }

ExtremeWirelessLogDiagEntry ::= SEQUENCE {
	extremeWirelessLogDiagEventLogTotalCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalEmergCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalAlertCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalCritCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalErrorCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalWarnCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalNoticeCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalInfoCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalDebugCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalSuppressedCount Unsigned32,
	extremeWirelessLogDiagEventLogTotalByteCount Unsigned32,
	extremeWirelessLogDiagSyslogTotalEventCount Unsigned32,
	extremeWirelessLogDiagSyslogTotalSuppressedCount Unsigned32,
	extremeWirelessLogDiagSyslogTotalByteCount Unsigned32,
	extremeWirelessLogDiagNVRAMTotalEventCount Unsigned32,
	extremeWirelessLogDiagNVRAMTotalSuppressedCount Unsigned32,
	extremeWirelessLogDiagNVRAMTotalDroppedCount Unsigned32,
	extremeWirelessLogDiagNVRAMTotalByteCount Unsigned32,
	extremeWirelessLogDiagClearStats TruthValue }

extremeWirelessLogDiagEventLogTotalCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged since the
	 last time the statistics were cleared."
	::= { extremeWirelessLogDiagEntry 1 }

extremeWirelessLogDiagEventLogTotalEmergCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the EMERG
	 level."
	::= { extremeWirelessLogDiagEntry 2 }

extremeWirelessLogDiagEventLogTotalAlertCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the ALERT
	 level."
	::= { extremeWirelessLogDiagEntry 3 }

extremeWirelessLogDiagEventLogTotalCritCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the CRIT
	 level."
	::= { extremeWirelessLogDiagEntry 4 }

extremeWirelessLogDiagEventLogTotalErrorCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the ERROR
	 level."
	::= { extremeWirelessLogDiagEntry 5 }

extremeWirelessLogDiagEventLogTotalWarnCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the WARN
	 level."
	::= { extremeWirelessLogDiagEntry 6 }

extremeWirelessLogDiagEventLogTotalNoticeCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the NOTICE
	 level."
	::= { extremeWirelessLogDiagEntry 7 }

extremeWirelessLogDiagEventLogTotalInfoCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the INFO
	 level."
	::= { extremeWirelessLogDiagEntry 8 }

extremeWirelessLogDiagEventLogTotalDebugCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages logged at the DEBUG
	 level."
	::= { extremeWirelessLogDiagEntry 9 }

extremeWirelessLogDiagEventLogTotalSuppressedCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages which were suppressed
	 due to log level."
	::= { extremeWirelessLogDiagEntry 10 }

extremeWirelessLogDiagEventLogTotalByteCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of bytes logged by the logging
	 subsystem."
	::= { extremeWirelessLogDiagEntry 11 }

extremeWirelessLogDiagSyslogTotalEventCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of messages which have been sent
	 out to remote syslog servers."
	::= { extremeWirelessLogDiagEntry 12 }

extremeWirelessLogDiagSyslogTotalSuppressedCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of syslog messages which have been 
	 suppressed due to log level."
	::= { extremeWirelessLogDiagEntry 13 }

extremeWirelessLogDiagSyslogTotalByteCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"This gives the total number of bytes of syslog messages which have
	 been logged."
	::= { extremeWirelessLogDiagEntry 14 }

extremeWirelessLogDiagNVRAMTotalEventCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Total number of messages which have been logged to NVRAM."
	::= { extremeWirelessLogDiagEntry 15 }

extremeWirelessLogDiagNVRAMTotalSuppressedCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Total number of messages which have been suppressed by NVRAM
	 due to NVRAM threashold."
	::= { extremeWirelessLogDiagEntry 16 }

extremeWirelessLogDiagNVRAMTotalDroppedCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Total number of messages which have been dropped by the 
	 NVRAM."
	::= { extremeWirelessLogDiagEntry 17 }

extremeWirelessLogDiagNVRAMTotalByteCount OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
	"Total number of bytes which have been written to the NVRAM."
	::= { extremeWirelessLogDiagEntry 18 }

extremeWirelessLogDiagClearStats OBJECT-TYPE
	SYNTAX TruthValue
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION
	"Setting this value to TRUE causes the statistics to be reset to
	 0.  Reading this value has no meaning."
	::= { extremeWirelessLogDiagEntry 19 }

-- ************************************************************** --
--                           TRAPS                                 --
--                                                                 --
-- NOTE: We define client Authentication and association traps but --
--       performance may dictate that this not be done.  In that   --
--       case we have already provided the Authentication and      --
--       association totals which can be watched from RMON.        --
--                                                                 --
-- NOTE: The MAP State Change trap is not sent when the MAP is     --
--       first discovered.  The first trap is sent by EDP.         --
-- ************************************************************** --
extremeWirelessPortStateChange NOTIFICATION-TYPE
	OBJECTS { ifIndex,
		  extremeWirelessPortState }
	STATUS current
	DESCRIPTION
		"This trap is generated when a wireless port moves 
		 into enabled, disabled or online."
	::= { extremeAPTrapsPrefix 1 }

extremeWirelessPortBootFailure NOTIFICATION-TYPE
       OBJECTS { extremeWirelessPortIfIndex }
       STATUS current
       DESCRIPTION
               "This trap is sent by the platform if a wireless 
		port fails to boot too many times."
       ::= { extremeAPTrapsPrefix 2 }

extremeWirelessClientStationAgedOut NOTIFICATION-TYPE
       OBJECTS { ifIndex, extremeWirelessClientID }
       STATUS current
       DESCRIPTION
               "This trap is generated when a client is aged out 
		of the table."
       ::= { extremeAPTrapsPrefix 3 }

extremeWirelessNetloginClientAssociated NOTIFICATION-TYPE
       OBJECTS { ifIndex, extremeWirelessClientID }
       STATUS current
       DESCRIPTION
               "This trap is generated when a client has associated 
		to an interface that is web based network login 
		enabled."
       ::= { extremeAPTrapsPrefix 4 }

extremeWirelessAPAdded NOTIFICATION-TYPE
       OBJECTS { ifIndex, 
		 extremeWirelessScanResultsStationId,
		 extremeWirelessScanResultsFirstSeen,
		 extremeWirelessScanResultsLastChange,
		 extremeWirelessScanResultsPacketRate,
		 extremeWirelessScanResultsChannel,
		 extremeWirelessScanResultsAvgRSS,
		 extremeWirelessScanResultsSSID,
		 extremeWirelessScanResultsRSNEnabled,
		 extremeWirelessScanResultsPrivacy,
		 extremeWirelessScanResultsNetworkType,
		 extremeWirelessScanWPAIEMcastCipher,
		 extremeWirelessScanWPAUcastCipherCount,
	 	 extremeWirelessScanWPAUcastCipher,
		 extremeWirelessScanWPAKeyMgmtCount,
		 extremeWirelessScanWPAKeyMgmtSuite,
         extremeWirelessScanResultsRateSet,      
         extremeWirelessScanResultsExtRateSet }
       STATUS current
       DESCRIPTION
               "This is generated when a new AP is added to the
	 	scan results table.  It will ony be generated if
	 	the value of extremeWirelessScanSendAPAddedTrap
		is true."
       ::= { extremeAPTrapsPrefix 5 }

extremeWirelessAPRemoved NOTIFICATION-TYPE
       OBJECTS { ifIndex, extremeWirelessScanResultsStationId }
       STATUS current
       DESCRIPTION
               "This is generated when an AP is removed from the
	 	scan results table.  It will ony be generated if
	 	the value of extremeWirelessScanSendAPRemovedTrap
		is true."
       ::= { extremeAPTrapsPrefix 6 }

extremeWirelessAPUpdated NOTIFICATION-TYPE
       OBJECTS { ifIndex, 
		 extremeWirelessScanResultsStationId,
		 extremeWirelessScanResultsFirstSeen,
		 extremeWirelessScanResultsLastChange,
		 extremeWirelessScanResultsPacketRate,
		 extremeWirelessScanResultsChannel,
		 extremeWirelessScanResultsAvgRSS,
		 extremeWirelessScanResultsSSID,
		 extremeWirelessScanResultsRSNEnabled,
		 extremeWirelessScanResultsPrivacy,
		 extremeWirelessScanResultsNetworkType,
		 extremeWirelessScanWPAIEMcastCipher,
		 extremeWirelessScanWPAUcastCipherCount,
	 	 extremeWirelessScanWPAUcastCipher,
		 extremeWirelessScanWPAKeyMgmtCount,
		 extremeWirelessScanWPAKeyMgmtSuite,
         extremeWirelessScanResultsRateSet,
         extremeWirelessScanResultsExtRateSet }
       STATUS current
       DESCRIPTION
               "This is generated when the IEs recorded for an AP
		in the scan results table change.  It will only be
		generated if the value of SendAPUpdatedTrap is true."
       ::= { extremeAPTrapsPrefix 7 }

extremeWirelessProbeInfoAdded NOTIFICATION-TYPE
       OBJECTS { ifIndex, extremeWirelessProbeInfoSource }
       STATUS current
       DESCRIPTION
               "This is generated when a new station is added to the
		probe info table.  It will only be generated if the 
		value of extremeWirelessProbeInfoSendAddedTrap is true."
       ::= { extremeAPTrapsPrefix 8 }

extremeWirelessProbeInfoRemoved NOTIFICATION-TYPE
       OBJECTS { ifIndex, extremeWirelessProbeInfoSource }
       STATUS current
       DESCRIPTION
               "This is generated when a station is removed from the
		probe info table.  It will only be generated if the 
		value of extremeWirelessProbeInfoSendRemovedTrap is 
		true."
       ::= { extremeAPTrapsPrefix 9 }

extremeWirelessOffChannelScanStarted NOTIFICATION-TYPE
	OBJECTS { ifIndex, extremeWirelessOffChannelScanList }
        STATUS current
        DESCRIPTION
                "This is generated when an off-channel scan starts
                 running."
        ::= { extremeAPTrapsPrefix 10 }

extremeWirelessOffChannelScanFinished NOTIFICATION-TYPE
        OBJECTS { ifIndex, extremeWirelessOffChannelScanList }
        STATUS current
        DESCRIPTION
                "This is generated when an off-channel scan finishes
                 running."
        ::= { extremeAPTrapsPrefix 11 }

extremeWirelessCounterMeasureStarted NOTIFICATION-TYPE
        OBJECTS { ifIndex, extremeWirelessCounterMeasureSource }
        STATUS current
        DESCRIPTION
        "This trap is generated when counter measures are started
        on a wireless interface."
        ::= { extremeAPTrapsPrefix 12 }

extremeWirelessCounterMeasureStopped NOTIFICATION-TYPE
        OBJECTS { ifIndex }
        STATUS current
        DESCRIPTION
        "This trap is generated when counter measures are stopped
        on a wireless interface."
        ::= { extremeAPTrapsPrefix 13 }

--DFS related
extremeWirelessInterfaceChannelRescan NOTIFICATION-TYPE
        OBJECTS { ifIndex,
                  extremeWirelessPhysInterfacePHYChannel,
                  extremeWirelessPhysInterfaceChannelAutoSelectStatus
            }
        STATUS current
        DESCRIPTION
                "This trap is generated when a wireless interface tries to
autoselect a channel. It indicates whether a particular channel scan was
successful, or if it failed then the reason for the failure."
        ::= { extremeAPTrapsPrefix 14 }

        --
        -- TODO: Should we move this somewhere else?
        --
extremeWirelessCounterMeasureSource OBJECT-TYPE
        SYNTAX INTEGER { clientReport(1),
            micFailure(2) }
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
            "This value is used in the counter measure traps to indicate the
            reason that counter measures have been invoked."
            ::= { extremeAP 44 }

extremeWirelessClientWPAStatsTable OBJECT-TYPE
	SYNTAX SEQUENCE OF ExtremeWirelessClientWPAStatsEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"This table contains information of the WPA state transitions
	on a per-client basis.  Rows are indexed first by virtual
	interface, then by client MAC."
	 ::= { extremeAP 47 }

extremeWirelessClientWPAStatsEntry OBJECT-TYPE
	SYNTAX ExtremeWirelessClientWPAStatsEntry
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
	"Note that ifIndex here is virtual ifIndex (SSID)."
	INDEX { ifIndex, extremeWirelessClientDiagMac }
	 ::= { extremeWirelessClientWPAStatsTable 1 }

ExtremeWirelessClientWPAStatsEntry ::= SEQUENCE {
	extremeWirelessClientWPAStatsStarts	Counter32,
	extremeWirelessClientWPAStatsPairwiseKeySuccesses	Counter32,
	extremeWirelessClientWPAStatsPairwiseKeyFailures	Counter32,
	extremeWirelessClientWPAStatsGroupKeySuccesses		Counter32,
	extremeWirelessClientWPAStatsGroupKeyFailures		Counter32,
	extremeWirelessClientWPAStatsPairwiseKey1Sends		Counter32,
	extremeWirelessClientWPAStatsPairwiseKey3Sends		Counter32,
	extremeWirelessClientWPAStatsGroupKeySends			Counter32,
	extremeWirelessClientWPAStatsEAPOLKeyReceivedInPairwise1Key	Counter32,
	extremeWirelessClientWPAStatsEAPOLKeyReceivedInPairwise3Key	Counter32,
	extremeWirelessClientWPAStatsEAPOLKeyReceivedInGroupKey		Counter32,
	extremeWirelessClientWPAStatsDoubleEAPOLKeyReceived		Counter32,
	extremeWirelessClientWPAStatsEAPOLKeyIgnores			Counter32,
	extremeWirelessClientWPAStatsEAPOLKeyErrors			Counter32,
	extremeWirelessClientWPAStatsEAPOLKeyAborts			Counter32,
	extremeWirelessClientWPAStatsEAPOLKeyVerificationSuccesses	Counter32
}
extremeWirelessClientWPAStatsStarts OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of starts of the WPA key exchange process"
	 ::= { extremeWirelessClientWPAStatsEntry 1 }

extremeWirelessClientWPAStatsPairwiseKeySuccesses OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of successes in the pairwise key handshake"
	 ::= { extremeWirelessClientWPAStatsEntry 2 }

extremeWirelessClientWPAStatsPairwiseKeyFailures OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of failures the pairwise key handshake"
	 ::= { extremeWirelessClientWPAStatsEntry 3 }

extremeWirelessClientWPAStatsGroupKeySuccesses OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of successes of the group key exchange"
	 ::= { extremeWirelessClientWPAStatsEntry 4 }

extremeWirelessClientWPAStatsGroupKeyFailures OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of failures of the group key handshake"
	 ::= { extremeWirelessClientWPAStatsEntry 5 }

extremeWirelessClientWPAStatsPairwiseKey1Sends OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times the first packet of the pairwise key exchange was sent."
	 ::= { extremeWirelessClientWPAStatsEntry 6 }

extremeWirelessClientWPAStatsPairwiseKey3Sends OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times the third packet of the pairwise key exchange was sent."
	 ::= { extremeWirelessClientWPAStatsEntry 7 }

extremeWirelessClientWPAStatsGroupKeySends OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times the group key packet was sent."
	 ::= { extremeWirelessClientWPAStatsEntry 8 }

extremeWirelessClientWPAStatsEAPOLKeyReceivedInPairwise1Key OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet was recieved after
	sending the 1st pairwise key exchange packet"
	 ::= { extremeWirelessClientWPAStatsEntry 9 }

extremeWirelessClientWPAStatsEAPOLKeyReceivedInPairwise3Key OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet was recieved after
	sending the 3rd pairwise key exchange packet"
	 ::= { extremeWirelessClientWPAStatsEntry 10 }

extremeWirelessClientWPAStatsEAPOLKeyReceivedInGroupKey OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet was recieved after
	sending the group key packet"
	 ::= { extremeWirelessClientWPAStatsEntry 11 }

extremeWirelessClientWPAStatsDoubleEAPOLKeyReceived OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet was recieved when already
	processing a previous key packet."
	 ::= { extremeWirelessClientWPAStatsEntry 12 }

extremeWirelessClientWPAStatsEAPOLKeyIgnores OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet was ignored."
	 ::= { extremeWirelessClientWPAStatsEntry 13 }

extremeWirelessClientWPAStatsEAPOLKeyErrors OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet was errored."
	 ::= { extremeWirelessClientWPAStatsEntry 14 }

extremeWirelessClientWPAStatsEAPOLKeyAborts OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet caused the WPA state machine to abort."
	 ::= { extremeWirelessClientWPAStatsEntry 15 }

extremeWirelessClientWPAStatsEAPOLKeyVerificationSuccesses OBJECT-TYPE
	SYNTAX Counter32
	MAX-ACCESS read-only
	STATUS	   current
	DESCRIPTION
	"Number of times an EAPOL Key packet was verified correctly."
	 ::= { extremeWirelessClientWPAStatsEntry 16 }

END
