IMCO-BIG-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE,
    Gauge32, enterprises
        FROM SNMPv2-SMI
    DisplayString, TimeStamp
        FROM SNMPv2-TC
    MODULE-COMPLIANCE --, OBJECT-GROUP
        FROM SNMPv2-CONF;



netPartner	OBJECT IDENTIFIER ::= { enterprises 2185 }

npModules	OBJECT IDENTIFIER ::= { netPartner 1 }
npProducts	OBJECT IDENTIFIER ::= { netPartner 2 }

imcoMIB MODULE-IDENTITY
    LAST-UPDATED "200603201030Z"
    ORGANIZATION "NetPartner"
    CONTACT-INFO
           "        Jaroslav Spacek
             Postal: Psohlavcu 322/4
		     Praha 4
                     Czech Republic

                Tel: +420 2 61309111
                Fax: +420 2 61309111

             E-mail: <EMAIL>"
    DESCRIPTION
            "The MIB module DC-power IMCO."
    REVISION     "200603201030Z"
    DESCRIPTION
            "Initial version."
    ::= { npModules 3 }


PositiveInteger ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS       current
    DESCRIPTION
            "This data type is a non-zero and non-negative value."
    SYNTAX       INTEGER (1..2147483647)

NonNegativeInteger ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS       current
    DESCRIPTION
            "This data type is a non-negative value."
    SYNTAX       INTEGER (0..2147483647)

--PositiveInteger ::=
--	INTEGER

--NonNegativeInteger ::=
--	INTEGER

imcoObjects3	OBJECT IDENTIFIER ::= { imcoMIB 11 }


--
-- The Device Identification group.
--
imco3Ident	OBJECT IDENTIFIER ::= { imcoObjects3 1 }

imco3IdentManufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imco3Ident 1 }

imco3IdentModel OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..63))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imco3Ident 2 }

imco3IdentSwVersion OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..63))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imco3Ident 3 }

imco3IdentAgentVersion OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..63))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imco3Ident 4 }

imco3IdentName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE(0..63))
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imco3Ident 5 }

imco3IdentAttachedDevices OBJECT-TYPE
    SYNTAX     DisplayString (SIZE(0..63))
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imco3Ident 6 }

imco3IdentPMnumber OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imco3Ident 10 }


imco3PanM	OBJECT IDENTIFIER ::= { imcoObjects3 2 }

--
-- Hodnoty PM 1, !!! toto este 3x
--
imPanM1		OBJECT IDENTIFIER ::= { imco3PanM 1 }
imPanM2		OBJECT IDENTIFIER ::= { imco3PanM 2 }
imPanM3		OBJECT IDENTIFIER ::= { imco3PanM 3 }
imPanM4		OBJECT IDENTIFIER ::= { imco3PanM 4 }

--
-- PM 1, SystemID group
--
imPM1SystemID	OBJECT IDENTIFIER ::= { imPanM1 1 }

imPM1SystemIDManufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID 1 }

imPM1SystemIDType OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID 2 }

imPM1SystemIDserNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID 3 }

imPM1SystemIDnextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID 4 }

imPM1SystemIDaddress OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID  5 }

imPM1SystemIDhwVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID  6 }

imPM1SystemIDswVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID  7 }

imPM1SystemIDPMserialNumber OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID 8 }

imPM1SystemIDbuttonName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemID 9 }

--
-- PM 1, SystemGENERAL group
--
imPM1SystemGEN	OBJECT IDENTIFIER ::= { imPanM1 2 }

imPM1SystemGENSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 1 }

imPM1SystemGENDoor1 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 2 }

imPM1SystemGENDoor2 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 3 }

imPM1SystemGENFan OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 4 }

imPM1SystemGENuser1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 5 }

imPM1SystemGENuser2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 6 }

imPM1SystemGENuser3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 7 }

imPM1SystemGENuser4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 8 }

imPM1SystemGENtemperature OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1SystemGEN 9 }
 
--
-- PM 1, Power 1 group
--
imPM1Power1	OBJECT IDENTIFIER ::= { imPanM1 3 }

imPM1Power1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 1 }

imPM1Power1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 2 }

imPM1Power1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 3 }

imPM1Power1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 4 }

imPm1Power1InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 5 }

imPm1Power1InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 6 }

imPm1Power1InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 7 }

imPm1Power1InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 8 }

imPm1Power1InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 9 }

imPm1Power1InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 10 }

imPm1Power1InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 11 }

imPm1Power1InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 12 }

imPm1Power1InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 13 }

imPm1Power1InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 14 }

imPm1Power1InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 15 }

imPm1Power1InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 16 }

imPm1Power1InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 17 }

imPm1Power1InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 18 }

imPm1Power1InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 19 }

imPm1Power1InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 20 }

imPm1Power1InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 21 }

imPm1Power1InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 22 }

imPm1Power1OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 23 }

imPm1Power1OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 24 }

imPm1Power1OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 25 }

imPm1Power1OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 26 }

imPm1Power1OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 27 }

imPm1Power1OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 28 }

imPm1Power1OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 29 }

imPm1Power1OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 30 }

imPm1Power1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 31 }
 
imPM1Power1Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 32 }

imPM1Power1Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 33 }

imPM1Power1Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 34 }

imPM1Power1Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 35 }

imPM1Power1Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 36 }

imPM1Power1Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 37 }

imPM1Power1Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 38 }

imPM1Power1Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 39 }

imPM1Power1InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 40 }

imPM1Power1InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 41 }

imPM1Power1InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 42 }

imPM1Power1InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 43 }

imPM1Power1InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 44 }

imPM1Power1InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 45 }

imPM1Power1InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 46 }

imPM1Power1InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 47 }

imPM1Power1InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 48 }

imPM1Power1OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 49 }

imPM1Power1OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 50 }

imPM1Power1OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 51 }

imPM1Power1OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 52 }

imPM1Power1OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 53 }

imPM1Power1OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 54 }

imPM1Power1Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 55 }

imPM1Power1BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 56 }

imPm1Power1OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 57 }

imPm1Power1OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 58 }

imPm1Power1OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 59 }

imPm1Power1OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 60 }

imPm1Power1OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 61 }

imPm1Power1OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 62 }

imPm1Power1OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 63 }

imPm1Power1OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 64 }

imPm1Power1OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 65 }

imPm1Power1OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 66 }

imPm1Power1OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 67 }

imPm1Power1OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power1 68 }

--
-- PM 1, Power 2 group
--
imPM1Power2	OBJECT IDENTIFIER ::= { imPanM1 4 }

imPM1Power2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 1 }

imPM1Power2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 2 }

imPM1Power2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 3 }

imPM1Power2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 4 }

imPm1Power2InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 5 }

imPm1Power2InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 6 }

imPm1Power2InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 7 }

imPm1Power2InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 8 }

imPm1Power2InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 9 }

imPm1Power2InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 10 }

imPm1Power2InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 11 }

imPm1Power2InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 12 }

imPm1Power2InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 13 }

imPm1Power2InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 14 }

imPm1Power2InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 15 }

imPm1Power2InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 16 }

imPm1Power2InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 17 }

imPm1Power2InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 18 }

imPm1Power2InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 19 }

imPm1Power2InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 20 }

imPm1Power2InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 21 }

imPm1Power2InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 22 }

imPm1Power2OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 23 }

imPm1Power2OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 24 }

imPm1Power2OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 25 }

imPm1Power2OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 26 }

imPm1Power2OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 27 }

imPm1Power2OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 28 }

imPm1Power2OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 29 }

imPm1Power2OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 30 }

imPm1Power2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 31 }
 
imPM1Power2Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 32 }

imPM1Power2Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 33 }

imPM1Power2Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 34 }

imPM1Power2Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 35 }

imPM1Power2Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 36 }

imPM1Power2Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 37 }

imPM1Power2Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 38 }

imPM1Power2Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 39 }

imPM1Power2InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 40 }

imPM1Power2InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 41 }

imPM1Power2InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 42 }

imPM1Power2InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 43 }

imPM1Power2InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 44 }

imPM1Power2InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 45 }

imPM1Power2InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 46 }

imPM1Power2InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 47 }

imPM1Power2InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 48 }

imPM1Power2OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 49 }

imPM1Power2OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 50 }

imPM1Power2OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 51 }

imPM1Power2OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 52 }

imPM1Power2OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 53 }

imPM1Power2OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 54 }

imPM1Power2Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 55 }

imPM1Power2BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 56 }

imPM1Power2OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 57 }

imPM1Power2OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 58 }

imPM1Power2OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 59 }

imPM1Power2OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 60 }

imPM1Power2OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 61 }

imPM1Power2OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 62 }

imPM1Power2OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 63 }

imPM1Power2OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 64 }

imPM1Power2OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 65 }

imPM1Power2OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 66 }

imPM1Power2OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 67 }

imPM1Power2OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power2 68 }

--
-- PM 1, Power 3 group
--
imPM1Power3	OBJECT IDENTIFIER ::= { imPanM1 5 }

imPM1Power3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 1 }

imPM1Power3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 2 }

imPM1Power3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 3 }

imPM1Power3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 4 }

imPm1Power3InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 5 }

imPm1Power3InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 6 }

imPm1Power3InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 7 }

imPm1Power3InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 8 }

imPm1Power3InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 9 }

imPm1Power3InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 10 }

imPm1Power3InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 11 }

imPm1Power3InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 12 }

imPm1Power3InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 13 }

imPm1Power3InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 14 }

imPm1Power3InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 15 }

imPm1Power3InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 16 }

imPm1Power3InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 17 }

imPm1Power3InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 18 }

imPm1Power3InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 19 }

imPm1Power3InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 20 }

imPm1Power3InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 21 }

imPm1Power3InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 22 }

imPm1Power3OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 23 }

imPm1Power3OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 24 }

imPm1Power3OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 25 }

imPm1Power3OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 26 }

imPm1Power3OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 27 }

imPm1Power3OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 28 }

imPm1Power3OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 29 }

imPm1Power3OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 30 }

imPm1Power3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 31 }
 
imPM1Power3Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 32 }

imPM1Power3Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 33 }

imPM1Power3Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 34 }

imPM1Power3Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 35 }

imPM1Power3Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 36 }

imPM1Power3Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 37 }

imPM1Power3Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 38 }

imPM1Power3Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 39 }

imPM1Power3InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 40 }

imPM1Power3InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 41 }

imPM1Power3InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 42 }

imPM1Power3InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 43 }

imPM1Power3InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 44 }

imPM1Power3InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 45 }

imPM1Power3InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 46 }

imPM1Power3InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 47 }

imPM1Power3InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 48 }

imPM1Power3OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 49 }

imPM1Power3OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 50 }

imPM1Power3OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 51 }

imPM1Power3OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 52 }

imPM1Power3OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 53 }

imPM1Power3OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 54 }

imPM1Power3Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 55 }

imPM1Power3BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 56 }

imPM1Power3OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 57 }

imPM1Power3OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 58 }

imPM1Power3OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 59 }

imPM1Power3OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 60 }

imPM1Power3OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 61 }

imPM1Power3OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 62 }

imPM1Power3OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 63 }

imPM1Power3OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 64 }

imPM1Power3OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 65 }

imPM1Power3OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 66 }

imPM1Power3OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 67 }

imPM1Power3OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Power3 68 }

--
-- PM 1, Battery group
--
imPM1Battery	OBJECT IDENTIFIER ::= { imPanM1 6 }

imPm1BatteryNominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Battery 1 }

imPm1BatteryVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Battery 2 }

imPm1BatteryCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Battery 3 }

imPm1BatteryChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Battery 4 }

imPm1BatteryAutonomyTime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Battery 5 }

imPm1BatteryTimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Battery 6 }

--
-- PM 1, Battery LEG1 group
--
imPM1BatLeg1	OBJECT IDENTIFIER ::= { imPanM1 7 }

imPM1BatLeg1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 1 }

imPM1BatLeg1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 2 }

imPM1BatLeg1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 3 }

imPM1BatLeg1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 4 }

imPM1BatLeg1InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 5 }

imPm1BatLeg1NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 6 }

imPm1BatLeg1NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 7 }

imPm1BatLeg1Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 8 }

imPm1BatLeg1Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 9 }

imPm1BatLeg1Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 10 }

imPm1BatLeg1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 11 }

imPm1BatLeg1ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 12 }

imPm1BatLeg1RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 13 }


imPm1BatLeg1Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 14 }

imPm1BatLeg1TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 15 }

imPM1BatLeg1Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 16 }

imPM1BatLeg1Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 17 }

imPM1BatLeg1LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg1 18 }

--
-- PM 1, Battery LEG2 group
--
imPM1BatLeg2	OBJECT IDENTIFIER ::= { imPanM1 8 }

imPM1BatLeg2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 1 }

imPM1BatLeg2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 2 }

imPM1BatLeg2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 3 }

imPM1BatLeg2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 4 }

imPM1BatLeg2InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 5 }

imPm1BatLeg2NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 6 }

imPm1BatLeg2NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 7 }

imPm1BatLeg2Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 8 }

imPm1BatLeg2Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 9 }

imPm1BatLeg2Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 10 }

imPm1BatLeg2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 11 }

imPm1BatLeg2ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 12 }

imPm1BatLeg2RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 13 }


imPm1BatLeg2Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 14 }

imPm1BatLeg2TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 15 }

imPM1BatLeg2Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 16 }

imPM1BatLeg2Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 17 }

imPM1BatLeg2LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg2 18 }

--
-- PM 1, Battery LEG3 group
--
imPM1BatLeg3	OBJECT IDENTIFIER ::= { imPanM1 9 }

imPM1BatLeg3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 1 }

imPM1BatLeg3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 2 }

imPM1BatLeg3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 3 }

imPM1BatLeg3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 4 }

imPM1BatLeg3InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 5 }

imPm1BatLeg3NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 6 }

imPm1BatLeg3NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 7 }

imPm1BatLeg3Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 8 }

imPm1BatLeg3Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 9 }

imPm1BatLeg3Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 10 }

imPm1BatLeg3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 11 }

imPm1BatLeg3ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 12 }

imPm1BatLeg3RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 13 }


imPm1BatLeg3Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 14 }

imPm1BatLeg3TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 15 }

imPM1BatLeg3Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 16 }

imPM1BatLeg3Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 17 }

imPM1BatLeg3LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1BatLeg3 18 }

--
-- PM 1, Ditribution group
--
imPM1Distrib	OBJECT IDENTIFIER ::= { imPanM1 10 }

imPm1Distrib OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Distrib 1 }

imPM1DistTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM1DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Distrib 2 }

imPM1DistEntry OBJECT-TYPE
    SYNTAX     ImPM1DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM1DistId }
    ::= { imPM1DistTable 1 }

ImPM1DistEntry ::= SEQUENCE {
    imPM1DistId       PositiveInteger,
    imPM1DistFuse     INTEGER,
    imPM1DistBreaker  INTEGER
}

imPM1DistId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1DistEntry 1 }

imPM1DistFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1DistEntry 2 }

imPM1DistBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1DistEntry 3 }

--
-- PM 1, Control group
--
imPM1Control	OBJECT IDENTIFIER ::= { imPanM1 11 }

imPM1ContTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM1ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Control 1 }

imPM1ContEntry OBJECT-TYPE
    SYNTAX     ImPM1ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM1ContId }
    ::= { imPM1ContTable 1 }

ImPM1ContEntry ::= SEQUENCE {
    imPM1ContId       PositiveInteger,
    imPM1ContState    INTEGER,
    imPM1ContLabel    DisplayString,
    imPM1ContTimeOFF  INTEGER,
    imPM1ContAuto     INTEGER
}

imPM1ContId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1ContEntry 1 }

imPM1ContState OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1ContEntry 2 }

imPM1ContLabel OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1ContEntry 3 }

imPM1ContTimeOFF OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1ContEntry 4 }

imPM1ContAuto OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1ContEntry 5 }

imPM1ContTestCAPcommand OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Control 2 }

imPM1ContTestCAPvoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Control 3 }

imPM1ContTestCAPcurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Control 4 }

imPM1ContTestCAPtemperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Control 5 }

imPM1ContTestCAPduration OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Control 6 }

imPM1ContTestCAPstatus OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM1Control 7 }

--
-- PM 2, SystemID group
--

imPM2SystemID	OBJECT IDENTIFIER ::= { imPanM2 1 }

imPM2SystemIDManufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID 1 }

imPM2SystemIDType OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID 2 }

imPM2SystemIDserNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID 3 }

imPM2SystemIDnextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID 4 }

imPM2SystemIDaddress OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID  5 }

imPM2SystemIDhwVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID  6 }

imPM2SystemIDswVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID  7 }

imPM2SystemIDPMserialNumber OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID 8 }

imPM2SystemIDbuttonName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemID 9 }

--
-- PM 2, SystemGENERAL group
--
imPM2SystemGEN	OBJECT IDENTIFIER ::= { imPanM2 2 }

imPM2SystemGENSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 1 }

imPM2SystemGENDoor1 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 2 }

imPM2SystemGENDoor2 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 3 }

imPM2SystemGENFan OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 4 }

imPM2SystemGENuser1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 5 }

imPM2SystemGENuser2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 6 }

imPM2SystemGENuser3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 7 }

imPM2SystemGENuser4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 8 }

imPM2SystemGENtemperature OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2SystemGEN 9 }
 
--
-- PM 2, Power 1 group
--
imPM2Power1	OBJECT IDENTIFIER ::= { imPanM2 3 }

imPM2Power1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 1 }

imPM2Power1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 2 }

imPM2Power1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 3 }

imPM2Power1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 4 }

imPM2Power1InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 5 }

imPM2Power1InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 6 }

imPM2Power1InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 7 }

imPM2Power1InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 8 }

imPM2Power1InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 9 }

imPM2Power1InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 10 }

imPM2Power1InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 11 }

imPM2Power1InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 12 }

imPM2Power1InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 13 }

imPM2Power1InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 14 }

imPM2Power1InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 15 }

imPM2Power1InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 16 }

imPM2Power1InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 17 }

imPM2Power1InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 18 }

imPM2Power1InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 19 }

imPM2Power1InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 20 }

imPM2Power1InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 21 }

imPM2Power1InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 22 }

imPM2Power1OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 23 }

imPM2Power1OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 24 }

imPM2Power1OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 25 }

imPM2Power1OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 26 }

imPM2Power1OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 27 }

imPM2Power1OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 28 }

imPM2Power1OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 29 }

imPM2Power1OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 30 }

imPM2Power1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 31 }
 
imPM2Power1Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 32 }

imPM2Power1Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 33 }

imPM2Power1Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 34 }

imPM2Power1Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 35 }

imPM2Power1Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 36 }

imPM2Power1Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 37 }

imPM2Power1Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 38 }

imPM2Power1Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 39 }

imPM2Power1InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 40 }

imPM2Power1InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 41 }

imPM2Power1InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 42 }

imPM2Power1InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 43 }

imPM2Power1InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 44 }

imPM2Power1InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 45 }

imPM2Power1InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 46 }

imPM2Power1InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 47 }

imPM2Power1InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 48 }

imPM2Power1OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 49 }

imPM2Power1OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 50 }

imPM2Power1OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 51 }

imPM2Power1OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 52 }

imPM2Power1OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 53 }

imPM2Power1OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 54 }

imPM2Power1Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 55 }

imPM2Power1BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 56 }

imPM2Power1OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 57 }

imPM2Power1OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 58 }

imPM2Power1OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 59 }

imPM2Power1OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 60 }

imPM2Power1OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 61 }

imPM2Power1OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 62 }

imPM2Power1OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 63 }

imPM2Power1OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 64 }

imPM2Power1OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 65 }

imPM2Power1OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 66 }

imPM2Power1OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 67 }

imPM2Power1OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power1 68 }

--
-- PM 2, Power 2 group
--
imPM2Power2	OBJECT IDENTIFIER ::= { imPanM2 4 }

imPM2Power2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 1 }

imPM2Power2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 2 }

imPM2Power2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 3 }

imPM2Power2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 4 }

imPM2Power2InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 5 }

imPM2Power2InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 6 }

imPM2Power2InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 7 }

imPM2Power2InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 8 }

imPM2Power2InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 9 }

imPM2Power2InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 10 }

imPM2Power2InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 11 }

imPM2Power2InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 12 }

imPM2Power2InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 13 }

imPM2Power2InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 14 }

imPM2Power2InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 15 }

imPM2Power2InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 16 }

imPM2Power2InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 17 }

imPM2Power2InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 18 }

imPM2Power2InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 19 }

imPM2Power2InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 20 }

imPM2Power2InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 21 }

imPM2Power2InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 22 }

imPM2Power2OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 23 }

imPM2Power2OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 24 }

imPM2Power2OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 25 }

imPM2Power2OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 26 }

imPM2Power2OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 27 }

imPM2Power2OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 28 }

imPM2Power2OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 29 }

imPM2Power2OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 30 }

imPM2Power2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 31 }
 
imPM2Power2Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 32 }

imPM2Power2Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 33 }

imPM2Power2Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 34 }

imPM2Power2Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 35 }

imPM2Power2Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 36 }

imPM2Power2Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 37 }

imPM2Power2Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 38 }

imPM2Power2Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 39 }

imPM2Power2InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 40 }

imPM2Power2InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 41 }

imPM2Power2InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 42 }

imPM2Power2InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 43 }

imPM2Power2InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 44 }

imPM2Power2InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 45 }

imPM2Power2InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 46 }

imPM2Power2InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 47 }

imPM2Power2InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 48 }

imPM2Power2OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 49 }

imPM2Power2OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 50 }

imPM2Power2OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 51 }

imPM2Power2OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 52 }

imPM2Power2OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 53 }

imPM2Power2OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 54 }

imPM2Power2Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 55 }

imPM2Power2BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 56 }

imPM2Power2OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 57 }

imPM2Power2OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 58 }

imPM2Power2OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 59 }

imPM2Power2OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 60 }

imPM2Power2OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 61 }

imPM2Power2OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 62 }

imPM2Power2OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 63 }

imPM2Power2OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 64 }

imPM2Power2OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 65 }

imPM2Power2OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 66 }

imPM2Power2OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 67 }

imPM2Power2OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power2 68 }

--
-- PM 2, Power 3 group
--
imPM2Power3	OBJECT IDENTIFIER ::= { imPanM2 5 }

imPM2Power3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 1 }

imPM2Power3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 2 }

imPM2Power3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 3 }

imPM2Power3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 4 }

imPM2Power3InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 5 }

imPM2Power3InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 6 }

imPM2Power3InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 7 }

imPM2Power3InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 8 }

imPM2Power3InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 9 }

imPM2Power3InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 10 }

imPM2Power3InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 11 }

imPM2Power3InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 12 }

imPM2Power3InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 13 }

imPM2Power3InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 14 }

imPM2Power3InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 15 }

imPM2Power3InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 16 }

imPM2Power3InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 17 }

imPM2Power3InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 18 }

imPM2Power3InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 19 }

imPM2Power3InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 20 }

imPM2Power3InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 21 }

imPM2Power3InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 22 }

imPM2Power3OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 23 }

imPM2Power3OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 24 }

imPM2Power3OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 25 }

imPM2Power3OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 26 }

imPM2Power3OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 27 }

imPM2Power3OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 28 }

imPM2Power3OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 29 }

imPM2Power3OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 30 }

imPM2Power3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 31 }
 
imPM2Power3Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 32 }

imPM2Power3Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 33 }

imPM2Power3Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 34 }

imPM2Power3Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 35 }

imPM2Power3Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 36 }

imPM2Power3Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 37 }

imPM2Power3Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 38 }

imPM2Power3Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 39 }

imPM2Power3InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 40 }

imPM2Power3InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 41 }

imPM2Power3InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 42 }

imPM2Power3InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 43 }

imPM2Power3InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 44 }

imPM2Power3InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 45 }

imPM2Power3InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 46 }

imPM2Power3InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 47 }

imPM2Power3InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 48 }

imPM2Power3OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 49 }

imPM2Power3OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 50 }

imPM2Power3OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 51 }

imPM2Power3OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 52 }

imPM2Power3OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 53 }

imPM2Power3OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 54 }

imPM2Power3Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 55 }

imPM2Power3BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 56 }

imPM2Power3OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 57 }

imPM2Power3OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 58 }

imPM2Power3OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 59 }

imPM2Power3OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 60 }

imPM2Power3OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 61 }

imPM2Power3OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 62 }

imPM2Power3OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 63 }

imPM2Power3OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 64 }

imPM2Power3OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 65 }

imPM2Power3OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 66 }

imPM2Power3OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 67 }

imPM2Power3OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Power3 68 }

--
-- PM 2, Battery group
--
imPM2Battery	OBJECT IDENTIFIER ::= { imPanM2 6 }

imPM2BatteryNominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Battery 1 }

imPM2BatteryVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Battery 2 }

imPM2BatteryCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Battery 3 }

imPM2BatteryChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Battery 4 }

imPM2BatteryAutonomyTime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Battery 5 }

imPM2BatteryTimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Battery 6 }

--
-- PM 2, Battery LEG1 group
--
imPM2BatLeg1	OBJECT IDENTIFIER ::= { imPanM2 7 }

imPM2BatLeg1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 1 }

imPM2BatLeg1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 2 }

imPM2BatLeg1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 3 }

imPM2BatLeg1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 4 }

imPM2BatLeg1InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 5 }

imPM2BatLeg1NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 6 }

imPM2BatLeg1NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 7 }

imPM2BatLeg1Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 8 }

imPM2BatLeg1Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 9 }

imPM2BatLeg1Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 10 }

imPM2BatLeg1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 11 }

imPM2BatLeg1ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 12 }

imPM2BatLeg1RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 13 }


imPM2BatLeg1Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 14 }

imPM2BatLeg1TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 15 }

imPM2BatLeg1Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 16 }

imPM2BatLeg1Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 17 }

imPM2BatLeg1LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg1 18 }

--
-- PM 2, Battery LEG2 group
--
imPM2BatLeg2	OBJECT IDENTIFIER ::= { imPanM2 8 }

imPM2BatLeg2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 1 }

imPM2BatLeg2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 2 }

imPM2BatLeg2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 3 }

imPM2BatLeg2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 4 }

imPM2BatLeg2InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 5 }

imPM2BatLeg2NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 6 }

imPM2BatLeg2NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 7 }

imPM2BatLeg2Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 8 }

imPM2BatLeg2Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 9 }

imPM2BatLeg2Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 10 }

imPM2BatLeg2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 11 }

imPM2BatLeg2ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 12 }

imPM2BatLeg2RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 13 }


imPM2BatLeg2Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 14 }

imPM2BatLeg2TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 15 }

imPM2BatLeg2Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 16 }

imPM2BatLeg2Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 17 }

imPM2BatLeg2LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg2 18 }

--
-- PM 2, Battery LEG3 group
--
imPM2BatLeg3	OBJECT IDENTIFIER ::= { imPanM2 9 }

imPM2BatLeg3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 1 }

imPM2BatLeg3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 2 }

imPM2BatLeg3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 3 }

imPM2BatLeg3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 4 }

imPM2BatLeg3InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 5 }

imPM2BatLeg3NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 6 }

imPM2BatLeg3NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 7 }

imPM2BatLeg3Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 8 }

imPM2BatLeg3Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 9 }

imPM2BatLeg3Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 10 }

imPM2BatLeg3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 11 }

imPM2BatLeg3ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 12 }

imPM2BatLeg3RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 13 }


imPM2BatLeg3Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 14 }

imPM2BatLeg3TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 15 }

imPM2BatLeg3Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 16 }

imPM2BatLeg3Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 17 }

imPM2BatLeg3LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2BatLeg3 18 }

--
-- PM 2, Ditribution group
--
imPM2Distrib	OBJECT IDENTIFIER ::= { imPanM2 10 }

imPm2Distrib OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Distrib 1 }

imPM2DistTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM2DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Distrib 2 }

imPM2DistEntry OBJECT-TYPE
    SYNTAX     ImPM2DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM2DistId }
    ::= { imPM2DistTable 1 }

ImPM2DistEntry ::= SEQUENCE {
    imPM2DistId       PositiveInteger,
    imPM2DistFuse     INTEGER,
    imPM2DistBreaker  INTEGER
}

imPM2DistId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2DistEntry 1 }

imPM2DistFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2DistEntry 2 }

imPM2DistBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2DistEntry 3 }

--
-- PM 2, Control group
--
imPM2Control	OBJECT IDENTIFIER ::= { imPanM2 11 }

imPM2ContTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM2ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Control 1 }

imPM2ContEntry OBJECT-TYPE
    SYNTAX     ImPM2ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM2ContId }
    ::= { imPM2ContTable 1 }

ImPM2ContEntry ::= SEQUENCE {
    imPM2ContId       PositiveInteger,
    imPM2ContState    INTEGER,
    imPM2ContLabel    DisplayString,
    imPM2ContTimeOFF  INTEGER,
    imPM2ContAuto     INTEGER
}

imPM2ContId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2ContEntry 1 }

imPM2ContState OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2ContEntry 2 }

imPM2ContLabel OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2ContEntry 3 }

imPM2ContTimeOFF OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2ContEntry 4 }

imPM2ContAuto OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2ContEntry 5 }

imPM2ContTestCAPcommand OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Control 2 }

imPM2ContTestCAPvoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Control 3 }

imPM2ContTestCAPcurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Control 4 }

imPM2ContTestCAPtemperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Control 5 }

imPM2ContTestCAPduration OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Control 6 }

imPM2ContTestCAPstatus OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM2Control 7 }

--
-- PM 3, SystemID group
--

imPM3SystemID	OBJECT IDENTIFIER ::= { imPanM3 1 }

imPM3SystemIDManufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID 1 }

imPM3SystemIDType OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID 2 }

imPM3SystemIDserNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID 3 }

imPM3SystemIDnextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID 4 }

imPM3SystemIDaddress OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID  5 }

imPM3SystemIDhwVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID  6 }

imPM3SystemIDswVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID  7 }

imPM3SystemIDPMserialNumber OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID 8 }

imPM3SystemIDbuttonName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemID 9 }

--
-- PM 3, SystemGENERAL group
--
imPM3SystemGEN	OBJECT IDENTIFIER ::= { imPanM3 2 }

imPM3SystemGENSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 1 }

imPM3SystemGENDoor1 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 2 }

imPM3SystemGENDoor2 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 3 }

imPM3SystemGENFan OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 4 }

imPM3SystemGENuser1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 5 }

imPM3SystemGENuser2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 6 }

imPM3SystemGENuser3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 7 }

imPM3SystemGENuser4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 8 }

imPM3SystemGENtemperature OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3SystemGEN 9 }
 
--
-- PM 3, Power 1 group
--
imPM3Power1	OBJECT IDENTIFIER ::= { imPanM3 3 }

imPM3Power1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 1 }

imPM3Power1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 2 }

imPM3Power1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 3 }

imPM3Power1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 4 }

imPM3Power1InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 5 }

imPM3Power1InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 6 }

imPM3Power1InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 7 }

imPM3Power1InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 8 }

imPM3Power1InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 9 }

imPM3Power1InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 10 }

imPM3Power1InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 11 }

imPM3Power1InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 12 }

imPM3Power1InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 13 }

imPM3Power1InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 14 }

imPM3Power1InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 15 }

imPM3Power1InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 16 }

imPM3Power1InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 17 }

imPM3Power1InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 18 }

imPM3Power1InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 19 }

imPM3Power1InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 20 }

imPM3Power1InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 21 }

imPM3Power1InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 22 }

imPM3Power1OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 23 }

imPM3Power1OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 24 }

imPM3Power1OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 25 }

imPM3Power1OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 26 }

imPM3Power1OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 27 }

imPM3Power1OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 28 }

imPM3Power1OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 29 }

imPM3Power1OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 30 }

imPM3Power1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 31 }
 
imPM3Power1Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 32 }

imPM3Power1Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 33 }

imPM3Power1Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 34 }

imPM3Power1Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 35 }

imPM3Power1Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 36 }

imPM3Power1Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 37 }

imPM3Power1Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 38 }

imPM3Power1Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 39 }

imPM3Power1InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 40 }

imPM3Power1InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 41 }

imPM3Power1InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 42 }

imPM3Power1InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 43 }

imPM3Power1InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 44 }

imPM3Power1InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 45 }

imPM3Power1InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 46 }

imPM3Power1InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 47 }

imPM3Power1InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 48 }

imPM3Power1OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 49 }

imPM3Power1OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 50 }

imPM3Power1OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 51 }

imPM3Power1OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 52 }

imPM3Power1OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 53 }

imPM3Power1OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 54 }

imPM3Power1Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 55 }

imPM3Power1BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 56 }

imPM3Power1OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 57 }

imPM3Power1OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 58 }

imPM3Power1OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 59 }

imPM3Power1OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 60 }

imPM3Power1OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 61 }

imPM3Power1OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 62 }

imPM3Power1OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 63 }

imPM3Power1OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 64 }

imPM3Power1OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 65 }

imPM3Power1OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 66 }

imPM3Power1OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 67 }

imPM3Power1OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power1 68 }

--
-- PM 3, Power 2 group
--
imPM3Power2	OBJECT IDENTIFIER ::= { imPanM3 4 }

imPM3Power2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 1 }

imPM3Power2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 2 }

imPM3Power2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 3 }

imPM3Power2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 4 }

imPM3Power2InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 5 }

imPM3Power2InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 6 }

imPM3Power2InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 7 }

imPM3Power2InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 8 }

imPM3Power2InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 9 }

imPM3Power2InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 10 }

imPM3Power2InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 11 }

imPM3Power2InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 12 }

imPM3Power2InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 13 }

imPM3Power2InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 14 }

imPM3Power2InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 15 }

imPM3Power2InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 16 }

imPM3Power2InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 17 }

imPM3Power2InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 18 }

imPM3Power2InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 19 }

imPM3Power2InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 20 }

imPM3Power2InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 21 }

imPM3Power2InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 22 }

imPM3Power2OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 23 }

imPM3Power2OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 24 }

imPM3Power2OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 25 }

imPM3Power2OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 26 }

imPM3Power2OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 27 }

imPM3Power2OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 28 }

imPM3Power2OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 29 }

imPM3Power2OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 30 }

imPM3Power2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 31 }
 
imPM3Power2Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 32 }

imPM3Power2Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 33 }

imPM3Power2Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 34 }

imPM3Power2Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 35 }

imPM3Power2Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 36 }

imPM3Power2Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 37 }

imPM3Power2Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 38 }

imPM3Power2Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 39 }

imPM3Power2InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 40 }

imPM3Power2InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 41 }

imPM3Power2InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 42 }

imPM3Power2InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 43 }

imPM3Power2InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 44 }

imPM3Power2InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 45 }

imPM3Power2InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 46 }

imPM3Power2InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 47 }

imPM3Power2InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 48 }

imPM3Power2OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 49 }

imPM3Power2OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 50 }

imPM3Power2OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 51 }

imPM3Power2OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 52 }

imPM3Power2OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 53 }

imPM3Power2OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 54 }

imPM3Power2Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 55 }

imPM3Power2BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 56 }

imPM3Power2OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 57 }

imPM3Power2OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 58 }

imPM3Power2OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 59 }

imPM3Power2OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 60 }

imPM3Power2OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 61 }

imPM3Power2OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 62 }

imPM3Power2OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 63 }

imPM3Power2OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 64 }

imPM3Power2OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 65 }

imPM3Power2OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 66 }

imPM3Power2OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 67 }

imPM3Power2OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power2 68 }

--
-- PM 3, Power 3 group
--
imPM3Power3	OBJECT IDENTIFIER ::= { imPanM3 5 }

imPM3Power3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 1 }

imPM3Power3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 2 }

imPM3Power3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 3 }

imPM3Power3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 4 }

imPM3Power3InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 5 }

imPM3Power3InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 6 }

imPM3Power3InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 7 }

imPM3Power3InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 8 }

imPM3Power3InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 9 }

imPM3Power3InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 10 }

imPM3Power3InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 11 }

imPM3Power3InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 12 }

imPM3Power3InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 13 }

imPM3Power3InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 14 }

imPM3Power3InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 15 }

imPM3Power3InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 16 }

imPM3Power3InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 17 }

imPM3Power3InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 18 }

imPM3Power3InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 19 }

imPM3Power3InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 20 }

imPM3Power3InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 21 }

imPM3Power3InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 22 }

imPM3Power3OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 23 }

imPM3Power3OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 24 }

imPM3Power3OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 25 }

imPM3Power3OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 26 }

imPM3Power3OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 27 }

imPM3Power3OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 28 }

imPM3Power3OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 29 }

imPM3Power3OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 30 }

imPM3Power3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 31 }
 
imPM3Power3Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 32 }

imPM3Power3Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 33 }

imPM3Power3Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 34 }

imPM3Power3Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 35 }

imPM3Power3Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 36 }

imPM3Power3Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 37 }

imPM3Power3Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 38 }

imPM3Power3Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 39 }

imPM3Power3InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 40 }

imPM3Power3InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 41 }

imPM3Power3InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 42 }

imPM3Power3InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 43 }

imPM3Power3InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 44 }

imPM3Power3InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 45 }

imPM3Power3InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 46 }

imPM3Power3InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 47 }

imPM3Power3InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 48 }

imPM3Power3OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 49 }

imPM3Power3OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 50 }

imPM3Power3OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 51 }

imPM3Power3OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 52 }

imPM3Power3OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 53 }

imPM3Power3OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 54 }

imPM3Power3Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 55 }

imPM3Power3BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 56 }

imPM3Power3OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 57 }

imPM3Power3OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 58 }

imPM3Power3OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 59 }

imPM3Power3OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 60 }

imPM3Power3OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 61 }

imPM3Power3OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 62 }

imPM3Power3OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 63 }

imPM3Power3OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 64 }

imPM3Power3OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 65 }

imPM3Power3OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 66 }

imPM3Power3OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 67 }

imPM3Power3OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Power3 68 }

--
-- PM 3, Battery group
--
imPM3Battery	OBJECT IDENTIFIER ::= { imPanM3 6 }

imPM3BatteryNominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Battery 1 }

imPM3BatteryVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Battery 2 }

imPM3BatteryCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Battery 3 }

imPM3BatteryChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Battery 4 }

imPM3BatteryAutonomyTime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Battery 5 }

imPM3BatteryTimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Battery 6 }

--
-- PM 3, Battery LEG1 group
--
imPM3BatLeg1	OBJECT IDENTIFIER ::= { imPanM3 7 }

imPM3BatLeg1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 1 }

imPM3BatLeg1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 2 }

imPM3BatLeg1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 3 }

imPM3BatLeg1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 4 }

imPM3BatLeg1InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 5 }

imPM3BatLeg1NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 6 }

imPM3BatLeg1NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 7 }

imPM3BatLeg1Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 8 }

imPM3BatLeg1Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 9 }

imPM3BatLeg1Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 10 }

imPM3BatLeg1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 11 }

imPM3BatLeg1ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 12 }

imPM3BatLeg1RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 13 }


imPM3BatLeg1Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 14 }

imPM3BatLeg1TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 15 }

imPM3BatLeg1Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 16 }

imPM3BatLeg1Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 17 }

imPM3BatLeg1LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg1 18 }

--
-- PM 3, Battery LEG2 group
--
imPM3BatLeg2	OBJECT IDENTIFIER ::= { imPanM3 8 }

imPM3BatLeg2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 1 }

imPM3BatLeg2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 2 }

imPM3BatLeg2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 3 }

imPM3BatLeg2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 4 }

imPM3BatLeg2InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 5 }

imPM3BatLeg2NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 6 }

imPM3BatLeg2NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 7 }

imPM3BatLeg2Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 8 }

imPM3BatLeg2Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 9 }

imPM3BatLeg2Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 10 }

imPM3BatLeg2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 11 }

imPM3BatLeg2ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 12 }

imPM3BatLeg2RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 13 }


imPM3BatLeg2Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 14 }

imPM3BatLeg2TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 15 }

imPM3BatLeg2Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 16 }

imPM3BatLeg2Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 17 }

imPM3BatLeg2LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg2 18 }

--
-- PM 3, Battery LEG3 group
--
imPM3BatLeg3	OBJECT IDENTIFIER ::= { imPanM3 9 }

imPM3BatLeg3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 1 }

imPM3BatLeg3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 2 }

imPM3BatLeg3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 3 }

imPM3BatLeg3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 4 }

imPM3BatLeg3InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 5 }

imPM3BatLeg3NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 6 }

imPM3BatLeg3NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 7 }

imPM3BatLeg3Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 8 }

imPM3BatLeg3Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 9 }

imPM3BatLeg3Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 10 }

imPM3BatLeg3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 11 }

imPM3BatLeg3ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 12 }

imPM3BatLeg3RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 13 }


imPM3BatLeg3Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 14 }

imPM3BatLeg3TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 15 }

imPM3BatLeg3Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 16 }

imPM3BatLeg3Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 17 }

imPM3BatLeg3LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3BatLeg3 18 }

--
-- PM 3, Ditribution group
--
imPM3Distrib	OBJECT IDENTIFIER ::= { imPanM3 10 }

imPm3Distrib OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Distrib 1 }

imPM3DistTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM3DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Distrib 2 }

imPM3DistEntry OBJECT-TYPE
    SYNTAX     ImPM3DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM3DistId }
    ::= { imPM3DistTable 1 }

ImPM3DistEntry ::= SEQUENCE {
    imPM3DistId       PositiveInteger,
    imPM3DistFuse     INTEGER,
    imPM3DistBreaker  INTEGER
}

imPM3DistId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3DistEntry 1 }

imPM3DistFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3DistEntry 2 }

imPM3DistBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3DistEntry 3 }

--
-- PM 3, Control group
--
imPM3Control	OBJECT IDENTIFIER ::= { imPanM3 11 }

imPM3ContTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM3ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Control 1 }

imPM3ContEntry OBJECT-TYPE
    SYNTAX     ImPM3ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM3ContId }
    ::= { imPM3ContTable 1 }

ImPM3ContEntry ::= SEQUENCE {
    imPM3ContId       PositiveInteger,
    imPM3ContState    INTEGER,
    imPM3ContLabel    DisplayString,
    imPM3ContTimeOFF  INTEGER,
    imPM3ContAuto     INTEGER
}

imPM3ContId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3ContEntry 1 }

imPM3ContState OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3ContEntry 2 }

imPM3ContLabel OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3ContEntry 3 }

imPM3ContTimeOFF OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3ContEntry 4 }

imPM3ContAuto OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3ContEntry 5 }

imPM3ContTestCAPcommand OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Control 2 }

imPM3ContTestCAPvoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Control 3 }

imPM3ContTestCAPcurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Control 4 }

imPM3ContTestCAPtemperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Control 5 }

imPM3ContTestCAPduration OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Control 6 }

imPM3ContTestCAPstatus OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM3Control 7 }

--
-- PM 4, SystemID group
--

imPM4SystemID	OBJECT IDENTIFIER ::= { imPanM4 1 }

imPM4SystemIDManufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID 1 }

imPM4SystemIDType OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID 2 }

imPM4SystemIDserNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID 3 }

imPM4SystemIDnextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID 4 }

imPM4SystemIDaddress OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID  5 }

imPM4SystemIDhwVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID  6 }

imPM4SystemIDswVersion OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID  7 }

imPM4SystemIDPMserialNumber OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID 8 }

imPM4SystemIDbuttonName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemID 9 }

--
-- PM 4, SystemGENERAL group
--
imPM4SystemGEN	OBJECT IDENTIFIER ::= { imPanM4 2 }

imPM4SystemGENSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 1 }

imPM4SystemGENDoor1 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 2 }

imPM4SystemGENDoor2 OBJECT-TYPE
    SYNTAX     INTEGER {
        close(0),
        open(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 3 }

imPM4SystemGENFan OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 4 }

imPM4SystemGENuser1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 5 }

imPM4SystemGENuser2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 6 }

imPM4SystemGENuser3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 7 }

imPM4SystemGENuser4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 8 }

imPM4SystemGENtemperature OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4SystemGEN 9 }
 
--
-- PM 4, Power 1 group
--
imPM4Power1	OBJECT IDENTIFIER ::= { imPanM4 3 }

imPM4Power1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 1 }

imPM4Power1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 2 }

imPM4Power1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 3 }

imPM4Power1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 4 }

imPM4Power1InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 5 }

imPM4Power1InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 6 }

imPM4Power1InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 7 }

imPM4Power1InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 8 }

imPM4Power1InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 9 }

imPM4Power1InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 10 }

imPM4Power1InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 11 }

imPM4Power1InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 12 }

imPM4Power1InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 13 }

imPM4Power1InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 14 }

imPM4Power1InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 15 }

imPM4Power1InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 16 }

imPM4Power1InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 17 }

imPM4Power1InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 18 }

imPM4Power1InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 19 }

imPM4Power1InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 20 }

imPM4Power1InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 21 }

imPM4Power1InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 22 }

imPM4Power1OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 23 }

imPM4Power1OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 24 }

imPM4Power1OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 25 }

imPM4Power1OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 26 }

imPM4Power1OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 27 }

imPM4Power1OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 28 }

imPM4Power1OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 29 }

imPM4Power1OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 30 }

imPM4Power1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 31 }
 
imPM4Power1Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 32 }

imPM4Power1Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 33 }

imPM4Power1Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 34 }

imPM4Power1Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 35 }

imPM4Power1Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 36 }

imPM4Power1Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 37 }

imPM4Power1Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 38 }

imPM4Power1Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 39 }

imPM4Power1InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 40 }

imPM4Power1InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 41 }

imPM4Power1InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 42 }

imPM4Power1InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 43 }

imPM4Power1InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 44 }

imPM4Power1InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 45 }

imPM4Power1InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 46 }

imPM4Power1InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 47 }

imPM4Power1InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 48 }

imPM4Power1OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 49 }

imPM4Power1OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 50 }

imPM4Power1OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 51 }

imPM4Power1OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 52 }

imPM4Power1OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 53 }

imPM4Power1OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 54 }

imPM4Power1Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 55 }

imPM4Power1BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 56 }

imPM4Power1OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 57 }

imPM4Power1OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 58 }

imPM4Power1OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 59 }

imPM4Power1OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 60 }

imPM4Power1OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 61 }

imPM4Power1OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 62 }

imPM4Power1OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 63 }

imPM4Power1OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 64 }

imPM4Power1OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 65 }

imPM4Power1OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 66 }

imPM4Power1OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 67 }

imPM4Power1OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power1 68 }

--
-- PM 4, Power 2 group
--
imPM4Power2	OBJECT IDENTIFIER ::= { imPanM4 4 }

imPM4Power2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 1 }

imPM4Power2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 2 }

imPM4Power2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 3 }

imPM4Power2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 4 }

imPM4Power2InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 5 }

imPM4Power2InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 6 }

imPM4Power2InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 7 }

imPM4Power2InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 8 }

imPM4Power2InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 9 }

imPM4Power2InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 10 }

imPM4Power2InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 11 }

imPM4Power2InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 12 }

imPM4Power2InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 13 }

imPM4Power2InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 14 }

imPM4Power2InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 15 }

imPM4Power2InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 16 }

imPM4Power2InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 17 }

imPM4Power2InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 18 }

imPM4Power2InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 19 }

imPM4Power2InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 20 }

imPM4Power2InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 21 }

imPM4Power2InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 22 }

imPM4Power2OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 23 }

imPM4Power2OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 24 }

imPM4Power2OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 25 }

imPM4Power2OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 26 }

imPM4Power2OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 27 }

imPM4Power2OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 28 }

imPM4Power2OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 29 }

imPM4Power2OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 30 }

imPM4Power2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 31 }
 
imPM4Power2Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 32 }

imPM4Power2Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 33 }

imPM4Power2Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 34 }

imPM4Power2Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 35 }

imPM4Power2Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 36 }

imPM4Power2Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 37 }

imPM4Power2Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 38 }

imPM4Power2Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 39 }

imPM4Power2InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 40 }

imPM4Power2InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 41 }

imPM4Power2InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 42 }

imPM4Power2InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 43 }

imPM4Power2InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 44 }

imPM4Power2InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 45 }

imPM4Power2InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 46 }

imPM4Power2InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 47 }

imPM4Power2InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 48 }

imPM4Power2OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 49 }

imPM4Power2OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 50 }

imPM4Power2OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 51 }

imPM4Power2OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 52 }

imPM4Power2OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 53 }

imPM4Power2OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 54 }

imPM4Power2Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 55 }

imPM4Power2BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 56 }

imPM4Power2OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 57 }

imPM4Power2OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 58 }

imPM4Power2OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 59 }

imPM4Power2OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 60 }

imPM4Power2OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 61 }

imPM4Power2OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 62 }

imPM4Power2OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 63 }

imPM4Power2OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 64 }

imPM4Power2OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 65 }

imPM4Power2OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 66 }

imPM4Power2OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 67 }

imPM4Power2OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power2 68 }

--
-- PM 4, Power 3 group
--
imPM4Power3	OBJECT IDENTIFIER ::= { imPanM4 5 }

imPM4Power3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 1 }

imPM4Power3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 2 }

imPM4Power3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 3 }

imPM4Power3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 4 }

imPM4Power3InputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 5 }

imPM4Power3InputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 6 }

imPM4Power3InputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 7 }

imPM4Power3InputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 8 }

imPM4Power3InputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 9 }

imPM4Power3InputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 10 }

imPM4Power3InputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 11 }

imPM4Power3InputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 12 }

imPM4Power3InputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 13 }

imPM4Power3InputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 14 }

imPM4Power3InputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 15 }

imPM4Power3InputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 16 }

imPM4Power3InputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 17 }

imPM4Power3InputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 18 }

imPM4Power3InputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 19 }

imPM4Power3InputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 20 }

imPM4Power3InputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 21 }

imPM4Power3InputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 22 }

imPM4Power3OutputVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 23 }

imPM4Power3OutputCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 24 }

imPM4Power3OutputPowerVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 25 }

imPM4Power3OutputPowerKVA OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 26 }

imPM4Power3OutputPowerW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "W"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 27 }

imPM4Power3OutputPowerKW OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kW"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 28 }

imPM4Power3OutputLoad OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 29 }

imPM4Power3OutputFrequency OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "0.1 Hz"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 30 }

imPM4Power3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 31 }
 
imPM4Power3Running1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 32 }

imPM4Power3Running2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 33 }

imPM4Power3Running3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 34 }

imPM4Power3Running4 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 35 }

imPM4Power3Running5 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 36 }

imPM4Power3Running6 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 37 }

imPM4Power3Running7 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 38 }

imPM4Power3Running8 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 39 }

imPM4Power3InputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 40 }

imPM4Power3InputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 41 }

imPM4Power3InputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 42 }

imPM4Power3InputFuse3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 43 }

imPM4Power3InputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 44 }

imPM4Power3InputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 45 }

imPM4Power3InputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 46 }

imPM4Power3InputBreaker3 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 47 }

imPM4Power3InputSurgeArrester OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 48 }

imPM4Power3OutputFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 49 }

imPM4Power3OutputFuse1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 50 }

imPM4Power3OutputFuse2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 51 }

imPM4Power3OutputBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 52 }

imPM4Power3OutputBreaker1 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 53 }

imPM4Power3OutputBreaker2 OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 54 }

imPM4Power3Fan OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 55 }

imPM4Power3BatteryAvailable OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 56 }

imPM4Power3OutputVoltagePhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 57 }

imPM4Power3OutputCurrentPhase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 58 }

imPM4Power3OutputPowerVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 59 }

imPM4Power3OutputPowerKVAphase1 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 60 }

imPM4Power3OutputVoltagePhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 61 }

imPM4Power3OutputCurrentPhase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 62 }

imPM4Power3OutputPowerVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 63 }

imPM4Power3OutputPowerKVAphase2 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 64 }

imPM4Power3OutputVoltagePhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 65 }

imPM4Power3OutputCurrentPhase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 66 }

imPM4Power3OutputPowerVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "VA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 67 }

imPM4Power3OutputPowerKVAphase3 OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 kVA"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Power3 68 }

--
-- PM 4, Battery group
--
imPM4Battery	OBJECT IDENTIFIER ::= { imPanM4 6 }

imPM4BatteryNominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Battery 1 }

imPM4BatteryVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Battery 2 }

imPM4BatteryCurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Battery 3 }

imPM4BatteryChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Battery 4 }

imPM4BatteryAutonomyTime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Battery 5 }

imPM4BatteryTimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Battery 6 }

--
-- PM 4, Battery LEG1 group
--
imPM4BatLeg1	OBJECT IDENTIFIER ::= { imPanM4 7 }

imPM4BatLeg1Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 1 }

imPM4BatLeg1Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 2 }

imPM4BatLeg1serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 3 }

imPM4BatLeg1nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 4 }

imPM4BatLeg1InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 5 }

imPM4BatLeg1NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 6 }

imPM4BatLeg1NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 7 }

imPM4BatLeg1Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 8 }

imPM4BatLeg1Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 9 }

imPM4BatLeg1Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 10 }

imPM4BatLeg1Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 11 }

imPM4BatLeg1ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 12 }

imPM4BatLeg1RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 13 }


imPM4BatLeg1Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 14 }

imPM4BatLeg1TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 15 }

imPM4BatLeg1Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 16 }

imPM4BatLeg1Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 17 }

imPM4BatLeg1LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg1 18 }

--
-- PM 4, Battery LEG2 group
--
imPM4BatLeg2	OBJECT IDENTIFIER ::= { imPanM4 8 }

imPM4BatLeg2Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 1 }

imPM4BatLeg2Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 2 }

imPM4BatLeg2serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 3 }

imPM4BatLeg2nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 4 }

imPM4BatLeg2InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 5 }

imPM4BatLeg2NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 6 }

imPM4BatLeg2NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 7 }

imPM4BatLeg2Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 8 }

imPM4BatLeg2Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 9 }

imPM4BatLeg2Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 10 }

imPM4BatLeg2Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 11 }

imPM4BatLeg2ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 12 }

imPM4BatLeg2RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 13 }


imPM4BatLeg2Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 14 }

imPM4BatLeg2TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 15 }

imPM4BatLeg2Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 16 }

imPM4BatLeg2Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 17 }

imPM4BatLeg2LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg2 18 }

--
-- PM 4, Battery LEG3 group
--
imPM4BatLeg3	OBJECT IDENTIFIER ::= { imPanM4 9 }

imPM4BatLeg3Manufacturer OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 1 }

imPM4BatLeg3Type OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 2 }

imPM4BatLeg3serNumb OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 3 }

imPM4BatLeg3nextServiceDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 4 }

imPM4BatLeg3InstallationDate OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 5 }

imPM4BatLeg3NominalVoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 6 }

imPM4BatLeg3NominalCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 7 }

imPM4BatLeg3Lifetime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "years"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 8 }

imPM4BatLeg3Voltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 9 }

imPM4BatLeg3Current OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 10 }

imPM4BatLeg3Temperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 11 }

imPM4BatLeg3ChargeState OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "%"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 12 }

imPM4BatLeg3RestCapacity OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "Ah"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 13 }


imPM4BatLeg3Autonomytime OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 14 }

imPM4BatLeg3TimeOnBattery OBJECT-TYPE
    SYNTAX     NonNegativeInteger
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 15 }

imPM4BatLeg3Fuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 16 }

imPM4BatLeg3Breaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 17 }

imPM4BatLeg3LowVoltageDisconnect OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4BatLeg3 18 }

--
-- PM 4, Ditribution group
--
imPM4Distrib	OBJECT IDENTIFIER ::= { imPanM4 10 }

imPm4Distrib OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Distrib 1 }

imPM4DistTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM4DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Distrib 2 }

imPM4DistEntry OBJECT-TYPE
    SYNTAX     ImPM4DistEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM4DistId }
    ::= { imPM4DistTable 1 }

ImPM4DistEntry ::= SEQUENCE {
    imPM4DistId       PositiveInteger,
    imPM4DistFuse     INTEGER,
    imPM4DistBreaker  INTEGER
}

imPM4DistId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4DistEntry 1 }

imPM4DistFuse OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4DistEntry 2 }

imPM4DistBreaker OBJECT-TYPE
    SYNTAX     INTEGER {
        ok(0),
        err(1)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4DistEntry 3 }

--
-- PM 4, Control group
--
imPM4Control	OBJECT IDENTIFIER ::= { imPanM4 11 }

imPM4ContTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF ImPM4ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Control 1 }

imPM4ContEntry OBJECT-TYPE
    SYNTAX     ImPM4ContEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    INDEX { imPM4ContId }
    ::= { imPM4ContTable 1 }

ImPM4ContEntry ::= SEQUENCE {
    imPM4ContId       PositiveInteger,
    imPM4ContState    INTEGER,
    imPM4ContLabel    DisplayString,
    imPM4ContTimeOFF  INTEGER,
    imPM4ContAuto     INTEGER
}

imPM4ContId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4ContEntry 1 }

imPM4ContState OBJECT-TYPE
    SYNTAX     INTEGER {
        on(0),
        off(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4ContEntry 2 }

imPM4ContLabel OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..15))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4ContEntry 3 }

imPM4ContTimeOFF OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4ContEntry 4 }

imPM4ContAuto OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4ContEntry 5 }

imPM4ContTestCAPcommand OBJECT-TYPE
    SYNTAX     INTEGER {
        off(0),
        on(1)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Control 2 }

imPM4ContTestCAPvoltage OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 V"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Control 3 }

imPM4ContTestCAPcurrent OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "0.1 A"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Control 4 }

imPM4ContTestCAPtemperature OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "degrees Centigrade"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Control 5 }

imPM4ContTestCAPduration OBJECT-TYPE
    SYNTAX     INTEGER
    UNITS      "min"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Control 6 }

imPM4ContTestCAPstatus OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "???."
    ::= { imPM4Control 7 }

--
-- Alarm group
--
imco3Alarm	OBJECT IDENTIFIER ::= { imcoObjects3 3 }

imco3AlarmsPresent OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The present number of active alarm conditions."
    ::= { imco3Alarm 1 }

imco3AlarmTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF Imco3AlarmEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "A list of alarm table entries.

            The number of rows in the table at any given time is
            reflected by the value of imco3AlarmsPresent."
    ::= { imco3Alarm 2 }

imco3AlarmEntry OBJECT-TYPE
    SYNTAX     Imco3AlarmEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry containing information applicable to a
            particular alarm."
    INDEX { imco3AlarmId }
    ::= { imco3AlarmTable 1 }

Imco3AlarmEntry ::= SEQUENCE {
    imco3AlarmId          PositiveInteger,
    imco3AlarmDescr       INTEGER,
    imco3AlarmTime        TimeStamp,
    imco3AlarmPMnumber    INTEGER
}

imco3AlarmId OBJECT-TYPE
    SYNTAX     PositiveInteger
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "A unique identifier for an alarm condition.  This
            value must remain constant."
    ::= { imco3AlarmEntry 1 }

imco3AlarmDescr OBJECT-TYPE
    SYNTAX     INTEGER {
	aCommunicationLost(1),
	aLowBattery(2),
	aOnBattery(3),
	aActionAlarm4(4),
	aActionAlarm5(5),
	aActionAlarm6(6),
	aActionAlarm7(7),
	aActionAlarm8(8),
	aTimeforSystemService(9),
	aGeneralSurgeArresterActivated(10),
	aGeneralDoor1Opened(11),
	aGeneralDoor2Opened(12),
	aGeneralFanalarm(13),
	aGeneralOvertemperature(14),
	aGeneralUndertemperature(15),
	aGeneralUser1Alarm(16),
	aGeneralUser2Alarm(17),
	aGeneralUser3Alarm(18),
	aGeneralUser4Alarm(19),
	aTimeforPowerSupply1Service(20),
	aPowerSupply1InputOvervoltage(21),
	aPowerSupply1InputUndervoltage(22),
	aPowerSupply1InputOvercurrent(23),
	aPowerSupply1InputOverload(24),
	aPowerSupply1OutputOvervoltage(25),
	aPowerSupply1OutputUndervoltage(26),
	aPowerSupply1OutputOvercurrent(27),
	aPowerSupply1OutputOverload(28),
	aPowerSupply1Overtemperature(29),
	aPowerSupply1Undertemperature(30),
	aPowerSupply1Converter1notRunning(31),
	aPowerSupply1Converter2notRunning(32),
	aPowerSupply1Converter3notRunning(33),
	aPowerSupply1Converter4notRunning(34),
	aPowerSupply1Converter5notRunning(35),
	aPowerSupply1Converter6notRunning(36),
	aPowerSupply1Converter7notRunning(37),
	aPowerSupply1Converter8notRunning(38),
	aPowerSupply1InputFuseOff(39),
	aPowerSupply1InputFuse1Off(40),
	aPowerSupply1InputFuse2Off(41),
	aPowerSupply1InputFuse3Off(42),
	aPowerSupply1InputBreakerOff(43),
	aPowerSupply1InputBreaker1Off(44),
	aPowerSupply1InputBreaker2Off(45),
	aPowerSupply1InputBreaker3Off(46),
	aPowerSupply1InputSurgeArrester(47),
	aPowerSupply1OutputFuseOff(48),
	aPowerSupply1OutputFuse1Off(49),
	aPowerSupply1OutputFuse2Off(50),
	aPowerSupply1OutputBreakerOff(51),
	aPowerSupply1OutputBreaker1Off(52),
	aPowerSupply1OutputBreaker2Off(53),
	aPowerSupply1FanMalfunction(54),
	aPowerSupply1OutputOutOfPhase(55),
	aPowerSupply1UserAlarm1(56),
	aPowerSupply1UserAlarm2(57),
	aTimeOrPowerSupply2Service(58),
	aPowerSupply2InputOvervoltage(59),
	aPowerSupply2InputUndervoltage(60),
	aPowerSupply2InputOvercurrent(61),
	aPowerSupply2InputOverload(62),
	aPowerSupply2OutputOvervoltage(63),
	aPowerSupply2OutputUndervoltage(64),
	aPowerSupply2OutputOvercurrent(65),
	aPowerSupply2OutputOverload(66),
	aPowerSupply2Overtemperature(67),
	aPowerSupply2Undertemperature(68),
	aPowerSupply2Converter1NotRunning(69),
	aPowerSupply2Converter2NotRunning(70),
	aPowerSupply2Converter3NotRunning(71),
	aPowerSupply2Converter4NotRunning(72),
	aPowerSupply2Converter5NotRunning(73),
	aPowerSupply2Converter6NotRunning(74),
	aPowerSupply2Converter7NotRunning(75),
	aPowerSupply2Converter8NotRunning(76),
	aPowerSupply2InputFuseOff(77),
	aPowerSupply2InputFuse1Off(78),
	aPowerSupply2InputFuse2Off(79),
	aPowerSupply2InputFuse3Off(80),
	aPowerSupply2InputBreakerOff(81),
	aPowerSupply2InputBreaker1Off(82),
	aPowerSupply2InputBreaker2Off(83),
	aPowerSupply2InputBreaker3Off(84),
	aPowerSupply2InputSurgeArrester(85),
	aPowerSupply2OutputFuseOff(86),
	aPowerSupply2OutputFuse1Off(87),
	aPowerSupply2OutputFuse2Off(88),
	aPowerSupply2OutputBreakerOff(89),
	aPowerSupply2OutputBreaker1Off(90),
	aPowerSupply2OutputBreaker2Off(91),
	aPowerSupply2FanMalfunction(92),
	aPowerSupply2OutputOutOfPhase(93),
	aPowerSupply2UserAlarm1(94),
	aPowerSupply2UserAlarm2(95),
	aTimeForPowerSupply3Service(96),
	aPowerSupply3InputOvervoltage(97),
	aPowerSupply3InputUndervoltage(98),
	aPowerSupply3InputOvercurrent(99),
	aPowerSupply3InputOverload(100),
	aPowerSupply3OutputOvervoltage(101),
	aPowerSupply3OutputUndervoltage(102),
	aPowerSupply3OutputOvercurrent(103),
	aPowerSupply3OutputOverload(104),
	aPowerSupply3Overtemperature(105),
	aPowerSupply3Undertemperature(106),
	aPowerSupply3Converter1notRunning(107),
	aPowerSupply3Converter2notRunning(108),
	aPowerSupply3Converter3notRunning(109),
	aPowerSupply3Converter4notRunning(110),
	aPowerSupply3Converter5notRunning(111),
	aPowerSupply3Converter6notRunning(112),
	aPowerSupply3Converter7notRunning(113),
	aPowerSupply3Converter8notRunning(114),
	aPowerSupply3InputFuseOff(115),
	aPowerSupply3InputFuse1Off(116),
	aPowerSupply3InputFuse2Off(117),
	aPowerSupply3InputFuse3Off(118),
	aPowerSupply3InputBreakerOff(119),
	aPowerSupply3InputBreaker1Off(120),
	aPowerSupply3InputBreaker2Off(121),
	aPowerSupply3InputBreaker3Off(122),
	aPowerSupply3InputSurgeArrester(123),
	aPowerSupply3OutputFuseOff(124),
	aPowerSupply3OutputFuse1Off(125),
	aPowerSupply3OutputFuse2Off(126),
	aPowerSupply3OutputBreakerOff(127),
	aPowerSupply3OutputBreaker1Off(128),
	aPowerSupply3OutputBreaker2Off(129),
	aPowerSupply3FanMalfunction(130),
	aPowerSupply3OutputOutOfPhase(131),
	aPowerSupply3UserAlarm1(132),
	aPowerSupply3UserAlarm2(133),
	aBatteryOvervoltage(134),
	aBatteryUndervoltage(135),
	aBatteryOvercurrent(136),
	aRunningOnBattery(137),
	aBatteryLow(138),
	aTimeForBatteryLeg1Service(139),
	aBatteryLeg1LifetimeExpired(140),
	aBatteryLeg1Overvoltage(141),
	aBatteryLeg1Undervoltage(142),
	aBatteryLeg1Overcurrent(143),
	aBatteryLeg1Overtemperature(144),
	aBatteryLeg1Undertemperature(145),
	aRunningOnBatteryLeg1(146),
	aBatteryLeg1Low(147),
	aBatteryLeg1Critical(148),
	aBatteryLeg1FuseOff(149),
	aBatteryLeg1BreakerOff(150),
	aBatteryLeg1LVDOff(151),
	aBatteryLeg1UserAlarm1(152),
	aBatteryLeg1UserAlarm2(153),
	aTimeForBatteryLeg2Service(154),
	aBatteryLeg2LifetimeExpired(155),
	aBatteryLeg2Overvoltage(156),
	aBatteryLeg2Undervoltage(157),
	aBatteryLeg2Overcurrent(158),
	aBatteryLeg2Overtemperature(159),
	aBatteryLeg2Undertemperature(160),
	aRunningOnBatteryLeg2(161),
	aBatteryLeg2Low(162),
	aBatteryLeg2Critical(163),
	aBatteryLeg2FuseOff(164),
	aBatteryLeg2BreakerOff(165),
	aBatteryLeg2LVDOff(166),
	aBatteryLeg2UserAlarm1(167),
	aBatteryLeg2UserAlarm2(168),
	aTimeForBatteryLeg3Service(169),
	aBatteryLeg3LifetimeExpired(170),
	aBatteryLeg3Overvoltage(171),
	aBatteryLeg3Undervoltage(172),
	aBatteryLeg3Overcurrent(173),
	aBatteryLeg3Overtemperature(174),
	aBatteryLeg3Undertemperature(175),
	aRunningOnBatteryLeg3(176),
	aBatteryLeg3Low(177),
	aBatteryLeg3Critical(178),
	aBatteryLeg3FuseOff(179),
	aBatteryLeg3BreakerOff(180),
	aBatteryLeg3LVDOff(181),
	aBatteryLeg3UserAlarm1(182),
	aBatteryLeg3UserAlarm2(183),
	aDistributionFuse1Off(184),
	aDistributionFuse2Off(185),
	aDistributionFuse3Off(186),
	aDistributionFuse4Off(187),
	aDistributionFuse5Off(188),
	aDistributionFuse6Off(189),
	aDistributionFuse7Off(190),
	aDistributionFuse8Off(191),
	aDistributionFuse9Off(192),
	aDistributionFuse10Off(193),
	aDistributionFuse11Off(194),
	aDistributionFuse12Off(195),
	aDistributionFuse13Off(196),
	aDistributionFuse14Off(197),
	aDistributionFuse15Off(198),
	aDistributionFuse16Off(199),
	aDistributionFuse17Off(200),
	aDistributionFuse18Off(201),
	aDistributionFuse19Off(202),
	aDistributionFuse20Off(203),
	aDistributionFuse21Off(204),
	aDistributionFuse22Off(205),
	aDistributionFuse23Off(206),
	aDistributionFuse24Off(207),
	aDistributionFuse25Off(208),
	aDistributionFuse26Off(209),
	aDistributionFuse27Off(210),
	aDistributionFuse28Off(211),
	aDistributionFuse29Off(212),
	aDistributionFuse30Off(213),
	aDistributionFuse31Off(214),
	aDistributionFuse32Off(215),
	aDistributionBreaker1Off(216),
	aDistributionBreaker2Off(217),
	aDistributionBreaker3Off(218),
	aDistributionBreaker4Off(219),
	aDistributionBreaker5Off(220),
	aDistributionBreaker6Off(221),
	aDistributionBreaker7Off(222),
	aDistributionBreaker8Off(223),
	aDistributionBreaker9Off(224),
	aDistributionBreaker10Off(225),
	aDistributionBreaker11Off(226),
	aDistributionBreaker12Off(227),
	aDistributionBreaker13Off(228),
	aDistributionBreaker14Off(229),
	aDistributionBreaker15Off(230),
	aDistributionBreaker16Off(231),
	aDistributionBreaker17Off(232),
	aDistributionBreaker18Off(233),
	aDistributionBreaker19Off(234),
	aDistributionBreaker20Off(235),
	aDistributionBreaker21Off(236),
	aDistributionBreaker22Off(237),
	aDistributionBreaker23Off(238),
	aDistributionBreaker24Off(239),
	aDistributionBreaker25Off(240),
	aDistributionBreaker26Off(241),
	aDistributionBreaker27Off(242),
	aDistributionBreaker28Off(243),
	aDistributionBreaker29Off(244),
	aDistributionBreaker30Off(245),
	aDistributionBreaker31Off(246),
	aDistributionBreaker32Off(247),
	aDistributionBreaker33Off(248),
	aDistributionBreaker34Off(249),
	aDistributionBreaker35Off(250),
	aDistributionBreaker36Off(251),
	aDistributionBreaker37Off(252),
	aDistributionBreaker38Off(253),
	aDistributionBreaker39Off(254),
	aDistributionBreaker40Off(255),
	aDistributionBreaker41Off(256),
	aDistributionBreaker42Off(257),
	aDistributionBreaker43Off(258),
	aDistributionBreaker44Off(259),
	aDistributionBreaker45Off(260),
	aDistributionBreaker46Off(261),
	aDistributionBreaker47Off(262),
	aDistributionBreaker48Off(263),
	aDistributionBreaker49Off(264),
	aDistributionBreaker50Off(265),
	aDistributionBreaker51Off(266),
	aDistributionBreaker52Off(267),
	aDistributionBreaker53Off(268),
	aDistributionBreaker54Off(269),
	aDistributionBreaker55Off(270),
	aDistributionBreaker56Off(271),
	aDistributionBreaker57Off(272),
	aDistributionBreaker58Off(273),
	aDistributionBreaker59Off(274),
	aDistributionBreaker60Off(275),
	aDistributionBreaker61Off(276),
	aDistributionBreaker62Off(277),
	aDistributionBreaker63Off(278),
	aDistributionBreaker64Off(279),
	aDistributionStripOvertemperature(280)
	aSupply1UnitAny(281)
        aSupply1UnitAll(282)
        aSupply1UnitMains(283)
        aSupply1UnitInputPhase1(284)
        aSupply1UnitInputPhase2(285)
        aSupply1UnitInputPhase3(286)
        aSupply1UnitInputPhaseAll(287)
        aSupply1Reserva1(288)
        aSupply1Reserva2(289)
        aSupply1Reserva3(290)
        aSupply1Reserva4(291)
        aSupply1Reserva5(292)
        aSupply2UnitAny(293)
        aSupply2UnitAll(294)
        aSupply2UnitMains(295)
        aSupply2UnitInputPhase1(296)
        aSupply2UnitInputPhase2(297)
        aSupply2UnitInputPhase3(298)
        aSupply2UnitInputPhaseAll(299)
        aSupply2Reserva1(300)
        aSupply2Reserva2(301)
        aSupply2Reserva3(302)
        aSupply2Reserva4(303)
        aSupply2Reserva5(304)
        aSupply3UnitAny(305)
        aSupply3UnitAll(306)
        aSupply3UnitMains(307)
        aSupply3UnitInputPhase1(308)
        aSupply3UnitInputPhase2(309)
        aSupply3UnitInputPhase3(310)
        aSupply3UnitInputPhaseAll(311)
        aSupply3Reserva1(312)
        aSupply3Reserva2(313)
        aSupply3Reserva3(314)
        aSupply3Reserva4(315)
        aSupply3Reserva5(316)
    }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Description of the alarm condition."
    ::= { imco3AlarmEntry 2 }


imco3AlarmTime OBJECT-TYPE
    SYNTAX     TimeStamp
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of sysUpTime when the alarm condition was
            detected.  If the alarm condition was detected at the
            time of agent startup and presumably existed before
            agent startup, the value of gsAlarmTime shall equal
            0."
    ::= { imco3AlarmEntry 3 }

imco3AlarmPMnumber OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "PM number of the alarm condition."
    ::= { imco3AlarmEntry 4 }
  
--
-- notifications, i.e., traps
--
imco3Traps	OBJECT IDENTIFIER ::= { imcoMIB 12 }


--imco3TrapNoDevice NOTIFICATION-TYPE
--    ENTERPRISE Imco3Traps
--    DESCRIPTION
--            "???."
--  ::= { imco3Traps 1 }

imco3TrapNoDevice NOTIFICATION-TYPE
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 1 }

imco3TrapOnBattery NOTIFICATION-TYPE
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 2 }

imco3TrapNewAlarm NOTIFICATION-TYPE
    OBJECTS { imco3AlarmDescr, imco3AlarmTime, imco3AlarmPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 3 }

imco3TrapAlarmEnd NOTIFICATION-TYPE
    OBJECTS { imco3AlarmDescr, imco3AlarmTime, imco3AlarmPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 4 }

imco3TrapAlarmOut1ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 10 }

imco3TrapAlarmOut2ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 11 }

imco3TrapAlarmOut3ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 12 }

imco3TrapAlarmOut4ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 13 }

imco3TrapAlarmOut5ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 14 }

imco3TrapAlarmOut6ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 15 }

imco3TrapAlarmOut7ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 16 }

imco3TrapAlarmOut8ON NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 17 }

imco3TrapAlarmOut1OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 20 }

imco3TrapAlarmOut2OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 21 }

imco3TrapAlarmOut3OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 22 }

imco3TrapAlarmOut4OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 23 }

imco3TrapAlarmOut5OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 24 }

imco3TrapAlarmOut6OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 25 }

imco3TrapAlarmOut7OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 26 }

imco3TrapAlarmOut8OFF NOTIFICATION-TYPE
    OBJECTS { imco3IdentPMnumber }
    STATUS  current
    DESCRIPTION
            "???."
  ::= { imco3Traps 27 }

imco3TrapTestCAPstart NOTIFICATION-TYPE
	OBJECTS { imco3IdentPMnumber }
	STATUS current
	DESCRIPTION
		"???."
  ::= { imco3Traps 30 }

imco3TrapTestCAPstop NOTIFICATION-TYPE
	OBJECTS { imco3IdentPMnumber }
	STATUS current
	DESCRIPTION
		"???."
  ::= { imco3Traps 31 }


END

