ExaltComm DEFINITIONS ::= BEGIN
    IMPORTS
    MODULE-IDENTITY, enterprises, Integer32, Gauge32
        FROM SNMPv2-SMI
    TEXTUAL-CONVENTION
        FROM SNMPv2-TC;

    RadioSourceT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Radio Source. Radio A or Radio B"
        SYNTAX		    INTEGER {
                                radioA( 1 ),
                                radioB( 2 )
                            }

    BandwidthT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Radio Bandwidth."
        SYNTAX		    INTEGER {
                                khz1750( 1750 ),
                                khz3500( 3500 ),
                                khz5000( 5000 ),
                                khz7000( 7000 ),
                                khz8000( 8000 ),
                                khz10000( 10000 ),
                                khz12500( 12500 ),
                                khz13750( 13750 ),
                                khz14000( 14000 ),
                                khz16000( 16000 ),
                                khz20000( 20000 ),
                                khz25000( 25000 ),
                                khz27500( 27500 ),
                                khz28000( 28000 ),
                                khz29650( 29650 ),
                                khz30000( 30000 ),
                                khz32000( 32000 ),
                                khz40000( 40000 ),
                                khz50000( 50000 ),
                                khz55000( 55000 ),
                                khz56000( 56000 ),
                                khz59300( 59300 ),
                                khz60000( 60000 ),
                                khz64000( 64000 ),
                                khz80000( 80000 )
                            }

    ModulationT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the supported Modulation types."
        SYNTAX		    INTEGER {
                                mQPSK( 0 ),
                                m16QAM( 1 ),
                                m32QAM( 5 ),
                                m64QAM( 2 ),
                                m128QAM( 3 ),
                                m256QAM( 4 ),
                                m512QAM( 6 )
                            }

    LinkDistanceT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Distance between Radios in miles."
        SYNTAX		    INTEGER {
                                under2( 2 ),
                                under5( 5 ),
                                under10( 10 ),
                                under20( 20 ),
                                under50( 50 )
                            }

    TddFrameSizeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The TDD Frame Size enum in tenths of milliseconds."
        SYNTAX		    INTEGER {
                                tdd05( 5 ),
                                tdd10( 10 ),
                                tdd20( 20 ),
                                tdd25( 25 ),
                                tdd40( 40 ),
                                tdd50( 50 )
                            }

    ExtAlarmsT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This is the condidtion that an alarm should be
                            raised."
        SYNTAX		    INTEGER {
                                alarmOnCLOSE( 1 ),
                                alarmOnOPEN( 2 )
                            }

    EthMainStatusT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Main ethernet port status."
        SYNTAX		    INTEGER {
                                enableWithMute( 0 ),
                                enableNOmute( 1 ),
                                disableAlarm( 2 )
                            }

    EthAuxStatusT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Aux ethernet port Alarm enabled status"
        SYNTAX		    INTEGER {
                                enableAlarm( 0 ),
                                disableAlarm( 1 )
                            }

    EthPortMode ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Ethernet port operation Modes"
        SYNTAX		    INTEGER {
			        full100( 0 ),
                                full10( 1 ),
                                half100( 2 ),
                                half10( 3 ),
                                auto( 4 )

                            }

    GbeEthPortMode ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Ethernet port operation Modes for GigE interfaces"
        SYNTAX		    INTEGER {
                                full1000( 1 ),
                                half1000( 2 ),
                                full100( 3 ),
                                half100( 4 ),
                                full10( 5 ),
                                half10( 6 ),
                                auto( 7 )
                            }

    AuxNmsMode ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The NMS mode (in-band or out-of-band)"
        SYNTAX		    INTEGER {
                                inBand( 1 ),
                                outBand( 2 )
                            }

    EnableStatusT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Enable / Disable parameter"
        SYNTAX		    INTEGER {
                                disable( 0 ),
                                enable( 1 )
                            }

    EthernetMgmtTypeT ::= TEXTUAL-CONVENTION
        STATUS              current
        DESCRIPTION         "Defines Ethernet switch management options"
        SYNTAX              INTEGER {
                                inBand( 0 ),
                                outOfBand( 1 ),
                                portToPort( 2 ),
                                legacy( 3 ),
                                advanced( 4 )
                            }

    MhsRoleT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The designated role of the MHS."
        SYNTAX		    INTEGER {
                                notDefined( 0 ),
                                primary( 1 ),
                                secondary( 2 )
                            }

    MhsLockOnT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Lock on configuration of the MHS."
        SYNTAX		    INTEGER {
                                noLock( 0 ),
                                lockOnline( 1 ),
                                lockStandby( 2 )
                            }

    MhsTimeoutT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The lock on timeout of the MHS."
        SYNTAX		    INTEGER {
                                infinite( 0 ),
                                twentySeconds( 1 ),
                                tenMinutes( 2 )
                            }

    Te1StatusT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The T1/E1 enabled status."
        SYNTAX		    INTEGER {
                                disabled( 0 ),
                                enabled( 1 )
                            }

    Te1LboT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Te1 LBO types."
        SYNTAX		    INTEGER {
                                notAvail( -1 ),
                                lbo0to133( 0 ),
                                lbo133to266( 1 ),
                                lbo266to399( 2 ),
                                lbo399to533( 3 ),
                                lbo533to655( 4 )
                            }

    Te1LineCodeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The T1/E1 Line Codes."
        SYNTAX		    INTEGER {
                                notAvail( -1 ),
                                b8zs( 0 ),
                                ami( 1 )
                            }

    Te1LoopBackModeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the loopback modes of the T1/E1 interfaces."
        SYNTAX		    INTEGER {
                                lbDisabled( 0 ),
                                lbExtLocal( 1 ),
                                lbExtRemote( 2 ),
                                lbInternal( 3 )
                            }

    AlarmLevelT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Alarm Levels."
        SYNTAX		    INTEGER {
                                almNORMAL( 0 ),
                                almMINOR( 1 ),
                                almMAJOR( 2 ),
                                almDisable( 3 ),
				almNotAvailable (4),
                                almClearChanel( 5 ),
                                almNonOccupant( 6 )

                            }

    BandSelectT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Band Selections"
        SYNTAX		    INTEGER {
                                ghz53( 0 ),
                                ghz54( 1 ),
                                ghz58( 2 )
                            }

    FreqGroupT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Frequency Group"
        SYNTAX		    INTEGER {
                                all( 0 ),
                                preferred( 1 )
                            }

    Led4ColorT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "4 Color front pannel LED Bitmap."
        SYNTAX		    INTEGER {
                                ledOFF( 0 ),
                                redSlowBlink( 85 ),
                                yellowSlowBlink( 102 ),
                                greenSlowBlink( 119 ),
                                redFastBlink( 153 ),
                                yellowFastBlink( 170 ),
                                greenFastBlink( 187 ),
                                redON( 221 ),
                                yellowON( 238 ),
                                greenON( 255 )
                            }

    RadioFreq24T ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This is the 2.4 ghz band freq ranges."
        SYNTAX		    Integer32 (2406..2468)

    RadioFreq5gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the 5.3 and 5.8 Freq ranges."
        SYNTAX		    Integer32 (5260..5332 | 5731..5844)

    RadioTxPwr24T ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 2.4 GHz band Power levels."
        SYNTAX		    Integer32 (70..270)

    RadioTxPwr5gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 5 GHz Band Power levels."
        SYNTAX		    Integer32 (-70..240)

    RadioTxPwr6gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 6 GHz Band Power levels."
        SYNTAX		    Integer32 (0..320)

    RadioTxPwr7LgT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 7.0L GHz Band Power levels."
        SYNTAX		    Integer32 (0..320)

    RadioTxPwr7gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 7 GHz Band Power levels."
        SYNTAX		    Integer32 (30..270)

    RadioTxPwr8gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 8 GHz Band Power levels."
        SYNTAX		    Integer32 (30..270)

    RadioTxPwr11gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 11 GHz Band Power levels."
        SYNTAX		    Integer32 (0..270)

    RadioTxPwrHP11gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 11 GHz HP Band Power levels."
        SYNTAX		    Integer32 (100..290)

    RadioTxPwr13gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 13 GHz Band Power levels."
        SYNTAX		    Integer32 (0..270)

    RadioTxPwr15gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 15 GHz Band Power levels."
        SYNTAX		    Integer32 (0..270)

    RadioTxPwr18gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 18 GHz Band Power levels."
        SYNTAX		    Integer32 (0..270)

    RadioTxPwr23gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 23 GHz Band Power levels."
        SYNTAX		    Integer32 (0..260)

    RadioTxPwr24gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 24 GHz Band Power levels."
        SYNTAX		    Integer32 (-250..50)

    RadioTxPwr28gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 28 GHz Band Power levels."
        SYNTAX		    Integer32 (0..260)

    RadioTxPwr38gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 38 GHz Band Power levels."
        SYNTAX		    Integer32 (0..240)

    RadioTxPwr42gT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The 42 GHz Band Power levels."
        SYNTAX		    Integer32 (0..230)

    DiplexerConfigG2T ::= TEXTUAL-CONVENTION
        STATUS              current
        DESCRIPTION         "These are diplexer configuration options for lower band radios, where
                             Unconfigured is not configurable."
        SYNTAX              INTEGER {
                                unconfigured( 0 ),
                                diplexer701( 701 ),
                                diplexer702( 702 ),
                                diplexer703( 703 ),
                                diplexer704( 704 ),
                                diplexer705( 705 ),
                                diplexer706( 706 ),
                                diplexer707( 707 ),
                                diplexer708( 708 ),
                                diplexer709( 709 ),
                                diplexer710( 710 ),
                                diplexer711( 711 ),
                                diplexer712( 712 ),
                                diplexer713( 713 ),
                                diplexer714( 714 ),
                                diplexer715( 715 ),
                                diplexer716( 716 ),
                                diplexer717( 717 ),
                                diplexer718( 718 ),
                                diplexer719( 719 ),
                                diplexer720( 720 ),
                                diplexer721( 721 ),
                                diplexer722( 722 ),
                                diplexer723( 723 ),
                                diplexer724( 724 ),
                                diplexer725( 725 ),
                                diplexer726( 726 ),
                                diplexer727( 727 ),
                                diplexer728( 728 ),
                                diplexer729( 729 ),
                                diplexer730( 730 ),
                                diplexer731( 731 ),
                                diplexer732( 732 ),
                                diplexer733( 733 ),
                                diplexer734( 734 ),
                                diplexer735( 735 ),
                                diplexer736( 736 ),
                                diplexer737( 737 ),
                                diplexer738( 738 ),
                                diplexer739( 739 ),
                                diplexer740( 740 ),
                                diplexer741( 741 ),
                                diplexer742( 742 ),
                                diplexer743( 743 ),
                                diplexer744( 744 ),
                                diplexer745( 745 ),
                                diplexer746( 746 ),
                                diplexer747( 747 ),
                                diplexer748( 748 ),
                                diplexer749( 749 ),
                                diplexer750( 750 ),
                                diplexer751( 751 ),
                                diplexer752( 752 ),
                                diplexer753( 753 ),
                                diplexer754( 754 ),
                                diplexer755( 755 ),
                                diplexer756( 756 ),
                                diplexer757( 757 ),
                                diplexer758( 758 ),
                                diplexer759( 759 ),
                                diplexer760( 760 ),
                                diplexer761( 761 ),
                                diplexer762( 762 ),
                                diplexer763( 763 ),
                                diplexer764( 764 ),
                                diplexer765( 765 ),
                                diplexer766( 766 ),
                                diplexer767( 767 ),
                                diplexer768( 768 ),
                                diplexer769( 769 ),
                                diplexer770( 770 ),
                                diplexer771( 771 ),
                                diplexer772( 772 ),
                                diplexer773( 773 ),
                                diplexer774( 774 ),
                                diplexer775( 775 ),
                                diplexer776( 776 ),
                                diplexer777( 777 ),
                                diplexer778( 778 ),
                                diplexer779( 779 ),
                                diplexer780( 780 ),
                                diplexer781( 781 ),
                                diplexer782( 782 ),
                                diplexer783( 783 ),
                                diplexer784( 784 ),
                                diplexer785( 785 ),
                                diplexer786( 786 ),
                                diplexer787( 787 ),
                                diplexer788( 788 ),
                                diplexer789( 789 ),
                                diplexer790( 790 ),
                                diplexer791( 791 ),
                                diplexer792( 792 ),
                                diplexer793( 793 ),
                                diplexer794( 794 ),
                                diplexer795( 795 ),
                                diplexer796( 796 ),
                                diplexer797( 797 ),
                                diplexer798( 798 ),
                                diplexer799( 799 ),
                                diplexer800( 800 ),
                                diplexer801( 801 ),
                                diplexer802( 802 ),
                                diplexer803( 803 ),
                                diplexer804( 804 ),
                                diplexer805( 805 ),
                                diplexer806( 806 ),
                                diplexer807( 807 ),
                                diplexer808( 808 ),
                                diplexer809( 809 ),
                                diplexer810( 810 ),
                                diplexer811( 811 ),
                                diplexer812( 812 ),
                                diplexer813( 813 ),
                                diplexer814( 814 ),
                                diplexer815( 815 ),
                                diplexer816( 816 ),
                                diplexer817( 817 ),
                                diplexer818( 818 ),
                                diplexer819( 819 ),
                                diplexer820( 820 ),
                                diplexer821( 821 ),
                                diplexer822( 822 ),
                                diplexer823( 823 ),
                                diplexer824( 824 ),
                                diplexer825( 825 ),
                                diplexer826( 826 ),
                                diplexer827( 827 ),
                                diplexer828( 828 ),
                                diplexer829( 829 ),
                                diplexer830( 830 ),
                                diplexer831( 831 ),
                                diplexer832( 832 ),
                                diplexer833( 833 ),
                                diplexer834( 834 ),
                                diplexer835( 835 ),
                                diplexer836( 836 ),
                                diplexer837( 837 ),
                                diplexer838( 838 ),
                                diplexer839( 839 ),
                                diplexer840( 840 ),
                                diplexer841( 841 ),
                                diplexer842( 842 ),
                                diplexer843( 843 ),
                                diplexer844( 844 ),
                                diplexer845( 845 ),
                                diplexer846( 846 ),
                                diplexer847( 847 ),
                                diplexer848( 848 ),
                                diplexer849( 849 ),
                                diplexer850( 850 ),
                                diplexer851( 851 ),
                                diplexer852( 852 ),
                                diplexer853( 853 ),
                                diplexer854( 854 ),
                                diplexer855( 855 ),
                                diplexer856( 856 ),
                                diplexer857( 857 ),
                                diplexer858( 858 ),
                                diplexer859( 859 ),
                                diplexer860( 860 ),
                                diplexer861( 861 ),
                                diplexer862( 862 ),
                                diplexer863( 863 ),
                                diplexer864( 864 ),
                                diplexer865( 865 ),
                                diplexer866( 866 ),
                                diplexer867( 867 ),
                                diplexer868( 868 ),
                                diplexer869( 869 ),
                                diplexer870( 870 ),
                                diplexer871( 871 ),
                                diplexer872( 872 ),
                                diplexer875( 875 ),
                                diplexer876( 876 ),
                                diplexer877( 877 ),
                                diplexer878( 878 ),
                                diplexer879( 879 ),
                                diplexer880( 880 ),
                                diplexer881( 881 ),
                                diplexer882( 882 ),
                                diplexer883( 883 ),
                                diplexer884( 884 ),
                                diplexer885( 885 ),
                                diplexer886( 886 ),
                                diplexer887( 887 ),
                                diplexer888( 888 ),
                                diplexer889( 889 ),
                                diplexer890( 890 ),
                                diplexer891( 891 ),
                                diplexer892( 892 ),
                                diplexer893( 893 ),
                                diplexer894( 894 ),
                                diplexer895( 895 ),
                                diplexer896( 896 ),
                                diplexer897( 897 ),
                                diplexer898( 898 ),
                                diplexer899( 899 ),
                                diplexer900( 900 ),
                                diplexer901( 901 ),
                                diplexer902( 902 ),
                                diplexer903( 903 ),
                                diplexer904( 904 ),
                                diplexer905( 905 ),
                                diplexer906( 906 ),
                                diplexer907( 907 ),
                                diplexer908( 908 ),
                                diplexer909( 909 ),
                                diplexer910( 910 ),
                                diplexer911( 911 ),
                                diplexer912( 912 ),
                                diplexer913( 913 ),
                                diplexer914( 914 ),
                                diplexer915( 915 ),
                                diplexer916( 916 ),
                                diplexer917( 917 ),
                                diplexer918( 918 ),
                                diplexer919( 919 ),
                                diplexer920( 920 ),
                                other( 7 )
                            }

    DiplexerConfigT ::= TEXTUAL-CONVENTION
        STATUS              current
        DESCRIPTION         "These are diplexer configuration options for lower band radios, where
                             Unconfigured is not configurable."
        SYNTAX              INTEGER {
                                unconfigured( 0 ),
                                diplexer125( 125 ),
                                diplexer126( 126 ),
                                diplexer127( 127 ),
                                diplexer128( 128 ),
                                diplexer129( 129 ),
                                diplexer130( 130 ),
                                diplexer301( 301 ),
                                diplexer302( 302 ),
                                diplexer303( 303 ),
                                diplexer304( 304 ),
                                diplexer305( 305 ),
                                diplexer306( 306 ),
                                diplexer307( 307 ),
                                diplexer308( 308 ),
                                diplexer309( 309 ),
                                diplexer310( 310 ),
                                diplexer311( 311 ),
                                diplexer312( 312 ),
                                diplexer313( 313 ),
                                diplexer314( 314 ),
                                diplexer315( 315 ),
                                diplexer316( 316 ),
                                diplexer317( 317 ),
                                diplexer318( 318 ),
                                diplexer319( 319 ),
                                diplexer320( 320 ),
                                diplexer321( 321 ),
                                diplexer322( 322 ),
                                diplexer323( 323 ),
                                diplexer324( 324 ),
                                diplexer325( 325 ),
                                diplexer326( 326 ),
                                diplexer327( 327 ),
                                diplexer328( 328 ),
                                diplexer329( 329 ),
                                diplexer330( 330 ),
                                diplexer331( 331 ),
                                diplexer332( 332 ),
                                diplexer333( 333 ),
                                diplexer334( 334 ),
                                diplexer335( 335 ),
                                diplexer336( 336 ),
                                diplexer337( 337 ),
                                diplexer338( 338 ),
                                diplexer339( 339 ),
                                diplexer340( 340 ),
                                diplexer341( 341 ),
                                diplexer342( 342 ),
                                diplexer343( 343 ),
                                diplexer344( 344 ),
                                diplexer345( 345 ),
                                diplexer346( 346 ),
                                diplexer347( 347 ),
                                diplexer348( 348 ),
                                diplexer349( 349 ),
                                diplexer350( 350 ),
                                diplexer351( 351 ),
                                diplexer352( 352 ),
                                diplexer353( 353 ),
                                diplexer354( 354 ),
                                diplexer355( 355 ),
                                diplexer356( 356 ),
                                diplexer357( 357 ),
                                diplexer358( 358 ),
                                diplexer359( 359 ),
                                diplexer360( 360 ),
                                diplexer361( 361 ),
                                diplexer362( 362 ),
                                diplexer363( 363 ),
                                diplexer364( 364 ),
                                diplexer365( 365 ),
                                diplexer366( 366 ),
                                diplexer367( 367 ),
                                diplexer368( 368 ),
                                diplexer377( 377 ),
                                diplexer378( 378 ),
                                diplexer379( 379 ),
                                diplexer380( 380 ),
                                diplexer381( 381 ),
                                diplexer382( 382 ),
                                diplexer389( 389 ),
                                diplexer390( 390 ),
                                diplexer391( 391 ),
                                diplexer392( 392 ),
                                diplexer393( 393 ),
                                diplexer394( 394 ),
                                diplexer395( 395 ),
                                diplexer396( 396 ),
                                diplexer397( 397 ),
                                diplexer398( 398 ),
                                diplexer399( 399 ),
                                diplexer400( 400 ),
                                diplexer401( 401 ),
                                diplexer402( 402 ),
                                diplexer403( 403 ),
                                diplexer404( 404 ),
                                diplexer405( 405 ),
                                diplexer406( 406 ),
                                diplexer407( 407 ),
                                diplexer408( 408 ),
                                diplexer409( 409 ),
                                diplexer410( 410 ),
                                diplexer411( 411 ),
                                diplexer412( 412 ),
                                diplexer413( 413 ),
                                diplexer414( 414 ),
                                diplexer415( 415 ),
                                diplexer416( 416 ),
                                other( 7 )
                            }

    RadioCollocSyncSourceT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This is the source to get SYNC timing. Choices
                            are Off, GPS, or Internal"
        SYNTAX		    INTEGER {
                                syncOFF( 0 ),
                                syncGPS( 1 ),
                                syncINTERNAL( 2 )
                            }

    RadioCollocSyncTypeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This is the radio SYNC type. Choices
                            are SYNC_AUTO, SYNC_SRC, SYNC_SINK or SYNC_NONE"
        SYNTAX		    INTEGER {
                                syncAuto( 1 ),
                                syncSrc( 2 ),
                                syncSink( 3 ),
                                syncNone( 4 )
                            }

    VlanStatusT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This is definition for Vlan status. (Enabled or
                            disabled)"
        SYNTAX		    INTEGER {
                                disabled( 0 ),
                                enableBlkUtag( 1 ),
                                enablePassUtag( 2 ),
                                enableTagUtag( 3 ),
				enableMgmtOnly(4)
                            }

    VlanIdT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The VLAN ID"
        SYNTAX		    Integer32 (1..4095)

    VlanGroupT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the Valid options for vlan ethernet
                            interfaces."
        SYNTAX		    INTEGER {
                                groupNONE( 0 ),
                                groupETH2( 1 ),
                                groupPoE1( 2 ),
                                groupETH2andPoE1( 3 )
                            }


    TxOffsetT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable sets TX offset(in uSecs) of our radios
	                     to sync with other vendors' radios in a collocated configuration."
        SYNTAX		    Integer32 (0..5000)

    AntPortT ::= TEXTUAL-CONVENTION
        STATUS              current
        DESCRIPTION         "This MIB variable selects antenna port for ODU.
			     1- portA for 5rc, port H for 5r , ANT1 for 5rc Denali
			     2- portB for 5rc, portV for 5r, ANT2 for 5rc Denali;"

        SYNTAX              INTEGER {
                                portONE( 1 ),
                                portTWO( 2 )
                            }


    PolarizationT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable selects ODU polarizations for GigE radios.
				1 - selects portV for Veritical ,
                                2 - select portH for Horizontal ,
                                3- for Cross-Pole (V+H)"
        SYNTAX		    INTEGER {
                                portV( 1 ),
                                portH( 2 ),
                                portVplusH( 3 )
                            }


    RslPortT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable sets the RF channel for antenna alignment purpose."
        SYNTAX		    INTEGER {
                                portV(1),
                                portH(2)
			    }

    BuzTimeoutT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable selects ODU buzzer timeout period.
	                     1 - turns off buzzer,
			     2 - sets buzzer timeout period to 10 minutes,
			     3 - sets buzzer timeout period to 2 hours."
        SYNTAX		    INTEGER {
                                buzzerOFF( 0 ),
                                buzzerTenMinute( 1 ),
				buzzerTwoHour( 2 )
                            }

    FlyWheelT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable selects ODU fly wheeling timeout period.
	                    0 - fly wheel for 5 minutes, 1 - fly wheel forever."
        SYNTAX		    INTEGER {
                                fwNormal( 0 ),
                                fwInfinite( 1 )
                            }

    TxDcycleT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This sets Fixed Asymmetric Throughput in both tx and rx directions.
	                     User Options are: tx(20%)/rx(80%), 35/65, 50/50, 65/35 and 80/20."
        SYNTAX		    INTEGER {
                                t20r80( 1 ),
                                t35r65( 2 ),
                                t50r50( 3 ),
                                t65r35( 4 ),
                                t80r20( 5 )
                            }

    DfsEnableT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable sets DFS enable/disable option; this is option is
	                     availble for Radio A only."
        SYNTAX		    INTEGER {
	                        disable(0),
				enable(1)
			    }

    DfsAntGainT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable sets DFS antenna gain (in dB); available antenna
	                     gain ranges from 18 to 38 dB, with 1 dB step."
        SYNTAX		    Integer32 (18..38)

    QosTagT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable sets Qos 802.1p tag."
        SYNTAX		    INTEGER (0..7)

    QosQueueSizePercentT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Qos queue size in percentage"
        SYNTAX		    Integer32 (1..100)

    QosQueueSizeByteT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Qos queue size in bytes"
        SYNTAX		    Integer32 (1..2097152)

    QosTtlT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The Qos time to live in miliseconds in a queue"
        SYNTAX		    Integer32 (100..100000)

    V35StatusT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The V35 enabled status."
        SYNTAX		    INTEGER {
                                disabled( 0 ),
                                enabled( 1 )
                            }

    V35RateT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The V35 channel rate Depends upon the T1/E1 settings of V35 Interface
                             channel rate selction differs
                             To set V35_T1, T1 mode should be selected
                             To set V35_E1, E1 mode should be selected."
        SYNTAX		    INTEGER {

                                v35Rate56K  (0),
                                v35Rate64K  (1),
                                v35Rate128K (2),
                                v35Rate256K (3),
                                v35Rate384K (4),
                                v35Rate512K (5),
                                v35Rate768K (6),
                                v35Rate1024K(7),
                                v35Rate1544K(8 ),
                                v35Rate2048K(9 )
                            }

    V35ClockInversionT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The V35 clock inversion."
        SYNTAX		    INTEGER {
                           txNormalRxNormal(0),
                           txInvertRxNormal(1),
                           txNormalRxInvert(2),
                           txInvertRxInvert(3)
                         }

    V35TxClockT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The V35 transmit clock ."
        SYNTAX		    INTEGER {
                            rxClock(0),
                            externalTxClock(1),
                            internalGenerator(2)
                         }

    V35CTSHandshakeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The V35 CTS handshake."
        SYNTAX		    INTEGER {
                            handshakeAlwaysOn(0),
                            handshakeFollowRTS(1)
                         }

    V35LoopBackModeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the loopback modes of the V35 interfaces."
        SYNTAX		    INTEGER {
                                lbDisabled( 0 ),
                                lbExtLocal( 1 ),
                                lbExtRemote( 2 ),
                                lbInternal( 3 )
                            }

    DS3StatusT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The DS3 enabled status."
        SYNTAX		    INTEGER {
                                disabled( 0 ),
                                enabled( 1 )
                            }

    DS3LineCodeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "The DS3 Line Codes."
        SYNTAX		    INTEGER {
                                notAvail( -1 ),
                                b8zs( 0 ),
                                ami( 1 )
                            }


    DS3LoopBackModeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the loopback modes of the DS3 interfaces."
        SYNTAX		    INTEGER {
                                lbDisabled( 0 ),
                                lbExtLocal( 1 ),
                                lbExtRemote( 2 ),
                                lbInternal( 3 )
                            }

    ExaltEnableT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Enable / Disable parameter"
        SYNTAX		    INTEGER {
                                disable( 0 ),
                                enable( 1 )
                            }

    AcmPolicyT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "This MIB variable selects ACM Policy.
	                     0 - sets Conservative policy,
			     1 - sets Agressive policy."
        SYNTAX		    INTEGER {
                                conservative( 0 ),
                                agressive( 1 )
                            }

    AcmBaseModulationT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the supported ACM Base Modulation types."
        SYNTAX		    INTEGER {
                                mQPSK( 0 ),
                                m8PSK( 6 ),
                                m16QAM( 1 ),
                                m32QAM( 5 ),
                                m64QAM( 2 ),
                                m128QAM( 3 ),
                                m256QAM( 4 )
                            }

    AcmTargetModulationT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "These are the supported ACM Target Modulation types."
        SYNTAX		    INTEGER {
                                m16QAM( 1 ),
                                m32QAM( 5 ),
                                m64QAM( 2 ),
                                m128QAM( 3 ),
                                m256QAM( 4 ),
                                m512QAM( 6 )
                            }

    AcmModulationT ::= TEXTUAL-CONVENTION
        STATUS current
        DESCRIPTION "These are the supported ACM Modulation types. When ACM is disabled or when telemetry is down, the modulation is -9999."
        SYNTAX INTEGER {
           notAvailable( -9999 ),
           mQPSK( 0 ),
           m8PSK( 7 ),
           m16QAM( 1 ),
           m32QAM( 5 ),
           m64QAM( 2 ),
           m128QAM( 3 ),
           m256QAM( 4 ),
           m512QAM( 6 ),
           mBPSK2( 8 ),
           mQPSK2( 9 ),
           m8PSK2( 10 ),
           m16QAM2( 14 ),
           m32QAM2( 11 ),
           m64QAM2( 12 ),
           m128QAM2( 13 ),
           m512QAM2( 15 ),
           mQPSK3( 16 ),
           mBPSK3( 17 )
        }

    TcmModulationT ::= TEXTUAL-CONVENTION
        STATUS current
        DESCRIPTION "These are the supported TCM Modulation types. When ACM is enabled, the TCM modulation will be invalid."
        SYNTAX INTEGER {
           notAvailable( -9999 ),
           mQPSKThru( 0 ),
           m16QAMThru( 1 ),
	   m32QAMThru( 5 ),
           m64QAMThru( 2 ),
           m128QAMThru( 3 ),
           m256QAMThru( 4 ),

           mQPSKBase( 8 ),
           m16QAMBase( 9 ),
           m32QAMBase( 13 ),
           m64QAMBase( 10 ),
           m128QAMBase( 11 ),
           m256QAMBase( 12 ),

           mQPSKSysG( 16 ),
           m16QAMSysG( 17 ),
           m32QAMSysG( 21 ),
           m64QAMSysG( 18 ),
           m128QAMSysG( 19 ),
           m256QAMSysG( 20 )

        }

    AcmPowerBoostEnableT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "ACM Power Boost Enable / Disable parameter"
        SYNTAX		    INTEGER {
                                disable( 0 ),
                                enable( 1 )
                            }

    FileTransferTypeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Supported file transfer types."
        SYNTAX		    INTEGER {
                                uploadFirmware( 0 ),
                                uploadConfig( 1 ),
                                uploadRDD( 2 ),
                                downloadFirmware( 3 ),
                                downloadConfig( 4 ),
                                downloadMIBs( 5 ),
                                downloadEventLogs( 6 )
                            }

    FileTransferStartT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Supported file start/status types."
        SYNTAX		    INTEGER {
                                noAction( 0 ),
                                startTransfer( 1 )
                            }

    PerformanceModeT ::= TEXTUAL-CONVENTION
        STATUS current
        DESCRIPTION "These are the supported performance mode when TCM modulation is used."
        SYNTAX INTEGER {
           maxThroughputMinLatency( 0 ),
           maxSystemGain( 1 ),
           balancedPerformance( 2 )
        }

    NtpClientEnableT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Enable or disable NTP client"
        SYNTAX		    INTEGER {
                                disable( 0 ),
                                enableWith1Server( 1 ),
                                enableWith2Servers( 2 ),
                                enableWith3Servers( 3 ),
                                enableWith4Servers( 4 )
                            }

    TimeZoneT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Time Zone"
        SYNTAX		    INTEGER {
                                samoa( 0 ),
                                hawaii( 1 ),
                                alaska( 2 ),
                                pacific( 3 ),
                                arizona( 4 ),   
                                mountain( 5 ),  
                                central( 6 ),   
                                eastern( 7 ),   
                                east-Indiana( 8 ),
                                atlantic( 9 ),  
                                uTC( 10 ),       
                                london( 11 ),    
                                berlin( 12 ),    
                                belgrade( 13 ),  
                                paris( 14 ),     
                                cairo( 15 ),     
                                helsinki( 16 ),  
                                baghdad( 17 ),   
                                moscow( 18 ),    
                                tehran( 19 ),    
                                kabul( 20 ),     
                                karachi( 21 ),   
                                bangkok( 22 ),   
                                shanghai( 23 ),  
                                taipei( 24 ),    
                                tokyo( 25 ),     
                                seoul( 26 ),     
                                sydney( 27 ),    
                                vladivostok( 28)
                            }

    SyslogEnableT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Enable or disable Syslog logging to remote."
        SYNTAX		    INTEGER {
                                disabled( 0 ),
                                enabled( 1 )
                            }

    SyslogFilterSelectT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Select logging filter to control events to be sent to remote host"
        SYNTAX		    INTEGER {
                                all( 0 ),
                                minor( 1 ),
                                minorMajorCritical( 2 ),
                                major( 3 ),
                                majorCritical( 4 ),
			        critical(5)
                            }

    DualRadioXpicEnableT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Disable XPIC or enable XPIC with vertical/horizontal polarity"
        SYNTAX		    INTEGER {
                                disable( 0 ),
                                vertical( 1 ),
                                horizontal( 2 )
                            }

    MefEnableT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "Enable or disable MEF setting"
        SYNTAX		    INTEGER {
                                disabled( 0 ),
                                enabled( 1 )
                            }

    MefMasterModeT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "MEF Master Mode (Sync Ethernet)"
        SYNTAX		    INTEGER {
                                auto( 0 ),
                                forceMaster( 1 ),
                                forceSlave( 2 ),
                                notPresent( 3 )
                            }

    MefIrgT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "MEF IRG"
        SYNTAX		    INTEGER {
                                r64kbps( 0 ),
                                r128kbps( 1 ),
                                r256kbps( 2 ),
                                r512kbps( 3 ),
                                r1000kbps( 4 )
                            }

    MefMacT ::= TEXTUAL-CONVENTION
        STATUS		    current
        DESCRIPTION         "MEF MAC Block"
        SYNTAX		    INTEGER {
                                discard( 0 ),
                                forward( 1 )
                            }

    MefRateLimitTypeT ::= TEXTUAL-CONVENTION
        STATUS              current
        DESCRIPTION         "The MEF ethernet rate limit type in KBPS (or) MBPS."
        SYNTAX              INTEGER {
                                kbps( 0 ),
                                mbps( 1 )
                            }

    MefRateLimitValueT ::= TEXTUAL-CONVENTION
        STATUS              current
        DESCRIPTION         "The MEF ethernet rate limit, if the rate limit is enabled,
                             the value is applied on to the port.
                             eg., rate in KBPS (64..1792, stepsize 64)
                                  rate in MBPS (2..100, stepsize 1) and (104..1000, stepsize 8)"
        SYNTAX              Integer32

    exaltcommunications OBJECT IDENTIFIER ::= { enterprises 25651 }

END

