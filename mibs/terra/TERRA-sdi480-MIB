--File Name : sdi480-TERRA-MIB
--Date      : January 19 2017
--****************************************************
--sdi480-TERRA-MIB:     sdi480 Platform Specific MIB
--
--January 2017, <PERSON><PERSON><PERSON><PERSON>
--
--Copyright (c) 2017 by TERRA Electronics.
--All rights reserved.
--
--****************************************************

TERRA-sdi480-MIB DEFINITIONS ::= BEGIN
    IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Integer32,
    NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    DisplayString
        FROM SNMPv2-TC
    OBJECT-GROUP, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    DefStatus
        FROM TERRA-DEFINITIONS-MIB
    terraProducts
        FROM TERRA-PRODUCTS-MIB;

    terra-sdi480 MODULE-IDENTITY
        LAST-UPDATED        "201701190000Z"
        ORGANIZATION        "TERRA Electronics"
        CONTACT-INFO        "TERRA Electronics
                            
                            Draugystes 22
                            Kaunas
                            Lithuania
                            LT-51256
                            E-mail: <EMAIL>"
        DESCRIPTION         "This MIB provides Terra eight channel DVB-S/S2 to IP streamer sdi480 information."

        REVISION            "201701190900Z"
        DESCRIPTION         "Production version"
        ::= { terraProducts 17 }


    sdi480status OBJECT IDENTIFIER ::= { terra-sdi480 1 }

    rFinStatus1 OBJECT IDENTIFIER ::= { sdi480status 1 }

    inLock1 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus1 1 }

    inlevel1 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus1 2 }

    insnr1 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus1 3 }

    inbr1 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus1 4 }

    inper1 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus1 5 }

    rFinStatus2 OBJECT IDENTIFIER ::= { sdi480status 2 }

    inLock2 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus2 1 }

    inlevel2 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus2 2 }

    insnr2 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus2 3 }

    inbr2 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus2 4 }

    inper2 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus2 5 }

    rFinStatus3 OBJECT IDENTIFIER ::= { sdi480status 3 }

    inLock3 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus3 1 }

    inlevel3 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus3 2 }

    insnr3 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus3 3 }

    inbr3 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus3 4 }

    inper3 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus3 5 }

    rFinStatus4 OBJECT IDENTIFIER ::= { sdi480status 4 }

    inLock4 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus4 1 }

    inlevel4 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus4 2 }

    insnr4 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus4 3 }

    inbr4 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus4 4 }

    inper4 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus4 5 }

    rFinStatus5 OBJECT IDENTIFIER ::= { sdi480status 5 }

    inLock5 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus5 1 }

    inlevel5 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus5 2 }

    insnr5 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus5 3 }

    inbr5 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus5 4 }

    inper5 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus5 5 }

    rFinStatus6 OBJECT IDENTIFIER ::= { sdi480status 6 }

    inLock6 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus6 1 }

    inlevel6 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus6 2 }

    insnr6 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus6 3 }

    inbr6 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus6 4 }

    inper6 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus6 5 }

    rFinStatus7 OBJECT IDENTIFIER ::= { sdi480status 7 }

    inLock7 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus7 1 }

    inlevel7 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus7 2 }

    insnr7 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus7 3 }

    inbr7 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus7 4 }

    inper7 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus7 5 }

    rFinStatus8 OBJECT IDENTIFIER ::= { sdi480status 8 }

    inLock8 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Tuner lock"
        ::= { rFinStatus8 1 }

    inlevel8 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of dbuV"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal level in tenth of dbuV"
        ::= { rFinStatus8 2 }

    insnr8 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of db"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal SNR in tenth of db"
        ::= { rFinStatus8 3 }

    inbr8 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal bitrate in tenth of Mbps"
        ::= { rFinStatus8 4 }

    inper8 OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input signal packet error ratio"
        ::= { rFinStatus8 5 }

    usbStatus OBJECT IDENTIFIER ::= { sdi480status 9 }

    usbinBR OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "USB input bitrate in tenth of Mbps"
        ::= { usbStatus 1 }

    outStream1 OBJECT IDENTIFIER ::= { sdi480status 10 }

    outBr1 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream1 1 }

    outStream2 OBJECT IDENTIFIER ::= { sdi480status 11 }

    outBr2 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream2 1 }

    outStream3 OBJECT IDENTIFIER ::= { sdi480status 12 }

    outBr3 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream3 1 }

    outStream4 OBJECT IDENTIFIER ::= { sdi480status 13 }

    outBr4 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream4 1 }

    outStream5 OBJECT IDENTIFIER ::= { sdi480status 14 }

    outBr5 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream5 1 }

    outStream6 OBJECT IDENTIFIER ::= { sdi480status 15 }

    outBr6 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream6 1 }

    outStream7 OBJECT IDENTIFIER ::= { sdi480status 16 }

    outBr7 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream7 1 }

    outStream8 OBJECT IDENTIFIER ::= { sdi480status 17 }

    outBr8 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream8 1 }

    outStream9 OBJECT IDENTIFIER ::= { sdi480status 18 }

    outBr9 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream9 1 }

    outStream10 OBJECT IDENTIFIER ::= { sdi480status 19 }

    outBr10 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream10 1 }

    outStream11 OBJECT IDENTIFIER ::= { sdi480status 20 }

    outBr11 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream11 1 }

    outStream12 OBJECT IDENTIFIER ::= { sdi480status 21 }

    outBr12 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream12 1 }

    outStream13 OBJECT IDENTIFIER ::= { sdi480status 22 }

    outBr13 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream13 1 }

    outStream14 OBJECT IDENTIFIER ::= { sdi480status 23 }

    outBr14 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream14 1 }

    outStream15 OBJECT IDENTIFIER ::= { sdi480status 24 }

    outBr15 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream15 1 }

    outStream16 OBJECT IDENTIFIER ::= { sdi480status 25 }

    outBr16 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream16 1 }

    outStream17 OBJECT IDENTIFIER ::= { sdi480status 26 }

    outBr17 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream17 1 }

    outStream18 OBJECT IDENTIFIER ::= { sdi480status 27 }

    outBr18 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream18 1 }

    outStream19 OBJECT IDENTIFIER ::= { sdi480status 28 }

    outBr19 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream19 1 }

    outStream20 OBJECT IDENTIFIER ::= { sdi480status 29 }

    outBr20 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream20 1 }

    outStream21 OBJECT IDENTIFIER ::= { sdi480status 30 }

    outBr21 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream21 1 }

    outStream22 OBJECT IDENTIFIER ::= { sdi480status 31 }

    outBr22 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream22 1 }

    outStream23 OBJECT IDENTIFIER ::= { sdi480status 32 }

    outBr23 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream23 1 }

    outStream24 OBJECT IDENTIFIER ::= { sdi480status 33 }

    outBr24 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream24 1 }

    outStream25 OBJECT IDENTIFIER ::= { sdi480status 34 }

    outBr25 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream25 1 }

    outStream26 OBJECT IDENTIFIER ::= { sdi480status 35 }

    outBr26 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream26 1 }

    outStream27 OBJECT IDENTIFIER ::= { sdi480status 36 }

    outBr27 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream27 1 }

    outStream28 OBJECT IDENTIFIER ::= { sdi480status 37 }

    outBr28 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream28 1 }

    outStream29 OBJECT IDENTIFIER ::= { sdi480status 38 }

    outBr29 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream29 1 }

    outStream30 OBJECT IDENTIFIER ::= { sdi480status 39 }

    outBr30 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream30 1 }

    outStream31 OBJECT IDENTIFIER ::= { sdi480status 40 }

    outBr31 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream31 1 }

    outStream32 OBJECT IDENTIFIER ::= { sdi480status 41 }

    outBr32 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream32 1 }

    outStream33 OBJECT IDENTIFIER ::= { sdi480status 42 }

    outBr33 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream33 1 }

    outStream34 OBJECT IDENTIFIER ::= { sdi480status 43 }

    outBr34 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream34 1 }

    outStream35 OBJECT IDENTIFIER ::= { sdi480status 44 }

    outBr35 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream35 1 }

    outStream36 OBJECT IDENTIFIER ::= { sdi480status 45 }

    outBr36 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream36 1 }

    outStream37 OBJECT IDENTIFIER ::= { sdi480status 46 }

    outBr37 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream37 1 }

    outStream38 OBJECT IDENTIFIER ::= { sdi480status 47 }

    outBr38 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream38 1 }

    outStream39 OBJECT IDENTIFIER ::= { sdi480status 48 }

    outBr39 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream39 1 }

    outStream40 OBJECT IDENTIFIER ::= { sdi480status 49 }

    outBr40 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream40 1 }

    outStream41 OBJECT IDENTIFIER ::= { sdi480status 50 }

    outBr41 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream41 1 }

    outStream42 OBJECT IDENTIFIER ::= { sdi480status 51 }

    outBr42 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream42 1 }

    outStream43 OBJECT IDENTIFIER ::= { sdi480status 52 }

    outBr43 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream43 1 }

    outStream44 OBJECT IDENTIFIER ::= { sdi480status 53 }

    outBr44 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream44 1 }

    outStream45 OBJECT IDENTIFIER ::= { sdi480status 54 }

    outBr45 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream45 1 }

    outStream46 OBJECT IDENTIFIER ::= { sdi480status 55 }

    outBr46 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream46 1 }

    outStream47 OBJECT IDENTIFIER ::= { sdi480status 56 }

    outBr47 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream47 1 }

    outStream48 OBJECT IDENTIFIER ::= { sdi480status 57 }

    outBr48 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream48 1 }

    outStream49 OBJECT IDENTIFIER ::= { sdi480status 58 }

    outBr49 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream49 1 }

    outStream50 OBJECT IDENTIFIER ::= { sdi480status 59 }

    outBr50 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream50 1 }

    outStream51 OBJECT IDENTIFIER ::= { sdi480status 60 }

    outBr51 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream51 1 }

    outStream52 OBJECT IDENTIFIER ::= { sdi480status 61 }

    outBr52 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream52 1 }

    outStream53 OBJECT IDENTIFIER ::= { sdi480status 62 }

    outBr53 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream53 1 }

    outStream54 OBJECT IDENTIFIER ::= { sdi480status 63 }

    outBr54 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream54 1 }

    outStream55 OBJECT IDENTIFIER ::= { sdi480status 64 }

    outBr55 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream55 1 }

    outStream56 OBJECT IDENTIFIER ::= { sdi480status 65 }

    outBr56 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream56 1 }

    outStream57 OBJECT IDENTIFIER ::= { sdi480status 66 }

    outBr57 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream57 1 }

    outStream58 OBJECT IDENTIFIER ::= { sdi480status 67 }

    outBr58 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream58 1 }

    outStream59 OBJECT IDENTIFIER ::= { sdi480status 68 }

    outBr59 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream59 1 }

    outStream60 OBJECT IDENTIFIER ::= { sdi480status 69 }

    outBr60 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream60 1 }

    outStream61 OBJECT IDENTIFIER ::= { sdi480status 70 }

    outBr61 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream61 1 }

    outStream62 OBJECT IDENTIFIER ::= { sdi480status 71 }

    outBr62 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream62 1 }

    outStream63 OBJECT IDENTIFIER ::= { sdi480status 72 }

    outBr63 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream63 1 }

    outStream64 OBJECT IDENTIFIER ::= { sdi480status 73 }

    outBr64 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream64 1 }

    outStream65 OBJECT IDENTIFIER ::= { sdi480status 74 }

    outBr65 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream65 1 }

    outStream66 OBJECT IDENTIFIER ::= { sdi480status 75 }

    outBr66 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream66 1 }

    outStream67 OBJECT IDENTIFIER ::= { sdi480status 76 }

    outBr67 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream67 1 }

    outStream68 OBJECT IDENTIFIER ::= { sdi480status 77 }

    outBr68 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream68 1 }

    outStream69 OBJECT IDENTIFIER ::= { sdi480status 78 }

    outBr69 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream69 1 }

    outStream70 OBJECT IDENTIFIER ::= { sdi480status 79 }

    outBr70 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream70 1 }

    outStream71 OBJECT IDENTIFIER ::= { sdi480status 80 }

    outBr71 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream71 1 }

    outStream72 OBJECT IDENTIFIER ::= { sdi480status 81 }

    outBr72 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream72 1 }

    outStream73 OBJECT IDENTIFIER ::= { sdi480status 82 }

    outBr73 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream73 1 }

    outStream74 OBJECT IDENTIFIER ::= { sdi480status 83 }

    outBr74 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream74 1 }

    outStream75 OBJECT IDENTIFIER ::= { sdi480status 84 }

    outBr75 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream75 1 }

    outStream76 OBJECT IDENTIFIER ::= { sdi480status 85 }

    outBr76 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream76 1 }

    outStream77 OBJECT IDENTIFIER ::= { sdi480status 86 }

    outBr77 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream77 1 }

    outStream78 OBJECT IDENTIFIER ::= { sdi480status 87 }

    outBr78 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream78 1 }

    outStream79 OBJECT IDENTIFIER ::= { sdi480status 88 }

    outBr79 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream79 1 }

    outStream80 OBJECT IDENTIFIER ::= { sdi480status 89 }

    outBr80 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream80 1 }

    outStream81 OBJECT IDENTIFIER ::= { sdi480status 90 }

    outBr81 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream81 1 }

    outStream82 OBJECT IDENTIFIER ::= { sdi480status 91 }

    outBr82 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream82 1 }

    outStream83 OBJECT IDENTIFIER ::= { sdi480status 92 }

    outBr83 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream83 1 }

    outStream84 OBJECT IDENTIFIER ::= { sdi480status 93 }

    outBr84 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream84 1 }

    outStream85 OBJECT IDENTIFIER ::= { sdi480status 94 }

    outBr85 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream85 1 }

    outStream86 OBJECT IDENTIFIER ::= { sdi480status 95 }

    outBr86 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream86 1 }

    outStream87 OBJECT IDENTIFIER ::= { sdi480status 96 }

    outBr87 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream87 1 }

    outStream88 OBJECT IDENTIFIER ::= { sdi480status 97 }

    outBr88 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream88 1 }

    outStream89 OBJECT IDENTIFIER ::= { sdi480status 98 }

    outBr89 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream89 1 }

    outStream90 OBJECT IDENTIFIER ::= { sdi480status 99 }

    outBr90 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream90 1 }

    outStream91 OBJECT IDENTIFIER ::= { sdi480status 100 }

    outBr91 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream91 1 }

    outStream92 OBJECT IDENTIFIER ::= { sdi480status 101 }

    outBr92 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream92 1 }

    outStream93 OBJECT IDENTIFIER ::= { sdi480status 102 }

    outBr93 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream93 1 }

    outStream94 OBJECT IDENTIFIER ::= { sdi480status 103 }

    outBr94 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream94 1 }

    outStream95 OBJECT IDENTIFIER ::= { sdi480status 104 }

    outBr95 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream95 1 }

    outStream96 OBJECT IDENTIFIER ::= { sdi480status 105 }

    outBr96 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream96 1 }

    outStream97 OBJECT IDENTIFIER ::= { sdi480status 106 }

    outBr97 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream97 1 }

    outStream98 OBJECT IDENTIFIER ::= { sdi480status 107 }

    outBr98 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream98 1 }

    outStream99 OBJECT IDENTIFIER ::= { sdi480status 108 }

    outBr99 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream99 1 }

    outStream100 OBJECT IDENTIFIER ::= { sdi480status 109 }

    outBr100 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream100 1 }

    outStream101 OBJECT IDENTIFIER ::= { sdi480status 110 }

    outBr101 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream101 1 }

    outStream102 OBJECT IDENTIFIER ::= { sdi480status 111 }

    outBr102 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream102 1 }

    outStream103 OBJECT IDENTIFIER ::= { sdi480status 112 }

    outBr103 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream103 1 }

    outStream104 OBJECT IDENTIFIER ::= { sdi480status 113 }

    outBr104 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream104 1 }

    outStream105 OBJECT IDENTIFIER ::= { sdi480status 114 }

    outBr105 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream105 1 }

    outStream106 OBJECT IDENTIFIER ::= { sdi480status 115 }

    outBr106 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream106 1 }

    outStream107 OBJECT IDENTIFIER ::= { sdi480status 116 }

    outBr107 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream107 1 }

    outStream108 OBJECT IDENTIFIER ::= { sdi480status 117 }

    outBr108 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream108 1 }

    outStream109 OBJECT IDENTIFIER ::= { sdi480status 118 }

    outBr109 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream109 1 }

    outStream110 OBJECT IDENTIFIER ::= { sdi480status 119 }

    outBr110 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream110 1 }

    outStream111 OBJECT IDENTIFIER ::= { sdi480status 120 }

    outBr111 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream111 1 }

    outStream112 OBJECT IDENTIFIER ::= { sdi480status 121 }

    outBr112 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream112 1 }

    outStream113 OBJECT IDENTIFIER ::= { sdi480status 122 }

    outBr113 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream113 1 }

    outStream114 OBJECT IDENTIFIER ::= { sdi480status 123 }

    outBr114 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream114 1 }

    outStream115 OBJECT IDENTIFIER ::= { sdi480status 124 }

    outBr115 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream115 1 }

    outStream116 OBJECT IDENTIFIER ::= { sdi480status 125 }

    outBr116 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream116 1 }

    outStream117 OBJECT IDENTIFIER ::= { sdi480status 126 }

    outBr117 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream117 1 }

    outStream118 OBJECT IDENTIFIER ::= { sdi480status 127 }

    outBr118 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream118 1 }

    outStream119 OBJECT IDENTIFIER ::= { sdi480status 128 }

    outBr119 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream119 1 }

    outStream120 OBJECT IDENTIFIER ::= { sdi480status 129 }

    outBr120 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream120 1 }

    outStream121 OBJECT IDENTIFIER ::= { sdi480status 130 }

    outBr121 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream121 1 }

    outStream122 OBJECT IDENTIFIER ::= { sdi480status 131 }

    outBr122 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream122 1 }

    outStream123 OBJECT IDENTIFIER ::= { sdi480status 132 }

    outBr123 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream123 1 }

    outStream124 OBJECT IDENTIFIER ::= { sdi480status 133 }

    outBr124 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream124 1 }

    outStream125 OBJECT IDENTIFIER ::= { sdi480status 134 }

    outBr125 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream125 1 }

    outStream126 OBJECT IDENTIFIER ::= { sdi480status 135 }

    outBr126 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream126 1 }

    outStream127 OBJECT IDENTIFIER ::= { sdi480status 136 }

    outBr127 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream127 1 }

    outStream128 OBJECT IDENTIFIER ::= { sdi480status 137 }

    outBr128 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream128 1 }

    outStream129 OBJECT IDENTIFIER ::= { sdi480status 138 }

    outBr129 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream129 1 }

    outStream130 OBJECT IDENTIFIER ::= { sdi480status 139 }

    outBr130 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream130 1 }

    outStream131 OBJECT IDENTIFIER ::= { sdi480status 140 }

    outBr131 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream131 1 }

    outStream132 OBJECT IDENTIFIER ::= { sdi480status 141 }

    outBr132 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream132 1 }

    outStream133 OBJECT IDENTIFIER ::= { sdi480status 142 }

    outBr133 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream133 1 }

    outStream134 OBJECT IDENTIFIER ::= { sdi480status 143 }

    outBr134 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream134 1 }

    outStream135 OBJECT IDENTIFIER ::= { sdi480status 144 }

    outBr135 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream135 1 }

    outStream136 OBJECT IDENTIFIER ::= { sdi480status 145 }

    outBr136 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream136 1 }

    outStream137 OBJECT IDENTIFIER ::= { sdi480status 146 }

    outBr137 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream137 1 }

    outStream138 OBJECT IDENTIFIER ::= { sdi480status 147 }

    outBr138 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream138 1 }

    outStream139 OBJECT IDENTIFIER ::= { sdi480status 148 }

    outBr139 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream139 1 }

    outStream140 OBJECT IDENTIFIER ::= { sdi480status 149 }

    outBr140 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream140 1 }

    outStream141 OBJECT IDENTIFIER ::= { sdi480status 150 }

    outBr141 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream141 1 }

    outStream142 OBJECT IDENTIFIER ::= { sdi480status 151 }

    outBr142 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream142 1 }

    outStream143 OBJECT IDENTIFIER ::= { sdi480status 152 }

    outBr143 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream143 1 }

    outStream144 OBJECT IDENTIFIER ::= { sdi480status 153 }

    outBr144 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream144 1 }

    outStream145 OBJECT IDENTIFIER ::= { sdi480status 154 }

    outBr145 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream145 1 }

    outStream146 OBJECT IDENTIFIER ::= { sdi480status 155 }

    outBr146 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream146 1 }

    outStream147 OBJECT IDENTIFIER ::= { sdi480status 156 }

    outBr147 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream147 1 }

    outStream148 OBJECT IDENTIFIER ::= { sdi480status 157 }

    outBr148 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream148 1 }

    outStream149 OBJECT IDENTIFIER ::= { sdi480status 158 }

    outBr149 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream149 1 }

    outStream150 OBJECT IDENTIFIER ::= { sdi480status 159 }

    outBr150 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream150 1 }

    outStream151 OBJECT IDENTIFIER ::= { sdi480status 160 }

    outBr151 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream151 1 }

    outStream152 OBJECT IDENTIFIER ::= { sdi480status 161 }

    outBr152 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream152 1 }

    outStream153 OBJECT IDENTIFIER ::= { sdi480status 162 }

    outBr153 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream153 1 }

    outStream154 OBJECT IDENTIFIER ::= { sdi480status 163 }

    outBr154 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream154 1 }

    outStream155 OBJECT IDENTIFIER ::= { sdi480status 164 }

    outBr155 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream155 1 }

    outStream156 OBJECT IDENTIFIER ::= { sdi480status 165 }

    outBr156 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream156 1 }

    outStream157 OBJECT IDENTIFIER ::= { sdi480status 166 }

    outBr157 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream157 1 }

    outStream158 OBJECT IDENTIFIER ::= { sdi480status 167 }

    outBr158 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream158 1 }

    outStream159 OBJECT IDENTIFIER ::= { sdi480status 168 }

    outBr159 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream159 1 }

    outStream160 OBJECT IDENTIFIER ::= { sdi480status 169 }

    outBr160 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream160 1 }

    outStream161 OBJECT IDENTIFIER ::= { sdi480status 170 }

    outBr161 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream161 1 }

    outStream162 OBJECT IDENTIFIER ::= { sdi480status 171 }

    outBr162 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream162 1 }

    outStream163 OBJECT IDENTIFIER ::= { sdi480status 172 }

    outBr163 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream163 1 }

    outStream164 OBJECT IDENTIFIER ::= { sdi480status 173 }

    outBr164 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream164 1 }

    outStream165 OBJECT IDENTIFIER ::= { sdi480status 174 }

    outBr165 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream165 1 }

    outStream166 OBJECT IDENTIFIER ::= { sdi480status 175 }

    outBr166 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream166 1 }

    outStream167 OBJECT IDENTIFIER ::= { sdi480status 176 }

    outBr167 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream167 1 }

    outStream168 OBJECT IDENTIFIER ::= { sdi480status 177 }

    outBr168 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream168 1 }

    outStream169 OBJECT IDENTIFIER ::= { sdi480status 178 }

    outBr169 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream169 1 }

    outStream170 OBJECT IDENTIFIER ::= { sdi480status 179 }

    outBr170 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream170 1 }

    outStream171 OBJECT IDENTIFIER ::= { sdi480status 180 }

    outBr171 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream171 1 }

    outStream172 OBJECT IDENTIFIER ::= { sdi480status 181 }

    outBr172 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream172 1 }

    outStream173 OBJECT IDENTIFIER ::= { sdi480status 182 }

    outBr173 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream173 1 }

    outStream174 OBJECT IDENTIFIER ::= { sdi480status 183 }

    outBr174 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream174 1 }

    outStream175 OBJECT IDENTIFIER ::= { sdi480status 184 }

    outBr175 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream175 1 }

    outStream176 OBJECT IDENTIFIER ::= { sdi480status 185 }

    outBr176 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream176 1 }

    outStream177 OBJECT IDENTIFIER ::= { sdi480status 186 }

    outBr177 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream177 1 }

    outStream178 OBJECT IDENTIFIER ::= { sdi480status 187 }

    outBr178 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream178 1 }

    outStream179 OBJECT IDENTIFIER ::= { sdi480status 188 }

    outBr179 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream179 1 }

    outStream180 OBJECT IDENTIFIER ::= { sdi480status 189 }

    outBr180 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream180 1 }

    outStream181 OBJECT IDENTIFIER ::= { sdi480status 190 }

    outBr181 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream181 1 }

    outStream182 OBJECT IDENTIFIER ::= { sdi480status 191 }

    outBr182 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream182 1 }

    outStream183 OBJECT IDENTIFIER ::= { sdi480status 192 }

    outBr183 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream183 1 }

    outStream184 OBJECT IDENTIFIER ::= { sdi480status 193 }

    outBr184 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream184 1 }

    outStream185 OBJECT IDENTIFIER ::= { sdi480status 194 }

    outBr185 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream185 1 }

    outStream186 OBJECT IDENTIFIER ::= { sdi480status 195 }

    outBr186 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream186 1 }

    outStream187 OBJECT IDENTIFIER ::= { sdi480status 196 }

    outBr187 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream187 1 }

    outStream188 OBJECT IDENTIFIER ::= { sdi480status 197 }

    outBr188 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream188 1 }

    outStream189 OBJECT IDENTIFIER ::= { sdi480status 198 }

    outBr189 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream189 1 }

    outStream190 OBJECT IDENTIFIER ::= { sdi480status 199 }

    outBr190 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream190 1 }

    outStream191 OBJECT IDENTIFIER ::= { sdi480status 200 }

    outBr191 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream191 1 }

    outStream192 OBJECT IDENTIFIER ::= { sdi480status 201 }

    outBr192 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream192 1 }

    outStream193 OBJECT IDENTIFIER ::= { sdi480status 202 }

    outBr193 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream193 1 }

    outStream194 OBJECT IDENTIFIER ::= { sdi480status 203 }

    outBr194 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream194 1 }

    outStream195 OBJECT IDENTIFIER ::= { sdi480status 204 }

    outBr195 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream195 1 }

    outStream196 OBJECT IDENTIFIER ::= { sdi480status 205 }

    outBr196 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream196 1 }

    outStream197 OBJECT IDENTIFIER ::= { sdi480status 206 }

    outBr197 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream197 1 }

    outStream198 OBJECT IDENTIFIER ::= { sdi480status 207 }

    outBr198 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream198 1 }

    outStream199 OBJECT IDENTIFIER ::= { sdi480status 208 }

    outBr199 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream199 1 }

    outStream200 OBJECT IDENTIFIER ::= { sdi480status 209 }

    outBr200 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream200 1 }

    outStream201 OBJECT IDENTIFIER ::= { sdi480status 210 }

    outBr201 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream201 1 }

    outStream202 OBJECT IDENTIFIER ::= { sdi480status 211 }

    outBr202 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream202 1 }

    outStream203 OBJECT IDENTIFIER ::= { sdi480status 212 }

    outBr203 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream203 1 }

    outStream204 OBJECT IDENTIFIER ::= { sdi480status 213 }

    outBr204 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream204 1 }

    outStream205 OBJECT IDENTIFIER ::= { sdi480status 214 }

    outBr205 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream205 1 }

    outStream206 OBJECT IDENTIFIER ::= { sdi480status 215 }

    outBr206 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream206 1 }

    outStream207 OBJECT IDENTIFIER ::= { sdi480status 216 }

    outBr207 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream207 1 }

    outStream208 OBJECT IDENTIFIER ::= { sdi480status 217 }

    outBr208 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream208 1 }

    outStream209 OBJECT IDENTIFIER ::= { sdi480status 218 }

    outBr209 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream209 1 }

    outStream210 OBJECT IDENTIFIER ::= { sdi480status 219 }

    outBr210 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream210 1 }

    outStream211 OBJECT IDENTIFIER ::= { sdi480status 220 }

    outBr211 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream211 1 }

    outStream212 OBJECT IDENTIFIER ::= { sdi480status 221 }

    outBr212 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream212 1 }

    outStream213 OBJECT IDENTIFIER ::= { sdi480status 222 }

    outBr213 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream213 1 }

    outStream214 OBJECT IDENTIFIER ::= { sdi480status 223 }

    outBr214 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream214 1 }

    outStream215 OBJECT IDENTIFIER ::= { sdi480status 224 }

    outBr215 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream215 1 }

    outStream216 OBJECT IDENTIFIER ::= { sdi480status 225 }

    outBr216 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream216 1 }

    outStream217 OBJECT IDENTIFIER ::= { sdi480status 226 }

    outBr217 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream217 1 }

    outStream218 OBJECT IDENTIFIER ::= { sdi480status 227 }

    outBr218 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream218 1 }

    outStream219 OBJECT IDENTIFIER ::= { sdi480status 228 }

    outBr219 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream219 1 }

    outStream220 OBJECT IDENTIFIER ::= { sdi480status 229 }

    outBr220 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream220 1 }

    outStream221 OBJECT IDENTIFIER ::= { sdi480status 230 }

    outBr221 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream221 1 }

    outStream222 OBJECT IDENTIFIER ::= { sdi480status 231 }

    outBr222 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream222 1 }

    outStream223 OBJECT IDENTIFIER ::= { sdi480status 232 }

    outBr223 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream223 1 }

    outStream224 OBJECT IDENTIFIER ::= { sdi480status 233 }

    outBr224 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream224 1 }

    outStream225 OBJECT IDENTIFIER ::= { sdi480status 234 }

    outBr225 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream225 1 }

    outStream226 OBJECT IDENTIFIER ::= { sdi480status 235 }

    outBr226 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream226 1 }

    outStream227 OBJECT IDENTIFIER ::= { sdi480status 236 }

    outBr227 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream227 1 }

    outStream228 OBJECT IDENTIFIER ::= { sdi480status 237 }

    outBr228 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream228 1 }

    outStream229 OBJECT IDENTIFIER ::= { sdi480status 238 }

    outBr229 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream229 1 }

    outStream230 OBJECT IDENTIFIER ::= { sdi480status 239 }

    outBr230 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream230 1 }

    outStream231 OBJECT IDENTIFIER ::= { sdi480status 240 }

    outBr231 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream231 1 }

    outStream232 OBJECT IDENTIFIER ::= { sdi480status 241 }

    outBr232 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream232 1 }

    outStream233 OBJECT IDENTIFIER ::= { sdi480status 242 }

    outBr233 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream233 1 }

    outStream234 OBJECT IDENTIFIER ::= { sdi480status 243 }

    outBr234 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream234 1 }

    outStream235 OBJECT IDENTIFIER ::= { sdi480status 244 }

    outBr235 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream235 1 }

    outStream236 OBJECT IDENTIFIER ::= { sdi480status 245 }

    outBr236 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream236 1 }

    outStream237 OBJECT IDENTIFIER ::= { sdi480status 246 }

    outBr237 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream237 1 }

    outStream238 OBJECT IDENTIFIER ::= { sdi480status 247 }

    outBr238 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream238 1 }

    outStream239 OBJECT IDENTIFIER ::= { sdi480status 248 }

    outBr239 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream239 1 }

    outStream240 OBJECT IDENTIFIER ::= { sdi480status 249 }

    outBr240 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream240 1 }

    outStream241 OBJECT IDENTIFIER ::= { sdi480status 250 }

    outBr241 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream241 1 }

    outStream242 OBJECT IDENTIFIER ::= { sdi480status 251 }

    outBr242 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream242 1 }

    outStream243 OBJECT IDENTIFIER ::= { sdi480status 252 }

    outBr243 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream243 1 }

    outStream244 OBJECT IDENTIFIER ::= { sdi480status 253 }

    outBr244 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream244 1 }

    outStream245 OBJECT IDENTIFIER ::= { sdi480status 254 }

    outBr245 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream245 1 }

    outStream246 OBJECT IDENTIFIER ::= { sdi480status 255 }

    outBr246 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream246 1 }

    outStream247 OBJECT IDENTIFIER ::= { sdi480status 256 }

    outBr247 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream247 1 }

    outStream248 OBJECT IDENTIFIER ::= { sdi480status 257 }

    outBr248 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream248 1 }

    outStream249 OBJECT IDENTIFIER ::= { sdi480status 258 }

    outBr249 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream249 1 }

    outStream250 OBJECT IDENTIFIER ::= { sdi480status 259 }

    outBr250 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream250 1 }

    outStream251 OBJECT IDENTIFIER ::= { sdi480status 260 }

    outBr251 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream251 1 }

    outStream252 OBJECT IDENTIFIER ::= { sdi480status 261 }

    outBr252 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream252 1 }

    outStream253 OBJECT IDENTIFIER ::= { sdi480status 262 }

    outBr253 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream253 1 }

    outStream254 OBJECT IDENTIFIER ::= { sdi480status 263 }

    outBr254 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream254 1 }

    outStream255 OBJECT IDENTIFIER ::= { sdi480status 264 }

    outBr255 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream255 1 }

    outStream256 OBJECT IDENTIFIER ::= { sdi480status 265 }

    outBr256 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream256 1 }

    outStream257 OBJECT IDENTIFIER ::= { sdi480status 266 }

    outBr257 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream257 1 }

    outStream258 OBJECT IDENTIFIER ::= { sdi480status 267 }

    outBr258 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream258 1 }

    outStream259 OBJECT IDENTIFIER ::= { sdi480status 268 }

    outBr259 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream259 1 }

    outStream260 OBJECT IDENTIFIER ::= { sdi480status 269 }

    outBr260 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream260 1 }

    outStream261 OBJECT IDENTIFIER ::= { sdi480status 270 }

    outBr261 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream261 1 }

    outStream262 OBJECT IDENTIFIER ::= { sdi480status 271 }

    outBr262 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream262 1 }

    outStream263 OBJECT IDENTIFIER ::= { sdi480status 272 }

    outBr263 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream263 1 }

    outStream264 OBJECT IDENTIFIER ::= { sdi480status 273 }

    outBr264 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream264 1 }

    outStream265 OBJECT IDENTIFIER ::= { sdi480status 274 }

    outBr265 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream265 1 }

    outStream266 OBJECT IDENTIFIER ::= { sdi480status 275 }

    outBr266 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream266 1 }

    outStream267 OBJECT IDENTIFIER ::= { sdi480status 276 }

    outBr267 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream267 1 }

    outStream268 OBJECT IDENTIFIER ::= { sdi480status 277 }

    outBr268 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream268 1 }

    outStream269 OBJECT IDENTIFIER ::= { sdi480status 278 }

    outBr269 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream269 1 }

    outStream270 OBJECT IDENTIFIER ::= { sdi480status 279 }

    outBr270 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream270 1 }

    outStream271 OBJECT IDENTIFIER ::= { sdi480status 280 }

    outBr271 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream271 1 }

    outStream272 OBJECT IDENTIFIER ::= { sdi480status 281 }

    outBr272 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream272 1 }

    outStream273 OBJECT IDENTIFIER ::= { sdi480status 282 }

    outBr273 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream273 1 }

    outStream274 OBJECT IDENTIFIER ::= { sdi480status 283 }

    outBr274 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream274 1 }

    outStream275 OBJECT IDENTIFIER ::= { sdi480status 284 }

    outBr275 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream275 1 }

    outStream276 OBJECT IDENTIFIER ::= { sdi480status 285 }

    outBr276 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream276 1 }

    outStream277 OBJECT IDENTIFIER ::= { sdi480status 286 }

    outBr277 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream277 1 }

    outStream278 OBJECT IDENTIFIER ::= { sdi480status 287 }

    outBr278 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream278 1 }

    outStream279 OBJECT IDENTIFIER ::= { sdi480status 288 }

    outBr279 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream279 1 }

    outStream280 OBJECT IDENTIFIER ::= { sdi480status 289 }

    outBr280 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream280 1 }

    outStream281 OBJECT IDENTIFIER ::= { sdi480status 290 }

    outBr281 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream281 1 }

    outStream282 OBJECT IDENTIFIER ::= { sdi480status 291 }

    outBr282 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream282 1 }

    outStream283 OBJECT IDENTIFIER ::= { sdi480status 292 }

    outBr283 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream283 1 }

    outStream284 OBJECT IDENTIFIER ::= { sdi480status 293 }

    outBr284 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream284 1 }

    outStream285 OBJECT IDENTIFIER ::= { sdi480status 294 }

    outBr285 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream285 1 }

    outStream286 OBJECT IDENTIFIER ::= { sdi480status 295 }

    outBr286 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream286 1 }

    outStream287 OBJECT IDENTIFIER ::= { sdi480status 296 }

    outBr287 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream287 1 }

    outStream288 OBJECT IDENTIFIER ::= { sdi480status 297 }

    outBr288 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream288 1 }

    outStream289 OBJECT IDENTIFIER ::= { sdi480status 298 }

    outBr289 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream289 1 }

    outStream290 OBJECT IDENTIFIER ::= { sdi480status 299 }

    outBr290 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream290 1 }

    outStream291 OBJECT IDENTIFIER ::= { sdi480status 300 }

    outBr291 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream291 1 }

    outStream292 OBJECT IDENTIFIER ::= { sdi480status 301 }

    outBr292 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream292 1 }

    outStream293 OBJECT IDENTIFIER ::= { sdi480status 302 }

    outBr293 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream293 1 }

    outStream294 OBJECT IDENTIFIER ::= { sdi480status 303 }

    outBr294 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream294 1 }

    outStream295 OBJECT IDENTIFIER ::= { sdi480status 304 }

    outBr295 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream295 1 }

    outStream296 OBJECT IDENTIFIER ::= { sdi480status 305 }

    outBr296 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream296 1 }

    outStream297 OBJECT IDENTIFIER ::= { sdi480status 306 }

    outBr297 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream297 1 }

    outStream298 OBJECT IDENTIFIER ::= { sdi480status 307 }

    outBr298 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream298 1 }

    outStream299 OBJECT IDENTIFIER ::= { sdi480status 308 }

    outBr299 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream299 1 }

    outStream300 OBJECT IDENTIFIER ::= { sdi480status 309 }

    outBr300 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream300 1 }

    outStream301 OBJECT IDENTIFIER ::= { sdi480status 310 }

    outBr301 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream301 1 }

    outStream302 OBJECT IDENTIFIER ::= { sdi480status 311 }

    outBr302 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream302 1 }

    outStream303 OBJECT IDENTIFIER ::= { sdi480status 312 }

    outBr303 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream303 1 }

    outStream304 OBJECT IDENTIFIER ::= { sdi480status 313 }

    outBr304 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream304 1 }

    outStream305 OBJECT IDENTIFIER ::= { sdi480status 314 }

    outBr305 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream305 1 }

    outStream306 OBJECT IDENTIFIER ::= { sdi480status 315 }

    outBr306 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream306 1 }

    outStream307 OBJECT IDENTIFIER ::= { sdi480status 316 }

    outBr307 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream307 1 }

    outStream308 OBJECT IDENTIFIER ::= { sdi480status 317 }

    outBr308 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream308 1 }

    outStream309 OBJECT IDENTIFIER ::= { sdi480status 318 }

    outBr309 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream309 1 }

    outStream310 OBJECT IDENTIFIER ::= { sdi480status 319 }

    outBr310 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream310 1 }

    outStream311 OBJECT IDENTIFIER ::= { sdi480status 320 }

    outBr311 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream311 1 }

    outStream312 OBJECT IDENTIFIER ::= { sdi480status 321 }

    outBr312 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream312 1 }

    outStream313 OBJECT IDENTIFIER ::= { sdi480status 322 }

    outBr313 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream313 1 }

    outStream314 OBJECT IDENTIFIER ::= { sdi480status 323 }

    outBr314 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream314 1 }

    outStream315 OBJECT IDENTIFIER ::= { sdi480status 324 }

    outBr315 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream315 1 }

    outStream316 OBJECT IDENTIFIER ::= { sdi480status 325 }

    outBr316 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream316 1 }

    outStream317 OBJECT IDENTIFIER ::= { sdi480status 326 }

    outBr317 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream317 1 }

    outStream318 OBJECT IDENTIFIER ::= { sdi480status 327 }

    outBr318 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream318 1 }

    outStream319 OBJECT IDENTIFIER ::= { sdi480status 328 }

    outBr319 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream319 1 }

    outStream320 OBJECT IDENTIFIER ::= { sdi480status 329 }

    outBr320 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream320 1 }

    outStream321 OBJECT IDENTIFIER ::= { sdi480status 330 }

    outBr321 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream321 1 }

    outStream322 OBJECT IDENTIFIER ::= { sdi480status 331 }

    outBr322 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream322 1 }

    outStream323 OBJECT IDENTIFIER ::= { sdi480status 332 }

    outBr323 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream323 1 }

    outStream324 OBJECT IDENTIFIER ::= { sdi480status 333 }

    outBr324 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream324 1 }

    outStream325 OBJECT IDENTIFIER ::= { sdi480status 334 }

    outBr325 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream325 1 }

    outStream326 OBJECT IDENTIFIER ::= { sdi480status 335 }

    outBr326 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream326 1 }

    outStream327 OBJECT IDENTIFIER ::= { sdi480status 336 }

    outBr327 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream327 1 }

    outStream328 OBJECT IDENTIFIER ::= { sdi480status 337 }

    outBr328 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream328 1 }

    outStream329 OBJECT IDENTIFIER ::= { sdi480status 338 }

    outBr329 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream329 1 }

    outStream330 OBJECT IDENTIFIER ::= { sdi480status 339 }

    outBr330 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream330 1 }

    outStream331 OBJECT IDENTIFIER ::= { sdi480status 340 }

    outBr331 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream331 1 }

    outStream332 OBJECT IDENTIFIER ::= { sdi480status 341 }

    outBr332 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream332 1 }

    outStream333 OBJECT IDENTIFIER ::= { sdi480status 342 }

    outBr333 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream333 1 }

    outStream334 OBJECT IDENTIFIER ::= { sdi480status 343 }

    outBr334 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream334 1 }

    outStream335 OBJECT IDENTIFIER ::= { sdi480status 344 }

    outBr335 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream335 1 }

    outStream336 OBJECT IDENTIFIER ::= { sdi480status 345 }

    outBr336 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream336 1 }

    outStream337 OBJECT IDENTIFIER ::= { sdi480status 346 }

    outBr337 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream337 1 }

    outStream338 OBJECT IDENTIFIER ::= { sdi480status 347 }

    outBr338 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream338 1 }

    outStream339 OBJECT IDENTIFIER ::= { sdi480status 348 }

    outBr339 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream339 1 }

    outStream340 OBJECT IDENTIFIER ::= { sdi480status 349 }

    outBr340 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream340 1 }

    outStream341 OBJECT IDENTIFIER ::= { sdi480status 350 }

    outBr341 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream341 1 }

    outStream342 OBJECT IDENTIFIER ::= { sdi480status 351 }

    outBr342 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream342 1 }

    outStream343 OBJECT IDENTIFIER ::= { sdi480status 352 }

    outBr343 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream343 1 }

    outStream344 OBJECT IDENTIFIER ::= { sdi480status 353 }

    outBr344 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream344 1 }

    outStream345 OBJECT IDENTIFIER ::= { sdi480status 354 }

    outBr345 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream345 1 }

    outStream346 OBJECT IDENTIFIER ::= { sdi480status 355 }

    outBr346 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream346 1 }

    outStream347 OBJECT IDENTIFIER ::= { sdi480status 356 }

    outBr347 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream347 1 }

    outStream348 OBJECT IDENTIFIER ::= { sdi480status 357 }

    outBr348 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream348 1 }

    outStream349 OBJECT IDENTIFIER ::= { sdi480status 358 }

    outBr349 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream349 1 }

    outStream350 OBJECT IDENTIFIER ::= { sdi480status 359 }

    outBr350 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream350 1 }

    outStream351 OBJECT IDENTIFIER ::= { sdi480status 360 }

    outBr351 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream351 1 }

    outStream352 OBJECT IDENTIFIER ::= { sdi480status 361 }

    outBr352 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream352 1 }

    outStream353 OBJECT IDENTIFIER ::= { sdi480status 362 }

    outBr353 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream353 1 }

    outStream354 OBJECT IDENTIFIER ::= { sdi480status 363 }

    outBr354 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream354 1 }

    outStream355 OBJECT IDENTIFIER ::= { sdi480status 364 }

    outBr355 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream355 1 }

    outStream356 OBJECT IDENTIFIER ::= { sdi480status 365 }

    outBr356 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream356 1 }

    outStream357 OBJECT IDENTIFIER ::= { sdi480status 366 }

    outBr357 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream357 1 }

    outStream358 OBJECT IDENTIFIER ::= { sdi480status 367 }

    outBr358 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream358 1 }

    outStream359 OBJECT IDENTIFIER ::= { sdi480status 368 }

    outBr359 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream359 1 }

    outStream360 OBJECT IDENTIFIER ::= { sdi480status 369 }

    outBr360 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream360 1 }

    outStream361 OBJECT IDENTIFIER ::= { sdi480status 370 }

    outBr361 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream361 1 }

    outStream362 OBJECT IDENTIFIER ::= { sdi480status 371 }

    outBr362 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream362 1 }

    outStream363 OBJECT IDENTIFIER ::= { sdi480status 372 }

    outBr363 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream363 1 }

    outStream364 OBJECT IDENTIFIER ::= { sdi480status 373 }

    outBr364 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream364 1 }

    outStream365 OBJECT IDENTIFIER ::= { sdi480status 374 }

    outBr365 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream365 1 }

    outStream366 OBJECT IDENTIFIER ::= { sdi480status 375 }

    outBr366 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream366 1 }

    outStream367 OBJECT IDENTIFIER ::= { sdi480status 376 }

    outBr367 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream367 1 }

    outStream368 OBJECT IDENTIFIER ::= { sdi480status 377 }

    outBr368 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream368 1 }

    outStream369 OBJECT IDENTIFIER ::= { sdi480status 378 }

    outBr369 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream369 1 }

    outStream370 OBJECT IDENTIFIER ::= { sdi480status 379 }

    outBr370 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream370 1 }

    outStream371 OBJECT IDENTIFIER ::= { sdi480status 380 }

    outBr371 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream371 1 }

    outStream372 OBJECT IDENTIFIER ::= { sdi480status 381 }

    outBr372 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream372 1 }

    outStream373 OBJECT IDENTIFIER ::= { sdi480status 382 }

    outBr373 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream373 1 }

    outStream374 OBJECT IDENTIFIER ::= { sdi480status 383 }

    outBr374 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream374 1 }

    outStream375 OBJECT IDENTIFIER ::= { sdi480status 384 }

    outBr375 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream375 1 }

    outStream376 OBJECT IDENTIFIER ::= { sdi480status 385 }

    outBr376 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream376 1 }

    outStream377 OBJECT IDENTIFIER ::= { sdi480status 386 }

    outBr377 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream377 1 }

    outStream378 OBJECT IDENTIFIER ::= { sdi480status 387 }

    outBr378 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream378 1 }

    outStream379 OBJECT IDENTIFIER ::= { sdi480status 388 }

    outBr379 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream379 1 }

    outStream380 OBJECT IDENTIFIER ::= { sdi480status 389 }

    outBr380 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream380 1 }

    outStream381 OBJECT IDENTIFIER ::= { sdi480status 390 }

    outBr381 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream381 1 }

    outStream382 OBJECT IDENTIFIER ::= { sdi480status 391 }

    outBr382 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream382 1 }

    outStream383 OBJECT IDENTIFIER ::= { sdi480status 392 }

    outBr383 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream383 1 }

    outStream384 OBJECT IDENTIFIER ::= { sdi480status 393 }

    outBr384 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream384 1 }

    outStream385 OBJECT IDENTIFIER ::= { sdi480status 394 }

    outBr385 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream385 1 }

    outStream386 OBJECT IDENTIFIER ::= { sdi480status 395 }

    outBr386 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream386 1 }

    outStream387 OBJECT IDENTIFIER ::= { sdi480status 396 }

    outBr387 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream387 1 }

    outStream388 OBJECT IDENTIFIER ::= { sdi480status 397 }

    outBr388 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream388 1 }

    outStream389 OBJECT IDENTIFIER ::= { sdi480status 398 }

    outBr389 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream389 1 }

    outStream390 OBJECT IDENTIFIER ::= { sdi480status 399 }

    outBr390 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream390 1 }

    outStream391 OBJECT IDENTIFIER ::= { sdi480status 400 }

    outBr391 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream391 1 }

    outStream392 OBJECT IDENTIFIER ::= { sdi480status 401 }

    outBr392 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream392 1 }

    outStream393 OBJECT IDENTIFIER ::= { sdi480status 402 }

    outBr393 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream393 1 }

    outStream394 OBJECT IDENTIFIER ::= { sdi480status 403 }

    outBr394 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream394 1 }

    outStream395 OBJECT IDENTIFIER ::= { sdi480status 404 }

    outBr395 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream395 1 }

    outStream396 OBJECT IDENTIFIER ::= { sdi480status 405 }

    outBr396 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream396 1 }

    outStream397 OBJECT IDENTIFIER ::= { sdi480status 406 }

    outBr397 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream397 1 }

    outStream398 OBJECT IDENTIFIER ::= { sdi480status 407 }

    outBr398 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream398 1 }

    outStream399 OBJECT IDENTIFIER ::= { sdi480status 408 }

    outBr399 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream399 1 }

    outStream400 OBJECT IDENTIFIER ::= { sdi480status 409 }

    outBr400 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream400 1 }

    outStream401 OBJECT IDENTIFIER ::= { sdi480status 410 }

    outBr401 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream401 1 }

    outStream402 OBJECT IDENTIFIER ::= { sdi480status 411 }

    outBr402 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream402 1 }

    outStream403 OBJECT IDENTIFIER ::= { sdi480status 412 }

    outBr403 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream403 1 }

    outStream404 OBJECT IDENTIFIER ::= { sdi480status 413 }

    outBr404 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream404 1 }

    outStream405 OBJECT IDENTIFIER ::= { sdi480status 414 }

    outBr405 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream405 1 }

    outStream406 OBJECT IDENTIFIER ::= { sdi480status 415 }

    outBr406 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream406 1 }

    outStream407 OBJECT IDENTIFIER ::= { sdi480status 416 }

    outBr407 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream407 1 }

    outStream408 OBJECT IDENTIFIER ::= { sdi480status 417 }

    outBr408 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream408 1 }

    outStream409 OBJECT IDENTIFIER ::= { sdi480status 418 }

    outBr409 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream409 1 }

    outStream410 OBJECT IDENTIFIER ::= { sdi480status 419 }

    outBr410 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream410 1 }

    outStream411 OBJECT IDENTIFIER ::= { sdi480status 420 }

    outBr411 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream411 1 }

    outStream412 OBJECT IDENTIFIER ::= { sdi480status 421 }

    outBr412 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream412 1 }

    outStream413 OBJECT IDENTIFIER ::= { sdi480status 422 }

    outBr413 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream413 1 }

    outStream414 OBJECT IDENTIFIER ::= { sdi480status 423 }

    outBr414 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream414 1 }

    outStream415 OBJECT IDENTIFIER ::= { sdi480status 424 }

    outBr415 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream415 1 }

    outStream416 OBJECT IDENTIFIER ::= { sdi480status 425 }

    outBr416 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream416 1 }

    outStream417 OBJECT IDENTIFIER ::= { sdi480status 426 }

    outBr417 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream417 1 }

    outStream418 OBJECT IDENTIFIER ::= { sdi480status 427 }

    outBr418 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream418 1 }

    outStream419 OBJECT IDENTIFIER ::= { sdi480status 428 }

    outBr419 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream419 1 }

    outStream420 OBJECT IDENTIFIER ::= { sdi480status 429 }

    outBr420 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream420 1 }

    outStream421 OBJECT IDENTIFIER ::= { sdi480status 430 }

    outBr421 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream421 1 }

    outStream422 OBJECT IDENTIFIER ::= { sdi480status 431 }

    outBr422 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream422 1 }

    outStream423 OBJECT IDENTIFIER ::= { sdi480status 432 }

    outBr423 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream423 1 }

    outStream424 OBJECT IDENTIFIER ::= { sdi480status 433 }

    outBr424 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream424 1 }

    outStream425 OBJECT IDENTIFIER ::= { sdi480status 434 }

    outBr425 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream425 1 }

    outStream426 OBJECT IDENTIFIER ::= { sdi480status 435 }

    outBr426 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream426 1 }

    outStream427 OBJECT IDENTIFIER ::= { sdi480status 436 }

    outBr427 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream427 1 }

    outStream428 OBJECT IDENTIFIER ::= { sdi480status 437 }

    outBr428 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream428 1 }

    outStream429 OBJECT IDENTIFIER ::= { sdi480status 438 }

    outBr429 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream429 1 }

    outStream430 OBJECT IDENTIFIER ::= { sdi480status 439 }

    outBr430 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream430 1 }

    outStream431 OBJECT IDENTIFIER ::= { sdi480status 440 }

    outBr431 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream431 1 }

    outStream432 OBJECT IDENTIFIER ::= { sdi480status 441 }

    outBr432 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream432 1 }

    outStream433 OBJECT IDENTIFIER ::= { sdi480status 442 }

    outBr433 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream433 1 }

    outStream434 OBJECT IDENTIFIER ::= { sdi480status 443 }

    outBr434 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream434 1 }

    outStream435 OBJECT IDENTIFIER ::= { sdi480status 444 }

    outBr435 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream435 1 }

    outStream436 OBJECT IDENTIFIER ::= { sdi480status 445 }

    outBr436 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream436 1 }

    outStream437 OBJECT IDENTIFIER ::= { sdi480status 446 }

    outBr437 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream437 1 }

    outStream438 OBJECT IDENTIFIER ::= { sdi480status 447 }

    outBr438 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream438 1 }

    outStream439 OBJECT IDENTIFIER ::= { sdi480status 448 }

    outBr439 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream439 1 }

    outStream440 OBJECT IDENTIFIER ::= { sdi480status 449 }

    outBr440 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream440 1 }

    outStream441 OBJECT IDENTIFIER ::= { sdi480status 450 }

    outBr441 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream441 1 }

    outStream442 OBJECT IDENTIFIER ::= { sdi480status 451 }

    outBr442 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream442 1 }

    outStream443 OBJECT IDENTIFIER ::= { sdi480status 452 }

    outBr443 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream443 1 }

    outStream444 OBJECT IDENTIFIER ::= { sdi480status 453 }

    outBr444 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream444 1 }

    outStream445 OBJECT IDENTIFIER ::= { sdi480status 454 }

    outBr445 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream445 1 }

    outStream446 OBJECT IDENTIFIER ::= { sdi480status 455 }

    outBr446 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream446 1 }

    outStream447 OBJECT IDENTIFIER ::= { sdi480status 456 }

    outBr447 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream447 1 }

    outStream448 OBJECT IDENTIFIER ::= { sdi480status 457 }

    outBr448 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream448 1 }

    outStream449 OBJECT IDENTIFIER ::= { sdi480status 458 }

    outBr449 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream449 1 }

    outStream450 OBJECT IDENTIFIER ::= { sdi480status 459 }

    outBr450 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream450 1 }

    outStream451 OBJECT IDENTIFIER ::= { sdi480status 460 }

    outBr451 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream451 1 }

    outStream452 OBJECT IDENTIFIER ::= { sdi480status 461 }

    outBr452 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream452 1 }

    outStream453 OBJECT IDENTIFIER ::= { sdi480status 462 }

    outBr453 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream453 1 }

    outStream454 OBJECT IDENTIFIER ::= { sdi480status 463 }

    outBr454 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream454 1 }

    outStream455 OBJECT IDENTIFIER ::= { sdi480status 464 }

    outBr455 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream455 1 }

    outStream456 OBJECT IDENTIFIER ::= { sdi480status 465 }

    outBr456 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream456 1 }

    outStream457 OBJECT IDENTIFIER ::= { sdi480status 466 }

    outBr457 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream457 1 }

    outStream458 OBJECT IDENTIFIER ::= { sdi480status 467 }

    outBr458 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream458 1 }

    outStream459 OBJECT IDENTIFIER ::= { sdi480status 468 }

    outBr459 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream459 1 }

    outStream460 OBJECT IDENTIFIER ::= { sdi480status 469 }

    outBr460 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream460 1 }

    outStream461 OBJECT IDENTIFIER ::= { sdi480status 470 }

    outBr461 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream461 1 }

    outStream462 OBJECT IDENTIFIER ::= { sdi480status 471 }

    outBr462 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream462 1 }

    outStream463 OBJECT IDENTIFIER ::= { sdi480status 472 }

    outBr463 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream463 1 }

    outStream464 OBJECT IDENTIFIER ::= { sdi480status 473 }

    outBr464 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream464 1 }

    outStream465 OBJECT IDENTIFIER ::= { sdi480status 474 }

    outBr465 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream465 1 }

    outStream466 OBJECT IDENTIFIER ::= { sdi480status 475 }

    outBr466 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream466 1 }

    outStream467 OBJECT IDENTIFIER ::= { sdi480status 476 }

    outBr467 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream467 1 }

    outStream468 OBJECT IDENTIFIER ::= { sdi480status 477 }

    outBr468 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream468 1 }

    outStream469 OBJECT IDENTIFIER ::= { sdi480status 478 }

    outBr469 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream469 1 }

    outStream470 OBJECT IDENTIFIER ::= { sdi480status 479 }

    outBr470 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream470 1 }

    outStream471 OBJECT IDENTIFIER ::= { sdi480status 480 }

    outBr471 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream471 1 }

    outStream472 OBJECT IDENTIFIER ::= { sdi480status 481 }

    outBr472 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream472 1 }

    outStream473 OBJECT IDENTIFIER ::= { sdi480status 482 }

    outBr473 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream473 1 }

    outStream474 OBJECT IDENTIFIER ::= { sdi480status 483 }

    outBr474 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream474 1 }

    outStream475 OBJECT IDENTIFIER ::= { sdi480status 484 }

    outBr475 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream475 1 }

    outStream476 OBJECT IDENTIFIER ::= { sdi480status 485 }

    outBr476 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream476 1 }

    outStream477 OBJECT IDENTIFIER ::= { sdi480status 486 }

    outBr477 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream477 1 }

    outStream478 OBJECT IDENTIFIER ::= { sdi480status 487 }

    outBr478 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream478 1 }

    outStream479 OBJECT IDENTIFIER ::= { sdi480status 488 }

    outBr479 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream479 1 }

    outStream480 OBJECT IDENTIFIER ::= { sdi480status 489 }

    outBr480 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream480 1 }

    outStream481 OBJECT IDENTIFIER ::= { sdi480status 490 }

    outBr481 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream481 1 }

    outStream482 OBJECT IDENTIFIER ::= { sdi480status 491 }

    outBr482 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream482 1 }

    outStream483 OBJECT IDENTIFIER ::= { sdi480status 492 }

    outBr483 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream483 1 }

    outStream484 OBJECT IDENTIFIER ::= { sdi480status 493 }

    outBr484 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream484 1 }

    outStream485 OBJECT IDENTIFIER ::= { sdi480status 494 }

    outBr485 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream485 1 }

    outStream486 OBJECT IDENTIFIER ::= { sdi480status 495 }

    outBr486 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream486 1 }

    outStream487 OBJECT IDENTIFIER ::= { sdi480status 496 }

    outBr487 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream487 1 }

    outStream488 OBJECT IDENTIFIER ::= { sdi480status 497 }

    outBr488 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream488 1 }

    outStream489 OBJECT IDENTIFIER ::= { sdi480status 498 }

    outBr489 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream489 1 }

    outStream490 OBJECT IDENTIFIER ::= { sdi480status 499 }

    outBr490 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream490 1 }

    outStream491 OBJECT IDENTIFIER ::= { sdi480status 500 }

    outBr491 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream491 1 }

    outStream492 OBJECT IDENTIFIER ::= { sdi480status 501 }

    outBr492 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream492 1 }

    outStream493 OBJECT IDENTIFIER ::= { sdi480status 502 }

    outBr493 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream493 1 }

    outStream494 OBJECT IDENTIFIER ::= { sdi480status 503 }

    outBr494 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream494 1 }

    outStream495 OBJECT IDENTIFIER ::= { sdi480status 504 }

    outBr495 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream495 1 }

    outStream496 OBJECT IDENTIFIER ::= { sdi480status 505 }

    outBr496 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream496 1 }

    outStream497 OBJECT IDENTIFIER ::= { sdi480status 506 }

    outBr497 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream497 1 }

    outStream498 OBJECT IDENTIFIER ::= { sdi480status 507 }

    outBr498 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream498 1 }

    outStream499 OBJECT IDENTIFIER ::= { sdi480status 508 }

    outBr499 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream499 1 }

    outStream500 OBJECT IDENTIFIER ::= { sdi480status 509 }

    outBr500 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream500 1 }

    outStream501 OBJECT IDENTIFIER ::= { sdi480status 510 }

    outBr501 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream501 1 }

    outStream502 OBJECT IDENTIFIER ::= { sdi480status 511 }

    outBr502 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream502 1 }

    outStream503 OBJECT IDENTIFIER ::= { sdi480status 512 }

    outBr503 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream503 1 }

    outStream504 OBJECT IDENTIFIER ::= { sdi480status 513 }

    outBr504 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream504 1 }

    outStream505 OBJECT IDENTIFIER ::= { sdi480status 514 }

    outBr505 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream505 1 }

    outStream506 OBJECT IDENTIFIER ::= { sdi480status 515 }

    outBr506 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream506 1 }

    outStream507 OBJECT IDENTIFIER ::= { sdi480status 516 }

    outBr507 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream507 1 }

    outStream508 OBJECT IDENTIFIER ::= { sdi480status 517 }

    outBr508 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream508 1 }

    outStream509 OBJECT IDENTIFIER ::= { sdi480status 518 }

    outBr509 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream509 1 }

    outStream510 OBJECT IDENTIFIER ::= { sdi480status 519 }

    outBr510 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream510 1 }

    outStream511 OBJECT IDENTIFIER ::= { sdi480status 520 }

    outBr511 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream511 1 }

    outStream512 OBJECT IDENTIFIER ::= { sdi480status 521 }

    outBr512 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream512 1 }

    outStream513 OBJECT IDENTIFIER ::= { sdi480status 522 }

    outBr513 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream513 1 }

    outStream514 OBJECT IDENTIFIER ::= { sdi480status 523 }

    outBr514 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream514 1 }

    outStream515 OBJECT IDENTIFIER ::= { sdi480status 524 }

    outBr515 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream515 1 }

    outStream516 OBJECT IDENTIFIER ::= { sdi480status 525 }

    outBr516 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream516 1 }

    outStream517 OBJECT IDENTIFIER ::= { sdi480status 526 }

    outBr517 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream517 1 }

    outStream518 OBJECT IDENTIFIER ::= { sdi480status 527 }

    outBr518 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream518 1 }

    outStream519 OBJECT IDENTIFIER ::= { sdi480status 528 }

    outBr519 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream519 1 }

    outStream520 OBJECT IDENTIFIER ::= { sdi480status 529 }

    outBr520 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream520 1 }

    outStream521 OBJECT IDENTIFIER ::= { sdi480status 530 }

    outBr521 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream521 1 }

    outStream522 OBJECT IDENTIFIER ::= { sdi480status 531 }

    outBr522 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream522 1 }

    outStream523 OBJECT IDENTIFIER ::= { sdi480status 532 }

    outBr523 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream523 1 }

    outStream524 OBJECT IDENTIFIER ::= { sdi480status 533 }

    outBr524 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream524 1 }

    outStream525 OBJECT IDENTIFIER ::= { sdi480status 534 }

    outBr525 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream525 1 }

    outStream526 OBJECT IDENTIFIER ::= { sdi480status 535 }

    outBr526 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream526 1 }

    outStream527 OBJECT IDENTIFIER ::= { sdi480status 536 }

    outBr527 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream527 1 }

    outStream528 OBJECT IDENTIFIER ::= { sdi480status 537 }

    outBr528 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream528 1 }

    outStream529 OBJECT IDENTIFIER ::= { sdi480status 538 }

    outBr529 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream529 1 }

    outStream530 OBJECT IDENTIFIER ::= { sdi480status 539 }

    outBr530 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream530 1 }

    outStream531 OBJECT IDENTIFIER ::= { sdi480status 540 }

    outBr531 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream531 1 }

    outStream532 OBJECT IDENTIFIER ::= { sdi480status 541 }

    outBr532 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream532 1 }

    outStream533 OBJECT IDENTIFIER ::= { sdi480status 542 }

    outBr533 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream533 1 }

    outStream534 OBJECT IDENTIFIER ::= { sdi480status 543 }

    outBr534 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream534 1 }

    outStream535 OBJECT IDENTIFIER ::= { sdi480status 544 }

    outBr535 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream535 1 }

    outStream536 OBJECT IDENTIFIER ::= { sdi480status 545 }

    outBr536 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream536 1 }

    outStream537 OBJECT IDENTIFIER ::= { sdi480status 546 }

    outBr537 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream537 1 }

    outStream538 OBJECT IDENTIFIER ::= { sdi480status 547 }

    outBr538 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream538 1 }

    outStream539 OBJECT IDENTIFIER ::= { sdi480status 548 }

    outBr539 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream539 1 }

    outStream540 OBJECT IDENTIFIER ::= { sdi480status 549 }

    outBr540 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream540 1 }

    outStream541 OBJECT IDENTIFIER ::= { sdi480status 550 }

    outBr541 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream541 1 }

    outStream542 OBJECT IDENTIFIER ::= { sdi480status 551 }

    outBr542 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream542 1 }

    outStream543 OBJECT IDENTIFIER ::= { sdi480status 552 }

    outBr543 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream543 1 }

    outStream544 OBJECT IDENTIFIER ::= { sdi480status 553 }

    outBr544 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream544 1 }

    outStream545 OBJECT IDENTIFIER ::= { sdi480status 554 }

    outBr545 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream545 1 }

    outStream546 OBJECT IDENTIFIER ::= { sdi480status 555 }

    outBr546 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream546 1 }

    outStream547 OBJECT IDENTIFIER ::= { sdi480status 556 }

    outBr547 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream547 1 }

    outStream548 OBJECT IDENTIFIER ::= { sdi480status 557 }

    outBr548 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream548 1 }

    outStream549 OBJECT IDENTIFIER ::= { sdi480status 558 }

    outBr549 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream549 1 }

    outStream550 OBJECT IDENTIFIER ::= { sdi480status 559 }

    outBr550 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream550 1 }

    outStream551 OBJECT IDENTIFIER ::= { sdi480status 560 }

    outBr551 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream551 1 }

    outStream552 OBJECT IDENTIFIER ::= { sdi480status 561 }

    outBr552 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream552 1 }

    outStream553 OBJECT IDENTIFIER ::= { sdi480status 562 }

    outBr553 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream553 1 }

    outStream554 OBJECT IDENTIFIER ::= { sdi480status 563 }

    outBr554 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream554 1 }

    outStream555 OBJECT IDENTIFIER ::= { sdi480status 564 }

    outBr555 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream555 1 }

    outStream556 OBJECT IDENTIFIER ::= { sdi480status 565 }

    outBr556 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream556 1 }

    outStream557 OBJECT IDENTIFIER ::= { sdi480status 566 }

    outBr557 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream557 1 }

    outStream558 OBJECT IDENTIFIER ::= { sdi480status 567 }

    outBr558 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream558 1 }

    outStream559 OBJECT IDENTIFIER ::= { sdi480status 568 }

    outBr559 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream559 1 }

    outStream560 OBJECT IDENTIFIER ::= { sdi480status 569 }

    outBr560 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream560 1 }

    outStream561 OBJECT IDENTIFIER ::= { sdi480status 570 }

    outBr561 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream561 1 }

    outStream562 OBJECT IDENTIFIER ::= { sdi480status 571 }

    outBr562 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream562 1 }

    outStream563 OBJECT IDENTIFIER ::= { sdi480status 572 }

    outBr563 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream563 1 }

    outStream564 OBJECT IDENTIFIER ::= { sdi480status 573 }

    outBr564 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream564 1 }

    outStream565 OBJECT IDENTIFIER ::= { sdi480status 574 }

    outBr565 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream565 1 }

    outStream566 OBJECT IDENTIFIER ::= { sdi480status 575 }

    outBr566 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream566 1 }

    outStream567 OBJECT IDENTIFIER ::= { sdi480status 576 }

    outBr567 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream567 1 }

    outStream568 OBJECT IDENTIFIER ::= { sdi480status 577 }

    outBr568 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream568 1 }

    outStream569 OBJECT IDENTIFIER ::= { sdi480status 578 }

    outBr569 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream569 1 }

    outStream570 OBJECT IDENTIFIER ::= { sdi480status 579 }

    outBr570 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream570 1 }

    outStream571 OBJECT IDENTIFIER ::= { sdi480status 580 }

    outBr571 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream571 1 }

    outStream572 OBJECT IDENTIFIER ::= { sdi480status 581 }

    outBr572 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream572 1 }

    outStream573 OBJECT IDENTIFIER ::= { sdi480status 582 }

    outBr573 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream573 1 }

    outStream574 OBJECT IDENTIFIER ::= { sdi480status 583 }

    outBr574 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream574 1 }

    outStream575 OBJECT IDENTIFIER ::= { sdi480status 584 }

    outBr575 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream575 1 }

    outStream576 OBJECT IDENTIFIER ::= { sdi480status 585 }

    outBr576 OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate in tenth of Mbps"
        ::= { outStream576 1 }

    commStatus OBJECT IDENTIFIER ::= { sdi480status 586 }

    inTotbr OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Total input bitrate in tenth of Mbps"
        ::= { commStatus 1 }

    outTotbr OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of Mbps"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Total output bitrate in tenth of Mbps"
        ::= { commStatus 2 }

    cpuLoad OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "%"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "CPU load in %"
        ::= { commStatus 3 }

    intTemp OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "deg C"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Internal temperature in deg C"
        ::= { commStatus 4 }

    demodTemp OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "deg C"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator temperature in deg C"
        ::= { commStatus 5 }

    volt OBJECT-TYPE
        SYNTAX              Integer32
        UNITS               "tenth of volt"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Supply voltage in tenth of volt"
        ::= { commStatus 6 }

    sdi480alarms OBJECT IDENTIFIER ::= { terra-sdi480 2 }

    alarmLnb1 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #1"
        ::= { sdi480alarms 1 }

    alarmLnb2 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #2"
        ::= { sdi480alarms 2 }

    alarmLnb3 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #3"
        ::= { sdi480alarms 3 }

    alarmLnb4 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #4"
        ::= { sdi480alarms 4 }

    alarmStlink OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Streaming ETH interface link down"
        ::= { sdi480alarms 5 }

    alarmCtrlink OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Control ETH interface link down"
        ::= { sdi480alarms 6 }

    alarmBrovf OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Output bitrate overflow"
        ::= { sdi480alarms 7 }

    alarmUnlock1 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 1 unlocked"
        ::= { sdi480alarms 8 }

    alarmUnlock2 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 2 unlocked"
        ::= { sdi480alarms 9 }

    alarmUnlock3 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 3 unlocked"
        ::= { sdi480alarms 10 }

    alarmUnlock4 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 4 unlocked"
        ::= { sdi480alarms 11 }

    alarmUnlock5 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 5 unlocked"
        ::= { sdi480alarms 12 }

    alarmUnlock6 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 6 unlocked"
        ::= { sdi480alarms 13 }

    alarmUnlock7 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 7 unlocked"
        ::= { sdi480alarms 14 }

    alarmUnlock8 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 8 unlocked"
        ::= { sdi480alarms 15 }

    alarmUnlock9 OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Demodulator 9 unlocked"
        ::= { sdi480alarms 16 }

    alarmPowerr OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Power voltage is out of limits"
        ::= { sdi480alarms 17 }

    alarmTemperr OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Module is overheated"
        ::= { sdi480alarms 18 }

    alarmIbrer OBJECT-TYPE
        SYNTAX              DefStatus
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Input bitrate overflow"
        ::= { sdi480alarms 19 }

    sdi480notifications OBJECT IDENTIFIER ::= { terra-sdi480 3 }

    notifyLnb1 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmLnb1
                }
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #1"
        ::= { sdi480notifications 1 }

    notifyLnb2 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmLnb2
                }
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #2"
        ::= { sdi480notifications 2 }

    notifyLnb3 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmLnb3
                }
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #3"
        ::= { sdi480notifications 3 }

    notifyLnb4 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmLnb4
                }
        STATUS              current
        DESCRIPTION         "LNB power overloaded LNB #4"
        ::= { sdi480notifications 4 }

    notifyStlink NOTIFICATION-TYPE
        OBJECTS {           
                        alarmStlink
                }
        STATUS              current
        DESCRIPTION         "Streaming ETH interface link down"
        ::= { sdi480notifications 5 }

    notifyCtrlink NOTIFICATION-TYPE
        OBJECTS {           
                        alarmCtrlink
                }
        STATUS              current
        DESCRIPTION         "Control ETH interface link down"
        ::= { sdi480notifications 6 }

    notifyBrovf NOTIFICATION-TYPE
        OBJECTS {           
                        alarmBrovf
                }
        STATUS              current
        DESCRIPTION         "Output bitrate overflow"
        ::= { sdi480notifications 7 }

    notifyUnlock1 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock1
                }
        STATUS              current
        DESCRIPTION         "Demodulator 1 unlocked"
        ::= { sdi480notifications 8 }

    notifyUnlock2 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock2
                }
        STATUS              current
        DESCRIPTION         "Demodulator 2 unlocked"
        ::= { sdi480notifications 9 }

    notifyUnlock3 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock3
                }
        STATUS              current
        DESCRIPTION         "Demodulator 3 unlocked"
        ::= { sdi480notifications 10 }

    notifyUnlock4 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock4
                }
        STATUS              current
        DESCRIPTION         "Demodulator 4 unlocked"
        ::= { sdi480notifications 11 }

    notifyUnlock5 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock5
                }
        STATUS              current
        DESCRIPTION         "Demodulator 5 unlocked"
        ::= { sdi480notifications 12 }

    notifyUnlock6 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock6
                }
        STATUS              current
        DESCRIPTION         "Demodulator 6 unlocked"
        ::= { sdi480notifications 13 }

    notifyUnlock7 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock7
                }
        STATUS              current
        DESCRIPTION         "Demodulator 7 unlocked"
        ::= { sdi480notifications 14 }

    notifyUnlock8 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock8
                }
        STATUS              current
        DESCRIPTION         "Demodulator 8 unlocked"
        ::= { sdi480notifications 15 }

    notifyUnlock9 NOTIFICATION-TYPE
        OBJECTS {           
                        alarmUnlock9
                }
        STATUS              current
        DESCRIPTION         "Demodulator 9 unlocked"
        ::= { sdi480notifications 16 }

    notifyPowerr NOTIFICATION-TYPE
        OBJECTS {           
                        alarmPowerr
                }
        STATUS              current
        DESCRIPTION         "Power voltage is out of limits"
        ::= { sdi480notifications 17 }

    notifyTemperr NOTIFICATION-TYPE
        OBJECTS {           
                        alarmTemperr
                }
        STATUS              current
        DESCRIPTION         "Module is overheated"
        ::= { sdi480notifications 18 }

    notifyIbrer NOTIFICATION-TYPE
        OBJECTS {           
                        alarmIbrer
                }
        STATUS              current
        DESCRIPTION         "Input bitrate overflow"
        ::= { sdi480notifications 19 }

    sdi480Info OBJECT IDENTIFIER ::= { terra-sdi480 4 }

    infVersion OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Represents device firmware version number"
        ::= { sdi480Info 1 }

    infSerNum OBJECT-TYPE
        SYNTAX              DisplayString
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION         "Represents device Serial Number"
        ::= { sdi480Info 2 }

    terrasdi480MIBConformance OBJECT IDENTIFIER ::= { terra-sdi480 5 }

    terrasdi480MIBGroups OBJECT IDENTIFIER ::= { terrasdi480MIBConformance 1 }

    sdi480TerraMibAllObjects OBJECT-GROUP
        OBJECTS {           
                        inLock1,
                        inlevel1,
                        insnr1,
                        inbr1,
                        inper1,
                        inLock2,
                        inlevel2,
                        insnr2,
                        inbr2,
                        inper2,
                        inLock3,
                        inlevel3,
                        insnr3,
                        inbr3,
                        inper3,
                        inLock4,
                        inlevel4,
                        insnr4,
                        inbr4,
                        inper4,
                        inLock5,
                        inlevel5,
                        insnr5,
                        inbr5,
                        inper5,
                        inLock6,
                        inlevel6,
                        insnr6,
                        inbr6,
                        inper6,
                        inLock7,
                        inlevel7,
                        insnr7,
                        inbr7,
                        inper7,
                        inLock8,
                        inlevel8,
                        insnr8,
                        inbr8,
                        inper8,
                        usbinBR,
                        outBr1,
                        outBr2,
                        outBr3,
                        outBr4,
                        outBr5,
                        outBr6,
                        outBr7,
                        outBr8,
                        outBr9,
                        outBr10,
                        outBr11,
                        outBr12,
                        outBr13,
                        outBr14,
                        outBr15,
                        outBr16,
                        outBr17,
                        outBr18,
                        outBr19,
                        outBr20,
                        outBr21,
                        outBr22,
                        outBr23,
                        outBr24,
                        outBr25,
                        outBr26,
                        outBr27,
                        outBr28,
                        outBr29,
                        outBr30,
                        outBr31,
                        outBr32,
                        outBr33,
                        outBr34,
                        outBr35,
                        outBr36,
                        outBr37,
                        outBr38,
                        outBr39,
                        outBr40,
                        outBr41,
                        outBr42,
                        outBr43,
                        outBr44,
                        outBr45,
                        outBr46,
                        outBr47,
                        outBr48,
                        outBr49,
                        outBr50,
                        outBr51,
                        outBr52,
                        outBr53,
                        outBr54,
                        outBr55,
                        outBr56,
                        outBr57,
                        outBr58,
                        outBr59,
                        outBr60,
                        outBr61,
                        outBr62,
                        outBr63,
                        outBr64,
                        outBr65,
                        outBr66,
                        outBr67,
                        outBr68,
                        outBr69,
                        outBr70,
                        outBr71,
                        outBr72,
                        outBr73,
                        outBr74,
                        outBr75,
                        outBr76,
                        outBr77,
                        outBr78,
                        outBr79,
                        outBr80,
                        outBr81,
                        outBr82,
                        outBr83,
                        outBr84,
                        outBr85,
                        outBr86,
                        outBr87,
                        outBr88,
                        outBr89,
                        outBr90,
                        outBr91,
                        outBr92,
                        outBr93,
                        outBr94,
                        outBr95,
                        outBr96,
                        outBr97,
                        outBr98,
                        outBr99,
                        outBr100,
                        outBr101,
                        outBr102,
                        outBr103,
                        outBr104,
                        outBr105,
                        outBr106,
                        outBr107,
                        outBr108,
                        outBr109,
                        outBr110,
                        outBr111,
                        outBr112,
                        outBr113,
                        outBr114,
                        outBr115,
                        outBr116,
                        outBr117,
                        outBr118,
                        outBr119,
                        outBr120,
                        outBr121,
                        outBr122,
                        outBr123,
                        outBr124,
                        outBr125,
                        outBr126,
                        outBr127,
                        outBr128,
                        outBr129,
                        outBr130,
                        outBr131,
                        outBr132,
                        outBr133,
                        outBr134,
                        outBr135,
                        outBr136,
                        outBr137,
                        outBr138,
                        outBr139,
                        outBr140,
                        outBr141,
                        outBr142,
                        outBr143,
                        outBr144,
                        outBr145,
                        outBr146,
                        outBr147,
                        outBr148,
                        outBr149,
                        outBr150,
                        outBr151,
                        outBr152,
                        outBr153,
                        outBr154,
                        outBr155,
                        outBr156,
                        outBr157,
                        outBr158,
                        outBr159,
                        outBr160,
                        outBr161,
                        outBr162,
                        outBr163,
                        outBr164,
                        outBr165,
                        outBr166,
                        outBr167,
                        outBr168,
                        outBr169,
                        outBr170,
                        outBr171,
                        outBr172,
                        outBr173,
                        outBr174,
                        outBr175,
                        outBr176,
                        outBr177,
                        outBr178,
                        outBr179,
                        outBr180,
                        outBr181,
                        outBr182,
                        outBr183,
                        outBr184,
                        outBr185,
                        outBr186,
                        outBr187,
                        outBr188,
                        outBr189,
                        outBr190,
                        outBr191,
                        outBr192,
                        outBr193,
                        outBr194,
                        outBr195,
                        outBr196,
                        outBr197,
                        outBr198,
                        outBr199,
                        outBr200,
                        outBr201,
                        outBr202,
                        outBr203,
                        outBr204,
                        outBr205,
                        outBr206,
                        outBr207,
                        outBr208,
                        outBr209,
                        outBr210,
                        outBr211,
                        outBr212,
                        outBr213,
                        outBr214,
                        outBr215,
                        outBr216,
                        outBr217,
                        outBr218,
                        outBr219,
                        outBr220,
                        outBr221,
                        outBr222,
                        outBr223,
                        outBr224,
                        outBr225,
                        outBr226,
                        outBr227,
                        outBr228,
                        outBr229,
                        outBr230,
                        outBr231,
                        outBr232,
                        outBr233,
                        outBr234,
                        outBr235,
                        outBr236,
                        outBr237,
                        outBr238,
                        outBr239,
                        outBr240,
                        outBr241,
                        outBr242,
                        outBr243,
                        outBr244,
                        outBr245,
                        outBr246,
                        outBr247,
                        outBr248,
                        outBr249,
                        outBr250,
                        outBr251,
                        outBr252,
                        outBr253,
                        outBr254,
                        outBr255,
                        outBr256,
                        outBr257,
                        outBr258,
                        outBr259,
                        outBr260,
                        outBr261,
                        outBr262,
                        outBr263,
                        outBr264,
                        outBr265,
                        outBr266,
                        outBr267,
                        outBr268,
                        outBr269,
                        outBr270,
                        outBr271,
                        outBr272,
                        outBr273,
                        outBr274,
                        outBr275,
                        outBr276,
                        outBr277,
                        outBr278,
                        outBr279,
                        outBr280,
                        outBr281,
                        outBr282,
                        outBr283,
                        outBr284,
                        outBr285,
                        outBr286,
                        outBr287,
                        outBr288,
                        outBr289,
                        outBr290,
                        outBr291,
                        outBr292,
                        outBr293,
                        outBr294,
                        outBr295,
                        outBr296,
                        outBr297,
                        outBr298,
                        outBr299,
                        outBr300,
                        outBr301,
                        outBr302,
                        outBr303,
                        outBr304,
                        outBr305,
                        outBr306,
                        outBr307,
                        outBr308,
                        outBr309,
                        outBr310,
                        outBr311,
                        outBr312,
                        outBr313,
                        outBr314,
                        outBr315,
                        outBr316,
                        outBr317,
                        outBr318,
                        outBr319,
                        outBr320,
                        outBr321,
                        outBr322,
                        outBr323,
                        outBr324,
                        outBr325,
                        outBr326,
                        outBr327,
                        outBr328,
                        outBr329,
                        outBr330,
                        outBr331,
                        outBr332,
                        outBr333,
                        outBr334,
                        outBr335,
                        outBr336,
                        outBr337,
                        outBr338,
                        outBr339,
                        outBr340,
                        outBr341,
                        outBr342,
                        outBr343,
                        outBr344,
                        outBr345,
                        outBr346,
                        outBr347,
                        outBr348,
                        outBr349,
                        outBr350,
                        outBr351,
                        outBr352,
                        outBr353,
                        outBr354,
                        outBr355,
                        outBr356,
                        outBr357,
                        outBr358,
                        outBr359,
                        outBr360,
                        outBr361,
                        outBr362,
                        outBr363,
                        outBr364,
                        outBr365,
                        outBr366,
                        outBr367,
                        outBr368,
                        outBr369,
                        outBr370,
                        outBr371,
                        outBr372,
                        outBr373,
                        outBr374,
                        outBr375,
                        outBr376,
                        outBr377,
                        outBr378,
                        outBr379,
                        outBr380,
                        outBr381,
                        outBr382,
                        outBr383,
                        outBr384,
                        outBr385,
                        outBr386,
                        outBr387,
                        outBr388,
                        outBr389,
                        outBr390,
                        outBr391,
                        outBr392,
                        outBr393,
                        outBr394,
                        outBr395,
                        outBr396,
                        outBr397,
                        outBr398,
                        outBr399,
                        outBr400,
                        outBr401,
                        outBr402,
                        outBr403,
                        outBr404,
                        outBr405,
                        outBr406,
                        outBr407,
                        outBr408,
                        outBr409,
                        outBr410,
                        outBr411,
                        outBr412,
                        outBr413,
                        outBr414,
                        outBr415,
                        outBr416,
                        outBr417,
                        outBr418,
                        outBr419,
                        outBr420,
                        outBr421,
                        outBr422,
                        outBr423,
                        outBr424,
                        outBr425,
                        outBr426,
                        outBr427,
                        outBr428,
                        outBr429,
                        outBr430,
                        outBr431,
                        outBr432,
                        outBr433,
                        outBr434,
                        outBr435,
                        outBr436,
                        outBr437,
                        outBr438,
                        outBr439,
                        outBr440,
                        outBr441,
                        outBr442,
                        outBr443,
                        outBr444,
                        outBr445,
                        outBr446,
                        outBr447,
                        outBr448,
                        outBr449,
                        outBr450,
                        outBr451,
                        outBr452,
                        outBr453,
                        outBr454,
                        outBr455,
                        outBr456,
                        outBr457,
                        outBr458,
                        outBr459,
                        outBr460,
                        outBr461,
                        outBr462,
                        outBr463,
                        outBr464,
                        outBr465,
                        outBr466,
                        outBr467,
                        outBr468,
                        outBr469,
                        outBr470,
                        outBr471,
                        outBr472,
                        outBr473,
                        outBr474,
                        outBr475,
                        outBr476,
                        outBr477,
                        outBr478,
                        outBr479,
                        outBr480,
                        outBr481,
                        outBr482,
                        outBr483,
                        outBr484,
                        outBr485,
                        outBr486,
                        outBr487,
                        outBr488,
                        outBr489,
                        outBr490,
                        outBr491,
                        outBr492,
                        outBr493,
                        outBr494,
                        outBr495,
                        outBr496,
                        outBr497,
                        outBr498,
                        outBr499,
                        outBr500,
                        outBr501,
                        outBr502,
                        outBr503,
                        outBr504,
                        outBr505,
                        outBr506,
                        outBr507,
                        outBr508,
                        outBr509,
                        outBr510,
                        outBr511,
                        outBr512,
                        outBr513,
                        outBr514,
                        outBr515,
                        outBr516,
                        outBr517,
                        outBr518,
                        outBr519,
                        outBr520,
                        outBr521,
                        outBr522,
                        outBr523,
                        outBr524,
                        outBr525,
                        outBr526,
                        outBr527,
                        outBr528,
                        outBr529,
                        outBr530,
                        outBr531,
                        outBr532,
                        outBr533,
                        outBr534,
                        outBr535,
                        outBr536,
                        outBr537,
                        outBr538,
                        outBr539,
                        outBr540,
                        outBr541,
                        outBr542,
                        outBr543,
                        outBr544,
                        outBr545,
                        outBr546,
                        outBr547,
                        outBr548,
                        outBr549,
                        outBr550,
                        outBr551,
                        outBr552,
                        outBr553,
                        outBr554,
                        outBr555,
                        outBr556,
                        outBr557,
                        outBr558,
                        outBr559,
                        outBr560,
                        outBr561,
                        outBr562,
                        outBr563,
                        outBr564,
                        outBr565,
                        outBr566,
                        outBr567,
                        outBr568,
                        outBr569,
                        outBr570,
                        outBr571,
                        outBr572,
                        outBr573,
                        outBr574,
                        outBr575,
                        outBr576,
                        inTotbr,
                        outTotbr,
                        cpuLoad,
                        intTemp,
                        demodTemp,
                        volt,
                        alarmLnb1,
                        alarmLnb2,
                        alarmLnb3,
                        alarmLnb4,
                        alarmStlink,
                        alarmCtrlink,
                        alarmBrovf,
                        alarmUnlock1,
                        alarmUnlock2,
                        alarmUnlock3,
                        alarmUnlock4,
                        alarmUnlock5,
                        alarmUnlock6,
                        alarmUnlock7,
                        alarmUnlock8,
                        alarmUnlock9,
                        alarmPowerr,
                        alarmTemperr,
                        alarmIbrer,
                        infVersion,
                        infSerNum
                }
        STATUS              current
        DESCRIPTION         "This automatically created object group contains
                            all those objects that do not belong to any other
                            OBJECT-GROUP"
        ::= { terrasdi480MIBGroups 1 }

    sdi480TerraMibAllNotifications NOTIFICATION-GROUP
        NOTIFICATIONS       {
                                notifyLnb1,
                                notifyLnb2,
                                notifyLnb3,
                                notifyLnb4,
                                notifyStlink,
                                notifyCtrlink,
                                notifyBrovf,
                                notifyUnlock1,
                                notifyUnlock2,
                                notifyUnlock3,
                                notifyUnlock4,
                                notifyUnlock5,
                                notifyUnlock6,
                                notifyUnlock7,
                                notifyUnlock8,
                                notifyUnlock9,
                                notifyPowerr,
                                notifyTemperr,
                                notifyIbrer
                            }
        STATUS              current
        DESCRIPTION         "This automatically created notification group
                            contains all notifications that do not belong
                            to any other NOTIFICATION-GROUP"
        ::= { terrasdi480MIBGroups 2 }

END
