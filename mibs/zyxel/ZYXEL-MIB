-- ZyXEL Communications Corporation
-- Private Enterprise MIB definition 
  
-- This file describes the ZyXEL Communications Corporation Enterprise MIB.
-- It contains ZyXEL products OIDs, and common managed objects.

    ZYXEL-MIB DEFINITIONS ::= BEGIN

    IMPORTS
	enterprises		FROM RFC1155-SMI;
	
	

	zyxel			OBJECT IDENTIFIER ::= { enterprises 890 }
	
	products		OBJECT IDENTIFIER ::= { zyxel 1 }
	
	prestige 		OBJECT IDENTIFIER ::= { products 2 }
	rack			OBJECT IDENTIFIER ::= { products 3 }
	dslam			OBJECT IDENTIFIER ::= { products 4 }
	accessSwitch  		OBJECT IDENTIFIER ::= { products 5 }
	zywall			OBJECT IDENTIFIER ::= { products 6 }
	atmEncryptor		OBJECT IDENTIFIER ::= { products 7 }
	serviceGateway  	OBJECT IDENTIFIER ::= { products 8 }
	proWireless	  	OBJECT IDENTIFIER ::= { products 9 }
	wirelessController	OBJECT IDENTIFIER ::= { products 10 }
	ipav			OBJECT IDENTIFIER ::= { products 11 }
	wimax		  	OBJECT IDENTIFIER ::= { products 12 }
	ems				OBJECT IDENTIFIER ::= { products 13 }
	smallCell		OBJECT IDENTIFIER ::= { products 14 }

	-- Prestige products

	prestigeCommon		OBJECT IDENTIFIER ::= { prestige 1 }
	p200Series		OBJECT IDENTIFIER ::= { prestige 2 }
	p300Series		OBJECT IDENTIFIER ::= { prestige 3 }
	p400Series		OBJECT IDENTIFIER ::= { prestige 4 }
	p500Series		OBJECT IDENTIFIER ::= { prestige 5 }
	p600Series		OBJECT IDENTIFIER ::= { prestige 6 }
	p641			OBJECT IDENTIFIER ::= { p600Series 1 }
	p642			OBJECT IDENTIFIER ::= { p600Series 2 }
	p643			OBJECT IDENTIFIER ::= { p600Series 3 }	
	p700Series		OBJECT IDENTIFIER ::= { prestige 7 }
	p794ra			OBJECT IDENTIFIER ::= { p700Series 1 }
	p794rb			OBJECT IDENTIFIER ::= { p700Series 2 }	
	p800Series		OBJECT IDENTIFIER ::= { prestige 8 }
	p900Series		OBJECT IDENTIFIER ::= { prestige 9 }
	
	-- Access Switch products
	accessSwitchCommon	OBJECT IDENTIFIER ::= { accessSwitch 1 }
	aes100			OBJECT IDENTIFIER ::= { accessSwitch 2 }
	pes100			OBJECT IDENTIFIER ::= { accessSwitch 3 }
	ves1012			OBJECT IDENTIFIER ::= { accessSwitch 4 }
	sesSeries		OBJECT IDENTIFIER ::= { accessSwitch 5 }
	p1600			OBJECT IDENTIFIER ::= { accessSwitch 6 }
	p1400			OBJECT IDENTIFIER ::= { accessSwitch 7 }
	esSeries		OBJECT IDENTIFIER ::= { accessSwitch 8 }
	aes100-1		OBJECT IDENTIFIER ::= { accessSwitch 9 }
	pes1014			OBJECT IDENTIFIER ::= { accessSwitch 10 }
	aesSeries		OBJECT IDENTIFIER ::= { accessSwitch 11 }
	vesSeries		OBJECT IDENTIFIER ::= { accessSwitch 12 }
	iesSeries		OBJECT IDENTIFIER ::= { accessSwitch 13 }
	accessSwitchCommonATM	OBJECT IDENTIFIER ::= { accessSwitch 14 }	
	ponSeries		OBJECT IDENTIFIER ::= { accessSwitch 15 }
	vopSeries		OBJECT IDENTIFIER ::= { accessSwitch 16 }
	gponSeries		OBJECT IDENTIFIER ::= { accessSwitch 17 }	
	gesSeries		OBJECT IDENTIFIER ::= { accessSwitch 18 }
	
	-- SES Series
	sesSeriesCommon		OBJECT IDENTIFIER ::= { sesSeries 1 }
	sam1008			OBJECT IDENTIFIER ::= { sesSeries 2 }
	ses1024			OBJECT IDENTIFIER ::= { sesSeries 3 }
	slc1024			OBJECT IDENTIFIER ::= { sesSeries 4 }
	slc1224-22		OBJECT IDENTIFIER ::= { sesSeries 5 }
	sam1216-22		OBJECT IDENTIFIER ::= { sesSeries 6 }
	ies708-22a-stuc		OBJECT IDENTIFIER ::= { sesSeries 7 }
	ies708-22a-stur		OBJECT IDENTIFIER ::= { sesSeries 8 }	
			
	-- ES Series	
	esSeriesCommon		OBJECT IDENTIFIER ::= { esSeries 1 }
	ees1024af		OBJECT IDENTIFIER ::= { esSeries 2 }
	es2008			OBJECT IDENTIFIER ::= { esSeries 3 }
	es2008-gtp		OBJECT IDENTIFIER ::= { esSeries 4 }
	es2008-sc		OBJECT IDENTIFIER ::= { esSeries 5 }
	es2008-sc30		OBJECT IDENTIFIER ::= { esSeries 6 }
	es3024			OBJECT IDENTIFIER ::= { esSeries 7 }
	es4024			OBJECT IDENTIFIER ::= { esSeries 8 }
	es2024			OBJECT IDENTIFIER ::= { esSeries 9 }
	gs3012			OBJECT IDENTIFIER ::= { esSeries 10 }
	gs3012f			OBJECT IDENTIFIER ::= { esSeries 11 }
	es3124			OBJECT IDENTIFIER ::= { esSeries 12 }
	gs4024			OBJECT IDENTIFIER ::= { esSeries 13 }
	es3124pwr		OBJECT IDENTIFIER ::= { esSeries 14 }
	gs2024			OBJECT IDENTIFIER ::= { esSeries 15 }
	es2024a			OBJECT IDENTIFIER ::= { esSeries 16 }
	es3148			OBJECT IDENTIFIER ::= { esSeries 17 }
	es2108			OBJECT IDENTIFIER ::= { esSeries 18 }
	es2108g			OBJECT IDENTIFIER ::= { esSeries 19 }
	gs4012f			OBJECT IDENTIFIER ::= { esSeries 20 }
	es2108pwr		OBJECT IDENTIFIER ::= { esSeries 21 }
	es2108lc		OBJECT IDENTIFIER ::= { esSeries 22 }
	es2048			OBJECT IDENTIFIER ::= { esSeries 23 }
	es4124			OBJECT IDENTIFIER ::= { esSeries 24 }
--	gs3012			OBJECT IDENTIFIER ::= { esSeries 25 }
	es3124-4f		OBJECT IDENTIFIER ::= { esSeries 26 }
	es2024pwr		OBJECT IDENTIFIER ::= { esSeries 27 }
	es3724			OBJECT IDENTIFIER ::= { esSeries 28 }
	es2108f			OBJECT IDENTIFIER ::= { esSeries 29 }
	es2226			OBJECT IDENTIFIER ::= { esSeries 30 }
	es3124f			OBJECT IDENTIFIER ::= { esSeries 31 }
	es1528			OBJECT IDENTIFIER ::= { esSeries 32 }
	es1552			OBJECT IDENTIFIER ::= { esSeries 33 }
	ms7206			OBJECT IDENTIFIER ::= { esSeries 34 }
	gs2724			OBJECT IDENTIFIER ::= { esSeries 35 }
	gs1524			OBJECT IDENTIFIER ::= { esSeries 36 }
	gs1548			OBJECT IDENTIFIER ::= { esSeries 37 }
	es305			OBJECT IDENTIFIER ::= { esSeries 38 }
	xsg4528f		OBJECT IDENTIFIER ::= { esSeries 39 }
	es3528			OBJECT IDENTIFIER ::= { esSeries 40 }
	es315			OBJECT IDENTIFIER ::= { esSeries 41 }
	es315f			OBJECT IDENTIFIER ::= { esSeries 42 }
	gs2726			OBJECT IDENTIFIER ::= { esSeries 43 }
	gs2750			OBJECT IDENTIFIER ::= { esSeries 44 }
	mes3728			OBJECT IDENTIFIER ::= { esSeries 45 }
	xgs4728f		OBJECT IDENTIFIER ::= { esSeries 46 }
	mgs3712			OBJECT IDENTIFIER ::= { esSeries 47 }
	mgs3712f		OBJECT IDENTIFIER ::= { esSeries 48 }
	es2105			OBJECT IDENTIFIER ::= { esSeries 49 }
	gs4012f-ncic		OBJECT IDENTIFIER ::= { esSeries 50 }  
	mes3528			OBJECT IDENTIFIER ::= { esSeries 51 } 
	xgs2726			OBJECT IDENTIFIER ::= { esSeries 52 }
	gs2200-48		OBJECT IDENTIFIER ::= { esSeries 53 }
		
	-- AES Series	
	aesSeriesCommon		OBJECT IDENTIFIER ::= { aesSeries 1 }
	aes1024			OBJECT IDENTIFIER ::= { aesSeries 2 }
	alc1024-61		OBJECT IDENTIFIER ::= { aesSeries 3 }
	aam1008-63		OBJECT IDENTIFIER ::= { aesSeries 4 }
	alc1024-63		OBJECT IDENTIFIER ::= { aesSeries 5 }
	aam1008-61		OBJECT IDENTIFIER ::= { aesSeries 6 }
	alc1224-71		OBJECT IDENTIFIER ::= { aesSeries 7 }
	ies1248-71		OBJECT IDENTIFIER ::= { aesSeries 9 }
	ies1248-73		OBJECT IDENTIFIER ::= { aesSeries 10 }
	aam1212-51		OBJECT IDENTIFIER ::= { aesSeries 11 }
	aam1212-53		OBJECT IDENTIFIER ::= { aesSeries 12 }
	ies1248-51		OBJECT IDENTIFIER ::= { aesSeries 13 }
	ies1248-53		OBJECT IDENTIFIER ::= { aesSeries 14 }
	alc1224-51		OBJECT IDENTIFIER ::= { aesSeries 15 }
	alc1224-53		OBJECT IDENTIFIER ::= { aesSeries 16 }
	ies1248-51v		OBJECT IDENTIFIER ::= { aesSeries 17 }
	
	-- VES Series			
	vesSeriesCommon		OBJECT IDENTIFIER ::= { vesSeries 1 }
	ves1008			OBJECT IDENTIFIER ::= { vesSeries 2 }
--	vmb2024			OBJECT IDENTIFIER ::= { vesSeries 3 }
	ves1024			OBJECT IDENTIFIER ::= { vesSeries 4 }
	vlc1012			OBJECT IDENTIFIER ::= { vesSeries 5 }
	ves1316			OBJECT IDENTIFIER ::= { vesSeries 6 }	
	ves1416			OBJECT IDENTIFIER ::= { vesSeries 7 }
	vlc1124			OBJECT IDENTIFIER ::= { vesSeries 8 }
	ves1124			OBJECT IDENTIFIER ::= { vesSeries 9 }
	ves1616f34		OBJECT IDENTIFIER ::= { vesSeries 10 }
	ves1616f44		OBJECT IDENTIFIER ::= { vesSeries 11 }
	ves1624f44		OBJECT IDENTIFIER ::= { vesSeries 12 }
	ves1624fa44		OBJECT IDENTIFIER ::= { vesSeries 13 }
	ves1616fa44		OBJECT IDENTIFIER ::= { vesSeries 14 }
	ves1616fa34-co4		OBJECT IDENTIFIER ::= { vesSeries 15 }
	ves1616fa35-co4		OBJECT IDENTIFIER ::= { vesSeries 16 }	
	ves1608fa34		OBJECT IDENTIFIER ::= { vesSeries 17 }
	ves1608fa35		OBJECT IDENTIFIER ::= { vesSeries 18 }
	ves1616fa54		OBJECT IDENTIFIER ::= { vesSeries 19 }	
	ves1624fa54		OBJECT IDENTIFIER ::= { vesSeries 20 }	
	ves1616fa55		OBJECT IDENTIFIER ::= { vesSeries 21 }	
	ves1624fa55		OBJECT IDENTIFIER ::= { vesSeries 22 }
	ves1624ft			OBJECT IDENTIFIER ::= { vesSeries 23 }
	ves1624fa34		OBJECT IDENTIFIER ::= { vesSeries 24 }
	ves1608fc54		OBJECT IDENTIFIER ::= { vesSeries 25 }
	ves1616fc54		OBJECT IDENTIFIER ::= { vesSeries 26 }
	ves1624ft54		OBJECT IDENTIFIER ::= { vesSeries 27 }
	ves1624ftv54		OBJECT IDENTIFIER ::= { vesSeries 28 }
	ves1616cta54		OBJECT IDENTIFIER ::= { vesSeries 29 }
	ves1624cta54		OBJECT IDENTIFIER ::= { vesSeries 30 }
	ves1608cea54		OBJECT IDENTIFIER ::= { vesSeries 31 }
	ves1616cea54		OBJECT IDENTIFIER ::= { vesSeries 32 }
	ves1616fe55a		OBJECT IDENTIFIER ::= { vesSeries 33 }
	ves1624ft55a		OBJECT IDENTIFIER ::= { vesSeries 34 }
	ves1616fb35		OBJECT IDENTIFIER ::= { vesSeries 35 }
	ves1608pe35		OBJECT IDENTIFIER ::= { vesSeries 36 }
	ves1616fe53a 		OBJECT IDENTIFIER ::= { vesSeries 37 }
	ves1616ft54		OBJECT IDENTIFIER ::= { vesSeries 38 }
	ves1616ctb54		OBJECT IDENTIFIER ::= { vesSeries 39 }
	ves1624ctb54		OBJECT IDENTIFIER ::= { vesSeries 40 }
	ves1624ftv55a		OBJECT IDENTIFIER ::= { vesSeries 41 }
	ves1608fe53a		OBJECT IDENTIFIER ::= { vesSeries 42 }
	ves1608fe57		OBJECT IDENTIFIER ::= { vesSeries 43 }
        ves1602fe57             OBJECT IDENTIFIER ::= { vesSeries 44 }
        ves1724-58b		OBJECT IDENTIFIER ::= { vesSeries 45 }
	ves1724-56		OBJECT IDENTIFIER ::= { vesSeries 46 }
        ves1724-56fanless	OBJECT IDENTIFIER ::= { vesSeries 47 }
	ves1724-55C		OBJECT IDENTIFIER ::= { vesSeries 48 }
	ves1724-58V		OBJECT IDENTIFIER ::= { vesSeries 49 }

	-- IES Series	
	iesSeriesCommon		OBJECT IDENTIFIER ::= { iesSeries 1 }			
	ies2000			OBJECT IDENTIFIER ::= { iesSeries 2 }
	ies3000			OBJECT IDENTIFIER ::= { iesSeries 3 }
	ies5000			OBJECT IDENTIFIER ::= { iesSeries 5 }
	ies5005			OBJECT IDENTIFIER ::= { iesSeries 7 }
	ies6000			OBJECT IDENTIFIER ::= { iesSeries 8 }
	ies5006			OBJECT IDENTIFIER ::= { iesSeries 9 }
	ies5106			OBJECT IDENTIFIER ::= { iesSeries 10 }
	ies5112			OBJECT IDENTIFIER ::= { iesSeries 11 }
	ies4005			OBJECT IDENTIFIER ::= { iesSeries 12 }
	ies5206			OBJECT IDENTIFIER ::= { iesSeries 13 }
	ies6100			OBJECT IDENTIFIER ::= { iesSeries 14 }
	ies4204			OBJECT IDENTIFIER ::= { iesSeries 15 }	
	ies5212			OBJECT IDENTIFIER ::= { iesSeries 16 }
	ies4105			OBJECT IDENTIFIER ::= { iesSeries 17 }
	ies6217			OBJECT IDENTIFIER ::= { iesSeries 18 }
			
	-- PON Series
	olt1308			OBJECT IDENTIFIER ::= { ponSeries 1 }
	lt20h			OBJECT IDENTIFIER ::= { ponSeries 2 }
	olt2300-12		OBJECT IDENTIFIER ::= { ponSeries 3 }
	olt1308s22		OBJECT IDENTIFIER ::= { ponSeries 4 }
	olt2300v3		OBJECT IDENTIFIER ::= { ponSeries 5 }
	eonu24240		OBJECT IDENTIFIER ::= { ponSeries 6 }
	eonu16160		OBJECT IDENTIFIER ::= { ponSeries 7 }
	eonu2400		OBJECT IDENTIFIER ::= { ponSeries 8 }
	eonu1600		OBJECT IDENTIFIER ::= { ponSeries 9 }

	-- VOP Series
	vopSeriesCommon		OBJECT IDENTIFIER ::= { vopSeries 1 }			
	vop1224-61		OBJECT IDENTIFIER ::= { vopSeries 2 }
	
	-- GPON Series
	gponSeriesCommon	OBJECT IDENTIFIER ::= { gponSeries 1 }			
	o-00240v-p		OBJECT IDENTIFIER ::= { gponSeries 2 }
	olt2412			OBJECT IDENTIFIER ::= { gponSeries 3 }
	
	-- GES Series			
	gesSeriesCommon		OBJECT IDENTIFIER ::= { gesSeries 1 }
	ges2104			OBJECT IDENTIFIER ::= { gesSeries 2 }
	ges1116			OBJECT IDENTIFIER ::= { gesSeries 3 }

	-- DSLAM products
	dslamCommon             OBJECT IDENTIFIER ::= { dslam 1 }

	-- ZyWALL series
	zywallCommon       	OBJECT IDENTIFIER ::= { zywall 1 }
	zywall1		      	OBJECT IDENTIFIER ::= { zywall 2 }
	zywall2		        OBJECT IDENTIFIER ::= { zywall 3 }
	zywall2w	      	OBJECT IDENTIFIER ::= { zywall 4 }
	zywall10	       	OBJECT IDENTIFIER ::= { zywall 5 }
	zywall10ii		    OBJECT IDENTIFIER ::= { zywall 6 }
	zywall10w		    OBJECT IDENTIFIER ::= { zywall 7 }
	zywall50	      	OBJECT IDENTIFIER ::= { zywall 8 }
	zywall100	      	OBJECT IDENTIFIER ::= { zywall 9 }
	zywall200	       	OBJECT IDENTIFIER ::= { zywall 10 }
	zywallidp10        	OBJECT IDENTIFIER ::= { zywall 11 }
	zywall5           	OBJECT IDENTIFIER ::= { zywall 12 }
	zywall30w         	OBJECT IDENTIFIER ::= { zywall 13 }
	zywall35           	OBJECT IDENTIFIER ::= { zywall 14 }
	zywall70           	OBJECT IDENTIFIER ::= { zywall 15 }
	zywall1000         	OBJECT IDENTIFIER ::= { zywall 16 }
	zywallCHT1         	OBJECT IDENTIFIER ::= { zywall 17 }
	zywallM70          	OBJECT IDENTIFIER ::= { zywall 18 }
	zywallP1           	OBJECT IDENTIFIER ::= { zywall 19 }
    zywallP2           	OBJECT IDENTIFIER ::= { zywall 20 }
	zywallM110         	OBJECT IDENTIFIER ::= { zywall 21 }

	-- ZyWALL ZLD series
	zywallZLDCommon		OBJECT IDENTIFIER ::= { zywall 22 }
	-- Service Gateway products
	serviceGWCommon			OBJECT IDENTIFIER ::= { serviceGateway 1 }
	vsg1000				OBJECT IDENTIFIER ::= { serviceGateway 2 }
	vsg1200				OBJECT IDENTIFIER ::= { serviceGateway 3 }
	vsg1200v2			OBJECT IDENTIFIER ::= { serviceGateway 4 }

	-- EMS products
	netAtlasEMS			OBJECT IDENTIFIER ::= { ems 1 }	
	
	-- Small Cell products
	smallCellCommon		OBJECT IDENTIFIER ::= { smallCell 1 }
	LMT3313				OBJECT IDENTIFIER ::= { smallCell 2 }
END
