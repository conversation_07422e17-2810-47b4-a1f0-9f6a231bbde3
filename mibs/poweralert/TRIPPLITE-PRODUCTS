-- ------------------------------------------------------
-- TRIPP LITE PRODUCTS MIB
-- Copyright (c) 1999-2018 Tripp Lite
-- All rights reserved
--
-- Revision History:
-- 03/13/2013 Consolidated for PAL v12.06
-- 04/14/2014 Consolidated for PAL v15.00
-- 12/04/2014 Created ATS section
-- 03/19/2015 Created tlpPduHeatsink and tlpAtsHeatsink
-- 04/20/2015 Added tlpAtsConfigVoltageRangeTable
-- 06/08/2015 Added tlpAtsInputBadVoltageThreshold
-- 08/19/2015 Added tlpAtsConfigThresholdTable
-- 10/16/2015 Added tlpAtsConfigSource#ReturnTime
-- 11/10/2015 Refactored tlpUps
-- 11/25/2015 Refactored UPS, PDU and ATS input sections
-- 01/08/2016 Added tlpAtsConfigVoltageRangeLimitsTable
-- 01/20/2016 Added tlpDeviceIdentUptime
-- 01/25/2016 Redefined tlpUpsAlarmLoadLevelAboveThreshold
-- 02/02/2016 Reworked tlpAgentAttributes
-- 03/31/2016 Expanded well known alarms
-- 06/22/2016 Refactored MIB to extend from tripplite.mib
-- 07/11/2016 Expanded tlpNotify for non-alarm notifications
-- 01/17/2017 Changed tlpUpsBatteryDetailCurrent to signed
-- 04/03/2017 Refactored Cooling section
-- 04/19/2017 Refactored tlpAtsConfigVoltageRangeTable
-- 05/23/2017 Added more objects to Cooling section
-- 07/31/2017 Added tlpPduConfigPhaseThresholdTable,tlpAtsConfigPhaseThresholdTable
---           and new alarms to support "Output Current Below Threshold" events
-- 08/30/2017 Updated the description of tlpPduIdentNumBreakers and tlpAtsIdentNumBreakers
-- 09/28/2017 Added tlpCoolingCompressorStatus
-- 11/30/2017 Added tlpAtsOutputFrequency and tlpPduOutputFrequency
-- 01/15/2018 Updated/Corrected some descriptions and typos
-- 03/19/2018 Added tlpCoolingWaterStatus
-- 04/25/2018 Obsoleted tlpAgentSnmpContactPrivPassword and tlpAgentSnmpContactAuthPassword
-- 04/30/2018 Added tlpUpsOutputVARating
-- 05/08/2018 Updated the description of tlpCoolingFanSpeed
-- 05/10/2018 Updated the UNIT definition of tlpUpsDeviceTemperatureF, tlpUpsDeviceTemperatureC,
--            tlpPduInputLowTransferVoltageLowerBound, tlpPduInputLowTransferVoltageUpperBound,
--            tlpPduInputHighTransferVoltageLowerBound and tlpPduInputHighTransferVoltageUpperBound
-- 07/17/2018 Added tlpPduOutputCalculatedPowerKVA, tlpPduOutputCalculatedPowerKW tlpPduOutputCalculated24hrEnergy,
--            tlpAtsOutputCalculatedPowerKVA, tlpAtsOutputCalculatedPowerKW and tlpAtsOutputCalculated24hrEnergy
-- 01/17/2019 Added tlpDeviceIdentFirmwareVersion2, tlpDeviceIdentFirmwareVersion3, tlpUpsDeviceLastACFailureReason,
--            tlpUpsDeviceLastShutdownReason, tlpUpsIdentNumFans, tlpUpsFanTable, tlpUpsInputWattHours,
--            tlpUpsControlResetWattHours, tlpDeviceIdentNvrID, Added more notification to support new device events
-- 05/06/2019 Added tlpUpsDeviceOutputCurrentPrecision
--            Updated the UNITS to "0.01 Amps" for tlpUpsOutputLineCurrent
--            Updated the description of tlpUpsOutputLineCurrent, tlpPduOutputCurrent, tlpPduOutputCurrentMin,
--            tlpPduOutputCurrentMax, tlpAtsOutputCurrent, tlpAtsOutputCurrentMin and tlpAtsOutputCurrentMax
-- 05/07/2019 Added tlpNotifyTest
-- 06/18/2019 Added tlpNotificationsDeviceUnhandledFault, tlpNotificationsUpsLineConnectRelayFaultShutdown,
--            tlpNotificationsUpsTransferToFaultBypass, tlpNotificationsUpsBypassPowerDistributionModuleDisconnected,
--            tlpNotificationsUpsManualBypassActivatedFromUnsupportedMode, tlpNotificationsUpsOnBatteryTimeoutShutdown,
--            tlpNotificationsUpsLowBatteryPercentageShutdown
-- 09/17/2019 Added tlpUpsIdentNumHeatsinks, tlpUpsIdentNumOutputContacts,tlpUpsIdentNumInputContacts,
--            tlpUpsOutputLineApparentPower, tlpUpsControlCancelSelfTest,  tlpUpsControlResetAllParameters,
--            tlpUpsConfigLineQualifyTime, tlpUpsConfigACPowerSenseType, tlpUpsOutputPowerFactor, tlpUpsBatteryTotalMinutesOnBattery
--            Added Tables: tlpUpsHeatsinkTable, tlpUpsOutputContactTable, tlpUpsInputContactTable, tlpUpsConfigVoltageTable.
--            tlpUpsConfigContactTable, tlpUpsConfigOutputContactTable, tlpUpsConfigInputContactTable.
--            Added more alarms to tlpAlarmsWellKnown section
--            Added 'testing' to tlpUpsOutputSource
--            Added UNITS and updated the description of tlpDeviceIdentCurrentUptime and tlpDeviceIdentTotalUptime
---------------

TRIPPLITE-PRODUCTS DEFINITIONS ::= BEGIN

IMPORTS
  DisplayString,
  TruthValue,
  TimeStamp,
  RowStatus
    FROM SNMPv2-TC
  sysUpTime
    FROM SNMPv2-MIB
  OBJECT-GROUP,
  NOTIFICATION-GROUP
    FROM SNMPv2-CONF
  MODULE-IDENTITY,
  OBJECT-IDENTITY,
  OBJECT-TYPE,
  NOTIFICATION-TYPE,
  Gauge32,
  Integer32,
  Unsigned32,
  enterprises,
  IpAddress,
  TimeTicks
    FROM SNMPv2-SMI
  tripplite
    FROM TRIPPLITE;

tlpProducts MODULE-IDENTITY
  LAST-UPDATED "201909270930Z"
  ORGANIZATION "Tripp Lite"
  CONTACT-INFO
    "Software Engineering
    Tripp Lite
    1111 W. 35th St.
    Chicago, IL 60609"
  DESCRIPTION
    "This MIB module defines MIB objects which provide mechanisms for
    remote management capabilities of Tripp Lite PowerAlert and related
    software."
  REVISION "201909270930Z"
  DESCRIPTION
    "Added tlpUpsIdentNumHeatsinks, tlpUpsIdentNumOutputContacts,tlpUpsIdentNumInputContacts,
    tlpUpsOutputLineApparentPower, tlpUpsControlCancelSelfTest,  tlpUpsControlResetAllParameters,
    tlpUpsConfigLineQualifyTime, tlpUpsConfigACPowerSenseType, tlpUpsOutputPowerFactor,
    Added Tables: tlpUpsHeatsinkTable, tlpUpsOutputContactTable, tlpUpsInputContactTable, tlpUpsConfigVoltageTable.
    tlpUpsConfigContactTable, tlpUpsConfigOutputContactTable,  tlpUpsConfigInputContactTable.
    Added more alarm entries to tlpAlarmsWellKnown section
	Added 'testing' to tlpUpsOutputSource
	Added UNITS and updated the description of tlpDeviceIdentCurrentUptime and tlpDeviceIdentTotalUptime"
  REVISION "201906180930Z"
  DESCRIPTION
    "Added tlpNotificationsDeviceUnhandledFault, tlpNotificationsUpsLineConnectRelayFaultShutdown,
    tlpNotificationsUpsTransferToFaultBypass, tlpNotificationsUpsBypassPowerDistributionModuleDisconnected,
    tlpNotificationsUpsManualBypassActivatedFromUnsupportedMode, tlpNotificationsUpsOnBatteryTimeoutShutdown,
    tlpNotificationsUpsLowBatteryPercentageShutdown"
  REVISION "201905071250Z"
  DESCRIPTION
    "Added tlpNotifyTest"
  REVISION "201905061030Z"
  DESCRIPTION
    "Updated the UNITS to 0.01 Amps for tlpUpsOutputLineCurrent
    Updated the description of tlpUpsOutputLineCurrent, tlpPduOutputCurrent, tlpPduOutputCurrentMin,
    tlpPduOutputCurrentMax, tlpAtsOutputCurrent, tlpAtsOutputCurrentMin and tlpAtsOutputCurrentMax"
  REVISION "201901171030Z"
  DESCRIPTION
      "Added tlpDeviceIdentFirmwareVersion2, tlpDeviceIdentFirmwareVersion3, tlpUpsDeviceLastACFailureReason,
       tlpUpsDeviceLastShutdownReason, tlpUpsIdentNumFans, tlpUpsFanTable, tlpUpsInputWattHours,
       tlpUpsControlResetWattHours, tlpDeviceIdentNvrID. Added more notification to support new device events"
  REVISION "201807131430Z"
  DESCRIPTION
      "Added tlpPduOutputCalculatedPowerKVA, tlpPduOutputCalculatedPowerKW, tlpPduOutputCalculated24hrEnergy,
	  tlpAtsOutputCalculatedPowerKVA, tlpAtsOutputCalculatedPowerKW and tlpAtsOutputCalculated24hrEnergy"
  REVISION "201805101430Z"
  DESCRIPTION
      "Updated the UNIT definition of tlpUpsDeviceTemperatureF, tlpUpsDeviceTemperatureC,
      tlpPduInputLowTransferVoltageLowerBound, tlpPduInputLowTransferVoltageUpperBound,
      tlpPduInputHighTransferVoltageLowerBound and tlpPduInputHighTransferVoltageUpperBound"
  REVISION  "201805081330Z"
  DESCRIPTION
      "Updated the description of tlpCoolingFanSpeed"
  REVISION "201804301530Z"
  DESCRIPTION
     "Added tlpUpsOutputVARating"
  REVISION "201804251330Z"
  DESCRIPTION
     "Obsoleted tlpAgentSnmpContactPrivPassword and tlpAgentSnmpContactAuthPassword"
  REVISION "201803190730Z"
  DESCRIPTION
     "Added tlpCoolingWaterStatus"
  REVISION "201801151130Z"
  DESCRIPTION
	"Updated/Corrected some descriptions and typos"
  REVISION "201712291330Z"
  DESCRIPTION
      "Updated the output current OIDs description with regards to precision"
  REVISION "201711301130Z"
  DESCRIPTION
     "Added tlpAtsOutputFrequency and tlpPduOutputFrequency"
  REVISION "201709290800Z"
  DESCRIPTION
     "Added tlpCoolingCompressorStatus"
  REVISION "201708300900Z"
  DESCRIPTION
     "Updated the description of tlpPduIdentNumBreakers and tlpAtsIdentNumBreakers"
  REVISION "201707310900Z"
  DESCRIPTION
	"Added tlpPduConfigThresholdTable and new alarms to support Output Current Below Threshold events"
  REVISION "201705231423Z"
  DESCRIPTION
    "Added more objects to Cooling section"
  REVISION "201704192230Z"
  DESCRIPTION
    "Refactored tlpAtsConfigVoltageRangeTable"
  REVISION "201704031533Z"
  DESCRIPTION
    "Refactored Cooling section"
  REVISION "201702281600Z"
  DESCRIPTION
    "Corrected spelling errors."
  REVISION "201701171250Z"
  DESCRIPTION
    "Changed tlpUpsBatteryDetailCurrent to signed with units of 0.01A.
    This value was previous not reported by a device using prior units
    of 0.1A; therefore, there no conflict exists with this change."
  REVISION "201607111215Z"
  DESCRIPTION
    "Expanded tlpNotify for non-alarm notifications"
  REVISION "201606221115Z"
  DESCRIPTION
    "Refactored MIB to extend from tripplite.mib"
  REVISION "201603161210Z"
  DESCRIPTION
    "Expanded well known alarms"
  REVISION "201602021115Z"
  DESCRIPTION
    "Redefined tlpUpsAlarmLoadLevelAboveThreshold"
  REVISION "201601251230Z"
  DESCRIPTION
    "Redefined Reworked tlpAgentAttributes"
  REVISION "201601201200Z"
  DESCRIPTION
    "Added tlpDeviceIdentUptime"
  REVISION "201601081140Z"
  DESCRIPTION
    "Added tlpAtsConfigVoltageRangeLimitsTable"
  REVISION "201511251300Z"
  DESCRIPTION
    "Refactored UPS, PDU and ATS input sections"
  REVISION "201511101300Z"
  DESCRIPTION
    "Refactored tlpUps"
  REVISION "201510161230Z"
  DESCRIPTION
    "Added tlpAtsConfigSource#ReturnTime"
  REVISION "201508191200Z"
  DESCRIPTION
    "Added tlpAtsConfigThresholdTable"
  REVISION "201412041000Z"
  DESCRIPTION
    "Created ATS device-specific section"
  REVISION "201404140900Z"
  DESCRIPTION
    "Consolidated and Released for PAL v12.07.007x"

  ::= { tripplite 1 }

-- tlpProducts                 OBJECT IDENTIFIER   ::= { tripplite 1 }

tlpHardware                 OBJECT IDENTIFIER   ::= { tlpProducts 1 }
tlpSoftware                 OBJECT IDENTIFIER   ::= { tlpProducts 2 }
tlpAlarms                   OBJECT IDENTIFIER   ::= { tlpProducts 3 }
tlpNotify                   OBJECT IDENTIFIER   ::= { tlpProducts 4 }

tlpDevice                   OBJECT IDENTIFIER   ::= { tlpHardware 1 }
tlpDeviceDetail             OBJECT IDENTIFIER   ::= { tlpHardware 2 }
tlpDeviceTypes              OBJECT IDENTIFIER   ::= { tlpHardware 3 }

tlpUps                      OBJECT IDENTIFIER   ::= { tlpDeviceTypes 1 }
tlpPdu                      OBJECT IDENTIFIER   ::= { tlpDeviceTypes 2 }
tlpEnvirosense              OBJECT IDENTIFIER   ::= { tlpDeviceTypes 3 }
tlpAts                      OBJECT IDENTIFIER   ::= { tlpDeviceTypes 4 }
tlpCooling                  OBJECT IDENTIFIER   ::= { tlpDeviceTypes 5 }
tlpKvm                      OBJECT IDENTIFIER   ::= { tlpDeviceTypes 6 }
tlpRackTrack                OBJECT IDENTIFIER   ::= { tlpDeviceTypes 7 }
tlpSwitch                   OBJECT IDENTIFIER   ::= { tlpDeviceTypes 8 }

tlpUpsIdent                 OBJECT IDENTIFIER   ::= { tlpUps 1 }
tlpUpsDevice                OBJECT IDENTIFIER   ::= { tlpUps 2 }
tlpUpsDetail                OBJECT IDENTIFIER   ::= { tlpUps 3 }
tlpUpsControl               OBJECT IDENTIFIER   ::= { tlpUps 4 }
tlpUpsConfig                OBJECT IDENTIFIER   ::= { tlpUps 5 }

tlpUpsBattery               OBJECT IDENTIFIER   ::= { tlpUpsDetail 1 }
tlpUpsInput                 OBJECT IDENTIFIER   ::= { tlpUpsDetail 2 }
tlpUpsOutput                OBJECT IDENTIFIER   ::= { tlpUpsDetail 3 }
tlpUpsBypass                OBJECT IDENTIFIER   ::= { tlpUpsDetail 4 }
tlpUpsOutlet                OBJECT IDENTIFIER   ::= { tlpUpsDetail 5 }
tlpUpsWatchdog              OBJECT IDENTIFIER   ::= { tlpUpsDetail 6 }
tlpUpsFan                   OBJECT IDENTIFIER   ::= { tlpUpsDetail 7 }
tlpUpsHeatsink              OBJECT IDENTIFIER   ::= { tlpUpsDetail 8 }
tlpUpsContact               OBJECT IDENTIFIER   ::= { tlpUpsDetail 9 }

tlpUpsInputContact          OBJECT IDENTIFIER   ::= { tlpUpsContact 1 }
tlpUpsOutputContact         OBJECT IDENTIFIER   ::= { tlpUpsContact 2 }

tlpPduIdent                 OBJECT IDENTIFIER   ::= { tlpPdu 1 }
tlpPduDevice                OBJECT IDENTIFIER   ::= { tlpPdu 2 }
tlpPduDetail                OBJECT IDENTIFIER   ::= { tlpPdu 3 }
tlpPduControl               OBJECT IDENTIFIER   ::= { tlpPdu 4 }
tlpPduConfig                OBJECT IDENTIFIER   ::= { tlpPdu 5 }

tlpPduInput                 OBJECT IDENTIFIER   ::= { tlpPduDetail 1 }
tlpPduOutput                OBJECT IDENTIFIER   ::= { tlpPduDetail 2 }
tlpPduOutlet                OBJECT IDENTIFIER   ::= { tlpPduDetail 3 }
tlpPduCircuit               OBJECT IDENTIFIER   ::= { tlpPduDetail 4 }
tlpPduBreaker               OBJECT IDENTIFIER   ::= { tlpPduDetail 5 }
tlpPduHeatsink              OBJECT IDENTIFIER   ::= { tlpPduDetail 6 }

tlpEnvIdent                 OBJECT IDENTIFIER   ::= { tlpEnvirosense 1 }
-- tlpEnvDevice             OBJECT IDENTIFIER   ::= { tlpEnvirosense 2 }
tlpEnvDetail                OBJECT IDENTIFIER   ::= { tlpEnvirosense 3 }
-- tlpEnvControl            OBJECT IDENTIFIER   ::= { tlpEnvirosense 4 }
tlpEnvConfig                OBJECT IDENTIFIER   ::= { tlpEnvirosense 5 }

tlpAtsIdent                 OBJECT IDENTIFIER   ::= { tlpAts 1 }
tlpAtsDevice                OBJECT IDENTIFIER   ::= { tlpAts 2 }
tlpAtsDetail                OBJECT IDENTIFIER   ::= { tlpAts 3 }
tlpAtsControl               OBJECT IDENTIFIER   ::= { tlpAts 4 }
tlpAtsConfig                OBJECT IDENTIFIER   ::= { tlpAts 5 }

tlpAtsInput                 OBJECT IDENTIFIER   ::= { tlpAtsDetail 1 }
tlpAtsOutput                OBJECT IDENTIFIER   ::= { tlpAtsDetail 2 }
tlpAtsOutlet                OBJECT IDENTIFIER   ::= { tlpAtsDetail 3 }
tlpAtsCircuit               OBJECT IDENTIFIER   ::= { tlpAtsDetail 4 }
tlpAtsBreaker               OBJECT IDENTIFIER   ::= { tlpAtsDetail 5 }
tlpAtsHeatsink              OBJECT IDENTIFIER   ::= { tlpAtsDetail 6 }

tlpCoolingIdent             OBJECT IDENTIFIER   ::= { tlpCooling 1 }
tlpCoolingDevice            OBJECT IDENTIFIER   ::= { tlpCooling 2 }
tlpCoolingDetail            OBJECT IDENTIFIER   ::= { tlpCooling 3 }
tlpCoolingControl           OBJECT IDENTIFIER   ::= { tlpCooling 4 }
tlpCoolingConfig            OBJECT IDENTIFIER   ::= { tlpCooling 5 }

tlpCoolingTemperature       OBJECT IDENTIFIER   ::= { tlpCoolingDetail 1 }
tlpCoolingPressure          OBJECT IDENTIFIER   ::= { tlpCoolingDetail 2 }

tlpKvmIdent                 OBJECT IDENTIFIER   ::= { tlpKvm 1 }
tlpKvmDevice                OBJECT IDENTIFIER   ::= { tlpKvm 2 }
tlpKvmDetail                OBJECT IDENTIFIER   ::= { tlpKvm 3 }
tlpKvmControl               OBJECT IDENTIFIER   ::= { tlpKvm 4 }
tlpKvmConfig                OBJECT IDENTIFIER   ::= { tlpKvm 5 }

tlpRackTrackIdent           OBJECT IDENTIFIER   ::= { tlpRackTrack 1 }
tlpRackTrackDevice          OBJECT IDENTIFIER   ::= { tlpRackTrack 2 }
tlpRackTrackDetail          OBJECT IDENTIFIER   ::= { tlpRackTrack 3 }
tlpRackTrackControl         OBJECT IDENTIFIER   ::= { tlpRackTrack 4 }
tlpRackTrackConfig          OBJECT IDENTIFIER   ::= { tlpRackTrack 5 }

tlpSwitchIdent              OBJECT IDENTIFIER   ::= { tlpSwitch 1 }
tlpSwitchDevice             OBJECT IDENTIFIER   ::= { tlpSwitch 2 }
tlpSwitchDetail             OBJECT IDENTIFIER   ::= { tlpSwitch 3 }
tlpSwitchControl            OBJECT IDENTIFIER   ::= { tlpSwitch 4 }
tlpSwitchConfig             OBJECT IDENTIFIER   ::= { tlpSwitch 5 }

tlpAgentDetails             OBJECT IDENTIFIER   ::= { tlpSoftware 1 }
tlpAgentSettings            OBJECT IDENTIFIER   ::= { tlpSoftware 2 }
tlpAgentContacts            OBJECT IDENTIFIER   ::= { tlpSoftware 3 }

tlpAgentIdent               OBJECT IDENTIFIER   ::= { tlpAgentDetails 1}
tlpAgentAttributes          OBJECT IDENTIFIER   ::= { tlpAgentDetails 2}

tlpAgentConfig              OBJECT IDENTIFIER   ::= { tlpAgentSettings 1}

tlpAgentEmailContacts       OBJECT IDENTIFIER   ::= { tlpAgentContacts 1}
tlpAgentSnmpContacts        OBJECT IDENTIFIER   ::= { tlpAgentContacts 2}

tlpAlarmsWellKnown          OBJECT IDENTIFIER   ::= { tlpAlarms 3 }
tlpAlarmControl             OBJECT IDENTIFIER   ::= { tlpAlarms 4 }

tlpAgentAlarms              OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 1 }
tlpDeviceAlarms             OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 2 }
tlpUpsAlarms                OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 3 }
tlpPduAlarms                OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 4 }
tlpEnvAlarms                OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 5 }
tlpAtsAlarms                OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 6 }
tlpCoolingAlarms            OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 7 }
tlpKvmAlarms                OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 8 }
tlpRackTrackAlarms          OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 9 }
tlpSwitchAlarms             OBJECT IDENTIFIER   ::= { tlpAlarmsWellKnown 10 }


tlpNotifications            OBJECT IDENTIFIER   ::= { tlpNotify 1 }
tlpNotificationsEvent           OBJECT IDENTIFIER   ::= { tlpNotify 2 }
tlpNotificationsEventParameters OBJECT IDENTIFIER   ::= { tlpNotify 3 }

tlpDeviceNumDevices OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of devices that this agent is currently managing."
    ::= { tlpDevice 1 }

tlpDeviceTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of device table entries.  The number of entries
        is given by the value of tlpDevicesNumDevices."
    ::= { tlpDevice 2 }

tlpDeviceEntry OBJECT-TYPE
    SYNTAX TlpDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular device
        managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpDeviceTable 1 }

TlpDeviceEntry ::= SEQUENCE {
    tlpDeviceIndex                  Unsigned32,
    tlpDeviceRowStatus              RowStatus,
    tlpDeviceType                   OBJECT IDENTIFIER,
    tlpDeviceManufacturer           DisplayString,
    tlpDeviceModel                  DisplayString,
    tlpDeviceName                   DisplayString,
    tlpDeviceID                     INTEGER,
    tlpDeviceLocation               DisplayString,
    tlpDeviceRegion                 DisplayString,
    tlpDeviceStatus                 INTEGER }

tlpDeviceIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the device."
    ::= { tlpDeviceEntry 1 }

tlpDeviceRowStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Used with table edits to indicate the status of a given row in
        tlpDeviceTable."
    ::= { tlpDeviceEntry 2 }

tlpDeviceType OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of device. One of the appropriate hardware types:
        ups, pdu, envirosense, etc."
    ::= { tlpDeviceEntry 3 }

tlpDeviceManufacturer OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The name of the manufacturer."
    ::= { tlpDeviceEntry 4 }

tlpDeviceModel OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The model designation."
    ::= { tlpDeviceEntry 5 }

tlpDeviceName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-supplied name for the device."
    ::= { tlpDeviceEntry 6 }

tlpDeviceID OBJECT-TYPE
    SYNTAX INTEGER (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-supplied ID for the device."
    ::= { tlpDeviceEntry 7 }

tlpDeviceLocation OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-supplied location for the device."
    ::= { tlpDeviceEntry 8 }

tlpDeviceRegion OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-supplied region for the device."
    ::= { tlpDeviceEntry 9 }

tlpDeviceStatus OBJECT-TYPE
    SYNTAX INTEGER {
        none(0),
        critical(1),
        warning(2),
        info(3),
        status(4),
        offline(5),
        custom(6),
        configuration(7) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The currently most critical alarm status for the device."
    ::= { tlpDeviceEntry 10 }

tlpDeviceIdentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpDeviceIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The identity details of each device in device table. The number
        of entries is given by the value of tlpDevicesNumDevices."
    ::= { tlpDeviceDetail 1 }

tlpDeviceIdentEntry OBJECT-TYPE
    SYNTAX TlpDeviceIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing details for a particular device."
        INDEX { tlpDeviceIndex }
    ::= { tlpDeviceIdentTable 1 }

TlpDeviceIdentEntry ::= SEQUENCE {
    tlpDeviceIdentProtocol              DisplayString,
    tlpDeviceIdentCommPortType          INTEGER,
    tlpDeviceIdentCommPortName          DisplayString,
    tlpDeviceIdentFirmwareVersion       DisplayString,
    tlpDeviceIdentSerialNum             DisplayString,
    tlpDeviceIdentDateInstalled         DisplayString,
    tlpDeviceIdentHardwareVersion       DisplayString,
    tlpDeviceIdentCurrentUptime         DisplayString,
    tlpDeviceIdentTotalUptime           DisplayString,
    tlpDeviceIdentFirmwareVersion2      DisplayString,
    tlpDeviceIdentFirmwareVersion3      DisplayString,
    tlpDeviceIdentNvrID                 DisplayString }

tlpDeviceIdentProtocol OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The Tripplite protocol used to communicate with the device."
    ::= { tlpDeviceIdentEntry 1 }

tlpDeviceIdentCommPortType OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        serial(1),
        usb(2),
        hid(3),
        simulated(4),
        unittest(5) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of communications port used to connect with the device."
    ::= { tlpDeviceIdentEntry 2 }

tlpDeviceIdentCommPortName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The name of the communications port."
    ::= { tlpDeviceIdentEntry 3 }

tlpDeviceIdentFirmwareVersion OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The firmware version of the device."
    ::= { tlpDeviceIdentEntry 4 }

tlpDeviceIdentSerialNum OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The serial number of the device."
    ::= { tlpDeviceIdentEntry 5 }

tlpDeviceIdentDateInstalled OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The installation date for this device in the format of mm/dd/yyyy."
    ::= { tlpDeviceIdentEntry 6 }

tlpDeviceIdentHardwareVersion OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The hardware revision of the device. This value will return ERROR_NO_SUCH_NAME
        if not supported by the device."
    ::= { tlpDeviceIdentEntry 7 }

tlpDeviceIdentCurrentUptime OBJECT-TYPE
    SYNTAX DisplayString
    UNITS "hours"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The device uptime since its last startup. This value will return
        ERROR_NO_SUCH_NAME if not supported by the device."
    ::= { tlpDeviceIdentEntry 8 }

tlpDeviceIdentTotalUptime OBJECT-TYPE
    SYNTAX DisplayString
    UNITS "days"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The cumulative (total) uptime for the device. This value will return
        ERROR_NO_SUCH_NAME if not supported by the device."
    ::= { tlpDeviceIdentEntry 9 }

tlpDeviceIdentFirmwareVersion2 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The firmware version of second IC on the device. This value will return
		ERROR_NO_SUCH_NAME if not supported by the device."
    ::= { tlpDeviceIdentEntry 10 }

tlpDeviceIdentFirmwareVersion3 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The firmware version of third IC on the device. This value will return
		ERROR_NO_SUCH_NAME if not supported by the device."
    ::= { tlpDeviceIdentEntry 11 }

tlpDeviceIdentNvrID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The id of NVRAM. This value will return ERROR_NO_SUCH_NAME if not supported by the device."
    ::= { tlpDeviceIdentEntry 12 }

-- ups

tlpUpsIdentNumUps OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of ups devices that this agent is currently managing."
    ::= { tlpUpsIdent 1 }

tlpUpsIdentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The identity details of each ups in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are upss."
    ::= { tlpUpsIdent 2 }

tlpUpsIdentEntry OBJECT-TYPE
    SYNTAX TlpUpsIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing details for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsIdentTable 1 }

TlpUpsIdentEntry ::= SEQUENCE {
    tlpUpsIdentNumInputs                Unsigned32,
    tlpUpsIdentNumOutputs               Unsigned32,
    tlpUpsIdentNumBypass                Unsigned32,
    tlpUpsIdentNumPhases                Unsigned32,
    tlpUpsIdentNumOutlets               Unsigned32,
    tlpUpsIdentNumOutletGroups          Unsigned32,
    tlpUpsIdentNumBatteryPacks          Unsigned32,
    tlpUpsIdentNumFans                  Unsigned32,
    tlpUpsIdentNumHeatsinks             Unsigned32,
    tlpUpsIdentNumInputContacts         Unsigned32,
    tlpUpsIdentNumOutputContacts        Unsigned32 }

tlpUpsIdentNumInputs OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of input lines supported by the ups."
    ::= { tlpUpsIdentEntry 1 }

tlpUpsIdentNumOutputs OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of output lines supported by the ups."
    ::= { tlpUpsIdentEntry 2 }

tlpUpsIdentNumBypass OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of bypass lines supported by the ups."
    ::= { tlpUpsIdentEntry 3 }

tlpUpsIdentNumPhases OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of phases supported by the ups."
    ::= { tlpUpsIdentEntry 4 }

tlpUpsIdentNumOutlets OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of receptacles supported by the ups."
    ::= { tlpUpsIdentEntry 5 }

tlpUpsIdentNumOutletGroups OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of receptacle groups supported by the ups."
    ::= { tlpUpsIdentEntry 6 }

tlpUpsIdentNumBatteryPacks OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of battery packs, internal and external combined,
        supported by the ups."
    ::= { tlpUpsIdentEntry 7 }

tlpUpsIdentNumFans OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of fans supported by the ups."
    ::= { tlpUpsIdentEntry 8 }

tlpUpsIdentNumHeatsinks OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of heatsink thermistors supported by the ups."
    ::= { tlpUpsIdentEntry 9 }

tlpUpsIdentNumInputContacts OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of input contacts supported by the ups."
    ::= { tlpUpsIdentEntry 10 }

tlpUpsIdentNumOutputContacts OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of output contacts supported by the ups."
    ::= { tlpUpsIdentEntry 11 }

tlpUpsSupportsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsSupportsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The supported details of each ups in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are upss."
    ::= { tlpUpsIdent 3 }

tlpUpsSupportsEntry OBJECT-TYPE
    SYNTAX TlpUpsSupportsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing supported details for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsSupportsTable 1 }

TlpUpsSupportsEntry ::= SEQUENCE {
    tlpUpsSupportsEnergywise            TruthValue,
    tlpUpsSupportsRampShed              TruthValue,
    tlpUpsSupportsOutletGroup           TruthValue,
    tlpUpsSupportsOutletCurrentPower    TruthValue,
    tlpUpsSupportsOutletVoltage         TruthValue }

tlpUpsSupportsEnergywise OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ups supports Cisco EnergyWise."
    ::= { tlpUpsSupportsEntry 1 }

tlpUpsSupportsRampShed OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ups supports ramping and shedding."
    ::= { tlpUpsSupportsEntry 2 }

tlpUpsSupportsOutletGroup OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ups supports outlet groups."
    ::= { tlpUpsSupportsEntry 3 }

tlpUpsSupportsOutletCurrentPower OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ups reports individual receptacle current
        and power measurements. Devices that do not support individual
        receptacle measurements will always return ERROR_NO_SUCH_NAME for
        tlpUpsOutletCurrent and tlpUpsOutletPower."
    ::= { tlpUpsSupportsEntry 4 }

tlpUpsSupportsOutletVoltage OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ups reports individual receptacle voltage
        measurements. Devices that do not support individual receptacle
        measurements will always return ERROR_NO_SUCH_NAME for
        tlpUpsOutletVoltage."
    ::= { tlpUpsSupportsEntry 5 }

tlpUpsDeviceTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The device details of each ups in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are upss."
    ::= { tlpUpsDevice 1 }

tlpUpsDeviceEntry OBJECT-TYPE
    SYNTAX TlpUpsDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing device details for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsDeviceTable 1 }

TlpUpsDeviceEntry ::= SEQUENCE {
    tlpUpsDeviceMainLoadState           INTEGER,
    tlpUpsDeviceMainLoadControllable    TruthValue,
    tlpUpsDeviceMainLoadCommand         INTEGER,
    tlpUpsDevicePowerOnDelay            Integer32,
    tlpUpsDeviceTestDate                DisplayString,
    tlpUpsDeviceTestResultsStatus       INTEGER,
    tlpUpsDeviceTemperatureC            Integer32,
    tlpUpsDeviceTemperatureF            Integer32,
    tlpUpsDeviceLastACFailureReason     INTEGER,
    tlpUpsDeviceLastShutdownReason      INTEGER,
    tlpUpsDeviceOutputCurrentPrecision  INTEGER }

tlpUpsDeviceMainLoadState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the main output of the device."
    ::= { tlpUpsDeviceEntry 1 }

tlpUpsDeviceMainLoadControllable OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not the main output is controllable, and whether
        the ups can be turned off and on."
    ::= { tlpUpsDeviceEntry 2 }

tlpUpsDeviceMainLoadCommand OBJECT-TYPE
    SYNTAX INTEGER {
        idle(0),
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value to
        turnOff(1) will turn off the main output of the ups. Setting this
        value to turnOn(2) will turn on the main output of the ups. Setting
        this value to cycle(3) will turn off the main output, then turn it
        back on."
    ::= { tlpUpsDeviceEntry 3 }

tlpUpsDevicePowerOnDelay OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The configurable delay value, which sets the amount of additional
        time a given device will wait before it connects to a valid AC
        source. By default the delay value is set to 0."
    ::= { tlpUpsDeviceEntry 4 }

tlpUpsDeviceTestDate OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The date of the last self-test run on the ups, in the format YYYY-MM-DD."
    ::= { tlpUpsDeviceEntry 5 }

tlpUpsDeviceTestResultsStatus OBJECT-TYPE
    SYNTAX INTEGER {
        noTest(0),
        doneAndPassed(1),
        doneAndWarning(2),
        doneAndError(3),
        aborted(4),
        inProgress(5),
        noTestInitiated(6),
        badBattery(7),
        overCurrent(8),
        batteryFailed(9) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The result of the last self-test run on the ups."
    ::= { tlpUpsDeviceEntry 6 }

tlpUpsDeviceTemperatureC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the ups casing. Temperature
        measurements are not supported on all models. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsDeviceEntry 7 }

tlpUpsDeviceTemperatureF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the ups casing. Temperature
        measurements are not supported on all models. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsDeviceEntry 8 }

tlpUpsDeviceLastACFailureReason OBJECT-TYPE
    SYNTAX INTEGER {
        noACFailureSinceReset(0),
        blackout(1),
        instantaneousHighLineFrequency(2),
        highLineVoltage(3),
        lowLineVoltage(4),
        averageFrequencyFault(5),
        instantaneousLowLineFrequency(6),
		delayedBrownout(7) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The last AC failure reason."
    ::= { tlpUpsDeviceEntry 9 }

tlpUpsDeviceLastShutdownReason OBJECT-TYPE
    SYNTAX INTEGER {
	    noShutdownSinceReset(0),
        onOffButtonShutdown(1),
        averageVbattSmallerThenLVCShutdown(2),
        fastVbattKeepaliveVoltageFastShutdown(3),
        averageCurrentLimitShutdown(4),
        fastCurrentLimitShutdown(5),
        heavyLoadThermalTimeLimitShutdown(6),
        fixedInvertModeTimeLimitShutdown(7),
        highBatteryVoltageFault(8),
        averageVbattKeepaliveVoltageShutdown(9),
        shutdownCommandWithAutorestartEnabled(10),
        shutdownCommandWithoutAutorestartEnabled(11),
        lineConnectRelayFault(12),
        epoInvertMode(13),
        epoRebootMode(14),
        invalidLineStandbyMode(15),
        chargerLimitMinBatteryVoltage(16),
        chargerLimitMaxBatteryVoltage(17),
        invalidLineDelayedWakeupAutorestartEnabled(18),
        invalidLineDelayedWakeupAutorestartDisabled(19),
        minBatteryVoltagePriorDelayedWakeupAutorestartEnabled(20),
        minBatteryVoltagePriorDelayedWakeupAutorestartDisabled(21),
        lvcShutdownAutorestartEnabled(22),
        lvcShutdownAutorestartDisabled(23),
        overloadShutdownAutorestartEnabled(24),
        overloadShutdownAutorestartDisabled(25),
        overTemperatureShutdownAutorestartEnabled(26),
        overTemperatureShutdownAutorestartDisabled(27),
        onOffButtonShutdownAutorestartEnabled(28),
        onOffButtonShutdownAutorestartDisabled(29),
        batteryReportedFault(30),
        batteryCommunicationLost(31),
        batteryJumpStartChargerTimeoutError(32)  }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The last unit shut down reason."
    ::= { tlpUpsDeviceEntry 10 }

tlpUpsDeviceOutputCurrentPrecision OBJECT-TYPE
    SYNTAX INTEGER {
        wholeNumber(0),
        tenths(1),
        hundredths(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Output current is reported by  tlpUpsOutputLineCurrent,  tlpUpsOutputLineCurrentMin and
        tlpUpsOutputLineCurrentMax. These values are all reported in hundredths(0.01A),which
        accommodates all models;  however, on any given device the actual precision may vary. This
        value describes the actual precision, which can be used as a divisor against the output current
        values to obtain the true output current measurement. For example, if tlpUpsOutputLineCurrent reports
        530 and tlpUpsDeviceOutputCurrentPrecision is tenths(1), then the actual output current is 5.3."
    ::= { tlpUpsDeviceEntry 11 }

tlpUpsBatterySummaryTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsBatterySummaryEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The battery system summary of each ups in the device
        table. This is a sparse dependent table, and will contain the
        number of entries based upon the number of upss managed by
        the agent as reported by tlpUpsIdentNumUps."
    ::= { tlpUpsBattery 1 }

tlpUpsBatterySummaryEntry OBJECT-TYPE
    SYNTAX TlpUpsBatterySummaryEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        battery system managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsBatterySummaryTable 1 }

TlpUpsBatterySummaryEntry ::= SEQUENCE {
    tlpUpsBatteryStatus                     INTEGER,
    tlpUpsSecondsOnBattery                  Unsigned32,
    tlpUpsEstimatedMinutesRemaining         Unsigned32,
    tlpUpsEstimatedChargeRemaining          INTEGER,
    tlpUpsBatteryRunTimeRemaining           TimeTicks,
    tlpUpsBatteryTotalMinutesOnBattery  	Unsigned32 }

tlpUpsBatteryStatus OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(1),
        batteryNormal(2),
        batteryLow(3),
        batteryDepleted(4) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The indication of the capacity remaining in the UPS system's
        batteries. A value of batteryNormal indicates that the
        remaining run-time is greater than tlpUpsConfigLowBattTime. A
        value of batteryLow indicates that the remaining battery run-time
        is less than or equal to tlpUpsConfigLowBattTime.  A value of
        batteryDepleted indicates that the UPS will be unable to sustain
        the present load when and if the utility power is lost (including
        the possibility that the utility power is currently absent and
        the UPS is unable to sustain the output)."
    ::= { tlpUpsBatterySummaryEntry 1 }

tlpUpsSecondsOnBattery OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "seconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "If the unit is on battery power, the elapsed time since the UPS
        last switched to battery power, or the time since the network
        management subsystem was last restarted, whichever is less. Zero
        shall be returned if the unit is not on battery power."
    ::= { tlpUpsBatterySummaryEntry 2 }

tlpUpsEstimatedMinutesRemaining OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "minutes"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An estimate of the time to battery charge depletion
        under the present load conditions if the utility power
        is off and remains off, or if it were to be lost and
        remain off."
    ::= { tlpUpsBatterySummaryEntry 3 }

tlpUpsEstimatedChargeRemaining OBJECT-TYPE
    SYNTAX INTEGER (0..100)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An estimate of the battery charge remaining expressed
        as a percent of full charge."
    ::= { tlpUpsBatterySummaryEntry 4 }

tlpUpsBatteryRunTimeRemaining OBJECT-TYPE
    SYNTAX TimeTicks
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The run time remaining before batteries are exhausted. If the device
        does not support run time remaining, then the agent reports
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatterySummaryEntry 5 }

tlpUpsBatteryTotalMinutesOnBattery OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "minutes"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total accumulative minutes the ups has run on the battery."
    ::= { tlpUpsBatterySummaryEntry 6 }

tlpUpsBatteryDetailTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsBatteryDetailEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The battery detail of each ups in the device table. This is a sparse
        dependent table, and will contain the number of entries based upon the
        number of upss managed by the agent as reported by tlpUpsIdentNumUps."
    ::= { tlpUpsBattery 2 }

tlpUpsBatteryDetailEntry OBJECT-TYPE
    SYNTAX TlpUpsBatteryDetailEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        battery system managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsBatteryDetailTable 1 }

TlpUpsBatteryDetailEntry ::= SEQUENCE {
    tlpUpsBatteryDetailVoltage              Unsigned32,
    tlpUpsBatteryDetailCurrent              Integer32,
    tlpUpsBatteryDetailCapacity             INTEGER,
    tlpUpsBatteryDetailCharge               INTEGER,
    tlpUpsBatteryDetailChargerStatus        INTEGER }

tlpUpsBatteryDetailVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volt DC"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present battery voltage."
    ::= { tlpUpsBatteryDetailEntry 1 }

tlpUpsBatteryDetailCurrent OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps DC"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present battery current."
    ::= { tlpUpsBatteryDetailEntry 2 }

tlpUpsBatteryDetailCapacity OBJECT-TYPE
    SYNTAX INTEGER (0..100)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The estimated remaining battery capacity expressed as a percent of
        full capacity."
    ::= { tlpUpsBatteryDetailEntry 3 }

tlpUpsBatteryDetailCharge OBJECT-TYPE
    SYNTAX INTEGER {
        floating(0),
        charging(1),
        resting(2),
        discharging(3),
        normal(4),
        standby(5) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes the charge state of the battery."
    ::= { tlpUpsBatteryDetailEntry 4 }

tlpUpsBatteryDetailChargerStatus OBJECT-TYPE
    SYNTAX INTEGER {
        ok(0),
        inFaultCondition(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The status of the battery charger. If the device does not support this
        value, then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryDetailEntry 5 }

tlpUpsBatteryPackIdentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsBatteryIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The battery identification details for each ups in the device
        table. This is a sparse dependent table, and will contain the
        number of entries based upon the number of upss managed by the
        agent as reported by upsIdentNumUps and the number of battery
        battery packs per each as reported by tlpUpsNumBatteryPacks."
    ::= { tlpUpsBattery 3 }

tlpUpsBatteryPackIdentEntry OBJECT-TYPE
    SYNTAX TlpUpsBatteryIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        battery pack managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsBatteryPackIdentIndex }
    ::= { tlpUpsBatteryPackIdentTable 1 }

TlpUpsBatteryIdentEntry ::= SEQUENCE {
    tlpUpsBatteryPackIdentIndex             Unsigned32,
    tlpUpsBatteryPackIdentManufacturer      DisplayString,
    tlpUpsBatteryPackIdentModel             DisplayString,
    tlpUpsBatteryPackIdentSerialNum         DisplayString,
    tlpUpsBatteryPackIdentFirmware          DisplayString,
    tlpUpsBatteryPackIdentSKU               DisplayString }

tlpUpsBatteryPackIdentIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups battery pack."
    ::= { tlpUpsBatteryPackIdentEntry 1 }

tlpUpsBatteryPackIdentManufacturer OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The manufacturer of the battery pack. If the device does not
        support this value, then the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackIdentEntry 2 }

tlpUpsBatteryPackIdentModel OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The model or device name of the battery pack. If the device does not
        support this value, then the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackIdentEntry 3 }

tlpUpsBatteryPackIdentSerialNum OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The serial number of the battery pack. If the device does not support
        this value, then the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackIdentEntry 4 }

tlpUpsBatteryPackIdentFirmware OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The firmware of the battery pack. If the device does not support this
        value, then the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackIdentEntry 5 }

tlpUpsBatteryPackIdentSKU OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The SKU of the battery pack. If the device does not support this value,
        then the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackIdentEntry 6 }

tlpUpsBatteryPackConfigTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsBatteryConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The battery configuration details for each ups in the device
        table. This is a sparse dependent table, and will contain the
        number of entries based upon the number of upss managed by the
        agent as reported by upsIdentNumUps and the number of battery
        battery packs per each as reported by tlpUpsNumBatteryPacks."
    ::= { tlpUpsBattery 4 }

tlpUpsBatteryPackConfigEntry OBJECT-TYPE
    SYNTAX TlpUpsBatteryConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        battery pack managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsBatteryPackIdentIndex }
    ::= { tlpUpsBatteryPackConfigTable 1 }

TlpUpsBatteryConfigEntry ::= SEQUENCE {
    tlpUpsBatteryPackConfigChemistry            INTEGER,
    tlpUpsBatteryPackConfigStyle                INTEGER,
    tlpUpsBatteryPackConfigLocation             INTEGER,
    tlpUpsBatteryPackConfigStrings              Unsigned32,
    tlpUpsBatteryPackConfigBatteriesPerString   Unsigned32,
    tlpUpsBatteryPackConfigCellsPerBattery      INTEGER,
    tlpUpsBatteryPackConfigNumBatteries         Unsigned32,
    tlpUpsBatteryPackConfigCapacityUnits        INTEGER,
    tlpUpsBatteryPackConfigDesignCapacity       Unsigned32,
    tlpUpsBatteryPackConfigCellCapacity         Unsigned32,
    tlpUpsBatteryPackConfigMinCellVoltage       Unsigned32,
    tlpUpsBatteryPackConfigMaxCellVoltage       Unsigned32 }

tlpUpsBatteryPackConfigChemistry OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        leadAcid(1),
        nickelCadmium(2),
        lithiumIon(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of battery pack."
    ::= { tlpUpsBatteryPackConfigEntry 1 }

tlpUpsBatteryPackConfigStyle OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        nonsmart(1),
        smart(2),
        bms(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The style of battery pack."
    ::= { tlpUpsBatteryPackConfigEntry 2 }

tlpUpsBatteryPackConfigLocation OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        internal(1),
        external(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The location of the battery pack."
    ::= { tlpUpsBatteryPackConfigEntry 3 }

tlpUpsBatteryPackConfigStrings OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of series strings in parallel contained in the battery
        pack. If the device does not support this value, then the agent will
        report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 4 }

tlpUpsBatteryPackConfigBatteriesPerString OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of battery per each string in the battery pack. If the
        device does not support this value, then the agent will report
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 5 }

tlpUpsBatteryPackConfigCellsPerBattery OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        one(1),
        two(2),
        four(4),
        six(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of cells per battery.."
    ::= { tlpUpsBatteryPackConfigEntry 6 }

tlpUpsBatteryPackConfigNumBatteries OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total number of batteries contained in the battery pack. If the device
        does not support this value, then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 7 }

tlpUpsBatteryPackConfigCapacityUnits OBJECT-TYPE
    SYNTAX INTEGER {
        mAHr(0),
        mWHr(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The units of measurement used for tlpUpsBatteryPackConfigDesignCapacity and
        tlpUpsBatteryPackConfigCellCapacity. If supported, this value will report either
        mAHR(1), amps per hour, or mWhr(1), watts per hour. If the device does not
        support this value, then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 8 }

tlpUpsBatteryPackConfigDesignCapacity OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The theoretical or nominal capacity of the battery pack in the measurement
        defined by tlpUpsBatteryPackConfigCapacityUnits. If the device does not support
        this value, then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 9 }

tlpUpsBatteryPackConfigCellCapacity OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The capacity of the batteries in the measurement defined by
        tlpUpsBatteryPackConfigCapacityUnits. If the device does not support this value,
        then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 10 }

tlpUpsBatteryPackConfigMinCellVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum cell voltage for the batteries. If the device does not support
        this value, then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 11 }

tlpUpsBatteryPackConfigMaxCellVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum cell voltage for the batteries. If the device does not support
        this value, then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackConfigEntry 12 }

tlpUpsBatteryPackDetailTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsBatteryPackDetailEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The details of each battery pack for each ups in the device
        table. This is a sparse dependent table, and will contain the
        number of entries based upon the number of upss and battery
        packs managed by the agent as reported by tlpUpsIdentNumUps
        and tlpUpsIdentNumBatteryPacks."
    ::= { tlpUpsBattery 5 }

tlpUpsBatteryPackDetailEntry OBJECT-TYPE
    SYNTAX TlpUpsBatteryPackDetailEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        battery system managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsBatteryPackIdentIndex }
    ::= { tlpUpsBatteryPackDetailTable 1 }

TlpUpsBatteryPackDetailEntry ::= SEQUENCE {
    tlpUpsBatteryPackDetailCondition            INTEGER,
    tlpUpsBatteryPackDetailTemperatureC         Unsigned32,
    tlpUpsBatteryPackDetailTemperatureF         Unsigned32,
    tlpUpsBatteryPackDetailAge                  Unsigned32,
    tlpUpsBatteryPackDetailLastReplaceDate      DisplayString,
    tlpUpsBatteryPackDetailNextReplaceDate      DisplayString,
    tlpUpsBatteryPackDetailCycleCount           Unsigned32 }

tlpUpsBatteryPackDetailCondition OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        good(1),
        weak(2),
        bad(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The condition of the battery. A bad battery should be replaced."
    ::= { tlpUpsBatteryPackDetailEntry 1 }

tlpUpsBatteryPackDetailTemperatureC OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the UPS Battery casing."
    ::= { tlpUpsBatteryPackDetailEntry 2 }

tlpUpsBatteryPackDetailTemperatureF OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the UPS Battery casing."
    ::= { tlpUpsBatteryPackDetailEntry 3 }

tlpUpsBatteryPackDetailAge OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Years"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The age of the battery in years. This is equal to the current
        date minus tlpUpsBatteryPackIdentInstallDate."
    ::= { tlpUpsBatteryPackDetailEntry 4 }

tlpUpsBatteryPackDetailLastReplaceDate OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The date when the batteries were last replaced in YYYYMMDD format."
    ::= { tlpUpsBatteryPackDetailEntry 5 }

tlpUpsBatteryPackDetailNextReplaceDate OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The date when the batteries should next be replaced in YYYYMMDD
        format."
    ::= { tlpUpsBatteryPackDetailEntry 6 }

tlpUpsBatteryPackDetailCycleCount OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of times the battery went into invert mode. This number
        is incremented by 1 each time the unit goes on battery mode (with
        the exception of Self Test mode). If the device does not support this
        value, then the agent will report ERROR_NO_SUCH_NAME."
    ::= { tlpUpsBatteryPackDetailEntry 7 }

tlpUpsInputTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsInputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input details of each ups in the device table. This is
        a sparse dependent table, and will contain the number of entries
        based upon the number of upss managed by the agent as reported
        by tlpUpsIdentNumUps."
    ::= { tlpUpsInput 1 }

tlpUpsInputEntry OBJECT-TYPE
    SYNTAX TlpUpsInputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to all input lines per
        each ups managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsInputTable 1 }

TlpUpsInputEntry ::= SEQUENCE {
    tlpUpsInputLineBads                           INTEGER,
    tlpUpsInputNominalVoltage                     Unsigned32,
    tlpUpsInputNominalFrequency                   Unsigned32,
    tlpUpsInputLowTransferVoltage                 Unsigned32,
    tlpUpsInputLowTransferVoltageLowerBound       Unsigned32,
    tlpUpsInputLowTransferVoltageUpperBound       Unsigned32,
    tlpUpsInputHighTransferVoltage                Unsigned32,
    tlpUpsInputHighTransferVoltageLowerBound      Unsigned32,
    tlpUpsInputHighTransferVoltageUpperBound      Unsigned32,
    tlpUpsInputWattHours                          Unsigned32,
    tlpUpsInputLowTransferVoltageResetTolerance   Unsigned32,
    tlpUpsInputHighTransferVoltageResetTolerance  Unsigned32 }

tlpUpsInputLineBads OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A count of the number of times the input entered an out-of-tolerance
        condition as defined by the manufacturer.  This count is incremented
        by one each time the input transitions from zero out-of-tolerance
        lines to one or more input lines out-of-tolerance. This value is not
        supported on all units. If the ups does not support this value, then
        the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsInputEntry 1 }

tlpUpsInputNominalVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsInputEntry 2 }

tlpUpsInputNominalFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line frequency. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsInputEntry 3 }

tlpUpsInputLowTransferVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum line voltage allowed before the ups system transfers to
         battery backup."
    ::= { tlpUpsInputEntry 4 }

tlpUpsInputLowTransferVoltageLowerBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The lower boundary value of tlpUpsInputLowTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpUpsInputEntry 5 }

tlpUpsInputLowTransferVoltageUpperBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The upper boundary value of tlpUpsInputLowTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpUpsInputEntry 6 }

tlpUpsInputHighTransferVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum line voltage allowed before the ups system transfers to
        battery backup."
    ::= { tlpUpsInputEntry 7 }

tlpUpsInputHighTransferVoltageLowerBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The lower boundary value of tlpUpsInputHighTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpUpsInputEntry 8 }

tlpUpsInputHighTransferVoltageUpperBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The upper boundary value of tlpUpsInputHighTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpUpsInputEntry 9 }

tlpUpsInputWattHours OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "WH"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of watt hours of energy utilized in the past 24 hours. Use
		tlpUpsControlResetWattHours to reset the watt hours calculation algorithm."
    ::= { tlpUpsInputEntry 10 }

tlpUpsInputLowTransferVoltageResetTolerance OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "If a unit is transferred to battery backup due to under-voltage, the
        line voltage has to rise above this value from tlpUpsConfigLowVoltageTransfer,
        before the unit switched back to the AC input."
    ::= { tlpUpsInputEntry 11 }

tlpUpsInputHighTransferVoltageResetTolerance OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "If an input transferred to battery backup due to over-voltage, the line voltage
        on the input source has to fall below this value from tlpUpsConfigHighVoltageTransfer,
        before the unit switched back to the AC input."
    ::= { tlpUpsInputEntry 12 }

tlpUpsInputPhaseTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsInputPhaseEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input phase details of each ups in the device table. This is
        a sparse dependent table, and will contain the number of entries
        based upon the number of upss managed by the agent as reported
        by tlpUpsIdentNumUps and tlpUpsIdentNumPhases."
    ::= { tlpUpsInput 2 }

tlpUpsInputPhaseEntry OBJECT-TYPE
    SYNTAX TlpUpsInputPhaseEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        input phase managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsInputPhaseIndex }
    ::= { tlpUpsInputPhaseTable 1 }

TlpUpsInputPhaseEntry ::= SEQUENCE {
    tlpUpsInputPhaseIndex            Unsigned32,
    tlpUpsInputPhaseFrequency        Unsigned32,
    tlpUpsInputPhaseVoltage          Unsigned32,
    tlpUpsInputPhaseVoltageMin       Unsigned32,
    tlpUpsInputPhaseVoltageMax       Unsigned32,
    tlpUpsInputPhaseCurrent          Unsigned32,
    tlpUpsInputPhasePower            Unsigned32 }

tlpUpsInputPhaseIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups input phase."
    ::= { tlpUpsInputPhaseEntry 1 }

tlpUpsInputPhaseFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present input frequency for a given input line."
    ::= { tlpUpsInputPhaseEntry 2 }

tlpUpsInputPhaseVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present input voltage for a given input line."
    ::= { tlpUpsInputPhaseEntry 3 }

tlpUpsInputPhaseVoltageMin OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The minimum input voltage value since the last reset of this
        value for a given input line. If any value is written to either
        tlpUpsInputVoltageMin or tlpUpsInputVoltageMax, both will be reset to
        their appropriate minimum and maximum values."
    ::= { tlpUpsInputPhaseEntry 4 }

tlpUpsInputPhaseVoltageMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The maximum input voltage value since the last reset of this
        value for a given input line. If any value is written to either
        tlpUpsInputVoltageMin or tlpUpsInputVoltageMax, both will be reset to
        their appropriate minimum and maximum values."
    ::= { tlpUpsInputPhaseEntry 5 }

tlpUpsInputPhaseCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present input current."
    ::= { tlpUpsInputPhaseEntry 6 }

tlpUpsInputPhasePower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present input true power."
    ::= { tlpUpsInputPhaseEntry 7 }

tlpUpsOutputTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsOutputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The output details of each ups in the device table. This is a sparse
        dependent table, and will contain the number of entries based upon the
        number of upss managed by the agent as reported by tlpUpsIdentNumUps."
    ::= { tlpUpsOutput 1 }

tlpUpsOutputEntry OBJECT-TYPE
    SYNTAX TlpUpsOutputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        output line managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsOutputTable 1 }

TlpUpsOutputEntry ::= SEQUENCE {
    tlpUpsOutputSource              INTEGER,
    tlpUpsOutputNominalVoltage      Unsigned32,
    tlpUpsOutputFrequency           Unsigned32,
    tlpUpsOutputVARating            Unsigned32,
    tlpUpsOutputPowerFactor         Unsigned32 }

tlpUpsOutputSource OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        other(1),
        none(2),
        normal(3),
        bypass(4),
        battery(5),
        boosting(6),
        reducing(7),
        second(8),
        economy(9),
        testing(10) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present source of output power. The enumeration none(2)
        indicates that there is no source of output power (and
        therefore no output power), for example, the system has opened
        the output breaker."
    ::= { tlpUpsOutputEntry 1 }

tlpUpsOutputNominalVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the output voltage. This value may not be
        supported on all models. If not supported, the agent reports
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutputEntry 2 }

tlpUpsOutputFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output frequency. The output frequency on most ups
        devices is common across all output lines. Some ups devices, however,
        maintain the output frequency per each line. If a common frequency is
        supported by the ups, then this frequency will contain the common
        frequency value. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutputEntry 3 }

tlpUpsOutputVARating OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "VA"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The VA Rating of the ups. This value may not be
        supported on all models. If not supported, the agent reports
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutputEntry 4 }

tlpUpsOutputPowerFactor OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The percentage of output power factor."
    ::= { tlpUpsOutputEntry 5 }

tlpUpsOutputLineTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsOutputLineEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The output line details of each ups in the device table. This is
        a sparse dependent table, and will contain the number of entries
        based upon the number of upss managed by the agent as reported
        by tlpUpsIdentNumUps."
    ::= { tlpUpsOutput 2 }

tlpUpsOutputLineEntry OBJECT-TYPE
    SYNTAX TlpUpsOutputLineEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        output line managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsOutputLineIndex }
    ::= { tlpUpsOutputLineTable 1 }

TlpUpsOutputLineEntry ::= SEQUENCE {
    tlpUpsOutputLineIndex           Unsigned32,
    tlpUpsOutputLineVoltage         Unsigned32,
    tlpUpsOutputLineCurrent         Unsigned32,
    tlpUpsOutputLinePower           Unsigned32,
    tlpUpsOutputLinePercentLoad     INTEGER,
    tlpUpsOutputLineFrequency       Unsigned32,
    tlpUpsOutputLineApparentPower   Unsigned32 }

tlpUpsOutputLineIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups output line."
    ::= { tlpUpsOutputLineEntry 1 }

tlpUpsOutputLineVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present output voltage for a given output line."
    ::= { tlpUpsOutputLineEntry 2 }

tlpUpsOutputLineCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output current, in hundredths of amps, for a given line. The actual
        precision of this value, whether a whole number, tenths or hundredths, is
        returned by tlpUpsDeviceOutputCurrentPrecision."
    ::= { tlpUpsOutputLineEntry 3 }

tlpUpsOutputLinePower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output true power."
    ::= { tlpUpsOutputLineEntry 4 }

tlpUpsOutputLinePercentLoad OBJECT-TYPE
    SYNTAX INTEGER (0..200)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The percentage of the UPS power capacity presently being used on
        this output line, i.e., the greater of the percent load of true
        power capacity and the percent load of VA."
    ::= { tlpUpsOutputLineEntry 5 }

tlpUpsOutputLineFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output frequency on this output line. The output frequency on
        most ups devices is common across all output lines. Some ups devices, however,
        maintain the output frequency per each line. If a per line frequency is
        supported by the ups, then this frequency value will reports the value of this
        line. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutputLineEntry 6 }

tlpUpsOutputLineApparentPower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output apparent power."
    ::= { tlpUpsOutputLineEntry 9 }

tlpUpsBypassTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsBypassEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The bypass details of each ups in the device table. This is a sparse
        dependent table, and will contain the number of entries based upon the
        number of upss managed by the agent as reported by tlpUpsIdentNumUps."
    ::= { tlpUpsBypass 1 }

tlpUpsBypassEntry OBJECT-TYPE
    SYNTAX TlpUpsBypassEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to all input lines per
        each ups managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsBypassTable 1 }

TlpUpsBypassEntry ::= SEQUENCE {
    tlpUpsBypassFrequency          Unsigned32 }

tlpUpsBypassFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present bypass frequency."
    ::= { tlpUpsBypassEntry 1 }

tlpUpsBypassLineTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsBypassLineEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The bypass line details of each ups in the device table. This is
        a sparse dependent table, and will contain the number of entries
        based upon the number of upss managed by the agent as reported
        by tlpUpsIdentNumUps."
    ::= { tlpUpsBypass 2 }

tlpUpsBypassLineEntry OBJECT-TYPE
    SYNTAX TlpUpsBypassLineEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular
        bypass line managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsBypassLineIndex }
    ::= { tlpUpsBypassLineTable 1 }

TlpUpsBypassLineEntry ::= SEQUENCE {
    tlpUpsBypassLineIndex           Unsigned32,
    tlpUpsBypassLineVoltage         Unsigned32,
    tlpUpsBypassLineCurrent         Unsigned32,
    tlpUpsBypassLinePower           Unsigned32 }

tlpUpsBypassLineIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The bypass line identifier."
    ::= { tlpUpsBypassLineEntry 1 }

tlpUpsBypassLineVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present bypass voltage."
    ::= { tlpUpsBypassLineEntry 2 }

tlpUpsBypassLineCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present bypass current."
    ::= { tlpUpsBypassLineEntry 3 }

tlpUpsBypassLinePower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present true power conveyed by the bypass."
    ::= { tlpUpsBypassLineEntry 4 }

tlpUpsOutletTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsOutletEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The details of each ups outlet in the device table. This is
        a sparse dependent table, and will contain the number of entries
        based upon the number of upss managed by the agent as reported
        by tlpUpsIdentNumUps."
    ::= { tlpUpsOutlet 1 }

tlpUpsOutletEntry OBJECT-TYPE
    SYNTAX TlpUpsOutletEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular outlet
        managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsOutletIndex }
    ::= { tlpUpsOutletTable 1 }

TlpUpsOutletEntry ::= SEQUENCE {
    tlpUpsOutletIndex               Unsigned32,
    tlpUpsOutletName                DisplayString,
    tlpUpsOutletDescription         DisplayString,
    tlpUpsOutletState               INTEGER,
    tlpUpsOutletControllable        TruthValue,
    tlpUpsOutletCommand             INTEGER,
    tlpUpsOutletVoltage             Unsigned32,
    tlpUpsOutletCurrent             Unsigned32,
    tlpUpsOutletPower               Unsigned32,
    tlpUpsOutletRampAction          INTEGER,
    tlpUpsOutletRampDelay           Integer32,
    tlpUpsOutletShedAction          INTEGER,
    tlpUpsOutletShedDelay           Integer32,
    tlpUpsOutletGroup               Integer32 }

tlpUpsOutletIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups outlet."
    ::= { tlpUpsOutletEntry 1 }

tlpUpsOutletName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-definable string identifying the name of the outlet."
    ::= { tlpUpsOutletEntry 2 }

tlpUpsOutletDescription OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-definable string identifying the device(s) connected to
        the given outlet."
    ::= { tlpUpsOutletEntry 3 }

tlpUpsOutletState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the outlet."
    ::= { tlpUpsOutletEntry 4 }

tlpUpsOutletControllable OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes whether this outlet is controllable."
    ::= { tlpUpsOutletEntry 5 }

tlpUpsOutletCommand OBJECT-TYPE
    SYNTAX INTEGER {
        idle(0),
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value
        to turnOff(1) will turn off the outlet. Setting this value
        to turnOn(2) will turn on the outlet. Setting this value to
        cycle(3) will turn the outlet off, then turn it back on."
    ::= { tlpUpsOutletEntry 6 }

tlpUpsOutletVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present output voltage for a given outlet.
        If the voltage measurement is not supported for an individual
        receptacle regardless whether device-wide or just for this
        receptacle, then this value will always return ERROR_NO_SUCH_NAME.
        Device-wide support for receptacle voltage measurements is denoted
        by tlpUpsSupportsOutletVoltage."
    ::= { tlpUpsOutletEntry 7 }

tlpUpsOutletCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output current, in hundredths of amps, of a given outlet.
        If the current measurement is not supported for an individual
        receptacle regardless whether device-wide or just for this
        receptacle, then this value will always return ERROR_NO_SUCH_NAME.
        Device-wide support for receptacle current measurements is denoted
        by tlpUpsSupportsCurrentPower."
    ::= { tlpUpsOutletEntry 8 }

tlpUpsOutletPower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output power of a given outlet. If the power measurement is
        not supported for an individual receptacle regardless whether
        device-wide or just for this receptacle, then this value will
        always return ERROR_NO_SUCH_NAME. Device-wide support for receptacle
        power measurements is denoted by tlpUpsSupportsCurrentPower."
    ::= { tlpUpsOutletEntry 9 }

tlpUpsOutletRampAction OBJECT-TYPE
    SYNTAX INTEGER {
        remainOff(0),
        turnOnAfterDelay(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The ramp action to take on a given outlet when powering on the
        device. If this receptacle is not controllable, as denoted by
        tlpUpsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutletEntry 10 }

tlpUpsOutletRampDelay OBJECT-TYPE
    SYNTAX Integer32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of seconds to delay before powering on the given outlet.
        If this receptacle is not controllable, as denoted by
        tlpUpsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutletEntry 11 }

tlpUpsOutletShedAction OBJECT-TYPE
    SYNTAX INTEGER {
        remainOn(0),
        turnOffAfterDelay(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The shed action to take on a given outlet when powering off the
        device. If this receptacle is not controllable, as denoted by
        tlpUpsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutletEntry 12 }

tlpUpsOutletShedDelay OBJECT-TYPE
    SYNTAX Integer32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of seconds to delay before powering off the given outlet.
        If this receptacle is not controllable, as denoted by
        tlpUpsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutletEntry 13 }

tlpUpsOutletGroup OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The tlpUpsOutletGroupIndex group index of the respective group to
        which the given outlet is assigned, or zero (0) if the given
        outlet is not grouped."
    ::= { tlpUpsOutletEntry 14 }

tlpUpsOutletGroupTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsOutletGroupEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The receptacle grouping details of each ups in device table.
        This is a sparse dependent table, and will contain the number
        of entries based upon the value of tlpUpsIdentNumOutletGroups per
        each device."
    ::= { tlpUpsOutlet 2 }

tlpUpsOutletGroupEntry OBJECT-TYPE
    SYNTAX TlpUpsOutletGroupEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular outlet
        group managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsOutletGroupIndex }
    ::= { tlpUpsOutletGroupTable 1 }

TlpUpsOutletGroupEntry ::= SEQUENCE {
    tlpUpsOutletGroupIndex              Unsigned32,
    tlpUpsOutletGroupRowStatus	        RowStatus,
    tlpUpsOutletGroupName               DisplayString,
    tlpUpsOutletGroupDescription        DisplayString,
    tlpUpsOutletGroupState              INTEGER,
    tlpUpsOutletGroupCommand            INTEGER }

tlpUpsOutletGroupIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups outlet group."
    ::= { tlpUpsOutletGroupEntry 1 }

tlpUpsOutletGroupRowStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Row status for the tlpUpsOutletGroupTable"
    ::= { tlpUpsOutletGroupEntry 2 }

tlpUpsOutletGroupName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of this outlet group."
    ::= { tlpUpsOutletGroupEntry 3 }

tlpUpsOutletGroupDescription OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A description for this outlet group."
    ::= { tlpUpsOutletGroupEntry 4 }

tlpUpsOutletGroupState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2),
        mixed(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the outlet group. This value will report
        off(1) if all outlets in the group are off, on(2) if all outlets
        in the group are on, mixed(3) if some outlets in the group are
        off and others are on, or unknown(0) if the state of the outlets
        cannot be determined."
    ::= { tlpUpsOutletGroupEntry 5 }

tlpUpsOutletGroupCommand OBJECT-TYPE
    SYNTAX INTEGER {
        idle(0),
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value to
        turnOff(1) will turn off every outlet associated with the group.
        Setting this value to turnOn(2) will turn on every outlet associated
        with the group. Setting this value to cycle(3) will turn off every
        outlet associated with the group, then turn each outlet back on."
    ::= { tlpUpsOutletGroupEntry 6 }

tlpUpsWatchdogTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsWatchdogEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The watchdog details of each ups in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are upss."
    ::= { tlpUpsWatchdog 1 }

tlpUpsWatchdogEntry OBJECT-TYPE
    SYNTAX TlpUpsWatchdogEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing watchdog details for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsWatchdogTable 1 }

TlpUpsWatchdogEntry ::= SEQUENCE {
    tlpUpsWatchdogSupported              TruthValue,
    tlpUpsWatchdogSecsBeforeReboot       Unsigned32 }

tlpUpsWatchdogSupported OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not this ups supports a watchdog reboot."
    ::= { tlpUpsWatchdogEntry 1 }

tlpUpsWatchdogSecsBeforeReboot OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The maximum number of seconds that can expire between polls that
        the engine makes to the ups for data.  If this time runs out, then
        the ups will cycle its outputs.  Set this to zero to turns disable
        this feature."
    ::= { tlpUpsWatchdogEntry 2 }

tlpUpsFanTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsFanEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The fan speed of each ups in the device table. This is a sparse
        dependent table, and will contain the number of entries based upon the
        number of upss managed by the agent as reported by tlpUpsIdentNumFans."
    ::= { tlpUpsFan 1 }

tlpUpsFanEntry  OBJECT-TYPE
    SYNTAX TlpUpsFanEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular fan
        managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsFanIndex }
    ::= { tlpUpsFanTable 1 }

TlpUpsFanEntry ::= SEQUENCE {
    tlpUpsFanIndex            Unsigned32,
    tlpUpsFanSpeed            Unsigned32 }

tlpUpsFanIndex  OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups fan speed."
    ::= { tlpUpsFanEntry 1 }

tlpUpsFanSpeed  OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "RPM"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The fan speed in rotations per minute."
    ::= { tlpUpsFanEntry 2 }

tlpUpsHeatsinkTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsHeatsinkEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of ups heatsink thermistor table entries. The number of entries
        is given by the value of tlpUpsIdentNumHeatsinks."
    ::= { tlpUpsHeatsink 1 }

tlpUpsHeatsinkEntry OBJECT-TYPE
    SYNTAX TlpUpsHeatsinkEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular heatsink
        managed by this agent."
    INDEX { tlpDeviceIndex, tlpUpsHeatsinkIndex }
    ::= { tlpUpsHeatsinkTable 1 }

TlpUpsHeatsinkEntry ::= SEQUENCE {
    tlpUpsHeatsinkIndex            Unsigned32,
    tlpUpsHeatsinkTemperatureC     Integer32,
    tlpUpsHeatsinkTemperatureF     Integer32 }

tlpUpsHeatsinkIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups heatsink."
    ::= { tlpUpsHeatsinkEntry 1 }

tlpUpsHeatsinkTemperatureC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature at the heatsink thermistor in tenths degrees Celsius.
        Heatsink temperature measurements are not supported on all models. If
        not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsHeatsinkEntry 2 }

tlpUpsHeatsinkTemperatureF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature at the heatsink thermistor in tenths degrees Fahrenheit.
        Heatsink temperature measurements are not supported on all models. If
        not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsHeatsinkEntry 3 }

tlpUpsInputContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsInputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input contact details of each ups in the device table. The number of entries
        is given by the value of tlpUpsIdentNumInputContacts."
    ::= { tlpUpsInputContact 1 }

tlpUpsInputContactEntry OBJECT-TYPE
    SYNTAX TlpUpsInputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing input contact details for a particular ups."
    INDEX { tlpDeviceIndex, tlpUpsInputContactIndex }
    ::= { tlpUpsInputContactTable 1 }

TlpUpsInputContactEntry ::= SEQUENCE {
    tlpUpsInputContactIndex            Unsigned32,
    tlpUpsInputContactInAlarm          INTEGER }

tlpUpsInputContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups input contact details."
    ::= { tlpUpsInputContactEntry 1 }

tlpUpsInputContactInAlarm OBJECT-TYPE
    SYNTAX INTEGER {
		notInAlarm(0),
		inAlarm(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The alarm status of the input contact. Input contacts are not supported on
        all models. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsInputContactEntry 2 }

tlpUpsOutputContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsOutputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The output contact details of each ups in the device table. The number of entries
        is given by the value of tlpUpsIdentNumOuputContacts."
    ::= { tlpUpsOutputContact 1 }

tlpUpsOutputContactEntry OBJECT-TYPE
    SYNTAX TlpUpsOutputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing output contact details for a particular ups."
    INDEX { tlpDeviceIndex, tlpUpsOutputContactIndex }
    ::= { tlpUpsOutputContactTable 1 }

TlpUpsOutputContactEntry ::= SEQUENCE {
    tlpUpsOutputContactIndex            Unsigned32,
    tlpUpsOutputContactInAlarm          INTEGER }

tlpUpsOutputContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups output contact details."
    ::= { tlpUpsOutputContactEntry 1 }

tlpUpsOutputContactInAlarm OBJECT-TYPE
    SYNTAX INTEGER {
		notInAlarm(0),
		inAlarm(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The alarm status of the output contact. Output contacts are not supported on
        all models. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsOutputContactEntry 2 }

tlpUpsControlTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The actionable controls for each ups in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are upss."
    ::= { tlpUpsControl 1 }

tlpUpsControlEntry OBJECT-TYPE
    SYNTAX TlpUpsControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing controls for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsControlTable 1 }

TlpUpsControlEntry ::= SEQUENCE {
    tlpUpsControlSelfTest            TruthValue,
    tlpUpsControlRamp                TruthValue,
    tlpUpsControlShed                TruthValue,
    tlpUpsControlUpsOn               TruthValue,
    tlpUpsControlUpsOff              TruthValue,
    tlpUpsControlUpsReboot           TruthValue,
    tlpUpsControlBypass              INTEGER,
    tlpUpsControlResetWattHours      TruthValue,
    tlpUpsControlCancelSelfTest      TruthValue,
    tlpUpsControlResetAllParameters  TruthValue }

tlpUpsControlSelfTest OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to initiate a self test on the ups."
    ::= { tlpUpsControlEntry 1 }

tlpUpsControlRamp OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to initiate the pre-designated ramp
        sequence on the ups."
    ::= { tlpUpsControlEntry 2 }

tlpUpsControlShed OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to initiate the pre-designated shed
        sequence on the ups."
    ::= { tlpUpsControlEntry 3 }

tlpUpsControlUpsOn OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to turn the ups on."
    ::= { tlpUpsControlEntry 4 }

tlpUpsControlUpsOff OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to turn the ups off."
    ::= { tlpUpsControlEntry 5 }

tlpUpsControlUpsReboot OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to reboot the ups."
    ::= { tlpUpsControlEntry 6 }

tlpUpsControlBypass OBJECT-TYPE
    SYNTAX INTEGER {
        disable(0),
        enable(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This switches the ups in and out of bypass mode. This value returns
        the current bypass mode: disable(0) if the ups is not in bypass mode,
        enable(1) if the ups is in bypass mode. Set to disable(0) to exit
        bypass mode, or enable(1) to enter bypass mode. If switching bypass
        mode is not supported, this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsControlEntry 7 }

tlpUpsControlResetWattHours OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to reset the tlpUpsInputWattHours calculation algorithm."
    ::= { tlpUpsControlEntry 8 }

tlpUpsControlCancelSelfTest OBJECT-TYPE
	SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to cancel the self test in progress."
    ::= { tlpUpsControlEntry 9 }

tlpUpsControlResetAllParameters OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to reset all configuration settings to the default values."
    ::= { tlpUpsControlEntry 10 }

tlpUpsConfigTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration details of each ups in the device table. This is
        a sparse dependent table, and will contain the number of entries
        based upon the number of upss managed by the agent as reported by
        tlpUpsIdentNumUps."
    ::= { tlpUpsConfig 1 }

tlpUpsConfigEntry OBJECT-TYPE
    SYNTAX TlpUpsConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to all input lines per
        each ups managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsConfigTable 1 }

TlpUpsConfigEntry ::= SEQUENCE {
    tlpUpsConfigInputVoltage                Unsigned32,
    tlpUpsConfigInputFrequency              Unsigned32,
    tlpUpsConfigOutputVoltage               Unsigned32,
    tlpUpsConfigOutputFrequency             Unsigned32,
    tlpUpsConfigAudibleStatus               INTEGER,
    tlpUpsConfigAutoBatteryTest             INTEGER,
    tlpUpsConfigAutoRestartAfterShutdown    INTEGER,
    tlpUpsConfigAutoRampOnTransition        INTEGER,
    tlpUpsConfigAutoShedOnTransition        INTEGER,
    tlpUpsConfigBypassLowerLimitPercent     INTEGER,
    tlpUpsConfigBypassUpperLimitPercent     INTEGER,
    tlpUpsConfigBypassLowerLimitVoltage     Unsigned32,
    tlpUpsConfigBypassUpperLimitVoltage     Unsigned32,
    tlpUpsConfigColdStart                   INTEGER,
    tlpUpsConfigEconomicMode                INTEGER,
    tlpUpsConfigFaultAction                 INTEGER,
    tlpUpsConfigOffMode                     INTEGER,
    tlpUpsConfigLineSensitivity             INTEGER,
    tlpUpsConfigLineQualifyTime             Unsigned32,
    tlpUpsConfigACPowerSenseType            INTEGER }

tlpUpsConfigInputVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME. This value may not be writable on all models. If
        not writable, the value will be rejected and an appropriate error
        message will be returned."
    ::= { tlpUpsConfigEntry 1 }

tlpUpsConfigInputFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The nominal input frequency. On those systems which support read-write
        access to this object, if there is an attempt to set this variable to a
        value that is not supported, the request must be rejected and the agent
        shall respond with an appropriate error message, i.e., badValue for
        SNMPv1, or inconsistentValue for SNMPv2."
    ::= { tlpUpsConfigEntry 2 }

tlpUpsConfigOutputVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The nominal value of the output voltage. This value may not be
        supported on all models. If not supported, the agent reports
        ERROR_NO_SUCH_NAME. This value may not be writable on all models. If
        not writable, the value will be rejected and an appropriate error
        message will be returned."
    ::= { tlpUpsConfigEntry 3 }

tlpUpsConfigOutputFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
    "The nominal output frequency. On those systems which support read-write
    access to this object, if there is an attempt to set this variable to a
    value that is not supported, the request will be rejected and the agent
    will respond with an appropriate error message."
    ::= { tlpUpsConfigEntry 4 }

tlpUpsConfigAudibleStatus OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(1),
        enabled(2),
        muted(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The requested state of the audible alarm. When in the disabled state,
        the audible alarm should never sound. The enabled state is self-
        describing. Setting this object to muted(3) when the audible alarm is
        sounding shall temporarily silence the alarm. It will remain muted until
        it would normally stop sounding and the value returned for read operations
        during this period shall equal muted(3). At the end of this period, the
        value shall revert to enabled(2). Writes of the value muted(3) when the
        audible alarm is not sounding shall be accepted but otherwise shall have no
        effect."
    ::= { tlpUpsConfigEntry 5 }

tlpUpsConfigAutoBatteryTest OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        biweekly(1),
        monthly(2),
        quarterly(3),
        semiannually(4) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When disabled(0), the ups will not perform an automatic battery selftest.
        When set to another value, the ups will perform an automatic selftest in
        the approximate period defined by each respective enumerated value. This
        feature is generally not recommended."
    ::= { tlpUpsConfigEntry 6 }

tlpUpsConfigAutoRestartAfterShutdown OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled(1), the ups should autorestart after a shutdown from battery
        mode if valid AC is applied. When disabled(0), the ups should not autorestart
        when AC is restored and instead power up into standby mode."
    ::= { tlpUpsConfigEntry 7 }

tlpUpsConfigAutoRampOnTransition OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled(1), the ups should perform its preconfigured ramp sequence when
        valid AC is restored while in battery mode. If this value is not supported by
        the ups, then this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 8 }

tlpUpsConfigAutoShedOnTransition OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled(1), the ups should perform its preconfigured shed sequence when
        valid AC is lost causing the ups to enter battery mode. If this value is not
        supported by the ups, then this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 9 }

tlpUpsConfigBypassLowerLimitPercent OBJECT-TYPE
    SYNTAX INTEGER (-20..-5)
    UNITS "percent"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The lowest AC input voltage allowed to be output to the load while in bypass
        mode. This setting is expressed as a percentage from nominal voltage. If this
        value is not supported by the ups, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 10 }

tlpUpsConfigBypassUpperLimitPercent OBJECT-TYPE
    SYNTAX INTEGER (5..20)
    UNITS "percent"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The highest AC input voltage allowed to be output to the load while in bypass
        mode. This setting is expressed as a percentage from nominal voltage. If this
        value is not supported by the ups, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 11 }

tlpUpsConfigBypassLowerLimitVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The lowest AC input voltage allowed to be output to the load while in bypass
        mode. If this value is not supported by the ups, then this value will always
        return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 12 }

tlpUpsConfigBypassUpperLimitVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The highest AC input voltage allowed to be output to the load while in bypass
        mode. If this value is not supported by the ups, then this value will always
        return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 13 }

tlpUpsConfigColdStart OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the ability to turn the ups on without AC
        supplied. If this value is not supported by the ups, then this value will
        always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 14 }

tlpUpsConfigEconomicMode OBJECT-TYPE
    SYNTAX INTEGER {
        online(0),
        economy(1),
        constant50Hz(2),
        constant60Hz(3),
        constantAuto(4),
        autoAdaptive(5) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The configurable economic mode options for the ups. The online(0) mode is
        the default configuration of most upss, which is synonymous as having the
        economic mode disabled. The economy(1) mode is the typical configuration to
        run in economy mode, which is synonymous as having the economic mode enabled.
        Constant50Hz(2) is a power strategy, which fixes the output frequency at 50Hz.
        Constant60Hz(3) is a power strategy, which fixes the output frequency at 60Hz.
        ConstantAuto(4) regulates the output frequency at nominal input frequency.
        AutoAdaptive(5) mode will cause the ups to automatically switch between
        online(0), maximum quality, and economy(1), maximum efficiency, to improve
        efficiency during stable utility periods and improve output regulation during
        unstable utility periods. Not all values are supported by all upss. Most upss,
        if supportive of economic mode, only supports online(0) and economy(1). If the
        ups does not support economic mode, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 15 }

tlpUpsConfigFaultAction OBJECT-TYPE
    SYNTAX INTEGER {
        bypass(0),
        standby(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Determines the action to take if the output needs to be shut down due to a
        fault condition, limited by the type of fault incurred. When configured for
        bypass(0), the ups will go to bypass on a fault condition. When configured
        for standby(1), the ups will go to standby mode on a fault condition. Either
        configuration will be valid as long as AC input is within normal line voltage
        and frequency limits when the fault condition occurs. If the line is outside
        these limits, the ups will turn off. If this value is not supported by the ups,
        then this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 16 }

tlpUpsConfigOffMode OBJECT-TYPE
    SYNTAX INTEGER {
        standby(0),
        bypass(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option governs what mode the ups should go into when AC input is
        available, but the load is either off or not battery backed up. If this
        setting is configured for bypass(1), the intention is that any time the ups
        would normally go into standby mode, it would instead go into bypass mode. Note
        that this setting does not dictate the mode for fault conditions, which is
        governed by tlpUpsConfigFaultAction. If this value is not supported by the ups,
        then this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 17 }

tlpUpsConfigLineSensitivity OBJECT-TYPE
    SYNTAX INTEGER {
        normal(0),
        reduced(1),
        fullyReduced(2) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Adjusts the AC line voltage sensitivity setting. If this value is not supported
        by the ups, then this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 18 }

tlpUpsConfigLineQualifyTime OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
    "The amount of time the ups monitors line power quality for stability before ac power
    is restored. If there is an attempt to set this variable to a
    value that is not supported, the request will be rejected and the agent
    will respond with an appropriate error message."
    ::= { tlpUpsConfigEntry 19 }

tlpUpsConfigACPowerSenseType OBJECT-TYPE
    SYNTAX INTEGER {
        generator(1),
        ups(2) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables configuration of ups AC power sensing to maximize ups
		operation for standard line power AC and generator power applications.
		If this value is not supported by the ups, then this value will always return
		ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigEntry 20 }

tlpUpsConfigAutoRestartTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsConfigAutoRestartEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configurable automatic restart options of each ups in device table.
        This is a sparse dependent table, and will contain some subset of devices
        that are upss. Not all options are available on all upss."
    ::= { tlpUpsConfig 2 }

tlpUpsConfigAutoRestartEntry OBJECT-TYPE
    SYNTAX TlpUpsConfigAutoRestartEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing configurable automatic restart options for a particular
        ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsConfigAutoRestartTable 1 }

TlpUpsConfigAutoRestartEntry ::= SEQUENCE {
    tlpUpsConfigAutoRestartInverterShutdown     INTEGER,
    tlpUpsConfigAutoRestartDelayedWakeup        INTEGER,
    tlpUpsConfigAutoRestartLowVoltageCutoff     INTEGER,
    tlpUpsConfigAutoRestartOverLoad             INTEGER,
    tlpUpsConfigAutoRestartOverTemperature      INTEGER }

tlpUpsConfigAutoRestartInverterShutdown OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If enabled(1) and the ups is turned off from invert mode (via user
        interaction), if line becomes valid, the ups will automatically go to line
        mode. If disabled(0), the ups will turn back on and go to standby mode
        instead."
    ::= { tlpUpsConfigAutoRestartEntry 1 }

tlpUpsConfigAutoRestartDelayedWakeup OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If enabled(1) and power fails during a DelayBeforeStartup and line remains
        invalid after the timeout expires, the ups will be set to turn back on
        automatically. If disabled(0), the ups will turn back on and go to standby
        mode instead."
    ::= { tlpUpsConfigAutoRestartEntry 2 }

tlpUpsConfigAutoRestartLowVoltageCutoff OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If enabled(1) and the ups is turned off from invert mode (via low voltage
        cutoff), if line becomes valid, the ups will automatically go to line mode.
        If disabled(0), the ups will turn back on and go to standby mode instead."
    ::= { tlpUpsConfigAutoRestartEntry 3 }

tlpUpsConfigAutoRestartOverLoad OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If enabled(1) and the ups is turned off from invert mode (via overload), if
        line becomes valid, the ups will automatically go to line mode. If disabled(0),
        the ups will turn back on and go to standby mode instead."
    ::= { tlpUpsConfigAutoRestartEntry 4 }

tlpUpsConfigAutoRestartOverTemperature OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If enabled(1) and the ups is turned off from invert mode (via over temperature),
        if line becomes valid, the ups will automatically go to line mode. If disabled(0),
        the ups will turn back on and go to standby mode instead."
    ::= { tlpUpsConfigAutoRestartEntry 5 }

tlpUpsConfigThresholdTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsConfigThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configurable threshold options of each ups in device table. This
        is a sparse dependent table, and will contain some subset of devices that
        are upss. Not all options are available on all upss."
    ::= { tlpUpsConfig 3 }

tlpUpsConfigThresholdEntry OBJECT-TYPE
    SYNTAX TlpUpsConfigThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing configurable threshold options for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsConfigThresholdTable 1 }

TlpUpsConfigThresholdEntry ::= SEQUENCE {
    tlpUpsConfigBatteryAgeThreshold             Unsigned32,
    tlpUpsConfigLowBatteryThreshold             INTEGER,
    tlpUpsConfigLowBatteryTime                  Unsigned32,
    tlpUpsConfigOverLoadThreshold               INTEGER }

tlpUpsConfigBatteryAgeThreshold OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "months"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of months for a battery to be in service. The battery age alarm,
        tlpUpsAlarmBatteryAgeAboveThreshold, is triggered when the current date
        exceeds tlpUpsBatteryPackIdentInstallDate by tlpUpsConfigBatteryAgeThreshold
        months."
    ::= { tlpUpsConfigThresholdEntry 1 }

tlpUpsConfigLowBatteryThreshold OBJECT-TYPE
    SYNTAX INTEGER (5..95)
    UNITS "percent"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This value denotes the percentage of battery capacity remaining warning
        level. The low battery alarm, tlpUpsAlarmLowBattery, is triggered when
        tlpUpsEstimatedChargeRemaining drops below this value."
    ::= { tlpUpsConfigThresholdEntry 2 }

tlpUpsConfigLowBatteryTime OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The value in seconds of tlpUpsEstimatedMinutesRemaining at which a lowBattery
        condition is declared. For agents which support only discrete (discontinuous)
        values, then the agent shall round up to the next supported value. If the
        requested value is larger than the largest supported value, then the largest
        supported value shall be selected."
    ::= { tlpUpsConfigThresholdEntry 3 }

tlpUpsConfigOverLoadThreshold OBJECT-TYPE
    SYNTAX INTEGER (5..105)
    UNITS "percent"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This value denotes the percentage of load at which the overload condition
        occurs. The overload alarm, tlpUpsAlarmOutputOverload, is triggered when the
        percentage of output load exceeds this value."
    ::= { tlpUpsConfigThresholdEntry 4 }

tlpUpsConfigVoltageTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsConfigVoltageEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The voltage configuration details for each ups in device table. This
        is a sparse dependent table, and will contain some subset of devices
        that are upss. Not all options are available on all upss."
    ::= { tlpUpsConfig 4 }

tlpUpsConfigVoltageEntry OBJECT-TYPE
    SYNTAX TlpUpsConfigVoltageEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing voltage configuration options for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsConfigVoltageTable 1 }

TlpUpsConfigVoltageEntry ::= SEQUENCE {
    tlpUpsConfigHighVoltageTransfer                 Unsigned32,
    tlpUpsConfigHighVoltageResetTolerance           Unsigned32,
	tlpUpsConfigHighVoltageReset                    Unsigned32,
    tlpUpsConfigLowVoltageTransfer                  Unsigned32,
    tlpUpsConfigLowVoltageResetTolerance            Unsigned32,
	tlpUpsConfigLowVoltageReset                     Unsigned32 }

tlpUpsConfigHighVoltageTransfer OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The maximum line voltage allowed on the input source before the unit
        disconnects the AC input connection."
    ::= { tlpUpsConfigVoltageEntry 1 }

tlpUpsConfigHighVoltageResetTolerance OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If an input connection is disconnected due to over-voltage, the line voltage on the input source
		has to fall below tlpUpsConfigHighVoltageTransfer less tlpUpsConfigHighVoltageTransferResetTolerance	`
        before the unit reconnects the AC input to input source."
    ::= { tlpUpsConfigVoltageEntry 2 }

tlpUpsConfigHighVoltageReset OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If an input connection is disconnected due to over-voltage, the line voltage on the input source
		has to fall below tlpUpsConfigHighVoltageReset before the unit reconnects the AC input to input source."
    ::= { tlpUpsConfigVoltageEntry 3 }

tlpUpsConfigLowVoltageTransfer OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The minimum line voltage allowed before the unit disconnects the AC input
        connection."
    ::= { tlpUpsConfigVoltageEntry 4 }

tlpUpsConfigLowVoltageResetTolerance OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If a unit is disconnected due to under-voltage or is not powered up yet, the line voltage
        has to rise above tlpUpsConfigLowVoltageTransfer plus tlpUpsConfigLowVoltageTransferResetTolerance
        before the unit reonnect the AC input to input source."
    ::= { tlpUpsConfigVoltageEntry 5 }

tlpUpsConfigLowVoltageReset OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If a unit is disconnected due to under-voltage or is not powered up yet, the line voltage
        has to rise above tlpUpsConfigLowVoltageReset before the unit reonnect the AC input to input source."
    ::= { tlpUpsConfigVoltageEntry 6 }

tlpUpsConfigContact OBJECT IDENTIFIER::= { tlpUpsConfig 5 }

tlpUpsConfigContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsConfigContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration options of each ups in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are upss. Not all options are available on all
        upss."
    ::= {  tlpUpsConfigContact  1 }

tlpUpsConfigContactEntry OBJECT-TYPE
    SYNTAX TlpUpsConfigContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing contact configuration options for a particular ups."
    INDEX { tlpDeviceIndex }
    ::= { tlpUpsConfigContactTable 1 }

TlpUpsConfigContactEntry ::= SEQUENCE {
    tlpUpsConfigOutputContactBackupTimer    INTEGER }

tlpUpsConfigOutputContactBackupTimer OBJECT-TYPE
    SYNTAX INTEGER
    MAX-ACCESS  read-write
    STATUS current
    DESCRIPTION
        "This is the amount of runtime the UPS supports in battery mode before sending
		a configured output contact notification.  Output contacts are not supported on all
		models. If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigContactEntry 1 }

tlpUpsConfigInputContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsConfigInputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input contact details of each ups in device table. the number of entries is
        given by tlpUpsIdentNumInputContacts. This is a sparse dependent table, and will
        contain some subset of devices that are upss. Not all options are available on all
        upss."
    ::= { tlpUpsConfigContact  2 }

tlpUpsConfigInputContactEntry OBJECT-TYPE
    SYNTAX TlpUpsConfigInputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing input contact details for a particular ups."
    INDEX { tlpDeviceIndex, tlpUpsConfigInputContactIndex }
    ::= { tlpUpsConfigInputContactTable 1 }

TlpUpsConfigInputContactEntry ::= SEQUENCE {
    tlpUpsConfigInputContactIndex            Unsigned32,
    tlpUpsConfigInputContactSetup            INTEGER }

tlpUpsConfigInputContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups input contact details."
    ::= { tlpUpsConfigInputContactEntry 1 }

tlpUpsConfigInputContactSetup  OBJECT-TYPE
    SYNTAX INTEGER {
		userDefined(0),
		externalAlarm(1),
		externalBatteryAlarm(2),
		externalFanFailed(3),
		doorUnlock(4)}
    MAX-ACCESS  read-write
    STATUS current
    DESCRIPTION
        "Input contact configuration options. Input contacts are not supported on
        all models. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigInputContactEntry 2 }

tlpUpsConfigOutputContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpUpsConfigOutputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Output contact details of each ups in device table. The number of entries is
        given by tlpUpsIdentNumOutputContacts. This is a sparse dependent table, and
        will contain some subset of devices that are upss. Not all options are available
        on all upss."
    ::= {  tlpUpsConfigContact  3 }

tlpUpsConfigOutputContactEntry OBJECT-TYPE
    SYNTAX TlpUpsConfigOutputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing output contact details for a particular ups."
    INDEX { tlpDeviceIndex, tlpUpsConfigOutputContactIndex }
    ::= { tlpUpsConfigOutputContactTable 1 }

TlpUpsConfigOutputContactEntry ::= SEQUENCE {
    tlpUpsConfigOutputContactIndex            Unsigned32,
    tlpUpsConfigOutputContactSetup            INTEGER }

tlpUpsConfigOutputContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ups output contact details."
    ::= { tlpUpsConfigOutputContactEntry 1 }

tlpUpsConfigOutputContactSetup OBJECT-TYPE
    SYNTAX INTEGER {
		disabled(0),
		onBattery(1),
		batteryLow(2),
		timer(3),
		alarm(4),
		fault(5),
		outputOff(6) }
    MAX-ACCESS  read-write
    STATUS current
    DESCRIPTION
        "Output contact configuration options. Output contacts are not supported on
        all models. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpUpsConfigOutputContactEntry 2 }

-- pdu

tlpPduIdentNumPdu OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of pdu devices that this agent is currently managing."
    ::= { tlpPduIdent 1 }

tlpPduIdentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The identity details of each pdu in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are pdus."
    ::= { tlpPduIdent 2 }

tlpPduIdentEntry OBJECT-TYPE
    SYNTAX TlpPduIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing details for a particular pdu."
    INDEX { tlpDeviceIndex }
    ::= { tlpPduIdentTable 1 }

TlpPduIdentEntry ::= SEQUENCE {
    tlpPduIdentNumInputs                Unsigned32,
    tlpPduIdentNumOutputs               Unsigned32,
    tlpPduIdentNumPhases                Unsigned32,
    tlpPduIdentNumOutlets               Unsigned32,
    tlpPduIdentNumOutletGroups          Unsigned32,
    tlpPduIdentNumCircuits              Unsigned32,
    tlpPduIdentNumBreakers              Unsigned32,
    tlpPduIdentNumHeatsinks             Unsigned32 }

tlpPduIdentNumInputs OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of input lines supported by the pdu."
    ::= { tlpPduIdentEntry 1 }

tlpPduIdentNumOutputs OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of output lines supported by the pdu."
    ::= { tlpPduIdentEntry 2 }

tlpPduIdentNumPhases OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of phases supported by the pdu."
    ::= { tlpPduIdentEntry 3 }

tlpPduIdentNumOutlets OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of receptacles supported by the pdu."
    ::= { tlpPduIdentEntry 4 }

tlpPduIdentNumOutletGroups OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of receptacle groups supported by the pdu."
    ::= { tlpPduIdentEntry 5 }

tlpPduIdentNumCircuits OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of circuits supported by the pdu."
    ::= { tlpPduIdentEntry 6 }

tlpPduIdentNumBreakers OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of monitored bank breakers supported by the pdu.
		 If the pdu has breakers that are not monitored by the device,
		 the value of zero(0) will be returned."
    ::= { tlpPduIdentEntry 7 }

tlpPduIdentNumHeatsinks OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of heatsink thermistors supported by the pdu."
    ::= { tlpPduIdentEntry 8 }

tlpPduSupportsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduSupportsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The supported details of each pdu in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are pdus."
    ::= { tlpPduIdent 3 }

tlpPduSupportsEntry OBJECT-TYPE
    SYNTAX TlpPduSupportsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing supported details for a particular pdu."
    INDEX { tlpDeviceIndex }
    ::= { tlpPduSupportsTable 1 }

TlpPduSupportsEntry ::= SEQUENCE {
    tlpPduSupportsEnergywise            TruthValue,
    tlpPduSupportsRampShed              TruthValue,
    tlpPduSupportsOutletGroup           TruthValue,
    tlpPduSupportsOutletCurrentPower    TruthValue,
    tlpPduSupportsOutletVoltage         TruthValue }

tlpPduSupportsEnergywise OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the pdu supports Cisco EnergyWise."
    ::= { tlpPduSupportsEntry 1 }

tlpPduSupportsRampShed OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the pdu supports ramping and shedding."
    ::= { tlpPduSupportsEntry 2 }

tlpPduSupportsOutletGroup OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the pdu supports outlet groups."
    ::= { tlpPduSupportsEntry 3 }

tlpPduSupportsOutletCurrentPower OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the pdu reports individual receptacle current
        and power measurements. Devices that do not support individual
        receptacle measurements will always return ERROR_NO_SUCH_NAME for
        tlpPduOutletCurrent and tlpPduOutletPower."
    ::= { tlpPduSupportsEntry 4 }

tlpPduSupportsOutletVoltage OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the pdu reports individual receptacle voltage
        measurements. Devices that do not support individual receptacle
        measurements will always return ERROR_NO_SUCH_NAME for
        tlpPduOutletVoltage."
    ::= { tlpPduSupportsEntry 5 }

tlpPduDisplayTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduDisplayEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The display details of each pdu in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are pdus."
    ::= { tlpPduIdent 4 }

tlpPduDisplayEntry OBJECT-TYPE
    SYNTAX TlpPduDisplayEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing display details for a particular pdu."
    INDEX { tlpDeviceIndex }
    ::= { tlpPduDisplayTable 1 }

TlpPduDisplayEntry ::= SEQUENCE {
    tlpPduDisplayScheme             INTEGER,
    tlpPduDisplayOrientation        INTEGER,
    tlpPduDisplayAutoScroll         INTEGER,
    tlpPduDisplayIntensity          INTEGER,
    tlpPduDisplayUnits              INTEGER }

tlpPduDisplayScheme OBJECT-TYPE
    SYNTAX INTEGER {
        schemeReverse(0),
        schemeNormal(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the display scheme of the LED display. The value
        schemeReverse(0) indicates the tricolor LED indicator uses an
        alternate pattern. The value schemeNormal(1) indicates the
        LED indicator uses the standard pattern. This value may not
        be supported on all models. If not supported, the value will
        always return schemeNormal(1)."
    ::= { tlpPduDisplayEntry 1 }

tlpPduDisplayOrientation OBJECT-TYPE
    SYNTAX INTEGER {
        displayNormal(0),
        displayReverse(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the physical orientation of the LED display. The value
        displayNormal(0) indicates the display is right-side-up (normal).
        The value displayReverse(1) indicates the display is upside-down.
        This value may not be supported on all models. If not supported,
        the value will always return displayNormal(0)."
    ::= { tlpPduDisplayEntry 2 }

tlpPduDisplayAutoScroll OBJECT-TYPE
    SYNTAX INTEGER {
        scrollDisabled(0),
        scrollEnabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates whether the LED display automatically scrolls through
        displaying status values."
    ::= { tlpPduDisplayEntry 3 }

tlpPduDisplayIntensity OBJECT-TYPE
    SYNTAX INTEGER {
        intensity25(1),
        intensity50(2),
        intensity75(3),
        intensity100(4) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the intensity, as a percentage, of the LED display. The
        value intensity100(4) indicates the display is at 100% intensity.
        The other values indicate the display is at a lesser intensity based
        upon the respective percentage denoted."
    ::= { tlpPduDisplayEntry 4 }

tlpPduDisplayUnits OBJECT-TYPE
    SYNTAX INTEGER {
        normal(0),
        metric(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the units of measurement displayed on the LED display.
        The value normal(0) indicates that a temperature value, for example,
        is displayed in degrees Fahrenheit. The value metric(1) indicates a
        temperature value is displayed in degrees Celsius."
    ::= { tlpPduDisplayEntry 5 }

tlpPduDeviceTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The device details of each pdu in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are pdus."
    ::= { tlpPduDevice 1 }

tlpPduDeviceEntry OBJECT-TYPE
    SYNTAX TlpPduDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing device details for a particular pdu."
    INDEX { tlpDeviceIndex }
    ::= { tlpPduDeviceTable 1 }

TlpPduDeviceEntry ::= SEQUENCE {
    tlpPduDeviceMainLoadState               INTEGER,
    tlpPduDeviceMainLoadControllable        TruthValue,
    tlpPduDeviceMainLoadCommand             INTEGER,
    tlpPduDevicePowerOnDelay                Integer32,
    tlpPduDeviceTotalInputPowerRating       Integer32,
    tlpPduDeviceTemperatureC                Integer32,
    tlpPduDeviceTemperatureF                Integer32,
    tlpPduDevicePhaseImbalance              INTEGER,
    tlpPduDeviceOutputPowerTotal            Unsigned32,
    tlpPduDeviceAggregatePowerFactor        Unsigned32,
    tlpPduDeviceOutputCurrentPrecision      INTEGER }

tlpPduDeviceMainLoadState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the main output of the device."
    ::= { tlpPduDeviceEntry 1 }

tlpPduDeviceMainLoadControllable OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not the main output is controllable, and whether
        the ups can be turned off and on."
    ::= { tlpPduDeviceEntry 2 }

tlpPduDeviceMainLoadCommand OBJECT-TYPE
    SYNTAX INTEGER {
        idle(0),
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value to
        turnOff(1) will turn off the main output of the ups. Setting this
        value to turnOn(2) will turn on the main output of the ups. Setting
        this value to cycle(3) will turn off the main output, then turn it
        back on."
    ::= { tlpPduDeviceEntry 3 }

tlpPduDevicePowerOnDelay OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The configurable delay value, which sets the amount of additional
        time a given device will wait before it connects to a valid AC
        source. By default the delay value is set to 0. For devices with
        multiple AC input sources, this delay only applies to the primary
        input source."
    ::= { tlpPduDeviceEntry 4 }

tlpPduDeviceTotalInputPowerRating OBJECT-TYPE
    SYNTAX Integer32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total input power rating of all phases on the device. This
        value may not be supported on all models. If not supported, this
        value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpPduDeviceEntry 5 }

tlpPduDeviceTemperatureC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the PDU casing. Temperature
        measurements are not supported on all models. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpPduDeviceEntry 6 }

tlpPduDeviceTemperatureF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the PDU casing. Temperature
        measurements are not supported on all models. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpPduDeviceEntry 7 }

tlpPduDevicePhaseImbalance OBJECT-TYPE
    SYNTAX INTEGER (0..200)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The percentage of imbalance of the three phases. This value is in
        the range from 0% imbalance to 100% imbalance. The lower the value,
        the better balanced the phases are. If all load is on a single phase,
        the imbalance is defined as 100%."
    ::= { tlpPduDeviceEntry 8 }

tlpPduDeviceOutputPowerTotal OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The AC output total power for all circuits."
    ::= { tlpPduDeviceEntry 9 }

tlpPduDeviceAggregatePowerFactor OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The aggregate power factor of all phases."
  ::= { tlpPduDeviceEntry 10 }

tlpPduDeviceOutputCurrentPrecision OBJECT-TYPE
    SYNTAX INTEGER {
        wholeNumber(0),
        tenths(1),
        hundredths(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Output current is reported by tlpPduOutputCurrent, tlpPduOutputCurrentMin
        and tlpPduOutputCurrentMax. These values are all reported in hundredths(0.01A),
        which accommodates all models; however, on any given device the actual precision
        may vary. All current values must be divided by 100 to obtain the actual value.
        For example, if tlpPduOutputCurrent reports 500, then the actual output current
        is 5.00. This tlpPduDeviceOutputCurrentPrecision defines the actual resolution
        of that value: wholeNumber(0) is 5, tenths(1) is 5.0 and hundredths(2) is 5.00."
    ::= { tlpPduDeviceEntry 11 }

tlpPduInputTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduInputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input details of each pdu in device table. This is a sparse
        dependent table, and will contain the number of entries based upon
        the number of pdus managed by the agent as reported by tlpPduIdentNumPdus."
    ::= { tlpPduInput 1 }

tlpPduInputEntry OBJECT-TYPE
    SYNTAX TlpPduInputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular pdu managed
        by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpPduInputTable 1 }

TlpPduInputEntry ::= SEQUENCE {
    tlpPduInputNominalVoltage                   Unsigned32,
    tlpPduInputNominalVoltagePhaseToPhase       Unsigned32,
    tlpPduInputNominalVoltagePhaseToNeutral     Unsigned32,
    tlpPduInputLowTransferVoltage               Unsigned32,
    tlpPduInputLowTransferVoltageLowerBound     Unsigned32,
    tlpPduInputLowTransferVoltageUpperBound     Unsigned32,
    tlpPduInputHighTransferVoltage              Unsigned32,
    tlpPduInputHighTransferVoltageLowerBound    Unsigned32,
    tlpPduInputHighTransferVoltageUpperBound    Unsigned32,
    tlpPduInputCurrentLimit                     Unsigned32 }

tlpPduInputNominalVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpPduInputEntry 1 }

tlpPduInputNominalVoltagePhaseToPhase OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage as measured from
        phase to phase. This value may not be supported on all models.
        If not supported, this value will always return zero."
    ::= { tlpPduInputEntry 2 }

tlpPduInputNominalVoltagePhaseToNeutral OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage as measured from
        phase to neutral. This value may not be supported on all models.
        If not supported, this value will always return zero."
    ::= { tlpPduInputEntry 3 }

tlpPduInputLowTransferVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum line voltage allowed before the unit disconnects the
        AC input connection. This value may not be writable on all models.
        If not writable, the value will be rejected and an appropriate error
        message will be returned. When supported, the minimum and maximum
        values allowed are dictated by tlpPduInputLowTransferVoltageLowerBound
        and tlpPduInputLowTransferVoltageUpperBound respectively."
    ::= { tlpPduInputEntry 4 }

tlpPduInputLowTransferVoltageLowerBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The lower boundary value of tlpPduInputLowTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpPduInputEntry 5 }

tlpPduInputLowTransferVoltageUpperBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The upper boundary value of tlpPduInputLowTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpPduInputEntry 6 }

tlpPduInputHighTransferVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum line voltage allowed before the unit disconnects the
        AC input connection. This value may not be writable on all models.
        If not writable, the value will be rejected and an appropriate error
        message will be returned. When supported, the minimum and maximum
        values allowed are dictated by tlpPduInputHighTransferVoltageLowerBound
        and tlpPduInputHighTransferVoltageUpperBound respectively."
    ::= { tlpPduInputEntry 7 }

tlpPduInputHighTransferVoltageLowerBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The lower boundary value of tlpPduInputHighTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpPduInputEntry 8 }

tlpPduInputHighTransferVoltageUpperBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The upper boundary value of tlpPduInputHighTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpPduInputEntry 9 }

tlpPduInputCurrentLimit OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The capacity of each phase conductor in the pdu limited by the AC input
        cord or any other potential limiting factors unique to a given unit."
    ::= { tlpPduInputEntry 10 }

tlpPduInputPhaseTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduInputPhaseEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input phase details of each pdu in device table. This is a sparse
        dependent table, and will contain the number of entries based upon
        the value of tlpPduIdentNumInputs and tlpPduIdentNumPhases per each
        device."
    ::= { tlpPduInput 2 }

tlpPduInputPhaseEntry OBJECT-TYPE
    SYNTAX TlpPduInputPhaseEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular input
        phase managed by this agent."
    INDEX { tlpDeviceIndex, tlpPduInputPhaseIndex }
    ::= { tlpPduInputPhaseTable 1 }

TlpPduInputPhaseEntry ::= SEQUENCE {
    tlpPduInputPhaseIndex            Unsigned32,
    tlpPduInputPhasePhaseType        INTEGER,
    tlpPduInputPhaseFrequency        Unsigned32,
    tlpPduInputPhaseVoltage          Unsigned32,
    tlpPduInputPhaseVoltageMin       Unsigned32,
    tlpPduInputPhaseVoltageMax       Unsigned32,
    tlpPduInputPhaseCurrent          Unsigned32 }

tlpPduInputPhaseIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu phase."
    ::= { tlpPduInputPhaseEntry 1 }

tlpPduInputPhasePhaseType OBJECT-TYPE
    SYNTAX INTEGER {
        phaseToNeutral(0),
        phaseToPhase(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes whether measurements are phase to neutral(0) or phase
        to phase(1)."
    ::= { tlpPduInputPhaseEntry 2 }

tlpPduInputPhaseFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present input frequency for a given phase."
    ::= { tlpPduInputPhaseEntry 3 }

tlpPduInputPhaseVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present input voltage for a given phase."
    ::= { tlpPduInputPhaseEntry 4 }

tlpPduInputPhaseVoltageMin OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The minimum input voltage value since the last reset of this
        value for a given phase. If any value is written to either
        tlpPduInputVoltageMin or tlpPduInputVoltageMax, both will be
        reset to their appropriate minimum and maximum values."
    ::= { tlpPduInputPhaseEntry 5 }

tlpPduInputPhaseVoltageMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The maximum input voltage value since the last reset of this
        value for a given phase. If any value is written to either
        tlpPduInputVoltageMin or tlpPduInputVoltageMax, both will be
        reset to their appropriate minimum and maximum values."
    ::= { tlpPduInputPhaseEntry 6 }

tlpPduInputPhaseCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present input current for a given phase."
    ::= { tlpPduInputPhaseEntry 7 }

tlpPduOutputTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduOutputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The output details of each pdu in device table. This is a sparse
        dependent table, and will contain the number of entries based upon
        the value of tlpPduIdentNumOutputs per each device."
    ::= { tlpPduOutput 1 }

tlpPduOutputEntry OBJECT-TYPE
    SYNTAX TlpPduOutputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing output information applicable to a particular
        phase managed by this agent."
    INDEX { tlpDeviceIndex, tlpPduOutputIndex }
    ::= { tlpPduOutputTable 1 }

TlpPduOutputEntry ::= SEQUENCE {
    tlpPduOutputIndex               Unsigned32,
    tlpPduOutputPhase               INTEGER,
    tlpPduOutputPhaseType           INTEGER,
    tlpPduOutputVoltage             Unsigned32,
    tlpPduOutputCurrent             Unsigned32,
    tlpPduOutputCurrentMin          Unsigned32,
    tlpPduOutputCurrentMax          Unsigned32,
    tlpPduOutputActivePower         Unsigned32,
    tlpPduOutputPowerFactor         Unsigned32,
    tlpPduOutputSource              INTEGER,
    tlpPduOutputFrequency           Unsigned32,
    tlpPduOutputCalculatedPowerKVA	Unsigned32,
    tlpPduOutputCalculatedPowerKW	Unsigned32,
    tlpPduOutputCalculated24hrEnergy Unsigned32 }

tlpPduOutputIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu phase."
    ::= { tlpPduOutputEntry 1 }

tlpPduOutputPhase OBJECT-TYPE
    SYNTAX INTEGER {
        phase1(1),
        phase2(2),
        phase3(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates the phase associated with the output line."
    ::= { tlpPduOutputEntry 2 }

tlpPduOutputPhaseType OBJECT-TYPE
    SYNTAX INTEGER {
        phaseToNeutral(0),
        phaseToPhase(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes whether measurements are phase to neutral(0) or phase
        to phase(1)."
    ::= { tlpPduOutputEntry 3 }

tlpPduOutputVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present output voltage for a given output line."
    ::= { tlpPduOutputEntry 4 }

tlpPduOutputCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output current, in hundredths of amps, for a given phase.
		The actual precision of this value, whether a whole number, tenths
		or hundredths, is returned by tlpPduDeviceOutputCurrentPrecision."
    ::= { tlpPduOutputEntry 5 }

tlpPduOutputCurrentMin OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum current, in hundredths of amps, observed for a given phase since last reset.
        The actual precision of this value, whether a whole number, tenths or hundredths, is
        returned by tlpPduDeviceOutputCurrentPrecision."
    ::= { tlpPduOutputEntry 6 }

tlpPduOutputCurrentMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum current, in hundredths of amps, observed for a given phase since last reset.
        The actual precision of this value, whether a whole number, tenths or hundredths, is
        returned by tlpPduDeviceOutputCurrentPrecision."
    ::= { tlpPduOutputEntry 7 }

tlpPduOutputActivePower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output power for a given phase."
    ::= { tlpPduOutputEntry 8 }

tlpPduOutputPowerFactor OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output power factor, in hundredths of a percent, for a given
        phase."
    ::= { tlpPduOutputEntry 9 }

tlpPduOutputSource OBJECT-TYPE
    SYNTAX INTEGER {
        none(0),
        normal(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present source of output power. The enumeration none(0)
        indicates that there is no source of output power (and
        therefore no output power), for example, the system has opened
        the output breaker."
    ::= { tlpPduOutputEntry 10 }

tlpPduOutputFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output frequency. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpPduOutputEntry 11 }


tlpPduOutputCalculatedPowerKVA OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 KVA"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The calculated amount of apparent power being used."
    ::= { tlpPduOutputEntry 12 }

tlpPduOutputCalculatedPowerKW OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 KW"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The calculated amount of active power being used."
    ::= { tlpPduOutputEntry 13 }

tlpPduOutputCalculated24hrEnergy OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 KWH"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Estimated 24 hour power consumption using present value
        of tlpPduOutputCalculatedPowerKW."
    ::= { tlpPduOutputEntry 14 }


tlpPduOutletTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduOutletEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The receptacle status details of each pdu in device table. This
        is a sparse dependent table, and will contain the number of entries
        based upon the value of tlpPduIdentNumOutlets per each device."
    ::= { tlpPduOutlet 1 }

tlpPduOutletEntry OBJECT-TYPE
    SYNTAX TlpPduOutletEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular outlet
        managed by this agent."
    INDEX { tlpDeviceIndex, tlpPduOutletIndex }
    ::= { tlpPduOutletTable 1 }

TlpPduOutletEntry ::= SEQUENCE {
    tlpPduOutletIndex                   Unsigned32,
    tlpPduOutletName                    DisplayString,
    tlpPduOutletDescription             DisplayString,
    tlpPduOutletState                   INTEGER,
    tlpPduOutletControllable            TruthValue,
    tlpPduOutletCommand                 INTEGER,
    tlpPduOutletVoltage                 Unsigned32,
    tlpPduOutletCurrent                 Unsigned32,
    tlpPduOutletPower                   Unsigned32,
    tlpPduOutletRampAction              INTEGER,
    tlpPduOutletRampDelay               Integer32,
    tlpPduOutletShedAction              INTEGER,
    tlpPduOutletShedDelay               Integer32,
    tlpPduOutletGroup                   Integer32,
    tlpPduOutletBank                    Integer32,
    tlpPduOutletCircuit                 Integer32,
    tlpPduOutletPhase                   INTEGER }

tlpPduOutletIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu outlet."
    ::= { tlpPduOutletEntry 1 }

tlpPduOutletName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-definable string identifying the name of the outlet."
    ::= { tlpPduOutletEntry 2 }

tlpPduOutletDescription OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-definable string identifying the device(s) connected to
        the given outlet."
    ::= { tlpPduOutletEntry 3 }

tlpPduOutletState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the outlet."
    ::= { tlpPduOutletEntry 4 }

tlpPduOutletControllable OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes whether this outlet is controllable."
    ::= { tlpPduOutletEntry 5 }

tlpPduOutletCommand OBJECT-TYPE
    SYNTAX INTEGER {
        idle(0),
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value
        to turnOff(1) will turn off the outlet. Setting this value
        to turnOn(2) will turn on the outlet. Setting this value to
        cycle(3) will turn the outlet off, then turn it back on."
    ::= { tlpPduOutletEntry 6 }

tlpPduOutletVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present output voltage for a given outlet.
        If the voltage measurement is not supported for an individual
        receptacle regardless whether device-wide or just for this
        receptacle, then this value will always return ERROR_NO_SUCH_NAME.
        Device-wide support for receptacle voltage measurements is denoted
        by tlpPduSupportsOutletVoltage."
    ::= { tlpPduOutletEntry 7 }

tlpPduOutletCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output current, in hundredths of amps, of a given outlet.
        If the current measurement is not supported for an individual
        receptacle regardless whether device-wide or just for this
        receptacle, then this value will always return ERROR_NO_SUCH_NAME.
        Device-wide support for receptacle current measurements is denoted
        by tlpPduSupportsCurrentPower."
    ::= { tlpPduOutletEntry 8 }

tlpPduOutletPower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output power of a given outlet. If the power measurement is
        not supported for an individual receptacle regardless whether
        device-wide or just for this receptacle, then this value will
        always return ERROR_NO_SUCH_NAME. Device-wide support for receptacle
        power measurements is denoted by tlpPduSupportsCurrentPower."
    ::= { tlpPduOutletEntry 9 }

tlpPduOutletRampAction OBJECT-TYPE
    SYNTAX INTEGER {
        remainOff(0),
        turnOnAfterDelay(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The ramp action to take on a given outlet when powering on the
        device. If this receptacle is not controllable, as denoted by
        tlpPduOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpPduOutletEntry 10 }

tlpPduOutletRampDelay OBJECT-TYPE
    SYNTAX Integer32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of seconds to delay before powering on the given outlet.
        If this receptacle is not controllable, as denoted by
        tlpPduOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpPduOutletEntry 11 }

tlpPduOutletShedAction OBJECT-TYPE
    SYNTAX INTEGER {
        remainOn(0),
        turnOffAfterDelay(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The shed action to take on a given outlet when powering off the
        device. If this receptacle is not controllable, as denoted by
        tlpPduOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpPduOutletEntry 12 }

tlpPduOutletShedDelay OBJECT-TYPE
    SYNTAX Integer32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of seconds to delay before powering off the given outlet.
        If this receptacle is not controllable, as denoted by
        tlpPduOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpPduOutletEntry 13 }

tlpPduOutletGroup OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The tlpPduOutletGroupIndex group index of the respective group to
        which the given outlet is assigned, or zero (0) if the given
        outlet is not grouped."
    ::= { tlpPduOutletEntry 14 }

tlpPduOutletBank OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which bank the given outlet is located."
    ::= { tlpPduOutletEntry 15 }

tlpPduOutletCircuit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which circuit the given outlet is wired."
    ::= { tlpPduOutletEntry 16 }

tlpPduOutletPhase OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        phase1(1),
        phase2(2),
        phase3(3),
        phase1-2(4),
        phase2-3(5),
        phase3-1(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which phase or phases the given outlet is
        associated. For single phase devices, this object will
        always return phase1(1). For 3-phase devices with outlets
        wired to a single phase, this object will return phase1(1),
        phase2(2) or phase3(3). For 3-phase devices with outlets
        wired to two phases, this object will return phase1-2(4)
        for phases 1 and 2, phase2-3(5) for phases 2 and 3, and
        phase3-1(6) for phases 3 and 1. Devices that do not report
        phase connections will return unknown(0)."
    ::= { tlpPduOutletEntry 17 }

tlpPduOutletGroupTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduOutletGroupEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The receptacle grouping details of each pdu in device table.
        This is a sparse dependent table, and will contain the number
        of entries based upon the value of tlpPduIdentNumOutletGroups per
        each device."
    ::= { tlpPduOutlet 2 }

tlpPduOutletGroupEntry OBJECT-TYPE
    SYNTAX TlpPduOutletGroupEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular outlet
        group managed by this agent."
    INDEX { tlpDeviceIndex, tlpPduOutletGroupIndex }
    ::= { tlpPduOutletGroupTable 1 }

TlpPduOutletGroupEntry ::= SEQUENCE {
    tlpPduOutletGroupIndex              Unsigned32,
    tlpPduOutletGroupRowStatus	        RowStatus,
    tlpPduOutletGroupName               DisplayString,
    tlpPduOutletGroupDescription        DisplayString,
    tlpPduOutletGroupState              INTEGER,
    tlpPduOutletGroupCommand            INTEGER }

tlpPduOutletGroupIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu outlet group."
    ::= { tlpPduOutletGroupEntry 1 }

tlpPduOutletGroupRowStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Row status for the tlpPduOutletGroupTable"
    ::= { tlpPduOutletGroupEntry 2 }

tlpPduOutletGroupName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of this outlet group."
    ::= { tlpPduOutletGroupEntry 3 }

tlpPduOutletGroupDescription OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A description for this outlet group."
    ::= { tlpPduOutletGroupEntry 4 }

tlpPduOutletGroupState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2),
        mixed(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the outlet group. This value will report
        off(1) if all outlets in the group are off, on(2) if all outlets
        in the group are on, mixed(3) if some outlets in the group are
        off and others are on, or unknown(0) if the state of the outlets
        cannot be determined."
    ::= { tlpPduOutletGroupEntry 5 }

tlpPduOutletGroupCommand OBJECT-TYPE
    SYNTAX INTEGER {
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value to
        turnOff(1) will turn off every outlet associated with the group.
        Setting this value to turnOn(2) will turn on every outlet associated
        with the group. Setting this value to cycle(3) will turn off every
        outlet associated with the group, then turn each outlet back on."
    ::= { tlpPduOutletGroupEntry 6 }

tlpPduCircuitTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduCircuitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of pdu circuit table entries.  The number of entries
        is given by the value of tlpPduIdentNumCircuits."
    ::= { tlpPduCircuit 1 }

tlpPduCircuitEntry OBJECT-TYPE
    SYNTAX TlpPduCircuitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular circuit
        managed by this agent."
    INDEX { tlpDeviceIndex, tlpPduCircuitIndex }
    ::= { tlpPduCircuitTable 1 }

TlpPduCircuitEntry ::= SEQUENCE {
    tlpPduCircuitIndex              Unsigned32,
    tlpPduCircuitPhase              INTEGER,
    tlpPduCircuitInputVoltage       Integer32,
    tlpPduCircuitTotalCurrent       Integer32,
    tlpPduCircuitCurrentLimit       Integer32,
    tlpPduCircuitCurrentMin         Integer32,
    tlpPduCircuitCurrentMax         Integer32,
    tlpPduCircuitTotalPower         Integer32,
    tlpPduCircuitPowerFactor        INTEGER,
    tlpPduCircuitUtilization        Unsigned32 }

tlpPduCircuitIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu circuit."
    ::= { tlpPduCircuitEntry 1 }

tlpPduCircuitPhase OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        phase1(1),
        phase2(2),
        phase3(3),
        phase1-2(4),
        phase2-3(5),
        phase3-1(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which phase or phases the given circuit is
        wired. For single phase devices, this object will always
        return phase1(1). For 3-phase devices with outlets wired
        to a single phase, this object will return phase1(1),
        phase2(2) or phase3(3). For 3-phase devices with outlets
        wired to two phases, this object will return phase1-2(4)
        for phases 1 and 2, phase2-3(5) for phases 2 and 3, and
        phase3-1(6) for phases 3 and 1. Devices that do not report
        phase connections will return unknown(0)."
    ::= { tlpPduCircuitEntry 2 }

tlpPduCircuitInputVoltage OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The input voltage for a circuit."
    ::= { tlpPduCircuitEntry 3 }

tlpPduCircuitTotalCurrent OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total current, in hundredths of amps, for all receptacles
        in a given circuit."
    ::= { tlpPduCircuitEntry 4 }

tlpPduCircuitCurrentLimit OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current capacity, in hundredths of amps, of a circuit in
        the system limited by the circuit breaker or wiring used in a
        given unit."
    ::= { tlpPduCircuitEntry 5 }

tlpPduCircuitCurrentMin OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
         "The minimum total current, in hundredths of amps, for
         all receptacles in a given circuit since last reset."
    ::= { tlpPduCircuitEntry 6 }

tlpPduCircuitCurrentMax OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum total current, in hundredths of amps, for
        all receptacles in a given circuit since last reset."
    ::= { tlpPduCircuitEntry 7 }

tlpPduCircuitTotalPower OBJECT-TYPE
    SYNTAX Integer32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total power for all receptacles in a given circuit."
    ::= { tlpPduCircuitEntry 8 }

tlpPduCircuitPowerFactor OBJECT-TYPE
    SYNTAX INTEGER (0..200)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The power factor for all receptacles in a given circuit."
    ::= { tlpPduCircuitEntry 9 }

tlpPduCircuitUtilization OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The percentage of utilization of a given circuit. This is typically
        a calculation of tlpPduCircuitTotalCurrent / tlpPduCircuitCurrentLimit."
    ::= { tlpPduCircuitEntry 10 }

tlpPduBreakerTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduBreakerEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of pdu bank breaker table entries. The number of entries
        is given by the value of tlpPduIdentNumBreakers."
    ::= { tlpPduBreaker 1 }

tlpPduBreakerEntry OBJECT-TYPE
    SYNTAX TlpPduBreakerEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular bank
        breaker managed by this agent."
    INDEX { tlpDeviceIndex, tlpPduBreakerIndex }
    ::= { tlpPduBreakerTable 1 }

TlpPduBreakerEntry ::= SEQUENCE {
    tlpPduBreakerIndex          Unsigned32,
    tlpPduBreakerStatus         INTEGER }

tlpPduBreakerIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu bank breaker."
    ::= { tlpPduBreakerEntry 1 }

tlpPduBreakerStatus OBJECT-TYPE
    SYNTAX INTEGER {
        open(0),
        closed(1),
        notInstalled(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the bank breaker is open(0), closed(1) or not
        installed(2)."
    ::= { tlpPduBreakerEntry 2 }

tlpPduHeatsinkTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduHeatsinkEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of pdu heatsink thermistor table entries. The number of entries
        is given by the value of tlpPduIdentNumHeatsinks."
    ::= { tlpPduHeatsink 1 }

tlpPduHeatsinkEntry OBJECT-TYPE
    SYNTAX TlpPduHeatsinkEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular heatsink
        thermistor managed by this agent."
    INDEX { tlpDeviceIndex, tlpPduHeatsinkIndex }
    ::= { tlpPduHeatsinkTable 1 }

TlpPduHeatsinkEntry ::= SEQUENCE {
    tlpPduHeatsinkIndex             Unsigned32,
    tlpPduHeatsinkStatus            INTEGER,
    tlpPduHeatsinkTemperatureC      Integer32,
    tlpPduHeatsinkTemperatureF      Integer32 }

tlpPduHeatsinkIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu heatsink thermistor."
    ::= { tlpPduHeatsinkEntry 1 }

tlpPduHeatsinkStatus OBJECT-TYPE
    SYNTAX INTEGER {
        notAvailable(0),
        available(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the heatsink thermistor is available. The entry
        will return notAvailable(0) if a thermistor is either non-existent or
        disconnected, or available(1) when connected."
    ::= { tlpPduHeatsinkEntry 2 }

tlpPduHeatsinkTemperatureC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature at the heatsink thermistor in tenths degrees Celsius.
        Heatsink temperature measurements are not supported on all models. If
        not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpPduHeatsinkEntry 3 }

tlpPduHeatsinkTemperatureF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature at the heatsink thermistor in tenths degrees Fahrenheit.
        Heatsink temperature measurements are not supported on all models. If
        not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpPduHeatsinkEntry 4 }

tlpPduControlTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The actionable controls for each pdu in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are pdus."
    ::= { tlpPduControl 1 }

tlpPduControlEntry OBJECT-TYPE
    SYNTAX TlpPduControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing controls for a particular pdu."
    INDEX { tlpDeviceIndex }
    ::= { tlpPduControlTable 1 }

TlpPduControlEntry ::= SEQUENCE {
    tlpPduControlRamp           TruthValue,
    tlpPduControlShed           TruthValue,
    tlpPduControlPduOn          TruthValue,
    tlpPduControlPduOff         TruthValue,
    tlpPduControlPduReboot      TruthValue }

tlpPduControlRamp OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to initiate the pre-designated ramp
        sequence on the pdu."
    ::= { tlpPduControlEntry 1 }

tlpPduControlShed OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to initiate the pre-designated shed
        sequence on the pdu."
    ::= { tlpPduControlEntry 2 }

tlpPduControlPduOn OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to turn the pdu on."
    ::= { tlpPduControlEntry 3 }

tlpPduControlPduOff OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to turn the pdu off."
    ::= { tlpPduControlEntry 4 }

tlpPduControlPduReboot OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to reboot the pdu."
    ::= { tlpPduControlEntry 5 }

tlpPduConfigTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration options of each pdu in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are pdus. Not all options are available on all
        pdus."
    ::= { tlpPduConfig 1 }

tlpPduConfigEntry OBJECT-TYPE
    SYNTAX TlpPduConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing configuration options for a particular pdu."
    INDEX { tlpDeviceIndex }
    ::= { tlpPduConfigTable 1 }

TlpPduConfigEntry ::= SEQUENCE {
    tlpPduConfigInputVoltage            Unsigned32 }

tlpPduConfigInputVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME. This value may not be writable on all models. If
        not writable, the value will be rejected and an appropriate error
        message will be returned."
    ::= { tlpPduConfigEntry 1 }

tlpPduConfigPhaseThresholdTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpPduConfigPhaseThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration options of each pdu in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are pdus. Not all options are available on all
        pdus."
    ::= { tlpPduConfig 2 }

tlpPduConfigPhaseThresholdEntry OBJECT-TYPE
    SYNTAX TlpPduConfigPhaseThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing threshold configuration options for a particular pdu."
    INDEX { tlpDeviceIndex, tlpPduPhaseIndex }
    ::= { tlpPduConfigPhaseThresholdTable 1 }

TlpPduConfigPhaseThresholdEntry ::= SEQUENCE {
    tlpPduPhaseIndex                             Unsigned32,
    tlpPduConfigOutputCurrentThresholdTolerance  Unsigned32,
	tlpPduConfigOutputCurrentHighThreshold       Unsigned32,
	tlpPduConfigOutputCurrentLowThreshold        Unsigned32}

tlpPduPhaseIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the pdu phase."
    ::= { tlpPduConfigPhaseThresholdEntry 1 }

tlpPduConfigOutputCurrentThresholdTolerance OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The threshold tolerance value used together with output current high/low
		threshold to evaluate output current high/low condition."
    ::= { tlpPduConfigPhaseThresholdEntry 2 }

tlpPduConfigOutputCurrentHighThreshold OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The value at which a output current high condition is declared."
    ::= { tlpPduConfigPhaseThresholdEntry 3 }

tlpPduConfigOutputCurrentLowThreshold OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The value at which a output current low condition is declared."
    ::= { tlpPduConfigPhaseThresholdEntry 4 }

-- envirosense

tlpEnvIdentNumEnvirosense OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of envirosense devices that this agent is currently managing."
    ::= { tlpEnvIdent 1 }

tlpEnvIdentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpEnvIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The identity details of each envirosense in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are envirosenses."
    ::= { tlpEnvIdent 2 }

tlpEnvIdentEntry OBJECT-TYPE
    SYNTAX TlpEnvIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing details for a particular envirosense."
    INDEX { tlpDeviceIndex }
    ::= { tlpEnvIdentTable 1 }

TlpEnvIdentEntry ::= SEQUENCE {
    tlpEnvIdentTempSupported        TruthValue,
    tlpEnvIdentHumiditySupported    TruthValue,
    tlpEnvNumInputContacts          Unsigned32,
    tlpEnvNumOutputContacts         Unsigned32 }

tlpEnvIdentTempSupported OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not temperature is supported."
    ::= { tlpEnvIdentEntry 1 }

tlpEnvIdentHumiditySupported OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not temperature is supported."
    ::= { tlpEnvIdentEntry 2 }

tlpEnvNumInputContacts OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of input contacts supported by the envirosense."
    ::= { tlpEnvIdentEntry 3 }

tlpEnvNumOutputContacts OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of output contacts supported by the envirosense."
    ::= { tlpEnvIdentEntry 4 }

tlpEnvTemperatureTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpEnvTemperatureEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The temperature details of each envirosense in device table. The
        number of entries is given by the value of tlpDevicesNumDevices."
    ::= { tlpEnvDetail 1 }

tlpEnvTemperatureEntry OBJECT-TYPE
    SYNTAX TlpEnvTemperatureEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing temperature details for a particular
        envirosense."
        INDEX { tlpDeviceIndex }
    ::= { tlpEnvTemperatureTable 1 }

TlpEnvTemperatureEntry ::= SEQUENCE {
    tlpEnvTemperatureC              Integer32,
    tlpEnvTemperatureF              Integer32,
    tlpEnvTemperatureInAlarm        TruthValue }

tlpEnvTemperatureC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature."
    ::= { tlpEnvTemperatureEntry 1 }

tlpEnvTemperatureF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature."
    ::= { tlpEnvTemperatureEntry 2 }

tlpEnvTemperatureInAlarm OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not temperature is in alarm."
    ::= { tlpEnvTemperatureEntry 3 }

tlpEnvHumidityTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpEnvHumidityEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The humidity details of each envirosense in device table. The
        number of entries is given by the value of tlpDevicesNumDevices."
    ::= { tlpEnvDetail 2 }

tlpEnvHumidityEntry OBJECT-TYPE
    SYNTAX TlpEnvHumidityEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing humidity details for a particular
        envirosense."
        INDEX { tlpDeviceIndex }
    ::= { tlpEnvHumidityTable 1 }

TlpEnvHumidityEntry ::= SEQUENCE {
    tlpEnvHumidityHumidity          INTEGER,
    tlpEnvHumidityInAlarm           TruthValue }

tlpEnvHumidityHumidity OBJECT-TYPE
    SYNTAX INTEGER (0..100)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient humidity."
    ::= { tlpEnvHumidityEntry 1 }

tlpEnvHumidityInAlarm OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not humidity is in alarm."
    ::= { tlpEnvHumidityEntry 2 }

tlpEnvInputContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpEnvInputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input contact details of each envirosense in device table.
        The number of entries is given by the value of tlpDevicesNumDevices."
    ::= { tlpEnvDetail 3 }

tlpEnvInputContactEntry OBJECT-TYPE
    SYNTAX TlpEnvInputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing input contact details for a particular
        envirosense."
        INDEX { tlpDeviceIndex, tlpEnvInputContactIndex }
    ::= { tlpEnvInputContactTable 1 }

TlpEnvInputContactEntry ::= SEQUENCE {
    tlpEnvInputContactIndex             Unsigned32,
    tlpEnvInputContactName              DisplayString,
    tlpEnvInputContactNormalState       INTEGER,
    tlpEnvInputContactCurrentState      INTEGER,
    tlpEnvInputContactInAlarm           TruthValue }

tlpEnvInputContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the envirosense input contact details."
    ::= { tlpEnvInputContactEntry 1 }

tlpEnvInputContactName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name or description of the contact."
    ::= { tlpEnvInputContactEntry 2 }

tlpEnvInputContactNormalState OBJECT-TYPE
    SYNTAX INTEGER {
        open(0),
        closed(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The normal operating position of the contact."
    ::= { tlpEnvInputContactEntry 3 }

tlpEnvInputContactCurrentState OBJECT-TYPE
    SYNTAX INTEGER {
        open(0),
        closed(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the contact."
    ::= { tlpEnvInputContactEntry 4 }

tlpEnvInputContactInAlarm OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not the contact is in alarm."
    ::= { tlpEnvInputContactEntry 5 }

tlpEnvOutputContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpEnvOutputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The output contact details of each envirosense in device table.
        The number of entries is given by the value of tlpDevicesNumDevices."
    ::= { tlpEnvDetail 4 }

tlpEnvOutputContactEntry OBJECT-TYPE
    SYNTAX TlpEnvOutputContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing output contact details for a particular
        envirosense."
        INDEX { tlpDeviceIndex, tlpEnvOutputContactIndex }
    ::= { tlpEnvOutputContactTable 1 }

TlpEnvOutputContactEntry ::= SEQUENCE {
    tlpEnvOutputContactIndex            Unsigned32,
    tlpEnvOutputContactName             DisplayString,
    tlpEnvOutputContactNormalState      INTEGER,
    tlpEnvOutputContactCurrentState     INTEGER,
    tlpEnvOutputContactInAlarm          TruthValue }

tlpEnvOutputContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the envirosense output contact details."
    ::= { tlpEnvOutputContactEntry 1 }

tlpEnvOutputContactName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name or description of the contact."
    ::= { tlpEnvOutputContactEntry 2 }

tlpEnvOutputContactNormalState OBJECT-TYPE
    SYNTAX INTEGER {
        open(0),
        closed(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The normal operating position of the contact."
    ::= { tlpEnvOutputContactEntry 3 }

tlpEnvOutputContactCurrentState OBJECT-TYPE
    SYNTAX INTEGER {
        open(0),
        closed(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The current state of the contact."
    ::= { tlpEnvOutputContactEntry 4 }

tlpEnvOutputContactInAlarm OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not the contact is in alarm."
    ::= { tlpEnvOutputContactEntry 5 }

tlpEnvConfigTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpEnvConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration options of each envirosense in device table.
        This is a sparse dependent table, and will contain some subset of
        devices that are envirosenses. Not all options are available on
        all envirosenses."
    ::= { tlpEnvConfig 1 }

tlpEnvConfigEntry OBJECT-TYPE
    SYNTAX TlpEnvConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing configuration options for a particular
        envirosenses."
    INDEX { tlpDeviceIndex }
    ::= { tlpEnvConfigTable 1 }

TlpEnvConfigEntry ::= SEQUENCE {
    tlpEnvTemperatureLowLimit           Integer32,
    tlpEnvTemperatureHighLimit          Integer32,
    tlpEnvHumidityLowLimit              INTEGER,
    tlpEnvHumidityHighLimit             INTEGER }

tlpEnvTemperatureLowLimit OBJECT-TYPE
    SYNTAX Integer32
    UNITS "degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The lower alarm limit for ambient temperature."
    ::= { tlpEnvConfigEntry 1 }

tlpEnvTemperatureHighLimit OBJECT-TYPE
    SYNTAX Integer32
    UNITS "degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The upper alarm limit for ambient temperature."
    ::= { tlpEnvConfigEntry 2 }

tlpEnvHumidityLowLimit OBJECT-TYPE
    SYNTAX INTEGER (0..100)
    UNITS "percent"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The lower alarm limit for ambient humidity."
    ::= { tlpEnvConfigEntry 3 }

tlpEnvHumidityHighLimit OBJECT-TYPE
    SYNTAX INTEGER (0..100)
    UNITS "percent"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The upper alarm limit for ambient humidity."
    ::= { tlpEnvConfigEntry 4 }

-- ats

tlpAtsIdentNumAts OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of ats devices that this agent is currently managing."
    ::= { tlpAtsIdent 1 }

tlpAtsIdentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The identity details of each ats in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are atss."
    ::= { tlpAtsIdent 2 }

tlpAtsIdentEntry OBJECT-TYPE
    SYNTAX TlpAtsIdentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing details for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsIdentTable 1 }

TlpAtsIdentEntry ::= SEQUENCE {
    tlpAtsIdentNumInputs                Unsigned32,
    tlpAtsIdentNumOutputs               Unsigned32,
    tlpAtsIdentNumPhases                Unsigned32,
    tlpAtsIdentNumOutlets               Unsigned32,
    tlpAtsIdentNumOutletGroups          Unsigned32,
    tlpAtsIdentNumCircuits              Unsigned32,
    tlpAtsIdentNumBreakers              Unsigned32,
    tlpAtsIdentNumHeatsinks             Unsigned32 }

tlpAtsIdentNumInputs OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of inputs supported by the ats."
    ::= { tlpAtsIdentEntry 1 }

tlpAtsIdentNumOutputs OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of outputs supported by the ats."
    ::= { tlpAtsIdentEntry 2 }

tlpAtsIdentNumPhases OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of phases per input supported by the ats."
    ::= { tlpAtsIdentEntry 3 }

tlpAtsIdentNumOutlets OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of receptacles supported by the ats."
    ::= { tlpAtsIdentEntry 4 }

tlpAtsIdentNumOutletGroups OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of receptacle groups supported by the ats."
    ::= { tlpAtsIdentEntry 5 }

tlpAtsIdentNumCircuits OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of circuits supported by the ats."
    ::= { tlpAtsIdentEntry 6 }

tlpAtsIdentNumBreakers OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of monitored bank breakers supported by the ats.
		 If the ats has breakers that are not monitored by the device,
		 the value of zero(0) will be returned."
    ::= { tlpAtsIdentEntry 7 }

tlpAtsIdentNumHeatsinks OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of heatsink thermistors supported by the ats."
    ::= { tlpAtsIdentEntry 8 }

tlpAtsSupportsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsSupportsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The supported details of each ats in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are atss."
    ::= { tlpAtsIdent 3 }

tlpAtsSupportsEntry OBJECT-TYPE
    SYNTAX TlpAtsSupportsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing supported details for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsSupportsTable 1 }

TlpAtsSupportsEntry ::= SEQUENCE {
    tlpAtsSupportsEnergywise            TruthValue,
    tlpAtsSupportsRampShed              TruthValue,
    tlpAtsSupportsOutletGroup           TruthValue,
    tlpAtsSupportsOutletCurrentPower    TruthValue,
    tlpAtsSupportsOutletVoltage         TruthValue }

tlpAtsSupportsEnergywise OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ats supports Cisco EnergyWise."
    ::= { tlpAtsSupportsEntry 1 }

tlpAtsSupportsRampShed OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ats supports ramping and shedding."
    ::= { tlpAtsSupportsEntry 2 }

tlpAtsSupportsOutletGroup OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ats supports outlet groups."
    ::= { tlpAtsSupportsEntry 3 }

tlpAtsSupportsOutletCurrentPower OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ats reports individual receptacle current
        and power measurements. Devices that do not support individual
        receptacle measurements will always return ERROR_NO_SUCH_NAME for
        tlpAtsOutletCurrent and tlpAtsOutletPower."
    ::= { tlpAtsSupportsEntry 4 }

tlpAtsSupportsOutletVoltage OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the ats reports individual receptacle voltage
        measurements. Devices that do not support individual receptacle
        measurements will always return ERROR_NO_SUCH_NAME for
        tlpAtsOutletVoltage."
    ::= { tlpAtsSupportsEntry 5 }

tlpAtsDisplayTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsDisplayEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The display details of each ats in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are atss."
    ::= { tlpAtsIdent 4 }

tlpAtsDisplayEntry OBJECT-TYPE
    SYNTAX TlpAtsDisplayEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing display details for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsDisplayTable 1 }

TlpAtsDisplayEntry ::= SEQUENCE {
    tlpAtsDisplayScheme             INTEGER,
    tlpAtsDisplayOrientation        INTEGER,
    tlpAtsDisplayAutoScroll         INTEGER,
    tlpAtsDisplayIntensity          INTEGER,
    tlpAtsDisplayUnits              INTEGER }

tlpAtsDisplayScheme OBJECT-TYPE
    SYNTAX INTEGER {
        schemeReverse(0),
        schemeNormal(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the display scheme of the LED display. The value
        schemeReverse(0) indicates the tricolor LED indicator uses an
        alternate pattern. The value schemeNormal(1) indicates the
        LED indicator uses the standard pattern. This value may not
        be supported on all models. If not supported, the value will
        always return schemeNormal(1)."
    ::= { tlpAtsDisplayEntry 1 }

tlpAtsDisplayOrientation OBJECT-TYPE
    SYNTAX INTEGER {
        displayNormal(0),
        displayReverse(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the physical orientation of the LED display. The value
        displayNormal(0) indicates the display is right-side-up (normal).
        The value displayReverse(1) indicates the display is upside-down.
        This value may not be supported on all models. If not supported,
        the value will always return displayNormal(0)."
    ::= { tlpAtsDisplayEntry 2 }

tlpAtsDisplayAutoScroll OBJECT-TYPE
    SYNTAX INTEGER {
        scrollDisabled(0),
        scrollEnabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates whether the LED display automatically scrolls through
        displaying status values."
    ::= { tlpAtsDisplayEntry 3 }

tlpAtsDisplayIntensity OBJECT-TYPE
    SYNTAX INTEGER {
        intensity25(1),
        intensity50(2),
        intensity75(3),
        intensity100(4) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the intensity, as a percentage, of the LED display. The
        value intensity100(4) indicates the display is at 100% intensity.
        The other values indicate the display is at a lesser intensity based
        upon the respective percentage denoted."
    ::= { tlpAtsDisplayEntry 4 }

tlpAtsDisplayUnits OBJECT-TYPE
    SYNTAX INTEGER {
        normal(0),
        metric(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates the units of measurement displayed on the LED display.
        The value normal(0) indicates that a temperature value, for example,
        is displayed in degrees Fahrenheit. The value metric(1) indicates a
        temperature value is displayed in degrees Celsius."
    ::= { tlpAtsDisplayEntry 5 }

tlpAtsDeviceTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The device details of each ats in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are atss."
    ::= { tlpAtsDevice 1 }

tlpAtsDeviceEntry OBJECT-TYPE
    SYNTAX TlpAtsDeviceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing device details for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsDeviceTable 1 }

TlpAtsDeviceEntry ::= SEQUENCE {
    tlpAtsDeviceMainLoadState               INTEGER,
    tlpAtsDeviceMainLoadControllable        TruthValue,
    tlpAtsDeviceMainLoadCommand             INTEGER,
    tlpAtsDevicePowerOnDelay                Integer32,
    tlpAtsDeviceTotalInputPowerRating       Integer32,
    tlpAtsDeviceTemperatureC                Integer32,
    tlpAtsDeviceTemperatureF                Integer32,
    tlpAtsDevicePhaseImbalance              INTEGER,
    tlpAtsDeviceOutputPowerTotal            Unsigned32,
    tlpAtsDeviceAggregatePowerFactor        Unsigned32,
    tlpAtsDeviceOutputCurrentPrecision      INTEGER,
    tlpAtsDeviceGeneralFault                TruthValue }

tlpAtsDeviceMainLoadState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the main output of the device."
    ::= { tlpAtsDeviceEntry 1 }

tlpAtsDeviceMainLoadControllable OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not the main output is controllable, and whether
        the ups can be turned off and on."
    ::= { tlpAtsDeviceEntry 2 }

tlpAtsDeviceMainLoadCommand OBJECT-TYPE
    SYNTAX INTEGER {
        idle(0),
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value to
        turnOff(1) will turn off the main output of the ups. Setting this
        value to turnOn(2) will turn on the main output of the ups. Setting
        this value to cycle(3) will turn off the main output, then turn it
        back on."
    ::= { tlpAtsDeviceEntry 3 }

tlpAtsDevicePowerOnDelay OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The configurable delay value, which sets the amount of additional
        time a given device will wait before it connects to a valid AC
        source. By default the delay value is set to 0. For devices with
        multiple AC input sources, this delay only applies to the primary
        input source."
    ::= { tlpAtsDeviceEntry 4 }

tlpAtsDeviceTotalInputPowerRating OBJECT-TYPE
    SYNTAX Integer32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total input power rating of all phases on the device. This
        value may not be supported on all models. If not supported, this
        value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsDeviceEntry 5 }

tlpAtsDeviceTemperatureC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the ats casing. Temperature
        measurements are not supported on all models. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsDeviceEntry 6 }

tlpAtsDeviceTemperatureF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature at or near the ats casing. Temperature
        measurements are not supported on all models. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsDeviceEntry 7 }

tlpAtsDevicePhaseImbalance OBJECT-TYPE
    SYNTAX INTEGER (0..200)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The percentage of imbalance of the three phases. This value is in
        the range from 0% imbalance to 100% imbalance. The lower the value,
        the better balanced the phases are. If all load is on a single phase,
        the imbalance is defined as 100%."
    ::= { tlpAtsDeviceEntry 8 }

tlpAtsDeviceOutputPowerTotal OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The AC output total power for all circuits."
    ::= { tlpAtsDeviceEntry 9 }

tlpAtsDeviceAggregatePowerFactor OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The aggregate power factor of all phases."
  ::= { tlpAtsDeviceEntry 10 }

tlpAtsDeviceOutputCurrentPrecision OBJECT-TYPE
    SYNTAX INTEGER {
        none(0),
        tenths(1),
        hundredths(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Output current is reported by tlpAtsOutputCurrent, tlpAtsOutputCurrentMin
        and tlpAtsOutputCurrentMax. These values are all reported in hundredths
        (0.01A), which accommodates all models; however, on any given device the
        actual precision may vary. This value describes the actual precision, which
        can be used as a divisor against the output current values to obtain the
        true output current measurement. For example, if tlpAtsOutputCurrent reports
        530 and tlpAtsDeviceOutputCurrentPrecision is tenths(1), then the actual
        output current is 5.3."
    ::= { tlpAtsDeviceEntry 11 }

tlpAtsDeviceGeneralFault OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether or not the ats is in a system fault condition. When the
        ats is in such a fault condition, the condition can be cleared with
        tlpAtsControlResetGeneralFault."
    ::= { tlpAtsDeviceEntry 12 }

tlpAtsInputTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsInputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input details of each ats in device table. This is a sparse
        dependent table, and will contain the number of entries based upon
        the number of atss managed by the agent as reported by tlpAtsIdentNumAts."
    ::= { tlpAtsInput 1 }

tlpAtsInputEntry OBJECT-TYPE
    SYNTAX TlpAtsInputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular primary
        input phase managed by this agent."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsInputTable 1 }

TlpAtsInputEntry ::= SEQUENCE {
    tlpAtsInputNominalVoltage                   Unsigned32,
    tlpAtsInputNominalVoltagePhaseToPhase       Unsigned32,
    tlpAtsInputNominalVoltagePhaseToNeutral     Unsigned32,
    tlpAtsInputBadTransferVoltage               Unsigned32,
    tlpAtsInputBadTransferVoltageLowerBound     Unsigned32,
    tlpAtsInputBadTransferVoltageUpperBound     Unsigned32,
    tlpAtsInputHighTransferVoltage              Unsigned32,
    tlpAtsInputHighTransferVoltageLowerBound    Unsigned32,
    tlpAtsInputHighTransferVoltageUpperBound    Unsigned32,
    tlpAtsInputFairVoltageThreshold             Unsigned32,
    tlpAtsInputBadVoltageThreshold              Unsigned32,
    tlpAtsInputSourceAvailability               INTEGER,
    tlpAtsInputSourceInUse                      INTEGER,
    tlpAtsInputSourceTransitionCount            Unsigned32,
    tlpAtsInputCurrentLimit                     Unsigned32 }

tlpAtsInputNominalVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsInputEntry 1 }

tlpAtsInputNominalVoltagePhaseToPhase OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage as measured from
        phase to phase. This value may not be supported on all models.
        If not supported, this value will always return zero."
    ::= { tlpAtsInputEntry 2 }

tlpAtsInputNominalVoltagePhaseToNeutral OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage as measured from
        phase to neutral. This value may not be supported on all models.
        If not supported, this value will always return zero."
    ::= { tlpAtsInputEntry 3 }

tlpAtsInputBadTransferVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum line voltage allowed before the unit disconnects the
        AC input connection."
    ::= { tlpAtsInputEntry 4 }

tlpAtsInputBadTransferVoltageLowerBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The lower boundary value of tlpAtsInputBadTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpAtsInputEntry 5 }

tlpAtsInputBadTransferVoltageUpperBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The upper boundary value of tlpAtsInputBadTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpAtsInputEntry 6 }

tlpAtsInputHighTransferVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum line voltage allowed before the unit disconnects the
        AC input connection."
    ::= { tlpAtsInputEntry 7 }

tlpAtsInputHighTransferVoltageLowerBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The lower boundary value of tlpAtsInputHighTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpAtsInputEntry 8 }

tlpAtsInputHighTransferVoltageUpperBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The upper boundary value of tlpAtsInputHighTransferVoltage as dictated
        by the hardware of the unit."
    ::= { tlpAtsInputEntry 9 }

tlpAtsInputFairVoltageThreshold OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The voltage level at which the device will transition between sources."
    ::= { tlpAtsInputEntry 10 }

tlpAtsInputBadVoltageThreshold OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum line voltage allowed before the device disconnects the AC input
        connection."
    ::= { tlpAtsInputEntry 11 }

tlpAtsInputSourceAvailability OBJECT-TYPE
    SYNTAX INTEGER {
        none(0),
        inputSourceA(1),
        inputSourceB(2),
        inputSourceAB(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The determination as to which of two AC input lines are available.
        The agent will return either inputSourceA(1) or inputSourceB(2) if
        only one line is available, or inputSourceAB(3) if both lines are
        available. The agent will never return none(0) since the device will
        be powered off if neither line is available."
    ::= { tlpAtsInputEntry 12 }

tlpAtsInputSourceInUse OBJECT-TYPE
    SYNTAX INTEGER {
        inputSourceA(0),
        inputSourceB(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The determination as to which one of two AC input lines is in use. Not all
        devices support the reporting of which source is currently in use. If not
        supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsInputEntry 13 }

tlpAtsInputSourceTransitionCount OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of times the device transitions between primary and secondary
        sources. Most devices are limited to 65533 transitions, after which the
        value will not increment. If not supported, this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsInputEntry 14 }

tlpAtsInputCurrentLimit OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The capacity of each phase conductor in the ats limited by the AC input
        cord or any other potential limiting factors unique to a given unit. If not
        supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsInputEntry 15 }

tlpAtsInputPhaseTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsInputPhaseEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input phase details of each ats in device table. This is a sparse
        dependent table, and will contain the number of entries based upon
        the value of tlpAtsIdentNumInputs and tlpAtsIdentNumPhases per each
        device."
    ::= { tlpAtsInput 2 }

tlpAtsInputPhaseEntry OBJECT-TYPE
    SYNTAX TlpAtsInputPhaseEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular primary
        input phase managed by this agent."
    INDEX { tlpDeviceIndex, tlpAtsInputLineIndex, tlpAtsInputPhaseIndex }
    ::= { tlpAtsInputPhaseTable 1 }

TlpAtsInputPhaseEntry ::= SEQUENCE {
    tlpAtsInputLineIndex                    Unsigned32,
    tlpAtsInputPhaseIndex                   Unsigned32,
    tlpAtsInputPhaseType                    INTEGER,
    tlpAtsInputPhaseFrequency               Unsigned32,
    tlpAtsInputPhaseVoltage                 Unsigned32,
    tlpAtsInputPhaseVoltageMin              Unsigned32,
    tlpAtsInputPhaseVoltageMax              Unsigned32,
    tlpAtsInputPhaseCurrent                 Unsigned32 }

tlpAtsInputLineIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the input line on the ats."
    ::= { tlpAtsInputPhaseEntry 1 }

tlpAtsInputPhaseIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the input phase for the input line."
    ::= { tlpAtsInputPhaseEntry 2 }

tlpAtsInputPhaseType OBJECT-TYPE
    SYNTAX INTEGER {
        phaseToNeutral(0),
        phaseToPhase(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes whether measurements are phase to neutral(0) or phase
        to phase(1)."
    ::= { tlpAtsInputPhaseEntry 3 }

tlpAtsInputPhaseFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present input frequency for a given phase."
    ::= { tlpAtsInputPhaseEntry 4 }

tlpAtsInputPhaseVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present input voltage for a given phase."
    ::= { tlpAtsInputPhaseEntry 5 }

tlpAtsInputPhaseVoltageMin OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The minimum input voltage value since the last reset of this
        value for a given phase. If any value is written to either
        tlpAtsInputVoltageMin or tlpAtsInputVoltageMax, both
        will be reset to their appropriate minimum and maximum values."
    ::= { tlpAtsInputPhaseEntry 6 }

tlpAtsInputPhaseVoltageMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The maximum input voltage value since the last reset of this
        value for a given phase. If any value is written to either
        tlpAtsInputVoltageMin or tlpAtsInputVoltageMax, both
        will be reset to their appropriate minimum and maximum values."
    ::= { tlpAtsInputPhaseEntry 7 }

tlpAtsInputPhaseCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present input current for a given phase."
    ::= { tlpAtsInputPhaseEntry 8 }

tlpAtsOutputTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsOutputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The output details of each ats in device table. This is a sparse
        dependent table, and will contain the number of entries based upon
        the value of tlpAtsIdentNumOutputs per each device."
    ::= { tlpAtsOutput 1 }

tlpAtsOutputEntry OBJECT-TYPE
    SYNTAX TlpAtsOutputEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing output information applicable to a particular
        phase managed by this agent."
    INDEX { tlpDeviceIndex, tlpAtsOutputIndex }
    ::= { tlpAtsOutputTable 1 }

TlpAtsOutputEntry ::= SEQUENCE {
    tlpAtsOutputIndex               Unsigned32,
    tlpAtsOutputPhase               INTEGER,
    tlpAtsOutputPhaseType           INTEGER,
    tlpAtsOutputVoltage             Unsigned32,
    tlpAtsOutputCurrent             Unsigned32,
    tlpAtsOutputCurrentMin          Unsigned32,
    tlpAtsOutputCurrentMax          Unsigned32,
    tlpAtsOutputActivePower         Unsigned32,
    tlpAtsOutputPowerFactor         Unsigned32,
    tlpAtsOutputSource              INTEGER,
    tlpAtsOutputFrequency           Unsigned32,
	tlpAtsOutputCalculatedPowerKVA  Unsigned32,
	tlpAtsOutputCalculatedPowerKW   Unsigned32,
	tlpAtsOutputCalculated24hrEnergy Unsigned32 }

tlpAtsOutputIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ats phase."
    ::= { tlpAtsOutputEntry 1 }

tlpAtsOutputPhase OBJECT-TYPE
    SYNTAX INTEGER {
        phase1(1),
        phase2(2),
        phase3(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates the phase associated with the output line."
    ::= { tlpAtsOutputEntry 2 }

tlpAtsOutputPhaseType OBJECT-TYPE
    SYNTAX INTEGER {
        phaseToNeutral(0),
        phaseToPhase(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes whether measurements are phase to neutral(0) or phase
        to phase(1)."
    ::= { tlpAtsOutputEntry 3 }

tlpAtsOutputVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present output voltage for a given output line."
    ::= { tlpAtsOutputEntry 4 }

tlpAtsOutputCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output current, in hundredths of amps, for a given phase.
        The actual precision of this value, whether a whole number, tenths
        or hundredths, is returned by tlpAtsDeviceOutputCurrentPrecision."
    ::= { tlpAtsOutputEntry 5 }

tlpAtsOutputCurrentMin OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum current, in hundredths of amps, observed for a given phase
        since last reset. The actual precision of this value, whether a whole number,
        tenths or hundredths, is returned by tlpAtsDeviceOutputCurrentPrecision."
    ::= { tlpAtsOutputEntry 6 }

tlpAtsOutputCurrentMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum current, in hundredths of amps, observed for a given phase
        since last reset. The actual precision of this value, whether a whole number,
        tenths or hundredths, is returned by tlpAtsDeviceOutputCurrentPrecision."
    ::= { tlpAtsOutputEntry 7 }

tlpAtsOutputActivePower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output power for a given phase."
    ::= { tlpAtsOutputEntry 8 }

tlpAtsOutputPowerFactor OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output power factor, in hundredths of a percent, for a given
        phase."
    ::= { tlpAtsOutputEntry 9 }

tlpAtsOutputSource OBJECT-TYPE
    SYNTAX INTEGER {
        none(0),
        normal(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present source of output power. The enumeration none(0)
        indicates that there is no source of output power (and
        therefore no output power), for example, the system has opened
        the output breaker."
    ::= { tlpAtsOutputEntry 10 }

tlpAtsOutputFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output frequency. If not supported, the agent reports ERROR_NO_SUCH_NAME."
    ::= { tlpAtsOutputEntry 11 }

tlpAtsOutputCalculatedPowerKVA OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 KVA"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The calculated amount of apparent power being used."
    ::= { tlpAtsOutputEntry 12 }

tlpAtsOutputCalculatedPowerKW OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 KW"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The calculated amount of active power being used."
    ::= { tlpAtsOutputEntry 13 }

tlpAtsOutputCalculated24hrEnergy OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 KWH"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Estimated 24 hour power consumption using present value
        of tlpAtsOutputCalculatedPowerKW."
    ::= { tlpAtsOutputEntry 14 }

tlpAtsOutletTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsOutletEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The receptacle status details of each ats in device table. This
        is a sparse dependent table, and will contain the number of entries
        based upon the value of tlpAtsIdentNumOutlets per each device."
    ::= { tlpAtsOutlet 1 }

tlpAtsOutletEntry OBJECT-TYPE
    SYNTAX TlpAtsOutletEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular outlet
        managed by this agent."
    INDEX { tlpDeviceIndex, tlpAtsOutletIndex }
    ::= { tlpAtsOutletTable 1 }

TlpAtsOutletEntry ::= SEQUENCE {
    tlpAtsOutletIndex                   Unsigned32,
    tlpAtsOutletName                    DisplayString,
    tlpAtsOutletDescription             DisplayString,
    tlpAtsOutletState                   INTEGER,
    tlpAtsOutletControllable            TruthValue,
    tlpAtsOutletCommand                 INTEGER,
    tlpAtsOutletVoltage                 Unsigned32,
    tlpAtsOutletCurrent                 Unsigned32,
    tlpAtsOutletPower                   Unsigned32,
    tlpAtsOutletRampAction              INTEGER,
    tlpAtsOutletRampDelay               Integer32,
    tlpAtsOutletShedAction              INTEGER,
    tlpAtsOutletShedDelay               Integer32,
    tlpAtsOutletGroup                   Integer32,
    tlpAtsOutletBank                    Integer32,
    tlpAtsOutletCircuit                 Integer32,
    tlpAtsOutletPhase                   INTEGER }

tlpAtsOutletIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ats outlet."
    ::= { tlpAtsOutletEntry 1 }

tlpAtsOutletName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-definable string identifying the name of the outlet."
    ::= { tlpAtsOutletEntry 2 }

tlpAtsOutletDescription OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A user-definable string identifying the device(s) connected to
        the given outlet."
    ::= { tlpAtsOutletEntry 3 }

tlpAtsOutletState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the outlet."
    ::= { tlpAtsOutletEntry 4 }

tlpAtsOutletControllable OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Denotes whether this outlet is controllable."
    ::= { tlpAtsOutletEntry 5 }

tlpAtsOutletCommand OBJECT-TYPE
    SYNTAX INTEGER {
        idle(0),
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value
        to turnOff(1) will turn off the outlet. Setting this value
        to turnOn(2) will turn on the outlet. Setting this value to
        cycle(3) will turn the outlet off, then turn it back on."
    ::= { tlpAtsOutletEntry 6 }

tlpAtsOutletVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The magnitude of the present output voltage for a given outlet.
        If the voltage measurement is not supported for an individual
        receptacle regardless whether device-wide or just for this
        receptacle, then this value will always return ERROR_NO_SUCH_NAME.
        Device-wide support for receptacle voltage measurements is denoted
        by tlpAtsSupportsOutletVoltage."
    ::= { tlpAtsOutletEntry 7 }

tlpAtsOutletCurrent OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present output current, in hundredths of amps, of a given outlet.
        If the current measurement is not supported for an individual
        receptacle regardless whether device-wide or just for this
        receptacle, then this value will always return ERROR_NO_SUCH_NAME.
        Device-wide support for receptacle current measurements is denoted
        by tlpAtsSupportsCurrentPower."
    ::= { tlpAtsOutletEntry 8 }

tlpAtsOutletPower OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The output power of a given outlet. If the power measurement is
        not supported for an individual receptacle regardless whether
        device-wide or just for this receptacle, then this value will
        always return ERROR_NO_SUCH_NAME. Device-wide support for receptacle
        power measurements is denoted by tlpAtsSupportsCurrentPower."
    ::= { tlpAtsOutletEntry 9 }

tlpAtsOutletRampAction OBJECT-TYPE
    SYNTAX INTEGER {
        remainOff(0),
        turnOnAfterDelay(1)
    }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The ramp action to take on a given outlet when powering on the
        device. If this receptacle is not controllable, as denoted by
        tlpAtsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsOutletEntry 10 }

tlpAtsOutletRampDelay OBJECT-TYPE
    SYNTAX Integer32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of seconds to delay before powering on the given outlet.
        If this receptacle is not controllable, as denoted by
        tlpAtsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsOutletEntry 11 }

tlpAtsOutletShedAction OBJECT-TYPE
    SYNTAX INTEGER {
        remainOn(0),
        turnOffAfterDelay(1)
    }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The shed action to take on a given outlet when powering off the
        device. If this receptacle is not controllable, as denoted by
        tlpAtsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsOutletEntry 12 }

tlpAtsOutletShedDelay OBJECT-TYPE
    SYNTAX Integer32
    UNITS "seconds"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of seconds to delay before powering off the given outlet.
        If this receptacle is not controllable, as denoted by
        tlpAtsOutletControllable, then this value will always return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsOutletEntry 13 }

tlpAtsOutletGroup OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The tlpAtsOutletGroupIndex group index of the respective group to
        which the given outlet is assigned, or zero (0) if the given
        outlet is not grouped."
    ::= { tlpAtsOutletEntry 14 }

tlpAtsOutletBank OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which bank the given outlet is located."
    ::= { tlpAtsOutletEntry 15 }

tlpAtsOutletCircuit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which circuit the given outlet is wired."
    ::= { tlpAtsOutletEntry 16 }

tlpAtsOutletPhase OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        phase1(1),
        phase2(2),
        phase3(3),
        phase1-2(4),
        phase2-3(5),
        phase3-1(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which phase or phases the given outlet is
        associated. For single phase devices, this object will
        always return phase1(1). For 3-phase devices with outlets
        wired to a single phase, this object will return phase1(1),
        phase2(2) or phase3(3). For 3-phase devices with outlets
        wired to two phases, this object will return phase1-2(4)
        for phases 1 and 2, phase2-3(5) for phases 2 and 3, and
        phase3-1(6) for phases 3 and 1. Devices that do not report
        phase connections will return unknown(0)."
    ::= { tlpAtsOutletEntry 17 }

tlpAtsOutletGroupTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsOutletGroupEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The receptacle grouping details of each ats in device table.
        This is a sparse dependent table, and will contain the number
        of entries based upon the value of tlpAtsIdentNumOutletGroups per
        each device."
    ::= { tlpAtsOutlet 2 }

tlpAtsOutletGroupEntry OBJECT-TYPE
    SYNTAX TlpAtsOutletGroupEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular outlet
        group managed by this agent."
    INDEX { tlpDeviceIndex, tlpAtsOutletGroupIndex }
    ::= { tlpAtsOutletGroupTable 1 }

TlpAtsOutletGroupEntry ::= SEQUENCE {
    tlpAtsOutletGroupIndex              Unsigned32,
    tlpAtsOutletGroupRowStatus	        RowStatus,
    tlpAtsOutletGroupName               DisplayString,
    tlpAtsOutletGroupDescription        DisplayString,
    tlpAtsOutletGroupState              INTEGER,
    tlpAtsOutletGroupCommand            INTEGER }

tlpAtsOutletGroupIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ats outlet group."
    ::= { tlpAtsOutletGroupEntry 1 }

tlpAtsOutletGroupRowStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Row status for the tlpAtsOutletGroupTable"
    ::= { tlpAtsOutletGroupEntry 2 }

tlpAtsOutletGroupName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of this outlet group."
    ::= { tlpAtsOutletGroupEntry 3 }

tlpAtsOutletGroupDescription OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A description for this outlet group."
    ::= { tlpAtsOutletGroupEntry 4 }

tlpAtsOutletGroupState OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        off(1),
        on(2),
        mixed(3) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of the outlet group. This value will report
        off(1) if all outlets in the group are off, on(2) if all outlets
        in the group are on, mixed(3) if some outlets in the group are
        off and others are on, or unknown(0) if the state of the outlets
        cannot be determined."
    ::= { tlpAtsOutletGroupEntry 5 }

tlpAtsOutletGroupCommand OBJECT-TYPE
    SYNTAX INTEGER {
        turnOff(1),
        turnOn(2),
        cycle(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Reading this value will always return 0. Setting this value to
        turnOff(1) will turn off every outlet associated with the group.
        Setting this value to turnOn(2) will turn on every outlet associated
        with the group. Setting this value to cycle(3) will turn off every
        outlet associated with the group, then turn each outlet back on."
    ::= { tlpAtsOutletGroupEntry 6 }

tlpAtsCircuitTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsCircuitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of ats circuit table entries.  The number of entries
        is given by the value of tlpAtsIdentNumCircuits."
    ::= { tlpAtsCircuit 1 }

tlpAtsCircuitEntry OBJECT-TYPE
    SYNTAX TlpAtsCircuitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular circuit
        managed by this agent."
    INDEX { tlpDeviceIndex, tlpAtsCircuitIndex }
    ::= { tlpAtsCircuitTable 1 }

TlpAtsCircuitEntry ::= SEQUENCE {
    tlpAtsCircuitIndex              Unsigned32,
    tlpAtsCircuitPhase              INTEGER,
    tlpAtsCircuitInputVoltage       Integer32,
    tlpAtsCircuitTotalCurrent       Integer32,
    tlpAtsCircuitCurrentLimit       Integer32,
    tlpAtsCircuitCurrentMin         Integer32,
    tlpAtsCircuitCurrentMax         Integer32,
    tlpAtsCircuitTotalPower         Integer32,
    tlpAtsCircuitPowerFactor        INTEGER,
    tlpAtsCircuitUtilization        Unsigned32 }

tlpAtsCircuitIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ats circuit."
    ::= { tlpAtsCircuitEntry 1 }

tlpAtsCircuitPhase OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        phase1(1),
        phase2(2),
        phase3(3),
        phase1-2(4),
        phase2-3(5),
        phase3-1(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates on which phase or phases the given circuit is
        wired. For single phase devices, this object will always
        return phase1(1). For 3-phase devices with outlets wired
        to a single phase, this object will return phase1(1),
        phase2(2) or phase3(3). For 3-phase devices with outlets
        wired to two phases, this object will return phase1-2(4)
        for phases 1 and 2, phase2-3(5) for phases 2 and 3, and
        phase3-1(6) for phases 3 and 1. Devices that do not report
        phase connections will return unknown(0)."
    ::= { tlpAtsCircuitEntry 2 }

tlpAtsCircuitInputVoltage OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 Volts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The input voltage for a circuit."
    ::= { tlpAtsCircuitEntry 3 }

tlpAtsCircuitTotalCurrent OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total current for all receptacles in a given circuit."
    ::= { tlpAtsCircuitEntry 4 }

tlpAtsCircuitCurrentLimit OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current capacity of a circuit in the system limited by
        the circuit breaker or wiring used in a given unit."
    ::= { tlpAtsCircuitEntry 5 }

tlpAtsCircuitCurrentMin OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum total current for all receptacles in a given
        circuit since last reset."
    ::= { tlpAtsCircuitEntry 6 }

tlpAtsCircuitCurrentMax OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.01 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum total current for all receptacles in a given
        circuit since last reset."
    ::= { tlpAtsCircuitEntry 7 }

tlpAtsCircuitTotalPower OBJECT-TYPE
    SYNTAX Integer32
    UNITS "Watts"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total power for all receptacles in a given circuit."
    ::= { tlpAtsCircuitEntry 8 }

tlpAtsCircuitPowerFactor OBJECT-TYPE
    SYNTAX INTEGER (0..200)
    UNITS "percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The power factor for all receptacles in a given circuit."
    ::= { tlpAtsCircuitEntry 9 }

tlpAtsCircuitUtilization OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.01 percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The percentage of utilization of a given circuit. This is typically
        a calculation of tlpAtsCircuitTotalCurrent / tlpAtsCircuitCurrentLimit."
    ::= { tlpAtsCircuitEntry 10 }

tlpAtsBreakerTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsBreakerEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of ats bank breaker table entries. The number of entries
        is given by the value of tlpAtsIdentNumBreakers."
    ::= { tlpAtsBreaker 1 }

tlpAtsBreakerEntry OBJECT-TYPE
    SYNTAX TlpAtsBreakerEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular bank
        breaker managed by this agent."
    INDEX { tlpDeviceIndex, tlpAtsBreakerIndex }
    ::= { tlpAtsBreakerTable 1 }

TlpAtsBreakerEntry ::= SEQUENCE {
    tlpAtsBreakerIndex          Unsigned32,
    tlpAtsBreakerStatus         INTEGER }

tlpAtsBreakerIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ats bank breaker."
    ::= { tlpAtsBreakerEntry 1 }

tlpAtsBreakerStatus OBJECT-TYPE
    SYNTAX INTEGER {
        open(0),
        closed(1),
        notInstalled(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the bank breaker is open(0), closed(1) or not
        installed(2)."
    ::= { tlpAtsBreakerEntry 2 }

tlpAtsHeatsinkTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsHeatsinkEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of ats heatsink thermistor table entries. The number of entries
        is given by the value of tlpAtsIdentNumHeatsinks."
    ::= { tlpAtsHeatsink 1 }

tlpAtsHeatsinkEntry OBJECT-TYPE
    SYNTAX TlpAtsHeatsinkEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information applicable to a particular heatsink
        thermistor managed by this agent."
    INDEX { tlpDeviceIndex, tlpAtsHeatsinkIndex }
    ::= { tlpAtsHeatsinkTable 1 }

TlpAtsHeatsinkEntry ::= SEQUENCE {
    tlpAtsHeatsinkIndex             Unsigned32,
    tlpAtsHeatsinkStatus            INTEGER,
    tlpAtsHeatsinkTemperatureC      Integer32,
    tlpAtsHeatsinkTemperatureF      Integer32 }

tlpAtsHeatsinkIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the ats heatsink thermistor."
    ::= { tlpAtsHeatsinkEntry 1 }

tlpAtsHeatsinkStatus OBJECT-TYPE
    SYNTAX INTEGER {
        notAvailable(0),
        available(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the heatsink thermistor is available. The entry
        will return notAvailable(0) if a thermistor is either non-existent or
        disconnected, or available(1) when connected."
    ::= { tlpAtsHeatsinkEntry 2 }

tlpAtsHeatsinkTemperatureC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature at the heatsink thermistor in tenths degrees Celsius.
        Heatsink temperature measurements are not supported on all models. If
        not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsHeatsinkEntry 3 }

tlpAtsHeatsinkTemperatureF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature at the heatsink thermistor in tenths degrees Fahrenheit.
        Heatsink temperature measurements are not supported on all models. If
        not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsHeatsinkEntry 4 }

tlpAtsControlTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The actionable controls for each ats in device table. This is a
        sparse dependent table, and will contain some subset of devices
        that are atss."
    ::= { tlpAtsControl 1 }

tlpAtsControlEntry OBJECT-TYPE
    SYNTAX TlpAtsControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing controls for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsControlTable 1 }

TlpAtsControlEntry ::= SEQUENCE {
    tlpAtsControlRamp               TruthValue,
    tlpAtsControlShed               TruthValue,
    tlpAtsControlAtsOn              TruthValue,
    tlpAtsControlAtsOff             TruthValue,
    tlpAtsControlAtsReboot          TruthValue,
    tlpAtsControlResetGeneralFault  TruthValue }

tlpAtsControlRamp OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to initiate the pre-designated ramp
        sequence on the ats."
    ::= { tlpAtsControlEntry 1 }

tlpAtsControlShed OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to initiate the pre-designated shed
        sequence on the ats."
    ::= { tlpAtsControlEntry 2 }

tlpAtsControlAtsOn OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to turn the ats on."
    ::= { tlpAtsControlEntry 3 }

tlpAtsControlAtsOff OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to turn the ats off."
    ::= { tlpAtsControlEntry 4 }

tlpAtsControlAtsReboot OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to reboot the ats."
    ::= { tlpAtsControlEntry 5 }

tlpAtsControlResetGeneralFault OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Set to TRUE to reset the general fault alarm indicated by
        tlpAtsDeviceGeneralFault."
    ::= { tlpAtsControlEntry 6 }

tlpAtsConfigTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration options of each ats in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are atss. Not all options are available on all
        atss."
    ::= { tlpAtsConfig 1 }

tlpAtsConfigEntry OBJECT-TYPE
    SYNTAX TlpAtsConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing configuration options for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsConfigTable 1 }

TlpAtsConfigEntry ::= SEQUENCE {
    tlpAtsConfigInputVoltage                Unsigned32,
    tlpAtsConfigSourceSelect                INTEGER,
    tlpAtsConfigSource1ReturnTime           Unsigned32,
    tlpAtsConfigSource2ReturnTime           Unsigned32,
    tlpAtsConfigAutoRampOnTransition        INTEGER,
    tlpAtsConfigAutoShedOnTransition        INTEGER }

tlpAtsConfigInputVoltage OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The nominal value of the input line voltage. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME. This value may not be writable on all models. If
        not writable, the value will be rejected and an appropriate error
        message will be returned."
    ::= { tlpAtsConfigEntry 1 }

tlpAtsConfigSourceSelect OBJECT-TYPE
    SYNTAX INTEGER {
        inputSourceA(1),
        inputSourceB(2) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The determination as to which of two AC input lines is designated
        as the primary input source. The other is designated as the
        secondary input source. This value will always return inputSourceA(1)
        for devices with a single input source. Setting this value is
        supported only on selected models."
    ::= { tlpAtsConfigEntry 2 }

tlpAtsConfigSource1ReturnTime OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The nominal time to delay a transition to Source 1, if it is the
        preferred source."
    ::= { tlpAtsConfigEntry 3 }

tlpAtsConfigSource2ReturnTime OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The nominal time to delay a transition to Source 2, if it is the
        preferred source."
    ::= { tlpAtsConfigEntry 4 }

tlpAtsConfigAutoRampOnTransition OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled(1), the ats should perform its preconfigured ramp sequence when
        the input source transfers from its designated secondary source to its
        designated primary source. This value not supported on all ats devices. If not
        supported, then this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigEntry 5 }

tlpAtsConfigAutoShedOnTransition OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled(1), the ats should perform its preconfigured shed sequence when
        the input source transfers from its designated primary source to its designated
        secondary source. This value not supported on all ats devices. If not supported,
        then this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigEntry 6 }

tlpAtsConfigVoltageRangeOldTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsConfigVoltageRangeOldEntry
    MAX-ACCESS not-accessible
    STATUS obsolete
    DESCRIPTION
        "The voltage range options in this table is intended to configure the
        operating voltages of an ats. This is a sparse dependent table, and will
        contain some subset of devices that are atss. All voltage points defined
        in this table are listed in a specific order from high to low. All voltage
        points must satisfy the conditions and ranges defined for each point. Any
        point violating any of the relative limits will be rejected.

        For optimal operation, it is recommended that tlpAtsConfigHighVoltageReset
        be set lower than tlpAtsConfigHighVoltageTransfer by a margin no less than 1%
        of tlpAtsConfigHighVoltageTransfer; and that tlpAtsConfigSource1TransferReset
        (tlpAtsConfigSource2TransferReset) be set higher than
        tlpAtsConfigSource1BrownoutSet (tlpAtsConfigSource2BrownoutSet) by a margin no
        less than 1% of tlpAtsConfigSource1BrownoutSet (tlpAtsConfigSource2BrownoutSet).

        *The upper limit of line voltage is tlpAtsInputHighTransferVoltageUpperBound.
        The lower limit of line voltage is tlpAtsInputBadTransferVoltageLowerBound. All
        minimum and maximum limits for each voltage point are all exclusive unless
        indicated otherwise.

        This table in its entirety is obsolete and replaced by tlpAtsConfigVoltageRangeTable."
    ::= { tlpAtsConfig 2 }

tlpAtsConfigVoltageRangeOldEntry OBJECT-TYPE
    SYNTAX TlpAtsConfigVoltageRangeOldEntry
    MAX-ACCESS not-accessible
    STATUS obsolete
    DESCRIPTION
        "An entry containing voltage range configuration options for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsConfigVoltageRangeOldTable 1 }

TlpAtsConfigVoltageRangeOldEntry ::= SEQUENCE {
    tlpAtsConfigHighVoltageTransferOld     Unsigned32,
    tlpAtsConfigHighVoltageResetOld        Unsigned32,
    tlpAtsConfigSource1TransferResetOld    Unsigned32,
    tlpAtsConfigSource1BrownoutSetOld      Unsigned32,
    tlpAtsConfigSource1TransferSetOld      Unsigned32,
    tlpAtsConfigSource2TransferResetOld    Unsigned32,
    tlpAtsConfigSource2BrownoutSetOld      Unsigned32,
    tlpAtsConfigSource2TransferSetOld      Unsigned32,
    tlpAtsConfigLowVoltageResetOld         Unsigned32,
    tlpAtsConfigLowVoltageTransferOld      Unsigned32 }

tlpAtsConfigHighVoltageTransferOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "The maximum line voltage allowed before the unit disconnects the
        AC input connection. The maximum value allowed is dictated by
        tlpAtsInputHighTransferVoltageUpperBound (inclusively). The minimum
        value allowed is dictated by tlpAtsConfigHighVoltageResetOld. This value
        may not be configurable on all models. If not supported, this value will
        return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 1 }

tlpAtsConfigHighVoltageResetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "If a unit is disconnected due to over-voltage, the line voltage has to
        fall below this value before the unit re-connects the AC input connection.
        The maximum value allowed is dictated by tlpAtsConfigHighVoltageTransferOld.
        The minimum value allowed is dictated by the greater of
        tlpAtsConfigSource1TransferResetOld and tlpAtsConfigSource2TransferResetOld. If
        not supported, this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 2 }

tlpAtsConfigSource1TransferResetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "If input source 1 voltage level is above this value, a unit connected to
        source 2 will transition to source 1 (if it is the preferred source). The
        maximum value allowed is dictated by tlpAtsConfigHighVoltageResetOld. The
        minimum value allowed is dictated by tlpAtsConfigSource1BrownoutSetOld. If not
        supported, this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 3 }

tlpAtsConfigSource1BrownoutSetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "This value dictates the voltage level below which source 1 goes into a
        brown out condition. The maximum value allowed is dictated by
        tlpAtsConfigSource1TransferResetOld. The minimum value allowed is dictated by
        tlpAtsConfigSource1TransferSetOld. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 4 }

tlpAtsConfigSource1TransferSetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "If input source 1 voltage level falls below this value, a unit connected to
        source 1 will transition to source 2 (if it is in fair condition as denoted
        by tlpAtsConfigSource2TransferSetOld). The maximum value allowed is dictated by
        tlpAtsConfigSource1BrownoutSetOld. The minimum value allowed is dictated by
        tlpAtsConfigLowVoltageResetOld. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 5 }

tlpAtsConfigSource2TransferResetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "If input source 2 voltage level is above this value, a unit connected to
        source 1 will transition to source 2 (if it is the preferred source). The
        maximum value allowed is dictated by tlpAtsConfigHighVoltageResetOld. The
        minimum value allowed is dictated by tlpAtsConfigSource2BrownoutSetOld. If not
        supported, this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 6 }

tlpAtsConfigSource2BrownoutSetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "This value dictates the voltage level below which source 2 goes into a
        brown out condition. The maximum value allowed is dictated by
        tlpAtsConfigSource2TransferResetOld. The minimum value allowed is dictated by
        tlpAtsConfigSource2TransferSetOld. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 7 }

tlpAtsConfigSource2TransferSetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "If input source 2 voltage level falls below this value, a unit connected to
        source 2 will transition to source 1 (if it is in fair condition as denoted
        by tlpAtsConfigSource1TransferSetOld). The maximum value allowed is dictated by
        tlpAtsConfigSource2BrownoutSetOld. The minimum value allowed is dictated by
        tlpAtsConfigLowVoltageResetOld. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 8 }

tlpAtsConfigLowVoltageResetOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "If a unit is disconnected due to under-voltage or is not powered up yet, the
        line voltage has to rise above this value before the unit gets connected to
        the AC input connection. The maximum value allowed is dictated by the lesser
        value of tlpAtsConfigSource1TransferSetOld and tlpAtsConfigSource2TransferSetOld.
        The minimum value allowed is dictated by tlpAtsConfigLowVoltageTransferOld. If
        not supported, this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 9 }

tlpAtsConfigLowVoltageTransferOld OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "The minimum line voltage allowed before the unit disconnects the
        AC input connection. The maximum value allowed is dictated by
        tlpAtsConfigLowVoltageResetOld. The minimum value allowed is dictated by
        tlpAtsInputBadTransferVoltageLowerBound (inclusively). If not supported, this
        value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeOldEntry 10 }

tlpAtsConfigVoltageRangeLimitsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsConfigVoltageRangeLimitsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The voltage range table, as documented by tlpAtsConfigVoltageRangeTable requires
        that all changes adhere with stack order, from high transfer voltage down to low
        transfer voltage. In addition to stack order, certain voltage range values have
        limits defining the absolute minimum and absolute maximum for such values. Any
        voltage value violating any respective limit defined herein will be rejected."
    ::= { tlpAtsConfig 3 }

tlpAtsConfigVoltageRangeLimitsEntry OBJECT-TYPE
    SYNTAX TlpAtsConfigVoltageRangeLimitsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing voltage range limits configuration options for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsConfigVoltageRangeLimitsTable 1 }

TlpAtsConfigVoltageRangeLimitsEntry ::= SEQUENCE {
    tlpAtsConfigSourceBrownoutSetMinimum    Unsigned32,
    tlpAtsConfigSourceBrownoutSetMaximum    Unsigned32,
    tlpAtsConfigSourceTransferSetMinimum    Unsigned32,
    tlpAtsConfigSourceTransferSetMaximum    Unsigned32 }

tlpAtsConfigSourceBrownoutSetMinimum OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This value dictates the absolute lower limit by which tlpAtsConfigSource1BrownoutSet
        and tlpAtsConfigSource2BrownoutSet can be set. tlpAtsConfigSource1TransferSet and
        tlpAtsConfigSource2TransferSet, respectively, are also used when determining the
        lowest value a brownout value can be set at any given time. If not supported, this
        value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeLimitsEntry 1 }

tlpAtsConfigSourceBrownoutSetMaximum OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This value dictates the absolute upper limit by which tlpAtsConfigSource1BrownoutSet
        and tlpAtsConfigSource2BrownoutSet can be set. tlpAtsConfigSource1TransferReset and
        tlpAtsConfigSource2TransferReset, respectively, are also used when determining the
        greatest value a brownout value can be set at any given time. If not supported, this
        value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeLimitsEntry 2 }

tlpAtsConfigSourceTransferSetMinimum OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This value dictates the absolute lower limit by which tlpAtsConfigSource1TransferSet
        and tlpAtsConfigSource2TransferSet can be set. tlpAtsConfigLowVoltageReset is also
        used when determining the lowest value a transferset value can be set at any given
        time. If not supported, this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeLimitsEntry 3 }

tlpAtsConfigSourceTransferSetMaximum OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This value dictates the absolute upper limit by which tlpAtsConfigSource1TransferSet
        and tlpAtsConfigSource2TransferSet can be set. tlpAtsConfigSource1BrownoutSet and
        tlpAtsConfigSource2BrownoutSet, respectively, are also used when determining the
        greatest value a transferset value can be set at any given time. If not supported,
        this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeLimitsEntry 4 }

tlpAtsConfigThresholdTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsConfigThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configurable threshold options of each ats in device table. This
        is a sparse dependent table, and will contain some subset of devices that
        are atss. Not all options are available on all atss."
    ::= { tlpAtsConfig 4 }

tlpAtsConfigThresholdEntry OBJECT-TYPE
    SYNTAX TlpAtsConfigThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing configurable threshold options for a particular ats."
    INDEX { tlpDeviceIndex }
    ::= { tlpAtsConfigThresholdTable 1 }

TlpAtsConfigThresholdEntry ::= SEQUENCE {
    tlpAtsConfigOutputCurrentThreshold          Unsigned32,
    tlpAtsConfigOverTemperatureThreshold        Unsigned32,
    tlpAtsConfigOverVoltageThreshold            Unsigned32,
    tlpAtsConfigOverLoadThreshold               Unsigned32 }

tlpAtsConfigOutputCurrentThreshold OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Amps"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The output current measurement at which an over current condition is reported.
        This value may not be supported on all models. If not supported, this value
        will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigThresholdEntry 1 }

tlpAtsConfigOverTemperatureThreshold OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The system temperature at which an over temperature condition is reported.
        This value may not be supported on all models. If not supported, this value
        will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigThresholdEntry 2 }

tlpAtsConfigOverVoltageThreshold OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The input voltage measurement at which an over voltage condition is reported.
        This value may not be supported on all models. If not supported, this value
        will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigThresholdEntry 3 }

tlpAtsConfigOverLoadThreshold OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The system load at which an overload condition is reported. This value may not
        be supported on all models. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigThresholdEntry 4 }

tlpAtsConfigVoltageRangeTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsConfigVoltageRangeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The voltage range options in this table is intended to configure the
        operating voltages of an ats. This is a sparse dependent table, and will
        contain some subset of devices that are atss. All voltage points defined
        in this table are listed in a specific order from high to low. All voltage
        points must satisfy the conditions and ranges defined for each point. Any
        point violating any of the relative limits will be rejected.

        For optimal operation, it is recommended that tlpAtsConfigHighVoltageReset
        be set lower than tlpAtsConfigHighVoltageTransfer by a margin no less than 1%
        of tlpAtsConfigHighVoltageTransfer; and that tlpAtsConfigSource1TransferReset
        (tlpAtsConfigSource2TransferReset) be set higher than
        tlpAtsConfigSource1BrownoutSet (tlpAtsConfigSource2BrownoutSet) by a margin no
        less than 1% of tlpAtsConfigSource1BrownoutSet (tlpAtsConfigSource2BrownoutSet).

        *The upper limit of line voltage is tlpAtsInputHighTransferVoltageUpperBound.
        The lower limit of line voltage is tlpAtsInputBadTransferVoltageLowerBound. All
        minimum and maximum limits for each voltage point are all exclusive unless
        indicated otherwise."
    ::= { tlpAtsConfig 5 }

tlpAtsConfigVoltageRangeEntry OBJECT-TYPE
    SYNTAX TlpAtsConfigVoltageRangeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing voltage range configuration options for a particular ats."
    INDEX { tlpDeviceIndex, tlpAtsInputLineIndex }
    ::= { tlpAtsConfigVoltageRangeTable 1 }

TlpAtsConfigVoltageRangeEntry ::= SEQUENCE {
    tlpAtsConfigHighVoltageTransfer      Unsigned32,
    tlpAtsConfigHighVoltageReset         Unsigned32,
    tlpAtsConfigTransferReset            Unsigned32,
    tlpAtsConfigBrownoutSet              Unsigned32,
    tlpAtsConfigTransferSet              Unsigned32,
    tlpAtsConfigLowVoltageReset          Unsigned32,
    tlpAtsConfigLowVoltageTransfer       Unsigned32 }

tlpAtsConfigHighVoltageTransfer OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The maximum line voltage allowed on the input source before the unit
        disconnects the AC input connection. tlpAtsConfigHighVoltageTransfer is the
        same value for both input sources on some units. On such units, changing
        this value on one source will change the value on the other source. The
        maximum value allowed is dictated by tlpAtsInputHighTransferVoltageUpperBound
        (inclusively). The minimum value allowed is dictated by tlpAtsConfigHighVoltageReset.
        This value may not be configurable on all models. If not supported, this value will
        return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeEntry 1 }

tlpAtsConfigHighVoltageReset OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If an input connection is disconnected due to over-voltage, the line voltage
        on the input source has to fall below this value before the unit re-connects
        the AC input connection for the input source. tlpAtsConfigHighVoltageReset is the
        same value for both input sources on some units. On such units, changing this
        value on one source will change the value on the other source. The maximum value
        allowed is dictated by tlpAtsConfigHighVoltageTransfer. On units where this is
        the same value for both sources, the minimum value allowed is dictated by the
        greater of tlpAtsConfigTransferReset.1 and tlpAtsConfigTransferReset.2. On units
        where the value is distinct for each source, the minimum value allowed is
        dictated solely by this source's tlpAtsConfigTransferReset. If not supported,
        this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeEntry 2 }

tlpAtsConfigTransferReset OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If input source voltage level is above this value, a unit connected to
        this other input source will transition to this input source (if it is the
        preferred source). The maximum value allowed is dictated by
        tlpAtsConfigHighVoltageReset. The minimum value allowed is dictated by
        tlpAtsConfigBrownoutSet. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeEntry 3 }

tlpAtsConfigBrownoutSet OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This value dictates the voltage level below which the input source goes
        into a brown out condition. The maximum value allowed is dictated by
        tlpAtsConfigTransferReset. The minimum value allowed is dictated by
        tlpAtsConfigTransferSet. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeEntry 4 }

tlpAtsConfigTransferSet OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If input source voltage level falls below this value, a unit connected to
        this input source will transition to other input source (if it is in fair
        condition as denoted by tlpAtsConfigTransferSet). The maximum value allowed
        is dictated by tlpAtsConfigBrownoutSet. The minimum value allowed is dictated
        by tlpAtsConfigLowVoltageReset. If not supported, this value will return
        ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeEntry 5 }

tlpAtsConfigLowVoltageReset OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "If a unit is disconnected due to under-voltage or is not powered up yet, the
        line voltage has to rise above this value before the unit gets connected to
        the AC input connection. tlpAtsConfigLowVoltageReset is the same value for both
        input sources. As such, changing this value on one source will change the value
        on the other source. The maximum value allowed is dictated by the lesser value
        of tlpAtsConfigTransferSet.1 and tlpAtsConfigTransferSet.2. The minimum value
        allowed is dictated by tlpAtsConfigLowVoltageTransfer. If not supported, this
        value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeEntry 6 }

tlpAtsConfigLowVoltageTransfer OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "0.1 Volts"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The minimum line voltage allowed before the unit disconnects the AC input
        connection. tlpAtsConfigLowVoltageTransfer is the same value for both input
        sources. As such, changing this value on one source will change the value on the
        other source. The maximum value allowed is dictated by tlpAtsConfigLowVoltageReset.
        The minimum value allowed is dictated by tlpAtsInputBadTransferVoltageLowerBound
        (inclusively). If not supported, this value will return ERROR_NO_SUCH_NAME."
    ::= { tlpAtsConfigVoltageRangeEntry 7 }

tlpAtsConfigPhaseThresholdTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAtsConfigPhaseThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration options of each Ats in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are Atss. Not all options are available on all
        Atss."
    ::= { tlpAtsConfig 6 }

tlpAtsConfigPhaseThresholdEntry OBJECT-TYPE
    SYNTAX TlpAtsConfigPhaseThresholdEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing threshold configuration options for a particular Ats."
    INDEX { tlpDeviceIndex, tlpAtsPhaseIndex }
    ::= { tlpAtsConfigPhaseThresholdTable 1 }

TlpAtsConfigPhaseThresholdEntry ::= SEQUENCE {
    tlpAtsPhaseIndex                             Unsigned32,
    tlpAtsConfigOutputCurrentThresholdTolerance  Unsigned32,
	tlpAtsConfigOutputCurrentHighThreshold       Unsigned32,
	tlpAtsConfigOutputCurrentLowThreshold        Unsigned32}

tlpAtsPhaseIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the Ats phase."
    ::= { tlpAtsConfigPhaseThresholdEntry 1 }

tlpAtsConfigOutputCurrentThresholdTolerance OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The threshold tolerance value used together with output current high/low
		threshold to evaluate output current high/low condition."
    ::= { tlpAtsConfigPhaseThresholdEntry 2 }

tlpAtsConfigOutputCurrentHighThreshold OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The value at which a output current high condition is declared."
    ::= { tlpAtsConfigPhaseThresholdEntry 3 }

tlpAtsConfigOutputCurrentLowThreshold OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The value at which a output current low condition is declared."
    ::= { tlpAtsConfigPhaseThresholdEntry 4 }

-- cooling

tlpCoolingIdentNumCooling OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of cooling devices that this agent is currently managing."
    ::= { tlpCoolingIdent 1 }

tlpCoolingCurrentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingCurrentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The input current for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by tlpCoolingIdentNumCooling."
    ::= { tlpCoolingDetail 3 }

tlpCoolingCurrentEntry OBJECT-TYPE
    SYNTAX TlpCoolingCurrentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing input current for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingCurrentTable 1 }

TlpCoolingCurrentEntry ::= SEQUENCE {
    tlpCoolingUnitCurrent            Unsigned32,
    tlpCoolingCompressorCurrent      Unsigned32,
    tlpCoolingEvaporatorFanCurrent   Unsigned32,
    tlpCoolingCondenserFanCurrent    Unsigned32 }

tlpCoolingUnitCurrent OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The total current draw of the unit in tenths of Amps. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingCurrentEntry 1 }

tlpCoolingCompressorCurrent  OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The compressor current draw of the unit in tenths of Amps. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingCurrentEntry 2 }

tlpCoolingEvaporatorFanCurrent OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current draw of the evaporator fan in tenths of Amps. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingCurrentEntry 3 }

tlpCoolingCondenserFanCurrent OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Amps"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current draw of the condenser fan in tenths of Amps. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingCurrentEntry 4 }

tlpCoolingTemperatureFahrenheitTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingTemperatureFahrenheitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The temperature details for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by tlpCoolingIdentNumCooling."
    ::= {  tlpCoolingTemperature 1 }

tlpCoolingTemperatureFahrenheitEntry OBJECT-TYPE
    SYNTAX TlpCoolingTemperatureFahrenheitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing temperature details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingTemperatureFahrenheitTable 1 }

TlpCoolingTemperatureFahrenheitEntry ::= SEQUENCE {
    tlpCoolingAmbientDegF            Integer32,
	tlpCoolingSupplyAirDegF          Integer32,
	tlpCoolingReturnAirDegF          Integer32,
	tlpCoolingCondenserInletDegF     Integer32,
	tlpCoolingEvaporatorDegF         Integer32,
	tlpCoolingSuctionDegF            Integer32,
	tlpCoolingRemoteDegF             Integer32,
	tlpCoolingCondenserOutletDegF    Integer32}

tlpCoolingAmbientDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature in tenths degrees Fahrenheit. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 1 }

tlpCoolingSupplyAirDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air entering the cooling unit in tenths degrees Fahrenheit.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 2 }

tlpCoolingReturnAirDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air entering the cooling unit in tenths degrees Fahrenheit.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 3 }

tlpCoolingCondenserInletDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air entering the condenser in tenths degrees Fahrenheit.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 4 }

tlpCoolingEvaporatorDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the evaporator surface in tenths degrees Fahrenheit.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 5 }

tlpCoolingSuctionDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the low pressure refrigerant line in tenths degrees Fahrenheit.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 6 }

tlpCoolingRemoteDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The external remote temperature in tenths degrees Fahrenheit. If not supported,
         this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 7 }

tlpCoolingCondenserOutletDegF OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air leaving the condenser in tenths degrees Fahrenheit.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureFahrenheitEntry 8 }

tlpCoolingTemperatureCelsiusTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingTemperatureCelsiusEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The temperature details for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by tlpCoolingIdentNumCooling."
    ::= { tlpCoolingTemperature 2 }

tlpCoolingTemperatureCelsiusEntry OBJECT-TYPE
    SYNTAX TlpCoolingTemperatureCelsiusEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing temperature details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingTemperatureCelsiusTable 1 }

TlpCoolingTemperatureCelsiusEntry ::= SEQUENCE {
    tlpCoolingAmbientDegC            Integer32,
	tlpCoolingSupplyAirDegC          Integer32,
	tlpCoolingReturnAirDegC          Integer32,
	tlpCoolingCondenserInletDegC     Integer32,
	tlpCoolingEvaporatorDegC         Integer32,
	tlpCoolingSuctionDegC            Integer32,
	tlpCoolingRemoteDegC             Integer32,
    tlpCoolingCondenserOutletDegC    Integer32 }

tlpCoolingAmbientDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ambient temperature in tenths degrees Celsius. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 1 }

tlpCoolingSupplyAirDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air entering the cooling unit in tenths degrees Celsius. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 2 }

tlpCoolingReturnAirDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air entering the cooling unit in tenths degrees Celsius. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 3 }

tlpCoolingCondenserInletDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air entering the condenser in tenths degrees Celsius. If not supported,
         this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 4 }

tlpCoolingEvaporatorDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the evaporator surface in tenths degrees Celsius. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 5 }

tlpCoolingSuctionDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the low pressure refrigerant line in tenths degrees Celsius. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 6 }

tlpCoolingRemoteDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The external remote temperature in tenths degrees Celsius. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 7 }

tlpCoolingCondenserOutletDegC OBJECT-TYPE
    SYNTAX Integer32
	UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The temperature of the air leaving the condenser in tenths degrees Celsius. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingTemperatureCelsiusEntry 8 }

tlpCoolingPressureMpaTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingPressureMpaEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The pressure detail for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by  tlpCoolingIdentNumCooling."
    ::= {  tlpCoolingPressure 1 }

tlpCoolingPressureMpaEntry OBJECT-TYPE
    SYNTAX TlpCoolingPressureMpaEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing pressure details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingPressureMpaTable 1 }

TlpCoolingPressureMpaEntry ::= SEQUENCE {
    tlpCoolingSuctionPressureMpa          Unsigned32,
	tlpCoolingDischargePressureMpa        Unsigned32 }

tlpCoolingSuctionPressureMpa OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Mpa"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The pressure of the compressor discharge line in tenths of Mpa. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingPressureMpaEntry 1 }

tlpCoolingDischargePressureMpa     OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Mpa"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The pressure of the compressor discharge line in tenths of Mpa. If not supported,
         this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingPressureMpaEntry 2 }

tlpCoolingPressurePsiTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingPressurePsiEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The pressure detail for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by  tlpCoolingIdentNumCooling."
    ::= {  tlpCoolingPressure 2 }

tlpCoolingPressurePsiEntry OBJECT-TYPE
    SYNTAX TlpCoolingPressurePsiEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing pressure details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingPressurePsiTable 1 }

TlpCoolingPressurePsiEntry ::= SEQUENCE {
    tlpCoolingSuctionPressurePsi          Unsigned32,
	tlpCoolingDischargePressurePsi        Unsigned32 }

tlpCoolingSuctionPressurePsi OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Psi"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The pressure of the compressor discharge line in tenths of Psi. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingPressurePsiEntry 1 }

tlpCoolingDischargePressurePsi OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 Psi"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The pressure of the compressor discharge line in tenths of Psi. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingPressurePsiEntry 2 }

tlpCoolingDynamicTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingDynamicEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The dynamic detail for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by  tlpCoolingIdentNumCooling."
    ::= {  tlpCoolingDetail 4 }

tlpCoolingDynamicEntry OBJECT-TYPE
    SYNTAX TlpCoolingDynamicEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry dynamic details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingDynamicTable 1 }

TlpCoolingDynamicEntry ::= SEQUENCE {
    tlpCoolingEvaporatorFanSpeed    INTEGER,
    tlpCoolingCondenserFanSpeed     INTEGER,
	tlpCoolingCompressorFrequency   Unsigned32,
	tlpCoolingEEVSteps              INTEGER }

tlpCoolingEvaporatorFanSpeed OBJECT-TYPE
    SYNTAX INTEGER {
	    off(0),
		low(1),
		mediumLow(2),
		medium(3),
		mediumHigh(4),
		high(5),
		auto(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current speed of the evaporator fan. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingDynamicEntry 1 }

tlpCoolingCondenserFanSpeed OBJECT-TYPE
    SYNTAX INTEGER {
        off(0),
        low(1),
        mediumLow(2),
        medium(3),
        mediumHigh(4),
        high(5),
        auto(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current speed of the condenser fan. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingDynamicEntry 2 }

tlpCoolingCompressorFrequency OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "Hertz"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The operating mechanical frequency(Hz) of the compressor. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= {  tlpCoolingDynamicEntry 3 }

tlpCoolingEEVSteps OBJECT-TYPE
	SYNTAX INTEGER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "The current EEV steps. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingDynamicEntry 4 }

tlpCoolingRuntimeTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingRuntimeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The runtime detail for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by  tlpCoolingIdentNumCooling."
    ::= {  tlpCoolingDetail 5 }

tlpCoolingRuntimeEntry OBJECT-TYPE
    SYNTAX TlpCoolingRuntimeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing runtime for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingRuntimeTable 1 }

TlpCoolingRuntimeEntry ::= SEQUENCE {
    tlpCoolingCompressorRunDays       Unsigned32,
    tlpCoolingCondensatePumpRunDays   Unsigned32,
    tlpCoolingAirFilterRunHours       Unsigned32,
	tlpCoolingEvaporatorFanRunDays    Unsigned32,
	tlpCoolingCondenserFanRunDays     Unsigned32,
    tlpCoolingAtomizerRunDays	      Unsigned32 }

tlpCoolingCompressorRunDays OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "days"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total days the compressor has been under operation. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingRuntimeEntry 1 }

tlpCoolingCondensatePumpRunDays OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "days"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total days the condensate pump has been under operation. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingRuntimeEntry 2}

tlpCoolingAirFilterRunHours OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "hours"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total hours the air filter has been under operation. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingRuntimeEntry 3 }

tlpCoolingEvaporatorFanRunDays OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "days"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total days the evaporator fan has been under operation. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingRuntimeEntry 4 }

tlpCoolingCondenserFanRunDays OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "days"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total days the condenser fan has been under operation. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingRuntimeEntry 5 }

tlpCoolingAtomizerRunDays OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "days"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total days the atomizer has been under operation. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingRuntimeEntry 6 }

tlpCoolingStatusTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingStatusEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The status detail for each cooling device in device table. This is a
        sparse dependent table, and will contain the number of entries based upon the
        number of cooling device managed by the agent as reported by tlpCoolingIdentNumCooling."
    ::= {  tlpCoolingDetail 6 }

tlpCoolingStatusEntry OBJECT-TYPE
    SYNTAX TlpCoolingStatusEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing status for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingStatusTable 1 }

TlpCoolingStatusEntry ::= SEQUENCE {
    tlpCoolingOperatingMode    INTEGER,
    tlpCoolingCoolOutput	   Unsigned32,
	tlpCoolingAlarmStatus      INTEGER,
	tlpCoolingCompressorStatus INTEGER,
	tlpCoolingWaterStatus      INTEGER}

tlpCoolingOperatingMode OBJECT-TYPE
    SYNTAX INTEGER {
	    off(0),
		idle(1),
		cooling(2),
		shuttingDown(3),
		dehumidifying(4),
		defrosting(5),
		notConnected(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current operating mode. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingStatusEntry 1 }

tlpCoolingCoolOutput OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "0.1 kilowatts"
	MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current cooling output of the cooling device in tenth of kilowatt. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingStatusEntry 2 }

tlpCoolingAlarmStatus OBJECT-TYPE
    SYNTAX INTEGER {
	    noAlarm(0),
		warning(1),
		critical(2)}
	MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current alarm status of the cooling device. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingStatusEntry 3 }

tlpCoolingCompressorStatus OBJECT-TYPE
    SYNTAX INTEGER {
	    off(0),
		on(1)}
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current operating status of compressor. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingStatusEntry 4 }

tlpCoolingWaterStatus OBJECT-TYPE
    SYNTAX INTEGER {
	    notFull(0),
	    full(1)}
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current water status of cooling unit. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingStatusEntry 5 }

tlpCoolingControlTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The actionable controls for each cooling device in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are cooling device."
    ::= { tlpCoolingControl 1 }

tlpCoolingControlEntry OBJECT-TYPE
    SYNTAX TlpCoolingControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing controls for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingControlTable 1 }

TlpCoolingControlEntry ::= SEQUENCE {
    tlpCoolingOnOff    INTEGER }

tlpCoolingOnOff OBJECT-TYPE
    SYNTAX INTEGER {
        turnOffUnit(0),
        turnOnUnit(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Turn the unit on or off."
    ::= { tlpCoolingControlEntry 1 }

tlpCoolingConfigTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration details for each each cooling device in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are cooling device. Not all options are available on all
        cooling devices."
    ::= { tlpCoolingConfig 1 }

tlpCoolingConfigEntry OBJECT-TYPE
    SYNTAX TlpCoolingConfigEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry configuration details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingConfigTable 1 }

TlpCoolingConfigEntry ::= SEQUENCE {
	tlpCoolingAutoStart            INTEGER,
	tlpCoolingFanSpeed             INTEGER,
	tlpCoolingControlType          INTEGER,
	tlpCoolingDisplayUnits         INTEGER,
	tlpCoolingBeepOnKey            INTEGER,
	tlpCoolingInputContactType     INTEGER,
	tlpCoolingOutputContactType    INTEGER,
	tlpCoolingOutputRelaySource    INTEGER,
	tlpCoolingOutputRelayDefault   INTEGER,
	tlpCoolingOffOnInputContact    INTEGER,
    tlpCoolingOffOnLeak             INTEGER,
	tlpCoolingWaterLeakContactType INTEGER,
	tlpCoolingAirFilterInterval    Unsigned32,
    tlpCoolingAirFilterAlarm       INTEGER,
	tlpCoolingMaxAirFilterRunHours Unsigned32,
	tlpCoolingStartTimer           Unsigned32,
    tlpCoolingStopTimer            Unsigned32,
	tlpCoolingEnergyMode           INTEGER,
	tlpCoolingDefrostMode          INTEGER,
    tlpCoolingRemoteTemperatureSensor  	INTEGER,
	tlpCoolingDehumidifyingMode    INTEGER,
    tlpCoolingFanAlwaysOn          INTEGER,
    tlpCoolingQuietMode       	   INTEGER,
	tlpCoolingHotGasBypass         INTEGER,
	tlpCoolingAutoFanSpeed         INTEGER }

tlpCoolingAutoStart OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Specifies if the unit should start automatically on power up.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 1 }

tlpCoolingFanSpeed OBJECT-TYPE
    SYNTAX INTEGER {
	    off(0),
		low(1),
		mediumLow(2),
		medium(3),
		mediumHigh(4),
		high(5),
		auto(6) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Specifies the actual speed of the evaporator fan. Setting the fan speed
		to auto(6) will enable tlpCoolingAutoFanSpeed; however, on most units getting
		the fan speed will return the actual fan speed regardless whether auto fan
		speed is enabled or disabled. The value auto(6) is returned only on units that
		do not report the actual fan speed when auto fan speed is enabled. Not all
		values are supported on all cooling units. Invalid values will return an appropriate
		error when accessed. If not supported, this value will always return ERROR_NO_SUCH_NAME"
    ::= { tlpCoolingConfigEntry 2 }

tlpCoolingControlType OBJECT-TYPE
    SYNTAX INTEGER {
	    returnAirTemperature(0),
		remoteTemperature(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Specifies which temperature is used as a control point. If not supported,
		this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 3 }

tlpCoolingDisplayUnits OBJECT-TYPE
    SYNTAX INTEGER {
	    metric(0),
		english(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Specifies whether displays output values in Metric or English formats. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 4 }

tlpCoolingBeepOnKey OBJECT-TYPE
    SYNTAX INTEGER {
        noBeep(0),
        beepOn(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Specifies whether a beep is emitted when a key is pressed on the unit's display panel. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 5 }

tlpCoolingInputContactType OBJECT-TYPE
    SYNTAX INTEGER {
        normallyClosed(0),
        normallyOpen(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The input contact type. If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 6 }

tlpCoolingOutputContactType OBJECT-TYPE
    SYNTAX INTEGER {
        normallyClosed(0),
        normallyOpen(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The output contact type. If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 7 }

tlpCoolingOutputRelaySource OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        allAlarmsAndWarnings(1),
        criticalAlarmsOnly(2) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The alarm level that causes the output relay to activate. If not supported,
		this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 8 }

tlpCoolingOutputRelayDefault OBJECT-TYPE
    SYNTAX INTEGER {
        normallyClosed(0),
        normallyOpen(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Type of contact to make when output relay is activated. If not supported,
		this value will always return ERROR_NO_SUCH_NAME."
   ::= { tlpCoolingConfigEntry 9 }

tlpCoolingOffOnInputContact OBJECT-TYPE
    SYNTAX INTEGER {
        turnOff(0),
        alarmOnly(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates if the unit should stop cooling when input contact is activated. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 10 }

tlpCoolingOffOnLeak OBJECT-TYPE
    SYNTAX INTEGER {
        turnOff(0),
        alarmOnly(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Indicates if the unit should stop cooling when leak input is activated. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 11 }

tlpCoolingWaterLeakContactType OBJECT-TYPE
    SYNTAX INTEGER {
	    normallyClosed(0),
		normallyOpen(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The water leak contact type. If not supported,
		this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 12 }

tlpCoolingAirFilterInterval OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "weeks"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number of weeks before the air filter needs to be changed.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 13 }

tlpCoolingAirFilterAlarm OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
	UNITS "hours"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When enabled, an alarm should occur when the maximum number of air filter run hours
		has been exceeded. If not supported, this value will always return ERROR_NO_SUCH_NAME."
  ::= { tlpCoolingConfigEntry 14 }

tlpCoolingMaxAirFilterRunHours OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "hours"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The maximum interval for air filter run hours before warning is annunciated.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 15 }

tlpCoolingStartTimer OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "minutes"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit will start operate after the specified timer elapsed. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 16 }

tlpCoolingStopTimer  OBJECT-TYPE
    SYNTAX Unsigned32
	UNITS "minutes"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The unit will switch to standby mode after the specified timer elapsed. If not supported,
        this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 17 }

tlpCoolingEnergyMode  OBJECT-TYPE
    SYNTAX INTEGER {
        normalCooling(0),
        energySaving(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the energy saving mode.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 18 }

tlpCoolingDefrostMode OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the defrost mode.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 19 }

tlpCoolingRemoteTemperatureSensor OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the remote temperature sensor.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 20 }

tlpCoolingDehumidifyingMode OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the dehumidifying mode.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 21 }

tlpCoolingFanAlwaysOn OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the fan always on.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 22 }

tlpCoolingQuietMode OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the quiet mode.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 23 }

tlpCoolingHotGasBypass OBJECT-TYPE
    SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the hot gas bypass.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 24 }

tlpCoolingAutoFanSpeed OBJECT-TYPE
  	SYNTAX INTEGER {
        disabled(0),
        enabled(1) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This option enables or disables the auto fan speed.
         If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigEntry 25 }


tlpCoolingConfigTemperatureFahrenheitTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingConfigTemperatureFahrenheitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration details for each each cooling device in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are cooling device. Not all options are available on all
        cooling devices."
    ::= { tlpCoolingConfig 2 }

tlpCoolingConfigTemperatureFahrenheitEntry OBJECT-TYPE
    SYNTAX TlpCoolingConfigTemperatureFahrenheitEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry configuration details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingConfigTemperatureFahrenheitTable 1 }

TlpCoolingConfigTemperatureFahrenheitEntry ::= SEQUENCE {
    tlpCoolingSetPointDegF  	        Integer32,
	tlpCoolingSupplyAirHighLimitDegF    Integer32,
	tlpCoolingSupplyAirLowLimitDegF     Integer32,
	tlpCoolingMaxDeviationLimitDegF     Integer32,
	tlpCoolingReturnAirHighLimitDegF    Integer32,
	tlpCoolingReturnAirLowLimitDegF     Integer32,
    tlpCoolingRemoteSetPointDegF  	    Integer32 }

tlpCoolingSetPointDegF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The set point temperature the unit will control the temperature in tenths degrees Fahrenheit.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureFahrenheitEntry 1 }

tlpCoolingSupplyAirHighLimitDegF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum allowed supply air temperature in tenths degrees Fahrenheit before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureFahrenheitEntry 2  }

tlpCoolingSupplyAirLowLimitDegF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minimum allowed supply air temperature in tenths degrees Fahrenheit before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureFahrenheitEntry 3 }

tlpCoolingMaxDeviationLimitDegF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum allowed air temperature deviation from set temperature before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureFahrenheitEntry  4 }

tlpCoolingReturnAirHighLimitDegF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum allowed supply air temperature in tenths degrees Fahrenheit before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureFahrenheitEntry 5 }

tlpCoolingReturnAirLowLimitDegF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minimum allowed supply air temperature in tenths degrees Fahrenheit before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureFahrenheitEntry 6 }

tlpCoolingRemoteSetPointDegF OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Fahrenheit"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The remote set point temperature the unit will control the temperature in tenths degrees Fahrenheit.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureFahrenheitEntry 7 }


tlpCoolingConfigTemperatureCelsiusTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpCoolingConfigTemperatureCelsiusEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The configuration details for each cooling device in device table. This
        is a sparse dependent table, and will contain some subset of
        devices that are cooling device. Not all options are available on all
        cooling devices."
    ::= { tlpCoolingConfig 3 }

tlpCoolingConfigTemperatureCelsiusEntry OBJECT-TYPE
    SYNTAX TlpCoolingConfigTemperatureCelsiusEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry configuration details for a particular cooling device."
    INDEX { tlpDeviceIndex }
    ::= { tlpCoolingConfigTemperatureCelsiusTable 1 }

TlpCoolingConfigTemperatureCelsiusEntry ::= SEQUENCE {
	tlpCoolingSetPointDegC  	      Integer32,
	tlpCoolingSupplyAirHighLimitDegC  Integer32,
	tlpCoolingSupplyAirLowLimitDegC   Integer32,
	tlpCoolingMaxDeviationLimitDegC   Integer32,
	tlpCoolingReturnAirHighLimitDegC  Integer32,
	tlpCoolingReturnAirLowLimitDegC   Integer32,
    tlpCoolingRemoteSetPointDegC  	  Integer32 }

tlpCoolingSetPointDegC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The set point temperature the unit will control the temperature in tenths degrees Celsius.
	    If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureCelsiusEntry 1 }

tlpCoolingSupplyAirHighLimitDegC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum allowed supply air temperature in tenths degrees Celsius before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureCelsiusEntry 2 }

tlpCoolingSupplyAirLowLimitDegC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minimum allowed supply air temperature in tenths degrees Celsius before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureCelsiusEntry 3 }

tlpCoolingMaxDeviationLimitDegC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum allowed air temperature deviation from set temperature before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureCelsiusEntry 4 }

tlpCoolingReturnAirHighLimitDegC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum allowed return air temperature in tenths degrees Celsius before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureCelsiusEntry 5 }

tlpCoolingReturnAirLowLimitDegC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minimum allowed return air temperature in tenths degrees Celsius before firing an alarm.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureCelsiusEntry 6 }

tlpCoolingRemoteSetPointDegC OBJECT-TYPE
    SYNTAX Integer32
    UNITS "0.1 degrees Celsius"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The remote set point temperature the unit will control the temperature in tenths degrees Celsius.
        If not supported, this value will always return ERROR_NO_SUCH_NAME."
    ::= { tlpCoolingConfigTemperatureCelsiusEntry 7 }

-- kvm

tlpKvmIdentNumKvm OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of kvm devices that this agent is currently managing."
    ::= { tlpKvmIdent 1 }

-- rackTrack

tlpRackTrackIdentNumRackTrack OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of rack track devices that this agent is currently managing."
    ::= { tlpRackTrackIdent 1 }

-- switch

tlpSwitchIdentNumSwitch OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of switch devices that this agent is currently managing."
    ::= { tlpSwitchIdent 1 }

-- software

tlpAgentType OBJECT-TYPE
    SYNTAX INTEGER {
        unknown(0),
        pal(1),
        pansa(2),
        delta(3),
        sinetica(4),
        netos6(5),
        netos7(6),
        panms(7),
        nmc5(8) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of PowerAlert engine providing this data."
    ::= { tlpAgentIdent 1 }

tlpAgentVersion OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The version of the agent software."
    ::= { tlpAgentIdent 2 }

tlpAgentDriverVersion OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The driver version of the agent software."
    ::= { tlpAgentIdent 3 }

tlpAgentMAC OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The MAC Address of the agent."
    ::= { tlpAgentIdent 4 }

tlpAgentSerialNum OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Serial number of the web card providing the agent data. This value is
        only supported by a web card agent. PowerAlert for PC (PAL) agents do
        not support this value and will return ERROR_NO_SUCH_NAME."
    ::= { tlpAgentIdent 5 }

tlpAgentUuid OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Universally unique identifier for this host in the form 8-4-4-4-12 for a
        total of 36 characters (32 alphanumeric characters and four hyphens). For
        example: 550e8400-e29b-41d4-a716-************."
    ::= { tlpAgentIdent 6 }

tlpAgentAttributesSupports  OBJECT IDENTIFIER
    ::= { tlpAgentAttributes 1 }

tlpAgentAttributesSupportsHTTP OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports HTTP connections."
    ::= { tlpAgentAttributesSupports 1 }

tlpAgentAttributesSupportsHTTPS OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports HTTPS connections."
    ::= { tlpAgentAttributesSupports 2 }

tlpAgentAttributesSupportsFTP OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports FTP connections."
    ::= { tlpAgentAttributesSupports 3 }

tlpAgentAttributesSupportsTelnetMenu OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports Telnet menu connections."
    ::= { tlpAgentAttributesSupports 4 }

tlpAgentAttributesSupportsTelnetCLI OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports Telnet CLI connections."
    ::= { tlpAgentAttributesSupports 5 }

tlpAgentAttributesSupportsSSHMenu OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports SSH menu connections."
    ::= { tlpAgentAttributesSupports 6 }

tlpAgentAttributesSupportsSSHCLI OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports SSH CLI connections."
    ::= { tlpAgentAttributesSupports 7 }

tlpAgentAttributesSupportsSNMP OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports SNMP connections."
    ::= { tlpAgentAttributesSupports 8 }

tlpAgentAttributesSupportsSNMPTrap OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent supports listening for SNMP traps. This
        is only applicable for network monitoring applications, PowerAlert
        Shutdown Agent for an example, that connect to a web card or PowerAlert
        Local and listen for outbound traps. As such, this value is not supported
        and will always return 2(false) for a web card or PowerAlert Local."
    ::= { tlpAgentAttributesSupports 9 }

tlpAgentAttributesAutostart  OBJECT IDENTIFIER
    ::= { tlpAgentAttributes 2 }

tlpAgentAttributesAutostartHTTP OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts HTTP services."
    ::= { tlpAgentAttributesAutostart 1 }

tlpAgentAttributesAutostartHTTPS OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts HTTPS services."
    ::= { tlpAgentAttributesAutostart 2 }

tlpAgentAttributesAutostartFTP OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts FTP services."
    ::= { tlpAgentAttributesAutostart 3 }

tlpAgentAttributesAutostartTelnetMenu OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts Telnet menu services."
    ::= { tlpAgentAttributesAutostart 4 }

tlpAgentAttributesAutostartTelnetCLI OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts Telnet CLI services."
    ::= { tlpAgentAttributesAutostart 5 }

tlpAgentAttributesAutostartSSHMenu OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts SSH menu services."
    ::= { tlpAgentAttributesAutostart 6 }

tlpAgentAttributesAutostartSSHCLI OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts SSH CLI services."
    ::= { tlpAgentAttributesAutostart 7 }

tlpAgentAttributesAutostartSNMP OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent autostarts SNMP services."
    ::= { tlpAgentAttributesAutostart 8 }

tlpAgentAttributesSnmp  OBJECT IDENTIFIER
    ::= { tlpAgentAttributes 3 }

tlpAgentAttributesSNMPv1Enabled OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent will allow SNMPv1 connections."
    ::= { tlpAgentAttributesSnmp 1 }

tlpAgentAttributesSNMPv2cEnabled OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent will allow SNMPv2c connections."
    ::= { tlpAgentAttributesSnmp 2 }

tlpAgentAttributesSNMPv3Enabled OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Indicates whether the agent will allow SNMPv3 connections."
    ::= { tlpAgentAttributesSnmp 3 }

tlpAgentAttributesPorts  OBJECT IDENTIFIER
    ::= { tlpAgentAttributes 4 }

tlpAgentAttributesHTTPPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for HTTP communications."
    ::= { tlpAgentAttributesPorts 1 }

tlpAgentAttributesHTTPSPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for HTTPS communications."
    ::= { tlpAgentAttributesPorts 2 }

tlpAgentAttributesFTPPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for FTP communications."
    ::= { tlpAgentAttributesPorts 3 }

tlpAgentAttributesTelnetMenuPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for Telnet Menu communications."
    ::= { tlpAgentAttributesPorts 4 }

tlpAgentAttributesTelnetCLIPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for Telnet CLI communications."
    ::= { tlpAgentAttributesPorts 5 }

tlpAgentAttributesSSHMenuPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for SSH Menu communications."
    ::= { tlpAgentAttributesPorts 6 }

tlpAgentAttributesSSHCLIPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for SSH CLI communications."
    ::= { tlpAgentAttributesPorts 7 }

tlpAgentAttributesSNMPPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for SNMP communications."
    ::= { tlpAgentAttributesPorts 8 }

tlpAgentAttributesSNMPTrapPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The port used for SNMP trap and notifications."
    ::= { tlpAgentAttributesPorts 9 }

tlpAgentConfigRemoteRegistration OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Registers an SNMP contact's IP address and the port on which
        the contact is listening for SNMP traps.  Setting this
        value is used to CREATE a table entry in a non-standard way.
        Getting this value will return a string INADDR_ANY and any
        port number.

        The port number should always be included. When unregistering
        an SNMP contact, the requester's IP address should be provided
        with a port number of 0.  When setting or unsetting this OID,
        the IP address string should never be 0 nor should it be empty.

        The registration uses its default community string to register
        each contact. To use a community other than the default, then
        it must be appended to the end of the string delimited with a
        comma.

        The address should be of the format 0.0.0.0:00000 for IPv4.
        If IPv6 is supported, use the bracket-enclosed format given
        in IETF RFC 2732 Section 2, with the following examples:

        [FEDC:BA98:7654:3210:FEDC:BA98:7654:3210]:80
        [1080:0:0:0:8:800:200C:417A]:162
        [3ffe:2a00:100:7031::1]:162
        [1080::8:800:200C:417A]:162,myCommunity
        [::*********]:162,myCommunity
        [::FFFF:*************]:80
        [2010:836B:4179::836B:4179]:162"
    ::= { tlpAgentConfig 1 }

tlpAgentConfigCurrentTime OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The current date and time of the system."
    ::= { tlpAgentConfig 2 }

tlpAgentNumEmailContacts OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of email contacts currently defined in the agent."
    ::= { tlpAgentEmailContacts 1 }

tlpAgentEmailContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAgentEmailContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of email contact entries. The number of entries is given by
        the value of tlpAgentNumEmailContacts."
    ::= { tlpAgentEmailContacts 2 }

tlpAgentEmailContactEntry OBJECT-TYPE
    SYNTAX TlpAgentEmailContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing the details of an email contact."
    INDEX { tlpAgentEmailContactIndex }
    ::= { tlpAgentEmailContactTable 1 }

TlpAgentEmailContactEntry ::= SEQUENCE {
    tlpAgentEmailContactIndex           Unsigned32,
    tlpAgentEmailContactRowStatus       RowStatus,
    tlpAgentEmailContactName            DisplayString,
    tlpAgentEmailContactAddress         DisplayString }

tlpAgentEmailContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the email contact."
    ::= { tlpAgentEmailContactEntry 1 }

tlpAgentEmailContactRowStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Row status for the tlpAgentEmailContactTable."
    ::= { tlpAgentEmailContactEntry 2 }

tlpAgentEmailContactName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the email contact."
    ::= { tlpAgentEmailContactEntry 3 }

tlpAgentEmailContactAddress OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The address of the email contact."
    ::= { tlpAgentEmailContactEntry 4 }

tlpAgentNumSnmpContacts OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of SNMP contacts currently defined in the agent."
    ::= { tlpAgentSnmpContacts 1 }

tlpAgentSnmpContactTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAgentSnmpContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of SNMP contact entries. The number of entries is given by
        the value of tlpAgentNumSnmpContacts."
    ::= { tlpAgentSnmpContacts 2 }

tlpAgentSnmpContactEntry OBJECT-TYPE
    SYNTAX TlpAgentSnmpContactEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing the details of an SNMP contact."
        INDEX { tlpAgentSnmpContactIndex }
    ::= { tlpAgentSnmpContactTable 1 }

TlpAgentSnmpContactEntry ::= SEQUENCE {
    tlpAgentSnmpContactIndex            Unsigned32,
    tlpAgentSnmpContactRowStatus        RowStatus,
    tlpAgentSnmpContactName             DisplayString,
    tlpAgentSnmpContactIpAddress        DisplayString,
    tlpAgentSnmpContactPort             Unsigned32,
    tlpAgentSnmpContactSnmpVersion      INTEGER,
    tlpAgentSnmpContactSecurityName     DisplayString,
    tlpAgentSnmpContactPrivPassword     DisplayString,
    tlpAgentSnmpContactAuthPassword     DisplayString }

tlpAgentSnmpContactIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for the SNMP contact."
    ::= { tlpAgentSnmpContactEntry 1 }

tlpAgentSnmpContactRowStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Row status for the tlpAgentSnmpContactTable."
    ::= { tlpAgentSnmpContactEntry 2 }

tlpAgentSnmpContactName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the SNMP contact."
    ::= { tlpAgentSnmpContactEntry 3 }

tlpAgentSnmpContactIpAddress OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IP address of the SNMP contact."
    ::= { tlpAgentSnmpContactEntry 4 }

tlpAgentSnmpContactPort OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The port of the SNMP contact."
    ::= { tlpAgentSnmpContactEntry 5 }

tlpAgentSnmpContactSnmpVersion OBJECT-TYPE
    SYNTAX INTEGER {
        snmpv1(1),
        snmpv2c(2),
        snmpv3(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The SNMP contact version to use for sending traps."
    ::= { tlpAgentSnmpContactEntry 6 }

tlpAgentSnmpContactSecurityName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The security name for the SNMP contact."
    ::= { tlpAgentSnmpContactEntry 7 }
tlpAgentSnmpContactPrivPassword OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "The privacy password to use for sending V3 traps to the SNMP
        contact."
    ::= { tlpAgentSnmpContactEntry 8 }
tlpAgentSnmpContactAuthPassword OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS obsolete
    DESCRIPTION
        "The authentication password to use for sending V3 traps to the
        SNMP contact."
    ::= { tlpAgentSnmpContactEntry 9 }

-- alarms

tlpAlarmsPresent OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The present number of active alarm conditions."
    ::= { tlpAlarms 1 }

tlpAlarmTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpDeviceAlarmEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of alarm conditions for all devices managed by this agent. This
        table contains zero, one, or many rows at any moment, depending upon
        the number of alarm conditions in effect or conditions not acknowledged.
        The agent creates a row in the table each time a condition is detected
        and deletes that row when that condition no longer pertains (becomes
        inactive and is acknowledged). The agent creates the first row with
        tlpAlarmId equal to 1, and increments the value of tlpAlarmId each time
        a new row is created, wrapping to the first free value greater than or
        equal to 1 when the maximum value of tlpAlarmId would otherwise be
        exceeded. Consequently, after multiple operations, the table may become
        sparse (e.g., containing entries for rows 95, 100, 101, and 203) and the
        entries should not be assumed to be in chronological order because
        tlpAlarmId might have wrapped.

        The number of rows in the table at any given time is reflected by
        the value of tlpAlarmsPresent."
    ::= { tlpAlarms 2 }

tlpAlarmEntry OBJECT-TYPE
    SYNTAX TlpDeviceAlarmEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing information application to a particular
        alarm."
    INDEX { tlpAlarmId }
    ::= { tlpAlarmTable 1 }

TlpDeviceAlarmEntry ::= SEQUENCE {
    tlpAlarmId                Unsigned32,
    tlpAlarmDescr             OBJECT IDENTIFIER,
    tlpAlarmTime              TimeStamp,
    tlpAlarmTableRef          OBJECT IDENTIFIER,
    tlpAlarmTableRowRef       OBJECT IDENTIFIER,
    tlpAlarmDetail            DisplayString,
    tlpAlarmType              INTEGER,
    tlpAlarmState             INTEGER,
    tlpAlarmAcknowledged      INTEGER }

tlpAlarmId OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A unique identifier for an alarm condition. This value will remain
        constant for the duration of the alarm condition."
    ::= { tlpAlarmEntry 1 }

tlpAlarmDescr OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A reference of the alarm description object. The object referenced
        should not be accessible, but rather be used to provide a unique
        description for the alarm condition. The object referenced will be a
        well known alarm object associated with the device on which the alarm
        condition is active."
    ::= { tlpAlarmEntry 2 }

tlpAlarmTime OBJECT-TYPE
    SYNTAX TimeStamp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The value of sysUpTime when the alarm condition was detected. If the
        alarm condition was detected at the time of agent startup and presumably
        existed before agent startup, the value of tlpAlarmsTime shall equal
        0."
    ::= { tlpAlarmEntry 3 }

tlpAlarmTableRef OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This value references the OID of a table object applicable to the alarm
        condition. This, in conjunction with the table row as defined by
        tlpAlarmTableRowRef, specifies the object/row within this table to which
        the alarm condition applies.

        Example 1 -- The following sample values will be returned for a UPS on
        battery:
        tlpAlarmId                28
        tlpAlarmDescr             tlpUpsAlarmOnBattery
        tlpAlarmTime              12345678
        tlpAlarmTableRef          tlpDeviceTable
        tlpAlarmTableRowRef       tlpDeviceIndex.2
        tlpAlarmDetail            On Battery
        tlpAlarmType              warning
        tlpAlarmState             active
        tlpAlarmAcknowledged      notAcknowledged

        In the above example, tlpAlarmTableRef references the device table and
        tlpAlarmTableRowRef references the 2nd managed device in that table.

        Example 2 -- The following sample values will be returned for a PDU with
        a load turned off:
        tlpAlarmId                46
        tlpAlarmDescr             tlpPduAlarmLoadOff
        tlpAlarmTime              12345678
        tlpAlarmTableRef          tlpPduOutletTable
        tlpAlarmTableRowRef       tlpOutletIndex.1.4
        tlpAlarmDetail            Load Off
        tlpAlarmType              warning
        tlpAlarmState             active
        tlpAlarmAcknowledged      acknowledged

        In the above example, tlpAlarmTableRef references the pdu outlet table and
        tlpAlarmTableRowRef references the 4th outlet on the 1st managed device."
    ::= { tlpAlarmEntry 4 }

tlpAlarmTableRowRef OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This value is used in conjunction with tlpAlarmTableRef to define the
        object associated with this alarm condition. See the description of
        tlpAlarmTableRef for more information."
    ::= { tlpAlarmEntry 5 }

tlpAlarmDetail OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A textual description of the alarm condition."
    ::= { tlpAlarmEntry 6 }

tlpAlarmType OBJECT-TYPE
    SYNTAX INTEGER {
        critical(1),
        warning(2),
        info(3),
        status(4),
        offline(5),
        custom(6) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The category/group of this alarm."
    ::= { tlpAlarmEntry 7 }

tlpAlarmState OBJECT-TYPE
    SYNTAX INTEGER {
        active(1),
        inactive(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current state of this alarm condition. An active(1) alarm is a condition
        that currently exists. An inactive(2) alarm is a condition that no longer exists
        because the alarm either cleared or was resolved. Each alarm condition must be
        cleared and acknowledged before it is removed from this table (see
        tlpAlarmAcknowledged)."
    ::= { tlpAlarmEntry 8 }

tlpAlarmAcknowledged OBJECT-TYPE
    SYNTAX INTEGER {
        notAcknowledged(1),
        acknowledged(2) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Denotes whether the alarm condition has been acknowledged. The agent will
        return notAcknowledged(1) if the condition has not been acknowledged, or
        acknowledged(2) when acknowledged. Writing the value acknowledged(2) for an
        alarm acknowledges the alarm condition. Each alarm condition must be cleared
        and acknowledged before it is removed from this table; therefore, even if
        acknowledged, the alarm will remain in this table until the alarm becomes
        inactive as denoted by tlpAlarmState."
    ::= { tlpAlarmEntry 9 }

tlpAlarmCommunicationsLost OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A problem has been encountered in the communications between the agent
        and the device."
    ::= { tlpDeviceAlarms 1 }

tlpAlarmUserDefined OBJECT IDENTIFIER
    ::= { tlpDeviceAlarms 2 }

tlpAlarmUserDefined01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 1 is in alarm."
    ::= { tlpAlarmUserDefined 1 }

tlpAlarmUserDefined02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 2 is in alarm."
    ::= { tlpAlarmUserDefined 2 }

tlpAlarmUserDefined03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 3 is in alarm."
    ::= { tlpAlarmUserDefined 3 }

tlpAlarmUserDefined04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 4 is in alarm."
    ::= { tlpAlarmUserDefined 4 }

tlpAlarmUserDefined05 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 5 is in alarm."
    ::= { tlpAlarmUserDefined 5 }

tlpAlarmUserDefined06 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 6 is in alarm."
    ::= { tlpAlarmUserDefined 6 }

tlpAlarmUserDefined07 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 7 is in alarm."
    ::= { tlpAlarmUserDefined 7 }

tlpAlarmUserDefined08 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 8 is in alarm."
    ::= { tlpAlarmUserDefined 8 }

tlpAlarmUserDefined09 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "User-defined event 9 is in alarm."
    ::= { tlpAlarmUserDefined 9 }

tlpAlarmInternalFirmwareError OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A problem has been encountered with the device firmware."
    ::= { tlpDeviceAlarms 3 }

tlpAlarmDeviceEEPROMFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A problem has been encountered with the device EEPROM."
    ::= { tlpDeviceAlarms 4 }

tlpUpsAlarmBatteryBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "One or more batteries have been determined to require replacement."
    ::= { tlpUpsAlarms 1 }

tlpUpsAlarmOnBattery OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The UPS is drawing power from the batteries."
    ::= { tlpUpsAlarms 2 }

tlpUpsAlarmLowBattery OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The remaining battery charge is less than or equal to the percentage of
        capacity warning specified by tlpUpsConfigBatteryCapacityThreshold."
    ::= { tlpUpsAlarms 3 }

tlpUpsAlarmDepletedBattery OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The UPS will be unable to sustain the present load when and if
        the utility power is lost."
    ::= { tlpUpsAlarms 4 }

tlpUpsAlarmTempBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A temperature is out of tolerance."
    ::= { tlpUpsAlarms 5 }

tlpUpsAlarmInputBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "An input condition is out of tolerance."
    ::= { tlpUpsAlarms 6 }

tlpUpsAlarmOutputBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "An output condition (other than OutputOverload) is out of
        tolerance."
    ::= { tlpUpsAlarms 7 }

tlpUpsAlarmOutputOverload OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output load exceeds the UPS output capacity."
    ::= { tlpUpsAlarms 8 }

tlpUpsAlarmOnBypass OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The Bypass is presently engaged on the UPS."
    ::= { tlpUpsAlarms 9 }

tlpUpsAlarmBypassBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The Bypass is out of tolerance."
    ::= { tlpUpsAlarms 10 }

tlpUpsAlarmOutputOffAsRequested OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The UPS has shutdown as requested, i.e., the output is off."
    ::= { tlpUpsAlarms 11 }

tlpUpsAlarmUpsOffAsRequested OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The entire UPS has shutdown as commanded."
    ::= { tlpUpsAlarms 12 }

tlpUpsAlarmChargerFailed OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "An uncorrected problem has been detected within the UPS
        charger subsystem."
    ::= { tlpUpsAlarms 13 }

tlpUpsAlarmUpsOutputOff OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output of the UPS is in the off state."
    ::= { tlpUpsAlarms 14 }

tlpUpsAlarmUpsSystemOff OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The UPS system is in the off state."
    ::= { tlpUpsAlarms 15 }

tlpUpsAlarmFanFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The failure of one or more fans in the UPS has been detected."
    ::= { tlpUpsAlarms 16 }

tlpUpsAlarmFuseFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The failure of one or more fuses has been detected."
    ::= { tlpUpsAlarms 17 }

tlpUpsAlarmGeneralFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A general fault in the UPS has been detected."
    ::= { tlpUpsAlarms 18 }

tlpUpsAlarmDiagnosticTestFailed OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The result of the last diagnostic test indicates a failure."
    ::= { tlpUpsAlarms 19 }

tlpUpsAlarmAwaitingPower OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The UPS output is off and the UPS is awaiting the return of
        input power."
    ::= { tlpUpsAlarms 20 }

tlpUpsAlarmShutdownPending OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A tlpUpsShutdownAfterDelay countdown is underway."
    ::= { tlpUpsAlarms 21 }

tlpUpsAlarmShutdownImminent OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The UPS will turn off power to the load in less than 5 seconds;
        this may be either a timed shutdown or a low battery shutdown."
    ::= { tlpUpsAlarms 22 }

tlpUpsAlarmLoadLevelAboveThreshold OBJECT IDENTIFIER
    ::= { tlpUpsAlarms 23 }

tlpUpsAlarmLoadLevelAboveThresholdTotal OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The total load level is above the designated threshold."
    ::= { tlpUpsAlarmLoadLevelAboveThreshold 1 }

tlpUpsAlarmLoadLevelAboveThresholdPhase1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The load level on phase 1 exceeds load limits."
    ::= { tlpUpsAlarmLoadLevelAboveThreshold 2 }

tlpUpsAlarmLoadLevelAboveThresholdPhase2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The load level on phase 2 exceeds load limits."
    ::= { tlpUpsAlarmLoadLevelAboveThreshold 3 }

tlpUpsAlarmLoadLevelAboveThresholdPhase3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The load level on phase 3 exceeds load limits."
    ::= { tlpUpsAlarmLoadLevelAboveThreshold 4 }

tlpUpsAlarmOutputCurrentChanged OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current changed from its last known value."
    ::= { tlpUpsAlarms 24 }

tlpUpsAlarmBatteryAgeAboveThreshold OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "One or more batteries have been determined to require replacement."
    ::= { tlpUpsAlarms 25 }

tlpUpsAlarmLoadOff OBJECT IDENTIFIER
    ::= { tlpUpsAlarms 26 }

tlpUpsAlarmLoadOff01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 1 is off."
    ::= { tlpUpsAlarmLoadOff 1 }

tlpUpsAlarmLoadOff02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 2 is off."
    ::= { tlpUpsAlarmLoadOff 2 }

tlpUpsAlarmLoadOff03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 3 is off."
    ::= { tlpUpsAlarmLoadOff 3 }

tlpUpsAlarmLoadOff04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 4 is off."
    ::= { tlpUpsAlarmLoadOff 4 }

tlpUpsAlarmLoadOff05 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 5 is off."
    ::= { tlpUpsAlarmLoadOff 5 }

tlpUpsAlarmLoadOff06 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 6 is off."
    ::= { tlpUpsAlarmLoadOff 6 }

tlpUpsAlarmLoadOff07 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 7 is off."
    ::= { tlpUpsAlarmLoadOff 7 }

tlpUpsAlarmLoadOff08 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 8 is off."
    ::= { tlpUpsAlarmLoadOff 8 }

tlpUpsAlarmLoadOff09 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 9 is off."
    ::= { tlpUpsAlarmLoadOff 9 }

tlpUpsAlarmLoadOff10 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 10 is off."
    ::= { tlpUpsAlarmLoadOff 10 }

tlpUpsAlarmLoadOff11 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 11 is off."
    ::= { tlpUpsAlarmLoadOff 11 }

tlpUpsAlarmLoadOff12 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 12 is off."
    ::= { tlpUpsAlarmLoadOff 12 }

tlpUpsAlarmLoadOff13 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 13 is off."
    ::= { tlpUpsAlarmLoadOff 13 }

tlpUpsAlarmLoadOff14 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 14 is off."
    ::= { tlpUpsAlarmLoadOff 14 }

tlpUpsAlarmLoadOff15 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 15 is off."
    ::= { tlpUpsAlarmLoadOff 15 }

tlpUpsAlarmLoadOff16 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 16 is off."
    ::= { tlpUpsAlarmLoadOff 16 }

tlpUpsAlarmLoadOff17 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 17 is off."
    ::= { tlpUpsAlarmLoadOff 17 }

tlpUpsAlarmLoadOff18 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 18 is off."
    ::= { tlpUpsAlarmLoadOff 18 }

tlpUpsAlarmLoadOff19 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 19 is off."
    ::= { tlpUpsAlarmLoadOff 19 }

tlpUpsAlarmLoadOff20 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 20 is off."
    ::= { tlpUpsAlarmLoadOff 20 }

tlpUpsAlarmLoadOff21 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 21 is off."
    ::= { tlpUpsAlarmLoadOff 21 }

tlpUpsAlarmLoadOff22 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 22 is off."
    ::= { tlpUpsAlarmLoadOff 22 }

tlpUpsAlarmLoadOff23 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 23 is off."
    ::= { tlpUpsAlarmLoadOff 23 }

tlpUpsAlarmLoadOff24 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 24 is off."
    ::= { tlpUpsAlarmLoadOff 24 }

tlpUpsAlarmLoadOff25 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 25 is off."
    ::= { tlpUpsAlarmLoadOff 25 }

tlpUpsAlarmLoadOff26 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 26 is off."
    ::= { tlpUpsAlarmLoadOff 26 }

tlpUpsAlarmLoadOff27 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 27 is off."
    ::= { tlpUpsAlarmLoadOff 27 }

tlpUpsAlarmLoadOff28 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 28 is off."
    ::= { tlpUpsAlarmLoadOff 28 }

tlpUpsAlarmLoadOff29 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 29 is off."
    ::= { tlpUpsAlarmLoadOff 29 }

tlpUpsAlarmLoadOff30 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 30 is off."
    ::= { tlpUpsAlarmLoadOff 30 }

tlpUpsAlarmLoadOff31 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 31 is off."
    ::= { tlpUpsAlarmLoadOff 31 }

tlpUpsAlarmLoadOff32 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 32 is off."
    ::= { tlpUpsAlarmLoadOff 32 }

tlpUpsAlarmLoadOff33 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 33 is off."
    ::= { tlpUpsAlarmLoadOff 33 }

tlpUpsAlarmLoadOff34 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 34 is off."
    ::= { tlpUpsAlarmLoadOff 34 }

tlpUpsAlarmLoadOff35 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 35 is off."
    ::= { tlpUpsAlarmLoadOff 35 }

tlpUpsAlarmLoadOff36 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 36 is off."
    ::= { tlpUpsAlarmLoadOff 36 }

tlpUpsAlarmLoadOff37 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 37 is off."
    ::= { tlpUpsAlarmLoadOff 37 }

tlpUpsAlarmLoadOff38 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 38 is off."
    ::= { tlpUpsAlarmLoadOff 38 }

tlpUpsAlarmLoadOff39 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 39 is off."
    ::= { tlpUpsAlarmLoadOff 39 }

tlpUpsAlarmLoadOff40 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 40 is off."
    ::= { tlpUpsAlarmLoadOff 40 }

tlpUpsAlarmCurrentAboveThreshold OBJECT IDENTIFIER
    ::= { tlpUpsAlarms 27 }

tlpUpsAlarmCurrentAboveThreshold1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 1 exceeds load limits."
    ::= { tlpUpsAlarmCurrentAboveThreshold 1 }

tlpUpsAlarmCurrentAboveThreshold2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 2 exceeds load limits."
    ::= { tlpUpsAlarmCurrentAboveThreshold 2 }

tlpUpsAlarmCurrentAboveThreshold3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 3 exceeds load limits."
    ::= { tlpUpsAlarmCurrentAboveThreshold 3 }

tlpUpsAlarmRuntimeBelowWarningLevel OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The battery seconds remaining is below warning level."
    ::= { tlpUpsAlarms 28 }

tlpUpsAlarmBusStartVoltageLow OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter start up bus voltage is too low."
    ::= { tlpUpsAlarms 29 }

tlpUpsAlarmBusOverVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter bus voltage is too high."
    ::= { tlpUpsAlarms 30 }

tlpUpsAlarmBusUnderVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter bus voltage is too low."
    ::= { tlpUpsAlarms 31 }

tlpUpsAlarmBusVoltageUnbalanced OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter bus voltage is not balanced."
    ::= { tlpUpsAlarms 32 }

tlpUpsAlarmInverterSoftStartBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter bus voltage cannot reach desired voltage within a
        specified duration."
    ::= { tlpUpsAlarms 33 }

tlpUpsAlarmInverterOverVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter voltage is too high."
    ::= { tlpUpsAlarms 34 }

tlpUpsAlarmInverterUnderVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter voltage is too low."
    ::= { tlpUpsAlarms 35 }

tlpUpsAlarmInverterCircuitBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter output is shorted."
    ::= { tlpUpsAlarms 36 }

tlpUpsAlarmBatteryOverVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The battery voltage is too high."
    ::= { tlpUpsAlarms 37 }

tlpUpsAlarmBatteryUnderVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The battery voltage is too low."
    ::= { tlpUpsAlarms 38 }

tlpUpsAlarmSiteWiringFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The utility input wiring is faulty."
    ::= { tlpUpsAlarms 39 }

tlpUpsAlarmOverTemperatureProtection OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The over temperature protection is activated."
    ::= { tlpUpsAlarms 40 }

tlpUpsAlarmOverCharged OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The UPS is over charged."
    ::= { tlpUpsAlarms 41 }

tlpUpsAlarmEPOActive OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The EPO is currently activated."
    ::= { tlpUpsAlarms 42 }

tlpUpsAlarmBypassFrequencyBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The bypass frequency is out of tolerance."
    ::= { tlpUpsAlarms 43 }

tlpUpsAlarmExternalSmartBatteryAgeAboveThreshold OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "One or more external smart batteries have been determined to require
        replacement."
    ::= { tlpUpsAlarms 44 }

tlpUpsAlarmExternalNonSmartBatteryAgeAboveThreshold OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "One or more external non-smart batteries have been determined to require
        replacement."
    ::= { tlpUpsAlarms 45 }

tlpUpsAlarmSmartBatteryCommLost OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Communications with the smart batteries was lost."
    ::= { tlpUpsAlarms 46 }

tlpUpsAlarmLoadsNotAllOn OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "One of more loads are not on."
    ::= { tlpUpsAlarms 47 }

tlpUpsAlarmBatteryTemperatureHigh OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The battery temperature is high."
    ::= { tlpUpsAlarms 48 }

tlpUpsAlarmBatteryTemperatureLow OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The battery temperature is low."
    ::= { tlpUpsAlarms 49 }

tlpUpsAlarmBatteryDisconnected OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The battery disconnected."
    ::= { tlpUpsAlarms 50 }

tlpUpsAlarmBatteryTemperatureSensorDisconnected OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The battery temperature sensor disconnected."
    ::= { tlpUpsAlarms 51 }

tlpUpsAlarmTemperatureDerating 	OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Power derating due to high environment temperature."
    ::= { tlpUpsAlarms 52 }

tlpUpsAlarmInputContact OBJECT IDENTIFIER
	::= { tlpUpsAlarms 53 }

tlpUpsAlarmInputContact1 OBJECT-IDENTITY
	STATUS current
    DESCRIPTION
        "Input contact 1 in alarm."
	::= { tlpUpsAlarmInputContact 1 }

tlpUpsAlarmOutputContact OBJECT IDENTIFIER
	::= { tlpUpsAlarms 54 }

tlpUpsAlarmOutputContact1 OBJECT-IDENTITY
	STATUS current
    DESCRIPTION
        "Output contact 1 in alarm."
	::= { tlpUpsAlarmOutputContact 1 }

tlpUpsAlarmOutputContact2 OBJECT-IDENTITY
	STATUS current
    DESCRIPTION
        "Output contact 2 in alarm."
	::= { tlpUpsAlarmOutputContact 2 }

tlpUpsAlarmOutputContact3 OBJECT-IDENTITY
	STATUS current
    DESCRIPTION
        "Output contact 3 in alarm."
	::= { tlpUpsAlarmOutputContact 3 }

tlpUpsAlarmOutputContact4 OBJECT-IDENTITY
	STATUS current
    DESCRIPTION
        "Output contact 4 in alarm."
	::= { tlpUpsAlarmOutputContact 4 }

tlpUpsAlarmOutputContact5 OBJECT-IDENTITY
	STATUS current
    DESCRIPTION
        "Output contact 5 in alarm."
	::= { tlpUpsAlarmOutputContact 5 }

tlpUpsAlarmOutputContact6 OBJECT-IDENTITY
	STATUS current
    DESCRIPTION
        "Output contact 6 in alarm."
	::= { tlpUpsAlarmOutputContact 6 }

tlpPduAlarmLoadLevelAboveThreshold OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The load level is above the designated threshold."
    ::= { tlpPduAlarms 1 }

tlpPduAlarmInputBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "An input condition is out of tolerance."
    ::= { tlpPduAlarms 2 }

tlpPduAlarmOutputBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "An output condition (other than OutputOverload) is  out of
        tolerance."
    ::= { tlpPduAlarms 3 }

tlpPduAlarmOutputOverload OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output load exceeds the pdu output capacity."
    ::= { tlpPduAlarms 4 }

tlpPduAlarmOutputOff OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output of the pdu is in the off state."
    ::= { tlpPduAlarms 5 }

tlpPduAlarmLoadOff OBJECT IDENTIFIER
    ::= { tlpPduAlarms 6 }

tlpPduAlarmLoadOff01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 1 is off."
    ::= { tlpPduAlarmLoadOff 1 }

tlpPduAlarmLoadOff02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 2 is off."
    ::= { tlpPduAlarmLoadOff 2 }

tlpPduAlarmLoadOff03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 3 is off."
    ::= { tlpPduAlarmLoadOff 3 }

tlpPduAlarmLoadOff04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 4 is off."
    ::= { tlpPduAlarmLoadOff 4 }

tlpPduAlarmLoadOff05 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 5 is off."
    ::= { tlpPduAlarmLoadOff 5 }

tlpPduAlarmLoadOff06 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 6 is off."
    ::= { tlpPduAlarmLoadOff 6 }

tlpPduAlarmLoadOff07 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 7 is off."
    ::= { tlpPduAlarmLoadOff 7 }

tlpPduAlarmLoadOff08 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 8 is off."
    ::= { tlpPduAlarmLoadOff 8 }

tlpPduAlarmLoadOff09 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 9 is off."
    ::= { tlpPduAlarmLoadOff 9 }

tlpPduAlarmLoadOff10 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 10 is off."
    ::= { tlpPduAlarmLoadOff 10 }

tlpPduAlarmLoadOff11 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 11 is off."
    ::= { tlpPduAlarmLoadOff 11 }

tlpPduAlarmLoadOff12 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 12 is off."
    ::= { tlpPduAlarmLoadOff 12 }

tlpPduAlarmLoadOff13 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 13 is off."
    ::= { tlpPduAlarmLoadOff 13 }

tlpPduAlarmLoadOff14 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 14 is off."
    ::= { tlpPduAlarmLoadOff 14 }

tlpPduAlarmLoadOff15 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 15 is off."
    ::= { tlpPduAlarmLoadOff 15 }

tlpPduAlarmLoadOff16 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 16 is off."
    ::= { tlpPduAlarmLoadOff 16 }

tlpPduAlarmLoadOff17 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 17 is off."
    ::= { tlpPduAlarmLoadOff 17 }

tlpPduAlarmLoadOff18 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 18 is off."
    ::= { tlpPduAlarmLoadOff 18 }

tlpPduAlarmLoadOff19 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 19 is off."
    ::= { tlpPduAlarmLoadOff 19 }

tlpPduAlarmLoadOff20 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 20 is off."
    ::= { tlpPduAlarmLoadOff 20 }

tlpPduAlarmLoadOff21 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 21 is off."
    ::= { tlpPduAlarmLoadOff 21 }

tlpPduAlarmLoadOff22 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 22 is off."
    ::= { tlpPduAlarmLoadOff 22 }

tlpPduAlarmLoadOff23 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 23 is off."
    ::= { tlpPduAlarmLoadOff 23 }

tlpPduAlarmLoadOff24 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 24 is off."
    ::= { tlpPduAlarmLoadOff 24 }

tlpPduAlarmLoadOff25 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 25 is off."
    ::= { tlpPduAlarmLoadOff 25 }

tlpPduAlarmLoadOff26 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 26 is off."
    ::= { tlpPduAlarmLoadOff 26 }

tlpPduAlarmLoadOff27 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 27 is off."
    ::= { tlpPduAlarmLoadOff 27 }

tlpPduAlarmLoadOff28 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 28 is off."
    ::= { tlpPduAlarmLoadOff 28 }

tlpPduAlarmLoadOff29 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 29 is off."
    ::= { tlpPduAlarmLoadOff 29 }

tlpPduAlarmLoadOff30 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 30 is off."
    ::= { tlpPduAlarmLoadOff 30 }

tlpPduAlarmLoadOff31 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 31 is off."
    ::= { tlpPduAlarmLoadOff 31 }

tlpPduAlarmLoadOff32 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 32 is off."
    ::= { tlpPduAlarmLoadOff 32 }

tlpPduAlarmLoadOff33 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 33 is off."
    ::= { tlpPduAlarmLoadOff 33 }

tlpPduAlarmLoadOff34 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 34 is off."
    ::= { tlpPduAlarmLoadOff 34 }

tlpPduAlarmLoadOff35 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 35 is off."
    ::= { tlpPduAlarmLoadOff 35 }

tlpPduAlarmLoadOff36 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 36 is off."
    ::= { tlpPduAlarmLoadOff 36 }

tlpPduAlarmLoadOff37 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 37 is off."
    ::= { tlpPduAlarmLoadOff 37 }

tlpPduAlarmLoadOff38 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 38 is off."
    ::= { tlpPduAlarmLoadOff 38 }

tlpPduAlarmLoadOff39 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 39 is off."
    ::= { tlpPduAlarmLoadOff 39 }

tlpPduAlarmLoadOff40 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 40 is off."
    ::= { tlpPduAlarmLoadOff 40 }

tlpPduAlarmCircuitBreakerOpen OBJECT IDENTIFIER
    ::= { tlpPduAlarms 7 }

tlpPduAlarmCircuitBreakerOpen01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 1 is open."
    ::= { tlpPduAlarmCircuitBreakerOpen 1 }

tlpPduAlarmCircuitBreakerOpen02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 2 is open."
    ::= { tlpPduAlarmCircuitBreakerOpen 2 }

tlpPduAlarmCircuitBreakerOpen03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 3 is open."
    ::= { tlpPduAlarmCircuitBreakerOpen 3 }

tlpPduAlarmCircuitBreakerOpen04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 4 is open."
    ::= { tlpPduAlarmCircuitBreakerOpen 4 }

tlpPduAlarmCircuitBreakerOpen05 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 5 is open."
    ::= { tlpPduAlarmCircuitBreakerOpen 5 }

tlpPduAlarmCircuitBreakerOpen06 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 6 is open."
    ::= { tlpPduAlarmCircuitBreakerOpen 6 }

tlpPduAlarmCurrentAboveThreshold OBJECT IDENTIFIER
    ::= { tlpPduAlarms 8 }

tlpPduAlarmCurrentAboveThreshold1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 1 exceeds load limits."
    ::= { tlpPduAlarmCurrentAboveThreshold 1 }

tlpPduAlarmCurrentAboveThreshold2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 2 exceeds load limits."
    ::= { tlpPduAlarmCurrentAboveThreshold 2 }

tlpPduAlarmCurrentAboveThreshold3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 3 exceeds load limits."
    ::= { tlpPduAlarmCurrentAboveThreshold 3 }

tlpPduAlarmLoadsNotAllOn OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "One of more loads are not on."
    ::= { tlpPduAlarms 9 }

tlpPduAlarmLoadLevelBelowThreshold OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The load level is below the designated threshold."
    ::= { tlpPduAlarms 10 }

tlpPduAlarmCurrentBelowThreshold OBJECT IDENTIFIER
    ::= { tlpPduAlarms 11 }

tlpPduAlarmCurrentBelowThreshold1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 1 below load limits."
    ::= { tlpPduAlarmCurrentBelowThreshold 1 }

tlpPduAlarmCurrentBelowThreshold2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 2 below load limits."
    ::= { tlpPduAlarmCurrentBelowThreshold 2 }

tlpPduAlarmCurrentBelowThreshold3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on phase 3 below load limits."
    ::= { tlpPduAlarmCurrentBelowThreshold 3 }

tlpEnvAlarmTemperatureBeyondLimits OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The temperature exceeds limits."
    ::= { tlpEnvAlarms 1 }

tlpEnvAlarmHumidityBeyondLimits OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The humidity exceeds limits."
    ::= { tlpEnvAlarms 2 }

tlpEnvAlarmInputContact OBJECT IDENTIFIER
    ::= { tlpEnvAlarms 3 }

tlpEnvAlarmInputContact01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Input contact 1 is in alarm."
    ::= { tlpEnvAlarmInputContact 1 }

tlpEnvAlarmInputContact02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Input contact 2 is in alarm."
    ::= { tlpEnvAlarmInputContact 2 }

tlpEnvAlarmInputContact03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Input contact 3 is in alarm."
    ::= { tlpEnvAlarmInputContact 3 }

tlpEnvAlarmInputContact04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Input contact 4 is in alarm."
    ::= { tlpEnvAlarmInputContact 4 }

tlpEnvAlarmOutputContact OBJECT IDENTIFIER
    ::= { tlpEnvAlarms 4 }

tlpEnvAlarmOutputContact01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Output contact 1 is in alarm."
    ::= { tlpEnvAlarmOutputContact 1 }

tlpEnvAlarmOutputContact02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Output contact 2 is in alarm."
    ::= { tlpEnvAlarmOutputContact 2 }

tlpEnvAlarmOutputContact03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Output contact 3 is in alarm."
    ::= { tlpEnvAlarmOutputContact 3 }

tlpEnvAlarmOutputContact04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Output contact 4 is in alarm."
    ::= { tlpEnvAlarmOutputContact 4 }

tlpAtsAlarmOutage OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 1 }

tlpAtsAlarmSource1Outage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Input on source 1 is not present. For atss with the designation
        of primary and secondary, this alarm denotes input on the primary
        source is not present."
    ::= { tlpAtsAlarmOutage 1 }

tlpAtsAlarmSource2Outage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Input on source 2 is not present. For atss with the designation
        of primary and secondary, this alarm denotes input on the secondary
        source is not present."
    ::= { tlpAtsAlarmOutage 2 }

tlpAtsAlarmTemperature OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 2 }

tlpAtsAlarmSystemTemperature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The system temperature is out of tolerance."
    ::= { tlpAtsAlarmTemperature 1 }

tlpAtsAlarmSource1Temperature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The source 1 temperature is out of tolerance."
    ::= { tlpAtsAlarmTemperature 2 }

tlpAtsAlarmSource2Temperature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The source 2 temperature is out of tolerance."
    ::= { tlpAtsAlarmTemperature 3 }

tlpAtsAlarmLoadLevelAboveThreshold OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The load level is above the designated threshold."
    ::= { tlpAtsAlarms 3 }

tlpAtsAlarmInputBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "An input condition is out of tolerance."
    ::= { tlpAtsAlarms 4 }

tlpAtsAlarmOutputBad OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "An output condition (other than OutputOverload) is  out of
        tolerance."
    ::= { tlpAtsAlarms 5 }

tlpAtsAlarmOutputOverload OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output load exceeds the ats output capacity."
    ::= { tlpAtsAlarms 6 }

tlpAtsAlarmOutputOff OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output of the ats is in the off state."
    ::= { tlpAtsAlarms 7 }

tlpAtsAlarmLoadOff OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 8 }

tlpAtsAlarmLoadOff01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 1 is off."
    ::= { tlpAtsAlarmLoadOff 1 }

tlpAtsAlarmLoadOff02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 2 is off."
    ::= { tlpAtsAlarmLoadOff 2 }

tlpAtsAlarmLoadOff03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 3 is off."
    ::= { tlpAtsAlarmLoadOff 3 }

tlpAtsAlarmLoadOff04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 4 is off."
    ::= { tlpAtsAlarmLoadOff 4 }

tlpAtsAlarmLoadOff05 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 5 is off."
    ::= { tlpAtsAlarmLoadOff 5 }

tlpAtsAlarmLoadOff06 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 6 is off."
    ::= { tlpAtsAlarmLoadOff 6 }

tlpAtsAlarmLoadOff07 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 7 is off."
    ::= { tlpAtsAlarmLoadOff 7 }

tlpAtsAlarmLoadOff08 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 8 is off."
    ::= { tlpAtsAlarmLoadOff 8 }

tlpAtsAlarmLoadOff09 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 9 is off."
    ::= { tlpAtsAlarmLoadOff 9 }

tlpAtsAlarmLoadOff10 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 10 is off."
    ::= { tlpAtsAlarmLoadOff 10 }

tlpAtsAlarmLoadOff11 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 11 is off."
    ::= { tlpAtsAlarmLoadOff 11 }

tlpAtsAlarmLoadOff12 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 12 is off."
    ::= { tlpAtsAlarmLoadOff 12 }

tlpAtsAlarmLoadOff13 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 13 is off."
    ::= { tlpAtsAlarmLoadOff 13 }

tlpAtsAlarmLoadOff14 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 14 is off."
    ::= { tlpAtsAlarmLoadOff 14 }

tlpAtsAlarmLoadOff15 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 15 is off."
    ::= { tlpAtsAlarmLoadOff 15 }

tlpAtsAlarmLoadOff16 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 16 is off."
    ::= { tlpAtsAlarmLoadOff 16 }

tlpAtsAlarmLoadOff17 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 17 is off."
    ::= { tlpAtsAlarmLoadOff 17 }

tlpAtsAlarmLoadOff18 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 18 is off."
    ::= { tlpAtsAlarmLoadOff 18 }

tlpAtsAlarmLoadOff19 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 19 is off."
    ::= { tlpAtsAlarmLoadOff 19 }

tlpAtsAlarmLoadOff20 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 20 is off."
    ::= { tlpAtsAlarmLoadOff 20 }

tlpAtsAlarmLoadOff21 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 21 is off."
    ::= { tlpAtsAlarmLoadOff 21 }

tlpAtsAlarmLoadOff22 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 22 is off."
    ::= { tlpAtsAlarmLoadOff 22 }

tlpAtsAlarmLoadOff23 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 23 is off."
    ::= { tlpAtsAlarmLoadOff 23 }

tlpAtsAlarmLoadOff24 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 24 is off."
    ::= { tlpAtsAlarmLoadOff 24 }

tlpAtsAlarmLoadOff25 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 25 is off."
    ::= { tlpAtsAlarmLoadOff 25 }

tlpAtsAlarmLoadOff26 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 26 is off."
    ::= { tlpAtsAlarmLoadOff 26 }

tlpAtsAlarmLoadOff27 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 27 is off."
    ::= { tlpAtsAlarmLoadOff 27 }

tlpAtsAlarmLoadOff28 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 28 is off."
    ::= { tlpAtsAlarmLoadOff 28 }

tlpAtsAlarmLoadOff29 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 29 is off."
    ::= { tlpAtsAlarmLoadOff 29 }

tlpAtsAlarmLoadOff30 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 30 is off."
    ::= { tlpAtsAlarmLoadOff 30 }

tlpAtsAlarmLoadOff31 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 31 is off."
    ::= { tlpAtsAlarmLoadOff 31 }

tlpAtsAlarmLoadOff32 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 32 is off."
    ::= { tlpAtsAlarmLoadOff 32 }

tlpAtsAlarmLoadOff33 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 33 is off."
    ::= { tlpAtsAlarmLoadOff 33 }

tlpAtsAlarmLoadOff34 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 34 is off."
    ::= { tlpAtsAlarmLoadOff 34 }

tlpAtsAlarmLoadOff35 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 35 is off."
    ::= { tlpAtsAlarmLoadOff 35 }

tlpAtsAlarmLoadOff36 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 36 is off."
    ::= { tlpAtsAlarmLoadOff 36 }

tlpAtsAlarmLoadOff37 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 37 is off."
    ::= { tlpAtsAlarmLoadOff 37 }

tlpAtsAlarmLoadOff38 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 38 is off."
    ::= { tlpAtsAlarmLoadOff 38 }

tlpAtsAlarmLoadOff39 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 39 is off."
    ::= { tlpAtsAlarmLoadOff 39 }

tlpAtsAlarmLoadOff40 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Load 40 is off."
    ::= { tlpAtsAlarmLoadOff 40 }

tlpAtsAlarmCircuitBreakerOpen OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 9 }

tlpAtsAlarmCircuitBreakerOpen01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 1 is open."
    ::= { tlpAtsAlarmCircuitBreakerOpen 1 }

tlpAtsAlarmCircuitBreakerOpen02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 2 is open."
    ::= { tlpAtsAlarmCircuitBreakerOpen 2 }

tlpAtsAlarmCircuitBreakerOpen03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 3 is open."
    ::= { tlpAtsAlarmCircuitBreakerOpen 3 }

tlpAtsAlarmCircuitBreakerOpen04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 4 is open."
    ::= { tlpAtsAlarmCircuitBreakerOpen 4 }

tlpAtsAlarmCircuitBreakerOpen05 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 5 is open."
    ::= { tlpAtsAlarmCircuitBreakerOpen 5 }

tlpAtsAlarmCircuitBreakerOpen06 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Circuit breaker 6 is open."
    ::= { tlpAtsAlarmCircuitBreakerOpen 6 }

tlpAtsAlarmCurrentAboveThreshold OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 10 }

tlpAtsAlarmCurrentAboveThresholdA1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 1 phase 1 exceeds load limits."
    ::= { tlpAtsAlarmCurrentAboveThreshold 1 }

tlpAtsAlarmCurrentAboveThresholdA2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 1 phase 2 exceeds load limits."
    ::= { tlpAtsAlarmCurrentAboveThreshold 2 }

tlpAtsAlarmCurrentAboveThresholdA3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 1 phase 3 exceeds load limits."
    ::= { tlpAtsAlarmCurrentAboveThreshold 3 }

tlpAtsAlarmCurrentAboveThresholdB1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 2 phase 1 exceeds load limits."
    ::= { tlpAtsAlarmCurrentAboveThreshold 4 }

tlpAtsAlarmCurrentAboveThresholdB2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 2 phase 2 exceeds load limits."
    ::= { tlpAtsAlarmCurrentAboveThreshold 5 }

tlpAtsAlarmCurrentAboveThresholdB3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 2 phase 3 exceeds load limits."
    ::= { tlpAtsAlarmCurrentAboveThreshold 6 }

tlpAtsAlarmLoadsNotAllOn OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "One of more loads are not on."
    ::= { tlpAtsAlarms 11 }

tlpAtsAlarmGeneralFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A general fault in the ats has been detected."
    ::= { tlpAtsAlarms 12 }

tlpAtsAlarmVoltage OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 13 }

tlpAtsAlarmOverVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The input voltage is out of tolerance."
    ::= { tlpAtsAlarmVoltage 1 }

tlpAtsAlarmSource1OverVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The voltage on source 1 is out of tolerance."
    ::= { tlpAtsAlarmVoltage 2 }

tlpAtsAlarmSource2OverVoltage OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The voltage on source 2 is out of tolerance."
    ::= { tlpAtsAlarmVoltage 3 }

tlpAtsAlarmFrequency OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 14 }

tlpAtsAlarmSource1InvalidFrequency OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The voltage on source 1 is out of tolerance."
    ::= { tlpAtsAlarmFrequency 1 }

tlpAtsAlarmSource2InvalidFrequency OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The voltage on source 2 is out of tolerance."
    ::= { tlpAtsAlarmFrequency 2 }

tlpAtsAlarmLoadLevelBelowThreshold OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The load level is below the designated threshold."
    ::= {  tlpAtsAlarms 15 }

tlpAtsAlarmCurrentBelowThreshold OBJECT IDENTIFIER
    ::= { tlpAtsAlarms 16 }

tlpAtsAlarmCurrentBelowThresholdA1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 1 phase 1 below load limits."
    ::= { tlpAtsAlarmCurrentBelowThreshold 1 }

tlpAtsAlarmCurrentBelowThresholdA2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 1 phase 2 below load limits."
    ::= { tlpAtsAlarmCurrentBelowThreshold 2 }

tlpAtsAlarmCurrentBelowThresholdA3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 1 phase 3 below load limits."
    ::= { tlpAtsAlarmCurrentBelowThreshold 3 }

tlpAtsAlarmCurrentBelowThresholdB1 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 2 phase 1 below load limits."
    ::= { tlpAtsAlarmCurrentBelowThreshold 4 }

tlpAtsAlarmCurrentBelowThresholdB2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 2 phase 2 below load limits."
    ::= { tlpAtsAlarmCurrentBelowThreshold 5 }

tlpAtsAlarmCurrentBelowThresholdB3 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The output current on input source 2 phase 3 below load limits."
    ::= { tlpAtsAlarmCurrentBelowThreshold 6 }

tlpCoolingAlarmSupplyAirSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The supply air sensor is in fault."
    ::= { tlpCoolingAlarms 1 }

tlpCoolingAlarmReturnAirSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The return air sensor is in fault."
    ::= { tlpCoolingAlarms 2 }

tlpCoolingAlarmCondenserInletAirSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The condenser inlet air sensor is in fault."
    ::= { tlpCoolingAlarms 3 }

tlpCoolingAlarmCondenserOutletAirSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The condenser outlet air sensor is in fault."
    ::= { tlpCoolingAlarms 4 }

tlpCoolingAlarmSuctionTemperatureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The suction temperature sensor is in fault."
    ::= { tlpCoolingAlarms 5 }

tlpCoolingAlarmEvaporatorTemperatureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The evaporator temperature sensor is in fault."
    ::= { tlpCoolingAlarms 6 }

tlpCoolingAlarmAirFilterClogged OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The air filter is clogged."
    ::= { tlpCoolingAlarms 7 }

tlpCoolingAlarmAirFilterRunHoursViolation OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The air filter run hours was violated."
    ::= { tlpCoolingAlarms 8 }

tlpCoolingAlarmSuctionPressureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The suction pressure sensor is in fault."
    ::= { tlpCoolingAlarms 9 }

tlpCoolingAlarmInverterCommunicationsFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The inverter communications is in fault."
    ::= { tlpCoolingAlarms 10 }

tlpCoolingAlarmRemoteShutdownViaInputContact OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "A remote shutdown was triggerd by an input contact."
    ::= { tlpCoolingAlarms 11 }

tlpCoolingAlarmCondensatePumpFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The condensate pump is in fault."
    ::= { tlpCoolingAlarms 12 }

tlpCoolingAlarmLowRefrigerantStartupFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The low refrigerant startup is in fault."
    ::= { tlpCoolingAlarms 13 }

tlpCoolingAlarmCondenserFanFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The condenser fan is in fault."
    ::= { tlpCoolingAlarms 14 }

tlpCoolingAlarmCondenserFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The condenser has failed."
    ::= { tlpCoolingAlarms 15 }

tlpCoolingAlarmEvaporatorCoolingFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The evaporator cooling is in fault."
    ::= { tlpCoolingAlarms 16 }

tlpCoolingAlarmReturnAirTempHigh OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The return air temperature is high."
    ::= { tlpCoolingAlarms 17 }

tlpCoolingAlarmSupplyAirTempHigh OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The supply air temperature is high."
    ::= { tlpCoolingAlarms 18 }

tlpCoolingAlarmEvaporatorFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The evaporator has failed."
    ::= { tlpCoolingAlarms 19 }

tlpCoolingAlarmEvaporatorFreezeUp OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The evaporator is frozen."
    ::= { tlpCoolingAlarms 20 }

tlpCoolingAlarmDischargePressureHigh OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The discharge pressure is high."
    ::= { tlpCoolingAlarms 21 }

tlpCoolingAlarmPressureGaugeFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The pressure gauge has failed."
    ::= { tlpCoolingAlarms 22 }

tlpCoolingAlarmDischargePressurePersistentHigh OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The discharge pressure is persistently high."
    ::= { tlpCoolingAlarms 23 }

tlpCoolingAlarmSuctionPressureLowStartFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Cannot start due to low suction pressure."
    ::= { tlpCoolingAlarms 24 }

tlpCoolingAlarmSuctionPressureLow OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The suction pressure is low."
    ::= { tlpCoolingAlarms 25 }

tlpCoolingAlarmSuctionPressurePersistentLow OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The suction pressure is persistently low."
    ::= { tlpCoolingAlarms 26 }

tlpCoolingAlarmStartupLinePressureImbalance OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The startup line pressure is in imbalance."
    ::= { tlpCoolingAlarms 27 }

tlpCoolingAlarmCompressorFailure OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The compressor has failed."
    ::= { tlpCoolingAlarms 28 }

tlpCoolingAlarmCurrentLimit OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The current is out of range."
    ::= { tlpCoolingAlarms 29 }

tlpCoolingAlarmWaterLeak OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Water is leaking."
    ::= { tlpCoolingAlarms 30 }

tlpCoolingAlarmFanUnderCurrent OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The fan is under current."
    ::= { tlpCoolingAlarms 31 }

tlpCoolingAlarmFanOverCurrent OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The fan is over current."
    ::= { tlpCoolingAlarms 32 }

tlpCoolingAlarmDischargePressureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The discharge pressure sensor is in fault."
    ::= { tlpCoolingAlarms 33 }

tlpCoolingAlarmWaterFull OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Water is Full."
    ::= { tlpCoolingAlarms 34 }

tlpCoolingAlarmAutoCoolingOn OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Automatic cooling was activated to lower the temperature below the
        remote set point."
    ::= { tlpCoolingAlarms 35 }

tlpCoolingAlarmPowerButtonPressed OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Power button was pressed on the front panel."
    ::= { tlpCoolingAlarms 36 }

tlpCoolingAlarmDisconnectedFromDevice OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The device communications is disconnected."
    ::= { tlpCoolingAlarms 37 }

tlpCoolingAlarmAmbientTemperatureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Ambient temperature sensor is in fault."
    ::= { tlpCoolingAlarms 38 }

tlpCoolingAlarmRemoteTemperatureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Remote temperature sensor is in fault."
    ::= { tlpCoolingAlarms 39 }

tlpCoolingAlarmLowPressureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Low pressure sensor is in fault."
    ::= { tlpCoolingAlarms 40 }

tlpCoolingAlarmHighPressureSensorFault OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "High pressure sensor is in fault."
    ::= { tlpCoolingAlarms 41 }

tlpCoolingAlarmSentrytimerTimeout OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sentry timer time out."
    ::= { tlpCoolingAlarms 42 }

tlpAutoProbeAlarms OBJECT IDENTIFIER
    ::= { tlpAgentAlarms 1 }

tlpAutoProbeAlarm01 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 1 had failed"
    ::= { tlpAutoProbeAlarms 1 }

tlpAutoProbeAlarm02 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 2 had failed"
    ::= { tlpAutoProbeAlarms 2 }

tlpAutoProbeAlarm03 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 3 had failed"
    ::= { tlpAutoProbeAlarms 3 }

tlpAutoProbeAlarm04 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 4 had failed"
    ::= { tlpAutoProbeAlarms 4 }

tlpAutoProbeAlarm05 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 5 had failed"
    ::= { tlpAutoProbeAlarms 5 }

tlpAutoProbeAlarm06 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 6 had failed"
    ::= { tlpAutoProbeAlarms 6 }

tlpAutoProbeAlarm07 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 7 had failed"
    ::= { tlpAutoProbeAlarms 7 }

tlpAutoProbeAlarm08 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 8 had failed"
    ::= { tlpAutoProbeAlarms 8 }


tlpAutoProbeAlarm09 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 9 had failed"
    ::= { tlpAutoProbeAlarms 9 }

tlpAutoProbeAlarm10 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 10 had failed"
    ::= { tlpAutoProbeAlarms 10 }

tlpAutoProbeAlarm11 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 11 had failed"
    ::= { tlpAutoProbeAlarms 11 }

tlpAutoProbeAlarm12 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 12 had failed"
    ::= { tlpAutoProbeAlarms 12 }

tlpAutoProbeAlarm13 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 13 had failed"
    ::= { tlpAutoProbeAlarms 13 }

tlpAutoProbeAlarm14 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 14 had failed"
    ::= { tlpAutoProbeAlarms 14 }

tlpAutoProbeAlarm15 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 15 had failed"
    ::= { tlpAutoProbeAlarms 15 }

tlpAutoProbeAlarm16 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 16 had failed"
    ::= { tlpAutoProbeAlarms 16 }

tlpAutoProbeAlarm17 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 17 had failed"
    ::= { tlpAutoProbeAlarms 17 }

tlpAutoProbeAlarm18 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 18 had failed"
    ::= { tlpAutoProbeAlarms 18 }

tlpAutoProbeAlarm19 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 19 had failed"
    ::= { tlpAutoProbeAlarms 19 }

tlpAutoProbeAlarm20 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 20 had failed"
    ::= { tlpAutoProbeAlarms 20 }

tlpAutoProbeAlarm21 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 21 had failed"
    ::= { tlpAutoProbeAlarms 21 }

tlpAutoProbeAlarm22 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 22 had failed"
    ::= { tlpAutoProbeAlarms 22 }

tlpAutoProbeAlarm23 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 23 had failed"
    ::= { tlpAutoProbeAlarms 23 }

tlpAutoProbeAlarm24 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 24 had failed"
    ::= { tlpAutoProbeAlarms 24 }

tlpAutoProbeAlarm25 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 25 had failed"
    ::= { tlpAutoProbeAlarms 25 }

tlpAutoProbeAlarm26 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 26 had failed"
    ::= { tlpAutoProbeAlarms 26 }

tlpAutoProbeAlarm27 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 27 had failed"
    ::= { tlpAutoProbeAlarms 27 }

tlpAutoProbeAlarm28 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 28 had failed"
    ::= { tlpAutoProbeAlarms 28 }

tlpAutoProbeAlarm29 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 29 had failed"
    ::= { tlpAutoProbeAlarms 29 }

tlpAutoProbeAlarm30 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 30 had failed"
    ::= { tlpAutoProbeAlarms 30 }

tlpAutoProbeAlarm31 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 31 had failed"
    ::= { tlpAutoProbeAlarms 31 }

tlpAutoProbeAlarm32 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 32 had failed"
    ::= { tlpAutoProbeAlarms 32 }

tlpAutoProbeAlarm33 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 33 had failed"
    ::= { tlpAutoProbeAlarms 33 }

tlpAutoProbeAlarm34 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 34 had failed"
    ::= { tlpAutoProbeAlarms 34 }

tlpAutoProbeAlarm35 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 35 had failed"
    ::= { tlpAutoProbeAlarms 35 }

tlpAutoProbeAlarm36 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 36 had failed"
    ::= { tlpAutoProbeAlarms 36 }

tlpAutoProbeAlarm37 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 37 had failed"
    ::= { tlpAutoProbeAlarms 37 }

tlpAutoProbeAlarm38 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 38 had failed"
    ::= { tlpAutoProbeAlarms 38 }

tlpAutoProbeAlarm39 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 39 had failed"
    ::= { tlpAutoProbeAlarms 39 }

tlpAutoProbeAlarm40 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 40 had failed"
    ::= { tlpAutoProbeAlarms 40 }

tlpAutoProbeAlarm41 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 41 had failed"
    ::= { tlpAutoProbeAlarms 41 }

tlpAutoProbeAlarm42 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 42 had failed"
    ::= { tlpAutoProbeAlarms 42 }

tlpAutoProbeAlarm43 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 43 had failed"
    ::= { tlpAutoProbeAlarms 43 }

tlpAutoProbeAlarm44 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 44 had failed"
    ::= { tlpAutoProbeAlarms 44 }

tlpAutoProbeAlarm45 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 45 had failed"
    ::= { tlpAutoProbeAlarms 45 }

tlpAutoProbeAlarm46 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 46 had failed"
    ::= { tlpAutoProbeAlarms 46 }

tlpAutoProbeAlarm47 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 47 had failed"
    ::= { tlpAutoProbeAlarms 47 }

tlpAutoProbeAlarm48 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 48 had failed"
    ::= { tlpAutoProbeAlarms 48 }

tlpAutoProbeAlarm49 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 49 had failed"
    ::= { tlpAutoProbeAlarms 49 }

tlpAutoProbeAlarm50 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 50 had failed"
    ::= { tlpAutoProbeAlarms 50 }

tlpAutoProbeAlarm51 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 51 had failed"
    ::= { tlpAutoProbeAlarms 51 }

tlpAutoProbeAlarm52 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 52 had failed"
    ::= { tlpAutoProbeAlarms 52 }

tlpAutoProbeAlarm53 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 53 had failed"
    ::= { tlpAutoProbeAlarms 53 }

tlpAutoProbeAlarm54 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 54 had failed"
    ::= { tlpAutoProbeAlarms 54 }

tlpAutoProbeAlarm55 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 55 had failed"
    ::= { tlpAutoProbeAlarms 55 }

tlpAutoProbeAlarm56 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 56 had failed"
    ::= { tlpAutoProbeAlarms 56 }

tlpAutoProbeAlarm57 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 57 had failed"
    ::= { tlpAutoProbeAlarms 57 }

tlpAutoProbeAlarm58 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 58 had failed"
    ::= { tlpAutoProbeAlarms 58 }

tlpAutoProbeAlarm59 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 59 had failed"
    ::= { tlpAutoProbeAlarms 59 }

tlpAutoProbeAlarm60 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 60 had failed"
    ::= { tlpAutoProbeAlarms 60 }

tlpAutoProbeAlarm61 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 61 had failed"
    ::= { tlpAutoProbeAlarms 61 }

tlpAutoProbeAlarm62 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 62 had failed"
    ::= { tlpAutoProbeAlarms 62 }

tlpAutoProbeAlarm63 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 63 had failed"
    ::= { tlpAutoProbeAlarms 63 }

tlpAutoProbeAlarm64 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Auto Probe 64 had failed"
    ::= { tlpAutoProbeAlarms 64 }
	tlpAlarmControlTable OBJECT-TYPE
    SYNTAX SEQUENCE OF TlpAlarmControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of alarm conditions supported for each device managed by this
        agent. This table contains zero, one, or many rows depending upon the
        number of available alarms managed by each device."
    ::= { tlpAlarmControl 1 }

tlpAlarmControlEntry OBJECT-TYPE
    SYNTAX TlpAlarmControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An entry containing details for a particular alarm condition."
    INDEX { tlpDeviceIndex, tlpAlarmControlIndex }
    ::= { tlpAlarmControlTable 1 }

TlpAlarmControlEntry ::= SEQUENCE {
    tlpAlarmControlIndex                Unsigned32,
    tlpAlarmControlDescr                OBJECT IDENTIFIER,
    tlpAlarmControlDetail               DisplayString,
    tlpAlarmControlSeverity             INTEGER }

tlpAlarmControlIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The table row index for this alarm control entry for this device. The
        index of any one row represents the index of the alarm entry within the
        agent system. As such, the index values for any one device is not
        guaranteed to start with 1 or be numerically sequential."
    ::= { tlpAlarmControlEntry 1 }

tlpAlarmControlDescr OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A reference of the alarm description object. The object referenced
        should not be accessible, but rather be used to provide a unique
        description for the alarm condition. The object referenced will be a
        well known alarm object associated with the device on which the alarm
        condition is active. This will be the same object description of an
        alarm when in alarm as referenced by tlpAlarmDescr."
    ::= { tlpAlarmControlEntry 2 }

tlpAlarmControlDetail OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A textual description of the alarm condition. This will be the same
        textual description of an alarm when in alarm as referenced by
        tlpAlarmDetail."
    ::= { tlpAlarmControlEntry 3 }

tlpAlarmControlSeverity OBJECT-TYPE
    SYNTAX INTEGER {
        critical(1),
        warning(2),
        info(3) }
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The severity of this alarm, which can be configured as desired for
        each alarm condition. This will be the same severity used when in alarm
        as referenced by tlpAlarmType."
    ::= { tlpAlarmControlEntry 4 }

-- notifications

tlpNotificationsAlarmEntryAdded NOTIFICATION-TYPE
    OBJECTS { tlpAlarmId, tlpAlarmDescr, tlpAlarmTime,
              tlpAlarmTableRef, tlpAlarmTableRowRef, tlpAlarmDetail,
              tlpAlarmType }
    STATUS current
    DESCRIPTION
        "This notification is sent each time an alarm condition is inserted into
        the device alarm table, tlpAlarmTable."
    ::= { tlpNotifications 1 }

tlpNotificationsAlarmEntryRemoved NOTIFICATION-TYPE
    OBJECTS { tlpAlarmId, tlpAlarmDescr, tlpAlarmTime,
              tlpAlarmTableRef, tlpAlarmTableRowRef, tlpAlarmDetail,
              tlpAlarmType }
    STATUS current
    DESCRIPTION
        "This notification is sent each time an alarm condition is removed from
        the device alarm table, tlpAlarmTable."
    ::= { tlpNotifications 2 }

tlpNotifySystemStartup NOTIFICATION-TYPE
    STATUS current
    DESCRIPTION
        "Used to inform another entity of an agent's startup. The varbinds that follow are:

        OBJ1      = tlpAgentType
        Obj1Value = The type of engine providing this data
        OBJ2      = tlpAgentVersion
        Obj2Value = The current version of the engine
        OBJ3      = tlpAgentMAC
        Obj3Value = The MAC address of the engine
        OBJ4      = tlpAgentSerialNum
        Obj4Value = The serial number of the web card [should be 0 on PC]

        --- if this trap is extended, place all static OIDs above this mark. all objects below
        --- the number of devices (tlpDeviceNumDevices) is explicit as per device for enumeration by
        --- the target receiver

        OBJ5      = tlpDeviceNumDevices
        Obj5Value = The number of devices attached

        --- The following objects will be replicated per each device:

        OBJ6      = The OID for an entry of tlpDeviceManufacturer, indexed by device
        Obj6Value = The device's manufacturer
        OBJ7      = The OID for an entry of tlpDeviceModel, indexed by device
        Obj7Value = The device's model name
        OBJ8      = The OID for an entry of tlpDeviceName, indexed by device
        Obj8Value = The device's display name
        OBJ9      = The OID for an entry of tlpDeviceLocation, indexed by device
        Obj9Value = The device's location
        OBJ10     = The OID for an entry of tlpDeviceRegion, indexed by device
        Obj10Value= The device's region
        OBJ11     = The OID for an entry of tlpDeviceProtocol, indexed by device
        Obj11Value= The device's protocol (display string format)
        "
    ::= { tlpNotifications 3 }

tlpNotifySystemShutdown NOTIFICATION-TYPE
    STATUS current
    DESCRIPTION
        "Used to inform another entity of an agent's shutdown. The varbinds that follow are:

        OBJ1      = tlpAgentType
        Obj1Value = The type of engine providing this data
        OBJ2      = tlpAgentVersion
        Obj2Value = The current version of the engine
        OBJ3      = tlpAgentMAC
        Obj3Value = The MAC address of the engine
        OBJ4      = tlpAgentSerialNum
        Obj4Value = The serial number of the web card [should be 0 on PC]

        --- if this trap is extended, place all static OIDs above this mark. all objects below
        --- the number of devices (tlpDeviceNumDevices) is explicit as per device for enumeration by
        --- the target receiver

        OBJ5      = tlpDeviceNumDevices
        Obj5Value = The number of devices attached

        --- The following objects will be replicated per each device:

        OBJ6      = The OID for an entry of tlpDeviceManufacturer, indexed by device
        Obj6Value = The device's manufacturer
        OBJ7      = The OID for an entry of tlpDeviceModel, indexed by device
        Obj7Value = The device's model name
        OBJ8      = The OID for an entry of tlpDeviceName, indexed by device
        Obj8Value = The device's display name
        OBJ9      = The OID for an entry of tlpDeviceLocation, indexed by device
        Obj9Value = The device's location
        OBJ10     = The OID for an entry of tlpDeviceRegion, indexed by device
        Obj10Value= The device's region
        OBJ11     = The OID for an entry of tlpDeviceProtocol, indexed by device
        Obj11Value= The device's protocol (display string format)
        "
    ::= { tlpNotifications 4 }

tlpNotifySystemUpdate NOTIFICATION-TYPE
  STATUS current
  DESCRIPTION
    "Used to inform another entity of an engine's startup. The varbinds that follow are:

    OBJ1      = tlpAgentType
    Obj1Value = The type of engine providing this data
    OBJ2      = tlpAgentVersion
    Obj2Value = The current version of the engine
    OBJ3      = tlpAgentMAC
    Obj3Value = The MAC address of the engine
    OBJ4      = tlpAgentSerialNum
    Obj4Value = The serial number of the web card [should be 0 on PC]

    --- if this trap is extended, place all static OIDs above this mark. all objects below
    --- the number of devices (tlpDeviceNumDevices) is explicit as per device for enumeration by
    --- the target receiver

    OBJ5      = tlpDeviceNumDevices
    Obj5Value = The number of devices attached

    --- The following objects will be replicated per each device:

    OBJ6      = The OID for an entry of tlpDeviceManufacturer, indexed by device
    Obj6Value = The device's manufacturer
    OBJ7      = The OID for an entry of tlpDeviceModel, indexed by device
    Obj7Value = The device's model name
    OBJ8      = The OID for an entry of tlpDeviceName, indexed by device
    Obj8Value = The device's display name
    OBJ9      = The OID for an entry of tlpDeviceLocation, indexed by device
    Obj9Value = The device's location
    OBJ10     = The OID for an entry of tlpDeviceRegion, indexed by device
    Obj10Value= The device's region
    OBJ11     = The OID for an entry of tlpDeviceProtocol, indexed by device
    Obj11Value= The device's protocol (display string format)
    "
  ::= { tlpNotifications 5 }

tlpNotifyTest NOTIFICATION-TYPE
  STATUS current
  DESCRIPTION
    "Used when performing a test of the system or an alert notification contact. The varbinds
    that follow are:

    OBJ1      = tlpAgentType
    Obj1Value = The type of engine providing this data
    OBJ2      = tlpAgentVersion
    Obj2Value = The current version of the engine
    OBJ3      = tlpAgentMAC
    Obj3Value = The MAC address of the engine
    OBJ4      = tlpAgentSerialNum
    Obj4Value = The serial number of the web card [should be 0 on PC]
    "
  ::= { tlpNotifications 6 }

tlpNotificationsAgent OBJECT IDENTIFIER ::= { tlpNotificationsEvent 1 }

tlpNotificationsDevice OBJECT IDENTIFIER ::= { tlpNotificationsEvent 2 }

tlpNotificationsDeviceInternalFirmwareFault NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device experiences an internal
        firmware failure."
    ::= { tlpNotificationsDevice 1 }

tlpNotificationsDeviceInternalHardwareFault NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device experiences an internal
        hardware failure."
    ::= { tlpNotificationsDevice 2 }

tlpNotificationsDeviceConfigurationChange NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device configuration changes."
    ::= { tlpNotificationsDevice 3 }

tlpNotificationsDeviceFirmwareUpdate NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device firmware is updated."
    ::= { tlpNotificationsDevice 4 }

tlpNotificationsDeviceMicrocontrollerReset NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device microcontroller resets."
    ::= { tlpNotificationsDevice 5 }

tlpNotificationsDeviceSystemInformation NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device sysytem information changes."
    ::= { tlpNotificationsDevice 6 }

tlpNotificationsDeviceNvrChecksumMismatch NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the NVR is corrupted."
    ::= { tlpNotificationsDevice 7 }

tlpNotificationsDeviceUsbCommunicationsEstablished NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device USB communications is established."
    ::= { tlpNotificationsDevice 8 }

tlpNotificationsDeviceUsbCommunicationsLost NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device USB communications is lost."
    ::= { tlpNotificationsDevice 9 }

tlpNotificationsDevicePowerSupplyShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to a power supply fault."
    ::= { tlpNotificationsDevice 10 }

tlpNotificationsDeviceOverLoadShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to an over load fault."
    ::= { tlpNotificationsDevice 11 }

tlpNotificationsDeviceCurrentLimitShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to a current limit fault."
    ::= { tlpNotificationsDevice 12 }

tlpNotificationsDeviceOverTemperatureShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to an over temperature fault."
    ::= { tlpNotificationsDevice 13 }

tlpNotificationsDeviceModeTransitionTimeout NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device timed out transitioning to a mode."
    ::= { tlpNotificationsDevice 14 }

tlpNotificationsDeviceFailedModeTransitionToIdle NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device failed a transition to idle mode."
    ::= { tlpNotificationsDevice 15 }

tlpNotificationsDeviceInternalCommunicationsLost NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device lost internal communications."
    ::= { tlpNotificationsDevice 16 }

tlpNotificationsDeviceAcknowledgeFaults NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when device faults are acknowledged."
    ::= { tlpNotificationsDevice 17 }

tlpNotificationsDeviceTransferToReboot NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when a device reboot occurred."
    ::= { tlpNotificationsDevice 18 }

tlpNotificationsDeviceTransferToWatchdogReboot NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when a device reboot occurred due to a watchdog fault."
    ::= { tlpNotificationsDevice 19 }

tlpNotificationsDeviceTransferToLatchedIdle NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device transferred to latched idle mode."
    ::= { tlpNotificationsDevice 20 }

tlpNotificationsDeviceTransferToLatchedStandby NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device transferred to latched standby mode."
    ::= {tlpNotificationsDevice 21 }

tlpNotificationsDeviceTransferToLatchedLine NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device transferred to latched line mode."
    ::= { tlpNotificationsDevice 22 }

tlpNotificationsDeviceFailSafeClockMonitorEngaged NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the fail safe clock monitor is engaged."
    ::= { tlpNotificationsDevice 23 }

tlpNotificationsDeviceTransferToIdle NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device transferred to idle mode."
    ::= { tlpNotificationsDevice 24 }

tlpNotificationsDeviceVoutUnderVoltageShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to a vout under voltage fault."
    ::= {  tlpNotificationsDevice 25 }

tlpNotificationsDeviceVoutOverVoltageShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to a vout over voltage fault."
    ::= { tlpNotificationsDevice 26 }

tlpNotificationsDeviceUnhandledFault NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports an unhandled fault."
    ::= { tlpNotificationsDevice  27 }

tlpNotificationsUps OBJECT IDENTIFIER ::= { tlpNotificationsEvent 3 }

tlpNotificationsUpsBatteryLowVoltageShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups reports a shutdown occurred due to a low battery voltage fault."
    ::= { tlpNotificationsUps 1 }

tlpNotificationsUpsBatteryLowPercentageShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups reports a shutdown occurred due to a low battery percentage fault."
    ::= { tlpNotificationsUps 2 }

tlpNotificationsUpsBatteryOverVoltageShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups reports a shutdown occurred due to a battery over voltage fault."
    ::= { tlpNotificationsUps 3 }

tlpNotificationsUpsBatteryOverCurrentShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups reports a shutdown occurred due to a battery over current fault."
    ::= { tlpNotificationsUps 4 }

tlpNotificationsUpsBatteryUnknownTypeShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups reports a shutdown occurred due to an unknown battery type fault."
    ::= { tlpNotificationsUps 5 }

tlpNotificationsUpsBatteryConfigurationMismatch NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups battery configuration is mismatched."
    ::= { tlpNotificationsUps 6 }

tlpNotificationsUpsBatteryUnderVoltageShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups reports a shutdown occurred due to a battery under voltage fault."
    ::= { tlpNotificationsUps 7 }

tlpNotificationsUpsSlaBatteryOverTemperatureShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the ups reports a shutdown occurred due to an SLA battery over temperature fault."
    ::= { tlpNotificationsUps 8 }

tlpNotificationsUpsBatteryInstallation NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when a battery installation occurs."
    ::= { tlpNotificationsUps 9 }

tlpNotificationsUpsChargerBulkToAcceptance NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when ups battery charging."
    ::= { tlpNotificationsUps 10 }

tlpNotificationsUpsChargerAcceptanceToFloat NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when ups battery fully charged."
    ::= { tlpNotificationsUps 11 }

tlpNotificationsUpsChargerFloatToBulk NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when ups battery not fully charged."
    ::= { tlpNotificationsUps 12 }

tlpNotificationsUpsTransferToSelftest NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when a self test is initiated."
    ::= { tlpNotificationsUps 13 }

tlpNotificationsUpsTransferToEconomy NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device transferred to economy mode."
    ::= { tlpNotificationsUps 14 }

tlpNotificationsUpsLineConnectRelayFaultShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to line connect relay fault."
    ::= { tlpNotificationsUps 15 }

tlpNotificationsUpsTransferToFaultBypass NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device transferred to fault bypass mode."
    ::= { tlpNotificationsUps 16 }

tlpNotificationsUpsBypassPowerDistributionModuleDisconnected NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when bypass power distribution module is disconnected."
    ::= { tlpNotificationsUps 17 }

tlpNotificationsUpsManualBypassActivatedFromUnsupportedMode NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when manual bypass activated from an unsupported UPS mode."
    ::= { tlpNotificationsUps 18 }

tlpNotificationsUpsOnBatteryTimeoutShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to on battery timeout."
    ::= { tlpNotificationsUps 19 }

tlpNotificationsUpsLowBatteryPercentageShutdown NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the device reports a shutdown occurred due to low battery percentage."
    ::= { tlpNotificationsUps 20 }

tlpNotificationsPdu OBJECT IDENTIFIER ::= { tlpNotificationsEvent 4 }

tlpNotificationsEnvirosense OBJECT IDENTIFIER ::= { tlpNotificationsEvent 5 }

tlpNotificationsAts OBJECT IDENTIFIER ::= { tlpNotificationsEvent 6 }

tlpNotificationsAtsSource1NotAvailable NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when input on source one is not available.
        For atss with the designation of primary and secondary, this
        notification denotes input on the primary source is not available."
    ::= { tlpNotificationsAts 1 }

tlpNotificationsAtsSource2NotAvailable NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when input on source two is not available.
        For atss with the designation of primary and secondary, this
        notification denotes input on the secondary source is not available."
    ::= { tlpNotificationsAts 2 }

tlpNotificationsAtsLockdownToSource1 NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent upon a transfer circuit fault causing the
        device to be locked on source 1."
    ::= { tlpNotificationsAts 3 }

tlpNotificationsAtsLockdownToSource2 NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent upon a transfer circuit fault causing the
        device to be locked on source 2."
    ::= { tlpNotificationsAts 4 }

tlpNotificationsAtsSource1Available NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when source 1 is available and ready for
        use."
    ::= { tlpNotificationsAts 5 }

tlpNotificationsAtsSource2Available NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when source 2 is available and ready for
        use."
    ::= { tlpNotificationsAts 6 }

tlpNotificationsAtsSource1Brownout NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when a brownout condition occurred on source 1."
    ::= { tlpNotificationsAts 7 }

tlpNotificationsAtsSource2Brownout NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when a brownout condition occurred on source 2."
    ::= { tlpNotificationsAts 8 }

tlpNotificationsAtsSource1VoltageDrop NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the voltage on source 1 drops below a
        usable state."
    ::= { tlpNotificationsAts 9 }

tlpNotificationsAtsSource2VoltageDrop NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the voltage on source 2 drops below a
        usable state."
    ::= { tlpNotificationsAts 10 }

tlpNotificationsAtsTimedReturnToSource1 NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the active input source returns to
        source 1 from source 2 after a delay time expires."
    ::= { tlpNotificationsAts 11 }

tlpNotificationsAtsTimedReturnToSource2 NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the active input source returns to
        source 2 from source 1 after a delay time expires."
    ::= { tlpNotificationsAts 12 }

tlpNotificationsAtsTransferToSource1 NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the active input source transfers
        from source 2 to source 1."
    ::= { tlpNotificationsAts 13 }

tlpNotificationsAtsTransferToSource2 NOTIFICATION-TYPE
    OBJECTS { sysUpTime, tlpNotifyEventTableRef, tlpNotifyEventTableRowRef }
    STATUS current
    DESCRIPTION
        "This notification is sent when the active input source transfers
        from source 1 to source 2."
    ::= { tlpNotificationsAts 14 }

tlpNotificationsCooling OBJECT IDENTIFIER ::= { tlpNotificationsEvent 7 }

tlpNotificationsKvm OBJECT IDENTIFIER ::= { tlpNotificationsEvent 8 }

tlpNotificationsRackTrack OBJECT IDENTIFIER ::= { tlpNotificationsEvent 9 }

tlpNotificationsSwitch OBJECT IDENTIFIER ::= { tlpNotificationsEvent 10 }

tlpNotifyEventTableRef OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This value references the OID of a table object applicable to the event
        condition. This, in conjunction with the table row as defined by
        tlpAlarmTableRowRef, specifies the object/row within this table to which
        the event condition applies. Refer to tlpAlarmTableRef for more information."
    ::= { tlpNotificationsEventParameters 1 }

tlpNotifyEventTableRowRef OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This value is used in conjunction with tlpNotifyEventTableRef to define the
        object associated with this event condition. See the description of
        tlpNotifyEventTableRef for more information."
    ::= { tlpNotificationsEventParameters 2 }

END
