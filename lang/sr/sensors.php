<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Sensors Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to translate names and units of sensors
    |
    */

    'airflow' => [
        'short' => 'Protok vazduha',
        'long' => 'Protok vazduha',
        'unit' => 'cfm',
        'unit_long' => 'Kubika po minuti',
    ],
    'ber' => [
        'short' => 'BER',
        'long' => 'Brzina grešaka',
        'unit' => '',
        'unit_long' => '',
    ],
    'charge' => [
        'short' => 'Napunjenost',
        'long' => '',
        'unit' => '%',
        'unit_long' => 'Procenata',
    ],
    'chromatic_dispersion' => [
        'short' => 'Hromatična disperzija',
        'long' => 'Hromatična disperzija',
        'unit' => 'ps/nm',
        'unit_long' => 'Picosekundi po nanometru',
    ],
    'cooling' => [
        'short' => 'Hlađenje',
        'long' => '',
        'unit' => 'W',
        'unit_long' => 'Watt',
    ],
    'count' => [
        'short' => 'Brojač',
        'long' => 'Brojač',
        'unit' => '',
        'unit_long' => '',
    ],
    'current' => [
        'short' => 'Struja',
        'long' => 'Struja',
        'unit' => 'A',
        'unit_long' => 'Ampera',
    ],
    'dbm' => [
        'short' => 'dBm',
        'long' => 'dBm',
        'unit' => 'dBm',
        'unit_long' => 'Decibela po Milliwatu',
    ],
    'delay' => [
        'short' => 'Kašnjenje',
        'long' => 'Kašnjenje',
        'unit' => 's',
        'unit_long' => 'Sekundi',
    ],
    'eer' => [
        'short' => 'EER',
        'long' => 'Odnos energetske efikasnosti',
        'unit' => '',
        'unit_long' => '',
    ],
    'fanspeed' => [
        'short' => 'Ventilatori',
        'long' => 'Brzina ventilatora',
        'unit' => 'RPM',
        'unit_long' => 'obrtaja po minuti',
    ],
    'frequency' => [
        'short' => 'Frekvencija',
        'long' => 'Frekvencija',
        'unit' => 'Hz',
        'unit_long' => 'Hertz',
    ],
    'humidity' => [
        'short' => 'Vlažnost',
        'long' => 'Procenata Vlažnosti',
        'unit' => '%',
        'unit_long' => 'Procenata',
    ],
    'load' => [
        'short' => 'Opterećenje',
        'long' => 'Opterećenje',
        'unit' => '%',
        'unit_long' => 'Procenata',
    ],
    'power' => [
        'short' => 'Potrošnja',
        'long' => 'Električna potrošnja',
        'unit' => 'W',
        'unit_long' => 'Watt',
    ],
    'power_consumed' => [
        'short' => 'Potrošeno el.energije',
        'long' => 'Potrošeno električne energije',
        'unit' => 'kWh',
        'unit_long' => 'Kilovat čas',
    ],
    'power_factor' => [
        'short' => 'Faktor iskorišćenja',
        'long' => 'Faktor iskoriščćenja',
        'unit' => '',
        'unit_long' => '',
    ],
    'pressure' => [
        'short' => 'Pritisak',
        'long' => 'Pritisak',
        'unit' => 'kPa',
        'unit_long' => 'Kilo paskal',
    ],
    'quality_factor' => [
        'short' => 'Kvalitet',
        'long' => 'Faktor kvaliteta',
        'unit' => '',
        'unit_long' => '',
    ],
    'runtime' => [
        'short' => 'Vreme rada',
        'long' => 'Vreme rada',
        'unit' => 'Min',
        'unit_long' => 'Minuta',
    ],
    'signal' => [
        'short' => 'Signal',
        'long' => 'Jačina signala',
        'unit' => 'dBm',
        'unit_long' => 'Decibel-Milliwatt',
    ],
    'snr' => [
        'short' => 'SNR',
        'long' => 'Odnos sinal-šum',
        'unit' => 'dB',
        'unit_long' => 'Decibel',
    ],
    'state' => [
        'short' => 'Stanje',
        'long' => 'Trenutno stanje',
        'unit' => '',
    ],
    'temperature' => [
        'short' => 'Temperatura',
        'long' => 'Temperatura',
        'unit' => '°C',
        'unit_long' => '° Celsiusa',
    ],
    'voltage' => [
        'short' => 'Napon',
        'long' => 'Napon',
        'unit' => 'V',
        'unit_long' => 'Volti',
    ],
    'waterflow' => [
        'short' => 'Protok vode',
        'long' => 'Protok vode',
        'unit' => 'l/m',
        'unit_long' => 'Litara po minuti',
    ],
];
