<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Сообщения об ошибках валидации
    |--------------------------------------------------------------------------
    |
    | Следующие строки содержат стандартные сообщения об ошибках, используемые
    | классом валидатора. Некоторые из этих правил имеют несколько версий, таких
    | как правила размера. Вы можете настроить каждое из этих сообщений здесь.
    |
    */

    'accepted' => 'Поле :attribute должно быть принято.',
    'accepted_if' => 'Поле :attribute должно быть принято, когда :other равно :value.',
    'active_url' => 'Поле :attribute должно быть действительным URL.',
    'after' => 'Поле :attribute должно быть датой после :date.',
    'after_or_equal' => 'Поле :attribute должно быть датой после или равной :date.',
    'alpha' => 'Поле :attribute должно содержать только буквы.',
    'alpha_dash' => 'Поле :attribute должно содержать только буквы, цифры, дефисы и подчеркивания.',
    'alpha_num' => 'Поле :attribute должно содержать только буквы и цифры.',
    'array' => 'Поле :attribute должно быть массивом.',
    'ascii' => 'Поле :attribute должно содержать только однобайтовые буквенно-цифровые символы и знаки.',
    'before' => 'Поле :attribute должно быть датой до :date.',
    'before_or_equal' => 'Поле :attribute должно быть датой до или равной :date.',
    'between' => [
        'array' => 'Поле :attribute должно содержать от :min до :max элементов.',
        'file' => 'Поле :attribute должно быть от :min до :max килобайт.',
        'numeric' => 'Поле :attribute должно быть между :min и :max.',
        'string' => 'Поле :attribute должно содержать от :min до :max символов.',
    ],
    'boolean' => 'Поле :attribute должно быть истинным или ложным.',
    'confirmed' => 'Подтверждение поля :attribute не совпадает.',
    'current_password' => 'Пароль неверный.',
    'date' => 'Поле :attribute должно быть действительной датой.',
    'date_equals' => 'Поле :attribute должно быть датой, равной :date.',
    'date_format' => 'Поле :attribute должно соответствовать формату :format.',
    'decimal' => 'Поле :attribute должно иметь :decimal десятичных знаков.',
    'declined' => 'Поле :attribute должно быть отклонено.',
    'declined_if' => 'Поле :attribute должно быть отклонено, когда :other равно :value.',
    'different' => 'Поле :attribute и :other должны быть разными.',
    'digits' => 'Поле :attribute должно содержать :digits цифр.',
    'digits_between' => 'Поле :attribute должно содержать от :min до :max цифр.',
    'dimensions' => 'Поле :attribute имеет недопустимые размеры изображения.',
    'distinct' => 'Поле :attribute имеет дублирующее значение.',
    'doesnt_end_with' => 'Поле :attribute не должно заканчиваться на одно из следующих значений: :values.',
    'doesnt_start_with' => 'Поле :attribute не должно начинаться с одного из следующих значений: :values.',
    'email' => 'Поле :attribute должно быть действительным адресом электронной почты.',
    'ends_with' => 'Поле :attribute должно заканчиваться на одно из следующих значений: :values.',
    'enum' => 'Выбранный :attribute недействителен.',
    'exists' => 'Выбранный :attribute недействителен.',
    'file' => 'Поле :attribute должно быть файлом.',
    'filled' => 'Поле :attribute должно иметь значение.',
    'gt' => [
        'array' => 'Поле :attribute должно содержать более :value элементов.',
        'file' => 'Поле :attribute должно быть больше :value килобайт.',
        'numeric' => 'Поле :attribute должно быть больше :value.',
        'string' => 'Поле :attribute должно содержать более :value символов.',
    ],
    'gte' => [
        'array' => 'Поле :attribute должно содержать :value элементов или больше.',
        'file' => 'Поле :attribute должно быть больше или равно :value килобайт.',
        'numeric' => 'Поле :attribute должно быть больше или равно :value.',
        'string' => 'Поле :attribute должно содержать больше или равно :value символов.',
    ],
    'image' => 'Поле :attribute должно быть изображением.',
    'in' => 'Выбранный :attribute недействителен.',
    'in_array' => 'Поле :attribute должно существовать в :other.',
    'integer' => 'Поле :attribute должно быть целым числом.',
    'ip' => 'Поле :attribute должно быть действительным IP адресом.',
    'ipv4' => 'Поле :attribute должно быть действительным IPv4 адресом.',
    'ipv6' => 'Поле :attribute должно быть действительным IPv6 адресом.',
    'json' => 'Поле :attribute должно быть действительной JSON строкой.',
    'lowercase' => 'Поле :attribute должно быть в нижнем регистре.',
    'lt' => [
        'array' => 'Поле :attribute должно содержать менее :value элементов.',
        'file' => 'Поле :attribute должно быть меньше :value килобайт.',
        'numeric' => 'Поле :attribute должно быть меньше :value.',
        'string' => 'Поле :attribute должно содержать менее :value символов.',
    ],
    'lte' => [
        'array' => 'Поле :attribute не должно содержать более :value элементов.',
        'file' => 'Поле :attribute должно быть меньше или равно :value килобайт.',
        'numeric' => 'Поле :attribute должно быть меньше или равно :value.',
        'string' => 'Поле :attribute должно содержать меньше или равно :value символов.',
    ],
    'mac_address' => 'Поле :attribute должно быть действительным MAC адресом.',
    'max' => [
        'array' => 'Поле :attribute не должно содержать более :max элементов.',
        'file' => 'Поле :attribute не должно превышать :max килобайт.',
        'numeric' => 'Поле :attribute не должно превышать :max.',
        'string' => 'Поле :attribute не должно превышать :max символов.',
    ],
    'max_digits' => 'Поле :attribute не должно содержать более :max цифр.',
    'mimes' => 'Поле :attribute должно быть файлом типа: :values.',
    'mimetypes' => 'Поле :attribute должно быть файлом типа: :values.',
    'min' => [
        'array' => 'Поле :attribute должно содержать не менее :min элементов.',
        'file' => 'Поле :attribute должно быть не менее :min килобайт.',
        'numeric' => 'Поле :attribute должно быть не менее :min.',
        'string' => 'Поле :attribute должно содержать не менее :min символов.',
    ],
    'min_digits' => 'Поле :attribute должно содержать не менее :min цифр.',
    'missing' => 'Поле :attribute должно отсутствовать.',
    'missing_if' => 'Поле :attribute должно отсутствовать, когда :other равно :value.',
    'missing_unless' => 'Поле :attribute должно отсутствовать, если :other не равно :value.',
    'missing_with' => 'Поле :attribute должно отсутствовать, когда :values присутствует.',
    'missing_with_all' => 'Поле :attribute должно отсутствовать, когда :values присутствуют.',
    'multiple_of' => 'Поле :attribute должно быть кратно :value.',
    'not_in' => 'Выбранный :attribute недействителен.',
    'not_regex' => 'Формат поля :attribute недействителен.',
    'numeric' => 'Поле :attribute должно быть числом.',
    'password' => [
        'letters' => 'Поле :attribute должно содержать хотя бы одну букву.',
        'mixed' => 'Поле :attribute должно содержать хотя бы одну заглавную и одну строчную букву.',
        'numbers' => 'Поле :attribute должно содержать хотя бы одну цифру.',
        'symbols' => 'Поле :attribute должно содержать хотя бы один символ.',
        'uncompromised' => 'Указанный :attribute был обнаружен в утечке данных. Пожалуйста, выберите другой :attribute.',
    ],
    'present' => 'Поле :attribute должно присутствовать.',
    'prohibited' => 'Поле :attribute запрещено.',
    'prohibited_if' => 'Поле :attribute запрещено, когда :other равно :value.',
    'prohibited_unless' => 'Поле :attribute запрещено, если :other не в :values.',
    'prohibits' => 'Поле :attribute запрещает присутствие :other.',
    'regex' => 'Формат поля :attribute недействителен.',
    'required' => 'Поле :attribute обязательно для заполнения.',
    'required_array_keys' => 'Поле :attribute должно содержать записи для: :values.',
    'required_if' => 'Поле :attribute обязательно для заполнения, когда :other равно :value.',
    'required_if_accepted' => 'Поле :attribute обязательно для заполнения, когда :other принято.',
    'required_unless' => 'Поле :attribute обязательно для заполнения, если :other не в :values.',
    'required_with' => 'Поле :attribute обязательно для заполнения, когда :values присутствует.',
    'required_with_all' => 'Поле :attribute обязательно для заполнения, когда :values присутствуют.',
    'required_without' => 'Поле :attribute обязательно для заполнения, когда :values отсутствуют.',
    'required_without_all' => 'Поле :attribute обязательно для заполнения, когда ни одно из :values не присутствует.',
    'same' => 'Поле :attribute должно совпадать с :other.',
    'size' => [
        'array' => 'Поле :attribute должно содержать :size элементов.',
        'file' => 'Поле :attribute должно быть :size килобайт.',
        'numeric' => 'Поле :attribute должно быть :size.',
        'string' => 'Поле :attribute должно содержать :size символов.',
    ],
    'starts_with' => 'Поле :attribute должно начинаться с одного из следующих значений: :values.',
    'string' => 'Поле :attribute должно быть строкой.',
    'timezone' => 'Поле :attribute должно быть действительным часовым поясом.',
    'unique' => ':attribute уже занят.',
    'uploaded' => 'Не удалось загрузить :attribute.',
    'uppercase' => 'Поле :attribute должно быть заглавным.',
    'url' => 'Поле :attribute должно быть действительным URL.',
    'ulid' => 'Поле :attribute должно быть действительным ULID.',
    'uuid' => 'Поле :attribute должно быть действительным UUID.',

    // Специфические для Librenms
    'alpha_space' => 'Поле :attribute может содержать только буквы, цифры, подчеркивания и пробелы.',
    'ip_or_hostname' => 'Поле :attribute должно быть действительным IP адресом/сетью или именем хоста.',
    'is_regex' => 'Поле :attribute не является действительным регулярным выражением',
    'keys_in' => 'Поле :attribute содержит недопустимые ключи: :extra. Допустимые ключи: :values',

    /*
    |--------------------------------------------------------------------------
    | Пользовательские сообщения об ошибках валидации
    |--------------------------------------------------------------------------
    |
    | Здесь вы можете указать пользовательские сообщения валидации для атрибутов,
    | используя соглашение "attribute.rule" для именования строк. Это позволяет
    | быстро указать конкретную пользовательскую строку для данного правила атрибута.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Пользовательские атрибуты валидации
    |--------------------------------------------------------------------------
    |
    | Следующие строки используются для замены плейсхолдера атрибута
    | на что-то более удобочитаемое, например "Адрес электронной почты"
    | вместо "email". Это просто помогает сделать наше сообщение более выразительным.
    |
    */

    'attributes' => [],

    'results' => [
        'autofix' => 'Попытка автоматического исправления',
        'fix' => 'Исправить',
        'fixed' => 'Исправление завершено, обновите страницу для повторной проверки.',
        'fetch_failed' => 'Не удалось получить результаты валидации',
        'backend_failed' => 'Не удалось загрузить данные с сервера, проверьте консоль на наличие ошибок.',
        'invalid_fixer' => 'Недопустимый исправитель',
        'show_all' => 'Показать все',
        'show_less' => 'Показать меньше',
        'validate' => 'Проверить',
        'validating' => 'Проверка',
    ],
    'validations' => [
        'rrd' => [
            'CheckRrdVersion' => [
                'fail' => 'Версия rrdtool, которую вы указали, новее установленной. Конфигурация: :config_version Установлено: :installed_version',
                'fix' => 'Закомментируйте или удалите $config[\'rrdtool_version\'] = \':version\'; из вашего файла config.php',
                'ok' => 'Версия rrdtool в порядке',
            ],
            'CheckRrdcachedConnectivity' => [
                'fail_socket' => ':socket, похоже, не существует, тест соединения с rrdcached не удался',
                'fail_port' => 'Не удается подключиться к серверу rrdcached на порту :port',
                'ok' => 'Подключено к rrdcached',
            ],
            'CheckRrdDirPermissions' => [
                'fail_root' => 'Ваша директория RRD принадлежит root, пожалуйста, подумайте о смене на пользователя, не являющегося root',
                'fail_mode' => 'Ваша директория RRD не установлена на 0775',
                'ok' => 'rrd_dir доступен для записи',
            ],
        ],
        'database' => [
            'CheckDatabaseTableNamesCase' => [
                'fail' => 'У вас установлено lower_case_table_names = 1 или true в конфигурации mysql.',
                'fix' => 'Установите lower_case_table_names=0 в вашем конфигурационном файле mysql в разделе [mysqld].',
                'ok' => 'lower_case_table_names включен',
            ],
            'CheckDatabaseServerVersion' => [
                'fail' => ':server версия :min является минимально поддерживаемой версией на :date.',
                'fix' => 'Обновите :server до поддерживаемой версии, рекомендуется :suggested.',
                'ok' => 'SQL Server соответствует минимальным требованиям',
            ],
            'CheckMysqlEngine' => [
                'fail' => 'Некоторые таблицы не используют рекомендованный движок InnoDB, это может вызвать проблемы.',
                'tables' => 'Таблицы',
                'ok' => 'Движок MySQL оптимален',
            ],
            'CheckSqlServerTime' => [
                'fail' => "Время между этим сервером и базой данных mysql отличается\n Время Mysql :mysql_time\n Время PHP :php_time",
                'ok' => 'Время MySQL и PHP совпадает',
            ],
            'CheckSchemaVersion' => [
                'fail_outdated' => 'Ваша база данных устарела!',
                'fail_legacy_outdated' => 'Ваша схема базы данных (:current) старше последней (:latest).',
                'fix_legacy_outdated' => 'Выполните ./daily.sh вручную и проверьте наличие ошибок.',
                'warn_extra_migrations' => 'Ваша схема базы данных имеет дополнительные миграции (:migrations). Если вы только что переключились на стабильный релиз с ежедневного, ваша база данных находится между релизами, и это будет решено с следующим релизом.',
                'warn_legacy_newer' => 'Ваша схема базы данных (:current) новее ожидаемой (:latest). Если вы только что переключились на стабильный релиз с ежедневного, ваша база данных находится между релизами, и это будет решено с следующим релизом.',
                'ok' => 'Схема базы данных актуальна',
            ],
            'CheckSchemaCollation' => [
                'ok' => 'Сравнение базы данных и колонок корректное',
            ],
        ],
        'distributedpoller' => [
            'CheckDistributedPollerEnabled' => [
                'ok' => 'Настройка распределенного опроса включена глобально',
                'not_enabled' => 'Вы не включили distributed_poller',
                'not_enabled_globally' => 'Вы не включили distributed_poller глобально',
            ],
            'CheckMemcached' => [
                'not_configured_host' => 'Вы не настроили distributed_poller_memcached_host',
                'not_configured_port' => 'Вы не настроили distributed_poller_memcached_port',
                'could_not_connect' => 'Не удалось подключиться к серверу memcached',
                'ok' => 'Подключение к memcached в порядке',
            ],
            'CheckRrdcached' => [
                'fail' => 'Вы не включили rrdcached',
            ],
        ],
        'poller' => [
            'CheckActivePoller' => [
                'fail' => 'Опрашиватель не работает. Ни один опрашиватель не работал в последние :interval секунд.',
                'both_fail' => 'Обе службы диспетчера и Python Wrapper были активны недавно, это может вызвать двойной опрос',
                'ok' => 'Найденные активные опрашиватели',
            ],
            'CheckDispatcherService' => [
                'fail' => 'Не найдено активных узлов диспетчера',
                'ok' => 'Служба диспетчера включена',
                'nodes_down' => 'Некоторые узлы диспетчера не проверялись недавно',
                'not_detected' => 'Служба диспетчера не обнаружена',
                'warn' => 'Служба диспетчера использовалась, но не недавно',
            ],
            'CheckLocking' => [
                'fail' => 'Проблема с блокировкой сервера: :message',
                'ok' => 'Блокировки функционируют',
            ],
            'CheckPythonWrapper' => [
                'fail' => 'Не найдено активных опрашивателей Python wrapper',
                'no_pollers' => 'Не найдено опрашивателей Python wrapper',
                'cron_unread' => 'Не удалось прочитать файлы cron',
                'ok' => 'Обертка опрашивателя Python работает',
                'nodes_down' => 'Некоторые узлы опрашивателя не проверялись недавно',
                'not_detected' => 'Запись cron для обертки Python не присутствует',
            ],
            'CheckRedis' => [
                'bad_driver' => 'Используется :driver для блокировки, вы должны установить CACHE_DRIVER=redis',
                'ok' => 'Redis функционирует',
                'unavailable' => 'Redis недоступен',
            ],
        ],
    ],
];
