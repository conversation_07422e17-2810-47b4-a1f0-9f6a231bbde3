<?php

return [
    'title' => 'Настройки',
    'readonly' => 'Установите в config.php, удалите из config.php для включения.',
    'groups' => [
        'alerting' => 'Оповещения',
        'api' => 'API',
        'auth' => 'Аутентификация',
        'authorization' => 'Авторизация',
        'external' => 'Внешние',
        'global' => 'Глобальные',
        'os' => 'ОС',
        'discovery' => 'Обнаружение',
        'graphing' => 'Графики',
        'poller' => 'Опрашиватель',
        'system' => 'Система',
        'webui' => 'Веб-интерфейс',
    ],
    'sections' => [
        'alerting' => [
            'general' => ['name' => 'Общие настройки оповещений'],
            'email' => ['name' => 'Настройки электронной почты'],
            'rules' => ['name' => 'Настройки правил оповещений по умолчанию'],
        ],
        'api' => [
            'cors' => ['name' => 'CORS'],
        ],
        'auth' => [
            'general' => ['name' => 'Общие настройки аутентификации'],
            'ad' => ['name' => 'Настройки Active Directory'],
            'ldap' => ['name' => 'Настройки LDAP'],
            'radius' => ['name' => 'Настройки Radius'],
            'socialite' => ['name' => 'Настройки Socialite'],
        ],
        'authorization' => [
            'device-group' => ['name' => 'Настройки групп устройств'],
        ],
        'discovery' => [
            'general' => ['name' => 'Общие настройки обнаружения'],
            'route' => ['name' => 'Модуль обнаружения маршрутов'],
            'discovery_modules' => ['name' => 'Модули обнаружения'],
            'ports' => ['name' => 'Модуль портов'],
            'storage' => ['name' => 'Модуль хранения'],
            'networks' => ['name' => 'Сети'],
        ],
        'external' => [
            'binaries' => ['name' => 'Расположение бинарных файлов'],
            'location' => ['name' => 'Настройки расположения'],
            'graylog' => ['name' => 'Интеграция с Graylog'],
            'oxidized' => ['name' => 'Интеграция с Oxidized'],
            'mac_oui' => ['name' => 'Интеграция поиска Mac OUI'],
            'peeringdb' => ['name' => 'Интеграция с PeeringDB'],
            'nfsen' => ['name' => 'Интеграция с NfSen'],
            'unix-agent' => ['name' => 'Интеграция с Unix-Agent'],
            'smokeping' => ['name' => 'Интеграция с Smokeping'],
            'snmptrapd' => ['name' => 'Интеграция с SNMP Traps'],
        ],
        'poller' => [
            'availability' => ['name' => 'Доступность устройств'],
            'distributed' => ['name' => 'Распределенный опрашиватель'],
            'graphite' => ['name' => 'Магазин данных: Graphite'],
            'influxdb' => ['name' => 'Магазин данных: InfluxDB'],
            'influxdbv2' => ['name' => 'Магазин данных: InfluxDBv2'],
            'opentsdb' => ['name' => 'Магазин данных: OpenTSDB'],
            'ping' => ['name' => 'Ping'],
            'prometheus' => ['name' => 'Магазин данных: Prometheus'],
            'rrdtool' => ['name' => 'Магазин данных: RRDTool'],
            'snmp' => ['name' => 'SNMP'],
            'dispatcherservice' => ['name' => 'Служба диспетчера'],
            'poller_modules' => ['name' => 'Модули опрашивателя'],
        ],
        'system' => [
            'cleanup' => ['name' => 'Очистка'],
            'proxy' => ['name' => 'Прокси'],
            'updates' => ['name' => 'Обновления'],
            'scheduledtasks' => ['name' => 'Запланированные задачи'],
            'server' => ['name' => 'Сервер'],
            'reporting' => ['name' => 'Отчетность'],
        ],
        'webui' => [
            'availability-map' => ['name' => 'Настройки карты доступности'],
            'custom-map' => ['name' => 'Настройки пользовательской карты'],
            'graph' => ['name' => 'Настройки графиков'],
            'dashboard' => ['name' => 'Настройки панели управления'],
            'port-descr' => ['name' => 'Парсинг описания интерфейсов'],
            'search' => ['name' => 'Настройки поиска'],
            'style' => ['name' => 'Стиль'],
            'device' => ['name' => 'Настройки устройства'],
            'worldmap' => ['name' => 'Настройки мировой карты'],
        ],
    ],
    'settings' => [
        'active_directory' => [
            'users_purge' => [
                'description' => 'Сохранять неактивных пользователей в течение',
                'help' => 'Пользователи будут удалены из LibreNMS после этого количества дней без входа. 0 означает никогда, и пользователи будут восстановлены, если они снова войдут в систему.',
            ],
        ],
        'addhost_alwayscheckip' => [
            'description' => 'Проверять на дублирование IP при добавлении устройств',
            'help' => 'Если хост добавляется как IP-адрес, проверяется, чтобы убедиться, что этот IP уже не присутствует. Если IP уже присутствует, хост не добавляется. Если хост добавляется по имени хоста, эта проверка не выполняется. Если настройка включена, имена хостов разрешаются, и проверка также выполняется. Это помогает предотвратить случайное дублирование хостов.',
        ],
        'alert_rule' => [
            'acknowledged_alerts' => [
                'description' => 'Подтвержденные оповещения',
                'help' => 'Отправлять оповещения, когда оповещение подтверждено',
            ],
            'severity' => [
                'description' => 'Серьезность',
                'help' => 'Серьезность для оповещения',
            ],
            'max_alerts' => [
                'description' => 'Максимальное количество оповещений',
                'help' => 'Количество оповещений, которые будут отправлены',
            ],
            'delay' => [
                'description' => 'Задержка',
                'help' => 'Задержка перед отправкой оповещения',
            ],
            'interval' => [
                'description' => 'Интервал',
                'help' => 'Интервал, в течение которого будет проверяться это оповещение',
            ],
            'mute_alerts' => [
                'description' => 'Отключить оповещения',
                'help' => 'Должно ли оповещение отображаться только в WebUI',
            ],
            'invert_rule_match' => [
                'description' => 'Инвертировать соответствие правила',
                'help' => 'Оповещать только в том случае, если правило не совпадает',
            ],
            'recovery_alerts' => [
                'description' => 'Оповещения о восстановлении',
                'help' => 'Уведомлять, если оповещение восстанавливается',
            ],
            'acknowledgement_alerts' => [
                'description' => 'Оповещения о подтверждении',
                'help' => 'Уведомлять, если оповещение подтверждено',
            ],
            'invert_map' => [
                'description' => 'Все устройства, кроме указанных в списке',
                'help' => 'Оповещать только для устройств, которые не перечислены',
            ],
        ],
        'alert' => [
            'ack_until_clear' => [
                'description' => 'Опция подтверждения по умолчанию до очистки оповещения',
                'help' => 'Подтверждение по умолчанию до очистки оповещения',
            ],
            'admins' => [
                'description' => 'Отправлять оповещения администраторам (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
            'default_copy' => [
                'description' => 'Копировать все оповещения по электронной почте на контакт по умолчанию (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
            'default_if_none' => [
                'description' => 'нельзя установить в webui? (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
            'default_mail' => [
                'description' => 'Контакт по умолчанию (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
            'default_only' => [
                'description' => 'Отправлять оповещения только на контакт по умолчанию (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
            'disable' => [
                'description' => 'Отключить оповещения',
                'help' => 'Остановить генерацию оповещений',
            ],
            'acknowledged' => [
                'description' => 'Отправлять подтвержденные оповещения',
                'help' => 'Уведомлять, если оповещение было подтверждено',
            ],
            'fixed-contacts' => [
                'description' => 'Отключить изменения контактов для активных оповещений',
                'help' => 'Если TRUE, любые изменения в sysContact или электронных адресах пользователей не будут учитываться, пока оповещение активно',
            ],
            'globals' => [
                'description' => 'Отправлять оповещения пользователям только для чтения (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
            'syscontact' => [
                'description' => 'Отправлять оповещения sysContact (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
            'transports' => [
                'mail' => [
                    'description' => 'Включить оповещения по электронной почте',
                    'help' => 'Транспорт для оповещений по электронной почте',
                ],
            ],
            'tolerance_window' => [
                'description' => 'Окно допустимого времени для cron',
                'help' => 'Окно допустимого времени в секундах',
            ],
            'users' => [
                'description' => 'Отправлять оповещения обычным пользователям (устарело)',
                'help' => 'Устарело, используйте транспорт для оповещений по электронной почте вместо этого.',
            ],
        ],
        'alert_log_purge' => [
            'description' => 'Записи журнала оповещений старше',
            'help' => 'Очистка выполняется с помощью daily.sh',
        ],
        'discovery_on_reboot' => [
            'description' => 'Обнаружение при перезагрузке',
            'help' => 'Выполнить обнаружение на перезагруженном устройстве',
        ],
        'allow_duplicate_sysName' => [
            'description' => 'Разрешить дублирование sysName',
            'help' => 'По умолчанию дублирующие sysNames не могут быть добавлены, чтобы предотвратить добавление устройства с несколькими интерфейсами несколько раз',
        ],
        'allow_unauth_graphs' => [
            'description' => 'Разрешить доступ к графикам без аутентификации',
            'help' => 'Позволяет любому получать доступ к графикам без входа в систему',
        ],
        'allow_unauth_graphs_cidr' => [
            'description' => 'Разрешить доступ к графикам для заданных сетей',
            'help' => 'Разрешить доступ к графикам без аутентификации для заданных сетей (не применяется, когда доступ к графикам без аутентификации включен)',
        ],
        'api' => [
            'cors' => [
                'allowheaders' => [
                    'description' => 'Разрешить заголовки',
                    'help' => 'Устанавливает заголовок ответа Access-Control-Allow-Headers',
                ],
                'allowcredentials' => [
                    'description' => 'Разрешить учетные данные',
                    'help' => 'Устанавливает заголовок Access-Control-Allow-Credentials',
                ],
                'allowmethods' => [
                    'description' => 'Разрешенные методы',
                    'help' => 'Соответствует методу запроса.',
                ],
                'enabled' => [
                    'description' => 'Включить поддержку CORS для API',
                    'help' => 'Позволяет загружать ресурсы API из веб-клиента',
                ],
                'exposeheaders' => [
                    'description' => 'Экспонировать заголовки',
                    'help' => 'Устанавливает заголовок ответа Access-Control-Expose-Headers',
                ],
                'maxage' => [
                    'description' => 'Максимальный возраст',
                    'help' => 'Устанавливает заголовок ответа Access-Control-Max-Age',
                ],
                'origin' => [
                    'description' => 'Разрешить источники запросов',
                    'help' => 'Соответствует источнику запроса. Можно использовать подстановочные знаки, например, *.mydomain.com',
                ],
            ],
        ],
        'apps' => [
            'powerdns-recursor' => [
                'api-key' => [
                    'description' => 'API ключ для PowerDNS Recursor',
                    'help' => 'API ключ для приложения PowerDNS Recursor при прямом подключении',
                ],
                'https' => [
                    'description' => 'Использует ли PowerDNS Recursor HTTPS?',
                    'help' => 'Использовать HTTPS вместо HTTP для приложения PowerDNS Recursor при прямом подключении',
                ],
                'port' => [
                    'description' => 'Порт PowerDNS Recursor',
                    'help' => 'TCP порт для использования в приложении PowerDNS Recursor при прямом подключении',
                ],
            ],
        ],
        'astext' => [
            'description' => 'Ключ для хранения кэша описаний автономных систем',
        ],
        'auth' => [
            'allow_get_login' => [
                'description' => 'Разрешить вход (небезопасно)',
                'help' => 'Разрешить вход, помещая переменные имени пользователя и пароля в URL запроса GET, полезно для систем отображения, где нельзя войти интерактивно. Это считается небезопасным, так как пароль будет отображаться в логах, а входы не имеют ограничения по количеству, что может открыть возможность для атак методом подбора.',
            ],
            'socialite' => [
                'redirect' => [
                    'description' => 'Перенаправление на страницу входа',
                    'help' => 'Страница входа должна немедленно перенаправлять на первого определенного провайдера.<br><br>СОВЕТ: Вы можете предотвратить это, добавив ?redirect=0 в URL',
                ],
                'register' => [
                    'description' => 'Разрешить регистрацию через провайдера',
                ],
                'configs' => [
                    'description' => 'Конфигурации провайдера',
                ],
                'scopes' => [
                    'description' => 'Области, которые должны быть включены в запрос аутентификации',
                    'help' => 'Смотрите https://laravel.com/docs/10.x/socialite#access-scopes',
                ],
            ],
        ],
        'auth_ad_base_dn' => [
            'description' => 'Базовый DN',
            'help' => 'Группы и пользователи должны находиться под этим DN. Пример: dc=example,dc=com',
        ],
        'auth_ad_check_certificates' => [
            'description' => 'Проверка сертификата',
            'help' => 'Проверка сертификатов на действительность. Некоторые серверы используют самоподписанные сертификаты, отключение этой проверки позволяет это.',
        ],
        'auth_ad_debug' => [
            'description' => 'Отладка',
            'help' => 'Показать подробные сообщения об ошибках, не оставляйте это включенным, так как это может привести к утечке данных.',
        ],
        'auth_ad_domain' => [
            'description' => 'Домен Active Directory',
            'help' => 'Пример домена Active Directory: example.com',
        ],
        'auth_ad_group_filter' => [
            'description' => 'Фильтр групп LDAP',
            'help' => 'Фильтр LDAP Active Directory для выбора групп',
        ],
        'auth_ad_groups' => [
            'description' => 'Доступ к группам',
            'help' => 'Определите группы, имеющие доступ и уровень',
        ],
        'auth_ad_require_groupmembership' => [
            'description' => 'Требовать членство в группе',
            'help' => 'Разрешить вход пользователям только если они являются частью определенной группы',
        ],
        'auth_ad_user_filter' => [
            'description' => 'Фильтр пользователей LDAP',
            'help' => 'Фильтр LDAP Active Directory для выбора пользователей',
        ],
        'auth_ad_url' => [
            'description' => 'Сервер(ы) Active Directory',
            'help' => 'Установите сервер(ы), разделенные пробелами. Добавьте префикс ldaps:// для SSL. Пример: ldaps://dc1.example.com ldaps://dc2.example.com',
        ],
        'auth_ldap_attr' => [
            'uid' => [
                'description' => 'Атрибут для проверки имени пользователя',
                'help' => 'Атрибут, используемый для идентификации пользователей по имени пользователя',
            ],
        ],
        'auth_ldap_binddn' => [
            'description' => 'Bind DN (перезаписывает имя пользователя для привязки)',
            'help' => 'Полный DN пользователя для привязки',
        ],
        'auth_ldap_bindpassword' => [
            'description' => 'Пароль для привязки',
            'help' => 'Пароль для пользователя привязки',
        ],
        'auth_ldap_binduser' => [
            'description' => 'Имя пользователя для привязки',
            'help' => 'Используется для запроса к серверу LDAP, когда ни один пользователь не вошел в систему (уведомления, API и т. д.)',
        ],
        'auth_ad_binddn' => [
            'description' => 'Bind DN (перезаписывает имя пользователя для привязки)',
            'help' => 'Полный DN пользователя для привязки',
        ],
        'auth_ad_bindpassword' => [
            'description' => 'Пароль для привязки',
            'help' => 'Пароль для пользователя привязки',
        ],
        'auth_ad_binduser' => [
            'description' => 'Имя пользователя для привязки',
            'help' => 'Используется для запроса к серверу AD, когда ни один пользователь не вошел в систему (уведомления, API и т. д.)',
        ],
        'auth_ad_starttls' => [
            'description' => 'Использовать STARTTLS',
            'help' => 'Использовать STARTTLS для защиты соединения. Альтернатива LDAPS.',
            'options' => [
                'disabled' => 'Отключено',
                'optional' => 'Необязательно',
                'required' => 'Обязательно',
            ],
        ],
        'auth_ldap_cache_ttl' => [
            'description' => 'Срок действия кэша LDAP',
            'help' => 'Временное хранение результатов запросов LDAP. Улучшает скорость, но данные могут быть устаревшими.',
        ],
        'auth_ldap_debug' => [
            'description' => 'Показать отладку',
            'help' => 'Показать отладочную информацию. Может раскрыть конфиденциальную информацию, не оставляйте включенным.',
        ],
        'auth_ldap_cacertfile' => [
            'description' => 'Перезаписать системный сертификат CA TLS',
            'help' => 'Использовать предоставленный сертификат CA для LDAPS.',
        ],
        'auth_ldap_ignorecert' => [
            'description' => 'Не требовать действительный сертификат',
            'help' => 'Не требовать действительный TLS сертификат для LDAPS.',
        ],
        'auth_ldap_emailattr' => [
            'description' => 'Атрибут электронной почты',
        ],
        'auth_ldap_group' => [
            'description' => 'DN группы доступа',
            'help' => 'Уникальное имя группы для предоставления нормального уровня доступа. Пример: cn=groupname,ou=groups,dc=example,dc=com',
        ],
        'auth_ldap_groupbase' => [
            'description' => 'Базовый DN группы',
            'help' => 'Уникальное имя для поиска групп. Пример: ou=group,dc=example,dc=com',
        ],
        'auth_ldap_groupmemberattr' => [
            'description' => 'Атрибут членов группы',
        ],
        'auth_ldap_groupmembertype' => [
            'description' => 'Найти членов группы по',
            'options' => [
                'username' => 'Имя пользователя',
                'fulldn' => 'Полный DN (с использованием префикса и суффикса)',
                'puredn' => 'Поиск DN (поиск с использованием атрибута uid)',
            ],
        ],
        'auth_ldap_groups' => [
            'description' => 'Доступ к группам',
            'help' => 'Определите группы, имеющие доступ и уровень',
        ],
        'auth_ldap_require_groupmembership' => [
            'description' => 'Проверка членства в группе LDAP',
            'help' => 'Выполнить (или пропустить) ldap_compare, когда провайдер позволяет (или не позволяет) действие сравнения.',
        ],
        'auth_ldap_port' => [
            'description' => 'Порт LDAP',
            'help' => 'Порт для подключения к серверам. Для LDAP это должно быть 389, для LDAPS - 636',
        ],
        'auth_ldap_prefix' => [
            'description' => 'Префикс пользователя',
            'help' => 'Используется для преобразования имени пользователя в уникальное имя',
        ],
        'auth_ldap_server' => [
            'description' => 'Сервер(ы) LDAP',
            'help' => 'Установите сервер(ы), разделенные пробелами. Добавьте префикс ldaps:// для SSL',
        ],
        'auth_ldap_starttls' => [
            'description' => 'Использовать STARTTLS',
            'help' => 'Использовать STARTTLS для защиты соединения. Альтернатива LDAPS.',
            'options' => [
                'disabled' => 'Отключено',
                'optional' => 'Необязательно',
                'required' => 'Обязательно',
            ],
        ],
        'auth_ldap_suffix' => [
            'description' => 'Суффикс пользователя',
            'help' => 'Используется для преобразования имени пользователя в уникальное имя',
        ],
        'auth_ldap_timeout' => [
            'description' => 'Тайм-аут подключения',
            'help' => 'Если один или несколько серверов не отвечают, более высокие тайм-ауты могут вызвать медленный доступ. Слишком низкие могут вызвать сбои подключения в некоторых случаях',
        ],
        'auth_ldap_uid_attribute' => [
            'description' => 'Атрибут уникального ID',
            'help' => 'Атрибут LDAP для идентификации пользователей, должен быть числовым',
        ],
        'auth_ldap_userdn' => [
            'description' => 'Использовать полный DN пользователя',
            'help' => 'Использует полный DN пользователя в качестве значения атрибута члена группы вместо member: username, используя префикс и суффикс. (это member: uid=username,ou=groups,dc=domain,dc=com)',
        ],
        'auth_ldap_wildcard_ou' => [
            'description' => 'Шаблонный OU пользователя',
            'help' => 'Поиск пользователя, соответствующего имени пользователя независимо от OU, установленного в суффиксе пользователя. Полезно, если ваши пользователи находятся в разных OU. Имя пользователя для привязки, если установлено, все равно используется суффикс пользователя',
        ],
        'auth_ldap_version' => [
            'description' => 'Версия LDAP',
            'help' => 'Версия LDAP, которую следует использовать для общения с сервером. Обычно это должна быть версия 3',
            'options' => [
                '2' => '2',
                '3' => '3',
            ],
        ],
        'auth_mechanism' => [
            'description' => 'Метод авторизации (осторожно!)',
            'help' => "Метод авторизации. Осторожно, вы можете потерять возможность входа. Вы можете переопределить это обратно на mysql, установив \$config['auth_mechanism'] = 'mysql'; в вашем config.php",
            'options' => [
                'mysql' => 'MySQL (по умолчанию)',
                'active_directory' => 'Active Directory',
                'ldap' => 'LDAP',
                'radius' => 'Radius',
                'http-auth' => 'HTTP аутентификация',
                'ad-authorization' => 'Внешне аутентифицированный AD',
                'ldap-authorization' => 'Внешне аутентифицированный LDAP',
                'sso' => 'Единый вход',
            ],
        ],
        'auth_remember' => [
            'description' => 'Срок действия функции "Запомнить меня"',
            'help' => 'Количество дней, в течение которых пользователь будет оставаться в системе, если установлен флажок "Запомнить меня" при входе.',
        ],
        'authlog_purge' => [
            'description' => 'Записи журнала авторизации старше',
            'help' => 'Очистка выполняется скриптом daily.sh',
        ],
        'peering_descr' => [
            'description' => 'Типы портов пиринга',
            'help' => 'Порты указанных типов описания будут показаны в разделе меню пиринговых портов. См. документацию по парсингу описаний интерфейсов для получения дополнительной информации.',
        ],
        'transit_descr' => [
            'description' => 'Типы транзитных портов',
            'help' => 'Порты указанных типов описания будут показаны в разделе меню транзитных портов. См. документацию по парсингу описаний интерфейсов для получения дополнительной информации.',
        ],
        'core_descr' => [
            'description' => 'Типы основных портов',
            'help' => 'Порты указанных типов описания будут показаны в разделе меню основных портов. См. документацию по парсингу описаний интерфейсов для получения дополнительной информации.',
        ],
        'custom_map' => [
            'background_type' => [
                'description' => 'Тип фона',
                'help' => 'Тип фона по умолчанию для новых карт. Требуется набор данных фона.',
            ],
            'background_data' => [
                'color' => [
                    'description' => 'Цвет фона',
                    'help' => 'Начальный цвет для фона карты',
                ],
                'lat' => [
                    'description' => 'Широта фона карты',
                    'help' => 'Начальная широта для фоновой геокарты',
                ],
                'lng' => [
                    'description' => 'Долгота фона карты',
                    'help' => 'Начальная долгота для фоновой геокарты',
                ],
                'layer' => [
                    'description' => 'Слой фона карты',
                    'help' => 'Начальный слой карты для фоновой геокарты',
                ],
                'zoom' => [
                    'description' => 'Масштаб фона карты',
                    'help' => 'Начальный масштаб для фоновой геокарты',
                ],
            ],
            'edge_font_color' => [
                'description' => 'Цвет текста краев',
                'help' => 'Цвет шрифта по умолчанию для меток краев',
            ],
            'edge_font_face' => [
                'description' => 'Шрифт краев',
                'help' => 'Шрифт по умолчанию для меток краев',
            ],
            'edge_font_size' => [
                'description' => 'Размер текста краев',
                'help' => 'Размер шрифта по умолчанию для меток краев',
            ],
            'edge_seperation' => [
                'description' => 'Разделение краев',
                'help' => 'Разделение краев по умолчанию для новых карт',
            ],
            'height' => [
                'description' => 'Высота карты',
                'help' => 'Высота карты по умолчанию для новых карт',
            ],
            'node_align' => [
                'description' => 'Выравнивание узлов',
                'help' => 'Выравнивание узлов по умолчанию для новых карт',
            ],
            'node_background' => [
                'description' => 'Фон узлов',
                'help' => 'Цвет фона по умолчанию для меток узлов',
            ],
            'node_border' => [
                'description' => 'Граница узлов',
                'help' => 'Цвет границы по умолчанию для меток узлов',
            ],
            'node_font_color' => [
                'description' => 'Цвет текста узлов',
                'help' => 'Цвет шрифта по умолчанию для меток узлов',
            ],
            'node_font_face' => [
                'description' => 'Шрифт узлов',
                'help' => 'Шрифт по умолчанию для меток узлов',
            ],
            'node_font_size' => [
                'description' => 'Размер текста узлов',
                'help' => 'Размер шрифта по умолчанию для меток узлов',
            ],
            'node_size' => [
                'description' => 'Размер узлов',
                'help' => 'Размер по умолчанию для узлов',
            ],
            'node_type' => [
                'description' => 'Тип отображения узлов',
                'help' => 'Тип отображения по умолчанию для узлов',
            ],
            'reverse_arrows' => [
                'description' => 'Обратные стрелки краев',
                'help' => 'Направление стрелок по умолчанию. К центру (по умолчанию) или к концам',
            ],
            'width' => [
                'description' => 'Ширина карты',
                'help' => 'Ширина карты по умолчанию для новых карт',
            ],
        ],
        'customers_descr' => [
            'description' => 'Типы портов клиентов',
            'help' => 'Порты указанных типов описания будут показаны в разделе меню портов клиентов. См. документацию по парсингу описаний интерфейсов для получения дополнительной информации.',
        ],
        'base_url' => [
            'description' => 'Конкретный URL',
            'help' => 'Это должно быть установлено *только* в том случае, если вы хотите *принудительно* установить определенное имя хоста/порт. Это предотвратит использование веб-интерфейса с любого другого имени хоста',
        ],
        'discovery_modules' => [
            'arp-table' => [
                'description' => 'ARP таблица',
            ],
            'applications' => [
                'description' => 'Приложения',
            ],
            'bgp-peers' => [
                'description' => 'BGP Пиры',
            ],
            'cisco-cbqos' => [
                'description' => 'Cisco CBQOS',
            ],
            'cisco-cef' => [
                'description' => 'Cisco CEF',
            ],
            'cisco-mac-accounting' => [
                'description' => 'Cisco MAC Учет',
            ],
            'cisco-otv' => [
                'description' => 'Cisco OTV',
            ],
            'cisco-qfp' => [
                'description' => 'Cisco QFP',
            ],
            'slas' => [
                'description' => 'Отслеживание соглашений об уровне обслуживания',
            ],
            'cisco-pw' => [
                'description' => 'Cisco PW',
            ],
            'cisco-vrf-lite' => [
                'description' => 'Cisco VRF Lite',
            ],
            'discovery-arp' => [
                'description' => 'Обнаружение ARP',
            ],
            'discovery-protocols' => [
                'description' => 'Протоколы обнаружения',
            ],
            'entity-physical' => [
                'description' => 'Физическая сущность',
            ],
            'entity-state' => [
                'description' => 'Состояние сущности',
            ],
            'fdb-table' => [
                'description' => 'FDB таблица',
            ],
            'hr-device' => [
                'description' => 'HR Устройство',
            ],
            'ipv4-addresses' => [
                'description' => 'IPv4 адреса',
            ],
            'ipv6-addresses' => [
                'description' => 'IPv6 адреса',
            ],
            'isis' => [
                'description' => 'ISIS',
            ],
            'junose-atm-vp' => [
                'description' => 'Junose ATM VP',
            ],
            'loadbalancers' => [
                'description' => 'Балансировщики нагрузки',
            ],
            'mef' => [
                'description' => 'MEF',
            ],
            'mempools' => [
                'description' => 'Память',
            ],
            'mpls' => [
                'description' => 'MPLS',
            ],
            'ntp' => [
                'description' => 'NTP',
            ],
            'os' => [
                'description' => 'ОС',
            ],
            'ports' => [
                'description' => 'Порты',
            ],
            'ports-stack' => [
                'description' => 'Стек портов',
            ],
            'processors' => [
                'description' => 'Процессоры',
            ],
            'route' => [
                'description' => 'Маршрут',
            ],
            'sensors' => [
                'description' => 'Датчики',
            ],
            'services' => [
                'description' => 'Сервисы',
            ],
            'storage' => [
                'description' => 'Хранение',
            ],
            'stp' => [
                'description' => 'STP',
            ],
            'ucd-diskio' => [
                'description' => 'UCD DiskIO',
            ],
            'vlans' => [
                'description' => 'VLAN',
            ],
            'vminfo' => [
                'description' => 'Информация о гипервизоре VM',
            ],
            'vrf' => [
                'description' => 'VRF',
            ],
            'wireless' => [
                'description' => 'Беспроводной',
            ],
            'xdsl' => [
                'description' => 'xDSL',
            ],
            'printer-supplies' => [
                'description' => 'Запасные части для принтеров',
            ],
        ],
        'distributed_poller' => [
            'description' => 'Включить распределенное опрос (требуется дополнительная настройка)',
            'help' => 'Включает распределенную систему опроса по всей системе. Это предназначено для распределения нагрузки, а не для удаленного опроса. Вам необходимо прочитать документацию для получения шагов по включению: https://docs.librenms.org/Extensions/Distributed-Poller/',
        ],
        'default_poller_group' => [
            'description' => 'Группа опроса по умолчанию',
            'help' => 'Группа опроса по умолчанию, которую должны использовать все опросы, если в config.php не указано иное',
        ],
        'distributed_poller_memcached_host' => [
            'description' => 'Хост Memcached',
            'help' => 'Имя хоста или IP-адрес для сервера memcached. Это необходимо для блокировки poller_wrapper.py и daily.sh.',
        ],
        'distributed_poller_memcached_port' => [
            'description' => 'Порт Memcached',
            'help' => 'Порт для сервера memcached. По умолчанию 11211',
        ],
        'email_auto_tls' => [
            'description' => 'Поддержка Auto TLS',
            'help' => 'Пытается использовать TLS перед возвратом к нешифрованному соединению',
        ],
        'email_attach_graphs' => [
            'description' => 'Прикрепить графические изображения',
            'help' => 'Это создаст график, когда будет поднято предупреждение, и прикрепит его и вставит в электронное письмо.',
        ],
        'email_backend' => [
            'description' => 'Как доставлять почту',
            'help' => 'Бэкенд для отправки электронной почты, может быть mail, sendmail или SMTP',
            'options' => [
                'mail' => 'mail',
                'sendmail' => 'sendmail',
                'smtp' => 'SMTP',
            ],
        ],
        'email_from' => [
            'description' => 'Адрес электронной почты отправителя',
            'help' => 'Адрес электронной почты, используемый для отправки писем (от)',
        ],
        'email_html' => [
            'description' => 'Использовать HTML-письма',
            'help' => 'Отправлять HTML-письма',
        ],
        'email_sendmail_path' => [
            'description' => 'Путь к бинарному файлу sendmail',
        ],
        'email_smtp_auth' => [
            'description' => 'SMTP-аутентификация',
            'help' => 'Включите это, если ваш SMTP-сервер требует аутентификации',
        ],
        'email_smtp_host' => [
            'description' => 'SMTP сервер',
            'help' => 'IP-адрес или DNS-имя для SMTP-сервера для доставки почты',
        ],
        'email_smtp_password' => [
            'description' => 'Пароль SMTP-аутентификации',
        ],
        'email_smtp_port' => [
            'description' => 'Настройка порта SMTP',
        ],
        'email_smtp_secure' => [
            'description' => 'Шифрование',
            'options' => [
                '' => 'Отключено',
                'tls' => 'TLS',
                'ssl' => 'SSL',
            ],
        ],
        'email_smtp_timeout' => [
            'description' => 'Настройка таймаута SMTP',
        ],
        'email_smtp_username' => [
            'description' => 'Имя пользователя для аутентификации SMTP',
        ],
        'email_user' => [
            'description' => 'Имя отправителя',
            'help' => 'Имя, используемое в качестве части адреса отправителя',
        ],
        'eventlog_purge' => [
            'description' => 'Записи журнала событий старше',
            'help' => 'Очистка выполняется с помощью daily.sh',
        ],
        'favicon' => [
            'description' => 'Favicon',
            'help' => 'Перезаписывает стандартный favicon.',
        ],
        'fping' => [
            'description' => 'Путь к fping',
        ],
        'fping6' => [
            'description' => 'Путь к fping6',
        ],
        'fping_options' => [
            'count' => [
                'description' => 'Количество fping',
                'help' => 'Количество пингов, отправляемых при проверке, доступен ли хост через ICMP',
            ],
            'interval' => [
                'description' => 'Интервал fping',
                'help' => 'Количество миллисекунд, которое нужно ждать между пингами',
            ],
            'timeout' => [
                'description' => 'Таймаут fping',
                'help' => 'Количество миллисекунд, которое нужно ждать ответа эха перед тем, как сдаться',
            ],
        ],
        'geoloc' => [
            'api_key' => [
                'description' => 'API ключ для картографического движка',
                'help' => 'API ключ для геокодирования (необходим для работы)',
            ],
            'dns' => [
                'description' => 'Использовать DNS-запись местоположения',
                'help' => 'Использовать LOC-запись с DNS-сервера для получения географических координат для имени хоста',
            ],
            'engine' => [
                'description' => 'Картографический движок',
                'options' => [
                    'google' => 'Google Maps',
                    'openstreetmap' => 'OpenStreetMap',
                    'mapquest' => 'MapQuest',
                    'bing' => 'Bing Maps',
                    'esri' => 'ESRI ArcGIS',
                ],
            ],
            'latlng' => [
                'description' => 'Попытка геокодирования местоположений',
                'help' => 'Попробуйте найти широту и долготу через API геокодирования во время опроса',
            ],
            'layer' => [
                'description' => 'Начальный слой карты',
                'help' => 'Начальный слой карты для отображения. *Не все слои доступны для всех картографических движков.',
                'options' => [
                    'Streets' => 'Улицы',
                    'Sattelite' => 'Спутник',
                    'Topography' => 'Топография',
                ],
            ],
        ],
        'graphite' => [
            'enable' => [
                'description' => 'Включить',
                'help' => 'Экспортирует метрики в Graphite',
            ],
            'host' => [
                'description' => 'Сервер',
                'help' => 'IP-адрес или имя хоста сервера Graphite для отправки данных',
            ],
            'port' => [
                'description' => 'Порт',
                'help' => 'Порт для подключения к серверу Graphite',
            ],
            'prefix' => [
                'description' => 'Префикс (необязательно)',
                'help' => 'Добавит префикс к началу всех метрик. Должен быть алфавитно-цифровым, разделенным точками',
            ],
        ],
        'graphing' => [
            'availability' => [
                'description' => 'Продолжительность',
                'help' => 'Расчет доступности устройства для указанных продолжительностей. (Продолжительности определены в секундах)',
            ],
            'availability_consider_maintenance' => [
                'description' => 'Запланированное обслуживание не влияет на доступность',
                'help' => 'Отключает создание сбоев и снижение доступности для устройств, находящихся в режиме обслуживания.',
            ],
        ],
        'graphs' => [
            'port_speed_zoom' => [
                'description' => 'Увеличить графики портов до скорости порта',
                'help' => 'Увеличить графики портов так, чтобы максимальное значение всегда соответствовало скорости порта, отключенные графики портов увеличиваются по трафику',
            ],
        ],
        'graylog' => [
            'base_uri' => [
                'description' => 'Базовый URI',
                'help' => 'Перезаписывает базовый URI в случае, если вы изменили стандартный Graylog.',
            ],
            'device-page' => [
                'loglevel' => [
                    'description' => 'Уровень журнала на странице обзора устройства',
                    'help' => 'Устанавливает максимальный уровень журнала, отображаемый на странице обзора устройства.',
                ],
                'rowCount' => [
                    'description' => 'Количество строк на странице обзора устройства',
                    'help' => 'Устанавливает количество строк, отображаемых на странице обзора устройства.',
                ],
            ],
            'password' => [
                'description' => 'Пароль',
                'help' => 'Пароль для доступа к API Graylog.',
            ],
            'port' => [
                'description' => 'Порт',
                'help' => 'Порт, используемый для доступа к API Graylog. Если не указан, будет 80 для http и 443 для https.',
            ],
            'server' => [
                'description' => 'Сервер',
                'help' => 'IP-адрес или имя хоста конечной точки API Graylog.',
            ],
            'timezone' => [
                'description' => 'Часовой пояс отображения',
                'help' => 'Времена Graylog хранятся в GMT, эта настройка изменит отображаемый часовой пояс. Значение должно быть допустимым часовым поясом PHP.',
            ],
            'username' => [
                'description' => 'Имя пользователя',
                'help' => 'Имя пользователя для доступа к API Graylog.',
            ],
            'version' => [
                'description' => 'Версия',
                'help' => 'Используется для автоматического создания base_uri для API Graylog. Если вы изменили URI API по умолчанию, установите это значение на другое и укажите свой base_uri.',
            ],
            'query' => [
                'field' => [
                    'description' => 'Поле запроса API',
                    'help' => 'Изменяет поле по умолчанию для запроса к API Graylog.',
                ],
            ],
        ],
        'html' => [
            'device' => [
                'primary_link' => [
                    'description' => 'Основная ссылка в выпадающем меню устройства',
                    'help' => 'Устанавливает основную ссылку в выпадающем меню устройства',
                ],
            ],
        ],
        'http_auth_header' => [
            'description' => 'Имя поля, содержащее имя пользователя',
            'help' => 'Может быть переменной ENV или HTTP-заголовком, такой как REMOTE_USER, PHP_AUTH_USER или пользовательским вариантом',
        ],
        'http_proxy' => [
            'description' => 'HTTP-прокси',
            'help' => 'Установите это в качестве резервного варианта, если переменная окружения http_proxy недоступна.',
        ],
        'https_proxy' => [
            'description' => 'HTTPS-прокси',
            'help' => 'Установите это в качестве резервного варианта, если переменная окружения https_proxy недоступна.',
        ],
        'ignore_mount' => [
            'description' => 'Точки монтирования, которые следует игнорировать',
            'help' => 'Не отслеживать использование диска для этих точек монтирования',
        ],
        'ignore_mount_network' => [
            'description' => 'Игнорировать сетевые точки монтирования',
            'help' => 'Не отслеживать использование диска для сетевых точек монтирования',
        ],
        'ignore_mount_optical' => [
            'description' => 'Игнорировать оптические приводы',
            'help' => 'Не отслеживать использование диска для оптических приводов',
        ],
        'ignore_mount_removable' => [
            'description' => 'Игнорировать съемные диски',
            'help' => 'Не отслеживать использование диска для съемных устройств',
        ],
        'ignore_mount_regexp' => [
            'description' => 'Точки монтирования, соответствующие регулярному выражению, которые следует игнорировать',
            'help' => 'Не отслеживать использование диска для точек монтирования, которые соответствуют хотя бы одному из этих регулярных выражений',
        ],
        'ignore_mount_string' => [
            'description' => 'Точки монтирования, содержащие строку, которую следует игнорировать',
            'help' => 'Не отслеживать использование диска для точек монтирования, которые содержат хотя бы одну из этих строк',
        ],
        'influxdb' => [
            'db' => [
                'description' => 'База данных',
                'help' => 'Имя базы данных InfluxDB для хранения метрик',
            ],
            'enable' => [
                'description' => 'Включить',
                'help' => 'Экспортирует метрики в InfluxDB',
            ],
            'host' => [
                'description' => 'Сервер',
                'help' => 'IP-адрес или имя хоста сервера InfluxDB для отправки данных',
            ],
            'password' => [
                'description' => 'Пароль',
                'help' => 'Пароль для подключения к InfluxDB, если требуется',
            ],
            'port' => [
                'description' => 'Порт',
                'help' => 'Порт для подключения к серверу InfluxDB',
            ],
            'timeout' => [
                'description' => 'Таймаут',
                'help' => 'Время ожидания ответа от сервера InfluxDB, 0 означает использование значения по умолчанию',
            ],
            'transport' => [
                'description' => 'Транспорт',
                'help' => 'Порт для подключения к серверу InfluxDB',
                'options' => [
                    'http' => 'HTTP',
                    'https' => 'HTTPS',
                    'udp' => 'UDP',
                ],
            ],
            'username' => [
                'description' => 'Имя пользователя',
                'help' => 'Имя пользователя для подключения к InfluxDB, если требуется',
            ],
            'verifySSL' => [
                'description' => 'Проверка SSL',
                'help' => 'Проверяет, что SSL-сертификат действителен и доверен',
            ],
        ],
        'influxdbv2' => [
            'bucket' => [
                'description' => 'Корзина',
                'help' => 'Имя корзины InfluxDB для хранения метрик',
            ],
            'enable' => [
                'description' => 'Включить',
                'help' => 'Экспортирует метрики в InfluxDB с использованием API InfluxDBv2',
            ],
            'host' => [
                'description' => 'Сервер',
                'help' => 'IP-адрес или имя хоста сервера InfluxDB для отправки данных',
            ],
            'token' => [
                'description' => 'Токен',
                'help' => 'Токен для подключения к InfluxDB, если требуется',
            ],
            'port' => [
                'description' => 'Порт',
                'help' => 'Порт для подключения к серверу InfluxDB',
            ],
            'transport' => [
                'description' => 'Транспорт',
                'help' => 'Порт для подключения к серверу InfluxDB',
                'options' => [
                    'http' => 'HTTP',
                    'https' => 'HTTPS',
                ],
            ],
            'organization' => [
                'description' => 'Организация',
                'help' => 'Организация, содержащая корзину на сервере InfluxDB',
            ],
            'allow_redirects' => [
                'description' => 'Разрешить перенаправления',
                'help' => 'Разрешить перенаправления от сервера InfluxDB',
            ],
            'debug' => [
                'description' => 'Отладка',
                'help' => 'Включить или отключить подробный вывод в CLI',
            ],
            'groups-exclude' => [
                'description' => 'Исключенные группы устройств',
                'help' => 'Группы устройств, исключенные из отправки данных в InfluxDBv2',
            ],
        ],
        'ipmitool' => [
            'description' => 'Путь к ipmitool',
        ],
        'login_message' => [
            'description' => 'Сообщение для входа',
            'help' => 'Отображается на странице входа',
        ],
        'mac_oui' => [
            'enabled' => [
                'description' => 'Включить поиск MAC OUI',
                'help' => 'Включить поиск вендоров по MAC-адресу (данные загружаются с помощью daily.sh)',
            ],
        ],
        'mono_font' => [
            'description' => 'Моноширинный шрифт',
        ],
        'mtr' => [
            'description' => 'Путь к mtr',
        ],
        'mydomain' => [
            'description' => 'Основной домен',
            'help' => 'Этот домен используется для автоматического обнаружения сети и других процессов. LibreNMS попытается добавить его к неквалифицированным именам хостов.',
        ],
        'network_map_show_on_worldmap' => [
            'description' => 'Отображать сетевые ссылки на карте',
            'help' => 'Показать сетевые ссылки между разными местоположениями на мировой карте (подобно погодной карте)',
        ],
        'network_map_worldmap_show_disabled_alerts' => [
            'description' => 'Показать устройства с отключенными оповещениями',
            'help' => 'Показать устройства на сетевой карте, у которых отключены оповещения',
        ],
        'network_map_worldmap_link_type' => [
            'description' => 'Источник сетевой карты',
            'help' => 'Выберите источник данных для ссылок на сетевой карте',
        ],
        'nfsen_enable' => [
            'description' => 'Включить NfSen',
            'help' => 'Включить интеграцию с NfSen',
        ],
        'nfsen_rrds' => [
            'description' => 'Каталоги NfSen RRD',
            'help' => 'Это значение указывает, где находятся ваши файлы RRD NFSen.',
        ],
        'nfsen_subdirlayout' => [
            'description' => 'Установить макет подкаталогов NfSen',
            'help' => 'Это должно соответствовать макету подкаталогов, который вы установили в NfSen. 1 — значение по умолчанию.',
        ],
        'nfsen_last_max' => [
            'description' => 'Последний максимум',
        ],
        'nfsen_top_max' => [
            'description' => 'Максимум топа',
            'help' => 'Максимальное значение topN для статистики',
        ],
        'nfsen_top_N' => [
            'description' => 'Топ N',
        ],
        'nfsen_top_default' => [
            'description' => 'Топ N по умолчанию',
        ],
        'nfsen_stat_default' => [
            'description' => 'Статистика по умолчанию',
        ],
        'nfsen_order_default' => [
            'description' => 'Порядок по умолчанию',
        ],
        'nfsen_last_default' => [
            'description' => 'Последний по умолчанию',
        ],
        'nfsen_lasts' => [
            'description' => 'Опции последних по умолчанию',
        ],
        'nfsen_base' => [
            'description' => 'Базовый каталог NFSen',
            'help' => 'Используется для поиска графиков, специфичных для устройства',
        ],
        'nfsen_split_char' => [
            'description' => 'Символ разделения',
            'help' => 'Это значение указывает, что нужно заменить точки `.` в имени хоста устройства. Обычно: `_`',
        ],
        'nfsen_suffix' => [
            'description' => 'Суффикс имени файла',
            'help' => 'Это очень важный момент, так как имена устройств в NfSen ограничены 21 символом. Это означает, что полные доменные имена для устройств могут быть проблематичными для втиснуть, поэтому этот фрагмент обычно удаляется.',
        ],
        'nmap' => [
            'description' => 'Путь к nmap',
        ],
        'no_proxy' => [
            'description' => 'Исключения прокси',
            'help' => 'Установите это в качестве резервного варианта, если переменная окружения no_proxy недоступна. Список IP, хостов или доменов, которые следует игнорировать, разделенный запятыми.',
        ],
        'opentsdb' => [
            'enable' => [
                'description' => 'Включить',
                'help' => 'Экспортирует метрики в OpenTSDB',
            ],
            'host' => [
                'description' => 'Сервер',
                'help' => 'IP-адрес или имя хоста сервера OpenTSDB для отправки данных',
            ],
            'port' => [
                'description' => 'Порт',
                'help' => 'Порт для подключения к серверу OpenTSDB',
            ],
        ],
        'own_hostname' => [
            'description' => 'Имя хоста LibreNMS',
            'help' => 'Должно быть установлено на имя хоста/IP, под которым сервер librenms добавлен',
        ],
        'oxidized' => [
            'default_group' => [
                'description' => 'Установить группу по умолчанию для возврата',
            ],
            'ignore_groups' => [
                'description' => 'Не делать резервные копии этих групп Oxidized',
                'help' => 'Группы (установленные через Переменное отображение), исключенные из отправки в Oxidized',
            ],
            'enabled' => [
                'description' => 'Включить поддержку Oxidized',
            ],
            'features' => [
                'versioning' => [
                    'description' => 'Включить доступ к версии конфигурации',
                    'help' => 'Включить версионирование конфигурации Oxidized (требуется бэкенд git)',
                ],
            ],
            'group_support' => [
                'description' => 'Включить возврат групп в Oxidized',
            ],
            'ignore_os' => [
                'description' => 'Не делать резервные копии этих ОС',
                'help' => 'Не делать резервные копии указанных ОС с помощью Oxidized. ОС должны соответствовать названию ОС LibreNMS (все строчные буквы без пробелов). Разрешены только существующие ОС.',
            ],
            'ignore_types' => [
                'description' => 'Не делать резервные копии этих типов устройств',
                'help' => 'Не делать резервные копии указанных типов устройств с помощью Oxidized. Разрешены только существующие типы.',
            ],
            'reload_nodes' => [
                'description' => 'Перезагрузить список узлов Oxidized каждый раз, когда добавляется устройство',
            ],
            'maps' => [
                'description' => 'Отображение переменных',
                'help' => 'Используется для установки группы или других переменных или отображения названий ОС, которые отличаются.',
            ],
            'url' => [
                'description' => 'URL вашего API Oxidized',
                'help' => 'URL API Oxidized (например: http://127.0.0.1:8888)',
            ],
        ],
        'password' => [
            'min_length' => [
                'description' => 'Минимальная длина пароля',
                'help' => 'Пароли короче указанной длины будут отклонены',
            ],
        ],
        'peeringdb' => [
            'enabled' => [
                'description' => 'Включить поиск в PeeringDB',
                'help' => 'Включить поиск в PeeringDB (данные загружаются с помощью daily.sh)',
            ],
        ],
        'permission' => [
            'device_group' => [
                'allow_dynamic' => [
                    'description' => 'Включить доступ пользователей через динамические группы устройств',
                ],
            ],
        ],
        'bad_if' => [
            'description' => 'Плохие имена интерфейсов',
            'help' => 'Сетевой интерфейс IF-MIB:!:ifName, который следует игнорировать',
        ],
        'bad_if_regexp' => [
            'description' => 'Регулярное выражение для плохих имен интерфейсов',
            'help' => 'Сетевой интерфейс IF-MIB:!:ifName, который следует игнорировать, используя регулярные выражения',
        ],
        'bad_ifoperstatus' => [
            'description' => 'Плохой статус работы интерфейса',
            'help' => 'Сетевой интерфейс IF-MIB:!:ifOperStatus, который следует игнорировать',
        ],
        'bad_iftype' => [
            'description' => 'Плохие типы интерфейсов',
            'help' => 'Сетевой интерфейс IF-MIB:!:ifType, который следует игнорировать',
        ],
        'ping' => [
            'description' => 'Путь к ping',
        ],
        'ping_rrd_step' => [
            'description' => 'Частота пинга',
            'help' => 'Как часто проверять. Устанавливает значение по умолчанию для всех узлов. Внимание! Если вы измените это, вам нужно будет внести дополнительные изменения. Ознакомьтесь с документацией по Fast Ping.',
        ],
        'poller_modules' => [
            'unix-agent' => [
                'description' => 'Unix-агент',
            ],
            'os' => [
                'description' => 'ОС',
            ],
            'ipmi' => [
                'description' => 'IPMI',
            ],
            'sensors' => [
                'description' => 'Датчики',
            ],
            'processors' => [
                'description' => 'Процессоры',
            ],
            'mempools' => [
                'description' => 'Память',
            ],
            'storage' => [
                'description' => 'Хранилище',
            ],
            'netstats' => [
                'description' => 'Сетевые статистики',
            ],
            'hr-mib' => [
                'description' => 'HR MIB',
            ],
            'ucd-mib' => [
                'description' => 'UCD MIB',
            ],
            'ipSystemStats' => [
                'description' => 'ipSystemStats',
            ],
            'ports' => [
                'description' => 'Порты',
            ],
            'bgp-peers' => [
                'description' => 'BGP-соседи',
            ],
            'junose-atm-vp' => [
                'description' => 'JunOS ATM VP',
            ],
            'ucd-diskio' => [
                'description' => 'UCD DiskIO',
            ],
            'wireless' => [
                'description' => 'Беспроводная связь',
            ],
            'ospf' => [
                'description' => 'OSPF',
            ],
            'isis' => [
                'description' => 'ISIS',
            ],
            'cisco-ipsec-flow-monitor' => [
                'description' => 'Монитор потока Cisco IPSec',
            ],
            'cisco-remote-access-monitor' => [
                'description' => 'Монитор удаленного доступа Cisco',
            ],
            'cisco-cef' => [
                'description' => 'Cisco CEF',
            ],
            'slas' => [
                'description' => 'Отслеживание соглашений об уровне обслуживания',
            ],
            'cisco-mac-accounting' => [
                'description' => 'Учет MAC Cisco',
            ],
            'cipsec-tunnels' => [
                'description' => 'Туннели Cipsec',
            ],
            'cisco-ace-loadbalancer' => [
                'description' => 'Балансировщик нагрузки Cisco ACE',
            ],
            'cisco-ace-serverfarms' => [
                'description' => 'Серверные фермы Cisco ACE',
            ],
            'cisco-asa-firewall' => [
                'description' => 'Firewall Cisco ASA',
            ],
            'cisco-cbqos' => [
                'description' => 'Cisco CBQOS',
            ],
            'cisco-otv' => [
                'description' => 'Cisco OTV',
            ],
            'cisco-qfp' => [
                'description' => 'Cisco QFP',
            ],
            'cisco-vpdn' => [
                'description' => 'Cisco VPDN',
            ],
            'nac' => [
                'description' => 'NAC',
            ],
            'netscaler-vsvr' => [
                'description' => 'Netscaler VSVR',
            ],
            'aruba-controller' => [
                'description' => 'Контроллер Aruba',
            ],
            'availability' => [
                'description' => 'Доступность',
            ],
            'entity-physical' => [
                'description' => 'Физические сущности',
            ],
            'entity-state' => [
                'description' => 'Состояние сущности',
            ],
            'applications' => [
                'description' => 'Приложения',
            ],
            'stp' => [
                'description' => 'STP',
            ],
            'vminfo' => [
                'description' => 'Информация о виртуальных машинах Hypervisor',
            ],
            'ntp' => [
                'description' => 'NTP',
            ],
            'loadbalancers' => [
                'description' => 'Балансировщики нагрузки',
            ],
            'mef' => [
                'description' => 'MEF',
            ],
            'mpls' => [
                'description' => 'MPLS',
            ],
            'xdsl' => [
                'description' => 'xDSL',
            ],
            'printer-supplies' => [
                'description' => 'Расходные материалы для принтеров',
            ],
        ],
        'ports_fdb_purge' => [
            'description' => 'Записи FDB порта старше',
            'help' => 'Очистка выполняется daily.sh',
        ],
        'ports_nac_purge' => [
            'description' => 'Записи NAC порта старше',
            'help' => 'Очистка выполняется daily.sh',
        ],
        'ports_purge' => [
            'description' => 'Очистка удаленных портов',
            'help' => 'Очистка выполняется daily.sh',
        ],
        'prometheus' => [
            'enable' => [
                'description' => 'Включить',
                'help' => 'Экспортирует метрики в Prometheus Push Gateway',
            ],
            'url' => [
                'description' => 'URL',
                'help' => 'URL Prometheus Push Gateway для отправки данных',
            ],
            'Job' => [
                'description' => 'Работа',
                'help' => 'Метка работы для экспортируемых метрик',
            ],
            'attach_sysname' => [
                'description' => 'Прикрепить sysName устройства',
                'help' => 'Прикрепить информацию sysName к Prometheus.',
            ],
            'prefix' => [
                'description' => 'Префикс',
                'help' => 'Дополнительный текст, который будет добавлен к именам экспортируемых метрик',
            ],
        ],
        'public_status' => [
            'description' => 'Показать статус публично',
            'help' => 'Показывает статус некоторых устройств на странице входа без аутентификации.',
        ],
        'routes_max_number' => [
            'description' => 'Максимальное количество маршрутов, разрешенных для обнаружения',
            'help' => 'Маршрут не будет обнаружен, если размер таблицы маршрутизации превышает это число',
        ],
        'default_port_group' => [
            'description' => 'Группа портов по умолчанию',
            'help' => 'Новые обнаруженные порты будут назначены в эту группу портов.',
        ],
        'nets' => [
            'description' => 'Автообнаруживаемые сети',
            'help' => 'Сети, из которых устройства будут обнаруживаться автоматически.',
        ],
        'autodiscovery' => [
            'nets-exclude' => [
                'description' => 'Сети/IP, которые нужно игнорировать',
                'help' => 'Сети/IP, которые не будут автоматически обнаружены. Исключает также IP из автообнаруживаемых сетей',
            ],
        ],
        'radius' => [
            'default_roles' => [
                'description' => 'Роли пользователей по умолчанию',
                'help' => 'Устанавливает роли, которые будут назначены пользователю, если Radius не отправляет атрибуты, указывающие роль(и)',
            ],
            'enforce_roles' => [
                'description' => 'Принудительное применение ролей при входе',
                'help' => 'Если включено, роли будут установлены в те, что указаны атрибутом Filter-ID или radius.default_roles при входе. В противном случае они будут установлены при создании пользователя и никогда не изменятся после этого.',
            ],
        ],
        'reporting' => [
            'error' => [
                'description' => 'Отправить отчеты об ошибках',
                'help' => 'Отправляет некоторые ошибки в LibreNMS для анализа и исправления',
            ],
            'usage' => [
                'description' => 'Отправить отчеты об использовании',
                'help' => 'Отправляет отчеты о использовании и версиях в LibreNMS. Чтобы удалить анонимную статистику, посетите страницу "О программе". Вы можете просмотреть статистику на https://stats.librenms.org',
            ],
            'dump_errors' => [
                'description' => 'Сбросить отладочные ошибки (может сломать вашу установку)',
                'help' => 'Сбрасывает ошибки, которые обычно скрыты, чтобы вы как разработчик могли найти и исправить возможные проблемы.',
            ],
            'throttle' => [
                'description' => 'Ограничить отчеты об ошибках',
                'help' => 'Отчеты будут отправляться только каждые указанные секунды. Без этого, если у вас есть ошибка в общем коде, отчеты могут стать неуправляемыми. Установите 0, чтобы отключить ограничение.',
            ],
        ],
        'route_purge' => [
            'description' => 'Записи маршрутов старше',
            'help' => 'Очистка выполняется daily.sh',
        ],
        'rrd' => [
            'heartbeat' => [
                'description' => 'Изменить значение heartbeat rrd (по умолчанию 600)',
            ],
            'step' => [
                'description' => 'Изменить значение step rrd (по умолчанию 300)',
            ],
        ],
        'rrd_dir' => [
            'description' => 'Расположение RRD',
            'help' => 'Местоположение файлов rrd. По умолчанию это rrd в директории LibreNMS. Изменение этой настройки не переместит файлы rrd.',
        ],
        'rrd_purge' => [
            'description' => 'Записи файлов RRD старше',
            'help' => 'Очистка выполняется daily.sh',
        ],
        'rrd_rra' => [
            'description' => 'Настройки формата RRD',
            'help' => 'Эти настройки не могут быть изменены без удаления ваших существующих файлов RRD. Хотя можно было бы увеличить или уменьшить размер каждого RRA, если у вас есть проблемы с производительностью или если у вас очень быстрая подсистема I/O без проблем с производительностью.',
        ],
        'rrdcached' => [
            'description' => 'Включить rrdcached (сокет)',
            'help' => 'Включает rrdcached, устанавливая местоположение сокета rrdcached. Может быть unix или сетевой сокет (unix:/run/rrdcached.sock или localhost:42217)',
        ],
        'rrdtool' => [
            'description' => 'Путь к rrdtool',
        ],
        'rrdtool_tune' => [
            'description' => 'Настроить все rrd файлы портов для использования максимальных значений',
            'help' => 'Автоматически настраивает максимальное значение для файлов rrd портов',
        ],
        'rrdtool_version' => [
            'description' => 'Устанавливает версию rrdtool на вашем сервере',
            'help' => 'Все версии выше 1.5.5 поддерживают все функции, используемые LibreNMS, не устанавливайте выше вашей установленной версии',
        ],
        'schedule_type' => [
            'alerting' => [
                'description' => 'Оповещения',
                'help' => 'Метод планирования задач оповещения. Устаревший метод будет использовать cron, если запись crontab существует, и службу диспетчера, если устаревшая конфигурация service_billing_enabled установлена в true.',
                'options' => [
                    'legacy' => 'Устаревший (Без ограничений)',
                    'cron' => 'Cron (alerts.php)',
                    'dispatcher' => 'Служба диспетчера',
                ],
            ],
            'billing' => [
                'description' => 'Выставление счетов',
                'help' => 'Метод планирования задач выставления счетов. Устаревший метод будет использовать cron, если запись crontab существует, и службу диспетчера, если устаревшая конфигурация service_billing_enabled установлена в true.',
                'options' => [
                    'legacy' => 'Устаревший (Без ограничений)',
                    'cron' => 'Cron (poll-billing.php и billing-calculate.php)',
                    'dispatcher' => 'Служба диспетчера',
                ],
            ],
            'discovery' => [
                'description' => 'Обнаружение',
                'help' => 'Метод планирования задач обнаружения. Устаревший метод будет использовать cron, если запись crontab существует, и службу диспетчера, если устаревшая конфигурация service_discovery_enabled установлена в true.',
                'options' => [
                    'legacy' => 'Устаревший (Без ограничений)',
                    'cron' => 'Cron (discovery.php)',
                    'dispatcher' => 'Служба диспетчера',
                ],
            ],
            'ping' => [
                'description' => 'Быстрый пинг',
                'help' => 'Метод планирования задач быстрого пинга. Устаревший метод будет использовать cron, если запись crontab существует, и использовать службу диспетчера, если устаревшая конфигурация service_ping_enabled установлена в true.',
                'options' => [
                    'legacy' => 'Устаревший (Без ограничений)',
                    'disabled' => 'Отключено (пинги только во время опроса)',
                    'cron' => 'Cron (ping.php)',
                    'dispatcher' => 'Служба диспетчера',
                ],
            ],
            'poller' => [
                'description' => 'Опрос',
                'help' => 'Метод планирования задач опроса. Устаревший метод будет использовать cron, если запись crontab существует, и службу диспетчера, если устаревшая конфигурация service_poller_enabled установлена в true.',
                'options' => [
                    'legacy' => 'Устаревший (Без ограничений)',
                    'cron' => 'Cron (poller.php)',
                    'dispatcher' => 'Служба диспетчера',
                ],
            ],
            'services' => [
                'description' => 'Сервисы',
                'help' => 'Метод планирования задач сервисов. Устаревший метод будет использовать cron, если запись crontab существует, и службу диспетчера, если устаревшая конфигурация service_services_enabled установлена в true.',
                'options' => [
                    'legacy' => 'Устаревший (Без ограничений)',
                    'cron' => 'Cron (check-services.php)',
                    'dispatcher' => 'Служба диспетчера',
                ],
            ],
        ],
        'service_master_timeout' => [
            'description' => 'Таймаут мастер-диспетчера',
            'help' => 'Время, через которое истекает блокировка мастера. Если мастер отключается, потребуется это время, чтобы другой узел взял на себя управление. Однако, если на выполнение работы потребуется больше времени, чем таймаут, у вас будет несколько мастеров.',
        ],
        'service_poller_workers' => [
            'description' => 'Рабочие процессы опроса',
            'help' => 'Количество рабочих процессов опроса для запуска. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_poller_frequency' => [
            'description' => 'Частота опроса (Предупреждение!)',
            'help' => 'Как часто опрашивать устройства. Устанавливает значение по умолчанию для всех узлов. Предупреждение! Изменение этого без исправления файлов rrd приведет к поломке графиков. Смотрите документацию для получения дополнительной информации.',
        ],
        'service_poller_down_retry' => [
            'description' => 'Повторная попытка при отключенном устройстве',
            'help' => 'Если устройство отключено во время попытки опроса. Это время ожидания перед повторной попыткой. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_discovery_workers' => [
            'description' => 'Рабочие процессы обнаружения',
            'help' => 'Количество рабочих процессов для запуска обнаружения. Слишком высокое значение может вызвать перегрузку. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_discovery_frequency' => [
            'description' => 'Частота обнаружения',
            'help' => 'Как часто выполнять обнаружение устройств. Устанавливает значение по умолчанию для всех узлов. По умолчанию 4 раза в день.',
        ],
        'service_services_workers' => [
            'description' => 'Рабочие процессы сервисов',
            'help' => 'Количество рабочих процессов для сервисов. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_services_frequency' => [
            'description' => 'Частота сервисов',
            'help' => 'Как часто запускать сервисы. Это должно совпадать с частотой опроса. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_billing_frequency' => [
            'description' => 'Частота выставления счетов',
            'help' => 'Как часто собирать данные для выставления счетов. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_billing_calculate_frequency' => [
            'description' => 'Частота расчета выставления счетов',
            'help' => 'Как часто рассчитывать использование для выставления счетов. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_alerting_frequency' => [
            'description' => 'Частота оповещений',
            'help' => 'Как часто проверяются правила оповещения. Обратите внимание, что данные обновляются только на основе частоты опроса. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_update_enabled' => [
            'description' => 'Ежедневное обслуживание включено',
            'help' => 'Запускает скрипт обслуживания daily.sh и перезапускает службу диспетчера после этого. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_update_frequency' => [
            'description' => 'Частота обслуживания',
            'help' => 'Как часто выполнять ежедневное обслуживание. По умолчанию 1 день. Настоятельно рекомендуется не изменять это значение. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_loglevel' => [
            'description' => 'Уровень логирования',
            'help' => 'Уровень логирования службы диспетчера. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_watchdog_enabled' => [
            'description' => 'Мониторинг включен',
            'help' => 'Мониторинг следит за файлом журнала и перезапускает службу, если он не обновляется. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'service_watchdog_log' => [
            'description' => 'Файл журнала для мониторинга',
            'help' => 'По умолчанию это файл журнала LibreNMS. Устанавливает значение по умолчанию для всех узлов.',
        ],
        'sfdp' => [
            'description' => 'Путь к sfdp',
        ],
        'shorthost_target_length' => [
            'description' => 'Максимальная длина сокращенного имени хоста',
            'help' => 'Сжимает имя хоста до максимальной длины, но всегда сохраняет полные части поддомена.',
        ],
        'site_style' => [
            'description' => 'Тема по умолчанию',
            'options' => [
                'blue' => 'Синий',
                'dark' => 'Темный',
                'light' => 'Светлый',
                'mono' => 'Моно',
            ],
        ],
        'snmp' => [
            'transports' => [
                'description' => 'Транспорт (приоритет)',
                'help' => 'Выберите включенные транспорты и упорядочите их по желаемому порядку.',
            ],
            'version' => [
                'description' => 'Версия (приоритет)',
                'help' => 'Выберите включенные версии и упорядочите их по желаемому порядку.',
            ],
            'community' => [
                'description' => 'Сообщества (приоритет)',
                'help' => 'Введите строки сообщества для v1 и v2c и упорядочите их по желаемому порядку.',
            ],
            'max_oid' => [
                'description' => 'Максимальные OID',
                'help' => 'Максимальное количество OID на запрос. Может быть переопределено на уровне ОС и устройства.',
            ],
            'max_repeaters' => [
                'description' => 'Максимальное количество повторителей',
                'help' => 'Установите повторители для использования в SNMP-запросах с использованием bulk.',
            ],
            'oids' => [
                'no_bulk' => [
                    'description' => 'Отключить SNMP bulk для OID',
                    'help' => 'Отключить операцию SNMP bulk для определенных OID. Обычно это следует установить на уровне ОС. Формат должен быть MIB::OID.',
                ],
                'unordered' => [
                    'description' => 'Разрешить неупорядоченные SNMP-ответы для OID',
                    'help' => 'Игнорировать неупорядоченные OID в SNMP-ответах для определенных OID. Неупорядоченные OID могут привести к циклу OID во время SNMP-обхода. Обычно это следует установить на уровне ОС. Формат должен быть MIB::OID.',
                ],
            ],
            'port' => [
                'description' => 'Порт',
                'help' => 'Установите tcp/udp порт для использования SNMP.',
            ],
            'timeout' => [
                'description' => 'Таймаут',
                'help' => 'Таймаут SNMP в секундах.',
            ],
            'retries' => [
                'description' => 'Повторные попытки',
                'help' => 'Сколько раз повторить запрос.',
            ],
            'v3' => [
                'description' => 'Аутентификация SNMP v3 (приоритет)',
                'help' => 'Настройте переменные аутентификации v3 и упорядочите их по желаемому порядку.',
                'auth' => 'Аутентификация',
                'crypto' => 'Шифрование',
                'fields' => [
                    'authalgo' => 'Алгоритм',
                    'authlevel' => 'Уровень',
                    'authname' => 'Имя пользователя',
                    'authpass' => 'Пароль',
                    'cryptoalgo' => 'Алгоритм',
                    'cryptopass' => 'Пароль',
                ],
                'level' => [
                    'noAuthNoPriv' => 'Без аутентификации, без конфиденциальности',
                    'authNoPriv' => 'Аутентификация, без конфиденциальности',
                    'authPriv' => 'Аутентификация и конфиденциальность',
                ],
            ],
        ],
        'snmpbulkwalk' => [
            'description' => 'Путь к snmpbulkwalk',
        ],
        'snmpget' => [
            'description' => 'Путь к snmpget',
        ],
        'snmpgetnext' => [
            'description' => 'Путь к snmpgetnext',
        ],
        'snmptranslate' => [
            'description' => 'Путь к snmptranslate',
        ],
        'snmptraps' => [
            'eventlog' => [
                'description' => 'Создать журнал событий для snmptraps',
                'help' => 'Независимо от действия, которое может быть сопоставлено с ловушкой.',
            ],
            'eventlog_detailed' => [
                'description' => 'Включить детализированные журналы',
                'help' => 'Добавить все OID, полученные с ловушкой, в журнал событий.',
            ],
        ],
        'snmpwalk' => [
            'description' => 'Путь к snmpwalk',
        ],
        'syslog_filter' => [
            'description' => 'Фильтр сообщений syslog, содержащих',
        ],
        'syslog_purge' => [
            'description' => 'Записи syslog старше',
            'help' => 'Очистка выполняется daily.sh.',
        ],
        'title_image' => [
            'description' => 'Изображение заголовка',
            'help' => 'Заменяет изображение заголовка по умолчанию.',
        ],
        'traceroute' => [
            'description' => 'Путь к traceroute',
        ],
        'twofactor' => [
            'description' => 'Двухфакторная аутентификация',
            'help' => 'Позволяет пользователям активировать и использовать временные (TOTP) или основанные на счетчике (HOTP) одноразовые пароли (OTP).',
        ],
        'twofactor_lock' => [
            'description' => 'Время блокировки двухфакторной аутентификации (секунды)',
            'help' => 'Время блокировки, которое нужно ждать в секундах перед тем, как разрешить дальнейшие попытки, если двухфакторная аутентификация не удалась 3 раза подряд - пользователь будет проинформирован о необходимости подождать это время. Установите 0, чтобы отключить, что приведет к постоянной блокировке аккаунта и сообщению пользователю обратиться к администратору.',
        ],
        'unix-agent' => [
            'connection-timeout' => [
                'description' => 'Таймаут соединения unix-agent',
            ],
            'port' => [
                'description' => 'Порт по умолчанию для unix-agent',
                'help' => 'Порт по умолчанию для unix-agent (check_mk).',
            ],
            'read-timeout' => [
                'description' => 'Таймаут чтения unix-agent',
            ],
        ],
        'update' => [
            'description' => 'Включить обновления в ./daily.sh',
        ],
        'update_channel' => [
            'description' => 'Канал обновлений',
            'options' => [
                'master' => 'Ежедневно',
                'release' => 'Ежемесячно',
            ],
        ],
        'uptime_warning' => [
            'description' => 'Показывать устройство как предупреждение, если время работы ниже (секунды)',
            'help' => 'Показывает устройство как предупреждение, если время работы ниже этого значения. По умолчанию 24 часа.',
        ],
        'virsh' => [
            'description' => 'Путь к virsh',
        ],
        'webui' => [
            'availability_map_box_size' => [
                'description' => 'Ширина коробки доступности',
                'help' => 'Введите желаемую ширину плитки в пикселях для размера коробки в полном режиме',
            ],
            'availability_map_compact' => [
                'description' => 'Компактный вид карты доступности',
                'help' => 'Вид карты доступности с маленькими индикаторами',
            ],
            'availability_map_sort_status' => [
                'description' => 'Сортировать по статусу',
                'help' => 'Сортировать устройства и службы по статусу',
            ],
            'availability_map_use_device_groups' => [
                'description' => 'Использовать фильтр групп устройств',
                'help' => 'Включить использование фильтра групп устройств',
            ],
            'default_dashboard_id' => [
                'description' => 'Дашборд по умолчанию',
                'help' => 'Глобальный идентификатор дашборда по умолчанию для всех пользователей, у которых не установлен собственный дашборд по умолчанию',
            ],
            'dynamic_graphs' => [
                'description' => 'Включить динамические графики',
                'help' => 'Включить динамические графики, позволяет масштабирование и панорамирование на графиках',
            ],
            'global_search_result_limit' => [
                'description' => 'Установить максимальный лимит результатов поиска',
                'help' => 'Глобальный лимит результатов поиска',
            ],
            'graph_stacked' => [
                'description' => 'Использовать сложенные графики',
                'help' => 'Отображать сложенные графики вместо инвертированных графиков',
            ],
            'graph_type' => [
                'description' => 'Установить тип графика',
                'help' => 'Установить тип графика по умолчанию',
                'options' => [
                    'png' => 'PNG',
                    'svg' => 'SVG',
                ],
            ],
            'min_graph_height' => [
                'description' => 'Установить минимальную высоту графика',
                'help' => 'Минимальная высота графика (по умолчанию: 300)',
            ],
            'graph_stat_percentile_disable' => [
                'description' => 'Отключить процентиль для статистических графиков глобально',
                'help' => 'Отключает отображение значений и линий процентиля для графиков, которые их отображают',
            ],
        ],
        'device_display_default' => [
            'description' => 'Шаблон имени устройства по умолчанию',
            'help' => 'Устанавливает имя для отображения по умолчанию для всех устройств (можно переопределить для каждого устройства).  Имя хоста/IP: Просто показать имя хоста или IP, с которым устройство было добавлено. sysName: Просто показать sysName из snmp. Имя хоста или sysName: Показать имя хоста, но если это IP, показать sysName.',
            'options' => [
                'hostname' => 'Имя хоста / IP',
                'sysName_fallback' => 'Имя хоста, резервное копирование на sysName для IP',
                'sysName' => 'sysName',
                'ip' => 'IP (из имени хоста или разрешенного IP)',
            ],
        ],
        'device_location_map_open' => [
            'description' => 'Карта местоположения открыта',
            'help' => 'Карта местоположения показывается по умолчанию',
        ],
        'device_location_map_show_devices' => [
            'description' => 'Показать устройства на карте местоположения',
            'help' => 'Показать все устройства на карте местоположения, когда она видима',
        ],
        'device_location_map_show_device_dependencies' => [
            'description' => 'Показать зависимости устройств на карте местоположения',
            'help' => 'Показать связи между устройствами на карте местоположения на основе родительских зависимостей',
        ],
        'whois' => [
            'description' => 'Путь к whois',
        ],
        'smokeping.integration' => [
            'description' => 'Включить',
            'help' => 'Включить интеграцию smokeping',
        ],
        'smokeping.dir' => [
            'description' => 'Путь к rrds',
            'help' => 'Полный путь к RRD Smokeping',
        ],
        'smokeping.pings' => [
            'description' => 'Пинги',
            'help' => 'Количество пингов, настроенных в Smokeping',
        ],
        'smokeping.url' => [
            'description' => 'URL для smokeping',
            'help' => 'Полный URL для графического интерфейса smokeping',
        ],
    ],
    'twofactor' => [
        'description' => 'Включить двухфакторную аутентификацию',
        'help' => 'Включает встроенную двухфакторную аутентификацию. Вы должны настроить каждую учетную запись, чтобы активировать эту функцию.',
    ],
    'units' => [
        'days' => 'дни',
        'ms' => 'мс',
        'seconds' => 'секунды',
    ],
    'validate' => [
        'boolean' => ':value не является допустимым булевым значением',
        'color' => ':value не является допустимым шестнадцатеричным кодом цвета',
        'email' => ':value не является допустимым адресом электронной почты',
        'float' => ':value не является числом с плавающей точкой',
        'integer' => ':value не является целым числом',
        'password' => 'Пароль неверный',
        'select' => ':value не является допустимым значением',
        'text' => ':value не разрешено',
        'array' => 'Неверный формат',
        'executable' => ':value не является допустимым исполняемым файлом',
        'directory' => ':value не является допустимым каталогом',
    ],
];
