<?php

return [
    'checks' => [
        'comment' => 'Комментарий',
        'item' => 'Элемент',
        'php_required' => 'Требуется :version или выше',
        'status' => 'Статус',
        'title' => 'Предварительные проверки установки',
    ],
    'database' => [
        'credentials' => 'Учетные данные базы данных',
        'host' => 'Хост',
        'host_placeholder' => 'Используйте localhost для Unix-Socket',
        'name' => 'Имя базы данных',
        'password' => 'Пароль',
        'port' => 'Порт',
        'port_placeholder' => 'Оставьте пустым, если используете Unix-Socket',
        'socket' => 'Unix-Socket',
        'socket_placeholder' => 'Используйте только для пользовательского пути сокета',
        'test' => 'Проверить учетные данные',
        'title' => 'Настроить базу данных',
        'username' => 'Пользователь',
    ],
    'finish' => [
        'config_exists' => 'Файл config.php существует',
        'config_not_required' => 'Этот файл не требуется. Вот значение по умолчанию.',
        'config_not_written' => 'Не удалось записать config.php',
        'config_written' => 'Файл config.php записан',
        'copied' => 'Скопировано в буфер обмена',
        'dashboard' => 'Панель управления',
        'env_manual' => 'Обновите вручную :file с следующим содержимым',
        'env_not_written' => 'Не удалось записать файл .env',
        'env_written' => 'Файл .env записан',
        'failed' => 'Не удалось сохранить .env',
        'finish' => 'Завершить установку',
        'manual_copy' => 'Нажмите Ctrl-C для копирования',
        'retry' => 'Повторить',
        'settings' => 'Дополнительные настройки',
        'success' => 'Установка завершена',
        'thanks' => 'Спасибо за настройку LibreNMS.',
        'title' => 'Завершить установку',
        'validate_button' => 'Проверить установку',
    ],
    'install' => 'Установить',
    'migrate' => [
        'building_interrupt' => 'Не закрывайте эту страницу и не прерывайте импорт!',
        'error' => 'Обнаружена ошибка, проверьте вывод для получения деталей.',
        'migrate' => 'Создать базу данных',
        'retry' => 'Повторить',
        'timeout' => 'HTTP-запрос превысил время ожидания, структура вашей базы данных может быть неконсистентной.',
        'wait' => 'Пожалуйста, подождите...',
    ],
    'steps' => [
        'checks' => 'Предварительные проверки установки',
        'database' => 'База данных',
        'finish' => 'Завершить установку',
        'migrate' => 'Создать базу данных',
        'user' => 'Создать пользователя',
    ],
    'title' => 'Установка LibreNMS',
    'user' => [
        'button' => 'Добавить пользователя',
        'created' => 'Пользователь создан',
        'email' => 'Электронная почта',
        'failure' => 'Не удалось создать пользователя',
        'password' => 'Пароль',
        'success' => 'Пользователь успешно создан',
        'title' => 'Создать администратора',
        'username' => 'Имя пользователя',
    ],
];
