<?php

return [
    'stp_info' => 'STP Instance Information',
    'stp_ports' => 'STP Ports',
    'vlan' => 'VLAN',
    'root_bridge' => 'Root bridge',
    'bridge_address' => 'Bridge address (MAC)',
    'protocol' => 'Protocol specification',
    'priority' => 'Priority',
    'last_topology_change' => 'Time since topology change',
    'topology_changes' => 'Topology changes',
    'designated_root' => 'Designated root (MAC)',
    'root_cost' => 'Root cost',
    'root_port' => 'Root port',
    'max_age' => 'Max age (s)',
    'hello_time' => 'Hello time (s)',
    'hold_time' => 'Hold time (s)',
    'forward_delay' => 'Forward delay (s)',
    'bridge_max_age' => 'Bridge max age (s)',
    'bridge_hello_time' => 'Bridge hello time (s)',
    'bridge_forward_delay' => 'Bridge forward delay (s)',
];
