<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Wireless Sensors Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to translate names and units of wireless sensors
    |
    */

    'title' => 'Wireless',
    'ap-count' => [
        'short' => 'APs',
        'long' => 'AP Count',
        'unit' => '',
    ],
    'clients' => [
        'short' => 'Clients',
        'long' => 'Client Count',
        'unit' => '',
    ],
    'capacity' => [
        'short' => 'Capacity',
        'long' => 'Capacity',
        'unit' => '%',
    ],
    'ccq' => [
        'short' => 'CCQ',
        'long' => 'Client Connection Quality',
        'unit' => '%',
    ],
    'errors' => [
        'short' => 'Errors',
        'long' => 'Error Count',
        'unit' => '',
    ],
    'error-ratio' => [
        'short' => 'Error Ratio',
        'long' => 'Bit/Packet Error Ratio',
        'unit' => '%',
    ],
    'error-rate' => [
        'short' => 'BER',
        'long' => 'Bit Error Rate',
        'unit' => 'bps',
    ],
    'frequency' => [
        'short' => 'Frequency',
        'long' => 'Frequency',
        'unit' => 'MHz',
    ],
    'distance' => [
        'short' => 'Distance',
        'long' => 'Distance',
        'unit' => 'm',
    ],
    'mse' => [
        'short' => 'MSE',
        'long' => 'Mean Square Error',
        'unit' => 'dB',
    ],
    'noise-floor' => [
        'short' => 'Noise Floor',
        'long' => 'Noise Floor',
        'unit' => 'dBm',
    ],
    'power' => [
        'short' => 'Power/Signal',
        'long' => 'TX/RX Power or Signal',
        'unit' => 'dBm',
    ],
    'quality' => [
        'short' => 'Quality',
        'long' => 'Quality',
        'unit' => '%',
    ],
    'rate' => [
        'short' => 'Rate',
        'long' => 'TX/RX Rate',
        'unit' => 'bps',
    ],
    'rssi' => [
        'short' => 'RSSI',
        'long' => 'Received Signal Strength Indicator',
        'unit' => 'dBm',
    ],
    'snr' => [
        'short' => 'SNR',
        'long' => 'Signal-to-Noise Ratio',
        'unit' => 'dB',
    ],
    'sinr' => [
        'short' => 'SINR',
        'long' => ' Signal-to-Interference-plus-Noise Ratio',
        'unit' => 'dB',
    ],
    'rsrq' => [
        'short' => 'RSRQ',
        'long' => 'Reference Signal Received Quality',
        'unit' => 'dB',
    ],
    'rsrp' => [
        'short' => 'RSRP',
        'long' => 'Reference Signals Received Power',
        'unit' => 'dBm',
    ],
    'ssr' => [
        'short' => 'SSR',
        'long' => 'Signal Strength Ratio',
        'unit' => 'dB',
    ],
    'utilization' => [
        'short' => 'Utilization',
        'long' => 'utilization',
        'unit' => '%',
    ],
    'xpi' => [
        'short' => 'XPI',
        'long' => 'Cross Polar Interference',
        'unit' => 'dB',
    ],
    'cell' => [
        'short' => 'Cell',
        'long' => 'Cell',
        'unit' => '',
    ],
    'channel' => [
        'short' => 'Channel',
        'long' => 'Channel',
        'unit' => '',
    ],
    'width' => [
        'short' => 'Width',
        'long' => 'Width',
        'unit' => '',
    ],
    'nodeinfo' => [
        'short' => 'Node Info',
        'long' => 'Node Info',
        'unit' => '',
    ],
    'clients-rate' => [
        'short' => 'Clients Rate',
        'long' => 'Clients Rate',
        'unit' => '',
    ],
];
