<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Sensors Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to translate names and units of sensors
    |
    */

    'title' => 'Sensors',
    'airflow' => [
        'short' => 'Airflow',
        'long' => 'Airflow',
        'unit' => 'cfm',
        'unit_long' => 'Cubic Feet per Minute',
    ],
    'ber' => [
        'short' => 'BER',
        'long' => 'Bit Error Rate',
        'unit' => '',
        'unit_long' => '',
    ],
    'charge' => [
        'short' => 'Charge',
        'long' => 'Charge Percent',
        'unit' => '%',
        'unit_long' => 'Percent',
    ],
    'chromatic_dispersion' => [
        'short' => 'Chromatic Dispersion',
        'long' => 'Chromatic Dispersion',
        'unit' => 'ps/nm',
        'unit_long' => 'Picoseconds per Nanometer',
    ],
    'cooling' => [
        'short' => 'Cooling',
        'long' => '',
        'unit' => 'W',
        'unit_long' => 'Watts',
    ],
    'count' => [
        'short' => 'Count',
        'long' => 'Count',
        'unit' => '',
        'unit_long' => '',
    ],
    'current' => [
        'short' => 'Current',
        'long' => 'Current',
        'unit' => 'A',
        'unit_long' => 'Amperes',
    ],
    'dbm' => [
        'short' => 'dBm',
        'long' => 'dBm',
        'unit' => 'dBm',
        'unit_long' => 'Decibel-Milliwatts',
    ],
    'delay' => [
        'short' => 'Delay',
        'long' => 'Delay',
        'unit' => 's',
        'unit_long' => 'Seconds',
    ],
    'eer' => [
        'short' => 'EER',
        'long' => 'Energy Efficient Ratio',
        'unit' => '',
        'unit_long' => '',
    ],
    'fanspeed' => [
        'short' => 'Fanspeed',
        'long' => 'Fan Speed',
        'unit' => 'RPM',
        'unit_long' => 'Rotations per Minute',
    ],
    'frequency' => [
        'short' => 'Frequency',
        'long' => 'Frequency',
        'unit' => 'Hz',
        'unit_long' => 'Hertz',
    ],
    'humidity' => [
        'short' => 'Humidity',
        'long' => 'Humidity Percent',
        'unit' => '%',
        'unit_long' => 'Percent',
    ],
    'load' => [
        'short' => 'Load',
        'long' => 'Load Percent',
        'unit' => '%',
        'unit_long' => 'Percent',
    ],
    'loss' => [
        'short' => 'Percent',
        'long' => 'Loss Percentage',
        'unit' => '%',
        'unit_long' => 'percentage',
    ],
    'power' => [
        'short' => 'Power',
        'long' => 'Power',
        'unit' => 'W',
        'unit_long' => 'Watts',
    ],
    'power_consumed' => [
        'short' => 'Power Consumed',
        'long' => 'Power Consumed',
        'unit' => 'kWh',
        'unit_long' => 'Kilowatt-Hours',
    ],
    'power_factor' => [
        'short' => 'Power Factor',
        'long' => 'Power Factor',
        'unit' => '',
        'unit_long' => '',
    ],
    'pressure' => [
        'short' => 'Pressure',
        'long' => 'Pressure',
        'unit' => 'kPa',
        'unit_long' => 'Kilopascals',
    ],
    'quality_factor' => [
        'short' => 'Quality Factor',
        'long' => 'Quality Factor',
        'unit' => '',
        'unit_long' => '',
    ],
    'runtime' => [
        'short' => 'Runtime',
        'long' => 'Runtime',
        'unit' => 'Min',
        'unit_long' => 'Minutes',
    ],
    'signal' => [
        'short' => 'Signal',
        'long' => 'Signal',
        'unit' => 'dBm',
        'unit_long' => 'Decibal-Milliwatts',
    ],
    'tv_signal' => [
        'short' => 'Signal',
        'long' => 'TV signal',
        'unit' => 'dBmV',
        'unit_long' => 'Decibel-Millivolts',
    ],
    'bitrate' => [
        'short' => 'Bitrate',
        'long' => 'Bitrate',
        'unit' => 'bps',
        'unit_long' => 'bits per second',
    ],
    'snr' => [
        'short' => 'SNR',
        'long' => 'Signal to Noise Ratio',
        'unit' => 'dB',
        'unit_long' => 'Decibels',
    ],
    'state' => [
        'short' => 'State',
        'long' => 'State',
        'unit' => '',
    ],
    'temperature' => [
        'short' => 'Temperature',
        'long' => 'Temperature',
        'unit' => '°C',
        'unit_long' => '° Celsius',
    ],
    'voltage' => [
        'short' => 'Voltage',
        'long' => 'voltage',
        'unit' => 'V',
        'unit_long' => 'Volts',
    ],
    'waterflow' => [
        'short' => 'Waterflow',
        'long' => 'Water Flow',
        'unit' => 'l/m',
        'unit_long' => 'Liters Per Minute',
    ],
    'percent' => [
        'short' => 'Percent',
        'long' => 'Percent',
        'unit' => '%',
        'unit_long' => 'Percent',
    ],
];
