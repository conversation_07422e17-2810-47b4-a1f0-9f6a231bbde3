<?php

return [
    'checks' => [
        'comment' => 'Commento',
        'item' => 'Elemento',
        'php_required' => 'richiede la versione :version o superiore',
        'status' => 'Stato',
        'title' => 'Controlli pre-installazione',
    ],
    'database' => [
        'credentials' => 'Credenziali del database',
        'host' => 'Host',
        'host_placeholder' => 'Usare localhost per Unix-Socket',
        'name' => 'Nome del database',
        'password' => 'Password',
        'port' => 'Porta',
        'port_placeholder' => 'Las<PERSON>re vuoto se si usa Unix-Socket',
        'socket' => 'Unix-Socket',
        'socket_placeholder' => 'Usare solo per percorsi socket personalizzati',
        'test' => 'Controlla credenziali',
        'title' => 'Configurazione database',
        'username' => 'Utente',
    ],
    'finish' => [
        'config_exists' => 'il file config.php esiste già',
        'config_not_required' => 'Questo file non è richiesto. Questi i valori predefiniti.',
        'config_not_written' => 'Non posso salvare il file config.php',
        'config_written' => 'file config.php salvato',
        'copied' => 'Copiato negli appunti',
        'env_manual' => 'Aggiorna manualmente :file con il seguente contenuto',
        'env_not_written' => 'Non posso salvare il file .env',
        'env_written' => 'file .env salvato',
        'manual_copy' => 'Premi Ctrl-C per copiare',
        'not_finished' => 'Non hai ancora terminato!',
        'retry' => 'Riprova',
        'statistics' => 'Sarebbe fantastico se contribuissi alle nostre statistiche, puoi farlo su :about selezionando il pulsante sotto statistiche.',
        'statistics_link' => 'Informazioni su LibreNMS',
        'thanks' => 'Grazie per aver configurato LibreNMS.',
        'title' => 'Installazione terminata',
        'validate' => 'Prima, è necessario :validate e risolvere eventuali problemi.',
        'validate_link' => 'convalida la tua installazione',
    ],
    'install' => 'Installazione',
    'migrate' => [
        'building_interrupt' => 'Non chiudere questa pagina o interromperai l\'importazione!',
        'error' => 'Si è verificato un errore, controllare l\'output per i dettagli.',
        'migrate' => 'Versione database',
        'retry' => 'Riprova',
        'timeout' => 'Richiesta HTTP scaduta, la struttura del database potrebbe essere incoerente.',
        'wait' => 'Attendere prego...',
    ],
    'steps' => [
        'checks' => 'Controlli pre-installazione',
        'database' => 'Database',
        'finish' => 'Installazione terminata',
        'migrate' => 'Versione database',
        'user' => 'Creazione utente',
    ],
    'title' => 'Installazione di LibreNMS',
    'user' => [
        'button' => 'Aggiungi utente',
        'created' => 'Utente creato',
        'email' => 'E-mail',
        'failure' => 'Errone nella creazione utente',
        'password' => 'Password',
        'success' => 'Utente creato correttamente',
        'title' => 'Crea utente Admin',
        'username' => 'Username',
    ],
];
