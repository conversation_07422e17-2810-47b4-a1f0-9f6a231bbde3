<?php

return [
    'checks' => [
        'comment' => '评论',
        'item' => '项目',
        'php_required' => ':version 或更高版本是必需的',
        'status' => '状态',
        'title' => '预安装检查',
    ],
    'database' => [
        'credentials' => '数据库凭据',
        'host' => '主机',
        'host_placeholder' => '对于Unix套接字，请使用localhost',
        'name' => '数据库名称',
        'password' => '密码',
        'port' => '端口',
        'port_placeholder' => '如果使用Unix套接字，请留空',
        'socket' => 'Unix套接字',
        'socket_placeholder' => '仅在自定义套接字路径时使用',
        'test' => '检查凭据',
        'title' => '配置数据库',
        'username' => '用户名',
    ],
    'finish' => [
        'config_exists' => 'config.php 文件已存在',
        'config_not_required' => '此文件不是必需的。这是默认内容。',
        'config_not_written' => '无法写入 config.php',
        'config_written' => 'config.php 文件已写入',
        'copied' => '已复制到剪贴板',
        'dashboard' => '仪表板',
        'env_manual' => '手动使用以下内容更新 :file',
        'env_not_written' => '无法写入 .env 文件',
        'env_written' => '.env 文件已写入',
        'failed' => '保存 .env 失败',
        'finish' => '完成安装',
        'manual_copy' => '按Ctrl+C复制',
        'retry' => '重试',
        'settings' => '附加设置',
        'success' => '安装完成',
        'thanks' => '感谢您安装 LibreNMS。',
        'title' => '完成安装',
        'validate_button' => '验证安装',
    ],
    'install' => '安装',
    'migrate' => [
        'building_interrupt' => '不要关闭此页面或中断导入！',
        'error' => '遇到错误，请查看输出以获取详细信息',
        'migrate' => '构建数据库',
        'retry' => '重试',
        'timeout' => 'HTTP请求超时，您的数据库结构可能不一致',
        'wait' => '请稍候...',
    ],
    'steps' => [
        'checks' => '预安装检查',
        'database' => '数据库',
        'finish' => '完成安装',
        'migrate' => '构建数据库',
        'user' => '创建用户',
    ],
    'title' => 'LibreNMS 安装',
    'user' => [
        'button' => '添加用户',
        'created' => '用户已创建',
        'email' => '电子邮件',
        'failure' => '创建用户失败',
        'password' => '密码',
        'success' => '成功创建用户',
        'title' => '创建管理员用户',
        'username' => '用户名',
    ],
];
