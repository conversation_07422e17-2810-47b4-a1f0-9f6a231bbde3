<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Sensors Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to translate names and units of sensors
    |
    */

    'title' => '传感器',
    'airflow' => [
        'short' => '气流',
        'long' => '气流',
        'unit' => 'cfm',
        'unit_long' => '每分钟立方英尺',
    ],
    'ber' => [
        'short' => '误码率',
        'long' => '比特错误率',
        'unit' => '',
        'unit_long' => '',
    ],
    'charge' => [
        'short' => '电量',
        'long' => '电量百分比',
        'unit' => '%',
        'unit_long' => '百分比',
    ],
    'chromatic_dispersion' => [
        'short' => '色散',
        'long' => '色散',
        'unit' => 'ps/nm',
        'unit_long' => '皮秒/纳米',
    ],
    'cooling' => [
        'short' => '冷却',
        'long' => '',
        'unit' => 'W',
        'unit_long' => '瓦特',
    ],
    'count' => [
        'short' => '计数',
        'long' => '计数',
        'unit' => '',
        'unit_long' => '',
    ],
    'current' => [
        'short' => '电流',
        'long' => '电流',
        'unit' => 'A',
        'unit_long' => '安培',
    ],
    'dbm' => [
        'short' => 'dBm',
        'long' => 'dBm',
        'unit' => 'dBm',
        'unit_long' => '分贝毫瓦',
    ],
    'delay' => [
        'short' => '延迟',
        'long' => '延迟',
        'unit' => 's',
        'unit_long' => '秒',
    ],
    'eer' => [
        'short' => '能效比',
        'long' => '能源效率比率',
        'unit' => '',
        'unit_long' => '',
    ],
    'fanspeed' => [
        'short' => '风扇速度',
        'long' => '风扇速度',
        'unit' => 'RPM',
        'unit_long' => '每分钟转数',
    ],
    'frequency' => [
        'short' => '频率',
        'long' => '频率',
        'unit' => 'Hz',
        'unit_long' => '赫兹',
    ],
    'humidity' => [
        'short' => '湿度',
        'long' => '湿度百分比',
        'unit' => '%',
        'unit_long' => '百分比',
    ],
    'load' => [
        'short' => '负载',
        'long' => '负载百分比',
        'unit' => '%',
        'unit_long' => '百分比',
    ],
    'loss' => [
        'short' => '百分比',
        'long' => '损耗百分比',
        'unit' => '%',
        'unit_long' => '百分比',
    ],
    'power' => [
        'short' => '功率',
        'long' => '功率',
        'unit' => 'W',
        'unit_long' => '瓦特',
    ],
    'power_consumed' => [
        'short' => '消耗功率',
        'long' => '消耗功率',
        'unit' => 'kWh',
        'unit_long' => '千瓦时',
    ],
    'power_factor' => [
        'short' => '功率因数',
        'long' => '功率因数',
        'unit' => '',
        'unit_long' => '',
    ],
    'pressure' => [
        'short' => '压力',
        'long' => '压力',
        'unit' => 'kPa',
        'unit_long' => '千帕斯卡',
    ],
    'quality_factor' => [
        'short' => '质量因素',
        'long' => '质量因素',
        'unit' => '',
        'unit_long' => '',
    ],
    'runtime' => [
        'short' => '运行时间',
        'long' => '运行时间',
        'unit' => 'Min',
        'unit_long' => '分钟',
    ],
    'signal' => [
        'short' => '信号',
        'long' => '信号',
        'unit' => 'dBm',
        'unit_long' => '分贝毫瓦',
    ],
    'tv_signal' => [
        'short' => '信号',
        'long' => '电视信号',
        'unit' => 'dBmV',
        'unit_long' => '分贝毫伏',
    ],
    'bitrate' => [
        'short' => '比特率',
        'long' => '比特率',
        'unit' => 'bps',
        'unit_long' => '每秒位数',
    ],
    'snr' => [
        'short' => '信噪比',
        'long' => '信号与噪声比',
        'unit' => 'dB',
        'unit_long' => '分贝',
    ],
    'state' => [
        'short' => '状态',
        'long' => '状态',
        'unit' => '',
    ],
    'temperature' => [
        'short' => '温度',
        'long' => '温度',
        'unit' => '°C',
        'unit_long' => '摄氏度',
    ],
    'voltage' => [
        'short' => '电压',
        'long' => '电压',
        'unit' => 'V',
        'unit_long' => '伏特',
    ],
    'waterflow' => [
        'short' => '水流量',
        'long' => '水流量',
        'unit' => 'l/m',
        'unit_long' => '每分钟升',
    ],
    'percent' => [
        'short' => '百分比',
        'long' => '百分比',
        'unit' => '%',
        'unit_long' => '百分比',
    ],
];
