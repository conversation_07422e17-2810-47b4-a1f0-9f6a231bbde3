<div class="form-group ssid">
    <label for="ssid" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('SSID Name')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="ssid" name="ssid"
               value="{{ isset($groupSettings->ssid_name) && $groupSettings->ssid_name ? $groupSettings->ssid_name : '' }}">
        <span class="help-block" id="ssid-error">@error('ssid') {{ $message }} @enderror</span>
    </div>
</div>


<div class="form-group ssid-test-status" style="float: right;margin-bottom: 20px;">
    <label for="ssid-test-status" class="control-label col-sm-3 col-md-2 text-nowrap">@lang("Show test SSID's")</label>
    <div class="col-sm-6 col-md-6" style="float: right;">
        <input type="checkbox" class="form-control" id="ssid-test-status" name="ssid-test-status">
        <script>
            $("#ssid-test-status").bootstrapSwitch('state', {{ isset($groupSettings->ssid_test_enabled) && $groupSettings->ssid_test_enabled ? 'true' : 'false' }});
        </script>
    </div>
</div>

<div class="form-group " style="clear:both;">
    <label for="primary_dns" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Parental Control') <i
            class="fa fa-question-circle fa-fw" data-toggle="tooltip" data-placement="bottom"
            title="Parental control functionality allows you to get an account with a parental control DNS service such as open-dns.com.   Enter the primary and secondary DNS IP's they provide in these entry boxes. You can then activate or deactivate use of that service for each of the service level offerings you configure below."
            data-container="body" style="position: absolute;top: 2px;margin-left: 2px;"></i></label>

    <div class="col-md-2">
        <label for="primary_dns" class="control-label col-sm-3 col-md-2 text-nowrap"> @lang('Primary DNS IP')</label>
    </div>
    <div class="col-md-3 col-sm-3 primary_dns">
        <input type="text" class="form-control" id="primary_dns" name="primary_dns"
               value="{{ isset($groupSettings->dns_primary) && $groupSettings->dns_primary ? $groupSettings->dns_primary : '' }}">
        <span class="help-block" id="primary_dns-error"></span>
    </div>
    <div class="col-md-2 ">
        <label for="primary_dns" class="control-label col-sm-3 col-md-2 text-nowrap"> @lang('Secondary DNS IP')</label>
    </div>
    <div class="col-md-3 col-sm-3 secondary_dns">
        <input type="text" class="form-control" id="secondary_dns" name="secondary_dns"
               value="{{ isset($groupSettings->dns_secondary) && $groupSettings->dns_secondary ? $groupSettings->dns_secondary : '' }}">
        <span class="help-block" id="secondary_dns-error"></span>
    </div>

</div>


<input type="hidden" id="isset_parental_control" name="isset_parental_control" value="0">


<div class="form-group bm" style="clear:both;">
    <label for="bm" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Business Model')</label>
    <div class="col-sm-9 col-md-10">
        <select class="form-control" id="bm" name="bm" onchange="showbusinessmodel()">
            <option value="">Please select a Business Model</option>
            <option
                value="open-access" {{ isset($groupSettings->business_model) && $groupSettings->business_model == 'open-access' ? 'selected' : '' }}>
                Open Access
            </option>
            <option
                value="hotspot" {{ isset($groupSettings->business_model) && $groupSettings->business_model == 'hotspot' ? 'selected' : '' }}>
                Hotspot
            </option>
            <option
                value="monthly" {{ isset($groupSettings->business_model) && $groupSettings->business_model == 'monthly' ? 'selected' : '' }}>
                Monthly
            </option>
        </select>
        <input type="hidden" id="old_business_model_type"
               value="{{ isset($groupSettings->business_model) && $groupSettings->business_model ? $groupSettings->business_model : 'open-access' }}"/>
        <span class="help-block" id="bm-error"></span>
    </div>
</div>
@php
    if(isset($groupSettings->business_model_value) && $groupSettings->business_model_value){


        $busines_model_vals = $groupSettings->business_model_value;
        $vouchers_custom_selected = $groupSettings->vouchers_custom_selected;
    }

@endphp
<div id="hotspot-list" class="bm-options">
    <div class="col-md-2">&nbsp</div>
    <div class="col-md-9">
        <h4>Choose <strong>4</strong> Voucher Types</h4>
    </div>

    <div class="form-group">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3">
            <label class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Voucher')</label>
        </div>
        <div class="col-md-2">
            <label class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Price')</label>
        </div>
        <div class="col-md-2">
            <label for="1hr_price"
                   class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Parental Control') </label>
        </div>
        <div class="col-md-2">
            <label class="control-label col-sm-3 col-md-2 text-nowrap">Media Package (OTT) </label>
        </div>
    </div>
    <div
        class="form-group 1hr_price {{ isset($busines_model_vals['1hr']['checked']) && $busines_model_vals['1hr']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3" style="margin-left:15px;">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="vt[]"
                           value="1 HR" {{ isset($busines_model_vals['1hr']['checked']) && $busines_model_vals['1hr']['checked'] ? 'checked' : '' }} /> @lang('1 HR')
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="1hr_price" name="1hr_price"
                   value="{{ isset($busines_model_vals['1hr']['1hr_price']) && $busines_model_vals['1hr']['1hr_price'] ? $busines_model_vals['1hr']['1hr_price'] : '' }}">
            <span class="help-block" id="1hr_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="1hr_parental_control"
                   id="1hr_parental_control" name="1hr_parental_control">
        </div>
        <script>
            $("#1hr_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['1hr']['1hr_parental_control']) && $busines_model_vals['1hr']['1hr_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px"
                           name="1hr_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['1hr']['1hr_media_package']) && !empty($motvPackages) ? 'checked' : '' }} />
                    <select class="form-control"
                            name="1hr_media_package_value" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ (!empty($busines_model_vals['1hr']['1hr_media_package']) && $busines_model_vals['1hr']['1hr_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    <div
        class="form-group 4hr_price {{ isset($busines_model_vals['4hr']['checked']) && $busines_model_vals['4hr']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3" style="margin-left:15px;">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="vt[]"
                           value="4 HR" {{ isset($busines_model_vals['4hr']['checked']) && $busines_model_vals['4hr']['checked'] ? 'checked' : '' }} /> @lang('4 HR')
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="4hr_price" name="4hr_price"
                   value="{{ isset($busines_model_vals['4hr']['4hr_price']) && $busines_model_vals['4hr']['4hr_price'] ? $busines_model_vals['4hr']['4hr_price'] : '' }}">
            <span class="help-block" id="4hr_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="4hr_parental_control"
                   id="4hr_parental_control" name="4hr_parental_control">
        </div>
        <script>
            $("#4hr_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['4hr']['4hr_parental_control']) && $busines_model_vals['4hr']['4hr_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px"
                           name="4hr_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['4hr']['4hr_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control"
                            name="4hr_media_package_value" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ (!empty($busines_model_vals['4hr']['4hr_media_package']) && $busines_model_vals['4hr']['4hr_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    <div
        class="form-group 24hr_price {{ isset($busines_model_vals['24hr']['checked']) && $busines_model_vals['24hr']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3" style="margin-left:15px;">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="vt[]"
                           value="24 HR" {{ isset($busines_model_vals['24hr']['checked']) && $busines_model_vals['24hr']['checked'] ? 'checked' : '' }} /> @lang('24 HR')
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="24hr_price" name="24hr_price"
                   value="{{ isset($busines_model_vals['24hr']['24hr_price']) && $busines_model_vals['24hr']['24hr_price'] ? $busines_model_vals['24hr']['24hr_price'] : '' }}">
            <span class="help-block" id="24hr_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="24hr_parental_control"
                   id="24hr_parental_control" name="24hr_parental_control">
        </div>
        <script>
            $("#24hr_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['24hr']['24hr_parental_control']) && $busines_model_vals['24hr']['24hr_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px"
                           name="24hr_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['24hr']['24hr_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control"
                            name="24hr_media_package_value" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ (!empty($busines_model_vals['24hr']['24hr_media_package']) && $busines_model_vals['24hr']['24hr_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    <div
        class="form-group 1week_price {{ isset($busines_model_vals['1week']['checked']) && $busines_model_vals['1week']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3" style="margin-left:15px;">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="vt[]"
                           value="1 WEEK" {{ isset($busines_model_vals['1week']['checked']) && $busines_model_vals['1week']['checked'] ? 'checked' : '' }} /> @lang('1 WEEK')
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="1week_price" name="1week_price"
                   value="{{ isset($busines_model_vals['1week']['1week_price']) && $busines_model_vals['1week']['1week_price'] ? $busines_model_vals['1week']['1week_price'] : '' }}">
            <span class="help-block" id="1week_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="1week_parental_control"
                   id="1week_parental_control" name="1week_parental_control">
        </div>
        <script>
            $("#1week_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['1week']['1week_parental_control']) && $busines_model_vals['1week']['1week_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px"
                           name="1week_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['1week']['1week_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control"
                            name="1week_media_package_value" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ (!empty($busines_model_vals['1week']['1week_media_package']) && $busines_model_vals['1week']['1week_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    <div
        class="form-group 1month_price {{ isset($busines_model_vals['1month']['checked']) && $busines_model_vals['1month']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3" style="margin-left:15px;">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="vt[]"
                           value="1 MONTH" {{ isset($busines_model_vals['1month']['checked']) && $busines_model_vals['1month']['checked'] ? 'checked' : '' }} /> @lang('1 MONTH')
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="1month_price" name="1month_price"
                   value="{{ isset($busines_model_vals['1month']['1month_price']) && $busines_model_vals['1month']['1month_price'] ? $busines_model_vals['1month']['1month_price'] : '' }}">
            <span class="help-block" id="1month_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="1month_parental_control"
                   id="1month_parental_control" name="1month_parental_control">
        </div>
        <script>
            $("#1month_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['1month']['1month_parental_control']) && $busines_model_vals['1month']['1month_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px"
                           name="1month_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['1month']['1month_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control"
                            name="1month_media_package_value" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ (!empty($busines_model_vals['1month']['1month_media_package']) && $busines_model_vals['1month']['1month_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    <div
        class="form-group forever_price {{ isset($busines_model_vals['forever']['checked']) && $busines_model_vals['forever']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3" style="margin-left:15px;">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="vt[]"
                           value="FOREVER" {{ isset($busines_model_vals['forever']['checked']) && $busines_model_vals['forever']['checked'] ? 'checked' : '' }} /> @lang('FOREVER')
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="forever_price" name="forever_price"
                   value="{{ isset($busines_model_vals['forever']['forever_price']) && $busines_model_vals['forever']['forever_price'] ? $busines_model_vals['forever']['forever_price'] : '' }}">
            <span class="help-block" id="forever_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="forever_parental_control"
                   id="forever_parental_control" name="forever_parental_control">
        </div>
        <script>
            $("#forever_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['forever']['forever_parental_control']) && $busines_model_vals['forever']['forever_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px"
                           name="forever_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['forever']['forever_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control"
                            name="forever_media_package_value" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ (!empty($busines_model_vals['forever']['forever_media_package']) && $busines_model_vals['forever']['forever_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    <div
        class="form-group 10mb_price {{ isset($busines_model_vals['10mb']['checked']) && $busines_model_vals['10mb']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3" style="margin-left:15px;">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="vt[]"
                           value="10 MB" {{ isset($busines_model_vals['10mb']['checked']) && $busines_model_vals['10mb']['checked'] ? 'checked' : '' }} /> @lang('10 MB')
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="10mb_price" name="10mb_price"
                   value="{{ isset($busines_model_vals['10mb']['10mb_price']) && $busines_model_vals['10mb']['10mb_price'] ? $busines_model_vals['10mb']['10mb_price'] : '' }}">
            <span class="help-block" id="10mb_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="10mb_parental_control"
                   id="10mb_parental_control" name="10mb_parental_control">
        </div>
        <script>
            $("#10mb_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['10mb']['10mb_parental_control']) && $busines_model_vals['10mb']['10mb_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px" name="10mb_media_package" disabled/>
                    <select class="form-control" name="10mb_media_package_value" disabled>
                        <option value="" selected>Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option value="">{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    @php
        $custom1_value = isset($vouchers_custom_selected['custom1']) ? $vouchers_custom_selected['custom1'] : 'custom1';

        $custom1_price = isset($vouchers_custom_selected['custom1']) && isset($busines_model_vals[$vouchers_custom_selected['custom1']][$vouchers_custom_selected['custom1'].'_price']) ? $busines_model_vals[$vouchers_custom_selected['custom1']][$vouchers_custom_selected['custom1'].'_price'] : '';

        $custom1_parental_control = isset($vouchers_custom_selected['custom1']) && isset($busines_model_vals[$vouchers_custom_selected['custom1']][$vouchers_custom_selected['custom1'].'_parental_control']) ? $busines_model_vals[$vouchers_custom_selected['custom1']][$vouchers_custom_selected['custom1'].'_parental_control'] : '';

        $custom1_media_package = isset($vouchers_custom_selected['custom1']) && isset($busines_model_vals[$vouchers_custom_selected['custom1']][$vouchers_custom_selected['custom1'].'_media_package']) ? $busines_model_vals[$vouchers_custom_selected['custom1']][$vouchers_custom_selected['custom1'].'_media_package'] : '';
    @endphp
    <div class="form-group custom1 {{ isset($vouchers_custom_selected['custom1']) ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3 custom_voucher" style="margin-left:15px;">
            <div class="checkbox" style="padding-top:0px">
                <label>
                    <input type="checkbox" name="vt[]" value="custom1"
                           {{ isset($vouchers_custom_selected['custom1']) ? 'checked' : '' }} style="margin-top:10px"/>
                    <select name="custom1_value" class="form-control" id="custom1_value"
                            @if(!isset($vouchers_custom_selected['custom1'])) disabled @endif>
                        <option value="" data-allow-media-package="no" data-custom-name="custom1">Custom</option>
                        <option value="100mb"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === '100mb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom1">@lang('100 MB')</option>
                        <option value="1gb"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === '1gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom1">@lang('1 GB')</option>
                        <option value="10gb"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === '10gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom1">@lang('10 GB')</option>
                        <option value="100gb"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === '100gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom1">@lang('100 GB')</option>
                        <option value="1-hour"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === '1-hour') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom1">1-Hour
                        </option>
                        <option value="1-week"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === '1-week') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom1">1-Week
                        </option>
                        <option value="1-month"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === '1-month') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom1">1-Month
                        </option>
                        <option value="forever-1"
                                @if(isset($vouchers_custom_selected['custom1']) && $vouchers_custom_selected['custom1'] === 'forever-1') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom1">Forever-1
                        </option>
                    </select>
                </label>
                <span class="help-block" id="custom1_value-error"></span>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="custom1_price" name="custom1_price"
                   value="{{ $custom1_price }}">
            <span class="help-block" id="custom1_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="custom1_parental_control"
                   id="custom1_parental_control" name="custom1_parental_control">
        </div>
        <script>
            $("#custom1_parental_control").bootstrapSwitch('state', {{ !empty($custom1_parental_control) ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px" name="custom1_media_package"
                           id="custom1_media_package" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom1'])) ? 'disabled' : ''}} {{ !empty($custom1_media_package) && !empty($motvPackages) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="custom1_media_package_value"
                            id="custom1_media_package_value" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom1'])) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ ($custom1_media_package == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    @php
        $custom2_value = isset($vouchers_custom_selected['custom2']) ? $vouchers_custom_selected['custom2'] : 'custom2';

        $custom2_price = isset($vouchers_custom_selected['custom2']) && isset($busines_model_vals[$vouchers_custom_selected['custom2']][$vouchers_custom_selected['custom2'].'_price']) ? $busines_model_vals[$vouchers_custom_selected['custom2']][$vouchers_custom_selected['custom2'].'_price'] : '';

        $custom2_parental_control = isset($vouchers_custom_selected['custom2']) && isset($busines_model_vals[$vouchers_custom_selected['custom2']][$vouchers_custom_selected['custom2'].'_parental_control']) ? $busines_model_vals[$vouchers_custom_selected['custom2']][$vouchers_custom_selected['custom2'].'_parental_control'] : '';

        $custom2_media_package = isset($vouchers_custom_selected['custom2']) && isset($busines_model_vals[$vouchers_custom_selected['custom2']][$vouchers_custom_selected['custom2'].'_media_package']) ? $busines_model_vals[$vouchers_custom_selected['custom2']][$vouchers_custom_selected['custom2'].'_media_package'] : '';
    @endphp

    <div class="form-group custom2 {{ isset($vouchers_custom_selected['custom2']) ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3 custom_voucher" style="margin-left:15px;">
            <div class="checkbox" style="padding-top:0px">
                <label>
                    <input type="checkbox" name="vt[]" value="custom2"
                           {{ isset($vouchers_custom_selected['custom2']) ? 'checked' : '' }} style="margin-top:10px"/>
                    <select name="custom2_value" class="form-control" id="custom2_value"
                            @if(!isset($vouchers_custom_selected['custom2'])) disabled @endif
                    >
                        <option value="" data-allow-media-package="no" data-custom-name="custom2">Custom</option>
                        <option value="100mb"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === '100mb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom2">@lang('100 MB')</option>
                        <option value="1gb"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === '1gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom2">@lang('1 GB')</option>
                        <option value="10gb"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === '10gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom2">@lang('10 GB')</option>
                        <option value="100gb"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === '100gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom2">@lang('100 GB')</option>
                        <option value="1-hour"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === '1-hour') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom2">1-Hour
                        </option>
                        <option value="1-week"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === '1-week') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom2">1-Week
                        </option>
                        <option value="1-month"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === '1-month') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom2">1-Month
                        </option>
                        <option value="forever-1"
                                @if(isset($vouchers_custom_selected['custom2']) && $vouchers_custom_selected['custom2'] === 'forever-1') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom2">Forever-1
                        </option>
                    </select>
                    <span class="help-block" id="custom2_value-error"></span>
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="custom2_price" name="custom2_price"
                   value="{{ $custom2_price }}">
            <span class="help-block" id="custom2_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="custom2_parental_control"
                   id="custom2_parental_control" name="custom2_parental_control">
        </div>
        <script>
            $("#custom2_parental_control").bootstrapSwitch('state', {{ !empty($custom2_parental_control) ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px" name="custom2_media_package"
                           id="custom2_media_package" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom2'])) ? 'disabled' : ''}} {{ !empty($custom2_media_package) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="custom2_media_package_value"
                            id="custom2_media_package_value" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom2'])) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ ($custom2_media_package == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    @php
        $custom3_value = isset($vouchers_custom_selected['custom3']) ? $vouchers_custom_selected['custom3'] : 'custom3';

        $custom3_price = isset($vouchers_custom_selected['custom3']) && isset($busines_model_vals[$vouchers_custom_selected['custom3']][$vouchers_custom_selected['custom3'].'_price']) ? $busines_model_vals[$vouchers_custom_selected['custom3']][$vouchers_custom_selected['custom3'].'_price'] : '';

        $custom3_parental_control = isset($vouchers_custom_selected['custom3']) && isset($busines_model_vals[$vouchers_custom_selected['custom3']][$vouchers_custom_selected['custom3'].'_parental_control']) ? $busines_model_vals[$vouchers_custom_selected['custom3']][$vouchers_custom_selected['custom3'].'_parental_control'] : '';

        $custom3_media_package = isset($vouchers_custom_selected['custom3']) && isset($busines_model_vals[$vouchers_custom_selected['custom3']][$vouchers_custom_selected['custom3'].'_media_package']) ? $busines_model_vals[$vouchers_custom_selected['custom3']][$vouchers_custom_selected['custom3'].'_media_package'] : '';
    @endphp
    <div class="form-group custom3 {{ isset($vouchers_custom_selected['custom3']) ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3 custom_voucher" style="margin-left:15px;">
            <div class="checkbox" style="padding-top:0px">
                <label>
                    <input type="checkbox" name="vt[]" value="custom3"
                           {{ isset($vouchers_custom_selected['custom3']) ? 'checked' : '' }} style="margin-top:10px"/>
                    <select name="custom3_value" class="form-control" id="custom3_value"
                            @if(!isset($vouchers_custom_selected['custom3'])) disabled @endif>
                        <option value="" data-allow-media-package="no" data-custom-name="custom3">Custom</option>
                        <option value="100mb"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === '100mb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom3">@lang('100 MB')</option>
                        <option value="1gb"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === '1gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom3">@lang('1 GB')</option>
                        <option value="10gb"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === '10gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom3">@lang('10 GB')</option>
                        <option value="100gb"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === '100gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom3">@lang('100 GB')</option>
                        <option value="1-hour"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === '1-hour') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom3">1-Hour
                        </option>
                        <option value="1-week"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === '1-week') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom3">1-Week
                        </option>
                        <option value="1-month"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === '1-month') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom3">1-Month
                        </option>
                        <option value="forever-1"
                                @if(isset($vouchers_custom_selected['custom3']) && $vouchers_custom_selected['custom3'] === 'forever-1') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom3">Forever-1
                        </option>
                    </select>
                    <span class="help-block" id="custom3_value-error"></span>
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="custom3_price" name="custom3_price"
                   value="{{ $custom3_price }}">
            <span class="help-block" id="custom3_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="custom3_parental_control"
                   id="custom3_parental_control" name="custom3_parental_control">
        </div>
        <script>
            $("#custom3_parental_control").bootstrapSwitch('state', {{ !empty($custom3_parental_control) ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px" name="custom3_media_package"
                           id="custom3_media_package" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom3'])) ? 'disabled' : ''}} {{ !empty($custom3_media_package) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="custom3_media_package_value"
                            id="custom3_media_package_value" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom3'])) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ ($custom3_media_package == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>

    @php
        $custom4_value = isset($vouchers_custom_selected['custom4']) ? $vouchers_custom_selected['custom4'] : 'custom4';

        $custom4_price = isset($vouchers_custom_selected['custom4']) && isset($busines_model_vals[$vouchers_custom_selected['custom4']][$vouchers_custom_selected['custom4'].'_price']) ? $busines_model_vals[$vouchers_custom_selected['custom4']][$vouchers_custom_selected['custom4'].'_price'] : '';

        $custom4_parental_control = isset($vouchers_custom_selected['custom4']) && isset($busines_model_vals[$vouchers_custom_selected['custom4']][$vouchers_custom_selected['custom4'].'_parental_control']) ? $busines_model_vals[$vouchers_custom_selected['custom4']][$vouchers_custom_selected['custom4'].'_parental_control'] : '';

        $custom4_media_package = isset($vouchers_custom_selected['custom4']) && isset($busines_model_vals[$vouchers_custom_selected['custom4']][$vouchers_custom_selected['custom4'].'_media_package']) ? $busines_model_vals[$vouchers_custom_selected['custom4']][$vouchers_custom_selected['custom4'].'_media_package'] : '';
    @endphp
    <div class="form-group custom4 {{ isset($vouchers_custom_selected['custom4']) ? 'active' : '' }}">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-3 custom_voucher" style="margin-left:15px;">
            <div class="checkbox" style="padding-top:0px">
                <label>
                    <input type="checkbox" name="vt[]" value="custom4"
                           {{ isset($vouchers_custom_selected['custom4']) ? 'checked' : '' }} style="margin-top:10px"/>
                    <select name="custom4_value" class="form-control" id="custom4_value"
                            @if(!isset($vouchers_custom_selected['custom4'])) disabled @endif>
                        <option value="" data-allow-media-package="no" data-custom-name="custom4">Custom</option>
                        <option value="100mb"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === '100mb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom4">@lang('100 MB')</option>
                        <option value="1gb"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === '1gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom4">@lang('1 GB')</option>
                        <option value="10gb"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === '10gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom4">@lang('10 GB')</option>
                        <option value="100gb"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === '100gb') selected
                                @endif data-allow-media-package="no" data-custom-name="custom4">@lang('100 GB')</option>
                        <option value="1-hour"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === '1-hour') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom4">1-Hour
                        </option>
                        <option value="1-week"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === '1-week') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom4">1-Week
                        </option>
                        <option value="1-month"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === '1-month') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom4">1-Month
                        </option>
                        <option value="forever-1"
                                @if(isset($vouchers_custom_selected['custom4']) && $vouchers_custom_selected['custom4'] === 'forever-1') selected
                                @endif data-allow-media-package="yes" data-custom-name="custom4">Forever-1
                        </option>
                    </select>
                </label>
                <span class="help-block" id="custom4_value-error"></span>
            </div>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control" id="custom4_price" name="custom4_price"
                   value="{{ $custom4_price }}">
            <span class="help-block" id="custom4_price-error"></span>
        </div>
        <div class="col-md-2">
            <input type="checkbox" class="form-control c_parental_control" data-mid="custom4_parental_control"
                   id="custom4_parental_control" name="custom4_parental_control">
        </div>
        <script>
            $("#custom4_parental_control").bootstrapSwitch('state', {{ !empty($custom4_parental_control) ? 'true' : 'false' }});
        </script>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" style="margin-top:10px" name="custom4_media_package"
                           id="custom4_media_package" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom4']) ) ? 'disabled' : ''}} {{ !empty($custom4_media_package) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="custom4_media_package_value"
                            id="custom4_media_package_value" {{(empty($motvPackages) || !isset($vouchers_custom_selected['custom4']) ) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ ($custom4_media_package == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>
    </div>
</div>

<div id="open-access-list" class="bm-options">
    <div class="form-group 100gb_price">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-6">
            <div class="checkbox">
                <label>
                    <input type="radio" name="open_access_val"
                           value="Unsecured Access" {{ isset($busines_model_vals['open-access']) && $busines_model_vals['open-access'] == 'Unsecured Access' ? 'checked' : '' }} /> @lang('UNSECURED ACCESS')
                </label>
            </div>
        </div>
    </div>

    <div class="form-group oc_password">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="radio" name="open_access_val"
                           value="Password" {{ isset($busines_model_vals['open-access']) && $busines_model_vals['open-access'] == 'Password' ? 'checked' : '' }} /> @lang('PASSWORD')
                </label>
            </div>
        </div>
        <div class="col-md-3">
            <input type="password" class="form-control" id="oc_password" name="oc_password"
                   placeholder="Please insert password"
                   value="{{ isset($busines_model_vals['open-access']) && $busines_model_vals['open-access'] == 'Password' && isset($busines_model_vals['open-access-password']) && $busines_model_vals['open-access-password']   ? $busines_model_vals['open-access-password'] : '' }}"
                   style="display: none;">
            <span class="help-block" id="oc_password-error"></span>
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-2" style="margin-left: 20px;">Parental Control</div>
        <div class="col-md-2" style="margin-left: -20px;">
            <input type="checkbox" class="form-control c_parental_control" data-mid="open-access-parental-control"
                   id="open-access-parental-control" name="open-access-parental-control">
        </div>
        <script>
            $("#open-access-parental-control").bootstrapSwitch('state', {{ isset($busines_model_vals['open-access-parental-control']) && $busines_model_vals['open-access-parental-control'] ? 'true' : 'false' }});
        </script>
    </div>

    <div class="form-group open-access-list-results">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-6">
            <span class="help-block" id="open-access-list-results-error"></span>
        </div>
    </div>
</div>

<div id="monthly-list" class="bm-options">

    <div class="form-group 1_type">
        <label for="1_type" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Type')</label>
        <div class="col-sm-9 col-md-10">
            <select class="form-control" id="1_type" name="1_type" onchange="showtypemodel()">
                <option value="router"
                        @if(isset($busines_model_vals['1_mt']['1_type']) && $busines_model_vals['1_mt']['1_type'] == 'user') selected @endif>
                    Per Router
                </option>
                <option value="user"
                        @if(isset($busines_model_vals['1_mt']['1_type']) && $busines_model_vals['1_mt']['1_type'] == 'user')
                            selected
                        @elseif(!isset($busines_model_vals['1_mt']['1_type']))
                            selected
                    @endif
                >Per User
                </option>
            </select>
        </div>
    </div>


    <div class="form-group redirect_url" style="margin-top: 25px;">
        <label for="redirect_url" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Redirect URL')</label>
        <div class="col-sm-9 col-md-10">
            <input type="text" class="form-control" id="redirect_url" name="redirect_url"
                   value="{{ isset($busines_model_vals['1_mt']['redirect_url']) && $busines_model_vals['1_mt']['redirect_url'] ? $busines_model_vals['1_mt']['redirect_url'] : 'https://www.google.com' }}">
            <span class="help-block" id="redirect_url-error"></span>
        </div>
    </div>

    <div class="col-md-2">
        &nbsp
    </div>
    <div class="col-md-10">
        <h4>Choose Up to <strong>3</strong> Service Levels</h4>
    </div>
    <div class="form-group monthly-list-results">
        <div class="col-md-2">&nbsp</div>
        <div class="col-md-2">
            <span class="help-block" id="monthly-list-results-error"></span>
        </div>
    </div>


    <div id="m_type_user"
         @if(isset($busines_model_vals['1_mt']['1_type']) && $busines_model_vals['1_mt']['1_type'] == 'user')
         @elseif(!isset($busines_model_vals['1_mt']['1_type']))
         @else
             style="display:none;"
        @endif
    >


        <div class="form-group">

            <div class="col-md-2">
                <label class="text-nowrap" style="padding-left: 35px;">@lang('Name')</label>
            </div>
            <div class="col-md-1">
                <label class="text-nowrap" style="padding-left: 5px">@lang('Speed')</label>
            </div>

            <div class="col-md-1">
                <label class="" style="padding-left: 5px">@lang('Monthly Price')</label>
            </div>

            <div class="col-md-1">
                <label class="" style="padding-left: 5px">Max Devices</label>
            </div>
            <div class="col-md-1">
                <label class="">@lang('Parental Control') </label>
            </div>
            <div class="col-md-4">
                <label class="text-nowrap" style="padding-left: 15px">@lang('Description')</label>
            </div>
            <div class="col-md-2">
                <label class="text-nowrap" style="padding-left: 5px">Media Package <br>(OTT) </label>
            </div>

        </div>
        <div
            class="form-group 4_mt {{ isset($busines_model_vals['4_mt']['checked']) && $busines_model_vals['4_mt']['checked'] ? 'active' : '' }}"
        ">

        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" id="4_mt" class="user_type m_check" name="mt[]"
                           value="4_mt" {{ isset($busines_model_vals['4_mt']['checked']) && $busines_model_vals['4_mt']['checked'] ? 'checked' : '' }} />
                    <input style="margin-top: -7px;" type="text" class="form-control" id="4_mt_name" name="4_mt_name"
                           value="{{ isset($busines_model_vals['4_mt']['4_mt_name']) && $busines_model_vals['4_mt']['4_mt_name'] ? $busines_model_vals['4_mt']['4_mt_name'] : '' }}">
                </label>
                <span class="help-block" id="4_mt-error"></span>
            </div>
        </div>
        <div class="col-md-1">
            <input type="text" class="form-control disable-speed" id="4_mt_speed" name="4_mt_speed"
                   value="{{ isset($busines_model_vals['4_mt']['4_mt_speed']) && $busines_model_vals['4_mt']['4_mt_speed'] ? $busines_model_vals['4_mt']['4_mt_speed'] : '0' }}">
        </div>

        <div class="col-md-1">
            <input type="text" class="form-control" id="4_mt_price" name="4_mt_price"
                   value="{{ isset($busines_model_vals['4_mt']['4_mt_price']) && $busines_model_vals['4_mt']['4_mt_price'] ? $busines_model_vals['4_mt']['4_mt_price'] : '0' }}">
        </div>
        <div class="col-md-1">
            <select class="form-control" id="4_mt_max_devices" name="4_mt_max_devices" style="min-width: 58px;">
                <option
                    value="1" {{ isset($busines_model_vals['4_mt']['4_mt_max_devices']) && $busines_model_vals['4_mt']['4_mt_max_devices'] == 1 ? 'selected' : '' }}>
                    1
                </option>
                <option value="5"
                        @if(isset($busines_model_vals['4_mt']['4_mt_max_devices']) && $busines_model_vals['4_mt']['4_mt_max_devices'] == 5)
                            selected
                        @elseif(!isset($busines_model_vals['4_mt']['4_mt_max_devices']))
                            selected
                @else
                    @endif
                >5
                </option>
                <option
                    value="10" {{ isset($busines_model_vals['4_mt']['4_mt_max_devices']) && $busines_model_vals['4_mt']['4_mt_max_devices'] == 10 ? 'selected' : '' }}>
                    10
                </option>
            </select>
        </div>
        <div class="col-md-1">
            <input type="checkbox" class="form-control c_parental_control" data-mid="4_mt" id="4_mt_parental_control"
                   name="4_mt_parental_control">
        </div>
        <script>
            $("#4_mt_parental_control").bootstrapSwitch({
                state: {{ isset($busines_model_vals['4_mt']['4_mt_parental_control']) && $busines_model_vals['4_mt']['4_mt_parental_control'] ? 'true' : 'false' }},
                size: 'small'
            });
        </script>
        <div class="col-md-4" style="padding-left: 25px;">
            <label class="control-label col-sm-3 col-md-2 text-nowrap box-style-label" style="margin-right: 25px;">
                <a href="javascript:void(0)" data-toggle="modal" data-target="#add-box-style-4_mt">Box Style</a></label>
            <textarea rows="2" class="form-control jqte-gs" id="4_mt_description"
                      name="4_mt_description">{{ isset($busines_model_vals['4_mt']['4_mt_description']) && $busines_model_vals['4_mt']['4_mt_description'] ? $busines_model_vals['4_mt']['4_mt_description'] : '' }}</textarea>
        </div>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox"
                           name="4_mt_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['4_mt']['4_mt_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="4_mt_media_package_value"
                            style="min-width: 110px; margin-top: -7px;" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ !empty($busines_model_vals['4_mt']['4_mt_media_package']) && ($busines_model_vals['4_mt']['4_mt_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>

        <div class="modal fade" id="add-box-style-4_mt" tabindex="-1" role="dialog" aria-labelledby="Add Style"
             aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h5 class="modal-title" id="Create">Box Style</h5>
                    </div>
                    <div class="modal-body">

                        <br/>
                        <div class="form-horizontal alerts-form">
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="main">
                                    <div class="form-group form-inline">
                                        <label for='4_mt_background_type' class='col-sm-3 col-md-2 control-label'>Background: </label>
                                        <div class="col-sm-3">
                                            <select class="form-control background_select" data-select-name="4_mt"
                                                    id="4_mt_background_type" name="4_mt_background_type"
                                                    style="padding-left: 6px;">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['4_mt']['4_mt_background_type']) && $busines_model_vals['4_mt']['4_mt_background_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['4_mt']['4_mt_background_type']) && $busines_model_vals['4_mt']['4_mt_background_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                                <option
                                                    value="image" {{ isset($busines_model_vals['4_mt']['4_mt_background_type']) && $busines_model_vals['4_mt']['4_mt_background_type'] == 'image' ? 'selected' : '' }}>
                                                    Upload Image
                                                </option>
                                            </select>
                                            <div id="background_details_color_4_mt" class="background_details_4_mt"
                                                 style="{{ isset($busines_model_vals['4_mt']['4_mt_background_type']) && $busines_model_vals['4_mt']['4_mt_background_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="4_mt_background_color" name="4_mt_background_color"
                                                       value="{{ isset($busines_model_vals['4_mt']['4_mt_background_color']) && $busines_model_vals['4_mt']['4_mt_background_color'] ? $busines_model_vals['4_mt']['4_mt_background_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#4_mt_background_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['4_mt']['4_mt_background_color']) && $busines_model_vals['4_mt']['4_mt_background_color'] ? $busines_model_vals['4_mt']['4_mt_background_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>

                                        </div>
                                        <div class="col-sm-3">
                                            <label for='4_mt_border_type' class='control-label'
                                                   style="vertical-align: top;">Border: </label>
                                            <select class="form-control border_select" data-select-name="4_mt"
                                                    id="4_mt_border_type" name="4_mt_border_type">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['4_mt']['4_mt_border_type']) && $busines_model_vals['4_mt']['4_mt_border_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['4_mt']['4_mt_border_type']) && $busines_model_vals['4_mt']['4_mt_border_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                            </select>
                                            <div id="border_details_color_4_mt"
                                                 class="border_details_4_mt border_details"
                                                 style=" {{ isset($busines_model_vals['4_mt']['4_mt_border_type']) && $busines_model_vals['4_mt']['4_mt_border_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="4_mt_border_color" name="4_mt_border_color"
                                                       value="{{ isset($busines_model_vals['4_mt']['4_mt_border_color']) && $busines_model_vals['4_mt']['4_mt_border_color'] ? $busines_model_vals['4_mt']['4_mt_border_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#4_mt_border_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['4_mt']['4_mt_border_color']) && $busines_model_vals['4_mt']['4_mt_border_color'] ? $busines_model_vals['4_mt']['4_mt_border_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-inline">

                                        <div id="background_details_image_4_mt" class="background_details_4_mt"
                                             style="margin-top:5px; {{ isset($busines_model_vals['4_mt']['4_mt_background_type']) && $busines_model_vals['4_mt']['4_mt_background_type'] == 'image' ? '' : 'display:none' }}">
                                            <div class=" 4_mt_image">

                                                <label for='4_mt_image'
                                                       class='col-sm-3 col-md-2 control-label'>@lang('Upload Image')
                                                    : </label>
                                                <div class="col-sm-3">
                                                    <input type="file" class="form-control" id="4_mt_image"
                                                           name="4_mt_image" style="width:213px"
                                                           onchange="readTempImage(this,'4_mt_image');">
                                                    <span class="help-block" id="4_mt_image-error"></span>
                                                </div>
                                            </div>
                                            <div class="4_mt_image_temp"
                                                 style="margin-left: 20px;clear: both; display:none;">
                                                <label for="4_mt_image_temp"
                                                       class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                <img id="4_mt_image_temp" height="73" src="#" alt="Background Temp"/>
                                                <input id="img_4_mt_image" name="img_4_mt_image" type="hidden"
                                                       value="{{(isset($busines_model_vals['4_mt']['4_mt_image']) && $busines_model_vals['4_mt']['4_mt_image'])?(1):(0)}}"/>
                                            </div>

                                            @if(isset($busines_model_vals['4_mt']['4_mt_image']) && $busines_model_vals['4_mt']['4_mt_image'])
                                                <div class="background_image_row_4_mt"
                                                     style="margin-left: 20px;clear: both;">
                                                    <label for="4_mt_background_image"
                                                           class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                    <div style="margin-top: 10px;margin-left: 10px;">
                                                        <img height="73"
                                                             src="{{URL::to("/")}}{{$busines_model_vals['4_mt']['4_mt_image']}}?v={{time()}}"/>
                                                        <a type="button" class="btn btn-danger"
                                                           style="margin-top: 10px;" href="javascript:void(0)"
                                                           onclick="deletemtimg('4_mt')">Delete image</a>
                                                    </div>

                                                </div>
                                            @endif

                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 30px;">
                                <div class="col-sm-12 text-center">
                                    <button type="button" class="btn btn-success btn-save-style"
                                            name="btn-save-style" data-dismiss="modal" aria-hidden="true">
                                        Save Style
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div
        class="form-group 5_mt {{ isset($busines_model_vals['5_mt']['checked']) && $busines_model_vals['5_mt']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="mt[]" id="5_mt" class="user_type m_check"
                           value="5_mt" {{ isset($busines_model_vals['5_mt']['checked']) && $busines_model_vals['5_mt']['checked'] ? 'checked' : '' }} />
                    <input style="margin-top: -7px;" type="text" class="form-control" id="5_mt_name" name="5_mt_name"
                           value="{{ isset($busines_model_vals['5_mt']['5_mt_name']) && $busines_model_vals['5_mt']['5_mt_name'] ? $busines_model_vals['5_mt']['5_mt_name'] : '' }}">
                </label>
                <span class="help-block" id="5_mt-error"></span>
            </div>
        </div>
        <div class="col-md-1">
            <input type="text" class="form-control disable-speed" id="5_mt_speed" name="5_mt_speed"
                   value="{{ isset($busines_model_vals['5_mt']['5_mt_speed']) && $busines_model_vals['5_mt']['5_mt_speed'] ? $busines_model_vals['5_mt']['5_mt_speed'] : '0' }}">
        </div>

        <div class="col-md-1">
            <input type="text" class="form-control" id="5_mt_price" name="5_mt_price"
                   value="{{ isset($busines_model_vals['5_mt']['5_mt_price']) && $busines_model_vals['5_mt']['5_mt_price'] ? $busines_model_vals['5_mt']['5_mt_price'] : '0' }}">
        </div>
        <div class="col-md-1">
            <select class="form-control" id="5_mt_max_devices" name="5_mt_max_devices" style="min-width: 58px;">
                <option
                    value="1" {{ isset($busines_model_vals['5_mt']['5_mt_max_devices']) && $busines_model_vals['5_mt']['5_mt_max_devices'] == 1 ? 'selected' : '' }}>
                    1
                </option>
                <option value="5"
                        @if(isset($busines_model_vals['5_mt']['5_mt_max_devices']) && $busines_model_vals['5_mt']['5_mt_max_devices'] == 5)
                            selected
                        @elseif(!isset($busines_model_vals['5_mt']['5_mt_max_devices']))
                            selected
                @else
                    @endif
                >5
                </option>
                <option
                    value="10" {{ isset($busines_model_vals['5_mt']['5_mt_max_devices']) && $busines_model_vals['5_mt']['5_mt_max_devices'] == 10 ? 'selected' : '' }}>
                    10
                </option>
            </select>
        </div>
        <div class="col-md-1">
            <input type="checkbox" class="form-control c_parental_control" data-mid="5_mt" id="5_mt_parental_control"
                   name="5_mt_parental_control">
        </div>
        <script>
            $("#5_mt_parental_control").bootstrapSwitch({
                state: {{ isset($busines_model_vals['5_mt']['5_mt_parental_control']) && $busines_model_vals['5_mt']['5_mt_parental_control'] ? 'true' : 'false' }},
                size: 'small'
            });
        </script>
        <div class="col-md-4" style="padding-left: 25px;">
            <label class="control-label col-sm-3 col-md-2 text-nowrap box-style-label" style="margin-right: 25px;"><a
                    href="javascript:void(0)" data-toggle="modal" data-target="#add-box-style-5_mt">Box
                    Style</a></label>
            <textarea rows="2" style="min-width: 285px;" class="form-control jqte-gs" id="5_mt_description"
                      name="5_mt_description">{{ isset($busines_model_vals['5_mt']['5_mt_description']) && $busines_model_vals['5_mt']['5_mt_description'] ? $busines_model_vals['5_mt']['5_mt_description'] : '' }}</textarea>
        </div>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox"
                           name="5_mt_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['5_mt']['5_mt_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="5_mt_media_package_value"
                            style="min-width: 110px; margin-top: -7px;" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ !empty($busines_model_vals['5_mt']['5_mt_media_package']) && ($busines_model_vals['5_mt']['5_mt_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>

        <div class="modal fade" id="add-box-style-5_mt" tabindex="-1" role="dialog" aria-labelledby="Add Style"
             aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h5 class="modal-title" id="Create">Box Style</h5>
                    </div>
                    <div class="modal-body">

                        <br/>
                        <div class="form-horizontal alerts-form">
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="main">
                                    <div class="form-group form-inline">
                                        <label for='5_mt_background_type' class='col-sm-3 col-md-2 control-label'>Background: </label>
                                        <div class="col-sm-3">
                                            <select class="form-control background_select" data-select-name="5_mt"
                                                    id="5_mt_background_type" name="5_mt_background_type"
                                                    style="padding-left: 6px;">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['5_mt']['5_mt_background_type']) && $busines_model_vals['5_mt']['5_mt_background_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['5_mt']['5_mt_background_type']) && $busines_model_vals['5_mt']['5_mt_background_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                                <option
                                                    value="image" {{ isset($busines_model_vals['5_mt']['5_mt_background_type']) && $busines_model_vals['5_mt']['5_mt_background_type'] == 'image' ? 'selected' : '' }}>
                                                    Upload Image
                                                </option>
                                            </select>
                                            <div id="background_details_color_5_mt" class="background_details_5_mt"
                                                 style="{{ isset($busines_model_vals['5_mt']['5_mt_background_type']) && $busines_model_vals['5_mt']['5_mt_background_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="5_mt_background_color" name="5_mt_background_color"
                                                       value="{{ isset($busines_model_vals['5_mt']['5_mt_background_color']) && $busines_model_vals['5_mt']['5_mt_background_color'] ? $busines_model_vals['5_mt']['5_mt_background_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#5_mt_background_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['5_mt']['5_mt_background_color']) && $busines_model_vals['5_mt']['5_mt_background_color'] ? $busines_model_vals['5_mt']['5_mt_background_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>

                                        </div>
                                        <div class="col-sm-3">
                                            <label for='5_mt_border_type' class='control-label'
                                                   style="vertical-align: top;">Border: </label>
                                            <select class="form-control border_select" data-select-name="5_mt"
                                                    id="5_mt_border_type" name="5_mt_border_type">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['5_mt']['5_mt_border_type']) && $busines_model_vals['5_mt']['5_mt_border_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['5_mt']['5_mt_border_type']) && $busines_model_vals['5_mt']['5_mt_border_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                            </select>
                                            <div id="border_details_color_5_mt"
                                                 class="border_details_5_mt border_details"
                                                 style="{{ isset($busines_model_vals['5_mt']['5_mt_border_type']) && $busines_model_vals['5_mt']['5_mt_border_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="5_mt_border_color" name="5_mt_border_color"
                                                       value="{{ isset($busines_model_vals['5_mt']['5_mt_border_color']) && $busines_model_vals['5_mt']['5_mt_border_color'] ? $busines_model_vals['5_mt']['5_mt_border_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#5_mt_border_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['5_mt']['5_mt_border_color']) && $busines_model_vals['5_mt']['5_mt_border_color'] ? $busines_model_vals['5_mt']['5_mt_border_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-inline">

                                        <div id="background_details_image_5_mt" class="background_details_5_mt"
                                             style="margin-top:5px; {{ isset($busines_model_vals['5_mt']['5_mt_background_type']) && $busines_model_vals['5_mt']['5_mt_background_type'] == 'image' ? '' : 'display:none' }}">
                                            <div class=" 5_mt_image">

                                                <label for='5_mt_image'
                                                       class='col-sm-3 col-md-2 control-label'>@lang('Upload Image')
                                                    : </label>
                                                <div class="col-sm-3">
                                                    <input type="file" class="form-control" id="5_mt_image"
                                                           name="5_mt_image" style="width:213px"
                                                           onchange="readTempImage(this,'5_mt_image');">
                                                    <span class="help-block" id="5_mt_image-error"></span>
                                                </div>
                                            </div>
                                            <div class="5_mt_image_temp"
                                                 style="margin-left: 20px;clear: both; display:none;">
                                                <label for="5_mt_image_temp"
                                                       class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                <img id="5_mt_image_temp" height="73" src="#" alt="Background Temp"/>
                                                <input id="img_5_mt_image" name="img_5_mt_image" type="hidden"
                                                       value="{{(isset($busines_model_vals['5_mt']['5_mt_image']) && $busines_model_vals['5_mt']['5_mt_image'])?(1):(0)}}"/>
                                            </div>

                                            @if(isset($busines_model_vals['5_mt']['5_mt_image']) && $busines_model_vals['5_mt']['5_mt_image'])
                                                <div class="background_image_row_5_mt"
                                                     style="margin-left: 20px;clear: both;">
                                                    <label for="5_mt_background_image"
                                                           class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                    <div style="margin-top: 10px;margin-left: 10px;">
                                                        <img height="73"
                                                             src="{{URL::to("/")}}{{$busines_model_vals['5_mt']['5_mt_image']}}?v={{time()}}"/>
                                                        <a type="button" class="btn btn-danger"
                                                           style="margin-top: 10px;" href="javascript:void(0)"
                                                           onclick="deletemtimg('5_mt')">Delete image</a>
                                                    </div>

                                                </div>
                                            @endif

                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 30px;">
                                <div class="col-sm-12 text-center">
                                    <button type="button" class="btn btn-success btn-save-style"
                                            name="btn-save-style" data-dismiss="modal" aria-hidden="true">
                                        Save Style
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div
        class="form-group 6_mt {{ isset($busines_model_vals['6_mt']['checked']) && $busines_model_vals['6_mt']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="mt[]" id="6_mt" class="user_type m_check"
                           value="6_mt" {{ isset($busines_model_vals['6_mt']['checked']) && $busines_model_vals['6_mt']['checked'] ? 'checked' : '' }} />
                    <input style="margin-top: -7px;" type="text" class="form-control" id="6_mt_name" name="6_mt_name"
                           value="{{ isset($busines_model_vals['6_mt']['6_mt_name']) && $busines_model_vals['6_mt']['6_mt_name'] ? $busines_model_vals['6_mt']['6_mt_name'] : '' }}">
                </label>
                <span class="help-block" id="6_mt-error"></span>
            </div>
        </div>
        <div class="col-md-1">

            <input type="text" class="form-control disable-speed" id="6_mt_speed" name="6_mt_speed"
                   value="{{ isset($busines_model_vals['6_mt']['6_mt_speed']) && $busines_model_vals['6_mt']['6_mt_speed'] ? $busines_model_vals['6_mt']['6_mt_speed'] : '0' }}">
        </div>

        <div class="col-md-1">
            <input type="text" class="form-control" id="6_mt_price" name="6_mt_price"
                   value="{{ isset($busines_model_vals['6_mt']['6_mt_price']) && $busines_model_vals['6_mt']['6_mt_price'] ? $busines_model_vals['6_mt']['6_mt_price'] : '0' }}">
        </div>
        <div class="col-md-1">
            <select class="form-control" id="6_mt_max_devices" name="6_mt_max_devices" style="min-width: 58px;">
                <option
                    value="1" {{ isset($busines_model_vals['6_mt']['6_mt_max_devices']) && $busines_model_vals['6_mt']['6_mt_max_devices'] == 1 ? 'selected' : '' }}>
                    1
                </option>
                <option value="5"
                        @if(isset($busines_model_vals['6_mt']['6_mt_max_devices']) && $busines_model_vals['6_mt']['6_mt_max_devices'] == 5)
                            selected
                        @elseif(!isset($busines_model_vals['6_mt']['6_mt_max_devices']))
                            selected
                @else
                    @endif
                >5
                </option>
                <option
                    value="10" {{ isset($busines_model_vals['6_mt']['6_mt_max_devices']) && $busines_model_vals['6_mt']['6_mt_max_devices'] == 10 ? 'selected' : '' }}>
                    10
                </option>
            </select>
        </div>
        <div class="col-md-1">

            <input type="checkbox" class="form-control c_parental_control" data-mid="6_mt" id="6_mt_parental_control"
                   name="6_mt_parental_control">
        </div>
        <script>
            $("#6_mt_parental_control").bootstrapSwitch({
                state: {{ isset($busines_model_vals['6_mt']['6_mt_parental_control']) && $busines_model_vals['6_mt']['6_mt_parental_control'] ? 'true' : 'false' }},
                size: 'small'
            });
        </script>
        <div class="col-md-4" style="padding-left: 25px;">
            <label class="control-label col-sm-3 col-md-2 text-nowrap box-style-label" style="margin-right: 25px;"><a
                    href="javascript:void(0)" data-toggle="modal" data-target="#add-box-style-6_mt">Box
                    Style</a></label>
            <textarea rows="2" style="min-width: 285px;" class="form-control jqte-gs" id="6_mt_description"
                      name="6_mt_description">{{ isset($busines_model_vals['6_mt']['6_mt_description']) && $busines_model_vals['6_mt']['6_mt_description'] ? $busines_model_vals['6_mt']['6_mt_description'] : '' }}</textarea>
        </div>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox"
                           name="6_mt_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['6_mt']['6_mt_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="6_mt_media_package_value"
                            style="min-width: 110px; margin-top: -7px;" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ !empty($busines_model_vals['6_mt']['6_mt_media_package']) && ($busines_model_vals['6_mt']['6_mt_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>

        <div class="modal fade" id="add-box-style-6_mt" tabindex="-1" role="dialog" aria-labelledby="Add Style"
             aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h5 class="modal-title" id="Create">Box Style</h5>
                    </div>
                    <div class="modal-body">

                        <br/>
                        <div class="form-horizontal alerts-form">
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="main">
                                    <div class="form-group form-inline">
                                        <label for='6_mt_background_type' class='col-sm-3 col-md-2 control-label'>Background: </label>
                                        <div class="col-sm-3">
                                            <select class="form-control background_select" data-select-name="6_mt"
                                                    id="6_mt_background_type" name="6_mt_background_type"
                                                    style="padding-left: 6px;">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['6_mt']['6_mt_background_type']) && $busines_model_vals['6_mt']['6_mt_background_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['6_mt']['6_mt_background_type']) && $busines_model_vals['6_mt']['6_mt_background_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                                <option
                                                    value="image" {{ isset($busines_model_vals['6_mt']['6_mt_background_type']) && $busines_model_vals['6_mt']['6_mt_background_type'] == 'image' ? 'selected' : '' }}>
                                                    Upload Image
                                                </option>
                                            </select>
                                            <div id="background_details_color_6_mt" class="background_details_6_mt"
                                                 style="{{ isset($busines_model_vals['6_mt']['6_mt_background_type']) && $busines_model_vals['6_mt']['6_mt_background_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="6_mt_background_color" name="6_mt_background_color"
                                                       value="{{ isset($busines_model_vals['6_mt']['6_mt_background_color']) && $busines_model_vals['6_mt']['6_mt_background_color'] ? $busines_model_vals['6_mt']['6_mt_background_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#6_mt_background_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['6_mt']['6_mt_background_color']) && $busines_model_vals['6_mt']['6_mt_background_color'] ? $busines_model_vals['6_mt']['6_mt_background_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>

                                        </div>
                                        <div class="col-sm-3">
                                            <label for='6_mt_border_type' class='control-label'
                                                   style="vertical-align: top;">Border: </label>
                                            <select class="form-control border_select" data-select-name="6_mt"
                                                    id="6_mt_border_type" name="6_mt_border_type">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['6_mt']['6_mt_border_type']) && $busines_model_vals['6_mt']['6_mt_border_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['6_mt']['6_mt_border_type']) && $busines_model_vals['6_mt']['6_mt_border_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                            </select>
                                            <div id="border_details_color_6_mt"
                                                 class="border_details_6_mt border_details"
                                                 style=" {{ isset($busines_model_vals['6_mt']['6_mt_border_type']) && $busines_model_vals['6_mt']['6_mt_border_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="6_mt_border_color" name="6_mt_border_color"
                                                       value="{{ isset($busines_model_vals['6_mt']['6_mt_border_color']) && $busines_model_vals['6_mt']['6_mt_border_color'] ? $busines_model_vals['6_mt']['6_mt_border_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#6_mt_border_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['6_mt']['6_mt_border_color']) && $busines_model_vals['6_mt']['6_mt_border_color'] ? $busines_model_vals['6_mt']['6_mt_border_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-inline">

                                        <div id="background_details_image_6_mt" class="background_details_6_mt"
                                             style="margin-top:5px; {{ isset($busines_model_vals['6_mt']['6_mt_background_type']) && $busines_model_vals['6_mt']['6_mt_background_type'] == 'image' ? '' : 'display:none' }}">
                                            <div class=" 6_mt_image">

                                                <label for='6_mt_image'
                                                       class='col-sm-3 col-md-2 control-label'>@lang('Upload Image')
                                                    : </label>
                                                <div class="col-sm-3">
                                                    <input type="file" class="form-control" id="6_mt_image"
                                                           name="6_mt_image" style="width:213px"
                                                           onchange="readTempImage(this,'6_mt_image');">
                                                    <span class="help-block" id="6_mt_image-error"></span>
                                                </div>
                                            </div>
                                            <div class="6_mt_image_temp"
                                                 style="margin-left: 20px;clear: both; display:none;">
                                                <label for="6_mt_image_temp"
                                                       class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                <img id="6_mt_image_temp" height="73" src="#" alt="Background Temp"/>
                                                <input id="img_6_mt_image" name="img_6_mt_image" type="hidden"
                                                       value="{{(isset($busines_model_vals['6_mt']['6_mt_image']) && $busines_model_vals['6_mt']['6_mt_image'])?(1):(0)}}"/>
                                            </div>

                                            @if(isset($busines_model_vals['6_mt']['6_mt_image']) && $busines_model_vals['6_mt']['6_mt_image'])
                                                <div class="background_image_row_6_mt"
                                                     style="margin-left: 20px;clear: both;">
                                                    <label for="6_mt_background_image"
                                                           class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                    <div style="margin-top: 10px;margin-left: 10px;">
                                                        <img height="73"
                                                             src="{{URL::to("/")}}{{$busines_model_vals['6_mt']['6_mt_image']}}?v={{time()}}"/>
                                                        <a type="button" class="btn btn-danger"
                                                           style="margin-top: 10px;" href="javascript:void(0)"
                                                           onclick="deletemtimg('6_mt')">Delete image</a>
                                                    </div>

                                                </div>
                                            @endif

                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 30px;">
                                <div class="col-sm-12 text-center">
                                    <button type="button" class="btn btn-success btn-save-style"
                                            name="btn-save-style" data-dismiss="modal" aria-hidden="true">
                                        Save Style
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

</div>

<div id="m_type_router"
     @if(isset($busines_model_vals['1_mt']['1_type']) && $busines_model_vals['1_mt']['1_type'] == 'router')
     @else
         style="display:none;"
    @endif
>


    <div class="form-group">
        <div class="col-md-2">

            <label class="text-nowrap" style="padding-left: 35px;">@lang('Name')</label>
        </div>

        <div class="col-md-1">
            <label class="text-nowrap" style="padding-left: 5px">@lang('Speed')</label>
        </div>

        <div class="col-md-1">
            <label class="" style="padding-left: 5px">@lang('Monthly Price')</label>
        </div>

        <div class="col-md-2">
            <label class="" style="padding-left: 2px">@lang('Parental Control') </label>
        </div>

        <div class="col-md-4">
            <label class="text-nowrap" style="padding-left: 5px">@lang('Description')</label>
        </div>
        <div class="col-md-2">
            <label class="text-nowrap" style="padding-left: 5px">Media Package <br>(OTT) </label>
        </div>

    </div>

    <div
        class="form-group 1_mt {{ isset($busines_model_vals['1_mt']['checked']) && $busines_model_vals['1_mt']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" class="m_check" id="1_mt" name="mt[]"
                           value="1_mt" {{ isset($busines_model_vals['1_mt']['checked']) && $busines_model_vals['1_mt']['checked'] ? 'checked' : '' }} />
                    <input style="margin-top: -7px;" type="text" class="form-control" id="1_mt_name" name="1_mt_name"
                           value="{{ isset($busines_model_vals['1_mt']['1_mt_name']) && $busines_model_vals['1_mt']['1_mt_name'] ? $busines_model_vals['1_mt']['1_mt_name'] : '' }}">

                </label>
                <span class="help-block" id="1_mt-error"></span>
            </div>
        </div>
        <div class="col-md-1">
            <input type="text" class="form-control disable-speed" id="1_mt_speed" name="1_mt_speed"
                   value="{{ isset($busines_model_vals['1_mt']['1_mt_speed']) && $busines_model_vals['1_mt']['1_mt_speed'] ? $busines_model_vals['1_mt']['1_mt_speed'] : '0' }}">
        </div>

        <div class="col-md-1">
            <input type="text" class="form-control" id="1_mt_price" name="1_mt_price"
                   value="{{ isset($busines_model_vals['1_mt']['1_mt_price']) && $busines_model_vals['1_mt']['1_mt_price'] ? $busines_model_vals['1_mt']['1_mt_price'] : '0' }}">
        </div>
        <div class="col-md-2">

            <input type="checkbox" class="form-control c_parental_control" data-mid="1_mt" id="1_mt_parental_control"
                   name="1_mt_parental_control">
        </div>
        <script>
            $("#1_mt_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['1_mt']['1_mt_parental_control']) && $busines_model_vals['1_mt']['1_mt_parental_control'] ? 'true' : 'false' }});
        </script>

        <div class="col-md-4">

            <label class="control-label col-sm-3 col-md-2 text-nowrap box-style-label" style="margin-right: 25px;"><a
                    href="javascript:void(0)" data-toggle="modal" data-target="#add-box-style-1_mt">Box
                    Style</a></label>
            <textarea rows="2" class="form-control jqte-gs" id="1_mt_description"
                      name="1_mt_description">{{ isset($busines_model_vals['1_mt']['1_mt_description']) && $busines_model_vals['1_mt']['1_mt_description'] ? $busines_model_vals['1_mt']['1_mt_description'] : '' }}</textarea>
        </div>

        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox"
                           name="1_mt_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['1_mt']['1_mt_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="1_mt_media_package_value"
                            style="min-width: 110px; margin-top: -7px;" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ !empty($busines_model_vals['1_mt']['1_mt_media_package']) && ($busines_model_vals['1_mt']['1_mt_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>

        <div class="modal fade" id="add-box-style-1_mt" tabindex="-1" role="dialog" aria-labelledby="Add Style"
             aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h5 class="modal-title" id="Create">Box Style</h5>
                    </div>
                    <div class="modal-body">

                        <br/>
                        <div class="form-horizontal alerts-form">
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="main">
                                    <div class="form-group form-inline">
                                        <label for='1_mt_background_type' class='col-sm-3 col-md-2 control-label'>Background: </label>
                                        <div class="col-sm-3">
                                            <select class="form-control background_select" data-select-name="1_mt"
                                                    id="1_mt_background_type" name="1_mt_background_type"
                                                    style="padding-left: 6px;">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['1_mt']['1_mt_background_type']) && $busines_model_vals['1_mt']['1_mt_background_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['1_mt']['1_mt_background_type']) && $busines_model_vals['1_mt']['1_mt_background_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                                <option
                                                    value="image" {{ isset($busines_model_vals['1_mt']['1_mt_background_type']) && $busines_model_vals['1_mt']['1_mt_background_type'] == 'image' ? 'selected' : '' }}>
                                                    Upload Image
                                                </option>
                                            </select>
                                            <div id="background_details_color_1_mt" class="background_details_1_mt"
                                                 style="{{ isset($busines_model_vals['1_mt']['1_mt_background_type']) && $busines_model_vals['1_mt']['1_mt_background_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="1_mt_background_color" name="1_mt_background_color"
                                                       value="{{ isset($busines_model_vals['1_mt']['1_mt_background_color']) && $busines_model_vals['1_mt']['1_mt_background_color'] ? $busines_model_vals['1_mt']['1_mt_background_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#1_mt_background_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['1_mt']['1_mt_background_color']) && $busines_model_vals['1_mt']['1_mt_background_color'] ? $busines_model_vals['1_mt']['1_mt_background_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>

                                        </div>
                                        <div class="col-sm-3">
                                            <label for='1_mt_border_type' class='control-label'
                                                   style="vertical-align: top;">Border: </label>
                                            <select class="form-control border_select" data-select-name="1_mt"
                                                    id="1_mt_border_type" name="1_mt_border_type">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['1_mt']['1_mt_border_type']) && $busines_model_vals['1_mt']['1_mt_border_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['1_mt']['1_mt_border_type']) && $busines_model_vals['1_mt']['1_mt_border_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                            </select>
                                            <div id="border_details_color_1_mt"
                                                 class="border_details_1_mt border_details"
                                                 style=" {{ isset($busines_model_vals['1_mt']['1_mt_border_type']) && $busines_model_vals['1_mt']['1_mt_border_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="1_mt_border_color" name="1_mt_border_color"
                                                       value="{{ isset($busines_model_vals['1_mt']['1_mt_border_color']) && $busines_model_vals['1_mt']['1_mt_border_color'] ? $busines_model_vals['1_mt']['1_mt_border_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#1_mt_border_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['1_mt']['1_mt_border_color']) && $busines_model_vals['1_mt']['1_mt_border_color'] ? $busines_model_vals['1_mt']['1_mt_border_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-inline">

                                        <div id="background_details_image_1_mt" class="background_details_1_mt"
                                             style="margin-top:5px; {{ isset($busines_model_vals['1_mt']['1_mt_background_type']) && $busines_model_vals['1_mt']['1_mt_background_type'] == 'image' ? '' : 'display:none' }}">
                                            <div class=" 1_mt_image">

                                                <label for='1_mt_image'
                                                       class='col-sm-3 col-md-2 control-label'>@lang('Upload Image')
                                                    : </label>
                                                <div class="col-sm-3">
                                                    <input type="file" class="form-control" id="1_mt_image"
                                                           name="1_mt_image" style="width:213px"
                                                           onchange="readTempImage(this,'1_mt_image');">
                                                    <span class="help-block" id="1_mt_image-error"></span>
                                                </div>
                                            </div>
                                            <div class="1_mt_image_temp"
                                                 style="margin-left: 20px;clear: both; display:none;">
                                                <label for="1_mt_image_temp"
                                                       class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                <img id="1_mt_image_temp" height="73" src="#" alt="Background Temp"/>
                                                <input id="img_1_mt_image" name="img_1_mt_image" type="hidden"
                                                       value="{{(isset($busines_model_vals['1_mt']['1_mt_image']) && $busines_model_vals['1_mt']['1_mt_image'])?(1):(0)}}"/>
                                            </div>

                                            @if(isset($busines_model_vals['1_mt']['1_mt_image']) && $busines_model_vals['1_mt']['1_mt_image'])
                                                <div class="background_image_row_1_mt"
                                                     style="margin-left: 20px;clear: both;">
                                                    <label for="1_mt_background_image"
                                                           class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                    <div style="margin-top: 10px;margin-left: 10px;">
                                                        <img height="73"
                                                             src="{{URL::to("/")}}{{$busines_model_vals['1_mt']['1_mt_image']}}?v={{time()}}"/>
                                                        <a type="button" class="btn btn-danger"
                                                           style="margin-top: 10px;" href="javascript:void(0)"
                                                           onclick="deletemtimg('1_mt')">Delete image</a>
                                                    </div>

                                                </div>
                                            @endif

                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 30px;">
                                <div class="col-sm-12 text-center">
                                    <button type="button" class="btn btn-success btn-save-style"
                                            name="btn-save-style" data-dismiss="modal" aria-hidden="true">
                                        Save Style
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <div
        class="form-group 2_mt {{ isset($busines_model_vals['2_mt']['checked']) && $busines_model_vals['2_mt']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" class="m_check" id="2_mt" name="mt[]"
                           value="2_mt" {{ isset($busines_model_vals['2_mt']['checked']) && $busines_model_vals['2_mt']['checked'] ? 'checked' : '' }} />
                    <input style="margin-top: -7px;" type="text" class="form-control" id="2_mt_name" name="2_mt_name"
                           value="{{ isset($busines_model_vals['2_mt']['2_mt_name']) && $busines_model_vals['2_mt']['2_mt_name'] ? $busines_model_vals['2_mt']['2_mt_name'] : '' }}">

                </label>
                <span class="help-block" id="2_mt-error"></span>
            </div>
        </div>
        <div class="col-md-1">
            <input type="text" class="form-control disable-speed" id="2_mt_speed" name="2_mt_speed"
                   value="{{ isset($busines_model_vals['2_mt']['2_mt_speed']) && $busines_model_vals['2_mt']['2_mt_speed'] ? $busines_model_vals['2_mt']['2_mt_speed'] : '0' }}">
        </div>

        <div class="col-md-1">
            <input type="text" class="form-control" id="2_mt_price" name="2_mt_price"
                   value="{{ isset($busines_model_vals['2_mt']['2_mt_price']) && $busines_model_vals['2_mt']['2_mt_price'] ? $busines_model_vals['2_mt']['2_mt_price'] : '0' }}">
        </div>
        <div class="col-md-2">

            <input type="checkbox" class="form-control c_parental_control" data-mid="2_mt" id="2_mt_parental_control"
                   name="2_mt_parental_control">
        </div>
        <script>
            $("#2_mt_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['2_mt']['2_mt_parental_control']) && $busines_model_vals['2_mt']['2_mt_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-4">

            <label class="control-label col-sm-3 col-md-2 text-nowrap box-style-label" style="margin-right: 25px;"><a
                    href="javascript:void(0)" data-toggle="modal" data-target="#add-box-style-2_mt">Box
                    Style</a></label>
            <textarea rows="2" style="min-width: 285px;" class="form-control jqte-gs" id="2_mt_description"
                      name="2_mt_description">{{ isset($busines_model_vals['2_mt']['2_mt_description']) && $busines_model_vals['2_mt']['2_mt_description'] ? $busines_model_vals['2_mt']['2_mt_description'] : '' }}</textarea>
        </div>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox"
                           name="2_mt_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['2_mt']['2_mt_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="2_mt_media_package_value"
                            style="min-width: 110px; margin-top: -7px;"
                        {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ !empty($busines_model_vals['2_mt']['2_mt_media_package']) && ($busines_model_vals['2_mt']['2_mt_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>

        <div class="modal fade" id="add-box-style-2_mt" tabindex="-1" role="dialog" aria-labelledby="Add Style"
             aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h5 class="modal-title" id="Create">Box Style</h5>
                    </div>
                    <div class="modal-body">

                        <br/>
                        <div class="form-horizontal alerts-form">
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="main">
                                    <div class="form-group form-inline">
                                        <label for='2_mt_background_type' class='col-sm-3 col-md-2 control-label'>Background: </label>
                                        <div class="col-sm-3">
                                            <select class="form-control background_select" data-select-name="2_mt"
                                                    id="2_mt_background_type" name="2_mt_background_type"
                                                    style="padding-left: 6px;">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['2_mt']['2_mt_background_type']) && $busines_model_vals['2_mt']['2_mt_background_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['2_mt']['2_mt_background_type']) && $busines_model_vals['2_mt']['2_mt_background_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                                <option
                                                    value="image" {{ isset($busines_model_vals['2_mt']['2_mt_background_type']) && $busines_model_vals['2_mt']['2_mt_background_type'] == 'image' ? 'selected' : '' }}>
                                                    Upload Image
                                                </option>
                                            </select>
                                            <div id="background_details_color_2_mt" class="background_details_2_mt"
                                                 style="{{ isset($busines_model_vals['2_mt']['2_mt_background_type']) && $busines_model_vals['2_mt']['2_mt_background_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="2_mt_background_color" name="2_mt_background_color"
                                                       value="{{ isset($busines_model_vals['2_mt']['2_mt_background_color']) && $busines_model_vals['2_mt']['2_mt_background_color'] ? $busines_model_vals['2_mt']['2_mt_background_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#2_mt_background_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['2_mt']['2_mt_background_color']) && $busines_model_vals['2_mt']['2_mt_background_color'] ? $busines_model_vals['2_mt']['2_mt_background_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>

                                        </div>
                                        <div class="col-sm-3">
                                            <label for='2_mt_border_type' class='control-label'
                                                   style="vertical-align: top;">Border: </label>
                                            <select class="form-control border_select" data-select-name="2_mt"
                                                    id="2_mt_border_type" name="2_mt_border_type">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['2_mt']['2_mt_border_type']) && $busines_model_vals['2_mt']['2_mt_border_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['2_mt']['2_mt_border_type']) && $busines_model_vals['2_mt']['2_mt_border_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                            </select>
                                            <div id="border_details_color_2_mt"
                                                 class="border_details_2_mt border_details"
                                                 style="{{ isset($busines_model_vals['2_mt']['2_mt_border_type']) && $busines_model_vals['2_mt']['2_mt_border_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="2_mt_border_color" name="2_mt_border_color"
                                                       value="{{ isset($busines_model_vals['2_mt']['2_mt_border_color']) && $busines_model_vals['2_mt']['2_mt_border_color'] ? $busines_model_vals['2_mt']['2_mt_border_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#2_mt_border_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['2_mt']['2_mt_border_color']) && $busines_model_vals['2_mt']['2_mt_border_color'] ? $busines_model_vals['2_mt']['2_mt_border_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-inline">

                                        <div id="background_details_image_2_mt" class="background_details_2_mt"
                                             style="margin-top:5px; {{ isset($busines_model_vals['2_mt']['2_mt_background_type']) && $busines_model_vals['2_mt']['2_mt_background_type'] == 'image' ? '' : 'display:none' }}">
                                            <div class=" 2_mt_image">

                                                <label for='2_mt_image'
                                                       class='col-sm-3 col-md-2 control-label'>@lang('Upload Image')
                                                    : </label>
                                                <div class="col-sm-3">
                                                    <input type="file" class="form-control" id="2_mt_image"
                                                           name="2_mt_image" style="width:213px"
                                                           onchange="readTempImage(this,'2_mt_image');">
                                                    <span class="help-block" id="2_mt_image-error"></span>
                                                </div>
                                            </div>
                                            <div class="2_mt_image_temp"
                                                 style="margin-left: 20px;clear: both; display:none;">
                                                <label for="2_mt_image_temp"
                                                       class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                <img id="2_mt_image_temp" height="73" src="#" alt="Background Temp"/>
                                                <input id="img_2_mt_image" name="img_2_mt_image" type="hidden"
                                                       value="{{(isset($busines_model_vals['2_mt']['2_mt_image']) && $busines_model_vals['2_mt']['2_mt_image'])?(1):(0)}}"/>
                                            </div>

                                            @if(isset($busines_model_vals['2_mt']['2_mt_image']) && $busines_model_vals['2_mt']['2_mt_image'])
                                                <div class="background_image_row_2_mt"
                                                     style="margin-left: 20px;clear: both;">
                                                    <label for="2_mt_background_image"
                                                           class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                    <div style="margin-top: 10px;margin-left: 10px;">
                                                        <img height="73"
                                                             src="{{URL::to("/")}}{{$busines_model_vals['2_mt']['2_mt_image']}}?v={{time()}}"/>
                                                        <a type="button" class="btn btn-danger"
                                                           style="margin-top: 10px;" href="javascript:void(0)"
                                                           onclick="deletemtimg('2_mt')">Delete image</a>
                                                    </div>

                                                </div>
                                            @endif

                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 30px;">
                                <div class="col-sm-12 text-center">
                                    <button type="button" class="btn btn-success btn-save-style"
                                            name="btn-save-style" data-dismiss="modal" aria-hidden="true">
                                        Save Style
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div
        class="form-group 3_mt {{ isset($busines_model_vals['3_mt']['checked']) && $busines_model_vals['3_mt']['checked'] ? 'active' : '' }}">
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox" class="m_check" id="3_mt" name="mt[]"
                           value="3_mt" {{ isset($busines_model_vals['3_mt']['checked']) && $busines_model_vals['3_mt']['checked'] ? 'checked' : '' }} />
                    <input style="margin-top: -7px;" type="text" class="form-control" id="3_mt_name" name="3_mt_name"
                           value="{{ isset($busines_model_vals['3_mt']['3_mt_name']) && $busines_model_vals['3_mt']['3_mt_name'] ? $busines_model_vals['3_mt']['3_mt_name'] : '' }}">

                </label>
                <span class="help-block" id="3_mt-error"></span>
            </div>
        </div>
        <div class="col-md-1">

            <input type="text" class="form-control disable-speed" id="3_mt_speed" name="3_mt_speed"
                   value="{{ isset($busines_model_vals['3_mt']['3_mt_speed']) && $busines_model_vals['3_mt']['3_mt_speed'] ? $busines_model_vals['3_mt']['3_mt_speed'] : '0' }}">
        </div>
        <div class="col-md-1">
            <input type="text" class="form-control" id="3_mt_price" name="3_mt_price"
                   value="{{ isset($busines_model_vals['3_mt']['3_mt_price']) && $busines_model_vals['3_mt']['3_mt_price'] ? $busines_model_vals['3_mt']['3_mt_price'] : '0' }}">
        </div>
        <div class="col-md-2">

            <input type="checkbox" class="form-control c_parental_control" data-mid="1_mt" id="3_mt_parental_control"
                   name="3_mt_parental_control">
        </div>
        <script>
            $("#3_mt_parental_control").bootstrapSwitch('state', {{ isset($busines_model_vals['3_mt']['3_mt_parental_control']) && $busines_model_vals['3_mt']['3_mt_parental_control'] ? 'true' : 'false' }});
        </script>
        <div class="col-md-4">

            <label class="control-label col-sm-3 col-md-2 text-nowrap box-style-label" style="margin-right: 25px;"><a
                    href="javascript:void(0)" data-toggle="modal" data-target="#add-box-style-3_mt">Box
                    Style</a></label>
            <textarea rows="2" style="min-width: 285px;" class="form-control jqte-gs" id="3_mt_description"
                      name="3_mt_description">{{ isset($busines_model_vals['3_mt']['3_mt_description']) && $busines_model_vals['3_mt']['3_mt_description'] ? $busines_model_vals['3_mt']['3_mt_description'] : '' }}</textarea>
        </div>
        <div class="col-md-2">
            <div class="checkbox">
                <label>
                    <input type="checkbox"
                           name="3_mt_media_package" {{(empty($motvPackages)) ? 'disabled' : ''}} {{ !empty($busines_model_vals['3_mt']['3_mt_media_package']) && !empty($motvPackages) ? 'checked' : '' }}/>
                    <select class="form-control" name="3_mt_media_package_value"
                            style="min-width: 110px; margin-top: -7px;" {{(empty($motvPackages)) ? 'disabled' : ''}}>
                        <option value="">Select</option>
                        @foreach($motvPackages as $motvPackage)
                            <option
                                value="{{$motvPackage['id']}}" {{ !empty($busines_model_vals['3_mt']['3_mt_media_package']) && ($busines_model_vals['3_mt']['3_mt_media_package'] == $motvPackage['id']) ? 'selected' : '' }}>{{$motvPackage['name']}}</option>
                        @endforeach
                    </select>
                </label>
            </div>
        </div>

        <div class="modal fade" id="add-box-style-3_mt" tabindex="-1" role="dialog" aria-labelledby="Add Style"
             aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h5 class="modal-title" id="Create">Box Style</h5>
                    </div>
                    <div class="modal-body">

                        <br/>
                        <div class="form-horizontal alerts-form">
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="main">
                                    <div class="form-group form-inline">
                                        <label for='3_mt_background_type' class='col-sm-3 col-md-2 control-label'>Background: </label>
                                        <div class="col-sm-3">
                                            <select class="form-control background_select" data-select-name="3_mt"
                                                    id="3_mt_background_type" name="3_mt_background_type"
                                                    style="padding-left: 6px;">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['3_mt']['3_mt_background_type']) && $busines_model_vals['3_mt']['3_mt_background_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['3_mt']['3_mt_background_type']) && $busines_model_vals['3_mt']['3_mt_background_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                                <option
                                                    value="image" {{ isset($busines_model_vals['3_mt']['3_mt_background_type']) && $busines_model_vals['3_mt']['3_mt_background_type'] == 'image' ? 'selected' : '' }}>
                                                    Upload Image
                                                </option>
                                            </select>
                                            <div id="background_details_color_3_mt" class="background_details_3_mt"
                                                 style="{{ isset($busines_model_vals['3_mt']['3_mt_background_type']) && $busines_model_vals['3_mt']['3_mt_background_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="3_mt_background_color" name="3_mt_background_color"
                                                       value="{{ isset($busines_model_vals['3_mt']['3_mt_background_color']) && $busines_model_vals['3_mt']['3_mt_background_color'] ? $busines_model_vals['3_mt']['3_mt_background_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#3_mt_background_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['3_mt']['3_mt_background_color']) && $busines_model_vals['3_mt']['3_mt_background_color'] ? $busines_model_vals['3_mt']['3_mt_background_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>

                                        </div>
                                        <div class="col-sm-3">
                                            <label for='3_mt_border_type' class='control-label'
                                                   style="vertical-align: top;">Border: </label>
                                            <select class="form-control border_select" data-select-name="3_mt"
                                                    id="3_mt_border_type" name="3_mt_border_type">
                                                <option
                                                    value="default" {{ isset($busines_model_vals['3_mt']['3_mt_border_type']) && $busines_model_vals['3_mt']['3_mt_border_type'] == 'default' ? 'selected' : '' }}>
                                                    Default
                                                </option>
                                                <option
                                                    value="color" {{ isset($busines_model_vals['3_mt']['3_mt_border_type']) && $busines_model_vals['3_mt']['3_mt_border_type'] == 'color' ? 'selected' : '' }}>
                                                    Color Picker
                                                </option>
                                            </select>
                                            <div id="border_details_color_3_mt"
                                                 class="border_details_3_mt border_details"
                                                 style=" {{ isset($busines_model_vals['3_mt']['3_mt_border_type']) && $busines_model_vals['3_mt']['3_mt_border_type'] == 'color' ? '' : 'display:none' }}">
                                                <input id="3_mt_border_color" name="3_mt_border_color"
                                                       value="{{ isset($busines_model_vals['3_mt']['3_mt_border_color']) && $busines_model_vals['3_mt']['3_mt_border_color'] ? $busines_model_vals['3_mt']['3_mt_border_color'] : '#f00' }}"/>
                                                <script>
                                                    $("#3_mt_border_color").spectrum({
                                                        color: "{{ isset($busines_model_vals['3_mt']['3_mt_border_color']) && $busines_model_vals['3_mt']['3_mt_border_color'] ? $busines_model_vals['3_mt']['3_mt_border_color'] : '#f00' }}",
                                                        showInput: true,
                                                        preferredFormat: "hex",
                                                    });
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-inline">
                                        <div id="background_details_image_3_mt" class="background_details_3_mt"
                                             style="margin-top:5px; {{ isset($busines_model_vals['3_mt']['3_mt_background_type']) && $busines_model_vals['3_mt']['3_mt_background_type'] == 'image' ? '' : 'display:none' }}">
                                            <div class=" 3_mt_image">
                                                <label for='3_mt_image'
                                                       class='col-sm-3 col-md-2 control-label'>@lang('Upload Image')
                                                    : </label>
                                                <div class="col-sm-3">
                                                    <input type="file" class="form-control" id="3_mt_image"
                                                           name="3_mt_image" style="width:213px"
                                                           onchange="readTempImage(this,'3_mt_image');">
                                                    <span class="help-block" id="3_mt_image-error"></span>
                                                </div>
                                            </div>
                                            <div class="3_mt_image_temp"
                                                 style="margin-left: 20px;clear: both; display:none;">
                                                <label for="3_mt_image_temp"
                                                       class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                <img id="3_mt_image_temp" height="73" src="#" alt="Background Temp"/>
                                                <input id="img_3_mt_image" name="img_3_mt_image" type="hidden"
                                                       value="{{(isset($busines_model_vals['3_mt']['3_mt_image']) && $busines_model_vals['3_mt']['3_mt_image'])?(1):(0)}}"/>
                                            </div>

                                            @if(isset($busines_model_vals['3_mt']['3_mt_image']) && $busines_model_vals['3_mt']['3_mt_image'])
                                                <div class="background_image_row_3_mt"
                                                     style="margin-left: 20px;clear: both;">
                                                    <label for="3_mt_background_image"
                                                           class="control-label col-sm-3 col-md-2 text-nowrap"></label>
                                                    <div style="margin-top: 10px;margin-left: 10px;">
                                                        <img height="73"
                                                             src="{{URL::to("/")}}{{$busines_model_vals['3_mt']['3_mt_image']}}?v={{time()}}"/>
                                                        <a type="button" class="btn btn-danger"
                                                           style="margin-top: 10px;" href="javascript:void(0)"
                                                           onclick="deletemtimg('3_mt')">Delete image</a>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 30px;">
                                <div class="col-sm-12 text-center">
                                    <button type="button" class="btn btn-success btn-save-style"
                                            name="btn-save-style" data-dismiss="modal" aria-hidden="true">
                                        Save Style
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<div class="col-md-12" style="margin-left: 15px;margin-top: 10px; padding-right: 0px;">

    <h4>BYOD <i class="fa fa-question-circle fa-fw" data-toggle="tooltip" data-placement="top"
                title="Any device who's MAC address is entered will automatically bypass any captive portal or provisioned business workflow that has been configured here in group settings."
                data-container="body" style="position: absolute;top: 5px;margin-left: 2px;font-size: 14px;"></i></h4>

</div>

<div class="col-md-12" style="margin-left: 15px;margin-bottom: 30px;">
    <div class="panel panel-default panel-condensed">

        <div class="devices-headers-table-menu" style="padding:6px 6px 0px 0px;">
            <div class="actions btn-group">
                <div class="dropdown btn-group">
                    <button class="btn btn-default dropdown-toggle" type="button" data-toggle="dropdown">
                        <span class="dropdown-text bd_counter_value">10</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu pull-right" role="menu">
                        <li aria-selected="false"><a data-counter_value="10"
                                                     class="dropdown-item dropdown-item-button bd_counter">10</a></li>
                        <li aria-selected="false"><a data-counter_value="25"
                                                     class="dropdown-item dropdown-item-button bd_counter">25</a></li>
                        <li aria-selected="false"><a data-counter_value="50"
                                                     class="dropdown-item dropdown-item-button bd_counter">50</a></li>
                    </ul>
                    <input type="hidden" id="bd_counter" value="10">
                    <input type="hidden" id="bd_current_page" value="0">
                    <input type="hidden" id="bd_total_items"
                           value="{{isset($group_BYOD_devices['items_totals']) ? $group_BYOD_devices['items_totals'] : 0 }}">

                </div>
            </div>
            <div class="pull-left">
                <div class="form-inline devices-search-header">
                    <div class="form-group " style="margin-left: 0px">

                        <input type="text" name="searchquery" id="searchquery" value="" class="form-control"
                               placeholder="Search">

                    </div>
                    <input type="button" class="btn btn-info" value="Search" id="bd_search" style="margin-left: 20px;">
                    <a href="javascript:void(0)" title="Reset criteria to default."
                       class="btn btn-default reset_bd_devices">Reset</a>
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal"
                            data-target="#add-edit-byod-device"><i class="fa fa-plus"></i> Add Device
                    </button>

                </div>
            </div>
        </div>
        <div class="row"></div>
        <div class="table-responsive" style="margin-top: 15px;">
            <table class="table table-hover table-condensed table-striped" aria-busy="false">
                <thead>
                <tr>
                    <th data-column-id="icon" class="text-left" style="width:120px; "><span class="text"
                                                                                            style="margin-left: 5px;color:var(--color-light2);">WiFi MAC</span>
                    </th>
                    <th data-column-id="icon" class="text-left" style="width:70px; "><span class="text"
                                                                                           style="color:var(--color-light2); margin-left: 30px;">Speed</span>
                    </th>

                    <th data-column-id="icon" class="text-left" style="width:140px; ">Parental Control</th>
                    <th data-column-id="hostname" class="text-left" style="width: 40%;"><span class="text"
                                                                                              style="color:var(--color-light2);">Description</span>
                    </th>

                    <th data-column-id="hostname" class="text-left"><span class="text"
                                                                          style="color:var(--color-light2);">Actions</span>
                    </th>
                </thead>
                <tbody class="bd_list">
                @if(isset($group_BYOD_devices['items_list']) && count($group_BYOD_devices['items_list']))
                    @foreach($group_BYOD_devices['items_list'] as $bd_device)
                        <tr id="row-id-{{$bd_device->id}}">
                            <td class="text-left" style="width:70px;"><span
                                    class="device-table-icon">{{$bd_device->mac_address}}</span></td>
                            <td class="text-left" style="width:5px;"><span class="device-table-icon"
                                                                           style="margin-left: 30px;">{{$bd_device->speed}}</span>
                            </td>

                            <td class="text-left" style="width:auto;"><span class="device-table-icon"
                                                                            style="width:auto;">{{($bd_device->parental_control)?("Yes"):("No")}}</span>
                            </td>
                            <td class="text-left" style="width:auto;"><span class="device-table-icon"
                                                                            style="width:auto;">{{$bd_device->description}}</span>
                            </td>

                            <td class="text-left" style="width:90px;">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-primary" data-toggle="modal"
                                            data-placement="left" data-target="#add-edit-byod-device"
                                            data-db_device_id="{{$bd_device->id}}"
                                            data-db_device_mac_address="{{$bd_device->mac_address}}"
                                            data-db_device_description="{{$bd_device->description}}"
                                            data-db_device_speed="{{$bd_device->speed}}"
                                            data-db_parental_control="{{($bd_device->parental_control)?("1"):("0")}}"
                                            name="edit-alert-rule" data-content="Edit" data-container="body"
                                            data-original-title="" title=""><i class="fa fa-lg fa-pencil"
                                                                               aria-hidden="true"></i></button>

                                    <button type="button" class="btn btn-danger" aria-label="Delete"
                                            data-placement="left" data-toggle="modal" data-target="#confirm-delete"
                                            data-db_device_id="{{$bd_device->id}}"
                                            data-db_device_name="{{$bd_device->mac_address}}" name="delete-alert-rule"
                                            data-content="Delete {{$bd_device->mac_address}}" data-container="body"
                                            data-original-title="" title=""><i class="fa fa-lg fa-trash"
                                                                               aria-hidden="true"></i></button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                @endif
                </tbody>
            </table>
        </div>
        <div class="container-fluid">
            <div class="row" id="bd_pagination">
            </div>
        </div>
    </div>


</div>

<div class="form-group company_name">
    <label for="company_name" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Name') *</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_name" name="company_name"
               value="{{ isset($groupSettings->company_name) && $groupSettings->company_name ? $groupSettings->company_name : '' }}">
        <span class="help-block" id="company_name-error">@error('company_name') {{ $message }} @enderror</span>
    </div>
</div>

<div class="form-group company_address">
    <label for="company_address" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Address')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_address" name="company_address"
               value="{{ isset($groupSettings->company_address) && $groupSettings->company_address ? $groupSettings->company_address : '' }}">
        <span class="help-block" id="company_address-error"></span>
    </div>
</div>

<div class="form-group company_email">
    <label for="company_email" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Email') *</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_email" name="company_email"
               value="{{ isset($groupSettings->company_email) && $groupSettings->company_email ? $groupSettings->company_email : '' }}">
        <span class="help-block" id="company_email-error">@error('company_email') {{ $message }} @enderror</span>
    </div>
</div>

<div class="form-group company_phone">
    <label for="company_phone" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Phone')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_phone" name="company_phone"
               value="{{ isset($groupSettings->company_phone) && $groupSettings->company_phone ? $groupSettings->company_phone : '' }}">
        <span class="help-block" id="company_phone-error"></span>
    </div>
</div>

<div class="form-group company_social_facebook">
    <label for="company_social_facebook"
           class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Facebook')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_social_facebook" name="company_social_facebook"
               value="{{ isset($groupSettings->company_social_facebook) && $groupSettings->company_social_facebook ? $groupSettings->company_social_facebook : '' }}">
        <span class="help-block" id="company_social_facebook-error"></span>
    </div>
</div>

<div class="form-group company_social_twitter">
    <label for="company_social_twitter"
           class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Twitter')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_social_twitter" name="company_social_twitter"
               value="{{ isset($groupSettings->company_social_twitter) && $groupSettings->company_social_twitter ? $groupSettings->company_social_twitter : '' }}">
        <span class="help-block" id="company_social_twitter-error"></span>
    </div>
</div>

<div class="form-group company_social_google">
    <label for="company_social_google"
           class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Google+')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_social_google" name="company_social_google"
               value="{{ isset($groupSettings->company_social_google) && $groupSettings->company_social_google ? $groupSettings->company_social_google : '' }}">
        <span class="help-block" id="company_social_google-error"></span>
    </div>
</div>

<div class="form-group company_social_dribbble">
    <label for="company_social_dribbble"
           class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Company Dribbble')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="company_social_dribbble" name="company_social_dribbble"
               value="{{ isset($groupSettings->company_social_dribbble) && $groupSettings->company_social_dribbble ? $groupSettings->company_social_dribbble : '' }}">
        <span class="help-block" id="company_social_dribbble-error"></span>
    </div>
</div>


<div class="form-group logo">
    <label for="logo" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Logo')</label>
    <div class="col-sm-9 col-md-10">
        <input type="file" class="form-control" id="logo" name="logo" style="padding-left: 12px;"
               onchange="readTempImage(this,'logo');">
        <span class="help-block" id="logo-error"></span>
    </div>
</div>
<div class="form-group logo_temp" style="display:none;">
    <label for="logo" class="control-label col-sm-3 col-md-2 text-nowrap"></label>
    <div class="col-sm-2 col-md-2">
        <img id="logo_temp" width="150" src="#" alt="Logo Temp"/>
    </div>
    <input id="img_logo" name="img_logo" type="hidden"
           value="{{(isset($groupSettings->logo) && $groupSettings->logo)?(1):(0)}}"/>
</div>
@if(isset($groupSettings->logo) && $groupSettings->logo)
    <div class="form-group logo_image">
        <label for="logo" class="control-label col-sm-3 col-md-2 text-nowrap"></label>
        <div class="col-sm-2 col-md-2">
            <img width="150" src="{{$groupSettings->logo}}?v={{time()}}"/>
        </div>
        <div class="col-sm-2 col-md-2">
            <a type="button" class="btn btn-danger" href="javascript:void(0)" onclick="deleteimg()">Delete image</a>
        </div>
    </div>
@endif

<div class="form-group background_image">
    <label for="background_image" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Background Image')
        <br><small>(@lang('Optional'))</small></label>
    <div class="col-sm-9 col-md-10">
        <input type="file" class="form-control" id="background_image" name="background_image"
               onchange="readTempImage(this,'background_image');">
        <span class="help-block" id="background_image-error"></span>
    </div>
</div>
<div class="form-group background_image_temp" style="display:none;">
    <label for="background_image_temp" class="control-label col-sm-3 col-md-2 text-nowrap"></label>
    <div class="col-sm-2 col-md-2">
        <img id="background_image_temp" width="150" src="#" alt="Background Image Temp"/>
    </div>
    <input id="img_background_image" name="img_background_image" type="hidden"
           value="{{(isset($groupSettings->background_image) && $groupSettings->background_image)?(1):(0)}}"/>
</div>
@if(isset($groupSettings->background_image) && $groupSettings->background_image)
    <div class="form-group background_image_row">
        <label for="background_image" class="control-label col-sm-3 col-md-2 text-nowrap"></label>
        <div class="col-sm-2 col-md-2">
            <img width="150" src="{{$groupSettings->background_image}}?v={{time()}}"/>
        </div>
        <div class="col-sm-2 col-md-2">
            <a type="button" class="btn btn-danger" href="javascript:void(0)" onclick="deletebkimg()">Delete image</a>
        </div>

    </div>
@endif

<div class="form-group incentivisation">
    <label for="incentivisation" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Incentivisation') *</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="incentivisation" name="incentivisation"
               value="{{ isset($groupSettings->incentivisation) && $groupSettings->incentivisation ? $groupSettings->incentivisation : '' }}">
        <span class="help-block" id="incentivisation-error">@error('incentivisation') {{ $message }} @enderror</span>
    </div>
</div>

<input type="hidden" id="group_id" name="group_id" value="{{ $groupSettings->device_group_id }}"/>

<div class="form-group hotspot-list-results" style="margin-bottom: 0px;margin-top: -20px;">
    <div class="col-md-2">&nbsp</div>
    <div class="col-md-2">
        <span class="help-block" id="hotspot-list-results-error"></span>
        <span class="help-block" id="general-results-error"></span>
    </div>
</div>
<div id="dialog-confirm" title="Warning!" style="display:none;">
    <p><span class="ui-icon ui-icon-alert"></span>Changing the Business Model will terminate existing service
        obligations and re-set the routers without recourse.<br/>Please confirm.</p>
</div>
