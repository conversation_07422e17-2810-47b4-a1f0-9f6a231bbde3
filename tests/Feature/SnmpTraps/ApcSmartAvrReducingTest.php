<?php
/**
 * ApcSmartAvrReducingTest.php
 *
 * -Description-
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 *
 * @link       https://www.librenms.org
 *
 * <AUTHOR>
 */

namespace LibreNMS\Tests\Feature\SnmpTraps;

use LibreNMS\Enum\Severity;

class ApcSmartAvrReducingTest extends SnmpTrapTestCase
{
    /**
     * Test ApcSmartAvrReducing handle
     *
     * @return void
     */
    public function testApcSmartAvrReducing(): void
    {
        $this->assertTrapLogsMessage(<<<'TRAP'
{{ hostname }}
UDP: [{{ ip }}]:57602->[********]:162
SNMPv2-MIB::sysUpTime.0 459:20:47:26.90
SNMPv2-MIB::snmpTrapOID.0 PowerNet-MIB::smartAvrReducing
PowerNet-MIB::mtrapargsString "UPS: Compensating for a high input voltage."
SNMPv2-MIB::snmpTrapEnterprise.0 PowerNet-MIB::apc
TRAP,
            'UPS: Compensating for a high input voltage.',
            'Could not handle testApcSmartAvrReducing trap',
            [Severity::Notice],
        );
    }
}
