<?php
/**
 * OspfTxRetransmitTest.php
 *
 * -Description-
 *
 * Unit test for the OspfTxRetransmit SNMP trap handler. Will verify
 * that the trap is handled correctly logging the right event.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 *
 * @link       https://www.librenms.org
 */

namespace LibreNMS\Tests\Feature\SnmpTraps;

class OspfTxRetransmitTest extends SnmpTrapTestCase
{
    /**
     * Test OSPF lsUpdate packet type trap
     *
     * @return void
     */
    public function testLsUpdatePacket(): void
    {
        $this->assertTrapLogsMessage(<<<'TRAP'
{{ hostname }}
UDP: [{{ ip }}]:57602->[********]:162
SNMPv2-MIB::sysUpTime.0 16:21:49.33
SNMPv2-MIB::snmpTrapOID.0 OSPF-TRAP-MIB::ospfTxRetransmit
OSPF-MIB::ospfRouterId ********
OSPF-MIB::ospfIfIpAddress *********
OSPF-MIB::ospfAddressLessIf 0
OSPF-MIB::ospfNbrRtrId ********
OSPF-TRAP-MIB::ospfPacketType lsUpdate
OSPF-MIB::ospfLsdbType routerLink
OSPF-MIB::ospfLsdbLsid ********
OSPF-MIB::ospfLsdbRouterId ********
TRAP,
            'SNMP Trap: OSPFTxRetransmit trap received from {{ hostname }}(Router ID: ********). A lsUpdate packet was sent to ********. LSType: routerLink, route ID: ********, originating from ********.',
            'Could not handle testlsUpdatePacket trap',
        );
    }

    /**
     * Test OSPF non lsUpdate packet type
     *
     * @return void
     */
    public function testNotLsUpdatePacket(): void
    {
        $this->assertTrapLogsMessage(<<<'TRAP'
{{ hostname }}
UDP: [{{ ip }}]:57602->[********]:162
SNMPv2-MIB::sysUpTime.0 16:21:49.33
SNMPv2-MIB::snmpTrapOID.0 OSPF-TRAP-MIB::ospfTxRetransmit
OSPF-MIB::ospfRouterId ********
OSPF-MIB::ospfIfIpAddress *********
OSPF-MIB::ospfAddressLessIf 0
OSPF-MIB::ospfNbrRtrId ********
OSPF-TRAP-MIB::ospfPacketType hello
OSPF-MIB::ospfLsdbType routerLink
OSPF-MIB::ospfLsdbLsid ********
OSPF-MIB::ospfLsdbRouterId ********
TRAP,
            'SNMP TRAP: {{ hostname }}(Router ID: ********) sent a hello packet to ********.',
            'Could not handle testNotLsUpdatePacket trap',
        );
    }
}
