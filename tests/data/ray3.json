{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.33555.4.1", "sysDescr": "Microwave Link", "sysContact": null, "version": "1.1.1.0", "hardware": "RAy3-24", "features": "U", "location": "<private>", "os": "ray3", "type": "wireless", "serial": "1801456327", "icon": "ray.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Eth1 (p1)", "ifName": "Eth1 (p1)", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Eth1 (p1)", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Eth2 (p10)", "ifName": "Eth2 (p10)", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Eth2 (p10)", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Air (p9)", "ifName": "Air (p9)", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Air (p9)", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": "eth1", "port_descr_descr": "p1", "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": "p1", "ifDescr": "Eth1 (p1)", "ifName": "Eth1 (p1)", "portName": null, "ifIndex": 1, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 10240, "ifType": "ethernetCsmacd", "ifAlias": "Eth1 (p1)", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 9, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 2991651445930, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 1183719842652, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 88493, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 87958, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 3249447, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 3334969, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": "eth2", "port_descr_descr": "p10", "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": "p10", "ifDescr": "Eth2 (p10)", "ifName": "Eth2 (p10)", "portName": null, "ifIndex": 2, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 10240, "ifType": "ethernetCsmacd", "ifAlias": "Eth2 (p10)", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": "air", "port_descr_descr": "p9", "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": "p9", "ifDescr": "Air (p9)", "ifName": "Air (p9)", "portName": null, "ifIndex": 3, "ifSpeed": 2500000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 10240, "ifType": "ethernetCsmacd", "ifAlias": "Air (p9)", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 1513514147873, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 2990994646164, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 87957, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 88491, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 3157724, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 3426685, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.33555.4.1.5.1.0", "processor_index": "0", "processor_type": "ray3", "processor_usage": 1, "processor_descr": "Processor", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "0", "entPhysicalIndex": null, "mempool_type": "ray3", "mempool_class": "system", "mempool_precision": 1, "mempool_descr": "Memory", "mempool_perc": 27, "mempool_perc_oid": ".*******.4.1.33555.4.1.5.2.0", "mempool_used": 27, "mempool_used_oid": null, "mempool_free": 73, "mempool_free_oid": null, "mempool_total": 100, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "ber", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******1.0", "sensor_index": "0", "sensor_type": "ray3", "sensor_descr": "BER", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": 0, "sensor_limit_low": null, "sensor_limit_low_warn": 0, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "rxModulationIndex-ray3", "sensor_descr": "Rx Modulation Rate", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "rxModulationIndex-ray3"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "systemStatus", "sensor_descr": "System Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "txModulationIndex-ray3", "sensor_descr": "Tx Modulation Rate", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "txModulationIndex-ray3"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "ray3", "sensor_descr": "Temperature", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 39.3, "sensor_limit": 59.3, "sensor_limit_warn": null, "sensor_limit_low": 29.3, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "ray3", "sensor_descr": "Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 44.2, "sensor_limit": 50.83, "sensor_limit_warn": null, "sensor_limit_low": 37.57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "rxModulationIndex-ray3", "state_descr": "QPSK_S", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QPSK", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM16", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM32", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM64", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM128", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM256", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM512", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM1024", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM2048", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM4096", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 0}, {"state_name": "systemStatus", "state_descr": "Unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "systemStatus", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "systemStatus", "state_descr": "Warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "systemStatus", "state_descr": "Alarm", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 3}, {"state_name": "txModulationIndex-ray3", "state_descr": "QPSK_S", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QPSK", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM16", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM32", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM64", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM128", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM256", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM512", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM1024", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM2048", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM4096", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 0}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "ber", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******1.0", "sensor_index": "0", "sensor_type": "ray3", "sensor_descr": "BER", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": 0, "sensor_limit_low": null, "sensor_limit_low_warn": 0, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "rxModulationIndex-ray3", "sensor_descr": "Rx Modulation Rate", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 9, "user_func": null, "rrd_type": "GAUGE", "state_name": "rxModulationIndex-ray3"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "systemStatus", "sensor_descr": "System Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "txModulationIndex-ray3", "sensor_descr": "Tx Modulation Rate", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 7, "user_func": null, "rrd_type": "GAUGE", "state_name": "txModulationIndex-ray3"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "ray3", "sensor_descr": "Temperature", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 39.3, "sensor_limit": 59.3, "sensor_limit_warn": null, "sensor_limit_low": 29.3, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "ray3", "sensor_descr": "Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 44.2, "sensor_limit": 50.83, "sensor_limit_warn": null, "sensor_limit_low": 37.57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "rxModulationIndex-ray3", "state_descr": "QPSK_S", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QPSK", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM16", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM32", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM64", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM128", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM256", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM512", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM1024", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM2048", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray3", "state_descr": "QAM4096", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 0}, {"state_name": "systemStatus", "state_descr": "Unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "systemStatus", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "systemStatus", "state_descr": "Warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "systemStatus", "state_descr": "Alarm", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 3}, {"state_name": "txModulationIndex-ray3", "state_descr": "QPSK_S", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QPSK", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM16", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM32", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM64", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM128", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM256", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM512", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM1024", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM2048", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray3", "state_descr": "QAM4096", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 0}]}}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom3-rx", "sensor_descr": "RX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 24032, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom3-tx", "sensor_descr": "TX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 24190, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom3-pow-conf", "sensor_descr": "Tx Power Configured", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 10, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom3-pow-cur", "sensor_descr": "Tx Power Current", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 10, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "2", "sensor_type": "racom3-maxNetBitrate", "sensor_descr": "Max Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000000, "sensor_aggregator": "sum", "sensor_current": 1003000000, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "1", "sensor_type": "racom3-netBitrate", "sensor_descr": "Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_aggregator": "sum", "sensor_current": 730530000, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "1", "sensor_type": "racom3", "sensor_descr": "RSSI", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -53.3, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom3-rx", "sensor_descr": "RX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 24032, "sensor_prev": 24032, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom3-tx", "sensor_descr": "TX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 24190, "sensor_prev": 24190, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom3-pow-conf", "sensor_descr": "Tx Power Configured", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 10, "sensor_prev": 10, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom3-pow-cur", "sensor_descr": "Tx Power Current", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 10, "sensor_prev": 10, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "2", "sensor_type": "racom3-maxNetBitrate", "sensor_descr": "Max Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000000, "sensor_aggregator": "sum", "sensor_current": 1003000000, "sensor_prev": 1003000000, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "1", "sensor_type": "racom3-netBitrate", "sensor_descr": "Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_aggregator": "sum", "sensor_current": 730530000, "sensor_prev": 730530000, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "1", "sensor_type": "racom3", "sensor_descr": "RSSI", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -53.3, "sensor_prev": -53.3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}]}}}