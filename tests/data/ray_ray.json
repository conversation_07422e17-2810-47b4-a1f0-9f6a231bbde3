{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.33555.1.1", "sysDescr": "Microwave Link", "sysContact": null, "version": "2.2.5.0", "hardware": "RAy2-17", "features": "U", "location": "<private>", "os": "ray", "type": "wireless", "serial": "9879730", "icon": "ray.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "p2_data_e1", "ifName": "p2_data_e1", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "p2_data_e1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "p4_data_e2", "ifName": "p4_data_e2", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "p4_data_e2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "p6_air", "ifName": "p6_air", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "p6_air", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "p2_data_e1", "ifName": "p2_data_e1", "portName": null, "ifIndex": 1, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 10240, "ifType": "ethernetCsmacd", "ifAlias": "p2_data_e1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1541434, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 2295681, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 983743, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 1263010, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 27448, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 28173, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 956281, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 1234825, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "p4_data_e2", "ifName": "p4_data_e2", "portName": null, "ifIndex": 2, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 10240, "ifType": "ethernetCsmacd", "ifAlias": "p4_data_e2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "p6_air", "ifName": "p6_air", "portName": null, "ifIndex": 3, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 10240, "ifType": "ethernetCsmacd", "ifAlias": "p6_air", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 6060624, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 5306547, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 1, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 1235860, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 1010868, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 28178, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 27453, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 1207669, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 983401, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.33555.1.1.5.1.0", "processor_index": "0", "processor_type": "ray", "processor_usage": 51, "processor_descr": "Processor", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "0", "entPhysicalIndex": null, "mempool_type": "ray", "mempool_class": "system", "mempool_precision": 1, "mempool_descr": "Memory", "mempool_perc": 37, "mempool_perc_oid": ".*******.4.1.33555.1.1.5.2.0", "mempool_used": 37, "mempool_used_oid": null, "mempool_free": 63, "mempool_free_oid": null, "mempool_total": 100, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "ber", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******1.0", "sensor_index": "0", "sensor_type": "ray", "sensor_descr": "BER", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": 0, "sensor_limit_low": null, "sensor_limit_low_warn": 0, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "rfPowerStatus", "sensor_descr": "RF Power Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "rfPowerStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "rxModulationIndex-ray", "sensor_descr": "Rx Modulation Rate", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "rxModulationIndex-ray"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "systemStatus", "sensor_descr": "System Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "txModulationIndex-ray", "sensor_descr": "Tx Modulation Rate", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "txModulationIndex-ray"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "ray", "sensor_descr": "Temperature", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 55, "sensor_limit_warn": null, "sensor_limit_low": 25, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.33555.*******.0", "sensor_index": "0", "sensor_type": "ray", "sensor_descr": "Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 24.5, "sensor_limit": 28.175, "sensor_limit_warn": null, "sensor_limit_low": 20.825, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "rfPowerStatus", "state_descr": "Unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "rfPowerStatus", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "rfPowerStatus", "state_descr": "Failure", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "rxModulationIndex-ray", "state_descr": "QPSK", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray", "state_descr": "QAM16", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray", "state_descr": "QAM32", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray", "state_descr": "QAM64", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray", "state_descr": "QAM128", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "rxModulationIndex-ray", "state_descr": "QAM256", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 0}, {"state_name": "systemStatus", "state_descr": "Unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "systemStatus", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "systemStatus", "state_descr": "Warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "systemStatus", "state_descr": "Alarm", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 3}, {"state_name": "txModulationIndex-ray", "state_descr": "QPSK", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray", "state_descr": "QAM16", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray", "state_descr": "QAM32", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray", "state_descr": "QAM64", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray", "state_descr": "QAM128", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "txModulationIndex-ray", "state_descr": "QAM256", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 0}]}, "poller": "matches discovery"}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom-rx", "sensor_descr": "RX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 17129.5, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom-tx", "sensor_descr": "TX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 17213.5, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom-pow-conf", "sensor_descr": "Tx Power Configured", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 5, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom-pow-cur", "sensor_descr": "Tx Power Current", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -2, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "2", "sensor_type": "racom-maxNetBitrate", "sensor_descr": "Max Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_aggregator": "sum", "sensor_current": 359000, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "1", "sensor_type": "racom-netBitrate", "sensor_descr": "Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_aggregator": "sum", "sensor_current": 337670000, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "1", "sensor_type": "racom", "sensor_descr": "RSSI", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -53.8, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "snr", "sensor_index": "1", "sensor_type": "racom", "sensor_descr": "CINR", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 29.1, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom-rx", "sensor_descr": "RX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 17129.5, "sensor_prev": 17129.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "1", "sensor_type": "racom-tx", "sensor_descr": "TX Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 17213.5, "sensor_prev": 17213.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom-pow-conf", "sensor_descr": "Tx Power Configured", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 5, "sensor_prev": 5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1", "sensor_type": "racom-pow-cur", "sensor_descr": "Tx Power Current", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -2, "sensor_prev": -2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "2", "sensor_type": "racom-maxNetBitrate", "sensor_descr": "Max Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_aggregator": "sum", "sensor_current": 359000, "sensor_prev": 359000, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rate", "sensor_index": "1", "sensor_type": "racom-netBitrate", "sensor_descr": "Net Bitrate", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_aggregator": "sum", "sensor_current": 337670000, "sensor_prev": 337670000, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.********.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "1", "sensor_type": "racom", "sensor_descr": "RSSI", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -53.8, "sensor_prev": -53.8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "snr", "sensor_index": "1", "sensor_type": "racom", "sensor_descr": "CINR", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 29.1, "sensor_prev": 29.1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.33555.*******.0\"]", "rrd_type": "GAUGE"}]}}}