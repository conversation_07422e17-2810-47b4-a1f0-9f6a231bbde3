{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.6527.6.1.1.2.1", "sysDescr": "TiMOS-B-9.0.R11 both/hops Nokia 7705 SAR Copyright (c) 2000-2020 Nokia.\nAll rights reserved. All use subject to applicable license agreements.\r\nBuilt on Tue Jul 14 11:11:26 EDT 2020 by builder in /builds/F90B/R11/panos/main", "sysContact": "<private>", "version": "9.0.R11", "hardware": "7705 SAR-8 v2", "features": null, "location": "<private>", "os": "timos", "type": "network", "serial": "LL122819029", "icon": "nokia.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "system, Loopback IP interface", "ifName": "system", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "Loopback IP interface", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "PtP-OC-Loopback, Loopback IP interface, \\IP interface", "ifName": "PtP-OC-Loopback", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "IP interface", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-SITEA-7750-1-1-8, IP interface", "ifName": "to-SITEA-7750-1-1-8", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ipForward", "ifAlias": "IP interface", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/1, 10/100/Gig Ethernet SFP", "ifName": "1/3/1", "portName": null, "ifIndex": 39878656, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/2, 10/100/Gig Ethernet SFP, \\Mob: DPAC-GU-LTE [Tam-Noc-GLTAM010-LTE-66010]", "ifName": "1/3/2", "portName": null, "ifIndex": 39911424, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Mob: DPAC-GU-LTE [Tam-Noc-GLTAM010-LTE-66010]", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/3, 10/100/Gig Ethernet SFP", "ifName": "1/3/3", "portName": null, "ifIndex": 39944192, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/4, 10/100/Gig Ethernet SFP, \\Temoprary-to-SITEB-7750 1/1/12", "ifName": "1/3/4", "portName": null, "ifIndex": 39976960, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Temoprary-to-SITEB-7750 1/1/12", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/5, 10/100/Gig Ethernet SFP, \\Mob: DPAC-GU-LTE [Tam-NOC-TOYCELL-GLTAM869-LTE-66869]", "ifName": "1/3/5", "portName": null, "ifIndex": 40009728, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Mob: DPAC-GU-LTE [Tam-NOC-TOYCELL-GLTAM869-LTE-66869]", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/6, 10/100/Gig Ethernet SFP, \\Mob: DPAC-GU-LTE [PakiPlaza-GLAPAKIPLZA-MRBTS-6123] (TEST)", "ifName": "1/3/6", "portName": null, "ifIndex": 40042496, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Mob: DPAC-GU-LTE [PakiPlaza-GLAPAKIPLZA-MRBTS-6123] (TEST)", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/7, 10/100/Gig Ethernet SFP, \\to-TEST-CISCO-2911", "ifName": "1/3/7", "portName": null, "ifIndex": 40075264, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "to-TEST-CISCO-2911", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/8, 10/100/Gig Ethernet SFP, \\SAR: to-SITEA-7750 1/1/8", "ifName": "1/3/8", "portName": null, "ifIndex": 40108032, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "SAR: to-SITEA-7750 1/1/8", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/1, 10/100/Gig Ethernet SFP", "ifName": "1/4/1", "portName": null, "ifIndex": 41975808, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/2, 10/100/Gig Ethernet SFP", "ifName": "1/4/2", "portName": null, "ifIndex": 42008576, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/3, 10/100/Gig Ethernet SFP", "ifName": "1/4/3", "portName": null, "ifIndex": 42041344, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/4, 10/100/Gig Ethernet SFP", "ifName": "1/4/4", "portName": null, "ifIndex": 42074112, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/5, 10/100/Gig Ethernet SFP", "ifName": "1/4/5", "portName": null, "ifIndex": 42106880, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/6, 10/100/Gig Ethernet SFP", "ifName": "1/4/6", "portName": null, "ifIndex": 42139648, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/7, 10/100/Gig Ethernet SFP", "ifName": "1/4/7", "portName": null, "ifIndex": 42172416, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/8, 10/100/Gig Ethernet SFP", "ifName": "1/4/8", "portName": null, "ifIndex": 42205184, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "A/1, 10/100 Ethernet TX", "ifName": "A/1", "portName": null, "ifIndex": 67141632, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100 Ethernet TX", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "system, Loopback IP interface", "ifName": "system", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "softwareLoopback", "ifAlias": "Loopback IP interface", "ifPhysAddress": "48f7f1d030f7", "ifLastChange": 267, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "PtP-OC-Loopback, Loopback IP interface, \\IP interface", "ifName": "PtP-OC-Loopback", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "softwareLoopback", "ifAlias": "IP interface", "ifPhysAddress": "48f7f1d030f7", "ifLastChange": 266, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-GU-AWS-GLATOYCELL-6500-Main", "ifName": "to-GU-AWS-GLATOYCELL-6500-Main", "portName": null, "ifIndex": 3, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "serviceVprn", "ifAlias": "to-GU-AWS-GLATOYCELL-6500-Main", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 992166, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 66961448, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 1065932416, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 47859286881, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-GU-AWS-GLATOYCELL-6500-OAM", "ifName": "to-GU-AWS-GLATOYCELL-6500-OAM", "portName": null, "ifIndex": 4, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "serviceVprn", "ifAlias": "to-GU-AWS-GLATOYCELL-6500-OAM", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 33320577, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 2636993, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 6967354499, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 1058658731, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-SITEA-7750-1-1-8, IP interface", "ifName": "to-SITEA-7750-1-1-8", "portName": null, "ifIndex": 5, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9198, "ifType": "ipForward", "ifAlias": "IP interface", "ifPhysAddress": "50406186d680", "ifLastChange": 8615, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1102264714, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 605403133, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 104223032343, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 61189060220, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 4, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 2004715, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-PakiPlaza-LTE-AWS-OAM", "ifName": "to-PakiPlaza-LTE-AWS-OAM", "portName": null, "ifIndex": 6, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "serviceVprn", "ifAlias": "to-PakiPlaza-LTE-AWS-OAM", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-GLTAM010-LTE-66010-Main", "ifName": "to-GLTAM010-LTE-66010-Main", "portName": null, "ifIndex": 7, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "serviceVprn", "ifAlias": "to-GLTAM010-LTE-66010-Main", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-GLTAM010-LTE-66010-OAM", "ifName": "to-GLTAM010-LTE-66010-OAM", "portName": null, "ifIndex": 8, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "serviceVprn", "ifAlias": "to-GLTAM010-LTE-66010-OAM", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-GLTAM869-TOY-LTE-66869-Main", "ifName": "to-GLTAM869-TOY-LTE-66869-Main", "portName": null, "ifIndex": 9, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "serviceVprn", "ifAlias": "to-GLTAM869-TOY-LTE-66869-Main", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to-GLTAM869-TOY-LTE-66869-OAM", "ifName": "to-GLTAM869-TOY-LTE-66869-OAM", "portName": null, "ifIndex": 10, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "serviceVprn", "ifAlias": "to-GLTAM869-TOY-LTE-66869-OAM", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "management", "ifName": "management", "portName": null, "ifIndex": 1280, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "network", "ifAlias": "management", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/1, 10/100/Gig Ethernet SFP", "ifName": "1/3/1", "portName": null, "ifIndex": 39878656, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "50406186d679", "ifLastChange": 485618342, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 6, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 19, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": "mob", "port_descr_descr": "DPAC-GU-LTE", "port_descr_circuit": null, "port_descr_speed": "Tam-Noc-GLTAM010-LTE-66010", "port_descr_notes": null, "ifDescr": "1/3/2, 10/100/Gig Ethernet SFP, \\Mob: DPAC-GU-LTE [Tam-Noc-GLTAM010-LTE-66010]", "ifName": "1/3/2", "portName": null, "ifIndex": 39911424, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "Mob: DPAC-GU-LTE [Tam-Noc-GLTAM010-LTE-66010]", "ifPhysAddress": "50406186d67a", "ifLastChange": 521796811, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 34319955, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 69605652, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 8033754051, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 48920179374, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 31, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 87, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 679, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/3, 10/100/Gig Ethernet SFP", "ifName": "1/3/3", "portName": null, "ifIndex": 39944192, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "50406186d67b", "ifLastChange": 485611565, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 92890852, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 82342360, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 328, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 63974775996, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 68869631417, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 14, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 15, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 25, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/4, 10/100/Gig Ethernet SFP, \\Temoprary-to-SITEB-7750 1/1/12", "ifName": "1/3/4", "portName": null, "ifIndex": 39976960, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "Temoprary-to-SITEB-7750 1/1/12", "ifPhysAddress": "50406186d67c", "ifLastChange": 7819, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": "mob", "port_descr_descr": "DPAC-GU-LTE", "port_descr_circuit": null, "port_descr_speed": "Tam-NOC-TOYCELL-GLTAM869-LTE-668", "port_descr_notes": null, "ifDescr": "1/3/5, 10/100/Gig Ethernet SFP, \\Mob: DPAC-GU-LTE [Tam-NOC-TOYCELL-GLTAM869-LTE-66869]", "ifName": "1/3/5", "portName": null, "ifIndex": 40009728, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "Mob: DPAC-GU-LTE [Tam-NOC-TOYCELL-GLTAM869-LTE-66869]", "ifPhysAddress": "50406186d67d", "ifLastChange": 99788625, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 1258000, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 18500, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": "mob", "port_descr_descr": "DPAC-GU-LTE", "port_descr_circuit": null, "port_descr_speed": "PakiPlaza-GLAPAKIPLZA-MRBTS-6123", "port_descr_notes": "TEST", "ifDescr": "1/3/6, 10/100/Gig Ethernet SFP, \\Mob: DPAC-GU-LTE [PakiPlaza-GLAPAKIPLZA-MRBTS-6123] (TEST)", "ifName": "1/3/6", "portName": null, "ifIndex": 40042496, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 2106, "ifType": "ethernetCsmacd", "ifAlias": "Mob: DPAC-GU-LTE [PakiPlaza-GLAPAKIPLZA-MRBTS-6123] (TEST)", "ifPhysAddress": "50406186d67e", "ifLastChange": 257, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/7, 10/100/Gig Ethernet SFP, \\to-TEST-CISCO-2911", "ifName": "1/3/7", "portName": null, "ifIndex": 40075264, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "to-TEST-CISCO-2911", "ifPhysAddress": "50406186d67f", "ifLastChange": 7819, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": "sar", "port_descr_descr": "to-SITEA-7750 1/1/8", "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/3/8, 10/100/Gig Ethernet SFP, \\SAR: to-SITEA-7750 1/1/8", "ifName": "1/3/8", "portName": null, "ifIndex": 40108032, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "SAR: to-SITEA-7750 1/1/8", "ifPhysAddress": "50406186d680", "ifLastChange": 8615, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1174354378, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 634479943, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 154159911454, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 69505947930, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 256259, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 4, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 3, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 7100299, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 5562464, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/1, 10/100/Gig Ethernet SFP", "ifName": "1/4/1", "portName": null, "ifIndex": 41975808, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae81", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/2, 10/100/Gig Ethernet SFP", "ifName": "1/4/2", "portName": null, "ifIndex": 42008576, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae82", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/3, 10/100/Gig Ethernet SFP", "ifName": "1/4/3", "portName": null, "ifIndex": 42041344, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae83", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/4, 10/100/Gig Ethernet SFP", "ifName": "1/4/4", "portName": null, "ifIndex": 42074112, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae84", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/5, 10/100/Gig Ethernet SFP", "ifName": "1/4/5", "portName": null, "ifIndex": 42106880, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae85", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/6, 10/100/Gig Ethernet SFP", "ifName": "1/4/6", "portName": null, "ifIndex": 42139648, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae86", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/7, 10/100/Gig Ethernet SFP", "ifName": "1/4/7", "portName": null, "ifIndex": 42172416, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae87", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/4/8, 10/100/Gig Ethernet SFP", "ifName": "1/4/8", "portName": null, "ifIndex": 42205184, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "10/100/Gig Ethernet SFP", "ifPhysAddress": "0c54b9abae88", "ifLastChange": 258, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "A/1, 10/100 Ethernet TX", "ifName": "A/1", "portName": null, "ifIndex": 67141632, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100 Ethernet TX", "ifPhysAddress": "cc66b2b409cb", "ifLastChange": 103, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.6527.*******.1.1.0", "processor_index": "0", "processor_type": "timos", "processor_usage": 29, "processor_descr": "Processor", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "0", "entPhysicalIndex": null, "mempool_type": "timos", "mempool_class": "system", "mempool_precision": 1000, "mempool_descr": "Memory", "mempool_perc": 45, "mempool_perc_oid": null, "mempool_used": 514530000, "mempool_used_oid": ".*******.4.1.6527.*******.1.11.0", "mempool_free": 641535000, "mempool_free_oid": ".*******.4.1.6527.*******.1.10.0", "mempool_total": 1156065000, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "vrf": {"discovery": {"vrfs": [{"vrf_oid": "1", "vrf_name": "Base", "bgpLocalAs": 65000, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": "1"}, {"vrf_oid": "2", "vrf_name": "vprn14", "bgpLocalAs": 65002, "mplsVpnVrfRouteDistinguisher": "65002:1", "mplsVpnVrfDescription": "", "ifIndices": "5"}, {"vrf_oid": "5", "vrf_name": "vprn2002", "bgpLocalAs": 65010, "mplsVpnVrfRouteDistinguisher": "65010:2002", "mplsVpnVrfDescription": "", "ifIndices": "2"}, {"vrf_oid": "4093", "vrf_name": "mw-management", "bgpLocalAs": 0, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": null}, {"vrf_oid": "4094", "vrf_name": "vpls-management", "bgpLocalAs": 0, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": null}, {"vrf_oid": "4095", "vrf_name": "management", "bgpLocalAs": 0, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.39976960", "sensor_index": "tx-bias-1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.02835, "sensor_limit": 0.13, "sensor_limit_warn": 0.125, "sensor_limit_low": 0.003, "sensor_limit_low_warn": 0.004, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.40108032", "sensor_index": "tx-bias-1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.01784, "sensor_limit": 0.065, "sensor_limit_warn": 0.06, "sensor_limit_low": 0.01, "sensor_limit_low_warn": 0.012, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.39976960", "sensor_index": "rx-1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4 Rx", "group": "1/3/4", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -40, "sensor_limit": 0.99991233544684, "sensor_limit_warn": -2.9998893767789, "sensor_limit_low": -23.01029995664, "sensor_limit_low_warn": -18.996294548824, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.40108032", "sensor_index": "rx-1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8 Rx", "group": "1/3/8", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -8.5387196432176, "sensor_limit": 0.99991233544684, "sensor_limit_warn": 0, "sensor_limit_low": -26.98970004336, "sensor_limit_low_warn": -26.02059991328, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.39976960", "sensor_index": "tx-1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4 Tx", "group": "1/3/4", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -4.4708854978349, "sensor_limit": 0.99991233544684, "sensor_limit_warn": -1.0001543745061, "sensor_limit_low": -13.496924768681, "sensor_limit_low_warn": -9.5000714307986, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.40108032", "sensor_index": "tx-1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8 Tx", "group": "1/3/8", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -7.1896663275227, "sensor_limit": 0.99991233544684, "sensor_limit_warn": 0, "sensor_limit_low": -13.001622741328, "sensor_limit_low_warn": -11.999706407559, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisCriticalLEDState.1", "sensor_type": "tmnxChassisCriticalLEDState", "sensor_descr": "Critical LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisCriticalLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMajorLEDState.1", "sensor_type": "tmnxChassisMajorLEDState", "sensor_descr": "Major LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMajorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMinorLEDState.1", "sensor_type": "tmnxChassisMinorLEDState", "sensor_descr": "Minor LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMinorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisOverTempState.1", "sensor_type": "tmnxChassisOverTempState", "sensor_descr": "<PERSON><PERSON><PERSON>mp", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisOverTempState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.100663297", "sensor_index": "tmnxHwID.1.100663297", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Fans", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 1 NS1806S0611", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150994977", "sensor_index": "tmnxHwID.1.150994977", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A NS1806S0611", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150994993", "sensor_index": "tmnxHwID.1.150994993", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772162", "sensor_index": "tmnxHwID.1.167772162", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772163", "sensor_index": "tmnxHwID.1.167772163", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16842753", "sensor_index": "tmnxHwID.1.16842753", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Fan Alarm Module NS1637S2845", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908289", "sensor_index": "tmnxHwID.1.16908289", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908290", "sensor_index": "tmnxHwID.1.16908290", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908291", "sensor_index": "tmnxHwID.1.16908291", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908292", "sensor_index": "tmnxHwID.1.16908292", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549633", "sensor_index": "tmnxHwID.1.184549633", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549634", "sensor_index": "tmnxHwID.1.184549634", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549635", "sensor_index": "tmnxHwID.1.184549635", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/3 NS1838S2366", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549636", "sensor_index": "tmnxHwID.1.184549636", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549637", "sensor_index": "tmnxHwID.1.184549637", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549638", "sensor_index": "tmnxHwID.1.184549638", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201327105", "sensor_index": "tmnxHwID.1.201327105", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201327106", "sensor_index": "tmnxHwID.1.201327106", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf2:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201327107", "sensor_index": "tmnxHwID.1.201327107", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf3:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.50331649", "sensor_index": "tmnxHwID.1.50331649", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Chassis 1 LL122819029", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886081", "sensor_index": "tmnxHwID.1.83886081", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Feed 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886082", "sensor_index": "tmnxHwID.1.83886082", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Feed 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.39976960", "sensor_index": "1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 25.2265625, "sensor_limit": 90, "sensor_limit_warn": 85, "sensor_limit_low": -45, "sensor_limit_low_warn": -40, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40009728", "sensor_index": "1.40009728", "sensor_type": "timos", "sensor_descr": "1/3/5", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 39.8125, "sensor_limit": 85, "sensor_limit_warn": 75, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40009728", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40042496", "sensor_index": "1.40042496", "sensor_type": "timos", "sensor_descr": "1/3/6", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 25.6875, "sensor_limit": 100, "sensor_limit_warn": 95, "sensor_limit_low": -40, "sensor_limit_low_warn": -35, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40042496", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40108032", "sensor_index": "1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 39.125, "sensor_limit": 100, "sensor_limit_warn": 95, "sensor_limit_low": -50, "sensor_limit_low_warn": -45, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "timos", "sensor_descr": "Slot 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150994977", "sensor_index": "tmnxHwID.1.150994977", "sensor_type": "timos", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549635", "sensor_index": "tmnxHwID.1.184549635", "sensor_type": "timos", "sensor_descr": "MDA 1/3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 85, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.39976960", "sensor_index": "1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3384, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 3, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40009728", "sensor_index": "1.40009728", "sensor_type": "timos", "sensor_descr": "1/3/5", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3016, "sensor_limit": 3.6, "sensor_limit_warn": 3.5, "sensor_limit_low": 2.8, "sensor_limit_low_warn": 2.9, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40009728", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40042496", "sensor_index": "1.40042496", "sensor_type": "timos", "sensor_descr": "1/3/6", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.2856, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40042496", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40108032", "sensor_index": "1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3128, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "tmnxChassisCriticalLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisOverTempState", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisOverTempState", "state_descr": "OverTemp", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmActive", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmCleared", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.39976960", "sensor_index": "tx-bias-1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.02835, "sensor_limit": 0.13, "sensor_limit_warn": 0.125, "sensor_limit_low": 0.003, "sensor_limit_low_warn": 0.004, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.40108032", "sensor_index": "tx-bias-1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.01784, "sensor_limit": 0.065, "sensor_limit_warn": 0.06, "sensor_limit_low": 0.01, "sensor_limit_low_warn": 0.012, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.39976960", "sensor_index": "rx-1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4 Rx", "group": "1/3/4", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -40, "sensor_limit": 0.99991233544684, "sensor_limit_warn": -2.9998893767789, "sensor_limit_low": -23.01029995664, "sensor_limit_low_warn": -18.996294548824, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.40108032", "sensor_index": "rx-1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8 Rx", "group": "1/3/8", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -8.5387196432176, "sensor_limit": 0.99991233544684, "sensor_limit_warn": 0, "sensor_limit_low": -26.98970004336, "sensor_limit_low_warn": -26.02059991328, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": -8.5387196432176, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.39976960", "sensor_index": "tx-1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4 Tx", "group": "1/3/4", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -4.4708854978349, "sensor_limit": 0.99991233544684, "sensor_limit_warn": -1.0001543745061, "sensor_limit_low": -13.496924768681, "sensor_limit_low_warn": -9.5000714307986, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": -4.4708854978349, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.40108032", "sensor_index": "tx-1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8 Tx", "group": "1/3/8", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -7.1896663275227, "sensor_limit": 0.99991233544684, "sensor_limit_warn": 0, "sensor_limit_low": -13.001622741328, "sensor_limit_low_warn": -11.999706407559, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": -7.1896663275227, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisCriticalLEDState.1", "sensor_type": "tmnxChassisCriticalLEDState", "sensor_descr": "Critical LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisCriticalLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMajorLEDState.1", "sensor_type": "tmnxChassisMajorLEDState", "sensor_descr": "Major LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMajorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMinorLEDState.1", "sensor_type": "tmnxChassisMinorLEDState", "sensor_descr": "Minor LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMinorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisOverTempState.1", "sensor_type": "tmnxChassisOverTempState", "sensor_descr": "<PERSON><PERSON><PERSON>mp", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisOverTempState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.100663297", "sensor_index": "tmnxHwID.1.100663297", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Fans", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 1 NS1806S0611", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150994977", "sensor_index": "tmnxHwID.1.150994977", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A NS1806S0611", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150994993", "sensor_index": "tmnxHwID.1.150994993", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772162", "sensor_index": "tmnxHwID.1.167772162", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772163", "sensor_index": "tmnxHwID.1.167772163", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16842753", "sensor_index": "tmnxHwID.1.16842753", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Fan Alarm Module NS1637S2845", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908289", "sensor_index": "tmnxHwID.1.16908289", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908290", "sensor_index": "tmnxHwID.1.16908290", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908291", "sensor_index": "tmnxHwID.1.16908291", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.16908292", "sensor_index": "tmnxHwID.1.16908292", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "External Alarm Input 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549633", "sensor_index": "tmnxHwID.1.184549633", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549634", "sensor_index": "tmnxHwID.1.184549634", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549635", "sensor_index": "tmnxHwID.1.184549635", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/3 NS1838S2366", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549636", "sensor_index": "tmnxHwID.1.184549636", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549637", "sensor_index": "tmnxHwID.1.184549637", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549638", "sensor_index": "tmnxHwID.1.184549638", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201327105", "sensor_index": "tmnxHwID.1.201327105", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201327106", "sensor_index": "tmnxHwID.1.201327106", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf2:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201327107", "sensor_index": "tmnxHwID.1.201327107", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf3:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.50331649", "sensor_index": "tmnxHwID.1.50331649", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Chassis 1 LL122819029", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886081", "sensor_index": "tmnxHwID.1.83886081", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Feed 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886082", "sensor_index": "tmnxHwID.1.83886082", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Feed 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.39976960", "sensor_index": "1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 25.2265625, "sensor_limit": 90, "sensor_limit_warn": 85, "sensor_limit_low": -45, "sensor_limit_low_warn": -40, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40009728", "sensor_index": "1.40009728", "sensor_type": "timos", "sensor_descr": "1/3/5", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 39.8125, "sensor_limit": 85, "sensor_limit_warn": 75, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40009728", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40042496", "sensor_index": "1.40042496", "sensor_type": "timos", "sensor_descr": "1/3/6", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 25.6875, "sensor_limit": 100, "sensor_limit_warn": 95, "sensor_limit_low": -40, "sensor_limit_low_warn": -35, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40042496", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40108032", "sensor_index": "1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 39.125, "sensor_limit": 100, "sensor_limit_warn": 95, "sensor_limit_low": -50, "sensor_limit_low_warn": -45, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "timos", "sensor_descr": "Slot 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150994977", "sensor_index": "tmnxHwID.1.150994977", "sensor_type": "timos", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549635", "sensor_index": "tmnxHwID.1.184549635", "sensor_type": "timos", "sensor_descr": "MDA 1/3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 85, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.39976960", "sensor_index": "1.39976960", "sensor_type": "timos", "sensor_descr": "1/3/4", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3384, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 3, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "39976960", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40009728", "sensor_index": "1.40009728", "sensor_type": "timos", "sensor_descr": "1/3/5", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3016, "sensor_limit": 3.6, "sensor_limit_warn": 3.5, "sensor_limit_low": 2.8, "sensor_limit_low_warn": 2.9, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40009728", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40042496", "sensor_index": "1.40042496", "sensor_type": "timos", "sensor_descr": "1/3/6", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.2856, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40042496", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.40108032", "sensor_index": "1.40108032", "sensor_type": "timos", "sensor_descr": "1/3/8", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3128, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "40108032", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "tmnxChassisCriticalLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisOverTempState", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisOverTempState", "state_descr": "OverTemp", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmActive", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmCleared", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}]}}, "bgp-peers": {"discovery": {"bgpPeers": [{"astext": "AS65000-MOCK-TEXT", "bgpPeerIdentifier": "*********", "bgpPeerRemoteAs": 65000, "bgpPeerState": "idle", "bgpPeerAdminStatus": "stop", "bgpPeerLastErrorCode": null, "bgpPeerLastErrorSubCode": null, "bgpPeerLastErrorText": null, "bgpPeerIface": null, "bgpLocalAddr": "0.0.0.0", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 0, "bgpPeerOutUpdates": 0, "bgpPeerInTotalMessages": 0, "bgpPeerOutTotalMessages": 0, "bgpPeerFsmEstablishedTime": 0, "bgpPeerInUpdateElapsedTime": 0, "context_name": null, "bgpLocalAs": 65000, "vrfLocalAs": 65000}, {"astext": "AS65000-MOCK-TEXT", "bgpPeerIdentifier": "*********", "bgpPeerRemoteAs": 65000, "bgpPeerState": "idle", "bgpPeerAdminStatus": "stop", "bgpPeerLastErrorCode": null, "bgpPeerLastErrorSubCode": null, "bgpPeerLastErrorText": null, "bgpPeerIface": null, "bgpLocalAddr": "0.0.0.0", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 0, "bgpPeerOutUpdates": 0, "bgpPeerInTotalMessages": 0, "bgpPeerOutTotalMessages": 0, "bgpPeerFsmEstablishedTime": 0, "bgpPeerInUpdateElapsedTime": 0, "context_name": null, "bgpLocalAs": 65000, "vrfLocalAs": 65000}]}, "poller": {"bgpPeers": [{"astext": "AS65000-MOCK-TEXT", "bgpPeerIdentifier": "*********", "bgpPeerRemoteAs": 65000, "bgpPeerState": "established", "bgpPeerAdminStatus": "receiveKeepAlive", "bgpPeerLastErrorCode": null, "bgpPeerLastErrorSubCode": null, "bgpPeerLastErrorText": null, "bgpPeerIface": null, "bgpLocalAddr": "0.0.0.0", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 0, "bgpPeerOutUpdates": 0, "bgpPeerInTotalMessages": 78954664, "bgpPeerOutTotalMessages": 5243874, "bgpPeerFsmEstablishedTime": 3, "bgpPeerInUpdateElapsedTime": 0, "context_name": null, "bgpLocalAs": 65000, "vrfLocalAs": 65000}, {"astext": "AS65000-MOCK-TEXT", "bgpPeerIdentifier": "*********", "bgpPeerRemoteAs": 65000, "bgpPeerState": "established", "bgpPeerAdminStatus": "receiveKeepAlive", "bgpPeerLastErrorCode": null, "bgpPeerLastErrorSubCode": null, "bgpPeerLastErrorText": null, "bgpPeerIface": null, "bgpLocalAddr": "0.0.0.0", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 0, "bgpPeerOutUpdates": 0, "bgpPeerInTotalMessages": 4828014, "bgpPeerOutTotalMessages": 5243447, "bgpPeerFsmEstablishedTime": 3, "bgpPeerInUpdateElapsedTime": 0, "context_name": null, "bgpLocalAs": 65000, "vrfLocalAs": 65000}]}}, "mpls": {"discovery": {"mpls_lsps": [{"vrf_oid": 1, "lsp_oid": 3, "mplsLspRowStatus": "active", "mplsLspLastChange": 3, "mplsLspName": "to-tam-7750-1-satellite", "mplsLspAdminState": "inService", "mplsLspOperState": "outOfService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "*********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}], "mpls_lsp_paths": [{"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 3, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "outOfService", "mplsLspPathOperState": "outOfService", "mplsLspPathState": "inactive", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 0, "mplsLspPathOperMetric": null, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 0, "mplsLspPathTunnelCHopListIndex": 0, "vrf_oid": 1, "lsp_oid": 3}, {"path_oid": 3, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 3, "mplsLspPathType": "standby", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "outOfService", "mplsLspPathState": "inactive", "mplsLspPathFailCode": "noCspfRouteToDestination", "mplsLspPathFailNodeAddr": "***********", "mplsLspPathMetric": 0, "mplsLspPathOperMetric": null, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 0, "mplsLspPathTunnelCHopListIndex": 0, "vrf_oid": 1, "lsp_oid": 3}], "mpls_sdps": [{"sdp_oid": 1, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "to-tam-7750-1", "sdpAdminStatus": "up", "sdpOperStatus": "down", "sdpAdminPathMtu": 1600, "sdpOperPathMtu": 1600, "sdpLastMgmtChange": 3, "sdpLastStatusChange": 7406217, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "*********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 2, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "to-SITEA-7750 MTU-9194", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 9194, "sdpOperPathMtu": 9194, "sdpLastMgmtChange": 3, "sdpLastStatusChange": 99, "sdpActiveLspType": "ldp", "sdpFarEndInetAddress": "*********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "to-tam-7750-1 MTU-9194", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 9194, "sdpOperPathMtu": 9194, "sdpLastMgmtChange": 3, "sdpLastStatusChange": 100, "sdpActiveLspType": "ldp", "sdpFarEndInetAddress": "*********", "sdpFarEndInetAddressType": "ipv4"}], "mpls_sdp_binds": [{"sdp_oid": 1, "svc_oid": 14, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "down", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 7406217, "sdpBindType": "spoke", "sdpBindVcType": "undef", "sdpBindBaseStatsIngFwdPackets": 0, "sdpBindBaseStatsIngFwdOctets": 0, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}, {"sdp_oid": 1, "svc_oid": 1010, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "down", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 7406217, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 6993654, "sdpBindBaseStatsIngFwdOctets": 596627767, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}, {"sdp_oid": 1, "svc_oid": 2002, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "down", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 7406217, "sdpBindType": "spoke", "sdpBindVcType": "undef", "sdpBindBaseStatsIngFwdPackets": 0, "sdpBindBaseStatsIngFwdOctets": 0, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}, {"sdp_oid": 2, "svc_oid": 2002, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 99, "sdpBindType": "spoke", "sdpBindVcType": "undef", "sdpBindBaseStatsIngFwdPackets": 0, "sdpBindBaseStatsIngFwdOctets": 0, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}], "mpls_services": [{"svc_oid": 14, "svcRowStatus": "active", "svcType": "vprn", "svcCustId": 500, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "VPRN-Guam-3G-LTE-Cell", "svcMtu": 0, "svcNumSaps": 8, "svcNumSdps": 1, "svcLastMgmtChange": 3, "svcLastStatusChange": 3, "svcVRouterId": 2, "svcTlsMacLearning": null, "svcTlsStpAdminStatus": null, "svcTlsStpOperStatus": null, "svcTlsFdbTableSize": null, "svcTlsFdbNumEntries": null}, {"svc_oid": 1010, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 100, "svcAdminStatus": "up", "svcOperStatus": "down", "svcDescription": "Remote Site Management ************/24", "svcMtu": 1514, "svcNumSaps": 0, "svcNumSdps": 1, "svcLastMgmtChange": 3, "svcLastStatusChange": 7406217, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 0}, {"svc_oid": 2002, "svcRowStatus": "active", "svcType": "vprn", "svcCustId": 15, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "Business Lines Internet L3VPN", "svcMtu": 0, "svcNumSaps": 1, "svcNumSdps": 2, "svcLastMgmtChange": 3, "svcLastStatusChange": 3, "svcVRouterId": 5, "svcTlsMacLearning": null, "svcTlsStpAdminStatus": null, "svcTlsStpOperStatus": null, "svcTlsFdbTableSize": null, "svcTlsFdbNumEntries": null}], "mpls_saps": [{"svc_oid": 14, "sapPortId": 39911424, "ifName": null, "sapEncapValue": "1252", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 39911424, "ifName": null, "sapEncapValue": "1253", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 39911424, "ifName": null, "sapEncapValue": "1374", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 39911424, "ifName": null, "sapEncapValue": "1375", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 40009728, "ifName": null, "sapEncapValue": "1384", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 373486, "sapLastStatusChange": 997886}, {"svc_oid": 14, "sapPortId": 40009728, "ifName": null, "sapEncapValue": "1385", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 373497, "sapLastStatusChange": 997886}, {"svc_oid": 14, "sapPortId": 40042496, "ifName": null, "sapEncapValue": "1268", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 3, "sapLastStatusChange": 0}, {"svc_oid": 14, "sapPortId": 40042496, "ifName": null, "sapEncapValue": "1269", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 3, "sapLastStatusChange": 0}, {"svc_oid": 2002, "sapPortId": 40075264, "ifName": null, "sapEncapValue": "0", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 3, "sapLastStatusChange": 0}]}, "poller": {"mpls_lsps": [{"vrf_oid": 1, "lsp_oid": 3, "mplsLspRowStatus": "active", "mplsLspLastChange": 3, "mplsLspName": "to-tam-7750-1-satellite", "mplsLspAdminState": "inService", "mplsLspOperState": "outOfService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "*********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 761832915, "mplsLspTimeUp": 0, "mplsLspTimeDown": 21211477, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": 280, "mplsLspLastTransition": 212115, "mplsLspConfiguredPaths": 2, "mplsLspStandbyPaths": 1, "mplsLspOperationalPaths": 0}], "mpls_lsp_paths": [{"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 3, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "outOfService", "mplsLspPathOperState": "outOfService", "mplsLspPathState": "inactive", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 0, "mplsLspPathOperMetric": null, "mplsLspPathTimeUp": 0, "mplsLspPathTimeDown": 761833003, "mplsLspPathTransitionCount": 0, "mplsLspPathTunnelARHopListIndex": 0, "mplsLspPathTunnelCHopListIndex": 0, "vrf_oid": 1, "lsp_oid": 3}, {"path_oid": 3, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 3, "mplsLspPathType": "standby", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "outOfService", "mplsLspPathState": "inactive", "mplsLspPathFailCode": "noCspfRouteToDestination", "mplsLspPathFailNodeAddr": "***********", "mplsLspPathMetric": 0, "mplsLspPathOperMetric": null, "mplsLspPathTimeUp": 0, "mplsLspPathTimeDown": 21211565, "mplsLspPathTransitionCount": 280, "mplsLspPathTunnelARHopListIndex": 0, "mplsLspPathTunnelCHopListIndex": 0, "vrf_oid": 1, "lsp_oid": 3}], "mpls_sdps": [{"sdp_oid": 1, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "to-tam-7750-1", "sdpAdminStatus": "up", "sdpOperStatus": "down", "sdpAdminPathMtu": 1600, "sdpOperPathMtu": 1600, "sdpLastMgmtChange": 3, "sdpLastStatusChange": 7406217, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "*********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 2, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "to-SITEA-7750 MTU-9194", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 9194, "sdpOperPathMtu": 9194, "sdpLastMgmtChange": 3, "sdpLastStatusChange": 99, "sdpActiveLspType": "ldp", "sdpFarEndInetAddress": "*********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "to-tam-7750-1 MTU-9194", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 9194, "sdpOperPathMtu": 9194, "sdpLastMgmtChange": 3, "sdpLastStatusChange": 100, "sdpActiveLspType": "ldp", "sdpFarEndInetAddress": "*********", "sdpFarEndInetAddressType": "ipv4"}], "mpls_sdp_binds": [{"sdp_oid": 1, "svc_oid": 14, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "down", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 7406217, "sdpBindType": "spoke", "sdpBindVcType": "undef", "sdpBindBaseStatsIngFwdPackets": 0, "sdpBindBaseStatsIngFwdOctets": 0, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}, {"sdp_oid": 1, "svc_oid": 1010, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "down", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 7406217, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 6993654, "sdpBindBaseStatsIngFwdOctets": 596627767, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}, {"sdp_oid": 1, "svc_oid": 2002, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "down", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 7406217, "sdpBindType": "spoke", "sdpBindVcType": "undef", "sdpBindBaseStatsIngFwdPackets": 0, "sdpBindBaseStatsIngFwdOctets": 0, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}, {"sdp_oid": 2, "svc_oid": 2002, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 3, "sdpBindLastStatusChange": 99, "sdpBindType": "spoke", "sdpBindVcType": "undef", "sdpBindBaseStatsIngFwdPackets": 0, "sdpBindBaseStatsIngFwdOctets": 0, "sdpBindBaseStatsEgrFwdPackets": 0, "sdpBindBaseStatsEgrFwdOctets": 0}], "mpls_services": [{"svc_oid": 14, "svcRowStatus": "active", "svcType": "vprn", "svcCustId": 500, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "VPRN-Guam-3G-LTE-Cell", "svcMtu": 0, "svcNumSaps": 8, "svcNumSdps": 1, "svcLastMgmtChange": 3, "svcLastStatusChange": 3, "svcVRouterId": 2, "svcTlsMacLearning": null, "svcTlsStpAdminStatus": null, "svcTlsStpOperStatus": null, "svcTlsFdbTableSize": null, "svcTlsFdbNumEntries": null}, {"svc_oid": 1010, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 100, "svcAdminStatus": "up", "svcOperStatus": "down", "svcDescription": "Remote Site Management ************/24", "svcMtu": 1514, "svcNumSaps": 0, "svcNumSdps": 1, "svcLastMgmtChange": 3, "svcLastStatusChange": 7406217, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 0}, {"svc_oid": 2002, "svcRowStatus": "active", "svcType": "vprn", "svcCustId": 15, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "Business Lines Internet L3VPN", "svcMtu": 0, "svcNumSaps": 1, "svcNumSdps": 2, "svcLastMgmtChange": 3, "svcLastStatusChange": 3, "svcVRouterId": 5, "svcTlsMacLearning": null, "svcTlsStpAdminStatus": null, "svcTlsStpOperStatus": null, "svcTlsFdbTableSize": null, "svcTlsFdbNumEntries": null}], "mpls_saps": [{"svc_oid": 14, "sapPortId": 39911424, "ifName": "1/3/2", "sapEncapValue": "1252", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 39911424, "ifName": "1/3/2", "sapEncapValue": "1253", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 39911424, "ifName": "1/3/2", "sapEncapValue": "1374", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 39911424, "ifName": "1/3/2", "sapEncapValue": "1375", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 3, "sapLastStatusChange": 5217968}, {"svc_oid": 14, "sapPortId": 40009728, "ifName": "1/3/5", "sapEncapValue": "1384", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 373486, "sapLastStatusChange": 997886}, {"svc_oid": 14, "sapPortId": 40009728, "ifName": "1/3/5", "sapEncapValue": "1385", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 373497, "sapLastStatusChange": 997886}, {"svc_oid": 14, "sapPortId": 40042496, "ifName": "1/3/6", "sapEncapValue": "1268", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 3, "sapLastStatusChange": 0}, {"svc_oid": 14, "sapPortId": 40042496, "ifName": "1/3/6", "sapEncapValue": "1269", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 3, "sapLastStatusChange": 0}, {"svc_oid": 2002, "sapPortId": 40075264, "ifName": "1/3/7", "sapEncapValue": "0", "sapRowStatus": "active", "sapType": "vprn", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "down", "sapLastMgmtChange": 3, "sapLastStatusChange": 0}]}}, "ospf": {"poller": {"ospf_ports": [{"ospf_port_id": "***********.0", "ospfIfIpAddress": "***********", "ospfAddressLessIf": 0, "ospfIfAreaId": "0.0.0.0", "ospfIfType": "broadcast", "ospfIfAdminStat": "enabled", "ospfIfRtrPriority": 1, "ospfIfTransitDelay": 1, "ospfIfRetransInterval": 5, "ospfIfHelloInterval": 10, "ospfIfRtrDeadInterval": 40, "ospfIfPollInterval": 120, "ospfIfState": "designated<PERSON>outer", "ospfIfDesignatedRouter": "***********", "ospfIfBackupDesignatedRouter": "0.0.0.0", "ospfIfEvents": 2, "ospfIfAuthKey": "", "ospfIfStatus": "active", "ospfIfMulticastForwarding": "blocked", "ospfIfDemand": "false", "ospfIfAuthType": "none", "ospfIfMetricIpAddress": "***********", "ospfIfMetricAddressLessIf": 0, "ospfIfMetricTOS": 0, "ospfIfMetricValue": 0, "ospfIfMetricStatus": "active", "context_name": "", "ifIndex": 1}, {"ospf_port_id": "***********.0", "ospfIfIpAddress": "***********", "ospfAddressLessIf": 0, "ospfIfAreaId": "0.0.0.0", "ospfIfType": "broadcast", "ospfIfAdminStat": "enabled", "ospfIfRtrPriority": 1, "ospfIfTransitDelay": 1, "ospfIfRetransInterval": 5, "ospfIfHelloInterval": 10, "ospfIfRtrDeadInterval": 40, "ospfIfPollInterval": 120, "ospfIfState": "designated<PERSON>outer", "ospfIfDesignatedRouter": "***********", "ospfIfBackupDesignatedRouter": "0.0.0.0", "ospfIfEvents": 2, "ospfIfAuthKey": "", "ospfIfStatus": "active", "ospfIfMulticastForwarding": "blocked", "ospfIfDemand": "false", "ospfIfAuthType": "none", "ospfIfMetricIpAddress": "***********", "ospfIfMetricAddressLessIf": 0, "ospfIfMetricTOS": 0, "ospfIfMetricValue": 0, "ospfIfMetricStatus": "active", "context_name": "", "ifIndex": 2}, {"ospf_port_id": "***********.0", "ospfIfIpAddress": "***********", "ospfAddressLessIf": 0, "ospfIfAreaId": "0.0.0.0", "ospfIfType": "pointToPoint", "ospfIfAdminStat": "enabled", "ospfIfRtrPriority": 1, "ospfIfTransitDelay": 1, "ospfIfRetransInterval": 5, "ospfIfHelloInterval": 10, "ospfIfRtrDeadInterval": 40, "ospfIfPollInterval": 120, "ospfIfState": "pointToPoint", "ospfIfDesignatedRouter": "0.0.0.0", "ospfIfBackupDesignatedRouter": "0.0.0.0", "ospfIfEvents": 1, "ospfIfAuthKey": "", "ospfIfStatus": "active", "ospfIfMulticastForwarding": "blocked", "ospfIfDemand": "false", "ospfIfAuthType": "none", "ospfIfMetricIpAddress": "***********", "ospfIfMetricAddressLessIf": 0, "ospfIfMetricTOS": 0, "ospfIfMetricValue": 100, "ospfIfMetricStatus": "active", "context_name": "", "ifIndex": 5}], "ospf_instances": [{"ospf_instance_id": 0, "ospfRouterId": "0.0.0.0", "ospfAdminStat": "enabled", "ospfVersionNumber": "version2", "ospfAreaBdrRtrStatus": "false", "ospfASBdrRtrStatus": "true", "ospfExternLsaCount": 41, "ospfExternLsaCksumSum": 1218716, "ospfTOSSupport": "false", "ospfOriginateNewLsas": 4085840, "ospfRxNewLsas": 0, "ospfExtLsdbLimit": -1, "ospfMulticastExtensions": 0, "ospfExitOverflowInterval": 0, "ospfDemandExtensions": "false", "context_name": ""}], "ospf_areas": [{"ospfAreaId": "0.0.0.0", "ospfAuthType": null, "ospfImportAsExtern": "importExternal", "ospfSpfRuns": 2473, "ospfAreaBdrRtrCount": 1, "ospfAsBdrRtrCount": 190, "ospfAreaLsaCount": 868, "ospfAreaLsaCksumSum": 27153464, "ospfAreaSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ospfAreaStatus": "active", "context_name": ""}], "ospf_nbrs": [{"port_id": null, "ospf_nbr_id": "***********.0", "ospfNbrIpAddr": "***********", "ospfNbrAddressLessIndex": 0, "ospfNbrRtrId": "*********", "ospfNbrOptions": 66, "ospfNbrPriority": 1, "ospfNbrState": "full", "ospfNbrEvents": 9, "ospfNbrLsRetransQLen": 0, "ospfNbmaNbrStatus": "active", "ospfNbmaNbrPermanence": "dynamic", "ospfNbrHelloSuppressed": "false", "context_name": ""}]}}, "entity-physical": {"discovery": {"entPhysical": [{"entPhysicalIndex": 1, "entPhysicalDescr": "8-Slot Chassis: 6 Adapter slots, 2 CSM slots", "entPhysicalClass": "chassis", "entPhysicalName": "7705 SAR-8 v2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": null, "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": null, "ifIndex": null}, {"entPhysicalIndex": 16842753, "entPhysicalDescr": null, "entPhysicalClass": "other", "entPhysicalName": "Fan Alarm Mo<PERSON>le", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE06792EAAC0101", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1637S2845", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "3HE06792EAAC0101", "ifIndex": null}, {"entPhysicalIndex": 16908289, "entPhysicalDescr": null, "entPhysicalClass": "other", "entPhysicalName": "External Alarm Input 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 16908290, "entPhysicalDescr": null, "entPhysicalClass": "other", "entPhysicalName": "External Alarm Input 2", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 16908291, "entPhysicalDescr": null, "entPhysicalClass": "other", "entPhysicalName": "External Alarm Input 3", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 16908292, "entPhysicalDescr": null, "entPhysicalClass": "other", "entPhysicalName": "External Alarm Input 4", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 50331649, "entPhysicalDescr": null, "entPhysicalClass": "phys<PERSON><PERSON><PERSON>", "entPhysicalName": "Chassis 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE06791AAAA0109", "entPhysicalVendorType": null, "entPhysicalSerialNum": "LL122819029", "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "3HE06791AAAA0109", "ifIndex": null}, {"entPhysicalIndex": 83886081, "entPhysicalDescr": null, "entPhysicalClass": "powerSupply", "entPhysicalName": "Power Feed 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 83886082, "entPhysicalDescr": null, "entPhysicalClass": "powerSupply", "entPhysicalName": "Power Feed 2", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 100663297, "entPhysicalDescr": null, "entPhysicalClass": "fan", "entPhysicalName": "Fans", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 134217729, "entPhysicalDescr": null, "entPhysicalClass": "ioModule", "entPhysicalName": "Slot 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "X-9.0.R7 on Tue Oct 29 13:45:23 EDT 2019 by builder", "entPhysicalSoftwareRev": "X-9.0.R7 on Tue Oct 29 13:45:23 EDT 2019 by builder", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE02774ABAD0101", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1806S0611", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "3HE02774ABAD0101", "ifIndex": null}, {"entPhysicalIndex": 150994977, "entPhysicalDescr": null, "entPhysicalClass": "cpmModule", "entPhysicalName": "Slot A", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "X-9.0.R7 on Tue Oct 29 13:45:23 EDT 2019 by builder", "entPhysicalSoftwareRev": "X-9.0.R7 on Tue Oct 29 13:45:23 EDT 2019 by builder", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE02774ABAD0101", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1806S0611", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "3HE02774ABAD0101", "ifIndex": null}, {"entPhysicalIndex": 150994993, "entPhysicalDescr": null, "entPhysicalClass": "cpmModule", "entPhysicalName": "Slot B", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 167772162, "entPhysicalDescr": null, "entPhysicalClass": "fabricModule", "entPhysicalName": "Slot A", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 167772163, "entPhysicalDescr": null, "entPhysicalClass": "fabricModule", "entPhysicalName": "Slot B", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 184549633, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 1/1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 134217729, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 184549634, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 1/2", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 134217729, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 184549635, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 1/3", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE06151ACAC0104", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1838S2366", "entPhysicalContainedIn": 134217729, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "3HE06151ACAC0104", "ifIndex": null}, {"entPhysicalIndex": 184549636, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 1/4", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 134217729, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 184549637, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 1/5", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 134217729, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 184549638, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 1/6", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 134217729, "entPhysicalParentRelPos": 6, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201327105, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "cf1:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150994977, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201327106, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "cf2:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150994977, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201327107, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "cf3:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150994977, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "", "ifIndex": null}]}, "poller": "matches discovery"}, "ports-stack": {"discovery": {"ports_stack": [{"high_ifIndex": 40108032, "low_ifIndex": 5, "ifStackStatus": "active"}]}}, "discovery-protocols": {"discovery": {"links": [{"active": 1, "protocol": "lldp", "remote_hostname": "SITEA-7750", "remote_port": "35913728", "remote_platform": null, "remote_version": "TiMOS-C-15.1.R6 cpm/hops64 Nokia 7750 SR Copyright (c) 2000-2018 Nokia.\nAll rights reserved. All use subject to applicable license agreements.\r\nBuilt on Wed Aug 15 13:44:27 PDT 2018 by builder in /builds/151B/R6/panos/main", "ifAlias": "SAR: to-SITEA-7750 1/1/8", "ifDescr": "1/3/8, 10/100/Gig Ethernet SFP, \\SAR: to-SITEA-7750 1/1/8", "ifName": "1/3/8"}]}}}