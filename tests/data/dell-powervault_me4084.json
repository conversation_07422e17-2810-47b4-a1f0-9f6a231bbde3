{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.674", "sysDescr": "DELL EMC ME4084", "sysContact": "<private>", "version": null, "hardware": "DELL EMC ME4084", "features": null, "location": "<private>", "os": "dell-powervault", "type": "storage", "serial": null, "icon": "dell.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "sit0", "ifName": "sit0", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "tunnel", "ifAlias": "sit0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "mcmc", "ifName": "mcmc", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "mcmc", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "bond0", "ifName": "bond0", "portName": null, "ifIndex": 7, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "bond0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "sit0", "ifName": "sit0", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "tunnel", "ifAlias": "sit0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "mcmc", "ifName": "mcmc", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "mcmc", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "bond0", "ifName": "bond0", "portName": null, "ifIndex": 7, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "bond0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.21", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.21", "sensor_type": "dellme", "sensor_descr": "Capacitor Charge-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 98, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.22", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.22", "sensor_type": "dellme", "sensor_descr": "Capacitor Charge-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 98, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.54", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.54", "sensor_type": "dellme", "sensor_descr": "Current 12V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44.57, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.55", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.55", "sensor_type": "dellme", "sensor_descr": "Current 5V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.15, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.56", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.56", "sensor_type": "dellme", "sensor_descr": "Current 12V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.57", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.57", "sensor_type": "dellme", "sensor_descr": "Current 5V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.03, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.23", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.23", "sensor_type": "dellme", "sensor_descr": "Overall Unit Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.1", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.1", "sensor_type": "dellme", "sensor_descr": "CPU Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 61, "sensor_limit": 81, "sensor_limit_warn": null, "sensor_limit_low": 51, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.10", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.10", "sensor_type": "dellme", "sensor_descr": "Host Left IOC Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 55, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.2", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.2", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.24", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.24", "sensor_type": "dellme", "sensor_descr": "SBB IOM Inlet Temperature Loc: lower-IOM B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44, "sensor_limit": 64, "sensor_limit_warn": null, "sensor_limit_low": 34, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.25", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.25", "sensor_type": "dellme", "sensor_descr": "Expander Internal Temperature Loc: lower-IOM B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 67, "sensor_limit": 87, "sensor_limit_warn": null, "sensor_limit_low": 57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.26", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.26", "sensor_type": "dellme", "sensor_descr": "SBB IOM Inlet Temperature Loc: upper-IOM A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.27", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.27", "sensor_type": "dellme", "sensor_descr": "Expander Internal Temperature Loc: upper-IOM A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 63, "sensor_limit": 83, "sensor_limit_warn": null, "sensor_limit_low": 53, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.28", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.28", "sensor_type": "dellme", "sensor_descr": "Temperature Inlet Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.29", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.29", "sensor_type": "dellme", "sensor_descr": "Temperature Hotspot Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 43, "sensor_limit": 63, "sensor_limit_warn": null, "sensor_limit_low": 33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.3", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.3", "sensor_type": "dellme", "sensor_descr": "Expander Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 67, "sensor_limit": 87, "sensor_limit_warn": null, "sensor_limit_low": 57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.30", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.30", "sensor_type": "dellme", "sensor_descr": "Temperature Inlet Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 55, "sensor_limit_warn": null, "sensor_limit_low": 25, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.31", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.31", "sensor_type": "dellme", "sensor_descr": "Temperature Hotspot Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 44, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.32", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.32", "sensor_type": "dellme", "sensor_descr": "Front-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 45, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.33", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.33", "sensor_type": "dellme", "sensor_descr": "Middle-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.34", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.34", "sensor_type": "dellme", "sensor_descr": "Rear-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.35", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.35", "sensor_type": "dellme", "sensor_descr": "Rear-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.36", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.36", "sensor_type": "dellme", "sensor_descr": "Front-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 45, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.37", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.37", "sensor_type": "dellme", "sensor_descr": "Middle-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.38", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.38", "sensor_type": "dellme", "sensor_descr": "Rear-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36, "sensor_limit": 56, "sensor_limit_warn": null, "sensor_limit_low": 26, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.39", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.39", "sensor_type": "dellme", "sensor_descr": "Rear-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.4", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.4", "sensor_type": "dellme", "sensor_descr": "Disk Controller Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 72, "sensor_limit": 92, "sensor_limit_warn": null, "sensor_limit_low": 62, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.44", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.44", "sensor_type": "dellme", "sensor_descr": "Ambient Temp, Left Sideplane", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 46, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.49", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.49", "sensor_type": "dellme", "sensor_descr": "Ambient Temp, Left Sideplane", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 46, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.5", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.5", "sensor_type": "dellme", "sensor_descr": "Host Left IOC Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 53, "sensor_limit": 73, "sensor_limit_warn": null, "sensor_limit_low": 43, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.6", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.6", "sensor_type": "dellme", "sensor_descr": "CPU Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66, "sensor_limit": 86, "sensor_limit_warn": null, "sensor_limit_low": 56, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.7", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.7", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.8", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.8", "sensor_type": "dellme", "sensor_descr": "Expander Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 63, "sensor_limit": 83, "sensor_limit_warn": null, "sensor_limit_low": 53, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.9", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.9", "sensor_type": "dellme", "sensor_descr": "Disk Controller Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 65, "sensor_limit": 85, "sensor_limit_warn": null, "sensor_limit_low": 55, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.11", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.11", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.12", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.12", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 1 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.13", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.13", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 2 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.14", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.14", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 3 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.15", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.15", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 4 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.16", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.16", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.17", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.17", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 1 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.18", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.18", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 2 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.19", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.19", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 3 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.20", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.20", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 4 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.50", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.50", "sensor_type": "dellme", "sensor_descr": "Voltage 12V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.51", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.51", "sensor_type": "dellme", "sensor_descr": "Voltage 5V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.52", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.52", "sensor_type": "dellme", "sensor_descr": "Voltage 12V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.53", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.53", "sensor_type": "dellme", "sensor_descr": "Voltage 5V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.21", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.21", "sensor_type": "dellme", "sensor_descr": "Capacitor Charge-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 98, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.22", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.22", "sensor_type": "dellme", "sensor_descr": "Capacitor Charge-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 98, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.54", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.54", "sensor_type": "dellme", "sensor_descr": "Current 12V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44.57, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.55", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.55", "sensor_type": "dellme", "sensor_descr": "Current 5V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.15, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.56", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.56", "sensor_type": "dellme", "sensor_descr": "Current 12V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.57", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.57", "sensor_type": "dellme", "sensor_descr": "Current 5V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.03, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.23", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.23", "sensor_type": "dellme", "sensor_descr": "Overall Unit Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.1", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.1", "sensor_type": "dellme", "sensor_descr": "CPU Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 61, "sensor_limit": 81, "sensor_limit_warn": null, "sensor_limit_low": 51, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.10", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.10", "sensor_type": "dellme", "sensor_descr": "Host Left IOC Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 55, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.2", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.2", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.24", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.24", "sensor_type": "dellme", "sensor_descr": "SBB IOM Inlet Temperature Loc: lower-IOM B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44, "sensor_limit": 64, "sensor_limit_warn": null, "sensor_limit_low": 34, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.25", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.25", "sensor_type": "dellme", "sensor_descr": "Expander Internal Temperature Loc: lower-IOM B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 67, "sensor_limit": 87, "sensor_limit_warn": null, "sensor_limit_low": 57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.26", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.26", "sensor_type": "dellme", "sensor_descr": "SBB IOM Inlet Temperature Loc: upper-IOM A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.27", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.27", "sensor_type": "dellme", "sensor_descr": "Expander Internal Temperature Loc: upper-IOM A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 63, "sensor_limit": 83, "sensor_limit_warn": null, "sensor_limit_low": 53, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.28", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.28", "sensor_type": "dellme", "sensor_descr": "Temperature Inlet Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.29", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.29", "sensor_type": "dellme", "sensor_descr": "Temperature Hotspot Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 43, "sensor_limit": 63, "sensor_limit_warn": null, "sensor_limit_low": 33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.3", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.3", "sensor_type": "dellme", "sensor_descr": "Expander Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 67, "sensor_limit": 87, "sensor_limit_warn": null, "sensor_limit_low": 57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.30", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.30", "sensor_type": "dellme", "sensor_descr": "Temperature Inlet Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 55, "sensor_limit_warn": null, "sensor_limit_low": 25, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.31", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.31", "sensor_type": "dellme", "sensor_descr": "Temperature Hotspot Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 44, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.32", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.32", "sensor_type": "dellme", "sensor_descr": "Front-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 45, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.33", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.33", "sensor_type": "dellme", "sensor_descr": "Middle-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.34", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.34", "sensor_type": "dellme", "sensor_descr": "Rear-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.35", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.35", "sensor_type": "dellme", "sensor_descr": "Rear-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.36", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.36", "sensor_type": "dellme", "sensor_descr": "Front-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 45, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.37", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.37", "sensor_type": "dellme", "sensor_descr": "Middle-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.38", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.38", "sensor_type": "dellme", "sensor_descr": "Rear-Left Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36, "sensor_limit": 56, "sensor_limit_warn": null, "sensor_limit_low": 26, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.39", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.39", "sensor_type": "dellme", "sensor_descr": "Rear-Right Baseplane Temp Loc: Drawer", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.4", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.4", "sensor_type": "dellme", "sensor_descr": "Disk Controller Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 72, "sensor_limit": 92, "sensor_limit_warn": null, "sensor_limit_low": 62, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.44", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.44", "sensor_type": "dellme", "sensor_descr": "Ambient Temp, Left Sideplane", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 46, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.49", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.49", "sensor_type": "dellme", "sensor_descr": "Ambient Temp, Left Sideplane", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 46, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.5", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.5", "sensor_type": "dellme", "sensor_descr": "Host Left IOC Temperature-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 53, "sensor_limit": 73, "sensor_limit_warn": null, "sensor_limit_low": 43, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.6", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.6", "sensor_type": "dellme", "sensor_descr": "CPU Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66, "sensor_limit": 86, "sensor_limit_warn": null, "sensor_limit_low": 56, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.7", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.7", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.8", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.8", "sensor_type": "dellme", "sensor_descr": "Expander Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 63, "sensor_limit": 83, "sensor_limit_warn": null, "sensor_limit_low": 53, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.9", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.9", "sensor_type": "dellme", "sensor_descr": "Disk Controller Temperature-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 65, "sensor_limit": 85, "sensor_limit_warn": null, "sensor_limit_low": 55, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.11", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.11", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 8.14, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.12", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.12", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 1 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.03, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.13", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.13", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 2 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.02, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.14", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.14", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 3 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.07, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.15", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.15", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 4 Voltage-Ctlr B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.01, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.16", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.16", "sensor_type": "dellme", "sensor_descr": "Capacitor Pack Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 8.18, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.17", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.17", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 1 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.04, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.18", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.18", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 2 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.04, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.19", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.19", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 3 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.07, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.20", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.20", "sensor_type": "dellme", "sensor_descr": "Capacitor Cell 4 Voltage-Ctlr A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.03, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.50", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.50", "sensor_type": "dellme", "sensor_descr": "Voltage 12V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12.42, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.51", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.51", "sensor_type": "dellme", "sensor_descr": "Voltage 5V Rail Loc: right-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5.27, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.52", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.52", "sensor_type": "dellme", "sensor_descr": "Voltage 12V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12.46, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.1.6.************.**********.0.0.0.0.0.0.0.0.53", "sensor_index": "************.**********.0.0.0.0.0.0.0.0.53", "sensor_type": "dellme", "sensor_descr": "Voltage 5V Rail Loc: left-PSU", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5.27, "sensor_limit": 29.9, "sensor_limit_warn": null, "sensor_limit_low": 22.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 26, "user_func": null, "rrd_type": "GAUGE", "state_name": null}]}}}