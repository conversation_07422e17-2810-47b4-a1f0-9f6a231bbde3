{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.3052.23.3.2", "sysDescr": "SiteBoss 360 2.12.540 STD", "sysContact": "<private>", "version": "2.12.540 STD", "hardware": "SiteBoss 360", "features": null, "location": "<private>", "os": "siteboss360", "type": "server", "serial": null, "icon": "siteboss.png"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.1", "sensor_index": "11.5.1", "sensor_type": "siteboss360", "sensor_descr": "DC Voltage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.2", "sensor_index": "11.5.2", "sensor_type": "siteboss360", "sensor_descr": "DC Current", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3.9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.3", "sensor_index": "11.5.3", "sensor_type": "siteboss360", "sensor_descr": "AC Input Voltage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 243.4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.4", "sensor_index": "11.5.4", "sensor_type": "siteboss360", "sensor_descr": "Total Rectifers", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.5", "sensor_index": "11.5.5", "sensor_type": "siteboss360", "sensor_descr": "Failed Rectifers", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.1", "sensor_index": "12.5.1", "sensor_type": "siteboss360", "sensor_descr": "Gen Fuel Level", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 89, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.10", "sensor_index": "12.5.10", "sensor_type": "siteboss360", "sensor_descr": "Gen O2 Sensor", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.11", "sensor_index": "12.5.11", "sensor_type": "siteboss360", "sensor_descr": "Gen Current Phs A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.12", "sensor_index": "12.5.12", "sensor_type": "siteboss360", "sensor_descr": "Gen Current Phs B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.13", "sensor_index": "12.5.13", "sensor_type": "siteboss360", "sensor_descr": "Gen Current Phs C", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.14", "sensor_index": "12.5.14", "sensor_type": "siteboss360", "sensor_descr": "Gen Aveg Current", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.15", "sensor_index": "12.5.15", "sensor_type": "siteboss360", "sensor_descr": "Gen Voltage Phs A-B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.16", "sensor_index": "12.5.16", "sensor_type": "siteboss360", "sensor_descr": "Gen Voltage Phs B-C", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.17", "sensor_index": "12.5.17", "sensor_type": "siteboss360", "sensor_descr": "Gen Voltage Phs C-A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.18", "sensor_index": "12.5.18", "sensor_type": "siteboss360", "sensor_descr": "Gen Aveg Voltage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.19", "sensor_index": "12.5.19", "sensor_type": "siteboss360", "sensor_descr": "Gen Power KW", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.2", "sensor_index": "12.5.2", "sensor_type": "siteboss360", "sensor_descr": "Gen RPM", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.20", "sensor_index": "12.5.20", "sensor_type": "siteboss360", "sensor_descr": "Gen Total PF", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 100, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.21", "sensor_index": "12.5.21", "sensor_type": "siteboss360", "sensor_descr": "Gen Frequency", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.3", "sensor_index": "12.5.3", "sensor_type": "siteboss360", "sensor_descr": "Gen Coolant Temp", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 93, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.4", "sensor_index": "12.5.4", "sensor_type": "siteboss360", "sensor_descr": "Gen Battery Voltage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26.3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.5", "sensor_index": "12.5.5", "sensor_type": "siteboss360", "sensor_descr": "Gen Run Hours", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 258.4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.6", "sensor_index": "12.5.6", "sensor_type": "siteboss360", "sensor_descr": "Gen Oil Pressure", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.7", "sensor_index": "12.5.7", "sensor_type": "siteboss360", "sensor_descr": "Gen Oil Temp", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.8", "sensor_index": "12.5.8", "sensor_type": "siteboss360", "sensor_descr": "Gen Coolant Level", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 62.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.9", "sensor_index": "12.5.9", "sensor_type": "siteboss360", "sensor_descr": "<PERSON>", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "humidity", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*******.1", "sensor_index": "1.3.1", "sensor_type": "siteboss360", "sensor_descr": "<PERSON><PERSON><PERSON><PERSON>", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 49, "sensor_limit": 70, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.1.7.1", "sensor_index": "1", "sensor_type": "siteboss360", "sensor_descr": "Fuel Level", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 88.99, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": 10, "sensor_limit_low_warn": 20, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.1", "sensor_index": "11.2.1", "sensor_type": "contactClosure", "sensor_descr": "AC Fail", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.2", "sensor_index": "11.2.2", "sensor_type": "contactClosure", "sensor_descr": "Battery on Discharge", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.3", "sensor_index": "11.2.3", "sensor_type": "contactClosure", "sensor_descr": "Battery Breaker Trip", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.4", "sensor_index": "11.2.4", "sensor_type": "contactClosure", "sensor_descr": "Dist Breaker Trip", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.5", "sensor_index": "11.2.5", "sensor_type": "contactClosure", "sensor_descr": "Rectifier Fail", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.6", "sensor_index": "11.2.6", "sensor_type": "contactClosure", "sensor_descr": "Reserve Time Low", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.7", "sensor_index": "11.2.7", "sensor_type": "contactClosure", "sensor_descr": "High Battery Temp", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.********.8", "sensor_index": "11.2.8", "sensor_type": "contactClosure", "sensor_descr": "Battery Test Active", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.1", "sensor_index": "200.2.1", "sensor_type": "contactClosure", "sensor_descr": "Door Sensor", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.16", "sensor_index": "200.2.16", "sensor_type": "contactClosure", "sensor_descr": "USAF Door", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.2", "sensor_index": "200.2.2", "sensor_type": "contactClosure", "sensor_descr": "High Temp", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.3", "sensor_index": "200.2.3", "sensor_type": "contactClosure", "sensor_descr": "Low Temp", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.4", "sensor_index": "200.2.4", "sensor_type": "contactClosure", "sensor_descr": "Smoke Detector", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.5", "sensor_index": "200.2.5", "sensor_type": "contactClosure", "sensor_descr": "Commercial Power", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.6", "sensor_index": "200.2.6", "sensor_type": "contactClosure", "sensor_descr": "A/C Surge Arrestor Fail", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.7", "sensor_index": "200.2.7", "sensor_type": "contactClosure", "sensor_descr": "HVAC", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*********.8", "sensor_index": "200.2.8", "sensor_type": "contactClosure", "sensor_descr": "Generator A/C Sensor", "group": "Contact Closures", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "contactClosure"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3052.********.*******.1", "sensor_index": "1.1.1", "sensor_type": "siteboss360", "sensor_descr": "Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 21.11, "sensor_limit": 41.11, "sensor_limit_warn": null, "sensor_limit_low": 11.11, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": "fahrenheit_to_celsius", "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "contactClosure", "state_descr": "event", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}, {"state_name": "contactClosure", "state_descr": "normal", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}]}, "poller": "matches discovery"}}