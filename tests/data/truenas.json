{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.8072.3.2.8", "sysDescr": "TrueNAS-11.2-U7 (b088e78e8d). Hardware: amd64 Intel(R) Xeon(R) CPU E5-2620 v2 @ 2.10GHz running at 2100. Software: FreeBSD 11.2-STABLE (revision 199506)", "sysContact": "<private>", "version": "11.2-U7", "hardware": "amd64 Intel(R) Xeon(R) CPU E5-2620 v2 @ 2.10GHz running at 2100", "features": null, "location": "<private>", "os": "truenas", "type": "storage", "serial": null, "icon": "truenas.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "cxl0", "ifName": "cxl0", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "cxl0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "cxl1", "ifName": "cxl1", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "cxl1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "igb0", "ifName": "igb0", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "igb0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "igb1", "ifName": "igb1", "portName": null, "ifIndex": 4, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "igb1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "em0", "ifName": "em0", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "em0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo0", "ifName": "lo0", "portName": null, "ifIndex": 6, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "pflog0", "ifName": "pflog0", "portName": null, "ifIndex": 7, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ifPwType", "ifAlias": "pflog0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "vlan19", "ifName": "vlan19", "portName": null, "ifIndex": 8, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "l2vlan", "ifAlias": "vlan19", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "vlan250", "ifName": "vlan250", "portName": null, "ifIndex": 9, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "l2vlan", "ifAlias": "vlan250", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "cxl0", "ifName": "cxl0", "portName": null, "ifIndex": 1, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9000, "ifType": "ethernetCsmacd", "ifAlias": "cxl0", "ifPhysAddress": "5dfc42dfaa13", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 16251166579, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 17788587329, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 12945951421558, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 22877547318994, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 2587191, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 14723784, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "cxl1", "ifName": "cxl1", "portName": null, "ifIndex": 2, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "cxl1", "ifPhysAddress": "000743389588", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "igb0", "ifName": "igb0", "portName": null, "ifIndex": 3, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "igb0", "ifPhysAddress": "00aa14e9eaf2", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "igb1", "ifName": "igb1", "portName": null, "ifIndex": 4, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "igb1", "ifPhysAddress": "00aa14e9eaf5", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "em0", "ifName": "em0", "portName": null, "ifIndex": 5, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "em0", "ifPhysAddress": "0005ca4134aa", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 71536620, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 74993132, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 8224005647, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 21260660850, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 7012141, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 6661088, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo0", "ifName": "lo0", "portName": null, "ifIndex": 6, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 16384, "ifType": "softwareLoopback", "ifAlias": "lo0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1062264513, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1062264513, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 75611012271, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 75611012271, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "pflog0", "ifName": "pflog0", "portName": null, "ifIndex": 7, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 33160, "ifType": "ifPwType", "ifAlias": "pflog0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "vlan19", "ifName": "vlan19", "portName": null, "ifIndex": 8, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9000, "ifType": "l2vlan", "ifAlias": "vlan19", "ifPhysAddress": "5dfc42dfaa13", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 7003491084, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 6327221366, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 2, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 11483946266349, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 22101946833086, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 4158126, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 7242318, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "vlan250", "ifName": "vlan250", "portName": null, "ifIndex": 9, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9000, "ifType": "l2vlan", "ifAlias": "vlan250", "ifPhysAddress": "5dfc42dfaa13", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 135068794, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 7260286, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 2, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 814448972875, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 505464377, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 960077, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 7242301, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 196608, "processor_oid": ".*******.2.1.25.3.3.1.2.196608", "processor_index": "196608", "processor_type": "hr", "processor_usage": 3, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196609, "processor_oid": ".*******.2.1.25.3.3.1.2.196609", "processor_index": "196609", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196610, "processor_oid": ".*******.2.1.25.3.3.1.2.196610", "processor_index": "196610", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196611, "processor_oid": ".*******.2.1.25.3.3.1.2.196611", "processor_index": "196611", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196612, "processor_oid": ".*******.2.1.25.3.3.1.2.196612", "processor_index": "196612", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196613, "processor_oid": ".*******.2.1.25.3.3.1.2.196613", "processor_index": "196613", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196614, "processor_oid": ".*******.2.1.25.3.3.1.2.196614", "processor_index": "196614", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196615, "processor_oid": ".*******.2.1.25.3.3.1.2.196615", "processor_index": "196615", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196616, "processor_oid": ".*******.2.1.25.3.3.1.2.196616", "processor_index": "196616", "processor_type": "hr", "processor_usage": 4, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196617, "processor_oid": ".*******.2.1.25.3.3.1.2.196617", "processor_index": "196617", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196618, "processor_oid": ".*******.2.1.25.3.3.1.2.196618", "processor_index": "196618", "processor_type": "hr", "processor_usage": 4, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196619, "processor_oid": ".*******.2.1.25.3.3.1.2.196619", "processor_index": "196619", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196620, "processor_oid": ".*******.2.1.25.3.3.1.2.196620", "processor_index": "196620", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196621, "processor_oid": ".*******.2.1.25.3.3.1.2.196621", "processor_index": "196621", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196622, "processor_oid": ".*******.2.1.25.3.3.1.2.196622", "processor_index": "196622", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196623, "processor_oid": ".*******.2.1.25.3.3.1.2.196623", "processor_index": "196623", "processor_type": "hr", "processor_usage": 3, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196624, "processor_oid": ".*******.2.1.25.3.3.1.2.196624", "processor_index": "196624", "processor_type": "hr", "processor_usage": 3, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196625, "processor_oid": ".*******.2.1.25.3.3.1.2.196625", "processor_index": "196625", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196626, "processor_oid": ".*******.2.1.25.3.3.1.2.196626", "processor_index": "196626", "processor_type": "hr", "processor_usage": 4, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196627, "processor_oid": ".*******.2.1.25.3.3.1.2.196627", "processor_index": "196627", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196628, "processor_oid": ".*******.2.1.25.3.3.1.2.196628", "processor_index": "196628", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196629, "processor_oid": ".*******.2.1.25.3.3.1.2.196629", "processor_index": "196629", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196630, "processor_oid": ".*******.2.1.25.3.3.1.2.196630", "processor_index": "196630", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196631, "processor_oid": ".*******.2.1.25.3.3.1.2.196631", "processor_index": "196631", "processor_type": "hr", "processor_usage": 2, "processor_descr": "Intel Xeon E5-2620 v2 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "system", "mempool_precision": 4096, "mempool_descr": "Physical memory", "mempool_perc": 98, "mempool_perc_oid": null, "mempool_used": 134064627712, "mempool_used_oid": ".*******.2.1.25.2.3.1.6.1", "mempool_free": 3298312192, "mempool_free_oid": null, "mempool_total": 137362939904, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 99}, {"mempool_index": "2", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "system", "mempool_precision": 4096, "mempool_descr": "Real memory", "mempool_perc": 100, "mempool_perc_oid": null, "mempool_used": 18198892544, "mempool_used_oid": ".*******.2.1.25.2.3.1.6.2", "mempool_free": 36872192, "mempool_free_oid": null, "mempool_total": 18235764736, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}, {"mempool_index": "3", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "virtual", "mempool_precision": 4096, "mempool_descr": "Virtual memory", "mempool_perc": 99, "mempool_perc_oid": null, "mempool_used": 20746682368, "mempool_used_oid": ".*******.2.1.25.2.3.1.6.3", "mempool_free": 126582784, "mempool_free_oid": null, "mempool_total": 20873265152, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 95}, {"mempool_index": "6", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "buffers", "mempool_precision": 1024, "mempool_descr": "Memory buffers", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": 0, "mempool_used_oid": ".*******.2.1.25.2.3.1.6.6", "mempool_free": 137362939904, "mempool_free_oid": null, "mempool_total": 137362939904, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "7", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "cached", "mempool_precision": 4096, "mempool_descr": "Cached memory", "mempool_perc": 50, "mempool_perc_oid": null, "mempool_used": 69237669888, "mempool_used_oid": ".*******.2.1.25.2.3.1.6.7", "mempool_free": 68125270016, "mempool_free_oid": null, "mempool_total": 137362939904, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "10", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "swap", "mempool_precision": 4096, "mempool_descr": "Swap space", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": 0, "mempool_used_oid": ".*******.2.1.25.2.3.1.6.10", "mempool_free": 17179734016, "mempool_free_oid": null, "mempool_total": 17179734016, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 10}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcC.0", "sensor_type": "truenas", "sensor_descr": "ARC Target Size (C)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40770786, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcData.0", "sensor_type": "truenas", "sensor_descr": "ARC Data", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36451872, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcHits_rate.0", "sensor_type": "truenas", "sensor_descr": "ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 415721177, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMeta.0", "sensor_type": "truenas", "sensor_descr": "ARC Metadata", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4367194, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMisses_rate.0", "sensor_type": "truenas", "sensor_descr": "AR<PERSON>es", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 375483528, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMissPercent.0", "sensor_type": "truenas", "sensor_descr": "ARC Miss Percent", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.29955942419893, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcP.0", "sensor_type": "truenas", "sensor_descr": "ARC MRU Target Size (P)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37353685, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcSize.0", "sensor_type": "truenas", "sensor_descr": "ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40819066, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcHits_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 167013121, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcMisses_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Misses", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 208461943, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcRead_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Reads", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3072506284, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcSize.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 698485632, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcWrite_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Writes", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 377661890, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps10sec.0", "sensor_type": "truenas", "sensor_descr": "ZIL Operations last 10 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps1sec.0", "sensor_type": "truenas", "sensor_descr": "ZIL Operations last second", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps5sec.0", "sensor_type": "truenas", "sensor_descr": "ZIL Operations last 5 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.7.1", "sensor_index": "zpoolHealth.1", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: freenas-boot", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.7.2", "sensor_index": "zpoolHealth.2", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: tank", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.1", "sensor_index": "1", "sensor_type": "lmsensors", "sensor_descr": "CPU0", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.2", "sensor_index": "2", "sensor_type": "lmsensors", "sensor_descr": "CPU1", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.3", "sensor_index": "3", "sensor_type": "lmsensors", "sensor_descr": "CPU2", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.4", "sensor_index": "4", "sensor_type": "lmsensors", "sensor_descr": "CPU3", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.5", "sensor_index": "5", "sensor_type": "lmsensors", "sensor_descr": "ada1", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.6", "sensor_index": "6", "sensor_type": "lmsensors", "sensor_descr": "ada0", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "zpoolTable", "state_descr": "online", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "zpoolTable", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "zpoolTable", "state_descr": "faulted", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "offline", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "unavail", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "removed", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcC.0", "sensor_type": "truenas", "sensor_descr": "ARC Target Size (C)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40770786, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcData.0", "sensor_type": "truenas", "sensor_descr": "ARC Data", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36451872, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcHits_rate.0", "sensor_type": "truenas", "sensor_descr": "ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 415721177, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMeta.0", "sensor_type": "truenas", "sensor_descr": "ARC Metadata", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4367194, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMisses_rate.0", "sensor_type": "truenas", "sensor_descr": "AR<PERSON>es", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 375483528, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMissPercent.0", "sensor_type": "truenas", "sensor_descr": "ARC Miss Percent", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.29955942419893, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 0.29955942419893, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcP.0", "sensor_type": "truenas", "sensor_descr": "ARC MRU Target Size (P)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37353685, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcSize.0", "sensor_type": "truenas", "sensor_descr": "ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40819066, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcHits_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 167013121, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcMisses_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Misses", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 208461943, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcRead_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Reads", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3072506284, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcSize.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 698485632, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcWrite_rate.0", "sensor_type": "truenas", "sensor_descr": "L2ARC Writes", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 377661890, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps10sec.0", "sensor_type": "truenas", "sensor_descr": "ZIL Operations last 10 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps1sec.0", "sensor_type": "truenas", "sensor_descr": "ZIL Operations last second", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps5sec.0", "sensor_type": "truenas", "sensor_descr": "ZIL Operations last 5 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.7.1", "sensor_index": "zpoolHealth.1", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: freenas-boot", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.7.2", "sensor_index": "zpoolHealth.2", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: tank", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.1", "sensor_index": "1", "sensor_type": "lmsensors", "sensor_descr": "CPU0", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.2", "sensor_index": "2", "sensor_type": "lmsensors", "sensor_descr": "CPU1", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.3", "sensor_index": "3", "sensor_type": "lmsensors", "sensor_descr": "CPU2", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.4", "sensor_index": "4", "sensor_type": "lmsensors", "sensor_descr": "CPU3", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 27.9, "sensor_limit": 47.9, "sensor_limit_warn": null, "sensor_limit_low": 17.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.5", "sensor_index": "5", "sensor_type": "lmsensors", "sensor_descr": "ada1", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.2021.*********.3.6", "sensor_index": "6", "sensor_type": "lmsensors", "sensor_descr": "ada0", "group": "lmsensors", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "zpoolTable", "state_descr": "online", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "zpoolTable", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "zpoolTable", "state_descr": "faulted", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "offline", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "unavail", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "removed", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}]}}, "storage": {"discovery": {"storage": [{"type": "freenas-dataset", "storage_index": "1", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT", "storage_size": 14255882240, "storage_size_oid": null, "storage_units": 4096, "storage_used": 9196281856, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.1", "storage_perc": 65, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "10", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.1-U4", "storage_size": 5060001792, "storage_size_oid": null, "storage_units": 4096, "storage_used": 401408, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.10", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "11", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U7", "storage_size": 14253064192, "storage_size_oid": null, "storage_units": 4096, "storage_used": 9193463808, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.11", "storage_perc": 65, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "12", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/default", "storage_size": 5059735552, "storage_size_oid": null, "storage_units": 4096, "storage_used": 135168, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.12", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "13", "storage_type": "dataset", "storage_descr": "freenas-boot/grub", "storage_size": 5066895360, "storage_size_oid": null, "storage_units": 4096, "storage_used": 7294976, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.13", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "14", "storage_type": "dataset", "storage_descr": "tank/Old Users", "storage_size": 102613222883330, "storage_size_oid": null, "storage_units": 65536, "storage_used": 8083898236928, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.14", "storage_perc": 8, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "15", "storage_type": "dataset", "storage_descr": "tank/Multimedia", "storage_size": 97814180069376, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3284855422976, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.15", "storage_perc": 3, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "16", "storage_type": "dataset", "storage_descr": "tank/legacy", "storage_size": 536870912000, "storage_size_oid": null, "storage_units": 4096, "storage_used": 240531058688, "storage_used_oid": null, "storage_free": 296339853312, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.16", "storage_perc": 45, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "17", "storage_type": "dataset", "storage_descr": "tank/Archive", "storage_size": 98121018048512, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3591693402112, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.17", "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "18", "storage_type": "dataset", "storage_descr": "tank/Time Machine", "storage_size": 5497558138880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 4606006181888, "storage_used_oid": null, "storage_free": 891551956992, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.18", "storage_perc": 84, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "19", "storage_type": "dataset", "storage_descr": "tank/Meta", "storage_size": 94529325236224, "storage_size_oid": null, "storage_units": 65536, "storage_used": 589824, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.19", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "2", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U6.1", "storage_size": 5059997696, "storage_size_oid": null, "storage_units": 4096, "storage_used": 397312, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.2", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "20", "storage_type": "dataset", "storage_descr": "tank/Dell", "storage_size": 94570048651264, "storage_size_oid": null, "storage_units": 65536, "storage_used": 40724004864, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.20", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "21", "storage_type": "dataset", "storage_descr": "tank/.system", "storage_size": 94530228125696, "storage_size_oid": null, "storage_units": 65536, "storage_used": 903479296, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.21", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "22", "storage_type": "dataset", "storage_descr": "tank/.system/cores", "storage_size": 94529334476800, "storage_size_oid": null, "storage_units": 65536, "storage_used": 9830400, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.22", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "23", "storage_type": "dataset", "storage_descr": "tank/.system/webui", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.23", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "24", "storage_type": "dataset", "storage_descr": "tank/.system/configs-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529461288960, "storage_size_oid": null, "storage_units": 65536, "storage_used": 136642560, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.24", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "25", "storage_type": "dataset", "storage_descr": "tank/.system/syslog-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529440710656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 116064256, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.25", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "26", "storage_type": "dataset", "storage_descr": "tank/.system/rrd-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529396670464, "storage_size_oid": null, "storage_units": 65536, "storage_used": 72024064, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.26", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "27", "storage_type": "dataset", "storage_descr": "tank/.system/samba4", "storage_size": 94529326022656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 1376256, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.27", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "28", "storage_type": "dataset", "storage_descr": "tank/Filestore Archive", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.28", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "29", "storage_type": "dataset", "storage_descr": "tank/Backup", "storage_size": 94581655404544, "storage_size_oid": null, "storage_units": 65536, "storage_used": 52330758144, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.29", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "3", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/Initial-Install", "storage_size": 5059600384, "storage_size_oid": null, "storage_units": 4096, "storage_used": 0, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.3", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "30", "storage_type": "dataset", "storage_descr": "tank/Backup/Backup1", "storage_size": 94581655207936, "storage_size_oid": null, "storage_units": 65536, "storage_used": 52330561536, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.30", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "31", "storage_type": "dataset", "storage_descr": "tank/Vroom", "storage_size": 98827364990976, "storage_size_oid": null, "storage_units": 65536, "storage_used": 4298040344576, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.31", "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "32", "storage_type": "dataset", "storage_descr": "tank/Active", "storage_size": 94539373281280, "storage_size_oid": null, "storage_units": 65536, "storage_used": 10048634880, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.32", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "33", "storage_type": "dataset", "storage_descr": "tank/Users", "storage_size": 10995116277760, "storage_size_oid": null, "storage_units": 8192, "storage_used": 4691064840192, "storage_used_oid": null, "storage_free": 6304051437568, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.33", "storage_perc": 43, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "4", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U5", "storage_size": 5059989504, "storage_size_oid": null, "storage_units": 4096, "storage_used": 389120, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.4", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "5", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U5.1", "storage_size": 5059969024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 368640, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.5", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "6", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U6", "storage_size": 5059981312, "storage_size_oid": null, "storage_units": 4096, "storage_used": 380928, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.6", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "7", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.1-U7.1", "storage_size": 5059866624, "storage_size_oid": null, "storage_units": 4096, "storage_used": 266240, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.7", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "8", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/9.10.2-U5", "storage_size": 5059772416, "storage_size_oid": null, "storage_units": 4096, "storage_used": 172032, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.8", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "9", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.1-U6.1", "storage_size": 5059874816, "storage_size_oid": null, "storage_units": 4096, "storage_used": 274432, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.9", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-zpool", "storage_index": "1", "storage_type": "zpool", "storage_descr": "freenas-boot", "storage_size": 14763950080, "storage_size_oid": null, "storage_units": 4096, "storage_used": 9247965184, "storage_used_oid": ".*******.4.1.50536.*******.5.1", "storage_free": 5515984896, "storage_free_oid": null, "storage_perc": 63, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-zpool", "storage_index": "2", "storage_type": "zpool", "storage_descr": "tank", "storage_size": 191315023233020, "storage_size_oid": null, "storage_units": 131072, "storage_used": 43403846746112, "storage_used_oid": ".*******.4.1.50536.*******.5.2", "storage_free": 147911176486910, "storage_free_oid": null, "storage_perc": 23, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "31", "storage_type": "hrStorageFixedDisk", "storage_descr": "/", "storage_size": 5926602240, "storage_size_oid": null, "storage_units": 512, "storage_used": 871591424, "storage_used_oid": ".*******.2.1.25.2.3.1.6.31", "storage_free": 5055010816, "storage_free_oid": null, "storage_perc": 15, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "33", "storage_type": "hrStorageFixedDisk", "storage_descr": "/etc", "storage_size": 17179607040, "storage_size_oid": null, "storage_units": 4096, "storage_used": 10919936, "storage_used_oid": ".*******.2.1.25.2.3.1.6.33", "storage_free": 17168687104, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "34", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt", "storage_size": 2147483648, "storage_size_oid": null, "storage_units": 4096, "storage_used": 8192, "storage_used_oid": ".*******.2.1.25.2.3.1.6.34", "storage_free": 2147475456, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "35", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var", "storage_size": 23443274989568, "storage_size_oid": null, "storage_units": 16384, "storage_used": 360890368, "storage_used_oid": ".*******.2.1.25.2.3.1.6.35", "storage_free": 23442914099200, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "37", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank", "storage_size": 94529324843008, "storage_size_oid": null, "storage_units": 65536, "storage_used": 196608, "storage_used_oid": ".*******.2.1.25.2.3.1.6.37", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "38", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Backup", "storage_size": 94529324843008, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": ".*******.2.1.25.2.3.1.6.38", "storage_free": 94529324711936, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "39", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Backup/Backup1", "storage_size": 94538332241920, "storage_size_oid": null, "storage_units": 65536, "storage_used": 9007595520, "storage_used_oid": ".*******.2.1.25.2.3.1.6.39", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "40", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Filestore Archive", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": ".*******.2.1.25.2.3.1.6.40", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "41", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Old Users", "storage_size": 99415669538816, "storage_size_oid": null, "storage_units": 65536, "storage_used": 4886344892416, "storage_used_oid": ".*******.2.1.25.2.3.1.6.41", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 5, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "42", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Dell", "storage_size": 94570048651264, "storage_size_oid": null, "storage_units": 65536, "storage_used": 40724004864, "storage_used_oid": ".*******.2.1.25.2.3.1.6.42", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "43", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Meta", "storage_size": 94529325236224, "storage_size_oid": null, "storage_units": 65536, "storage_used": 524288, "storage_used_oid": ".*******.2.1.25.2.3.1.6.43", "storage_free": 94529324711936, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "44", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Multimedia", "storage_size": 97814180069376, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3284855422976, "storage_used_oid": ".*******.2.1.25.2.3.1.6.44", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 3, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "45", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Time Machine", "storage_size": 5497558138880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 4606006177792, "storage_used_oid": ".*******.2.1.25.2.3.1.6.45", "storage_free": 891551961088, "storage_free_oid": null, "storage_perc": 84, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "46", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Users", "storage_size": 10995114377216, "storage_size_oid": null, "storage_units": 8192, "storage_used": 4691062939648, "storage_used_oid": ".*******.2.1.25.2.3.1.6.46", "storage_free": 6304051437568, "storage_free_oid": null, "storage_perc": 43, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "47", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Active", "storage_size": 94539373281280, "storage_size_oid": null, "storage_units": 65536, "storage_used": 10048634880, "storage_used_oid": ".*******.2.1.25.2.3.1.6.47", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "48", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Archive", "storage_size": 98121018048512, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3591693402112, "storage_used_oid": ".*******.2.1.25.2.3.1.6.48", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "49", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Vroom", "storage_size": 98827364990976, "storage_size_oid": null, "storage_units": 65536, "storage_used": 4298040344576, "storage_used_oid": ".*******.2.1.25.2.3.1.6.49", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "50", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/legacy", "storage_size": 536870912000, "storage_size_oid": null, "storage_units": 512, "storage_used": 240531058176, "storage_used_oid": ".*******.2.1.25.2.3.1.6.50", "storage_free": 296339853824, "storage_free_oid": null, "storage_perc": 45, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "51", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system", "storage_size": 94529891926016, "storage_size_oid": null, "storage_units": 65536, "storage_used": 567279616, "storage_used_oid": ".*******.2.1.25.2.3.1.6.51", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "52", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/cores", "storage_size": 94529334476800, "storage_size_oid": null, "storage_units": 65536, "storage_used": 9830400, "storage_used_oid": ".*******.2.1.25.2.3.1.6.52", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "53", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/samba4", "storage_size": 94529326022656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 1376256, "storage_used_oid": ".*******.2.1.25.2.3.1.6.53", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "54", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/syslog-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529440710656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 116064256, "storage_used_oid": ".*******.2.1.25.2.3.1.6.54", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "55", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/rrd-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529396670464, "storage_size_oid": null, "storage_units": 65536, "storage_used": 71958528, "storage_used_oid": ".*******.2.1.25.2.3.1.6.55", "storage_free": 94529324711936, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "56", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/configs-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529461288960, "storage_size_oid": null, "storage_units": 65536, "storage_used": 136642560, "storage_used_oid": ".*******.2.1.25.2.3.1.6.56", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "57", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/webui", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": ".*******.2.1.25.2.3.1.6.57", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}]}, "poller": {"storage": [{"type": "freenas-dataset", "storage_index": "1", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT", "storage_size": 14255882240, "storage_size_oid": null, "storage_units": 4096, "storage_used": 9196281856, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.1", "storage_perc": 65, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "10", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.1-U4", "storage_size": 5060001792, "storage_size_oid": null, "storage_units": 4096, "storage_used": 401408, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.10", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "11", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U7", "storage_size": 14253064192, "storage_size_oid": null, "storage_units": 4096, "storage_used": 9193463808, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.11", "storage_perc": 65, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "12", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/default", "storage_size": 5059735552, "storage_size_oid": null, "storage_units": 4096, "storage_used": 135168, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.12", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "13", "storage_type": "dataset", "storage_descr": "freenas-boot/grub", "storage_size": 5066895360, "storage_size_oid": null, "storage_units": 4096, "storage_used": 7294976, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.13", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "14", "storage_type": "dataset", "storage_descr": "tank/Old Users", "storage_size": 102613222883330, "storage_size_oid": null, "storage_units": 65536, "storage_used": 8083898236930, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.14", "storage_perc": 8, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "15", "storage_type": "dataset", "storage_descr": "tank/Multimedia", "storage_size": 97814180069376, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3284855422976, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.15", "storage_perc": 3, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "16", "storage_type": "dataset", "storage_descr": "tank/legacy", "storage_size": 536870912000, "storage_size_oid": null, "storage_units": 4096, "storage_used": 240531058688, "storage_used_oid": null, "storage_free": 296339853312, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.16", "storage_perc": 45, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "17", "storage_type": "dataset", "storage_descr": "tank/Archive", "storage_size": 98121018048512, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3591693402112, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.17", "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "18", "storage_type": "dataset", "storage_descr": "tank/Time Machine", "storage_size": 5497558138880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 4606006181888, "storage_used_oid": null, "storage_free": 891551956992, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.18", "storage_perc": 84, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "19", "storage_type": "dataset", "storage_descr": "tank/Meta", "storage_size": 94529325236224, "storage_size_oid": null, "storage_units": 65536, "storage_used": 589824, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.19", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "2", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U6.1", "storage_size": 5059997696, "storage_size_oid": null, "storage_units": 4096, "storage_used": 397312, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.2", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "20", "storage_type": "dataset", "storage_descr": "tank/Dell", "storage_size": 94570048651264, "storage_size_oid": null, "storage_units": 65536, "storage_used": 40724004864, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.20", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "21", "storage_type": "dataset", "storage_descr": "tank/.system", "storage_size": 94530228125696, "storage_size_oid": null, "storage_units": 65536, "storage_used": 903479296, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.21", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "22", "storage_type": "dataset", "storage_descr": "tank/.system/cores", "storage_size": 94529334476800, "storage_size_oid": null, "storage_units": 65536, "storage_used": 9830400, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.22", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "23", "storage_type": "dataset", "storage_descr": "tank/.system/webui", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.23", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "24", "storage_type": "dataset", "storage_descr": "tank/.system/configs-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529461288960, "storage_size_oid": null, "storage_units": 65536, "storage_used": 136642560, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.24", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "25", "storage_type": "dataset", "storage_descr": "tank/.system/syslog-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529440710656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 116064256, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.25", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "26", "storage_type": "dataset", "storage_descr": "tank/.system/rrd-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529396670464, "storage_size_oid": null, "storage_units": 65536, "storage_used": 72024064, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.26", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "27", "storage_type": "dataset", "storage_descr": "tank/.system/samba4", "storage_size": 94529326022656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 1376256, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.27", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "28", "storage_type": "dataset", "storage_descr": "tank/Filestore Archive", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.28", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "29", "storage_type": "dataset", "storage_descr": "tank/Backup", "storage_size": 94581655404544, "storage_size_oid": null, "storage_units": 65536, "storage_used": 52330758144, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.29", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "3", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/Initial-Install", "storage_size": 5059600384, "storage_size_oid": null, "storage_units": 4096, "storage_used": 0, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.3", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "30", "storage_type": "dataset", "storage_descr": "tank/Backup/Backup1", "storage_size": 94581655207936, "storage_size_oid": null, "storage_units": 65536, "storage_used": 52330561536, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.30", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "31", "storage_type": "dataset", "storage_descr": "tank/Vroom", "storage_size": 98827364990976, "storage_size_oid": null, "storage_units": 65536, "storage_used": 4298040344576, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.31", "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "32", "storage_type": "dataset", "storage_descr": "tank/Active", "storage_size": 94539373281280, "storage_size_oid": null, "storage_units": 65536, "storage_used": 10048634880, "storage_used_oid": null, "storage_free": 94529324646400, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.32", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "33", "storage_type": "dataset", "storage_descr": "tank/Users", "storage_size": 10995116277760, "storage_size_oid": null, "storage_units": 8192, "storage_used": 4691064840192, "storage_used_oid": null, "storage_free": 6304051437568, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.33", "storage_perc": 43, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "4", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U5", "storage_size": 5059989504, "storage_size_oid": null, "storage_units": 4096, "storage_used": 389120, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.4", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "5", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U5.1", "storage_size": 5059969024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 368640, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.5", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "6", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.2-U6", "storage_size": 5059981312, "storage_size_oid": null, "storage_units": 4096, "storage_used": 380928, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.6", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "7", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.1-U7.1", "storage_size": 5059866624, "storage_size_oid": null, "storage_units": 4096, "storage_used": 266240, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.7", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "8", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/9.10.2-U5", "storage_size": 5059772416, "storage_size_oid": null, "storage_units": 4096, "storage_used": 172032, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.8", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-dataset", "storage_index": "9", "storage_type": "dataset", "storage_descr": "freenas-boot/ROOT/11.1-U6.1", "storage_size": 5059874816, "storage_size_oid": null, "storage_units": 4096, "storage_used": 274432, "storage_used_oid": null, "storage_free": 5059600384, "storage_free_oid": ".*******.4.1.50536.1.2.1.1.6.9", "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-zpool", "storage_index": "1", "storage_type": "zpool", "storage_descr": "freenas-boot", "storage_size": 14763950080, "storage_size_oid": null, "storage_units": 4096, "storage_used": 9247965184, "storage_used_oid": ".*******.4.1.50536.*******.5.1", "storage_free": 5515984896, "storage_free_oid": null, "storage_perc": 63, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "freenas-zpool", "storage_index": "2", "storage_type": "zpool", "storage_descr": "tank", "storage_size": 191315023233020, "storage_size_oid": null, "storage_units": 131072, "storage_used": 43403846746112, "storage_used_oid": ".*******.4.1.50536.*******.5.2", "storage_free": 147911176486910, "storage_free_oid": null, "storage_perc": 23, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "31", "storage_type": "hrStorageFixedDisk", "storage_descr": "/", "storage_size": 5926602240, "storage_size_oid": null, "storage_units": 512, "storage_used": 871591424, "storage_used_oid": ".*******.2.1.25.2.3.1.6.31", "storage_free": 5055010816, "storage_free_oid": null, "storage_perc": 15, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "33", "storage_type": "hrStorageFixedDisk", "storage_descr": "/etc", "storage_size": 17179607040, "storage_size_oid": null, "storage_units": 4096, "storage_used": 10919936, "storage_used_oid": ".*******.2.1.25.2.3.1.6.33", "storage_free": 17168687104, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "34", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt", "storage_size": 2147483648, "storage_size_oid": null, "storage_units": 4096, "storage_used": 8192, "storage_used_oid": ".*******.2.1.25.2.3.1.6.34", "storage_free": 2147475456, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "35", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var", "storage_size": 23443274989568, "storage_size_oid": null, "storage_units": 16384, "storage_used": 360890368, "storage_used_oid": ".*******.2.1.25.2.3.1.6.35", "storage_free": 23442914099200, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "37", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank", "storage_size": 94529324843008, "storage_size_oid": null, "storage_units": 65536, "storage_used": 196608, "storage_used_oid": ".*******.2.1.25.2.3.1.6.37", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "38", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Backup", "storage_size": 94529324843008, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": ".*******.2.1.25.2.3.1.6.38", "storage_free": 94529324711936, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "39", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Backup/Backup1", "storage_size": 94538332241920, "storage_size_oid": null, "storage_units": 65536, "storage_used": 9007595520, "storage_used_oid": ".*******.2.1.25.2.3.1.6.39", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "40", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Filestore Archive", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": ".*******.2.1.25.2.3.1.6.40", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "41", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Old Users", "storage_size": 99415669538816, "storage_size_oid": null, "storage_units": 65536, "storage_used": 4886344892416, "storage_used_oid": ".*******.2.1.25.2.3.1.6.41", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 5, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "42", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Dell", "storage_size": 94570048651264, "storage_size_oid": null, "storage_units": 65536, "storage_used": 40724004864, "storage_used_oid": ".*******.2.1.25.2.3.1.6.42", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "43", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Meta", "storage_size": 94529325236224, "storage_size_oid": null, "storage_units": 65536, "storage_used": 524288, "storage_used_oid": ".*******.2.1.25.2.3.1.6.43", "storage_free": 94529324711936, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "44", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Multimedia", "storage_size": 97814180069376, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3284855422976, "storage_used_oid": ".*******.2.1.25.2.3.1.6.44", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 3, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "45", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Time Machine", "storage_size": 5497558138880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 4606006177792, "storage_used_oid": ".*******.2.1.25.2.3.1.6.45", "storage_free": 891551961088, "storage_free_oid": null, "storage_perc": 84, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "46", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Users", "storage_size": 10995114377216, "storage_size_oid": null, "storage_units": 8192, "storage_used": 4691062939648, "storage_used_oid": ".*******.2.1.25.2.3.1.6.46", "storage_free": 6304051437568, "storage_free_oid": null, "storage_perc": 43, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "47", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Active", "storage_size": 94539373281280, "storage_size_oid": null, "storage_units": 65536, "storage_used": 10048634880, "storage_used_oid": ".*******.2.1.25.2.3.1.6.47", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "48", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Archive", "storage_size": 98121018048512, "storage_size_oid": null, "storage_units": 65536, "storage_used": 3591693402112, "storage_used_oid": ".*******.2.1.25.2.3.1.6.48", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "49", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/Vroom", "storage_size": 98827364990976, "storage_size_oid": null, "storage_units": 65536, "storage_used": 4298040344576, "storage_used_oid": ".*******.2.1.25.2.3.1.6.49", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "50", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/tank/legacy", "storage_size": 536870912000, "storage_size_oid": null, "storage_units": 512, "storage_used": 240531058176, "storage_used_oid": ".*******.2.1.25.2.3.1.6.50", "storage_free": 296339853824, "storage_free_oid": null, "storage_perc": 45, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "51", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system", "storage_size": 94529891926016, "storage_size_oid": null, "storage_units": 65536, "storage_used": 567279616, "storage_used_oid": ".*******.2.1.25.2.3.1.6.51", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "52", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/cores", "storage_size": 94529334476800, "storage_size_oid": null, "storage_units": 65536, "storage_used": 9830400, "storage_used_oid": ".*******.2.1.25.2.3.1.6.52", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "53", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/samba4", "storage_size": 94529326022656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 1376256, "storage_used_oid": ".*******.2.1.25.2.3.1.6.53", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "54", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/syslog-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529440710656, "storage_size_oid": null, "storage_units": 65536, "storage_used": 116064256, "storage_used_oid": ".*******.2.1.25.2.3.1.6.54", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "55", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/rrd-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529396670464, "storage_size_oid": null, "storage_units": 65536, "storage_used": 71958528, "storage_used_oid": ".*******.2.1.25.2.3.1.6.55", "storage_free": 94529324711936, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "56", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/configs-0b74ab5d13d54dd2bc769d08fac9e2af", "storage_size": 94529461288960, "storage_size_oid": null, "storage_units": 65536, "storage_used": 136642560, "storage_used_oid": ".*******.2.1.25.2.3.1.6.56", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "57", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/webui", "storage_size": 94529324777472, "storage_size_oid": null, "storage_units": 65536, "storage_used": 131072, "storage_used_oid": ".*******.2.1.25.2.3.1.6.57", "storage_free": 94529324646400, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}]}}}