{"applications": {"discovery": {"applications": [{"app_type": "fbsd-nfs-server", "app_state": "UNKNOWN", "discovered": 1, "app_state_prev": null, "app_status": "", "app_instance": "", "data": null, "deleted_at": null}]}, "poller": {"applications": [{"app_type": "fbsd-nfs-server", "app_state": "OK", "discovered": 1, "app_state_prev": "UNKNOWN", "app_status": "", "app_instance": "", "data": null, "deleted_at": null}], "application_metrics": [{"metric": "Access", "value": 2756963, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Commit", "value": 226823, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Create", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Faults", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Fsinfo", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Fsstat", "value": 108431, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Getattr", "value": 2131314, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Idem", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Inprog", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Link", "value": 1250, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Lookup", "value": 6062252, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Misses", "value": 20162662, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Mkdir", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Mknod", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Nonidem", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Opsaved", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "PathConf", "value": 12, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "RdirPlus", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Read", "value": 8043676, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Readdir", "value": 221801, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Readlink", "value": 394, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Remove", "value": 32375, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "<PERSON><PERSON>", "value": 31097, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "RetFailed", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Rmdir", "value": 0, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Setattr", "value": 43045, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Symlink", "value": 24, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "Write", "value": 464478, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "WriteOps", "value": 464478, "value_prev": null, "app_type": "fbsd-nfs-server"}, {"metric": "WriteRPC", "value": 464478, "value_prev": null, "app_type": "fbsd-nfs-server"}]}}, "os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".1.3.6.1.4.1.8072.3.2.10", "sysDescr": "Linux server 3.10.0-693.5.2.el7.x86_64 #1 SMP Fri Oct 20 20:32:50 UTC 2017 x86_64", "sysContact": "<private>", "version": "3.10.0-693.5.2.el7.x86_64", "hardware": "Generic x86 64-bit", "features": null, "location": "<private>", "os": "linux", "type": "server", "serial": null, "icon": "linux.svg"}]}, "poller": "matches discovery"}}