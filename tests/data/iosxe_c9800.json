{"entity-physical": {"discovery": {"entPhysical": [{"entPhysicalIndex": 1, "entPhysicalDescr": "Cisco C9800 Dual Chassis", "entPhysicalClass": "stack", "entPhysicalName": "Multi Chassis System", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevHAC9800D<PERSON><PERSON><PERSON><PERSON>", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 2, "entPhysicalDescr": "Cisco C9800-L-F-K9 <PERSON><PERSON>s", "entPhysicalClass": "chassis", "entPhysicalName": "Chassis 1", "entPhysicalHardwareRev": "01", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "C9800-L-F-K9", "entPhysicalVendorType": "cevChassisC9800LFK9", "entPhysicalSerialNum": "<private>", "entPhysicalContainedIn": 1, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Cisco Systems Inc", "ifIndex": null}, {"entPhysicalIndex": 3, "entPhysicalDescr": "Power Supply Bay", "entPhysicalClass": "container", "entPhysicalName": "Chassis 1 Power Supply Bay 0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevContainerASR1000PowerSupplyBay", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 4, "entPhysicalDescr": "Cisco Catalyst Wireless Controller 12V DC Generic Power Supply", "entPhysicalClass": "powerSupply", "entPhysicalName": "Chassis 1 Power Supply Module 0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "PWR-12V", "entPhysicalVendorType": "cevPowerSupplyC9800LDC12V", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 3, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "Cisco Systems Inc", "ifIndex": null}, {"entPhysicalIndex": 14, "entPhysicalDescr": "Power Supply", "entPhysicalClass": "powerSupply", "entPhysicalName": "Chassis 1 Power Supply 0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPowerSupplyASR1000PS", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 4, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 24, "entPhysicalDescr": "Cisco C9800-L-F-K9 Fan Tray", "entPhysicalClass": "fan", "entPhysicalName": "Chassis 1 Fan Tray", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "C9800-L-F-K9-FAN", "entPhysicalVendorType": "cevFanC9800LFK9FanTray", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 35, "entPhysicalDescr": "Fan", "entPhysicalClass": "fan", "entPhysicalName": "Chassis 1 Fan 1/0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevFanASR1000Fan", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 24, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 36, "entPhysicalDescr": "Fan", "entPhysicalClass": "fan", "entPhysicalName": "Chassis 1 Fan 1/1", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevFanASR1000Fan", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 24, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 1000, "entPhysicalDescr": "Cisco C9800-L-F-K9 Modular Interface Processor", "entPhysicalClass": "module", "entPhysicalName": "module 0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "16.12(3r)", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "C9800-L-F-K9", "entPhysicalVendorType": "cevModuleC9800LFK9MIP", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "Cisco Systems Inc", "ifIndex": null}, {"entPhysicalIndex": 1040, "entPhysicalDescr": "Front Panel bay-0 4 ports 2.5 Gigabitethernet Module", "entPhysicalClass": "module", "entPhysicalName": "SPA subslot 0/0", "entPhysicalHardwareRev": "V01", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "N/A", "entPhysicalAssetID": "N/A", "entPhysicalIsFRU": "false", "entPhysicalModelName": "BUILT-IN-4x2_5GE", "entPhysicalVendorType": "cevModuleASR1000Type.104", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 1000, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "CISCO", "ifIndex": null}, {"entPhysicalIndex": 1041, "entPhysicalDescr": "BUILT-IN-4x2_5GE", "entPhysicalClass": "port", "entPhysicalName": "TwoGigabitEthernet0/0/0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPortTwoGigEthernet", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1040, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": 1}, {"entPhysicalIndex": 1042, "entPhysicalDescr": "BUILT-IN-4x2_5GE", "entPhysicalClass": "port", "entPhysicalName": "TwoGigabitEthernet0/0/1", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPortTwoGigEthernet", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1040, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": 2}, {"entPhysicalIndex": 1043, "entPhysicalDescr": "BUILT-IN-4x2_5GE", "entPhysicalClass": "port", "entPhysicalName": "TwoGigabitEthernet0/0/2", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPortTwoGigEthernet", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1040, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": 3}, {"entPhysicalIndex": 1044, "entPhysicalDescr": "BUILT-IN-4x2_5GE", "entPhysicalClass": "port", "entPhysicalName": "TwoGigabitEthernet0/0/3", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPortTwoGigEthernet", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1040, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "", "ifIndex": 4}, {"entPhysicalIndex": 1280, "entPhysicalDescr": "Front Panel bay-1 2 ports Ten/Gigabitethernet Module", "entPhysicalClass": "module", "entPhysicalName": "SPA subslot 0/1", "entPhysicalHardwareRev": "V01", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "N/A", "entPhysicalAssetID": "N/A", "entPhysicalIsFRU": "false", "entPhysicalModelName": "BUILT-IN-2x10GE-F", "entPhysicalVendorType": "cevModuleASR1000Type.106", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 1000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "CISCO", "ifIndex": null}, {"entPhysicalIndex": 1331, "entPhysicalDescr": "subslot 0/1 transceiver container 0", "entPhysicalClass": "container", "entPhysicalName": "subslot 0/1 transceiver container 0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevContainerSFP", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1280, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 1332, "entPhysicalDescr": "10GE CU3M", "entPhysicalClass": "module", "entPhysicalName": "subslot 0/1 transceiver 0", "entPhysicalHardwareRev": "V03", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "SFP-H10GB-CU3M", "entPhysicalVendorType": "cevSFPH10GBCU3M", "entPhysicalSerialNum": "MPH2624A287", "entPhysicalContainedIn": 1331, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "CISCO-MOLEX", "ifIndex": null}, {"entPhysicalIndex": 1333, "entPhysicalDescr": "BUILT-IN-2x10GE-F", "entPhysicalClass": "port", "entPhysicalName": "TenGigabitEthernet0/1/0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPort10GigSFPPlus", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1332, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": 5}, {"entPhysicalIndex": 1343, "entPhysicalDescr": "subslot 0/1 transceiver container 1", "entPhysicalClass": "container", "entPhysicalName": "subslot 0/1 transceiver container 1", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevContainerSFP", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1280, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 1344, "entPhysicalDescr": "10GE CU3M", "entPhysicalClass": "module", "entPhysicalName": "subslot 0/1 transceiver 1", "entPhysicalHardwareRev": "V03", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "SFP-H10GB-CU3M", "entPhysicalVendorType": "cevSFPH10GBCU3M", "entPhysicalSerialNum": "MPH2624A285", "entPhysicalContainedIn": 1343, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "CISCO-MOLEX", "ifIndex": null}, {"entPhysicalIndex": 1345, "entPhysicalDescr": "BUILT-IN-2x10GE-F", "entPhysicalClass": "port", "entPhysicalName": "TenGigabitEthernet0/1/1", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPort10GigSFPPlus", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1344, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": 6}, {"entPhysicalIndex": 2000, "entPhysicalDescr": "Cisco C9800-L-F-K9 Route Processor", "entPhysicalClass": "module", "entPhysicalName": "module R0", "entPhysicalHardwareRev": "01", "entPhysicalFirmwareRev": "16.12(3r)", "entPhysicalSoftwareRev": "17.09.04", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "C9800-L-F-K9", "entPhysicalVendorType": "cevModuleC9800LFK9RP", "entPhysicalSerialNum": "<private>", "entPhysicalContainedIn": 2, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Cisco Systems Inc", "ifIndex": null}, {"entPhysicalIndex": 2001, "entPhysicalDescr": "Temp: BRDTEMP1", "entPhysicalClass": "sensor", "entPhysicalName": "Temp: BRDTEMP1 R0/0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevSensorModuleDeviceTemp", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2000, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 2002, "entPhysicalDescr": "Temp: BRDTEMP2", "entPhysicalClass": "sensor", "entPhysicalName": "Temp: BRDTEMP2 R0/1", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevSensorModuleDeviceTemp", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 2003, "entPhysicalDescr": "Temp: CPU Die", "entPhysicalClass": "sensor", "entPhysicalName": "Temp: CPU Die  R0/2", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevSensorModuleDeviceTemp", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 2101, "entPhysicalDescr": "CPU 0 of module R0", "entPhysicalClass": "cpu", "entPhysicalName": "cpu R0/0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevModuleCpuType", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2000, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 2102, "entPhysicalDescr": "USB Port", "entPhysicalClass": "container", "entPhysicalName": "usb R0/0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevContainerUSB", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2000, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 2104, "entPhysicalDescr": "Network Management Ethernet", "entPhysicalClass": "port", "entPhysicalName": "NME R0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevPortGe", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 3000, "entPhysicalDescr": "Cisco C9800-L-F-K9 Embedded Services Processor", "entPhysicalClass": "module", "entPhysicalName": "module F0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "16.12(3r)", "entPhysicalSoftwareRev": "17.09.04", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "C9800-L-F-K9", "entPhysicalVendorType": "cevModuleC9800LFK9ESP", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 2, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Cisco Systems Inc", "ifIndex": null}, {"entPhysicalIndex": 3026, "entPhysicalDescr": "QFP 0 of module F0", "entPhysicalClass": "cpu", "entPhysicalName": "qfp F0/0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "cevModuleCpuType", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 3000, "entPhysicalParentRelPos": 0, "entPhysicalMfgName": "Cisco Systems Inc", "ifIndex": null}]}, "poller": "matches discovery"}, "transceivers": {"discovery": {"transceivers": [{"index": "1332", "entity_physical_index": 1332, "type": "10GE CU3M", "vendor": "CISCO-MOLEX", "oui": null, "model": "SFP-H10GB-CU3M", "revision": "V03", "serial": "MPH2624A287", "date": null, "ddm": null, "encoding": null, "cable": null, "distance": null, "wavelength": null, "connector": null, "channels": 1, "ifIndex": 5}, {"index": "1344", "entity_physical_index": 1344, "type": "10GE CU3M", "vendor": "CISCO-MOLEX", "oui": null, "model": "SFP-H10GB-CU3M", "revision": "V03", "serial": "MPH2624A285", "date": null, "ddm": null, "encoding": null, "cable": null, "distance": null, "wavelength": null, "connector": null, "channels": 1, "ifIndex": 6}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.*********.1.2.4", "sensor_index": "4", "sensor_type": "cefcFRUPowerOperStatus", "sensor_descr": "Chassis 1 Power Supply Module 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cefcFRUPowerOperStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.24", "sensor_index": "24", "sensor_type": "ciscoEnvMonFanState", "sensor_descr": "Chassis 1 Fan Tray", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "24", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonFanState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.35", "sensor_index": "35", "sensor_type": "ciscoEnvMonFanState", "sensor_descr": "Chassis 1 Fan 1/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonFanState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.36", "sensor_index": "36", "sensor_type": "ciscoEnvMonFanState", "sensor_descr": "Chassis 1 Fan 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "36", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonFanState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.14", "sensor_index": "14", "sensor_type": "ciscoEnvMonSupplyState", "sensor_descr": "Chassis 1 Power Supply 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "14", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonSupplyState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.4", "sensor_index": "4", "sensor_type": "ciscoEnvMonSupplyState", "sensor_descr": "Chassis 1 Power Supply Module 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonSupplyState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.6.2001", "sensor_index": "2001", "sensor_type": "ciscoEnvMonTemperatureState", "sensor_descr": "Temp: BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonTemperatureState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.6.2002", "sensor_index": "2002", "sensor_type": "ciscoEnvMonTemperatureState", "sensor_descr": "Temp: BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonTemperatureState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.6.2003", "sensor_index": "2003", "sensor_type": "ciscoEnvMonTemperatureState", "sensor_descr": "Temp: CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonTemperatureState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.**********.0", "sensor_index": "0", "sensor_type": "cRFCfgRedundancyOperMode", "sensor_descr": "VSS Mode", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "0", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cRFCfgRedundancyOperMode"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.*********.0", "sensor_index": "0", "sensor_type": "cRFStatusPeerUnitState", "sensor_descr": "VSS Peer State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "0", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cRFStatusPeerUnitState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.*********.0", "sensor_index": "0", "sensor_type": "cRFStatusUnitState", "sensor_descr": "VSS Device State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 14, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "0", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cRFStatusUnitState"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.2001", "sensor_index": "2001", "sensor_type": "cisco", "sensor_descr": "Temp: BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.2002", "sensor_index": "2002", "sensor_type": "cisco", "sensor_descr": "Temp: BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 64, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.2003", "sensor_index": "2003", "sensor_type": "cisco", "sensor_descr": "Temp: CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": 104, "sensor_limit_warn": null, "sensor_limit_low": 44, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.1.4.2001", "sensor_index": "2001", "sensor_type": "cisco-entity-sensor", "sensor_descr": "Module R0 - BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 57, "sensor_limit_warn": 54, "sensor_limit_low": -5, "sensor_limit_low_warn": -1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": "2000", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.1.4.2002", "sensor_index": "2002", "sensor_type": "cisco-entity-sensor", "sensor_descr": "Module R0 - BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 61, "sensor_limit_warn": 58, "sensor_limit_low": -5, "sensor_limit_low_warn": -1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": "2000", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.1.4.2003", "sensor_index": "2003", "sensor_type": "cisco-entity-sensor", "sensor_descr": "Module R0 - CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": 99, "sensor_limit_warn": 94, "sensor_limit_low": -5, "sensor_limit_low_warn": -1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": "2000", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.1.4.2001", "sensor_index": "2001", "sensor_type": "entity-sensor", "sensor_descr": "Temp: BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 59, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.1.4.2002", "sensor_index": "2002", "sensor_type": "entity-sensor", "sensor_descr": "Temp: BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.1.4.2003", "sensor_index": "2003", "sensor_type": "entity-sensor", "sensor_descr": "Temp: CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 55, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (other)", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "on", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (admin)", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (denied)", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (environmental)", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (temperature)", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (fan)", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "on (fan failed)", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 1}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (cooling)", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (connector rating)", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "on (no inline power)", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 1}, {"state_name": "ciscoEnvMonFanState", "state_descr": "normal", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "ciscoEnvMonFanState", "state_descr": "warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 1}, {"state_name": "ciscoEnvMonFanState", "state_descr": "critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "ciscoEnvMonFanState", "state_descr": "shutdown", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 3}, {"state_name": "ciscoEnvMonFanState", "state_descr": "notPresent", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 3}, {"state_name": "ciscoEnvMonFanState", "state_descr": "notFunctioning", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "normal", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 1}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "shutdown", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 3}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "notPresent", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 3}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "notFunctioning", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "normal", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 1}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "shutdown", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 3}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "notPresent", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 3}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "notFunctioning", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "nonRedundant", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "staticLoadShareNonRedundant", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "dynamicLoadShareNonRedundant", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "staticLoadShareRedundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "dynamicLoadShareRedundant", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "coldStandbyRedundant", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "warmStandbyRedundant", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "hotStandbyRedundant", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "notKnown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "disabled", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "initialization", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "negotiation", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyCold", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyColdConfig", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyColdFileSys", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyColdBulk", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyHot", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeFast", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeDrain", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activePreconfig", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activePostconfig", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "active", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeExtraload", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeHandback", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "notKnown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "cRFStatusUnitState", "state_descr": "disabled", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cRFStatusUnitState", "state_descr": "initialization", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "negotiation", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyCold", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyColdConfig", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyColdFileSys", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyColdBulk", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyHot", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "cRFStatusUnitState", "state_descr": "activeFast", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activeDrain", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activePreconfig", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activePostconfig", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "active", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "cRFStatusUnitState", "state_descr": "activeExtraload", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activeHandback", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 1}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.*********.1.2.4", "sensor_index": "4", "sensor_type": "cefcFRUPowerOperStatus", "sensor_descr": "Chassis 1 Power Supply Module 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cefcFRUPowerOperStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.24", "sensor_index": "24", "sensor_type": "ciscoEnvMonFanState", "sensor_descr": "Chassis 1 Fan Tray", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "24", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonFanState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.35", "sensor_index": "35", "sensor_type": "ciscoEnvMonFanState", "sensor_descr": "Chassis 1 Fan 1/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonFanState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.36", "sensor_index": "36", "sensor_type": "ciscoEnvMonFanState", "sensor_descr": "Chassis 1 Fan 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "36", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonFanState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.14", "sensor_index": "14", "sensor_type": "ciscoEnvMonSupplyState", "sensor_descr": "Chassis 1 Power Supply 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "14", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonSupplyState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.4", "sensor_index": "4", "sensor_type": "ciscoEnvMonSupplyState", "sensor_descr": "Chassis 1 Power Supply Module 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonSupplyState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.6.2001", "sensor_index": "2001", "sensor_type": "ciscoEnvMonTemperatureState", "sensor_descr": "Temp: BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonTemperatureState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.6.2002", "sensor_index": "2002", "sensor_type": "ciscoEnvMonTemperatureState", "sensor_descr": "Temp: BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonTemperatureState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.6.2003", "sensor_index": "2003", "sensor_type": "ciscoEnvMonTemperatureState", "sensor_descr": "Temp: CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "ciscoEnvMonTemperatureState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.**********.0", "sensor_index": "0", "sensor_type": "cRFCfgRedundancyOperMode", "sensor_descr": "VSS Mode", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "0", "entPhysicalIndex_measured": null, "sensor_prev": 1, "user_func": null, "rrd_type": "GAUGE", "state_name": "cRFCfgRedundancyOperMode"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.*********.0", "sensor_index": "0", "sensor_type": "cRFStatusPeerUnitState", "sensor_descr": "VSS Peer State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "0", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cRFStatusPeerUnitState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*******.*********.0", "sensor_index": "0", "sensor_type": "cRFStatusUnitState", "sensor_descr": "VSS Device State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 14, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "0", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cRFStatusUnitState"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.2001", "sensor_index": "2001", "sensor_type": "cisco", "sensor_descr": "Temp: BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.2002", "sensor_index": "2002", "sensor_type": "cisco", "sensor_descr": "Temp: BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 64, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.3.2003", "sensor_index": "2003", "sensor_type": "cisco", "sensor_descr": "Temp: CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": 104, "sensor_limit_warn": null, "sensor_limit_low": 44, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.1.4.2001", "sensor_index": "2001", "sensor_type": "cisco-entity-sensor", "sensor_descr": "Module R0 - BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 57, "sensor_limit_warn": 54, "sensor_limit_low": -5, "sensor_limit_low_warn": -1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": "2000", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.1.4.2002", "sensor_index": "2002", "sensor_type": "cisco-entity-sensor", "sensor_descr": "Module R0 - BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 61, "sensor_limit_warn": 58, "sensor_limit_low": -5, "sensor_limit_low_warn": -1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": "2000", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*******.********.1.4.2003", "sensor_index": "2003", "sensor_type": "cisco-entity-sensor", "sensor_descr": "Module R0 - CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": 99, "sensor_limit_warn": 94, "sensor_limit_low": -5, "sensor_limit_low_warn": -1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": "2000", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.1.4.2001", "sensor_index": "2001", "sensor_type": "entity-sensor", "sensor_descr": "Temp: BRDTEMP1 R0/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 59, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2001", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.1.4.2002", "sensor_index": "2002", "sensor_type": "entity-sensor", "sensor_descr": "Temp: BRDTEMP2 R0/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2002", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.********.1.1.4.2003", "sensor_index": "2003", "sensor_type": "entity-sensor", "sensor_descr": "Temp: CPU Die  R0/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 55, "sensor_limit": 75, "sensor_limit_warn": null, "sensor_limit_low": 45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2003", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (other)", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "on", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (admin)", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (denied)", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (environmental)", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (temperature)", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (fan)", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "on (fan failed)", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 1}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (cooling)", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "off (connector rating)", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 2}, {"state_name": "cefcFRUPowerOperStatus", "state_descr": "on (no inline power)", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 1}, {"state_name": "ciscoEnvMonFanState", "state_descr": "normal", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "ciscoEnvMonFanState", "state_descr": "warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 1}, {"state_name": "ciscoEnvMonFanState", "state_descr": "critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "ciscoEnvMonFanState", "state_descr": "shutdown", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 3}, {"state_name": "ciscoEnvMonFanState", "state_descr": "notPresent", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 3}, {"state_name": "ciscoEnvMonFanState", "state_descr": "notFunctioning", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "normal", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 1}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "shutdown", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 3}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "notPresent", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 3}, {"state_name": "ciscoEnvMonSupplyState", "state_descr": "notFunctioning", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "normal", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "warning", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 1}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "shutdown", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 3}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "notPresent", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 3}, {"state_name": "ciscoEnvMonTemperatureState", "state_descr": "notFunctioning", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "nonRedundant", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "staticLoadShareNonRedundant", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "dynamicLoadShareNonRedundant", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "staticLoadShareRedundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "dynamicLoadShareRedundant", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "coldStandbyRedundant", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "warmStandbyRedundant", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "cRFCfgRedundancyOperMode", "state_descr": "hotStandbyRedundant", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "notKnown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "disabled", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "initialization", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "negotiation", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyCold", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyColdConfig", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyColdFileSys", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyColdBulk", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "standbyHot", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeFast", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeDrain", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activePreconfig", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activePostconfig", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "active", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeExtraload", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 1}, {"state_name": "cRFStatusPeerUnitState", "state_descr": "activeHandback", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "notKnown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "cRFStatusUnitState", "state_descr": "disabled", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "cRFStatusUnitState", "state_descr": "initialization", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "negotiation", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyCold", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyColdConfig", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyColdFileSys", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyColdBulk", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "standbyHot", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "cRFStatusUnitState", "state_descr": "activeFast", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activeDrain", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activePreconfig", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activePostconfig", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "active", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "cRFStatusUnitState", "state_descr": "activeExtraload", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 1}, {"state_name": "cRFStatusUnitState", "state_descr": "activeHandback", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 1}]}}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/0", "ifName": "Tw0/0/0", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/1", "ifName": "Tw0/0/1", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/2", "ifName": "Tw0/0/2", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/3", "ifName": "Tw0/0/3", "portName": null, "ifIndex": 4, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/3", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TenGigabitEthernet0/1/0", "ifName": "Te0/1/0", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "***TO MAR-COR-SW-1 Ten 2/0/9***", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TenGigabitEthernet0/1/1", "ifName": "Te0/1/1", "portName": null, "ifIndex": 6, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "***TO MAR-COR-SW-1 Ten 1/0/9***", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "GigabitEthernet0", "ifName": "Gi0", "portName": null, "ifIndex": 7, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "GigabitEthernet0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Null0", "ifName": "Nu0", "portName": null, "ifIndex": 9, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "Null0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Vlan1", "ifName": "Vl1", "portName": null, "ifIndex": 10, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "propVirtual", "ifAlias": "Vlan1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Vlan52", "ifName": "Vl52", "portName": null, "ifIndex": 11, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "propVirtual", "ifAlias": "Vlan52", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Port-channel9", "ifName": "Po9", "portName": null, "ifIndex": 12, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "propVirtual", "ifAlias": "***TO MAR-COR-SW-1***", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/0", "ifName": "Tw0/0/0", "portName": null, "ifIndex": 1, "ifSpeed": 2500000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/0", "ifPhysAddress": "8c1e806f236c", "ifLastChange": 2155, "ifVlan": "1", "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/1", "ifName": "Tw0/0/1", "portName": null, "ifIndex": 2, "ifSpeed": 2500000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/1", "ifPhysAddress": "8c1e806f236d", "ifLastChange": 2164, "ifVlan": "1", "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/2", "ifName": "Tw0/0/2", "portName": null, "ifIndex": 3, "ifSpeed": 2500000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/2", "ifPhysAddress": "8c1e806f236e", "ifLastChange": 2164, "ifVlan": "1", "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TwoGigabitEthernet0/0/3", "ifName": "Tw0/0/3", "portName": null, "ifIndex": 4, "ifSpeed": 2500000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "TwoGigabitEthernet0/0/3", "ifPhysAddress": "8c1e806f236f", "ifLastChange": 2165, "ifVlan": "1", "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TenGigabitEthernet0/1/0", "ifName": "Te0/1/0", "portName": null, "ifIndex": 5, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "***TO MAR-COR-SW-1 Ten 2/0/9***", "ifPhysAddress": "8c1e806f2370", "ifLastChange": 5142, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 4671317769, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 8618094314, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 2354465580600, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 7121430672619, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "TenGigabitEthernet0/1/1", "ifName": "Te0/1/1", "portName": null, "ifIndex": 6, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "***TO MAR-COR-SW-1 Ten 1/0/9***", "ifPhysAddress": "8c1e806f2371", "ifLastChange": 5365, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 9021399224, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 5319195344, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 8236068738117, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": *************, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "GigabitEthernet0", "ifName": "Gi0", "portName": null, "ifIndex": 7, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "GigabitEthernet0", "ifPhysAddress": "8c1e806f2361", "ifLastChange": 2215, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Null0", "ifName": "Nu0", "portName": null, "ifIndex": 9, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "other", "ifAlias": "Null0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Vlan1", "ifName": "Vl1", "portName": null, "ifIndex": 10, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "propVirtual", "ifAlias": "Vlan1", "ifPhysAddress": "8c1e806f236b", "ifLastChange": 2219, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Vlan52", "ifName": "Vl52", "portName": null, "ifIndex": 11, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "propVirtual", "ifAlias": "Vlan52", "ifPhysAddress": "8c1e806f236b", "ifLastChange": 5342, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 3423380520, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 7082622858, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 1577047576050, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 7391100678772, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Port-channel9", "ifName": "Po9", "portName": null, "ifIndex": 12, "ifSpeed": 20000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "propVirtual", "ifAlias": "***TO MAR-COR-SW-1***", "ifPhysAddress": "8c1e806f2370", "ifLastChange": 5341, "ifVlan": "52", "ifTrunk": "dot1Q", "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 13692716993, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 13937289658, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 10590534318717, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 11685481313233, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "ap-count", "sensor_index": "0", "sensor_type": "ciscowlc", "sensor_descr": "Connected APs", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 55, "sensor_prev": null, "sensor_limit": 250, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.*******.513.********\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "0", "sensor_type": "ciscowlc", "sensor_descr": "Clients: Total", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 41, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.1\",\".*******.4.1.14179.*******.38.2\",\".*******.4.1.14179.*******.38.3\",\".*******.4.1.14179.*******.38.4\",\".*******.4.1.14179.*******.38.5\",\".*******.4.1.14179.*******.38.6\",\".*******.4.1.14179.*******.38.7\",\".*******.4.1.14179.*******.38.8\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "1", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: <private>", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 5, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.1\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "2", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NAI-Mobile", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 7, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.2\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "3", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NAI-IT", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.3\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "4", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NA Guest", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.4\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "5", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: SAP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 7, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.5\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "6", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: TIME", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 9, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.6\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "7", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: OTN", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 12, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.7\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "8", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NAI-TPE", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 1, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.8\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "ap-count", "sensor_index": "0", "sensor_type": "ciscowlc", "sensor_descr": "Connected APs", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 55, "sensor_prev": 55, "sensor_limit": 250, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.*******.513.********\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "0", "sensor_type": "ciscowlc", "sensor_descr": "Clients: Total", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 41, "sensor_prev": 41, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.1\",\".*******.4.1.14179.*******.38.2\",\".*******.4.1.14179.*******.38.3\",\".*******.4.1.14179.*******.38.4\",\".*******.4.1.14179.*******.38.5\",\".*******.4.1.14179.*******.38.6\",\".*******.4.1.14179.*******.38.7\",\".*******.4.1.14179.*******.38.8\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "1", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: <private>", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 5, "sensor_prev": 5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.1\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "2", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NAI-Mobile", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 7, "sensor_prev": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.2\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "3", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NAI-IT", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.3\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "4", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NA Guest", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.4\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "5", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: SAP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 7, "sensor_prev": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.5\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "6", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: TIME", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 9, "sensor_prev": 9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.6\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "7", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: OTN", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 12, "sensor_prev": 12, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.7\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "8", "sensor_type": "ciscowlc-ssid", "sensor_descr": "SSID: NAI-TPE", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 1, "sensor_prev": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14179.*******.38.8\"]", "rrd_type": "GAUGE"}]}}, "os": {"discovery": {"devices": [{"sysName": "mar-wlc-c9800.int.nutriasia.com.ph", "sysObjectID": ".*******.*******.2861", "sysDescr": "Cisco IOS Software [Cupertino], C9800 Software (C9800_IOSXE-K9), Version 17.9.4, RELEASE SOFTWARE (fc5)", "sysContact": "<EMAIL>", "version": "C9800_IOSXE-K9 17.9.4", "hardware": "Multi Chassis System", "features": "Cupertino", "location": "<private>", "os": "iosxe", "type": "network", "serial": null, "icon": "cisco.svg"}]}, "poller": "matches discovery"}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.0", "processor_index": "5.0", "processor_type": "cpm", "processor_usage": 3, "processor_descr": "cpu R0/0: Core 0", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.1", "processor_index": "5.1", "processor_type": "cpm", "processor_usage": 3, "processor_descr": "cpu R0/0: Core 1", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.2", "processor_index": "5.2", "processor_type": "cpm", "processor_usage": 3, "processor_descr": "cpu R0/0: Core 2", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.3", "processor_index": "5.3", "processor_type": "cpm", "processor_usage": 2, "processor_descr": "cpu R0/0: Core 3", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.4", "processor_index": "5.4", "processor_type": "cpm", "processor_usage": 1, "processor_descr": "cpu R0/0: Core 4", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.5", "processor_index": "5.5", "processor_type": "cpm", "processor_usage": 4, "processor_descr": "cpu R0/0: Core 5", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.6", "processor_index": "5.6", "processor_type": "cpm", "processor_usage": 2, "processor_descr": "cpu R0/0: Core 6", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.7", "processor_index": "5.7", "processor_type": "cpm", "processor_usage": 100, "processor_descr": "cpu R0/0: Core 7", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.8", "processor_index": "5.8", "processor_type": "cpm", "processor_usage": 0, "processor_descr": "cpu R0/0: Core 8", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.9", "processor_index": "5.9", "processor_type": "cpm", "processor_usage": 0, "processor_descr": "cpu R0/0: Core 9", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 2101, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.*********.1.5.5.10", "processor_index": "5.10", "processor_type": "cpm", "processor_usage": 0, "processor_descr": "cpu R0/0: Core 10", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 3026, "hrDeviceIndex": 0, "processor_oid": ".*******.*******.715.1.1.6.1.14.3026.3", "processor_index": "3026.3", "processor_type": "qfp", "processor_usage": 0, "processor_descr": "qfp F0/0", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "2000.1", "entPhysicalIndex": 2000, "mempool_type": "cemp", "mempool_class": "system", "mempool_precision": 1, "mempool_descr": "Module R0 - Processor", "mempool_perc": 27, "mempool_perc_oid": null, "mempool_used": 453666736, "mempool_used_oid": ".*******.*******.221.1.1.1.1.18.2000.1", "mempool_free": 1220346668, "mempool_free_oid": ".*******.*******.221.1.1.1.1.20.2000.1", "mempool_total": 1674013404, "mempool_total_oid": null, "mempool_largestfree": 1100603640, "mempool_lowestfree": 1215240996, "mempool_deleted": 0, "mempool_perc_warn": 90}, {"mempool_index": "2000.2", "entPhysicalIndex": 2000, "mempool_type": "cemp", "mempool_class": "system", "mempool_precision": 1, "mempool_descr": "Module R0 - Reserve Processor", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": 88, "mempool_used_oid": ".*******.*******.221.1.1.1.1.18.2000.2", "mempool_free": 102316, "mempool_free_oid": ".*******.*******.221.1.1.1.1.20.2000.2", "mempool_total": 102404, "mempool_total_oid": null, "mempool_largestfree": 102312, "mempool_lowestfree": 102316, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "vlans": {"discovery": {"vlans": [{"vlan_vlan": 1, "vlan_domain": 1, "vlan_name": "default", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 5, "vlan_domain": 1, "vlan_name": "ISE_NAI_WIFI", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 52, "vlan_domain": 1, "vlan_name": "ISE_NETWORK_MGMT", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 72, "vlan_domain": 1, "vlan_name": "MAR_NAI_MOBILE", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 176, "vlan_domain": 1, "vlan_name": "ISE_MAR_HR", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 177, "vlan_domain": 1, "vlan_name": "ISE_MAR_R&D", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 178, "vlan_domain": 1, "vlan_name": "ISE_MAR_<PERSON><PERSON><PERSON><PERSON>n", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 179, "vlan_domain": 1, "vlan_name": "ISE_MAR_TQM", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 180, "vlan_domain": 1, "vlan_name": "ISE_MAR_<PERSON><PERSON><PERSON><PERSON>", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 181, "vlan_domain": 1, "vlan_name": "ISE_MAR_CPG", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 182, "vlan_domain": 1, "vlan_name": "ISE_MAR_Finance", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 183, "vlan_domain": 1, "vlan_name": "MAR_SAP_IOT", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 184, "vlan_domain": 1, "vlan_name": "ISE_BGC_IT", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 186, "vlan_domain": 1, "vlan_name": "ISE_ClinicUsers", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 187, "vlan_domain": 1, "vlan_name": "MAR_SAP_USER", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 191, "vlan_domain": 1, "vlan_name": "MAR_TimeSync", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 193, "vlan_domain": 1, "vlan_name": "MAR_OT_WIRELESS", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 200, "vlan_domain": 1, "vlan_name": "ISE_WIFI_GUEST", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 205, "vlan_domain": 1, "vlan_name": "ISE_TPE", "vlan_type": "ethernet", "vlan_mtu": null}, {"vlan_vlan": 1002, "vlan_domain": 1, "vlan_name": "fddi-default", "vlan_type": "fddi", "vlan_mtu": null}, {"vlan_vlan": 1003, "vlan_domain": 1, "vlan_name": "token-ring-default", "vlan_type": "tokenRing", "vlan_mtu": null}, {"vlan_vlan": 1004, "vlan_domain": 1, "vlan_name": "fddinet-default", "vlan_type": "fddiNet", "vlan_mtu": null}, {"vlan_vlan": 1005, "vlan_domain": 1, "vlan_name": "trnet-default", "vlan_type": "trNet", "vlan_mtu": null}], "ports_vlans": [{"vlan": 1, "baseport": 0, "priority": 0, "state": "unknown", "cost": 0, "untagged": 1}, {"vlan": 1, "baseport": 0, "priority": 0, "state": "unknown", "cost": 0, "untagged": 1}, {"vlan": 1, "baseport": 0, "priority": 0, "state": "unknown", "cost": 0, "untagged": 1}, {"vlan": 1, "baseport": 0, "priority": 0, "state": "unknown", "cost": 0, "untagged": 1}, {"vlan": 52, "baseport": 0, "priority": 0, "state": "unknown", "cost": 0, "untagged": 1}, {"vlan": 52, "baseport": 0, "priority": 0, "state": "unknown", "cost": 0, "untagged": 1}, {"vlan": 52, "baseport": 0, "priority": 0, "state": "unknown", "cost": 0, "untagged": 1}]}}, "ports-stack": {"discovery": {"ports_stack": [{"high_ifIndex": 5, "low_ifIndex": 12, "ifStackStatus": "active"}, {"high_ifIndex": 6, "low_ifIndex": 12, "ifStackStatus": "active"}]}}, "vrf": {"discovery": {"vrfs": [{"vrf_oid": "************.**************.116.102", "vrf_name": "Mgmt-intf", "bgpLocalAs": null, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": "7"}]}}, "qos": {"discovery": {"qos": [{"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "18.65536", "rrd_id": "port-1-cbqos-18-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "34.65536", "rrd_id": "port-2-cbqos-34-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "50.65536", "rrd_id": "port-3-cbqos-50-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "66.65536", "rrd_id": "port-4-cbqos-66-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "82.65536", "rrd_id": "port-5-cbqos-82-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "98.65536", "rrd_id": "port-6-cbqos-98-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "18.131072", "rrd_id": "port-1-cbqos-18-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "34.131072", "rrd_id": "port-2-cbqos-34-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "50.131072", "rrd_id": "port-3-cbqos-50-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "66.131072", "rrd_id": "port-4-cbqos-66-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "82.131072", "rrd_id": "port-5-cbqos-82-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "98.131072", "rrd_id": "port-6-cbqos-98-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "18.1", "rrd_id": "cbqos-policymap-18-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "34.1", "rrd_id": "cbqos-policymap-34-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "50.1", "rrd_id": "cbqos-policymap-50-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "66.1", "rrd_id": "cbqos-policymap-66-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "82.1", "rrd_id": "cbqos-policymap-82-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "98.1", "rrd_id": "cbqos-policymap-98-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "18.196608", "rrd_id": "port-1-cbqos-18-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "34.196608", "rrd_id": "port-2-cbqos-34-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "50.196608", "rrd_id": "port-3-cbqos-50-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "66.196608", "rrd_id": "port-4-cbqos-66-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "82.196608", "rrd_id": "port-5-cbqos-82-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "98.196608", "rrd_id": "port-6-cbqos-98-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}]}, "poller": {"qos": [{"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "18.65536", "rrd_id": "port-1-cbqos-18-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "34.65536", "rrd_id": "port-2-cbqos-34-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "50.65536", "rrd_id": "port-3-cbqos-50-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "66.65536", "rrd_id": "port-4-cbqos-66-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "82.65536", "rrd_id": "port-5-cbqos-82-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 3067940392, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 24268853, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-CAPWAP-C-Class", "tooltip": "Match-Any:\n - Match access-group name AutoQos-4.0-Output-Acl-CAPWAP-C", "snmp_idx": "98.65536", "rrd_id": "port-6-cbqos-98-65536", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 3437671986, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 26396279, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "18.131072", "rrd_id": "port-1-cbqos-18-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "34.131072", "rrd_id": "port-2-cbqos-34-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "50.131072", "rrd_id": "port-3-cbqos-50-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "66.131072", "rrd_id": "port-4-cbqos-66-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "82.131072", "rrd_id": "port-5-cbqos-82-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 12784897674, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 30041645, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "AutoQos-4.0-Output-Voice-Class", "tooltip": "Match-Any:\n - Match  dscp ef (46)", "snmp_idx": "98.131072", "rrd_id": "port-6-cbqos-98-131072", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 13839952708, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 32496088, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "18.1", "rrd_id": "cbqos-policymap-18-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "34.1", "rrd_id": "cbqos-policymap-34-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "50.1", "rrd_id": "cbqos-policymap-50-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "66.1", "rrd_id": "cbqos-policymap-66-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "82.1", "rrd_id": "cbqos-policymap-82-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_policymap", "title": "AutoQos-4.0-wlan-Port-Output-Policy", "tooltip": null, "snmp_idx": "98.1", "rrd_id": "cbqos-policymap-98-1", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": null, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": null, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": null, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": null, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "18.196608", "rrd_id": "port-1-cbqos-18-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "34.196608", "rrd_id": "port-2-cbqos-34-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "50.196608", "rrd_id": "port-3-cbqos-50-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "66.196608", "rrd_id": "port-4-cbqos-66-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 0, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 0, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "82.196608", "rrd_id": "port-5-cbqos-82-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 4808025091058, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 5762314063, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}, {"type": "cisco_cbqos_classmap", "title": "class-default", "tooltip": "Match-Any:\n - Match any", "snmp_idx": "98.196608", "rrd_id": "port-6-cbqos-98-196608", "ingress": 0, "egress": 1, "disabled": 0, "ignore": 0, "max_in": null, "max_out": null, "last_bytes_in": null, "last_bytes_out": 3813483896917, "bytes_in_rate": null, "bytes_out_rate": null, "last_bytes_drop_in": null, "last_bytes_drop_out": 0, "bytes_drop_in_rate": null, "bytes_drop_out_rate": null, "last_packets_in": null, "last_packets_out": 4387860390, "packets_in_rate": null, "packets_out_rate": null, "last_packets_drop_in": null, "last_packets_drop_out": 0, "packets_drop_in_rate": null, "packets_drop_out_rate": null, "bytes_drop_in_pct": null, "bytes_drop_out_pct": null, "packets_drop_in_pct": null, "packets_drop_out_pct": null}]}}, "storage": {"discovery": {"storage": [{"type": "cisco-flash", "storage_index": "1.1", "storage_type": "FlashMemory", "storage_descr": "bootflash:", "storage_size": 26458804224, "storage_size_oid": null, "storage_units": 1, "storage_used": 9448710144, "storage_used_oid": null, "storage_free": 17010094080, "storage_free_oid": ".*******.*******.********.********.1", "storage_perc": 36, "storage_perc_oid": null, "storage_perc_warn": 60}]}, "poller": "matches discovery"}, "discovery-protocols": {"discovery": {"links": [{"active": 1, "protocol": "cdp", "remote_hostname": "MAR-COR-SW-1.int.nutriasia.com.ph", "remote_port": "TenGigabitEthernet1/0/9", "remote_platform": "cisco WS-C3850-12XS", "remote_version": "Cisco IOS Software [Gibraltar], Catalyst L3 Switch Software (CAT3K_CAA-UNIVERSALK9-M), Version 16.12.9, RELEASE SOFTWARE (fc2) Technical Support: http://www.cisco.com/techsupport Copyright (c) 1986-2023 by Cisco Systems, Inc. Compiled Mon 20-Mar-23 03:14", "ifAlias": "***TO MAR-COR-SW-1 Ten 1/0/9***", "ifDescr": "TenGigabitEthernet0/1/1", "ifName": "Te0/1/1"}, {"active": 1, "protocol": "cdp", "remote_hostname": "MAR-COR-SW-1.int.nutriasia.com.ph", "remote_port": "TenGigabitEthernet2/0/9", "remote_platform": "cisco WS-C3850-12XS", "remote_version": "Cisco IOS Software [Gibraltar], Catalyst L3 Switch Software (CAT3K_CAA-UNIVERSALK9-M), Version 16.12.9, RELEASE SOFTWARE (fc2) Technical Support: http://www.cisco.com/techsupport Copyright (c) 1986-2023 by Cisco Systems, Inc. Compiled Mon 20-Mar-23 03:14", "ifAlias": "***TO MAR-COR-SW-1 Ten 2/0/9***", "ifDescr": "TenGigabitEthernet0/1/0", "ifName": "Te0/1/0"}]}}}