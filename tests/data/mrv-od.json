{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.629.200.1.1.1", "sysDescr": "OD-NM OD-5.10.1 (32333) (Jul 13 2016 - 08:18:22), U-Boot 2013.01.02.16-00155-gdebf., Linux kernel v3.2.78-1-12777-g012a77d (#7 Wed Mar 9 15:29:57 PST 2016), OD-NM (firmware 20.16) (00:20:1a:06:08:3f)", "sysContact": "<private>", "version": "OD 5.2.1 (25684)", "hardware": "OD-16-HD", "features": null, "location": "<private>", "os": "mrv-od", "type": "network", "serial": "012345EMF8K0", "icon": "mrv.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "RJ-45 at 1.16.4", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port1", "ifName": "1.1.1", "portName": null, "ifIndex": 101001, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "converter-ifalias-port1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port2", "ifName": "1.1.2", "portName": null, "ifIndex": 101002, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "converter-ifalias-port2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port3", "ifName": "1.1.3", "portName": null, "ifIndex": 101003, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "converter-ifalias-port3", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port4", "ifName": "1.1.4", "portName": null, "ifIndex": 101004, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "converter-ifalias-port4", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "LC at 1.11.1", "ifName": "1.11.1", "portName": null, "ifIndex": 111001, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "LC at 1.11.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "LC at 1.11.2", "ifName": "1.11.2", "portName": null, "ifIndex": 111002, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "LC at 1.11.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.12.1", "ifName": "1.12.1", "portName": null, "ifIndex": 112001, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "FO_SC at 1.12.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.12.2", "ifName": "1.12.2", "portName": null, "ifIndex": 112002, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "FO_SC at 1.12.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.14.1", "ifName": "1.14.1", "portName": null, "ifIndex": 114001, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "FO_SC at 1.14.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.14.2", "ifName": "1.14.2", "portName": null, "ifIndex": 114002, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "FO_SC at 1.14.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "USB at 1.16.1", "ifName": "1.16.1", "portName": null, "ifIndex": 116001, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "USB at 1.16.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "SFP at 1.16.2", "ifName": "1.16.2", "portName": null, "ifIndex": 116002, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "SFP at 1.16.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "SFP at 1.16.3", "ifName": "1.16.3", "portName": null, "ifIndex": 116003, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "SFP at 1.16.3", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "RJ-45 at 1.16.4", "ifName": "1.16.4", "portName": null, "ifIndex": 116004, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "RJ-45 at 1.16.4", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 1, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "RJ-45 at 1.16.4", "ifPhysAddress": "00201a042a98", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 77451431, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 16585896, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 4294967295, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 3966386354, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 21303490, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port1", "ifName": "1.1.1", "portName": null, "ifIndex": 101001, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "converter-ifalias-port1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port2", "ifName": "1.1.2", "portName": null, "ifIndex": 101002, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "converter-ifalias-port2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port3", "ifName": "1.1.3", "portName": null, "ifIndex": 101003, "ifSpeed": 10300000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "converter-ifalias-port3", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "converter-ifalias-port4", "ifName": "1.1.4", "portName": null, "ifIndex": 101004, "ifSpeed": 10300000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "converter-ifalias-port4", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "LC at 1.11.1", "ifName": "1.11.1", "portName": null, "ifIndex": 111001, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "LC at 1.11.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "LC at 1.11.2", "ifName": "1.11.2", "portName": null, "ifIndex": 111002, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "LC at 1.11.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.12.1", "ifName": "1.12.1", "portName": null, "ifIndex": 112001, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "FO_SC at 1.12.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.12.2", "ifName": "1.12.2", "portName": null, "ifIndex": 112002, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "FO_SC at 1.12.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.14.1", "ifName": "1.14.1", "portName": null, "ifIndex": 114001, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "FO_SC at 1.14.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "FO_SC at 1.14.2", "ifName": "1.14.2", "portName": null, "ifIndex": 114002, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "FO_SC at 1.14.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "USB at 1.16.1", "ifName": "1.16.1", "portName": null, "ifIndex": 116001, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "USB at 1.16.1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "SFP at 1.16.2", "ifName": "1.16.2", "portName": null, "ifIndex": 116002, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "SFP at 1.16.2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "SFP at 1.16.3", "ifName": "1.16.3", "portName": null, "ifIndex": 116003, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "SFP at 1.16.3", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "RJ-45 at 1.16.4", "ifName": "1.16.4", "portName": null, "ifIndex": 116004, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "other", "ifAlias": "RJ-45 at 1.16.4", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 15938955, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 16243796, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 6222404031, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 4904125517, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 18786793, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 10, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 47598792, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 11339793, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortBiasAmps.1.1.3", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.3 Tx Bias Current", "group": null, "sensor_divisor": 1000000, "sensor_multiplier": 1, "sensor_current": 0.069984, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100333", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortBiasAmps.1.1.4", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.4 Tx Bias Current", "group": null, "sensor_divisor": 1000000, "sensor_multiplier": 1, "sensor_current": 0.033212, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100433", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortRxPower.1.1.3", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.3 Rx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": -16.798, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100332", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortRxPower.1.1.4", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.4 Rx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 0.062, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100432", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortRxPower.1.12.2", "sensor_type": "mrv-od", "sensor_descr": "Port 1.12.2 Rx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 6.26, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11200232", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortRxPower.1.14.2", "sensor_type": "mrv-od", "sensor_descr": "Port 1.14.2 Rx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": -14.03, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11400232", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortTxPower.1.1.3", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.3 Tx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 1.552, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100331", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortTxPower.1.1.4", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.4 Tx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": -2.413, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100431", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortTxPower.1.12.1", "sensor_type": "mrv-od", "sensor_descr": "Port 1.12.1 Tx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 15.26, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11200131", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortTxPower.1.14.1", "sensor_type": "mrv-od", "sensor_descr": "Port 1.14.1 Tx Power", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": -3.76, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11400131", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.11.1", "sensor_index": "nbsCmmcChassisFan1Status.1", "sensor_type": "nbsCmmcChassisFanStatus", "sensor_descr": "Chassis 1 Fan 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10000011", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcChassisFanStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.7.1", "sensor_index": "nbsCmmcChassisPS1Status.1", "sensor_type": "nbsCmmcChassisPSStatus", "sensor_descr": "Chassis 1 Power Supply 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1000007", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcChassisPSStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.8.1", "sensor_index": "nbsCmmcChassisPS2Status.1", "sensor_type": "nbsCmmcChassisPSStatus", "sensor_descr": "Chassis 1 Power Supply 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1000008", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcChassisPSStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortDigitalDiags.1.1.3", "sensor_type": "nbsCmmcPortDigitalDiags", "sensor_descr": "Port 1.1.3 Overall DigiDiags State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100338", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcPortDigitalDiags"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortDigitalDiags.1.1.4", "sensor_type": "nbsCmmcPortDigitalDiags", "sensor_descr": "Port 1.1.4 Overall DigiDiags State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100438", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcPortDigitalDiags"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortDigitalDiags.1.12.1", "sensor_type": "nbsCmmcPortDigitalDiags", "sensor_descr": "Port 1.12.1 Overall DigiDiags State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11200138", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcPortDigitalDiags"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortDigitalDiags.1.12.2", "sensor_type": "nbsCmmcPortDigitalDiags", "sensor_descr": "Port 1.12.2 Overall DigiDiags State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11200238", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcPortDigitalDiags"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortDigitalDiags.1.14.1", "sensor_type": "nbsCmmcPortDigitalDiags", "sensor_descr": "Port 1.14.1 Overall DigiDiags State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11400138", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcPortDigitalDiags"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortDigitalDiags.1.14.2", "sensor_type": "nbsCmmcPortDigitalDiags", "sensor_descr": "Port 1.14.2 Overall DigiDiags State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11400238", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "nbsCmmcPortDigitalDiags"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.15.1", "sensor_index": "nbsCmmcChassisTemperature.1", "sensor_type": "mrv-od", "sensor_descr": "Chassis 1 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 45, "sensor_limit_warn": null, "sensor_limit_low": 5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10000015", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortTemperature.1.1.3", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.3 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 55, "sensor_limit_warn": null, "sensor_limit_low": 25, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100330", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortTemperature.1.1.4", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.4 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100430", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortTemperature.1.12.1", "sensor_type": "mrv-od", "sensor_descr": "Port 1.12.1 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 48, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11200130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortTemperature.1.12.2", "sensor_type": "mrv-od", "sensor_descr": "Port 1.12.2 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 48, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11200230", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortTemperature.1.14.1", "sensor_type": "mrv-od", "sensor_descr": "Port 1.14.1 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 46, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11400130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortTemperature.1.14.2", "sensor_type": "mrv-od", "sensor_descr": "Port 1.14.2 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 46, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11400230", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.34.1.13", "sensor_index": "nbsCmmcSlotTemperature.1.13", "sensor_type": "mrv-od", "sensor_descr": "Slot 1.13 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 65, "sensor_limit_warn": null, "sensor_limit_low": 5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11300034", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.34.1.15", "sensor_index": "nbsCmmcSlotTemperature.1.15", "sensor_type": "mrv-od", "sensor_descr": "Slot 1.15 Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11500034", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortSupplyVolts.1.1.3", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.3 Tx Supply Voltage", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 3.339, "sensor_limit": 3.83985, "sensor_limit_warn": null, "sensor_limit_low": 2.83815, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100334", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.********", "sensor_index": "nbsCmmcPortSupplyVolts.1.1.4", "sensor_type": "mrv-od", "sensor_descr": "Port 1.1.4 Tx Supply Voltage", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 3.245, "sensor_limit": 3.73175, "sensor_limit_warn": null, "sensor_limit_low": 2.75825, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "10100434", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortSupplyVolts.1.12.1", "sensor_type": "mrv-od", "sensor_descr": "Port 1.12.1 Tx Supply Voltage", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 3.28, "sensor_limit": 3.772, "sensor_limit_warn": null, "sensor_limit_low": 2.788, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11200134", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.629.*********.*********", "sensor_index": "nbsCmmcPortSupplyVolts.1.14.1", "sensor_type": "mrv-od", "sensor_descr": "Port 1.14.1 Tx Supply Voltage", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 3.28, "sensor_limit": 3.772, "sensor_limit_warn": null, "sensor_limit_low": 2.788, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "11400134", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "nbsCmmcChassisFanStatus", "state_descr": "notSupported", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "nbsCmmcChassisFanStatus", "state_descr": "bad", "state_draw_graph": -1, "state_value": 2, "state_generic_value": 2}, {"state_name": "nbsCmmcChassisFanStatus", "state_descr": "good", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "nbsCmmcChassisFanStatus", "state_descr": "notInstalled", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 2}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "notInstalled", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "acBad", "state_draw_graph": -1, "state_value": 2, "state_generic_value": 2}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "dcBad", "state_draw_graph": -1, "state_value": 3, "state_generic_value": 2}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "acGood", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "dc<PERSON><PERSON>", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "notSupported", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 3}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "good", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}, {"state_name": "nbsCmmcChassisPSStatus", "state_descr": "bad", "state_draw_graph": -1, "state_value": 8, "state_generic_value": 2}, {"state_name": "nbsCmmcPortDigitalDiags", "state_descr": "notSupported", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "nbsCmmcPortDigitalDiags", "state_descr": "diagsOk", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "nbsCmmcPortDigitalDiags", "state_descr": "diagsWarning", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "nbsCmmcPortDigitalDiags", "state_descr": "diagsAlarm", "state_draw_graph": 2, "state_value": 4, "state_generic_value": 2}]}, "poller": "matches discovery"}, "entity-physical": {"discovery": {"entPhysical": [{"entPhysicalIndex": 1000001, "entPhysicalDescr": "Power Supply", "entPhysicalClass": "powerSupply", "entPhysicalName": "Power Supply 1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 1000002, "entPhysicalDescr": "Power Supply", "entPhysicalClass": "powerSupply", "entPhysicalName": "Power Supply 2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 1000007, "entPhysicalDescr": "Power Supply State", "entPhysicalClass": "sensor", "entPhysicalName": "Power Supply 1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1000001, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 1000008, "entPhysicalDescr": "Power Supply State", "entPhysicalClass": "sensor", "entPhysicalName": "Power Supply 2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1000002, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10000000, "entPhysicalDescr": "MRV OptiDriver OD-16-HD", "entPhysicalClass": "chassis", "entPhysicalName": "Chassis 1", "entPhysicalHardwareRev": "2081330-003R", "entPhysicalFirmwareRev": "Firmware: 0x7", "entPhysicalSoftwareRev": null, "entPhysicalAlias": "chassis-hostname1", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "OD-16-HD", "entPhysicalVendorType": "od16", "entPhysicalSerialNum": "012345EMF8K0", "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10000001, "entPhysicalDescr": "<PERSON>", "entPhysicalClass": "fan", "entPhysicalName": "Fan Tray 1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10000011, "entPhysicalDescr": "Fan State", "entPhysicalClass": "sensor", "entPhysicalName": "Fan 1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000001, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10000015, "entPhysicalDescr": "<PERSON><PERSON><PERSON>", "entPhysicalClass": "sensor", "entPhysicalName": "<PERSON><PERSON><PERSON>", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10100000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10100001, "entPhysicalDescr": "MRV Converter Card", "entPhysicalClass": "module", "entPhysicalName": "Card 1.1", "entPhysicalHardwareRev": "2081152-009R, EDC: 02.18", "entPhysicalFirmwareRev": "Firmware: 0x0015", "entPhysicalSoftwareRev": null, "entPhysicalAlias": "EM316DMR10G-3R at 1.1", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "EM316DMR10G-3R", "entPhysicalVendorType": "em316dmr10g3r", "entPhysicalSerialNum": "012345EME83T", "entPhysicalContainedIn": 10100000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10100100, "entPhysicalDescr": "SFP Transceiver Container", "entPhysicalClass": "container", "entPhysicalName": "Transceiver Container 1.1.1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100001, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10100200, "entPhysicalDescr": "SFP Transceiver Container", "entPhysicalClass": "container", "entPhysicalName": "Transceiver Container 1.1.2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100001, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10100300, "entPhysicalDescr": "SFP Transceiver Container", "entPhysicalClass": "container", "entPhysicalName": "Transceiver Container 1.1.3", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100001, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10100301, "entPhysicalDescr": "SFP Port, 1559.79nm Tx Signal, foLC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.1.3", "entPhysicalHardwareRev": "0001", "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "converter-ifalias-port3", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "SFP-10GDWZR-22", "entPhysicalVendorType": "125", "entPhysicalSerialNum": "Z01234UJL", "entPhysicalContainedIn": 10100300, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV", "ifIndex": 101003}, {"entPhysicalIndex": 10100330, "entPhysicalDescr": "Port Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100301, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV", "ifIndex": null}, {"entPhysicalIndex": 10100331, "entPhysicalDescr": "Port Tx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100301, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV", "ifIndex": null}, {"entPhysicalIndex": 10100332, "entPhysicalDescr": "Port Rx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Rx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100301, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV", "ifIndex": null}, {"entPhysicalIndex": 10100333, "entPhysicalDescr": "Port Tx Bias Current Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Bias Current", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100301, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV", "ifIndex": null}, {"entPhysicalIndex": 10100334, "entPhysicalDescr": "Port Tx Supply Voltage Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Supply Voltage", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100301, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV", "ifIndex": null}, {"entPhysicalIndex": 10100338, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100301, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV", "ifIndex": null}, {"entPhysicalIndex": 10100400, "entPhysicalDescr": "SFP Transceiver Container", "entPhysicalClass": "container", "entPhysicalName": "Transceiver Container 1.1.4", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100001, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10100401, "entPhysicalDescr": "SFP Port, 1310nm Tx Signal, foLC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.1.4", "entPhysicalHardwareRev": "A", "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "converter-ifalias-port4", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "P.1396.10", "entPhysicalVendorType": "125", "entPhysicalSerialNum": "F01V23U", "entPhysicalContainedIn": 10100400, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV FLEXOPTIX", "ifIndex": 101004}, {"entPhysicalIndex": 10100430, "entPhysicalDescr": "Port Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100401, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV FLEXOPTIX", "ifIndex": null}, {"entPhysicalIndex": 10100431, "entPhysicalDescr": "Port Tx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100401, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV FLEXOPTIX", "ifIndex": null}, {"entPhysicalIndex": 10100432, "entPhysicalDescr": "Port Rx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Rx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100401, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV FLEXOPTIX", "ifIndex": null}, {"entPhysicalIndex": 10100433, "entPhysicalDescr": "Port Tx Bias Current Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Bias Current", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100401, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV FLEXOPTIX", "ifIndex": null}, {"entPhysicalIndex": 10100434, "entPhysicalDescr": "Port Tx Supply Voltage Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Supply Voltage", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100401, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV FLEXOPTIX", "ifIndex": null}, {"entPhysicalIndex": 10100438, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10100401, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV FLEXOPTIX", "ifIndex": null}, {"entPhysicalIndex": 10200000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10300000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.3", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10400000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.4", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10500000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.5", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10600000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.6", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 6, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10700000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.7", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 7, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10800000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.8", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 8, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 10900000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.9", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 9, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11000000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.10", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 10, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11100000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.11", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 11, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11100001, "entPhysicalDescr": "MRV OptPassive Card", "entPhysicalClass": "module", "entPhysicalName": "Card 1.11", "entPhysicalHardwareRev": "5, ", "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "EM316-GEN-PASV at 1.11", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "EM316DCM40", "entPhysicalVendorType": "em316dcmxx", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11100000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11100101, "entPhysicalDescr": "Built-in Port, foLC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.11.1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "LC at 1.11.1", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "207", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11100001, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": 111001}, {"entPhysicalIndex": 11100138, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11100101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11100201, "entPhysicalDescr": "Built-in Port, foLC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.11.2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "LC at 1.11.2", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "207", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11100001, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "MRV Communications", "ifIndex": 111002}, {"entPhysicalIndex": 11100238, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11100201, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.12", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 12, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200001, "entPhysicalDescr": "MRV OptAmpBoosterAGC Card", "entPhysicalClass": "module", "entPhysicalName": "Card 1.12", "entPhysicalHardwareRev": "2081151-002R, BRICK S/N 1431312011", "entPhysicalFirmwareRev": "Firmware 0x6b", "entPhysicalSoftwareRev": null, "entPhysicalAlias": "EM316EDFA at 1.12", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "EM316EA-BR0918", "entPhysicalVendorType": "em316edfalv", "entPhysicalSerialNum": "012345EMF5HV", "entPhysicalContainedIn": 11200000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200101, "entPhysicalDescr": "Built-in Port, EDFA OUTPUT, foSC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.12.1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "FO_SC at 1.12.1", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "206", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11200001, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": 112001}, {"entPhysicalIndex": 11200130, "entPhysicalDescr": "Port Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11200101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200131, "entPhysicalDescr": "Port Tx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11200101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200134, "entPhysicalDescr": "Port Tx Supply Voltage Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Supply Voltage", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11200101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200138, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11200101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200201, "entPhysicalDescr": "Built-in Port, EDFA INPUT, foSC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.12.2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "FO_SC at 1.12.2", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "206", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11200001, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "MRV Communications", "ifIndex": 112002}, {"entPhysicalIndex": 11200230, "entPhysicalDescr": "Port Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11200201, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200232, "entPhysicalDescr": "Port Rx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Rx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11200201, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11200238, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11200201, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11300000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.13", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 13, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11300001, "entPhysicalDescr": "MRV OptAmpBoosterAGC Card", "entPhysicalClass": "module", "entPhysicalName": "Card 1.13", "entPhysicalHardwareRev": "2081151-002R, BRICK S/N 1431312011", "entPhysicalFirmwareRev": "Firmware 0x6b", "entPhysicalSoftwareRev": null, "entPhysicalAlias": "EM316EDFA at 1.13", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "EM316EA-BR0918", "entPhysicalVendorType": "em316edfar", "entPhysicalSerialNum": "012345EMF5HV", "entPhysicalContainedIn": 11300000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11300034, "entPhysicalDescr": "Card Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Card Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11300001, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.14", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 14, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400001, "entPhysicalDescr": "MRV OptAmpPreampAGC Card", "entPhysicalClass": "module", "entPhysicalName": "Card 1.14", "entPhysicalHardwareRev": "1294106-002R, BRICK S/N 1631401010", "entPhysicalFirmwareRev": "Firmware 0x6b", "entPhysicalSoftwareRev": null, "entPhysicalAlias": "EM316EDFA at 1.14", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "EM316EA-PR1013", "entPhysicalVendorType": "em316edfalv", "entPhysicalSerialNum": "012345EMEBYX", "entPhysicalContainedIn": 11400000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400101, "entPhysicalDescr": "Built-in Port, EDFA OUTPUT, foSC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.14.1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "FO_SC at 1.14.1", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "206", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11400001, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": 114001}, {"entPhysicalIndex": 11400130, "entPhysicalDescr": "Port Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11400101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400131, "entPhysicalDescr": "Port Tx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11400101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400134, "entPhysicalDescr": "Port Tx Supply Voltage Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Tx Supply Voltage", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11400101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400138, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11400101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400201, "entPhysicalDescr": "Built-in Port, EDFA INPUT, foSC Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.14.2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "FO_SC at 1.14.2", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "206", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11400001, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "MRV Communications", "ifIndex": 114002}, {"entPhysicalIndex": 11400230, "entPhysicalDescr": "Port Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11400201, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400232, "entPhysicalDescr": "Port Rx Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Rx Power", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11400201, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11400238, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11400201, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11500000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.15", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 15, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11500001, "entPhysicalDescr": "MRV OptAmpPreampAGC Card", "entPhysicalClass": "module", "entPhysicalName": "Card 1.15", "entPhysicalHardwareRev": "1294106-002R, BRICK S/N 1631401010", "entPhysicalFirmwareRev": "Firmware 0x6b", "entPhysicalSoftwareRev": null, "entPhysicalAlias": "EM316EDFA at 1.15", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "EM316EA-PR1013", "entPhysicalVendorType": "em316edfar", "entPhysicalSerialNum": "012345EMEBYX", "entPhysicalContainedIn": 11500000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11500034, "entPhysicalDescr": "Card Temperature Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Card Temperature", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11500001, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11600000, "entPhysicalDescr": "MRV OptiDriver Slot", "entPhysicalClass": "container", "entPhysicalName": "Card Slot 1.16", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10000000, "entPhysicalParentRelPos": 16, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11600001, "entPhysicalDescr": "MRV Management Card", "entPhysicalClass": "module", "entPhysicalName": "Card 1.16", "entPhysicalHardwareRev": "6, CPLD: 0x2001", "entPhysicalFirmwareRev": "Firmware: 0x2001", "entPhysicalSoftwareRev": null, "entPhysicalAlias": "OD-NM at 1.16", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": "OD-NM", "entPhysicalVendorType": "odnm", "entPhysicalSerialNum": "00201a012345", "entPhysicalContainedIn": 11600000, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11600101, "entPhysicalDescr": "Built-in Port, rj45wUSBRJ45Active Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.16.1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "USB at 1.16.1", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "222", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11600001, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "MRV Communications", "ifIndex": 116001}, {"entPhysicalIndex": 11600138, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11600101, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11600200, "entPhysicalDescr": "SFP Transceiver Container", "entPhysicalClass": "container", "entPhysicalName": "Transceiver Container 1.16.2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11600001, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11600300, "entPhysicalDescr": "SFP Transceiver Container", "entPhysicalClass": "container", "entPhysicalName": "Transceiver Container 1.16.3", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11600001, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}, {"entPhysicalIndex": 11600401, "entPhysicalDescr": "Built-in Port, cuRj45 Connector", "entPhysicalClass": "port", "entPhysicalName": "Port 1.16.4", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "RJ-45 at 1.16.4", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": "28", "entPhysicalSerialNum": "N/A", "entPhysicalContainedIn": 11600001, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "MRV Communications", "ifIndex": 116004}, {"entPhysicalIndex": 11600438, "entPhysicalDescr": "Port Overall DigiDiags State Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "Port Overall DigiDiags State", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 11600401, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "MRV Communications", "ifIndex": null}]}, "poller": "matches discovery"}}