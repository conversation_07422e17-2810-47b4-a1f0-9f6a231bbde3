{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.318.1.3.5.4", "sysDescr": "APC Web/SNMP Management Card (MB:v4.1.0 PF:v6.2.0 PN:apc_hw05_aos_620.bin AF1:v6.2.0 AN1:apc_hw05_sy_620.bin MN:AP9631 HR:05 SN: 5A1224T01862 MD:06/12/2012) (Embedded PowerNet SNMP Agent SW v2.2 compatible)", "sysContact": "<private>", "version": "AOS v6.2.0 / App v6.2.0", "hardware": "Symmetra LX 16000 RM 267.557.I", "features": null, "location": null, "os": "apc", "type": "power", "serial": "5D2123T08251", "icon": "apc.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "LOOPBACK", "ifName": "LOOPBACK", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "LOOPBACK", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lance", "ifName": "lance", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "lance", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "LOOPBACK", "ifName": "LOOPBACK", "portName": null, "ifIndex": 1, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "softwareLoopback", "ifAlias": "LOOPBACK", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lance", "ifName": "lance", "portName": null, "ifIndex": 2, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "lance", "ifPhysAddress": "00c0b79bc17f", "ifLastChange": 10, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 718865, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 712738, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 82312589, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 127128047, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 8503, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 48454, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.1.0", "sensor_index": "0", "sensor_type": "apc", "sensor_descr": "Battery Charge", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 100, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": 10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.2.6.0", "sensor_index": "upsAdvBatteryNumOfBadBattPacks.0", "sensor_type": "apc", "sensor_descr": "Bad batteries", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.2.5.0", "sensor_index": "upsAdvBatteryNumOfBattPacks.0", "sensor_type": "apc", "sensor_descr": "Installed batteries", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.4.0", "sensor_index": "0", "sensor_type": "apcUPS", "sensor_descr": "Phase 0 Output", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 34.7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.4.0", "sensor_index": "*******", "sensor_type": "apc", "sensor_descr": "Input", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 49.9, "sensor_limit": 52.395, "sensor_limit_warn": null, "sensor_limit_low": 47.405, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.2.0", "sensor_index": "*******", "sensor_type": "apc", "sensor_descr": "Output", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 49.9, "sensor_limit": 52.395, "sensor_limit_warn": null, "sensor_limit_low": 47.405, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "load", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.3.0", "sensor_index": ".*******.4.1.318.*******.3.3.0", "sensor_type": "apc", "sensor_descr": "Load(VA)", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 64, "sensor_limit": 80, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "runtime", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.2.3.0", "sensor_index": "upsAdvBatteryRunTimeRemaining.0", "sensor_type": "apc", "sensor_descr": "Runtime", "group": null, "sensor_divisor": 6000, "sensor_multiplier": 1, "sensor_current": 14, "sensor_limit": 3000, "sensor_limit_warn": 2000, "sensor_limit_low": 5, "sensor_limit_low_warn": 10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.2.4.0", "sensor_index": "0", "sensor_type": "upsAdvBatteryReplaceIndicator", "sensor_descr": "UPS Battery Replacement Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "0", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsAdvBatteryReplaceIndicator"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.2.5.0", "sensor_index": "upsAdvInputLineFailCause.0", "sensor_type": "upsAdvInputLineFailCause", "sensor_descr": "Last failure cause", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsAdvInputLineFailCause"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.1.1.0", "sensor_index": "0", "sensor_type": "upsBasicOutputStatus", "sensor_descr": "Output Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsBasicOutputStatus"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.2.0", "sensor_index": "0", "sensor_type": "apc", "sensor_descr": "Internal Temperature", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 59, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.4.0", "sensor_index": "*******", "sensor_type": "apc", "sensor_descr": "Battery Bus", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 138.7, "sensor_limit": 159.505, "sensor_limit_warn": null, "sensor_limit_low": 117.895, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.1.0", "sensor_index": "*******", "sensor_type": "apc", "sensor_descr": "Input", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 223.3, "sensor_limit": 256.795, "sensor_limit_warn": null, "sensor_limit_low": 189.805, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.318.*******.3.1.0", "sensor_index": "*******", "sensor_type": "apc", "sensor_descr": "Output", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 230.2, "sensor_limit": 264.73, "sensor_limit_warn": null, "sensor_limit_low": 195.67, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "upsAdvBatteryReplaceIndicator", "state_descr": "noBatteryNeedsReplacing", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "upsAdvBatteryReplaceIndicator", "state_descr": "batteryNeedsReplacing", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "noTransfer", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "highLineVoltage", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "brownout", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "blackout", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "smallMomentarySag", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "deepMomentarySag", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "smallMomentarySag", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "largeMomentarySpike", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "selfTest", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "upsAdvInputLineFailCause", "state_descr": "rateOfVoltageChange", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "upsBasicOutputStatus", "state_descr": "onLine", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "onBattery", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "onSmartBoost", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "timedSleeping", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "softwareBypass", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "off", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 2}, {"state_name": "upsBasicOutputStatus", "state_descr": "rebooting", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "switchedBypass", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "hardwareFailureBypass", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "upsBasicOutputStatus", "state_descr": "sleepingUntilPowerReturn", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "onSmartTrim", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "ecoMode", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "hotStandby", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "onBatteryTest", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "emergencyStaticBypass", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 2}, {"state_name": "upsBasicOutputStatus", "state_descr": "staticBypassStandby", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 1}, {"state_name": "upsBasicOutputStatus", "state_descr": "powerSavingMode", "state_draw_graph": 0, "state_value": 18, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "spotMode", "state_draw_graph": 0, "state_value": 19, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "eConversion", "state_draw_graph": 0, "state_value": 20, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "chargerSpotmode", "state_draw_graph": 0, "state_value": 21, "state_generic_value": 0}, {"state_name": "upsBasicOutputStatus", "state_descr": "inverterSpotmode", "state_draw_graph": 0, "state_value": 22, "state_generic_value": 0}]}, "poller": "matches discovery"}}