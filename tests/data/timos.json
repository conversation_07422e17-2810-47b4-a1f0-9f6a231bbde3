{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.6527.6.2.1.2.6.1", "sysDescr": "TiMOS-C-10.0.R4 cpm/hops Nokia SAS-R 7210 Copyright (c) 2000-2018 Nokia.\nAll rights reserved. All use subject to applicable license agreements.\r\nBuilt on Mon Apr 30 12:30:30 IST 2018 by sasbuild in /home/<USER>/10.0B1/R4/panos/main", "sysContact": "<private>", "version": "10.0.R4", "hardware": "7210 SAS-R6", "features": null, "location": "<private>", "os": "timos", "type": "network", "serial": "NS1806C3752", "icon": "nokia.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "system, Loopback IP interface", "ifName": "system", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "Loopback IP interface", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to_nokia-sw-b, IP interface", "ifName": "to_nokia-sw-b", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ipForward", "ifAlias": "IP interface", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to_bng-a, IP interface", "ifName": "to_bng-a", "portName": null, "ifIndex": 4, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ipForward", "ifAlias": "IP interface", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/1/1, 10-<PERSON><PERSON>", "ifName": "1/1/1", "portName": null, "ifIndex": 35684352, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/1/2, 10-<PERSON><PERSON>", "ifName": "1/1/2", "portName": null, "ifIndex": 35717120, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "2/1/1, 10-<PERSON><PERSON>, \\transit:", "ifName": "2/1/1", "portName": null, "ifIndex": 69238784, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "transit:", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "2/1/2, 10-<PERSON><PERSON>", "ifName": "2/1/2", "portName": null, "ifIndex": 69271552, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "3/1/1, 10-<PERSON><PERSON>", "ifName": "3/1/1", "portName": null, "ifIndex": 102793216, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "3/1/2, 10-<PERSON><PERSON>", "ifName": "3/1/2", "portName": null, "ifIndex": 102825984, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "A/1, 10/100 Ethernet TX", "ifName": "A/1", "portName": null, "ifIndex": 234913792, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100 Ethernet TX", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "B/1, 10/100 Ethernet TX", "ifName": "B/1", "portName": null, "ifIndex": 268468224, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "10/100 Ethernet TX", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lag-1, LAG Group, \\to_bng-a", "ifName": "lag-1", "portName": null, "ifIndex": 1342177281, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ieee8023adLag", "ifAlias": "to_bng-a", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lag-2, LAG Group, \\to_nokia-sw-b", "ifName": "lag-2", "portName": null, "ifIndex": 1342177282, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ieee8023adLag", "ifAlias": "to_nokia-sw-b", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lag-3, LAG Group, \\to_service-a", "ifName": "lag-3", "portName": null, "ifIndex": 1342177283, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ieee8023adLag", "ifAlias": "to_service-a", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "system, Loopback IP interface", "ifName": "system", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "softwareLoopback", "ifAlias": "Loopback IP interface", "ifPhysAddress": "8cf77337e077", "ifLastChange": 794, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to_nokia-sw-b, IP interface", "ifName": "to_nokia-sw-b", "portName": null, "ifIndex": 3, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9198, "ifType": "ipForward", "ifAlias": "IP interface", "ifPhysAddress": "8cf77337e094", "ifLastChange": 13819, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 77443610, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 99209928, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 13827816873, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 12856216124, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 2, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 13077147, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "to_bng-a, IP interface", "ifName": "to_bng-a", "portName": null, "ifIndex": 4, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9198, "ifType": "ipForward", "ifAlias": "IP interface", "ifPhysAddress": "8cf77337e093", "ifLastChange": 58355742, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 60311900, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 50214267, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 11408173430, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 8764854770, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 3, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 12923486, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "management", "ifName": "management", "portName": null, "ifIndex": 1280, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 0, "ifType": "network", "ifAlias": "management", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": "18230571304430338048", "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 2791463, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": "18374686483949813760", "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 236277835, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/1/1, 10-<PERSON><PERSON>", "ifName": "1/1/1", "portName": null, "ifIndex": 35684352, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": "8cf77322c10b", "ifLastChange": 58355742, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 7224729219535, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 7523861487594, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 5680098420082678, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 6025900727304484, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 3, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 2, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 20392554, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 18321512, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "1/1/2, 10-<PERSON><PERSON>", "ifName": "1/1/2", "portName": null, "ifIndex": 35717120, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": "8cf77322c10c", "ifLastChange": 13818, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 5210168108539, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 5948180286740, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": ****************, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": ****************, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 2, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 3, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 20741628, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 20728560, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "2/1/1, 10-<PERSON><PERSON>, \\transit:", "ifName": "2/1/1", "portName": null, "ifIndex": 69238784, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 1522, "ifType": "ethernetCsmacd", "ifAlias": "transit:", "ifPhysAddress": "8cf773191fa1", "ifLastChange": 4228665181, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 2768299961535, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1764080183209, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 1114, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 3385645537022360, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 836102404300147, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 36894974, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 141154, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 4160207, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 4463119, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "2/1/2, 10-<PERSON><PERSON>", "ifName": "2/1/2", "portName": null, "ifIndex": 69271552, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 1526, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": "8cf773191fa2", "ifLastChange": 725648953, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 711961990010, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 673787229515, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 2, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 578643821634351, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 2373254153150447, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 19038856, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 7357648, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 90710825, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 1189379339659, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "3/1/1, 10-<PERSON><PERSON>", "ifName": "3/1/1", "portName": null, "ifIndex": 102793216, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": "8cf773579ab5", "ifLastChange": 790, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "3/1/2, 10-<PERSON><PERSON>", "ifName": "3/1/2", "portName": null, "ifIndex": 102825984, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 9212, "ifType": "ethernetCsmacd", "ifAlias": "10-<PERSON><PERSON>", "ifPhysAddress": "8cf773579ab6", "ifLastChange": 14504, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "A/1, 10/100 Ethernet TX", "ifName": "A/1", "portName": null, "ifIndex": 234913792, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100 Ethernet TX", "ifPhysAddress": "8cf7730c0cc8", "ifLastChange": 349, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 94305066, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 90007307, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 19233499345, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 28389816173, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 37668950, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 22233422, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 44905, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 64611376, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "B/1, 10/100 Ethernet TX", "ifName": "B/1", "portName": null, "ifIndex": 268468224, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 1514, "ifType": "ethernetCsmacd", "ifAlias": "10/100 Ethernet TX", "ifPhysAddress": "8cf7730c0ca4", "ifLastChange": 3321, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 5182118, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 2999, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 10206884966, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 193278, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 37668970, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 22278283, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 5, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 64611354, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lag-1, LAG Group, \\to_bng-a", "ifName": "lag-1", "portName": null, "ifIndex": 1342177281, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9212, "ifType": "ieee8023adLag", "ifAlias": "to_bng-a", "ifPhysAddress": "8cf77337e093", "ifLastChange": 58355742, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 7224729219535, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 7523861487594, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 5680098420082678, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 6025900727304484, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 3, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 2, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 20392554, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 18321512, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lag-2, LAG Group, \\to_nokia-sw-b", "ifName": "lag-2", "portName": null, "ifIndex": 1342177282, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 9212, "ifType": "ieee8023adLag", "ifAlias": "to_nokia-sw-b", "ifPhysAddress": "8cf77337e094", "ifLastChange": 13819, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 5210168108539, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 5948180286740, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": ****************, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": ****************, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 2, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 3, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 20741628, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 20728560, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lag-3, LAG Group, \\to_service-a", "ifName": "lag-3", "portName": null, "ifIndex": 1342177283, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1526, "ifType": "ieee8023adLag", "ifAlias": "to_service-a", "ifPhysAddress": "8cf77337e095", "ifLastChange": 725648953, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 711961990010, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 673787229515, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 2, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 578643821634351, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 2373254153150447, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 19038856, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 7357648, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 90710825, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 1189379339659, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.6527.*******.1.1.0", "processor_index": "0", "processor_type": "timos", "processor_usage": 6, "processor_descr": "Processor", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "0", "entPhysicalIndex": null, "mempool_type": "timos", "mempool_class": "system", "mempool_precision": 1000, "mempool_descr": "Memory", "mempool_perc": 93, "mempool_perc_oid": null, "mempool_used": 2996224000, "mempool_used_oid": ".*******.4.1.6527.*******.1.11.0", "mempool_free": 212992000, "mempool_free_oid": ".*******.4.1.6527.*******.1.10.0", "mempool_total": 3209216000, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "vrf": {"discovery": {"vrfs": [{"vrf_oid": "1", "vrf_name": "Base", "bgpLocalAs": 0, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": "1,3,4"}, {"vrf_oid": "254", "vrf_name": "vpls-management", "bgpLocalAs": 0, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": null}, {"vrf_oid": "255", "vrf_name": "management", "bgpLocalAs": 0, "mplsVpnVrfRouteDistinguisher": null, "mplsVpnVrfDescription": "", "ifIndices": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSSubscribers.1", "sensor_type": "timos", "sensor_descr": "Active subscribers on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSTotal.1", "sensor_type": "timos", "sensor_descr": "Total hosts on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSV4.1", "sensor_type": "timos", "sensor_descr": "V4 hosts on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSV6.1", "sensor_type": "timos", "sensor_descr": "V6 hosts on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.35684352", "sensor_index": "tx-bias-1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.032898, "sensor_limit": 0.08, "sensor_limit_warn": 0.07, "sensor_limit_low": 0, "sensor_limit_low_warn": 0, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.35717120", "sensor_index": "tx-bias-1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.040768, "sensor_limit": 0.085, "sensor_limit_warn": 0.08, "sensor_limit_low": 0.015, "sensor_limit_low_warn": 0.02, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.69238784", "sensor_index": "tx-bias-1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.041606, "sensor_limit": 0.085, "sensor_limit_warn": 0.08, "sensor_limit_low": 0.015, "sensor_limit_low_warn": 0.02, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.69271552", "sensor_index": "tx-bias-1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.03308, "sensor_limit": 0.08, "sensor_limit_warn": 0.07, "sensor_limit_low": 0, "sensor_limit_low_warn": 0, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.35684352", "sensor_index": "rx-1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1 Rx", "group": "1/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.3784680769641, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -16.00326278519, "sensor_limit_low_warn": -15.003129173816, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.35717120", "sensor_index": "rx-1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2 Rx", "group": "1/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -3.8912699961995, "sensor_limit": 2.500050284869, "sensor_limit_warn": 2.000018654066, "sensor_limit_low": -20, "sensor_limit_low_warn": -18.013429130456, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.69238784", "sensor_index": "rx-1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1 Rx", "group": "2/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -6.2930190742442, "sensor_limit": 2.500050284869, "sensor_limit_warn": 2.000018654066, "sensor_limit_low": -20, "sensor_limit_low_warn": -18.013429130456, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.69271552", "sensor_index": "rx-1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2 Rx", "group": "2/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.33735909348, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -16.00326278519, "sensor_limit_low_warn": -15.003129173816, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.35684352", "sensor_index": "tx-1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1 Tx", "group": "1/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.8878347567891, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -8.9997426989214, "sensor_limit_low_warn": -7.9997073344623, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.35717120", "sensor_index": "tx-1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2 Tx", "group": "1/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -1.3798794874978, "sensor_limit": 2.000018654066, "sensor_limit_warn": 0.99991233544684, "sensor_limit_low": -7.9997073344623, "sensor_limit_low_warn": -7.0005709997723, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.69238784", "sensor_index": "tx-1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1 Tx", "group": "2/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -1.5039634191755, "sensor_limit": 2.000018654066, "sensor_limit_warn": 0.99991233544684, "sensor_limit_low": -7.9997073344623, "sensor_limit_low_warn": -7.0005709997723, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.69271552", "sensor_index": "tx-1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2 Tx", "group": "2/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.9610704636746, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -8.9997426989214, "sensor_limit_low_warn": -7.9997073344623, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisCriticalLEDState.1", "sensor_type": "tmnxChassisCriticalLEDState", "sensor_descr": "Critical LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisCriticalLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMajorLEDState.1", "sensor_type": "tmnxChassisMajorLEDState", "sensor_descr": "Major LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMajorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMinorLEDState.1", "sensor_type": "tmnxChassisMinorLEDState", "sensor_descr": "Minor LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMinorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisOverTempState.1", "sensor_type": "tmnxChassisOverTempState", "sensor_descr": "<PERSON><PERSON><PERSON>mp", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisOverTempState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.100663297", "sensor_index": "tmnxHwID.1.100663297", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Fan 1 NS1746C6321", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 1 NS1803C4073", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217730", "sensor_index": "tmnxHwID.1.134217730", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 2 NS1752C2633", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217731", "sensor_index": "tmnxHwID.1.134217731", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 3 NS1815C3093", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217732", "sensor_index": "tmnxHwID.1.134217732", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217733", "sensor_index": "tmnxHwID.1.134217733", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217734", "sensor_index": "tmnxHwID.1.134217734", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995057", "sensor_index": "tmnxHwID.1.150995057", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A NS1747C3434", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995073", "sensor_index": "tmnxHwID.1.150995073", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B NS1747C3432", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772167", "sensor_index": "tmnxHwID.1.167772167", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772168", "sensor_index": "tmnxHwID.1.167772168", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549633", "sensor_index": "tmnxHwID.1.184549633", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/1 NS1803C4073", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549889", "sensor_index": "tmnxHwID.1.184549889", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 2/1 NS1752C2633", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184550145", "sensor_index": "tmnxHwID.1.184550145", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 3/1 NS1815C3093", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328385", "sensor_index": "tmnxHwID.1.201328385", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328386", "sensor_index": "tmnxHwID.1.201328386", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf2:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328387", "sensor_index": "tmnxHwID.1.201328387", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "uf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328641", "sensor_index": "tmnxHwID.1.201328641", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328642", "sensor_index": "tmnxHwID.1.201328642", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf2:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328643", "sensor_index": "tmnxHwID.1.201328643", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "uf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.50331649", "sensor_index": "tmnxHwID.1.50331649", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Chassis 1 NS1806C3752", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886081", "sensor_index": "tmnxHwID.1.83886081", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Supply 1 NS1803C7135", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886082", "sensor_index": "tmnxHwID.1.83886082", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Supply 2 NS1803C7157", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.102793216", "sensor_index": "1.102793216", "sensor_type": "timos", "sensor_descr": "3/1/1", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 29.68359375, "sensor_limit": 80, "sensor_limit_warn": 75, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "102793216", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35684352", "sensor_index": "1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 40.25, "sensor_limit": 75, "sensor_limit_warn": 70, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35717120", "sensor_index": "1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 38.734375, "sensor_limit": 78, "sensor_limit_warn": 73, "sensor_limit_low": -13, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69238784", "sensor_index": "1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 35.70703125, "sensor_limit": 78, "sensor_limit_warn": 73, "sensor_limit_low": -13, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69271552", "sensor_index": "1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 35.875, "sensor_limit": 75, "sensor_limit_warn": 70, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "timos", "sensor_descr": "Slot 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 45, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 35, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217730", "sensor_index": "tmnxHwID.1.134217730", "sensor_type": "timos", "sensor_descr": "Slot 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217731", "sensor_index": "tmnxHwID.1.134217731", "sensor_type": "timos", "sensor_descr": "Slot 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995057", "sensor_index": "tmnxHwID.1.150995057", "sensor_type": "timos", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 47, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 37, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995073", "sensor_index": "tmnxHwID.1.150995073", "sensor_type": "timos", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 46, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 36, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549633", "sensor_index": "tmnxHwID.1.184549633", "sensor_type": "timos", "sensor_descr": "MDA 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 26, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549889", "sensor_index": "tmnxHwID.1.184549889", "sensor_type": "timos", "sensor_descr": "MDA 2/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184550145", "sensor_index": "tmnxHwID.1.184550145", "sensor_type": "timos", "sensor_descr": "MDA 3/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.50331649", "sensor_index": "tmnxHwID.1.50331649", "sensor_type": "timos", "sensor_descr": "Chassis 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 47, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 37, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.102793216", "sensor_index": "1.102793216", "sensor_type": "timos", "sensor_descr": "3/1/1", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.287, "sensor_limit": 3.5, "sensor_limit_warn": 3.48, "sensor_limit_low": 3.08, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "102793216", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35684352", "sensor_index": "1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.2787, "sensor_limit": 3.6, "sensor_limit_warn": 3.5, "sensor_limit_low": 3, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35717120", "sensor_index": "1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3112, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69238784", "sensor_index": "1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3098, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69271552", "sensor_index": "1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.2525, "sensor_limit": 3.6, "sensor_limit_warn": 3.5, "sensor_limit_low": 3, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "tmnxChassisCriticalLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisOverTempState", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisOverTempState", "state_descr": "OverTemp", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmActive", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmCleared", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSSubscribers.1", "sensor_type": "timos", "sensor_descr": "Active subscribers on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSTotal.1", "sensor_type": "timos", "sensor_descr": "Total hosts on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSV4.1", "sensor_type": "timos", "sensor_descr": "V4 hosts on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.********.**********.1", "sensor_index": "tmnxSubMgmtSystSV6.1", "sensor_type": "timos", "sensor_descr": "V6 hosts on this system", "group": "Subscribers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.35684352", "sensor_index": "tx-bias-1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.032898, "sensor_limit": 0.08, "sensor_limit_warn": 0.07, "sensor_limit_low": 0, "sensor_limit_low_warn": 0, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.35717120", "sensor_index": "tx-bias-1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.040768, "sensor_limit": 0.085, "sensor_limit_warn": 0.08, "sensor_limit_low": 0.015, "sensor_limit_low_warn": 0.02, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.69238784", "sensor_index": "tx-bias-1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.041606, "sensor_limit": 0.085, "sensor_limit_warn": 0.08, "sensor_limit_low": 0.015, "sensor_limit_low_warn": 0.02, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********1.1.69271552", "sensor_index": "tx-bias-1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2 Tx Bias", "group": null, "sensor_divisor": 500000, "sensor_multiplier": 1, "sensor_current": 0.03308, "sensor_limit": 0.08, "sensor_limit_warn": 0.07, "sensor_limit_low": 0, "sensor_limit_low_warn": 0, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.35684352", "sensor_index": "rx-1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1 Rx", "group": "1/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.3784680769641, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -16.00326278519, "sensor_limit_low_warn": -15.003129173816, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": -2.3784680769641, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.35717120", "sensor_index": "rx-1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2 Rx", "group": "1/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -3.8912699961995, "sensor_limit": 2.500050284869, "sensor_limit_warn": 2.000018654066, "sensor_limit_low": -20, "sensor_limit_low_warn": -18.013429130456, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": -3.8912699961995, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.69238784", "sensor_index": "rx-1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1 Rx", "group": "2/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -6.2930190742442, "sensor_limit": 2.500050284869, "sensor_limit_warn": 2.000018654066, "sensor_limit_low": -20, "sensor_limit_low_warn": -18.013429130456, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": -6.2930190742442, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.*********.1.69271552", "sensor_index": "rx-1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2 Rx", "group": "2/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.33735909348, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -16.00326278519, "sensor_limit_low_warn": -15.003129173816, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": -2.33735909348, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.35684352", "sensor_index": "tx-1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1 Tx", "group": "1/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.8878347567891, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -8.9997426989214, "sensor_limit_low_warn": -7.9997073344623, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": -2.8878347567891, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.35717120", "sensor_index": "tx-1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2 Tx", "group": "1/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -1.3798794874978, "sensor_limit": 2.000018654066, "sensor_limit_warn": 0.99991233544684, "sensor_limit_low": -7.9997073344623, "sensor_limit_low_warn": -7.0005709997723, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": -1.3798794874978, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.69238784", "sensor_index": "tx-1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1 Tx", "group": "2/1/1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -1.5039634191755, "sensor_limit": 2.000018654066, "sensor_limit_warn": 0.99991233544684, "sensor_limit_low": -7.9997073344623, "sensor_limit_low_warn": -7.0005709997723, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": -1.5039634191755, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********6.1.69271552", "sensor_index": "tx-1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2 Tx", "group": "2/1/2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.9610704636746, "sensor_limit": 1.4998845649148, "sensor_limit_warn": 0.49992856920143, "sensor_limit_low": -8.9997426989214, "sensor_limit_low_warn": -7.9997073344623, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": -2.9610704636746, "user_func": "uw_to_dbm", "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisCriticalLEDState.1", "sensor_type": "tmnxChassisCriticalLEDState", "sensor_descr": "Critical LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisCriticalLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMajorLEDState.1", "sensor_type": "tmnxChassisMajorLEDState", "sensor_descr": "Major LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMajorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisMinorLEDState.1", "sensor_type": "tmnxChassisMinorLEDState", "sensor_descr": "Minor LED", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisMinorLEDState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1", "sensor_index": "tmnxChassisOverTempState.1", "sensor_type": "tmnxChassisOverTempState", "sensor_descr": "<PERSON><PERSON><PERSON>mp", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxChassisOverTempState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.100663297", "sensor_index": "tmnxHwID.1.100663297", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Fan 1 NS1746C6321", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 1 NS1803C4073", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217730", "sensor_index": "tmnxHwID.1.134217730", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 2 NS1752C2633", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217731", "sensor_index": "tmnxHwID.1.134217731", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 3 NS1815C3093", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217732", "sensor_index": "tmnxHwID.1.134217732", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217733", "sensor_index": "tmnxHwID.1.134217733", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217734", "sensor_index": "tmnxHwID.1.134217734", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot 6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995057", "sensor_index": "tmnxHwID.1.150995057", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A NS1747C3434", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995073", "sensor_index": "tmnxHwID.1.150995073", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B NS1747C3432", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772167", "sensor_index": "tmnxHwID.1.167772167", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.167772168", "sensor_index": "tmnxHwID.1.167772168", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549633", "sensor_index": "tmnxHwID.1.184549633", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 1/1 NS1803C4073", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549889", "sensor_index": "tmnxHwID.1.184549889", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 2/1 NS1752C2633", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184550145", "sensor_index": "tmnxHwID.1.184550145", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "MDA 3/1 NS1815C3093", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328385", "sensor_index": "tmnxHwID.1.201328385", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328386", "sensor_index": "tmnxHwID.1.201328386", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf2:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328387", "sensor_index": "tmnxHwID.1.201328387", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "uf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328641", "sensor_index": "tmnxHwID.1.201328641", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328642", "sensor_index": "tmnxHwID.1.201328642", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "cf2:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.201328643", "sensor_index": "tmnxHwID.1.201328643", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "uf1:", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.50331649", "sensor_index": "tmnxHwID.1.50331649", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Chassis 1 NS1806C3752", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886081", "sensor_index": "tmnxHwID.1.83886081", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Supply 1 NS1803C7135", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.83886082", "sensor_index": "tmnxHwID.1.83886082", "sensor_type": "tmnxHwAlarmState", "sensor_descr": "Power Supply 2 NS1803C7157", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tmnxHwAlarmState"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.102793216", "sensor_index": "1.102793216", "sensor_type": "timos", "sensor_descr": "3/1/1", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 29.68359375, "sensor_limit": 80, "sensor_limit_warn": 75, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "102793216", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35684352", "sensor_index": "1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 40.25, "sensor_limit": 75, "sensor_limit_warn": 70, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35717120", "sensor_index": "1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 38.734375, "sensor_limit": 78, "sensor_limit_warn": 73, "sensor_limit_low": -13, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69238784", "sensor_index": "1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 35.70703125, "sensor_limit": 78, "sensor_limit_warn": 73, "sensor_limit_low": -13, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69271552", "sensor_index": "1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2", "group": null, "sensor_divisor": 256, "sensor_multiplier": 1, "sensor_current": 35.875, "sensor_limit": 75, "sensor_limit_warn": 70, "sensor_limit_low": -10, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217729", "sensor_index": "tmnxHwID.1.134217729", "sensor_type": "timos", "sensor_descr": "Slot 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 45, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 35, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217730", "sensor_index": "tmnxHwID.1.134217730", "sensor_type": "timos", "sensor_descr": "Slot 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.134217731", "sensor_index": "tmnxHwID.1.134217731", "sensor_type": "timos", "sensor_descr": "Slot 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995057", "sensor_index": "tmnxHwID.1.150995057", "sensor_type": "timos", "sensor_descr": "Slot A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 47, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 37, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.150995073", "sensor_index": "tmnxHwID.1.150995073", "sensor_type": "timos", "sensor_descr": "Slot B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 46, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 36, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549633", "sensor_index": "tmnxHwID.1.184549633", "sensor_type": "timos", "sensor_descr": "MDA 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 26, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184549889", "sensor_index": "tmnxHwID.1.184549889", "sensor_type": "timos", "sensor_descr": "MDA 2/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.184550145", "sensor_index": "tmnxHwID.1.184550145", "sensor_type": "timos", "sensor_descr": "MDA 3/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.50331649", "sensor_index": "tmnxHwID.1.50331649", "sensor_type": "timos", "sensor_descr": "Chassis 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 47, "sensor_limit": 74, "sensor_limit_warn": null, "sensor_limit_low": 37, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.102793216", "sensor_index": "1.102793216", "sensor_type": "timos", "sensor_descr": "3/1/1", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.287, "sensor_limit": 3.5, "sensor_limit_warn": 3.48, "sensor_limit_low": 3.08, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "102793216", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35684352", "sensor_index": "1.35684352", "sensor_type": "timos", "sensor_descr": "1/1/1", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.2787, "sensor_limit": 3.6, "sensor_limit_warn": 3.5, "sensor_limit_low": 3, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35684352", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.35717120", "sensor_index": "1.35717120", "sensor_type": "timos", "sensor_descr": "1/1/2", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3112, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "35717120", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69238784", "sensor_index": "1.69238784", "sensor_type": "timos", "sensor_descr": "2/1/1", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.3098, "sensor_limit": 3.7, "sensor_limit_warn": 3.6, "sensor_limit_low": 2.9, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69238784", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.6527.*******.********.1.69271552", "sensor_index": "1.69271552", "sensor_type": "timos", "sensor_descr": "2/1/2", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 3.2525, "sensor_limit": 3.6, "sensor_limit_warn": 3.5, "sensor_limit_low": 3, "sensor_limit_low_warn": 3.1, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "69271552", "entPhysicalIndex_measured": "ports", "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "tmnxChassisCriticalLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisCriticalLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMajorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "NotApplicable", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Off", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Red", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Amber", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Yellow", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "Green", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "AmberBlink", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "YellowBlink", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "GreenBlink", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "tmnxChassisMinorLEDState", "state_descr": "RedBlink", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "tmnxChassisOverTempState", "state_descr": "Ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "tmnxChassisOverTempState", "state_descr": "OverTemp", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmActive", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "tmnxHwAlarmState", "state_descr": "alarmCleared", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}]}}, "mpls": {"discovery": {"mpls_lsps": [{"vrf_oid": 1, "lsp_oid": 2, "mplsLspRowStatus": "active", "mplsLspLastChange": 8, "mplsLspName": "l-to_nokia-sw-b", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 3, "mplsLspRowStatus": "active", "mplsLspLastChange": 8, "mplsLspName": "l-to_nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 5, "mplsLspRowStatus": "active", "mplsLspLastChange": 6568308, "mplsLspName": "l-ks-0-nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 6, "mplsLspRowStatus": "active", "mplsLspLastChange": 9341769, "mplsLspName": "l-to_bng-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 7, "mplsLspRowStatus": "active", "mplsLspLastChange": 11924597, "mplsLspName": "l-to_bng-b", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 8, "mplsLspRowStatus": "active", "mplsLspLastChange": 34739895, "mplsLspName": "l-to_service-rt-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "172.17.0.8", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 9, "mplsLspRowStatus": "active", "mplsLspLastChange": 34739943, "mplsLspName": "l-to_service-rt-b", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "172.17.0.9", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 12, "mplsLspRowStatus": "active", "mplsLspLastChange": 36211926, "mplsLspName": "l-to_nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********0", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 13, "mplsLspRowStatus": "active", "mplsLspLastChange": 36211937, "mplsLspName": "l-to_bng-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "172.17.0.6", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 14, "mplsLspRowStatus": "active", "mplsLspLastChange": 5337444, "mplsLspName": "l-to_nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********1", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": null, "mplsLspTimeUp": null, "mplsLspTimeDown": null, "mplsLspPrimaryTimeUp": null, "mplsLspTransitions": null, "mplsLspLastTransition": null, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}], "mpls_lsp_paths": [{"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 8, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 10, "mplsLspPathOperMetric": 10, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 2, "mplsLspPathTunnelCHopListIndex": 2, "vrf_oid": 1, "lsp_oid": 2}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 8, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 3, "mplsLspPathTunnelCHopListIndex": 10, "vrf_oid": 1, "lsp_oid": 3}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 6568304, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 1, "mplsLspPathTunnelCHopListIndex": 8, "vrf_oid": 1, "lsp_oid": 5}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 9341762, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 10, "mplsLspPathOperMetric": 10, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 4, "mplsLspPathTunnelCHopListIndex": 5, "vrf_oid": 1, "lsp_oid": 6}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 11924596, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 5, "mplsLspPathTunnelCHopListIndex": 11, "vrf_oid": 1, "lsp_oid": 7}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 34739895, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 10, "mplsLspPathTunnelCHopListIndex": 316, "vrf_oid": 1, "lsp_oid": 8}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 34739941, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 30, "mplsLspPathOperMetric": 30, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 8, "mplsLspPathTunnelCHopListIndex": 324, "vrf_oid": 1, "lsp_oid": 9}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 36211926, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 30, "mplsLspPathOperMetric": 30, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 6, "mplsLspPathTunnelCHopListIndex": 296, "vrf_oid": 1, "lsp_oid": 12}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 36211937, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 40, "mplsLspPathOperMetric": 40, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 7, "mplsLspPathTunnelCHopListIndex": 297, "vrf_oid": 1, "lsp_oid": 13}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 5337440, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 30, "mplsLspPathOperMetric": 30, "mplsLspPathTimeUp": null, "mplsLspPathTimeDown": null, "mplsLspPathTransitionCount": null, "mplsLspPathTunnelARHopListIndex": 9, "mplsLspPathTunnelCHopListIndex": 262, "vrf_oid": 1, "lsp_oid": 14}], "mpls_sdps": [{"sdp_oid": 10001, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 8, "sdpLastStatusChange": 16538799, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10002, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 8, "sdpLastStatusChange": 32330107, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10003, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9186, "sdpLastMgmtChange": 13057174, "sdpLastStatusChange": 16811037, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10004, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 9342591, "sdpLastStatusChange": 9342591, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10005, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 11924642, "sdpLastStatusChange": 34312390, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10020, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 1546, "sdpLastMgmtChange": 36298092, "sdpLastStatusChange": 6328403, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********0", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10028, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 5337496, "sdpLastStatusChange": 5337523, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********1", "sdpFarEndInetAddressType": "ipv4"}], "mpls_sdp_binds": [{"sdp_oid": 10002, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 8, "sdpBindLastStatusChange": 36211628, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 140938175, "sdpBindBaseStatsIngFwdOctets": 53377853805, "sdpBindBaseStatsEgrFwdPackets": 161327732, "sdpBindBaseStatsEgrFwdOctets": 20673500506}, {"sdp_oid": 10002, "svc_oid": 118208020, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 28081218, "sdpBindLastStatusChange": 32330107, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 1189280583499, "sdpBindBaseStatsIngFwdOctets": 1645914838103462, "sdpBindBaseStatsEgrFwdPackets": 6872, "sdpBindBaseStatsEgrFwdOctets": 1461830}, {"sdp_oid": 10003, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 6568883, "sdpBindLastStatusChange": 36211628, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 88576971, "sdpBindBaseStatsIngFwdOctets": 25099489006, "sdpBindBaseStatsEgrFwdPackets": 123685104, "sdpBindBaseStatsEgrFwdOctets": 15832532104}, {"sdp_oid": 10003, "svc_oid": 118208004, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 18844830, "sdpBindLastStatusChange": 18846521, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 5056160654, "sdpBindBaseStatsIngFwdOctets": 3064394677859, "sdpBindBaseStatsEgrFwdPackets": 3274700093, "sdpBindBaseStatsEgrFwdOctets": 442836822296}, {"sdp_oid": 10004, "svc_oid": 118208010, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 9676834, "sdpBindLastStatusChange": 9681381, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 1500314593776, "sdpBindBaseStatsIngFwdOctets": 804431089432433, "sdpBindBaseStatsEgrFwdPackets": 2586581457517, "sdpBindBaseStatsEgrFwdOctets": 3232820826447300}, {"sdp_oid": 10004, "svc_oid": 118208012, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 13559106, "sdpBindLastStatusChange": 13559187, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 287843161852, "sdpBindBaseStatsIngFwdOctets": 283563364126581, "sdpBindBaseStatsEgrFwdPackets": 375207868799, "sdpBindBaseStatsEgrFwdOctets": 430717715263258}, {"sdp_oid": 10005, "svc_oid": 118208010, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 18735388, "sdpBindLastStatusChange": 34312390, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 263729038150, "sdpBindBaseStatsIngFwdOctets": 62787263274433, "sdpBindBaseStatsEgrFwdPackets": 176985447344, "sdpBindBaseStatsEgrFwdOctets": 199681599767857}, {"sdp_oid": 10005, "svc_oid": 118208012, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 18738566, "sdpBindLastStatusChange": 34312390, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 28120925666, "sdpBindBaseStatsIngFwdOctets": 10407841661579, "sdpBindBaseStatsEgrFwdPackets": 131410379020, "sdpBindBaseStatsEgrFwdOctets": 126888297053546}, {"sdp_oid": 10020, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 36298826, "sdpBindLastStatusChange": 6328403, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 87463506, "sdpBindBaseStatsIngFwdOctets": 53188834782, "sdpBindBaseStatsEgrFwdPackets": 66716730, "sdpBindBaseStatsEgrFwdOctets": 8956009415}, {"sdp_oid": 10028, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 5337604, "sdpBindLastStatusChange": 5337913, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 2305355, "sdpBindBaseStatsIngFwdOctets": 734782936, "sdpBindBaseStatsEgrFwdPackets": 3340003, "sdpBindBaseStatsEgrFwdOctets": 427863425}, {"sdp_oid": 10028, "svc_oid": 118208002, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 6365338, "sdpBindLastStatusChange": 6365338, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 337966148, "sdpBindBaseStatsIngFwdOctets": 278558380269, "sdpBindBaseStatsEgrFwdPackets": 191361674, "sdpBindBaseStatsEgrFwdOctets": 22822838418}, {"sdp_oid": 10028, "svc_oid": 118208003, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 6365938, "sdpBindLastStatusChange": 6365938, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 16932797, "sdpBindBaseStatsIngFwdOctets": 4185728123, "sdpBindBaseStatsEgrFwdPackets": 29816841, "sdpBindBaseStatsEgrFwdOctets": 29313724244}], "mpls_services": [{"svc_oid": 118208001, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "Management", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 4, "svcLastMgmtChange": 5337604, "svcLastStatusChange": 36211628, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 37}, {"svc_oid": 118208002, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "transfer Mgmt", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 6365338, "svcLastStatusChange": 13038062, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 2}, {"svc_oid": 118208003, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "O", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 6365938, "svcLastStatusChange": 15315561, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 19}, {"svc_oid": 118208004, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "transfer Mgmt", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 18844847, "svcLastStatusChange": 18844847, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 2}, {"svc_oid": 118208010, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "BNG", "svcMtu": 1518, "svcNumSaps": 1, "svcNumSdps": 2, "svcLastMgmtChange": 18735388, "svcLastStatusChange": 9676834, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 3}, {"svc_oid": 118208012, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "BGP Peering", "svcMtu": 1518, "svcNumSaps": 1, "svcNumSdps": 2, "svcLastMgmtChange": 18738566, "svcLastStatusChange": 13559111, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 6}, {"svc_oid": 118208020, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "TV", "svcMtu": 1518, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 28081223, "svcLastStatusChange": 28081223, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 1}], "mpls_saps": [{"svc_oid": 118208001, "sapPortId": 1342177283, "ifName": null, "sapEncapValue": "10", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "RZ Management VLAN 10", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 12437249, "sapLastStatusChange": 36211628}, {"svc_oid": 118208002, "sapPortId": 1342177283, "ifName": null, "sapEncapValue": "2002", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 13038042, "sapLastStatusChange": 13038062}, {"svc_oid": 118208003, "sapPortId": 1342177283, "ifName": null, "sapEncapValue": "2512", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 15315533, "sapLastStatusChange": 15315561}, {"svc_oid": 118208004, "sapPortId": 1342177283, "ifName": null, "sapEncapValue": "2003", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 18844769, "sapLastStatusChange": 18844847}, {"svc_oid": 118208010, "sapPortId": 69238784, "ifName": null, "sapEncapValue": "58", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "BGP peering", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 27554054, "sapLastStatusChange": 42286652}, {"svc_oid": 118208012, "sapPortId": 1342177283, "ifName": null, "sapEncapValue": "113", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "BGP Peering", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 27554068, "sapLastStatusChange": 13559111}, {"svc_oid": 118208020, "sapPortId": 1342177283, "ifName": null, "sapEncapValue": "38", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "Kabelfernsehen", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 28082922, "sapLastStatusChange": 28081223}]}, "poller": {"mpls_lsps": [{"vrf_oid": 1, "lsp_oid": 2, "mplsLspRowStatus": "active", "mplsLspLastChange": 8, "mplsLspName": "l-to_nokia-sw-b", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 3, "mplsLspRowStatus": "active", "mplsLspLastChange": 8, "mplsLspName": "l-to_nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 5, "mplsLspRowStatus": "active", "mplsLspLastChange": 6568308, "mplsLspName": "l-ks-0-nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 6, "mplsLspRowStatus": "active", "mplsLspLastChange": 9341769, "mplsLspName": "l-to_bng-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 7, "mplsLspRowStatus": "active", "mplsLspLastChange": 11924597, "mplsLspName": "l-to_bng-b", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 8, "mplsLspRowStatus": "active", "mplsLspLastChange": 34739895, "mplsLspName": "l-to_service-rt-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "172.17.0.8", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 9, "mplsLspRowStatus": "active", "mplsLspLastChange": 34739943, "mplsLspName": "l-to_service-rt-b", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "172.17.0.9", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 12, "mplsLspRowStatus": "active", "mplsLspLastChange": 36211926, "mplsLspName": "l-to_nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********0", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 13, "mplsLspRowStatus": "active", "mplsLspLastChange": 36211937, "mplsLspName": "l-to_bng-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "172.17.0.6", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}, {"vrf_oid": 1, "lsp_oid": 14, "mplsLspRowStatus": "active", "mplsLspLastChange": 5337444, "mplsLspName": "l-to_nokia-sw-a", "mplsLspAdminState": "inService", "mplsLspOperState": "inService", "mplsLspFromAddr": "0.0.0.0", "mplsLspToAddr": "**********1", "mplsLspType": "dynamic", "mplsLspFastReroute": "true", "mplsLspAge": 0, "mplsLspTimeUp": 0, "mplsLspTimeDown": 0, "mplsLspPrimaryTimeUp": 0, "mplsLspTransitions": null, "mplsLspLastTransition": 0, "mplsLspConfiguredPaths": null, "mplsLspStandbyPaths": null, "mplsLspOperationalPaths": null}], "mpls_lsp_paths": [{"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 8, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 10, "mplsLspPathOperMetric": 10, "mplsLspPathTimeUp": 674295803, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 1, "mplsLspPathTunnelARHopListIndex": 2, "mplsLspPathTunnelCHopListIndex": 2, "vrf_oid": 1, "lsp_oid": 2}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 8, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": 1736272298, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 21, "mplsLspPathTunnelARHopListIndex": 3, "mplsLspPathTunnelCHopListIndex": 10, "vrf_oid": 1, "lsp_oid": 3}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 6568304, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": 1006787097, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 19, "mplsLspPathTunnelARHopListIndex": 1, "mplsLspPathTunnelCHopListIndex": 8, "vrf_oid": 1, "lsp_oid": 5}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 9341762, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 10, "mplsLspPathOperMetric": 10, "mplsLspPathTimeUp": 259861163, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 1, "mplsLspPathTunnelARHopListIndex": 4, "mplsLspPathTunnelCHopListIndex": 5, "vrf_oid": 1, "lsp_oid": 6}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 11924596, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": 1538043999, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 3, "mplsLspPathTunnelARHopListIndex": 5, "mplsLspPathTunnelCHopListIndex": 11, "vrf_oid": 1, "lsp_oid": 7}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 34739895, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 20, "mplsLspPathOperMetric": 20, "mplsLspPathTimeUp": 34313099, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 17, "mplsLspPathTunnelARHopListIndex": 10, "mplsLspPathTunnelCHopListIndex": 316, "vrf_oid": 1, "lsp_oid": 8}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 34739941, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 30, "mplsLspPathOperMetric": 30, "mplsLspPathTimeUp": 16275299, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 15, "mplsLspPathTunnelARHopListIndex": 8, "mplsLspPathTunnelCHopListIndex": 324, "vrf_oid": 1, "lsp_oid": 9}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 36211926, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 30, "mplsLspPathOperMetric": 30, "mplsLspPathTimeUp": 41475398, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 39, "mplsLspPathTunnelARHopListIndex": 6, "mplsLspPathTunnelCHopListIndex": 296, "vrf_oid": 1, "lsp_oid": 12}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 36211937, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 40, "mplsLspPathOperMetric": 40, "mplsLspPathTimeUp": 41474298, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 41, "mplsLspPathTunnelARHopListIndex": 7, "mplsLspPathTunnelCHopListIndex": 297, "vrf_oid": 1, "lsp_oid": 13}, {"path_oid": 1, "mplsLspPathRowStatus": "active", "mplsLspPathLastChange": 5337440, "mplsLspPathType": "primary", "mplsLspPathBandwidth": 0, "mplsLspPathOperBandwidth": 0, "mplsLspPathAdminState": "inService", "mplsLspPathOperState": "inService", "mplsLspPathState": "active", "mplsLspPathFailCode": "noError", "mplsLspPathFailNodeAddr": "0.0.0.0", "mplsLspPathMetric": 30, "mplsLspPathOperMetric": 30, "mplsLspPathTimeUp": 140571299, "mplsLspPathTimeDown": 0, "mplsLspPathTransitionCount": 1, "mplsLspPathTunnelARHopListIndex": 9, "mplsLspPathTunnelCHopListIndex": 262, "vrf_oid": 1, "lsp_oid": 14}], "mpls_sdps": [{"sdp_oid": 10001, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 8, "sdpLastStatusChange": 16538799, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10002, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 8, "sdpLastStatusChange": 32330107, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10003, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9186, "sdpLastMgmtChange": 13057174, "sdpLastStatusChange": 16811037, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10004, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 9342591, "sdpLastStatusChange": 9342591, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10005, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 11924642, "sdpLastStatusChange": 34312390, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10020, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 1546, "sdpLastMgmtChange": 36298092, "sdpLastStatusChange": 6328403, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********0", "sdpFarEndInetAddressType": "ipv4"}, {"sdp_oid": 10028, "sdpRowStatus": "active", "sdpDelivery": "mpls", "sdpDescription": "", "sdpAdminStatus": "up", "sdpOperStatus": "up", "sdpAdminPathMtu": 0, "sdpOperPathMtu": 9190, "sdpLastMgmtChange": 5337496, "sdpLastStatusChange": 5337523, "sdpActiveLspType": "rsvp", "sdpFarEndInetAddress": "**********1", "sdpFarEndInetAddressType": "ipv4"}], "mpls_sdp_binds": [{"sdp_oid": 10002, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 8, "sdpBindLastStatusChange": 36211628, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 140938175, "sdpBindBaseStatsIngFwdOctets": 53377853805, "sdpBindBaseStatsEgrFwdPackets": 161327732, "sdpBindBaseStatsEgrFwdOctets": 20673500506}, {"sdp_oid": 10002, "svc_oid": 118208020, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 28081218, "sdpBindLastStatusChange": 32330107, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 1189280583499, "sdpBindBaseStatsIngFwdOctets": 1645914838103462, "sdpBindBaseStatsEgrFwdPackets": 6872, "sdpBindBaseStatsEgrFwdOctets": 1461830}, {"sdp_oid": 10003, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 6568883, "sdpBindLastStatusChange": 36211628, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 88576971, "sdpBindBaseStatsIngFwdOctets": 25099489006, "sdpBindBaseStatsEgrFwdPackets": 123685104, "sdpBindBaseStatsEgrFwdOctets": 15832532104}, {"sdp_oid": 10003, "svc_oid": 118208004, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 18844830, "sdpBindLastStatusChange": 18846521, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 5056160654, "sdpBindBaseStatsIngFwdOctets": 3064394677859, "sdpBindBaseStatsEgrFwdPackets": 3274700093, "sdpBindBaseStatsEgrFwdOctets": 442836822296}, {"sdp_oid": 10004, "svc_oid": 118208010, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 9676834, "sdpBindLastStatusChange": 9681381, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 1500314593776, "sdpBindBaseStatsIngFwdOctets": 804431089432433, "sdpBindBaseStatsEgrFwdPackets": 2586581457517, "sdpBindBaseStatsEgrFwdOctets": 3232820826447300}, {"sdp_oid": 10004, "svc_oid": 118208012, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 13559106, "sdpBindLastStatusChange": 13559187, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 287843161852, "sdpBindBaseStatsIngFwdOctets": 283563364126581, "sdpBindBaseStatsEgrFwdPackets": 375207868799, "sdpBindBaseStatsEgrFwdOctets": 430717715263258}, {"sdp_oid": 10005, "svc_oid": 118208010, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 18735388, "sdpBindLastStatusChange": 34312390, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 263729038150, "sdpBindBaseStatsIngFwdOctets": 62787263274433, "sdpBindBaseStatsEgrFwdPackets": 176985447344, "sdpBindBaseStatsEgrFwdOctets": 199681599767857}, {"sdp_oid": 10005, "svc_oid": 118208012, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 18738566, "sdpBindLastStatusChange": 34312390, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 28120925666, "sdpBindBaseStatsIngFwdOctets": 10407841661579, "sdpBindBaseStatsEgrFwdPackets": 131410379020, "sdpBindBaseStatsEgrFwdOctets": 126888297053546}, {"sdp_oid": 10020, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 36298826, "sdpBindLastStatusChange": 6328403, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 87463506, "sdpBindBaseStatsIngFwdOctets": 53188834782, "sdpBindBaseStatsEgrFwdPackets": 66716730, "sdpBindBaseStatsEgrFwdOctets": 8956009415}, {"sdp_oid": 10028, "svc_oid": 118208001, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 5337604, "sdpBindLastStatusChange": 5337913, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 2305355, "sdpBindBaseStatsIngFwdOctets": 734782936, "sdpBindBaseStatsEgrFwdPackets": 3340003, "sdpBindBaseStatsEgrFwdOctets": 427863425}, {"sdp_oid": 10028, "svc_oid": 118208002, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 6365338, "sdpBindLastStatusChange": 6365338, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 337966148, "sdpBindBaseStatsIngFwdOctets": 278558380269, "sdpBindBaseStatsEgrFwdPackets": 191361674, "sdpBindBaseStatsEgrFwdOctets": 22822838418}, {"sdp_oid": 10028, "svc_oid": 118208003, "sdpBindRowStatus": "active", "sdpBindAdminStatus": "up", "sdpBindOperStatus": "up", "sdpBindLastMgmtChange": 6365938, "sdpBindLastStatusChange": 6365938, "sdpBindType": "mesh", "sdpBindVcType": "ether", "sdpBindBaseStatsIngFwdPackets": 16932797, "sdpBindBaseStatsIngFwdOctets": 4185728123, "sdpBindBaseStatsEgrFwdPackets": 29816841, "sdpBindBaseStatsEgrFwdOctets": 29313724244}], "mpls_services": [{"svc_oid": 118208001, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "Management", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 4, "svcLastMgmtChange": 5337604, "svcLastStatusChange": 36211628, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 37}, {"svc_oid": 118208002, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "transfer Mgmt", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 6365338, "svcLastStatusChange": 13038062, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 2}, {"svc_oid": 118208003, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "O", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 6365938, "svcLastStatusChange": 15315561, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 19}, {"svc_oid": 118208004, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "transfer Mgmt", "svcMtu": 1514, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 18844847, "svcLastStatusChange": 18844847, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 2}, {"svc_oid": 118208010, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "BNG", "svcMtu": 1518, "svcNumSaps": 1, "svcNumSdps": 2, "svcLastMgmtChange": 18735388, "svcLastStatusChange": 9676834, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 3}, {"svc_oid": 118208012, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "BGP Peering", "svcMtu": 1518, "svcNumSaps": 1, "svcNumSdps": 2, "svcLastMgmtChange": 18738566, "svcLastStatusChange": 13559111, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 6}, {"svc_oid": 118208020, "svcRowStatus": "active", "svcType": "tls", "svcCustId": 1, "svcAdminStatus": "up", "svcOperStatus": "up", "svcDescription": "TV", "svcMtu": 1518, "svcNumSaps": 1, "svcNumSdps": 1, "svcLastMgmtChange": 28081223, "svcLastStatusChange": 28081223, "svcVRouterId": 0, "svcTlsMacLearning": "enabled", "svcTlsStpAdminStatus": "disabled", "svcTlsStpOperStatus": "down", "svcTlsFdbTableSize": 250, "svcTlsFdbNumEntries": 1}], "mpls_saps": [{"svc_oid": 118208001, "sapPortId": 1342177283, "ifName": "lag-3", "sapEncapValue": "10", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "RZ Management VLAN 10", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 12437249, "sapLastStatusChange": 36211628}, {"svc_oid": 118208002, "sapPortId": 1342177283, "ifName": "lag-3", "sapEncapValue": "2002", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 13038042, "sapLastStatusChange": 13038062}, {"svc_oid": 118208003, "sapPortId": 1342177283, "ifName": "lag-3", "sapEncapValue": "2512", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 15315533, "sapLastStatusChange": 15315561}, {"svc_oid": 118208004, "sapPortId": 1342177283, "ifName": "lag-3", "sapEncapValue": "2003", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 18844769, "sapLastStatusChange": 18844847}, {"svc_oid": 118208010, "sapPortId": 69238784, "ifName": "2/1/1", "sapEncapValue": "58", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "BGP peering", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 27554054, "sapLastStatusChange": 42286652}, {"svc_oid": 118208012, "sapPortId": 1342177283, "ifName": "lag-3", "sapEncapValue": "113", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "BGP Peering", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 27554068, "sapLastStatusChange": 13559111}, {"svc_oid": 118208020, "sapPortId": 1342177283, "ifName": "lag-3", "sapEncapValue": "38", "sapRowStatus": "active", "sapType": "tls", "sapDescription": "Kabelfernsehen", "sapAdminStatus": "up", "sapOperStatus": "up", "sapLastMgmtChange": 28082922, "sapLastStatusChange": 28081223}]}}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1.39878656", "sensor_type": "Nokia-Packet-MW-Rx", "sensor_descr": "Rx (MW-to-Barrigada-V)", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -46, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.6527.*******.7.1.*******.39878656\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1.39911424", "sensor_type": "Nokia-Packet-MW-Rx", "sensor_descr": "Rx (MW-to-Barrigada-H)", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -47.1, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.6527.*******.7.1.*******.39911424\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1.39878656", "sensor_type": "Nokia-Packet-MW-Rx", "sensor_descr": "Rx (MW-to-Barrigada-V)", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -46, "sensor_prev": -46, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.6527.*******.7.1.*******.39878656\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "1.39911424", "sensor_type": "Nokia-Packet-MW-Rx", "sensor_descr": "Rx (MW-to-Barrigada-H)", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -47.1, "sensor_prev": -47.1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.6527.*******.7.1.*******.39911424\"]", "rrd_type": "GAUGE"}]}}, "ports-stack": {"discovery": {"ports_stack": [{"high_ifIndex": 35684352, "low_ifIndex": 1342177281, "ifStackStatus": "active"}, {"high_ifIndex": 35717120, "low_ifIndex": 1342177282, "ifStackStatus": "active"}, {"high_ifIndex": 69271552, "low_ifIndex": 1342177283, "ifStackStatus": "active"}, {"high_ifIndex": 1342177281, "low_ifIndex": 4, "ifStackStatus": "active"}, {"high_ifIndex": 1342177282, "low_ifIndex": 3, "ifStackStatus": "active"}]}}, "entity-physical": {"discovery": {"entPhysical": [{"entPhysicalIndex": 1, "entPhysicalDescr": "SAS-R CHASSIS", "entPhysicalClass": "chassis", "entPhysicalName": "7210 SAS-R6", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": null, "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": null, "ifIndex": null}, {"entPhysicalIndex": 50331649, "entPhysicalDescr": null, "entPhysicalClass": "phys<PERSON><PERSON><PERSON>", "entPhysicalName": "Chassis 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE08151AARE01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1806C3752", "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "3HE08151AARE01", "ifIndex": null}, {"entPhysicalIndex": 83886081, "entPhysicalDescr": null, "entPhysicalClass": "powerSupply", "entPhysicalName": "Power Supply 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE08153AARD01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1803C7135", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "3HE08153AARD01", "ifIndex": null}, {"entPhysicalIndex": 83886082, "entPhysicalDescr": null, "entPhysicalClass": "powerSupply", "entPhysicalName": "Power Supply 2", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE08153AARD01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1803C7157", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "3HE08153AARD01", "ifIndex": null}, {"entPhysicalIndex": 100663297, "entPhysicalDescr": null, "entPhysicalClass": "fan", "entPhysicalName": "Fan 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE08152AARE02", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1746C6321", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "3HE08152AARE02", "ifIndex": null}, {"entPhysicalIndex": 134217729, "entPhysicalDescr": null, "entPhysicalClass": "ioModule", "entPhysicalName": "Slot 1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "X-9.0.R6 on Mon May 8 14:01:43 IST 2017 by builder", "entPhysicalSoftwareRev": "X-9.0.R6 on Mon May 8 14:01:43 IST 2017 by builder", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE09153AARC01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1803C4073", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "3HE09153AARC01", "ifIndex": null}, {"entPhysicalIndex": 134217730, "entPhysicalDescr": null, "entPhysicalClass": "ioModule", "entPhysicalName": "Slot 2", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "X-9.0.R6 on Mon May 8 14:01:43 IST 2017 by builder", "entPhysicalSoftwareRev": "X-9.0.R6 on Mon May 8 14:01:43 IST 2017 by builder", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE09153AARC01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1752C2633", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "3HE09153AARC01", "ifIndex": null}, {"entPhysicalIndex": 134217731, "entPhysicalDescr": null, "entPhysicalClass": "ioModule", "entPhysicalName": "Slot 3", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "X-9.0.R6 on Mon May 8 14:01:43 IST 2017 by builder", "entPhysicalSoftwareRev": "X-9.0.R6 on Mon May 8 14:01:43 IST 2017 by builder", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE09153AARC01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1815C3093", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "3HE09153AARC01", "ifIndex": null}, {"entPhysicalIndex": 134217732, "entPhysicalDescr": null, "entPhysicalClass": "ioModule", "entPhysicalName": "Slot 4", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 134217733, "entPhysicalDescr": null, "entPhysicalClass": "ioModule", "entPhysicalName": "Slot 5", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 134217734, "entPhysicalDescr": null, "entPhysicalClass": "ioModule", "entPhysicalName": "Slot 6", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 6, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 150995057, "entPhysicalDescr": null, "entPhysicalClass": "cpmModule", "entPhysicalName": "Slot A", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "X-10.0.R3 on Thu Mar 29 10:16:31 IST 2018 by sasbuild", "entPhysicalSoftwareRev": "X-10.0.R3 on Thu Mar 29 10:16:31 IST 2018 by sasbuild", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE08154ABRE02", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1747C3434", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 7, "entPhysicalMfgName": "3HE08154ABRE02", "ifIndex": null}, {"entPhysicalIndex": 150995073, "entPhysicalDescr": null, "entPhysicalClass": "cpmModule", "entPhysicalName": "Slot B", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "X-10.0.R3 on Thu Mar 29 10:16:31 IST 2018 by sasbuild", "entPhysicalSoftwareRev": "X-10.0.R3 on Thu Mar 29 10:16:31 IST 2018 by sasbuild", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "3HE08154ABRE02", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1747C3432", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 8, "entPhysicalMfgName": "3HE08154ABRE02", "ifIndex": null}, {"entPhysicalIndex": 167772167, "entPhysicalDescr": null, "entPhysicalClass": "fabricModule", "entPhysicalName": "Slot A", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 7, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 167772168, "entPhysicalDescr": null, "entPhysicalClass": "fabricModule", "entPhysicalName": "Slot B", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 50331649, "entPhysicalParentRelPos": 8, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 184549633, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 1/1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "3HE09153AARC01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1803C4073", "entPhysicalContainedIn": 134217729, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "3HE09153AARC01", "ifIndex": null}, {"entPhysicalIndex": 184549889, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 2/1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "3HE09153AARC01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1752C2633", "entPhysicalContainedIn": 134217730, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "3HE09153AARC01", "ifIndex": null}, {"entPhysicalIndex": 184550145, "entPhysicalDescr": null, "entPhysicalClass": "mdaModule", "entPhysicalName": "MDA 3/1", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "3HE09153AARC01", "entPhysicalVendorType": null, "entPhysicalSerialNum": "NS1815C3093", "entPhysicalContainedIn": 134217731, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "3HE09153AARC01", "ifIndex": null}, {"entPhysicalIndex": 201328385, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "cf1:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150995057, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201328386, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "cf2:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150995057, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201328387, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "uf1:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150995057, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201328641, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "cf1:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150995073, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201328642, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "cf2:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150995073, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 201328643, "entPhysicalDescr": null, "entPhysicalClass": "flashDiskModule", "entPhysicalName": "uf1:", "entPhysicalHardwareRev": "1.0", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "", "entPhysicalContainedIn": 150995073, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "", "ifIndex": null}]}, "poller": "matches discovery"}, "discovery-protocols": {"discovery": {"links": [{"active": 1, "protocol": "lldp", "remote_hostname": "bng-a.mgmt..net", "remote_port": "35717120", "remote_platform": null, "remote_version": "TiMOS-C-15.1.R4 cpm/hops64 Nokia 7750 SR Copyright (c) 2000-2018 Nokia.\nAll rights reserved. All use subject to applicable license agreements.\r\nBuilt on Wed Apr 25 13:13:58 PDT 2018 by builder in /builds/151B/R4/panos/main", "ifAlias": "10-<PERSON><PERSON>", "ifDescr": "1/1/1, 10-<PERSON><PERSON>", "ifName": "1/1/1"}, {"active": 1, "protocol": "lldp", "remote_hostname": "nokia-sw-b.mgmt..net", "remote_port": "35717120", "remote_platform": null, "remote_version": "TiMOS-C-10.0.R4 cpm/hops Nokia SAS-R 7210 Copyright (c) 2000-2018 Nokia.\nAll rights reserved. All use subject to applicable license agreements.\r\nBuilt on Mon Apr 30 12:30:30 IST 2018 by sasbuild in /home/<USER>/10.0B1/R4/panos/main", "ifAlias": "10-<PERSON><PERSON>", "ifDescr": "1/1/2, 10-<PERSON><PERSON>", "ifName": "1/1/2"}, {"active": 1, "protocol": "lldp", "remote_hostname": "service-sw-a", "remote_port": "line1", "remote_platform": null, "remote_version": "", "ifAlias": "10-<PERSON><PERSON>", "ifDescr": "2/1/2, 10-<PERSON><PERSON>", "ifName": "2/1/2"}, {"active": 1, "protocol": "lldp", "remote_hostname": "SAS-R6-DUS..net", "remote_port": "102793216", "remote_platform": null, "remote_version": "TiMOS-C-10.0.R4 cpm/hops Nokia SAS-R 7210 Copyright (c) 2000-2018 Nokia.\nAll rights reserved. All use subject to applicable license agreements.\r\nBuilt on Mon Apr 30 12:30:30 IST 2018 by sasbuild in /home/<USER>/10.0B1/R4/panos/main", "ifAlias": "transit:", "ifDescr": "2/1/1, 10-<PERSON><PERSON>, \\transit:", "ifName": "2/1/1"}]}}}