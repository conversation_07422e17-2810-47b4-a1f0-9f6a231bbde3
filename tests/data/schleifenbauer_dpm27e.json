{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.31034.11.3", "sysDescr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sysContact": "<private>", "version": "250 (180914PL1760)", "hardware": "", "features": null, "location": "<private>", "os": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "power", "serial": "SVNL00655429", "icon": "schleifenbauer.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "en", "ifName": "en", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "en", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "en", "ifName": "en", "portName": null, "ifIndex": 1, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "en", "ifPhysAddress": "d02212ba0045", "ifLastChange": 351623473, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 485041, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 412879, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 86370226, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 76619213, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 68270, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 4332, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 2, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 53765, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.1.4.0", "sensor_index": "sdbMgmtStsDuplicateDevices.0", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "Duplicate Device Addresses", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.1.3.0", "sensor_index": "sdbMgmtStsNewDevices.0", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "New Devices", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 2, "sensor_limit_warn": 1, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429 Outlet 1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel1", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1201120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.10", "sensor_index": "SVNL00655429 Outlet 10", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel10", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1210120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.11", "sensor_index": "SVNL00655429 Outlet 11", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel11", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 1.94, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1211120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.12", "sensor_index": "SVNL00655429 Outlet 12", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel12", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1212120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429 Outlet 2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel2", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1202120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429 Outlet 3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel3", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1203120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.4", "sensor_index": "SVNL00655429 Outlet 4", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel4", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1204120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.5", "sensor_index": "SVNL00655429 Outlet 5", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel5", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1205120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.6", "sensor_index": "SVNL00655429 Outlet 6", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel6", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1206120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.7", "sensor_index": "SVNL00655429 Outlet 7", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel7", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1207120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.8", "sensor_index": "SVNL00655429 Outlet 8", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel8", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1208120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.9", "sensor_index": "SVNL00655429 Outlet 9", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel9", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 200, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1209120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L1 RMS Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 14, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L2 RMS Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 14, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L3 RMS Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 64, "sensor_limit_warn": 14, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.1", "sensor_index": "SVNL00655429 Outlet 1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1201130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.10", "sensor_index": "SVNL00655429 Outlet 10", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 290, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1210130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.11", "sensor_index": "SVNL00655429 Outlet 11", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 450, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1211130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.12", "sensor_index": "SVNL00655429 Outlet 12", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1212130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.2", "sensor_index": "SVNL00655429 Outlet 2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1202130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.3", "sensor_index": "SVNL00655429 Outlet 3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1203130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.4", "sensor_index": "SVNL00655429 Outlet 4", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1204130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.5", "sensor_index": "SVNL00655429 Outlet 5", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1205130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.6", "sensor_index": "SVNL00655429 Outlet 6", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1206130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.7", "sensor_index": "SVNL00655429 Outlet 7", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1207130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.8", "sensor_index": "SVNL00655429 Outlet 8", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1208130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.********.1.9", "sensor_index": "SVNL00655429 Outlet 9", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1209130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L1 Apparent Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L2 Apparent Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L3 Apparent Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429 Outlet 1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1201140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.10", "sensor_index": "SVNL00655429 Outlet 10", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1210140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.11", "sensor_index": "SVNL00655429 Outlet 11", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1211140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.12", "sensor_index": "SVNL00655429 Outlet 12", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1212140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429 Outlet 2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1202140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429 Outlet 3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1203140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.4", "sensor_index": "SVNL00655429 Outlet 4", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 532, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1204140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.5", "sensor_index": "SVNL00655429 Outlet 5", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 402, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1205140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.6", "sensor_index": "SVNL00655429 Outlet 6", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1206140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.7", "sensor_index": "SVNL00655429 Outlet 7", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1207140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.8", "sensor_index": "SVNL00655429 Outlet 8", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1208140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.9", "sensor_index": "SVNL00655429 Outlet 9", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1209140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L1 Lifetime kWh Total", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L2 Lifetime kWh Total", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L3 Lifetime kWh Total", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429 Outlet 1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel1", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1201150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.10", "sensor_index": "SVNL00655429 Outlet 10", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel10", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 0.0531, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1210150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.11", "sensor_index": "SVNL00655429 Outlet 11", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel11", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1211150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.12", "sensor_index": "SVNL00655429 Outlet 12", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel12", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1212150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429 Outlet 2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel2", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1202150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429 Outlet 3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel3", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1203150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.4", "sensor_index": "SVNL00655429 Outlet 4", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel4", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1204150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.5", "sensor_index": "SVNL00655429 Outlet 5", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel5", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1205150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.6", "sensor_index": "SVNL00655429 Outlet 6", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel6", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1206150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.7", "sensor_index": "SVNL00655429 Outlet 7", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel7", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1207150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.8", "sensor_index": "SVNL00655429 Outlet 8", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel8", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1208150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.9", "sensor_index": "SVNL00655429 Outlet 9", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel9", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1209150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L1 Power Factor", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L2 Power Factor", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L3 Power Factor", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.1.5.0", "sensor_index": "sdbMgmtStsRingState.0", "sensor_type": "sdbMgmtStsRingState", "sensor_descr": "Databus Ring State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "sdbMgmtStsRingState"}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429 Outlet 1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel1", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 234.33, "sensor_limit": 269.4795, "sensor_limit_warn": null, "sensor_limit_low": 199.1805, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1201110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.10", "sensor_index": "SVNL00655429 Outlet 10", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel10", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 233.09, "sensor_limit": 268.0535, "sensor_limit_warn": null, "sensor_limit_low": 198.1265, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1210110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.11", "sensor_index": "SVNL00655429 Outlet 11", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel11", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 233.39, "sensor_limit": 268.3985, "sensor_limit_warn": null, "sensor_limit_low": 198.3815, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1211110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.12", "sensor_index": "SVNL00655429 Outlet 12", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel12", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 233.2, "sensor_limit": 268.18, "sensor_limit_warn": null, "sensor_limit_low": 198.22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1212110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429 Outlet 2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel2", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 234.96, "sensor_limit": 270.204, "sensor_limit_warn": null, "sensor_limit_low": 199.716, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1202110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429 Outlet 3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel3", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 234.54, "sensor_limit": 269.721, "sensor_limit_warn": null, "sensor_limit_low": 199.359, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1203110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.4", "sensor_index": "SVNL00655429 Outlet 4", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel4", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 235.51, "sensor_limit": 270.8365, "sensor_limit_warn": null, "sensor_limit_low": 200.1835, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1204110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.5", "sensor_index": "SVNL00655429 Outlet 5", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel5", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 236.68, "sensor_limit": 272.182, "sensor_limit_warn": null, "sensor_limit_low": 201.178, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1205110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.6", "sensor_index": "SVNL00655429 Outlet 6", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel6", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 236.53, "sensor_limit": 272.0095, "sensor_limit_warn": null, "sensor_limit_low": 201.0505, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1206110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.7", "sensor_index": "SVNL00655429 Outlet 7", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel7", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 232.97, "sensor_limit": 267.9155, "sensor_limit_warn": null, "sensor_limit_low": 198.0245, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1207110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.8", "sensor_index": "SVNL00655429 Outlet 8", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel8", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 233.68, "sensor_limit": 268.732, "sensor_limit_warn": null, "sensor_limit_low": 198.628, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1208110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.9", "sensor_index": "SVNL00655429 Outlet 9", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "channel9", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 233.46, "sensor_limit": 268.479, "sensor_limit_warn": null, "sensor_limit_low": 198.441, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1209110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.1", "sensor_index": "SVNL00655429-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L1 Voltage", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.2", "sensor_index": "SVNL00655429-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L2 Voltage", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.1.3", "sensor_index": "SVNL00655429-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00655429-L3 Voltage", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "sdbMgmtStsRingState", "state_descr": "open", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 2}, {"state_name": "sdbMgmtStsRingState", "state_descr": "closed", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}]}, "poller": "matches discovery"}, "entity-physical": {"discovery": {"entPhysical": [{"entPhysicalIndex": 10, "entPhysicalDescr": "Schleifenbauer 3-phase, 27-outlet PDU", "entPhysicalClass": "chassis", "entPhysicalName": "Schleifenbauer PDU - SPDM v250", "entPhysicalHardwareRev": "SO# ", "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": "250", "entPhysicalAlias": "dpm27e-test @ s34dak", "entPhysicalAssetID": "Smartdc", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": null, "entPhysicalSerialNum": "SVNL00655429", "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1101000, "entPhysicalDescr": "64A input phase", "entPhysicalClass": "powerSupply", "entPhysicalName": "Input L1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1101110, "entPhysicalDescr": "Input L1 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1101000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1101120, "entPhysicalDescr": "Input L1 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1101000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1101130, "entPhysicalDescr": "Input L1 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1101000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1101140, "entPhysicalDescr": "Input L1 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1101000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1101150, "entPhysicalDescr": "Input L1 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1101000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1102000, "entPhysicalDescr": "64A input phase", "entPhysicalClass": "powerSupply", "entPhysicalName": "Input L2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1102110, "entPhysicalDescr": "Input L2 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1102000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1102120, "entPhysicalDescr": "Input L2 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1102000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1102130, "entPhysicalDescr": "Input L2 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1102000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1102140, "entPhysicalDescr": "Input L2 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1102000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1102150, "entPhysicalDescr": "Input L2 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1102000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1103000, "entPhysicalDescr": "64A input phase", "entPhysicalClass": "powerSupply", "entPhysicalName": "Input L3", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1103110, "entPhysicalDescr": "Input L3 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1103000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1103120, "entPhysicalDescr": "Input L3 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1103000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1103130, "entPhysicalDescr": "Input L3 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1103000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1103140, "entPhysicalDescr": "Input L3 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1103000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1103150, "entPhysicalDescr": "Input L3 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1103000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1200000, "entPhysicalDescr": "27 outlets", "entPhysicalClass": "backplane", "entPhysicalName": "Outlets", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1201000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel1", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1201110, "entPhysicalDescr": "Outlet #1 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1201000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1201120, "entPhysicalDescr": "Outlet #1 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1201000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1201130, "entPhysicalDescr": "Outlet #1 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1201000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1201140, "entPhysicalDescr": "Outlet #1 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1201000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1201150, "entPhysicalDescr": "Outlet #1 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1201000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1202000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel2", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1202110, "entPhysicalDescr": "Outlet #2 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1202000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1202120, "entPhysicalDescr": "Outlet #2 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1202000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1202130, "entPhysicalDescr": "Outlet #2 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1202000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1202140, "entPhysicalDescr": "Outlet #2 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1202000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1202150, "entPhysicalDescr": "Outlet #2 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1202000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1203000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #3", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel3", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1203110, "entPhysicalDescr": "Outlet #3 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1203000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1203120, "entPhysicalDescr": "Outlet #3 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1203000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1203130, "entPhysicalDescr": "Outlet #3 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1203000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1203140, "entPhysicalDescr": "Outlet #3 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1203000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1203150, "entPhysicalDescr": "Outlet #3 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1203000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1204000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #4", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel4", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1204110, "entPhysicalDescr": "Outlet #4 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1204000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1204120, "entPhysicalDescr": "Outlet #4 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1204000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1204130, "entPhysicalDescr": "Outlet #4 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1204000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1204140, "entPhysicalDescr": "Outlet #4 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1204000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1204150, "entPhysicalDescr": "Outlet #4 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1204000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1205000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #5", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel5", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1205110, "entPhysicalDescr": "Outlet #5 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1205000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1205120, "entPhysicalDescr": "Outlet #5 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1205000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1205130, "entPhysicalDescr": "Outlet #5 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1205000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1205140, "entPhysicalDescr": "Outlet #5 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1205000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1205150, "entPhysicalDescr": "Outlet #5 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1205000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1206000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #6", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel6", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 6, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1206110, "entPhysicalDescr": "Outlet #6 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1206000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1206120, "entPhysicalDescr": "Outlet #6 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1206000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1206130, "entPhysicalDescr": "Outlet #6 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1206000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1206140, "entPhysicalDescr": "Outlet #6 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1206000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1206150, "entPhysicalDescr": "Outlet #6 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1206000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1207000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #7", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel7", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 7, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1207110, "entPhysicalDescr": "Outlet #7 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1207000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1207120, "entPhysicalDescr": "Outlet #7 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1207000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1207130, "entPhysicalDescr": "Outlet #7 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1207000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1207140, "entPhysicalDescr": "Outlet #7 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1207000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1207150, "entPhysicalDescr": "Outlet #7 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1207000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1208000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #8", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel8", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 8, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1208110, "entPhysicalDescr": "Outlet #8 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1208000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1208120, "entPhysicalDescr": "Outlet #8 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1208000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1208130, "entPhysicalDescr": "Outlet #8 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1208000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1208140, "entPhysicalDescr": "Outlet #8 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1208000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1208150, "entPhysicalDescr": "Outlet #8 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1208000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1209000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #9", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel9", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 9, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1209110, "entPhysicalDescr": "Outlet #9 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1209000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1209120, "entPhysicalDescr": "Outlet #9 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1209000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1209130, "entPhysicalDescr": "Outlet #9 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1209000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1209140, "entPhysicalDescr": "Outlet #9 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1209000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1209150, "entPhysicalDescr": "Outlet #9 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1209000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1210000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #10", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel10", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 10, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1210110, "entPhysicalDescr": "Outlet #10 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1210000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1210120, "entPhysicalDescr": "Outlet #10 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1210000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1210130, "entPhysicalDescr": "Outlet #10 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1210000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1210140, "entPhysicalDescr": "Outlet #10 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1210000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1210150, "entPhysicalDescr": "Outlet #10 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1210000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1211000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #11", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel11", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 11, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1211110, "entPhysicalDescr": "Outlet #11 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1211000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1211120, "entPhysicalDescr": "Outlet #11 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1211000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1211130, "entPhysicalDescr": "Outlet #11 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1211000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1211140, "entPhysicalDescr": "Outlet #11 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1211000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1211150, "entPhysicalDescr": "Outlet #11 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1211000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1212000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #12", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel12", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 12, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1212110, "entPhysicalDescr": "Outlet #12 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1212000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1212120, "entPhysicalDescr": "Outlet #12 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1212000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1212130, "entPhysicalDescr": "Outlet #12 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1212000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1212140, "entPhysicalDescr": "Outlet #12 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1212000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1212150, "entPhysicalDescr": "Outlet #12 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1212000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1213000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #13", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel13", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 13, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1213110, "entPhysicalDescr": "Outlet #13 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1213000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1213120, "entPhysicalDescr": "Outlet #13 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1213000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1213130, "entPhysicalDescr": "Outlet #13 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1213000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1213140, "entPhysicalDescr": "Outlet #13 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1213000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1213150, "entPhysicalDescr": "Outlet #13 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1213000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1214000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #14", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel14", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 14, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1214110, "entPhysicalDescr": "Outlet #14 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1214000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1214120, "entPhysicalDescr": "Outlet #14 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1214000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1214130, "entPhysicalDescr": "Outlet #14 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1214000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1214140, "entPhysicalDescr": "Outlet #14 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1214000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1214150, "entPhysicalDescr": "Outlet #14 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1214000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1215000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #15", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel15", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 15, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1215110, "entPhysicalDescr": "Outlet #15 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1215000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1215120, "entPhysicalDescr": "Outlet #15 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1215000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1215130, "entPhysicalDescr": "Outlet #15 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1215000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1215140, "entPhysicalDescr": "Outlet #15 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1215000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1215150, "entPhysicalDescr": "Outlet #15 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1215000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1216000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #16", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel16", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 16, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1216110, "entPhysicalDescr": "Outlet #16 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1216000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1216120, "entPhysicalDescr": "Outlet #16 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1216000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1216130, "entPhysicalDescr": "Outlet #16 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1216000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1216140, "entPhysicalDescr": "Outlet #16 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1216000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1216150, "entPhysicalDescr": "Outlet #16 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1216000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1217000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #17", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel17", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 17, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1217110, "entPhysicalDescr": "Outlet #17 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1217000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1217120, "entPhysicalDescr": "Outlet #17 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1217000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1217130, "entPhysicalDescr": "Outlet #17 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1217000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1217140, "entPhysicalDescr": "Outlet #17 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1217000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1217150, "entPhysicalDescr": "Outlet #17 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1217000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1218000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #18", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel18", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 18, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1218110, "entPhysicalDescr": "Outlet #18 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1218000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1218120, "entPhysicalDescr": "Outlet #18 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1218000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1218130, "entPhysicalDescr": "Outlet #18 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1218000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1218140, "entPhysicalDescr": "Outlet #18 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1218000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1218150, "entPhysicalDescr": "Outlet #18 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1218000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1219000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #19", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel19", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 19, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1219110, "entPhysicalDescr": "Outlet #19 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1219000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1219120, "entPhysicalDescr": "Outlet #19 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1219000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1219130, "entPhysicalDescr": "Outlet #19 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1219000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1219140, "entPhysicalDescr": "Outlet #19 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1219000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1219150, "entPhysicalDescr": "Outlet #19 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1219000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1220000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #20", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel20", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 20, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1220110, "entPhysicalDescr": "Outlet #20 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1220000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1220120, "entPhysicalDescr": "Outlet #20 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1220000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1220130, "entPhysicalDescr": "Outlet #20 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1220000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1220140, "entPhysicalDescr": "Outlet #20 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1220000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1220150, "entPhysicalDescr": "Outlet #20 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1220000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1221000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #21", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel21", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 21, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1221110, "entPhysicalDescr": "Outlet #21 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1221000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1221120, "entPhysicalDescr": "Outlet #21 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1221000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1221130, "entPhysicalDescr": "Outlet #21 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1221000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1221140, "entPhysicalDescr": "Outlet #21 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1221000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1221150, "entPhysicalDescr": "Outlet #21 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1221000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1222000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #22", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel22", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 22, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1222110, "entPhysicalDescr": "Outlet #22 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1222000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1222120, "entPhysicalDescr": "Outlet #22 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1222000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1222130, "entPhysicalDescr": "Outlet #22 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1222000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1222140, "entPhysicalDescr": "Outlet #22 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1222000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1222150, "entPhysicalDescr": "Outlet #22 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1222000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1223000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #23", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel23", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 23, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1223110, "entPhysicalDescr": "Outlet #23 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1223000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1223120, "entPhysicalDescr": "Outlet #23 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1223000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1223130, "entPhysicalDescr": "Outlet #23 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1223000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1223140, "entPhysicalDescr": "Outlet #23 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1223000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1223150, "entPhysicalDescr": "Outlet #23 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1223000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1224000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #24", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel24", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 24, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1224110, "entPhysicalDescr": "Outlet #24 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1224000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1224120, "entPhysicalDescr": "Outlet #24 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1224000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1224130, "entPhysicalDescr": "Outlet #24 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1224000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1224140, "entPhysicalDescr": "Outlet #24 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1224000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1224150, "entPhysicalDescr": "Outlet #24 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1224000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1225000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #25", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel25", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 25, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1225110, "entPhysicalDescr": "Outlet #25 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1225000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1225120, "entPhysicalDescr": "Outlet #25 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1225000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1225130, "entPhysicalDescr": "Outlet #25 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1225000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1225140, "entPhysicalDescr": "Outlet #25 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1225000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1225150, "entPhysicalDescr": "Outlet #25 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1225000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1226000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #26", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel26", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 26, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1226110, "entPhysicalDescr": "Outlet #26 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1226000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1226120, "entPhysicalDescr": "Outlet #26 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1226000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1226130, "entPhysicalDescr": "Outlet #26 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1226000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1226140, "entPhysicalDescr": "Outlet #26 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1226000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1226150, "entPhysicalDescr": "Outlet #26 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1226000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1227000, "entPhysicalDescr": "PDU outlet", "entPhysicalClass": "powerSupply", "entPhysicalName": "Outlet #27", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "channel27", "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1200000, "entPhysicalParentRelPos": 27, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1227110, "entPhysicalDescr": "Outlet #27 voltage sensor (V)", "entPhysicalClass": "sensor", "entPhysicalName": "Voltage Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1227000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1227120, "entPhysicalDescr": "Outlet #27 RMS current sensor (A)", "entPhysicalClass": "sensor", "entPhysicalName": "Current Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1227000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1227130, "entPhysicalDescr": "Outlet #27 apparent power sensor (W)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1227000, "entPhysicalParentRelPos": 3, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1227140, "entPhysicalDescr": "Outlet #27 lifetime power consumed sensor (kWh)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Consumed Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1227000, "entPhysicalParentRelPos": 4, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1227150, "entPhysicalDescr": "Outlet #27 power factor sensor (ratio)", "entPhysicalClass": "sensor", "entPhysicalName": "Power Factor Sensor", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1227000, "entPhysicalParentRelPos": 5, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1300000, "entPhysicalDescr": "2 external sensors", "entPhysicalClass": "container", "entPhysicalName": "Sensor Container", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": null, "entPhysicalAssetID": null, "entPhysicalIsFRU": "false", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 10, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1301000, "entPhysicalDescr": "Dry switch contact (binary)", "entPhysicalClass": "sensor", "entPhysicalName": "External Sensor #1", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1300000, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}, {"entPhysicalIndex": 1302000, "entPhysicalDescr": "Dry switch contact (binary)", "entPhysicalClass": "sensor", "entPhysicalName": "External Sensor #2", "entPhysicalHardwareRev": null, "entPhysicalFirmwareRev": null, "entPhysicalSoftwareRev": null, "entPhysicalAlias": "", "entPhysicalAssetID": null, "entPhysicalIsFRU": "true", "entPhysicalModelName": null, "entPhysicalVendorType": null, "entPhysicalSerialNum": null, "entPhysicalContainedIn": 1300000, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "Schleifenbauer Products B.V.", "ifIndex": null}]}, "poller": "matches discovery"}}