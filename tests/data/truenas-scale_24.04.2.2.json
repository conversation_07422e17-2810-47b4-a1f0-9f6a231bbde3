{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.50536.3.1", "sysDescr": "TrueNAS-SCALE-24.04.2.2. Hardware: x86_64 Intel(R) Core(TM) i7-4790 CPU @ 3.60GHz. Software: Linux 6.6.32-production+truenas (revision #1 SMP PREEMPT_DYNAMIC Sat Sep 14 14:36:03 UTC 2024)", "sysContact": "<private>", "version": "24.04.2.2", "hardware": "x86_64 Intel(R) Core(TM) i7-4790 CPU @ 3.60GHz", "features": null, "location": "<private>", "os": "truenas-scale", "type": "storage", "serial": null, "icon": "truenas-scale.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "enp0s3", "ifName": "enp0s3", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "enp0s3", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 65536, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 19916, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 19916, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 8679954, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 8679954, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "enp0s3", "ifName": "enp0s3", "portName": null, "ifIndex": 2, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "enp0s3", "ifPhysAddress": "080027650af0", "ifLastChange": 7320507, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 2601935, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 220064, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 3722187865, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 80572497, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 5494, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 196608, "processor_oid": ".*******.2.1.25.3.3.1.2.196608", "processor_index": "196608", "processor_type": "hr", "processor_usage": 4, "processor_descr": "Intel Core i7-4790 @ 3.60GHz", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "system", "mempool_precision": 1024, "mempool_descr": "Physical memory", "mempool_perc": 88, "mempool_perc_oid": null, "mempool_used": 1814851584, "mempool_used_oid": ".*******.********.3.1.6.1", "mempool_free": 241467392, "mempool_free_oid": null, "mempool_total": 2056318976, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 99}, {"mempool_index": "3", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "virtual", "mempool_precision": 1024, "mempool_descr": "Virtual memory", "mempool_perc": 95, "mempool_perc_oid": null, "mempool_used": 1955573760, "mempool_used_oid": ".*******.********.3.1.6.3", "mempool_free": 100745216, "mempool_free_oid": null, "mempool_total": 2056318976, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 95}, {"mempool_index": "6", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "buffers", "mempool_precision": 1024, "mempool_descr": "Memory buffers", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": 0, "mempool_used_oid": ".*******.********.3.1.6.6", "mempool_free": 2056318976, "mempool_free_oid": null, "mempool_total": 2056318976, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "7", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "cached", "mempool_precision": 1024, "mempool_descr": "Cached memory", "mempool_perc": 7, "mempool_perc_oid": null, "mempool_used": 140722176, "mempool_used_oid": ".*******.********.3.1.6.7", "mempool_free": 1915596800, "mempool_free_oid": null, "mempool_total": 2056318976, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "8", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "shared", "mempool_precision": 1024, "mempool_descr": "Shared memory", "mempool_perc": 3, "mempool_perc_oid": null, "mempool_used": 65839104, "mempool_used_oid": ".*******.********.3.1.6.8", "mempool_free": 1990479872, "mempool_free_oid": null, "mempool_total": 2056318976, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcC.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Target Size (C)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 148572, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcData.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Data", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 50934, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcHitRatio.0", "sensor_type": "truenas-scale", "sensor_descr": "<PERSON> Hit Ratio Percentage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 99.8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcHits_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29495346, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMeta.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Metadata", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 52241, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMisses_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "AR<PERSON>es", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 57914, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMissPercent.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Miss Percent", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.19596484448755, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.********", "sensor_index": "zfsArcMissRatio.0", "sensor_type": "truenas-scale", "sensor_descr": "<PERSON> Miss Ratio Per<PERSON>age", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcSize.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 110376, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcHits_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcMisses_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Misses", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcRead_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Reads", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcSize.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcWrite_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Writes", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps10sec.0", "sensor_type": "truenas-scale", "sensor_descr": "ZIL Operations last 10 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps1sec.0", "sensor_type": "truenas-scale", "sensor_descr": "ZIL Operations last second", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps5sec.0", "sensor_type": "truenas-scale", "sensor_descr": "ZIL Operations last 5 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.3.1", "sensor_index": "zpoolHealth.1", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: boot-pool", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.3.2", "sensor_index": "zpoolHealth.2", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: juandi<PERSON>", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}], "state_indexes": [{"state_name": "zpoolTable", "state_descr": "ONLINE", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "zpoolTable", "state_descr": "DEGRADED", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "zpoolTable", "state_descr": "FAULTED", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "OFFLINE", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "UNAVAIL", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "REMOVED", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcC.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Target Size (C)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 148572, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcData.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Data", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 50934, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcHitRatio.0", "sensor_type": "truenas-scale", "sensor_descr": "<PERSON> Hit Ratio Percentage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 99.8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcHits_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29495346, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMeta.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Metadata", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 52241, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMisses_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "AR<PERSON>es", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 57914, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcMissPercent.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Miss Percent", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.19596484448755, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 0.19596484448755, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.********", "sensor_index": "zfsArcMissRatio.0", "sensor_type": "truenas-scale", "sensor_descr": "<PERSON> Miss Ratio Per<PERSON>age", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsArcSize.0", "sensor_type": "truenas-scale", "sensor_descr": "ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 110376, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcHits_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Hits", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcMisses_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Misses", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcRead_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Reads", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcSize.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Size", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsL2ArcWrite_rate.0", "sensor_type": "truenas-scale", "sensor_descr": "L2ARC Writes", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps10sec.0", "sensor_type": "truenas-scale", "sensor_descr": "ZIL Operations last 10 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps1sec.0", "sensor_type": "truenas-scale", "sensor_descr": "ZIL Operations last second", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******", "sensor_index": "zfsZilstatOps5sec.0", "sensor_type": "truenas-scale", "sensor_descr": "ZIL Operations last 5 seconds", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.3.1", "sensor_index": "zpoolHealth.1", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: boot-pool", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.50536.*******.3.2", "sensor_index": "zpoolHealth.2", "sensor_type": "zpoolTable", "sensor_descr": "ZPool: juandi<PERSON>", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "zpoolTable"}], "state_indexes": [{"state_name": "zpoolTable", "state_descr": "ONLINE", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "zpoolTable", "state_descr": "DEGRADED", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "zpoolTable", "state_descr": "FAULTED", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "OFFLINE", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "UNAVAIL", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "zpoolTable", "state_descr": "REMOVED", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}]}}, "storage": {"discovery": {"storage": [{"type": "hrstorage", "storage_index": "35", "storage_type": "hrStorageFixedDisk", "storage_descr": "/run", "storage_size": 205635584, "storage_size_oid": null, "storage_units": 4096, "storage_used": 65597440, "storage_used_oid": ".*******.********.3.1.6.35", "storage_free": 140038144, "storage_free_oid": null, "storage_perc": 32, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "36", "storage_type": "hrStorageFixedDisk", "storage_descr": "/", "storage_size": 3899523072, "storage_size_oid": null, "storage_units": 131072, "storage_used": 171573248, "storage_used_oid": ".*******.********.3.1.6.36", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "37", "storage_type": "hrStorageFixedDisk", "storage_descr": "/audit", "storage_size": 3728080896, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.3.1.6.37", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "38", "storage_type": "hrStorageFixedDisk", "storage_descr": "/conf", "storage_size": 3728080896, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.3.1.6.38", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "39", "storage_type": "hrStorageFixedDisk", "storage_descr": "/data", "storage_size": 3735814144, "storage_size_oid": null, "storage_units": 131072, "storage_used": 7864320, "storage_used_oid": ".*******.********.3.1.6.39", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "40", "storage_type": "hrStorageFixedDisk", "storage_descr": "/etc", "storage_size": 3731226624, "storage_size_oid": null, "storage_units": 131072, "storage_used": 3276800, "storage_used_oid": ".*******.********.3.1.6.40", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "41", "storage_type": "hrStorageFixedDisk", "storage_descr": "/home", "storage_size": 3728080896, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.3.1.6.41", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "42", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt", "storage_size": 3728080896, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.3.1.6.42", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "43", "storage_type": "hrStorageFixedDisk", "storage_descr": "/opt", "storage_size": 3803578368, "storage_size_oid": null, "storage_units": 131072, "storage_used": 75628544, "storage_used_oid": ".*******.********.3.1.6.43", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 2, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "44", "storage_type": "hrStorageFixedDisk", "storage_descr": "/root", "storage_size": 3728080896, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.3.1.6.44", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "45", "storage_type": "hrStorageFixedDisk", "storage_descr": "/usr", "storage_size": 5752881152, "storage_size_oid": null, "storage_units": 131072, "storage_used": 2024931328, "storage_used_oid": ".*******.********.3.1.6.45", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 35, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "46", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var", "storage_size": 3747741696, "storage_size_oid": null, "storage_units": 131072, "storage_used": 19791872, "storage_used_oid": ".*******.********.3.1.6.46", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 1, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "47", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/local/ca-certificates", "storage_size": 3728080896, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.3.1.6.47", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "48", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/log", "storage_size": 3730440192, "storage_size_oid": null, "storage_units": 131072, "storage_used": 2490368, "storage_used_oid": ".*******.********.3.1.6.48", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "50", "storage_type": "hrStorageFixedDisk", "storage_descr": "/dev/shm", "storage_size": 1028157440, "storage_size_oid": null, "storage_units": 4096, "storage_used": 98304, "storage_used_oid": ".*******.********.3.1.6.50", "storage_free": 1028059136, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "51", "storage_type": "hrStorageFixedDisk", "storage_descr": "/run/lock", "storage_size": 5242880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 0, "storage_used_oid": ".*******.********.3.1.6.51", "storage_free": 5242880, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "60", "storage_type": "hrStorageFixedDisk", "storage_descr": "/tmp", "storage_size": 1028157440, "storage_size_oid": null, "storage_units": 4096, "storage_used": 0, "storage_used_oid": ".*******.********.********", "storage_free": 1028157440, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "66", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/juandipool", "storage_size": 9176088576, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.********", "storage_free": 9175957504, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "68", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system", "storage_size": 10497687552, "storage_size_oid": null, "storage_units": 131072, "storage_used": 1321730048, "storage_used_oid": ".*******.********.********", "storage_free": 9175957504, "storage_free_oid": null, "storage_perc": 13, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "69", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/cores", "storage_size": 1073741824, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.********", "storage_free": 1073610752, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "70", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/samba4", "storage_size": 9176350720, "storage_size_oid": null, "storage_units": 131072, "storage_used": 393216, "storage_used_oid": ".*******.********.3.1.6.70", "storage_free": 9175957504, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "71", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/configs-f92d694b6b01475ca55fc635cbf72414", "storage_size": 9176219648, "storage_size_oid": null, "storage_units": 131072, "storage_used": 262144, "storage_used_oid": ".*******.********.3.1.6.71", "storage_free": 9175957504, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "72", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/db/system/netdata-f92d694b6b01475ca55fc635cbf72414", "storage_size": 9184083968, "storage_size_oid": null, "storage_units": 131072, "storage_used": 8126464, "storage_used_oid": ".*******.********.3.1.6.72", "storage_free": 9175957504, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "73", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/lib/systemd/coredump", "storage_size": 1073741824, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.3.1.6.73", "storage_free": 1073610752, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "76", "storage_type": "hrStorageFixedDisk", "storage_descr": "/boot/grub", "storage_size": 3730046976, "storage_size_oid": null, "storage_units": 131072, "storage_used": 2097152, "storage_used_oid": ".*******.********.********", "storage_free": 3727949824, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "78", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/juandipool/DatasetTerminator", "storage_size": 11408900096, "storage_size_oid": null, "storage_units": 131072, "storage_used": 2232942592, "storage_used_oid": ".*******.********.********", "storage_free": 9175957504, "storage_free_oid": null, "storage_perc": 20, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "79", "storage_type": "hrStorageFixedDisk", "storage_descr": "/mnt/juandipool/ai-defend-us-from-skynet", "storage_size": 9176088576, "storage_size_oid": null, "storage_units": 131072, "storage_used": 131072, "storage_used_oid": ".*******.********.********", "storage_free": 9175957504, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "truenas-scale-zv", "storage_index": "1", "storage_type": "zvol", "storage_descr": "juandipool/zvolTerminator", "storage_size": 20085932032, "storage_size_oid": null, "storage_units": 1, "storage_used": 5454970880, "storage_used_oid": ".*******.4.1.50536.*******.3.1", "storage_free": 14630961152, "storage_free_oid": ".*******.4.1.50536.*******.4.1", "storage_perc": 27, "storage_perc_oid": null, "storage_perc_warn": 60}]}, "poller": "matches discovery"}}