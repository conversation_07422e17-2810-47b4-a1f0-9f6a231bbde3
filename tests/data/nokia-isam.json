{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.637.61.1", "sysDescr": "R6.5.02r NFXS-E FANT-H NOKIA ISAM Copyright (c) 2016 Nokia. All rights reserved. All use subject to applicable license agreement. Nokia and Alcatel-Lucent are registered trademarks of Nokia Corporation.", "sysContact": "<private>", "version": "R6.5.02r", "hardware": "NFXS-E", "features": null, "location": "<private>", "os": "nokia-isam", "type": "network", "serial": "FH2027A0E3C", "icon": "nokia.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Nokia ASAM, OAM IP interface", "ifName": "Nokia ASAM, OAM IP interface", "portName": null, "ifIndex": 20972544, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Nokia ASAM, OAM IP interface", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Ethernet Link", "ifName": "P2P Ethernet interface", "portName": null, "ifIndex": 1079377920, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Ethernet Link", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Nokia ASAM, OAM IP interface", "ifName": "Nokia ASAM, OAM IP interface", "portName": null, "ifIndex": 20972544, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "Nokia ASAM, OAM IP interface", "ifPhysAddress": "aabbccddddff", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 773, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 156482, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Ethernet Link", "ifName": "P2P Ethernet interface", "portName": null, "ifIndex": 1079377920, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": null, "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1980, "ifType": "ethernetCsmacd", "ifAlias": "Ethernet Link", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "ports-stack": {"discovery": {"ports_stack": [{"high_ifIndex": 67109312, "low_ifIndex": 79692224, "ifStackStatus": "active"}, {"high_ifIndex": 67109824, "low_ifIndex": 79692736, "ifStackStatus": "active"}, {"high_ifIndex": 67111744, "low_ifIndex": 79694656, "ifStackStatus": "active"}, {"high_ifIndex": 67112384, "low_ifIndex": 79695296, "ifStackStatus": "active"}, {"high_ifIndex": 67112431, "low_ifIndex": 79695343, "ifStackStatus": "active"}, {"high_ifIndex": 67113280, "low_ifIndex": 79696192, "ifStackStatus": "active"}, {"high_ifIndex": 67113920, "low_ifIndex": 79696832, "ifStackStatus": "active"}, {"high_ifIndex": 67113967, "low_ifIndex": 79696879, "ifStackStatus": "active"}, {"high_ifIndex": 67114432, "low_ifIndex": 79697344, "ifStackStatus": "active"}, {"high_ifIndex": 67114479, "low_ifIndex": 79697391, "ifStackStatus": "active"}, {"high_ifIndex": 67114816, "low_ifIndex": 79697728, "ifStackStatus": "active"}, {"high_ifIndex": 67305920, "low_ifIndex": 79888832, "ifStackStatus": "active"}, {"high_ifIndex": 67305967, "low_ifIndex": 79888879, "ifStackStatus": "active"}, {"high_ifIndex": 67306016, "low_ifIndex": 79888928, "ifStackStatus": "active"}, {"high_ifIndex": 67306944, "low_ifIndex": 79889856, "ifStackStatus": "active"}, {"high_ifIndex": 67307968, "low_ifIndex": 79890880, "ifStackStatus": "active"}, {"high_ifIndex": 67308480, "low_ifIndex": 79891392, "ifStackStatus": "active"}, {"high_ifIndex": 67308864, "low_ifIndex": 79891776, "ifStackStatus": "active"}, {"high_ifIndex": 67309376, "low_ifIndex": 79892288, "ifStackStatus": "active"}, {"high_ifIndex": 67309888, "low_ifIndex": 79892800, "ifStackStatus": "active"}, {"high_ifIndex": 67311040, "low_ifIndex": 79893952, "ifStackStatus": "active"}, {"high_ifIndex": 67311552, "low_ifIndex": 79894464, "ifStackStatus": "active"}, {"high_ifIndex": 67312064, "low_ifIndex": 79894976, "ifStackStatus": "active"}, {"high_ifIndex": 67312576, "low_ifIndex": 79895488, "ifStackStatus": "active"}, {"high_ifIndex": 67313088, "low_ifIndex": 79896000, "ifStackStatus": "active"}, {"high_ifIndex": 67313472, "low_ifIndex": 79896384, "ifStackStatus": "active"}, {"high_ifIndex": 67314112, "low_ifIndex": 79897024, "ifStackStatus": "active"}, {"high_ifIndex": 67314208, "low_ifIndex": 79897120, "ifStackStatus": "active"}, {"high_ifIndex": 67355072, "low_ifIndex": 79937984, "ifStackStatus": "active"}, {"high_ifIndex": 67371456, "low_ifIndex": 79954368, "ifStackStatus": "active"}, {"high_ifIndex": 67371503, "low_ifIndex": 79954415, "ifStackStatus": "active"}, {"high_ifIndex": 67371968, "low_ifIndex": 79954880, "ifStackStatus": "active"}, {"high_ifIndex": 67372992, "low_ifIndex": 79955904, "ifStackStatus": "active"}, {"high_ifIndex": 67373504, "low_ifIndex": 79956416, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96468992, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96469504, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96471552, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96472064, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96473088, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96473600, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96474112, "ifStackStatus": "active"}, {"high_ifIndex": 71303168, "low_ifIndex": 96474624, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96665600, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96666112, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96666624, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96667648, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96668160, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96668672, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96669184, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96669696, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96670208, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96670720, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96671232, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96671744, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96672256, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96672768, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96673280, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96673792, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96674304, "ifStackStatus": "active"}, {"high_ifIndex": 71499776, "low_ifIndex": 96714752, "ifStackStatus": "active"}, {"high_ifIndex": 71565312, "low_ifIndex": 96731136, "ifStackStatus": "active"}, {"high_ifIndex": 71565312, "low_ifIndex": 96731648, "ifStackStatus": "active"}, {"high_ifIndex": 71565312, "low_ifIndex": 96732672, "ifStackStatus": "active"}, {"high_ifIndex": 71565312, "low_ifIndex": 96733184, "ifStackStatus": "active"}, {"high_ifIndex": 79692224, "low_ifIndex": 81788955, "ifStackStatus": "active"}, {"high_ifIndex": 79692224, "low_ifIndex": 81788957, "ifStackStatus": "active"}, {"high_ifIndex": 79692224, "low_ifIndex": 81788960, "ifStackStatus": "active"}, {"high_ifIndex": 79692224, "low_ifIndex": 81788969, "ifStackStatus": "active"}, {"high_ifIndex": 79692736, "low_ifIndex": 81788928, "ifStackStatus": "active"}, {"high_ifIndex": 79692736, "low_ifIndex": 81788933, "ifStackStatus": "active"}, {"high_ifIndex": 79692736, "low_ifIndex": 81788939, "ifStackStatus": "active"}, {"high_ifIndex": 79692736, "low_ifIndex": 81788948, "ifStackStatus": "active"}, {"high_ifIndex": 79694656, "low_ifIndex": 81788980, "ifStackStatus": "active"}, {"high_ifIndex": 79695296, "low_ifIndex": 81788958, "ifStackStatus": "active"}, {"high_ifIndex": 79695343, "low_ifIndex": 81788953, "ifStackStatus": "active"}, {"high_ifIndex": 79696192, "low_ifIndex": 81788942, "ifStackStatus": "active"}, {"high_ifIndex": 79696192, "low_ifIndex": 81788949, "ifStackStatus": "active"}, {"high_ifIndex": 79696832, "low_ifIndex": 81788952, "ifStackStatus": "active"}, {"high_ifIndex": 79696832, "low_ifIndex": 81788973, "ifStackStatus": "active"}, {"high_ifIndex": 79696832, "low_ifIndex": 81788978, "ifStackStatus": "active"}, {"high_ifIndex": 79696832, "low_ifIndex": 81788981, "ifStackStatus": "active"}, {"high_ifIndex": 79696879, "low_ifIndex": 81788986, "ifStackStatus": "active"}, {"high_ifIndex": 79697344, "low_ifIndex": 81788989, "ifStackStatus": "active"}, {"high_ifIndex": 79697344, "low_ifIndex": 81788990, "ifStackStatus": "active"}, {"high_ifIndex": 79697344, "low_ifIndex": 81788991, "ifStackStatus": "active"}, {"high_ifIndex": 79697391, "low_ifIndex": 81788992, "ifStackStatus": "active"}, {"high_ifIndex": 79697728, "low_ifIndex": 81788993, "ifStackStatus": "active"}, {"high_ifIndex": 79697728, "low_ifIndex": 81788994, "ifStackStatus": "active"}, {"high_ifIndex": 79697728, "low_ifIndex": 81788995, "ifStackStatus": "active"}, {"high_ifIndex": 79697728, "low_ifIndex": 81788996, "ifStackStatus": "active"}, {"high_ifIndex": 79888832, "low_ifIndex": 81985538, "ifStackStatus": "active"}, {"high_ifIndex": 79888832, "low_ifIndex": 81985539, "ifStackStatus": "active"}, {"high_ifIndex": 79888832, "low_ifIndex": 81985540, "ifStackStatus": "active"}, {"high_ifIndex": 79888879, "low_ifIndex": 81985544, "ifStackStatus": "active"}, {"high_ifIndex": 79888928, "low_ifIndex": 81985537, "ifStackStatus": "active"}, {"high_ifIndex": 79888928, "low_ifIndex": 81985548, "ifStackStatus": "active"}, {"high_ifIndex": 79888928, "low_ifIndex": 81985549, "ifStackStatus": "active"}, {"high_ifIndex": 79888928, "low_ifIndex": 81985555, "ifStackStatus": "active"}, {"high_ifIndex": 79889856, "low_ifIndex": 81985553, "ifStackStatus": "active"}, {"high_ifIndex": 79889856, "low_ifIndex": 81985554, "ifStackStatus": "active"}, {"high_ifIndex": 79890880, "low_ifIndex": 81985545, "ifStackStatus": "active"}, {"high_ifIndex": 79890880, "low_ifIndex": 81985546, "ifStackStatus": "active"}, {"high_ifIndex": 79891776, "low_ifIndex": 81985551, "ifStackStatus": "active"}, {"high_ifIndex": 79891776, "low_ifIndex": 81985558, "ifStackStatus": "active"}, {"high_ifIndex": 79891776, "low_ifIndex": 81985559, "ifStackStatus": "active"}, {"high_ifIndex": 79891776, "low_ifIndex": 81985567, "ifStackStatus": "active"}, {"high_ifIndex": 79892800, "low_ifIndex": 81985542, "ifStackStatus": "active"}, {"high_ifIndex": 79892800, "low_ifIndex": 81985543, "ifStackStatus": "active"}, {"high_ifIndex": 79892800, "low_ifIndex": 81985590, "ifStackStatus": "active"}, {"high_ifIndex": 79893952, "low_ifIndex": 81985572, "ifStackStatus": "active"}, {"high_ifIndex": 79893952, "low_ifIndex": 81985612, "ifStackStatus": "active"}, {"high_ifIndex": 79893952, "low_ifIndex": 81985613, "ifStackStatus": "active"}, {"high_ifIndex": 79894464, "low_ifIndex": 81985552, "ifStackStatus": "active"}, {"high_ifIndex": 79894464, "low_ifIndex": 81985574, "ifStackStatus": "active"}, {"high_ifIndex": 79894976, "low_ifIndex": 81985578, "ifStackStatus": "active"}, {"high_ifIndex": 79894976, "low_ifIndex": 81985579, "ifStackStatus": "active"}, {"high_ifIndex": 79894976, "low_ifIndex": 81985580, "ifStackStatus": "active"}, {"high_ifIndex": 79895488, "low_ifIndex": 81985585, "ifStackStatus": "active"}, {"high_ifIndex": 79895488, "low_ifIndex": 81985587, "ifStackStatus": "active"}, {"high_ifIndex": 79896000, "low_ifIndex": 81985575, "ifStackStatus": "active"}, {"high_ifIndex": 79896000, "low_ifIndex": 81985576, "ifStackStatus": "active"}, {"high_ifIndex": 79896000, "low_ifIndex": 81985614, "ifStackStatus": "active"}, {"high_ifIndex": 79896384, "low_ifIndex": 81985564, "ifStackStatus": "active"}, {"high_ifIndex": 79896384, "low_ifIndex": 81985583, "ifStackStatus": "active"}, {"high_ifIndex": 79897024, "low_ifIndex": 81985595, "ifStackStatus": "active"}, {"high_ifIndex": 79897024, "low_ifIndex": 81985596, "ifStackStatus": "active"}, {"high_ifIndex": 79897120, "low_ifIndex": 81985591, "ifStackStatus": "active"}, {"high_ifIndex": 79897120, "low_ifIndex": 81985592, "ifStackStatus": "active"}, {"high_ifIndex": 79897120, "low_ifIndex": 81985593, "ifStackStatus": "active"}, {"high_ifIndex": 79937984, "low_ifIndex": 81985570, "ifStackStatus": "active"}, {"high_ifIndex": 79937984, "low_ifIndex": 81985571, "ifStackStatus": "active"}, {"high_ifIndex": 79937984, "low_ifIndex": 81985584, "ifStackStatus": "active"}, {"high_ifIndex": 79954368, "low_ifIndex": 82051098, "ifStackStatus": "active"}, {"high_ifIndex": 79954368, "low_ifIndex": 82051105, "ifStackStatus": "active"}, {"high_ifIndex": 79954415, "low_ifIndex": 82051118, "ifStackStatus": "active"}, {"high_ifIndex": 79954880, "low_ifIndex": 82051141, "ifStackStatus": "active"}, {"high_ifIndex": 79954880, "low_ifIndex": 82051142, "ifStackStatus": "active"}, {"high_ifIndex": 79954880, "low_ifIndex": 82051143, "ifStackStatus": "active"}, {"high_ifIndex": 79955904, "low_ifIndex": 82051109, "ifStackStatus": "active"}, {"high_ifIndex": 79955904, "low_ifIndex": 82051144, "ifStackStatus": "active"}, {"high_ifIndex": 79955904, "low_ifIndex": 82051147, "ifStackStatus": "active"}, {"high_ifIndex": 79956416, "low_ifIndex": 82051145, "ifStackStatus": "active"}, {"high_ifIndex": 79956416, "low_ifIndex": 82051146, "ifStackStatus": "active"}, {"high_ifIndex": 94380032, "low_ifIndex": 71303168, "ifStackStatus": "active"}, {"high_ifIndex": 94445568, "low_ifIndex": 71368704, "ifStackStatus": "active"}, {"high_ifIndex": 94511104, "low_ifIndex": 71434240, "ifStackStatus": "active"}, {"high_ifIndex": 94576640, "low_ifIndex": 71499776, "ifStackStatus": "active"}, {"high_ifIndex": 94642176, "low_ifIndex": 71565312, "ifStackStatus": "active"}, {"high_ifIndex": 94707712, "low_ifIndex": 71630848, "ifStackStatus": "active"}, {"high_ifIndex": 94773248, "low_ifIndex": 71696384, "ifStackStatus": "active"}, {"high_ifIndex": 94838784, "low_ifIndex": 71761920, "ifStackStatus": "active"}, {"high_ifIndex": 94904320, "low_ifIndex": 71827456, "ifStackStatus": "active"}, {"high_ifIndex": 94969856, "low_ifIndex": 71892992, "ifStackStatus": "active"}, {"high_ifIndex": 95035392, "low_ifIndex": 71958528, "ifStackStatus": "active"}, {"high_ifIndex": 95100928, "low_ifIndex": 72024064, "ifStackStatus": "active"}, {"high_ifIndex": 95166464, "low_ifIndex": 72089600, "ifStackStatus": "active"}, {"high_ifIndex": 95232000, "low_ifIndex": 72155136, "ifStackStatus": "active"}, {"high_ifIndex": 95297536, "low_ifIndex": 72220672, "ifStackStatus": "active"}, {"high_ifIndex": 95363072, "low_ifIndex": 72286208, "ifStackStatus": "active"}, {"high_ifIndex": 96468992, "low_ifIndex": 67109312, "ifStackStatus": "active"}, {"high_ifIndex": 96468992, "low_ifIndex": 67109359, "ifStackStatus": "active"}, {"high_ifIndex": 96469504, "low_ifIndex": 67109824, "ifStackStatus": "active"}, {"high_ifIndex": 96469504, "low_ifIndex": 67109871, "ifStackStatus": "active"}, {"high_ifIndex": 96471552, "low_ifIndex": 67111744, "ifStackStatus": "active"}, {"high_ifIndex": 96471552, "low_ifIndex": 67111919, "ifStackStatus": "active"}, {"high_ifIndex": 96472064, "low_ifIndex": 67112384, "ifStackStatus": "active"}, {"high_ifIndex": 96472064, "low_ifIndex": 67112431, "ifStackStatus": "active"}, {"high_ifIndex": 96473088, "low_ifIndex": 67113280, "ifStackStatus": "active"}, {"high_ifIndex": 96473088, "low_ifIndex": 67113455, "ifStackStatus": "active"}, {"high_ifIndex": 96473600, "low_ifIndex": 67113920, "ifStackStatus": "active"}, {"high_ifIndex": 96473600, "low_ifIndex": 67113967, "ifStackStatus": "active"}, {"high_ifIndex": 96474112, "low_ifIndex": 67114432, "ifStackStatus": "active"}, {"high_ifIndex": 96474112, "low_ifIndex": 67114479, "ifStackStatus": "active"}, {"high_ifIndex": 96474624, "low_ifIndex": 67114816, "ifStackStatus": "active"}, {"high_ifIndex": 96474624, "low_ifIndex": 67114991, "ifStackStatus": "active"}, {"high_ifIndex": 96665600, "low_ifIndex": 67305920, "ifStackStatus": "active"}, {"high_ifIndex": 96665600, "low_ifIndex": 67305967, "ifStackStatus": "active"}, {"high_ifIndex": 96666112, "low_ifIndex": 67306016, "ifStackStatus": "active"}, {"high_ifIndex": 96666624, "low_ifIndex": 67306944, "ifStackStatus": "active"}, {"high_ifIndex": 96666624, "low_ifIndex": 67306991, "ifStackStatus": "active"}, {"high_ifIndex": 96667648, "low_ifIndex": 67307968, "ifStackStatus": "active"}, {"high_ifIndex": 96667648, "low_ifIndex": 67308015, "ifStackStatus": "active"}, {"high_ifIndex": 96668160, "low_ifIndex": 67308480, "ifStackStatus": "active"}, {"high_ifIndex": 96668672, "low_ifIndex": 67308864, "ifStackStatus": "active"}, {"high_ifIndex": 96668672, "low_ifIndex": 67309039, "ifStackStatus": "active"}, {"high_ifIndex": 96669184, "low_ifIndex": 67309376, "ifStackStatus": "active"}, {"high_ifIndex": 96669184, "low_ifIndex": 67309551, "ifStackStatus": "active"}, {"high_ifIndex": 96669696, "low_ifIndex": 67309888, "ifStackStatus": "active"}, {"high_ifIndex": 96669696, "low_ifIndex": 67310063, "ifStackStatus": "active"}, {"high_ifIndex": 96670720, "low_ifIndex": 67311040, "ifStackStatus": "active"}, {"high_ifIndex": 96670720, "low_ifIndex": 67311087, "ifStackStatus": "active"}, {"high_ifIndex": 96671232, "low_ifIndex": 67311552, "ifStackStatus": "active"}, {"high_ifIndex": 96671232, "low_ifIndex": 67311599, "ifStackStatus": "active"}, {"high_ifIndex": 96671744, "low_ifIndex": 67312064, "ifStackStatus": "active"}, {"high_ifIndex": 96672256, "low_ifIndex": 67312576, "ifStackStatus": "active"}, {"high_ifIndex": 96672256, "low_ifIndex": 67312623, "ifStackStatus": "active"}, {"high_ifIndex": 96672768, "low_ifIndex": 67313088, "ifStackStatus": "active"}, {"high_ifIndex": 96672768, "low_ifIndex": 67313135, "ifStackStatus": "active"}, {"high_ifIndex": 96673280, "low_ifIndex": 67313472, "ifStackStatus": "active"}, {"high_ifIndex": 96673280, "low_ifIndex": 67313647, "ifStackStatus": "active"}, {"high_ifIndex": 96673792, "low_ifIndex": 67314112, "ifStackStatus": "active"}, {"high_ifIndex": 96674304, "low_ifIndex": 67314208, "ifStackStatus": "active"}, {"high_ifIndex": 96674304, "low_ifIndex": 67314496, "ifStackStatus": "active"}, {"high_ifIndex": 96714752, "low_ifIndex": 67355072, "ifStackStatus": "active"}, {"high_ifIndex": 96731136, "low_ifIndex": 67371456, "ifStackStatus": "active"}, {"high_ifIndex": 96731136, "low_ifIndex": 67371503, "ifStackStatus": "active"}, {"high_ifIndex": 96731648, "low_ifIndex": 67371968, "ifStackStatus": "active"}, {"high_ifIndex": 96731648, "low_ifIndex": 67372015, "ifStackStatus": "active"}, {"high_ifIndex": 96732672, "low_ifIndex": 67372992, "ifStackStatus": "active"}, {"high_ifIndex": 96733184, "low_ifIndex": 67373504, "ifStackStatus": "active"}, {"high_ifIndex": 96733184, "low_ifIndex": 67373551, "ifStackStatus": "active"}]}}, "entity-physical": {"discovery": {"entPhysical": [{"entPhysicalIndex": 1, "entPhysicalDescr": "Power Unit", "entPhysicalClass": "13", "entPhysicalName": "NGFC-E", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "true", "entPhysicalModelName": "", "entPhysicalVendorType": "zeroDotZero", "entPhysicalSerialNum": "2024A831E", "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "ALCL", "ifIndex": null}, {"entPhysicalIndex": 2, "entPhysicalDescr": "Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "INA220", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "zeroDotZero", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1, "entPhysicalParentRelPos": 1, "entPhysicalMfgName": "TI", "ifIndex": null}, {"entPhysicalIndex": 3, "entPhysicalDescr": "Power Sensor", "entPhysicalClass": "sensor", "entPhysicalName": "INA220", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "zeroDotZero", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 1, "entPhysicalParentRelPos": 2, "entPhysicalMfgName": "TI", "ifIndex": null}]}, "poller": "matches discovery"}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.637.61.1.9.29.1.1.4.4354", "processor_index": "4354", "processor_type": "nokia-isam", "processor_usage": 1, "processor_descr": "CPU 4354", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.637.61.1.9.29.1.1.4.4355", "processor_index": "4355", "processor_type": "nokia-isam", "processor_usage": 7, "processor_descr": "CPU 4355", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "4354", "entPhysicalIndex": null, "mempool_type": "nokia-isam", "mempool_class": "system", "mempool_precision": 1048576, "mempool_descr": "FANT-H: 255 Memory (4354)", "mempool_perc": 75, "mempool_perc_oid": null, "mempool_used": 1158676480, "mempool_used_oid": ".*******.4.1.637.61.1.9.29.2.1.2.4354", "mempool_free": 377487360, "mempool_free_oid": null, "mempool_total": 1536163840, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 98}, {"mempool_index": "4355", "entPhysicalIndex": null, "mempool_type": "nokia-isam", "mempool_class": "system", "mempool_precision": 1048576, "mempool_descr": "FWLT-C: 2 Memory (4355)", "mempool_perc": 62, "mempool_perc_oid": null, "mempool_used": 1449132032, "mempool_used_oid": ".*******.4.1.637.61.1.9.29.2.1.2.4355", "mempool_free": 870318080, "mempool_free_oid": null, "mempool_total": 2319450112, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 98}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.6.4355.1", "sensor_index": "lt:1/1/1/1-tx", "sensor_type": "nokia-isam", "sensor_descr": "lt:1/1/1/1 Tx Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4.94, "sensor_limit": -3, "sensor_limit_warn": -4, "sensor_limit_low": -9, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.6.4355.4", "sensor_index": "lt:1/1/1/4-tx", "sensor_type": "nokia-isam", "sensor_descr": "lt:1/1/1/4 Tx Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": -3, "sensor_limit_warn": -4, "sensor_limit_low": -9, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.6.4355.5", "sensor_index": "lt:1/1/1/5-tx", "sensor_type": "nokia-isam", "sensor_descr": "lt:1/1/1/5 Tx Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5.72, "sensor_limit": -3, "sensor_limit_warn": -4, "sensor_limit_low": -9, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4354.257", "sensor_index": "nt-b:xfp:1-rx", "sensor_type": "nokia-isam", "sensor_descr": "nt-b:xfp:1 Rx Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": -9.36, "sensor_limit": -3, "sensor_limit_warn": -5, "sensor_limit_low": -22, "sensor_limit_low_warn": -20, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.6.4354.257", "sensor_index": "nt-b:xfp:1-tx", "sensor_type": "nokia-isam", "sensor_descr": "nt-b:xfp:1 Tx Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": -0.34, "sensor_limit": -3, "sensor_limit_warn": -4, "sensor_limit_low": -9, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4352", "sensor_index": "35.4352", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4353", "sensor_index": "35.4353", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4354", "sensor_index": "35.4354", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4355", "sensor_index": "35.4355", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4356", "sensor_index": "35.4356", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4357", "sensor_index": "35.4357", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4358", "sensor_index": "35.4358", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4359", "sensor_index": "35.4359", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4360", "sensor_index": "35.4360", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4361", "sensor_index": "35.4361", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4362", "sensor_index": "35.4362", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.35.4481", "sensor_index": "35.4481", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4352", "sensor_index": "36.4352", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4353", "sensor_index": "36.4353", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4354", "sensor_index": "36.4354", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4355", "sensor_index": "36.4355", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4356", "sensor_index": "36.4356", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4357", "sensor_index": "36.4357", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4358", "sensor_index": "36.4358", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4359", "sensor_index": "36.4359", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4360", "sensor_index": "36.4360", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4361", "sensor_index": "36.4361", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4362", "sensor_index": "36.4362", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.36.4481", "sensor_index": "36.4481", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4352", "sensor_index": "37.4352", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4353", "sensor_index": "37.4353", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4354", "sensor_index": "37.4354", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4355", "sensor_index": "37.4355", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4356", "sensor_index": "37.4356", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4357", "sensor_index": "37.4357", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4358", "sensor_index": "37.4358", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4359", "sensor_index": "37.4359", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4360", "sensor_index": "37.4360", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4361", "sensor_index": "37.4361", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4362", "sensor_index": "37.4362", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.37.4481", "sensor_index": "37.4481", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4352", "sensor_index": "38.4352", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4353", "sensor_index": "38.4353", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4354", "sensor_index": "38.4354", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4355", "sensor_index": "38.4355", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4356", "sensor_index": "38.4356", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4357", "sensor_index": "38.4357", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4358", "sensor_index": "38.4358", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4359", "sensor_index": "38.4359", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4360", "sensor_index": "38.4360", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4361", "sensor_index": "38.4361", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4362", "sensor_index": "38.4362", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.38.4481", "sensor_index": "38.4481", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4352", "sensor_index": "39.4352", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4353", "sensor_index": "39.4353", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4354", "sensor_index": "39.4354", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4355", "sensor_index": "39.4355", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4356", "sensor_index": "39.4356", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4357", "sensor_index": "39.4357", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4358", "sensor_index": "39.4358", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4359", "sensor_index": "39.4359", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4360", "sensor_index": "39.4360", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4361", "sensor_index": "39.4361", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4362", "sensor_index": "39.4362", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.39.4481", "sensor_index": "39.4481", "sensor_type": "eqptBoardOperError", "sensor_descr": "  ()", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4352", "sensor_index": "4352", "sensor_type": "eqptBoardOperError", "sensor_descr": "acu:1/1/ NGFC-E (NGFC-E)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4353", "sensor_index": "4353", "sensor_type": "eqptBoardOperError", "sensor_descr": "nt-a: FANT-H (FANT-H)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4354", "sensor_index": "4354", "sensor_type": "eqptBoardOperError", "sensor_descr": "nt-b: FANT-H (FANT-H)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4355", "sensor_index": "4355", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/1/ FWLT-C (FWLT-C)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4356", "sensor_index": "4356", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/2/ EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4357", "sensor_index": "4357", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/3/ EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4358", "sensor_index": "4358", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/4/ EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4359", "sensor_index": "4359", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/5/ EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4360", "sensor_index": "4360", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/6/ EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4361", "sensor_index": "4361", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/7/ EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4362", "sensor_index": "4362", "sensor_type": "eqptBoardOperError", "sensor_descr": "lt:1/1/8/ EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.1.7.4481", "sensor_index": "4481", "sensor_type": "eqptBoardOperError", "sensor_descr": "4481 EMPTY (NOT_PLANNED)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "eqptBoardOperError"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.*********.9.0", "sensor_index": "0", "sensor_type": "fanMode", "sensor_descr": "Fan Mode", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fanMode"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.**********.1.2.4353.1", "sensor_index": "nt-a:.1-temp", "sensor_type": "nokia-isam", "sensor_descr": "nt-a: Sensor 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 43, "sensor_limit": 0, "sensor_limit_warn": 0, "sensor_limit_low": -9, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.637.**********.1.2.4353.2", "sensor_index": "nt-a:.2-temp", "sensor_type": "nokia-isam", "sensor_descr": "nt-a: Sensor 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 53, "sensor_limit": 0, "sensor_limit_warn": 0, "sensor_limit_low": -9, "sensor_limit_low_warn": -8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "eqptBoardOperError", "state_descr": "no-error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "eqptBoardOperError", "state_descr": "type-mismatch", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "board-missing", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "board-installation-missing", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "no-planned-board", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "waiting-for-sw", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "init-boot-failed", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "init-download-failed", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "init-connection-failed", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "init-configuration-failed", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "board-reset-protection", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "invalid-parameter", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "temperature-alarm", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "tempshutdown", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "defense", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "board-not-licensed", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "sem-power-fail", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "sem-ups-fail", "state_draw_graph": 0, "state_value": 18, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "board-in-incompatible-slot", "state_draw_graph": 0, "state_value": 19, "state_generic_value": 2}, {"state_name": "eqptBoardOperError", "state_descr": "download-ongoing", "state_draw_graph": 0, "state_value": 21, "state_generic_value": 1}, {"state_name": "eqptBoardOperError", "state_descr": "unknown-error", "state_draw_graph": 0, "state_value": 255, "state_generic_value": 2}, {"state_name": "fanMode", "state_descr": "default", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "fanMode", "state_descr": "eco", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 1}, {"state_name": "fanMode", "state_descr": "protect", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "fanMode", "state_descr": "classic", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}]}, "poller": "matches discovery"}}