{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.534.1", "sysDescr": "ConnectUPS Web/SNMP Card V4.36", "sysContact": "<private>", "version": "FP: 0.13  INV: 1.29", "hardware": "PW9130 1000VA-R", "features": null, "location": "<private>", "os": "eatonups", "type": "power", "serial": "GG435A0270", "icon": "eaton.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "UM9008", "ifName": "UM9008", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "iso88023Csmacd", "ifAlias": "UM9008", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "UM9008", "ifName": "UM9008", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "iso88023Csmacd", "ifAlias": "UM9008", "ifPhysAddress": "00e0d8173bf3", "ifLastChange": 1640132, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 48639, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 47629, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 5772939, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 7088615, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 25, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "0", "sensor_type": "eatonups", "sensor_descr": "Battery Charge", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 100, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": 10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.3.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Output", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "3.1.0", "sensor_type": "xups", "sensor_descr": "Input", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 52.5, "sensor_limit_warn": null, "sensor_limit_low": 47.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "4.2.0", "sensor_type": "xups", "sensor_descr": "Output", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 52.5, "sensor_limit_warn": null, "sensor_limit_low": 47.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "5.1.0", "sensor_type": "xups", "sensor_descr": "Bypass", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 5, "sensor_limit": 5.25, "sensor_limit_warn": null, "sensor_limit_low": 4.75, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "load", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsOutputLoad.0", "sensor_type": "eatonups", "sensor_descr": "Output Load", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 80, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.4.1", "sensor_index": "1", "sensor_type": "eatonups", "sensor_descr": "Power Usage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 246, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "runtime", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsBatTimeRemaining.0", "sensor_type": "eatonups", "sensor_descr": "Runtime", "group": null, "sensor_divisor": 60, "sensor_multiplier": 1, "sensor_current": 32.466666666667, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsBatteryAbmStatus.0", "sensor_type": "xupsBattery", "sensor_descr": "Battery Status 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "xupsBattery"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.0", "sensor_index": "xupsTestBatteryStatus.0", "sensor_type": "xupsTest", "sensor_descr": "Battery Test Status 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "xupsTest"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.0.0", "sensor_index": "xupsTestBatteryStatus.0.0", "sensor_type": "xupsTest", "sensor_descr": "Battery Test Status 0.0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "xupsTest"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsEnvAmbientTemp.0", "sensor_type": "eatonups", "sensor_descr": "Ambient", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 45, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "*******", "sensor_type": "xups", "sensor_descr": "Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 44.85, "sensor_limit_warn": null, "sensor_limit_low": 33.15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.2.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Input", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 234, "sensor_limit": 269.1, "sensor_limit_warn": null, "sensor_limit_low": 198.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.2.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Output", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 229, "sensor_limit": 263.35, "sensor_limit_warn": null, "sensor_limit_low": 194.65, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.2.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Bypass", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 234, "sensor_limit": 269.1, "sensor_limit_warn": null, "sensor_limit_low": 198.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "xupsBattery", "state_descr": "batteryDischarging", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "xupsBattery", "state_descr": "batteryFloating", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "xupsBattery", "state_descr": "batteryResting", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "xupsBattery", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 3}, {"state_name": "xupsBattery", "state_descr": "batteryDisconnected", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "xupsBattery", "state_descr": "batteryUnderTest", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}, {"state_name": "xupsBattery", "state_descr": "checkBattery", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 2}, {"state_name": "xupsTest", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "xupsTest", "state_descr": "passed", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "xupsTest", "state_descr": "failed", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "xupsTest", "state_descr": "inProgress", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "xupsTest", "state_descr": "notSupported", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "xupsTest", "state_descr": "inhibited", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "xupsTest", "state_descr": "scheduled", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "0", "sensor_type": "eatonups", "sensor_descr": "Battery Charge", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 100, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": 10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.3.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Output", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "3.1.0", "sensor_type": "xups", "sensor_descr": "Input", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 52.5, "sensor_limit_warn": null, "sensor_limit_low": 47.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "4.2.0", "sensor_type": "xups", "sensor_descr": "Output", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 52.5, "sensor_limit_warn": null, "sensor_limit_low": 47.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "5.1.0", "sensor_type": "xups", "sensor_descr": "Bypass", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 5.25, "sensor_limit_warn": null, "sensor_limit_low": 4.75, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 5, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "load", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsOutputLoad.0", "sensor_type": "eatonups", "sensor_descr": "Output Load", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 80, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.4.1", "sensor_index": "1", "sensor_type": "eatonups", "sensor_descr": "Power Usage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 246, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "runtime", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsBatTimeRemaining.0", "sensor_type": "eatonups", "sensor_descr": "Runtime", "group": null, "sensor_divisor": 60, "sensor_multiplier": 1, "sensor_current": 32.466666666667, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 32.466666666667, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsBatteryAbmStatus.0", "sensor_type": "xupsBattery", "sensor_descr": "Battery Status 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "xupsBattery"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.0", "sensor_index": "xupsTestBatteryStatus.0", "sensor_type": "xupsTest", "sensor_descr": "Battery Test Status 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "xupsTest"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.0.0", "sensor_index": "xupsTestBatteryStatus.0.0", "sensor_type": "xupsTest", "sensor_descr": "Battery Test Status 0.0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 2, "user_func": null, "rrd_type": "GAUGE", "state_name": "xupsTest"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "xupsEnvAmbientTemp.0", "sensor_type": "eatonups", "sensor_descr": "Ambient", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 45, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******", "sensor_index": "*******", "sensor_type": "xups", "sensor_descr": "Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 44.85, "sensor_limit_warn": null, "sensor_limit_low": 33.15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.2.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Input", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 234, "sensor_limit": 269.1, "sensor_limit_warn": null, "sensor_limit_low": 198.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.2.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Output", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 229, "sensor_limit": 263.35, "sensor_limit_warn": null, "sensor_limit_low": 194.65, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.534.*******.2.1", "sensor_index": "*******.1", "sensor_type": "xups", "sensor_descr": "Bypass", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 234, "sensor_limit": 269.1, "sensor_limit_warn": null, "sensor_limit_low": 198.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "xupsBattery", "state_descr": "batteryDischarging", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "xupsBattery", "state_descr": "batteryFloating", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "xupsBattery", "state_descr": "batteryResting", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "xupsBattery", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 3}, {"state_name": "xupsBattery", "state_descr": "batteryDisconnected", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "xupsBattery", "state_descr": "batteryUnderTest", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}, {"state_name": "xupsBattery", "state_descr": "checkBattery", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 2}, {"state_name": "xupsTest", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "xupsTest", "state_descr": "passed", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "xupsTest", "state_descr": "failed", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "xupsTest", "state_descr": "inProgress", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "xupsTest", "state_descr": "notSupported", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 0}, {"state_name": "xupsTest", "state_descr": "inhibited", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "xupsTest", "state_descr": "scheduled", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 0}]}}}