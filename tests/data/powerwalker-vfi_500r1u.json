{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.3808.1.1.1", "sysDescr": "Power Management Card", "sysContact": "<private>", "version": "1.0.6", "hardware": "500R", "features": null, "location": "<private>", "os": "powerwalker-vfi", "type": "power", "serial": null, "icon": "powerwalker.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ag...", "ifName": "ag...", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "0", "ifAlias": "ag...", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ag...", "ifName": "ag...", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "7", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "0", "ifAlias": "ag...", "ifPhysAddress": "000c1503512b", "ifLastChange": 8343900, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.********.2.4.0", "sensor_index": "upsEstimatedChargeRemaining.0", "sensor_type": "powerwalker-vfi", "sensor_descr": "Battery charge remaining", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 100, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsInputCurrent.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "Input Phase 1", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsOutputCurrent.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "Output Phase 1", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.********.5.1.0", "sensor_index": "upsBypassputFrequency.0", "sensor_type": "powerwalker-vfi", "sensor_descr": "Bypass Frequency 0", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 52.5, "sensor_limit_warn": null, "sensor_limit_low": 47.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsInputFrequency.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "Input Frequency 1", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 52.5, "sensor_limit_warn": null, "sensor_limit_low": 47.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.********.4.2.0", "sensor_index": "upsOutputFrequency.0", "sensor_type": "powerwalker-vfi", "sensor_descr": "Output Frequency 0", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 52.5, "sensor_limit_warn": null, "sensor_limit_low": 47.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "load", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsOutputPercentLoad.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "Percentage Load 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 80, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsInputTruePower.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "System Input Watts 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsOutputPower.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "System Output Watts 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "runtime", "poller_type": "snmp", "sensor_oid": ".*******.********.2.3.0", "sensor_index": "upsEstimatedMinutesRemaining.0", "sensor_type": "powerwalker-vfi", "sensor_descr": "Estimated time on battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 20, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "runtime", "poller_type": "snmp", "sensor_oid": ".*******.********.2.2.0", "sensor_index": "upsSecondsOnBattery.0", "sensor_type": "powerwalker-vfi", "sensor_descr": "Current time on battery", "group": null, "sensor_divisor": 60, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.********.2.1.0", "sensor_index": "0", "sensor_type": "upsBatteryStatus", "sensor_descr": "Battery Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.********.4.1.0", "sensor_index": "0", "sensor_type": "upsOutputSource", "sensor_descr": "Output Source", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsOutputSource"}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.2.5.0", "sensor_index": "upsBatteryVoltage.0", "sensor_type": "powerwalker-vfi", "sensor_descr": "Battery Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 13.6, "sensor_limit": 15.64, "sensor_limit_warn": null, "sensor_limit_low": 11.56, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsInputVoltage.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "Input Phase 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 227, "sensor_limit": 261.05, "sensor_limit_warn": null, "sensor_limit_low": 192.95, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.********.*******.1", "sensor_index": "upsOutputVoltage.1", "sensor_type": "powerwalker-vfi", "sensor_descr": "Output Phase 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 226, "sensor_limit": 259.9, "sensor_limit_warn": null, "sensor_limit_low": 192.1, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "upsBatteryStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "upsBatteryStatus", "state_descr": "batteryNormal", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "upsBatteryStatus", "state_descr": "batteryLow", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "upsBatteryStatus", "state_descr": "batteryDepleted", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "upsOutputSource", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "upsOutputSource", "state_descr": "none", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "upsOutputSource", "state_descr": "normal", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "upsOutputSource", "state_descr": "bypass", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "upsOutputSource", "state_descr": "battery", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "upsOutputSource", "state_descr": "booster", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "upsOutputSource", "state_descr": "reducer", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 2}]}, "poller": "matches discovery"}}