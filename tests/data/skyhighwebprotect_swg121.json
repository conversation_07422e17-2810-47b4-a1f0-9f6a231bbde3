{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.1230.2.7.1.1", "sysDescr": "Skyhigh Secure Web Gateway 7;VMWare;VMware, Inc.", "sysContact": "<private>", "version": "12.1.3", "hardware": null, "features": null, "location": "<private>", "os": "skyhighwebprotect", "type": "firewall", "serial": null, "icon": "skyhigh.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 196608, "processor_oid": ".*******.2.1.25.3.3.1.2.196608", "processor_index": "196608", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 6152 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196609, "processor_oid": ".*******.2.1.25.3.3.1.2.196609", "processor_index": "196609", "processor_type": "hr", "processor_usage": 3, "processor_descr": "Intel Xeon Gold 6152 @ 2.10GHz", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "system", "mempool_precision": 1024, "mempool_descr": "Physical memory", "mempool_perc": 38, "mempool_perc_oid": null, "mempool_used": 3149291520, "mempool_used_oid": ".*******.2.1.25.*******.1", "mempool_free": 5075726336, "mempool_free_oid": null, "mempool_total": 8225017856, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 99}, {"mempool_index": "3", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "virtual", "mempool_precision": 1024, "mempool_descr": "Virtual memory", "mempool_perc": 66, "mempool_perc_oid": null, "mempool_used": 6775844864, "mempool_used_oid": ".*******.2.1.25.*******.3", "mempool_free": 3447754752, "mempool_free_oid": null, "mempool_total": 10223599616, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 95}, {"mempool_index": "6", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "buffers", "mempool_precision": 1024, "mempool_descr": "Memory buffers", "mempool_perc": 4, "mempool_perc_oid": null, "mempool_used": 298156032, "mempool_used_oid": ".*******.2.1.25.*******.6", "mempool_free": 7926861824, "mempool_free_oid": null, "mempool_total": 8225017856, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "7", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "cached", "mempool_precision": 1024, "mempool_descr": "Cached memory", "mempool_perc": 40, "mempool_perc_oid": null, "mempool_used": 3327860736, "mempool_used_oid": ".*******.2.1.25.*******.7", "mempool_free": 4897157120, "mempool_free_oid": null, "mempool_total": 8225017856, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "8", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "shared", "mempool_precision": 1024, "mempool_descr": "Shared memory", "mempool_perc": 3, "mempool_perc_oid": null, "mempool_used": 250208256, "mempool_used_oid": ".*******.2.1.25.*******.8", "mempool_free": 7974809600, "mempool_free_oid": null, "mempool_total": 8225017856, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "10", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "swap", "mempool_precision": 1024, "mempool_descr": "Swap space", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": 536576, "mempool_used_oid": ".*******.2.1.25.*******.10", "mempool_free": 1998045184, "mempool_free_oid": null, "mempool_total": 1998581760, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 10}]}, "poller": "matches discovery"}, "storage": {"discovery": {"storage": [{"type": "hrstorage", "storage_index": "33", "storage_type": "hrStorageFixedDisk", "storage_descr": "/run", "storage_size": 4112506880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 235536384, "storage_used_oid": ".*******.2.1.25.*******.33", "storage_free": 3876970496, "storage_free_oid": null, "storage_perc": 6, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "36", "storage_type": "hrStorageFixedDisk", "storage_descr": "/dev/shm", "storage_size": 4112506880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 12746752, "storage_used_oid": ".*******.2.1.25.*******.36", "storage_free": 4099760128, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "37", "storage_type": "hrStorageFixedDisk", "storage_descr": "/", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.37", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "56", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/localtime", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.56", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "57", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/named.root.key", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.57", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "58", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/named.conf", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.58", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "59", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/named.rfc1912.zones", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.59", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "60", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/rndc.key", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.60", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "61", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/named.iscdlv.key", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.61", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "62", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/crypto-policies/back-ends/bind.config", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.62", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "63", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/protocols", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.63", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "64", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/services", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.64", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "65", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/etc/named", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.65", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "66", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/usr/lib64/bind", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.66", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "67", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/run/named", "storage_size": 4112506880, "storage_size_oid": null, "storage_units": 4096, "storage_used": 235536384, "storage_used_oid": ".*******.2.1.25.*******.67", "storage_free": 3876970496, "storage_free_oid": null, "storage_perc": 6, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "68", "storage_type": "hrStorageFixedDisk", "storage_descr": "/var/named/chroot/var/named", "storage_size": 18683777024, "storage_size_oid": null, "storage_units": 4096, "storage_used": 5379362816, "storage_used_oid": ".*******.2.1.25.*******.68", "storage_free": 13304414208, "storage_free_oid": null, "storage_perc": 29, "storage_perc_oid": null, "storage_perc_warn": 60}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.4.0", "sensor_index": "stBlockedByAntiMalware0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Connections blocked by Anti-Malware", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.6.0", "sensor_index": "stBlockedByMediaFilter0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Connections blocked by the Media-Type", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.7.0", "sensor_index": "stBlockedByURLFilter0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Connections blocked by the URL filter", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.2.0", "sensor_index": "stClientCount.0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Connected clients", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.3.0", "sensor_index": "stConnectedSockets.0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Open network sockets", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.5.0", "sensor_index": "stConnectionsBlocked0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Total Connections blocked", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.3.0", "sensor_index": "stConnectionsLegitimate0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Total requests", "group": "Requests", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.1.0", "sensor_index": "stHttpRequests0", "sensor_type": "skyhighwebprotect", "sensor_descr": "HTTP requests", "group": "Requests", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.1.0", "sensor_index": "stHttpsRequests0", "sensor_type": "skyhighwebprotect", "sensor_descr": "HTTPS requests", "group": "Requests", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.2.0", "sensor_index": "stMalwareDetected0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Infections detected by Antimalware", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.8.0", "sensor_index": "stMimeType0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Media types detected by the Media Type filter", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "COUNTER", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.59732.*******.6.0", "sensor_index": "stResolveHostViaDNS.0", "sensor_type": "skyhighwebprotect", "sensor_descr": "Time to resolve DNS (ms)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}]}, "poller": "matches discovery"}}