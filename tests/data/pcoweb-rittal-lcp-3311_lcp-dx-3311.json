{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.9839.2606.3311", "sysDescr": "HVAC Unit 2", "sysContact": "<private>", "version": null, "hardware": null, "features": null, "location": null, "os": "pcoweb-rittal-lcp-3311", "type": "environment", "serial": null, "icon": "rittal.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 16436, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1164, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1164, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 154520, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 154520, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": "000a5c10d182", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 191529, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 29841, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 1, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 13327264, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 4, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.2021.11.11.0", "processor_index": "0", "processor_type": "ucd-old", "processor_usage": 11, "processor_descr": "CPU", "processor_precision": -1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "ucd", "mempool_class": "system", "mempool_precision": 1024, "mempool_descr": "Physical memory", "mempool_perc": 44, "mempool_perc_oid": null, "mempool_used": 6680576, "mempool_used_oid": null, "mempool_free": 8536064, "mempool_free_oid": ".*******.4.1.2021.4.6.0", "mempool_total": 15216640, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": null}, {"mempool_index": "3", "entPhysicalIndex": null, "mempool_type": "ucd", "mempool_class": "buffers", "mempool_precision": 1024, "mempool_descr": "Memory buffers", "mempool_perc": 8, "mempool_perc_oid": null, "mempool_used": 1236992, "mempool_used_oid": ".*******.4.1.2021.4.14.0", "mempool_free": 13979648, "mempool_free_oid": null, "mempool_total": 15216640, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": null}, {"mempool_index": "4", "entPhysicalIndex": null, "mempool_type": "ucd", "mempool_class": "cached", "mempool_precision": 1024, "mempool_descr": "Cached memory", "mempool_perc": 40, "mempool_perc_oid": null, "mempool_used": 6131712, "mempool_used_oid": ".*******.4.1.2021.4.15.0", "mempool_free": 9084928, "mempool_free_oid": null, "mempool_total": 15216640, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": null}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "cooling", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "coolingCapacity.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Free cooling capacity", "group": null, "sensor_divisor": 1, "sensor_multiplier": 120, "sensor_current": 10080, "sensor_limit": 10584, "sensor_limit_warn": null, "sensor_limit_low": 1000, "sensor_limit_low_warn": 1500, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorMotorCurrent.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Compressor motor current", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 6.1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "fanSpeedRpm.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Fan speed", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1295, "sensor_limit": 3600, "sensor_limit_warn": 3400, "sensor_limit_low": 1036, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "pressure", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorDischargePressure.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Compressor discharge pressure", "group": null, "sensor_divisor": 1, "sensor_multiplier": 10, "sensor_current": 2290, "sensor_limit": 2900, "sensor_limit_warn": 2800, "sensor_limit_low": 2175.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "pressure", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorSuctionPressure.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Compressor suction pressure", "group": null, "sensor_divisor": 1, "sensor_multiplier": 10, "sensor_current": 750, "sensor_limit": 2900, "sensor_limit_warn": 2800, "sensor_limit_low": 712.5, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******5.0", "sensor_index": "compressorDeltaPressureAlarm.0", "sensor_type": "compressorDeltaPressureAlarm", "sensor_descr": "Compressor delta pressure", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorDeltaPressureAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorDischargePressureProbeAlarm.0", "sensor_type": "compressorDischargePressureProbeAlarm", "sensor_descr": "Compressor discharge pressure probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorDischargePressureProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorDischargeTemperatureProbeAlarm.0", "sensor_type": "compressorDischargeTemperatureProbeAlarm", "sensor_descr": "Compressor discharge temperature probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorDischargeTemperatureProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorEnvelopeAlarm.0", "sensor_type": "compressorEnvelopeAlarm", "sensor_descr": "Compressor envelope", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorEnvelopeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "compressorOverloadAlarm.0", "sensor_type": "compressorOverloadAlarm", "sensor_descr": "Compressor overload", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorOverloadAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorStartupFailureAlarm.0", "sensor_type": "compressorStartupFailureAlarm", "sensor_descr": "Compressor startup (max retries)", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorStartupFailureAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorSuctionPressureProbeAlarm.0", "sensor_type": "compressorSuctionPressureProbeAlarm", "sensor_descr": "Compressor suction pressure probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorSuctionPressureProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorSuctionTemperatureProbeAlarm.0", "sensor_type": "compressorSuctionTemperatureProbeAlarm", "sensor_descr": "Compressor suction temperature probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "compressorSuctionTemperatureProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "currentErrorCode.0", "sensor_type": "currentErrorCode", "sensor_descr": "Current Error code", "group": "Alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "currentErrorCode"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "driveAlarm.0", "sensor_type": "driveAlarm", "sensor_descr": "Power+ drive", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "driveAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "driverPowerStatus.0", "sensor_type": "driverPowerStatus", "sensor_descr": "Power+ drive status", "group": "Power", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "driverPowerStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "envelopeZone.0", "sensor_type": "envelopeZone", "sensor_descr": "Cooling envelope zone", "group": "Operating envelop", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "envelopeZone"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******3.0", "sensor_index": "generalAlarm.0", "sensor_type": "general<PERSON><PERSON><PERSON>", "sensor_descr": "General alarm", "group": "Alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "general<PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "highPressureAlarm.0", "sensor_type": "highPressureAlarm", "sensor_descr": "High pressure", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "highPressureAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "inputTemperatureBottomProbeAlarm.0", "sensor_type": "inputTemperatureBottomProbeAlarm", "sensor_descr": "Input bottom probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "inputTemperatureBottomProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "inputTemperatureMidProbeAlarm.0", "sensor_type": "inputTemperatureMidProbeAlarm", "sensor_descr": "Input mid probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "inputTemperatureMidProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "inputTemperatureTopProbeAlarm.0", "sensor_type": "inputTemperatureTopProbeAlarm", "sensor_descr": "Input top probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "inputTemperatureTopProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "inverterAlarm.0", "sensor_type": "inverterAlarm", "sensor_descr": "Inverter", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "inverterAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "inverterOnOff.0", "sensor_type": "inverterOnOff", "sensor_descr": "Inverter", "group": "Power", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "inverterOnOff"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******3.0", "sensor_index": "maxDischargeTemperatureAlarm.0", "sensor_type": "maxDischargeTemperatureAlarm", "sensor_descr": "Maximum discharge temperature", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "maxDischargeTemperatureAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******6.0", "sensor_index": "oilReturnAlarm.0", "sensor_type": "oilReturnAlarm", "sensor_descr": "Oil return (lubrication issue)", "group": "Component alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "oilReturnAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "outputTemperatureBottomProbeAlarm.0", "sensor_type": "outputTemperatureBottomProbeAlarm", "sensor_descr": "Output bottom probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "outputTemperatureBottomProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******9.0", "sensor_index": "outputTemperatureMidProbeAlarm.0", "sensor_type": "outputTemperatureMidProbeAlarm", "sensor_descr": "Output mid probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "outputTemperatureMidProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******8.0", "sensor_index": "outputTemperatureTopProbeAlarm.0", "sensor_type": "outputTemperatureTopProbeAlarm", "sensor_descr": "Output top probe", "group": "Probe alarms", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "outputTemperatureTopProbeAlarm"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "unitOnOff.0", "sensor_type": "unitOnOff", "sensor_descr": "Unit", "group": "Power", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "unitOnOff"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "compressorDischargeTemperature.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Compressor discharge temperature", "group": "Unit", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 92.4, "sensor_limit": 900, "sensor_limit_warn": 880, "sensor_limit_low": 30, "sensor_limit_low_warn": 50, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "compressorSuctionTemperature.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Compressor suction temperature", "group": "Unit", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 29.7, "sensor_limit": 900, "sensor_limit_warn": 880, "sensor_limit_low": 30, "sensor_limit_low_warn": 50, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "condensingTemperature.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Condensor temperature", "group": "Unit", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 39.6, "sensor_limit": 500, "sensor_limit_warn": 450, "sensor_limit_low": 30, "sensor_limit_low_warn": 50, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "driverTemperature.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Power+ driver temperature", "group": "Unit", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 7.1, "sensor_limit": 120, "sensor_limit_warn": 100, "sensor_limit_low": 10, "sensor_limit_low_warn": 20, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "evaporatorTemperature.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Evaporator temperature", "group": "Unit", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 2.4, "sensor_limit": 200, "sensor_limit_warn": 150, "sensor_limit_low": -100, "sensor_limit_low_warn": -80, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "inputTemperatureAverage.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Average input temperature", "group": "Input", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 31.3, "sensor_limit": 440, "sensor_limit_warn": 400, "sensor_limit_low": -140, "sensor_limit_low_warn": -100, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "inputTemperatureBottomSensor.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Bottom sensor input temperature", "group": "Input", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 31.6, "sensor_limit": 440, "sensor_limit_warn": 400, "sensor_limit_low": -140, "sensor_limit_low_warn": -100, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "inputTemperatureMidSensor.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Mid sensor input temperature", "group": "Input", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 32.1, "sensor_limit": 440, "sensor_limit_warn": 400, "sensor_limit_low": -140, "sensor_limit_low_warn": -100, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "inputTemperatureTopSensor.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Top sensor input temperature", "group": "Input", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 30.2, "sensor_limit": 440, "sensor_limit_warn": 400, "sensor_limit_low": -140, "sensor_limit_low_warn": -100, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "lcpSetpoint.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "LCP setpoint", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 21, "sensor_limit": 280, "sensor_limit_warn": null, "sensor_limit_low": 150, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.********.0", "sensor_index": "outputTemperatureAverage.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Average output temperature", "group": "Output", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 28.3, "sensor_limit": 300, "sensor_limit_warn": 280, "sensor_limit_low": 150, "sensor_limit_low_warn": 160, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "outputTemperatureBottomSensor.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Bottom sensor output temperature", "group": "Output", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 28.2, "sensor_limit": 300, "sensor_limit_warn": 280, "sensor_limit_low": 150, "sensor_limit_low_warn": 160, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "outputTemperatureMidSensor.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Mid sensor output temperature", "group": "Output", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 28.2, "sensor_limit": 300, "sensor_limit_warn": 280, "sensor_limit_low": 150, "sensor_limit_low_warn": 160, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "outputTemperatureTopSensor.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Top sensor output temperature", "group": "Output", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 28.5, "sensor_limit": 300, "sensor_limit_warn": 280, "sensor_limit_low": 150, "sensor_limit_low_warn": 160, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "dcBusVoltage.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Power+ DC voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 52.8, "sensor_limit": 570, "sensor_limit_warn": 565, "sensor_limit_low": 500, "sensor_limit_low_warn": 510, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.2606.*******.0", "sensor_index": "motorVoltage.0", "sensor_type": "pcoweb-rittal-lcp-3311", "sensor_descr": "Motor voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 25.1, "sensor_limit": 270, "sensor_limit_warn": 265, "sensor_limit_low": 20, "sensor_limit_low_warn": 50, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "compressorDeltaPressureAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorDeltaPressureAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "compressorDischargePressureProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorDischargePressureProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "compressorDischargeTemperatureProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorDischargeTemperatureProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "compressorEnvelopeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorEnvelopeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "compressorOverloadAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorOverloadAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "compressorStartupFailureAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorStartupFailureAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "compressorSuctionPressureProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorSuctionPressureProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "compressorSuctionTemperatureProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "compressorSuctionTemperatureProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "currentErrorCode", "state_descr": "ALA02 - Probe B2 faulty or disconnected", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA03 - Probe B3 faulty or disconnected", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA04 - Probe B4 faulty or disconnected", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA05 - Probe B5 faulty or disconnected", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA06 - Probe B6 faulty or disconnected", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA07 - Probe B7 faulty or disconnected", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA08 - Probe B8 faulty or disconnected", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA09 - Probe B9 faulty or disconnected", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA10 - Probe B10 faulty or disconnected", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA11 - Probe B11 faulty or disconnected", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALA12 - Probe B12 faulty or disconnected", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALB01 - High pressure", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALB02 - High pressure compressor 1 by transducer", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALB03 - Low pressure compressor/compressors by transducer", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALC01 - Compressor 1 overload or inverter alarm", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALC03 - Envelope alarm zone", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALC04 - Compressor start failure", "state_draw_graph": 0, "state_value": 18, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALC05 - High discharge gas temperature", "state_draw_graph": 0, "state_value": 19, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALC06 - Low pressure differential (insufficient lubrication)", "state_draw_graph": 0, "state_value": 20, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALF01 - Fan overload", "state_draw_graph": 0, "state_value": 21, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD02 - Sensor failure", "state_draw_graph": 0, "state_value": 22, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD03 - EEV motor error", "state_draw_graph": 0, "state_value": 23, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD04 - Low superheat", "state_draw_graph": 0, "state_value": 24, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD05 - Low suction temperature", "state_draw_graph": 0, "state_value": 25, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD06 - Low evaporation temperature", "state_draw_graph": 0, "state_value": 26, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD07 - High evaporation temperature", "state_draw_graph": 0, "state_value": 27, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD08 - High condensing temperature", "state_draw_graph": 0, "state_value": 28, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALD09 - Driver offline", "state_draw_graph": 0, "state_value": 29, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALL01 - Power+ offline", "state_draw_graph": 0, "state_value": 30, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALL02 - Power+ Generic Alarm", "state_draw_graph": 0, "state_value": 31, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALL99 - Unexpected inverter stop", "state_draw_graph": 0, "state_value": 32, "state_generic_value": 2}, {"state_name": "currentErrorCode", "state_descr": "ALW04 - Max temperature (warning)", "state_draw_graph": 0, "state_value": 33, "state_generic_value": 2}, {"state_name": "driveAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "driveAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "driverPowerStatus", "state_descr": "stop", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "driverPowerStatus", "state_descr": "run", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "driverPowerStatus", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "envelopeZone", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "maximum compression ratio", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "maximum discharge power", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "current limit", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "maximum suction power", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "minimum compression ratio", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "minimum delta power", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "minimum discharge power", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "envelopeZone", "state_descr": "minimum suction power", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "general<PERSON><PERSON><PERSON>", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 2}, {"state_name": "general<PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "highPressureAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "highPressureAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "inputTemperatureBottomProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "inputTemperatureBottomProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "inputTemperatureMidProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "inputTemperatureMidProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "inputTemperatureTopProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "inputTemperatureTopProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "inverterAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "inverterAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "inverterOnOff", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "inverterOnOff", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "maxDischargeTemperatureAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "maxDischargeTemperatureAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "oilReturnAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "oilReturnAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "outputTemperatureBottomProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "outputTemperatureBottomProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "outputTemperatureMidProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "outputTemperatureMidProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "outputTemperatureTopProbeAlarm", "state_descr": "ok", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "outputTemperatureTopProbeAlarm", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "unitOnOff", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "unitOnOff", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "unitOnOff", "state_descr": "energy-save", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "unitOnOff", "state_descr": "auto", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}]}, "poller": "matches discovery"}}