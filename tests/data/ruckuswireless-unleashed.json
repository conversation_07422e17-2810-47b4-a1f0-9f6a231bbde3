{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.25053.3.1.5.20", "sysDescr": "Ruckus Wireless T300", "sysContact": "<private>", "version": "200.15.6.112 build 54 (US)", "hardware": "T300", "features": null, "location": "<private>", "os": "ruckuswireless-unleashed", "type": "wireless", "serial": "231603507471", "icon": "ruckus.svg"}]}, "poller": "matches discovery"}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.25053.********.1.15.13.0", "processor_index": "0", "processor_type": "ruckuswireless", "processor_usage": 0, "processor_descr": "Processor", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "0", "entPhysicalIndex": null, "mempool_type": "ruckuswireless-unleashed", "mempool_class": "system", "mempool_precision": 1, "mempool_descr": "System Memory", "mempool_perc": 59, "mempool_perc_oid": ".*******.4.1.25053.********.1.15.14.0", "mempool_used": 59, "mempool_used_oid": null, "mempool_free": 41, "mempool_free_oid": null, "mempool_total": 100, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "ap-count", "sensor_index": "1", "sensor_type": "ruckuswireless-unleashed", "sensor_descr": "Connected APs", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 1, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.25053.********.********\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "1", "sensor_type": "ruckuswireless-unleashed", "sensor_descr": "Clients: Total", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.25053.********.********\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "ap-count", "sensor_index": "1", "sensor_type": "ruckuswireless-unleashed", "sensor_descr": "Connected APs", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 1, "sensor_prev": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.25053.********.********\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "1", "sensor_type": "ruckuswireless-unleashed", "sensor_descr": "Clients: Total", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.25053.********.********\"]", "rrd_type": "GAUGE"}]}}}