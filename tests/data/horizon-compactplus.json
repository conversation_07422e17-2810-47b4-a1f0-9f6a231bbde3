{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.7262.2.5", "sysDescr": "Mode: hy56_380_256qam Omni: 1.4.1", "sysContact": "<private>", "version": "1.4.1", "hardware": "PLHP11B1SXR2", "features": null, "location": "<private>", "os": "horizon-compactplus", "type": "wireless", "serial": null, "icon": "dragonwave.png"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortRxFramesLengthErrors.1", "sensor_type": "horizon-compactplus", "sensor_descr": "Enet Port1 Input Errors", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortRxFramesLengthErrors.2", "sensor_type": "horizon-compactplus", "sensor_descr": "Enet Port2 Input Errors", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.********", "sensor_index": "hzCpAlarmStatus.modemRxLossOfSignal.1", "sensor_type": "hzCpAlarmStatus.modemRxLossOfSignal.1", "sensor_descr": "Modem1 Rx Loss Of Signal", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpAlarmStatus.modemRxLossOfSignal.1"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortLinkStatus.1", "sensor_type": "hzCpEnetPortLinkStatus", "sensor_descr": "Enet Port1 Link State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpEnetPortLinkStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortLinkStatus.2", "sensor_type": "hzCpEnetPortLinkStatus", "sensor_descr": "Enet Port2 Link State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpEnetPortLinkStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpModemModulationType.1", "sensor_type": "hzCpModemModulationType", "sensor_descr": "Modem1 Modulation", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 256, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpModemModulationType"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.********", "sensor_index": "hzCpRadioTemperature.1", "sensor_type": "horizon-compactplus", "sensor_descr": "Radio1 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 37.2, "sensor_limit": 57.2, "sensor_limit_warn": null, "sensor_limit_low": 27.2, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "hzCpAlarmStatus.modemRxLossOfSignal.1", "state_descr": "No", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "hzCpAlarmStatus.modemRxLossOfSignal.1", "state_descr": "Yes", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "hzCpEnetPortLinkStatus", "state_descr": "Down", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "hzCpEnetPortLinkStatus", "state_descr": "Up", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "hzCpModemModulationType", "state_descr": "qpsk", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "hzCpModemModulationType", "state_descr": "qam16", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "hzCpModemModulationType", "state_descr": "qam32", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "hzCpModemModulationType", "state_descr": "qam64", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "hzCpModemModulationType", "state_descr": "qam128", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 1}, {"state_name": "hzCpModemModulationType", "state_descr": "qam256", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "hzCpModemModulationType", "state_descr": "qam512", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "hzCpModemModulationType", "state_descr": "qam1024", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "hzCpModemModulationType", "state_descr": "qam2048", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortRxFramesLengthErrors.1", "sensor_type": "horizon-compactplus", "sensor_descr": "Enet Port1 Input Errors", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortRxFramesLengthErrors.2", "sensor_type": "horizon-compactplus", "sensor_descr": "Enet Port2 Input Errors", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.********", "sensor_index": "hzCpAlarmStatus.modemRxLossOfSignal.1", "sensor_type": "hzCpAlarmStatus.modemRxLossOfSignal.1", "sensor_descr": "Modem1 Rx Loss Of Signal", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpAlarmStatus.modemRxLossOfSignal.1"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortLinkStatus.1", "sensor_type": "hzCpEnetPortLinkStatus", "sensor_descr": "Enet Port1 Link State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpEnetPortLinkStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpEnetPortLinkStatus.2", "sensor_type": "hzCpEnetPortLinkStatus", "sensor_descr": "Enet Port2 Link State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpEnetPortLinkStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.*******", "sensor_index": "hzCpModemModulationType.1", "sensor_type": "hzCpModemModulationType", "sensor_descr": "Modem1 Modulation", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 256, "user_func": null, "rrd_type": "GAUGE", "state_name": "hzCpModemModulationType"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.7262.*******.********", "sensor_index": "hzCpRadioTemperature.1", "sensor_type": "horizon-compactplus", "sensor_descr": "Radio1 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 37.2, "sensor_limit": 57.2, "sensor_limit_warn": null, "sensor_limit_low": 27.2, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "hzCpAlarmStatus.modemRxLossOfSignal.1", "state_descr": "No", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "hzCpAlarmStatus.modemRxLossOfSignal.1", "state_descr": "Yes", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "hzCpEnetPortLinkStatus", "state_descr": "Down", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "hzCpEnetPortLinkStatus", "state_descr": "Up", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "hzCpModemModulationType", "state_descr": "qpsk", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "hzCpModemModulationType", "state_descr": "qam16", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "hzCpModemModulationType", "state_descr": "qam32", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "hzCpModemModulationType", "state_descr": "qam64", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "hzCpModemModulationType", "state_descr": "qam128", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 1}, {"state_name": "hzCpModemModulationType", "state_descr": "qam256", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 1}, {"state_name": "hzCpModemModulationType", "state_descr": "qam512", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "hzCpModemModulationType", "state_descr": "qam1024", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "hzCpModemModulationType", "state_descr": "qam2048", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}]}}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "Rx <PERSON>", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "COUNTER"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "Tx Power", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 23, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "RSL", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -43.3, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "snr", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "SNR", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 38.4, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "Rx <PERSON>", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "COUNTER"}, {"sensor_deleted": 0, "sensor_class": "power", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "Tx Power", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 23, "sensor_prev": 23, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "RSL", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -43.3, "sensor_prev": -43.3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "snr", "sensor_index": "0", "sensor_type": "horizon-compactplus", "sensor_descr": "SNR", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 38.4, "sensor_prev": 38.4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.7262.*******.*******\"]", "rrd_type": "GAUGE"}]}}}