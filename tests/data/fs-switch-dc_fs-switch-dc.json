{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.52642.1.1.10.1.516", "sysDescr": null, "sysContact": null, "version": "N5860_FSOS 11.0(5)B9P124", "hardware": "N5860-48SC", "features": null, "location": null, "os": "fs-switch-dc", "type": "network", "serial": "G1RL71S000193", "icon": "fs.svg"}]}, "poller": "matches discovery"}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.52642.********.3*******.5.1", "processor_index": "1", "processor_type": "fs-switch-dc", "processor_usage": 36, "processor_descr": "Slot 0: N5860-48SC, Cpu 0", "processor_precision": 1, "processor_perc_warn": 85}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "fs-switch-dc", "mempool_class": "system", "mempool_precision": 1024, "mempool_descr": "Slot 0: N5860-48SC, Cpu 0", "mempool_perc": 38, "mempool_perc_oid": null, "mempool_used": 1624862720, "mempool_used_oid": ".*******.4.1.52642.********.35.1.2.1.7.1", "mempool_free": 2670104576, "mempool_free_oid": null, "mempool_total": 4294967296, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 15}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.1", "sensor_index": "rx-1", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/1 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.88, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.10", "sensor_index": "rx-10", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/10 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -40, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.12", "sensor_index": "rx-12", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/12 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.4, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.13", "sensor_index": "rx-13", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/13 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.75, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.14", "sensor_index": "rx-14", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/14 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -3.04, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.15", "sensor_index": "rx-15", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/15 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.37, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.16", "sensor_index": "rx-16", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/16 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -3.04, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.17", "sensor_index": "rx-17", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/17 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -3.04, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.18", "sensor_index": "rx-18", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/18 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -3.9, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.21", "sensor_index": "rx-21", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/21 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -40, "sensor_limit": 0, "sensor_limit_warn": -1, "sensor_limit_low": -20, "sensor_limit_low_warn": -18.01, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.22", "sensor_index": "rx-22", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/22 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -40, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.3", "sensor_index": "rx-3", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/3 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.37, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.4", "sensor_index": "rx-4", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/4 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.79, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.5", "sensor_index": "rx-5", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/5 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.42, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.6", "sensor_index": "rx-6", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/6 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.37, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.7", "sensor_index": "rx-7", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/7 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -9.09, "sensor_limit": 3.5, "sensor_limit_warn": 0.5, "sensor_limit_low": -18.39, "sensor_limit_low_warn": -14.4, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.8", "sensor_index": "rx-8", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/8 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -7.17, "sensor_limit": 3.5, "sensor_limit_warn": 0.5, "sensor_limit_low": -18.39, "sensor_limit_low_warn": -14.4, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.76.9", "sensor_index": "rx-9", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/9 RX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -40, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -12, "sensor_limit_low_warn": -10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.1", "sensor_index": "tx-1", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/1 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.89, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.10", "sensor_index": "tx-10", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/10 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.03, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.12", "sensor_index": "tx-12", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/12 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.1, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.13", "sensor_index": "tx-13", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/13 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.72, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.14", "sensor_index": "tx-14", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/14 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.12, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.15", "sensor_index": "tx-15", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/15 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.62, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.16", "sensor_index": "tx-16", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/16 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.88, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.17", "sensor_index": "tx-17", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/17 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.99, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.18", "sensor_index": "tx-18", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/18 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.11, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.21", "sensor_index": "tx-21", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/21 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.65, "sensor_limit": 0, "sensor_limit_warn": -1, "sensor_limit_low": -6, "sensor_limit_low_warn": -5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.22", "sensor_index": "tx-22", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/22 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.14, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.3", "sensor_index": "tx-3", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/3 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -1.98, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.4", "sensor_index": "tx-4", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/4 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.43, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.5", "sensor_index": "tx-5", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/5 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.28, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.6", "sensor_index": "tx-6", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/6 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.51, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.7", "sensor_index": "tx-7", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/7 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.24, "sensor_limit": 3.5, "sensor_limit_warn": 0.5, "sensor_limit_low": -12.2, "sensor_limit_low_warn": -8.2, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.8", "sensor_index": "tx-8", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/8 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.9, "sensor_limit": 3.5, "sensor_limit_warn": 0.5, "sensor_limit_low": -12.2, "sensor_limit_low_warn": -8.2, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.81.9", "sensor_index": "tx-9", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/9 TX", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": -2.02, "sensor_limit": 1, "sensor_limit_warn": -1, "sensor_limit_low": -9.3, "sensor_limit_low_warn": -7, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.1.1", "sensor_type": "fs-switch-dc", "sensor_descr": "Fan 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 11261, "sensor_limit": 20269.8, "sensor_limit_warn": null, "sensor_limit_low": 9008.8, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.2.1", "sensor_type": "fs-switch-dc", "sensor_descr": "Fan 1/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 11169, "sensor_limit": 20104.2, "sensor_limit_warn": null, "sensor_limit_low": 8935.2, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.3.1", "sensor_type": "fs-switch-dc", "sensor_descr": "Fan 1/3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 11202, "sensor_limit": 20163.6, "sensor_limit_warn": null, "sensor_limit_low": 8961.6, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.4.1", "sensor_type": "fs-switch-dc", "sensor_descr": "Fan 1/4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 11210, "sensor_limit": 20178, "sensor_limit_warn": null, "sensor_limit_low": 8968, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.15.1.1", "sensor_index": "1.1", "sensor_type": "fs-switch-dc", "sensor_descr": "Power supply 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 56, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.15.1.2", "sensor_index": "1.2", "sensor_type": "fs-switch-dc", "sensor_descr": "Power supply 1/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 53, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.1", "sensor_index": "tempstat-1", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/1 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.10", "sensor_index": "tempstat-10", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/10 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.12", "sensor_index": "tempstat-12", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/12 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.13", "sensor_index": "tempstat-13", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/13 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.14", "sensor_index": "tempstat-14", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/14 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.15", "sensor_index": "tempstat-15", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/15 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.16", "sensor_index": "tempstat-16", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/16 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.17", "sensor_index": "tempstat-17", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/17 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.18", "sensor_index": "tempstat-18", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/18 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.21", "sensor_index": "tempstat-21", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/21 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.22", "sensor_index": "tempstat-22", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/22 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.3", "sensor_index": "tempstat-3", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/3 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.4", "sensor_index": "tempstat-4", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/4 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.5", "sensor_index": "tempstat-5", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/5 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.6", "sensor_index": "tempstat-6", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/6 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.7", "sensor_index": "tempstat-7", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/7 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.8", "sensor_index": "tempstat-8", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/8 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.18.9", "sensor_index": "tempstat-9", "sensor_type": "fsFiberTempStatus", "sensor_descr": "TenGigabitEthernet 0/9 Temperature", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsFiberTempStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.3.1.1", "sensor_index": "psu-1.1", "sensor_type": "fsSystemElectricalInformationStatus", "sensor_descr": "Power supply 1/1", "group": "Power Supplies", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsSystemElectricalInformationStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.3.1.2", "sensor_index": "psu-1.2", "sensor_type": "fsSystemElectricalInformationStatus", "sensor_descr": "Power supply 1/2", "group": "Power Supplies", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsSystemElectricalInformationStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "fan-1.1.1", "sensor_type": "fsSystemFanStatus", "sensor_descr": "Fan 1/1", "group": "Fans", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsSystemFanStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "fan-1.2.1", "sensor_type": "fsSystemFanStatus", "sensor_descr": "Fan 1/2", "group": "Fans", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsSystemFanStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "fan-1.3.1", "sensor_type": "fsSystemFanStatus", "sensor_descr": "Fan 1/3", "group": "Fans", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsSystemFanStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "fan-1.4.1", "sensor_type": "fsSystemFanStatus", "sensor_descr": "Fan 1/4", "group": "Fans", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fsSystemFanStatus"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.0.1", "sensor_type": "fs-switch-dc", "sensor_descr": "air_outlet 1/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 90, "sensor_limit_warn": 85, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.0.2", "sensor_type": "fs-switch-dc", "sensor_descr": "air_inlet 1/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 34, "sensor_limit": 60, "sensor_limit_warn": 50, "sensor_limit_low": 24, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.0.3", "sensor_type": "fs-switch-dc", "sensor_descr": "board 1/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 90, "sensor_limit_warn": 85, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.0.4", "sensor_type": "fs-switch-dc", "sensor_descr": "cpu 1/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 49, "sensor_limit": 100, "sensor_limit_warn": 85, "sensor_limit_low": 39, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.*******", "sensor_index": "1.0.5", "sensor_type": "fs-switch-dc", "sensor_descr": "switch 1/0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 47, "sensor_limit": 105, "sensor_limit_warn": 100, "sensor_limit_low": 37, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.1", "sensor_index": "temp-1", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/1", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 61, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.10", "sensor_index": "temp-10", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/10", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 38, "sensor_limit": 58, "sensor_limit_warn": null, "sensor_limit_low": 28, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.12", "sensor_index": "temp-12", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/12", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 38, "sensor_limit": 58, "sensor_limit_warn": null, "sensor_limit_low": 28, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.13", "sensor_index": "temp-13", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/13", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.14", "sensor_index": "temp-14", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/14", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 59, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.15", "sensor_index": "temp-15", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/15", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 57, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.16", "sensor_index": "temp-16", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/16", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 59, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.17", "sensor_index": "temp-17", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/17", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 34, "sensor_limit": 54, "sensor_limit_warn": null, "sensor_limit_low": 24, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.18", "sensor_index": "temp-18", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/18", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36, "sensor_limit": 56, "sensor_limit_warn": null, "sensor_limit_low": 26, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.21", "sensor_index": "temp-21", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/21", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 55, "sensor_limit_warn": null, "sensor_limit_low": 25, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.22", "sensor_index": "temp-22", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/22", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.3", "sensor_index": "temp-3", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/3", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 61, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.4", "sensor_index": "temp-4", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/4", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 61, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.5", "sensor_index": "temp-5", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/5", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 59, "sensor_limit_warn": null, "sensor_limit_low": 29, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.6", "sensor_index": "temp-6", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/6", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 61, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.7", "sensor_index": "temp-7", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/7", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 43, "sensor_limit": 63, "sensor_limit_warn": null, "sensor_limit_low": 33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.8", "sensor_index": "temp-8", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/8", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 47, "sensor_limit": 67, "sensor_limit_warn": null, "sensor_limit_low": 37, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.*********.17.9", "sensor_index": "temp-9", "sensor_type": "fs-switch-dc", "sensor_descr": "TenGigabitEthernet 0/9", "group": "Transceivers", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 61, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.11.1.1", "sensor_index": "1.1", "sensor_type": "fs-switch-dc", "sensor_descr": "Power supply 1/1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 119, "sensor_limit": 136.85, "sensor_limit_warn": null, "sensor_limit_low": 101.15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.52642.********.********.11.1.2", "sensor_index": "1.2", "sensor_type": "fs-switch-dc", "sensor_descr": "Power supply 1/2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 119, "sensor_limit": 136.85, "sensor_limit_warn": null, "sensor_limit_low": 101.15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "fsFiberTempStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "fsFiberTempStatus", "state_descr": "warning", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "fsFiberTempStatus", "state_descr": "alarm", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 2}, {"state_name": "fsSystemElectricalInformationStatus", "state_descr": "noexist", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "fsSystemElectricalInformationStatus", "state_descr": "existnopower", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "fsSystemElectricalInformationStatus", "state_descr": "existreadypower", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "fsSystemElectricalInformationStatus", "state_descr": "normal", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "fsSystemElectricalInformationStatus", "state_descr": "powerbutabnormal", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "fsSystemElectricalInformationStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 3}, {"state_name": "fsSystemFanStatus", "state_descr": "noexist", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "fsSystemFanStatus", "state_descr": "existnopower", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 2}, {"state_name": "fsSystemFanStatus", "state_descr": "existreadypower", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 1}, {"state_name": "fsSystemFanStatus", "state_descr": "normal", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "fsSystemFanStatus", "state_descr": "powerbutabnormal", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "fsSystemFanStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 3}]}, "poller": "matches discovery"}}