{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.12356.101.1.1000", "sysDescr": null, "sysContact": null, "version": "v6.4.8,build1914,211117 (GA)", "hardware": "FortiGate 100", "features": null, "location": null, "os": "fortigate", "type": "firewall", "serial": null, "icon": "fortinet.svg"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.*********.1.5.1", "sensor_index": "fgLinkMonitorJitter.1", "sensor_type": "fortigate", "sensor_descr": "Monitor Jitter lhm-am7", "group": "Link monitor", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.092, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.*********.1.4.1", "sensor_index": "fgLinkMonitorLatency.1", "sensor_type": "fortigate", "sensor_descr": "Monitor Latency lhm-am7", "group": "Link monitor", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4.102, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.*********.1.8.1", "sensor_index": "fgLinkMonitorPacketLoss.1", "sensor_type": "fortigate", "sensor_descr": "Monitor Packet Loss lhm-am7", "group": "Link monitor", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.*********.0", "sensor_index": "fgSysSesCount.0", "sensor_type": "sessions", "sensor_descr": "Session count", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.**********.0", "sensor_index": "fgSysSesRate1.0", "sensor_type": "sessions", "sensor_descr": "Sessions/sec 1m avg", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.**********.0", "sensor_index": "fgSysSesRate10.0", "sensor_type": "sessions", "sensor_descr": "Sessions/sec 10m avg", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.**********.0", "sensor_index": "fgSysSesRate30.0", "sensor_type": "sessions", "sensor_descr": "Sessions/sec 30m avg", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.**********.0", "sensor_index": "fgSysSesRate60.0", "sensor_type": "sessions", "sensor_descr": "Sessions/sec 60m avg", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.12356.*********.1.3.1", "sensor_index": "fgLinkMonitorID.1", "sensor_type": "fgLinkMonitorTable", "sensor_descr": "lhm-am7", "group": "Link monitor", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "fgLinkMonitorTable"}], "state_indexes": [{"state_name": "fgLinkMonitorTable", "state_descr": "Alive", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "fgLinkMonitorTable", "state_descr": "Dead", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}]}, "poller": "matches discovery"}}