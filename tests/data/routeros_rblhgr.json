{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.14988.1", "sysDescr": "RouterOS RBLHGR", "sysContact": "<private>", "version": "6.45.7", "hardware": "RBLHGR", "features": "Level 3", "location": "<private>", "os": "routeros", "type": "network", "serial": "A33309596248", "icon": "mikrotik.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lte1", "ifName": "lte1", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "other", "ifAlias": "lte1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ether1", "ifName": "ether1", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "ether1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lte1", "ifName": "lte1", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "other", "ifAlias": "lte1", "ifPhysAddress": "001122334456", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 71190, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 56657, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 41180564, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 7741634, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ether1", "ifName": "ether1", "portName": null, "ifIndex": 2, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "ether1", "ifPhysAddress": "b869f4b7ce59", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 22084, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 37353, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 3901189, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 38099673, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 95, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 459, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 8, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 918, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 1, "processor_oid": ".*******.2.1.25.3.3.1.2.1", "processor_index": "1", "processor_type": "hr", "processor_usage": 6, "processor_descr": "Processor", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "65536", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "system", "mempool_precision": 1024, "mempool_descr": "main memory", "mempool_perc": 35, "mempool_perc_oid": null, "mempool_used": 23597056, "mempool_used_oid": ".*******.********.3.1.6.65536", "mempool_free": 43511808, "mempool_free_oid": null, "mempool_total": 67108864, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "storage": {"discovery": {"storage": [{"type": "hrstorage", "storage_index": "131072", "storage_type": "hrStorageFixedDisk", "storage_descr": "system disk", "storage_size": 16777216, "storage_size_oid": null, "storage_units": 1024, "storage_used": 15380480, "storage_used_oid": ".*******.********.3.1.6.131072", "storage_free": 1396736, "storage_free_oid": null, "storage_perc": 92, "storage_perc_oid": null, "storage_perc_warn": 60}]}, "poller": "matches discovery"}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "rsrp", "sensor_index": "1", "sensor_type": "routeros", "sensor_descr": "lte1: Signal RSRP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -97, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14988.********.1.4.1\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rsrq", "sensor_index": "1", "sensor_type": "routeros", "sensor_descr": "lte1: Signal RSRQ", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -8, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14988.********.1.3.1\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "sinr", "sensor_index": "1", "sensor_type": "routeros", "sensor_descr": "lte1: Signal SINR", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 23, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14988.********.1.7.1\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "rsrp", "sensor_index": "1", "sensor_type": "routeros", "sensor_descr": "lte1: Signal RSRP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -97, "sensor_prev": -97, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14988.********.1.4.1\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rsrq", "sensor_index": "1", "sensor_type": "routeros", "sensor_descr": "lte1: Signal RSRQ", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -8, "sensor_prev": -8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14988.********.1.3.1\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "sinr", "sensor_index": "1", "sensor_type": "routeros", "sensor_descr": "lte1: Signal SINR", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 23, "sensor_prev": 23, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.14988.********.1.7.1\"]", "rrd_type": "GAUGE"}]}}, "stp": {"discovery": {"stp": [{"vlan": null, "rootBridge": 1, "bridgeAddress": "", "protocolSpecification": "ieee8021d", "priority": 0, "timeSinceTopologyChange": "0", "topChanges": 0, "designatedRoot": "", "rootCost": 0, "rootPort": 0, "maxAge": 0, "helloTime": 0, "holdTime": 0, "forwardDelay": 0, "bridgeMaxAge": 0, "bridgeHelloTime": 0, "bridgeForwardDelay": 0}]}, "poller": "matches discovery"}, "entity-physical": {"discovery": {"entPhysical": [{"entPhysicalIndex": 65536, "entPhysicalDescr": "RouterOS 6.45.7 (stable) on RBLHGR", "entPhysicalClass": "chassis", "entPhysicalName": "MIPS 24Kc V7.4", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "", "entPhysicalVendorType": "zeroDotZero", "entPhysicalSerialNum": "", "entPhysicalContainedIn": 0, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "", "ifIndex": null}, {"entPhysicalIndex": 262145, "entPhysicalDescr": "Linux 3.3.5 ehci_hcd RB400 EHCI", "entPhysicalClass": "unknown", "entPhysicalName": "1-0", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "0x0002", "entPhysicalVendorType": "zeroDotZero", "entPhysicalSerialNum": "rb400_usb", "entPhysicalContainedIn": 65536, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "0x1d6b", "ifIndex": null}, {"entPhysicalIndex": 262146, "entPhysicalDescr": "'MikroTik' 'R11e-4G'", "entPhysicalClass": "unknown", "entPhysicalName": "1-1", "entPhysicalHardwareRev": "", "entPhysicalFirmwareRev": "", "entPhysicalSoftwareRev": "", "entPhysicalAlias": "", "entPhysicalAssetID": "", "entPhysicalIsFRU": "false", "entPhysicalModelName": "0x0003", "entPhysicalVendorType": "zeroDotZero", "entPhysicalSerialNum": "9CB903B1E0AB", "entPhysicalContainedIn": 65536, "entPhysicalParentRelPos": -1, "entPhysicalMfgName": "0x2cd2", "ifIndex": null}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.14988.*******.0", "sensor_index": "mtxrDHCPLeaseCount.0", "sensor_type": "routeros", "sensor_descr": "DHCP Lease Count", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}]}, "poller": "matches discovery"}}